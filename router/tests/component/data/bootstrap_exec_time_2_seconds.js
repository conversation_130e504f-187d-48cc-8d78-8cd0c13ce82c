var common_stmts = require("common_statements");

var options = {
  cluster_type: "gr",
  gr_id: mysqld.global.gr_id,
  router_version: mysqld.global.router_version,
  innodb_cluster_name: "my-cluster",
  innodb_cluster_instances:
      [["localhost", 5500], ["localhost", 5510], ["localhost", 5520]],
};

var common_responses = common_stmts.prepare_statement_responses(
    [
      "router_set_session_options",
      "router_set_gr_consistency_level",
      "router_select_cluster_type_v2",
      "router_select_current_instance_attributes",
      "router_select_group_membership",
      "router_count_clusters_v2",
      "router_check_member_state",
      "router_select_members_count",
      "router_select_replication_group_name",
      "router_show_cipher_status",
      "router_select_cluster_instances_v2_gr",
      "router_select_cluster_instance_addresses_v2",
      "router_start_transaction",
      "router_commit",
      "router_clusterset_present",
      "get_routing_guidelines_version",
    ],
    options);

var router_select_schema_version =
    common_stmts.get("router_select_schema_version", options);

var common_responses_regex = common_stmts.prepare_statement_responses_regex(
    [
      "router_insert_into_routers",
      "router_create_user_if_not_exists",
      "router_check_auth_plugin",
      "router_grant_on_metadata_db",
      "router_grant_on_pfs_db",
      "router_grant_on_routers",
      "router_grant_on_v2_routers",
      "router_update_routers_in_metadata",
      "router_update_router_options_in_metadata",
      "router_select_config_defaults_stored_gr_cluster",
      "router_update_local_cluster_in_metadata",
    ],
    options);

if (mysqld.global.blocked === undefined) {
  mysqld.global.blocked = 0;
}

({
  stmts: function(stmt) {
    mysqld.global.blocked = 0;
    var res;
    if (common_responses.hasOwnProperty(stmt)) {
      return common_responses[stmt];
    }
    if (stmt === router_select_schema_version.stmt) {
      mysqld.global.blocked = 1;
      router_select_schema_version.exec_time = 2000.0;
      return router_select_schema_version;
    } else if (
        (res = common_stmts.handle_regex_stmt(stmt, common_responses_regex)) !==
        undefined) {
      return res;
    } else {
      return common_stmts.unknown_statement_response(stmt);
    }
  }
})
