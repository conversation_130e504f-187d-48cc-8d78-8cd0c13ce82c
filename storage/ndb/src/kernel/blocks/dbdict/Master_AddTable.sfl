// Copyright (c) 2003, 2025, Oracle and/or its affiliates.
//
// This program is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License, version 2.0,
// as published by the Free Software Foundation.
//
// This program is designed to work with certain software (including
// but not limited to OpenSSL) that is licensed under separate terms,
// as designated in a particular file or component or in included license
// documentation.  The authors of MySQL hereby grant you an additional
// permission to link the program and your derivative works with the
// separately licensed software that they have either included with
// the program or referenced in the documentation.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License, version 2.0, for more details.
//
// You should have received a copy of the GNU General Public License
// along with this program; if not, write to the Free Software
// Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
// ---------------------------------------------------------------------------
// This file contains a signal log trace for DBDICT at the master for a
// create table. Another file contains the signal log for the participant
// node. Master node is 2, participant node 4 and api node is 3.

// ---------------------------------------------------------------------------
// First arrives the table description in a number of DICTTABINFO signals.
// These have a header of 5 words (see DictTabInfo.hpp for details) and
// upto 20 words of property data per signal. The property data is packed
// by the SimpleProperties class.
// ---------------------------------------------------------------------------
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57069 gsn: 204 "DICTTABINFO" prio: 1
s.bn: 0 "API", s.proc: 3, s.sigId: 940284 length: 25 trace: 0
 H'00010003 H'00047700 H'00000001 H'00000042 H'00000000 H'4e444250 H'524f5053
 H'00010000 H'00000000 H'1c0a1203 H'524f4c46 H'00020001 H'0000000a H'56504e5f
 H'55534552 H'53000000 H'0001000a H'0000004b H'000203e8 H'00000007 H'56504e5f
 H'49440000 H'000103ee H'00000001 H'000203e8
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57069 gsn: 204 "DICTTABINFO" prio: 1
s.bn: 0 "API", s.proc: 3, s.sigId: 940284 length: 25 trace: 0
 H'00010003 H'00047700 H'00000001 H'00000042 H'00000014 H'00000007 H'56504e5f
 H'4e420000 H'000103ee H'00000001 H'000203e8 H'0000000d H'44495245 H'43544f52
 H'595f4e42 H'00000000 H'000103eb H'00000003 H'000103ed H'0000000a H'000103ec
 H'00000002 H'000203e8 H'00000010 H'4c415354
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57069 gsn: 204 "DICTTABINFO" prio: 1
s.bn: 0 "API", s.proc: 3, s.sigId: 940284 length: 25 trace: 0
 H'00010003 H'00047700 H'00000001 H'00000042 H'00000028 H'5f43414c H'4c5f5041
 H'52545900 H'000103eb H'00000003 H'000103ed H'0000000a H'000103ec H'00000002
 H'000203e8 H'00000006 H'44455343 H'52000000 H'000103eb H'00000003 H'000103ed
 H'00000064 H'000103ec H'00000002 H'00010005
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57069 gsn: 204 "DICTTABINFO" prio: 1
s.bn: 0 "API", s.proc: 3, s.sigId: 940284 length: 11 trace: 0
 H'00010003 H'00047700 H'00000001 H'00000042 H'0000003c H'00000002 H'00010006
 H'00000005 H'0001000c H'00000002 H'0000ffff

// ---------------------------------------------------------------------------
// Send DICT_SCHEMAREQ to all nodes including ourselves to write the state
// ADD_STARTED in the schema file for the new table.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 132 "DICT_SCHEMAREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57069 length: 7 trace: 0
 H'00010003 H'00047700 H'00000002 H'00000001 H'00000000 H'00000000 H'00000001
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 4, gsn: 132 "DICT_SCHEMAREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57069 length: 7 trace: 0
 H'00010003 H'00047700 H'00000002 H'00000001 H'00000000 H'00000000 H'00000001
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57069 gsn: 132 "DICT_SCHEMAREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 57077 length: 7 trace: 0
 H'00010003 H'00047700 H'00000002 H'00000001 H'00000000 H'00000000 H'00000001

// ---------------------------------------------------------------------------
// Write both schema files with new state of table added.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 261 "FSOPENREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57069 length: 7 trace: 0
 UserReference: H'00fa0002, userPointer: H'00000000
 FileNumber[1-4]: H'ffffffff H'ffffffff H'ffffffff H'01050100
 FileFlags: H'00000311 Open write only, Create new file, Truncate existing file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57081 gsn: 259 "FSOPENCONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57082 length: 3 trace: 0
 UserPointer: H'00000000
 FilePointer: 99
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 272 "FSWRITEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57081 length: 8 trace: 0
 FilePointer: 99
 UserReference: H'00fa0002, UserPointer: H'00000000
 Operation flag: H'00000011, Sync, Format=Array of pages
 varIndex: 1
 numberOfPages: 1
 pageData:  H'00000008, H'00000000

---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57090 gsn: 270 "FSWRITECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57091 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 257 "FSCLOSEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57090 length: 4 trace: 0
 FilePointer: 99
 UserReference: H'00fa0002, userPointer: H'00000000
 Flags: H'00000000, Don't remove file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57099 gsn: 255 "FSCLOSECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57100 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 261 "FSOPENREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57099 length: 7 trace: 0
 UserReference: H'00fa0002, userPointer: H'00000000
 FileNumber[1-4]: H'ffffffff H'ffffffff H'ffffffff H'01050200
 FileFlags: H'00000311 Open write only, Create new file, Truncate existing file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57111 gsn: 259 "FSOPENCONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57112 length: 3 trace: 0
 UserPointer: H'00000000
 FilePointer: 100
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 272 "FSWRITEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57111 length: 8 trace: 0
 FilePointer: 100
 UserReference: H'00fa0002, UserPointer: H'00000000
 Operation flag: H'00000011, Sync, Format=Array of pages
 varIndex: 1
 numberOfPages: 1
 pageData:  H'00000008, H'00000000

---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57123 gsn: 270 "FSWRITECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57124 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 257 "FSCLOSEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57123 length: 4 trace: 0
 FilePointer: 100
 UserReference: H'00fa0002, userPointer: H'00000000
 Flags: H'00000000, Don't remove file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57132 gsn: 255 "FSCLOSECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57133 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 133 "DICT_SCHEMACONF" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 1 trace: 0
 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57132 gsn: 133 "DICT_SCHEMACONF" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 57135 length: 1 trace: 0
 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57132 gsn: 133 "DICT_SCHEMACONF" prio: 1
s.bn: 250 "DBDICT", s.proc: 4, s.sigId: 46718 length: 1 trace: 0
 H'00000004

// ---------------------------------------------------------------------------
// Pack Table description into pages in DICT using SimpleProperties class.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 3 trace: 0
 H'00000001 H'00000002 H'00000000
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57132 gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 57140 length: 3 trace: 0
 H'00000001 H'00000002 H'00000000
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 2 trace: 0
 H'00000002 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57132 gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 57141 length: 2 trace: 0
 H'00000002 H'00000002

// ---------------------------------------------------------------------------
// Send the table description over to the other NDB nodes.
// A CONTINUEB is sent for each signal sent to avoid overloading the
// transporters.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 4, gsn: 204 "DICTTABINFO" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 25 trace: 0
 H'00fa0002 H'00000000 H'00000002 H'0000006e H'00000000 H'4e444250 H'524f5053
 H'00002000 H'0000001c H'1c0a1203 H'524f4c46 H'00020001 H'0000000a H'56504e5f
 H'55534552 H'53000000 H'0001000a H'0000004b H'000203e8 H'00000007 H'56504e5f
 H'49440000 H'1cc03924 H'00000001 H'000203e8
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 2 trace: 0
 H'00000002 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57132 gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 57142 length: 2 trace: 0
 H'00000002 H'00000002
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 4, gsn: 204 "DICTTABINFO" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 25 trace: 0
 H'00fa0002 H'00000000 H'00000002 H'0000006e H'00000014 H'00000007 H'56504e5f
 H'4e420000 H'000103ee H'00000001 H'000203e8 H'0000000d H'44495245 H'43544f52
 H'595f4e42 H'00000000 H'000103eb H'00000003 H'524f4c46 H'00020001 H'0000000a
 H'56504e5f H'55534552 H'53000010 H'00010002
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 2 trace: 0
 H'00000002 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57132 gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 57143 length: 2 trace: 0
 H'00000002 H'00000002
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 4, gsn: 204 "DICTTABINFO" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 25 trace: 0
 H'00fa0002 H'00000000 H'00000002 H'0000006e H'00000028 H'00000002 H'00010011
 H'00000003 H'00010003 H'00000001 H'00010005 H'00000002 H'00010006 H'00000005
 H'0001000a H'0000004b H'0001000c H'00000002 H'000203e8 H'00000007 H'56504e5f
 H'49440064 H'000103e9 H'00000000 H'000103ee
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 2 trace: 0
 H'00000002 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57132 gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 57144 length: 2 trace: 0
 H'00000002 H'00000002
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 4, gsn: 204 "DICTTABINFO" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 25 trace: 0
 H'00fa0002 H'00000000 H'00000002 H'0000006e H'0000003c H'00000001 H'000203e8
 H'00000007 H'56504e5f H'4e420002 H'000103e9 H'00000001 H'000103ee H'00000001
 H'000203e8 H'0000000d H'44495245 H'43544f52 H'595f4e42 H'00000000 H'000103e9
 H'00000002 H'000103eb H'00000003 H'000103ec
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 2 trace: 0
 H'00000002 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57132 gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 57145 length: 2 trace: 0
 H'00000002 H'00000002
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 4, gsn: 204 "DICTTABINFO" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 25 trace: 0
 H'00fa0002 H'00000000 H'00000002 H'0000006e H'00000050 H'00000002 H'000103ed
 H'0000000a H'000203e8 H'00000010 H'4c415354 H'5f43414c H'4c5f5041 H'52545900
 H'000103e9 H'00000003 H'000103eb H'00000003 H'000103ec H'00000002 H'000103ed
 H'0000000a H'000203e8 H'00000006 H'44455343
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 2 trace: 0
 H'00000002 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57132 gsn: 164 "CONTINUEB" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 57146 length: 2 trace: 0
 H'00000002 H'00000002
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 4, gsn: 204 "DICTTABINFO" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 15 trace: 0
 H'00fa0002 H'00000000 H'00000002 H'0000006e H'00000064 H'52000000 H'000103e9
 H'00000004 H'000103eb H'00000003 H'000103ec H'00000002 H'000103ed H'00000064
 H'0000ffff

// ---------------------------------------------------------------------------
// In parallel with sending the table description to other nodes we will also
// write the table description to our local file system.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 261 "FSOPENREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57132 length: 7 trace: 0
 UserReference: H'00fa0002, userPointer: H'00000000
 FileNumber[1-4]: H'00000002 H'ffffffff H'00000001 H'010401ff
 FileFlags: H'00000311 Open write only, Create new file, Truncate existing file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57165 gsn: 259 "FSOPENCONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57166 length: 3 trace: 0
 UserPointer: H'00000000
 FilePointer: 101
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 272 "FSWRITEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57165 length: 8 trace: 0
 FilePointer: 101
 UserReference: H'00fa0002, UserPointer: H'00000000
 Operation flag: H'00000011, Sync, Format=Array of pages
 varIndex: 1
 numberOfPages: 1
 pageData:  H'00000000, H'00000000

---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57177 gsn: 270 "FSWRITECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57178 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 257 "FSCLOSEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57177 length: 4 trace: 0
 FilePointer: 101
 UserReference: H'00fa0002, userPointer: H'00000000
 Flags: H'00000000, Don't remove file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57186 gsn: 255 "FSCLOSECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57187 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 261 "FSOPENREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57186 length: 7 trace: 0
 UserReference: H'00fa0002, userPointer: H'00000000
 FileNumber[1-4]: H'00000002 H'ffffffff H'00000001 H'010402ff
 FileFlags: H'00000311 Open write only, Create new file, Truncate existing file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57195 gsn: 259 "FSOPENCONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57196 length: 3 trace: 0
 UserPointer: H'00000000
 FilePointer: 102
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 272 "FSWRITEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57195 length: 8 trace: 0
 FilePointer: 102
 UserReference: H'00fa0002, UserPointer: H'00000000
 Operation flag: H'00000011, Sync, Format=Array of pages
 varIndex: 1
 numberOfPages: 1
 pageData:  H'00000000, H'00000000

---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57204 gsn: 270 "FSWRITECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57205 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 257 "FSCLOSEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57204 length: 4 trace: 0
 FilePointer: 102
 UserReference: H'00fa0002, userPointer: H'00000000
 Flags: H'00000000, Don't remove file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57218 gsn: 255 "FSCLOSECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57219 length: 1 trace: 0
 UserPointer: H'00000000

// ---------------------------------------------------------------------------
// Completed writing to our file system the table description.
// ---------------------------------------------------------------------------

---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57229 gsn: 24 "DICTTABCONF" prio: 1
s.bn: 250 "DBDICT", s.proc: 4, s.sigId: 46803 length: 2 trace: 0
 H'00000002 H'00000004

// ---------------------------------------------------------------------------
// Also the participant have completed writing the table description to file.
// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------
// Write the state UPDATE_PAGE_COUNT to schema file for the new table.
// This also contains the number of pages used for the table description.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 132 "DICT_SCHEMAREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57229 length: 7 trace: 0
 H'00010003 H'00047700 H'00000002 H'00000001 H'00000001 H'00000000 H'00000002
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 4, gsn: 132 "DICT_SCHEMAREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57229 length: 7 trace: 0
 H'00010003 H'00047700 H'00000002 H'00000001 H'00000001 H'00000000 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57229 gsn: 132 "DICT_SCHEMAREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 57234 length: 7 trace: 0
 H'00010003 H'00047700 H'00000002 H'00000001 H'00000001 H'00000000 H'00000002

// ---------------------------------------------------------------------------
// Write schema file to disk
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 261 "FSOPENREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57229 length: 7 trace: 0
 UserReference: H'00fa0002, userPointer: H'00000000
 FileNumber[1-4]: H'ffffffff H'ffffffff H'ffffffff H'01050100
 FileFlags: H'00000311 Open write only, Create new file, Truncate existing file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57238 gsn: 259 "FSOPENCONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57239 length: 3 trace: 0
 UserPointer: H'00000000
 FilePointer: 103
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 272 "FSWRITEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57238 length: 8 trace: 0
 FilePointer: 103
 UserReference: H'00fa0002, UserPointer: H'00000000
 Operation flag: H'00000011, Sync, Format=Array of pages
 varIndex: 1
 numberOfPages: 1
 pageData:  H'00000008, H'00000000

---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57247 gsn: 270 "FSWRITECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57248 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 257 "FSCLOSEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57247 length: 4 trace: 0
 FilePointer: 103
 UserReference: H'00fa0002, userPointer: H'00000000
 Flags: H'00000000, Don't remove file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57257 gsn: 255 "FSCLOSECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57258 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 261 "FSOPENREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57257 length: 7 trace: 0
 UserReference: H'00fa0002, userPointer: H'00000000
 FileNumber[1-4]: H'ffffffff H'ffffffff H'ffffffff H'01050200
 FileFlags: H'00000311 Open write only, Create new file, Truncate existing file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57267 gsn: 259 "FSOPENCONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57268 length: 3 trace: 0
 UserPointer: H'00000000
 FilePointer: 104
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 272 "FSWRITEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57267 length: 8 trace: 0
 FilePointer: 104
 UserReference: H'00fa0002, UserPointer: H'00000000
 Operation flag: H'00000011, Sync, Format=Array of pages
 varIndex: 1
 numberOfPages: 1
 pageData:  H'00000008, H'00000000

---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57279 gsn: 270 "FSWRITECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57283 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 257 "FSCLOSEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57279 length: 4 trace: 0
 FilePointer: 104
 UserReference: H'00fa0002, userPointer: H'00000000
 Flags: H'00000000, Don't remove file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57290 gsn: 255 "FSCLOSECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 57291 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 133 "DICT_SCHEMACONF" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57290 length: 1 trace: 0
 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57290 gsn: 133 "DICT_SCHEMACONF" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 57293 length: 1 trace: 0
 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57299 gsn: 133 "DICT_SCHEMACONF" prio: 1
s.bn: 250 "DBDICT", s.proc: 4, s.sigId: 46860 length: 1 trace: 0
 H'00000004

// ---------------------------------------------------------------------------
// All schema files in the system have been updated.
// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------
// Now control is given to DIH for adding the fragments needed by this table.
// We first seize a record in DIH and then we send the add table request with
// the needed table parameters.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 246 "DBDIH", r.proc: 2, gsn: 238 "DISEIZEREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57299 length: 2 trace: 0
 H'00000000 H'00fa0002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57299 gsn: 236 "DISEIZECONF" prio: 1
s.bn: 246 "DBDIH", s.proc: 2, s.sigId: 57304 length: 2 trace: 0
 H'00000000 H'00000210
---- Send ----- Signal ----------------
r.bn: 246 "DBDIH", r.proc: 2, gsn: 187 "DIADDTABREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57299 length: 6 trace: 0
 H'00000210 H'00000002 H'00000000 H'00000006 H'00000000 H'00000001

// ---------------------------------------------------------------------------
// DIH requests us to add a certain fragment replica.
// ---------------------------------------------------------------------------

---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57400 gsn: 195 "DICTFRAGSREQ" prio: 1
s.bn: 246 "DBDIH", s.proc: 2, s.sigId: 57418 length: 7 trace: 0
 H'00000000 H'00000000 H'00000000 H'00000002 H'00150040 H'00000001 H'00000002

// ---------------------------------------------------------------------------
// We add the fragment by contacting LQH through sending a LQHFRAGREQ and
// a number of LQHADDATTREQ (in this case only one since not more than 8
// attributes).
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 247 "DBLQH", r.proc: 2, gsn: 313 "LQHFRAGREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57400 length: 17 trace: 0
 H'00000000 H'00fa0002 H'00000000 H'00000000 H'00000002 H'00000001 H'00000050
 H'0000004b H'00000006 H'00000001 H'00000000 H'00000005 H'00000000 H'00000000
 H'00000001 H'00000002 H'00000000
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57400 gsn: 311 "LQHFRAGCONF" prio: 1
s.bn: 247 "DBLQH", s.proc: 2, s.sigId: 57428 length: 2 trace: 0
 H'00000000 H'00000000
---- Send ----- Signal ----------------
r.bn: 247 "DBLQH", r.proc: 2, gsn: 310 "LQHADDATTREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57400 length: 12 trace: 0
 H'00000000 H'00000005 H'00000000 H'00012255 H'00000001 H'00012255 H'00000002
 H'000a2236 H'00000003 H'000a2236 H'00000004 H'00642236
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57400 gsn: 308 "LQHADDATTCONF" prio: 1
s.bn: 247 "DBLQH", s.proc: 2, s.sigId: 57450 length: 1 trace: 0
 H'00000000

// ---------------------------------------------------------------------------
// When we have completed adding the fragment we send DINEXTNODEREQ (should
// change name to DICTFRAGSCONF) to DIH indicate we have completed the task.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 246 "DBDIH", r.proc: 2, gsn: 231 "DINEXTNODEREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57400 length: 4 trace: 0
 H'00000210 H'00000000 H'00000001 H'00000000

// ---------------------------------------------------------------------------
// We continue by performing the same task again for the next fragment replica.
// We skip this from this log since they contain no more interesting stuff.
// ---------------------------------------------------------------------------

---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 57618 gsn: 185 "DIADDTABCONF" prio: 1
s.bn: 246 "DBDIH", s.proc: 2, s.sigId: 57655 length: 2 trace: 0
 H'00000000 H'00000002

// ---------------------------------------------------------------------------
// Now that we have added all fragments DIH gives back control to DICT by
// sending DIADDTABCONF.
// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------
// It is now time to decide which global checkpoint this table will be born.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 246 "DBDIH", r.proc: 2, gsn: 499 "WAIT_GCP_REQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 57618 length: 3 trace: 0
 H'00fa0002 H'00000000 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58288 gsn: 501 "WAIT_GCP_CONF" prio: 1
s.bn: 246 "DBDIH", s.proc: 2, s.sigId: 58296 length: 2 trace: 0
 H'00000000 H'0000000c

// ---------------------------------------------------------------------------
// We can update all schema files in the system with this global checkpoint
// number. We are certain that no transaction will be performed on the table
// before this global checkpoint.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 132 "DICT_SCHEMAREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58288 length: 7 trace: 0
 H'00010003 H'00047700 H'00000002 H'00000001 H'00000001 H'0000000c H'00000003
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 4, gsn: 132 "DICT_SCHEMAREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58288 length: 7 trace: 0
 H'00010003 H'00047700 H'00000002 H'00000001 H'00000001 H'0000000c H'00000003
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58288 gsn: 132 "DICT_SCHEMAREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 58298 length: 7 trace: 0
 H'00010003 H'00047700 H'00000002 H'00000001 H'00000001 H'0000000c H'00000003

// ---------------------------------------------------------------------------
// Write schema files as usual when updating schema file state.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 261 "FSOPENREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58288 length: 7 trace: 0
 UserReference: H'00fa0002, userPointer: H'00000000
 FileNumber[1-4]: H'ffffffff H'ffffffff H'ffffffff H'01050100
 FileFlags: H'00000311 Open write only, Create new file, Truncate existing file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58304 gsn: 259 "FSOPENCONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 58305 length: 3 trace: 0
 UserPointer: H'00000000
 FilePointer: 117
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 272 "FSWRITEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58304 length: 8 trace: 0
 FilePointer: 117
 UserReference: H'00fa0002, UserPointer: H'00000000
 Operation flag: H'00000011, Sync, Format=Array of pages
 varIndex: 1
 numberOfPages: 1
 pageData:  H'00000008, H'00000000

---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58315 gsn: 270 "FSWRITECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 58316 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 257 "FSCLOSEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58315 length: 4 trace: 0
 FilePointer: 117
 UserReference: H'00fa0002, userPointer: H'00000000
 Flags: H'00000000, Don't remove file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58326 gsn: 255 "FSCLOSECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 58327 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 261 "FSOPENREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58326 length: 7 trace: 0
 UserReference: H'00fa0002, userPointer: H'00000000
 FileNumber[1-4]: H'ffffffff H'ffffffff H'ffffffff H'01050200
 FileFlags: H'00000311 Open write only, Create new file, Truncate existing file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58339 gsn: 259 "FSOPENCONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 58340 length: 3 trace: 0
 UserPointer: H'00000000
 FilePointer: 118
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 272 "FSWRITEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58339 length: 8 trace: 0
 FilePointer: 118
 UserReference: H'00fa0002, UserPointer: H'00000000
 Operation flag: H'00000011, Sync, Format=Array of pages
 varIndex: 1
 numberOfPages: 1
 pageData:  H'00000008, H'00000000

---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58348 gsn: 270 "FSWRITECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 58349 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 253 "NDBFS", r.proc: 2, gsn: 257 "FSCLOSEREQ" prio: 0
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58348 length: 4 trace: 0
 FilePointer: 118
 UserReference: H'00fa0002, userPointer: H'00000000
 Flags: H'00000000, Don't remove file
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58359 gsn: 255 "FSCLOSECONF" prio: 1
s.bn: 253 "NDBFS", s.proc: 2, s.sigId: 58360 length: 1 trace: 0
 UserPointer: H'00000000
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 133 "DICT_SCHEMACONF" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58359 length: 1 trace: 0
 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58359 gsn: 133 "DICT_SCHEMACONF" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 58364 length: 1 trace: 0
 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58359 gsn: 133 "DICT_SCHEMACONF" prio: 1
s.bn: 250 "DBDICT", s.proc: 4, s.sigId: 47846 length: 1 trace: 0
 H'00000004

// ---------------------------------------------------------------------------
// Commit the table for usage in DIH and LQH in all nodes.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 247 "DBLQH", r.proc: 2, gsn: 398 "TAB_COMMITREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58359 length: 3 trace: 0
 H'00000000 H'00fa0002 H'00000002
---- Send ----- Signal ----------------
r.bn: 246 "DBDIH", r.proc: 2, gsn: 398 "TAB_COMMITREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58359 length: 3 trace: 0
 H'00000001 H'00fa0002 H'00000002
---- Send ----- Signal ----------------
r.bn: 247 "DBLQH", r.proc: 4, gsn: 398 "TAB_COMMITREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58359 length: 3 trace: 0
 H'00000000 H'00fa0002 H'00000002
---- Send ----- Signal ----------------
r.bn: 246 "DBDIH", r.proc: 4, gsn: 398 "TAB_COMMITREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58359 length: 3 trace: 0
 H'00000001 H'00fa0002 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58359 gsn: 396 "TAB_COMMITCONF" prio: 1
s.bn: 247 "DBLQH", s.proc: 2, s.sigId: 58370 length: 3 trace: 0
 H'00000000 H'00000002 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58359 gsn: 396 "TAB_COMMITCONF" prio: 1
s.bn: 246 "DBDIH", s.proc: 2, s.sigId: 58371 length: 3 trace: 0
 H'00000001 H'00000002 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58359 gsn: 396 "TAB_COMMITCONF" prio: 1
s.bn: 247 "DBLQH", s.proc: 4, s.sigId: 47846 length: 3 trace: 0
 H'00000000 H'00000004 H'00000002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58359 gsn: 396 "TAB_COMMITCONF" prio: 1
s.bn: 246 "DBDIH", s.proc: 4, s.sigId: 47846 length: 3 trace: 0
 H'00000001 H'00000004 H'00000002

// ---------------------------------------------------------------------------
// Finally also open the table for usage from TC in all nodes.
// After this signal is received in TC it is ok to execute transactions on
// this new empty table.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 245 "DBTC", r.proc: 2, gsn: 404 "TC_SCHVERREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58359 length: 5 trace: 0
 H'00000002 H'00000001 H'00000001 H'00fa0002 H'00000000
---- Send ----- Signal ----------------
r.bn: 245 "DBTC", r.proc: 4, gsn: 404 "TC_SCHVERREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58359 length: 5 trace: 0
 H'00000002 H'00000001 H'00000001 H'00fa0002 H'00000000
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58359 gsn: 403 "TC_SCHVERCONF" prio: 1
s.bn: 245 "DBTC", s.proc: 2, s.sigId: 58376 length: 2 trace: 0
 H'00000002 H'00000000
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58359 gsn: 403 "TC_SCHVERCONF" prio: 1
s.bn: 245 "DBTC", s.proc: 4, s.sigId: 47846 length: 2 trace: 0
 H'00000002 H'00000001

// ---------------------------------------------------------------------------
// Unblock dictionary to allow for another add table.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, gsn: 444 "UNBLO_DICTREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58359 length: 1 trace: 0
 H'00fa0002
---- Send ----- Signal ----------------
r.bn: 250 "DBDICT", r.proc: 4, gsn: 444 "UNBLO_DICTREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58359 length: 1 trace: 0
 H'00fa0002

// ---------------------------------------------------------------------------
// Send the confirmation to the requesting application process.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 1 "API", r.proc: 3, gsn: 24 "DICTTABCONF" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58359 length: 3 trace: 0
 H'00047700 H'00000002 H'00000001

// ---------------------------------------------------------------------------
// Also release the connection in DIH that was previously established.
// ---------------------------------------------------------------------------

---- Send ----- Signal ----------------
r.bn: 246 "DBDIH", r.proc: 2, gsn: 234 "DIRELEASEREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, sigId: 58359 length: 3 trace: 0
 H'00000210 H'00000000 H'00fa0002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58359 gsn: 444 "UNBLO_DICTREQ" prio: 1
s.bn: 250 "DBDICT", s.proc: 2, s.sigId: 58378 length: 1 trace: 0
 H'00fa0002
---- Received - Signal ----------------
r.bn: 250 "DBDICT", r.proc: 2, sigId: 58359 gsn: 232 "DIRELEASECONF" prio: 1
s.bn: 246 "DBDIH", s.proc: 2, s.sigId: 58380 length: 1 trace: 0
 H'00000000

// ---------------------------------------------------------------------------
// Now all actions regarding this add table have completed.
// ---------------------------------------------------------------------------
