include/rpl/init_source_replica.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection master]
[connection slave]
CHANGE REPLICATION SOURCE TO SOURCE_DELAY= 7;
include/rpl/start_replica.inc
include/assert.inc [Assert that the desired delay from performance_schema is same as set in the Change replication source to command.]
include/assert.inc [Assert that the sql_delay in show replica status is same as set in the Change replication source to command.]
# Adding debug point 'sql_delay_without_timestamps' to @@GLOBAL.debug
[connection master]
CREATE TABLE t1 (a INT);
[connection slave]
include/assert.inc [Assert that the REMAINING_DELAY from performance_schema is same as SQL_Remaining_Delay in the output of show replica status.]
[connection master]
include/rpl/sync_to_replica.inc
[connection master]
[connection slave]
include/assert.inc [In the old infrastructure, the execution time of the transaction in the slave must be at least the delay]
include/assert.inc [Status should not be 'Waiting until SOURCE_DELAY seconds after source executed event']
[connection master]
INSERT INTO t1 VALUES (1);
[connection slave]
include/assert.inc [Assert that the REMAINING_DELAY from performance_schema is same as SQL_Remaining_Delay in the output of show replica status.]
[connection master]
include/rpl/sync_to_replica.inc
[connection master]
[connection slave]
include/assert.inc [In the old infrastructure, the execution time of the transaction in the slave must be at least the delay]
include/assert.inc [Status should not be 'Waiting until SOURCE_DELAY seconds after source executed event']
[connection master]
UPDATE t1 SET a=2;
[connection slave]
include/assert.inc [Assert that the REMAINING_DELAY from performance_schema is same as SQL_Remaining_Delay in the output of show replica status.]
[connection master]
include/rpl/sync_to_replica.inc
[connection master]
[connection slave]
include/assert.inc [In the old infrastructure, the execution time of the transaction in the slave must be at least the delay]
include/assert.inc [Status should not be 'Waiting until SOURCE_DELAY seconds after source executed event']
[connection master]
DELETE FROM t1 WHERE a=2;
[connection slave]
include/assert.inc [Assert that the REMAINING_DELAY from performance_schema is same as SQL_Remaining_Delay in the output of show replica status.]
[connection master]
include/rpl/sync_to_replica.inc
[connection master]
[connection slave]
include/assert.inc [In the old infrastructure, the execution time of the transaction in the slave must be at least the delay]
include/assert.inc [Status should not be 'Waiting until SOURCE_DELAY seconds after source executed event']
[connection master]
START TRANSACTION;
INSERT INTO t1 VALUES (1);
INSERT INTO t1 VALUES (2);
INSERT INTO t1 VALUES (3);
COMMIT;
[connection slave]
include/assert.inc [Assert that the REMAINING_DELAY from performance_schema is same as SQL_Remaining_Delay in the output of show replica status.]
[connection master]
include/rpl/sync_to_replica.inc
[connection master]
[connection slave]
include/assert.inc [In the old infrastructure, the execution time of the transaction in the slave must be at least the delay]
include/assert.inc [Status should not be 'Waiting until SOURCE_DELAY seconds after source executed event']
[connection master]
DROP TABLE t1;
[connection slave]
include/assert.inc [Assert that the REMAINING_DELAY from performance_schema is same as SQL_Remaining_Delay in the output of show replica status.]
[connection master]
include/rpl/sync_to_replica.inc
[connection master]
[connection slave]
include/assert.inc [In the old infrastructure, the execution time of the transaction in the slave must be at least the delay]
include/assert.inc [Status should not be 'Waiting until SOURCE_DELAY seconds after source executed event']
[connection master]
[connection slave]
# Removing debug point 'sql_delay_without_timestamps' from @@GLOBAL.debug
include/rpl/stop_applier.inc
CHANGE REPLICATION SOURCE TO SOURCE_DELAY= 0;
include/rpl/start_applier.inc
[connection master]
include/rpl/deinit.inc
