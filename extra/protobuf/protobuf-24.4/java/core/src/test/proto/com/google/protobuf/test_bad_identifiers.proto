// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Author: <EMAIL> (Jon Perlow)

// This file tests that various identifiers work as field and type names even
// though the same identifiers are used internally by the java code generator.
// LINT: LEGACY_NAMES

syntax = "proto2";

package io_protocol_tests;

option java_generic_services = true;  // auto-added
option java_package = "com.google.protobuf";
option java_outer_classname = "TestBadIdentifiersProto";

// Message with field names using underscores that conflict with accessors in
// the base message class in java. See kForbiddenWordList in
// src/google/protobuf/compiler/java/java_helpers.cc
message ForbiddenWordsUnderscoreMessage {
  // java.lang.Object
  optional bool class = 1;
  // com.google.protobuf.MessageLiteOrBuilder
  optional bool default_instance_for_type = 2;
  // com.google.protobuf.MessageLite
  optional bool parser_for_type = 3;
  optional bool serialized_size = 4;
  // com.google.protobuf.MessageOrBuilder
  optional bool all_fields = 5;
  optional bool descriptor_for_type = 6;
  optional bool initialization_error_string = 7;
  optional bool unknown_fields = 8;
  // obsolete. kept for backwards compatibility of generated code
  optional bool cached_size = 9;
}

// Message with field names using leading underscores that conflict with
// accessors in the base message class in java. See kForbiddenWordList in
// src/google/protobuf/compiler/java/java_helpers.cc
message ForbiddenWordsLeadingUnderscoreMessage {
  // java.lang.Object
  optional bool _class = 1;
  // com.google.protobuf.MessageLiteOrBuilder
  optional bool _default_instance_for_type = 2;
  // com.google.protobuf.MessageLite
  optional bool _parser_for_type = 3;
  optional bool _serialized_size = 4;
  // com.google.protobuf.MessageOrBuilder
  optional bool _all_fields = 5;
  optional bool _descriptor_for_type = 6;
  optional bool _initialization_error_string = 7;
  // TODO(b/219045204): re-enable
  // optional bool _unknown_fields = 8;
  // obsolete. kept for backwards compatibility of generated code
  optional bool _cached_size = 9;
}

// Message with field names in camel case that conflict with accessors in the
// base message class in java. See kForbiddenWordList in
// src/google/protobuf/compiler/java/java_helpers.cc
message ForbiddenWordsCamelMessage {
  // java.lang.Object
  optional bool class = 1;
  // com.google.protobuf.MessageLiteOrBuilder
  optional bool defaultInstanceForType = 2;
  // com.google.protobuf.MessageLite
  optional bool serializedSize = 3;
  optional bool parserForType = 4;
  // com.google.protobuf.MessageOrBuilder:
  optional bool initializationErrorString = 5;
  optional bool descriptorForType = 6;
  optional bool allFields = 7;
  // TODO(b/219045204): re-enable
  // optional bool unknownFields = 8;
  // obsolete. kept for backwards compatibility of generated code
  optional bool cachedSize = 9;
}

message Descriptor {
  option no_standard_descriptor_accessor = true;

  optional string descriptor = 1;
  message NestedDescriptor {
    option no_standard_descriptor_accessor = true;

    optional string descriptor = 1;
  }
  optional NestedDescriptor nested_descriptor = 2;
  enum NestedEnum {
    UNKNOWN = 0;
    FOO = 1;
  }
}

message Parser {
  enum ParserEnum {
    UNKNOWN = 0;
    PARSER = 1;
  }
  optional ParserEnum parser = 1;
}

message Deprecated {
  enum TestEnum {
    UNKNOWN = 0;
    FOO = 1;

    // Test if @Deprecated annotation conflicts with Deprecated message name.
    BAR = 2 [deprecated = true];
  }

  optional int32 field1 = 1 [deprecated = true];
  optional TestEnum field2 = 2 [deprecated = true];
  optional ForbiddenWordsUnderscoreMessage field3 = 3 [deprecated = true];
}

message Override {
  optional int32 override = 1;
}

message Object {
  optional int32 object = 1;
  optional string string_object = 2;
}

message String {
  optional string string = 1;
}

message Integer {
  optional int32 integer = 1;
}

message Long {
  optional int32 long = 1;
}

message Float {
  optional float float = 1;
}

message Double {
  optional double double = 1;
}

service TestConflictingMethodNames {
  rpc Override(ForbiddenWordsUnderscoreMessage)
      returns (ForbiddenWordsUnderscoreMessage);
}

message TestConflictingFieldNames {
  // TODO(b/261750190) Remove these tests once this behavior is removed.
  option deprecated_legacy_json_field_conflicts = true;

  enum TestEnum {
    UNKNOWN = 0;
    FOO = 1;
  }
  message ForbiddenWordsUnderscoreMessage {}
  repeated int32 int32_field = 1;
  repeated TestEnum enum_field = 2;
  repeated string string_field = 3;
  repeated bytes bytes_field = 4;
  repeated ForbiddenWordsUnderscoreMessage message_field = 5;

  optional int32 int32_field_count = 11;
  optional TestEnum enum_field_count = 12;
  optional string string_field_count = 13;
  optional bytes bytes_field_count = 14;
  optional ForbiddenWordsUnderscoreMessage message_field_count = 15;

  repeated int32 Int32Field = 21;                              // NO_PROTO3
  repeated TestEnum EnumField = 22;                            // NO_PROTO3
  repeated string StringField = 23;                            // NO_PROTO3
  repeated bytes BytesField = 24;                              // NO_PROTO3
  repeated ForbiddenWordsUnderscoreMessage MessageField = 25;  // NO_PROTO3

  // This field conflicts with "int32_field" as they both generate
  // the method getInt32FieldList().
  required int32 int32_field_list = 31;  // NO_PROTO3

  // These field pairs have the same Java converted name
  optional string field_name = 32;   // NO_PROTO3
  optional string field__name = 33;  // NO_PROTO3
  optional int32 _2conflict = 34;    // NO_PROTO3
  optional int32 __2conflict = 35;

  extensions 1000 to max;  // NO_PROTO3

  repeated int64 int64_field = 41;
  extend TestConflictingFieldNames {  // NO_PROTO3
    // We don't generate accessors for extensions so the following extension
    // fields don't conflict with the repeated field "int64_field".
    optional int64 int64_field_count = 1001;  // NO_PROTO3
    optional int64 int64_field_list = 1002;   // NO_PROTO3
  }                                           // NO_PROTO3
}

message TestMapField {
  message MapField {}
  message Pair {}
  message Message {}

  map<int32, int32> map_field = 1;
}

message TestLeadingNumberFields {
  optional int32 _30day_impressions = 1;
  repeated string _60day_impressions = 2;

  optional string __2_underscores = 3;
  repeated string __2repeated_underscores = 4;

  optional int32 _32 = 32;
  repeated int64 _64 = 64;
}
