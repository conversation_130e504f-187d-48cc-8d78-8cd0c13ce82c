
#########################################################################
# 0) Setup group of 2 members (M1 and M2).
#########################################################################

include/group_replication.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection server1]

#########################################################################
# 1) Simulate a higher version on M1.
#########################################################################

[connection server1]
SET @debug_saved= @@GLOBAL.DEBUG;
SET @@GLOBAL.DEBUG= '+d,group_replication_compatibility_higher_major_version';
include/start_and_bootstrap_group_replication.inc

#########################################################################
# 2) Try to join M2 to the group.
#########################################################################

[connection server2]
# restart:--group_replication_local_address=GROUP_REPLICATION_LOCAL_ADDRESS --group_replication_group_seeds=GROUP_REPLICATION_GROUP_SEEDS --group-replication-group-name=GROUP_REPLICATION_GROUP_NAME --group_replication_start_on_boot=1 --loose-debug=+d,group_replication_wait_before_leave_on_error
include/rpl/reconnect.inc

#########################################################################
# 3) M2 super_read_only mode shall be OFF and in OFFLINE state
#    (since it failed to join the group).
#########################################################################

[connection server2]
SET DEBUG_SYNC= "now WAIT_FOR signal.wait_leave_process";
SET DEBUG_SYNC= "now SIGNAL signal.continue_leave_process";
include/gr_wait_for_member_state.inc
include/assert_grep.inc [GR reported expected incompatibility on member version]
[connection server1]
include/rpl/gr_wait_for_number_of_members.inc

#########################################################################
# 4) Change the exit_state_action to ABORT_SERVER. Try to join M2 to the
#    group again.
#########################################################################

[connection server2]
# restart:--group_replication_local_address=GROUP_REPLICATION_LOCAL_ADDRESS --group_replication_group_seeds=GROUP_REPLICATION_GROUP_SEEDS --group-replication-group-name=GROUP_REPLICATION_GROUP_NAME --group_replication_start_on_boot=1 --group_replication_exit_state_action=ABORT_SERVER

#########################################################################
# 5) M2 super_read_only mode shall be OFF and in OFFLINE state
#    (since it failed to join the group).
#########################################################################

[connection server1]
include/rpl/gr_wait_for_number_of_members.inc
include/rpl/reconnect.inc
[connection server2]
include/assert_grep.inc [GR reported expected incompatibility on member version]

#########################################################################
# 6) Restart M2 again without group_replication_start_on_boot enabled.
#    The server should start normally and be able to join the group.
#########################################################################

[connection server1]
SET @@GLOBAL.DEBUG = @debug_saved;
include/stop_group_replication.inc
include/start_and_bootstrap_group_replication.inc
[connection server2]
# restart:--group_replication_local_address=GROUP_REPLICATION_LOCAL_ADDRESS --group_replication_group_seeds=GROUP_REPLICATION_GROUP_SEEDS --group-replication-group-name=GROUP_REPLICATION_GROUP_NAME
include/rpl/reconnect.inc
include/start_group_replication.inc
include/rpl/gr_wait_for_number_of_members.inc
[connection server1]
include/rpl/gr_wait_for_number_of_members.inc

#########################################################################
# 7) Cleanup.
#########################################################################

include/group_replication_end.inc
