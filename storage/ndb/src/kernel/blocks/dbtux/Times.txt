ordered index performance
=========================

"mc02" 2x1700 MHz linux-2.4.9 gcc-2.96 -O3 one db-node

case a: maintenance: index on Unsigned
testOIBasic -case u -table 1 -index 2 -fragtype small -threads 10 -rows 100000 -subloop 1 -nologging

case b: maintenance: index on Varchar(5) + <PERSON><PERSON><PERSON><PERSON>(5) + <PERSON><PERSON><PERSON><PERSON>(20) + Unsigned
testOIBasic -case u -table 2 -index 5 -fragtype small -threads 10 -rows 100000 -subloop 1 -nologging

case c: full scan: index on PK Unsigned
testOIBasic -case v -table 1 -index 1 -fragtype small -threads 10 -rows 100000 -subloop 1 -nologging

case d: scan 1 tuple via EQ: index on PK Unsigned
testOIBasic -case w -table 1 -index 1 -fragtype small -threads 10 -rows 100000 -samples 50000 -subloop 1 -nologging -v2

a, b
1 million rows, pk update without index, pk update with index
shows ms / 1000 rows for each and pct overhead

c
1 million rows, index on PK, full table scan, full index scan
shows ms / 1000 rows for each and index time overhead

d
1 million rows, index on PK, read table via each pk, scan index for each pk
shows ms / 1000 rows for each and index time overhead
samples 10% of all PKs (100,000 pk reads, 100,000 scans)

the "pct" values are from more accurate total times (not shown)
comments [ ... ] are after the case

040616          mc02/a  40 ms   87 ms   114 pct
                mc02/b  51 ms  128 ms   148 pct

optim 1         mc02/a  38 ms   85 ms   124 pct
                mc02/b  51 ms  123 ms   140 pct

optim 2         mc02/a  41 ms   80 ms    96 pct
                mc02/b  51 ms  117 ms   128 pct

optim 3         mc02/a  43 ms   80 ms    85 pct
                mc02/b  54 ms  118 ms   117 pct

optim 4         mc02/a  42 ms   80 ms    87 pct
                mc02/b  51 ms  119 ms   129 pct

optim 5         mc02/a  43 ms   77 ms    77 pct
                mc02/b  54 ms  118 ms   117 pct

optim 6         mc02/a  42 ms   70 ms    66 pct
                mc02/b  53 ms  109 ms   105 pct

optim 7         mc02/a  42 ms   69 ms    61 pct
                mc02/b  52 ms  106 ms   101 pct

optim 8         mc02/a  42 ms   69 ms    62 pct
                mc02/b  54 ms  104 ms    92 pct

optim 9         mc02/a  43 ms   67 ms    54 pct
                mc02/b  53 ms  102 ms    91 pct

optim 10        mc02/a  44 ms   65 ms    46 pct
                mc02/b  53 ms   88 ms    66 pct

optim 11        mc02/a  43 ms   63 ms    46 pct
                mc02/b  52 ms   86 ms    63 pct

optim 12        mc02/a  38 ms   55 ms    43 pct
                mc02/b  47 ms   77 ms    63 pct
                mc02/c  10 ms   14 ms    47 pct
                mc02/d 176 ms  281 ms    59 pct

optim 13        mc02/a  40 ms   57 ms    42 pct
                mc02/b  47 ms   77 ms    61 pct
                mc02/c   9 ms   13 ms    50 pct
                mc02/d 170 ms  256 ms    50 pct

optim 13        mc02/a  39 ms   59 ms    50 pct
                mc02/b  47 ms   77 ms    61 pct
                mc02/c   9 ms   12 ms    44 pct
                mc02/d 246 ms  289 ms    17 pct

[ after wl-1884 store all-NULL keys (the tests have pctnull=10 per column) ]
[ case d: bug in testOIBasic killed PK read performance ]

optim 14        mc02/a  41 ms   60 ms    44 pct
                mc02/b  46 ms   81 ms    73 pct
                mc02/c   9 ms   13 ms    37 pct
                mc02/d 242 ms  285 ms    17 pct

[ case b: do long keys suffer from many subroutine calls? ]
[ case d: bug in testOIBasic killed PK read performance ]

none            mc02/a  35 ms   60 ms    71 pct
                mc02/b  42 ms   75 ms    76 pct
                mc02/c   5 ms   12 ms   106 pct
                mc02/d 165 ms  238 ms    44 pct

[ johan re-installed mc02 as fedora gcc-3.3.2, tux uses more C++ stuff than tup]

charsets        mc02/a  35 ms   60 ms    71 pct
                mc02/b  42 ms   84 ms    97 pct
                mc02/c   5 ms   12 ms   109 pct
                mc02/d 190 ms  236 ms    23 pct

[ case b: TUX can no longer use pointers to TUP data ]

optim 15        mc02/a  34 ms   60 ms    72 pct
                mc02/b  42 ms   85 ms   100 pct
                mc02/c   5 ms   12 ms   110 pct
                mc02/d 178 ms  242 ms    35 pct

[ corrected wasted space in index node ]

optim 16        mc02/a  34 ms   53 ms    53 pct
                mc02/b  42 ms   75 ms    75 pct

[ binary search of bounding node when adding entry ]

none            mc02/a  35 ms   53 ms    51 pct
                mc02/b  42 ms   75 ms    76 pct

[ rewrote treeAdd / treeRemove ]

optim 17        mc02/a  35 ms   52 ms    49 pct
                mc02/b  43 ms   75 ms    75 pct

[ allow slack (2) in interior nodes - almost no effect?? ]

wl-1942         mc02/a  35 ms   52 ms    49 pct
                mc02/b  42 ms   75 ms    76 pct

before          mc02/c   5 ms   13 ms   126 pct
                mc02/d 134 ms  238 ms    78 pct

after           mc02/c   5 ms   10 ms    70 pct
                mc02/d 178 ms  242 ms    69 pct

[ prelim performance fix for max batch size 16 -> 992 ]

wl-2066         mc02/c   5 ms   10 ms    87 pct
before          mc02/d 140 ms  237 ms    69 pct

wl-2066         mc02/c   5 ms   10 ms    69 pct
after           mc02/d 150 ms  229 ms    52 pct

[ wl-2066 = remove ACC storage, use TUX test to see effect ]

vim: set et:
