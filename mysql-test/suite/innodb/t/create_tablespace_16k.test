#
# WL#6205 - A series of tests to show the correct behavior for
# CREATE TABLESPACE and associated SQL statements for 16k page size.
#

--echo #
--echo # CREATE TABLESPACE related tests for 16k page sizes.
--echo #

SET DEFAULT_STORAGE_ENGINE=InnoDB;
LET $MYSQLD_DATADIR = `select @@datadir`;
LET $INNODB_PAGE_SIZE = `select @@innodb_page_size`;

--echo # Strict-mode has no effect on CREATE TABLESPACE. But it does affect
--echo # whether an invalid KEY_BLOCK_SIZE is rejected or adjusted.
SHOW VARIABLES LIKE 'innodb_strict_mode';
SHOW VARIABLES LIKE 'innodb_file_per_table';

--echo #
--echo # Create a tablespace with compressed page sizes that can match
--echo # innodb-page-size.
--echo #
CREATE TABLESPACE s_1k ADD DATAFILE 's_1k.ibd' FILE_BLOCK_SIZE=1k;
CREATE TABLESPACE s_2k ADD DATAFILE 's_2k.ibd' FILE_BLOCK_SIZE=2k;
CREATE TABLESPACE s_4k ADD DATAFILE 's_4k.ibd' FILE_BLOCK_SIZE=4k;
CREATE TABLESPACE s_8k ADD DATAFILE 's_8k.ibd' FILE_BLOCK_SIZE=8k;
CREATE TABLESPACE s_16k ADD DATAFILE 's_16k.ibd' FILE_BLOCK_SIZE=16k;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLESPACE s_32k ADD DATAFILE 's_32k.ibd' FILE_BLOCK_SIZE=32k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLESPACE s_64k ADD DATAFILE 's_64k.ibd' FILE_BLOCK_SIZE=64k;
SHOW WARNINGS;
--source suite/innodb/include/show_i_s_tablespaces.inc

--echo #
--echo # Try to put a temporary table into a non-temporary compressed tablespace
--echo #
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TEMPORARY TABLE t_temp_zip (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=1 TABLESPACE s_1k;
SHOW WARNINGS;

--echo #
--echo # Add tables to the 1K tablespace.
--echo #
CREATE TABLE t_zip1k_in_1k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=1 TABLESPACE s_1k;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip2k_in_1k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=2 TABLESPACE s_1k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip4k_in_1k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=4 TABLESPACE s_1k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip8k_in_1k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=8 TABLESPACE s_1k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip16k_in_1k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=16 TABLESPACE s_1k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip32k_in_1k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=32 TABLESPACE s_1k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_red_in_1k (a int, b text) ROW_FORMAT=Redundant TABLESPACE s_1k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_cmp_in_1k (a int, b text) ROW_FORMAT=Compact TABLESPACE s_1k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_dyn_in_1k (a int, b text) ROW_FORMAT=Dynamic TABLESPACE s_1k;
SHOW WARNINGS;

--echo #
--echo # Add tables to the 2K tablespace.
--echo #
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip1k_in_2k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=1 TABLESPACE s_2k;
SHOW WARNINGS;
CREATE TABLE t_zip2k_in_2k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=2 TABLESPACE s_2k;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip4k_in_2k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=4 TABLESPACE s_2k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip8k_in_2k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=8 TABLESPACE s_2k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip16k_in_2k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=16 TABLESPACE s_2k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip32k_in_2k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=32 TABLESPACE s_2k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_red_in_2k (a int, b text) ROW_FORMAT=Redundant TABLESPACE s_2k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_cmp_in_2k (a int, b text) ROW_FORMAT=Compact TABLESPACE s_2k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_dyn_in_2k (a int, b text) ROW_FORMAT=Dynamic TABLESPACE s_2k;
SHOW WARNINGS;

--echo #
--echo # Add tables to the 4K tablespace.
--echo #
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip1k_in_4k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=1 TABLESPACE s_4k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip2k_in_4k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=2 TABLESPACE s_4k;
SHOW WARNINGS;
CREATE TABLE t_zip4k_in_4k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=4 TABLESPACE s_4k;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip8k_in_4k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=8 TABLESPACE s_4k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip16k_in_4k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=16 TABLESPACE s_4k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip32k_in_4k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=32 TABLESPACE s_4k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_red_in_4k (a int, b text) ROW_FORMAT=Redundant TABLESPACE s_4k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_cmp_in_4k (a int, b text) ROW_FORMAT=Compact TABLESPACE s_4k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_dyn_in_4k (a int, b text) ROW_FORMAT=Dynamic TABLESPACE s_4k;
SHOW WARNINGS;

--echo #
--echo # Add tables to the 8K tablespace.
--echo #
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip1k_in_8k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=1 TABLESPACE s_8k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip2k_in_8k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=2 TABLESPACE s_8k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip4k_in_8k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=4 TABLESPACE s_8k;
SHOW WARNINGS;
CREATE TABLE t_zip8k_in_8k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=8 TABLESPACE s_8k;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip16k_in_8k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=16 TABLESPACE s_8k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip32k_in_8k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=32 TABLESPACE s_8k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_red_in_8k (a int, b text) ROW_FORMAT=redundant TABLESPACE s_8k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_com_in_8k (a int, b text) ROW_FORMAT=compact TABLESPACE s_8k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_dyn_in_8k (a int, b text) ROW_FORMAT=dynamic TABLESPACE s_8k;
SHOW WARNINGS;

--echo #
--echo # Add tables to the 16K tablespace.
--echo #
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip1k_in_16k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=1 TABLESPACE s_16k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip2k_in_16k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=2 TABLESPACE s_16k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip4k_in_16k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=4 TABLESPACE s_16k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip8k_in_16k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=8 TABLESPACE s_16k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip16k_in_16k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=16 TABLESPACE s_16k;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
CREATE TABLE t_zip32k_in_16k (a int, b text) ROW_FORMAT=Compressed KEY_BLOCK_SIZE=32 TABLESPACE s_16k;
SHOW WARNINGS;
CREATE TABLE t_red_in_16k (a int, b text) ROW_FORMAT=redundant TABLESPACE s_16k;
CREATE TABLE t_com_in_16k (a int, b text) ROW_FORMAT=compact TABLESPACE s_16k;
CREATE TABLE t_dyn_in_16k (a int, b text) ROW_FORMAT=dynamic TABLESPACE s_16k;

--echo # Add data to the existing Tables
INSERT INTO t_zip1k_in_1k VALUES (1,'a'),(2,'b'),(3,'c');
INSERT INTO t_zip2k_in_2k VALUES (1,'a'),(2,'b'),(3,'c');
INSERT INTO t_zip4k_in_4k VALUES (1,'a'),(2,'b'),(3,'c');
INSERT INTO t_zip8k_in_8k VALUES (1,'a'),(2,'b'),(3,'c');
INSERT INTO t_red_in_16k VALUES (1,'a'),(2,'b'),(3,'c');
INSERT INTO t_com_in_16k VALUES (1,'a'),(2,'b'),(3,'c');
INSERT INTO t_dyn_in_16k VALUES (1,'a'),(2,'b'),(3,'c');

--echo # Restart mysqld
--source include/restart_mysqld.inc

--echo #
--echo # Try to drop these tablespaces which are not empty
--echo #
--error ER_TABLESPACE_IS_NOT_EMPTY
DROP TABLESPACE s_1k; 
--error ER_TABLESPACE_IS_NOT_EMPTY
DROP TABLESPACE s_2k; 
--error ER_TABLESPACE_IS_NOT_EMPTY
DROP TABLESPACE s_4k; 
--error ER_TABLESPACE_IS_NOT_EMPTY
DROP TABLESPACE s_8k; 
--error ER_TABLESPACE_IS_NOT_EMPTY
DROP TABLESPACE s_16k; 

--echo #
--echo # Add more data to the existing Tables
--echo #
INSERT INTO t_zip1k_in_1k VALUES (4,'d');
INSERT INTO t_zip2k_in_2k VALUES (4,'d');
INSERT INTO t_zip4k_in_4k VALUES (4,'d');
INSERT INTO t_zip8k_in_8k VALUES (4,'d');
INSERT INTO t_red_in_16k VALUES (4,'d');
INSERT INTO t_com_in_16k VALUES (4,'d');
INSERT INTO t_dyn_in_16k VALUES (4,'d');

--echo #
--echo # Create an empty copy of each table using LIKE
--echo #
CREATE TABLE t_zip1k_in_1k_like LIKE t_zip1k_in_1k;
CREATE TABLE t_zip2k_in_2k_like LIKE t_zip2k_in_2k;
CREATE TABLE t_zip4k_in_4k_like LIKE t_zip4k_in_4k;
CREATE TABLE t_zip8k_in_8k_like LIKE t_zip8k_in_8k;
CREATE TABLE t_red_in_16k_like LIKE t_red_in_16k;
CREATE TABLE t_com_in_16k_like LIKE t_com_in_16k;
CREATE TABLE t_dyn_in_16k_like LIKE t_dyn_in_16k;

--echo #
--echo # Create a full copy of each table using AS
--echo #
CREATE TABLE t_zip1k_in_1k_as TABLESPACE=s_1k KEY_BLOCK_SIZE=1 AS (SELECT * FROM t_zip1k_in_1k);
CREATE TABLE t_zip2k_in_2k_as TABLESPACE=s_2k KEY_BLOCK_SIZE=2 AS (SELECT * FROM t_zip2k_in_2k);
CREATE TABLE t_zip4k_in_4k_as TABLESPACE=s_4k KEY_BLOCK_SIZE=4 AS (SELECT * FROM t_zip4k_in_4k);
CREATE TABLE t_zip8k_in_8k_as TABLESPACE=s_8k KEY_BLOCK_SIZE=8 AS (SELECT * FROM t_zip8k_in_8k);
CREATE TABLE t_red_in_16k_as TABLESPACE=s_16k ROW_FORMAT=redundant AS (SELECT * FROM t_red_in_16k);
CREATE TABLE t_com_in_16k_as TABLESPACE=s_16k ROW_FORMAT=compact AS (SELECT * FROM t_com_in_16k);
CREATE TABLE t_dyn_in_16k_as TABLESPACE=s_16k ROW_FORMAT=dynamic AS (SELECT * FROM t_dyn_in_16k);

--echo #
--echo # Create a copy of the 1k table, moving it to another tablespace with a different
--echo # FILE_BLOCK_SIZE, while changing the KEY_BLOCK_SIZE.
--echo #
CREATE TABLE t_zip2k_in_2k_from_1k TABLESPACE s_2k KEY_BLOCK_SIZE=2 AS (SELECT * FROM t_zip1k_in_1k);
CREATE TABLE t_zip4k_in_4k_from_1k TABLESPACE s_4k KEY_BLOCK_SIZE=4 AS (SELECT * FROM t_zip1k_in_1k);
CREATE TABLE t_zip8k_in_8k_from_1k TABLESPACE s_8k KEY_BLOCK_SIZE=8 AS (SELECT * FROM t_zip1k_in_1k);
CREATE TABLE t_red_in_16k_from_1k TABLESPACE s_16k ROW_FORMAT=redundant AS (SELECT * FROM t_zip1k_in_1k);
CREATE TABLE t_com_in_16k_from_1k TABLESPACE s_16k ROW_FORMAT=compact AS (SELECT * FROM t_zip1k_in_1k);
CREATE TABLE t_dyn_in_16k_from_1k TABLESPACE s_16k ROW_FORMAT=dynamic AS (SELECT * FROM t_zip1k_in_1k);

--echo #
--echo # Create a tablespace that will be deleted while the engine is not
--echo # running to show that it can still be dropped.
--echo #
CREATE TABLESPACE s_missing ADD DATAFILE 'delete_me.ibd';
CREATE TABLE t_missing (a int) TABLESPACE=s_missing;

--source suite/innodb/include/show_i_s_tablespaces.inc
--source suite/innodb/include/show_i_s_tables.inc
--echo # Directory of MYSQLD_DATADIR/
--list_files $MYSQLD_DATADIR/ *.ibd
--echo # Directory of MYSQLD_DATADIR/test/
--list_files $MYSQLD_DATADIR/test/ *.ibd

--echo #
--echo # Stop the server.
--source include/shutdown_mysqld.inc

--echo # Delete a general tablespace datafile.
--remove_file $MYSQLD_DATADIR/delete_me.ibd
--source include/start_mysqld.inc

--echo # Make sure that the missing tablespace can be dropped and
--echo # everything else is OK.
--echo #

--source suite/innodb/include/show_i_s_tablespaces.inc
--source suite/innodb/include/show_i_s_tables.inc
--echo # Directory of MYSQLD_DATADIR/
--list_files $MYSQLD_DATADIR/ *.ibd
--echo # Directory of MYSQLD_DATADIR/test/
--list_files $MYSQLD_DATADIR/test/ *.ibd

--echo #
--echo # Clean-up tables we no longer need
--echo #

--error ER_TABLESPACE_IS_NOT_EMPTY
DROP TABLESPACE s_missing;
DROP TABLE t_missing;
DROP TABLESPACE s_missing;

DROP TABLE t_zip1k_in_1k_like;
DROP TABLE t_zip2k_in_2k_like;
DROP TABLE t_zip4k_in_4k_like;
DROP TABLE t_zip8k_in_8k_like;
DROP TABLE t_red_in_16k_like;
DROP TABLE t_com_in_16k_like;
DROP TABLE t_dyn_in_16k_like;
DROP TABLE t_zip1k_in_1k_as;
DROP TABLE t_zip2k_in_2k_as;
DROP TABLE t_zip4k_in_4k_as;
DROP TABLE t_zip8k_in_8k_as;
DROP TABLE t_red_in_16k_as;
DROP TABLE t_com_in_16k_as;
DROP TABLE t_dyn_in_16k_as;
DROP TABLE t_zip2k_in_2k_from_1k;
DROP TABLE t_zip4k_in_4k_from_1k;
DROP TABLE t_zip8k_in_8k_from_1k;
DROP TABLE t_red_in_16k_from_1k;
DROP TABLE t_com_in_16k_from_1k;
DROP TABLE t_dyn_in_16k_from_1k;

CHECK TABLE t_zip1k_in_1k;
CHECK TABLE t_zip2k_in_2k;
CHECK TABLE t_zip4k_in_4k;
CHECK TABLE t_zip8k_in_8k;
CHECK TABLE t_red_in_16k;
CHECK TABLE t_com_in_16k;
CHECK TABLE t_dyn_in_16k;

DROP TABLE t_zip1k_in_1k;
DROP TABLE t_zip2k_in_2k;
DROP TABLE t_zip4k_in_4k;
DROP TABLE t_zip8k_in_8k;
DROP TABLE t_red_in_16k;
DROP TABLE t_com_in_16k;
DROP TABLE t_dyn_in_16k;

--echo #
--echo # Create compressed tables explicitly as file_per_table tablespaces.
--echo #
CREATE TABLE t_zip1k_as_file_per_table (a int, b text) TABLESPACE=innodb_file_per_table ROW_FORMAT=compressed KEY_BLOCK_SIZE=1;
CREATE TABLE t_zip2k_as_file_per_table (a int, b text) TABLESPACE=innodb_file_per_table ROW_FORMAT=compressed KEY_BLOCK_SIZE=2;
CREATE TABLE t_zip4k_as_file_per_table (a int, b text) TABLESPACE=innodb_file_per_table ROW_FORMAT=compressed KEY_BLOCK_SIZE=4;
CREATE TABLE t_zip8k_as_file_per_table (a int, b text) TABLESPACE=innodb_file_per_table ROW_FORMAT=compressed KEY_BLOCK_SIZE=8;
CREATE TABLE t_zip16k_as_file_per_table (a int, b text) TABLESPACE=innodb_file_per_table ROW_FORMAT=compressed KEY_BLOCK_SIZE=16;

--echo #
--echo # Create compressed tables in remote locations using file-per-table.
--echo #
--replace_result $MYSQL_TMP_DIR MYSQL_TMP_DIR
eval CREATE TABLE t_zip1k_as_remote (a int, b text) KEY_BLOCK_SIZE=1 TABLESPACE=innodb_file_per_table DATA DIRECTORY='$MYSQL_TMP_DIR';
--replace_result $MYSQL_TMP_DIR MYSQL_TMP_DIR
eval CREATE TABLE t_zip2k_as_remote (a int, b text) KEY_BLOCK_SIZE=2 TABLESPACE=innodb_file_per_table DATA DIRECTORY='$MYSQL_TMP_DIR';
--replace_result $MYSQL_TMP_DIR MYSQL_TMP_DIR
eval CREATE TABLE t_zip4k_as_remote (a int, b text) KEY_BLOCK_SIZE=4 TABLESPACE=innodb_file_per_table DATA DIRECTORY='$MYSQL_TMP_DIR';
--replace_result $MYSQL_TMP_DIR MYSQL_TMP_DIR
eval CREATE TABLE t_zip8k_as_remote (a int, b text) KEY_BLOCK_SIZE=8 TABLESPACE=innodb_file_per_table DATA DIRECTORY='$MYSQL_TMP_DIR';
--replace_result $MYSQL_TMP_DIR MYSQL_TMP_DIR
eval CREATE TABLE t_zip16k_as_remote (a int, b text) KEY_BLOCK_SIZE=16 TABLESPACE=innodb_file_per_table DATA DIRECTORY='$MYSQL_TMP_DIR';

--source suite/innodb/include/show_i_s_tablespaces.inc
--source suite/innodb/include/show_i_s_tables.inc
--echo # MYSQLD_DATADIR/
--list_files $MYSQLD_DATADIR/ *.ibd
--echo # MYSQLD_DATADIR/test/
--list_files $MYSQLD_DATADIR/test/ *.ibd

--echo #
--echo # These file_per_table tables cannot be moved to a general tablespace if the
--echo # FILE_BLOCK_SIZE does not match the KEY_BLOCK_SIZE or if the tablespace is
--echo # not compressed.
--echo #
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip2k_as_file_per_table` TABLESPACE=`s_1k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip4k_as_file_per_table` TABLESPACE=`s_1k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip8k_as_file_per_table` TABLESPACE=`s_1k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip16k_as_file_per_table` TABLESPACE=`s_1k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip2k_as_remote` TABLESPACE=`s_1k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip4k_as_remote` TABLESPACE=`s_1k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip8k_as_remote` TABLESPACE=`s_1k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip16k_as_remote` TABLESPACE=`s_1k`;
SHOW WARNINGS;

--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip1k_as_file_per_table` TABLESPACE=`s_2k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip4k_as_file_per_table` TABLESPACE=`s_2k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip8k_as_file_per_table` TABLESPACE=`s_2k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip16k_as_file_per_table` TABLESPACE=`s_2k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip1k_as_remote` TABLESPACE=`s_2k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip4k_as_remote` TABLESPACE=`s_2k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip8k_as_remote` TABLESPACE=`s_2k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip16k_as_remote` TABLESPACE=`s_2k`;
SHOW WARNINGS;

--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip1k_as_file_per_table` TABLESPACE=`s_4k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip2k_as_file_per_table` TABLESPACE=`s_4k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip8k_as_file_per_table` TABLESPACE=`s_4k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip16k_as_file_per_table` TABLESPACE=`s_4k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip1k_as_remote` TABLESPACE=`s_4k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip2k_as_remote` TABLESPACE=`s_4k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip8k_as_remote` TABLESPACE=`s_4k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip16k_as_remote` TABLESPACE=`s_4k`;
SHOW WARNINGS;

--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip1k_as_file_per_table` TABLESPACE=`s_8k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip2k_as_file_per_table` TABLESPACE=`s_8k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip4k_as_file_per_table` TABLESPACE=`s_8k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip16k_as_file_per_table` TABLESPACE=`s_8k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip1k_as_remote` TABLESPACE=`s_8k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip2k_as_remote` TABLESPACE=`s_8k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip4k_as_remote` TABLESPACE=`s_8k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip16k_as_remote` TABLESPACE=`s_8k`;
SHOW WARNINGS;

--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip1k_as_file_per_table` TABLESPACE=`s_16k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip2k_as_file_per_table` TABLESPACE=`s_16k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip4k_as_file_per_table` TABLESPACE=`s_16k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip8k_as_file_per_table` TABLESPACE=`s_16k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip16k_as_file_per_table` TABLESPACE=`s_16k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip1k_as_remote` TABLESPACE=`s_16k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip2k_as_remote` TABLESPACE=`s_16k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip4k_as_remote` TABLESPACE=`s_16k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip8k_as_remote` TABLESPACE=`s_16k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip16k_as_remote` TABLESPACE=`s_16k`;
SHOW WARNINGS;

DROP TABLE `t_zip16k_as_remote`;

--echo #
--echo # These explicit file_per_table tables can be moved to a general tablespace
--echo # if FILE_BLOCK_SIZE matches KEY_BLOCK_SIZE and the tablespace is compressed.
--echo #
ALTER TABLE `t_zip1k_as_file_per_table` TABLESPACE=`s_1k`, RENAME TO `t_zip1k_in_s_1k`;
ALTER TABLE `t_zip2k_as_file_per_table` TABLESPACE=`s_2k`, RENAME TO `t_zip2k_in_s_2k`;
ALTER TABLE `t_zip4k_as_file_per_table` TABLESPACE=`s_4k`, RENAME TO `t_zip4k_in_s_4k`;
ALTER TABLE `t_zip8k_as_file_per_table` TABLESPACE=`s_8k`, RENAME TO `t_zip8k_in_s_8k`;
ALTER TABLE `t_zip1k_as_remote` TABLESPACE=`s_1k`, RENAME TO `t_zip1k_remote_in_s_1k`;
ALTER TABLE `t_zip2k_as_remote` TABLESPACE=`s_2k`, RENAME TO `t_zip2k_remote_in_s_2k`;
ALTER TABLE `t_zip4k_as_remote` TABLESPACE=`s_4k`, RENAME TO `t_zip4k_remote_in_s_4k`;
ALTER TABLE `t_zip8k_as_remote` TABLESPACE=`s_8k`, RENAME TO `t_zip8k_remote_in_s_8k`;

--source suite/innodb/include/show_i_s_tablespaces.inc
--source suite/innodb/include/show_i_s_tables.inc
--echo # MYSQLD_DATADIR/
--list_files $MYSQLD_DATADIR/ *.ibd
--echo # MYSQLD_DATADIR/test/
--list_files $MYSQLD_DATADIR/test/ *.ibd

CHECK TABLE `t_zip1k_in_s_1k`;
CHECK TABLE `t_zip2k_in_s_2k`;
CHECK TABLE `t_zip4k_in_s_4k`;
CHECK TABLE `t_zip8k_in_s_8k`;
CHECK TABLE `t_zip1k_remote_in_s_1k`;
CHECK TABLE `t_zip2k_remote_in_s_2k`;
CHECK TABLE `t_zip4k_remote_in_s_4k`;
CHECK TABLE `t_zip8k_remote_in_s_8k`;

--echo #
--echo # Tables in general tablespaces cannot be moved to another general tablespace if the
--echo # FILE_BLOCK_SIZE does not match the KEY_BLOCK_SIZE or if the tablespace is
--echo # not compressed.
--echo #
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip2k_in_s_2k` TABLESPACE=`s_1k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip4k_in_s_4k` TABLESPACE=`s_1k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip8k_in_s_8k` TABLESPACE=`s_1k`;
SHOW WARNINGS;

--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip1k_in_s_1k` TABLESPACE=`s_2k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip4k_in_s_4k` TABLESPACE=`s_2k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip8k_in_s_8k` TABLESPACE=`s_2k`;
SHOW WARNINGS;

--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip1k_in_s_1k` TABLESPACE=`s_4k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip2k_in_s_2k` TABLESPACE=`s_4k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip8k_in_s_8k` TABLESPACE=`s_4k`;
SHOW WARNINGS;

--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip1k_in_s_1k` TABLESPACE=`s_8k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip2k_in_s_2k` TABLESPACE=`s_8k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip4k_in_s_4k` TABLESPACE=`s_8k`;
SHOW WARNINGS;

--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip1k_in_s_1k` TABLESPACE=`s_16k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip2k_in_s_2k` TABLESPACE=`s_16k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip4k_in_s_4k` TABLESPACE=`s_16k`;
SHOW WARNINGS;
--error ER_ILLEGAL_HA_CREATE_OPTION
ALTER TABLE `t_zip8k_in_s_8k` TABLESPACE=`s_16k`;
SHOW WARNINGS;

--echo #
--echo # Tables in a general tablespace can be moved to file_per_table locations.
--echo #
ALTER TABLE `t_zip1k_in_s_1k` TABLESPACE=`innodb_file_per_table`, RENAME TO `t_zip1k_to_file_per_table`;
ALTER TABLE `t_zip2k_in_s_2k` TABLESPACE=`innodb_file_per_table`, RENAME TO `t_zip2k_to_file_per_table`;
ALTER TABLE `t_zip4k_in_s_4k` TABLESPACE=`innodb_file_per_table`, RENAME TO `t_zip4k_to_file_per_table`;
ALTER TABLE `t_zip8k_in_s_8k` TABLESPACE=`innodb_file_per_table`, RENAME TO `t_zip8k_to_file_per_table`;
--source suite/innodb/include/show_i_s_tablespaces.inc
--source suite/innodb/include/show_i_s_tables.inc

CHECK TABLE `t_zip1k_to_file_per_table`;
CHECK TABLE `t_zip2k_to_file_per_table`;
CHECK TABLE `t_zip4k_to_file_per_table`;
CHECK TABLE `t_zip8k_to_file_per_table`;
CHECK TABLE `t_zip16k_as_file_per_table`;

DROP TABLE `t_zip1k_to_file_per_table`;
DROP TABLE `t_zip2k_to_file_per_table`;
DROP TABLE `t_zip4k_to_file_per_table`;
DROP TABLE `t_zip8k_to_file_per_table`;
DROP TABLE `t_zip16k_as_file_per_table`;

--echo #
--echo # Tables in a general tablespace cannot be moved to remote file_per_table locations
--echo # because MySQL ignores DATA DIRECTORY in ALTER TABLE.
--echo #
ALTER TABLE `t_zip1k_remote_in_s_1k` TABLESPACE=`innodb_file_per_table`, DATA DIRECTORY='$MYSQL_TMP_DIR', RENAME TO `t_zip1k_to_file_per_table`;
ALTER TABLE `t_zip2k_remote_in_s_2k` TABLESPACE=`innodb_file_per_table`, DATA DIRECTORY='$MYSQL_TMP_DIR', RENAME TO `t_zip2k_to_file_per_table`;
ALTER TABLE `t_zip4k_remote_in_s_4k` TABLESPACE=`innodb_file_per_table`, DATA DIRECTORY='$MYSQL_TMP_DIR', RENAME TO `t_zip4k_to_file_per_table`;
ALTER TABLE `t_zip8k_remote_in_s_8k` TABLESPACE=`innodb_file_per_table`, DATA DIRECTORY='$MYSQL_TMP_DIR', RENAME TO `t_zip8k_to_file_per_table`;
--source suite/innodb/include/show_i_s_tablespaces.inc
--source suite/innodb/include/show_i_s_tables.inc

DROP TABLE `t_zip1k_to_file_per_table`;
DROP TABLE `t_zip2k_to_file_per_table`;
DROP TABLE `t_zip4k_to_file_per_table`;
DROP TABLE `t_zip8k_to_file_per_table`;

--echo #
--echo # Clean-up.
--echo #

DROP TABLESPACE s_1k;
DROP TABLESPACE s_2k;
DROP TABLESPACE s_4k;
DROP TABLESPACE s_8k;
DROP TABLESPACE s_16k;
--rmdir $MYSQL_TMP_DIR/test

--disable_query_log
call mtr.add_suppression("\\[ERROR\\] .*MY-\\d+.* Operating system error number .* in a file operation");
call mtr.add_suppression("\\[ERROR\\] .*MY-\\d+.* The error means the system cannot find the path specified");
call mtr.add_suppression("\\[ERROR\\] .*MY-\\d+.* Cannot open datafile for read-only");
call mtr.add_suppression("\\[ERROR\\] .*MY-\\d+.* Cannot delete tablespace .* because it is not found in the tablespace memory cache");
call mtr.add_suppression("\\[ERROR\\] .*MY-\\d+.* Failed to find tablespace for table `test`.`t_missing` in the cache. Attempting to load the tablespace with space id .*");
call mtr.add_suppression("\\[Warning\\] .*MY-\\d+.* Tablespace .*, name 's_missing', file 'delete_me.ibd' is missing!");
call mtr.add_suppression("\\[Warning\\] .*MY-\\d+.* Ignoring tablespace `s_missing` because it could not be opened");
call mtr.add_suppression("\\[ERROR\\] .*MY-\\d+.* Unable to open tablespace .* \\(flags=.*, filename=delete_me.ibd\\);");
call mtr.add_suppression("\\[Warning\\] .*MY-\\d+.* Trying to access missing tablespace .*");
call mtr.add_suppression("\\[ERROR\\].* '.*s_.k.ibd' read failed! - attempted to read 4 bytes, read only 0 bytes at offset 16418");
call mtr.add_suppression("\\[Warning\\].* Ignoring '.*s_.k.ibd' invalid tablespace ID in the header");
--enable_query_log
