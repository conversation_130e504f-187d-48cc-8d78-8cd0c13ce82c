--source include/no_valgrind_without_big.inc

--disable_warnings
let $ENGINE=`select variable_value from performance_schema.global_variables where variable_name='DEFAULT_STORAGE_ENGINE'`;
--enable_warnings

#Want to skip this test from daily Valgrind execution.
--source include/no_valgrind_without_big.inc
#Considering the time taken for this test on slow solaris platforms making it a big test
--source include/big_test.inc
SET sql_mode='NO_ENGINE_SUBSTITUTION';
--disable_warnings
DROP TABLE IF EXISTS t1;
--enable_warnings
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 INTEGER NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BIGINT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 REAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DOUBLE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 FLOAT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DECIMAL NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 NUMERIC NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 DATE NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIME NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 YEAR NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGBLOB NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TINYTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 TEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 MEDIUMTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 LONGTEXT NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
SET sql_mode=default;
