DROP TABLE IF EXISTS t1,t2;
DROP FUNCTION IF EXISTS sf1;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT          
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     CONTAINS SQL    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     NO SQL    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     READS SQL DATA    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     MODIFIES SQL DATA    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT         COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     CONTAINS SQL   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     NO SQL   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     READS SQL DATA   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     MODIFIES SQL DATA   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL        
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   CONTAINS SQL    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   NO SQL    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   READS SQL DATA    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   MODIFIES SQL DATA    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL       COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   CONTAINS SQL   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   NO SQL   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   READS SQL DATA   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   MODIFIES SQL DATA   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC      
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC CONTAINS SQL    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC NO SQL    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC READS SQL DATA    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC MODIFIES SQL DATA    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC     COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC CONTAINS SQL   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC NO SQL   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC READS SQL DATA   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC MODIFIES SQL DATA   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC      
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC CONTAINS SQL    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC NO SQL    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC MODIFIES SQL DATA    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC     COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC CONTAINS SQL   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC NO SQL   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC MODIFIES SQL DATA   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC      
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC CONTAINS SQL    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC NO SQL    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC READS SQL DATA    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC MODIFIES SQL DATA    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC     COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC CONTAINS SQL   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC NO SQL   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC READS SQL DATA   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC MODIFIES SQL DATA   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC      
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC CONTAINS SQL    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC NO SQL    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC READS SQL DATA    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC MODIFIES SQL DATA    
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC     COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC CONTAINS SQL   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC NO SQL   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC READS SQL DATA   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC MODIFIES SQL DATA   COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT       SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     CONTAINS SQL SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     NO SQL SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     READS SQL DATA SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     MODIFIES SQL DATA SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT       SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     NO SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL     SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   CONTAINS SQL SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   NO SQL SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   READS SQL DATA SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   MODIFIES SQL DATA SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL     SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   NO SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC   SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC NO SQL SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC   SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC NO SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC   SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC NO SQL SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC   SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC NO SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC   SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC NO SQL SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC   SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC NO SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC   SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC NO SQL SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC   SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC NO SQL SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT        SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     CONTAINS SQL  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     NO SQL  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     READS SQL DATA  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     MODIFIES SQL DATA  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT        SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     NO SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT     MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL      SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   CONTAINS SQL  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   NO SQL  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   READS SQL DATA  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   MODIFIES SQL DATA  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL      SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   NO SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL   MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC    SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC NO SQL  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC    SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC NO SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   NOT DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC    SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC NO SQL  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC    SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC NO SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL NOT DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC    SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC NO SQL  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC    SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC NO SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT   DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC    SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC NO SQL  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER  
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC    SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC NO SQL  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
CREATE FUNCTION sf1 (p1 BIGINT) RETURNS BIGINT LANGUAGE SQL DETERMINISTIC MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment'
BEGIN
DECLARE ret INT DEFAULT 0;
SELECT c1*2 INTO ret FROM t1 WHERE c1 = p1;
RETURN ret;
END//
INSERT INTO t2 VALUES(1,sf1(1)), (2,sf1(2));
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	2
2	4
UPDATE t2 SET c2 = sf1(2) WHERE c1 = 1;
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
2	4
DELETE FROM t2 WHERE c1 = sf1(1);
SELECT * FROM t2 ORDER BY c1;
c1	c2
1	4
DROP FUNCTION sf1;
DROP TABLE t1,t2;
