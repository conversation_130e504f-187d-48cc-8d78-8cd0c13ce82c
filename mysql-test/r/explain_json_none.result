set end_markers_in_json=on;
# new "FORMAT" keyword doesn't conflict with the FORMAT() function name:
SELECT FORMAT(1, 2), FORMAT(1, 2, 3);
FORMAT(1, 2)	FORMAT(1, 2, 3)
1.00	1.00
Warnings:
Warning	1649	Unknown locale: '3'
# new "FORMAT" keyword is a valid identifier:
SET @FORMAT=10;
SELECT @FORMAT;
@FORMAT
10
CREATE TABLE t1 (format INT);
SELECT format FROM t1;
format
DROP TABLE t1;
# different ways of format name writing:
EXPLAIN FORMAT=traditional SELECT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
Warnings:
Note	1003	/* select#1 */ select 1 AS `1`
EXPLAIN FORMAT='TrAdItIoNaL' SELECT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
Warnings:
Note	1003	/* select#1 */ select 1 AS `1`
EXPLAIN FORMAT=JSON SELECT 1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "No tables used"
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select 1 AS `1`
EXPLAIN FORMAT=foo SELECT 1;
ERROR HY000: Unknown EXPLAIN format name: 'foo'
# various EXPLAIN output
CREATE TABLE t1 (i INT);
CREATE TABLE t2 (i INT);
CREATE TABLE t3 (i INT);
CREATE TABLE t4 (i INT);
# no end markers in JSON:
set end_markers_in_json=off;
EXPLAIN FORMAT=JSON SELECT * FROM t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "no matching row in const table"
  }
}
Warnings:
Note	1003	/* select#1 */ select NULL AS `i` from `test`.`t1`
set end_markers_in_json=on;
EXPLAIN             INSERT INTO t1 VALUES (10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` values (10)
EXPLAIN FORMAT=JSON INSERT INTO t1 VALUES (10);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "insert": true,
      "table_name": "t1",
      "access_type": "ALL"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	insert into `test`.`t1` values (10)
EXPLAIN             SELECT * FROM t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
Warnings:
Note	1003	/* select#1 */ select NULL AS `i` from `test`.`t1`
PREPARE stmt FROM 'EXPLAIN FORMAT=JSON SELECT * FROM t1';
EXECUTE stmt;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "no matching row in const table"
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select NULL AS `i` from `test`.`t1`
EXECUTE stmt;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "no matching row in const table"
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select NULL AS `i` from `test`.`t1`
INSERT INTO t1 VALUES (1), (2), (3), (4), (5), (6), (7);
INSERT INTO t2 VALUES (1), (2);
# Check materialized derived table
set @optimizer_switch_saved= @@optimizer_switch;
set optimizer_switch='derived_merge=off';
EXPLAIN
SELECT * FROM (SELECT * FROM (SELECT * FROM (SELECT a1.i FROM (SELECT * FROM t1) a1, t2) a2) a3) a4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	<derived2>	NULL	ALL	NULL	NULL	NULL	NULL	14	100.00	NULL
2	DERIVED	<derived3>	NULL	ALL	NULL	NULL	NULL	NULL	14	100.00	NULL
3	DERIVED	<derived4>	NULL	ALL	NULL	NULL	NULL	NULL	14	100.00	NULL
4	DERIVED	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
4	DERIVED	<derived5>	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	Using join buffer (hash join)
5	DERIVED	t1	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `a4`.`i` AS `i` from (/* select#2 */ select `a3`.`i` AS `i` from (/* select#3 */ select `a2`.`i` AS `i` from (/* select#4 */ select `a1`.`i` AS `i` from (/* select#5 */ select `test`.`t1`.`i` AS `i` from `test`.`t1`) `a1` join `test`.`t2`) `a2`) `a3`) `a4`
EXPLAIN FORMAT=JSON
SELECT * FROM (SELECT * FROM (SELECT * FROM (SELECT a1.i FROM (SELECT * FROM t1) a1, t2) a2) a3) a4;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4.07"
    } /* cost_info */,
    "table": {
      "table_name": "a4",
      "access_type": "ALL",
      "rows_examined_per_scan": 14,
      "rows_produced_per_join": 14,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "2.67",
        "eval_cost": "1.40",
        "prefix_cost": "4.08",
        "data_read_per_join": "224"
      } /* cost_info */,
      "used_columns": [
        "i"
      ] /* used_columns */,
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "4.07"
          } /* cost_info */,
          "table": {
            "table_name": "a3",
            "access_type": "ALL",
            "rows_examined_per_scan": 14,
            "rows_produced_per_join": 14,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "2.67",
              "eval_cost": "1.40",
              "prefix_cost": "4.08",
              "data_read_per_join": "224"
            } /* cost_info */,
            "used_columns": [
              "i"
            ] /* used_columns */,
            "materialized_from_subquery": {
              "using_temporary_table": true,
              "dependent": false,
              "cacheable": true,
              "query_block": {
                "select_id": 3,
                "cost_info": {
                  "query_cost": "4.07"
                } /* cost_info */,
                "table": {
                  "table_name": "a2",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 14,
                  "rows_produced_per_join": 14,
                  "filtered": "100.00",
                  "cost_info": {
                    "read_cost": "2.67",
                    "eval_cost": "1.40",
                    "prefix_cost": "4.08",
                    "data_read_per_join": "224"
                  } /* cost_info */,
                  "used_columns": [
                    "i"
                  ] /* used_columns */,
                  "materialized_from_subquery": {
                    "using_temporary_table": true,
                    "dependent": false,
                    "cacheable": true,
                    "query_block": {
                      "select_id": 4,
                      "cost_info": {
                        "query_cost": "4.69"
                      } /* cost_info */,
                      "nested_loop": [
                        {
                          "table": {
                            "table_name": "t2",
                            "access_type": "ALL",
                            "rows_examined_per_scan": 2,
                            "rows_produced_per_join": 2,
                            "filtered": "100.00",
                            "cost_info": {
                              "read_cost": "0.50",
                              "eval_cost": "0.20",
                              "prefix_cost": "0.70",
                              "data_read_per_join": "16"
                            } /* cost_info */
                          } /* table */
                        },
                        {
                          "table": {
                            "table_name": "a1",
                            "access_type": "ALL",
                            "rows_examined_per_scan": 7,
                            "rows_produced_per_join": 14,
                            "filtered": "100.00",
                            "using_join_buffer": "hash join",
                            "cost_info": {
                              "read_cost": "2.59",
                              "eval_cost": "1.40",
                              "prefix_cost": "4.69",
                              "data_read_per_join": "224"
                            } /* cost_info */,
                            "used_columns": [
                              "i"
                            ] /* used_columns */,
                            "materialized_from_subquery": {
                              "using_temporary_table": true,
                              "dependent": false,
                              "cacheable": true,
                              "query_block": {
                                "select_id": 5,
                                "cost_info": {
                                  "query_cost": "1.20"
                                } /* cost_info */,
                                "table": {
                                  "table_name": "t1",
                                  "access_type": "ALL",
                                  "rows_examined_per_scan": 7,
                                  "rows_produced_per_join": 7,
                                  "filtered": "100.00",
                                  "cost_info": {
                                    "read_cost": "0.50",
                                    "eval_cost": "0.70",
                                    "prefix_cost": "1.20",
                                    "data_read_per_join": "56"
                                  } /* cost_info */,
                                  "used_columns": [
                                    "i"
                                  ] /* used_columns */
                                } /* table */
                              } /* query_block */
                            } /* materialized_from_subquery */
                          } /* table */
                        }
                      ] /* nested_loop */
                    } /* query_block */
                  } /* materialized_from_subquery */
                } /* table */
              } /* query_block */
            } /* materialized_from_subquery */
          } /* table */
        } /* query_block */
      } /* materialized_from_subquery */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `a4`.`i` AS `i` from (/* select#2 */ select `a3`.`i` AS `i` from (/* select#3 */ select `a2`.`i` AS `i` from (/* select#4 */ select `a1`.`i` AS `i` from (/* select#5 */ select `test`.`t1`.`i` AS `i` from `test`.`t1`) `a1` join `test`.`t2`) `a2`) `a3`) `a4`
set optimizer_switch= @optimizer_switch_saved;
# subquery in WHERE
EXPLAIN             SELECT * FROM t1 WHERE i IN (SELECT i FROM t2 WHERE t1.i = 10 ORDER BY RAND());
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
Warnings:
Note	1276	Field or reference 'test.t1.i' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#2 */ select `test`.`t2`.`i` from `test`.`t2` where ((`test`.`t1`.`i` = 10) and (<cache>(`test`.`t1`.`i`) = `test`.`t2`.`i`))))
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE i IN (SELECT i FROM t2 WHERE t1.i = 10 ORDER BY RAND());
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.20"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 7,
      "rows_produced_per_join": 7,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.70",
        "prefix_cost": "1.20",
        "data_read_per_join": "56"
      } /* cost_info */,
      "used_columns": [
        "i"
      ] /* used_columns */,
      "attached_condition": "<in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#2 */ select `test`.`t2`.`i` from `test`.`t2` where ((`test`.`t1`.`i` = 10) and (<cache>(`test`.`t1`.`i`) = `test`.`t2`.`i`))))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.70"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 1,
              "filtered": "50.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.10",
                "prefix_cost": "0.70",
                "data_read_per_join": "8"
              } /* cost_info */,
              "used_columns": [
                "i"
              ] /* used_columns */,
              "attached_condition": "((`test`.`t1`.`i` = 10) and (<cache>(`test`.`t1`.`i`) = `test`.`t2`.`i`))"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1276	Field or reference 'test.t1.i' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#2 */ select `test`.`t2`.`i` from `test`.`t2` where ((`test`.`t1`.`i` = 10) and (<cache>(`test`.`t1`.`i`) = `test`.`t2`.`i`))))
# two subqueries in WHERE
EXPLAIN             SELECT * FROM t1
WHERE i IN (SELECT i FROM t2 WHERE t1.i = 10 ORDER BY RAND())
OR i IN (SELECT i FROM t4 ORDER BY RAND());
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	Using where
3	DEPENDENT SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
Warnings:
Note	1276	Field or reference 'test.t1.i' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` where (<in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#2 */ select `test`.`t2`.`i` from `test`.`t2` where ((`test`.`t1`.`i` = 10) and (<cache>(`test`.`t1`.`i`) = `test`.`t2`.`i`)))) or <in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#3 */ select NULL from `test`.`t4` where (<cache>(`test`.`t1`.`i`) = NULL))))
EXPLAIN FORMAT=JSON SELECT * FROM t1
WHERE i IN (SELECT i FROM t2 WHERE t1.i = 10 ORDER BY RAND())
OR i IN (SELECT i FROM t4 ORDER BY RAND());
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.20"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 7,
      "rows_produced_per_join": 7,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.70",
        "prefix_cost": "1.20",
        "data_read_per_join": "56"
      } /* cost_info */,
      "used_columns": [
        "i"
      ] /* used_columns */,
      "attached_condition": "(<in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#2 */ select `test`.`t2`.`i` from `test`.`t2` where ((`test`.`t1`.`i` = 10) and (<cache>(`test`.`t1`.`i`) = `test`.`t2`.`i`)))) or <in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#3 */ select NULL from `test`.`t4` where (<cache>(`test`.`t1`.`i`) = NULL))))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 3,
            "message": "no matching row in const table"
          } /* query_block */
        },
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.70"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 1,
              "filtered": "50.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.10",
                "prefix_cost": "0.70",
                "data_read_per_join": "8"
              } /* cost_info */,
              "used_columns": [
                "i"
              ] /* used_columns */,
              "attached_condition": "((`test`.`t1`.`i` = 10) and (<cache>(`test`.`t1`.`i`) = `test`.`t2`.`i`))"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1276	Field or reference 'test.t1.i' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` where (<in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#2 */ select `test`.`t2`.`i` from `test`.`t2` where ((`test`.`t1`.`i` = 10) and (<cache>(`test`.`t1`.`i`) = `test`.`t2`.`i`)))) or <in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#3 */ select NULL from `test`.`t4` where (<cache>(`test`.`t1`.`i`) = NULL))))
# simple UNION
EXPLAIN             SELECT * FROM t1 UNION SELECT * FROM t2 UNION SELECT * FROM t3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
2	UNION	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	UNION	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
4	UNION RESULT	<union1,2,3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` union /* select#2 */ select `test`.`t2`.`i` AS `i` from `test`.`t2` union /* select#3 */ select NULL AS `i` from `test`.`t3`
EXPLAIN FORMAT=JSON SELECT * FROM t1 UNION SELECT * FROM t2 UNION SELECT * FROM t3;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": true,
      "select_id": 4,
      "table_name": "<union1,2,3>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "1.20"
            } /* cost_info */,
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.70",
                "prefix_cost": "1.20",
                "data_read_per_join": "56"
              } /* cost_info */,
              "used_columns": [
                "i"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.70"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.20",
                "prefix_cost": "0.70",
                "data_read_per_join": "16"
              } /* cost_info */,
              "used_columns": [
                "i"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 3,
            "message": "no matching row in const table"
          } /* query_block */
        }
      ] /* query_specifications */
    } /* union_result */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` union /* select#2 */ select `test`.`t2`.`i` AS `i` from `test`.`t2` union /* select#3 */ select NULL AS `i` from `test`.`t3`
# more complex UNION
EXPLAIN             (SELECT t1.i FROM t1 JOIN t2) UNION ALL (SELECT * FROM t3 WHERE i IN (SELECT i FROM t4 ORDER BY RAND()));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	Using join buffer (hash join)
2	UNION	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
3	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Not optimized, outer query is empty
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` join `test`.`t2` union all /* select#2 */ select NULL AS `i` from `test`.`t3` where <in_optimizer>(NULL,<exists>(/* select#3 */ select `test`.`t4`.`i` from `test`.`t4` where (<cache>(NULL) = `test`.`t4`.`i`)))
EXPLAIN FORMAT=JSON (SELECT t1.i FROM t1 JOIN t2) UNION ALL (SELECT * FROM t3 WHERE i IN (SELECT i FROM t4 ORDER BY RAND()));
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": false,
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "2.60"
            } /* cost_info */,
            "nested_loop": [
              {
                "table": {
                  "table_name": "t2",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 2,
                  "rows_produced_per_join": 2,
                  "filtered": "100.00",
                  "cost_info": {
                    "read_cost": "0.50",
                    "eval_cost": "0.20",
                    "prefix_cost": "0.70",
                    "data_read_per_join": "16"
                  } /* cost_info */
                } /* table */
              },
              {
                "table": {
                  "table_name": "t1",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 7,
                  "rows_produced_per_join": 14,
                  "filtered": "100.00",
                  "using_join_buffer": "hash join",
                  "cost_info": {
                    "read_cost": "0.50",
                    "eval_cost": "1.40",
                    "prefix_cost": "2.60",
                    "data_read_per_join": "112"
                  } /* cost_info */,
                  "used_columns": [
                    "i"
                  ] /* used_columns */
                } /* table */
              }
            ] /* nested_loop */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "message": "no matching row in const table",
            "optimized_away_subqueries": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "message": "Not optimized, outer query is empty"
                } /* query_block */
              }
            ] /* optimized_away_subqueries */
          } /* query_block */
        }
      ] /* query_specifications */
    } /* union_result */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` join `test`.`t2` union all /* select#2 */ select NULL AS `i` from `test`.`t3` where <in_optimizer>(NULL,<exists>(/* select#3 */ select `test`.`t4`.`i` from `test`.`t4` where (<cache>(NULL) = `test`.`t4`.`i`)))
# UNION with subquery in outer ORDER BY
EXPLAIN             (SELECT * FROM t1) UNION (SELECT * FROM t2) ORDER BY (SELECT i LIMIT 1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
2	UNION	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
4	UNION RESULT	<union1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary; Using filesort
Warnings:
Note	1276	Field or reference 'i' of SELECT #5 was resolved in SELECT #1
Note	1249	Select 5 was reduced during optimization
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` union /* select#2 */ select `test`.`t2`.`i` AS `i` from `test`.`t2` order by `i`
EXPLAIN FORMAT=JSON (SELECT * FROM t1) UNION (SELECT * FROM t2) ORDER BY (SELECT i LIMIT 1);
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": true,
      "select_id": 4,
      "table_name": "<union1,2>",
      "access_type": "ALL",
      "using_filesort": true,
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "1.20"
            } /* cost_info */,
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.70",
                "prefix_cost": "1.20",
                "data_read_per_join": "56"
              } /* cost_info */,
              "used_columns": [
                "i"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.70"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.20",
                "prefix_cost": "0.70",
                "data_read_per_join": "16"
              } /* cost_info */,
              "used_columns": [
                "i"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        }
      ] /* query_specifications */
    } /* union_result */
  } /* query_block */
}
Warnings:
Note	1276	Field or reference 'i' of SELECT #5 was resolved in SELECT #1
Note	1249	Select 5 was reduced during optimization
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` union /* select#2 */ select `test`.`t2`.`i` AS `i` from `test`.`t2` order by `i`
# optimizer-time subquery
EXPLAIN SELECT * FROM t1 ORDER BY (SELECT LENGTH(1) FROM t2 LIMIT 1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
2	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` order by (/* select#2 */ select length(1) from `test`.`t2` limit 1)
EXPLAIN FORMAT=JSON SELECT * FROM t1 ORDER BY (SELECT LENGTH(1) FROM t2 LIMIT 1);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.20"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.50",
          "eval_cost": "0.70",
          "prefix_cost": "1.20",
          "data_read_per_join": "56"
        } /* cost_info */,
        "used_columns": [
          "i"
        ] /* used_columns */
      } /* table */,
      "optimized_away_subqueries": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.70"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.20",
                "prefix_cost": "0.70",
                "data_read_per_join": "16"
              } /* cost_info */
            } /* table */
          } /* query_block */
        }
      ] /* optimized_away_subqueries */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` order by (/* select#2 */ select length(1) from `test`.`t2` limit 1)
# subquery in the HAVING clause
EXPLAIN SELECT * FROM t1 HAVING i > ALL (SELECT i FROM t2) OR i < ALL (SELECT i FROM t2);;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
3	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
2	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` having (<not>((`test`.`t1`.`i` <= <max>(/* select#2 */ select `test`.`t2`.`i` from `test`.`t2`))) or <not>((`test`.`t1`.`i` >= <min>(/* select#3 */ select `test`.`t2`.`i` from `test`.`t2`))))
EXPLAIN FORMAT=JSON SELECT * FROM t1 HAVING i > ALL (SELECT i FROM t2) OR i < ALL (SELECT i FROM t2);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.20"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 7,
      "rows_produced_per_join": 7,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.70",
        "prefix_cost": "1.20",
        "data_read_per_join": "56"
      } /* cost_info */,
      "used_columns": [
        "i"
      ] /* used_columns */
    } /* table */,
    "having_subqueries": [
      {
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 3,
          "cost_info": {
            "query_cost": "0.70"
          } /* cost_info */,
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 2,
            "rows_produced_per_join": 2,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.50",
              "eval_cost": "0.20",
              "prefix_cost": "0.70",
              "data_read_per_join": "16"
            } /* cost_info */,
            "used_columns": [
              "i"
            ] /* used_columns */
          } /* table */
        } /* query_block */
      },
      {
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "0.70"
          } /* cost_info */,
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 2,
            "rows_produced_per_join": 2,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.50",
              "eval_cost": "0.20",
              "prefix_cost": "0.70",
              "data_read_per_join": "16"
            } /* cost_info */,
            "used_columns": [
              "i"
            ] /* used_columns */
          } /* table */
        } /* query_block */
      }
    ] /* having_subqueries */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` having (<not>((`test`.`t1`.`i` <= <max>(/* select#2 */ select `test`.`t2`.`i` from `test`.`t2`))) or <not>((`test`.`t1`.`i` >= <min>(/* select#3 */ select `test`.`t2`.`i` from `test`.`t2`))))
# subquery in the GROUP BY clause
EXPLAIN SELECT * FROM t1 GROUP BY i > ALL (SELECT i FROM t2) OR i < ALL (SELECT i FROM t2);;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	Using temporary
3	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` group by (<not>(<in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#2 */ select `test`.`t2`.`i` from `test`.`t2` where <if>(outer_field_is_not_null, ((<cache>(`test`.`t1`.`i`) <= `test`.`t2`.`i`) or (`test`.`t2`.`i` is null)), true) having <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t2`.`i`), true)))) or <not>(<in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#3 */ select `test`.`t2`.`i` from `test`.`t2` where <if>(outer_field_is_not_null, ((<cache>(`test`.`t1`.`i`) >= `test`.`t2`.`i`) or (`test`.`t2`.`i` is null)), true) having <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t2`.`i`), true)))))
EXPLAIN FORMAT=JSON SELECT * FROM t1 GROUP BY i > ALL (SELECT i FROM t2) OR i < ALL (SELECT i FROM t2);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.20"
    } /* cost_info */,
    "grouping_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.50",
          "eval_cost": "0.70",
          "prefix_cost": "1.20",
          "data_read_per_join": "56"
        } /* cost_info */,
        "used_columns": [
          "i"
        ] /* used_columns */
      } /* table */,
      "group_by_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 3,
            "cost_info": {
              "query_cost": "0.70"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.20",
                "prefix_cost": "0.70",
                "data_read_per_join": "16"
              } /* cost_info */,
              "used_columns": [
                "i"
              ] /* used_columns */,
              "attached_condition": "<if>(outer_field_is_not_null, ((<cache>(`test`.`t1`.`i`) >= `test`.`t2`.`i`) or (`test`.`t2`.`i` is null)), true)"
            } /* table */
          } /* query_block */
        },
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.70"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.20",
                "prefix_cost": "0.70",
                "data_read_per_join": "16"
              } /* cost_info */,
              "used_columns": [
                "i"
              ] /* used_columns */,
              "attached_condition": "<if>(outer_field_is_not_null, ((<cache>(`test`.`t1`.`i`) <= `test`.`t2`.`i`) or (`test`.`t2`.`i` is null)), true)"
            } /* table */
          } /* query_block */
        }
      ] /* group_by_subqueries */
    } /* grouping_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` group by (<not>(<in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#2 */ select `test`.`t2`.`i` from `test`.`t2` where <if>(outer_field_is_not_null, ((<cache>(`test`.`t1`.`i`) <= `test`.`t2`.`i`) or (`test`.`t2`.`i` is null)), true) having <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t2`.`i`), true)))) or <not>(<in_optimizer>(`test`.`t1`.`i`,<exists>(/* select#3 */ select `test`.`t2`.`i` from `test`.`t2` where <if>(outer_field_is_not_null, ((<cache>(`test`.`t1`.`i`) >= `test`.`t2`.`i`) or (`test`.`t2`.`i` is null)), true) having <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t2`.`i`), true)))))
# subquery in the SELECT list
EXPLAIN SELECT (SELECT i + 1 FROM t1 ORDER BY RAND() LIMIT 1), i FROM t1;;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	NULL
2	UNCACHEABLE SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	7	100.00	Using temporary; Using filesort
Warnings:
Note	1003	/* select#1 */ select (/* select#2 */ select (`test`.`t1`.`i` + 1) from `test`.`t1` order by rand() limit 1) AS `(SELECT i + 1 FROM t1 ORDER BY RAND() LIMIT 1)`,`test`.`t1`.`i` AS `i` from `test`.`t1`
EXPLAIN FORMAT=JSON SELECT (SELECT i + 1 FROM t1 ORDER BY RAND() LIMIT 1), i FROM t1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.20"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 7,
      "rows_produced_per_join": 7,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.70",
        "prefix_cost": "1.20",
        "data_read_per_join": "56"
      } /* cost_info */,
      "used_columns": [
        "i"
      ] /* used_columns */
    } /* table */,
    "select_list_subqueries": [
      {
        "dependent": false,
        "cacheable": false,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "1.20"
          } /* cost_info */,
          "ordering_operation": {
            "using_temporary_table": true,
            "using_filesort": true,
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 7,
              "rows_produced_per_join": 7,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.70",
                "prefix_cost": "1.20",
                "data_read_per_join": "56"
              } /* cost_info */,
              "used_columns": [
                "i"
              ] /* used_columns */
            } /* table */
          } /* ordering_operation */
        } /* query_block */
      }
    ] /* select_list_subqueries */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select (/* select#2 */ select (`test`.`t1`.`i` + 1) from `test`.`t1` order by rand() limit 1) AS `(SELECT i + 1 FROM t1 ORDER BY RAND() LIMIT 1)`,`test`.`t1`.`i` AS `i` from `test`.`t1`
DROP TABLE t1, t2, t3, t4;
# derived table that is optimized out
CREATE TABLE t1 (i INT);
EXPLAIN SELECT 1 FROM (SELECT 1 AS x FROM t1) tt WHERE x;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` where true
EXPLAIN FORMAT= JSON SELECT 1 FROM (SELECT 1 AS x FROM t1) tt WHERE x;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "no matching row in const table"
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` where true
DROP TABLE t1;
# complex subqueries
CREATE TABLE t1 (a INT, b INT);
CREATE TABLE t2 (c INT, d INT);
CREATE TABLE t3 (e INT);
CREATE TABLE t4 (f INT, g INT);
INSERT INTO t1 VALUES (1,10), (2,10);
INSERT INTO t2 VALUES (2,10), (2,20);
INSERT INTO t3 VALUES (10), (30);
INSERT INTO t4 VALUES (2,10), (2,10);
EXPLAIN SELECT * FROM t1 WHERE t1.a IN (SELECT c FROM t2 WHERE (SELECT e FROM t3) < SOME(SELECT e FROM t3 WHERE t1.b));;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
4	DEPENDENT SUBQUERY	t3	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
3	SUBQUERY	t3	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
Warnings:
Note	1276	Field or reference 'test.t1.b' of SELECT #4 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`c` from `test`.`t2` where (<nop>(<in_optimizer>((/* select#3 */ select `test`.`t3`.`e` from `test`.`t3`),<exists>(/* select#4 */ select `test`.`t3`.`e` from `test`.`t3` where ((0 <> `test`.`t1`.`b`) and (<cache>((/* select#3 */ select `test`.`t3`.`e` from `test`.`t3`)) < `test`.`t3`.`e`))))) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`c`))))
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE t1.a IN (SELECT c FROM t2 WHERE (SELECT e FROM t3) < SOME(SELECT e FROM t3 WHERE t1.b));;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 2,
      "rows_produced_per_join": 2,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.20",
        "prefix_cost": "0.70",
        "data_read_per_join": "32"
      } /* cost_info */,
      "used_columns": [
        "a",
        "b"
      ] /* used_columns */,
      "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`c` from `test`.`t2` where (<nop>(<in_optimizer>((/* select#3 */ select `test`.`t3`.`e` from `test`.`t3`),<exists>(/* select#4 */ select `test`.`t3`.`e` from `test`.`t3` where ((0 <> `test`.`t1`.`b`) and (<cache>((/* select#3 */ select `test`.`t3`.`e` from `test`.`t3`)) < `test`.`t3`.`e`))))) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`c`))))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.70"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 1,
              "filtered": "50.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.10",
                "prefix_cost": "0.70",
                "data_read_per_join": "16"
              } /* cost_info */,
              "used_columns": [
                "c"
              ] /* used_columns */,
              "attached_condition": "(<nop>(<in_optimizer>((/* select#3 */ select `test`.`t3`.`e` from `test`.`t3`),<exists>(/* select#4 */ select `test`.`t3`.`e` from `test`.`t3` where ((0 <> `test`.`t1`.`b`) and (<cache>((/* select#3 */ select `test`.`t3`.`e` from `test`.`t3`)) < `test`.`t3`.`e`))))) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`c`))",
              "attached_subqueries": [
                {
                  "dependent": true,
                  "cacheable": false,
                  "query_block": {
                    "select_id": 4,
                    "cost_info": {
                      "query_cost": "0.70"
                    } /* cost_info */,
                    "table": {
                      "table_name": "t3",
                      "access_type": "ALL",
                      "rows_examined_per_scan": 2,
                      "rows_produced_per_join": 1,
                      "filtered": "50.00",
                      "cost_info": {
                        "read_cost": "0.50",
                        "eval_cost": "0.10",
                        "prefix_cost": "0.70",
                        "data_read_per_join": "8"
                      } /* cost_info */,
                      "used_columns": [
                        "e"
                      ] /* used_columns */,
                      "attached_condition": "((0 <> `test`.`t1`.`b`) and (<cache>((/* select#3 */ select `test`.`t3`.`e` from `test`.`t3`)) < `test`.`t3`.`e`))"
                    } /* table */
                  } /* query_block */
                }
              ] /* attached_subqueries */
            } /* table */,
            "optimized_away_subqueries": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "cost_info": {
                    "query_cost": "0.70"
                  } /* cost_info */,
                  "table": {
                    "table_name": "t3",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 2,
                    "rows_produced_per_join": 2,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "0.50",
                      "eval_cost": "0.20",
                      "prefix_cost": "0.70",
                      "data_read_per_join": "16"
                    } /* cost_info */,
                    "used_columns": [
                      "e"
                    ] /* used_columns */
                  } /* table */
                } /* query_block */
              }
            ] /* optimized_away_subqueries */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1276	Field or reference 'test.t1.b' of SELECT #4 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`c` from `test`.`t2` where (<nop>(<in_optimizer>((/* select#3 */ select `test`.`t3`.`e` from `test`.`t3`),<exists>(/* select#4 */ select `test`.`t3`.`e` from `test`.`t3` where ((0 <> `test`.`t1`.`b`) and (<cache>((/* select#3 */ select `test`.`t3`.`e` from `test`.`t3`)) < `test`.`t3`.`e`))))) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`c`))))
DROP TABLE t1, t2, t3, t4;
# semi-join materialization (if enabled)
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (1), (1), (1), (1), (1), (1), (1), (1), (1), (1), (1);
CREATE TABLE t2 (a INT) SELECT * FROM t1;
CREATE TABLE t3 (a INT) SELECT * FROM t1;
CREATE TABLE t4 (a INT) SELECT * FROM t1;
EXPLAIN FORMAT=JSON
SELECT * FROM t1
WHERE t1.a IN (SELECT t2.a FROM t2 WHERE t2.a >  0) AND
t1.a IN (SELECT t3.a FROM t3 WHERE t3.a IN
(SELECT t4.a FROM t4 WHERE a > 0));
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.70"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 12,
      "rows_produced_per_join": 12,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.51",
        "eval_cost": "1.20",
        "prefix_cost": "1.71",
        "data_read_per_join": "96"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "(<in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`a` from `test`.`t2` where ((`test`.`t2`.`a` > 0) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`a`)))) and <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#3 */ select `test`.`t3`.`a` from `test`.`t3` where (<in_optimizer>(`test`.`t3`.`a`,<exists>(/* select#4 */ select `test`.`t4`.`a` from `test`.`t4` where ((`test`.`t4`.`a` > 0) and (<cache>(`test`.`t3`.`a`) = `test`.`t4`.`a`)))) and (<cache>(`test`.`t1`.`a`) = `test`.`t3`.`a`)))))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 3,
            "cost_info": {
              "query_cost": "1.70"
            } /* cost_info */,
            "table": {
              "table_name": "t3",
              "access_type": "ALL",
              "rows_examined_per_scan": 12,
              "rows_produced_per_join": 1,
              "filtered": "10.00",
              "cost_info": {
                "read_cost": "0.51",
                "eval_cost": "0.12",
                "prefix_cost": "1.71",
                "data_read_per_join": "9"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */,
              "attached_condition": "(<in_optimizer>(`test`.`t3`.`a`,<exists>(/* select#4 */ select `test`.`t4`.`a` from `test`.`t4` where ((`test`.`t4`.`a` > 0) and (<cache>(`test`.`t3`.`a`) = `test`.`t4`.`a`)))) and (<cache>(`test`.`t1`.`a`) = `test`.`t3`.`a`))",
              "attached_subqueries": [
                {
                  "dependent": true,
                  "cacheable": false,
                  "query_block": {
                    "select_id": 4,
                    "cost_info": {
                      "query_cost": "1.70"
                    } /* cost_info */,
                    "table": {
                      "table_name": "t4",
                      "access_type": "ALL",
                      "rows_examined_per_scan": 12,
                      "rows_produced_per_join": 0,
                      "filtered": "8.33",
                      "cost_info": {
                        "read_cost": "1.31",
                        "eval_cost": "0.10",
                        "prefix_cost": "1.71",
                        "data_read_per_join": "7"
                      } /* cost_info */,
                      "used_columns": [
                        "a"
                      ] /* used_columns */,
                      "attached_condition": "((`test`.`t4`.`a` > 0) and (<cache>(`test`.`t3`.`a`) = `test`.`t4`.`a`))"
                    } /* table */
                  } /* query_block */
                }
              ] /* attached_subqueries */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.70"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 12,
              "rows_produced_per_join": 0,
              "filtered": "8.33",
              "cost_info": {
                "read_cost": "1.31",
                "eval_cost": "0.10",
                "prefix_cost": "1.71",
                "data_read_per_join": "7"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */,
              "attached_condition": "((`test`.`t2`.`a` > 0) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`a`))"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (<in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`a` from `test`.`t2` where ((`test`.`t2`.`a` > 0) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`a`)))) and <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#3 */ select `test`.`t3`.`a` from `test`.`t3` where (<in_optimizer>(`test`.`t3`.`a`,<exists>(/* select#4 */ select `test`.`t4`.`a` from `test`.`t4` where ((`test`.`t4`.`a` > 0) and (<cache>(`test`.`t3`.`a`) = `test`.`t4`.`a`)))) and (<cache>(`test`.`t1`.`a`) = `test`.`t3`.`a`)))))
DROP TABLE t1, t2, t3, t4;
# the same subquery is associated with two different JOIN_TABs
CREATE TABLE t1 (
i1 INTEGER NOT NULL,
c1 VARCHAR(1) NOT NULL
) charset latin1 ENGINE=InnoDB;
INSERT INTO t1 VALUES (2,'w');
CREATE TABLE t2 (
i1 INTEGER NOT NULL,
c1 VARCHAR(1) NOT NULL,
c2 VARCHAR(1) NOT NULL,
KEY (c1, i1)
) charset latin1 ENGINE=InnoDB;
INSERT INTO t2 VALUES (8,'d','d');
INSERT INTO t2 VALUES (4,'v','v');
CREATE TABLE t3 (
c1 VARCHAR(1) NOT NULL
) charset latin1 ENGINE=InnoDB;
INSERT INTO t3 VALUES ('v');
EXPLAIN FORMAT=json
SELECT i1
FROM t1
WHERE EXISTS (SELECT t2.c1
FROM (t2 INNER JOIN t3 ON (t3.c1 = t2.c1))
WHERE t2.c2 != t1.c1 AND t2.c2 = (SELECT MIN(t3.c1)
FROM t3));
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.35"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 1,
      "rows_produced_per_join": 1,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.10",
        "prefix_cost": "0.35",
        "data_read_per_join": "8"
      } /* cost_info */,
      "used_columns": [
        "i1",
        "c1"
      ] /* used_columns */,
      "attached_condition": "exists(/* select#2 */ select `test`.`t2`.`c1` from `test`.`t2` join `test`.`t3` where ((`test`.`t2`.`c1` = `test`.`t3`.`c1`) and (`test`.`t2`.`c2` = (/* select#3 */ select min(`test`.`t3`.`c1`) from `test`.`t3`)) and ((/* select#3 */ select min(`test`.`t3`.`c1`) from `test`.`t3`) <> `test`.`t1`.`c1`)))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.70"
            } /* cost_info */,
            "nested_loop": [
              {
                "table": {
                  "table_name": "t3",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 1,
                  "rows_produced_per_join": 1,
                  "filtered": "100.00",
                  "cost_info": {
                    "read_cost": "0.25",
                    "eval_cost": "0.10",
                    "prefix_cost": "0.35",
                    "data_read_per_join": "8"
                  } /* cost_info */,
                  "used_columns": [
                    "c1"
                  ] /* used_columns */,
                  "attached_condition": "((/* select#3 */ select min(`test`.`t3`.`c1`) from `test`.`t3`) <> `test`.`t1`.`c1`)",
                  "attached_subqueries": [
                    {
                      "dependent": false,
                      "cacheable": true,
                      "query_block": {
                        "select_id": 3,
                        "cost_info": {
                          "query_cost": "0.35"
                        } /* cost_info */,
                        "table": {
                          "table_name": "t3",
                          "access_type": "ALL",
                          "rows_examined_per_scan": 1,
                          "rows_produced_per_join": 1,
                          "filtered": "100.00",
                          "cost_info": {
                            "read_cost": "0.25",
                            "eval_cost": "0.10",
                            "prefix_cost": "0.35",
                            "data_read_per_join": "8"
                          } /* cost_info */,
                          "used_columns": [
                            "c1"
                          ] /* used_columns */
                        } /* table */
                      } /* query_block */
                    }
                  ] /* attached_subqueries */
                } /* table */
              },
              {
                "table": {
                  "table_name": "t2",
                  "access_type": "ref",
                  "possible_keys": [
                    "c1"
                  ] /* possible_keys */,
                  "key": "c1",
                  "used_key_parts": [
                    "c1"
                  ] /* used_key_parts */,
                  "key_length": "3",
                  "ref": [
                    "test.t3.c1"
                  ] /* ref */,
                  "rows_examined_per_scan": 1,
                  "rows_produced_per_join": 0,
                  "filtered": "50.00",
                  "cost_info": {
                    "read_cost": "0.25",
                    "eval_cost": "0.05",
                    "prefix_cost": "0.70",
                    "data_read_per_join": "8"
                  } /* cost_info */,
                  "used_columns": [
                    "c1",
                    "c2"
                  ] /* used_columns */,
                  "attached_condition": "(`test`.`t2`.`c2` = (/* select#3 */ select min(`test`.`t3`.`c1`) from `test`.`t3`))",
                  "attached_subqueries": [
                    {
                      "dependent": false,
                      "cacheable": true,
                      "query_block": {
                        "select_id": 3,
                        "cost_info": {
                          "query_cost": "0.35"
                        } /* cost_info */,
                        "table": {
                          "table_name": "t3",
                          "access_type": "ALL",
                          "rows_examined_per_scan": 1,
                          "rows_produced_per_join": 1,
                          "filtered": "100.00",
                          "cost_info": {
                            "read_cost": "0.25",
                            "eval_cost": "0.10",
                            "prefix_cost": "0.35",
                            "data_read_per_join": "8"
                          } /* cost_info */,
                          "used_columns": [
                            "c1"
                          ] /* used_columns */
                        } /* table */
                      } /* query_block */
                    }
                  ] /* attached_subqueries */
                } /* table */
              }
            ] /* nested_loop */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1276	Field or reference 'test.t1.c1' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`i1` AS `i1` from `test`.`t1` where exists(/* select#2 */ select `test`.`t2`.`c1` from `test`.`t2` join `test`.`t3` where ((`test`.`t2`.`c1` = `test`.`t3`.`c1`) and (`test`.`t2`.`c2` = (/* select#3 */ select min(`test`.`t3`.`c1`) from `test`.`t3`)) and ((/* select#3 */ select min(`test`.`t3`.`c1`) from `test`.`t3`) <> `test`.`t1`.`c1`)))
DROP TABLE t1, t2, t3;
# multiple materialization groups
CREATE TABLE t1 (c_key INT, KEY c_key (c_key));
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (c INT, c_key INT);
INSERT INTO t2 VALUES (8,5),(4,5),(8,1);
CREATE TABLE t3 LIKE t1;
INSERT INTO t3 SELECT * FROM t1;
CREATE TABLE t4 LIKE t2;
INSERT INTO t4 SELECT * FROM t2;
CREATE TABLE t5 (c INT);
INSERT INTO t5 VALUES (1), (2), (3);
# This should show two materialization groups where applicable
EXPLAIN SELECT * FROM t5
WHERE c IN (SELECT t2.c FROM t1 JOIN t2 ON t2.c_key = t1.c_key)
AND c IN (SELECT t4.c FROM t3 JOIN t4 ON t4.c_key = t3.c_key);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t5	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
3	DEPENDENT SUBQUERY	t4	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
3	DEPENDENT SUBQUERY	t3	NULL	ref	c_key	c_key	5	test.t4.c_key	1	100.00	Using index
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
2	DEPENDENT SUBQUERY	t1	NULL	ref	c_key	c_key	5	test.t2.c_key	2	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`c` AS `c` from `test`.`t5` where (<in_optimizer>(`test`.`t5`.`c`,<exists>(/* select#2 */ select `test`.`t2`.`c` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`c_key` = `test`.`t2`.`c_key`) and (<cache>(`test`.`t5`.`c`) = `test`.`t2`.`c`)))) and <in_optimizer>(`test`.`t5`.`c`,<exists>(/* select#3 */ select `test`.`t4`.`c` from `test`.`t3` join `test`.`t4` where ((`test`.`t3`.`c_key` = `test`.`t4`.`c_key`) and (<cache>(`test`.`t5`.`c`) = `test`.`t4`.`c`)))))
EXPLAIN FORMAT=JSON SELECT * FROM t5
WHERE c IN (SELECT t2.c FROM t1 JOIN t2 ON t2.c_key = t1.c_key)
AND c IN (SELECT t4.c FROM t3 JOIN t4 ON t4.c_key = t3.c_key);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "table_name": "t5",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 3,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.30",
        "prefix_cost": "0.80",
        "data_read_per_join": "24"
      } /* cost_info */,
      "used_columns": [
        "c"
      ] /* used_columns */,
      "attached_condition": "(<in_optimizer>(`test`.`t5`.`c`,<exists>(/* select#2 */ select `test`.`t2`.`c` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`c_key` = `test`.`t2`.`c_key`) and (<cache>(`test`.`t5`.`c`) = `test`.`t2`.`c`)))) and <in_optimizer>(`test`.`t5`.`c`,<exists>(/* select#3 */ select `test`.`t4`.`c` from `test`.`t3` join `test`.`t4` where ((`test`.`t3`.`c_key` = `test`.`t4`.`c_key`) and (<cache>(`test`.`t5`.`c`) = `test`.`t4`.`c`)))))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 3,
            "cost_info": {
              "query_cost": "1.15"
            } /* cost_info */,
            "nested_loop": [
              {
                "table": {
                  "table_name": "t4",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 3,
                  "rows_produced_per_join": 1,
                  "filtered": "33.33",
                  "cost_info": {
                    "read_cost": "0.50",
                    "eval_cost": "0.10",
                    "prefix_cost": "0.80",
                    "data_read_per_join": "16"
                  } /* cost_info */,
                  "used_columns": [
                    "c",
                    "c_key"
                  ] /* used_columns */,
                  "attached_condition": "((<cache>(`test`.`t5`.`c`) = `test`.`t4`.`c`) and (`test`.`t4`.`c_key` is not null))"
                } /* table */
              },
              {
                "table": {
                  "table_name": "t3",
                  "access_type": "ref",
                  "possible_keys": [
                    "c_key"
                  ] /* possible_keys */,
                  "key": "c_key",
                  "used_key_parts": [
                    "c_key"
                  ] /* used_key_parts */,
                  "key_length": "5",
                  "ref": [
                    "test.t4.c_key"
                  ] /* ref */,
                  "rows_examined_per_scan": 1,
                  "rows_produced_per_join": 1,
                  "filtered": "100.00",
                  "using_index": true,
                  "cost_info": {
                    "read_cost": "0.25",
                    "eval_cost": "0.10",
                    "prefix_cost": "1.15",
                    "data_read_per_join": "8"
                  } /* cost_info */,
                  "used_columns": [
                    "c_key"
                  ] /* used_columns */
                } /* table */
              }
            ] /* nested_loop */
          } /* query_block */
        },
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.26"
            } /* cost_info */,
            "nested_loop": [
              {
                "table": {
                  "table_name": "t2",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 3,
                  "rows_produced_per_join": 1,
                  "filtered": "33.33",
                  "cost_info": {
                    "read_cost": "0.50",
                    "eval_cost": "0.10",
                    "prefix_cost": "0.80",
                    "data_read_per_join": "16"
                  } /* cost_info */,
                  "used_columns": [
                    "c",
                    "c_key"
                  ] /* used_columns */,
                  "attached_condition": "((<cache>(`test`.`t5`.`c`) = `test`.`t2`.`c`) and (`test`.`t2`.`c_key` is not null))"
                } /* table */
              },
              {
                "table": {
                  "table_name": "t1",
                  "access_type": "ref",
                  "possible_keys": [
                    "c_key"
                  ] /* possible_keys */,
                  "key": "c_key",
                  "used_key_parts": [
                    "c_key"
                  ] /* used_key_parts */,
                  "key_length": "5",
                  "ref": [
                    "test.t2.c_key"
                  ] /* ref */,
                  "rows_examined_per_scan": 2,
                  "rows_produced_per_join": 2,
                  "filtered": "100.00",
                  "using_index": true,
                  "cost_info": {
                    "read_cost": "0.26",
                    "eval_cost": "0.20",
                    "prefix_cost": "1.26",
                    "data_read_per_join": "16"
                  } /* cost_info */,
                  "used_columns": [
                    "c_key"
                  ] /* used_columns */
                } /* table */
              }
            ] /* nested_loop */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t5`.`c` AS `c` from `test`.`t5` where (<in_optimizer>(`test`.`t5`.`c`,<exists>(/* select#2 */ select `test`.`t2`.`c` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`c_key` = `test`.`t2`.`c_key`) and (<cache>(`test`.`t5`.`c`) = `test`.`t2`.`c`)))) and <in_optimizer>(`test`.`t5`.`c`,<exists>(/* select#3 */ select `test`.`t4`.`c` from `test`.`t3` join `test`.`t4` where ((`test`.`t3`.`c_key` = `test`.`t4`.`c_key`) and (<cache>(`test`.`t5`.`c`) = `test`.`t4`.`c`)))))
DROP TABLE t1, t2, t3, t4, t5;
CREATE TABLE t1 (i INT);
CREATE TABLE t2 (i INT);
CREATE TABLE t3 (i INT);
INSERT INTO t1 VALUES (1);
INSERT INTO t2 VALUES (1);
INSERT INTO t3 VALUES (1);
# Subqueries in UPDATE values list
EXPLAIN UPDATE t1 SET i=(SELECT i FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
2	SUBQUERY	t2	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`i` = (/* select#2 */ select '1' from dual)
EXPLAIN FORMAT=JSON UPDATE t1 SET i=(SELECT i FROM t2);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 1,
      "filtered": "100.00"
    } /* table */,
    "update_value_subqueries": [
      {
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "1.00"
          } /* cost_info */,
          "table": {
            "table_name": "t2",
            "access_type": "system",
            "rows_examined_per_scan": 1,
            "rows_produced_per_join": 1,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.00",
              "eval_cost": "0.10",
              "prefix_cost": "0.00",
              "data_read_per_join": "8"
            } /* cost_info */,
            "used_columns": [
              "i"
            ] /* used_columns */
          } /* table */
        } /* query_block */
      }
    ] /* update_value_subqueries */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`i` = (/* select#2 */ select '1' from dual)
EXPLAIN UPDATE t1, t2 SET t1.i=(SELECT i FROM t3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
1	PRIMARY	t2	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
2	SUBQUERY	t3	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`i` = (/* select#2 */ select '1' from dual)
EXPLAIN FORMAT=JSON UPDATE t1, t2 SET t1.i=(SELECT i FROM t3);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.00"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t1",
          "access_type": "system",
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 1,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.00",
            "eval_cost": "0.10",
            "prefix_cost": "0.00",
            "data_read_per_join": "8"
          } /* cost_info */,
          "used_columns": [
            "i"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "system",
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 1,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.00",
            "eval_cost": "0.10",
            "prefix_cost": "0.00",
            "data_read_per_join": "8"
          } /* cost_info */
        } /* table */
      }
    ] /* nested_loop */,
    "update_value_subqueries": [
      {
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "1.00"
          } /* cost_info */,
          "table": {
            "table_name": "t3",
            "access_type": "system",
            "rows_examined_per_scan": 1,
            "rows_produced_per_join": 1,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.00",
              "eval_cost": "0.10",
              "prefix_cost": "0.00",
              "data_read_per_join": "8"
            } /* cost_info */,
            "used_columns": [
              "i"
            ] /* used_columns */
          } /* table */
        } /* query_block */
      }
    ] /* update_value_subqueries */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`i` = (/* select#2 */ select '1' from dual)
# INSERT ... ON DUPLICATE KEY UPDATE x=(SELECT ...) value list
EXPLAIN INSERT INTO t1 (i)
SELECT i FROM t2 ON DUPLICATE KEY UPDATE i=(SELECT i FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
1	PRIMARY	t2	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
2	SUBQUERY	t2	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`i`) /* select#1 */ select `test`.`t2`.`i` AS `i` from `test`.`t2` on duplicate key update `test`.`t1`.`i` = (/* select#2 */ select '1' from dual)
EXPLAIN FORMAT=JSON INSERT INTO t1 (i)
SELECT i FROM t2 ON DUPLICATE KEY UPDATE i=(SELECT i FROM t2);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.00"
    } /* cost_info */,
    "table": {
      "insert": true,
      "table_name": "t1",
      "access_type": "ALL"
    } /* table */,
    "insert_from": {
      "table": {
        "table_name": "t2",
        "access_type": "system",
        "rows_examined_per_scan": 1,
        "rows_produced_per_join": 1,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.00",
          "eval_cost": "0.10",
          "prefix_cost": "0.00",
          "data_read_per_join": "8"
        } /* cost_info */,
        "used_columns": [
          "i"
        ] /* used_columns */
      } /* table */
    } /* insert_from */,
    "insert_update_subqueries": [
      {
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "1.00"
          } /* cost_info */,
          "table": {
            "table_name": "t2",
            "access_type": "system",
            "rows_examined_per_scan": 1,
            "rows_produced_per_join": 1,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.00",
              "eval_cost": "0.10",
              "prefix_cost": "0.00",
              "data_read_per_join": "8"
            } /* cost_info */,
            "used_columns": [
              "i"
            ] /* used_columns */
          } /* table */
        } /* query_block */
      }
    ] /* insert_update_subqueries */
  } /* query_block */
}
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`i`) /* select#1 */ select `test`.`t2`.`i` AS `i` from `test`.`t2` on duplicate key update `test`.`t1`.`i` = (/* select#2 */ select '1' from dual)
EXPLAIN INSERT INTO t1 VALUES (1)
ON DUPLICATE KEY UPDATE i = (SELECT i FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
2	SUBQUERY	t2	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	insert into `test`.`t1` values (1) on duplicate key update `test`.`t1`.`i` = (/* select#2 */ select '1' from dual)
EXPLAIN FORMAT=JSON INSERT INTO t1 VALUES (1)
ON DUPLICATE KEY UPDATE i = (SELECT i FROM t2);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "insert": true,
      "table_name": "t1",
      "access_type": "ALL"
    } /* table */,
    "insert_update_subqueries": [
      {
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "1.00"
          } /* cost_info */,
          "table": {
            "table_name": "t2",
            "access_type": "system",
            "rows_examined_per_scan": 1,
            "rows_produced_per_join": 1,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.00",
              "eval_cost": "0.10",
              "prefix_cost": "0.00",
              "data_read_per_join": "8"
            } /* cost_info */,
            "used_columns": [
              "i"
            ] /* used_columns */
          } /* table */
        } /* query_block */
      }
    ] /* insert_update_subqueries */
  } /* query_block */
}
Warnings:
Note	1003	insert into `test`.`t1` values (1) on duplicate key update `test`.`t1`.`i` = (/* select#2 */ select '1' from dual)
# Subqueries in INSERT VALUES tuples:
EXPLAIN INSERT INTO t3 VALUES((SELECT i FROM t1)), ((SELECT i FROM t2));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t3	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
3	SUBQUERY	t2	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
2	SUBQUERY	t1	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	insert into `test`.`t3` values ((/* select#2 */ select `test`.`t1`.`i` from `test`.`t1`)),((/* select#3 */ select `test`.`t2`.`i` from `test`.`t2`))
EXPLAIN FORMAT=JSON INSERT INTO t3 VALUES((SELECT i FROM t1)), ((SELECT i FROM t2));
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "insert": true,
      "table_name": "t3",
      "access_type": "ALL"
    } /* table */,
    "insert_values_subqueries": [
      {
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 3,
          "cost_info": {
            "query_cost": "1.00"
          } /* cost_info */,
          "table": {
            "table_name": "t2",
            "access_type": "system",
            "rows_examined_per_scan": 1,
            "rows_produced_per_join": 1,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.00",
              "eval_cost": "0.10",
              "prefix_cost": "0.00",
              "data_read_per_join": "8"
            } /* cost_info */,
            "used_columns": [
              "i"
            ] /* used_columns */
          } /* table */
        } /* query_block */
      },
      {
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "1.00"
          } /* cost_info */,
          "table": {
            "table_name": "t1",
            "access_type": "system",
            "rows_examined_per_scan": 1,
            "rows_produced_per_join": 1,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.00",
              "eval_cost": "0.10",
              "prefix_cost": "0.00",
              "data_read_per_join": "8"
            } /* cost_info */,
            "used_columns": [
              "i"
            ] /* used_columns */
          } /* table */
        } /* query_block */
      }
    ] /* insert_values_subqueries */
  } /* query_block */
}
Warnings:
Note	1003	insert into `test`.`t3` values ((/* select#2 */ select `test`.`t1`.`i` from `test`.`t1`)),((/* select#3 */ select `test`.`t2`.`i` from `test`.`t2`))
DROP TABLE t1, t2, t3;
# Various queries
EXPLAIN SELECT a, b FROM
(SELECT 1 AS a, 2 AS b
UNION ALL
SELECT 1 AS a, 2 AS b) t1
GROUP BY a
ORDER BY b DESC;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	<derived2>	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using temporary; Using filesort
2	DERIVED	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
3	UNION	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
Warnings:
Note	1003	/* select#1 */ select `t1`.`a` AS `a`,`t1`.`b` AS `b` from (/* select#2 */ select 1 AS `a`,2 AS `b` union all /* select#3 */ select 1 AS `a`,2 AS `b`) `t1` group by `t1`.`a` order by `t1`.`b` desc
EXPLAIN FORMAT=JSON SELECT a, b FROM
(SELECT 1 AS a, 2 AS b
UNION ALL
SELECT 1 AS a, 2 AS b) t1
GROUP BY a
ORDER BY b DESC;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.72"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "2.52",
            "eval_cost": "0.20",
            "prefix_cost": "2.73",
            "data_read_per_join": "48"
          } /* cost_info */,
          "used_columns": [
            "a",
            "b"
          ] /* used_columns */,
          "materialized_from_subquery": {
            "using_temporary_table": true,
            "dependent": false,
            "cacheable": true,
            "query_block": {
              "union_result": {
                "using_temporary_table": false,
                "query_specifications": [
                  {
                    "dependent": false,
                    "cacheable": true,
                    "query_block": {
                      "select_id": 2,
                      "message": "No tables used"
                    } /* query_block */
                  },
                  {
                    "dependent": false,
                    "cacheable": true,
                    "query_block": {
                      "select_id": 3,
                      "message": "No tables used"
                    } /* query_block */
                  }
                ] /* query_specifications */
              } /* union_result */
            } /* query_block */
          } /* materialized_from_subquery */
        } /* table */
      } /* grouping_operation */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `t1`.`a` AS `a`,`t1`.`b` AS `b` from (/* select#2 */ select 1 AS `a`,2 AS `b` union all /* select#3 */ select 1 AS `a`,2 AS `b`) `t1` group by `t1`.`a` order by `t1`.`b` desc
#
CREATE TABLE t1(a INT, b INT);
INSERT INTO t1 VALUES (), ();
EXPLAIN SELECT 1 FROM t1 GROUP BY GREATEST(t1.a, (SELECT 1 FROM (SELECT t1.b FROM t1, t1 t2 ORDER BY t1.a, t1.a LIMIT 1) AS d));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using temporary
2	SUBQUERY	<derived3>	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
3	DERIVED	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using temporary; Using filesort
3	DERIVED	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` group by greatest(`test`.`t1`.`a`,(/* select#2 */ select 1 from dual))
EXPLAIN FORMAT=JSON SELECT 1 FROM t1 GROUP BY GREATEST(t1.a, (SELECT 1 FROM (SELECT t1.b FROM t1, t1 t2 ORDER BY t1.a, t1.a LIMIT 1) AS d));
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    } /* cost_info */,
    "grouping_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 2,
        "rows_produced_per_join": 2,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.50",
          "eval_cost": "0.20",
          "prefix_cost": "0.70",
          "data_read_per_join": "32"
        } /* cost_info */,
        "used_columns": [
          "a"
        ] /* used_columns */
      } /* table */,
      "group_by_subqueries": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "d",
              "access_type": "system",
              "rows_examined_per_scan": 1,
              "rows_produced_per_join": 1,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.00",
                "eval_cost": "0.10",
                "prefix_cost": "0.00",
                "data_read_per_join": "16"
              } /* cost_info */,
              "used_columns": [
                "b"
              ] /* used_columns */,
              "materialized_from_subquery": {
                "using_temporary_table": true,
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "cost_info": {
                    "query_cost": "1.60"
                  } /* cost_info */,
                  "ordering_operation": {
                    "using_temporary_table": true,
                    "using_filesort": true,
                    "nested_loop": [
                      {
                        "table": {
                          "table_name": "t1",
                          "access_type": "ALL",
                          "rows_examined_per_scan": 2,
                          "rows_produced_per_join": 2,
                          "filtered": "100.00",
                          "cost_info": {
                            "read_cost": "0.50",
                            "eval_cost": "0.20",
                            "prefix_cost": "0.70",
                            "data_read_per_join": "32"
                          } /* cost_info */,
                          "used_columns": [
                            "a",
                            "b"
                          ] /* used_columns */
                        } /* table */
                      },
                      {
                        "table": {
                          "table_name": "t2",
                          "access_type": "ALL",
                          "rows_examined_per_scan": 2,
                          "rows_produced_per_join": 4,
                          "filtered": "100.00",
                          "using_join_buffer": "hash join",
                          "cost_info": {
                            "read_cost": "0.50",
                            "eval_cost": "0.40",
                            "prefix_cost": "1.60",
                            "data_read_per_join": "64"
                          } /* cost_info */
                        } /* table */
                      }
                    ] /* nested_loop */
                  } /* ordering_operation */
                } /* query_block */
              } /* materialized_from_subquery */
            } /* table */
          } /* query_block */
        }
      ] /* group_by_subqueries */
    } /* grouping_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` group by greatest(`test`.`t1`.`a`,(/* select#2 */ select 1 from dual))
DROP TABLE t1;
#
CREATE TABLE t1(f1 INT);
INSERT INTO t1 VALUES (1),(1);
EXPLAIN SELECT 1 FROM t1 WHERE (SELECT (SELECT 1 FROM t1 GROUP BY f1));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	SUBQUERY	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using temporary
Warnings:
Note	1249	Select 2 was reduced during optimization
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` where true
EXPLAIN FORMAT=JSON SELECT 1 FROM t1 WHERE (SELECT (SELECT 1 FROM t1 GROUP BY f1));
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 2,
      "rows_produced_per_join": 2,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.20",
        "prefix_cost": "0.70",
        "data_read_per_join": "16"
      } /* cost_info */
    } /* table */,
    "optimized_away_subqueries": [
      {
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 3,
          "cost_info": {
            "query_cost": "0.70"
          } /* cost_info */,
          "grouping_operation": {
            "using_temporary_table": true,
            "using_filesort": false,
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.20",
                "prefix_cost": "0.70",
                "data_read_per_join": "16"
              } /* cost_info */,
              "used_columns": [
                "f1"
              ] /* used_columns */
            } /* table */
          } /* grouping_operation */
        } /* query_block */
      }
    ] /* optimized_away_subqueries */
  } /* query_block */
}
Warnings:
Note	1249	Select 2 was reduced during optimization
Note	1003	/* select#1 */ select 1 AS `1` from `test`.`t1` where true
DROP TABLE t1;
#
CREATE TABLE t1 (i INT);
CREATE TABLE t2 (i INT, j INT);
INSERT INTO t1 VALUES (1), (2), (3), (4), (5), (6), (7), (8), (9), (10);
INSERT INTO t2 SELECT i, i * 10 FROM t1;
EXPLAIN SELECT * FROM t1 ORDER BY (SELECT t2.j FROM t2 WHERE t2.i = t1.i);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using filesort
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	10.00	Using where
Warnings:
Note	1276	Field or reference 'test.t1.i' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` order by (/* select#2 */ select `test`.`t2`.`j` from `test`.`t2` where (`test`.`t2`.`i` = `test`.`t1`.`i`))
EXPLAIN FORMAT=JSON SELECT * FROM t1 ORDER BY (SELECT t2.j FROM t2 WHERE t2.i = t1.i);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "11.50"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "10.00"
      } /* cost_info */,
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.50",
          "eval_cost": "1.00",
          "prefix_cost": "1.50",
          "data_read_per_join": "80"
        } /* cost_info */,
        "used_columns": [
          "i"
        ] /* used_columns */
      } /* table */,
      "order_by_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.50"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 10,
              "rows_produced_per_join": 1,
              "filtered": "10.00",
              "cost_info": {
                "read_cost": "0.51",
                "eval_cost": "0.10",
                "prefix_cost": "1.51",
                "data_read_per_join": "16"
              } /* cost_info */,
              "used_columns": [
                "i",
                "j"
              ] /* used_columns */,
              "attached_condition": "(`test`.`t2`.`i` = `test`.`t1`.`i`)"
            } /* table */
          } /* query_block */
        }
      ] /* order_by_subqueries */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1276	Field or reference 'test.t1.i' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` order by (/* select#2 */ select `test`.`t2`.`j` from `test`.`t2` where (`test`.`t2`.`i` = `test`.`t1`.`i`))
EXPLAIN SELECT * FROM t1 GROUP BY (SELECT t2.j FROM t2 WHERE t2.i = t1.i);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using temporary
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	10	10.00	Using where
Warnings:
Note	1276	Field or reference 'test.t1.i' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` group by (/* select#2 */ select `test`.`t2`.`j` from `test`.`t2` where (`test`.`t2`.`i` = `test`.`t1`.`i`))
EXPLAIN FORMAT=JSON SELECT * FROM t1 GROUP BY (SELECT t2.j FROM t2 WHERE t2.i = t1.i);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.50"
    } /* cost_info */,
    "grouping_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.50",
          "eval_cost": "1.00",
          "prefix_cost": "1.50",
          "data_read_per_join": "80"
        } /* cost_info */,
        "used_columns": [
          "i"
        ] /* used_columns */
      } /* table */,
      "group_by_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.50"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 10,
              "rows_produced_per_join": 1,
              "filtered": "10.00",
              "cost_info": {
                "read_cost": "0.51",
                "eval_cost": "0.10",
                "prefix_cost": "1.51",
                "data_read_per_join": "16"
              } /* cost_info */,
              "used_columns": [
                "i",
                "j"
              ] /* used_columns */,
              "attached_condition": "(`test`.`t2`.`i` = `test`.`t1`.`i`)"
            } /* table */
          } /* query_block */
        }
      ] /* group_by_subqueries */
    } /* grouping_operation */
  } /* query_block */
}
Warnings:
Note	1276	Field or reference 'test.t1.i' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` group by (/* select#2 */ select `test`.`t2`.`j` from `test`.`t2` where (`test`.`t2`.`i` = `test`.`t1`.`i`))
DROP TABLE t1, t2;
CREATE TABLE t1 (a INT NOT NULL, b INT NOT NULL, KEY k1 (a, b));
INSERT INTO t1 VALUES (10,1),(10,2),(10,3),(20,4),(20,5),(20,6),
(30,7),(30,8),(30,9),(40,10),(40,11),(40,12),(40,13),
(40,14),(40,15),(40,16),(40,17),(40,18),(40,19),(40,20);
EXPLAIN FORMAT=JSON SELECT a, MIN(b) AS b FROM t1 GROUP BY a ORDER BY b;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.00"
    } /* cost_info */,
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "grouping_operation": {
        "using_filesort": false,
        "table": {
          "table_name": "t1",
          "access_type": "range",
          "possible_keys": [
            "k1"
          ] /* possible_keys */,
          "key": "k1",
          "used_key_parts": [
            "a"
          ] /* used_key_parts */,
          "key_length": "4",
          "rows_examined_per_scan": 11,
          "rows_produced_per_join": 11,
          "filtered": "100.00",
          "using_index_for_group_by": true,
          "cost_info": {
            "read_cost": "1.90",
            "eval_cost": "1.10",
            "prefix_cost": "3.00",
            "data_read_per_join": "176"
          } /* cost_info */,
          "used_columns": [
            "a",
            "b"
          ] /* used_columns */
        } /* table */
      } /* grouping_operation */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,min(`test`.`t1`.`b`) AS `b` from `test`.`t1` group by `test`.`t1`.`a` order by `b`
DROP TABLE t1;
#
CREATE TABLE t1 (a INT NOT NULL, b CHAR(3) NOT NULL, PRIMARY KEY (a)) charset utf8mb4;
INSERT INTO t1 VALUES (1,'ABC'), (2,'EFG'), (3,'HIJ');
CREATE TABLE t2 (a INT NOT NULL,b CHAR(3) NOT NULL,PRIMARY KEY (a, b)) charset utf8mb4;
INSERT INTO t2 VALUES (1,'a'),(1,'b'),(3,'F');
EXPLAIN FORMAT=JSON SELECT t1.a, GROUP_CONCAT(t2.b) AS b FROM t1 LEFT JOIN t2 ON t1.a=t2.a GROUP BY t1.a ORDER BY t1.b;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.85"
    } /* cost_info */,
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "grouping_operation": {
        "using_filesort": true,
        "nested_loop": [
          {
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "possible_keys": [
                "PRIMARY"
              ] /* possible_keys */,
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.30",
                "prefix_cost": "0.80",
                "data_read_per_join": "72"
              } /* cost_info */,
              "used_columns": [
                "a",
                "b"
              ] /* used_columns */
            } /* table */
          },
          {
            "table": {
              "table_name": "t2",
              "access_type": "ref",
              "possible_keys": [
                "PRIMARY"
              ] /* possible_keys */,
              "key": "PRIMARY",
              "used_key_parts": [
                "a"
              ] /* used_key_parts */,
              "key_length": "4",
              "ref": [
                "test.t1.a"
              ] /* ref */,
              "rows_examined_per_scan": 1,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "using_index": true,
              "cost_info": {
                "read_cost": "0.75",
                "eval_cost": "0.30",
                "prefix_cost": "1.85",
                "data_read_per_join": "72"
              } /* cost_info */,
              "used_columns": [
                "a",
                "b"
              ] /* used_columns */
            } /* table */
          }
        ] /* nested_loop */
      } /* grouping_operation */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,group_concat(`test`.`t2`.`b` separator ',') AS `b` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`a` = `test`.`t1`.`a`)) where true group by `test`.`t1`.`a` order by `test`.`t1`.`b`
DROP TABLE t1;
DROP TABLE t2;
#
CREATE TABLE t1 (a INT, b INT);
INSERT INTO t1 VALUES
(1,4),
(2,2), (2,2),
(4,1), (4,1), (4,1), (4,1),
(2,1), (2,1);
EXPLAIN FORMAT=JSON SELECT SUM(b) FROM t1 GROUP BY a WITH ROLLUP;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.40"
    } /* cost_info */,
    "grouping_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "9.00"
      } /* cost_info */,
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 9,
        "rows_produced_per_join": 9,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.50",
          "eval_cost": "0.90",
          "prefix_cost": "1.40",
          "data_read_per_join": "144"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b"
        ] /* used_columns */
      } /* table */
    } /* grouping_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select rollup_sum_switcher(sum(`test`.`t1`.`b`)) AS `SUM(b)` from `test`.`t1` group by `test`.`t1`.`a` with rollup
DROP TABLE t1;
# Composition of DISTINCT, GROUP BY and ORDER BY
CREATE TABLE t1 (a INT, b INT);
INSERT INTO t1 VALUES (1, 1), (1, 2), (2, 1), (2, 2), (3, 1);
EXPLAIN FORMAT=JSON SELECT DISTINCT SUM(b) s FROM t1 GROUP BY a ORDER BY s;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.00"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "duplicates_removal": {
        "using_filesort": false,
        "grouping_operation": {
          "using_temporary_table": true,
          "using_filesort": false,
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.50",
              "eval_cost": "0.50",
              "prefix_cost": "1.00",
              "data_read_per_join": "80"
            } /* cost_info */,
            "used_columns": [
              "a",
              "b"
            ] /* used_columns */
          } /* table */
        } /* grouping_operation */
      } /* duplicates_removal */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select distinct sum(`test`.`t1`.`b`) AS `s` from `test`.`t1` group by `test`.`t1`.`a` order by `s`
FLUSH STATUS;
SELECT DISTINCT SUM(b) s FROM t1 GROUP BY a ORDER BY s;
s
1
3
SHOW SESSION STATUS WHERE (Variable_name LIKE 'Sort_%' OR Variable_name LIKE 'Created_%_tables') AND Value > 0;
Variable_name	Value
Created_tmp_tables	1
Sort_rows	3
Sort_scan	1
DROP TABLE t1;
# "buffer_result" node
CREATE TABLE t1 (a INT NOT NULL);
CREATE TABLE t2 (a INT NOT NULL, PRIMARY KEY (a));
INSERT INTO t1 VALUES (1);
INSERT INTO t2 VALUES (1),(2);
EXPLAIN FORMAT=JSON SELECT SQL_BIG_RESULT DISTINCT t1.a FROM t1,t2 ORDER BY t2.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "duplicates_removal": {
        "using_temporary_table": true,
        "using_filesort": false,
        "nested_loop": [
          {
            "table": {
              "table_name": "t1",
              "access_type": "system",
              "rows_examined_per_scan": 1,
              "rows_produced_per_join": 1,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.00",
                "eval_cost": "0.10",
                "prefix_cost": "0.00",
                "data_read_per_join": "8"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          },
          {
            "table": {
              "table_name": "t2",
              "access_type": "index",
              "key": "PRIMARY",
              "used_key_parts": [
                "a"
              ] /* used_key_parts */,
              "key_length": "4",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "using_index": true,
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.20",
                "prefix_cost": "0.70",
                "data_read_per_join": "16"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          }
        ] /* nested_loop */
      } /* duplicates_removal */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select distinct sql_big_result '1' AS `a` from `test`.`t2` order by `test`.`t2`.`a`
DROP TABLE t1, t2;
#
CREATE TABLE t1 (a INT NOT NULL, b INT, PRIMARY KEY (a));
CREATE TABLE t2 (a INT NOT NULL, PRIMARY KEY (a));
INSERT INTO t1 VALUES (1,10), (2,20), (3,30),  (4,40);
INSERT INTO t2 VALUES (2), (3), (4), (5);
EXPLAIN FORMAT=JSON SELECT * FROM t2 WHERE t2.a IN (SELECT a FROM t1 WHERE t1.b <> 30);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.90"
    } /* cost_info */,
    "table": {
      "table_name": "t2",
      "access_type": "index",
      "key": "PRIMARY",
      "used_key_parts": [
        "a"
      ] /* used_key_parts */,
      "key_length": "4",
      "rows_examined_per_scan": 4,
      "rows_produced_per_join": 4,
      "filtered": "100.00",
      "using_index": true,
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.40",
        "prefix_cost": "0.90",
        "data_read_per_join": "32"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "<in_optimizer>(`test`.`t2`.`a`,<exists>(<primary_index_lookup>(<cache>(`test`.`t2`.`a`) in t1 on PRIMARY where (`test`.`t1`.`b` <> 30))))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.35"
            } /* cost_info */,
            "table": {
              "table_name": "t1",
              "access_type": "unique_subquery",
              "possible_keys": [
                "PRIMARY"
              ] /* possible_keys */,
              "key": "PRIMARY",
              "used_key_parts": [
                "a"
              ] /* used_key_parts */,
              "key_length": "4",
              "ref": [
                "func"
              ] /* ref */,
              "rows_examined_per_scan": 1,
              "rows_produced_per_join": 0,
              "filtered": "75.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.08",
                "prefix_cost": "0.35",
                "data_read_per_join": "12"
              } /* cost_info */,
              "used_columns": [
                "a",
                "b"
              ] /* used_columns */,
              "attached_condition": "(`test`.`t1`.`b` <> 30)"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a` from `test`.`t2` where <in_optimizer>(`test`.`t2`.`a`,<exists>(<primary_index_lookup>(<cache>(`test`.`t2`.`a`) in t1 on PRIMARY where (`test`.`t1`.`b` <> 30))))
DROP TABLE t1, t2;
#
# WL#11350 extensions
#
CREATE TABLE r(a INT);
INSERT INTO r VALUES (1), (2), (-1), (-2);
CREATE TABLE s(a INT);
INSERT INTO s VALUES (1), (10), (20), (-10), (-20);
CREATE TABLE t(a INT);
INSERT INTO t VALUES (10), (100), (200), (-100), (-200);
ANALYZE TABLE r,s,t;
Table	Op	Msg_type	Msg_text
test.r	analyze	status	OK
test.s	analyze	status	OK
test.t	analyze	status	OK
# Test nesting structures
EXPLAIN FORMAT=json ( ( SELECT * FROM r UNION
(SELECT * FROM s ORDER BY a LIMIT 1)) UNION
( SELECT * FROM s UNION SELECT 2 LIMIT 3)
ORDER BY 1 LIMIT 3)
ORDER BY 1;
EXPLAIN
{
  "query_block": {
    "unary_result": {
      "using_temporary_table": true,
      "select_id": 7,
      "table_name": "<ordered6>",
      "access_type": "ALL",
      "using_filesort": true,
      "query_specifications": [
        {
          "union_result": {
            "using_temporary_table": true,
            "select_id": 6,
            "table_name": "<union1,2,5>",
            "access_type": "ALL",
            "using_filesort": true,
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 1,
                  "cost_info": {
                    "query_cost": "0.90"
                  } /* cost_info */,
                  "table": {
                    "table_name": "r",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 4,
                    "rows_produced_per_join": 4,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "0.50",
                      "eval_cost": "0.40",
                      "prefix_cost": "0.90",
                      "data_read_per_join": "32"
                    } /* cost_info */,
                    "used_columns": [
                      "a"
                    ] /* used_columns */
                  } /* table */
                } /* query_block */
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 2,
                  "cost_info": {
                    "query_cost": "1.00"
                  } /* cost_info */,
                  "ordering_operation": {
                    "using_filesort": true,
                    "table": {
                      "table_name": "s",
                      "access_type": "ALL",
                      "rows_examined_per_scan": 5,
                      "rows_produced_per_join": 5,
                      "filtered": "100.00",
                      "cost_info": {
                        "read_cost": "0.50",
                        "eval_cost": "0.50",
                        "prefix_cost": "1.00",
                        "data_read_per_join": "40"
                      } /* cost_info */,
                      "used_columns": [
                        "a"
                      ] /* used_columns */
                    } /* table */
                  } /* ordering_operation */
                } /* query_block */
              },
              {
                "union_result": {
                  "using_temporary_table": true,
                  "select_id": 5,
                  "table_name": "<union3,4>",
                  "access_type": "ALL",
                  "query_specifications": [
                    {
                      "dependent": false,
                      "cacheable": true,
                      "query_block": {
                        "select_id": 3,
                        "cost_info": {
                          "query_cost": "1.00"
                        } /* cost_info */,
                        "table": {
                          "table_name": "s",
                          "access_type": "ALL",
                          "rows_examined_per_scan": 5,
                          "rows_produced_per_join": 5,
                          "filtered": "100.00",
                          "cost_info": {
                            "read_cost": "0.50",
                            "eval_cost": "0.50",
                            "prefix_cost": "1.00",
                            "data_read_per_join": "40"
                          } /* cost_info */,
                          "used_columns": [
                            "a"
                          ] /* used_columns */
                        } /* table */
                      } /* query_block */
                    },
                    {
                      "dependent": false,
                      "cacheable": true,
                      "query_block": {
                        "select_id": 4,
                        "message": "No tables used"
                      } /* query_block */
                    }
                  ] /* query_specifications */
                } /* union_result */
              } /* query_block */
            ] /* query_specifications */
          } /* union_result */
        } /* query_block */
      ] /* query_specifications */
    } /* unary_result */
  } /* query_block */
}
Warnings:
Note	1003	(/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` union (/* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s` order by `test`.`s`.`a` limit 1) union (/* select#3 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#4 */ select 2 AS `2` limit 3) order by `a` limit 3) order by `a`
# For comparison
EXPLAIN FORMAT=tree ( ( SELECT * FROM r UNION
(SELECT * FROM s ORDER BY a LIMIT 1)) UNION
( SELECT * FROM s UNION SELECT 2 LIMIT 3)
ORDER BY 1 LIMIT 3)
ORDER BY 1;
EXPLAIN
-> Sort: a  (rows=3)
    -> Table scan on <result temporary>  (rows=3)
        -> Temporary table  (rows=3)
            -> Limit: 3 row(s)  (rows=3)
                -> Sort: a, limit input to 3 row(s) per chunk  (rows=3)
                    -> Table scan on <union temporary>  (rows=8)
                        -> Union materialize with deduplication  (rows=8)
                            -> Table scan on r  (rows=4)
                            -> Limit: 1 row(s)  (rows=1)
                                -> Sort: s.a, limit input to 1 row(s) per chunk  (rows=5)
                                    -> Table scan on s  (rows=5)
                            -> Limit: 3 row(s)  (rows=3)
                                -> Table scan on <union temporary>  (rows=6)
                                    -> Union materialize with deduplication  (rows=6)
                                        -> Limit table size: 3 unique row(s)
                                            -> Table scan on s  (rows=5)
                                        -> Limit table size: 3 unique row(s)
                                            -> Rows fetched before execution  (rows=1)

# Test no result if no materializaton
EXPLAIN FORMAT=json SELECT 1 UNION ALL SELECT 2;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": false,
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "message": "No tables used"
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "message": "No tables used"
          } /* query_block */
        }
      ] /* query_specifications */
    } /* union_result */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select 1 AS `1` union all /* select#2 */ select 2 AS `2`
# Used to not show sorting before WL#11350
EXPLAIN FORMAT=json (SELECT a FROM r LIMIT 2) ORDER BY 1;
EXPLAIN
{
  "query_block": {
    "unary_result": {
      "using_temporary_table": true,
      "select_id": 2,
      "table_name": "<ordered1>",
      "access_type": "ALL",
      "using_filesort": true,
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "0.90"
            } /* cost_info */,
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 4,
              "rows_produced_per_join": 4,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.40",
                "prefix_cost": "0.90",
                "data_read_per_join": "32"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        }
      ] /* query_specifications */
    } /* unary_result */
  } /* query_block */
}
Warnings:
Note	1003	(/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` limit 2) order by `a`
EXPLAIN FORMAT=json ((SELECT a FROM t ORDER BY 1 limit 3) ORDER BY -a limit 2) order by a;
EXPLAIN
{
  "query_block": {
    "unary_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<ordered2>",
      "access_type": "ALL",
      "using_filesort": true,
      "query_specifications": [
        {
          "unary_result": {
            "using_temporary_table": true,
            "select_id": 2,
            "table_name": "<ordered/limited1>",
            "access_type": "ALL",
            "using_filesort": true,
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 1,
                  "cost_info": {
                    "query_cost": "1.00"
                  } /* cost_info */,
                  "ordering_operation": {
                    "using_filesort": true,
                    "table": {
                      "table_name": "t",
                      "access_type": "ALL",
                      "rows_examined_per_scan": 5,
                      "rows_produced_per_join": 5,
                      "filtered": "100.00",
                      "cost_info": {
                        "read_cost": "0.50",
                        "eval_cost": "0.50",
                        "prefix_cost": "1.00",
                        "data_read_per_join": "40"
                      } /* cost_info */,
                      "used_columns": [
                        "a"
                      ] /* used_columns */
                    } /* table */
                  } /* ordering_operation */
                } /* query_block */
              }
            ] /* query_specifications */
          } /* unary_result */
        } /* query_block */
      ] /* query_specifications */
    } /* unary_result */
  } /* query_block */
}
Warnings:
Note	1003	((/* select#1 */ select `test`.`t`.`a` AS `a` from `test`.`t` order by `test`.`t`.`a` limit 3) order by -(`a`) limit 2) order by `a`
# Test big one
# This query has max nesting and should work
EXPLAIN FORMAT=json (((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((SELECT * FROM r
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s)
UNION SELECT * FROM s);
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": true,
      "select_id": 203,
      "table_name": "<union1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,...,202>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "0.90"
            } /* cost_info */,
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 4,
              "rows_produced_per_join": 4,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.40",
                "prefix_cost": "0.90",
                "data_read_per_join": "32"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 3,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 4,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 5,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 6,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 7,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 8,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 9,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 10,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 11,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 12,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 13,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 14,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 15,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 16,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 17,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 18,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 19,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 20,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 21,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 22,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 23,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 24,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 25,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 26,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 27,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 28,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 29,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 30,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 31,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 32,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 33,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 34,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 35,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 36,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 37,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 38,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 39,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 40,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 41,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 42,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 43,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 44,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 45,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 46,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 47,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 48,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 49,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 50,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 51,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 52,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 53,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 54,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 55,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 56,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 57,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 58,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 59,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 60,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 61,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 62,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 63,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 64,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 65,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 66,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 67,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 68,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 69,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 70,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 71,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 72,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 73,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 74,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 75,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 76,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 77,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 78,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 79,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 80,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 81,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 82,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 83,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 84,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 85,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 86,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 87,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 88,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 89,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 90,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 91,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 92,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 93,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 94,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 95,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 96,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 97,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 98,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 99,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 100,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 101,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 102,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 103,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 104,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 105,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 106,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 107,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 108,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 109,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 110,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 111,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 112,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 113,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 114,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 115,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 116,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 117,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 118,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 119,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 120,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 121,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 122,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 123,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 124,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 125,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 126,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 127,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 128,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 129,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 130,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 131,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 132,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 133,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 134,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 135,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 136,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 137,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 138,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 139,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 140,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 141,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 142,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 143,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 144,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 145,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 146,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 147,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 148,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 149,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 150,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 151,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 152,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 153,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 154,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 155,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 156,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 157,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 158,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 159,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 160,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 161,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 162,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 163,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 164,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 165,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 166,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 167,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 168,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 169,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 170,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 171,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 172,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 173,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 174,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 175,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 176,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 177,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 178,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 179,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 180,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 181,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 182,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 183,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 184,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 185,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 186,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 187,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 188,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 189,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 190,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 191,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 192,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 193,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 194,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 195,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 196,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 197,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 198,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 199,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 200,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 201,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 202,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        }
      ] /* query_specifications */
    } /* union_result */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` union /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#3 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#4 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#5 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#6 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#7 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#8 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#9 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#10 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#11 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#12 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#13 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#14 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#15 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#16 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#17 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#18 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#19 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#20 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#21 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#22 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#23 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#24 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#25 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#26 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#27 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#28 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#29 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#30 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#31 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#32 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#33 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#34 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#35 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#36 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#37 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#38 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#39 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#40 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#41 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#42 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#43 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#44 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#45 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#46 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#47 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#48 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#49 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#50 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#51 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#52 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#53 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#54 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#55 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#56 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#57 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#58 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#59 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#60 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#61 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#62 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#63 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#64 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#65 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#66 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#67 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#68 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#69 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#70 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#71 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#72 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#73 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#74 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#75 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#76 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#77 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#78 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#79 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#80 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#81 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#82 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#83 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#84 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#85 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#86 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#87 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#88 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#89 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#90 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#91 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#92 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#93 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#94 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#95 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#96 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#97 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#98 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#99 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#100 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#101 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#102 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#103 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#104 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#105 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#106 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#107 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#108 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#109 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#110 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#111 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#112 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#113 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#114 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#115 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#116 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#117 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#118 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#119 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#120 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#121 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#122 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#123 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#124 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#125 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#126 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#127 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#128 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#129 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#130 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#131 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#132 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#133 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#134 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#135 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#136 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#137 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#138 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#139 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#140 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#141 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#142 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#143 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#144 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#145 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#146 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#147 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#148 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#149 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#150 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#151 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#152 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#153 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#154 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#155 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#156 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#157 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#158 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#159 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#160 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#161 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#162 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#163 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#164 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#165 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#166 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#167 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#168 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#169 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#170 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#171 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#172 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#173 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#174 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#175 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#176 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#177 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#178 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#179 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#180 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#181 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#182 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#183 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#184 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#185 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#186 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#187 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#188 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#189 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#190 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#191 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#192 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#193 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#194 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#195 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#196 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#197 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#198 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#199 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#200 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#201 */ select `test`.`s`.`a` AS `a` from `test`.`s` union /* select#202 */ select `test`.`s`.`a` AS `a` from `test`.`s`
#
# INTERSECT operator
#
EXPLAIN FORMAT = json SELECT * FROM r INTERSECT SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "intersect_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<intersect1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "0.90"
            } /* cost_info */,
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 4,
              "rows_produced_per_join": 4,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.40",
                "prefix_cost": "0.90",
                "data_read_per_join": "32"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        }
      ] /* query_specifications */
    } /* intersect_result */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` intersect /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r INTERSECT ALL SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "intersect_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<intersect1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "0.90"
            } /* cost_info */,
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 4,
              "rows_produced_per_join": 4,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.40",
                "prefix_cost": "0.90",
                "data_read_per_join": "32"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        }
      ] /* query_specifications */
    } /* intersect_result */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` intersect all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
#
# EXCEPT operator
#
EXPLAIN FORMAT = json SELECT * FROM r EXCEPT SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "except_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<except1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "0.90"
            } /* cost_info */,
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 4,
              "rows_produced_per_join": 4,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.40",
                "prefix_cost": "0.90",
                "data_read_per_join": "32"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        }
      ] /* query_specifications */
    } /* except_result */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r EXCEPT ALL SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "except_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<except1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "0.90"
            } /* cost_info */,
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 4,
              "rows_produced_per_join": 4,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.40",
                "prefix_cost": "0.90",
                "data_read_per_join": "32"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.00"
            } /* cost_info */,
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.50",
                "prefix_cost": "1.00",
                "data_read_per_join": "40"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */
            } /* table */
          } /* query_block */
        }
      ] /* query_specifications */
    } /* except_result */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
DROP TABLE r, s, t;
#
# Bug#34463119 WL#349 Assertion `current_context->type == CTX_JOIN || ..
#              at opt_explain_json.cc
#
CREATE TABLE t(pk int NOT NULL AUTO_INCREMENT, PRIMARY KEY (pk));
EXPLAIN FORMAT=JSON
SELECT * FROM (SELECT * FROM t) AS alias1
JOIN
t AS alias2
WHERE alias1.pk NOT IN (SELECT 2 INTERSECT SELECT 1) AND
alias1.pk = 105;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "no matching row in const table",
    "optimized_away_subqueries": [
      {
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "intersect_result": {
            "using_temporary_table": true,
            "message": "Not optimized, outer query is empty",
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "message": "Not optimized, outer query is empty"
                } /* query_block */
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 4,
                  "message": "Not optimized, outer query is empty"
                } /* query_block */
              }
            ] /* query_specifications */
          } /* intersect_result */
        } /* query_block */
      }
    ] /* optimized_away_subqueries */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select NULL AS `pk`,NULL AS `pk` from `test`.`t` join `test`.`t` `alias2` where (<in_optimizer>(NULL,<exists>(/* select#3 */ select 2 having (<cache>(NULL) = <ref_null_helper>(2)) intersect /* select#4 */ select 1 having (<cache>(NULL) = <ref_null_helper>(1))) is false) and multiple equal(105, NULL))
DROP TABLE t;
#
#  WL#15684 System variable to select JSON EXPLAIN format
#
SET @saved_json_format_version = @@explain_json_format_version;
SET @@end_markers_in_json=OFF;
SET @@explain_json_format_version = 1;
CREATE TABLE t (i INT);
INSERT INTO t VALUES (1), (2), (3);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=JSON SELECT * FROM t WHERE i IN (2, 3) LIMIT 1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    },
    "table": {
      "table_name": "t",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 1,
      "filtered": "50.00",
      "cost_info": {
        "read_cost": "0.65",
        "eval_cost": "0.15",
        "prefix_cost": "0.80",
        "data_read_per_join": "12"
      },
      "used_columns": [
        "i"
      ],
      "attached_condition": "(`test`.`t`.`i` in (2,3))"
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`i` AS `i` from `test`.`t` where (`test`.`t`.`i` in (2,3)) limit 1
SET @@explain_json_format_version = 2;
EXPLAIN FORMAT=JSON SELECT * FROM t WHERE i IN (2, 3) LIMIT 1;
EXPLAIN
{
  "query": "/* select#1 */ select `test`.`t`.`i` AS `i` from `test`.`t` where (`test`.`t`.`i` in (2,3)) limit 1",
  "query_plan": {
    "limit": 1,
    "inputs": [
      {
        "inputs": [
          {
            "operation": "Table scan on t",
            "table_name": "t",
            "access_type": "table",
            "schema_name": "test",
            "used_columns": [
              "i"
            ],
            "estimated_rows": 3.0,
            "estimated_total_cost": 0.8012817382812502
          }
        ],
        "condition": "(t.i in (2,3))",
        "operation": "Filter: (t.i in (2,3))",
        "access_type": "filter",
        "estimated_rows": 1.5,
        "filter_columns": [
          "test.t.i"
        ],
        "estimated_total_cost": 0.8012817382812502
      }
    ],
    "operation": "Limit: 1 row(s)",
    "access_type": "limit",
    "limit_offset": 0,
    "estimated_rows": 1.0,
    "estimated_total_cost": 0.8012817382812502
  },
  "query_type": "select",
  "json_schema_version": "2.0"
}
SET @@explain_json_format_version = 1;
# Check changing system variable in stored procedure works as expected.
CREATE PROCEDURE p (json_format_version INT) BEGIN
EXPLAIN FORMAT=JSON INTO @v1 SELECT * FROM t WHERE i IN (2, 3) LIMIT 1;
SELECT JSON_CONTAINS_PATH(@v1, 'one', '$.query') + 1 AS explain_json_format_version;
SET @stmt_txt = 'EXPLAIN FORMAT=JSON INTO @v1 SELECT * FROM t WHERE i IN (2, 3) LIMIT 1';
PREPARE stmt FROM @stmt_txt;
EXECUTE stmt;
SELECT JSON_CONTAINS_PATH(@v1, 'one', '$.query') + 1 AS explain_json_format_version;
SET @@explain_json_format_version = json_format_version;
EXPLAIN FORMAT=JSON INTO @v1 SELECT * FROM t WHERE i IN (2, 3) LIMIT 1;
SELECT JSON_CONTAINS_PATH(@v1, 'one', '$.query') + 1 AS explain_json_format_version;
EXECUTE stmt;
SELECT JSON_CONTAINS_PATH(@v1, 'one', '$.query') + 1 AS explain_json_format_version;
PREPARE stmt FROM @stmt_txt;
EXECUTE stmt;
SELECT JSON_CONTAINS_PATH(@v1, 'one', '$.query') + 1 AS explain_json_format_version;
END|
SET @@explain_json_format_version = 2;
CALL p(1);
explain_json_format_version
2
explain_json_format_version
2
explain_json_format_version
1
explain_json_format_version
1
explain_json_format_version
1
SELECT @@explain_json_format_version;
@@explain_json_format_version
1
CALL p(2);
explain_json_format_version
1
explain_json_format_version
1
explain_json_format_version
2
explain_json_format_version
2
explain_json_format_version
2
SELECT @@explain_json_format_version;
@@explain_json_format_version
2
DROP PROCEDURE p;
DROP TABLE t;
SET @@end_markers_in_json=ON;
SET @@explain_json_format_version = @saved_json_format_version;
#
# Bug#36134568 Add query type to iterator-based EXPLAIN FORMAT=JSON
#
SET @saved_json_format_version = @@explain_json_format_version;
SET @@explain_json_format_version = 2;
CREATE TABLE t1 (i1 INT PRIMARY KEY, i2 INT);
CREATE TABLE t2 (i3 INT, i4 INT);
INSERT INTO t1 VALUES (1,2), (2,3), (3,4);
INSERT INTO t2 SELECT i2, i1 FROM t1;
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN FORMAT=JSON INTO @v1 SELECT * FROM t1 JOIN t2 ON i1 = i3 WHERE i2 = 2;
SELECT JSON_EXTRACT(@v1, '$.query_type') = 'select';
JSON_EXTRACT(@v1, '$.query_type') = 'select'
1
SET @v1 = NULL;
EXPLAIN FORMAT=JSON INTO @v1 SELECT * FROM t1;
SELECT JSON_EXTRACT(@v1, '$.query_type') = 'select';
JSON_EXTRACT(@v1, '$.query_type') = 'select'
1
SET @v1 = NULL;
EXPLAIN FORMAT=JSON INTO @v1 INSERT INTO t1 VALUES (4,5);
SELECT JSON_EXTRACT(@v1, '$.query_type') = 'insert';
JSON_EXTRACT(@v1, '$.query_type') = 'insert'
1
SET @v1 = NULL;
EXPLAIN FORMAT=JSON INTO @v1 INSERT INTO t1 SELECT * FROM t2;
SELECT JSON_EXTRACT(@v1, '$.query_type') = 'insert';
JSON_EXTRACT(@v1, '$.query_type') = 'insert'
1
SET @v1 = NULL;
EXPLAIN FORMAT=JSON INTO @v1 UPDATE t1 SET i2 = i2 + 1 WHERE i1 = 1;
SELECT JSON_EXTRACT(@v1, '$.query_type') = 'update';
JSON_EXTRACT(@v1, '$.query_type') = 'update'
1
SET @v1 = NULL;
EXPLAIN FORMAT=JSON INTO @v1 REPLACE t1 SELECT * FROM t2;
SELECT JSON_EXTRACT(@v1, '$.query_type') = 'replace';
JSON_EXTRACT(@v1, '$.query_type') = 'replace'
1
SET @v1 = NULL;
EXPLAIN FORMAT=JSON INTO @v1 DELETE FROM t1;
SELECT JSON_EXTRACT(@v1, '$.query_type') = 'delete';
JSON_EXTRACT(@v1, '$.query_type') = 'delete'
1
SET @v1 = NULL;
EXPLAIN FORMAT=JSON INTO @v1 UPDATE t1, t2 SET i1 = i1 - 1, i3 = i3 + 1;
SELECT JSON_EXTRACT(@v1, '$.query_type') = 'update';
JSON_EXTRACT(@v1, '$.query_type') = 'update'
1
SET @v1 = NULL;
EXPLAIN FORMAT=JSON INTO @v1 DELETE t1, t2 FROM t1, t2;
SELECT JSON_EXTRACT(@v1, '$.query_type') = 'delete';
JSON_EXTRACT(@v1, '$.query_type') = 'delete'
1
DROP TABLE t1, t2;
SET @v1=NULL;
SET @@explain_json_format_version = @saved_json_format_version;
#
# Bug#37126176 Add lookup references to iterator-based EXPLAIN FORMAT=JSON for index lookups
#
SET @saved_json_format_version = @@explain_json_format_version;
SET @@explain_json_format_version = 2;
SET @v1 = NULL;
CREATE TABLE t (pk INT PRIMARY KEY AUTO_INCREMENT, i INT DEFAULT NULL, INDEX idx(i));
INSERT INTO t(i) VALUES (3), (2), (1), (NULL);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
# Index lookup shows correct reference type in "lookup_references".
EXPLAIN FORMAT=JSON INTO @v1 SELECT i FROM t WHERE i = 1;
SELECT JSON_EXTRACT(@v1,
CONCAT(
SUBSTRING_INDEX(index_access_type_path, '.',
CHAR_LENGTH(index_access_type_path)
-
CHAR_LENGTH(REPLACE(index_access_type_path, '.', ''))),
'.lookup_references[0]'))
= "const"
  AS index_lookup_references_const
FROM (SELECT JSON_UNQUOTE(JSON_SEARCH(@v1, 'one', 'index_lookup')) AS index_access_type_path) AS t;
index_lookup_references_const
1
SET @v1 = NULL;
EXPLAIN FORMAT=JSON INTO @v1 SELECT t1.pk FROM t t1, t t2 WHERE t1.pk = t2.pk + 1;
SELECT JSON_EXTRACT(@v1,
CONCAT(
SUBSTRING_INDEX(index_access_type_path, '.',
CHAR_LENGTH(index_access_type_path)
-
CHAR_LENGTH(REPLACE(index_access_type_path, '.', ''))),
'.lookup_references[0]'))
= "func"
  AS index_lookup_references_func
FROM (SELECT JSON_UNQUOTE(JSON_SEARCH(@v1, 'one', 'index_lookup')) AS index_access_type_path) AS t;
index_lookup_references_func
1
SET @v1 = NULL;
EXPLAIN FORMAT=JSON INTO @v1 SELECT t1.pk FROM t t1, t t2 WHERE t1.pk = t2.pk;
SELECT JSON_EXTRACT(@v1,
CONCAT(
SUBSTRING_INDEX(index_access_type_path, '.',
CHAR_LENGTH(index_access_type_path)
-
CHAR_LENGTH(REPLACE(index_access_type_path, '.', ''))),
'.lookup_references[0]'))
IN ("test.t1.pk", "test.t2.pk")
AS index_lookup_references_column
FROM (SELECT JSON_UNQUOTE(JSON_SEARCH(@v1, 'one', 'index_lookup')) AS index_access_type_path) AS t;
index_lookup_references_column
1
SET @v1 = NULL;
EXPLAIN FORMAT=JSON INTO @v1 SELECT * FROM t WHERE i = 1 OR i IS NULL;
SELECT JSON_UNQUOTE(JSON_EXTRACT(@v1,
CONCAT(
SUBSTRING_INDEX(index_access_type_path, '.',
CHAR_LENGTH(index_access_type_path)
-
CHAR_LENGTH(REPLACE(index_access_type_path, '.', ''))),
'.lookup_condition')))
LIKE "% or NULL"
  AS is_ref_or_null
FROM (SELECT JSON_UNQUOTE(JSON_SEARCH(@v1, 'one', 'index_lookup')) AS index_access_type_path) AS t;
is_ref_or_null
1
SET @v1 = NULL;
SET @saved_optimizer_switch = @@optimizer_switch;
SET optimizer_switch='batched_key_access=on,mrr=on,mrr_cost_based=off';
EXPLAIN FORMAT=JSON INTO @v1 SELECT /*+ BKA(t2) */ * FROM t t1 LEFT JOIN t t2 ON t1.pk = t2.pk AND t1.i = 1 AND t1.i = 2;
SELECT JSON_EXTRACT(@v1,
CONCAT(
SUBSTRING_INDEX(index_access_type_path, '.',
CHAR_LENGTH(index_access_type_path)
-
CHAR_LENGTH(REPLACE(index_access_type_path, '.', ''))),
'.lookup_references')) IS NOT NULL
AS mrr_has_lookup_references
FROM (SELECT JSON_UNQUOTE(JSON_SEARCH(@v1, 'one', 'multi_range_read')) AS index_access_type_path) AS t;
mrr_has_lookup_references
1
SET optimizer_switch = @saved_optimizer_switch;
SET @v1 = NULL;
DROP TABLE t;
# Fulltext search has "lookup_references", should only show "const"
CREATE TABLE t (a VARCHAR(200), b TEXT, FULLTEXT (a,b)) ENGINE = InnoDB charset utf8mb4;
INSERT INTO t VALUES  ('This is a sample text', 'I made up for this test.'),
('We want to show that fulltext', 'search references a constant in the "lookup_condition".'),
('In this test the EXPLAIN output', 'should contain a field called "lookup_references".'),
('"lookup_references" should be an array.', 'That array should contain an element that is "const".'),
('Function MATCH ... AGAINST()','is used to do a fulltext search.'),
('Fulltext searches in MySQL', 'are confusing.');
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=JSON INTO @v1 SELECT * FROM t WHERE MATCH(a,b) AGAINST ("fulltext");
SELECT JSON_EXTRACT(@v1,
CONCAT(
SUBSTRING_INDEX(index_access_type_path, '.',
CHAR_LENGTH(index_access_type_path)
-
CHAR_LENGTH(REPLACE(index_access_type_path, '.', ''))),
'.lookup_references[0]'))
= "const"
  AS fulltext_search_references_const
FROM (SELECT JSON_UNQUOTE(JSON_SEARCH(@v1, 'one', 'full_text_search')) AS index_access_type_path) AS t;
fulltext_search_references_const
1
SET @v1 = NULL;
DROP TABLE t;
SET @@explain_json_format_version = @saved_json_format_version;
#
# Bug#35239659 Separate query attributes and iterator attributes in iterator-based EXPLAIN JSON
#
SET @saved_json_format_version = @@explain_json_format_version;
SET @@explain_json_format_version = 2;
CREATE TABLE t (i INT);
EXPLAIN FORMAT=TREE SELECT * FROM t t1 JOIN t t2 WHERE t1.i = t2.i;
EXPLAIN
-> Zero rows (no matching row in const table)  (cost=0..0 rows=0)

EXPLAIN FORMAT=JSON INTO @var SELECT * FROM t t1 JOIN t t2 WHERE t1.i = t2.i;
SELECT JSON_KEYS(@var);
JSON_KEYS(@var)
["query", "query_plan", "query_type", "json_schema_version"]
DROP TABLE t;
SET @var = NULL;
SET @@explain_json_format_version = @saved_json_format_version;
set optimizer_switch=default;
