/*
 * Copyright <PERSON>, 2019
 * Copyright <PERSON>, 2020
 * Use, modification and distribution are subject to the
 * Boost Software License, Version 1.0. (See accompanying file
 * LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 */
// THIS FILE GENERATED BY EXAMPLE/DAUBECHIES_SCALING_INTEGER_GRID.CPP, DO NOT EDIT.
#ifndef BOOST_MATH_DAUBECHIES_SCALING_INTEGER_GRID_HPP
#define BOOST_MATH_DAUBECHIES_SCALING_INTEGER_GRID_HPP
#include <array>
#include <cfloat>
#include <boost/math/tools/config.hpp>
/*
In order to keep the character count as small as possible and speed up
compiler parsing times, we define a macro C_ which appends an appropriate
suffix to each literal, and then casts it to type Real.
The suffix is as follows:

* Q, when we have __float128 support.
* L, when we have either 80 or 128 bit long doubles.
* Nothing otherwise.
*/

#ifdef BOOST_MATH_USE_FLOAT128
#  define C_(x) static_cast<Real>(x##Q)
#elif (LDBL_MANT_DIG > DBL_MANT_DIG)
#  define C_(x) static_cast<Real>(x##L)
#else
#  define C_(x) static_cast<Real>(x)
#endif

namespace boost::math::detail {

template <typename Real, int p, int order> struct daubechies_scaling_integer_grid_imp;

template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 2, 0> { static inline constexpr std::array<Real, 4> value = { C_(0x0p+0), C_(0x1.5db3d742c265539d92ba16b83c5cp+0), C_(-0x1.76cf5d0b09954e764ae85ae0f17p-2), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 2, 1> { static inline constexpr std::array<Real, 4> value = { C_(0x0p+0), C_(0x1p+0), C_(-0x1p+0), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 3, 0> { static inline constexpr std::array<Real, 6> value = { C_(0x0p+0), C_(0x1.494d414ee19a0fc1701f9345a28bp+0), C_(-0x1.8b18d8251ec886399fc357ab26c9p-2), C_(0x1.863743274d78cfe42daf1d262e43p-4), C_(0x1.158087f14084ceb4f650d2c442e1p-8), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 3, 1> { static inline constexpr std::array<Real, 6> value = { C_(0x0p+0), C_(0x1.a3719cd426dbd5c2283e8b6cd98bp+0), C_(-0x1.1dcb0537f52904105ab1d13c6abfp+1), C_(0x1.19ae7cc6c0211df5e41745563cbbp-1), C_(0x1.69a5e70c6cb46c73632e8c1bb2d3p-5), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 3, 2> { static inline constexpr std::array<Real, 6> value = { C_(0x0p+0), C_(0x1.cef9cbb90bf242979b344cdd1e02p-1), C_(-0x1.b676b19591eb63e368ce734bad03p+0), C_(0x1.6ced632b23d6c7c6d19ce6975a06p-1), C_(0x1.8831a237a06deb43265d99170ff1p-4), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 4, 0> { static inline constexpr std::array<Real, 8> value = { C_(0x0p+0), C_(0x1.01d5e443d831d2252369827793d3p+0), C_(-0x1.15313c61b3acb474231dab078158p-5), C_(0x1.447d293e37264e578b865081223fp-5), C_(-0x1.817e96e0425ed094fa1a3a5cf10cp-7), C_(-0x1.3a0992ca5111744ed1b4ed1a6607p-10), C_(0x1.3be7b6cb6309fefbda0c9fa167f8p-16), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 4, 1> { static inline constexpr std::array<Real, 8> value = { C_(0x0p+0), C_(0x1.c6aca7b3a61af838bb320835a00cp+0), C_(-0x1.6486543c8460b11f1a4b4a357fbcp+1), C_(0x1.314491c6de2d2b798153746ca822p+0), C_(-0x1.0d0e14f1f7dc5a54f21ce3ff424bp-3), C_(-0x1.b6da96c702377f43a25f74c9467p-5), C_(0x1.d0194bee1a1742cd9ebfbba717b8p-10), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 4, 2> { static inline constexpr std::array<Real, 8> value = { C_(0x0p+0), C_(0x1.cf8cc69cc42a410e51b272a7a3c6p+0), C_(-0x1.0eaaa0c1e1c354610b35262e9078p+2), C_(0x1.66e46b718b500bb2be3ec05c7ac3p+1), C_(-0x1.26d9a4de19b73d34cbab42cdd8ccp-3), C_(-0x1.0ae7c18dd841d8689ede2f52570cp-2), C_(0x1.3a82a1b96295b447f46661fe5bd8p-6), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 4, 3> { static inline constexpr std::array<Real, 8> value = { C_(0x0p+0), C_(0x1.bb2f43bc30b80d947c8a537a20b2p-1), C_(-0x1.2d48b852668c646a630392a2b66dp+1), C_(0x1.c2596fe640cadf6ca968f3b30ff3p+0), C_(0x1.83800be8c4de9681c9e31925ce79p-3), C_(-0x1.233972e07202850d1be1b9fc8392p-1), C_(0x1.bcd16d394575254330d81aed4886p-4), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 5, 0> { static inline constexpr std::array<Real, 10> value = { C_(0x0p+0), C_(0x1.646bf1ec64a308c5df07d89c8e9bp-1), C_(0x1.cbd5c1bab5148530b0e77ecbff92p-2), C_(-0x1.754196833f706c7834855fa5579ep-3), C_(0x1.3100cab7c3f5f4fa75b80a67ed4bp-5), C_(0x1.8dd2be8c89c51e7b28b04315346bp-10), C_(-0x1.c4ab558ff2dcebdfdc6142054ddep-10), C_(0x1.3b27d2d798eecea1adaf099c04dp-15), C_(0x1.75f2b16626e9875840e692dcf344p-23), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 5, 1> { static inline constexpr std::array<Real, 10> value = { C_(0x0p+0), C_(0x1.8eee7927087b240ec5ade1ac81aep+0), C_(-0x1.37cf445237f1b72a99ae1be2616ep+1), C_(0x1.3c64475174b4a0c9b8a3ee3dfc2p+0), C_(-0x1.7841978e876db6599aa4ad10992fp-2), C_(-0x1.64d969ffaac153a6af06dc9cfb72p-6), C_(0x1.08fcfb4783fa7d234eba36053968p-5), C_(-0x1.5e1fef6185af966378a125175a7fp-10), C_(-0x1.984da1089743964ff5ac0959c3bcp-17), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 5, 2> { static inline constexpr std::array<Real, 10> value = { C_(0x0p+0), C_(0x1.22c5cb8d3f23b44faf592c365f5dp+1), C_(-0x1.83cca07a3ead78a73330d7cf1a39p+2), C_(0x1.5245172406b77d1d4fdfd2192d87p+2), C_(-0x1.2d10dba463fc1840fd45963c568fp+0), C_(-0x1.549f4c5e04f3991456e630b1555ep-1), C_(0x1.882ac9029ddc72b4c74d388da98p-2), C_(-0x1.3d5d0eda32d8b61e7ff27a395899p-5), C_(-0x1.65cdc06ecf6547344a6a8d527f6ap-11), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 5, 3> { static inline constexpr std::array<Real, 10> value = { C_(0x0p+0), C_(0x1.9877ac926fb3bdd992f46d848f57p+1), C_(-0x1.48c27a70b93cdddc043571f47b88p+3), C_(0x1.f7fdf60845cfba22e0c6bd043e1ep+2), C_(0x1.0b1ea0ee5f9ed06f5107d79a1aafp+3), C_(-0x1.15aaf38ada16a6358cd77355f075p+4), C_(0x1.4a45041b91e1bb24fe9a63ad188p+3), C_(-0x1.05013480812df4c7388d78daa7bap+1), C_(-0x1.13ee46114fd8519c5d7f29b7d91dp-4), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 5, 4> { static inline constexpr std::array<Real, 10> value = { C_(0x0p+0), C_(0x1.13b9e8c4fdc4ff9d9c7b93fda5d3p-1), C_(-0x1.e1e5e5cfd0a801f531eaef0fe6ap+0), C_(0x1.29ab2ec864d44a65c8f8abfdabb3p+1), C_(-0x1.3d6cc61e0f6a43e21f6553d10792p+0), C_(0x1.0227f72aa277daecfc1fddb3c5aep-1), C_(-0x1.9ada2a6a9a217e8dbe2b55c6bb8bp-2), C_(0x1.2c68da3893cd5c4c84ac50c60f2ap-3), C_(0x1.1a66dc6d2cfbf029033eb2e70439p-7), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 6, 0> { static inline constexpr std::array<Real, 12> value = { C_(0x0p+0), C_(0x1.bf65e79817d27ca7c78972c65f3p-2), C_(0x1.aa2466d50e4a4fcbbebe54f94c3cp-1), C_(-0x1.89fd104c6ff149bf7514a7ddf37fp-2), C_(0x1.24737e6f8ebf0ec8bb9fcc85bb71p-3), C_(-0x1.a1e9c758a51a08321aa63e9af1d7p-6), C_(-0x1.cec09a7ccf05b6ee1ea8f3c17f4ep-9), C_(0x1.cd4feb82d24938f30e41e1af2eap-10), C_(0x1.05937b8388af24ac43ec83967a2ep-16), C_(-0x1.5a00cfad970a7cbc309138fd3facp-19), C_(0x1.0fbc42c672231485a0bcbfe7e34bp-28), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 6, 1> { static inline constexpr std::array<Real, 12> value = { C_(0x0p+0), C_(0x1.2faeacbb8e753b40eb856e00f522p+0), C_(-0x1.8014975295b2136d5a1adb454387p+0), C_(0x1.05033635f5401c9b60636a32526ap-1), C_(-0x1.0ffe44cc8bc7ea4a6ce4734d8d36p-2), C_(0x1.0b11b28c7d67ad63229fbc3aa7dap-4), C_(0x1.721a1d14b8322b2431432e8b800fp-7), C_(-0x1.af662c52c3b7ae55063672df3c7fp-8), C_(-0x1.a69b860b67a33179a8c7a5f6b2acp-15), C_(0x1.45b12643cf8203bdf796367d51eep-16), C_(-0x1.01892163486cb41cb64453f7616fp-24), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 6, 2> { static inline constexpr std::array<Real, 12> value = { C_(0x0p+0), C_(0x1.1e62ddd540b05b0cc5ce587007d5p+1), C_(-0x1.980b3fe2011f7bc878be7d399794p+2), C_(0x1.a34dea8cd59b4187717c6efb0662p+2), C_(-0x1.45eb5e5e7e3c9e9ae26ca4aa207cp+1), C_(-0x1.0310f5a5d86de60ba877dea685a5p-1), C_(0x1.da7724c65802bc95482213f980a5p-1), C_(-0x1.4137cfe85b9e5b04e10239758a3cp-2), C_(0x1.7eb866ddca6ba425f9e4eb854a4dp-6), C_(0x1.4ebb22d1ac0760e1461881059843p-9), C_(-0x1.0c5bdfb916a449da5acc3ccfdc72p-16), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 6, 3> { static inline constexpr std::array<Real, 12> value = { C_(0x0p+0), C_(0x1.422f9f5227e7facaa94dd7d9e31p-2), C_(-0x1.255a76df553cbf84e8a0b0613039p+0), C_(0x1.484b537c56deca3daefc656e3b6ep+3), C_(-0x1.3c98d8a74cd7736b60af4e604261p+5), C_(0x1.10b7f9c607064185ab2dfc58353fp+6), C_(-0x1.d54dd14cd31af65e5343fc875b57p+5), C_(0x1.8049478b6740d0c6b010047a06c3p+4), C_(-0x1.77f8847acd28ad3549099ae2105ep+1), C_(-0x1.d3687a3bc39335bac7f281e76bbep-2), C_(0x1.816ef86832621ebc63e47cd2b70fp-8), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 6, 4> { static inline constexpr std::array<Real, 12> value = { C_(0x0p+0), C_(0x1.8faeef62b3a7fd26eb98dd7f984ap+0), C_(-0x1.93816e52ba1c8483ff81ea3e324ep+2), C_(0x1.337880b3a9a717e67b9cd7fec812p+3), C_(-0x1.c3f2cd3d53132aa78f87aee07639p+2), C_(0x1.c7801db7678046e8ebdb22cf642bp+1), C_(-0x1.46377db27f624959a2b9b7777bb1p+1), C_(0x1.77a6f5e8321cd39a930db39033fap+0), C_(-0x1.c4188733407f241acae534ed0f5dp-3), C_(-0x1.f8b0a606c7f795229d5947447836p-5), C_(0x1.b965933f534b1af276b67227f89dp-10), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 6, 5> { static inline constexpr std::array<Real, 12> value = { C_(0x0p+0), C_(0x1.cb91942378a6b29e2dd51c51bfa9p-2), C_(-0x1.e6b982120ca31c68b89329c09872p+0), C_(0x1.7b2e4714b270342ea6ba899785ecp+1), C_(-0x1.fa645ee8ca231003817fbe32a47bp+0), C_(0x1.29da9349c32a19bed5b810cc4b88p-1), C_(-0x1.8461e6acf9cb2af90934d76c7932p-2), C_(0x1.67c137342024d6c8052e3d876acdp-2), C_(-0x1.c1a09b7773a59d1a65f356773daap-5), C_(-0x1.0c11f4efd5c0b7d44ac1c3a0720ap-5), C_(0x1.0ac90a45b7a47759a7a92738fc7ep-9), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 7, 0> { static inline constexpr std::array<Real, 14> value = { C_(0x0p+0), C_(0x1.033291a6c76d6a2177301a6491e8p-2), C_(0x1.027f6bf7952cdbcf397ade5b83ap+0), C_(-0x1.919d92c6034c44a7ed45a578713cp-2), C_(0x1.7a0ca906acc4841a1191b023a485p-3), C_(-0x1.1142c3515ec433047c393c69962fp-4), C_(0x1.50e2d6421ee2654230b309723685p-7), C_(0x1.d0243aab613f83777184da54a80dp-10), C_(-0x1.1d4c6c249bf0ee08cde9105ae0e2p-11), C_(-0x1.17d0b3032ba5c5edbe3115298755p-14), C_(0x1.310ffd8b564011dafec30497e547p-19), C_(-0x1.509d126dab99ceafee21c5b15f2ep-25), C_(-0x1.57f8e6955253718d03dd42dcc06ep-36), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 7, 1> { static inline constexpr std::array<Real, 14> value = { C_(0x0p+0), C_(0x1.9ec434341eaed8ccc6e8d7244a33p-1), C_(-0x1.ca0692ca77439e8f752ca148c144p-2), C_(-0x1.2d988a986b0dea756341b6b8b7c2p-1), C_(0x1.145887eacb10f9d392eed69a9335p-2), C_(-0x1.4ab3cf0bae2a5b8432a0a181639cp-6), C_(-0x1.4e8938899ee371e40cd7b7528ae9p-5), C_(0x1.5cb5de436bfadb9e555d33c55719p-6), C_(-0x1.051eebbefaf7bd71954e297a61ffp-8), C_(0x1.0efaec65edbcc11b181ba7073e2cp-14), C_(0x1.6a87a6b931edc5ab5423dbf1babfp-15), C_(-0x1.4e8b2a27fe064ec01f627183042bp-23), C_(-0x1.54fd8f0625f118cd9bcdded88d81p-33), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 7, 2> { static inline constexpr std::array<Real, 14> value = { C_(0x0p+0), C_(0x1.e0eacca617ebb860573873f7b1a4p+0), C_(-0x1.5362d7fe36c56b6a443e9d6141a8p+2), C_(0x1.70aa17b7adc0e75a9ffe0b5a33b6p+2), C_(-0x1.86bc52429f3a3f79bb4f1cb73763p+1), C_(0x1.5709310e8d1f635f77e733f31613p-2), C_(0x1.9d599937b31fafd3d66db2bf1cbbp-1), C_(-0x1.18b244e50df675a487d76601b0e9p-1), C_(0x1.fe16d1a3cfb3e7ecc6834f2061cfp-4), C_(0x1.eb4cc917dd1cd56dce6a9280b03bp-12), C_(-0x1.4e09cf12f0cdcfd873bae5583582p-9), C_(0x1.c4a78957d279804ccd5115f6146ap-16), C_(0x1.cb0ccbca08e7961d7bc2f676b666p-25), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 7, 3> { static inline constexpr std::array<Real, 14> value = { C_(0x0p+0), C_(0x1.4ca9343b3236825367445e2a7e79p-1), C_(-0x1.492e70f2635c16c262e6190afacep+1), C_(0x1.171de5ca88cec2afbea5499914ebp+4), C_(-0x1.1e704d84a10750732762c3f4d8bap+6), C_(0x1.23bcc34d6a529b0b422faf672516p+7), C_(-0x1.415156a34428a6a6c08cde7dee49p+7), C_(0x1.7ff7929036ddd78bf54cfc378272p+6), C_(-0x1.a43b4147c122c6f143d7b591007fp+4), C_(0x1.833e6f5afaca4c566ce598593c06p-4), C_(0x1.13b5aaaf6c71d5b914583e48ae7dp+0), C_(-0x1.6365af2495a86c5f48d9bd1a47dp-6), C_(-0x1.64d19861a833792a3ce80f84d83cp-14), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 7, 4> { static inline constexpr std::array<Real, 14> value = { C_(0x0p+0), C_(0x1.525f0cb11c2720485c66b9e19f02p+1), C_(-0x1.7eda4b6a23328f4e2cec098f6666p+3), C_(0x1.578437542fe4f5fbe4cefec73f0cp+4), C_(-0x1.40c582473fa8c93703a1216d1613p+4), C_(0x1.9815cb7223c105bc2b63ed01a5fep+3), C_(-0x1.222479cd96ee9d17f59bff4df592p+3), C_(0x1.8c2cccc6ee34ca0f24458389d35dp+2), C_(-0x1.ffd4fb5b3918044492967474d1cep+0), C_(-0x1.3be427404fe7d12432d600f4382ep-3), C_(0x1.7c490d447fad2e27fa6b12a0d504p-3), C_(-0x1.23f9756392d0ee9915448c1be2b1p-7), C_(-0x1.1f66b1e7815295f48755fd4e3126p-14), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 7, 5> { static inline constexpr std::array<Real, 14> value = { C_(0x0p+0), C_(0x1.80a316d21a0eed480c84ea711d9ep+0), C_(-0x1.ce7e6827143677fbd12dabe06e9fp+2), C_(0x1.ac8b3ccd718c41df56b9c167f638p+3), C_(-0x1.74c81d1a27df5c33454de2219e89p+3), C_(0x1.4010d1eedca99cac8148c11888d4p+2), C_(-0x1.34da807d2aaec910bcf3aa770bf1p+1), C_(0x1.2d09c9846b581b01a4323f49de6p+1), C_(-0x1.97de12ea03ca10913f6e6b1c03cap-1), C_(-0x1.9df5b4fe2e271a26f87f7a1f78fbp-2), C_(0x1.16c54a229aae12ed509d340bab6bp-2), C_(-0x1.e323c5b92bd166e33ab5b4f9635p-6), C_(-0x1.c9a573f22c19a9032c5430df4c8ep-12), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 7, 6> { static inline constexpr std::array<Real, 14> value = { C_(0x0p+0), C_(0x1.a096c8f7fc8ab1575820ee7a8d4ep-3), C_(-0x1.01d8c748e11fa735da05e73f8394p+0), C_(0x1.099a073a7c7b41898468a5bd14cep+1), C_(-0x1.40ce386ca776e06ba6f31ba7dadep+1), C_(0x1.2a57634989c51fb583fc11fc2c8bp+1), C_(-0x1.e6f030cb23fbfde6dfddcd33a7b8p+0), C_(0x1.3544c2755b1d82eac792a5860a61p+0), C_(-0x1.6af581a622d416ac06a3c40b5b79p-1), C_(0x1.256afda077267e050d8164debfb4p-1), C_(-0x1.6569331439297d46e21a065d5029p-2), C_(0x1.4b88409bdd1172b9682df8963184p-4), C_(0x1.24061e2653bdb3c5fd153e823b33p-9), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 8, 0> { static inline constexpr std::array<Real, 16> value = { C_(0x0p+0), C_(0x1.184a0c288d22add7ea26104df2f3p-3), C_(0x1.fbaa047a94a613069e6f550b4e09p-1), C_(-0x1.596f5b6b28cb0681bf78a3e54b72p-3), C_(0x1.234875f46000e28dce7c3f342092p-4), C_(-0x1.90a568ef3b94afacf755834bab9ap-5), C_(0x1.82b42ea0f8a1613dc5ff9725c05p-6), C_(-0x1.96b60b10e59567d6b2c6577bad3bp-8), C_(0x1.8430d19335684e7577fd3b699763p-11), C_(-0x1.92df0230079088d5ad0746147153p-14), C_(0x1.da6faa767962723e71f08f2e9083p-16), C_(0x1.b237bc2c3b5257b1d937078b7396p-20), C_(0x1.66eb1a28068ae3ea1bd5d2683257p-25), C_(-0x1.f6ebd7398c4a3aa441e024a5714bp-33), C_(0x1.5690727034a8a4b1353895ae261dp-45), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 8, 1> { static inline constexpr std::array<Real, 16> value = { C_(0x0p+0), C_(0x1.03033f41d994757a2b4934423538p-1), C_(0x1.8347da7a3030b8b37cdc36ec3c9cp-2), C_(-0x1.7d2f17f2a95af6988be8eca125f4p+0), C_(0x1.c3013eaebde946f9eaa42bce8cf9p-1), C_(-0x1.4be40ed86f2cd0c168ad249492a4p-2), C_(0x1.4580356ca34ff1cf3c18408b1d74p-6), C_(0x1.7e9e8eefc30b45959dcb8ab52383p-5), C_(-0x1.594cf912461bb959724fb77ad3bbp-6), C_(0x1.27c56651be6ed873712071c19324p-9), C_(0x1.e972a09b0f4dc8fdb93114f9ed4p-12), C_(-0x1.1649ec5e541409b8483ac00aa989p-14), C_(0x1.c99c42a59648ab096d412a58741fp-23), C_(0x1.81519dee82f001d0cfd286a4bf1ep-26), C_(-0x1.06b5ee4f0711fc96e3337209d4e2p-37), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 8, 2> { static inline constexpr std::array<Real, 16> value = { C_(0x0p+0), C_(0x1.666d532df9def8a39e69d4c9dbd1p+0), C_(-0x1.c0374596ee4d8e750b0b0f065b11p+1), C_(0x1.a43eb9f4500c29913bdae70f2e12p+1), C_(-0x1.d627359f84d250c8825852fe20e7p+0), C_(0x1.891e36789053a132cb4397963bd6p-1), C_(0x1.a23c19ecf2deb44f4177f3e9336bp-6), C_(-0x1.d9c16d05d77872764189280b3978p-3), C_(0x1.b6728d569b4c6b3f03c9c20fd79fp-4), C_(-0x1.66849bebd2ebdfafabb131375ec9p-7), C_(-0x1.fc6301aedca903a123de4bceca69p-9), C_(0x1.7e62ae52149bd7e06a18746f5b91p-11), C_(-0x1.2c21c0130a3eaea0bd3721b2b8d6p-17), C_(-0x1.15f344e8ed1b26069acbf2a1a453p-21), C_(0x1.7bbd836b1bf84d53feb61580dc74p-32), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 8, 3> { static inline constexpr std::array<Real, 16> value = { C_(0x0p+0), C_(0x1.b44c4e267cbb4d43745305f720c9p+1), C_(-0x1.c1f7df3ad7ab79897dce80d62718p+3), C_(0x1.1601a8a979c78f82e398ce8545b9p+4), C_(0x1.e5bffb29b3c52bb885497a60d3d6p+3), C_(-0x1.38f35bf0e9ee0ae5b1772951f4d5p+6), C_(0x1.d1869b3f8e2b44288688d26e70d1p+6), C_(-0x1.70d2038145a54625eb25d875d7e5p+6), C_(0x1.34930dde67aea02e0a703b76a197p+5), C_(-0x1.4b77b4fb3674956338abd1824db4p+2), C_(-0x1.d27f3b25c1fd6ce83b2a8b0a4ac9p+0), C_(0x1.32a2c5a3dfe8119d379b71453288p-1), C_(-0x1.2fc64ff8d3420e31f2a6dd47d615p-7), C_(-0x1.c48021ad9bc1646021c012f5b071p-11), C_(0x1.364c0a7c4c64aef45189b312edb4p-20), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 8, 4> { static inline constexpr std::array<Real, 16> value = { C_(0x0p+0), C_(0x1.afe05c7a6ada1cb6172616d2c6a8p+1), C_(-0x1.0a8c44cdd47bf4e495a5398413fbp+4), C_(0x1.0f6c0325c5e1414a9285f59e5f14p+5), C_(-0x1.306ff1c62c8336aad5c3f9886bcep+5), C_(0x1.da69559cc91130d28dd4b63660e6p+4), C_(-0x1.6a2c1f828fe2d81030f1ad524eb7p+4), C_(0x1.0e28c771588c30581ce7071741b8p+4), C_(-0x1.ee4b3cc46d890789fa8b15b7da8p+2), C_(0x1.050e3e4d4732a1c55c0c971f3557p-1), C_(0x1.0ea6cad7a5a7fae2eab7d74b22a1p+0), C_(-0x1.6566f5ac7d80487971d04a5944efp-2), C_(0x1.0b828ad29e43eb178f6b65b8f31p-6), C_(0x1.3201b5c3bc1c21bf7086981ccb12p-10), C_(-0x1.a6f0242017b1488503cf0ef046e5p-19), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 8, 5> { static inline constexpr std::array<Real, 16> value = { C_(0x0p+0), C_(0x1.77885e6e270a744c868654d7cbb5p+1), C_(-0x1.f5abb22b1b6ebb333fd9f11e8f7cp+3), C_(0x1.0a779dee6c7bddbff2f0d8b9981bp+5), C_(-0x1.164d9f148bde4aa13b66fabbc2fp+5), C_(0x1.2333738e736f124a0ec3de563f7bp+4), C_(-0x1.af306993826a689f7543995893acp+2), C_(0x1.6dba5c59397a4426e98586aad789p+2), C_(-0x1.17ffc928dd1d8bf0f209d0c36abep+1), C_(-0x1.9f39e7400cf5d234f22720c7748ap+1), C_(0x1.d0d0a8f3b805dafc2d2b539d9bb4p+1), C_(-0x1.4ac5cadf9fb14bc48382ff0ecfb1p+0), C_(0x1.049faf6ae689948e66d5e5134564p-3), C_(0x1.5ad3792bdac35c5163486d9087edp-7), C_(-0x1.e6e9a51e36639d68c6db7d7495a4p-15), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 8, 6> { static inline constexpr std::array<Real, 16> value = { C_(0x0p+0), C_(0x1.1912b6ad90d43603284e290418eep+0), C_(-0x1.85c00c80d7a18fcc693e47644de7p+2), C_(0x1.b7d753e3c818997a04ded98f86e3p+3), C_(-0x1.076d50e7f7850b94ae780b4bf285p+4), C_(0x1.92a29d7ac0e1e0002558af869ed1p+3), C_(-0x1.20f516098425bb22664ce4ad84a9p+3), C_(0x1.dcba519c323cedf78dcb7794def8p+2), C_(-0x1.3fe31292805c0462ca96ae20fe5p+2), C_(0x1.5e9b74431b072dba61e526c0393fp+1), C_(-0x1.a6996ae95f76ef890f12b5a6c626p+0), C_(0x1.806826de1640aff2fbfd8d0d7d09p-1), C_(-0x1.ddc83de4acfaf20be5fee513819ep-4), C_(-0x1.e09e81650c6b74fbce41e5378d99p-7), C_(0x1.5c5b71613d71b7692e7e5f0ba23p-13), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 8, 7> { static inline constexpr std::array<Real, 16> value = { C_(0x0p+0), C_(0x1.eae80165a34ad1f2080107f7b7a5p-3), C_(-0x1.5a965a7535366541f0a1afe5172fp+0), C_(0x1.851adfd49d1a76d9d23c35315fc3p+1), C_(-0x1.b139c010d5d8ef58fb69c4f0efd8p+1), C_(0x1.ffefb7038043737a18fc832f1fb7p+0), C_(-0x1.0254056f57103b364cf4bcafdd14p+0), C_(0x1.ff103b26e087e22aa6dbe2498954p-1), C_(-0x1.7a4f70cc2f5d7a6d2a4e62aee34dp-1), C_(0x1.09dce627745a61500e9471981ea7p-2), C_(-0x1.c2f7ce5c8a37b8fcabdaf25d6d6bp-4), C_(0x1.41c46b919c33ee1c6941aae7132bp-4), C_(-0x1.03531c096423a7a88f95e3dc08eep-6), C_(-0x1.c24ed1e998c51356546e2437d6p-9), C_(0x1.5d1ed72a09587166cbb2e531d26cp-14), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 9, 0> { static inline constexpr std::array<Real, 18> value = { C_(0x0p+0), C_(0x1.1d2fbfac23949a1e129ec9f8391bp-4), C_(0x1.b1b6520101e0e6c7ddaa9da59f23p-1), C_(0x1.8b08d5cd4a2ad4b181244736a713p-3), C_(-0x1.4aef16c7e666f719f7698e178af1p-3), C_(0x1.fd1877f1138be38ba53889d724ecp-5), C_(-0x1.7ee87092fa242b1ef789aaf8def8p-8), C_(-0x1.09758849af60512fff4fa06073a6p-7), C_(0x1.3d3ef7388a3ade9f5adf4b3f580dp-8), C_(-0x1.29147421d57112477f4b7b3491f7p-10), C_(0x1.b33acccf47f1bda17f46b8106497p-15), C_(0x1.7e6498cdbddd4dce209effce7062p-16), C_(-0x1.53f9494edf1282277c1de6dc67e4p-19), C_(-0x1.58c1d88e09b30b0364784ebc1f68p-24), C_(0x1.278ba12dc249a4cd49c8d69ab623p-29), C_(-0x1.705a7a4d83450688cb9e189ad73ep-38), C_(-0x1.4fb4ee678fa0e1946f2bc7ed6f9ap-52), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 9, 1> { static inline constexpr std::array<Real, 18> value = { C_(0x0p+0), C_(0x1.2b90f770700937399f9f525fd17p-2), C_(0x1.af96b810375ae30c4c41bf8dddd3p-1), C_(-0x1.d3c16b16ba8799042d28d9c49845p+0), C_(0x1.1690032de8cfad91fdf041ea0eb6p+0), C_(-0x1.1f4f37268ff73d0393fe0f67790ep-1), C_(0x1.82c9256a70f8e96c68d16f2e3327p-3), C_(-0x1.540e9bea7c732170245209a302fbp-7), C_(-0x1.49841c554fed1d5f2443e15a5a95p-6), C_(0x1.96a364065f27ccd19657d67cdf64p-8), C_(0x1.0c49493de5a6ec95d0bb4c3aca89p-11), C_(-0x1.b592fbb7be41b5e92f003f6109fcp-12), C_(0x1.9d3f09be0cea6d8d6f8ed455255ap-16), C_(0x1.8562af0388b7df01536ccb66a5b4p-23), C_(-0x1.13cc2e0d34f2575dd283b068f4bap-24), C_(0x1.2234e02ed8630c149658a9630026p-34), C_(0x1.08644a120d1ef6f0d4b961ae1879p-47), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 9, 2> { static inline constexpr std::array<Real, 18> value = { C_(0x0p+0), C_(0x1.e4c00e361fc67a2e12b083b2df48p-1), C_(-0x1.aad6ffd5d77f153c17f1c8823935p+0), C_(0x1.135c57711e67099c1758b9c717c8p-2), C_(0x1.5b41454ff095722d0f803ea68127p-1), C_(-0x1.c92ba77b267a297500d5b5d44785p-4), C_(-0x1.4f00ea2c2d363149504e045fbe84p-2), C_(0x1.61aa479f9dc67b53226c771010fp-2), C_(-0x1.667619426289632c2e8907dc1851p-3), C_(0x1.7235017b58d31cc6ca2ab84f7b8bp-5), C_(-0x1.85c5f4a449272d8bf24d90db58c1p-10), C_(-0x1.2bb318efa4d23587f50785ea34e5p-9), C_(0x1.b3d73e818e5c9557fa6b95207f02p-12), C_(0x1.bca3f26f194cf877e2358ca6efddp-17), C_(-0x1.261d8947cb7277733f476ea3b7dfp-20), C_(0x1.36693a4632268e426c40140353cep-28), C_(0x1.1a9906065291eec094ff6b8172d8p-40), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 9, 3> { static inline constexpr std::array<Real, 18> value = { C_(0x0p+0), C_(0x1.27d6df36685a79598b6c86ce474dp+1), C_(-0x1.2ded79e41fdb0c43d1210e12de17p+3), C_(0x1.e79fd7c0b1c0ebd84f460885ecf3p+3), C_(-0x1.304a1a210a5f9ce8bc8b8a0cdf19p+3), C_(-0x1.effd2573aa9aafcb53e588a096b7p+2), C_(0x1.7ce72fdc2cb91189ae6d5e090c6bp+4), C_(-0x1.985f0196c72e53e6d1cfdee84e02p+4), C_(0x1.d3cfac07a9d2a8c51198c074937dp+3), C_(-0x1.e1a24f8472a680b7d24d058147a3p+1), C_(-0x1.9b8563bd7c7d7dc5768ba3df017ep-2), C_(0x1.fe4766e3faf53b0d124828083686p-2), C_(-0x1.73f12d4155f35b3e8452b4ff00abp-4), C_(-0x1.20dddf659ee509c6befdf1c7addep-10), C_(0x1.ffd28fb3400bf45efe5c328fc188p-12), C_(-0x1.fa83788d3f78d19a901b7477bae6p-20), C_(-0x1.cc794a76e5f165fc8108c92b057bp-31), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 9, 4> { static inline constexpr std::array<Real, 18> value = { C_(0x0p+0), C_(0x1.c7ced0f66aeaccfcda16588f9d75p+1), C_(-0x1.2ab8c9c81252e46b55777de43246p+4), C_(0x1.4df684dede46d3a014591fcfc934p+5), C_(-0x1.af3bf949d62b9c07b05b4ac4bf21p+5), C_(0x1.8eeb17bed133c367f65a67fc6e9fp+5), C_(-0x1.541e9c27826e49d24f626bb1a338p+5), C_(0x1.12ea47c60dec043f14d3664c7b9cp+5), C_(-0x1.35c8b07214e140ed7f1d446f3113p+4), C_(0x1.d829e1a7628821bc837255b88e76p+1), C_(0x1.69d6e9c20f73cb2d14ff7351adb6p+1), C_(-0x1.0235621854eeef97bfd3d4e3b1cp+1), C_(0x1.b423243b8b46906701ef92e0fa6p-2), C_(-0x1.2e290398a3fe2fc8cc3aebacf78cp-8), C_(-0x1.22b4e2598d44b0dd2f4a293dacc9p-8), C_(0x1.654cdf573874b672ebfe3509d857p-16), C_(0x1.43e711e67867252f78e7e45afce7p-26), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 9, 5> { static inline constexpr std::array<Real, 18> value = { C_(0x0p+0), C_(0x1.1617a4f9d885755cd3477307254fp+2), C_(-0x1.94d9c534a28d3ee0a8bf210cb612p+4), C_(0x1.dd0eb6aa76ffda42c87706ac442ap+5), C_(-0x1.168c0447c1b6e90c330602b02462p+6), C_(0x1.24c19c047007f67f0d2fc486ec63p+5), C_(-0x1.016a68491e4d871ca5058cc8b9fbp+0), C_(-0x1.c94ed28aba0f51cc92c5b4dc5aacp+2), C_(0x1.669c08c9522f315c7b041fb5fb05p+3), C_(-0x1.88dca456f345655b7cb4359a3262p+4), C_(0x1.b958d77e385a9163a2eb3b4f73aep+4), C_(-0x1.eccebab605f4b2d725512d8939ecp+3), C_(0x1.f4375d664ce342e4e51867996bcdp+1), C_(-0x1.2ade5d60c45811382f7ff19a5bd5p-3), C_(-0x1.491f178ce56460d544cbe167fd8cp-4), C_(0x1.5bccddec65e53c1b0416c330319fp-11), C_(0x1.398367062ce373b6810676d67904p-20), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 9, 6> { static inline constexpr std::array<Real, 18> value = { C_(0x0p+0), C_(0x1.3841475e9a3639fcb27c2a85a863p+1), C_(-0x1.dd3c5aa19c52e5c8a273f6da1b5ap+3), C_(0x1.303529a28e46fac3a8f56ea5a2b3p+5), C_(-0x1.a86d9ebb6edc3ca33aa7af7132e4p+5), C_(0x1.7d0e6c4e24170b40a4b9574c4b78p+5), C_(-0x1.25c8439d2df89fa5007905a011dap+5), C_(0x1.f5b3e1ac9966686ed844c7042856p+4), C_(-0x1.815ee05c85e8aa704180612d3a88p+4), C_(0x1.ca4482f4236a5516d7f638f2b575p+3), C_(-0x1.050825dec75a966028608d12e5c4p+3), C_(0x1.1eeca8acc3a791ffedf80be8793bp+2), C_(-0x1.6df69657c972c8ebec75c924bb61p+0), C_(0x1.75a7ac677c4eee273d03fc609c7dp-5), C_(0x1.ebd49f3810c888c69855752e166p-5), C_(-0x1.4344372e1b6fc786ddf90f275fd2p-10), C_(-0x1.20264586c6bb91b0aa0b4d170d57p-18), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 9, 7> { static inline constexpr std::array<Real, 18> value = { C_(0x0p+0), C_(0x1.041f759e862aa2196b99e32b0e5p+0), C_(-0x1.96fe795439508b28db4265486c81p+2), C_(0x1.04a7fd5b5e5923f7f627bd6af411p+4), C_(-0x1.5c5ede521aae5e58438f3722aadep+4), C_(0x1.0b593b29c51bbefd0a21a469361ap+4), C_(-0x1.3ddf512850559b028d83098aa4f2p+3), C_(0x1.12f0bcbe3fa44419a6b52304dc22p+3), C_(-0x1.d4ebfbf56d99633e38e5753902fdp+2), C_(0x1.d31827f76dc61557ae5c1ea99bcdp+1), C_(-0x1.6bf237312f18cf3b573ffc6eb123p+0), C_(0x1.d1d5c0ac4dc0c7147f916f90f5c1p-1), C_(-0x1.794e93c6506dcbe26714d9f89e44p-2), C_(-0x1.2e59e90e0a11c8b7ce9f9105aca7p-6), C_(0x1.26fbfe966bb82001d50ae928dc43p-5), C_(-0x1.e662faa96110f417469ce785ece9p-10), C_(-0x1.a81802ebf94f0a8bc8124e2acfbp-17), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 9, 8> { static inline constexpr std::array<Real, 18> value = { C_(0x0p+0), C_(0x1.9024e0f887aa33deb80194db4775p-3), C_(-0x1.3caa56e1c68f9f7ad07fd686ffdbp+0), C_(0x1.947e636a9e373f489450f999fe93p+1), C_(-0x1.033a79f260cef0d616212b14afddp+2), C_(0x1.51b6c9e0d0443759a67a3c3505fdp+1), C_(-0x1.0bd62fc1e045579c86e940e51c15p+0), C_(0x1.028bee5b6619ea0406791e99a571p+0), C_(-0x1.1620d9bfc03eb662d8d654bb6c17p+0), C_(0x1.b5ef819dccd9a27cb2716f32a404p-2), C_(-0x1.b7954b06be8aeb961960232f51e9p-6), C_(0x1.054d18d1cc90c79a1ef036069b3dp-4), C_(-0x1.4817258dd284697ebc00b788ccd7p-5), C_(-0x1.8ce86e181d4254ae37719aa28183p-6), C_(0x1.2926b8981e2a739eefaa1e5b0722p-6), C_(-0x1.20ae937e7a9305bbde13268a4994p-9), C_(-0x1.e260b0ee2a092f8355b87bfdf959p-16), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 10, 0> { static inline constexpr std::array<Real, 20> value = { C_(0x0p+0), C_(0x1.12cb0a33640b56e423474a1328bap-5), C_(0x1.4e2c27be21e0e479b88ad4851607p-1), C_(0x1.1c4636ef189eccc399682014d9d6p-1), C_(-0x1.85d2edb33e46dde58a5a504a9ab1p-2), C_(0x1.9e3dada7662a9865be6b99b24294p-3), C_(-0x1.494bbf8e53bfc023fe7e0915ab63p-4), C_(0x1.1d23df75685c890f2df5ab229b96p-6), C_(0x1.d4ed15a06848811344377f87f7eap-10), C_(-0x1.2885ec240634157f42540c2ec5a5p-9), C_(0x1.94f202846521b8fb6dee9352c088p-12), C_(0x1.44e94de53f6f632a67cbb5854d1fp-14), C_(-0x1.b37df99b75f08af621654dd8f54ep-16), C_(0x1.403905df41d323c0c33072aaf3cp-24), C_(0x1.c94370d7ecc9194547661e1cf7cp-24), C_(-0x1.58dbb791a7fb6c3e33eb2a850b1bp-28), C_(0x1.db358382953c45bea25d9891f3bcp-37), C_(0x1.afee7cd299cd64600d2193c416f4p-44), C_(-0x1.0988745ca3ec3e945e33c4d42e22p-59), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 10, 1> { static inline constexpr std::array<Real, 20> value = { C_(0x0p+0), C_(0x1.43cfcb9d739232114ccf7b42f286p-3), C_(0x1.f5f9c20090df2cd39669639ec7fdp-1), C_(-0x1.94bc27297b9f96d98ca06b8b780fp+0), C_(0x1.61584eb9fe3a67acec590408bca7p-1), C_(-0x1.a2cb67ff996a90130726bb551867p-2), C_(0x1.eef0eaaf4989e29be4ba95e2f12bp-3), C_(-0x1.ac22308c89e820172f07afea77d8p-4), C_(0x1.c84ec23c8cdbe0f0d252ca4a5fdap-6), C_(-0x1.213456d8479d5a0a002e8a35424cp-8), C_(0x1.2c315a17e4063f457d1b74aaa54fp-10), C_(-0x1.f375455c5c9e61be4fa670796c8cp-12), C_(0x1.ce035ff90a79b6418f9714e02027p-15), C_(0x1.06ba9aa22e87c46e9b5b517bf9e8p-17), C_(0x1.2eea964fb1e803735b7cbdec445fp-21), C_(0x1.f125f98e34b07ec90a3117922ea9p-26), C_(-0x1.51db791710be9863d69c55fb8f8fp-33), C_(-0x1.3ced9d37da5d28d7002692ae731fp-40), C_(0x1.85b82452d92694835d294f84ac62p-55), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 10, 2> { static inline constexpr std::array<Real, 20> value = { C_(0x0p+0), C_(0x1.2d6cb779664d44484073db45447cp-1), C_(-0x1.01b00cc8d5d0650bb7d9e8758312p-2), C_(-0x1.0fd0995df5aa2a4c5f39f93180c2p+1), C_(0x1.8bd77a675e82837408b645325bb2p+1), C_(-0x1.c8684eac579b3e1019c502de8594p+0), C_(0x1.6e6d859b2e72f80278552877464cp-2), C_(0x1.86e851ba85834ec58a127f16fe28p-2), C_(-0x1.9dcbe6db3790670c84be0eec2f7fp-2), C_(0x1.55c762fd5a0df64b9c06acd53195p-3), C_(-0x1.35a481158ac33b4716b6e91c5435p-6), C_(-0x1.51b88ceed7addc9a97168ddbed49p-7), C_(0x1.0767285c8f10645316c4c221fa2p-8), C_(-0x1.67961ae7bb99fbfcc0d26144bc53p-12), C_(-0x1.2b4a0836ed60ae3a1490ad53cc1ep-15), C_(0x1.bb4b6c6c9a6faf8c7dd84be23c4bp-19), C_(0x1.3f418b80d3d44c3e61fe90bb843bp-28), C_(-0x1.0d99c100eff2e6c61779a485054cp-32), C_(0x1.4b9c0643323fb1c0ce2d07b0b092p-46), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 10, 3> { static inline constexpr std::array<Real, 20> value = { C_(0x0p+0), C_(0x1.a8dadfc02dd0428b41cd0c1bb0afp+0), C_(-0x1.8d688d31d04a4214094b65c4cddp+2), C_(0x1.317cc4a10faf3ff9c44aa07180dp+3), C_(-0x1.00fa12867f49132d26f6109f1a1ep+3), C_(0x1.9e984b48af59218135976f585487p+1), C_(0x1.0dcc3bbac5e0885ef946fb573bd9p+1), C_(-0x1.41a435a1df480f6b501eca7093bep+2), C_(0x1.075a464fd9703954dc501fa9230ap+2), C_(-0x1.9984cb75bad50ddd23b7c6a931c3p+0), C_(0x1.9cf2e7196d1cbd2ef1ba36ca5473p-5), C_(0x1.c915787e3612e2d3277f4d8526a2p-3), C_(-0x1.60b8b0c3b1138df4716646253571p-4), C_(0x1.324229742ef4416185466d935171p-7), C_(0x1.dd5da666efa58ec47474db0b6e86p-11), C_(-0x1.351528a4bd8176153593c8a5a1a8p-13), C_(-0x1.03c4250827b68f69b983f4479114p-22), C_(0x1.77139f17861eb1acd308dc031079p-26), C_(-0x1.cd96fccfe9f0afba4ba09dac3a7dp-39), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 10, 4> { static inline constexpr std::array<Real, 20> value = { C_(0x0p+0), C_(0x1.a04f8069ea8b4e447d853aa2b3fp+1), C_(-0x1.18f0a34d9f68e4d06a24b5c8413dp+4), C_(0x1.4ce3831796a61ef6af32be9a023fp+5), C_(-0x1.dc75f9b0b9a1041da91933762b9bp+5), C_(0x1.fdbc97313e6ed6865f9096ac8807p+5), C_(-0x1.eefc0cdda4efa281b21fc89e970ap+5), C_(0x1.b92a7c1b6a889c31c867c0ce8c52p+5), C_(-0x1.2299d502734c3c9b7e4c9a6f523fp+5), C_(0x1.6502b707871df45089990b5055fdp+3), C_(0x1.11f33f4ede647d59a412fb3857a3p+2), C_(-0x1.73b0a319e6889e9aa2e82f2679b8p+2), C_(0x1.244d452e7630092d6c490c8cc448p+1), C_(-0x1.35466c5650c61e120220d045b2f3p-2), C_(-0x1.fc20c8dd92f19b8d6ed30b1d9872p-6), C_(0x1.0d9c8b9d476f7d129de2a5d13304p-7), C_(0x1.a4010b80e5495d0ed9331140ed28p-19), C_(-0x1.49b0c99eb9c648511ea730b4e4dp-19), C_(0x1.962a6dd57b7daa6dc5023a314835p-31), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 10, 5> { static inline constexpr std::array<Real, 20> value = { C_(0x0p+0), C_(0x1.55886d2f64380baeb961078fa176p+2), C_(-0x1.09d95f1cede31fcd15d2c7fab39ep+5), C_(0x1.505f98e4bf27766f2f4d3b7ea8ddp+6), C_(-0x1.96ec354b303069fd664614786911p+6), C_(0x1.17b23075f8010224c348afb3ebb9p+5), C_(0x1.fc17211fa4d6f3f6a32616561c88p+5), C_(-0x1.b35f4a36b31c3849a4f062a53c98p+6), C_(0x1.d17f22f2480f88bf1a425e0d999bp+6), C_(-0x1.12118c5678b4d442de8a0eef777p+7), C_(0x1.207a74ca5ad8bfc2ee103b7bf96dp+7), C_(-0x1.934735f943481c7f74f56dd5b187p+6), C_(0x1.3fa55ba9206e3812ee32da2442b6p+5), C_(-0x1.959601005ec548729558e21c9eb1p+2), C_(-0x1.9505e84d98e613cfe4ec3f6fdae5p-1), C_(0x1.43d2632e32cd0df0bb4a95d727cp-2), C_(-0x1.38e017336cd0614a6ed2ffd943cap-10), C_(-0x1.968e83ac595b8c6d9c172a00fddap-13), C_(0x1.f5ed4b124df3ad0700a32c86e4bbp-24), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 10, 6> { static inline constexpr std::array<Real, 20> value = { C_(0x0p+0), C_(0x1.f3cda2f9b135402362018230e37ap+1), C_(-0x1.9eeda12641b61ebb7d840ecfaa6cp+4), C_(0x1.260fdaadd19eb1efc078d7fded1bp+6), C_(-0x1.d673d80eeade4a070ce0e7052e5ep+6), C_(0x1.f01b2fa154d0352990b16aa70967p+6), C_(-0x1.addb2c4ad791e217324c7aafb066p+6), C_(0x1.7db87bda6fdf8a74bb08213b9495p+6), C_(-0x1.3f9860f6c4905e7132b1781ae693p+6), C_(0x1.ae731643e9198e621648583330d8p+5), C_(-0x1.fb5cc3351ebc176988bf6478c6bp+4), C_(0x1.259afcbb8804d6eae552ab39c09p+4), C_(-0x1.fe186e460309fdcedab5ec38f6a8p+2), C_(0x1.4d0298c21892754606c3d8ebb24fp+0), C_(0x1.a1f0a66406a122ae435ed98406f7p-2), C_(-0x1.40c0addfd85543af466cc7de08d7p-3), C_(0x1.ba72fac37f087c7aee39dd6d7d9ep-9), C_(0x1.c193f16bb678e5af6510cd4d8f9fp-13), C_(-0x1.16b4995bb4f073263c20b5b2b14bp-22), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 10, 7> { static inline constexpr std::array<Real, 20> value = { C_(0x0p+0), C_(0x1.3586d9a9e0e3d99d2b93a01af7d5p+1), C_(-0x1.08fa63342ecc1c41a46b365c4c22p+4), C_(0x1.7c41f01d6960cbda171d22b314bap+5), C_(-0x1.276b64d22e134fe39031785d1e73p+6), C_(0x1.14f1cf74869c077b4ddd600b6793p+6), C_(-0x1.83d54ac636b8244258f1e3c9d507p+5), C_(0x1.4053c8a2c8d2fc6aef1621e8f43ep+5), C_(-0x1.209b538b18df262448ee3cebbe29p+5), C_(0x1.65783340b3782c7d703e94b54e85p+4), C_(-0x1.2ddf670e51dfca7d85e8eeab11aep+3), C_(0x1.38744fdae212579791ea9e59ba4ep+2), C_(-0x1.4065c212ba5c0456783637741afcp+1), C_(0x1.6763c5d11b774e2d99bb54d3a752p-4), C_(0x1.181d4d676ea1f03e7d8c57b5965ap-1), C_(-0x1.87564028831fbd4eb29dad95b0cfp-3), C_(0x1.843d22529bb49b17cf15d645bef3p-7), C_(0x1.52be3b0b8c7af55af9d914b56a01p-11), C_(-0x1.a79c626c8654ebdb591ae5793d64p-20), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 10, 8> { static inline constexpr std::array<Real, 20> value = { C_(0x0p+0), C_(0x1.7a3cf471120ec198713994eca41fp+0), C_(-0x1.48b25e52d274b1e47c8864a7164p+3), C_(0x1.cbd93276e7d596419b912da6ea19p+4), C_(-0x1.37885d5242030806d66e5cf9cc78p+5), C_(0x1.4db4fb7a5ecdb9227e8824c1b905p+4), C_(0x1.74ac2dca21f55bb24886b2e8488cp+2), C_(-0x1.11ca2f9f0b7d05f1c16189874d5cp+3), C_(0x1.9fa6aaf0c8d980502f82ac3f1ed7p-4), C_(-0x1.8ceb3288570cd32c25919a739d51p+2), C_(0x1.e686e3fbc3fbf35811a68b06fad4p+3), C_(-0x1.7e0b780d60231a4c786ec3f17781p+3), C_(0x1.7e1a3482e160bfcf5bb1945be474p+2), C_(-0x1.247b57131543bcbee8b05ee74f53p+2), C_(0x1.b8a4e1c96768c895a2f3649c4cc8p+1), C_(-0x1.4f17d502240a8e6f55c9dea2c968p+0), C_(0x1.4b408438ac8689521da23d7beaedp-3), C_(0x1.7264788c9473dca575636b8d6c86p-7), C_(-0x1.d74fd6a021547c7f9f7dd044fd0ep-15), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 10, 9> { static inline constexpr std::array<Real, 20> value = { C_(0x0p+0), C_(0x1.f6438a3a88460091e0ea8516e904p-4), C_(-0x1.b7ba9255f9c77a442c3315f3c4dep-1), C_(0x1.3f509b3842148b5acdb05dfc7ebdp+1), C_(-0x1.eac616f0d4f869be16ce06d469adp+1), C_(0x1.ae9244fef585d2a56f1cb69c0c8dp+1), C_(-0x1.00d5d03220f3bbc8a51448aa67eep+1), C_(0x1.9413f3bab26fc85ab60692eb482fp+0), C_(-0x1.9bd9847a10dd537f2de875e0ae85p+0), C_(0x1.16aae27d18a67638fdda8c9d9188p+0), C_(-0x1.1587682f48215c21c42b36d23f1dp-1), C_(0x1.8145e3cc3cfcbcc409c895dd9662p-2), C_(-0x1.faf556e68970db4f5325d20bccb9p-3), C_(0x1.8ec972676b06aaf4a1909e5ab3bfp-4), C_(-0x1.49980ed40883cadc7b702f6c3e9fp-5), C_(0x1.5730c48bae56a4ea4426b047bab7p-6), C_(-0x1.0952d6b855c638b977457b4c3a4p-8), C_(-0x1.ce071dd8df1eee9d16331f022e54p-12), C_(0x1.30a4361eb25b38fd9207742aba25p-18), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 11, 0> { static inline constexpr std::array<Real, 22> value = { C_(0x0p+0), C_(0x1.f82d860ed311d7772f0a5fc02a6dp-7), C_(0x1.da87adf545dea36f29603c50206cp-2), C_(0x1.a074ea6831a179cbcefe13bdeb26p-1), C_(-0x1.d3dbc22db33632cb9d3eeea93097p-2), C_(0x1.07dc8c4b588098d99d74923fef1p-2), C_(-0x1.17e06a0deb06ffc5a354422c2913p-3), C_(0x1.d85300a8e9a59db3d8e328544719p-5), C_(-0x1.0710616766f9db787dd78480360dp-6), C_(0x1.0182a0c9a44ba81f2b490da87cf1p-9), C_(0x1.d91cd19825f46395eba40bde2534p-14), C_(0x1.f2bfaace30f58a825ed0b3a11e2p-15), C_(-0x1.d7e9ba7b3ac4736029b548526137p-15), C_(0x1.5c274e2e04a6575e26e75abeb4c3p-18), C_(0x1.63562e1b18da7fac5905b88575e8p-20), C_(-0x1.0af5800900ccbea5a647053aa052p-26), C_(0x1.1409cb495cfaa9cc50f1fb279896p-28), C_(-0x1.1601287f207dc804b32b2b372befp-35), C_(-0x1.4f944d6c974a0ba10244cbd7bf57p-42), C_(-0x1.ad914b3b9c6449c459a4d882621ap-53), C_(-0x1.65d8440cb5909e7eb82f547cb81ep-70), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 11, 1> { static inline constexpr std::array<Real, 22> value = { C_(0x0p+0), C_(0x1.4988f615d8fc2101372678651696p-4), C_(0x1.cd9786f046dbd06d9795ae5305eep-1), C_(-0x1.f034187a5d75333e9dbb5b1046e1p-1), C_(-0x1.075e3bcf1d71f31524cf997afbe8p-3), C_(0x1.303436308c600230256305532d57p-3), C_(-0x1.71d07ef81053ea5196f6a9c9ea93p-12), C_(-0x1.321c6b5970b994e47d89204a7726p-4), C_(0x1.0f00f0747578208709a35dbb5672p-4), C_(-0x1.fec807d6bcde2f6b9da93df4aa6dp-6), C_(0x1.092c0960e80ebe73a0fb5f592a81p-7), C_(-0x1.1857810566102a80b03f6d57486cp-11), C_(-0x1.693105a2bb706882bf9e39158aa2p-12), C_(0x1.a2acf3b1f9d0749452e4dc44d918p-14), C_(-0x1.c4d81f25bbab666371fdd702fceap-19), C_(-0x1.e0284772a21eeb07c87b76642d8fp-20), C_(0x1.7db784c0b013093ad9874c402637p-25), C_(0x1.739c15dc80c555f970d134b64517p-30), C_(-0x1.d9433a2520a2e30f13e7c351f17bp-36), C_(0x1.651bd505e5aaae5cab5b708f20cp-46), C_(0x1.29782f98b98d8b31230d837a9eccp-62), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 11, 2> { static inline constexpr std::array<Real, 22> value = { C_(0x0p+0), C_(0x1.5c07e5083d7090314f404490267fp-2), C_(0x1.307a2a8e783ea1f99bb361a3ce3fp-1), C_(-0x1.a41fd4c6e53047b890408086b518p+1), C_(0x1.08f3303fd9b5f02cbec87edc442ap+2), C_(-0x1.6a2a8758fd08bb3ed6b347424cecp+1), C_(0x1.5f04ae9c9034ab19fcfae00d09b9p+0), C_(-0x1.150c77d31565d35edf80240e5e1cp-2), C_(-0x1.78ed1cb9b0ec1e760990f81b60f9p-3), C_(0x1.4f99902d11603738023acee050bdp-3), C_(-0x1.41156e1c846c464a303629e298b7p-5), C_(-0x1.6c5861062a83616dee9275c18c97p-7), C_(0x1.21e04958552e6f03502dd7935724p-7), C_(-0x1.cd1c35ab2ad92bf156188522c5bfp-10), C_(0x1.af6480e4adc46cb9e0c7649b6f56p-17), C_(0x1.0136c3fb4b19b9b10157d7a834f7p-15), C_(-0x1.6928b0e495d07fb474aad6800549p-19), C_(-0x1.6377af73884bfae0ae8534a7569p-25), C_(0x1.64323c063686a79e3f723a6f6b93p-30), C_(-0x1.6fb51174aa8e375496c635af829fp-40), C_(-0x1.32449da1b960881894abd2226adap-55), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 11, 3> { static inline constexpr std::array<Real, 22> value = { C_(0x0p+0), C_(0x1.1c297e1a722977a767a1ff227742p+0), C_(-0x1.a72bc3c00615f73ba93716b01e65p+1), C_(0x1.8e98178e99b31d46ae26eec0b1cap+1), C_(-0x1.8f506e2a5e0eb5edc0313f13b62bp-1), C_(0x1.02364935e2222846b9cdda1bc87p-1), C_(-0x1.0fb2290536b31f2a2fbff3404bbdp+1), C_(0x1.80597d49465bf4c4d8869fb534fap+1), C_(-0x1.38ad9781c750136475272cd6e902p+1), C_(0x1.339d144fd9550bb8463b544a6643p+0), C_(-0x1.08b97b33caecc76a026ab8335d28p-2), C_(-0x1.3f8a268debfb77a1cde76d550fe7p-4), C_(0x1.2b8bd1a4fd0eaf4aaa08a96cf3ep-4), C_(-0x1.35b764060a713cc242948b5c4153p-6), C_(0x1.f33a86db42465a05a6ee3ed1f4ffp-12), C_(0x1.1c307da269dee355c5c2d340dfe6p-11), C_(-0x1.950dcb7c8e981d2c1ff88549cb89p-15), C_(-0x1.f904916865d4cb30ef383c46ae0fp-21), C_(0x1.8b6cb6bf7586b0f4f1fcded4430cp-25), C_(-0x1.21dedfd7476c5553241f507ac2d7p-34), C_(-0x1.e2c7778c519a040a7c97727ee351p-49), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 11, 4> { static inline constexpr std::array<Real, 22> value = { C_(0x0p+0), C_(0x1.51d8dbccf9206bcc4420e8d9ae07p+1), C_(-0x1.c33b049bfbce5ed995e58045194fp+3), C_(0x1.0e79522ccf3dd0b86ea89a5531bfp+5), C_(-0x1.9aba40cee29c09f71dc38c35a548p+5), C_(0x1.f144c4df7513d73b0fd71dc9df3fp+5), C_(-0x1.17ce56ad9d9026c80fe9053e3b69p+6), C_(0x1.1abf8d1adcc3e5071c7209bb8006p+6), C_(-0x1.aeeda88b7fe87fd16bcf8e054d05p+5), C_(0x1.6d1975fc2f42e1b2096263c969bbp+4), C_(0x1.64c18afe2844c60255dd74d07edbp+1), C_(-0x1.4b3d36d8f9770ee95e2721f907e9p+3), C_(0x1.8e75251a7f5bee7754c2a9513fecp+2), C_(-0x1.9e20d16541319f6d732cb61c018ap+0), C_(0x1.8f713f264dac28b16bf91d504bd5p-5), C_(0x1.002e9aac591974da848a16c844c8p-4), C_(-0x1.22b5d07d79f24f7de83980b5ed9fp-7), C_(-0x1.695b63166e6d63ad77759faf33b7p-13), C_(0x1.ba1b21857af82fb76bf4742957f6p-17), C_(-0x1.df066d7576ffcacbc6c94f3c14p-26), C_(-0x1.8ec06f9de2f7d48618cf3f2e0232p-39), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 11, 5> { static inline constexpr std::array<Real, 22> value = { C_(0x0p+0), C_(0x1.553841cb8504c59ec8828722c9cp+2), C_(-0x1.1648a261cfac86a07a577122f4eep+5), C_(0x1.7429c31b8bcdc777e06851df4ff3p+6), C_(-0x1.d2259be1a7818ecd1bf98b71d684p+6), C_(0x1.36cbd86221f4c1c381302622675ep+4), C_(0x1.4a282d1320ed73864b5c73d42965p+7), C_(-0x1.2e04ab290e2e062d6018a7405d3cp+8), C_(0x1.5f18c6755ba04a0fe5846dc7ce5fp+8), C_(-0x1.80bbefefb89592a7731263e58b6ep+8), C_(0x1.90ad4b61c673e71a7b1a638ff4cp+8), C_(-0x1.4159e0e62036c0e7d3a9372888dep+8), C_(0x1.4eba06db33d52e049a499539c59ap+7), C_(-0x1.6db5a82568a43d359e1324f88c7ep+5), C_(0x1.6613709f48645b09020d1137193bp-1), C_(0x1.7dbb7d4fa2b472146ed131c42bb3p+1), C_(-0x1.1851a9519e35d94904835e4c0f44p-1), C_(-0x1.230c93cadc0b78607a6df63dd398p-7), C_(0x1.7e95e283d8ce34ce8180e79a24c3p-10), C_(-0x1.136d2374e371184371f11564efd5p-18), C_(-0x1.ca2f0f4b750065dbf1a91e494693p-31), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 11, 6> { static inline constexpr std::array<Real, 22> value = { C_(0x0p+0), C_(0x1.43e579b56aec6db8f44fad1a2ca3p+2), C_(-0x1.2015783789a1305c4b04f4299082p+5), C_(0x1.be9eb07640a6e4dcf71e8edd1986p+6), C_(-0x1.917bfbcd50104121d7cf2e5fb61ap+7), C_(0x1.e7564ac670464ea6f5bb03253324p+7), C_(-0x1.dd46b6e14bd993fb7d27e4be31d5p+7), C_(0x1.c24b2cb18bb303674bf661030f65p+7), C_(-0x1.9299f7098407dc40f69f41f42557p+7), C_(0x1.2da088f392f175bbf1502b058265p+7), C_(-0x1.7f74fc0a8fbbbd2a662926cc0952p+6), C_(0x1.cafceab55c22225d33d140f11ba1p+5), C_(-0x1.cfad0a751fefca13c3b326a88e5bp+4), C_(0x1.f4f5eade96653abb41ce93f1a983p+2), C_(0x1.14b6d949957a5646a550369bc7aap+0), C_(-0x1.5e908a00f74d9f8f6b65d286f0e6p+0), C_(0x1.1ee7d490234e22c547ade0704929p-2), C_(-0x1.1e667fbdaff187310b0173bcb94dp-11), C_(-0x1.81c4231f27f23e6bc64b0c2f2493p-10), C_(0x1.28216d550397d430f8799924705dp-18), C_(0x1.ebdb45ca159a64fa9f199a16a149p-30), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 11, 7> { static inline constexpr std::array<Real, 22> value = { C_(0x0p+0), C_(0x1.0e918e0751a5f26d66d326215241p+2), C_(-0x1.f54b1dc71e94b922c8b7d974b91p+4), C_(0x1.8cc935a5962f326b8767e9c68e69p+6), C_(-0x1.5d87f4fd9af883e7ca8f85ba9a3ep+7), C_(0x1.7fbdfc1f5c92ac9439f72d5fc5fbp+7), C_(-0x1.356154b8ce2bfbb26ff26135d3d6p+7), C_(0x1.00a2f00b6f35fbd2365b8c4d7e4cp+7), C_(-0x1.d61ad68084631dc5b1cb350d96d3p+6), C_(0x1.4b192d7116369fcf46bbc7590d76p+6), C_(-0x1.2bd7eeccbbd8b3529c9ec7ea0986p+5), C_(0x1.d23af72a9213134d138ab79fb28fp+3), C_(-0x1.a5a3f6309aca858b5548cc51629cp+2), C_(-0x1.0097ba319005f0b824ee39f7e25p+0), C_(0x1.29abec5ca24e481b150656fa7b24p+2), C_(-0x1.6d63c669ef72a15dead10bbf465bp+1), C_(0x1.521b96f1f011ffd834fe0c4deb25p-1), C_(-0x1.89a1e9b6409a39917b2558fc77d9p-6), C_(-0x1.cc0f8b0b31975b1da167d6d21402p-8), C_(0x1.9e94a90cc2ee8162e5d90cf2df7p-16), C_(0x1.5739a9ee6eb1c850a9597f22010bp-26), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 11, 8> { static inline constexpr std::array<Real, 22> value = { C_(0x0p+0), C_(0x1.c2a4ffe9661190c09a7af8b79479p+0), C_(-0x1.a9c9682497d4356cc97c7817608dp+3), C_(0x1.5bbb8c487ae5bb615e8e73227ceep+5), C_(-0x1.452573aebb3bf474bf24d5595fd4p+6), C_(0x1.91c80b52c3671525c3375a1dd5efp+6), C_(-0x1.84d65b83affb5ca97ccaf8b73947p+6), C_(0x1.678e1d36597adc53993fd96dab7ap+6), C_(-0x1.4933025bdb6895888c173cc7b2a5p+6), C_(0x1.14413c8e18075f1444cc540dc34bp+6), C_(-0x1.b51d99f6678421b18139d5beb61cp+5), C_(0x1.483818775e0cb14480b32b95042cp+5), C_(-0x1.a72562d5d8064b092b7bae77a4cbp+4), C_(0x1.e00e48c9f77143cec0c43c67669bp+3), C_(-0x1.1522f77734c2edb19342e41508e1p+3), C_(0x1.152961e6e0f56f5672007e36f948p+2), C_(-0x1.3d9d88a64fae43ec46db73db181ap+0), C_(0x1.3f1c58e4e2a682bc6ff059fa1f8dp-4), C_(0x1.b3efb441a4e1aa6b7bfceb788233p-6), C_(-0x1.baa159e67dd4ea14a46828d67fefp-13), C_(-0x1.6c2d94cde3ae1bef5e760e39de7cp-22), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 11, 9> { static inline constexpr std::array<Real, 22> value = { C_(0x0p+0), C_(0x1.480f31520e3f310c306bdafaf042p-1), C_(-0x1.38fe2f09aac57b92aacdaa15d1b7p+2), C_(0x1.f8f411bacc9f961bc5fe5499f875p+3), C_(-0x1.bc60c3b54f90a9241998e0192f5ep+4), C_(0x1.d3d44a6a16adb2540f6bfe5db93ap+4), C_(-0x1.536d6f833b66773a970214ff031cp+4), C_(0x1.0953a7a524aca913282b24103258p+4), C_(-0x1.0c0dca303e8e262fc9682f1cf736p+4), C_(0x1.af56b1534f29dd21b9240915b75ap+3), C_(-0x1.f628948ceff73b6d1ae732743415p+2), C_(0x1.3ef834c3289aa35b262757674e4bp+2), C_(-0x1.c75ab5e1afc75a2d9d28a11fcce2p+1), C_(0x1.cb38bb2c5f4bebadf622e599ec8fp+0), C_(-0x1.624785bf15dbe093496b9f1d5135p-1), C_(0x1.61b17ae5ff0c0c37a77be972ec5ep-2), C_(-0x1.0b62472bd3d7849095f6dced344cp-3), C_(0x1.c9b3ef0688e890b76d9f130077d8p-8), C_(0x1.7c528a3f2156c588899985294901p-8), C_(-0x1.0c4738028aec930d4308df6549ap-13), C_(-0x1.b40e785cb88086ef360909630572p-22), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 11, 10> { static inline constexpr std::array<Real, 22> value = { C_(0x0p+0), C_(0x1.8a25fe79d1c4ddac335be8819a8ap-4), C_(-0x1.79dddd860d2147a7416541793f71p-1), C_(0x1.2f92842080f0827925b022200689p+1), C_(-0x1.04a53246109b6b6c0d3ef73b505ap+2), C_(0x1.fbc727fc23eefb6381085fb0ea64p+1), C_(-0x1.2e8a4ebac6b5e49a84fad5dff777p+1), C_(0x1.96ff87cfe4cc502ebda973db1ba2p+0), C_(-0x1.db8c7f7bf2c3a242351f9975276ep+0), C_(0x1.8867eccd39d1aef372869101502fp+0), C_(-0x1.7019bc278ffc03b406b2ff135206p-1), C_(0x1.a30d20b4e67c690471d7cbcbb0dfp-2), C_(-0x1.7340e7a399e63a19859bd6b84d4cp-2), C_(0x1.68181929e64c530045ddd9e48ddfp-3), C_(-0x1.50a6f5e8f3b308d50e585f11b7ap-5), C_(0x1.8d3b211eb839ac11193734372a7dp-6), C_(-0x1.b7227d5381373569d6206c572353p-7), C_(-0x1.a3114fdf2fbc06953fcb9931063cp-12), C_(0x1.71e7a3b4d88bf16a9f3c256dd239p-10), C_(-0x1.4eea023db8bd4900ec558c766ab3p-14), C_(-0x1.09aed93888304346e30d148d3c14p-21), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 12, 0> { static inline constexpr std::array<Real, 24> value = { C_(0x0p+0), C_(0x1.ba5636194c0939c427c9ec59581bp-8), C_(0x1.3af72853b1890334601f92a1284ap-2), C_(0x1.da1521352f487f1b71f5ba208041p-1), C_(-0x1.5c6f665240adde3380bdc0aea438p-2), C_(0x1.4840a35a3e5076b07b9bb98536cdp-3), C_(-0x1.9d4fdc34ae4edcb2a06a461fd21ap-4), C_(0x1.042a86a360591d6b2ba9327c6214p-4), C_(-0x1.08554bd18ab256f6f2efbe750de9p-5), C_(0x1.86c905bef28a68962be82a3bfae8p-7), C_(-0x1.8785b403c86f6f7548c23ebef922p-9), C_(0x1.f00fc00fce26eee7a53c75fa9287p-12), C_(-0x1.43b7ce1fa4e5b162d9e05ba1624fp-15), C_(-0x1.dafa2eb6c7f5cbd9451ac8b8db32p-18), C_(0x1.2d678c174b94d04aa232fc24fe09p-18), C_(-0x1.f0554b8e5712d06e0273c8cb503bp-22), C_(-0x1.934a0de4da29338aed7c3945f15bp-24), C_(-0x1.3a5d95f1928eec0223d4d93358b6p-29), C_(0x1.0e0d2138861875f5d48c437e1957p-33), C_(-0x1.652333b327609e7169123d062941p-41), C_(0x1.02e74c07e60809b1acff78842ce2p-47), C_(0x1.bb358cf4adf4da69e5fa9c480499p-60), C_(-0x1.f67e04f08f2152aabe6eb786996dp-79), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 12, 1> { static inline constexpr std::array<Real, 24> value = { C_(0x0p+0), C_(0x1.3daa1461d0cf47fff25f3df963e7p-5), C_(0x1.716f56bac1b1102dab376b1a3fe7p-1), C_(-0x1.16bee9987071960277d4321df078p-2), C_(-0x1.f51bd9fa1cf2d9e12262aabd1401p-1), C_(0x1.97243be7bf08455883f653e158edp-1), C_(-0x1.b8eef0110c9fa4de8e71d38444bcp-2), C_(0x1.2402bce395301d2ffcfa2c7a4938p-3), C_(0x1.80595820660c8830537a9f5a0e29p-10), C_(-0x1.fa47fa3f4b021cbace01025dd941p-6), C_(0x1.025a88022a413b155a9680077b35p-6), C_(-0x1.3d712aaad77758afb71e5644bf72p-9), C_(-0x1.c5c0d04fc87430265835a2d868c3p-11), C_(0x1.c45b69075cdb62e0f0501d72eab3p-12), C_(-0x1.6a1670435bfdd529f00d82c5e437p-15), C_(-0x1.13c0d643a27dc3e64f7aa5ea1b1dp-17), C_(0x1.b2cff794384635cf6c5bd8331af5p-20), C_(-0x1.9ee90facd3d1333133d7e205e062p-25), C_(-0x1.c87e1185ca55b3b93aa74571695cp-29), C_(0x1.b076e966620448a8ccbae1de5619p-34), C_(0x1.71c42c5b1e646b3eaaa36a5a302fp-44), C_(-0x1.e6d8e5785ac9381d9a7b90929674p-52), C_(0x1.13fd176d0c254d2994cc4f8bbae7p-69), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 12, 2> { static inline constexpr std::array<Real, 24> value = { C_(0x0p+0), C_(0x1.77fed2e01693602fb84977597092p-3), C_(0x1.e1d10780fb06110288f8999dc16cp-1), C_(-0x1.9e9c7ce63835ce70bd9f1949b4bp+1), C_(0x1.bc153af50c24b9575bdc1b7eea9p+1), C_(-0x1.240fe3f248860f90966b383ce203p+1), C_(0x1.80cee17390c17dd6afdc8b5e9bd2p+0), C_(-0x1.abf5df7fd7abfb9aaab015caa888p-1), C_(0x1.50c7e71a108baf578aa06cfc537p-2), C_(-0x1.4654ed4660723ccc0f0acc159e52p-4), C_(0x1.091b1dd62c3e5f033d13bd90e92bp-6), C_(-0x1.59c475f85d7f3ec16b121b44eccep-7), C_(0x1.935e464b93aa450281cf24f671a9p-8), C_(-0x1.8fe1513629011cd322fa61848df6p-10), C_(0x1.90feb0894b434ddffdbc8185f2a5p-15), C_(0x1.09bd1f2d4e386644ae469eb863f8p-15), C_(-0x1.da6da6ad5e76b858534775753bfcp-19), C_(0x1.a3886fa6d48c5693a0b3ec42e08ep-21), C_(0x1.0f7850b94c92b737f20b8005c4e4p-26), C_(-0x1.63226d50629bfc487dd5a4e8ef38p-31), C_(-0x1.84d7e34db15d3108fdfa03f5836dp-38), C_(0x1.75cc98099352929e39911fd43962p-48), C_(-0x1.a7d23115de5731da90d3ce0c96c3p-65), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 12, 3> { static inline constexpr std::array<Real, 24> value = { C_(0x0p+0), C_(0x1.60087b087d2ef4b82953952d9084p-1), C_(-0x1.1c49e2797e56cac67a206b14e7b4p+0), C_(-0x1.f023b1706ef0dac15d9852fec6b7p+0), C_(0x1.8d30c07e51574352c1f1eb2006b7p+2), C_(-0x1.80e0de445b219fbdea6ef733a307p+2), C_(0x1.c1feac4e70c181d6b4fca6d1ffb9p+0), C_(0x1.3621ab0ea1b052aecf48cff7492ap+1), C_(-0x1.f3ab98611d47a796d9c546361f74p+1), C_(0x1.5e6794d526a24de36780aeb9efa6p+1), C_(-0x1.c702eca93a33cb508d7b1d00211fp-1), C_(-0x1.ee149aa686bb2faa29ff6d4bcf1dp-4), C_(0x1.ffde0ca5fc1a1519c08ba08ef931p-3), C_(-0x1.a2cbb989ecd0a0b85c48b0eeedfp-4), C_(0x1.d58bf4f6c76ea5b68a5554193d1p-7), C_(0x1.206affc103ba0a875625f4581693p-9), C_(-0x1.dfc88961f6b157e54f77890bbc2ap-11), C_(0x1.f86e53415f2a00da542f82562b56p-15), C_(0x1.28c920e4a3f6bb0fc001a36c54d3p-18), C_(-0x1.9c90e430666c8d48daf021826ab7p-23), C_(-0x1.7e8cf0898f3336a3b6d1b959b3ebp-32), C_(0x1.cc8984cca607f1a4cd24395b33dcp-39), C_(-0x1.051a1f819156ba9bc5cb80611a1ep-54), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 12, 4> { static inline constexpr std::array<Real, 24> value = { C_(0x0p+0), C_(0x1.efa01fe281c907b167ef08b2a819p+0), C_(-0x1.34df3dac7fcd3f885ed5b13d2111p+3), C_(0x1.5961138d5be957877d8cfd1f8beep+4), C_(-0x1.014d7a672544a1c4d790253c77afp+5), C_(0x1.5a4b6fa251eaf90e3115b0c8a7d1p+5), C_(-0x1.d72d9b409f2b1bb94911072e35d5p+5), C_(0x1.1a6ca7d65632cc9e3b88e527a77dp+6), C_(-0x1.f8d9ef0a2890c5946bb98eca1feap+5), C_(0x1.142ce1c1f06e19e0c6f07b093cf5p+5), C_(-0x1.90f390767ce4703536c74e99b357p+1), C_(-0x1.822b1a96e2d91cc911472934db6dp+3), C_(0x1.52a34d125b1be5508a31d4593a7ep+3), C_(-0x1.0b934a511e088d9de4577e6b9d58p+2), C_(0x1.335fd67def29efb717b66730db11p-1), C_(0x1.17c48a3b6f0b811400fc04e29772p-3), C_(-0x1.0033850ae88073da0780e3e4b1ccp-4), C_(0x1.68d4635bbce683527c347878fb17p-8), C_(0x1.b6ad0e03e206f2cc34c3ad57b135p-12), C_(-0x1.c361dbb11082ba0b86af7e7039cep-16), C_(-0x1.d59d4ba004a567edb22672d12e01p-25), C_(0x1.f6c8c69c71b982736f46aec1c2fcp-31), C_(-0x1.1d186572e3d30ab58425e9b0d124p-45), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 12, 5> { static inline constexpr std::array<Real, 24> value = { C_(0x0p+0), C_(0x1.1672f76c48a3b085a419d3d84efep+2), C_(-0x1.d05e83fe81471c548c37702884afp+4), C_(0x1.49fc051fc99a825862e53c41bd9dp+6), C_(-0x1.e442104e65335b30f1932ff3fcdp+6), C_(0x1.09c52f838d2fca1c275b6ba1ca01p+6), C_(0x1.677b64cbf326e55bcfc76402e361p+6), C_(-0x1.fbf39786bea7b138ef97d391fb5cp+7), C_(0x1.628081b6fdd810aa965a5c98d2cp+8), C_(-0x1.a2714ddd5c22b603c83ee77be92bp+8), C_(0x1.cb4de777b4f362d79e6f05e8e906p+8), C_(-0x1.9cacd3aee3a7e296cd71908c1c97p+8), C_(0x1.055f4806a834671e4070618a5f49p+8), C_(-0x1.8ddf94a470b86c5104b74cbf3618p+6), C_(0x1.95561c2d77ea7cb1b418f1ad675dp+3), C_(0x1.8ab9cc38b4948a55522c2ec1bb2ep+2), C_(-0x1.6e4f007cb3f6f6544bd7c8aa8418p+1), C_(0x1.43d7023213a802dd64335ed45bfdp-2), C_(0x1.951cff21de4ac7e4a1f0a692a613p-6), C_(-0x1.5137c147339bc2af2a690f130f2cp-9), C_(-0x1.3e3df8b2da2752dcb7f745d4f5a7p-18), C_(0x1.78040f51a9567ebaa0b6d793cafp-23), C_(-0x1.aa8cd8e6f8971ce4ff33741ce32dp-37), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 12, 6> { static inline constexpr std::array<Real, 24> value = { C_(0x0p+0), C_(0x1.6688e31cf50e0564efea7c707539p+2), C_(-0x1.50ba2688bc0430a9ee7461099f42p+5), C_(0x1.18d5fc503a1772d2730b3ea50efdp+7), C_(-0x1.16422099d0e700c2cc83b554b1d3p+8), C_(0x1.7cdc33f38c2d168782ab512e0152p+8), C_(-0x1.a2f3c3b8e6e4ab101396a900d9eap+8), C_(0x1.aa319d6af6ec967e381e232c3d8cp+8), C_(-0x1.958a02a1024489567edfcd9f60c3p+8), C_(0x1.4bb5b15c50a647500af490b1348fp+8), C_(-0x1.caa9291eb011f2ccec525396312bp+7), C_(0x1.1f2d7abf22a7296277eac06229bap+7), C_(-0x1.3b920d0f7e0d09abc1f29bae2433p+6), C_(0x1.bc14b21b62b9334a0ec0723419b6p+4), C_(0x1.8c237a0fa26d7404976151e77cdep-1), C_(-0x1.7f694c35d9d2245677a758a1954ep+2), C_(0x1.3f67e104a689bca4d8d6dcd2113cp+1), C_(-0x1.4cc6c058f371d1d7aadbd425ad1p-2), C_(-0x1.6b557354b8d84aa675e72eefd888p-6), C_(0x1.38d6e371bb0518c7efdfa510e3cep-8), C_(0x1.260d95329ea8e0edce58683df434p-17), C_(-0x1.5c7911e0053bc1a14fff7c706e19p-21), C_(0x1.8b88d3fa551bf844985173c27b89p-34), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 12, 7> { static inline constexpr std::array<Real, 24> value = { C_(0x0p+0), C_(0x1.80f665163dff60603105460968d1p+2), C_(-0x1.7dd2228ef04492e49ffcbc26de51p+5), C_(0x1.48b0d88030936751bd070f0b70d7p+7), C_(-0x1.41512ba753d055c75dc8c75f4172p+8), C_(0x1.8f56f07493ef05c30063bd380daep+8), C_(-0x1.675a39773b2485da34f83484320cp+8), C_(0x1.2e9626a21624df1cdd8e35d51165p+8), C_(-0x1.1263590aebe88d200657c008c834p+8), C_(0x1.9699942af2221429cba47418cbf5p+7), C_(-0x1.59e9b6e0b9c21128db1641974871p+6), C_(0x1.09d1a0b7c583242c5ad30248e848p+3), C_(0x1.cc8ab1005087bf119ad7a75bf51cp+3), C_(-0x1.93c8d5094509572acb009457fd8ep+4), C_(0x1.013c24badc62703530bdc2a89abep+5), C_(-0x1.784654bbd73679c9f2def5413da7p+4), C_(0x1.21acdd98ca7e3a8d97a584ca3cd3p+3), C_(-0x1.6fa583815aba1a21d8ba9c3f6b87p+0), C_(-0x1.39156fe5f0a3b45977f7c9849262p-4), C_(0x1.34372727b2154e0f63382ce7b14dp-5), C_(0x1.c53d49d19717546a3333946ded1p-15), C_(-0x1.57bc52155abea4723bbb438d51fep-17), C_(0x1.869c11b6398bbfee9ec137b2aadp-29), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 12, 8> { static inline constexpr std::array<Real, 24> value = { C_(0x0p+0), C_(0x1.d573db31c98a592a2b6a796753f8p+1), C_(-0x1.ddfb9c5686a4efd2bd1e36073e0cp+4), C_(0x1.a873c238f1ad89ace04b7607dff2p+6), C_(-0x1.b2204de17a53a3315364d57fc2b6p+7), C_(0x1.23910ebcbe60d3ac86902adcdaa4p+8), C_(-0x1.29c6cec6860250c0db3c6f8b8962p+8), C_(0x1.1c64b27b4c107ca71ade24985867p+8), C_(-0x1.156985f5c8bce6de73bf23e5601fp+8), C_(0x1.ef2b60fc7ce315a6b8de2d939461p+7), C_(-0x1.84734ae811782beee80a7af9899ep+7), C_(0x1.20e874beb6697ebef627268d4461p+7), C_(-0x1.93d454c0120c50c5e5f45ca0c342p+6), C_(0x1.e8c645d5034483f58d32c0b8709p+5), C_(-0x1.0a8b2e2c06173020781b6687242cp+5), C_(0x1.14ef6d11452bbb05715fcae8f362p+4), C_(-0x1.bab6f3f4dbb55284f9925c8a3ab9p+2), C_(0x1.4e6aaf12e910db6611cb145f554fp+0), C_(0x1.e57ecc0ee730e09fd58c293b9a01p-4), C_(-0x1.0f174cea8c305d809bddd1a456bfp-4), C_(0x1.a1f2dc12ec55c96d12f54fd03177p-12), C_(0x1.4002914419fbb27ad65f422d91cep-15), C_(-0x1.6c7e600f14b10472b7579cd8544fp-26), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 12, 9> { static inline constexpr std::array<Real, 24> value = { C_(0x0p+0), C_(0x1.d2f4e044749b4e7a1c71b105042dp+0), C_(-0x1.e196df480fe71bea5cbbf6f938ep+3), C_(0x1.ab061d20276f0750e2a72080d7a9p+5), C_(-0x1.a7b259e6037ebf7c8e2adb89e41bp+6), C_(0x1.04d3977e22f60f45b515e3221088p+7), C_(-0x1.c308c46a51aa08f31731ad9b7844p+6), C_(0x1.759cb70549060301bbf1ea932595p+6), C_(-0x1.73a4db524ebc2efdc465e09da17dp+6), C_(0x1.4d02e405c663ff8abf63f28cd74p+6), C_(-0x1.c4dfc682dd95df4b0a54f646c034p+5), C_(0x1.217c835a844bf319c913aad35977p+5), C_(-0x1.9d3e5e5e092523c01057547d3214p+4), C_(0x1.eced01c912f6c17b1d536b46140dp+3), C_(-0x1.ab15772777a65a90ae2d7ecf56cbp+2), C_(0x1.780b7892927c5280dd5bee9ee8edp+1), C_(-0x1.59e4e79f9d38488dc3c59417b41ap+0), C_(0x1.04013ac7f90eca1ecf033aafa30cp-2), C_(0x1.63e903ad632e7f30307040928615p-4), C_(-0x1.26b339b06c46eff13165e32f7c85p-5), C_(0x1.1fc083a0e2c35a300d5c6fbf948ep-10), C_(0x1.9c18e59aa15ab3ece464b6468819p-15), C_(-0x1.d792baac952f2e4df120dee003e6p-25), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 12, 10> { static inline constexpr std::array<Real, 24> value = { C_(0x0p+0), C_(0x1.19eec26520f7247ef7c6a243923bp-1), C_(-0x1.249f95db3c6a85ee06007305aedcp+2), C_(0x1.0297dba66cdd344c6014ab433b38p+4), C_(-0x1.f4e8dc0eb78aa7eff1fa727c9c71p+4), C_(0x1.1efd1b3c60e0b36de4fe266b9d8ap+5), C_(-0x1.a397a05a5f774f4ee2078a58f21cp+4), C_(0x1.239464bf9e82c4fc4fdd79af7e82p+4), C_(-0x1.3946d065739beaad7a01650a25b9p+4), C_(0x1.2898316e66a7f2a70e1a6a12eca5p+4), C_(-0x1.5bb8e2c4642782930886e32d2ea4p+3), C_(0x1.68255c99ba4c6b07d82156e3f9d9p+2), C_(-0x1.21aae7ae7df31c29928804e58d84p+2), C_(0x1.64fab317448fb0474311942daf3fp+1), C_(-0x1.75b64c97e09686a5bb55f9818d82p-1), C_(0x1.22ff2ac4d8b9c4ff1397fddfa477p-3), C_(-0x1.07ed621dcab476dea8f9bcc8470ap-3), C_(-0x1.4a01e6ee0860d89681b645bb0864p-6), C_(0x1.2e7cdbbc900312ead84b8d7e757cp-4), C_(-0x1.b7f6865bae122ce58cd5b4105bcap-6), C_(0x1.17b65cad3d540e056708b80ca432p-9), C_(0x1.910e0462410b5b96ed51c4c46ef3p-14), C_(-0x1.cf434a44d45d4684dfb9a827c309p-23), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 12, 11> { static inline constexpr std::array<Real, 24> value = { C_(0x0p+0), C_(0x1.a6c51bc471a3098c7c3ba4b2e396p-5), C_(-0x1.b8315371d42a542d22b52e160edap-2), C_(0x1.89e13c889a74c808a95f3ae5db18p+0), C_(-0x1.8aa497719f070f6dc765c7af4f82p+1), C_(0x1.eb2b7ee2bc88d2eef92b780b6136p+1), C_(-0x1.a91739cfcb4374adc78a339981b3p+1), C_(0x1.4e87023b22be1f061884b91ff276p+1), C_(-0x1.31bb74af8ae4dfa54f812e36b5bep+1), C_(0x1.0e1263d4463c809bd8442e6ab9d2p+1), C_(-0x1.9cd0db1b42b96cb6927f614da713p+0), C_(0x1.3cec213a53551ad84004d758bdadp+0), C_(-0x1.daf35f59b2441d717c1690c25188p-1), C_(0x1.329c321d7ff6b77198e76070d6c7p-1), C_(-0x1.8936ae8cb907bb56ce7124a40ebp-2), C_(0x1.00dee0f339229a14bdfc063bfb91p-2), C_(-0x1.07bd180d959690eaf41df5a4ee44p-3), C_(0x1.cec01fe9b05f624125be250b50a9p-5), C_(-0x1.e6ef9d5209f879f2bf7a379cfa32p-6), C_(0x1.8f4f47250e3a4f324676c24519f5p-7), C_(-0x1.e72d4f4950c89aa59da6edc504bdp-10), C_(-0x1.e19176d7ba69146eb048e9181ac9p-14), C_(0x1.1b79fa688c946fc4993e527c8905p-21), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 0> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.749078c0867a9a724454443e75a7p-9), C_(0x1.8ae6a79b4938fbd909b199343609p-3), C_(0x1.d008898983913d9bf1d194ac98f7p-1), C_(-0x1.20e16d5539e3f25296b1c6880b53p-4), C_(-0x1.f688bd1280c70194203f744266d5p-5), C_(0x1.2fca4301354a4e285e82fc4b6183p-5), C_(-0x1.505c64431d7a6f1e36954a7c80cp-12), C_(-0x1.0145189baf0142f2a8862b8be415p-6), C_(0x1.c908becee5a6a6fcccc95605355fp-7), C_(-0x1.beac12be1e7de0939dd15f031644p-8), C_(0x1.0006a916adee342c6b2792c6bd7ep-9), C_(-0x1.d4cc286b09e13557f3e87a0f9caep-13), C_(-0x1.a08b65bbb598677114fddb7b661ep-15), C_(0x1.5f413ab3a7eb94dcb396b2657edap-16), C_(-0x1.5c34d16af592037a11107a487b39p-20), C_(-0x1.2dc4454eebde3379c7645c4ba87ep-21), C_(0x1.74f33a2ccbc6518fcdedc7d5aa01p-24), C_(0x1.ddb5df0a412fd407d061c573b81ep-29), C_(-0x1.07969b3a158bd160a5cdad0ece81p-32), C_(0x1.2c5958b74836c10a9059d0bfc767p-38), C_(0x1.52fae6e83091fa686190e11a0d2cp-45), C_(-0x1.d83da475a639c307966abb294b63p-53), C_(0x1.1342e56840ec19da921d2c329c6fp-65), C_(0x1.aa25edf8f64fee132e1b30933b5p-86), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 1> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.23923550f457ca7814b0bb3fd653p-6), C_(0x1.0c0767795d12ae5d156ec5cf4423p-1), C_(0x1.3373be839b47c40b1d36ec3e47ecp-2), C_(-0x1.856d7e6b6468c2deff8342e75baep+0), C_(0x1.22d30ac28dd9c9a5de5413538cd2p+0), C_(-0x1.72e38df054a23a8a5ebabc499005p-1), C_(0x1.8e439b194a1c16626ebcbea59c81p-2), C_(-0x1.3bc2524066e83eb2d5b3ff97e1edp-3), C_(0x1.1bc8f6e41059663daa79bf4a8b55p-5), C_(0x1.2884461caa1e3207b6de1334a7c9p-11), C_(-0x1.db22a9804aa1133069d05b96cb0fp-10), C_(-0x1.3482dca87f01c45e946a258c3e06p-11), C_(0x1.3285223dae55992a1d57ada1533cp-11), C_(-0x1.bbf1c4306d1a37f02d18135ce323p-14), C_(-0x1.17497a4dcd44ffdc92c6ebd1a068p-16), C_(0x1.bebec0cbd1f99371a7867cbe3fbap-18), C_(-0x1.bc40d833ef3598561d72cdf89bb4p-22), C_(0x1.6454c6cebdf97f43adbbdee1eeb8p-26), C_(0x1.5fce040bf0fa22a0bb7f52a52e7bp-29), C_(-0x1.1241493325c926f92e656210ed16p-33), C_(-0x1.ce70ab06ee7eb593175a3e65f68ep-41), C_(0x1.8616f674ef405eeabf053da9c72dp-48), C_(-0x1.7b38ad7c07a3a058098173d3a8bcp-60), C_(-0x1.258ba4702de85ac45e001504a52p-79), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 2> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.7e9e99d93265e1ecb27f1a82e385p-4), C_(0x1.e81e73bf7d4e6759d9f3db761257p-1), C_(-0x1.368292c7be10e513188a104c5c4ap+1), C_(0x1.a1c192a939b9674ffcca9b2f1e48p+0), C_(-0x1.58e6f9045a40dd2a472104dd0f2ep-2), C_(0x1.2f82100ba4f2879faa0ab7316342p-2), C_(-0x1.f8fd9be6cc910f763e95aa8aafdap-2), C_(0x1.017de77081fc30f9ad09eddaf43dp-1), C_(-0x1.5831ad14642112326c714ae42463p-2), C_(0x1.30d718554c4cabcfafc1434f8a7fp-3), C_(-0x1.30a6075105b3aa4d5336fc1950e8p-5), C_(-0x1.632f9e9eb99fc711faf42ce203b1p-11), C_(0x1.1cbed37ba4dbd96c225d81054e88p-8), C_(-0x1.77d86fa7164bfc8c126a64816ac4p-10), C_(0x1.50ec238da00966f5e3d28fc9265ep-14), C_(0x1.f99c3b6dd2e633388ada35d1bfe8p-15), C_(-0x1.8453f2cbc2d996b7775a123e9384p-17), C_(-0x1.b4056601f4bff8b5e7d36857cb2ap-23), C_(0x1.4c2776a5f116e1d85dd046bd9786p-24), C_(-0x1.287daf168a219aa1132a37934711p-29), C_(-0x1.b00a66e2af603b8210a104b463bdp-36), C_(0x1.401eb1fad689de068ef9a337dfddp-42), C_(-0x1.71528144e6954a34804ddc8b7672p-54), C_(-0x1.1de10823e626bbef4adf3610f4fep-72), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 3> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.95e033565cd465e59fc64a849ff5p-2), C_(0x1.e7908dd5753c97550b1b562a02c1p-3), C_(-0x1.293799fcf6c8da1f93fe2d057e3ap+2), C_(0x1.36cc467b08bf7d8a89d5be8f934fp+3), C_(-0x1.4335983e135774c5e5156a45190ap+3), C_(0x1.9c4d14b5414867b3e2c99a21a031p+2), C_(-0x1.ef81d85b23a01ac83af86d64d1abp+0), C_(-0x1.2378e73c6b10a2f7957b04478d3fp+0), C_(0x1.c6e27151761e3e586f5403563b06p+0), C_(-0x1.c857e67243153a965a648f8e6a77p-1), C_(0x1.3531afa42b77f4c754018d3dff11p-8), C_(0x1.05f57c1a6464d742d9d2e202da9bp-2), C_(-0x1.38a2db882124dfc4e44179ad6816p-3), C_(0x1.35c4a5041d9b9456d2520e65bc23p-5), C_(-0x1.fddc3c9f813d6aef1f4f22d0ffb3p-14), C_(-0x1.2653a0321997d1fa4ad992783a03p-9), C_(0x1.ec5d6f7ab50c177dc61e0a189fcdp-12), C_(-0x1.dc01f8633f4c61396f200866480ap-17), C_(-0x1.379717b64ca37d0f86a74f664124p-18), C_(0x1.7e5cfdc9a1b17dce328c7c0b5cf9p-23), C_(0x1.a124166c5786174e1b984a588c59p-29), C_(-0x1.4171311c3a7b2623f4df8ab8200ep-35), C_(0x1.65f402b77f1ab227d7a791609ed8p-46), C_(0x1.15123cc4efb60d3af1736012e615p-63), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 4> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.4c5d516dd5340d0879832abfae77p+0), C_(-0x1.5d2571cda5543923016a360261d3p+2), C_(0x1.24b0f03522629e31e5a0ddf9b0acp+3), C_(-0x1.39e0f8d13e8db1f029ce62587cecp+3), C_(0x1.f006cc927be5328f1a33d6aed069p+3), C_(-0x1.0ce2ffa4646117df99ed92fff69cp+5), C_(0x1.b8b8afcf323d00e8ad39d1ccb61dp+5), C_(-0x1.e6925570e3f510211d732b9179c8p+5), C_(0x1.503102590c0b7b7ae2742f43b1bp+5), C_(-0x1.6d86add093f074bc432a452b5189p+3), C_(-0x1.35b5dc1025f2d34a60aa7bf9f93dp+3), C_(0x1.a6237c764b4a28340b8488817371p+3), C_(-0x1.cda455c3ae3929d25c3c5a324857p+2), C_(0x1.d5a02bb93708a320124accef7fd1p+0), C_(0x1.ecd4c4adc075fe2457753c0ae29ap-5), C_(-0x1.660ee59df078b432fb4f889b38bfp-3), C_(0x1.5158e51b27e5dbfe89f577f9dcaep-5), C_(-0x1.73814bfd57b681fa89fa6df52a98p-10), C_(-0x1.23460c48f636fa706d007acb283p-11), C_(0x1.fa353cdebaaaaa09ad0c40e9cfcp-16), C_(0x1.19c5738584246e6431d3352c6ae1p-21), C_(-0x1.4bb4308ed51fff3108f30cb71f7ep-27), C_(0x1.fc9a1f4f09858f536f1a575d9e29p-38), C_(0x1.89a8b7b9d28d1d71597812aa68c7p-54), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 5> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.a35157d8dba56b3359c55dbb3245p+1), C_(-0x1.5a1bc35a1cc0dbb69c71add31e17p+4), C_(0x1.fa0a0878d9a933860f511fa37af4p+5), C_(-0x1.a68f6523a82701663e04e3ed3779p+6), C_(0x1.98403a971e63162a02dd92c33da9p+6), C_(-0x1.03810e60e02049d57e6cc9d05d6p+5), C_(-0x1.14faaec76846550eede58601f0cdp+6), C_(0x1.3d735b94c105436aa6c6accd08c9p+7), C_(-0x1.cdbd7de801407d716cbc38649987p+7), C_(0x1.20043470ad456af43bde8d7a0984p+8), C_(-0x1.2591e882d85cd18b0dca9775a589p+8), C_(0x1.b621996a63c17e6687cb0aeb2864p+7), C_(-0x1.a875ba068d8449d487f82d8f98a2p+6), C_(0x1.85c7d166f26b7783d793e4b535e5p+4), C_(0x1.1c0e0d6b929f95b9e528987efc33p+2), C_(-0x1.36efc345fd87e7f12a7cd8cf55f7p+2), C_(0x1.3e3f5c2c93c347939cad28d35ff2p+0), C_(-0x1.bffc3922a312894449dea079023bp-5), C_(-0x1.75f52c4bce3944c6d40bd8c68e83p-6), C_(0x1.dea5b49d2b299f2f3407a959506bp-10), C_(0x1.10aa3eff5f8aac0902d8bf4b6addp-15), C_(-0x1.ea6ed6c321e753c1b3e92351b813p-21), C_(0x1.089ab261bfe1c8b7f19aebdff953p-30), C_(0x1.9990a3735f2b8137827c03e7b5fp-46), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 6> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.5d159c98a090a206b1f5a487328bp+2), C_(-0x1.54873e7e1b3d210625d277e6898dp+5), C_(0x1.2c245e185ac8b14c7baac4033f4ap+7), C_(-0x1.416f5cc6c22c1fc04eeda9dfb493p+8), C_(0x1.e67e2b7f933044475571e632011ap+8), C_(-0x1.298ee57db6ed5fd4bacad0de9695p+9), C_(0x1.48ee3d90a4e34a8f4d18f98b7344p+9), C_(-0x1.4e6b4521eb7a56c1ef75797e43fdp+9), C_(0x1.2782e95d7bf9f5fc360d37ee013bp+9), C_(-0x1.bb493883e544d446bc2b8bcc693p+8), C_(0x1.25dfd4b9fa45afe8f2d50abdb77ep+8), C_(-0x1.575e0c206b7ee6818931b6231d56p+7), C_(0x1.1c028f2725fce675841ea5e425e4p+6), C_(-0x1.08a1fe0ac6819f96e8258a0368bp+2), C_(-0x1.12ed1d6ca77301d9871c862435c4p+4), C_(0x1.66791e904ad3b41e47b43fe289f8p+3), C_(-0x1.7aa9733036515afd48134b03fe32p+1), C_(0x1.41600caa02955d88aadf08f58823p-3), C_(0x1.275514f436a31e564b7d019a87cbp-4), C_(-0x1.29373f56ac1095884f483f83febcp-7), C_(-0x1.51ea7d9f345040b9bbf67fd2c9c4p-13), C_(0x1.deb156b08b87ef0592d66bd4c9c7p-18), C_(-0x1.6e30fae8c34ed8366ddfb0cc8561p-27), C_(-0x1.1b57abb7d7f1ba81ca336dd9e4d6p-41), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 7> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.d4fe27a0835ae1a04e0830be24cp+2), C_(-0x1.ecb0f32ca76fb494d571b0a3e338p+5), C_(0x1.c6da62a20cd0a85f32742b3dd231p+7), C_(-0x1.e3622dd5ba05d80a963a5e3e0d7dp+8), C_(0x1.49508717a90e836846bfe76665d8p+9), C_(-0x1.3d934aacf56d0a27027f03a0039p+9), C_(0x1.068600d74e1c90df9e5939a69755p+9), C_(-0x1.bb4eb52dbc23c8aa9dac0b1ae5b9p+8), C_(0x1.2c2d3c048caae8b4e7ebc3a10a9fp+8), C_(-0x1.6a6f07f5d07dd28cc71310327c39p+5), C_(-0x1.48c0f7b3bed9dd4e2bcfc6df9159p+7), C_(0x1.bce28601020e5546efe49f4f2facp+7), C_(-0x1.9e035ea535c3aebdf12735df11cep+7), C_(0x1.737017c34f7deef2722c9d811907p+7), C_(-0x1.15bd065f2789c251ed52867c6e81p+7), C_(0x1.1609d2e86de1f46bc02dc6e0a8fcp+6), C_(-0x1.30b382548235fc08ce2a6ea99e65p+4), C_(0x1.34c266d274d020226722064f6a4ap+0), C_(0x1.56d14dbd1c0ef8dc071f1d5be26fp-1), C_(-0x1.f82518456ddfa0a1ea7fb11fa704p-4), C_(-0x1.1d5d333a03734067f69cdf063e2dp-9), C_(0x1.599f88826e1df7529e25322303e3p-13), C_(-0x1.742b04eba227022dc2784e1aeb19p-22), C_(-0x1.1fd835e2f3ac373e68a1719919f7p-35), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 8> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.6f91b1fb16e463903f2417b8bc33p+2), C_(-0x1.8fef11e8059ceda062deb12c9387p+5), C_(0x1.809c444472a8d8415a2c7a9afbefp+7), C_(-0x1.b10bf090c92b5336357cfbd2f863p+8), C_(0x1.45258188f71468e863f92b55998cp+9), C_(-0x1.7231afc08bd276307932b219a0cfp+9), C_(0x1.7af82162ac359965f69e6fcab693p+9), C_(-0x1.823e9f8992b59fc66e1876f1aa68p+9), C_(0x1.6f4bc124eb7dc2e04c11a11579edp+9), C_(-0x1.32f291524b4fc819caccc41f919dp+9), C_(0x1.d856007cb8fa074ef0836c00fb93p+8), C_(-0x1.59b2f057a74e4aae3ebb4a35f68fp+8), C_(0x1.c4898ffe6b603cfd7a5fa627ad77p+7), C_(-0x1.01b04bb5c7e4ece20624945e7d4ap+7), C_(0x1.0ee550f2b46fb0067dbfbe5f34b2p+6), C_(-0x1.f149829700499105abd07323449cp+4), C_(0x1.21413698e7cf1b5b76cf50d81657p+3), C_(-0x1.1e358290df39caefe5b52d405e07p-2), C_(-0x1.54f12799790c9591a372397a1538p-1), C_(0x1.1dedd290f907f8d2a852f01bdfd7p-3), C_(0x1.2b8db385d6eeb017f64738645db6p-10), C_(-0x1.7cd67d6099898e46f355480e2303p-12), C_(0x1.c1758243d6f68f11163695671c33p-21), C_(0x1.5b5402fbe8db7e6e4a3332e846cap-33), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 9> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.e00f201ca9b47bec13b3bceeec35p+1), C_(-0x1.09aad588fc6e1d2e5e320a1b6965p+5), C_(0x1.0083b1ed3add1dfc189ab7aaac2p+7), C_(-0x1.1b01639f15a47195db844f5df4eap+8), C_(0x1.8e74c5b415b4c982c62a28306ab1p+8), C_(-0x1.907a858da198db5b4a86eef21cd8p+8), C_(0x1.68e4a68143eb6280f78231820634p+8), C_(-0x1.685dfe59eb94f38e35f487b91dc1p+8), C_(0x1.592f39b72049a36e7924f8b1defp+8), C_(-0x1.09589acf597f567d3772f2b218afp+8), C_(0x1.658b66c44d2d9f09118af69ddd27p+7), C_(-0x1.f90c71880a91658c0d9a38574dffp+6), C_(0x1.49a9a7009f2b267b39bab54a8a34p+6), C_(-0x1.441338caa52cfa0e2a27d488fb3dp+5), C_(0x1.0b7b2a297fde14bc6faf3e06bf71p+4), C_(-0x1.d98372b1c68d3f52be82670d6ee1p+2), C_(0x1.f43ec1f2a32806088746ff9dc715p+0), C_(0x1.6165b77555fca80d9c5c14948139p-1), C_(-0x1.4f8a8171bb08fcb9ccd7f4688dddp-1), C_(0x1.267683e93d2b771765dfc5bfe73cp-3), C_(-0x1.8ddc624d415c8d8d9889292068a5p-9), C_(-0x1.979cae945ae1031f8b5a56752269p-11), C_(0x1.95627b187fdb196e7b60fd3122a7p-20), C_(0x1.38bcd286c35de60d5fb4c84a2f35p-31), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 10> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.ddc9914b8fce26e89a34c64d6d6p+0), C_(-0x1.0aa6a6fe4749ff9a53419e5cb74fp+4), C_(0x1.ffd30a30f104e3ad2ade2b583021p+5), C_(-0x1.1076bbdb4cf7527cd039bd2ee863p+7), C_(0x1.5b314fd5bd8427278ea0f14850c8p+7), C_(-0x1.15e629a2f07ab3b285fb28d62142p+7), C_(0x1.73ecdaa7dbcd11927e445f4fc04ap+6), C_(-0x1.7ea4991281a803e96d951a02ad23p+6), C_(0x1.92fe5087093b614dba0f2e58af18p+6), C_(-0x1.e50ce86c3eca30ad8c357a5bc172p+5), C_(0x1.23fe36ce5250d05c3006a508171cp+4), C_(-0x1.1183a5ac74636a4f4f8077863f2cp+3), C_(0x1.325da71616787b1a97cd6b487bbp+2), C_(0x1.b5b93d37eb78f2e235cc59568f84p+2), C_(-0x1.6174cc292a977e8769fcc13572c1p+3), C_(0x1.b8e78e1a9ada492b4dd6fe85e7f5p+2), C_(-0x1.ffc6e54f09b8a6376ab9c26e41bp+1), C_(0x1.807b43d233e13293cd8f6f610b94p+1), C_(-0x1.949effe4fd5f10b6f0bb69f9fc41p+0), C_(0x1.99c706ee33d33292c4de755a9043p-2), C_(-0x1.8b4255eb2ab52df50469eaf1f575p-6), C_(-0x1.25fbde4ae0e29153ac78daabb6bap-8), C_(0x1.a2ffd5a58a3e625ad5baf547df1dp-17), C_(0x1.4225e5d67d6a903058f380dcef3ap-27), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 11> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.82555f0fca681c9ced300bba3465p-2), C_(-0x1.b108ac4bd9d5de90a612fa91bba4p+1), C_(0x1.a3af9f5e28533d44a218819eab18p+3), C_(-0x1.c92e2a69bebea1255ad98910de81p+4), C_(0x1.336156f19a3eab19184560490c7ep+5), C_(-0x1.154d20c532d37d99d550b554251fp+5), C_(0x1.af95c6a7f034f7267500247c3a39p+4), C_(-0x1.a382ed435f8ed2216764cb0a372p+4), C_(0x1.a5a57db59b67be50825ea51ac179p+4), C_(-0x1.48587bdb4781058d9836884c3b74p+4), C_(0x1.c6519b1cdb292e6d292e4f1f9c11p+3), C_(-0x1.64406dc5dece20ec3c4c58b87407p+3), C_(0x1.05a0da2a87dafe77cb9a54ec8326p+3), C_(-0x1.34062a3bf7c109c05654bbca487fp+2), C_(0x1.65a3327bcf1ac1cee14ebea4c8b6p+1), C_(-0x1.b27cf7ab49be37cde0f46679310bp+0), C_(0x1.a5bdc7192b9c3a20ece9060c1ba9p-1), C_(-0x1.571ac2983f85e3a6dc62f13fd384p-2), C_(0x1.31767817c6adec541d6996364d3fp-3), C_(-0x1.904f3491bf69c7251d1b40c4f69ep-5), C_(0x1.0ee4f3163f55f45905e2ddfb93a4p-8), C_(0x1.2584f1f36b43e7bbd83744e7e8bep-10), C_(-0x1.373cf584c03ee906a072cfcde9b5p-17), C_(-0x1.db60a068c3377c744925f382f6d8p-27), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 13, 12> { static inline constexpr std::array<Real, 26> value = { C_(0x0p+0), C_(0x1.8dbb03fad2c2ff4e565f682fc387p-5), C_(-0x1.bebdc5d3b23911ed145126163ba8p-2), C_(0x1.aeb7a5ea21052a1c43ba381385e3p+0), C_(-0x1.cb9851522d20abcc6f87f331e722p+1), C_(0x1.240bf59871b997fc41c935dfbef9p+2), C_(-0x1.cab7cf1ed537e0d17c393ba768f6p+1), C_(0x1.1fd4543a5fd8cb1e3fd209bf00ffp+1), C_(-0x1.1ce9744c1c9756de9b809c9cc5acp+1), C_(0x1.3bab08bd76344b447fe47382a836p+1), C_(-0x1.c40e7b4e77556f5ef8447dfa81f7p+0), C_(0x1.feadc8c7ce87a8fc67e2732ba34ap-1), C_(-0x1.a643a47d84b4da70d0fb0b9b5ca8p-1), C_(0x1.543b964e0f03a3a71ec489e8760ap-1), C_(-0x1.577aa576ffa64efb7d0e9a7c6b39p-2), C_(0x1.499f6f7a97a22bd1178c8279f046p-3), C_(-0x1.d5a15151b901635be5a327439552p-4), C_(0x1.d4c5badb46707713b03f6550ccfep-5), C_(-0x1.094f5215690db085443c42ab246bp-6), C_(0x1.cd0926fb9e142792dd84020c8a8bp-8), C_(-0x1.b2091c2c73dd02f84cb3796bc045p-9), C_(0x1.fdc7350edfadd24f5fb7a32829d8p-13), C_(0x1.4af17513fc3a876960b7cf51425ap-13), C_(-0x1.fd199a3cefa31b76bcac261239c5p-19), C_(-0x1.7fa406e41ed65db974ed315201c3p-27), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 0> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.2e3c0bb3f3ad4d49ee7e86ec562p-10), C_(0x1.d73cdc4f17147d4ff78a1eaebc89p-4), C_(0x1.980f074dbd462038712882b2223p-1), C_(0x1.0b8da7807c2738b0ef8a21bd41d9p-2), C_(-0x1.381b88314120b0755faac9b6a208p-2), C_(0x1.a64053bd4da4144d5c6645aafa5p-3), C_(-0x1.be6cb26620f84322f5bc9ad124p-4), C_(0x1.5369f32b472a8bf80c2615157588p-5), C_(-0x1.e553bfb83c481ee1c85b1021810cp-8), C_(-0x1.5b97376b07916025b5f5ed690f77p-9), C_(0x1.3ad93e070eb79f317793c09c0451p-9), C_(-0x1.6c8658d1b206dd40e11227faae77p-11), C_(0x1.421437e7b839d2e408af08229906p-16), C_(0x1.359cb55b7d335b5bc8ff9070d38p-15), C_(-0x1.16b1cde08937e6520514fc007dd3p-18), C_(-0x1.1e470298289ae452c29404b9c746p-19), C_(0x1.16b7d7c52ecb6bf5c9c270fe3977p-21), C_(-0x1.2a9017a9058ec8631bfea23cf0f2p-27), C_(-0x1.e6603a12c62203e481a6f751bb0fp-29), C_(0x1.ad635a5532ad9d7f9b990f3258b1p-33), C_(-0x1.0b7166f3d598041c11f82d418c5ep-37), C_(-0x1.2dcc8d3c8b0cf63074ab4d9fba6ep-43), C_(0x1.994c7452dcd30c07dade8e0a0b5bp-50), C_(0x1.d3e7db27a70ee63bd0dc60e06dc1p-60), C_(-0x1.ad34c0383fa643c903234154af78p-72), C_(0x1.c6fd0b1a8d6a2b5f9f47659e9cb5p-94), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 1> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.ffe6a833f41d1e41c335a1d66225p-8), C_(0x1.6878956519ce3162ba29291a8504p-2), C_(0x1.4f6222726462315c1311b1bd0b69p-1), C_(-0x1.9d7a4f00f9dc035ff30a13b3dd54p+0), C_(0x1.ee1a7236f71e3afe8c9241dc280bp-1), C_(-0x1.3a4c3d150c11dd01e59680a43c1ap-1), C_(0x1.a5124e1ab47a907fdd8a56cf5fd2p-2), C_(-0x1.fd47140509eb33a71307cdb49b3fp-3), C_(0x1.efeb8355fd2f814144fb4db02bdbp-4), C_(-0x1.695e5e5e49cf847101e5144b6fefp-5), C_(0x1.73cae05387f642ba0d652b2dbcabp-7), C_(-0x1.fa774911ba2d3bb25d61e69c8c8ep-10), C_(0x1.13b07ddddb3b836ee57d12843693p-13), C_(0x1.7cb11c0b4280a9d8a95e4eb20e72p-14), C_(-0x1.d2cbfff6e2a582bd1061aa2f380bp-15), C_(0x1.74388c9fd28e69e7d9f0c5af3fddp-17), C_(0x1.21ff895bfb5f2915922793fd222dp-21), C_(-0x1.156307b5c128c175f7e3a71477b9p-22), C_(-0x1.19b91bff8bef4f88f1ed76cce8eap-25), C_(0x1.f7d83e4d41c76f94d46210e4d1f2p-31), C_(0x1.5090626b206a430059df75028168p-34), C_(-0x1.405683426ad010e44f0a98cc929p-40), C_(0x1.6ac079eed130df81302e2b26c482p-46), C_(-0x1.9a49f0637a3b64a9eb35bd52aa23p-55), C_(-0x1.88bc66cc087f1d558950f224d6c9p-67), C_(0x1.a0541eb1bdceab613773d4dc3dd7p-88), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 2> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.70e38d13c6dccf6333417be4b4fbp-5), C_(0x1.98429094e12d1953c234a36b137bp-1), C_(-0x1.5a23aee5cfcf330386719e085aa1p+0), C_(-0x1.ca3a85e30ebf60ba5eccc5b7b84bp-2), C_(0x1.f2911f6caea6ac421bc7b6d2ce04p+0), C_(-0x1.902df5acb98563fb6c85941263b7p+0), C_(0x1.6e5d8a32fe3ae7b3c23b4e6e9c7ap-1), C_(-0x1.7cd7499a11b87c3bc4909e0bdb58p-5), C_(-0x1.cb51389df1436d54630b5c4050fbp-3), C_(0x1.8970fdea96db48b13c52294d029p-3), C_(-0x1.2e3ba43116d4ee1d14ccd7ef1eb6p-4), C_(0x1.a437288ed51ab324c667ecc7c439p-9), C_(0x1.5d8afaeb5ec2a57500ea441b58aap-7), C_(-0x1.3f213a25a4c39a4ee9f8ca7fc5f5p-8), C_(0x1.438df66495fed0569b1e6607263bp-11), C_(0x1.92aacb344e1aac45946a6dd95b1dp-13), C_(-0x1.4a2a2b6937dca67c2fb96409eaabp-14), C_(0x1.061494c076ea4c2b5c794f2371b4p-17), C_(0x1.1c61500cd5ff603bf7260846f007p-21), C_(-0x1.fed404b96c72e77fac42699e644dp-24), C_(0x1.9900fa8d5cb81723cdb543a2521ap-29), C_(0x1.4b71c6cbb7be353ab93ac21700e3p-33), C_(-0x1.f4e20f23251aa513b13eb0ad0eafp-40), C_(-0x1.ae764159a5bb5c8d340066c07578p-50), C_(0x1.0753e4c918ed6b59ce15c4a9365bp-59), C_(-0x1.1725d8c4676c38ff70e1cc359512p-79), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 3> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.b6406454732d4d440405d44a6a49p-3), C_(0x1.b949541f458cd808c01c6f4442bcp-1), C_(-0x1.49bc84198d8eee0e1769477dd34dp+2), C_(0x1.255157167a5528a3714c438e2bbbp+3), C_(-0x1.254591e8b6e06babda61e0b5bd92p+3), C_(0x1.bfdd01aa9e6916b30d7fb866cb4dp+2), C_(-0x1.258cb0e12f407e788c8d24c043p+2), C_(0x1.244b687910817f75269b9b13d0aap+1), C_(-0x1.76e9c86768fd81ae0140e512f91dp-1), C_(0x1.2db90e1b7e223907d65e1858ad53p-3), C_(-0x1.adc00aab7f786622c376bd60c25fp-4), C_(0x1.077b01cdc29a9bffc55813096431p-3), C_(-0x1.4e1bd3d7c8106ffe51d90e297c64p-4), C_(0x1.a8b1719f76969f8051acdf38ae9p-6), C_(-0x1.1b67a8fdd5d395a5e1003bc2df39p-9), C_(-0x1.5aa2219e8b072017d97056f432f5p-10), C_(0x1.0c296ebe05b6e9c7cf2917908081p-11), C_(-0x1.315112d6e2648d659715640aac77p-14), C_(-0x1.aa34a64782e3fdc563417212e36cp-23), C_(0x1.718ac2125512de66d6a9b5d79809p-20), C_(-0x1.4ed202350e6d4103a23efe811aep-25), C_(-0x1.6a2c3e7b115c942fffc924e2aa83p-29), C_(0x1.03cb1bd946e2daf6230de1902b89p-35), C_(0x1.d2ce73e99c2943d672b79f3df3dfp-46), C_(-0x1.110d7f1b0bfbfc4b48ffe175513cp-54), C_(0x1.2175be08d4af39d29c43830b5338p-73), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 4> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.9bd3bc24739608dd1ea0727f797p-1), C_(-0x1.218b20c923a72ab49d783c3c2182p+1), C_(-0x1.8f073ee2485429a822c3456748ddp-2), C_(0x1.10368c3ad14ec0121ffa10beccc4p+3), C_(-0x1.6e5fd027a16b3fa71b0c68881f6dp+3), C_(-0x1.fee6b19288528fc99125553bcb88p-2), C_(0x1.5dae0c64ec100c07cb64acfb530ap+4), C_(-0x1.253938fa65f9cdca9dcd99a2d96bp+5), C_(0x1.0846ca351ee337bf1cde15f6a892p+5), C_(-0x1.d3379cfd6d392ab0067070d38b2ap+3), C_(-0x1.bc1cbd5de053b1b3e6030cd2eb44p+1), C_(0x1.49e86d68d4cf8c86d82167732746p+3), C_(-0x1.e4ade08c4a6c2c8507d0731ef456p+2), C_(0x1.651f390634956dee37faca7ade61p+1), C_(-0x1.14e32e1f9b6bac3d6ef9b665a8dep-2), C_(-0x1.c20b1d287136abbd5724af54edd7p-3), C_(0x1.9dc73570ee73f3a5d7e75adc1efap-4), C_(-0x1.dbb9ebdd9c09deccb1020147719ep-7), C_(-0x1.c7f5c1ec60f696f9a40d5c2f13dep-11), C_(0x1.a5fc856e52fad70c72c88b43b17bp-12), C_(-0x1.07f65daa0049b089e54b5e492044p-16), C_(-0x1.0e378f07aff0a4003d557ea13ebap-20), C_(0x1.69bc58d5ec0ccbef38bec2915824p-26), C_(0x1.872aa1bcf368769fc21e287db36p-36), C_(-0x1.7b84969608016d06bfce8c098437p-44), C_(0x1.92548cc9b5b71c86f11e26fc3a07p-62), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 5> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.29b15e8e05f4922e7d4e47048a9cp+1), C_(-0x1.cfd365d6145278198eca9c82d9f8p+3), C_(0x1.440caad9c12360807dafb3bad1d1p+5), C_(-0x1.116de9d813b38a3407c462c9c8afp+6), C_(0x1.3b639c503b3cf7fe4e226236b434p+6), C_(-0x1.0040f57e975e1a258b9875fe1f17p+6), C_(0x1.e6fde6a95ce32902224ee9a0f717p+4), C_(0x1.5c1e9e155aab7b67b6f0684e8054p+3), C_(-0x1.b1e5da7957b3c8fabda1cfa1992fp+5), C_(0x1.7d1f138d16b9d31aa28298c6d585p+6), C_(-0x1.d6f4cad7bdd82bc575e84a0ae447p+6), C_(0x1.9fd808c31038fd9d9f9820868c69p+6), C_(-0x1.ecec6e1a57ab3b47a08d37ee9238p+5), C_(0x1.43fff7220eba3de58880aab6571cp+4), C_(0x1.5e941cd01c024910420637b6b61bp-2), C_(-0x1.d2a0789936f63c584facf37b3e67p+1), C_(0x1.940e89f9fbe3b90fa47d360fd472p+0), C_(-0x1.f62a7689777aabb4ab21abcf7e27p-3), C_(-0x1.2b3b3b35a204159ed02721cadaafp-6), C_(0x1.3de2ba1c1b05528a92ffeeb8fc2ep-7), C_(-0x1.21cbeea2dd955597cbff120c27ccp-11), C_(-0x1.2150f65158193f62d7d7f8ca1f69p-15), C_(0x1.159bf889454c6b38325ea0bd0aa7p-20), C_(0x1.b00f4f96ce85167dd0220db19d66p-30), C_(-0x1.21e8e13c855c01e5c446a0ad86p-37), C_(0x1.33590911079747fc32e0ebbb9924p-54), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 6> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.3069c9b572d786eb96d929d90cddp+2), C_(-0x1.2e376983f6cb6b3abd68459b0db9p+5), C_(0x1.1366d92966ea16320047794482afp+7), C_(-0x1.37b9852173f06f1765e1fe9f84p+8), C_(0x1.ff3f2db6a15a3d87e9d7df5b42f3p+8), C_(-0x1.57d9383606e506f39f38563cb6bep+9), C_(0x1.9e8b97533b1eae6eef5ccc2c8b76p+9), C_(-0x1.c556790a27e1d4367bdf86d03f05p+9), C_(0x1.af9eb47abc08e08daae8af537da6p+9), C_(-0x1.5e3047bf51b54c2759154839c7dbp+9), C_(0x1.efd9329bb40ff6440883f8a40f82p+8), C_(-0x1.337ec090261a5db68ef9839a3fcp+8), C_(0x1.1dd7e09a266914bfb2df7819245bp+7), C_(-0x1.34f8d53391e95dda3b7a36e1f269p+4), C_(-0x1.1b6423ffa4134de5e81b13aa374dp+5), C_(0x1.038ecbdebefe39ca7724b2c5d6b5p+5), C_(-0x1.9f2073b507553852ed1a7122dc5cp+3), C_(0x1.0d3b2378aacf1f56c9dbd6033ed8p+1), C_(0x1.b93ffbdcbcfbcafb2828e4f8a1e6p-3), C_(-0x1.00284db90f95b583e4f9723dea4p-3), C_(0x1.5202609236b6be9cf16ae5695042p-7), C_(0x1.4b9320a57f2323116dd63291deebp-11), C_(-0x1.d3b085775602aad7427a1a128489p-16), C_(-0x1.e975304f420640f339ac1f2721b2p-25), C_(0x1.e5cecfa6ab7bb5421ef8c106f4f7p-32), C_(-0x1.0188e4a2f5a9df06f6875fecaac5p-47), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 7> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.f579b17074e36836eacdf20b2124p+2), C_(-0x1.13b9cbe766166f25c9dace3616b1p+6), C_(0x1.0d0d0432bec134cd566f5783ca25p+8), C_(-0x1.309a86e68bfe9899fc0e86f2260fp+9), C_(0x1.b8e26231ea03e8ec859843dc6ce9p+9), C_(-0x1.b08b284517ec42e68106ca8a8bc1p+9), C_(0x1.3c7d2b26245024778e76cf716fbfp+9), C_(-0x1.7e7def0dd6c17f062ac5f280847p+8), C_(0x1.9c880448390e60397d83aacda54p+5), C_(0x1.dc1752cc2f2f84356ce375550f21p+8), C_(-0x1.e54a03fbbc2fa46a8399b07d254cp+9), C_(0x1.1a42e1dae56904bbcf3f8f7fa5c7p+10), C_(-0x1.f903f4a18c30e32dc6c260029f14p+9), C_(0x1.9d8db919083864fcc4e1ce713df3p+9), C_(-0x1.34173ffb143e1d9eed0dd73c6925p+9), C_(0x1.64eb3c27db564d469eb11c515c5dp+8), C_(-0x1.0b0c43171fee5929daa55b569994p+7), C_(0x1.5f0a5375d11c72dc55988338ff36p+4), C_(0x1.d8f6b9f1522f2e96a9e46aa5caf8p+1), C_(-0x1.1c75896d6d598c65080bd27d32aap+1), C_(0x1.f38ac1a749daef77915c1d44f9d7p-3), C_(0x1.f82700b4dc7274aa9bd956d7234cp-7), C_(-0x1.1306135d4672f12980f36c25a44ep-10), C_(-0x1.2dc514d6999140dbb2a21c1d56f1p-19), C_(0x1.1d494ae1d331f410d68f7a178351p-25), C_(-0x1.2e8464bcec8d40ffd77ac9bbd77ap-40), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 8> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.e32cc25ea4da498425dfec2497a6p+2), C_(-0x1.1692ca6458ea9e38b45000f9e974p+6), C_(0x1.1fa09c1316dfb4b439ad2e78bb59p+8), C_(-0x1.615e67f0c1ccc897da6f2d005018p+9), C_(0x1.265c8c1b4b1aa4b3e36b99e4a1a9p+10), C_(-0x1.7569ed1c8210ca209e47f9d0511cp+10), C_(0x1.9e96bde5063d41cf09da3c270b93p+10), C_(-0x1.bbbe167a38d63287ecfeda29fbbep+10), C_(0x1.be080201e737d36f7e79bffa876fp+10), C_(-0x1.8f099bdabb21898e359857d34007p+10), C_(0x1.43b83815e15a62b284f0ee906572p+10), C_(-0x1.ee6ad68b3bda4ee4ba8bf16f4fabp+9), C_(0x1.58d561cb84d267ecaf633fb5a5d5p+9), C_(-0x1.a3b282a8407a310226d833dadcd5p+8), C_(0x1.c79d620ffafc0589079eea90279ap+7), C_(-0x1.bc78cb9447aa124d62b31a0a7f54p+6), C_(0x1.418e35902e32cdd0c2f21fb873ep+5), C_(-0x1.3abf360acc2f742d879127375bb2p+2), C_(-0x1.ad7bb3867b3c2b046f80e1cb0482p+1), C_(0x1.a655024ebf2fd4378622021ee814p+0), C_(-0x1.affdcd4718ba7f1a965455b89c8ep-3), C_(-0x1.8341cd6c6b0a9d48905a1ecfc91fp-7), C_(0x1.b55710d3ebf552b754ea432b16e1p-10), C_(0x1.e321738ab144873b6e4379081333p-19), C_(-0x1.c53c1373c88584f948dc39838bd8p-24), C_(0x1.e0c270acb15c5fb50466c4df9e1p-38), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 9> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.8d1f8a32797636f661365be0e3e6p+2), C_(-0x1.d4881995a0c1ea2393ff422e453fp+5), C_(0x1.e86d414a78e33b3180b306c7fb52p+7), C_(-0x1.280027c9b087591ad85db337ae83p+9), C_(0x1.d3eb5f0b0594c2750ef02a700404p+9), C_(-0x1.0b82a55c2799ede8dec1e33e1dcdp+10), C_(0x1.07ce9f70ddb2869a658274b505f1p+10), C_(-0x1.0d7822536789d9033e70cbead7bap+10), C_(0x1.0df61d2a506b1f8fcfe72d1ca4cdp+10), C_(-0x1.c6893ac6cda6b55a4941f5e57261p+9), C_(0x1.45ac9e808978822ff4ad1899b3b8p+9), C_(-0x1.c9d686e237043dec7c04f88fd885p+8), C_(0x1.35c4c3819b30f9b6fdd5c67ec132p+8), C_(-0x1.4a58be360f4f421d89e83ecf3267p+7), C_(0x1.017559c35f7610aa1b72e5e5a83p+6), C_(-0x1.5d1ba7f13474232b92fdf4e5cb84p+4), C_(0x1.dadd49a935c8394385d3a3bdf6d5p+1), C_(0x1.87b01fd4326a3668ccbb1eed227dp+2), C_(-0x1.9ab15d247f90f9fe7c9b6b3dd567p+2), C_(0x1.46cd6a0d33a48aafbbc221eb1f48p+1), C_(-0x1.8955d7defe0d5217296739d07f07p-2), C_(-0x1.85244f4e65bbc4ccef7883bc8ba1p-7), C_(0x1.6f86d3c43106164aba39dbfe0b2fp-8), C_(0x1.1b4e3c12a74dff21568f59de83f3p-16), C_(-0x1.7914d82d3193f580c3335f167104p-21), C_(0x1.903b4b44dd8c7c0a4e702124dc3fp-34), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 10> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.041a0a897aa28eb3a0ccade17e3cp+1), C_(-0x1.3658b5d77609e7977d3641db3a11p+4), C_(0x1.5194af04e2f848c21930873e900ap+6), C_(-0x1.c3df81f0aaf28bccb5fed61687cfp+7), C_(0x1.ad183e8db3dabe867a1b1e1d7e93p+8), C_(-0x1.3d9c782d7007715e1144e807f092p+9), C_(0x1.852da35362a6d4101e229e2b5077p+9), C_(-0x1.9cca18f6947dd0d5bed4aff692d8p+9), C_(0x1.9a6efa498935e2e3d82e1fd52122p+9), C_(-0x1.a300c2ebdd39d26204b2e2ccd8bdp+9), C_(0x1.a735477cbff1b966c8a20eaae97p+9), C_(-0x1.7a384b1c312fbe2a2f45b3550037p+9), C_(0x1.2afd886ee72d716fc638e75f48b8p+9), C_(-0x1.d0f63946d275b18c0f5c2756f025p+8), C_(0x1.6384c4a13f54dc153d90136891aap+8), C_(-0x1.d6c269ce8bf34e1953164b65a3d5p+7), C_(0x1.08982647617ff8ae60bccefbfe0fp+7), C_(-0x1.1f7e83a0711235a83bc8e025bd62p+6), C_(0x1.2a5701b5bea0dde347ec02073e97p+5), C_(-0x1.c18aa4ec1159b6d22d740e4f35cdp+3), C_(0x1.4dc13861658a8dd406eb1a845d5ap+1), C_(0x1.bd5ac19033244ca1c006b305a421p-5), C_(-0x1.1aa94170fffad7174e8899965ff8p-4), C_(-0x1.97678bd8dc9fa54b7e4c01f3ba2ap-14), C_(0x1.25b3fc23ddcf5fe31ea2281e4301p-16), C_(-0x1.381fef501218e9cc3e0863bb3cadp-28), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 11> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.456a1d2a45d0d4e9e18e4013bda3p+0), C_(-0x1.86731e38f2c9e9bd245a6d7f6e86p+3), C_(0x1.99ddae9b5957da8932fc9013b15ep+5), C_(-0x1.ebccc1d5d759a0cf4adc92beb271p+6), C_(0x1.74f85dd906948af50c229b89c98dp+7), C_(-0x1.8426225b26443b4ef3c972f9489bp+7), C_(0x1.4fbcc1bdf3f1963c49a88c06aa08p+7), C_(-0x1.4776c0e99319f61e68a41fbe42b3p+7), C_(0x1.573bef3fda7eadeaaa4191468e3p+7), C_(-0x1.2b0c271bd883e242fdd8d9725e0bp+7), C_(0x1.bb5bca80b624fa5bb278816db9f3p+6), C_(-0x1.5808025c93fa4e6af5a7ad436556p+6), C_(0x1.0c1df40cf5e4c4ea4441c80693a5p+6), C_(-0x1.5dea01151a3e197eb05317c268edp+5), C_(0x1.997fc95198573b5277250e908707p+4), C_(-0x1.f5c4140f2a03e036d15c57dbb36p+3), C_(0x1.1578f4041624b0e3ebbbb44abecfp+3), C_(-0x1.e11a4c47848258b137f885157e3p+1), C_(0x1.8c2f5a8cae553e89ef1e861ef5b3p+0), C_(-0x1.42cdddde9b71e8354b8c2fb7c09ep-1), C_(0x1.1b37523680cd9a16c0e5dc3ebd03p-3), C_(0x1.61bca3fd3effb1c3f5807afcb9adp-7), C_(-0x1.eeca78cc1dd9a8d407a9b1f6577bp-8), C_(0x1.427b3775515b64598a140f609c8fp-14), C_(0x1.1c6c30fb5472c91c3ee2a25808b3p-18), C_(-0x1.2f056506ce5e619914ce605b0524p-29), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 12> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.45a484f799a722e4cd950a09d5fbp-2), C_(-0x1.87cf9eab4f40073db99e6a0fb481p+1), C_(0x1.99aecd437634b1af52fbd40a01bcp+3), C_(-0x1.e335dd2c6e9de306d05cc358c6bp+4), C_(0x1.5e3841e2195098fae8b77b691b21p+5), C_(-0x1.48839e048b83d3127ebe74020885p+5), C_(0x1.e11a7f30735e301f0eb2ede8063fp+4), C_(-0x1.bff27724d4103b12e92a64b54c9dp+4), C_(0x1.f68b6fe2ccc3168c1a15db6eeebdp+4), C_(-0x1.a9c89c30bca221a764ae454bfb09p+4), C_(0x1.147ddad811d2b11feabea5bba8adp+4), C_(-0x1.a10ee0eb1ccb64a80033704ddb98p+3), C_(0x1.5d8b9ffe3cdb891325e9bfc9fb55p+3), C_(-0x1.b0eebf69279efaf954dd166c21dep+2), C_(0x1.b180929e1a69b7ef5328a0cfd4a4p+1), C_(-0x1.11a2d370771ca7d9ba7c7444df33p+1), C_(0x1.45b0d998cab377e10ef3a92e12dfp+0), C_(-0x1.d74083eaced416102c8dd2af61fp-2), C_(0x1.27c1113b5bdb7975242772f43479p-3), C_(-0x1.2ee6a8512694e4da2e5b07f1c606p-4), C_(0x1.0ad11030da22955532f0e66e0c83p-6), C_(0x1.b719ad4ae715fb13a424d63fb241p-8), C_(-0x1.7b5726cd7507b577ab874fe8a876p-9), C_(0x1.efe0e7fbce7f41797c67293a0937p-14), C_(0x1.116839f7247ae044e8dfdefe7163p-18), C_(-0x1.24c158fd367df5563093507c0edcp-28), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 14, 13> { static inline constexpr std::array<Real, 28> value = { C_(0x0p+0), C_(0x1.439a95b32a47eb5722e3cd7fe3cap-5), C_(-0x1.85e5f8ce20b701780e2029b7ff55p-2), C_(0x1.959ab595a2ea47cca93fdec47d03p+0), C_(-0x1.d591277dff53d8dcf59277336155p+1), C_(0x1.43a6564933b3f0805a5a899c5bb9p+2), C_(-0x1.0a2c05cc597aaccc032de2cc831fp+2), C_(0x1.273950f6ffd17ee92b32456caf9bp+1), C_(-0x1.050c8b9cb84b7c5048bc5cd2f92ep+1), C_(0x1.600ea820111d062fdf2a6acf661cp+1), C_(-0x1.21f61a844a104f06b392bde6725p+1), C_(0x1.19f0e6ef31a0d26707b436c0ada9p+0), C_(-0x1.8e2103144a726110a565e981ea79p-1), C_(0x1.a07f2894f970eb1bd26c4012674ap-1), C_(-0x1.ccce8527cee5c45450edf8973a9cp-2), C_(0x1.e56137866a8254327c313c62b4d6p-4), C_(-0x1.5df48c0c87cd72cb9ba6c6b9ceadp-4), C_(0x1.19d3f039c46cde096e59d4adaef2p-4), C_(-0x1.9e6cd0f61f4caa9b0d4bb0b897fp-9), C_(-0x1.b966bdf45728a5a5bb304f91c5cp-7), C_(0x1.8240771a20727b5953c676de8a9p-9), C_(-0x1.1b05a2f57f924748a24f50e9e6eep-9), C_(0x1.7f8554959f4a532982e2ba397a2fp-9), C_(-0x1.23710df832eb1845bb5316dc1abfp-10), C_(0x1.cc575afdf3f3ce7d0de6ac81f14p-14), C_(0x1.1f238196ae22e33d86876e9e195ep-18), C_(-0x1.3697e9bf43069ef0c328d37a1aebp-27), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 0> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.d99d611c0465d7fca87092137394p-12), C_(0x1.0d2f985427a1dc1f62afa99d998fp-4), C_(0x1.4a539266aac6de6ff0ee136fe6aap-1), C_(0x1.1fc50d04f7f07a49e55f06b78355p-1), C_(-0x1.d497750272bfa28f3f1990b740bdp-2), C_(0x1.3575a87d9c1633de9f0ee95125a2p-2), C_(-0x1.7e644ab11888684db8a7b8d90489p-3), C_(0x1.a1785765bb948d52e0838b0df196p-4), C_(-0x1.7135a6ada8b52155f89869cf22c7p-5), C_(0x1.d8cfdcbb1618899a1617de65e071p-7), C_(-0x1.57e157b5bcea7fac037e4b9b62aap-9), C_(-0x1.5f633204e959fc32c151ba987165p-15), C_(0x1.10a4589d9f87d6dc51e36837ed0bp-13), C_(-0x1.592264d16e621b5275bdd9d12d3ep-16), C_(0x1.89979784d7185338d2badb26cabcp-18), C_(-0x1.4f841169a1bad189406e2832bc2dp-18), C_(0x1.74953d3967ccd8cc322dbf252946p-20), C_(-0x1.f7cb1acefcf435a3d287cb8094c6p-26), C_(-0x1.c92be21aef0c9ac0fb550ddd5f87p-26), C_(-0x1.3b9a57c4ce7e7a8e7b0e1df188abp-35), C_(-0x1.6a58cec0fa224ca49741d5041713p-35), C_(0x1.4bf1eb8cd4c9c2273508a8b73f46p-37), C_(0x1.697067f1715a141417bb7553b3a7p-44), C_(-0x1.513d790bd242fdd412e02eaf3fd9p-49), C_(-0x1.b3943c02986a6636aaf05d95d84p-57), C_(0x1.54d330be5783db7123cd1333ac9cp-67), C_(-0x1.3f6e1e631364c64c65f6b5a7fe23p-80), C_(-0x1.d0d882f8587b498d8efb759e05fp-104), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 1> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.af673717274af370272903c1fa7cp-9), C_(0x1.c7ad45d086ea4c5ed9ed7e340defp-3), C_(0x1.9756cb092ee06f6ac1eb577b96f6p-1), C_(-0x1.51916657c183447d0774789d6344p+0), C_(0x1.6cc6e8f0624052b2c0fdcc8950d3p-2), C_(-0x1.8cf37d6182ca0b170d0a042e4192p-4), C_(0x1.9a3fa2f41d4953051b0c6d4a1b03p-4), C_(-0x1.0a50be22f2b86dd755f1bb101decp-3), C_(0x1.df3f4f6c19d45193b61450be39f8p-4), C_(-0x1.2d4b7e715db6bf84cb93e1a16129p-4), C_(0x1.0681e4c20b9fce41d5b4e56756a7p-5), C_(-0x1.1b50d689cbed8df146901e68b6b4p-7), C_(0x1.49acd134a254ba0136bf2c9ddd8p-11), C_(0x1.16651955943e391787562b852142p-11), C_(-0x1.c26d1f2c70c86f0644644cef75b6p-13), C_(0x1.3002118e762a111c6bcee4aaae9bp-16), C_(0x1.49fbecfd7d9c6cba55cd30ac067dp-17), C_(-0x1.8781a4a6a5f7c8daadd9ede249c3p-19), C_(0x1.5ed0ad164acd249f0f9d827483fep-24), C_(0x1.da4a7558674addd03b35614f7a6dp-25), C_(-0x1.7e23c7237f3f9358779e22d38d23p-29), C_(-0x1.bd8f2cef8c9f03d137232e9e498bp-35), C_(0x1.1c61501fa10480730673344e3608p-37), C_(-0x1.c1b8b33989e0245e7c861b898f54p-44), C_(-0x1.95156f39cb33a3cd95db28e3bb11p-52), C_(0x1.d1f8ddb7d01989509bfbf0327d67p-60), C_(-0x1.34635de67ce8bb9a35ccd2f4bcc3p-74), C_(-0x1.c0c6d283f741e5d8d9e1ced44ca2p-97), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 2> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.52aa167437ed3a97a2d16e2e7e71p-6), C_(0x1.2f386450cd3603452775e405daacp-1), C_(-0x1.92da3e0f4795f52ddad307073cb1p-2), C_(-0x1.010e6c23362c2f5a0189ea0fe7f3p+1), C_(0x1.ba5e5707337cefc73943a8303bc5p+1), C_(-0x1.6efa1a351e68942304e8e68b7bacp+1), C_(0x1.e2efff792ca6cd2456dfdb721f76p+0), C_(-0x1.e9e84815ce29a5578b4bc0477acbp-1), C_(0x1.3e7bcfc83dd34f0d929299013451p-2), C_(-0x1.3f21e5e83db9b76a1e6a21ec3442p-6), C_(-0x1.00e091b8310d7c425038e08dacbfp-5), C_(0x1.93252ea5d40edb85ff0ddad301dp-8), C_(0x1.0cb53bda124f4c4d4c0ae3af4779p-7), C_(-0x1.6ecc6596fdeb97786cd08cbbbaedp-8), C_(0x1.1da3c88de4593a0d10cc249b83cep-10), C_(0x1.0f705c9109b88bc792c8e251c3f5p-12), C_(-0x1.687f176415b5a1410c9f394cb959p-13), C_(0x1.03bf749271ea0dc9bfa9a81f4245p-15), C_(-0x1.0a570c997b8f6bb3aa2b6fc454e1p-20), C_(-0x1.b60c856b75c6f8f8db3b0f76f67dp-22), C_(0x1.2f36c8024d47853b1fc5abd2c569p-24), C_(-0x1.ab6b4d2a0c9eb0fb78406697468ap-30), C_(-0x1.b77dbd4d624bb182ce2a8088a4c2p-33), C_(0x1.02e06a0b5252899742121667393ep-39), C_(0x1.f900db818405bc4cfc19c190531fp-46), C_(-0x1.5cc6aba4a366fbfbcb9e7e9b2b91p-54), C_(0x1.79a9be1badaf05e53c8d4ae211e7p-67), C_(0x1.12cb1c6610cf5a5ca8d9bf45819bp-88), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 3> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.bde47de89537efcc2943722b907bp-4), C_(0x1.ff6e39e05bc1960ac6a7468afb9ep-1), C_(-0x1.0edf7812c7e6f7d06a00b362ef27p+2), C_(0x1.788eafbfef56fcdc50a685d1f90ep+2), C_(-0x1.0b9e58d049e2b1cb9469b014878dp+2), C_(0x1.57d72a54d9b7d9046e3ca22b5dccp+1), C_(-0x1.5aec59993d64c03ae451f5fb822ap+1), C_(0x1.66be004fbc25830ff3a497ea74efp+1), C_(-0x1.257b730e2148d4f4b3feb863502dp+1), C_(0x1.61e6a248fa765db41939b126fafep+0), C_(-0x1.1d661708485736ce63b6c04b417fp-1), C_(0x1.7634ae9385e7d28433590ede5d72p-4), C_(0x1.aae5eb36cf2339bb1da3727f92c7p-5), C_(-0x1.66a2dd81189a5724bb1062f0dfebp-5), C_(0x1.9ee5c38dff3c5ada29575f743d87p-7), C_(0x1.cee452b55c630166e1f1c650af12p-12), C_(-0x1.7d0e63753adf810a13b7946ecd6fp-10), C_(0x1.95a9bb14ccec080a6175688009ap-12), C_(-0x1.ac68b8496deea7093d3eaa37178ap-17), C_(-0x1.4a3e531dccd5a7c88df667aa9928p-17), C_(0x1.45a5b2adacfee470f99a3f9acab4p-20), C_(-0x1.a0585e72874e51a270db9477829dp-28), C_(-0x1.4a4aaf36fdaec2c3a3e857f50877p-28), C_(0x1.818dc3c36642c77c4040a75f3899p-34), C_(0x1.bc8ec635290e455c58f05f4aae9ap-41), C_(-0x1.1c50f4adb4d48c39b492719c193cp-48), C_(0x1.541ee5efeba27ec1d6332fa4f538p-61), C_(0x1.eef3c669427ba33d298eab6d070cp-82), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 4> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.da9efb84cd101b891a55bf085a33p-2), C_(-0x1.0a963523c780f95520ba62e1635dp-2), C_(-0x1.6ead78b2a7f870bae850968c013ap+2), C_(0x1.1fb0b7ee7fe9b42077abdaf556e2p+4), C_(-0x1.a26b20f9bdeeff0cd0b5e878c8cap+4), C_(0x1.5a362701692b5d54a80faabe0329p+4), C_(-0x1.c3b93743ed5e1f4aae7ff96667ep+2), C_(-0x1.0393f54066e2a86d4f5766fcec98p+3), C_(0x1.ce0e335b9b5cfd8737f6de07dc86p+3), C_(-0x1.3371f4378fc25cdcbf584ed9698fp+3), C_(0x1.04744c732a8970957ba707eb759dp-2), C_(0x1.5d5bbaac2d3dd399541ae8c8527cp+2), C_(-0x1.56903abc9d372d08f72cf8f9b7f6p+2), C_(0x1.4f367450bb0294accf5c9a7e742cp+1), C_(-0x1.16f1bf1e47dd7b33b29f107a7717p-1), C_(-0x1.0ca6089fc41ee660907b2bb584cap-3), C_(0x1.fd1e6437298de912355a40f30818p-4), C_(-0x1.127569c6d3d201e096a74c3e92a3p-5), C_(0x1.031ce43d6f2a9b28afac205ca3a5p-9), C_(0x1.dbc5a12350173dc4ceb0edd40202p-11), C_(-0x1.6e57586c99db2a8af6576e5d9fc9p-13), C_(0x1.338c12c9a95b47e57d454a7c66e9p-19), C_(0x1.ec833bdc8d6ab1cf492647696452p-21), C_(-0x1.31ee1e222a82fe6e397b0f1ab0a7p-26), C_(-0x1.068cce25031d061c7d5229b85c51p-32), C_(0x1.ab486d191520d8a9aefca6d39becp-40), C_(-0x1.971ac61147c4bd5f21d58d94dd2bp-52), C_(-0x1.28365b5992261cb91d89a4a61c9dp-71), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 5> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.8a28bf1f4baf0256370de9e50cdfp+0), C_(-0x1.0b8ecb513e670e46e8b61ff5e5a2p+3), C_(0x1.301d17ac5e1c6daeb2bb903493bbp+4), C_(-0x1.80a5fd5ebbea4cbe232c3d8a57f1p+4), C_(0x1.4a39e3e1048d62539834148e9e34p+4), C_(-0x1.1148278e9d9a1304c4d27a6289e7p+4), C_(0x1.18e244d79a0328c7ff6eaa1380dep+4), C_(-0x1.3908669ba07796469d9944fd1af2p+4), C_(0x1.80446c71adb1488769cf4e603a7ap+4), C_(-0x1.0a40794798e411ebd23986dcd959p+5), C_(0x1.52509dd04c4f6aeb9cbf4fd2a65ep+5), C_(-0x1.4b468531c5b19f58ba9821140c9p+5), C_(0x1.c80f1430e334a07768f1a5bc676fp+4), C_(-0x1.82ded3c89e7e9fa329e76277e5cep+3), C_(0x1.7610a167089f1959ecdad2bfe856p+0), C_(0x1.b0a89a8818c0677d48d7304191d3p+0), C_(-0x1.2b516da5d080fbac57ead9c2cab4p+0), C_(0x1.488189c0e63233e310386fe21bdep-2), C_(-0x1.16dd91fce8d2f3a24746d48a1c72p-6), C_(-0x1.957d5757f4d581e1eb81397c4cedp-7), C_(0x1.613bcf2c2fc6a53ed2c91b207503p-9), C_(-0x1.0adbc024da55152d1e362e31540ap-14), C_(-0x1.44905597a89087a3899ef1de6ff1p-16), C_(0x1.238bd55115863b967f9591633549p-21), C_(0x1.0b863bc593a1d515b8c451bf0e45p-27), C_(-0x1.2dac52b5d35bb8e11505ea96f44p-34), C_(0x1.a8ab5a61388b1db01ad925c31a8cp-46), C_(0x1.34fcf88bcfb3fdd3eace81ef29a8p-64), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 6> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.e156f58145aba7d0b30fa35cff6p+1), C_(-0x1.d9350b2238c2b904f5fdfc3f59d8p+4), C_(0x1.b03e3957ecd6a002d130922f2289p+6), C_(-0x1.f51dfa789d878832ad001b3c3bbap+7), C_(0x1.b1d9ee3321d942364cb8258ac548p+8), C_(-0x1.3c90b7e3ca1db82e1a61d1d6988cp+9), C_(0x1.a1025f9df5665ef6d6390bf76e1p+9), C_(-0x1.ee82dbab6ab103949b52c0e24238p+9), C_(0x1.fcf169933904c38827bff6f06e1ap+9), C_(-0x1.beee0dc0c8ddb1ba16139309e6dap+9), C_(0x1.545f49e00f495f369a9c2bc574c5p+9), C_(-0x1.c39ca355bd5d296f37962bd729eep+8), C_(0x1.d17e6e268802ca2d8ab4ba84d645p+7), C_(-0x1.8d05e2b81cf726b84ae7be04d2f8p+5), C_(-0x1.a57ddae4deced8b3bcbf57c78c0cp+5), C_(0x1.065bfb9cfed103880a47e8cebd5dp+6), C_(-0x1.1a783beefce87b0115cd7f827bd2p+5), C_(0x1.2e9f2c1581a696ea729509934246p+3), C_(-0x1.3cad9a2427051e071bf1f9b27c09p-2), C_(-0x1.1b4b3a7aa70e82673e69d678de04p-1), C_(0x1.15a31469a9ce01c4f9e606bc0537p-3), C_(-0x1.5c66a63c9347469ce6f66ff5c6a6p-8), C_(-0x1.5db560cc8a8d9420c2df7533732ep-10), C_(0x1.c57be52a95f9a3c03fb08957c434p-15), C_(0x1.c9738067c5909d91761fe08abd47p-21), C_(-0x1.63f652c47225898a139ddd320614p-27), C_(0x1.769767db4423b6439ddbe400214ap-38), C_(0x1.108b386e39e95695575892a5277bp-55), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 7> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.d870debcc05c458cb431fd983345p+2), C_(-0x1.0c2b8b0b28193dcd228c0a0c405bp+6), C_(0x1.10bd374d63550b983999c56c677ap+8), C_(-0x1.4441daef321d4016bb9b2c10ec43p+9), C_(0x1.eaed48222cc9b9253ebb102b9831p+9), C_(-0x1.dea055aad4ecaefb97c3b4138e1fp+9), C_(0x1.0ea6de87556ea6a2de81d408b23ep+9), C_(0x1.3bb921604519d1ae88e6a7fa3786p+5), C_(-0x1.7c03d4dcf551472cf7483533c9dbp+9), C_(0x1.b77df6c77230b9a06a324d910941p+10), C_(-0x1.59dd45c186fb11c5ad121467058cp+11), C_(0x1.92ad5ee85458cf75ddc7493a06c7p+11), C_(-0x1.7716e9b298971ca51bace1729b0ap+11), C_(0x1.34cd942cd473a5a7100d811101f7p+11), C_(-0x1.d2108687fa36ec88dd6bfb56d5c3p+10), C_(0x1.27e2041912517aaa65b2132e4553p+10), C_(-0x1.0ee0131c1b04ee634d1b07d2c971p+9), C_(0x1.13ffc016f56ca3a3bb78dd1c64f4p+7), C_(0x1.9021a9dd4e12af18897a92604f62p+1), C_(-0x1.cb9505a70965b7f9ec7314ae6da2p+3), C_(0x1.f11cd618c3358a67a8f728986e77p+1), C_(-0x1.a3fefe1a942d41c95d66abeb0178p-3), C_(-0x1.a953da883746e1eb5fc521fbad44p-5), C_(0x1.9718bee3af728b9e4c2d8088bafdp-9), C_(0x1.ad4249f39958d903ac018ed0138fp-15), C_(-0x1.eb70cd1aa40511f5c12a3ada0131p-21), C_(0x1.72006a07a097f617e889edbdb056p-31), C_(0x1.0d30627e6a2ad3489cda0318f0edp-47), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 8> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.155158ac6ea984ad1f3ad98465a9p+3), C_(-0x1.4fec3a6a32c1ef2818fdbca7bd5fp+6), C_(0x1.70dec41eaf97d263c2e4504b0b21p+8), C_(-0x1.e94cfecb1efa1478b22094cf32b8p+9), C_(0x1.bf0b100d34d0405ff7a1094810ecp+10), C_(-0x1.39291803fcf47b5c26824c7c465ep+11), C_(0x1.79ccf50afb46dbd4b77400a63149p+11), C_(-0x1.ab6c90991611b280fa6243394583p+11), C_(0x1.c47ed9a17b9cc8042e779917f5f2p+11), C_(-0x1.af1005783c9c08a75d46ca645f35p+11), C_(0x1.72a859cabb471f7e1c8ba7bd2a6cp+11), C_(-0x1.2846e6b9b14f481005e402049827p+11), C_(0x1.b3d87dff6a954876ee30a3fd1e1p+10), C_(-0x1.1ae10527ae49437f7880589e6393p+10), C_(0x1.41e16e9c43532c08707a85b672ep+9), C_(-0x1.460030c5bf206e4d0618004a8fep+8), C_(0x1.0818e6dd9acdc76d8ca94db3b3b3p+7), C_(-0x1.8ef0b66c7662c653e53071c7fb7ep+4), C_(-0x1.6eb8ec2d2fcf0443ad7c1412d0d4p+3), C_(0x1.3aba0303f01c424453fb561d43b2p+3), C_(-0x1.588d6d239648dcf73cdacc19a23ap+1), C_(0x1.5ca063a2897a34772f3d9594be58p-3), C_(0x1.72c884e06d399a8e64ff05cdd7a2p-5), C_(-0x1.2b7b99da7239a8d08435e37194cp-8), C_(-0x1.359ad5ec914c5a48afa1ae580694p-14), C_(0x1.0cff089d4d592f01775a150c05eep-19), C_(-0x1.207535755ab2ee6fe496486e6edap-29), C_(-0x1.a3ad9d559345b904814003502ba2p-45), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 9> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.174fb1d6730638307d1e4b445fc7p+3), C_(-0x1.5cf63b4072a084fdeb886ad661e9p+6), C_(0x1.859702b93091fc7e65868db7a8fbp+8), C_(-0x1.008e38c64d4049e94bd38a5f00e3p+10), C_(0x1.c03c1b8df613f180675faa2c200ap+10), C_(-0x1.1e0d30516cd0d5b6c1126d4bf14cp+11), C_(0x1.33504e2e62ab9029d44ee63bfb62p+11), C_(-0x1.4498a39b45dd2d15e6ecc8905aabp+11), C_(0x1.500c422b7e4d651447cfcdfc454bp+11), C_(-0x1.2d6f916f2f063d6cac6b467c2932p+11), C_(0x1.c5bd8b8beccdb3ef655c28cdfe75p+10), C_(-0x1.3b7208dc1850750db50ab0ef592ep+10), C_(0x1.a4b1819107c720e54775d3b0e2c4p+9), C_(-0x1.c03583e49298125db8e2fdf27d4ep+8), C_(0x1.1254d5dd1a3c865791d5348ec394p+7), C_(0x1.be19509979c252700bdddf4796f4p+2), C_(-0x1.6abf524121ea45d75b1ac598d945p+5), C_(0x1.bdfa9494723d3641f8f4e66eff1ap+5), C_(-0x1.7a9c102bdb495114d11be11a8205p+5), C_(0x1.86920d3d07ed23a9933ffc257d69p+4), C_(-0x1.a9adfb25c4f27469db3fea5dd201p+2), C_(0x1.1337641b89dbbf1427d95e0cc537p-1), C_(0x1.191c64622c1b13245bafd3429ab6p-3), C_(-0x1.88d18423866c243d7190f3f8f03dp-6), C_(-0x1.aee4f466bb96965b6a34b6c35598p-12), C_(0x1.15e5e9747fe1bbdbc08f26b48b4cp-16), C_(-0x1.bc4491eee0fdcc0c5c6a94682339p-26), C_(-0x1.431c03e754cd8a35cd8981f0bf35p-40), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 10> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.57dd202e5e84ab856980df8a0abap+2), C_(-0x1.b426b94987908d499f3bffafa19ep+5), C_(0x1.ef50af36bc949ed3fa7cc19e8e96p+7), C_(-0x1.4da8e644667e6ac3b7b19c4c383dp+9), C_(0x1.2d889ab089910f7ad4219226878ep+10), C_(-0x1.9456ec86efc743d502abdf76eae3p+10), C_(0x1.ca1f75d29c1784a6a7a3af88a28cp+10), C_(-0x1.f2eaf24b48be90f886d98564f553p+10), C_(0x1.0a60e2fdcea779a991419059c46dp+11), C_(-0x1.07fe75c2d85392f12125b660934dp+11), C_(0x1.e2b37ed5f446198c3a6ab7d8b74fp+10), C_(-0x1.a28fcff6e684b5cca8e0268f1e92p+10), C_(0x1.5596d2be7c1f83852a4b17681f79p+10), C_(-0x1.02761175eccb4a7f32feddc56feep+10), C_(0x1.70f45a3e9b6c87dda520903f4aafp+9), C_(-0x1.edf5a5b354e128a66fada7d4bcf3p+8), C_(0x1.27b1e13298bb3ef65c7ced26ff2fp+8), C_(-0x1.3be69243ef2c31f0230131660198p+7), C_(0x1.3b71f0120f2a47ab2f14bf7b7d89p+6), C_(-0x1.11985d9404f45378f95059bc551ep+5), C_(0x1.3b5a9762f053ae040918d7979484p+3), C_(-0x1.c32c18767db723614ce78e339586p-1), C_(-0x1.5d8e89b8ba2f46a3386878a383e3p-2), C_(0x1.3b1b46a97c2fa9eb1545e9c9486p-4), C_(0x1.23706d03f6f7466fb42dcd72c19fp-10), C_(-0x1.9c26fc84f3ff5e0f45e88806fbbbp-14), C_(0x1.962939a138e2b376295550104948p-23), C_(0x1.27428a5934f9dbd8cb77b8eb93d8p-36), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 11> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.82ba18a2815549dbb583ba20e3a4p+1), C_(-0x1.ee321ac782e8103dfda5e2a50005p+4), C_(0x1.174e5f65a44a587e2370d6742aeep+7), C_(-0x1.6e81d57c4c979417ab1a67acd69dp+8), C_(0x1.368de3361fae4ae909df52a5836ap+9), C_(-0x1.7106731d7c870bc083e5fa51f617p+9), C_(0x1.661d220cefa0ffe013a28a70693fp+9), C_(-0x1.6833b778e773ab42be55fb8d5e31p+9), C_(0x1.833c1b7a24cc4442e638654d7d43p+9), C_(-0x1.704d0cf5b403f737ee51433c941bp+9), C_(0x1.2a287b4a8744be736369f360e45fp+9), C_(-0x1.d795731af32d4e2789960ef06165p+8), C_(0x1.7a6623bbe9af7bf6c822bfb77cd2p+8), C_(-0x1.0ddd9fa1ad021e162c6d71d17f6ap+8), C_(0x1.4f824182bb440977dbde7b407e9p+7), C_(-0x1.9b8d6efd833c3f31fdcf2bc0b9fap+6), C_(0x1.e6b9bfc912a60c385c4952c1112ep+5), C_(-0x1.d872a65489bc6c3914d51095ebc1p+4), C_(0x1.82e4e50171640b22bb8023c09d2ep+3), C_(-0x1.3c81b080d7c589f42c4b3bd2cdd6p+2), C_(0x1.87117e04fd822595de3e0b568ccp+0), C_(-0x1.f2d1dbd395098c995334374fed2ep-6), C_(-0x1.327c0d249413fa2745cbbd687d3ep-3), C_(0x1.13b8c9585145f7834dc70fb2de56p-5), C_(-0x1.094893076dab22e2cc6f5a93ce22p-13), C_(-0x1.784719262e6d99b5739a61a4981ap-14), C_(0x1.31d26875ab45ebb06f0484706cp-23), C_(0x1.bc3aef2e9698a9624f45add91d4dp-36), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 12> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.229017de89888d3b45e2c184d9bp+0), C_(-0x1.74b098d7b294230e1965426f3612p+3), C_(0x1.a3d6fe58853c1e5c5b0a3a09e40cp+5), C_(-0x1.0ee9fe2c639a74c7c045b3701c38p+7), C_(0x1.b7ec28edec4542d27f9646062fefp+7), C_(-0x1.dcbffedb3a507fa5c4f4218a69c2p+7), C_(0x1.8f9512e48d1c46e9aca0a2a1ffcap+7), C_(-0x1.7421b1a8804595d4b26746d9ca32p+7), C_(0x1.a17e1d21e73d1b2881053890e718p+7), C_(-0x1.8b13badbca1d886a8838c2c2e2e8p+7), C_(0x1.210062e92f4896d0f00a29c58cfbp+7), C_(-0x1.a9798d44da6c2035da535dfbec27p+6), C_(0x1.62f521d1972a85eb2c6f26f231b9p+6), C_(-0x1.f0d14679f69c06584d1c30d18738p+5), C_(0x1.0b0540b21cf0924f5546e5194d02p+5), C_(-0x1.2e7f81d7237507956599c3ca61c1p+4), C_(0x1.776b19bc1de7ac2a6f830edb0d24p+3), C_(-0x1.390aaca35a348d0447af0cac40bp+2), C_(0x1.0e68e9445701769efc7a68d035e5p+0), C_(-0x1.1d9e671612a45bff7deef6e6cc44p-2), C_(0x1.1e141f6f8381b1b9942811bfdb96p-5), C_(0x1.626281f06a565209b56968645fb1p-3), C_(-0x1.00510e27c6af9fccce7dab2661cp-3), C_(0x1.dda065aa933f3a25232d375fa99bp-6), C_(-0x1.397e42b4cc1c25443933189b9067p-10), C_(-0x1.5e2780fee3a224f5cbc2e8463206p-13), C_(0x1.93d63eaebefb797682d434c6108cp-23), C_(0x1.24c485651202d28a48b67967bbebp-34), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 13> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.2dba81606cdf44cd9f04d2f8bc14p-3), C_(-0x1.83ba9062dc10a95420ae8251630ap+0), C_(0x1.bc6ce42e9d3a21102e4fdf0d4671p+2), C_(-0x1.2c97bd817af94b6452b764a95107p+4), C_(0x1.0e06914d9315537516f107f294e6p+5), C_(-0x1.5f4706680d4ada71d3fe8928e941p+5), C_(0x1.6ea3fbf492340b1a8746b11d0c42p+5), C_(-0x1.5a777ed4931f215f8e0a373e4b9fp+5), C_(0x1.424978021982d2d37f62990dfb19p+5), C_(-0x1.3090ff3d7fed662537bbff91030bp+5), C_(0x1.23e4419ddb640edbe25a71aaad29p+5), C_(-0x1.07fd356f7673b92c13a9d66edf4fp+5), C_(0x1.a8f488a3b6e4b2eef60c68cd2c1fp+4), C_(-0x1.49932384ed8b4cc7bb541dede413p+4), C_(0x1.0a96d421140fecc2982baba8b026p+4), C_(-0x1.8f60cd9c056028206308e7df4429p+3), C_(0x1.003bcbf26a5db032107ad008618cp+3), C_(-0x1.433ef046d9ed13921cc6b579a83ap+2), C_(0x1.a65b7dff9352096da27547ffc3e7p+1), C_(-0x1.d36032adf2dbd6f014bd3f02cf6dp+0), C_(0x1.a4a1ad470bd0ddc88c1710fd49dcp-1), C_(-0x1.88e9f63513ce060ad42735911b82p-2), C_(0x1.6764f965c95116ec3ca5a4be4d2fp-3), C_(-0x1.91c36b23af1d7e93ac71caa65612p-5), C_(0x1.07d03d7a1adc182d361556de1e4bp-8), C_(0x1.362d3e37ff6cd230a2a77cad24c8p-11), C_(-0x1.92dbfdbfa8197afc834aaea734e3p-20), C_(-0x1.22ff804507dbe78851ce06e1d7fp-30), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 15, 14> { static inline constexpr std::array<Real, 30> value = { C_(0x0p+0), C_(0x1.8dd6e1684e11d5d1ba3be6ea3c1dp-6), C_(-0x1.ffb4de00653d36e2054de2d9b35cp-3), C_(0x1.1f8df89dea983b6a6839c59e1daap+0), C_(-0x1.6eae32a3a790681f248ae47d8bf4p+1), C_(0x1.200cfdab0a8887ae485a3302d7acp+2), C_(-0x1.1f287662480ab738cbe219de6228p+2), C_(0x1.90990b7ee9dd1f94866d2e2c98f7p+1), C_(-0x1.3d166b62b6085e29fb936a0976f7p+1), C_(0x1.74d60ae1a8ceaa71b4a74664e6fcp+1), C_(-0x1.68026579c2213b7f75e6c5dc86c1p+1), C_(0x1.e76ba8ae988d51e5857ecafcf58dp+0), C_(-0x1.60830949d4c82da954f5df283712p+0), C_(0x1.48f2a8ad7050c9588f35978df03dp+0), C_(-0x1.e7d09d1d5c76d8ae64a80dc22b4p-1), C_(0x1.1252ee85de9250052c51460801d2p-1), C_(-0x1.6e9d007e3ce50ac7539c0411656fp-2), C_(0x1.0a88fbec92db255ff329ff3310a4p-2), C_(-0x1.16ea71f276b00e5a14dad4f88487p-3), C_(0x1.018afa9348367b971e98c4cc0c2cp-4), C_(-0x1.2fa053de7510dd5de40051b25ee8p-5), C_(0x1.2375ee2696f86cd08bc27b41fb5bp-6), C_(-0x1.73b10c938e4b6609ab6cf8acbfb8p-8), C_(0x1.1decc8370b2354d839f55dfbd7ebp-9), C_(-0x1.b8713a4dd74a971b693427cc738fp-11), C_(0x1.732711f764c017456161af308333p-14), C_(0x1.5deb76c540b6c1414e8c8011ad5bp-16), C_(-0x1.9271259f104138f720ca0ebaa72bp-23), C_(-0x1.2099915b9335422a7d16c948a51bp-32), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 0> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.674708c20fd278e13cb308fbf6b6p-13), C_(0x1.27cdb30a789a9a4cba57381b80dfp-5), C_(0x1.f3f9a4213a58e15fea805c6fce03p-2), C_(0x1.8a1b0e63e768effc8742d136ac6ep-1), C_(-0x1.d0008134e7b24ce7a0bdb2d5e177p-2), C_(0x1.06c9817f611e98379b152a389fafp-2), C_(-0x1.4ec109006f3ab5eb9ef4b4145d24p-3), C_(0x1.b93c322324f48f0734b615fc9893p-4), C_(-0x1.09f5f4083e296fb6c1df50020f51p-4), C_(0x1.0b6a75dfa25cd5d117e10197333bp-5), C_(-0x1.a17eb803ff745c2b927f4b56bb55p-7), C_(0x1.cda18d3f662aa65f684ad290727fp-9), C_(-0x1.1815693b1e509dbfebb4547eb7c2p-11), C_(-0x1.547c4d557ee7eea60970ca463eb3p-15), C_(0x1.a1d518779619a935a6506c218d6ap-15), C_(-0x1.fb90c559a340a6d74429771a650ap-17), C_(0x1.085bbb58c3dc5e159337591d11c5p-19), C_(0x1.4a16ea18a78a9255137a36b6eeb6p-22), C_(-0x1.3b0f83cef7c9cd6e4992ef7a8e0ap-23), C_(0x1.426950d9b6bca7a253eba2c282ddp-28), C_(0x1.acf5cc83787c76964322f9b1343dp-29), C_(0x1.c7dfc1cbcd560059e1fa3bbb0da4p-37), C_(-0x1.99a4987142751268f68b0c6ed2ep-37), C_(0x1.0c54f6dbbd52aba64c70d9892a56p-42), C_(0x1.60b6215484a1c96af7151c51425dp-49), C_(-0x1.6360709f9f94bbe775bd0ffbeceep-54), C_(0x1.f308a7934d0993ea019328882821p-62), C_(-0x1.dcd83ccd7b40e72afa7a2bb7cfap-74), C_(-0x1.f4c81d09055a8112f9a8f0e29d58p-87), C_(0x1.f541a7bafa4019158f37f50aca47p-112), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 1> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.5e2be3ffc02e5cb63d505e4d42d9p-10), C_(0x1.115596e3350ec9cfc7fd8b282c15p-3), C_(0x1.8e066ec21fc6a13efd57c0a1a8d5p-1), C_(-0x1.9a3ae9a2a692b7d6fe0312f42fd2p-1), C_(-0x1.b4e823397cf87b829664fcfb615p-2), C_(0x1.24d79baf908bbde12d97a94a7f11p-1), C_(-0x1.96ff08990aa1ff2b3a6156b3a6f7p-2), C_(0x1.7cff079859d44a97134acb0b3815p-3), C_(-0x1.3b2f6fbc125eff4f9e00971a8041p-5), C_(-0x1.8c439625af31faef37a9ac49e57fp-6), C_(0x1.d82f61038e0e00ad9adf7940901dp-6), C_(-0x1.d308486caa607d54998083598bc8p-7), C_(0x1.b8ca6f1e525d8fc1aecc191249d8p-9), C_(0x1.4ea26d28d30874cd6f1bece4d0cdp-13), C_(-0x1.4455094b3efac9d3c28631c94fc4p-12), C_(0x1.116fb5095b299dc0400b6814e296p-15), C_(0x1.e6f46905743cf29b32a5a94efd0bp-16), C_(-0x1.73244b56511394e5623a6c13b582p-17), C_(0x1.105e30d7ecb45c8d929e54615ad8p-20), C_(0x1.85ee598e5c772af7b4423f5e4908p-23), C_(-0x1.5ac9f1c76ae72306c6963aa9eb6ap-25), C_(0x1.d789f3d703adce6fceeb7470ebe2p-30), C_(0x1.8d5e3e20027e865e92d2141a2b73p-35), C_(-0x1.c8b3833c3b76938050c849f3c902p-37), C_(0x1.38fe40b99904e82fbd88d14cb331p-43), C_(0x1.f524d19d8420a05a730c8d14369dp-49), C_(-0x1.a73b7d5e557e750b97979bfe8e3ep-57), C_(-0x1.c5320460bba019effc9564690ea4p-68), C_(0x1.a5354de9b5e4be0a26cfffd8bdbp-81), C_(-0x1.a59b918cf087f7e28a66eadc80bbp-105), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 2> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.295efeac152d79633d42d6d916dap-7), C_(0x1.9d61cc47e5de2628b18136c91ea6p-2), C_(0x1.161d416b67637e8de5329814dbadp-2), C_(-0x1.5b7a45663376a9aa5f95ed877064p+1), C_(0x1.d1174d09edd0e5a9b8dec824cf43p+1), C_(-0x1.64fd520a66219e33ceb19171c931p+1), C_(0x1.05168da96484602e8bf4113ac4e7p+1), C_(-0x1.6575089c4586c8407b43149ab11bp+0), C_(0x1.a38bf0b53f437316576c7b5f77a6p-1), C_(-0x1.889b16ed752f17512574ee20842fp-2), C_(0x1.152c76b8c5aa61f7d1f86545cf96p-3), C_(-0x1.1b4c99876d201e8bbadda8305eebp-5), C_(0x1.907f1e8b42628bd87c6dfaa42428p-8), C_(-0x1.48b152205d27ec7322d3d940da05p-13), C_(-0x1.a3276d4b1b41ee0d43115b5aa81dp-11), C_(0x1.19008ab2bad0e78321ce13187a8p-11), C_(-0x1.4a0f2fc3a3fe6743d3250b9f0687p-13), C_(0x1.b16d8531bcd974ffcfcfb275888ap-17), C_(0x1.3c9bc919e0e49ac8c1ec9b7c72d7p-19), C_(0x1.899254d74191895a0ee7c7e368dfp-24), C_(-0x1.e003adff466a6871b6189e153009p-24), C_(-0x1.b118f867cd64dc8214de14f22a3dp-27), C_(0x1.a8e81cf9b2bf4ed6a7f80b7fee49p-30), C_(0x1.472047d4ce812177c28860710d42p-36), C_(0x1.0bc546f1206b056c51ef6c4e3c73p-41), C_(-0x1.5b78778ec812c1a8126f0f855511p-47), C_(-0x1.75c5773026aebf48665648526e47p-53), C_(-0x1.4450e0ff79233cb66d7ae0406fdfp-64), C_(0x1.74639b34e9729d879c349c60a705p-76), C_(-0x1.74be14820bd545c3f1e6ecce41eap-99), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 3> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.ade0c67a6df62bfb0e80195f4993p-5), C_(0x1.c28b430a75f298c1d73bb81e4353p-1), C_(-0x1.5f4ccfba427cea15c66f4a5c12bbp+1), C_(0x1.cdb8f607de72e596ea568a034c8fp+0), C_(0x1.f77743196657a78ad5bf74425543p+0), C_(-0x1.dd879ba809267a9d7904ab437cc5p+1), C_(0x1.37053c10bce28d1022d31ac1246dp+1), C_(-0x1.1bfa76c0a943a24b6df5bb2abebdp-2), C_(-0x1.2b5034c5e7cec27aa44e7ea3a878p+0), C_(0x1.63e2260baa9fdeb5f048ba37a04bp+0), C_(-0x1.a29913a0c0f2ddeebb2bd3c275b3p-1), C_(0x1.852867c8ceb35a88f788b40857cap-3), C_(0x1.9a9664fc9e678ca180654356c297p-4), C_(-0x1.c29f78e2de8f527b7cd8a08d24efp-4), C_(0x1.54d634445fcf6eedbc5dccf04eadp-5), C_(-0x1.41b2a5a54a66286808252915b865p-9), C_(-0x1.225d9fec5c6e8c447caf2b42ced4p-8), C_(0x1.f9019974a7ddb631ebee782869d1p-10), C_(-0x1.2c283714217e754b55528b628588p-12), C_(-0x1.8e8742bbbfb2111f9226dbaea15fp-16), C_(0x1.c9f58c62dd5d38676514b49c9e2ep-17), C_(-0x1.61f6495ed1b8ece209fc78f1d085p-20), C_(-0x1.96b8b0f8a15e32028c8ee0076679p-25), C_(0x1.71f48c99d030adfe377a7056701dp-27), C_(-0x1.b5fc8d66ab6cfd5a83337a0cc9b9p-34), C_(-0x1.c5963ee2273d2d13e0940bb125cdp-38), C_(0x1.6b4e6ce52480a98b4ad64082a237p-45), C_(0x1.e67135f42b49adad00310559fa39p-58), C_(-0x1.6aff40a6dd5b409b62d44964fcf6p-67), C_(0x1.6b5790e714dd812f487575d04214p-89), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 4> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.002833198203bf56e8fbb6905c21p-2), C_(0x1.74f145f5180b48bf2521976daf68p-1), C_(-0x1.d40722c67b8b2c63d49b5a9b6cadp+2), C_(0x1.2af6f0f464bc571c8b763a8383eap+4), C_(-0x1.a43135145799a27dfd1fadf6761p+4), C_(0x1.9a15ae0412710439a9b40c2dd4dbp+4), C_(-0x1.38d1bd49e9af291f59c9f34bcde4p+4), C_(0x1.6c6cdb14d7fe107edc16c4953b35p+3), C_(-0x1.13619aaa119f97b37d6d1c2c4585p+2), C_(0x1.d56ce1ec874a0c5dd634fb59d6d1p-1), C_(-0x1.e42907ae7a26468d6ecb1d9e3f24p-1), C_(0x1.e52911f9f656640a2404f83a2b4fp+0), C_(-0x1.ee224ef8eb13d4b961d19b7d1c3bp+0), C_(0x1.1f26d6223d44cb481a2910c6eba2p+0), C_(-0x1.5888435f5783628967f9baa03f79p-2), C_(-0x1.9e45fc4e6f7c960ebee0a7c398e8p-7), C_(0x1.dcd2d2ebfb6e400bbcf752c0bf7fp-5), C_(-0x1.93116bc1ac6b7200b82ddbf8d638p-6), C_(0x1.0e01783e342a4693e98c9edbf33cp-8), C_(0x1.31f3faa90de9ede16d914789626ap-12), C_(-0x1.f6b0df77a54401ad9bab438557ddp-13), C_(0x1.f05077a02c3aba8741da9769ad4ep-16), C_(0x1.2ec5b9d224e6bceb5db9ee6cff1dp-20), C_(-0x1.47f2b0eb68eda8ce48926b156c9bp-22), C_(0x1.bcb021bf883a1f92a78fc9df6525p-29), C_(0x1.1bcefd6cfba520ba047378483637p-32), C_(-0x1.34d97adc52a38a676a55a692601cp-39), C_(-0x1.6f2c8c9ca394fbdec6dda2487026p-53), C_(0x1.34e43d9253e6e8975e6c8e7aefefp-60), C_(-0x1.352f98f0f2e48db2a203fb32a0f2p-81), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 5> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.e5d8b15d3664d52108fae09a8e52p-1), C_(-0x1.e7e4ae8313c9841f6b4ba59501e9p+1), C_(0x1.643947f56174ee22b35084254df8p+1), C_(0x1.8bf77730624fb34f058dbb215e6ap+3), C_(-0x1.39575fcf961c0f3da45ce2cb7fb1p+5), C_(0x1.f6657cd6c7e299b7cc89d7d3daa3p+5), C_(-0x1.2981aaa66ca36b3a94f8297750dp+6), C_(0x1.0cf300079688d85d6be476057064p+6), C_(-0x1.71528cd9cab137a39829bbec9ca2p+4), C_(-0x1.13658826409b41720bd51a0f2d32p+6), C_(0x1.5ecf135e336b7c201cd13f9f1c7bp+7), C_(-0x1.ca8fbd8754dc1920d78e0ace3c5ep+7), C_(0x1.83a96558c6c07294f0fd718cdf26p+7), C_(-0x1.9d91182e0786a78e2338c8bf2ff2p+6), C_(0x1.818f5ca05f023837d630b257f2ffp+4), C_(0x1.4e782cc994094deb153537dfc8d9p+3), C_(-0x1.7fba734a377970e0ae8d82bc8b81p+3), C_(0x1.361d7d826bb5b3b3a1616de586bdp+2), C_(-0x1.85fee8307d590ead49f8c992534dp-1), C_(-0x1.fcc3205624ce56be678cf9a5a7bcp-4), C_(0x1.30d54b861144b0080c77fb2ef955p-4), C_(-0x1.4236c7ccc62df6904f1792b21926p-7), C_(-0x1.6b6d4bcc089eed67f90762e3bff2p-12), C_(0x1.251cb75a77a65027fa18c49d6fe7p-13), C_(-0x1.7df627db3ed37f55c551bdd12823p-19), C_(-0x1.6d7ba0319a17f9c5347fd4ebc07ap-23), C_(0x1.047350b843565161f3ac671e5f76p-29), C_(0x1.ecabd0f857c5126fafed07e8e831p-41), C_(-0x1.035ef08b1273d485ae68801933a9p-49), C_(0x1.039e900e7fa12bdd9aa0b500dd5fp-69), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 6> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.5c435b4994699430de1f0bf0f96ep+1), C_(-0x1.45ad88073e6251792d84638c28d6p+4), C_(0x1.1ac643e9c99f8dff42b4b5f9e976p+6), C_(-0x1.3bcd24e1c22f08a7330d952db5cp+7), C_(0x1.1132bead650e64e0cc6250fd5a4cp+8), C_(-0x1.a3321607e359615c22640184890cp+8), C_(0x1.2bec4ce73b524de840d2af975fb7p+9), C_(-0x1.83ddd4224b9abfb64ecabd8660acp+9), C_(0x1.b1ad4241d86bedd378c0238858cbp+9), C_(-0x1.9d7a3218170b28b67328a4e4a6f8p+9), C_(0x1.5526f87e8214f36e89fc59fbe6b5p+9), C_(-0x1.e996857ebd3fd865f8b8b57715d2p+8), C_(0x1.186a9a24f3387e74b22113290d4ap+8), C_(-0x1.4f804f76b6d5b86a618f6e232bp+6), C_(-0x1.84954fc1821007c0fd22bc06ae8ep+5), C_(0x1.59efa11d2ec6f964630b6fd432d3p+6), C_(-0x1.dac7ad3b33c95d5f07d90297e68ap+5), C_(0x1.62078796373e73b5598b78c1554p+4), C_(-0x1.88bb023e9f2f98b9b7a5caf3d0d1p+1), C_(-0x1.04b9160269b89d57d0a515a32ad5p+0), C_(0x1.18c6982acbd2cd140bf32d2822bbp-1), C_(-0x1.508ddc9d3ccb43c37b8bf661b39ap-4), C_(-0x1.539386e1415cf5772194fa5cb789p-9), C_(0x1.9eab992a4b9ab37998a4782656d7p-10), C_(-0x1.980b92ff814cc556c4aeda05445fp-15), C_(-0x1.761c29381edd29e5680cf5367b41p-19), C_(0x1.64e7e2ec4af2d5325d3b1ebf7038p-25), C_(0x1.1823b743e4c412fe2e1a1caf98aep-35), C_(-0x1.623923d3ddbaeed10b04fa360d62p-44), C_(0x1.6290fa803758e40f0b825bbc6f28p-63), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 7> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.891443770e9f5b58ca0bdbd939d6p+2), C_(-0x1.c4b28f7dbecf38e874d3b242d4fbp+5), C_(0x1.d994c46f08417c9c24a3de260d39p+7), C_(-0x1.26defe09eb9e312242e62017ceb2p+9), C_(0x1.ddf577c4250f78c0132ae5d7d1cep+9), C_(-0x1.fd2079b0f3ddb297d557ffac7fap+9), C_(0x1.36152a2e286cfeac434a875affb8p+9), C_(0x1.d8616a51db368c3df9bbb5de82cep+6), C_(-0x1.190b42892374c709dd8c148043fep+10), C_(0x1.36219b396179567cea600ef5a5e7p+11), C_(-0x1.eed74001662f7e761e907ef9b166p+11), C_(0x1.31c149450a5e9832b2fbd7c32f38p+12), C_(-0x1.31807a4d8f7d8b9ec23e94e23c9ap+12), C_(0x1.07e37f0875c28ba98c5df12ea151p+12), C_(-0x1.9ca0e06fa4e40e6ba9a2f070a627p+11), C_(0x1.192392a49659f5be4afa683922a3p+11), C_(-0x1.28f7ef65a911c2242d4a04d3586cp+10), C_(0x1.8ef1182eeb70c3b810afe07ad9aep+8), C_(-0x1.076216da5c43b27292cd22bcb978p+5), C_(-0x1.3734a9c9c5014b6b42b155a6abedp+5), C_(0x1.2e313dce4fbf11c25ec52c766e83p+4), C_(-0x1.913ecda1e3436af7caac068805f8p+1), C_(-0x1.aba4e0591b35ef78388396d8949ap-4), C_(0x1.5578ca229685f75ec4dbb7fb72b6p-4), C_(-0x1.f3ff47839e58d032a1a624c1f604p-9), C_(-0x1.bd3a12076a4f509f06e99bb28599p-13), C_(0x1.29ffe91412a29d368ce7b0895443p-18), C_(0x1.5e7711914b058b73ae8c4ed370dp-28), C_(-0x1.267e3f1ece05182094aa29382cf7p-36), C_(0x1.26c8d9be624c3d09b42c989d403cp-54), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 8> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.1c06072fbf26f358c9f078f98565p+3), C_(-0x1.65d998749f19f3f52787d2ac7bd1p+6), C_(0x1.9d8978b96f57f154ef9ce52c7bdfp+8), C_(-0x1.24c8d76a7f36d7089e3f4940c827p+10), C_(0x1.21d479bc00697b14d91a6dd4bcabp+11), C_(-0x1.bbc006c9393532c76b5d7283eb2fp+11), C_(0x1.2211c4425df9c2e46c401315e702p+12), C_(-0x1.5c64ff1d8bf677ab09ac8c8005b4p+12), C_(0x1.845a35a3158347c96beac73413ep+12), C_(-0x1.87e6df0fbf8ef42fd538df2c44e6p+12), C_(0x1.6504110de640c4682cba264cf46ap+12), C_(-0x1.2b9aa02be2b35409ccae86a8991fp+12), C_(0x1.cebcfb329a57eadc95a6866f02e1p+11), C_(-0x1.3e375c669dabfd4de916796f2023p+11), C_(0x1.7d70c67fe8973e080c2a8eb7f182p+10), C_(-0x1.8fd6874952e8a903dace07633bccp+9), C_(0x1.57b6f4096f1cf0152662dc88de7ap+8), C_(-0x1.3e2c004c73161017d1fe47d49586p+6), C_(-0x1.f1e0f66437531ffa287a43716481p+4), C_(0x1.39f676c7227564daf7244ac826e7p+5), C_(-0x1.07f7de36ead717df75674d143f33p+4), C_(0x1.6eaf02b444bc4ce4684168c04e9ap+1), C_(0x1.f8896c7b27aea8a0c13b638bec5cp-4), C_(-0x1.b78e4a3636f77d36045fd051d0a7p-4), C_(0x1.faf293c65fc88bb39eaf07f0c833p-8), C_(0x1.a6b604defe6a94f42a0056ad171bp-12), C_(-0x1.9886be2865e4c650159b7d4d95b1p-17), C_(-0x1.7c0bde2d4dd50659d8e873b48d0ap-26), C_(0x1.90aca39ea2e8ac3870ca13b1a564p-34), C_(-0x1.91166efa6d0a92bc7a2e9704d452p-51), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 9> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.58e7df6732419db59e9ac42f9ab1p+3), C_(-0x1.c53a29616fa380817d7f5e347aefp+6), C_(0x1.0cc18d57a1a1fb48e108cac3a066p+9), C_(-0x1.7c92fc1b6ca3895680c38212861fp+10), C_(0x1.6a1945d20b68d8baff4381a817e2p+11), C_(-0x1.fab771c0e1baf24e2c8f95c4199fp+11), C_(0x1.254298acbb2d449a7b3bb526f729p+12), C_(-0x1.407f9dc95f9b1203f980178aa4a8p+12), C_(0x1.53233c63dd8a7f18f0a2b3af5ccep+12), C_(-0x1.3b528b34539979362d8c0e80e80ep+12), C_(0x1.e2ea2797d25e54507eb78c335f71p+11), C_(-0x1.3d6169dd11c9bca3212058149b0bp+11), C_(0x1.75fce5d641404560c1229131d8bdp+10), C_(-0x1.2b5b50127f9f56d44ea4f01f61c9p+9), C_(-0x1.18e0518b142ec5c99d05ce213217p+7), C_(0x1.ed1ee257a974ab35fe81e878de48p+8), C_(-0x1.f5a5a8d8a9b3c073ca67ee8615b8p+8), C_(0x1.920c943e9e1a266133bfad52e46dp+8), C_(-0x1.22c1385412c45fef72454e0ef403p+8), C_(0x1.4b1ba13f6628b1dd44be57f24352p+7), C_(-0x1.ec876f34dee07503da6a5d0e4af4p+5), C_(0x1.641261f15a2bc3d54eada0d94bc5p+3), C_(0x1.3bac7fcb6881fd2d747aef7d1cc5p-1), C_(-0x1.3ae0db5ff49e0fda9ef09cfb9b34p-1), C_(0x1.0ba445bef297ba3c0905b698e9c6p-4), C_(0x1.c3804abda5b0efa9daece8d22468p-9), C_(-0x1.3c78fba89f7e4dd2da5d2b0f1ab2p-13), C_(-0x1.6e90a45b17e11834e30d4035587ep-22), C_(0x1.34c398a5dd4857b9fcc2b9b3ec09p-29), C_(-0x1.351bbccd566d5b56e0d110fac029p-45), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 10> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.0cfd9304e9d0e9e89a508d967352p+3), C_(-0x1.68c00ea69764cc01a573447ef7eep+6), C_(0x1.b4a63a85c6a121d6db6758f94115p+8), C_(-0x1.3c4fcf304828edf08897e340df25p+10), C_(0x1.35efe9ab816f6ad2ce3b95243287p+11), C_(-0x1.c3872b5b6dcd2512d759e390543cp+11), C_(0x1.1261ac83f506bff0f38c31198cefp+12), C_(-0x1.39e1fa3f00833a2dd6c432f2edcep+12), C_(0x1.5e4c9d364466ef749b8d0d88979bp+12), C_(-0x1.6b72e41a84905cceda3e7fc6c066p+12), C_(0x1.578fd4c404248dabc968b7c529b9p+12), C_(-0x1.30faf3648505cdf5f671e0ae34fdp+12), C_(0x1.0174ad7c898217c903f8f07741efp+12), C_(-0x1.942bc49f2f52a22fa3eae80252dcp+11), C_(0x1.25cbc7992fd63cf31740b115daf5p+11), C_(-0x1.914caac5676309ecc6f6c550d8ebp+10), C_(0x1.f93f030387c4d019b6838cf24389p+9), C_(-0x1.1b4f5dfc787bbc32262ca3f167bep+9), C_(0x1.1df01b8a36a8b0e740af0c5b60a7p+8), C_(-0x1.048db6f3ab7c1cc8ce49930b812dp+7), C_(0x1.730bcb9c58bfc20d60bef0471523p+5), C_(-0x1.09b56b87c1c7fb7baeed74ce6cdbp+3), C_(-0x1.4fb833b30fa7eeef6a7358348b92p+0), C_(0x1.e38e1048601a60cbceead8c786b8p-1), C_(-0x1.ee07e572041f738c256c5953b0d7p-4), C_(-0x1.913ced3c0164b56d323de7f36c57p-8), C_(0x1.0726b83c4032bf81b9ad0141e709p-11), C_(0x1.2751c6763466bbc5b359801c4ca6p-20), C_(-0x1.00ceb77e39cabc22cbab1770a0b7p-26), C_(0x1.0123085a8910152fac9990b7a672p-41), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 11> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.6ddd33f198eb3c96ba1c54f670fdp+2), C_(-0x1.ef9ef6f596ce44132c5dde1777d4p+5), C_(0x1.2bec86422c72fb4a905555a96936p+8), C_(-0x1.ab3138ae63b3c1e29f1c692c8b8ep+9), C_(0x1.90171accf1ae92cfd9c146fd2557p+10), C_(-0x1.0b8279e6381aa2b741cb2d9796cbp+11), C_(0x1.21c7cbc3a51f50320f21aa56334ap+11), C_(-0x1.3291c6bd3630cafeef14937244cfp+11), C_(0x1.52187d4f278255d90661622e17d1p+11), C_(-0x1.5764b21031af003ebeca03a2b0b9p+11), C_(0x1.2d82b77758b7aad296e73eef7d7bp+11), C_(-0x1.efcf6fd97e7509c927b7122c5c13p+10), C_(0x1.96b834131566dc9e06167e63c7d4p+10), C_(-0x1.34d67cf0a39ad9453edbb75a25c5p+10), C_(0x1.9aafba6f0ff85ebebfb694ebd422p+9), C_(-0x1.002f1ef9723b265f846b3c2332f6p+9), C_(0x1.378956144d5be0ceae60b1a0aa28p+8), C_(-0x1.48f6827b3eeb52ea5b3fa287d164p+7), C_(0x1.17de886be7cb3ac887cff35422aap+6), C_(-0x1.abd1c377daaf4123bc47a29397f4p+4), C_(0x1.18836fa0aedf116ff47c167f808p+3), C_(-0x1.72b11631904f6bd3fd5a823e34cp-2), C_(-0x1.93c736e0c44c0da3f45cfa30760fp+0), C_(0x1.74105b4ecfea6c16423bc348f8fp-1), C_(-0x1.a9382883de207a4dbe62c7de34c2p-4), C_(-0x1.8ee02bc822713114de66688f8544p-9), C_(0x1.b89c5ecf7a7b773088c639b3a53ap-11), C_(0x1.5051009d42a4549b60dc1a4ffdc1p-19), C_(-0x1.a9ba33acc416babe2777385baddfp-25), C_(0x1.aa6a7d0ba81031b3c9971794d3cep-39), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 12> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.7e688b7526a774b76724ba13a96cp+1), C_(-0x1.044f66dd0bb355e9e82c72d5eaffp+5), C_(0x1.39d2780b0ad2f5c130250e42d893p+7), C_(-0x1.b63bb8898df0968b0e5291c5adf1p+8), C_(0x1.86b367123a2139a59288ccfca3dfp+9), C_(-0x1.d8001c1817ddf23795b1149354dfp+9), C_(0x1.b397f071da3115c284e47fbefa66p+9), C_(-0x1.9ca191acc971e01d76f080b3e85ap+9), C_(0x1.d28d7edb2db091286a502e3d1491p+9), C_(-0x1.daeab5bf793f0a5db198f7c1b01p+9), C_(0x1.738e37d47cc21d48d3f57dfe7cfep+9), C_(-0x1.0785a3e74df59d20286619983999p+9), C_(0x1.a556a97ba734840eab5a765f294cp+8), C_(-0x1.320671c41025d2eac683ee371196p+8), C_(0x1.33661041ead74be914f62376ca64p+7), C_(-0x1.d86506577d5080199e51f7260b6fp+5), C_(0x1.9ebe0657238551a24ab6c6c0e20bp+4), C_(-0x1.ec9ee904293324442bcbed481925p-1), C_(-0x1.1034340999c868fff5238312a8aap+4), C_(0x1.04d7524f6a9fff23ceab9523e19cp+4), C_(-0x1.2fcc89fd04e88d59c75667d7c4bbp+3), C_(0x1.7c24bc012bffc7d1e21de0348c52p+2), C_(-0x1.c84c145900f77b34a4876818624fp+1), C_(0x1.57bccbd9e4fcea0faae4ccf7a2f9p+0), C_(-0x1.d81daf1af068d1091e32e8f2fcc9p-3), C_(-0x1.20f9f72def1de8f22a005f73e8a4p-13), C_(0x1.c2f2ad7e016f5272ff115101365p-9), C_(0x1.baf6705e59cac3284164d29d2adfp-17), C_(-0x1.ae94eeab2dbd4d40eac4360abe26p-22), C_(0x1.af91281d9938911a63573a1c8a3ap-35), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 13> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.a308ef1d16805759614e47ced45fp-1), C_(-0x1.1df37cb20ef6cd526bae886688ccp+3), C_(0x1.5a47cc2daffcb2cf20f8cbe23f9dp+5), C_(-0x1.e7cc32d4c7f05cc178a92423af38p+6), C_(0x1.ba5aa6f58d154790fde87c08c1abp+7), C_(-0x1.137ebe2556bb5c31390bfbcc3555p+8), C_(0x1.07f96c27b79ba1c76a48900fad0dp+8), C_(-0x1.f176d6c3151d8ca65c2069cc8ba9p+7), C_(0x1.0c09a0e0bcb9ff8d7d5ef1548dd1p+8), C_(-0x1.12bf0f34a9a5500b54ae9f341239p+8), C_(0x1.de16d55d65d2bb62ec7f100c7235p+7), C_(-0x1.8c7b496d3d3fd8274eb146a1b265p+7), C_(0x1.56592506626bc28d37aec7a2a08bp+7), C_(-0x1.13d919c9693b2e85ee4209bf711ep+7), C_(0x1.8b69de5718a09e2e84e51d955579p+6), C_(-0x1.15b78c90e783729339c94d803f63p+6), C_(0x1.8188b8db550fea38899d2489ef66p+5), C_(-0x1.e03c011f666ac66011b7609652a9p+4), C_(0x1.12ed8b36e986458009c8fb08c63dp+4), C_(-0x1.36a5b057bc26ecf6312ef4c8aa89p+3), C_(0x1.3d2641cf67e8fbde6d24a0c08316p+2), C_(-0x1.11ef148707dfa11f55a9bad8158fp+1), C_(0x1.c294da23fe29fd37f9c13ab439f3p-1), C_(-0x1.4f57e7e05e818e7ff02709327119p-2), C_(0x1.1ebd9964081cf6e9a360b8a8d058p-4), C_(0x1.367917f76eee2f0773ffda2dee7cp-14), C_(-0x1.fdd37749c94d91b95e5811e014fcp-10), C_(-0x1.0fc0733cbc7d657bb2812f3ed59dp-22), C_(0x1.f945fb653cfda9182e6054d0438ep-22), C_(-0x1.fb1bcfae6e46d13bafafa99aa27p-34), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 14> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.74ef139ad4e11f00f65daa8820cdp-3), C_(-0x1.fd9d32fdef73fc52b4caeb648516p+0), C_(0x1.3320f17be8b7073381e865362afep+3), C_(-0x1.a9e31dbd98dd09635b0d379485c8p+4), C_(0x1.73dbdd38a87f5064433a350c721dp+5), C_(-0x1.aa50f206f25c72a99cb7b1dfa7d2p+5), C_(0x1.5e48ac9c2f1f1eff350b14cb6a0cp+5), C_(-0x1.20d7a78e95ecf57e7add31db3fd2p+5), C_(0x1.4350b94e5aa2da50e61a74e27b2cp+5), C_(-0x1.5499ff05228ddd2807c95d8259cp+5), C_(0x1.0d8c7214ffee0a1235b3874b3a3ap+5), C_(-0x1.8fe88996cfa9a55157f19af30b98p+4), C_(0x1.63da6bffb7322ee61e908c187b83p+4), C_(-0x1.24223f5c1b9d9506d6b401adee8dp+4), C_(0x1.7851e19176715ec2bb3ef2e77d74p+3), C_(-0x1.e4fd178fcd0ef2f7354c8299da37p+2), C_(0x1.610a439acb28979c58f2b0d330cdp+2), C_(-0x1.ae4c3e147b71c294863ea12a0857p+1), C_(0x1.a6fe28ab7ffa991086c47e322a6dp+0), C_(-0x1.ccc920f98175dd5797f1c22d8029p-1), C_(0x1.f9389387bff6b354c93278385205p-2), C_(-0x1.8612de961fd0228a7f5532d1edd2p-3), C_(0x1.fd43fc0e7883ee43ba7f30ad9465p-5), C_(-0x1.aafa1e0c4a86f88db60d4f1fa17bp-6), C_(0x1.b638737fa8e8eebd498a75e7c49cp-8), C_(0x1.071682a1f5057e2143b4d72fc7c5p-11), C_(-0x1.a84c3f530239b63f7da26929d4ffp-12), C_(0x1.a20c2374a0f6700016084886c2a8p-18), C_(0x1.e7f1c0adda0f62adb67917e7d654p-23), C_(-0x1.eb089a4058efe658e6efa5db9e34p-34), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 16, 15> { static inline constexpr std::array<Real, 32> value = { C_(0x0p+0), C_(0x1.31c729360063e0149d619e7c5659p-6), C_(-0x1.a21aa8a66a6833da5818bdb00dc4p-3), C_(0x1.f656a46fa2a9169e56e0e1915e67p-1), C_(-0x1.589b4fca92a9f10523379245a6fbp+1), C_(0x1.25049558932c1a7bf94dfb345bd9p+2), C_(-0x1.3b0f087615a26809432e1d6e01c8p+2), C_(0x1.bed87f332745bf792baa46fb22d3p+1), C_(-0x1.37fe75fa2aa3b693bb533ebc90ap+1), C_(0x1.75c34ae38a11fc31440e21813692p+1), C_(-0x1.9f32b219f201f09e83858382dcccp+1), C_(0x1.2a50b5ebe798e437ef2503085768p+1), C_(-0x1.80aff2523d4b3342a833db6031p+0), C_(0x1.71f9af43c19f0952a4e07472578p+0), C_(-0x1.40d21595459cbd897728656ded6bp+0), C_(0x1.6edc3b8de7cbccef39ac5c5b8452p-1), C_(-0x1.abb5cf29e076b1f30f36686df3b2p-2), C_(0x1.5fdcb9c5e1f87c285b04c2fd8053p-2), C_(-0x1.af4e9b9242255620df4c3ab9bf57p-3), C_(0x1.57b33e4d63cb5298c7100384e3b2p-4), C_(-0x1.736294d370dec79efb4014a700a8p-5), C_(0x1.e1a61d7964668ec603cb9aadd499p-6), C_(-0x1.38138139d6cd8275f0059b0302fbp-7), C_(0x1.a99b874715d59140bd445bce9797p-10), C_(-0x1.191ada841d111d8ae1875d5194c5p-10), C_(0x1.44a23a1750afa85a30082caf581dp-12), C_(0x1.4db128e094987961ef4278d6a462p-13), C_(-0x1.2cad37f3dbf9e7a235442f3e067dp-14), C_(0x1.f38c667f694821291da674a42ae8p-19), C_(0x1.c870773604d02f72b6568dfdd8eep-24), C_(-0x1.cdd01a024c710ef11bdc57da89cfp-34), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 0> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.086b758f2eb28fd921eab56a11edp-14), C_(0x1.39d1d9b6240962e535ae21b2376ap-6), C_(0x1.6586cdc7bb445ac141a0312b8103p-2), C_(0x1.b9d577601dcdb0dcb740f8b98373p-1), C_(-0x1.29ed86125a029803c293e63e1f67p-2), C_(0x1.35228579cf169af0c995d4c52ae4p-4), C_(-0x1.d940ea5a753eace5078cc210d381p-6), C_(0x1.eb66212482f668b8fcf8d3b919cbp-6), C_(-0x1.11e395c55e411390d39b33c12486p-5), C_(0x1.cac8225b45414be4173b8eab4ac8p-6), C_(-0x1.1c2e36898d63a7284aba1b17de4dp-6), C_(0x1.01459f9607ee130b7061d5eb1498p-7), C_(-0x1.3f705b6c01172b34ae01036bd601p-9), C_(0x1.aa4226008e468554c7236a5aa13dp-12), C_(0x1.a227b89277165ccd7a008b2025e9p-16), C_(-0x1.df4876fdde2706ccc1065c683114p-16), C_(0x1.a3348c487531e3e6d511223fa2f3p-19), C_(0x1.bc3886b839cf7c209eee289f6c48p-20), C_(-0x1.3c3d3ef190e94974e0b46a471d0ep-21), C_(0x1.fe10f39c7db3f74fc941d5c89993p-26), C_(0x1.28b87cd51d74b7827dcb3f76ba15p-26), C_(-0x1.5145c4e025139affe8138cf7767p-29), C_(-0x1.c7ef81e91e695e468ea8a5c582b5p-34), C_(0x1.74462d4702c982a79ea8a83c2f8bp-37), C_(-0x1.2eb21459dd6235173db20b5350d7p-41), C_(-0x1.802cada0cd8903e4e2cc5b09958dp-49), C_(0x1.f4dc3c81c07b2740f80e7509651ap-52), C_(-0x1.790641c0d0b7b530c5f3ea46fd8ap-59), C_(-0x1.7be9575142225928d5569ad8cee9p-68), C_(0x1.70d851342e8d54c091b220a3045ap-78), C_(-0x1.08ea9c21a48ad423ef299cf99255p-94), C_(-0x1.6d7121dda4d1a467dfa9e3b22e7bp-121), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 1> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.1287f0812571ced1e1d4f218f2e4p-11), C_(0x1.3963c1ea0712ddb1fe38bd9c9b8p-4), C_(0x1.56352993b88d1ab506ab53972cdp-1), C_(-0x1.f6edcab194e1f1b63bcde69f40efp-3), C_(-0x1.17b7ccfaac21f6709bae8571c345p+0), C_(0x1.0e886b0af5f1b1856a755304e662p+0), C_(-0x1.8ecc3f557378e15602e7822e181ep-1), C_(0x1.fc587a34e0e0edbbd49047b2d639p-2), C_(-0x1.0d307d8a24ec475046eaa126ebfbp-2), C_(0x1.aea667b7c25e2cfd5f86bd0180b7p-4), C_(-0x1.9d42bc92105fbc1675af424ec92cp-6), C_(-0x1.84840a8e18b0614ca4eaeb216151p-11), C_(0x1.a3a969f351753a208cd3e5a8f168p-9), C_(-0x1.2b9d22453328eaf6cc00ab2ce255p-10), C_(0x1.95e4d7f634c66f1127081eca4c12p-13), C_(-0x1.348b4478275cac4e09ed00491749p-14), C_(0x1.d3098c771ac4dfa090c0edef58f9p-15), C_(-0x1.49cdbb7d5d852bdcaa3c1cea09bfp-16), C_(0x1.12cb123d22d84321bf94a9d64f4p-19), C_(0x1.ee369d60388e0cdc7a4cb2173367p-22), C_(-0x1.fe1ef00c018375d62e92ff3458b4p-24), C_(0x1.a7341d99c255c94d652d9d8f033dp-28), C_(-0x1.530b27430376f235ffb52485dd26p-31), C_(0x1.baba95d77648bbe90c5142dc80f9p-36), C_(0x1.4f068520e819c6ced35b43d3471ap-37), C_(-0x1.57c84bc8729954adbcf1854852e1p-43), C_(-0x1.4644a16a824bfa62197420e08c45p-48), C_(-0x1.8db806597a9c3e694cd52ef5d34cp-57), C_(0x1.0d894936ca76f0f900d3760154afp-62), C_(-0x1.b4baf18dcac141cf35421e01ae8ap-74), C_(0x1.758520ee5d32f0284861059680c8p-88), C_(0x1.01a0cdf0066867c0900e21808ebp-113), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 2> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.f55ef3437afefacc7be18bbe98cep-9), C_(0x1.0777b4e7fcce2c0e40744ca54978p-2), C_(0x1.3f1d6fcf2ecbd47e40d3c1703896p-1), C_(-0x1.504b43d516f52267cd7e5fe7d16dp+1), C_(0x1.4eaade935c6e2740942fa58ebd6dp+1), C_(-0x1.5ad808042149f417dbd58af6b00dp+0), C_(0x1.cab544fbb7d4b4c60e5f5aaacf98p-1), C_(-0x1.9ef87caf9226b19c3d44c512bce3p-1), C_(0x1.71fed5bcb893256d48b5662cb1dbp-1), C_(-0x1.0f0d57adf520b2ff7dc518f39f1bp-1), C_(0x1.30c75d43e61143b20925258c8b83p-2), C_(-0x1.e86b0f7372ef580e2a548027c15bp-4), C_(0x1.b73425fd2a98ccd833576427e8eep-6), C_(0x1.4682acb561a50d1c8642601cd7d1p-9), C_(-0x1.2eacc75c57cd93079a89b775a2e9p-8), C_(0x1.a21a6f510bbe181a8681692c18eap-10), C_(-0x1.147cb459e63791d570da7fd30181p-14), C_(-0x1.28e19a4afa00bfea3a7c1b8c9b43p-13), C_(0x1.931858e53ca4936f19114cad31b8p-15), C_(-0x1.62eadbc9e95863c36c22bbc42279p-19), C_(-0x1.de496f2e6fedcba7342c1d21ed73p-20), C_(0x1.70387d4ff6ccbebda63c71a99d37p-22), C_(-0x1.75a0d535dc76533ec0cb363c4ecp-32), C_(-0x1.a0b3effc1d4a9bf1824673d2a6ap-29), C_(0x1.b9e3fb75297f14c235645de65301p-33), C_(0x1.a8bed9eb35249e37507da13a3226p-41), C_(-0x1.16b3429b615b32018d7a07c98618p-42), C_(0x1.ff8673e3757ca4761f1167047725p-50), C_(0x1.145878dae073bba8c6a0fb3960bap-57), C_(-0x1.9feee550a5c8e58e5b4245c07331p-67), C_(0x1.83b415f1b1f54454e6745eb4b4d9p-82), C_(0x1.0b692b789332c1658c8a24d9733ap-106), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 3> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.8aac090c80a048b0460ec2d7badbp-6), C_(0x1.5750bdc83f394eb3b8faccf4d05p-1), C_(-0x1.50c8f6f54e2cc9e3e11af9656bf6p+0), C_(-0x1.8a451969aae1b985b829fad06526p+0), C_(0x1.a2b7eb3937eee939c776f8d31095p+2), C_(-0x1.118e064b4695a7f92c4b4501fcb4p+3), C_(0x1.c7bf87fc63cf93f68f74e5e7c825p+2), C_(-0x1.1d8d78044ee8ee6d6d812a1942fep+2), C_(0x1.e73d8bee7689f31a0590abd75fdap+0), C_(-0x1.2add7b6319082ca3c2efcadae5afp-2), C_(-0x1.d3adb45266b9c9f4635ae6e3441ap-3), C_(0x1.033a83600934f9863377b693a726p-3), C_(0x1.b5451b19e5929d480505b5a86b0ep-5), C_(-0x1.7b8dd6246eb29127a889717ed76ep-4), C_(0x1.791fa45e84d145e6dc4c1510b4b6p-5), C_(-0x1.7c0beae24c83b65b5de7467eb78fp-8), C_(-0x1.5782b21d6af61f4f7dba20838e5fp-8), C_(0x1.a840c3e83eb503c78b20b8a28b62p-9), C_(-0x1.986e55de09433017be901893ecf5p-11), C_(0x1.26705b8c8ad65b55b61c41f75588p-15), C_(0x1.d3d83a9fdf1319302a3787bdd6a4p-16), C_(-0x1.d82fd865aa4582e2885f825ad846p-18), C_(0x1.f5b54c9c1896a94bf7ab1ebbd585p-22), C_(0x1.fdc61d80cbe3fa72384c0df210aep-25), C_(-0x1.0fd59d78e582bd5f0e263192f5d2p-27), C_(-0x1.b2380e734b4f9e20f629d35cee8ep-35), C_(0x1.88e8ce1bbad67bfa806d6889ad1bp-37), C_(-0x1.9888eae4a10ce8dbd9f3a51b6ca7p-45), C_(-0x1.466525261ac31d726b6464754be8p-51), C_(0x1.1b33afe3275e0f5266344c60d67dp-60), C_(-0x1.cb1864e2c62c6b93f97126fa2abep-75), C_(-0x1.3ca6dac99711dbeeb8df3656a48ap-98), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 4> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.0491b50257a217948037c313c632p-3), C_(0x1.088e085a415b940e86ec5f53c805p+0), C_(-0x1.9cc2273fe20fe221de7ca14dc50ap+2), C_(0x1.b3ef626d8f7b0512e777dbab2038p+3), C_(-0x1.fbdaa71ed3446ae9d3eb57fb2edp+3), C_(0x1.ba80f6fc4d07a16a8240d21d3427p+3), C_(-0x1.97b8947186ea82517ff8170fe92bp+3), C_(0x1.9ec07cc028783871c266a993e177p+3), C_(-0x1.7db26c06ff96156fee69f60a32a6p+3), C_(0x1.1910e3936c71e0698749e0100bd9p+3), C_(-0x1.2af49c29492fb4bebe4ea4c0492ep+2), C_(0x1.4fef94a557c4567b7c45f05c21cdp+0), C_(0x1.8759880e99b2b3b19fcf3cb856f9p-2), C_(-0x1.52e5ecb25ff9114988afe0915415p-1), C_(0x1.5f17262b3d7f451babb3a37c2f6bp-2), C_(-0x1.a7f494de47ba1f8ccefd1a48b767p-5), C_(-0x1.5dc2bf7dbd1d10649dd1efeed2ebp-5), C_(0x1.fa52c9fe27d631a55490bc5d6453p-6), C_(-0x1.0edbce8cbb631c85810ceb7afb83p-7), C_(0x1.51bdedea6ecf9aa291d741d5449p-13), C_(0x1.03e8c53050207786cbf7a98773ebp-11), C_(-0x1.fbae0e996b24eb9e7412c53365a6p-14), C_(0x1.a5405fbd60ce14b0ebcc8751154bp-18), C_(0x1.8401a133e3997a7f15f86f10443dp-20), C_(-0x1.9620d8180585bbc8bcc656387216p-23), C_(0x1.59175b5517be80fcdb2badddc5c9p-32), C_(0x1.a5d4ddd28a790634117b9f52ee98p-32), C_(-0x1.a9e2125d31a3c8bd4449e4b094e8p-39), C_(-0x1.068a399565e004f3cf609f3513f9p-45), C_(0x1.3ca73a6f9a6a05c209544433b8c6p-54), C_(-0x1.740e2252efb089d1ae6c93a1b5fp-68), C_(-0x1.009e0cb9ab19e5360f5dcaede516p-90), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 5> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.12eabbc13ae4184543451253a593p-1), C_(-0x1.dcd48a30309a985e66770059ddf6p-1), C_(-0x1.61bd6ca0b74ea8c2e61c8e522049p+2), C_(0x1.4b16a330057a9a464e1afd029265p+4), C_(-0x1.6645126fb9e4a4d9cdaf8add299bp+3), C_(-0x1.746a9ef1dde16ed58e0b432bfa77p+6), C_(0x1.49df58a6d0bb202b69c6e445a50dp+8), C_(-0x1.2844a2ebcc53c8df614ae3957cc5p+9), C_(0x1.2e42267f64054a81e12f7b2ff462p+9), C_(-0x1.0225a1d4c62f4d7b402b42538ffp+7), C_(-0x1.601645627c56a1dba4f1a59caa2dp+9), C_(0x1.58f8de6f258472f30e4dedd3b403p+10), C_(-0x1.693ca7a1da62866e49185596166ep+10), C_(0x1.d8c8ec24a42109f71622d8b3d887p+9), C_(-0x1.46d08ddd7ae2544339cc13f918fp+8), C_(-0x1.3b3b035293fc39a231fe7419868fp+5), C_(0x1.d12d6fce626a721298fd94ab886ap+6), C_(-0x1.06b1d4ca392cd5c4bed1c5482646p+6), C_(0x1.11a1d31e54e9ef311bda0933730cp+4), C_(-0x1.40c7309469440a4e5541e2e4c29cp-4), C_(-0x1.5ca50bfd4e3e4aa86ac8722ac5aep+0), C_(0x1.83af684fffb5b293cdc91cda7503p-2), C_(-0x1.9e6da0de05c76e1cd2d553753824p-6), C_(-0x1.7858ef153620d7c30fc31cf56a03p-8), C_(0x1.dbde29a936a150c27f696a66c4f8p-11), C_(-0x1.7bcec5a7e8c292f303bfbe3660adp-19), C_(-0x1.4f2043d7bc5e1f3a3aca5e8c6039p-19), C_(0x1.c2215a9e7432391b90e0fdc7be76p-26), C_(0x1.2faa97edde49cab9c4399ff18db1p-32), C_(-0x1.023988307e2875f374e798bb263p-40), C_(0x1.b307ce337f9f1788bff0e23a47f6p-54), C_(0x1.2c0d7e12bcbb5186b064f5495d76p-75), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 6> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.d53d20f76b4eb49c1b6330a595f1p+0), C_(-0x1.86cf243ed21bd08659834822cd59p+3), C_(0x1.17b81677ed649599a4d3595e967cp+5), C_(-0x1.abb0b3868d1ea2ff8d47921a5292p+5), C_(0x1.f121a0540b3049d09ec7e5551764p+4), C_(0x1.17380aa83335c1ea7599d28ad525p+6), C_(-0x1.0c95dd1e4ca7caa2bd6deac65cdcp+8), C_(0x1.0b7efc300b3c27dde71e697ba35dp+9), C_(-0x1.826ec4b2204db57bfd19486982e8p+9), C_(0x1.b8a41eb5a39bd8486cfb324fe223p+9), C_(-0x1.a1f5e828547f7c5b2bcb67af2fb7p+9), C_(0x1.5201c70981a0b03733b9e6dd6e09p+9), C_(-0x1.b80f281da59c5918dfb2c054585bp+8), C_(0x1.5562f5403383fa55297b9a337231p+7), C_(0x1.90f51962a56d70bb1b983c1ee4e5p+5), C_(-0x1.24ca65a640b01ad09d88f66dbd3cp+7), C_(0x1.f2553461b931355eaf34d3576431p+6), C_(-0x1.dd44638d13da936b6e3a63722e62p+5), C_(0x1.c0ad57b9e59f4a68dc33ce587da3p+3), C_(0x1.4256b15254b23391d079094819dbp+0), C_(-0x1.fef8071c6ce2bcb3957ad8d4d2e8p+0), C_(0x1.22e6dade4ecd5ba17bba3803612ep-1), C_(-0x1.5b42b69e978a5317df9091ce52d4p-5), C_(-0x1.642c165a0dc2d5830f0c68cef691p-7), C_(0x1.1410f766f54ec568508253190fd5p-9), C_(-0x1.866a34dd716a310ea2f7f87ada3fp-16), C_(-0x1.0b5a3f31f6ca3105091deb1b111dp-17), C_(0x1.e9e690f2d2cdcc9f8d2e274aa38ap-24), C_(0x1.73aa86e33bd9c22c0a03f2815f8fp-30), C_(-0x1.ab771b8e4149cc3a675cc4898a01p-38), C_(0x1.0db4d80a1d5dee01c7237b2562edp-50), C_(0x1.740bf5a4533bf10d9f87491f9f4bp-71), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 7> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.295ff3e65734f855f39c88490c77p+2), C_(-0x1.53451cd19bffc6de5e35716507fdp+5), C_(0x1.6528da0fe333f438d196bece2469p+7), C_(-0x1.cbdb85c1ecbc284c645d00a79bf9p+8), C_(0x1.932ea82ba595876f9e544cf675e8p+9), C_(-0x1.f84307101d81872f84fc73fbdae3p+9), C_(0x1.bf6854697aae94bb46e7b0542bd4p+9), C_(-0x1.bf12de658509da0b644cd695e0b7p+8), C_(-0x1.4368651bd726f170cb68d94b4efp+8), C_(0x1.6e6644c0e40c6d361a9bc94d8ca8p+10), C_(-0x1.63936602c4e494fb5778af823485p+11), C_(0x1.effe4a55a204acd95799b1a3ca8fp+11), C_(-0x1.113fec7086061d5415319b0c5f72p+12), C_(0x1.fcb0d58a8410175cda764e2c75f6p+11), C_(-0x1.a4bb9f060dab410fe3bd526fda39p+11), C_(0x1.329610ee0fa8084fbb618f8b1294p+11), C_(-0x1.6a3f2ab2a8a1738b723263aa7f2ap+10), C_(0x1.28b90043770072e85c55c758b4e2p+9), C_(-0x1.98095b9a0fe58b99d4dbb49d1d1ep+6), C_(-0x1.7e9c13fb873c76fdf7051d8da9ep+5), C_(0x1.38a8e96bcb8fab70b383bca5ab41p+5), C_(-0x1.67764003f2db50e6202ef878e23p+3), C_(0x1.be87d29fcd9eaf1da18ad636de29p-1), C_(0x1.281bdec2a7f8db103e09703f13fp-2), C_(-0x1.0bb48c6ffbbe8bdda51c03bcdddep-4), C_(0x1.77235116b4238df6f725d274e118p-10), C_(0x1.6675983c9f507c163e6782c7703ap-12), C_(-0x1.d3d6efe237ab3413c9e890035877p-18), C_(-0x1.81b8689afa6e0c7916f7a3885678p-24), C_(0x1.2f0e5249c6fc36ea9c04c196c326p-31), C_(-0x1.1cfcb984a013a5a7bc95cb9320e8p-43), C_(-0x1.891f706028ea406f88faa50dd1c9p-63), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 8> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.06fc2ba8e79eb72495307bac6ae7p+3), C_(-0x1.5489cef9cc78f65236603d4cf5b6p+6), C_(0x1.9903c6b2ba02d6090a910755f61cp+8), C_(-0x1.312486b078b78cd8f1a70419307dp+10), C_(0x1.4302b86058a5a1292888fa9ca885p+11), C_(-0x1.0b2ccf4526b8c244af343cdb508bp+12), C_(0x1.789932548bf931d92c8e1c8fdaeap+12), C_(-0x1.e12b9567cda5d69eb9d09a4087d7p+12), C_(0x1.1abf23f09fbf8f276f484c29af9dp+13), C_(-0x1.2d5cd803da680fe5c48016ca91a5p+13), C_(0x1.2251ae99e408a20ab182f4722d14p+13), C_(-0x1.003d36dfd5a7feaa6fd4657cd76ep+13), C_(0x1.9f286327797df00b01373e3a21a7p+12), C_(-0x1.2d0f0d917a1ccdeca67d314f4298p+12), C_(0x1.7c4b637f2390259cb36649bf5cadp+11), C_(-0x1.9dc666df2eb31fd9d525e80a61c4p+10), C_(0x1.706ff6209d6f31fbb17a3cac3d1cp+9), C_(-0x1.76b515afa4c5f086db4f0c0e11f9p+7), C_(-0x1.24480d16a6ef2cbf187e850a3c68p+6), C_(0x1.d48700b685d23247b608b5cfdb9bp+6), C_(-0x1.05a23476b2f21c52a72aaf8a7e1cp+6), C_(0x1.24935371deb2ec43e23d888eb93dp+4), C_(-0x1.4ca6dde02f223d08bbf5ac28bf78p+0), C_(-0x1.58615b018cee75761c906455ba3p-1), C_(0x1.608c7993e060e80a3ede46931d7cp-3), C_(-0x1.b69ce98ab84f025bb8e299cdad9p-8), C_(-0x1.4823967168edf829557ae3a04f84p-10), C_(0x1.35d91ac6fb610d89a9ca12ef88e9p-15), C_(0x1.1a7bfc97dc33f6885878a1ae185cp-21), C_(-0x1.2be69d6aba0faabdfc72f4f8d745p-28), C_(0x1.ab2f7726c736fed01e244eeb7278p-40), C_(0x1.26a24576b891cedff10c341592ecp-58), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 9> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.7d326037dd59f04b93695dea64a2p+3), C_(-0x1.057b7f9e150cfea8b7f60a37452p+7), C_(0x1.46966635c034e3d077a23a34c32dp+9), C_(-0x1.ebce82ab1e29c2e7e1d24be27a9dp+10), C_(0x1.f63288a008a00b0de93e3b52ce8cp+11), C_(-0x1.7a1dba3b5d1c5c524569d9d8ab6fp+12), C_(0x1.cf6c2665a1ce80b761f29f92f584p+12), C_(-0x1.02e9d9c7c198539a0401a9ab1cc4p+13), C_(0x1.126ddfd818acd4152500102b2dd1p+13), C_(-0x1.fbbfeeed9e84aef44a32d1cdc74ep+12), C_(0x1.6dd45600e62e38c3d8b7dfec1df4p+12), C_(-0x1.70d3cf8b17a54f73f23ea7c9008bp+11), C_(0x1.116e22f7a2135c32a071c68e4c4bp+9), C_(0x1.45e19d7a9f76f8d3600c21c7d961p+10), C_(-0x1.541801f00ec703cee53ee38cf9d1p+11), C_(0x1.9d27adde86cc9d38961105f8f3abp+11), C_(-0x1.718955043c8aaad595f50f39f434p+11), C_(0x1.13d222dd6eb22f7f0b910b51dfcbp+11), C_(-0x1.75dd9fab7780dd812eb2ca0b6548p+10), C_(0x1.b95889cd4ba403f3d771bad22aefp+9), C_(-0x1.880fb1002a98d978d68c5c6d01b8p+8), C_(0x1.a3c6bfae2048432d988090525bccp+6), C_(-0x1.6af0f7c1089720fadea20b94dfd9p+2), C_(-0x1.7dda27580f4d6fe605e5e2098125p+2), C_(0x1.bb56918206273622bbbf209a8389p+0), C_(-0x1.95410e4d3fa90c79d49021063b4ep-4), C_(-0x1.1e3dcf4406fb03d054399893d4cp-6), C_(0x1.863de856b0c02544096765b7d0aap-11), C_(0x1.82e7abd79b3d018087088704fa41p-17), C_(-0x1.21b01c6eb2b87dca0186448dc932p-23), C_(0x1.2f1ce1bb4e63e9b48ca04ddd06b5p-34), C_(0x1.a21b5c9a00d44d929d9e41311f24p-52), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 10> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.6636de936afdb6694c4ffaed6a31p+3), C_(-0x1.f93a40edfceae00ae68e29b08ccap+6), C_(0x1.446da371d76c83e79d3a084840a1p+9), C_(-0x1.f81b7969b6241ca8af219aea83e3p+10), C_(0x1.0bf59cc8ab3644f7ca5bdfe6f82cp+12), C_(-0x1.aac9a227eb351b4f5d24fbc72641p+12), C_(0x1.1995306c55b12646bead2c38ad5ep+13), C_(-0x1.558e777f304785d9e9d3bf74bb24p+13), C_(0x1.8e256f3ea2d2e1b7bb373067b872p+13), C_(-0x1.b16f217fc65b857f8f6c9d68eed5p+13), C_(0x1.ae7e2aa7eb75bb88db9f2bedf708p+13), C_(-0x1.8d7f6b89b7d82de8cbcb8a91aep+13), C_(0x1.5bc0c9e9bcdde01e3a5bcbdf7ae2p+13), C_(-0x1.1c9a0c6fad5ae4a6fdea1b4bf6cap+13), C_(0x1.ae2f95c3b0ec2e40af37580cdc26p+12), C_(-0x1.2f129053ebc24ed7fe6fd750dd19p+12), C_(0x1.8d5b23e080443674d13b71f54d14p+11), C_(-0x1.d627dd21f8491dafe966ff60514ap+10), C_(0x1.ed8f9f756f065170266103952acap+9), C_(-0x1.cf7165a4ea829414cafd158d5c4ep+8), C_(0x1.6d33ddb7ad7e8017c0cb0ef30276p+7), C_(-0x1.65b4962a79c56602e9794ee156bdp+5), C_(-0x1.4d3b8e7db26f275e39c95bf13c09p+1), C_(0x1.950dbe3d7aca2e2a683f470bff22p+2), C_(-0x1.dbafbd443f803e2caf8c9757690fp+0), C_(0x1.04861e6b2202e3d3d031b9ec9cb4p-3), C_(0x1.7fe12c9629eace47e413a0b6403dp-6), C_(-0x1.c081b52017ebd837349a62b66ef9p-10), C_(-0x1.adbded066f7794fda23e0774673bp-16), C_(0x1.dd5025fbe71cc3a7e8141de53daap-22), C_(-0x1.642f3973aefae4a4f6422168ecd6p-32), C_(-0x1.eb48617a106cce90559379e40303p-49), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 11> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.24754790ab93df0d2355b8c8a1d6p+3), C_(-0x1.a21d6a4e4d9eb295ee53b44586fdp+6), C_(0x1.0d7a54376cc0558116461ff0ad85p+9), C_(-0x1.9dbd2fa0800f2f31e9998fd08c87p+10), C_(0x1.a81b276d71be81d44423e8d7940ap+11), C_(-0x1.3af0559f33f06e14389580e81a29p+12), C_(0x1.79904c76e9a060ec8e465cd0f64ep+12), C_(-0x1.a8514199bacb20e9109b77f293abp+12), C_(0x1.e26f534b2b2fd591e9ff3ecf4696p+12), C_(-0x1.01e02902687a04ca65f96e6a8b68p+13), C_(0x1.e5294c43a413c0653234e7f1aa09p+12), C_(-0x1.a1b883ef12d1d5472469dd56bc1fp+12), C_(0x1.5e76af85cca307f1a26352596c69p+12), C_(-0x1.15aa3e69a2aa9b29daeedbfe44dep+12), C_(0x1.86a5a2358b24b67c01514a96d3ebp+11), C_(-0x1.f3289b5523f3f49f9c93e10ff537p+10), C_(0x1.313866068b105f8069b31bb16bf3p+10), C_(-0x1.4f6a48ce8e3698f8a03194833bbfp+9), C_(0x1.23a63c6ead8e0e6aa15fe6f45702p+8), C_(-0x1.8310a2cf199a480eca2c2b3394c8p+6), C_(0x1.66b7a5ffb79e9dc036820a3ad358p+4), C_(0x1.9e1737848ca25244943790e11b0ap+2), C_(-0x1.b5673299d6c08937871359b74265p+3), C_(0x1.06e8de128ea18c685f5f56cec9c2p+3), C_(-0x1.231b2877825a92418fa8440891fep+1), C_(0x1.8ab9028a51e09357bf81bd620a6dp-3), C_(0x1.fe0af766377cd4e6799421dcabc8p-6), C_(-0x1.2be854453bc6da5ab705ba515333p-8), C_(-0x1.2ae4048d20f92820dcdbdae1afdp-14), C_(0x1.cba1e05e755f67b92b6effedbc08p-20), C_(-0x1.07cc113fc2e535393592fd575de3p-29), C_(-0x1.6bcf254ca2da0ef6aaa6b7f61e81p-45), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 12> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.6734f40a39a9f749d116c99eb0b2p+3), C_(-0x1.02800bdef42eee8e9cbe8c5b799cp+7), C_(0x1.46b9a7fb0659b383f2e6ccc2bfdp+9), C_(-0x1.d4653513846d8b632e3ab00ec6aep+10), C_(0x1.960cff14f27e9f5b5c924a5e31a6p+11), C_(-0x1.978ccec6aa50557b75045abb874cp+11), C_(0x1.65f6a7cb1b6c9a8b6c858f9858eap+10), C_(0x1.c444279391a3fe3940190e9b0d42p+7), C_(-0x1.743641ef137ce3dda68a9d7a6a5ep+7), C_(0x1.b0d221397077b61aaac6990656bfp+8), C_(-0x1.789f01238bd736d3d165c526de34p+11), C_(0x1.78076d93e8b2a21b1e57f1d51e32p+12), C_(-0x1.b80f30dfff7c8968576fe93ef454p+12), C_(0x1.bb7e92e14a551f987f2cb928af9ep+12), C_(-0x1.d7e88c840d9e4cc3ea2da57543cp+12), C_(0x1.d2485f20f81231778ef5c3fde98cp+12), C_(-0x1.8196b31f2aea3e18b21270e4dab8p+12), C_(0x1.2207d34a8263912f9c133abdb819p+12), C_(-0x1.b1c880d8d5a9e0a75d42bdbef999p+11), C_(0x1.2ab917d5ba30fbe55897a0ecf4f1p+11), C_(-0x1.5bfb47d78d5c9aa6b7b3ea1583e5p+10), C_(0x1.6b044d9f20d2a049f5f6d532822ap+9), C_(-0x1.6ef48eab0e23a150ac4817c103f4p+8), C_(0x1.3c86344d962287236a63845979d3p+7), C_(-0x1.62bb42947746334cf9768602ee2bp+5), C_(0x1.302845e71cb15c4d9c86c4ce6c69p+2), C_(0x1.8f631724d133592549dc9efdce6cp-1), C_(-0x1.82b35bd32ad297d37e7e03fdd8d4p-3), C_(-0x1.957cf95e09e20118e411d55e99c2p-9), C_(0x1.f6dae900f0dfcd786a2b2d830063p-14), C_(-0x1.960194a81c1ce1e7d634227c61f3p-23), C_(-0x1.17e5f78c653c49cc8a92fdb4f4c1p-37), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 13> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.206cbb79429c2414ff0c64aec8d3p+1), C_(-0x1.a083553e4e5a13a3aa202924b202p+4), C_(0x1.0cf208fc3d0318bce33d11bbf4eep+7), C_(-0x1.98490e95032010181534884dfc77p+8), C_(0x1.94a218c65bbc9e721912af119136p+9), C_(-0x1.17c83af6fa175ec696cff584b399p+10), C_(0x1.2a2038ecd41ce99e3b7c57bb1465p+10), C_(-0x1.2a77ab7e6edd3ce02f308ffb22c9p+10), C_(0x1.48a4ad0a80d290f172a8699dd903p+10), C_(-0x1.63c7a0fcade974b2571bdc4dbe83p+10), C_(0x1.4db14cb7e55e4fa2d6df561d81b8p+10), C_(-0x1.1f7d1042db45f9c24caed3674305p+10), C_(0x1.f7c955767cdfe40b013173464e47p+9), C_(-0x1.aa68a77f455100ba1716b7e81665p+9), C_(0x1.42df44997682f8bbd8705c8237fap+9), C_(-0x1.cc3c8a0b1406c4cb1854f7f475a5p+8), C_(0x1.4691261a687538ac5d74074129f3p+8), C_(-0x1.af8b6d718425edc744dded4be2d2p+7), C_(0x1.fddec0373e9c198b0187bcd05338p+6), C_(-0x1.206dca918e07679e17977c9e643dp+6), C_(0x1.394f1079b1d1bab9757ca4be3df6p+5), C_(-0x1.259c4ffdb02b3840be66495f597dp+4), C_(0x1.dbd0529d14d06481a401f7d28924p+2), C_(-0x1.6f7d774b2017e6812c50c53f3953p+1), C_(0x1.bf296d0152554d98043114f06ab7p-1), C_(-0x1.7c872eb94aac4434f1f1ee90200cp-4), C_(-0x1.14b95c5b19212b4fc03cbfb78471p-5), C_(0x1.194e2e645655459aae5fe3cd821dp-7), C_(0x1.3531940b1823b632efb391ef6f52p-14), C_(-0x1.7166e830bc1e3944c924058c6f08p-17), C_(0x1.2436b817dcb001ed9c4696c6c208p-26), C_(0x1.92b45b9b9529b7ad46fa43bf2077p-40), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 14> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.74cc6314dd173590f359217811bfp-1), C_(-0x1.0da07c63bf5b57427067a74fa654p+3), C_(0x1.5af5b4b6a40764cc3bad47672c18p+5), C_(-0x1.03fb3e545cdf8287d11ea7aeca93p+7), C_(0x1.f3ca921092c121957e2f18e268fcp+7), C_(-0x1.44047871dcef9850bbedf1b419e5p+8), C_(0x1.3358182c099a552dc3876c2274b9p+8), C_(-0x1.10bd53f78b9c50f9a6e08d98e9a7p+8), C_(0x1.29eb9e1a3472cf232a6709ddfbd2p+8), C_(-0x1.4a2468b639e365dc0b3fd125eb7cp+8), C_(0x1.26dfa9c8f6c029f4753dc36c664p+8), C_(-0x1.d2f1acef4b4ba3b7e608497ba6a3p+7), C_(0x1.9597a28faf38ab1c2b429a5dd79dp+7), C_(-0x1.5e8348f17beed687dbbf25dfed75p+7), C_(0x1.f9613319a9e146e700ffce02e1bbp+6), C_(-0x1.4ea47fc45e78dfd9cd4f69ce75a7p+6), C_(0x1.dde07d45fce1683ab6993808e07bp+5), C_(-0x1.3e0ca7abd11c2d7cf0bb1fa5493p+5), C_(0x1.599232ab9283f8d2a3c47d198eb2p+4), C_(-0x1.6ac8188fba9f38deb15c420eb1d6p+3), C_(0x1.96eff61828d979e9be81958fbf3p+2), C_(-0x1.7071f5cc1d4694972d2c2c65d04cp+1), C_(0x1.e12484c6688efee796e8769de364p-1), C_(-0x1.4f89eb4be752a44a4402c23fb27ep-2), C_(0x1.c393c8a6167f5e45bd4990217d8dp-4), C_(0x1.7dd147fa057e0b50125bcfcb63a2p-9), C_(-0x1.13f7fd7ac3a280d730a2c002774bp-6), C_(0x1.03548ce92b6649d84ccad32bfb61p-8), C_(-0x1.2341881399bbc4b237f436539e91p-14), C_(-0x1.74f214490ba0ea1723d42cc72a56p-17), C_(0x1.5a01bfbda2f04b0c7709f4c8e447p-27), C_(0x1.dc5f89a7304e8e2e4721be65411cp-40), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 15> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.3c25010b60fb3674740b05f24332p-3), C_(-0x1.c9af2c3ccca23c68e09fc2f7dc65p+0), C_(0x1.254de8cf13856aa803ccd66fd125p+3), C_(-0x1.b1e5eadb5abe7e897564be3e19b7p+4), C_(0x1.9450c60bf67cdef9bb4416994ee8p+5), C_(-0x1.e89b5b0fb80bdea3060e89b3c3b1p+5), C_(0x1.8f3eff015290335d6c5ba8db3ba6p+5), C_(-0x1.26718b161a212a6473dfb26ae75ep+5), C_(0x1.4a6ff4d30582a729b53c09473635p+5), C_(-0x1.8a12303f1194f457265cb9c94d2ap+5), C_(0x1.48e1a80801a27ea5d6b8c09eef2ap+5), C_(-0x1.bb6e89adbb91b4400c7c8bacc9f2p+4), C_(0x1.826bb857299c08edd527360dc4c6p+4), C_(-0x1.66d59bbc1beb521a1822adef1e5ap+4), C_(0x1.d762d94bf79addacd550a56af00dp+3), C_(-0x1.017df19c95ab47cf0b3801bfa83ep+3), C_(0x1.7ce4baf9290af712ae0b872e1a1ap+2), C_(-0x1.076985c84651e1c5e5388d3ed212p+2), C_(0x1.a91f1aca1f4d85453f0f9c33299ap+0), C_(-0x1.19588e1d9e17bce2cb6d81a29cf5p-1), C_(0x1.70b50254db487528bd3d29d3be35p-2), C_(-0x1.a1a5a33d385d5b812d62c1b7093dp-4), C_(-0x1.4244fca3bc21ee34636ecc1caae2p-4), C_(0x1.e957c08844afbfd050ce104aed32p-5), C_(-0x1.7d54f6f8e016b5cc4d2c805b197dp-6), C_(0x1.293a2e4267dc81d9d07b602eb6d4p-6), C_(-0x1.5d4d2a8021a073e46a46ee79612ap-7), C_(0x1.5b4fecdba5f9087fe95645ea7106p-9), C_(-0x1.4ed9a3f4a9afdf2f3236e61b6585p-13), C_(-0x1.125ce08dc9b661014740f9362c84p-16), C_(0x1.5ea5b19f4ff29c7baf0a16f9257p-27), C_(0x1.e1d2c8108c65a8cd4dea742f8f7p-39), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 17, 16> { static inline constexpr std::array<Real, 34> value = { C_(0x0p+0), C_(0x1.78f50d65c4a16421841e3f303dcap-7), C_(-0x1.10f9973c2a07331ce0ccac35cccap-3), C_(0x1.5ea80ed1cf2f2dbfa0cb00aff04ep-1), C_(-0x1.04e32caec7824fdd8feada2c4721p+1), C_(0x1.ec7b02aa786ba97d52a304f12e4cp+1), C_(-0x1.3174e658f8076784a24459fccb68p+2), C_(0x1.04af0495e940fb1a0934ba937e55p+2), C_(-0x1.82d9ce4c80e7862d53bd54f737b2p+1), C_(0x1.8562e843b948818ab7543f47aa8ep+1), C_(-0x1.b4475da1bb05033056b650854fbcp+1), C_(0x1.773ee8a0b6e167fb7d5268d067eep+1), C_(-0x1.162bbf2e87d9ef969863945cc2b1p+1), C_(0x1.ed70fea486a4beb02761cf722ef7p+0), C_(-0x1.be0a12832787fdeed784930ccda8p+0), C_(0x1.43344776804460a755e69c9fe27dp+0), C_(-0x1.bc193640146ea4b82ef2e3c4b7fp-1), C_(0x1.59f9f9e4cd9a370cf83a45b3405fp-1), C_(-0x1.ea19fd7c2dc2c5db9794b9225413p-2), C_(0x1.24cb1e377b41a9dc8bcbafd5c9e4p-2), C_(-0x1.6d1685ae383191436a11192a6015p-3), C_(0x1.ce23289a0d5c025951ea09936714p-4), C_(-0x1.e968908c7474ea2a6b493052c12ep-5), C_(0x1.e76f4d23e3441d0b7efbafd90df3p-6), C_(-0x1.fe92310a812ac1b17c748db28b34p-7), C_(0x1.bcfa77e534eadd2bde270b26117bp-8), C_(-0x1.3fde2d0e55306e2cc04a5e77181ep-9), C_(0x1.f9933c512b48e3277e9a4bb4b19p-11), C_(-0x1.3cde4b0a8d6703babab6ae1d57aep-12), C_(0x1.055b9666e46160de479c93713a7fp-15), C_(0x1.0afdd4a80e426c9edced22de6763p-18), C_(-0x1.63fa7644baff156719997e19782bp-27), C_(-0x1.e7410ee7b8c80f0f9fbb1245ff4ep-38), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 0> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.7a4ce6ed992bb75d1922e250e291p-16), C_(0x1.42671fb0211a4a998d3a999cbba9p-7), C_(0x1.e6ee175fb834c8b714c01feffa11p-3), C_(0x1.b4d3dfe055a7b7209d9d957ea39fp-1), C_(-0x1.7d14720c62ef81c88eaa2934ef9ap-6), C_(-0x1.5a94efaefa077beed21f8e31afdfp-3), C_(0x1.42098eebb591e34e8dce26f5d9fdp-3), C_(-0x1.9b7dc4f9c2feaee194cc6094b673p-4), C_(0x1.80316f876ff22302ae3ab2cc5942p-5), C_(-0x1.8b13757e5a833cb7be38d086bf07p-7), C_(-0x1.9d8e278701564e508cebafcfb133p-9), C_(0x1.712f899035a27c983c908b6bba6cp-8), C_(-0x1.bedf3b9dd86feef8439cad4de336p-9), C_(0x1.4240f2118e493251bd8d9317b7d6p-10), C_(-0x1.0ae82fa70b020b05eaa57d2e86fcp-12), C_(0x1.5d87bb3a4b0411a199a2bbfb04acp-16), C_(-0x1.2d8c3a0a0933f834dfa752e731e6p-19), C_(0x1.e1643353d40db172275bb94c8db6p-19), C_(-0x1.66348482b32035f68245e91af884p-20), C_(0x1.38acac17e89fa0162b376bbd5bb2p-24), C_(0x1.00e7697e09dd627363f277ae5d0cp-24), C_(-0x1.a8f56e9d3a1cc00e461420464983p-27), C_(0x1.5a1ead033f0e454a9963da3438ap-33), C_(0x1.d4875150adea6366dba9b099b588p-34), C_(-0x1.cfdee2a4d421dce1ed3eb1824eb8p-39), C_(0x1.4ba806282cb55638c336fa42513ap-41), C_(-0x1.af632a71f0a85982bb61e8403dafp-51), C_(-0x1.cbdd4d28c6b8d8568783fc7a0356p-51), C_(0x1.4fd0be68bf175912b8e24547698ep-58), C_(0x1.61ffe57672ff2f1dc3af03a0658ep-65), C_(-0x1.3c884f70db98bc38107c463d9e95p-75), C_(-0x1.1f64d25df3ed3d5bb564497f042ep-86), C_(0x1.2bba4936fe9662bf3eb13c8582ep-103), C_(-0x1.1d5cf90ee87504b468831a41ae1ep-131), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 1> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.a0c37edf06a8f94f636662aa39bbp-13), C_(0x1.5938ebddd04e210d6e15ff1b97b9p-5), C_(0x1.0ce81716630c1f9cc4b07a770d1bp-1), C_(0x1.bad54d5c0a788c931739cae743bdp-3), C_(-0x1.734c9748f4ffbe66b4e75bb9c043p+0), C_(0x1.219848201b9a0b52ec9b8191e5b3p+0), C_(-0x1.967b651c15d3ab20c349ccf7a27fp-1), C_(0x1.1dd39ecf7d040d13482ae3416703p-1), C_(-0x1.7d233595983848fd2834ed2571cap-2), C_(0x1.bdf22d0e95afab2bb96e5bd19c6ep-3), C_(-0x1.ab4ab3ea2f36e1559b9c3420ec0fp-4), C_(0x1.361512fce1c67582139ca256de4fp-5), C_(-0x1.2052c9fd431d75170cd08f266eddp-7), C_(0x1.0fc840c4a9b1a2b7d29c508eb6d3p-12), C_(0x1.b1c90fbb8951feffd58470c9d80ep-11), C_(-0x1.c8b87ff5be380a38841f22318f52p-12), C_(0x1.11b72f5c2ab5d1894363c9e7ab05p-13), C_(-0x1.44fd9e718f23deb8dfb254ca9d3p-16), C_(-0x1.801763632cca8db4265fd9b7171p-19), C_(0x1.09096d92c04b504ace3e77b64297p-19), C_(-0x1.d590c9308d6d05c5021dabbaa32ep-23), C_(-0x1.b408b4e946eb9006560416ae94d7p-25), C_(0x1.26b8d9536f33f7963391a473b488p-27), C_(0x1.21ba41de7becb667214c3ca5d913p-30), C_(-0x1.005e15e86c4a56785a4a0f6ab3f1p-33), C_(-0x1.892c108c5e8de79fc1617882908ap-39), C_(0x1.5e2035f63580023798eda85d3d21p-42), C_(-0x1.d491970f7aaa6c617e7d1c311941p-48), C_(0x1.4a6b89912129028f7fea4136c808p-54), C_(0x1.1d009ad5392248132b10cca6afdep-61), C_(-0x1.a642a25e87ebcab18086a8e2fc06p-69), C_(-0x1.2011c2ce8690d9b9656d986e92cap-81), C_(0x1.9134326520639372add0f822994cp-96), C_(-0x1.7df9e44e24a4ebd5669f58093dafp-123), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 2> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.971a55e8255470a8e87397205afep-10), C_(0x1.3dc2c8dbfcf7f93df2e2e770167bp-3), C_(0x1.7546e57d9721df0ee0f4226116f4p-1), C_(-0x1.037adcec0657ef37e8f5501826c9p+1), C_(0x1.f0f2562cfec47ba3b00526650a7ep-1), C_(0x1.6c9838c4962e4af4c25ecbc468b7p-1), C_(-0x1.ebd5569ff73f2f877d55e4233979p-1), C_(0x1.2915d00cfaaa51da1d3b22bc1b91p-1), C_(-0x1.e34c46891f72dbbcd8a73f99bdap-4), C_(-0x1.5072010dc85e733fe3c06054204ap-3), C_(0x1.c67306010de8b6a212c8a67869b3p-3), C_(-0x1.2fa98054a2107feaebe98c8fc435p-3), C_(0x1.df7d703c2f7ab707c9106e495b0fp-5), C_(-0x1.28e5dcad2172c449a1b7dca486f3p-7), C_(-0x1.dc7a4c4cd9eeea5517aa806e57aap-9), C_(0x1.18e996b43138b67ddc3e006638c8p-9), C_(0x1.b32438920580e922f03409f3eb6p-18), C_(-0x1.9037acbcd4ec345580dcc6ddb006p-12), C_(0x1.44eb457dfe491144a6a8147f8c33p-13), C_(-0x1.4321ac67acfe448897274bc08792p-16), C_(-0x1.256fe0790910edb7d614dee4ba24p-18), C_(0x1.ed8efd908e26dfcaf46a03ca2c55p-20), C_(-0x1.a7d2bd14ff6144489ccdef55f7a8p-23), C_(-0x1.037133591bd1adccd684e0184c22p-27), C_(0x1.ab74edd3244b37270e37d649fe31p-29), C_(-0x1.fc9e38e19ebe258bd42cc39b8e13p-33), C_(-0x1.8084026dbca3fcd810e0eb658b42p-38), C_(0x1.368fe30917847fa68db0002b6cdap-41), C_(-0x1.39e30293b3af2c4949eb4a178d03p-51), C_(-0x1.7503d7779e458b1aa1fdddbe3326p-54), C_(0x1.9a1da2379c2a990ef23e5cbc6726p-63), C_(-0x1.f1aa7d24b3e7cf365e425fbf9856p-78), C_(-0x1.86a335fc44a9a2d537ec8158da4p-89), C_(0x1.73ea8d0ad818e245ff5b1e9884a2p-115), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 3> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.5a9454bfce16a0a0dcc504f76122p-7), C_(0x1.dac670f07b3810cfe0200e30b836p-2), C_(-0x1.07048222529da273b75a32ca582bp-2), C_(-0x1.b83c3b0926524121fff53826cc44p+1), C_(0x1.0676df488b1e4ed89c22050c7595p+3), C_(-0x1.30179a3f4da7316ca0c075836a81p+3), C_(0x1.05ba4eee5e31141776d12d72864ep+3), C_(-0x1.93e9c8737c9db916fd3aeb9dbbf9p+2), C_(0x1.11f35b97811e1392b7e5f33b18aep+2), C_(-0x1.34f47b568e89b7ecbfe83a3f3f92p+1), C_(0x1.13cb0f11e8118c8728dc07b4f30bp+0), C_(-0x1.7b2819c00008e638df3a1f667154p-2), C_(0x1.a1284251ac1c9a380e6762cf2045p-4), C_(-0x1.9462a198000c99e2d17bdaf7509fp-6), C_(0x1.98cbd04cf38d0b88fffaebdc2248p-10), C_(0x1.790fb9b4e1079fdb7c7f3de53909p-8), C_(-0x1.44467a122c6f0b3c619500e4be8cp-8), C_(0x1.0c81b81e9a914d549de9a5603fbfp-9), C_(-0x1.cac8f340d2aa3b68b219b85c015dp-12), C_(0x1.056344fb5f7fa4c5bdacd495990dp-15), C_(0x1.64dbcddfce7247b526ac9d7e0ccfp-18), C_(-0x1.2ea8218d9b8d1d441244fcd4b66ap-19), C_(0x1.ae941e74a568c6bb01832f906cd8p-21), C_(-0x1.1e524786cf0a95db795410244806p-23), C_(-0x1.2a97ea36eab85a08d496990ea5bdp-28), C_(0x1.91a720541503c3aab6c0537ffaf4p-30), C_(0x1.61eee7e526616835f0ba9666b29dp-35), C_(-0x1.1df0b9e54c56a105bc6728365e2fp-39), C_(-0x1.1ea73fd30a1b3871739285e4eb76p-44), C_(0x1.4cef49ba65ab3a484daa2106a09ap-51), C_(-0x1.042acc29aba7ca20f707f59b4985p-59), C_(0x1.2f927288a9fec8bba637277e07b5p-69), C_(0x1.f64dd565d432a80474f09d7042aap-85), C_(-0x1.de3b2de560b19ad42b01407d7ddfp-110), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 4> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.f65b07bf823cec483c39d5a11448p-5), C_(0x1.f0133093951f37f6a896ead34da4p-1), C_(-0x1.2157210d40dee8bdad6501ac0ec8p+2), C_(0x1.a490d9720df43c568fadfc931407p+2), C_(-0x1.0a37608588b0a4119bc76eb25c95p+1), C_(-0x1.166e49a8a0cc2b9e02e97716b213p+2), C_(0x1.47f9043ce81ccaeaa7b51a7192a1p+2), C_(-0x1.51650f8f0f7bc2a8e1c87a310a8ep-2), C_(-0x1.4a651a2d2c8b208523a548f223d3p+2), C_(0x1.daa4090446b0d279d1861aa53d55p+2), C_(-0x1.69495e8e6d87591bdc3892f6f8c5p+2), C_(0x1.0277af1839af3f779c7cdb3ae641p+1), C_(0x1.6b6419616b5982af3302995b743bp-1), C_(-0x1.731cf8395e4c8d5b88b93305475p+0), C_(0x1.d1e75297f2041df9c5f5bf256a3fp-1), C_(-0x1.d49cf0590577e4f277c27934579cp-3), C_(-0x1.397f17a4194e8ce372c9e426fbfbp-4), C_(0x1.7a21cbbedfbe11f0454df42e4775p-4), C_(-0x1.2988578b3adabc4b1512d4cbb6abp-5), C_(0x1.545144ccc581a21bd7cff42729e5p-8), C_(0x1.55e4a8c66e27b9cd3f291e5b6a18p-10), C_(-0x1.82c0f91d9bec49c90d6f1f27054cp-11), C_(0x1.ed2cd19280a89d42a9f703b9d37bp-14), C_(0x1.348863e0c3862c00204ef5c290d7p-19), C_(-0x1.8699ea90dfea228ea4870e9888cp-19), C_(0x1.03f81160244ea4e0561d2796396ap-22), C_(0x1.2dc9e87ced5d3e0075d1b0f48214p-27), C_(-0x1.1e0055531ecb5b5480ebd74d3d13p-30), C_(0x1.78b61664e5a70e15a0ed3d839b88p-39), C_(0x1.5f0c636dbd6bd39cd760ce07545p-42), C_(-0x1.6221e33afdbcbb6531e539a33b88p-50), C_(0x1.408f5ecc984b11ae43df9f32fee6p-64), C_(0x1.516335d80f8b3330068bd15b7661p-74), C_(-0x1.4137e85d31ab926f05f01bb46b99p-98), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 5> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.2c1ae6823399b2cb435cac91e1b4p-2), C_(0x1.0b5a4c7a50e393bc298a68b8c54ep-1), C_(-0x1.35e6d8721b448f3219c2ebf797cep+3), C_(0x1.08056d18cc3abfd158e32f385712p+5), C_(-0x1.e443d0b2f1917ce5a7864d3c4f97p+5), C_(0x1.26e95184621c1fe6c94f3f1e55c1p+6), C_(-0x1.027c3db720fc8465d08ef051f852p+6), C_(0x1.39b53e5c17e2d98c6d1b6a224a1p+5), C_(-0x1.a2ef9908e5f3063d5f6a67d79ad9p+3), C_(0x1.3c1856843b6ed43e69173ec9beb8p+2), C_(-0x1.50a61279deff7a9f95e9f5b1ffb7p+4), C_(0x1.7005437ea7f364b02fc7783b2745p+5), C_(-0x1.c35972d3e17999223e53468e37e1p+5), C_(0x1.5e3ef093f504f098160dfa010bc8p+5), C_(-0x1.3ec57fe40125d739e37516f7731fp+4), C_(0x1.8f87e8ce4ea1f97c1a2b6959d92ap+0), C_(0x1.3ebb2932b57e51e04c3ab524c19ep+2), C_(-0x1.f8f19a2160e20da38a2d0ba656dp+1), C_(0x1.767cc5a51bc973a84dbcca58047p+0), C_(-0x1.713b4eb249abff794f2872f24925p-3), C_(-0x1.57f96e30ddfd9bc4477fb05a13ecp-4), C_(0x1.6d3c7dfd75dd9d13bacd85402f0bp-5), C_(-0x1.f364307632fcaaf5a516d1734575p-8), C_(-0x1.a69b8516a1ae027062e6cc44b942p-13), C_(0x1.037145bb4087ace803c84d0a5bfcp-12), C_(-0x1.900324bb445222e2af610e0c858ap-16), C_(-0x1.c39b875e9ce90c4599df73f3cdd4p-21), C_(0x1.257ca20db0247e060d265d852efcp-23), C_(-0x1.94fc591b19bf8737ee11ebdb8608p-31), C_(-0x1.f784210bc45a0c13791f8d01e395p-35), C_(0x1.45da31c732197dc55fbe5cc58e5bp-42), C_(0x1.7b22a9fcf8e8894af17ff0c96d4ap-59), C_(-0x1.36334f22553eac1c5efc112842bap-65), C_(0x1.27559bf46283fc4563833952d7p-88), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 6> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.1b6522cefc386094703a1b940b9dp+0), C_(-0x1.725aa82bb56b65b988671eeb4df9p+2), C_(0x1.352392d274a5a82ecc23fa33228p+3), C_(0x1.0932fb369d6b1d42ff49967acb97p-1), C_(-0x1.879358a7154c8fe38abb0596bfcdp+3), C_(-0x1.7bc58b3667a9b25269bdd7a77a0cp+5), C_(0x1.0b13ecb8bf57a95468d44bdf0495p+8), C_(-0x1.43c9c4bcfe445dc8af77035b453p+9), C_(0x1.09ee5b9c8e5bb60028ced9f338a6p+10), C_(-0x1.51e18f7652afc8e422b14381c4bfp+10), C_(0x1.644767119a752b7a9c6636fe4ba3p+10), C_(-0x1.410473daba291fd610d9e089e60fp+10), C_(0x1.da130c42857b78f659a1d6303558p+9), C_(-0x1.d701d7e9133134b0f0d4fc7eec0bp+8), C_(0x1.63319398a8ba44714204cc51ebfap+1), C_(0x1.103c991a3daa09809634b6cd54c5p+8), C_(-0x1.27d3d6e7fd9bf2dd1ee7850f5b25p+8), C_(0x1.603f483ab97e5aeba0d81de20a75p+7), C_(-0x1.d0777f2eb89143bc896dcbff61bbp+5), C_(0x1.7a787d5d8610ba8dcf36333d8bcdp+1), C_(0x1.ac67a9959292f75e5874c7846e26p+2), C_(-0x1.90d4180a252c7e521492da4437e6p+1), C_(0x1.1a35f9399ae687cbbdc99928434bp-1), C_(0x1.6227b50ba315edc16940995bb21fp-6), C_(-0x1.88f3b956f9d0dbc770c981dd72f9p-6), C_(0x1.6aef03d1b7b80f64bb2a7833284p-9), C_(0x1.578d75e3a79566687a46c7635dbbp-14), C_(-0x1.6111d8865be635c9573ceef437cap-16), C_(0x1.98ae70f64468f801509511411944p-23), C_(0x1.ab515975e313ab940af4e8bf9f79p-27), C_(-0x1.5c32c92bf479e396733399534bacp-34), C_(-0x1.1971da66bec91c38c535f903a2a2p-47), C_(0x1.4b1b6c4a0c92ce2876a63b480607p-56), C_(-0x1.3b3d574a043e2a3cf8b53ffa3decp-78), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 7> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.a2b24d1ab719647b6a1c2ecf4af6p+1), C_(-0x1.c90129239b0561ac21474fdc1f01p+4), C_(0x1.cf2e00eaf4f0becc29975ebbb3d9p+6), C_(-0x1.2500bd3d88164480cc94fa086f38p+8), C_(0x1.077598d26b6930fe825f3677222p+9), C_(-0x1.6c0faa52dddafef24a3684f97c95p+9), C_(0x1.958eaac6820db16ee26374b84f58p+9), C_(-0x1.6c2fe2006b51236b8cd28d2a3376p+9), C_(0x1.b8f7517f01d05ad3f5b4ba6c9b6ap+8), C_(0x1.7e41eac4aec61876fb9933392dbp+6), C_(-0x1.a414506f1749a1c58305f0247915p+9), C_(0x1.8a42dc8d7e0c984036d44acf6da6p+10), C_(-0x1.fc70c5eb750631dda8bffa5c2966p+10), C_(0x1.078cc6f7751312b231c8496978c6p+11), C_(-0x1.d83a569460166e52313ff6ef2cdap+10), C_(0x1.72db6cb15cd72ceb44fc1391ca0ap+10), C_(-0x1.e3a6df319fa13f73a1becc99c1bp+9), C_(0x1.cf3b0d7df18f715bb39783adf96p+8), C_(-0x1.d51428e901c215eaaca0f201c961p+6), C_(-0x1.8c56c880ec38034e39c62c6aaf1ep+4), C_(0x1.2c380f5e865da81b223aaf9ba893p+5), C_(-0x1.fc04081d34cedd56a87f6a93c47p+3), C_(0x1.67cfd3a1ef5c6955d5156ad19fdfp+1), C_(0x1.9005cc81fa0dd8ddcb0ef2e368cap-3), C_(-0x1.6895dc001e228c4142a65a949d43p-3), C_(0x1.82652a80425786d395286ed1c874p-6), C_(0x1.2598eb52540c582b66a388a574c1p-11), C_(-0x1.f931a276c55e6702f13f4d2ec79cp-13), C_(0x1.dc668b4d899394715f65f547aa0ap-19), C_(0x1.b4c7dcafec63006d515f9533babdp-23), C_(-0x1.cb6cdde84a22b96a4fc307092c49p-30), C_(-0x1.f612f7790e7dfcfca6facf9edd4ep-42), C_(0x1.b3f84ae9c3a1d3983026d3d5d97bp-51), C_(-0x1.9f1407c19decc2d85cd5ba63de83p-72), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 8> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.bc1bbb932352fd4db217186d8038p+2), C_(-0x1.230220e94a6286e3ccf38d4bfcecp+6), C_(0x1.65729881a37a4e3a6ec39c0b03abp+8), C_(-0x1.146d11d32151befbe03f1861632dp+10), C_(0x1.34072da363b055ea4712dedb9e79p+11), C_(-0x1.0fba655ec8815d599296c7fe289ep+12), C_(0x1.9a1825ab277513bb221db58c408ep+12), C_(-0x1.16b4a3d0368f557cee02ea790ba7p+13), C_(0x1.59fb20e260f209444967e6c1a575p+13), C_(-0x1.8532af6245b8a37a5135bb9a8819p+13), C_(0x1.8c055ef648911dec2e20ac309451p+13), C_(-0x1.70169a3124976f7442e92622f30cp+13), C_(0x1.392792bc1a4318354c951213b5c5p+13), C_(-0x1.de2062219056d64d01622237bf5fp+12), C_(0x1.3e37823e9c9169638076f5c7aaebp+12), C_(-0x1.691be2f943c22d4422f81e97a65cp+11), C_(0x1.4b463c6bd400a0ecfedd95ba90c8p+10), C_(-0x1.5fc2af1919c21f21f19a7f30b116p+8), C_(-0x1.286e81ad3c5d4bb15d987b1d1575p+7), C_(0x1.11137fdb9735b4b7b7c77d4bdac5p+8), C_(-0x1.749f8e79c1414c37972b9280e062p+7), C_(0x1.1b06a540d8bf3d4241ae8a187538p+6), C_(-0x1.7a50c04170b90bf05ecb290f9376p+3), C_(-0x1.b0c80bbf87c7b0afb3614e48c05fp+0), C_(0x1.32db8166dd6bf259ffbc02129a89p+0), C_(-0x1.78bbab19bbfce1e222ea4ae4ebc1p-3), C_(-0x1.6be3931ec3a0a0f934055bd32184p-9), C_(0x1.4cb7ceaf89c2bc6ca23b4de198adp-9), C_(-0x1.ec1ff5f12b99a0b5484713b8896bp-15), C_(-0x1.a28eed0313308b4187f5c957721p-19), C_(0x1.1e4d1afc4f213777086c26ac990fp-25), C_(0x1.2e933ece77f3d4f12785b9380e9bp-36), C_(-0x1.0ed8efcca12869c8a8420946c2ebp-45), C_(0x1.01deae8b7334485f38756e42ddc9p-65), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 9> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.7d01eebdfe55fe3c6ed21587d92p+3), C_(-0x1.0e862c8ffe7ff18db1f4d0d89561p+7), C_(0x1.6068e5f78cc3a3034b49cf000d6fp+9), C_(-0x1.16e27e80bfe67c3ab640b5e1ecffp+11), C_(0x1.2cfcdcdcf07c3d87dcfc36a512dcp+12), C_(-0x1.de03a2d7212d303b5f3943016437p+12), C_(0x1.2ec8864cafb19603887a2d7c4703p+13), C_(-0x1.500683f99e667a964beb950c1258p+13), C_(0x1.545745c7955e4cdf07cc034c2854p+13), C_(-0x1.1c5400071f26b6f894c12a7b3f37p+13), C_(0x1.16dc653d8bc1d4bb36f214e1e20ep+12), C_(0x1.b4ea0cf6f0a6b2f781cf9163eadfp+10), C_(-0x1.c11dd36c4cf2a48e63345a744925p+12), C_(0x1.5827531f986387c8025c0b0f20cep+13), C_(-0x1.a3de245f7e97f4c57daf304b115p+13), C_(0x1.b4f7e9a0a0606952656f83a68277p+13), C_(-0x1.7f2cf0b4269dd38dde070b5f0cdep+13), C_(0x1.2042e1dec45f6d9e8ecdde4d30dp+13), C_(-0x1.84b7d120c9be3df99a52bbd8cee1p+12), C_(0x1.d5a3bb45a7db2c787108d4dd50f2p+11), C_(-0x1.d06f9035faa5ec088975f115cd7bp+10), C_(0x1.3bba5a0006ab829f47d96569222bp+9), C_(-0x1.633fc40d33ceb334ddc3416213edp+6), C_(-0x1.ea064cf26a80ac52af3e7edffe55p+4), C_(0x1.228bc38d7faa44c44536bc6e393bp+4), C_(-0x1.94763b8fc894319c6a4baa13397fp+1), C_(-0x1.d3df039dfc19a1e9d51d6ee6175fp-6), C_(0x1.e63bf96d77b58d48a45fb3200bfap-5), C_(-0x1.0df6f4a0b44ee3edfa91f00b193cp-9), C_(-0x1.be80af96687fddc94510e8ade719p-14), C_(0x1.a12b7b548061ec0aec1afee7ffb3p-20), C_(0x1.557c0a8693ccc679320f886d948dp-30), C_(-0x1.894221980c4b8d10bc7e6d5fc51ap-39), C_(0x1.766b6e3ec237c8c757492e2c2a6dp-58), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 10> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.a6f23ff482d2a8534d18ee2bd813p+3), C_(-0x1.37e1afb1392b3f40934da3d558f9p+7), C_(0x1.a686ad1a0ba3db778ce2719b5c95p+9), C_(-0x1.5df1b3980b353a49181f807fa276p+11), C_(0x1.912604b6b386163aa8a2a506984fp+12), C_(-0x1.5b61ddc6ff3217136a743b9c2999p+13), C_(0x1.f0e6d62d6c3adad4499f950bdba8p+13), C_(-0x1.40b6eb16bb3fcf7bbb403448e39fp+14), C_(0x1.87585fcb881fe424c96217849318p+14), C_(-0x1.be1e3f54a4c6e6b54ffcceb8221cp+14), C_(0x1.d1d8cfaa9552876a45180bcc50b2p+14), C_(-0x1.c1cad55e0d499c75b90a8681896dp+14), C_(0x1.98c3ae43fe1125e958f8e677c61fp+14), C_(-0x1.5c4d34f8b772415210663ccb0834p+14), C_(0x1.12a3aa37e5fd016d4163c33ef0a7p+14), C_(-0x1.9192f9cf12c8db367f3be99603f7p+13), C_(0x1.1122d052f1ea79533169d18075p+13), C_(-0x1.52bae1043f73d75aa63286049566p+12), C_(0x1.74a03155cefa742f49e86547448dp+11), C_(-0x1.690be5335ae783047d5d4ded6084p+10), C_(0x1.2aa92592f4ea4eab6ee72559722bp+9), C_(-0x1.54e703590f43bf9834fd6d7c3fcp+7), C_(-0x1.eedaac4f8962ad088ff79f7ab717p-1), C_(0x1.de050411f7cd568eda13f525f9dap+4), C_(-0x1.c148def3b49e232ee4958ee9fafep+3), C_(0x1.48e1be6a547f7c0d38d3ce51af9ep+1), C_(0x1.ccb6f2024dedfd3e23939d6e234ep-6), C_(-0x1.0b11a0ddf9bc2847e1d232a914d5p-4), C_(0x1.f0397fd92ef550872efcf8d934adp-9), C_(0x1.723cd3b01432ce7970852fde484dp-13), C_(-0x1.ed12536f5e4f2788058dfbdeb7b6p-19), C_(-0x1.6aaf317d39b56dc9439410fb90dbp-28), C_(0x1.cd1c2594785771509fd29eba3b57p-37), C_(-0x1.b707d969a42cbded13543076b42ap-55), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 11> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.9908fd0ce831f3487747d933d748p+3), C_(-0x1.3339176496abd2cc6f353a6e44a9p+7), C_(0x1.a399ad0400dbc9e71dafc101d1e1p+9), C_(-0x1.58f242d223982437163d99490d3fp+11), C_(0x1.7f8e29aa27ab125f24d2fcb7cf63p+12), C_(-0x1.38a61979e2b2ffe2ae4d4e65fc71p+13), C_(0x1.9add7b4a18f5c8300bd029e80d54p+13), C_(-0x1.ebee387c8e0aff52186a751f53cp+13), C_(0x1.219cbd1b2da3c7319d2594cddc32p+14), C_(-0x1.42ee71a32a848700929a8801a23fp+14), C_(0x1.4162f4284633fd1e7fba1bb3800ep+14), C_(-0x1.21467722a3c16c9f70c4e0f39397p+14), C_(0x1.f02472a8506fd3b8d32f77a4b8f9p+13), C_(-0x1.93ab7dcd5a6c14300ddac86b8dd8p+13), C_(0x1.26e6ca1326b3694ec43b05b1dfbbp+13), C_(-0x1.7e6f2ae54bf7f66077fef1175708p+12), C_(0x1.ca344102205561eaf28babf9b0b3p+11), C_(-0x1.ec7742f47fb3351081ba5a5968b7p+10), C_(0x1.8bd38bdf87a320af9a5418399e9fp+9), C_(-0x1.1acd500cc5943718da806ac7add3p+7), C_(-0x1.4d85442446456020706cd509b723p+6), C_(0x1.ed6ad17e0f78e02531c1a4770ad4p+6), C_(-0x1.b2f4089457d680f06bec3c3dd972p+6), C_(0x1.0c247d783695e3ffa9b901ecf60ap+6), C_(-0x1.9870dc8b45a9f4e303e8e8311b4ap+4), C_(0x1.3338d9a06d3fea3c6e122fc7de4bp+2), C_(0x1.0f6ca08ad5fae6b6177d3fc4323bp-5), C_(-0x1.4c4e08354cb7e3eae0ddb198440bp-3), C_(0x1.02d3a75dac8a719338fdc8517e15p-6), C_(0x1.7509581763d127e7d21fa4b754b1p-11), C_(-0x1.54ad634865cc13ff2aa8e2b2e8acp-16), C_(-0x1.7ef1d65121e030d70caf398558e9p-25), C_(0x1.3b877c4e5003312e2d376dc10e97p-33), C_(-0x1.2c6f23660e48f98694d796524bep-50), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 12> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.e6c2b226df78c279af13845051c3p+2), C_(-0x1.70ef0a94afa59b1a6aa8d2343645p+6), C_(0x1.fe115e7fdadc37c38b9e6da89457p+8), C_(-0x1.ab2ae85d8df6a5fa4cff025ed87bp+10), C_(0x1.e9076c74293a400016225b9ca839p+11), C_(-0x1.9fe839f33e758faf5bf246ffdc45p+12), C_(0x1.1ec83a83d597d7f150637077f9b7p+13), C_(-0x1.61f4e1f4fcae3d0d387b625dac79p+13), C_(0x1.a34037eb93edf57ca17d98f692cbp+13), C_(-0x1.df1d808eb7eddd0fc723f047f0e5p+13), C_(0x1.03261bb811b49bff52d5b3bd37b4p+14), C_(-0x1.08bec2cb9bb5325c84c6514f4d44p+14), C_(0x1.000c9dcb7996cb96b43e3d1c9772p+14), C_(-0x1.d42c4b6e482399636855cedb1712p+13), C_(0x1.9723de14cea5319a6860bbd3c799p+13), C_(-0x1.524bda96018d06eb97a9a94efd6ep+13), C_(0x1.08b8d63f8a5e0d7a39871bf89f73p+13), C_(-0x1.81d1060b61d0d3635b17f5b8f829p+12), C_(0x1.09630f3565a574b943c123a6bfa7p+12), C_(-0x1.59ea297354d075bf6b0a4b01f032p+11), C_(0x1.9c7ad6ace2b3cf85fb948aa7bdadp+10), C_(-0x1.b6b7705751739945f7c51d7dcccap+9), C_(0x1.aa6416006d3250b9dcdb9d2790c7p+8), C_(-0x1.7918296878e4f9853df6b6e8ae14p+7), C_(0x1.07145b6ad1daf57366d1625ece2dp+6), C_(-0x1.a108d75d6bbaf0652587e828bd3cp+3), C_(-0x1.33d485acc9655e7d88b74e802f9ap-2), C_(0x1.6d6fa9e91b92d2703b87421f247fp-1), C_(-0x1.78a5b635c316b5bfc318d4eea976p-4), C_(-0x1.16db8d9d41de38829032ed627d9fp-8), C_(0x1.9ed47b91da7177bda1e64cfb1175p-13), C_(0x1.dd448a5da4eed2af5748439dae43p-22), C_(-0x1.7fd3f33da284c2945eb532e858d6p-29), C_(0x1.6d7f73f5c3ea6d60bfb8d1a80ec8p-45), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 13> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.34875227fc78d9ef7ed7ac6e0f82p+2), C_(-0x1.d5cd5efb52a9ef14597d82d198bcp+5), C_(0x1.425dfad9b492e41845300927f3b1p+8), C_(-0x1.06c373fbafad1222f880762e04cp+10), C_(0x1.1b9bff7a51e187819c73161a78e1p+11), C_(-0x1.b244eac1fdcf0d5091a23ea449c8p+11), C_(0x1.01eb21ca101f022e5418621b2a9ep+12), C_(-0x1.166548c799cafbaa88fc763d8453p+12), C_(0x1.3b0ce98f7e34e32ea84135429657p+12), C_(-0x1.6362301a242eb8214208b870faaep+12), C_(0x1.65cf7c0d7f65c5008737a94a5fecp+12), C_(-0x1.4657f8cbcbde3d6a4e18c8e9c05ep+12), C_(0x1.24d7d2cdfa25a491f828221544bcp+12), C_(-0x1.010708097c75d2ac6e322278d08bp+12), C_(0x1.9d020a4459bdeb50b979470ee77dp+11), C_(-0x1.328cca6ef194279a5bb009ec9338p+11), C_(0x1.bc083519c657836208142c7eb836p+10), C_(-0x1.32ef111f54699b431e478036157dp+10), C_(0x1.7f8dca23d339c29569ea2ec801c2p+9), C_(-0x1.bc6a63cb2fc0846b3e6af0f884aep+8), C_(0x1.f09b702a78c4dd679fe395dd6a7dp+7), C_(-0x1.f532dbb546bbac5b183e2380610fp+6), C_(0x1.ab4ca1b61588ca9a0a6f9d915ea4p+5), C_(-0x1.463ffdaa1065322e64ac74f7fa8bp+4), C_(0x1.ba4ea70cee33c38b2b0e8b20a1aap+2), C_(-0x1.3f8f23678b563eb9630f6042805dp+0), C_(-0x1.2f5a76f330866b9d02c88ed42bb5p-2), C_(0x1.a0cbd7080604e3774928fed42dbdp-3), C_(-0x1.d523cae4ae400c2fe3e190e83e84p-6), C_(-0x1.01b8eb0a5cba7579162a23c9063cp-10), C_(0x1.02b9e23339b17453717d89dae4c9p-13), C_(0x1.5122002fcd3e4c50886af50f4453p-22), C_(-0x1.dca9566b2b5e33d63760b950eda2p-29), C_(0x1.c5fa90ac19cdbed6fcc50c677b98p-44), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 14> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.095a4630f25b345edb3a033429c7p+1), C_(-0x1.94f72d60a79651db24efb743df14p+4), C_(0x1.14fec0291d4b12e7113f25067be9p+7), C_(-0x1.bdfa0b6be593ce00570a7078fca5p+8), C_(0x1.d39577b7cad7e7c4fd5dbf90540fp+9), C_(-0x1.5193ad0b12f066b4cb0fcaacb204p+10), C_(0x1.6a2e0f056a38f1473da552dd4715p+10), C_(-0x1.5d0e503eeaf62b38a4d6937112eep+10), C_(0x1.7e6b7f3178684900694f3b8c2fp+10), C_(-0x1.b6360679ec82c511bf5718cad79ep+10), C_(0x1.ac545c0c739641a55bb4e4c80843p+10), C_(-0x1.6be3874eef6d1f1a32235a12525ap+10), C_(0x1.3c4c888cb63122525397dd653d36p+10), C_(-0x1.1879194795ebee18032f4a91a091p+10), C_(0x1.b5e083426b3c7c6a910b26a6198p+9), C_(-0x1.2f24fbf8b03f0944b0e6672a17c2p+9), C_(0x1.aab5115e884dc181977e8353a09cp+8), C_(-0x1.2726385d133e4a16d05b604abddfp+8), C_(0x1.59979df7b75e1cc96562d4bd910ep+7), C_(-0x1.65732078d27eb27f5ae5d7879665p+6), C_(0x1.7f6def467b5364cd233fba2c5a55p+5), C_(-0x1.704ee2b3997739837bc58d39a41p+4), C_(0x1.cc5ee35ab0ad93a46168320cb92ep+2), C_(-0x1.50976171160a6dd2e2a17268b2e6p+0), C_(0x1.f9c57fa6a287f2b26fe11e313d13p-4), C_(0x1.64b2a171f9a1261fcd30fc5b5dfep-2), C_(-0x1.998a031a0009070455c3c0080416p-2), C_(0x1.57c1f71aec6cd09bc7ae16b02e75p-3), C_(-0x1.b25ef4a8ceef29ad7df2d5db7234p-6), C_(-0x1.808f4aaa023730c43464ce0d9251p-14), C_(0x1.d605bd7dd6adec51bc920eb90a32p-13), C_(0x1.cf1c89541af51d642527681f35ep-21), C_(-0x1.a94329286bdc4b6164f134153d1ap-27), C_(0x1.952b6e764f688e496b665b4266b4p-41), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 15> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(-0x1.42660039f1e4ab1b56c2e8881a0bp-6), C_(0x1.ec93c1aa5b61c9b6d5696151fd09p-3), C_(-0x1.97ce26c5b170acf42f1f93361a05p-2), C_(-0x1.c01099aa543ca8b825e143b557bap+2), C_(0x1.a4014de9af920ed4c9ee54928ddep+5), C_(-0x1.6ded0cd4cc8b853b87bfbfacc025p+7), C_(0x1.8330f0dbb0c28dbf63333311ac1cp+8), C_(-0x1.0c893c9ed1485a74e7f7800e12b6p+9), C_(0x1.052ccc4345d2df5a1dbe6873e028p+9), C_(-0x1.c1974852375e5b099cd67d213452p+8), C_(0x1.fad23c454fb6519a66669891ce2dp+8), C_(-0x1.3ffe274228465c9afdc401f4f0f8p+9), C_(0x1.4635deb84c3cfd97dc985dd6672ep+9), C_(-0x1.19ba20aba1a1dc6b33ade2534e4dp+9), C_(0x1.065bcb209e9694669a116f8f1c4fp+9), C_(-0x1.015ece212696c91b2da16a54aabdp+9), C_(0x1.b4c17c7bd409b3d1eb496cf9d8c2p+8), C_(-0x1.48c8d9ca7f31bb244453a83bed84p+8), C_(0x1.008b40131779b22fe5ee591e5e55p+8), C_(-0x1.8c6185bde00310328e2d72cc1f71p+7), C_(0x1.08d51505255015c001174081c04ap+7), C_(-0x1.42df3db9c4b2e8aa91c152e2460ep+6), C_(0x1.93d8682a099131f2e30d1893505ep+5), C_(-0x1.d72aacc75787dddd2538f4cd11fbp+4), C_(0x1.c56cd8c23773878de5456aa0988fp+3), C_(-0x1.8c843e6f10e078b693975b9ceec3p+2), C_(0x1.620f8bdb46e5d416d1abe187fae9p+1), C_(-0x1.ff5dc9ffbca3cebcb086bd21ec14p-1), C_(0x1.8dbab0e6a8c888a4a8c14551861fp-3), C_(-0x1.14fd9c124f34ee1806a1877b89eep-8), C_(-0x1.8d698e7a70351fb6b69fe98cc8ecp-9), C_(-0x1.922f4b831ef6074ab73b0f88d222p-17), C_(0x1.661caab697dee3a1ab3a4d24609cp-22), C_(-0x1.556f58a6ea91cacbb7f565948433p-35), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 16> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.a102d4874492bf0c3f8abe5216a6p-4), C_(-0x1.3ebe78df84aaa4d0f4b794efc132p+0), C_(0x1.b2f7cb5502cda6c9d230fef5216p+2), C_(-0x1.5ac9e38bb117840228ea5f0969e6p+4), C_(0x1.63176aeb8932d6ab994905b6314fp+5), C_(-0x1.e61b7010df60cf6c4ad892eedae1p+5), C_(0x1.d104bdea5563bcce7c191c344f3p+5), C_(-0x1.7544f2a572dd637b67c83e625741p+5), C_(0x1.737d5aa77938e2318fbe4c3f84c8p+5), C_(-0x1.b1c6edf1bb3236287638385af6a1p+5), C_(0x1.9f0c5397c3faff6479945ec39ad4p+5), C_(-0x1.47ca4941183ccf73d74cc2ab091p+5), C_(0x1.1a93e6543e9f9a411108443c7824p+5), C_(-0x1.0927206f45006f2abe266610514dp+5), C_(0x1.a5f0e2f2d0b54c0cfa24d939192fp+4), C_(-0x1.2685107a4162e669c1fad19a69ebp+4), C_(0x1.bed6d3109a358fcb040f5e21b4d9p+3), C_(-0x1.52c12c07acc0e1d274e7d45ba9eap+3), C_(0x1.af31110ee1468e10bf21a15fab4p+2), C_(-0x1.02817ffcabad7fa52399e99ae606p+2), C_(0x1.4e0f916d4e048c57bf43251d9d88p+1), C_(-0x1.864c4e83e1764ac0ffdca7b484d8p+0), C_(0x1.7f1784ea638898f647b4e2787299p-1), C_(-0x1.8002ee89d7986397c24beb3e4ecfp-2), C_(0x1.7fc7499a09e8924504cfc808759p-3), C_(-0x1.2bfa08732bf79ef113f03ba38535p-4), C_(0x1.957da4e89baa41a793f6e28cc5c7p-6), C_(-0x1.2aec3447b7aecf379d17702dfbdp-7), C_(0x1.266385cc4f62510ab31310cb9e9ep-9), C_(-0x1.c0dfb99a8ea4dbba7703d245fd43p-16), C_(-0x1.1928369b116813aa42a010ac694dp-14), C_(0x1.2017f1a37d1405b50e30d1eabd65p-23), C_(0x1.0f4800b7b0a184fe6ec7608004b6p-26), C_(-0x1.03044a0f4ec3952e4f859c26c44fp-38), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 17> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(0x1.302549bcac16a07480ffb58950a4p-7), C_(-0x1.d1147836a22995451a237d2c571ep-4), C_(0x1.3c7951e94efea5a01e74ac179967p-1), C_(-0x1.f485b5137068e2de7e3ecadb998fp+0), C_(0x1.f6c9c4f20d2e109d726d9bfa2ad3p+1), C_(-0x1.498acbc4ea72d090f910303e1e3ap+2), C_(0x1.1d9755d014c869b79bc13e22f12fp+2), C_(-0x1.81525835d067c01df39c6d7268f2p+1), C_(0x1.6d720e347fb567a21392b49f69f3p+1), C_(-0x1.cedd19de5af2aa41ce8142ac49bdp+1), C_(0x1.b162c910bfde4ce0f673ec48bfa7p+1), C_(-0x1.2aa74c52348af8100314f0e62e0cp+1), C_(0x1.eda3d295893e868bfb35ec305bap+0), C_(-0x1.f7973b0f803ffd6ba7bc4f61d739p+0), C_(0x1.87d55f9f1654529cea30f503f524p+0), C_(-0x1.e3314a61cec2d1ec8852d0dbcec9p-1), C_(0x1.712067be057e009feff90d8198e8p-1), C_(-0x1.2d407bbb0b0ac1aee45b8cabc16p-1), C_(0x1.675f9e5eb7de518e9bb7430a57b9p-2), C_(-0x1.80b747814ba27c7cd05f743f5455p-3), C_(0x1.0a57b0f3cb83001cac6acade9288p-3), C_(-0x1.43510035a93ea4e9f4f0330f9b63p-4), C_(0x1.135c54f340512b998224396e7637p-5), C_(-0x1.042576d3101c9bfa2c3ee11498b6p-6), C_(0x1.28e7b29b5a905feb407256a997a8p-7), C_(-0x1.b45ff7bd1d15c0d666ce33e8722ep-9), C_(0x1.b09f4241d30f170ec83bd736891ep-11), C_(-0x1.7633994bcb363452031c0be60442p-12), C_(0x1.d2f415013436fa877d9f51f3a238p-14), C_(0x1.16fcce8bf9664ffcec318d033322p-17), C_(-0x1.f9a19b157868fe73cb52d3d8f794p-18), C_(0x1.555633d592e52d8c981188aedf8fp-23), C_(0x1.285fe2167b5b4b96d89f7083a8a9p-28), C_(-0x1.1bc8f87104551fb246bfa29e3c89p-39), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 18> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(-0x1.bd717b66e1d1d4d95ba3c08947dep-7), C_(-0x1.7b9fec6bb92af42e63e2e68ecca4p+2), C_(-0x1.1ead339bb1d44833388375e63d59p+7), C_(-0x1.012ddbc7bca1e6101aa1cf5d564ep+9), C_(0x1.c0b75070f83e37ada47fd8928c98p+3), C_(0x1.981856bff215b594a8e4e5f86ceep+6), C_(-0x1.7b31c06193f3e30ac8e863c6933ap+6), C_(0x1.e4866be11fb72b736fa85f578e21p+5), C_(-0x1.c461c239dff802f2348bdb864e24p+4), C_(0x1.d13242d9fb5ff5fac5479a566994p+2), C_(0x1.e6f4964d6a03d0f8158833051b6ap+0), C_(-0x1.b2b5fa84ed5bf72597a5eb232c95p+1), C_(0x1.0717ba30154525a4b9eaa944e41bp+1), C_(-0x1.7b72f81fcc526babbc7b68ad5d28p-1), C_(0x1.3a477558616d24c10f1dd99cb0c4p-3), C_(-0x1.9b911968d13ed312bc38bac2906ep-7), C_(0x1.631172e87e301114011b41a3b8a9p-10), C_(-0x1.1b6a6db603dde402c2223c4221d9p-9), C_(0x1.a5c80b0f2bc5bc3b3f1a94740a37p-11), C_(-0x1.702b73dc1bf286d5e095a7a0f5a4p-15), C_(-0x1.2e802f058eb73ef9b14848780ecp-15), C_(0x1.f461fea3bf76e5a0a5716d49933cp-18), C_(-0x1.978d16c26c68ef616764f83e76c8p-24), C_(-0x1.13d7c474d8cbc6770c364afcd753p-24), C_(0x1.1119bb3fe775e15526c923b289ap-29), C_(-0x1.8685450351316156519237add64dp-32), C_(0x1.fbf3d2901e1ab81addb9d152dd5p-42), C_(0x1.0ebded1ffbce50e958913e490b72p-41), C_(-0x1.8b6af637a9f31fc837c7aa02d1f6p-49), C_(-0x1.a0d4564bf2dc66611a174850c89ep-56), C_(0x1.74b661da2714cde7309e3860bc9fp-66), C_(0x1.5266f1077241c8b5ce693870559cp-77), C_(-0x1.60eccf731ce43024be7a209a4a5bp-94), C_(0x1.5002d3b2c3d4cf65f716a68d3eb6p-122), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 18, 19> { static inline constexpr std::array<Real, 36> value = { C_(0x0p+0), C_(-0x1.640d0cbfb34edc433e3c265970b3p-9), C_(-0x1.26ee7a9fef2f3cd66764d9f94d81p-1), C_(-0x1.cb77692ba61f4bed97023b434896p+2), C_(-0x1.7a52a3b4a77c5125cf118ae57d54p+1), C_(0x1.3d35a908c4d29476ad845269a92ap+4), C_(-0x1.eed0bb7ffc728b5021c0dd7f402dp+3), C_(0x1.5b446269f85399ef36e0a8f67578p+3), C_(-0x1.e860777819bbb58c5f71ba40875fp+2), C_(0x1.459d6005ddfc2d02a2fae3fd310dp+2), C_(-0x1.7cfb6d6fe6c7981796f3f294d796p+1), C_(0x1.6d0ba0eba8694d88a64c51b8f50ap+0), C_(-0x1.08e91d938b8389f5f0add295cdbap-1), C_(0x1.eca4948d0008e456bc9d67c81d75p-4), C_(-0x1.d06140d0577a1b263782da58dca4p-9), C_(-0x1.7297d215124a9ff8bc7627391918p-7), C_(0x1.862feda5c0e0ac9dd17f1152635dp-8), C_(-0x1.d3aeeacfe9c70326d1fac582acbcp-10), C_(0x1.15a5ade831e782a4ffc3bed5638p-12), C_(0x1.48236580a81473ae0d83a7ad5bf2p-15), C_(-0x1.c4dab80b872dfd428813332137cfp-16), C_(0x1.912930341a4fc5b6071d5bc9b09dp-19), C_(0x1.74839b9d5596fba97dd8392c7187p-21), C_(-0x1.f7937244a115a288dc7cde59188p-24), C_(-0x1.ef0ac8df8a590687cfdcc193b466p-27), C_(0x1.b60aa56de0f8813393124e0f539p-30), C_(0x1.4fe56c11057c7023ec8d56f2bd84p-35), C_(-0x1.2b1ee6f947a6f09f111f53e08b99p-38), C_(0x1.904f2b23e480ded5004c9a3b2081p-44), C_(-0x1.1a491e89e84c1ae1ad605792ed65p-50), C_(-0x1.e6f7ea660d39c7595d9913c47738p-58), C_(0x1.68bf338efe90278676de17896db9p-65), C_(0x1.ec3578649519ba8d72287ee8e375p-78), C_(-0x1.56c2062f8a091fe577e9571b2417p-92), C_(0x1.4654c88c71974e3c9f8fffc52861p-119), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 0> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.07725ffa38e9ced625cca68f3f21p-17), C_(0x1.418c13eff61cbb3a021569097a1cp-8), C_(0x1.3da46890dd9a0a4b666b663d4aa3p-3), C_(0x1.8a2b52a78c79acb029ea1b6db9aep-1), C_(0x1.1a351fcd80743de548c177ab2074p-2), C_(-0x1.854589d6ffe0f67b9ae39f8edb13p-2), C_(0x1.33e4c9737dbaaadf26763db4b5edp-2), C_(-0x1.aa815b6478cdd9f02d23c8d18fc1p-3), C_(0x1.087553137f3f92720c562745af4bp-3), C_(-0x1.1923520d0b86878b50e3a0765033p-4), C_(0x1.dbed9e8e6653062b736fe52ecb07p-6), C_(-0x1.13641cf1379f43fd1c7ac3bc71edp-7), C_(0x1.6875bc1af4453d3d4e0df0f5de38p-11), C_(0x1.a947dfa3688a3625b782cb79509ep-11), C_(-0x1.10b3493092979555022e5756e4f3p-11), C_(0x1.739af28e775c2a9c036cd7599ca6p-13), C_(-0x1.6cbb4a08c7eb5fe8237038a25da7p-15), C_(0x1.409e1bccbf4ef7d2793c14591929p-17), C_(-0x1.b4b5ec00d923dbc2091331f8dd78p-20), C_(-0x1.0bf29985eecde4627b6e6f4c0369p-23), C_(0x1.687a642b13ec01d769b15bc617e8p-23), C_(-0x1.137b64e3f44e9fdac7b535f30d85p-25), C_(-0x1.07f0606fa0dbad766d3197dd19cap-30), C_(0x1.6c85ed37d47e7b904cfd46760581p-31), C_(0x1.e0b71b0ad278ef564d1fcf1e88a3p-37), C_(-0x1.c7f3c451c06bde44cba8b0964cfp-39), C_(-0x1.3e734cfafd184d570a4dc2808fb9p-41), C_(0x1.ce28f04bbb94c603b91b46462315p-47), C_(0x1.70c276923dc2ecdd8f158e1477f7p-51), C_(-0x1.79a58db93f5236bb350459a10815p-57), C_(0x1.1f5027aac86b8e122cbfaf41ee62p-65), C_(-0x1.02276c5db8038c6bd4e95867d18dp-70), C_(0x1.254876c157537023d202fc646803p-80), C_(0x1.01c1badc280ce855150f875fe27fp-94), C_(0x1.81b6379c836e2bab36a7efba81e2p-110), C_(0x1.fb9eccd49ecd0870d19d3ffd447bp-140), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 1> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.32ea938ba90f99b6837f5af63be2p-14), C_(0x1.6ee1bf4cf707539d522967930e11p-6), C_(0x1.8a481c471691e9b156b25ff57636p-2), C_(0x1.0b4f5688ae00b19fc1e160cf6648p-1), C_(-0x1.73e1ddbd7901f6383d61c3a6ac0ap+0), C_(0x1.8b33bb402f96e0732af30db90eddp-1), C_(-0x1.9843e3061a5286745b1a22aa34f6p-2), C_(0x1.174b1b1b9671399a52d81cce6671p-2), C_(-0x1.d5ed9bfb0b3eacb0609851530eccp-3), C_(0x1.7f42427dbd66dd7c640694ddac63p-3), C_(-0x1.08eb5567536606f98b744fb38775p-3), C_(0x1.23d363b3776e341b922f685a5527p-4), C_(-0x1.e3311fba2b6786652a72b9017675p-6), C_(0x1.080310d541cbc7b415408ff9e9e9p-7), C_(-0x1.70478a76493e219892885c932ab3p-11), C_(-0x1.0307c808a4ebd5856963c5fffbdfp-11), C_(0x1.dee4ac1d99b73074451ae5f38c64p-13), C_(-0x1.44f6514c266093c947d073730317p-16), C_(-0x1.25e4b5de72eb99eebb1451b05b03p-16), C_(0x1.d4b5838001d90ed04674ba339f05p-18), C_(-0x1.0c63aca94f3b9788d5e3d0094dfdp-21), C_(-0x1.5d75636e21f07d7a3381c79d0362p-22), C_(0x1.731d5b5d003f55b6876fe0044ec6p-24), C_(-0x1.0c3e8c9e77afbdab5c815392ec9ep-29), C_(-0x1.add3791299db3c378e9de40e8a64p-30), C_(0x1.06fadabb3956eae25696a4f51f5cp-33), C_(-0x1.f4ce18079dfc9d6929b9dda1612cp-42), C_(-0x1.2eaa27d46e548153d91731a7ee6ap-41), C_(0x1.ae2bef5a0c85a05297e337e77b0ep-46), C_(0x1.4bfd5bd67de9ea53ad7c40089635p-54), C_(-0x1.1d671c704d7fca69eb029e029b5ap-57), C_(0x1.67963d56e9a972fedce141425dcep-66), C_(0x1.0b2bba52e426ee86c81ee87efd7fp-74), C_(-0x1.835ebfbafcc994f81e5e17cf300cp-86), C_(0x1.613768a516da5440f559a8b7229fp-103), C_(0x1.d0dabcbcf214e8044829d82792bcp-132), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 2> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.3f4897070d099352fef182a2ce17p-11), C_(0x1.6d998ddc083bb79599bbcdbf5aabp-4), C_(0x1.5cdfd95e7a29056f595ea6b04d0dp-1), C_(-0x1.3be70bab67dce74ec0a3fcb7068ep+0), C_(-0x1.4f0043bcb070f4a9035f0cb75f46p-1), C_(0x1.3f5cc039811e2b5b41e1339ce191p+1), C_(-0x1.435a201615b7649bde7e6bdcd35cp+1), C_(0x1.edddf036bb21e05065a4314b8003p+0), C_(-0x1.339bd26a01d7b24e4a113345801p+0), C_(0x1.26ea186703fa7b37f25c2df1ffcep-1), C_(-0x1.62a5bd05e71620f3397b8b55cadcp-3), C_(-0x1.8157fc40373b6ffb1b755d5d64ddp-9), C_(0x1.2626bd14128e3600ff5e4ef117ddp-5), C_(-0x1.458d354bc66bca085d03a73908bp-6), C_(0x1.60eb38bd6dcb5883ff977a13b631p-8), C_(-0x1.11a579a9f60ad1f4e34335fca023p-10), C_(0x1.8521f25159bae6c124f05e0aa6c8p-11), C_(-0x1.2e7cbaa1495260ee131d264b7fcbp-11), C_(0x1.d8a73116e5170bf766c62902d5cp-13), C_(-0x1.14baffbb5f3fc7cd4417451de38p-15), C_(-0x1.bc76b1b7765f460d127ed8109cb6p-18), C_(0x1.eb62162b875e74ac3ffbad741aedp-19), C_(-0x1.3ec5df1234a119b292d63f400f4fp-21), C_(0x1.0bd69df0fe817c3f4ec6f9220c7ep-25), C_(0x1.06532665f4ae126a371a91623062p-28), C_(-0x1.7b6b83afc0194421f9c689981f49p-30), C_(0x1.34507b73be114caacaacc09786fp-33), C_(0x1.3e99e3a60acfbc311476e151da98p-38), C_(-0x1.f151e40b836b8f8aaa3a3f0ced37p-42), C_(-0x1.448cc1196d194e9baa30923d08e2p-48), C_(0x1.4daf1830022e2a424c8ef6ce75a5p-53), C_(0x1.5011fa2f598d1104ecc45344c209p-62), C_(-0x1.1686e4fad9ced67d1bdbae1c9b3ep-69), C_(0x1.abb8cbb5d77c3d36c138309edcb2p-81), C_(-0x1.70542f5b2fe975bfb256d1ce4e9dp-97), C_(-0x1.e4be478d2f9719b08915aab446a4p-125), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 3> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.2434b5ba872d4c5808e71daa73f5p-8), C_(0x1.3130229eb7f3605b9b1b32cb9f97p-2), C_(0x1.7ae2dc23075619726157a440b0afp-2), C_(-0x1.f582a7e51625005d91ec63b814bap+1), C_(0x1.c7d08bb60552f4ce0a1dfeaea923p+2), C_(-0x1.a91c524d7a54ea689caba9ffde39p+2), C_(0x1.3c2342a582cf3f8debfc54cc85bap+2), C_(-0x1.088cd569576045a61a631e97afdcp+2), C_(0x1.d4e6f8f27b14576971c750ec5e9dp+1), C_(-0x1.7aa23dd152a396e3b62ff372d1d7p+1), C_(0x1.fadab4daf8f0e58f8d9d4ef01e97p+0), C_(-0x1.060b8820123e5eec7823aad8401ap+0), C_(0x1.6f3fb0d6ae2f90f1c572f1f9292fp-2), C_(-0x1.4c612316c2d6c3d361a3a87a8a95p-5), C_(-0x1.59b5936f0b00d23a57c7cb83dad9p-5), C_(0x1.f1cd6ac34748038c9618e97f54p-6), C_(-0x1.0815866d993d299c542a96e619ccp-7), C_(-0x1.33db5d2125a2859a2040c40794b8p-10), C_(0x1.bff774c7101082d480ffa7afad95p-10), C_(-0x1.14d1461893fe65443154fdd9ff78p-11), C_(0x1.60ee19b0b8b54d2e9cf10559c07ap-18), C_(0x1.6481008914d42c490ce92b7a443fp-15), C_(-0x1.79e8548e2225181445126279e5f6p-17), C_(0x1.0000883a713c85d37bfa6cb29923p-21), C_(0x1.0c4a52b19c7af0f6d1d88d405597p-22), C_(-0x1.51bfdb03a0cb4dfbccfaca10a1e4p-25), C_(0x1.2786225b5270b4ae5ce680be0a2p-30), C_(0x1.c2f3c31efae819fc824fc14e81e3p-33), C_(-0x1.e485fc83c20815b41344b0b5ebd8p-37), C_(-0x1.cdcc8478ab5ca320820b2bdaf727p-45), C_(0x1.248fa48f37bd758b38759e29a62cp-47), C_(-0x1.a1ed7efc74f0ddd5c3e7a26568ffp-56), C_(-0x1.257d232551abd9af4093fdff2e93p-63), C_(0x1.8e8d04aa5ac8d2663570e52c4402p-74), C_(-0x1.858c6aec0ddcc911f17d17aad5aap-90), C_(-0x1.0055b5f34554e57cc908df0fdeeep-116), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 4> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.cd2d34d1a42efd10b55402f678dcp-6), C_(0x1.84d83b63c2112e72eadf0e175b4dp-1), C_(-0x1.432ceae809a5e8ac576108008797p+1), C_(0x1.a0b9d38c7e490414c924ee603181p-2), C_(0x1.201a5e729e35c6b52d82081c697bp+3), C_(-0x1.2e113a8c2f74baddd86814ec6249p+4), C_(0x1.552ffbcbb449884fbffcd2fa1903p+4), C_(-0x1.094e929e449b6445010b66a63f08p+4), C_(0x1.1c13ef7eb64aa8ee0175687d62ffp+3), C_(-0x1.1af822f546327a2297be45973a7cp+1), C_(-0x1.f9d0cabdd71723c2b35a33ecead9p-1), C_(0x1.ddb0b49cb2203c31df0981557b5ep-1), C_(0x1.5318acd89fe65697aea31294c66ep-2), C_(-0x1.05b05a50c615d71587f066c9936dp+0), C_(0x1.9b5ab34637cfef44bfcb0657ebfap-1), C_(-0x1.1af795bed4913c5d4d0b7000d26bp-2), C_(-0x1.67451e2d12b8b1dce42c001cf264p-5), C_(0x1.99b155175f94ba8d3e0e29b8e60fp-4), C_(-0x1.ac7539c2e19ebed9f65c60048ba4p-5), C_(0x1.9ca72681ec9ea8c760a8f2a71fa1p-7), C_(0x1.34e3b1c23cb7d1aa00f8b981d763p-12), C_(-0x1.3a24c6f5381cdc3e9a0a2312b097p-10), C_(0x1.74fb460ba6a3c91ca053d755b7f7p-12), C_(-0x1.0b246714f1ffd71ceaf84df8a2f5p-15), C_(-0x1.ab7c59cb5e81411290eca69a51b1p-18), C_(0x1.dce3bc922f58f0b17196be55c2d5p-20), C_(-0x1.67697e8054a806243dffdcb105dap-24), C_(-0x1.9e416c1fd1c36255ecb5eed76efap-27), C_(0x1.c8b3d9029a8aec4464e3c0dd17ap-31), C_(0x1.5aaa4a43abfd9a111ae71704a9d5p-37), C_(-0x1.7db632c817eaeaef04d7671c292bp-41), C_(0x1.4a05d041f84f4639b3fa535ffbc5p-49), C_(0x1.e2fed86a51ad69aa2e1cca0d7a0cp-57), C_(-0x1.05d887c98f164a6efe9f254828cp-66), C_(0x1.42293c268faac3b8f9cecd0a784ap-82), C_(0x1.a7fbcc57e37f33cf909b7d2d16cep-108), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 5> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.31422db2538bc0a87b308ffa37p-3), C_(0x1.0d01250c1b0d06e5278d6357ef2dp+0), C_(-0x1.24921c6c7a3e4123721458dbf25dp+3), C_(0x1.a227c85b5e55bd2048d352a6c652p+4), C_(-0x1.524f50b139534832a4ee54d2fbecp+5), C_(0x1.8738d759012c36786ab70fb57053p+5), C_(-0x1.9323ab467eb529dc5b829fba165cp+5), C_(0x1.a42ab8b3ebb45f1751552cca4226p+5), C_(-0x1.a0b31f7eb9e57c7f0585d5e9fdbfp+5), C_(0x1.5b572ee0532df8736514c7068d41p+5), C_(-0x1.a65ce687fe55f007c4de98a4572ep+4), C_(0x1.d054e5f3566481470f5818319872p+2), C_(0x1.85d591edfc333f35ee49a560ad36p+2), C_(-0x1.3b9b5d3160df5b3c5b4fd29535fbp+3), C_(0x1.a0e3e2fe3af5aba2fa3cba939e51p+2), C_(-0x1.908502149e3126842af43ab5d7e1p+0), C_(-0x1.2fdcf4d1b74c11a7c8107dbf5a5dp+0), C_(0x1.71a344c5df788a37bb879332042cp+0), C_(-0x1.6f12d9a04f61757636a67705762bp-1), C_(0x1.40e9a8c631ed8f09035310ff998dp-3), C_(0x1.90c3dfa395147a7cc0c7676c3758p-6), C_(-0x1.cb2bc9edd20ef799e67ea0b4ad99p-6), C_(0x1.07343e2ee6832b57b8208a80d9e7p-7), C_(-0x1.49dfe7960ed68e24de4768224cf5p-11), C_(-0x1.b6e121c3001f0f7bec5598cab095p-13), C_(0x1.dcc996a9ecb3b5336d9759d46957p-15), C_(-0x1.a1bb03b6243b89e05627bbe8512bp-19), C_(-0x1.d9f622b803a645f37f52496ad03cp-22), C_(0x1.6819f189a7727ca3a62602f289abp-25), C_(0x1.4ca2954f6ae1a2e73667b4619ccfp-32), C_(-0x1.8c19531172f0aea0af3e91e143eep-35), C_(0x1.ad03ab6b6c0809d426ec9d64de28p-43), C_(0x1.92a5c80316f77784726d71e5f027p-50), C_(-0x1.126aebf2a41485b2605ffe0d6f2ep-59), C_(0x1.0d7f8030d9d60a694722396ad34fp-74), C_(0x1.62acfe05bfb1201e774235c1d27p-99), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 6> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.46ba9611fdf6c7a94d4f6d15ae64p-1), C_(-0x1.dc52978d552a4c87a8e0e11df878p+0), C_(-0x1.7fa540f99cfd7a53d485cc23b3dep+2), C_(0x1.5732b50df53b69d2559ff51a1e33p+5), C_(-0x1.b13b1bdb27ab101c396869bcc29bp+6), C_(0x1.33606cf67b649ff0c53d5daba4dp+7), C_(-0x1.bdcf241a98962a37ffef026534aep+6), C_(-0x1.6f03a4675ff5f681cbcdceb803f8p+5), C_(0x1.1808882b291db1c41b8c116f1fb3p+8), C_(-0x1.010ae854c8f06138d8c0149f09bcp+9), C_(0x1.53a4857a4441ea9cc9b274ab65a3p+9), C_(-0x1.6d4a26d83e33b0ac3dc870af21b4p+9), C_(0x1.3d6709439125c739fb2f5edfb5dcp+9), C_(-0x1.889b5e2f1c327048a556374f7da9p+8), C_(0x1.56fd4f6d97a60ae9928f3958a702p+6), C_(0x1.25b06390e20f015674e98cf0aeb5p+7), C_(-0x1.b399a1a2f22fe6161cb0e96be4a5p+7), C_(0x1.3df78e7e4d04373329461fc9ed72p+7), C_(-0x1.0ef9b79a3621238b48ca4f0954ap+6), C_(0x1.5f7982d2ac2abd1246025f08fe6fp+3), C_(0x1.6a625e6b6bab463f678abfc9f5a5p+2), C_(-0x1.198f31ed8cd901a93c9ab1bea14ep+2), C_(0x1.42ee7fc8a94768a05abd24f4f6ap+0), C_(-0x1.7d60037bbed413784256ebd48bcdp-4), C_(-0x1.6ff7a83ef962302af99cd8276389p-5), C_(0x1.ab35b35a042defdd50e378dee2cap-7), C_(-0x1.b332608ca1a847238ace5779c464p-11), C_(-0x1.021ab5a3bdbb30ee9a537ffac573p-13), C_(0x1.eac528756b7f5130df18318c4f94p-17), C_(0x1.0199b85d253f43e310c32a3ee23dp-24), C_(-0x1.6d0abfd965fbe6911b0989caac0cp-26), C_(0x1.0c61bef3e22c19990cf4042ba7e3p-33), C_(0x1.18cf50a4e4b4950ef758930ceb31p-40), C_(-0x1.0160d4507e328ccba58abe0174c2p-49), C_(0x1.7a161fd10bdebbba470322602b5ap-64), C_(0x1.f195998251a554e483f0c71a4471p-88), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 7> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.13189db507a58f44b230b3686764p+1), C_(-0x1.0f9b17b47559884eb516506b8576p+4), C_(0x1.e2750024accc2d74cdaf0e777dc5p+5), C_(-0x1.02e9517352f36c7e1cca5cdce0a3p+7), C_(0x1.84ea97af0478dfed161742a65f15p+7), C_(-0x1.cff7ea5e103622b218e35556d5c2p+7), C_(0x1.edfef34bb4dfb6181091bc4e1283p+7), C_(-0x1.f2fc15391d50c27f5075a6c8ff4bp+7), C_(0x1.eb246356ef9387175566a1e68a13p+7), C_(-0x1.02127d08179f9b70803e08887c21p+8), C_(0x1.3a1511b48828ca00655918525c18p+8), C_(-0x1.91c784aea677bcaa4677a1d92317p+8), C_(0x1.d755a30a6335088a6e9e8a634a61p+8), C_(-0x1.e5c19797a7c13f73162c80019235p+8), C_(0x1.be09360d2ab5d983638f05e968dcp+8), C_(-0x1.6e80a047a6bc49df58dac888c978p+8), C_(0x1.017887f1e05fdfd52e459afc793dp+8), C_(-0x1.16d045efd9fc23449024f4ef5b58p+7), C_(0x1.6f3c5a5aa270e946f4f0452290a4p+5), C_(0x1.00a0bb4260f4b86b22991895dfc7p+1), C_(-0x1.883af181a30c4b69a77ebd74a0cbp+3), C_(0x1.ca6f2d6266dac24630d5b0215037p+2), C_(-0x1.fb9354caf7469830d8b743a50504p+0), C_(0x1.c12f9fd42c0cff788f8d8a32aaf7p-4), C_(0x1.aa753c061078e6073623671f0cd1p-4), C_(-0x1.0149c0900041728b5dc19008c617p-5), C_(0x1.320d29d0ed46573ee176d2f36b8fp-9), C_(0x1.7569a3a8ad03ea55263aad4cfdap-12), C_(-0x1.c7142f2922eef32fe70a3d54296ap-15), C_(0x1.46d93383351fcdeef5e2617523b5p-25), C_(0x1.cc32d5d77364e5410b711a5b1237p-24), C_(-0x1.c3faa80be31a417c7ff07c960f74p-31), C_(-0x1.0e8f2f249697649bebd486ba2aa7p-37), C_(0x1.4b97e65c483067d885e78eb825fep-46), C_(-0x1.6f0e0ef20e7a06716a4c8ec9fe8dp-60), C_(-0x1.e310d5b8794443399cdd6ece490ap-83), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 8> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.580f506a271ceefdaa3463cf1629p+2), C_(-0x1.bed7dad0110afcce89ace888e88p+5), C_(0x1.122d5f16217b3e9bf4e80fdc4373p+8), C_(-0x1.ad0c5fd2ce7d97e26b1b63596f32p+9), C_(0x1.ebebc475930229b2000cf737b101p+10), C_(-0x1.c66859645ad39ff322d2fa4b454fp+11), C_(0x1.6b3c821d2c08e770e244c3fd9b4bp+12), C_(-0x1.05ed443c98660df7e825b62de19bp+13), C_(0x1.57df9f8d58ae967ab62cd9351becp+13), C_(-0x1.988852a4ddb8e2394c97f0e9d30bp+13), C_(0x1.b718499032f53cfb6791a553f164p+13), C_(-0x1.ae72afe9b5f0ce3d7715181f289ep+13), C_(0x1.817de8fd200b93d2fe94ab51ad77p+13), C_(-0x1.361e5f944923d3d5bdf35a20adffp+13), C_(0x1.b3a6d661a5e954603cd076722514p+12), C_(-0x1.036ff3829f5795f4ec64398489e6p+12), C_(0x1.edf47d39280a4d357e9e1781a39bp+10), C_(-0x1.10c036b4236268a93be68873acb9p+9), C_(-0x1.e0b697df7f002ac2801a14873b67p+7), C_(0x1.ecbda44c262ae523cdc868e322e1p+8), C_(-0x1.86f5d451d505eacfde5511850305p+8), C_(0x1.725c7f3a52e97d8a78a3843dfb9p+7), C_(-0x1.79d4729613fc7b35a032158d6205p+5), C_(0x1.fd7ee793312f8451ca67d9e92df2p-4), C_(0x1.0d01b8a4bb031f49626bbfdc2049p+2), C_(-0x1.47046f0d13d0e9edd2f830615f89p+0), C_(0x1.be74b29e56e9e7adf67a6938c90ep-4), C_(0x1.1bce9460eaf5c685ea35565b6408p-6), C_(-0x1.bea1c36d184dadae8028a6e3e14cp-9), C_(0x1.c87dd9f0e82f03f1f618a5e508a2p-16), C_(0x1.3736896d21eebb32c780ad16f33cp-17), C_(-0x1.9904cb4c4529ea63892483302a3cp-24), C_(-0x1.1964fba513e306ae2fe86b385614p-30), C_(0x1.cd54969392f7573024faf58e36bap-39), C_(-0x1.819d1aa8bbcb7dddef905812ef85p-52), C_(-0x1.fb7d3a090e0ab46eb1271ed833e8p-74), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 9> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.58fd39e3ea473b9c365112f9e5fdp+3), C_(-0x1.f5c4ea84a3bdf86fabc78b7ccf87p+6), C_(0x1.5132162ce5f74a25ac418aed5521p+9), C_(-0x1.153d0f08884aedd476baf2e683p+11), C_(0x1.382a3e583a8c4c9f1dbe11c15209p+12), C_(-0x1.01662d4c3e81ce469818c40b609cp+13), C_(0x1.4a1e803e16c3f08b00f992a85132p+13), C_(-0x1.5e4f1aa3047031adaa3ecd9f7d51p+13), C_(0x1.34f8d319cfee8ccaed9905f9568p+13), C_(-0x1.5037b2099231e17c790011360049p+12), C_(-0x1.c9d4325924514ceeece20c92f62cp+11), C_(0x1.ef1c5da047ca3c6636907219e698p+13), C_(-0x1.aa6460f62329021cc0a31061d8ffp+14), C_(0x1.1493ffefe577c924223ee91be135p+15), C_(-0x1.378dc9a8516802b7004a42d17ea6p+15), C_(0x1.3a895ef60ca292cbe80e485690b9p+15), C_(-0x1.17d3cdfce981563e49f923d38dc9p+15), C_(0x1.b32449a2a05e7c53387e3b5eab6ap+14), C_(-0x1.2d005b15e1ac660d6c6de2ac5d77p+14), C_(0x1.762585d24bf9c7735f9898961b24p+13), C_(-0x1.8db35bc09fe7ee5140a582ff984ep+12), C_(0x1.3cd0860a0aa20c2aa3d4d395b99bp+11), C_(-0x1.0eb312cd7109b3879fc433d5f57fp+9), C_(-0x1.439f55d9eae59605a3c39a59b84ep+6), C_(0x1.991df190f9945d0054430f51818fp+6), C_(-0x1.f474551766f4044e9817e92e973ap+4), C_(0x1.7b90022b6c4e7f168ce752888458p+1), C_(0x1.1453a51ab81ae063df6727985433p-1), C_(-0x1.0919f3da668fc601223376349f5bp-3), C_(0x1.2552ca2a72c78998c9ac373b5bbap-9), C_(0x1.0101dfee94cc5d7f60f2678fd0b1p-11), C_(-0x1.dade9b27d08cb19be2ad380c75a3p-18), C_(-0x1.64097fcb6571f6185210c51bbb62p-24), C_(0x1.8e12f1e504e13d786036befb6f25p-32), C_(-0x1.eef1afe29fcfbef2c0d8edeb2fdcp-45), C_(-0x1.45af8bce131ab3ac470aded6347bp-65), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 10> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.c2e57f37dba380a65ba9eda2079fp+3), C_(-0x1.59749d8525f12d8082256adb88ffp+7), C_(0x1.ea6ee085cc96e0d288f3be1b9ca2p+9), C_(-0x1.adf6c2874bed2b3f69d173f96a2fp+11), C_(0x1.07b4a22de335b119555a005db318p+13), C_(-0x1.ecd88a76ee3d3e922a5c04819366p+13), C_(0x1.7c29acad357038f41eb8b1dd0fc7p+14), C_(-0x1.052f5556b7e4a1a5c20ecd7e819fp+15), C_(0x1.4e46d551ac93ec5e56006a3de757p+15), C_(-0x1.8e77607cfd5ad57c6663b8875b37p+15), C_(0x1.b47447ef6b859ed13ace58a9a34dp+15), C_(-0x1.b911fbfaa77dec0071a250cc5fefp+15), C_(0x1.a115d6e8e14d04bc0db0286009bep+15), C_(-0x1.7187cfaf46cf0d5ed6f0095f890ep+15), C_(0x1.2fababdbdc3283e40c1d376f77f3p+15), C_(-0x1.cda4daf54c9cb2382843dcbe14cfp+14), C_(0x1.457825a36823f87f660fc5987cb3p+14), C_(-0x1.a46083210b99766804825ce7a718p+13), C_(0x1.e3885ee2753248f8df804c157d1cp+12), C_(-0x1.e49914e71496a8b65b2bc3b0a8d4p+11), C_(0x1.9ae829c152385eae9b1515bb5c36p+10), C_(-0x1.f191137e3badeb9e76f5cbed8cb2p+8), C_(0x1.57e65b068678833f27ed3ab74f4ep+1), C_(0x1.c2acc962d2fde80f4a40165a5fbdp+6), C_(-0x1.1b8eff5b124df721e99f0cb0e796p+6), C_(0x1.4ce0864f5bc843b2b9783951ecc5p+4), C_(-0x1.f98632e1903923d8655f653df191p+0), C_(-0x1.f614ca355bef0d83e7011b692c6bp-2), C_(0x1.105d05e210a04ac76f5004e82666p-3), C_(-0x1.2c35027764d106ecb7dcc0f035dp-8), C_(-0x1.76591805b151661a98c804b730dcp-11), C_(0x1.f83620ba68c2e3da4e964c603cc5p-17), C_(0x1.9ec84cf651d46727ce1b0a44d7ddp-23), C_(-0x1.358d783409c13e2404896fd5c7b4p-30), C_(0x1.257546b697ae8fd8394b22b0ae34p-42), C_(0x1.8233dd1d0686d57c137ec852b5d4p-62), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 11> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.ffa03a8700da7856451e869e7b5ap+3), C_(-0x1.91f11c60f20f4732595d765621bcp+7), C_(0x1.215104c7061731e391acb03d0135p+10), C_(-0x1.fa2000694cbffa8b68970bca9b4p+11), C_(0x1.2ea66d9c1c1c36281a4f053280ccp+13), C_(-0x1.0bde0020ff0122097ceed958162p+14), C_(0x1.7dfdf722720076bd662023e14b8fp+14), C_(-0x1.e6475940e8f80e3e0662171bf565p+14), C_(0x1.28fcf546d66430029dc0ae7f105p+15), C_(-0x1.570d2d144c0d85d5f33951d38d4dp+15), C_(0x1.64b992f501b16328e29ed09c3ca4p+15), C_(-0x1.4cdaed031e2e4abf8359db4b1506p+15), C_(0x1.21c4ffd20e63be71df3b6f2245d3p+15), C_(-0x1.db78c1b913f3be9244561a56f2ecp+14), C_(0x1.5e8fb02e9949d4c003e971dc8526p+14), C_(-0x1.bcf51c1ea8f31b04ed5d992397bap+13), C_(0x1.e583e17ea627e00530e2cb6f7d39p+12), C_(-0x1.aa4e81eafaf451b39ea0ddb0f57dp+11), C_(0x1.13f68d6d4da0d49f20c5505cdf9ep+9), C_(0x1.eda1ba36110bbecc88ad9ef4ce8cp+9), C_(-0x1.4fff2b926b6f5e6bcd2243e80004p+10), C_(0x1.12bce3e2f7931f11964e6bc3f549p+10), C_(-0x1.798d627cec26e7e1e4f53d7bdf9cp+9), C_(0x1.be6fc86ae4a8be5d77f2a1e9d57ap+8), C_(-0x1.8da2d6faf8e64c22dc4a043ad99dp+7), C_(0x1.b66a3015a1c5a11c948aaaf7cab3p+5), C_(-0x1.44429d9883de74d9c60a876b939p+2), C_(-0x1.c3e96613b6e5e2ff71adfacfda7fp+0), C_(0x1.1e57998dcfcf5d1648e257f8bf08p-1), C_(-0x1.07010d4834a6e7a67ce43acbf39ep-5), C_(-0x1.1b562f148b2fb64632b98ead9b7fp-8), C_(0x1.0af79c08ad78f06aac625b45eb1p-13), C_(0x1.f410b3bf125a82ae4c43c7624696p-20), C_(-0x1.f9be8616ca648419dbd80373acd5p-27), C_(0x1.6a9909c4a9f6ffff9ad70512f7b1p-38), C_(0x1.dd2f7b7aa9b821698f859b9d1a0fp-57), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 12> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.87fb22468465fb8a2934ce18ef16p+3), C_(-0x1.37c2f683aab22684a06e79a101a1p+7), C_(0x1.c5fd83f053f82dac4273a062a48dp+9), C_(-0x1.91b1e12dd60d97ec2e841dbfca05p+11), C_(0x1.e68387666347c07a316a9422b5b6p+12), C_(-0x1.b4dea0e7d579f84f7fb9d6c16bdcp+13), C_(0x1.3c269a772fa78e428d8f5044caa8p+14), C_(-0x1.97127ecaa2ab1915c1aae5c6fc8ap+14), C_(0x1.f6af289cce31c29a6ec330fbe9e1p+14), C_(-0x1.2a9f5b6b03ca5e9eb10038f1c5dbp+15), C_(0x1.4b10d6718988db4dba24714139cp+15), C_(-0x1.55a31577d419bbaf6e8ea53fe90dp+15), C_(0x1.4e7cdd5d8ea4ad7bd94b50975a01p+15), C_(-0x1.38022f772c0b4cce7e7232f4f9bap+15), C_(0x1.12be31f5fe34faa8426124a140c7p+15), C_(-0x1.c90c6d590473b7e23f50a6ff90abp+14), C_(0x1.68be09036d4b8e25079ad44a0fb5p+14), C_(-0x1.0c494e2492cb7350d7dea324e249p+14), C_(0x1.758345ee1ea103707e4fc5726b69p+13), C_(-0x1.e8a773cd517e44f45076f71519e1p+12), C_(0x1.2a474ea41676fa5a62a310e63587p+12), C_(-0x1.4af3fdfe318309e19b15b68fb972p+11), C_(0x1.49733b39a1c4ced0b1d897e0d8ecp+10), C_(-0x1.28058c9d809d32e67b550b8feec3p+9), C_(0x1.c739fb1ca38e254565961e77d1e7p+7), C_(-0x1.e2ee0f774ec3af6a1c1e4f7b17bcp+5), C_(0x1.f584835c1818f23dc08b323d98e5p+1), C_(0x1.dc8e84cc4afe7a2fb01ea8367041p+1), C_(-0x1.3b3a53bf8d0a53e1e10973253b98p+0), C_(0x1.6a83343080c3f8b48adb7f8a0fc4p-4), C_(0x1.952a26a6e4af59b3b26bda8c8609p-7), C_(-0x1.3a135cf1b710ef334411ec10d2fbp-11), C_(-0x1.208be3b61fd62740fba383db12cep-17), C_(0x1.a71d1df2466b06f953d67915b744p-24), C_(-0x1.b3e65dd0b550b982df6eefc5304bp-35), C_(-0x1.1ed120d152ef3b5a4475e78c15bcp-52), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 13> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.14760cca8b142294e79588592265p+3), C_(-0x1.ba74df47bf664ee2547385285e06p+6), C_(0x1.41711dcfaf9ae33ef5a42497cdccp+9), C_(-0x1.1818fad342f755601ec00141bb62p+11), C_(0x1.474e1b48df1603e34e0f10a63ce8p+12), C_(-0x1.1344c928a46119ef7f83bdf82b73p+13), C_(0x1.69c3448f5e9dbd2d90d955490598p+13), C_(-0x1.a6933630b28804e5fdf1db79e516p+13), C_(0x1.f096b7533884b484daabf2ec386p+13), C_(-0x1.22623e57fb5283c6330ad006e50dp+14), C_(0x1.3656cdaada5ee9f54e572471e7bep+14), C_(-0x1.2c112510224741f5281f55b6dc46p+14), C_(0x1.1649895fe753bac480e018c717a8p+14), C_(-0x1.f7bb2384208e05c66ecac730535dp+13), C_(0x1.a9475e44156e584ae36a2ec50f02p+13), C_(-0x1.4a8f9de0fe8b863882cf98bdc30ep+13), C_(0x1.eb5875381b21b266553a6296e0a5p+12), C_(-0x1.5ee64eec32c02ba06ecbd76a11b3p+12), C_(0x1.ccfb4d9d3a3fd43202c9da414974p+11), C_(-0x1.14a05b02023d93595fe14dff5f3bp+11), C_(0x1.3a9e21e52de90e0e25e5cf4ca63ap+10), C_(-0x1.4bc3a327f05f291344a38f901a5dp+9), C_(0x1.2adbd2de6a50340d8168b2783c01p+8), C_(-0x1.c3c2f4b5f88c5c0995d28d11b9ebp+6), C_(0x1.277a18d79f69dcf96944e2b0fcf5p+5), C_(-0x1.c9f8833026d5a408341117a30a92p+2), C_(-0x1.454631d4223dc7af67ef97ef1099p+1), C_(0x1.43b3c20fabe24e5d14333b1e0567p+1), C_(-0x1.7e2b7474504f09a810523ca07f92p-1), C_(0x1.0161c2b653dc5e969cd9fbd6d0edp-4), C_(0x1.00c51140e71642a9a41e83368bbdp-7), C_(-0x1.ae184c969f7530c56af21ca3a7a8p-11), C_(-0x1.8063c15ef2a25ff6da3cad34895fp-17), C_(0x1.7fa9f24707d3d7cbfa3e9820acf4p-23), C_(-0x1.2feaa75fa15b4cbbde0dc3686d2fp-33), C_(-0x1.8febd0375e4aae5f4479ef67c215p-50), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 14> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.309432a44a63023cd5d74f5bea55p+2), C_(-0x1.e8f069c87232260c91fa71890f2bp+5), C_(0x1.620bb6edc4428b1d5f4fa47a5ecep+8), C_(-0x1.30502125aeb77e95c45d9e43464ep+10), C_(0x1.5897d2f743a96869b1eb0ca13247p+11), C_(-0x1.10925ae24fc57451105c29bc2a9fp+12), C_(0x1.4362e4b63cec61ff21aac6b7ac6ep+12), C_(-0x1.4ffdb02cc70cfe03dbf6d475eb63p+12), C_(0x1.779b03f599f05e82b08d8b48f758p+12), C_(-0x1.baec7e0e55169b8140b39b8fb14bp+12), C_(0x1.ce2ff743f88370d5e72cb7988fbcp+12), C_(-0x1.9f067ce3296956a187e74bb12383p+12), C_(0x1.6abb1f9cef609047d528387a0ad9p+12), C_(-0x1.4450d5aabf62ce501c4c7a9271c2p+12), C_(0x1.08545f0a3187b9a2ec020313e51cp+12), C_(-0x1.7530230cd216d38656dfbf0553dap+11), C_(0x1.f952b436845ca1bafccd87887abfp+10), C_(-0x1.560fff419d2c2e711437c4013006p+10), C_(0x1.8c0d255b6e233d3a2cb91681d042p+9), C_(-0x1.5fdc3ca1d5db02eaba158929c246p+8), C_(0x1.0a97d5984be8315545f375086dccp+7), C_(-0x1.20a6202d36bbe9ea7b9fe18176b3p+5), C_(-0x1.5a7221251a0809c48fe9776bbed6p+4), C_(0x1.3634e4081412bfd6aa7ecc784b01p+5), C_(-0x1.bdda94264568de640cdd8fb6a91bp+4), C_(0x1.feb750a84a74f7d5dc8417586506p+3), C_(-0x1.275477518f18aa31553de78414f5p+3), C_(0x1.12ebd04f5867e9e6cd551be469c5p+2), C_(-0x1.30dbe403419efe34f764090a038fp+0), C_(0x1.058d7af30daa34c20339630ccaeap-3), C_(0x1.916084cd62258d7ea8b1535e2f8ep-7), C_(-0x1.732dce99f11c6f7cee7cd02c7701p-9), C_(-0x1.7126e2b2a55e5160267b465d584cp-15), C_(0x1.f42158d77d0c28962287129d0e59p-21), C_(-0x1.357be619f66df06ada13476a9641p-30), C_(-0x1.9731ccc0ad49712bbc19913bd785p-46), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 15> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.8a04267542fb82b6511fb8f206b7p+0), C_(-0x1.3cbc4bff9713202eddfb257134f3p+4), C_(0x1.cbebed769f041b8ff3b3ff336b7ep+6), C_(-0x1.8d408dd48e3feaaa0b268282946dp+8), C_(0x1.c5c362fe59a680f0eae654ab9d42p+9), C_(-0x1.6befff94df0fd5c1ea5963474048p+10), C_(0x1.b68e6a376222daa009bfb53e28dp+10), C_(-0x1.c650372cae06a09b58e507b0871ep+10), C_(0x1.e7dc6f316434651b61d954241667p+10), C_(-0x1.15cc9fcbffbdadb34e9d61d6c6eep+11), C_(0x1.25d8da27ae488c61183e3d71ffbcp+11), C_(-0x1.17ff07a3bf7e3928a0bec71f80dbp+11), C_(0x1.0475b26522c32263166cf14a852ap+11), C_(-0x1.e40da504c90b0dabfc10981e655cp+10), C_(0x1.a69e253ca704fe5504b2c83ebfdbp+10), C_(-0x1.57f0b96fa56496dc384c41ebdb2p+10), C_(0x1.11486c0ca46dc3a169a7bf688364p+10), C_(-0x1.a51b5b4b4c6e9a002c8fc1ca65e6p+9), C_(0x1.2ee508d88648873a9042e781e817p+9), C_(-0x1.9d3a15a142fb235220e5bdb7d581p+8), C_(0x1.11494e2078a35defd9005276ea95p+8), C_(-0x1.535f44eb08e29df270d509de713ep+7), C_(0x1.85e2cd196a74cb82559b30f986fdp+6), C_(-0x1.ab69cc5e56727978f17d430c4129p+5), C_(0x1.b6235da40abe2f3f3eea8ec9a5f6p+4), C_(-0x1.8b55be10b7592b154b2ba9939157p+3), C_(0x1.414892cca85f8967c379d65c1761p+2), C_(-0x1.e63611dc23584e97ec62af26dc25p+0), C_(0x1.1a15ef6adced3df1c6c0346a88a1p-1), C_(-0x1.2481c80f712590a8d277e551e4fep-4), C_(-0x1.27449cc156e8a2b8d79cdb23e19p-7), C_(0x1.70c363a504f9eefb5323d6beb984p-9), C_(0x1.513124306018b235441513a840aep-15), C_(-0x1.d1034c80bf12bfc41e39ee6d8817p-20), C_(0x1.59ccd407dcf5a9a7533ca67a560ap-29), C_(0x1.c6dbd9b6fbf34f8784ef66a9534dp-44), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 16> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.d374dc0cd29f78efae1c669adc98p-2), C_(-0x1.780e4d99b7126e1227fd8360470ep+2), C_(0x1.0fdf2439cdc107efd9b577f835a9p+5), C_(-0x1.cfa32c44d17cf6112a211a2ca37ap+6), C_(0x1.015959857901578ca504dacee669p+8), C_(-0x1.85bc6b66153fd1bea9b65cf18fa7p+8), C_(0x1.a64f0f8c995c4aa45de473272a55p+8), C_(-0x1.7a602a5238af6059f76503b7cbc2p+8), C_(0x1.7b654c5560678179ce7606e1eb5bp+8), C_(-0x1.bd48fca1124c5a0c87121a03cfe7p+8), C_(0x1.d04b583d66c5fddbbacf446d9c66p+8), C_(-0x1.9412225fd51522e93e47c93309f2p+8), C_(0x1.601c278c16ffe3bedc9949af1437p+8), C_(-0x1.4b7186886095601966984faa4acep+8), C_(0x1.1d01310f5613f772298e42534595p+8), C_(-0x1.abfcdefee09bf658999362e6f111p+7), C_(0x1.4372d34e52fa3409ae85e2a019f6p+7), C_(-0x1.f6105d4c84d999290c211733ed04p+6), C_(0x1.5bb1d872e66637325ce8f6aa17bep+6), C_(-0x1.b1d6456c69f76f04df5acebda739p+5), C_(0x1.15b4bfdbddb01fb2de3e2b574816p+5), C_(-0x1.5951b3080c61b9307228b6ed10bbp+4), C_(0x1.701e202aeccdeb8ac6b6b30bcacp+3), C_(-0x1.6ddc190753e3ea59a0862615508fp+2), C_(0x1.77099e572e056d9b063dba7e75c1p+1), C_(-0x1.4e4b25c08b76d3f35349a1737dcep+0), C_(0x1.d2b148f357c3f676caf89142fe6p-2), C_(-0x1.3f20a405e77bfa5f0c7b2c20c976p-3), C_(0x1.9e7b676edad2c438a7201cee715ap-5), C_(-0x1.868422c7d3c55e44cf7999291ce8p-8), C_(-0x1.3d8b8d9fd24906b21fe8a1d895d9p-9), C_(0x1.57f23d8a911c1c88e9f1808c31e4p-11), C_(0x1.21e64d9dcd8227de3458f3f5849cp-21), C_(-0x1.d450acc53a98cfc94056c9e9eb52p-21), C_(0x1.0524ac3af31035a21da55dd5b9a3p-30), C_(0x1.575344ae7025305d708f9f17a1a5p-44), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 17> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.515b9f63b7c8d9d621c7f83ca202p-4), C_(-0x1.0f7f6c187ccac0b76cfe6b463e44p+0), C_(0x1.8785c563b5897121211402b9f302p+2), C_(-0x1.4b2e4e52216e5851aba80d8bd8e5p+4), C_(0x1.6901cf579638d44ea9478cea01d4p+5), C_(-0x1.06c8002a381179d9f60f753114a4p+6), C_(0x1.05cb21187a0f4fd8449a14491a1ap+6), C_(-0x1.95b91f5bb17e01dfb0ac91c796f8p+5), C_(0x1.7701942190fc5f1e86a9d43b21f6p+5), C_(-0x1.ce78be0577929e543cbd7ad9b93bp+5), C_(0x1.e29898694f90f26ba0d29f50470ep+5), C_(-0x1.7f1203e71656f1392c2a5ed0ed1cp+5), C_(0x1.37e1b965edd32923f1ca90e8ee72p+5), C_(-0x1.32c7bde1b71fddd33a7396827f1p+5), C_(0x1.098dc7ef6c8b51bc5142e5eef069p+5), C_(-0x1.6fe8868cdb497ea274e3965b3923p+4), C_(0x1.0ba553b7e7f0be8b204b950f5ae8p+4), C_(-0x1.b42a38b735b9796714d4ff017726p+3), C_(0x1.28e9aca928974002f9a9710ee004p+3), C_(-0x1.515c3566a134d8fdf2749b107958p+2), C_(0x1.ae8a4319bcbc3c0849df1986eff8p+1), C_(-0x1.1895df589aea592ca4ae051262ecp+1), C_(0x1.14e9f7c12e2c99db92b3020068dbp+0), C_(-0x1.e47340b006f8809308435a5f94dcp-2), C_(0x1.062f736c8ea4db2c5025ce71eba6p-2), C_(-0x1.d4b69f6b077f6afaf5cbb7fe058ap-4), C_(0x1.ca34188e3ee232929967c28b9b2ep-6), C_(-0x1.abe68c068be60d047f0a28b8716cp-8), C_(0x1.690421cc61ef435baf7b95f1a52fp-9), C_(0x1.f0697cb773e3bc4b5c6f8da3552ap-12), C_(-0x1.c195d60892125cb3e4fc26494359p-11), C_(0x1.b812af9cf3a26f639a51b597d61cp-13), C_(-0x1.bff07c97b4e02892210bbfe57ccbp-18), C_(-0x1.52d29bd9004df24ead72772a383cp-21), C_(0x1.032cd4ca8cfc03e1481665c670fdp-32), C_(0x1.5462cc3c1b3950e1af932e243f9p-45), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 18> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.19f56cbab95bde76557badc0f4cfp-7), C_(-0x1.c5e9cb7948f398db235b0497713cp-4), C_(0x1.45ccfe221c0e9033121b0a870c75p-1), C_(-0x1.0ff7774a4970235ef0d0d38e3503p+1), C_(0x1.1f8e85bb37dc52e0187eb7a98e68p+2), C_(-0x1.86af502d50caccee8ec1f8fa9cd2p+2), C_(0x1.49bbefd2893ed26dde1f31860f1dp+2), C_(-0x1.6128a473d02d185e7eb02c402e5cp+1), C_(0x1.1828041581a25f5b721c3a717f02p+1), C_(-0x1.d956f1ddda30a36e6132710c578ep+1), C_(0x1.038faca047549c3c3be2f3f91741p+2), C_(-0x1.37b3d4e72913f8a1a7f18bf41b92p+1), C_(0x1.7db8d08d07a3f9e23e72ca58a9d9p+0), C_(-0x1.e8024d590281c76397a14742f7fep+0), C_(0x1.bf0c126480d51b8dd799512a9bbcp+0), C_(-0x1.9a6e24093a6a2e038548f4c21568p-1), C_(0x1.9d414904073073148ca53b09a0c9p-2), C_(-0x1.e94579a438133caa5c0cf94e7bc6p-2), C_(0x1.1fe0d75be11ddab72d3817a1df79p-2), C_(-0x1.138ce466125db8659c96547c9ecbp-7), C_(-0x1.34e623ad04da0418dc894a3d03adp-6), C_(-0x1.2fc16b0c0aa7163aea99c6e062f3p-7), C_(-0x1.d550ce5b878423f79b2e760869bbp-6), C_(0x1.5e3d43af8f8ddb8e3580c4af5d78p-5), C_(-0x1.6c1ef7a1d6e428cbd81df1c34e95p-6), C_(0x1.8a7d31fc8c9aee233f27827d8acap-7), C_(-0x1.52a8406945db2a229ecbdd1d3a6ap-7), C_(0x1.7e48f37194da1a9d35aebc50ef39p-8), C_(-0x1.157ac29aabe3ff0003550e84e639p-9), C_(0x1.ef1519ddac57eba8aaaa2d2f321ap-11), C_(-0x1.e9152bfae3a36641e3acf97f58dcp-12), C_(0x1.063d5981a1db32ae377107926c9ep-13), C_(-0x1.4c0cd023430cc482bd47183c0ef3p-17), C_(-0x1.c41b70da67df95d6233d9591b513p-21), C_(0x1.5615a8cb6d771ab8987aa2645074p-32), C_(0x1.c05941495cbd662c96cb64517d6bp-44), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 19> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.94435eb9af5275885daf683a6d65p-11), C_(0x1.ed6b4c3fc563bad856b7ff679708p-2), C_(0x1.e76d4a24be344d37fa2f63572709p+3), C_(0x1.2e6decff81b60c629b716c5e755bp+6), C_(0x1.b10d4368bfb547486b053181a892p+4), C_(-0x1.2aabf32fc96a83423162d2c816bp+5), C_(0x1.d877b83c63dd7e5e4cc6dd5d03a9p+4), C_(-0x1.473d4fc793666a7b844c67c60bd8p+4), C_(0x1.95d0bb71b70ebdc02338882a3446p+3), C_(-0x1.af691b75d1d83460cd380f784013p+2), C_(0x1.6d28dba75e1d6e3724ad5b4f87b2p+1), C_(-0x1.a6978440cef93cfbe48ab54a4883p-1), C_(0x1.1490ceb2bb752e576e6ab7934d2ap-4), C_(0x1.464cc9ffc1d7e4d14789afa29b8ap-4), C_(-0x1.a276802b30603b547d67dda28d1bp-5), C_(0x1.1d1df4fc9211fc9b42905d09c47cp-6), C_(-0x1.17d7d8739a0a8aedb047da85632p-8), C_(0x1.ebfe215a7af3d07469844cb63084p-11), C_(-0x1.4f11d28267254fe725cc5344d47fp-13), C_(-0x1.9b2b7a111f74e448315a5acfceb6p-17), C_(0x1.14946151574b1cc87019ae7e5eccp-16), C_(-0x1.a6bb3de2d570ec48084b02756991p-19), C_(-0x1.9504b8c2dfd49dc16ebf73767df5p-24), C_(0x1.17aee7159c99baf232751bad6bd3p-24), C_(0x1.70d51f32a8d959f82b22d341a23bp-30), C_(-0x1.5dd5370ddab610b2e6c646e6c17ap-32), C_(-0x1.e8aac4dbdf4eaec509688435a8c6p-35), C_(0x1.629884f91c42aa4b289bcdb69f39p-40), C_(0x1.1aef0590aab3b2864498ba08a331p-44), C_(-0x1.21c09a3e7d5c59641f5b485f413p-50), C_(0x1.b8e2ec16d3130e2d4b06e1e5ebcbp-59), C_(-0x1.8c242cff56d3b852843f6ec978c7p-64), C_(0x1.c20c22cff32a0ee7b7221886273ep-74), C_(0x1.8b88202dbba79e3009dedbb574b1p-88), C_(0x1.27f0bb4febe21c10d76a7232b49p-103), C_(0x1.8579c244925048746514bf00ae6dp-133), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 20> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(0x1.263966f58e93453d04e057c51a09p-10), C_(0x1.5fb5bf5017c5cbd580114bdb418p-2), C_(0x1.79fa0aa0ef4b1e94ae76e603830ep+2), C_(0x1.0041752577d99920a4756cf6dfd7p+3), C_(-0x1.6480edc11cb316f0df4191286f79p+4), C_(0x1.7adbeb32519375a4e9fc4f3e6a5bp+3), C_(-0x1.8761c804c949b74e319f96893292p+2), C_(0x1.0bbe5cbfe7fbfd16a26214b40cadp+2), C_(-0x1.c27eb52ff5df81afb96ebb8be06ap+1), C_(0x1.6f68e268413d3fe6c3a7fa4919f1p+1), C_(-0x1.fbed86051f0cd090a4eeebb3ba08p+0), C_(0x1.17c1f8ccfdcb9142f7ebab0435b5p+0), C_(-0x1.cf35ce446b0dbe81a8d8224d1015p-2), C_(0x1.fa3032b16e2ec1aa83ca0e93a41cp-4), C_(-0x1.610cbeafdcc29a63e60dfb78086p-7), C_(-0x1.f0a31aba969e59e1d540df6c7f09p-8), C_(0x1.cb16dc9a66c8c832a40588853909p-9), C_(-0x1.378619c057d6f59580d9bc18beb2p-12), C_(-0x1.19bd674de47d6898c22e3b3237e3p-12), C_(0x1.c15384b637285af63775101117b6p-14), C_(-0x1.014a5dd580db7e757daab9ece5fdp-17), C_(-0x1.4f01d7193ba039b859fe936d4da5p-18), C_(0x1.63c48bb9daaef9ef37d87bcf220dp-20), C_(-0x1.0126c6d159333aa9ffefda4ce01fp-25), C_(-0x1.9c0d1ce2d6bcfc30df35efd50ccbp-26), C_(0x1.f835a0a4ec39a7abf277183bd5afp-30), C_(-0x1.e018504ab9e4668fd6a8d7855266p-38), C_(-0x1.2225fdd38424d963076e1b05254fp-37), C_(0x1.9c61eaa91cf12e3330e482deddd5p-42), C_(0x1.3e42beb720a117dd0b00b4bdbe96p-50), C_(-0x1.1199b0b74da5aecc08a2c6ccc14ep-53), C_(0x1.58b777bfe6b1520fc88066b839f4p-62), C_(0x1.001f51ec932dbb77f103a0f7564dp-70), C_(-0x1.7359d9741f8ec2aef5430062a9fap-82), C_(0x1.529c13dec223da6d213dd3aa18cap-99), C_(0x1.bda18c791fe6b89a60c075d9dac4p-128), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 21> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(-0x1.55c2dd92705ef29dacf510df5483p-9), C_(-0x1.875690159b946a743f6031d71359p-2), C_(-0x1.756f6cbad17a45f8fdca57537e0cp+1), C_(0x1.522462f959194685a62f604b7373p+2), C_(0x1.6695cecd8b44feab641f1eb716abp+1), C_(-0x1.55d8721e5e38fe7f6b6b27b70f1ap+3), C_(0x1.5a1db9a981431612f8d678081919p+3), C_(-0x1.0851517441a6e04a55b896ef99e1p+3), C_(0x1.4943b00084361d8656967cf91ea6p+2), C_(-0x1.3bad2deaede17c67f605a6915c1cp+1), C_(0x1.7b9d5b96164de0ffe09e1ad56182p-1), C_(0x1.9c78d3c486af6a9c43cd6d82014p-7), C_(-0x1.3adc11c8d3555c352fd89192cfdp-3), C_(0x1.5c787302c6a0e308908c5a566d12p-4), C_(-0x1.79c3b00ffbead0016b7d6948b83ep-6), C_(0x1.24e941b9401cdd0859a58e2c60b8p-8), C_(-0x1.a08712ec476e0f62559aaf166efp-9), C_(0x1.43c84b18b6bc1f9bc1ed4ace622fp-9), C_(-0x1.f9ed905ce1a3fabec727edc27bfdp-11), C_(0x1.28365cf3c20b9931191c65e395d2p-13), C_(0x1.dbc105f0dcd19a43895a2c14219bp-16), C_(-0x1.06fd02a0249f9985362be2e45162p-16), C_(0x1.5536f1beba49f3fa62573cb3aefap-19), C_(-0x1.1eb1b927a2367b6b71d37a3a0b16p-23), C_(-0x1.18cae380af7c0fb2b2883f2e7a9fp-26), C_(0x1.962198a9fb4f49acf27837af9e55p-28), C_(-0x1.4a0510fbc2258e2b740de37281f1p-31), C_(-0x1.5507dda80e4462056671b4ed9443p-36), C_(0x1.0a2a68de8dc560166b8fa1f0e29fp-39), C_(0x1.5b65f0e23da86c0f9732111de5a7p-46), C_(-0x1.652ce69e3233a8cdb2d672d4de9fp-51), C_(-0x1.67baca3a273431e8c1471458d539p-60), C_(0x1.2a22a2a629f6431996d0904b53cbp-67), C_(-0x1.c9d5657e9bda6dc8a405fc5c3a28p-79), C_(0x1.8a4260a0edd15c6744f3a072dd9cp-95), C_(0x1.036f45f65701ff403784f02b67ep-122), C_(0x0p+0) }; };
template <typename Real> struct daubechies_scaling_integer_grid_imp <Real, 19, 22> { static inline constexpr std::array<Real, 38> value = { C_(0x0p+0), C_(-0x1.bda79ab9326966a8373d34c403d5p-10), C_(-0x1.d1744a8423410c1cdbc92198fb2ep-4), C_(-0x1.20ed565d8344f24b447d5dad1a94p-3), C_(0x1.7e6fc4bdbfcb347e60be376cd8fcp+0), C_(-0x1.5b972509d68a9e778b59e41da454p+1), C_(0x1.442d2d17d149d7c8adb7128a4a5bp+1), C_(-0x1.e2277288714cc11d45fb77f46c4ap+0), C_(0x1.9379ce87585cf7e36c9df60dab88p+0), C_(-0x1.659214144828aeeba2e8c5e20a63p+0), C_(0x1.20bc0fb1d00715f9a829adb0ea43p+0), C_(-0x1.8283009ebbc1eb153c3ab896b77bp-1), C_(0x1.8fa7bb64ff9ab76d61906bd796f1p-2), C_(-0x1.180d836c3d15c7dc5dd02b639eccp-3), C_(0x1.faecc9e70faee2855b6fee69691fp-7), C_(0x1.07a09dde4a2beade229071da7cf2p-6), C_(-0x1.7b9be86674bb70da10cbd67f2dafp-7), C_(0x1.92c3d8394a5b35c0ac95d90e2cd5p-9), C_(0x1.d5864f4fa99910c57caa7bc9e471p-12), C_(-0x1.559b12ec6e892a72794bb66e37cep-11), C_(0x1.a62f6a612f39a18acf0803b47144p-13), C_(-0x1.0d223ecdbfff75a7fa9690777531p-19), C_(-0x1.0fdbebf048174e10984f842f14c4p-16), C_(0x1.202e4a843fcd49b1f3f621772e1ep-18), C_(-0x1.86705793870af2933b93c10e211p-23), C_(-0x1.992e1c88b7cd13dae9b8e491d4dap-24), C_(0x1.018eb6827a7049feef27c5ca32bcp-26), C_(-0x1.c2b71846e1645f5cc0f2480787d5p-32), C_(-0x1.57e1e9171e5bc5fcf45eafbaecf7p-34), C_(0x1.717b9ae9f68e605d4d193d54fc52p-38), C_(0x1.6027613ce51bde3efec97caba709p-46), C_(-0x1.be324a2f0323f8a5380cb0e8a1d4p-49), C_(0x1.3eb2f19289730e4086f2e07ef711p-57), C_(0x1.bf9c807f8fa727e8ceb75bf12aacp-65), C_(-0x1.2fec3bd7b3920aea071e434958e4p-75), C_(0x1.290ed0b71d4a892449c6c2ced6f5p-91), C_(0x1.86f240571d54c453fa3cc6af74bep-118), C_(0x0p+0) }; };


template <typename Real, unsigned p, unsigned order>
constexpr inline std::array<Real, 2*p> daubechies_scaling_integer_grid()
{
    static_assert(sizeof(Real) <= 16, "Integer grids only computed up to 128 bits of precision.");
    static_assert(p <= 19, "Integer grids only implemented up to 19.");
    static_assert(p > 1, "Integer grids only implemented for p >= 2.");
    return daubechies_scaling_integer_grid_imp<Real, p, order>::value;
}

} // namespaces
#endif
