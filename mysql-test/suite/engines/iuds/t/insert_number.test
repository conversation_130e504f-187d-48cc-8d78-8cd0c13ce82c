--disable_warnings
DROP TABLE IF EXISTS t1,t2,t3,t4,t5;
--enable_warnings
SET sql_mode='NO_ENGINE_SUBSTITUTION';
######## Running INSERT tests for TINYINT ########

# Create tables
CREATE TABLE t1(c1 TINYINT UNSIGNED NOT NULL, c2 TINYINT NULL, c3 SMALLINT, c4 MEDIUMINT , c5 INT, c6 INTEGER, c7 BIGINT);
CREATE TABLE t2(c1 TINYINT UNSIGNED NOT NULL, c2 TINYINT NULL, c3 SMALLINT, c4 MEDIUMINT , c5 INT, c6 INTEGER, c7 BIGINT, PRIMARY KEY(c1,c6));
CREATE TABLE t3(c1 TINYINT UNSIGNED NOT NULL, c2 TINYINT NULL, c3 SMALLINT, c4 MEDIUMINT , c5 INT, c6 INTEGER, c7 BIGINT, index idx(c2,c7));

# Insert some rows with targeted values 
INSERT INTO t1 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);
INSERT INTO t2 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);
INSERT INTO t3 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);

# Insert duplicates for parts of the clustered key
INSERT INTO t2 VALUES(108,-109,110,111,112,101,114),(108,-109,110,111,112,102,114),(102,-109,110,111,112,113,114),(103,-109,110,111,112,113,114);
INSERT INTO t3 VALUES(101,-102,103,104,105,106,101),(101,-102,103,104,105,106,102),(101,-103,103,104,105,106,107),(101,-104,103,104,105,106,107);

# Insert permissible NULLs
INSERT INTO t1 VALUES(105,NULL,102,103,104,105,106);
INSERT INTO t2 VALUES(105,NULL,102,103,104,105,106);
INSERT INTO t3 VALUES(105,NULL,102,103,104,105,106);


# Insert empty string ''
INSERT INTO t1 VALUES('','',17,18,19,20,21);
INSERT INTO t2 VALUES('','',17,18,19,20,21);
INSERT INTO t3 VALUES('','',17,18,19,20,21);

# Insert negative value to unsigned integer, positive value to signed integer, zero values
INSERT INTO t1 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t2 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t3 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t1 VALUES(0,124,27,28,29,30,31);
INSERT INTO t2 VALUES(0,124,27,28,29,30,31);
INSERT INTO t3 VALUES(0,124,27,28,29,30,31);
INSERT INTO t1 VALUES(0,0,32,32,34,35,36);
INSERT INTO t2 VALUES(0,0,32,32,34,35,36);
INSERT INTO t3 VALUES(0,0,32,32,34,35,36);

# Insert non-numeric value to numeric column, would be converted to 0 or numeric value (if contains part)
INSERT INTO t1 VALUES('101.34 a','a',37,38,39,40,41);
INSERT INTO t2 VALUES('101.34 a','a',37,38,39,40,41);
INSERT INTO t3 VALUES('101.34 a','a',37,38,39,40,41);

--sorted_result
SELECT * FROM t1;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t1;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t1;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t2;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t2;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t3;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t3;

# Now select using various table access methods (full table scan, range scan, index scan etc.)
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 102;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 102;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 108;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 108;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;

# Using index for group-by
--sorted_result
SELECT c2 FROM t3 GROUP BY c2;
--sorted_result
SELECT DISTINCT c2 FROM t3;
--sorted_result
SELECT c2,MIN(c7) FROM t3 GROUP BY c2;

# Create tables with AUTO_INCREMENT columns and unique indexes
CREATE TABLE t4(c1 TINYINT SIGNED NOT NULL AUTO_INCREMENT, c2 TINYINT UNSIGNED NULL, c3 TINYINT UNSIGNED NOT NULL, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 INTEGER, c8 BIGINT, PRIMARY KEY(c1), UNIQUE INDEX idx(c2,c3));
CREATE TABLE t5(c1 TINYINT UNSIGNED NOT NULL AUTO_INCREMENT, c2 TINYINT SIGNED NOT NULL, c3 TINYINT SIGNED NOT NULL, c4 SMALLINT, c5 MEDIUMINT, c6 INT, c7 INTEGER, c8 BIGINT, PRIMARY KEY(c1,c2), UNIQUE INDEX(c3));

# Test with AUTO_INCREMENT columns
# Insert some rows
INSERT INTO t4 VALUES(-101,102,103,104,105,106,107,108),(-108,109,110,111,112,113,114,115);
INSERT INTO t5 VALUES(101,-102,-103,-104,105,106,107,108),(108,-109,-110,111,112,113,114,115);
# Insert 0 values, increments the value
INSERT INTO t4 VALUES(0,32,33,34,35,36,37,38);
--let $auto_value=`SELECT LAST_INSERT_ID()=1 AND c1=1 FROM t4 WHERE c2=32 AND c3=33;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on 0"
}
INSERT INTO t5 VALUES(0,-32,-32,33,34,35,36,37);
--let $auto_value=`SELECT LAST_INSERT_ID()=109 AND c1=109 FROM t5 WHERE c2=-32 AND c3=-32;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on 0"
}
# Insert NULL,  no error, increments the value
INSERT INTO t4(c1,c3,c4) VALUES(NULL,104,LAST_INSERT_ID());
--let $auto_value=`SELECT LAST_INSERT_ID()=2 AND c1=2 FROM t4 WHERE c3=104;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on NULL"
}
INSERT INTO t5(c1,c3,c4) VALUES(NULL,-104,LAST_INSERT_ID());
--let $auto_value=`SELECT LAST_INSERT_ID()=110 AND c1=110 FROM t5 WHERE c3=-104;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on NULL"
}
# Insert empty strings, increments the value 
INSERT INTO t4 VALUES('','',17,18,19,20,21,22);
--let $auto_value=`SELECT LAST_INSERT_ID()=3 AND c1=3 FROM t4 WHERE c2=0 AND c3=17;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on ''"
}
INSERT INTO t5 VALUES('','',-17,18,19,20,21,22);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=111 FROM t5 WHERE c2=0 AND c3=-17;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on ''"
}
# Insert strings with numeric part, inserts the numeric part of the string
INSERT INTO t4 VALUES('102.34 a','a','a',37,38,39,40,41);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=102 FROM t4 WHERE c2=0 AND c3=0;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on invalid strings with numeric part"
}
INSERT INTO t5 VALUES('102.34 a','a','a',37,38,39,40,41);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=102 FROM t5 WHERE c2=0 AND c3=0;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on invalid strings with numeric part"
}
# Insert negative values unsigned & positive value to signed
INSERT INTO t4 VALUES(4,7,8,9,10,11,12,13);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=4 FROM t4 WHERE c2=7 AND c3=8;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work on negative value"
}
INSERT INTO t5 VALUES(-1,-1,-1,8,9,10,11,12);
--let $auto_value=`SELECT LAST_INSERT_ID()=112 AND c1=112 FROM t5 WHERE c2=-1 AND c3=-1;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work for negative value on unsigned"
}
# Insert some rows with no values assigned to AUTO_INCREMENT columns, increments value
INSERT INTO t4(c2,c3,c4,c5,c6,c7,c8) VALUES(101,102,104,105,106,107,108),(108,109,111,112,113,114,115),(115,116,118,119,120,121,122),(122,123,125,126,127,128,128);
--let $auto_value=`SELECT LAST_INSERT_ID()=103 AND c1=103 FROM t4 WHERE c2=101 AND c3=102;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well"
}
INSERT INTO t5(c2,c3,c4,c5,c6,c7,c8) VALUES(-101,-102,104,105,106,107,108),(-108,-109,111,112,113,114,115),(-115,-116,118,119,120,121,122),(-122,-123,125,126,127,128,128);
--let $auto_value=`SELECT LAST_INSERT_ID()=113 AND c1=113 FROM t5 WHERE c2=-101 AND c3=-102;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well"
}

SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t4;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t5;
# Now select using various table access methods on auto-incremented pk value(full table scan, range scan, index scan etc.)
## Full table scan ##
--sorted_result
SELECT * FROM t4;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t4;
--sorted_result
SELECT * FROM t4 WHERE c4 = 104;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t4;

## Backward index scan, covering ##
SELECT c1 FROM t4 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t4;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t4;
--sorted_result
SELECT * FROM t4 WHERE c4 = 104;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t4;

## Backward index scan, covering ##
SELECT c1 FROM t4 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t5;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t5;
--sorted_result
SELECT * FROM t5 WHERE c5 = 112;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t5;

## Backward index scan, covering ##
SELECT c1 FROM t5 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t5 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t5 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;

# Index-merge access
--sorted_result
SELECT * FROM t4 WHERE c1=1 OR c2=109;

# Using index for group-by
--sorted_result
SELECT c2 FROM t4 GROUP BY c2;
--sorted_result
SELECT DISTINCT c2 FROM t4;
--sorted_result
SELECT c2,MIN(c3) FROM t4 GROUP BY c2;

# Test Error conditions- NULL into not null col, PK constraint violation, Unique constraint violation

# Insert NULL to non-null column
--error ER_BAD_NULL_ERROR
INSERT INTO t2(c1) VALUES(NULL);
--error ER_BAD_NULL_ERROR 
INSERT INTO t3 VALUES(NULL,1,2,3,4,5,6);
--error ER_BAD_NULL_ERROR
INSERT INTO t4(c3) VALUES(NULL);
--error ER_BAD_NULL_ERROR
INSERT INTO t5(c3) VALUES(NULL);

# Insert duplicate value to pk
--error ER_DUP_ENTRY
INSERT INTO t4(c1,c3) VALUES(-101,105) /*throws error as the row with c1=-101 exists */;

# Insert duplicate value to clustered pk, throws error
--error ER_DUP_ENTRY 
INSERT INTO t2(c1,c6) VALUES(101,106) /*throws error as the row with c1=101,c6=106 exists */;
--error ER_DUP_ENTRY
INSERT INTO t5(c1,c2,c3) VALUES(101,-102,4) /*throws error as the row with c1=101,c2=-102 exists */;

# Insert duplicate value to unique column, throws error
--error ER_DUP_ENTRY
INSERT INTO t5(c3) VALUES(-103) /*throws error as the row with c3=-103 exists */;
--error ER_DUP_ENTRY
# Insert duplicate value to clustered unique column, throws error
INSERT INTO t4(c2,c3) VALUES(102,103) /*throws error as the row with c2=102,c3=103 exists */;

# Test 'INSERT INTO SELECT FROM' 
CREATE TABLE t6(c1 TINYINT UNSIGNED NOT NULL, c2 TINYINT NULL, c3 SMALLINT, c4 MEDIUMINT , c5 INT, c6 INTEGER, c7 BIGINT);
INSERT INTO t6 SELECT * FROM t2 where c1 >= 109;
--sorted_result
SELECT * FROM t6;
TRUNCATE TABLE t6;
INSERT INTO t6 SELECT * FROM t3 where c2 <= -116;
--sorted_result
SELECT * FROM t6;
TRUNCATE TABLE t6;
# Test 'INSERT INTO SELECT FROM' primary key constraint violation 
CREATE TABLE t7(c1 TINYINT UNSIGNED NOT NULL PRIMARY KEY, c2 TINYINT NULL, c3 SMALLINT, c4 MEDIUMINT , c5 INT, c6 INTEGER, c7 BIGINT);
CREATE TABLE t8(c1 TINYINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY, c2 TINYINT NULL, c3 SMALLINT, c4 MEDIUMINT , c5 INT, c6 INTEGER, c7 BIGINT);
INSERT INTO t6 VALUES(1,-2,2,2,2,2,2),(1,-3,3,3,3,3,3);
INSERT INTO t7(c1) SELECT t6.c1 FROM t6 LIMIT 1;
--sorted_result
SELECT * FROM t7;
INSERT INTO t8(c1) SELECT t6.c1 FROM t6 LIMIT 1;
--sorted_result
SELECT * FROM t8;
--error ER_DUP_ENTRY
INSERT INTO t7(c1) SELECT t6.c1 FROM t6 /* throws error as t6 contains duplicate rows for c1=1 */;
--sorted_result
SELECT c1 FROM t7;
--error ER_DUP_ENTRY
INSERT INTO t8(c1) SELECT t6.c1 FROM t6 /* throws error as t6 contains duplicate rows for c1=1 */;
--sorted_result
SELECT c1 FROM t8;
TRUNCATE TABLE t6;
TRUNCATE TABLE t7;
INSERT INTO t6(c1) VALUES(0),(1),(2),(3),(3),(4),(4),(5);
--error ER_DUP_ENTRY
INSERT INTO t7(c1) SELECT c1 FROM t6;
TRUNCATE TABLE t6;

# Test 'INSERT INTO SELECT FROM' with AUTO_INCREMENT and inserting 0,NULL
INSERT INTO t6 VALUES(5,-1,1,1,1,1,1),(6,-2,2,2,2,2,2),(0,-4,4,4,4,4,4),(NULL,-5,5,5,5,5,5);
INSERT INTO t8 SELECT * FROM t6;
SELECT LAST_INSERT_ID() AS last_insert_id;
--sorted_result
SELECT * FROM t8;
TRUNCATE TABLE t8;
INSERT INTO t8(c2) SELECT c2 FROM t6 WHERE c1 >= 6;
--sorted_result
SELECT * FROM t8;
DROP TABLE t6,t7,t8;

# Test 'INSERT SET'
INSERT INTO t2 SET c3=5,c4=6;
SET SQL_MODE=STRICT_ALL_TABLES;
--error ER_NO_DEFAULT_FOR_FIELD
INSERT INTO t2 SET c3=5,c4=6;
SET SQL_MODE='';

# Test insert allowable bigger values to 'TINYINT' columns
INSERT INTO t1 VALUES(0,-128,1,2,3,4,5),(255,127,6,7,8,9,10);
INSERT INTO t2 VALUES(0,-128,1,2,3,4,5),(255,127,6,7,8,9,10);
INSERT INTO t3 VALUES(0,-128,1,2,3,4,5),(255,127,6,7,8,9,10);
INSERT INTO t4 VALUES(-128,0,1,2,3,4,5,5),(127,255,6,7,8,9,10,10);
INSERT INTO t5 VALUES(0,-128,1,2,3,4,5,5),(255,127,6,7,8,9,10,10);
--error ER_DUP_ENTRY
INSERT INTO t4(c2,c3) VALUES(31,32) /* tries to increment out of range */;
--disable_warnings
INSERT INTO t5(c2,c3) VALUES(33,34) /* tries to increment out of range */;
--enable_warnings
--sorted_result
SELECT * FROM t4;

# Insert signed value to unsigned 'TINYINT', outside range values to signed/unsigned columns
INSERT INTO t1 VALUES(-255,-129,26,27,28,29,30),(256,128,31,32,33,34,35);
INSERT INTO t2 VALUES(-255,-129,26,27,28,29,30),(256,128,31,32,33,34,35);
INSERT INTO t3 VALUES(-255,-129,26,27,28,29,30),(256,128,31,32,33,34,35);
# Insert into t4, t5 (TBD)

# Now select using various table access methods
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = -128;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,255) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,255) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 255 AND c6 = 4 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 255 AND c6 = 4 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,255) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,255) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 255 AND c6 = 4 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 255 AND c6 = 4 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 127;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 255 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,255) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,255) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 255 AND c6 = 4 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 255 AND c6 = 4 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,255) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,255) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 255 AND c6 = 4 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 255 AND c6 = 4 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = -129;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 256 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 256 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,256) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (-255,256) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 256 AND c6 = 29 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 256 AND c6 = 29 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 256 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 256 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,256) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (-255,256) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 256 AND c6 = 29 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 256 AND c6 = 29 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 128;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 256 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 256 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 256 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 256 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 256 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 256 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 256 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 256 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 256 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 256 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 256 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 256 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 256 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 256 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 256 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 256 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 256 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 256 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,256) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (-255,256) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 256 AND c6 = 29 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 256 AND c6 = 29 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 256 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 256 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 256 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 256 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 256 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 256 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 256 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 256 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 256 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 256 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 256 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 256 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 256 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 256 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,256) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (-255,256) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 256 AND c6 = 29 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 256 AND c6 = 29 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 0;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -128 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -128 AND 127 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -128 AND 127 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-128,127) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-128,127) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -128 AND c2 < 127 AND c7 = 5 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -128 AND c2 < 127 AND c7 = 5 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -128 AND 127 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -128 AND 127 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-128,127) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-128,127) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -128 AND c2 < 127 AND c7 = 5 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -128 AND c2 < 127 AND c7 = 5 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 255;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = 127 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = 127 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = 127 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = 127 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> 127 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> 127 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > 127 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > 127 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 127 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= 127 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < 127 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < 127 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 127 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= 127 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 127 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> 127 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -128 AND 127 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -128 AND 127 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-128,127) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-128,127) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -128 AND c2 < 127 AND c7 = 5 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -128 AND c2 < 127 AND c7 = 5 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> 127 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> 127 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > 127 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > 127 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 127 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= 127 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < 127 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < 127 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 127 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= 127 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 127 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> 127 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -128 AND 127 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -128 AND 127 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-128,127) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-128,127) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -128 AND c2 < 127 AND c7 = 5 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -128 AND c2 < 127 AND c7 = 5 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = -255;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -129 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -129 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -129 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -129 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -129 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -129 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -129 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -129 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -129 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -129 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -129 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -129 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -129 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -129 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -129 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -129 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -129 AND 128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -129 AND 128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-129,128) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-129,128) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -129 AND c2 < 128 AND c7 = 30 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -129 AND c2 < 128 AND c7 = 30 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -129 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -129 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -129 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -129 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -129 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -129 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -129 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -129 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -129 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -129 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -129 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -129 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -129 AND 128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -129 AND 128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-129,128) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-129,128) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -129 AND c2 < 128 AND c7 = 30 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -129 AND c2 < 128 AND c7 = 30 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 256;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = 128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = 128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = 128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = 128 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> 128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> 128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > 128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > 128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= 128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < 128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < 128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= 128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> 128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -129 AND 128 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -129 AND 128 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-129,128) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-129,128) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -129 AND c2 < 128 AND c7 = 30 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -129 AND c2 < 128 AND c7 = 30 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> 128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> 128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > 128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > 128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= 128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < 128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < 128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= 128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> 128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -129 AND 128 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -129 AND 128 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-129,128) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-129,128) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -129 AND c2 < 128 AND c7 = 30 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -129 AND c2 < 128 AND c7 = 30 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
DROP TABLE t1,t2,t3,t4,t5;

######## Running INSERT tests for SMALLINT ########

# Create tables
CREATE TABLE t1(c1 SMALLINT UNSIGNED NOT NULL, c2 SMALLINT NULL, c3 TINYINT, c4 MEDIUMINT , c5 INT, c6 INTEGER, c7 BIGINT);
CREATE TABLE t2(c1 SMALLINT UNSIGNED NOT NULL, c2 SMALLINT NULL, c3 TINYINT, c4 MEDIUMINT , c5 INT, c6 INTEGER, c7 BIGINT, PRIMARY KEY(c1,c6));
CREATE TABLE t3(c1 SMALLINT UNSIGNED NOT NULL, c2 SMALLINT NULL, c3 TINYINT, c4 MEDIUMINT , c5 INT, c6 INTEGER, c7 BIGINT, index idx(c2,c7));

# Insert some rows with targeted values 
INSERT INTO t1 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);
INSERT INTO t2 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);
INSERT INTO t3 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);

# Insert duplicates for parts of the clustered key
INSERT INTO t2 VALUES(108,-109,110,111,112,101,114),(108,-109,110,111,112,102,114),(102,-109,110,111,112,113,114),(103,-109,110,111,112,113,114);
INSERT INTO t3 VALUES(101,-102,103,104,105,106,101),(101,-102,103,104,105,106,102),(101,-103,103,104,105,106,107),(101,-104,103,104,105,106,107);

# Insert permissible NULLs
INSERT INTO t1 VALUES(105,NULL,102,103,104,105,106);
INSERT INTO t2 VALUES(105,NULL,102,103,104,105,106);
INSERT INTO t3 VALUES(105,NULL,102,103,104,105,106);

# Insert empty string ''
INSERT INTO t1 VALUES('','',17,18,19,20,21);
INSERT INTO t2 VALUES('','',17,18,19,20,21);
INSERT INTO t3 VALUES('','',17,18,19,20,21);

# Insert negative value to unsigned integer, positive value to signed integer, zero values
INSERT INTO t1 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t2 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t3 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t1 VALUES(0,124,27,28,29,30,31);
INSERT INTO t2 VALUES(0,124,27,28,29,30,31);
INSERT INTO t3 VALUES(0,124,27,28,29,30,31);
INSERT INTO t1 VALUES(0,0,32,32,34,35,36);
INSERT INTO t2 VALUES(0,0,32,32,34,35,36);
INSERT INTO t3 VALUES(0,0,32,32,34,35,36);

# Insert non-numeric value to numeric column, would be converted to 0 or numeric value (if contains part)
INSERT INTO t1 VALUES('101.34 a','a',37,38,39,40,41);
INSERT INTO t2 VALUES('101.34 a','a',37,38,39,40,41);
INSERT INTO t3 VALUES('101.34 a','a',37,38,39,40,41);

--sorted_result
SELECT * FROM t1;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t1;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t1;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t2;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t2;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t3;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t3;

# Now select using various table access methods (full table scan, range scan, index scan etc.)
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 102;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 102;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 108;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 108;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;

# Using index for group-by
--sorted_result
SELECT c2 FROM t3 GROUP BY c2;
--sorted_result
SELECT DISTINCT c2 FROM t3;
--sorted_result
SELECT c2,MIN(c7) FROM t3 GROUP BY c2;

# Create tables with AUTO_INCREMENT columns and unique indexes
CREATE TABLE t4(c1 SMALLINT SIGNED NOT NULL AUTO_INCREMENT, c2 SMALLINT UNSIGNED NULL, c3 SMALLINT UNSIGNED NOT NULL, c4 TINYINT , c5 MEDIUMINT, c6 INT, c7 INTEGER, c8 BIGINT, PRIMARY KEY(c1), UNIQUE INDEX idx(c2,c3));
CREATE TABLE t5(c1 SMALLINT UNSIGNED NOT NULL AUTO_INCREMENT, c2 SMALLINT SIGNED NOT NULL, c3 SMALLINT SIGNED NOT NULL, c4 TINYINT, c5 MEDIUMINT, c6 INT, c7 INTEGER, c8 BIGINT, PRIMARY KEY(c1,c2), UNIQUE INDEX(c3));

# Test with AUTO_INCREMENT columns
# Insert some rows
INSERT INTO t4 VALUES(-101,102,103,104,105,106,107,108),(-108,109,110,111,112,113,114,115);
INSERT INTO t5 VALUES(101,-102,-103,-104,105,106,107,108),(108,-109,-110,111,112,113,114,115);
# Insert 0 values, increments the value
INSERT INTO t4 VALUES(0,32,33,34,35,36,37,38);
--let $auto_value=`SELECT LAST_INSERT_ID()=1 AND c1=1 FROM t4 WHERE c2=32 AND c3=33;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on 0"
}
INSERT INTO t5 VALUES(0,-32,-32,33,34,35,36,37);
--let $auto_value=`SELECT LAST_INSERT_ID()=109 AND c1=109 FROM t5 WHERE c2=-32 AND c3=-32;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on 0"
}
# Insert NULL,  no error, increments the value
INSERT INTO t4(c1,c3,c4) VALUES(NULL,104,LAST_INSERT_ID());
--let $auto_value=`SELECT LAST_INSERT_ID()=2 AND c1=2 FROM t4 WHERE c3=104;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on NULL"
}
INSERT INTO t5(c1,c3,c4) VALUES(NULL,-104,LAST_INSERT_ID());
--let $auto_value=`SELECT LAST_INSERT_ID()=110 AND c1=110 FROM t5 WHERE c3=-104;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on NULL"
}
# Insert empty strings, increments the value 
INSERT INTO t4 VALUES('','',17,18,19,20,21,22);
--let $auto_value=`SELECT LAST_INSERT_ID()=3 AND c1=3 FROM t4 WHERE c2=0 AND c3=17;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on ''"
}
INSERT INTO t5 VALUES('','',-17,18,19,20,21,22);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=111 FROM t5 WHERE c2=0 AND c3=-17;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on ''"
}
# Insert strings with numeric part, inserts the numeric part of the string
INSERT INTO t4 VALUES('102.34 a','a','a',37,38,39,40,41);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=102 FROM t4 WHERE c2=0 AND c3=0;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on invalid strings with numeric part"
}
INSERT INTO t5 VALUES('102.34 a','a','a',37,38,39,40,41);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=102 FROM t5 WHERE c2=0 AND c3=0;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on invalid strings with numeric part"
}
# Insert negative values unsigned & positive value to signed
INSERT INTO t4 VALUES(4,7,8,9,10,11,12,13);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=4 FROM t4 WHERE c2=7 AND c3=8;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work on negative value"
}
INSERT INTO t5 VALUES(-1,-1,-1,8,9,10,11,12);
--let $auto_value=`SELECT LAST_INSERT_ID()=112 AND c1=112 FROM t5 WHERE c2=-1 AND c3=-1;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work for negative value on unsigned"
}
# Insert some rows with no values assigned to AUTO_INCREMENT columns, increments value
INSERT INTO t4(c2,c3,c4,c5,c6,c7,c8) VALUES(101,102,104,105,106,107,108),(108,109,111,112,113,114,115),(115,116,118,119,120,121,122),(122,123,125,126,127,128,128);
--let $auto_value=`SELECT LAST_INSERT_ID()=103 AND c1=103 FROM t4 WHERE c2=101 AND c3=102;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well"
}
INSERT INTO t5(c2,c3,c4,c5,c6,c7,c8) VALUES(-101,-102,104,105,106,107,108),(-108,-109,111,112,113,114,115),(-115,-116,118,119,120,121,122),(-122,-123,125,126,127,128,128);
--let $auto_value=`SELECT LAST_INSERT_ID()=113 AND c1=113 FROM t5 WHERE c2=-101 AND c3=-102;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well"
}

SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t4;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t5;
# Now select using various table access methods on auto-incremented pk value(full table scan, range scan, index scan etc.)
## Full table scan ##
--sorted_result
SELECT * FROM t4;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t4;
--sorted_result
SELECT * FROM t4 WHERE c4 = 104;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t4;

## Backward index scan, covering ##
SELECT c1 FROM t4 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t4;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t4;
--sorted_result
SELECT * FROM t4 WHERE c4 = 104;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t4;

## Backward index scan, covering ##
SELECT c1 FROM t4 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t5;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t5;
--sorted_result
SELECT * FROM t5 WHERE c5 = 112;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t5;

## Backward index scan, covering ##
SELECT c1 FROM t5 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t5 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t5 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;

# Index-merge access
--sorted_result
SELECT * FROM t4 WHERE c1=1 OR c2=109;

# Using index for group-by
--sorted_result
SELECT c2 FROM t4 GROUP BY c2;
--sorted_result
SELECT DISTINCT c2 FROM t4;
--sorted_result
SELECT c2,MIN(c3) FROM t4 GROUP BY c2;

# Test Error conditions- NULL into not null col, PK constraint violation, Unique constraint violation

# Insert NULL to non-null column
--error ER_BAD_NULL_ERROR
INSERT INTO t2(c1) VALUES(NULL);
--error ER_BAD_NULL_ERROR 
INSERT INTO t3 VALUES(NULL,1,2,3,4,5,6);
--error ER_BAD_NULL_ERROR
INSERT INTO t4(c3) VALUES(NULL);
--error ER_BAD_NULL_ERROR
INSERT INTO t5(c3) VALUES(NULL);

# Insert duplicate value to pk
--error ER_DUP_ENTRY
INSERT INTO t4(c1,c3) VALUES(-101,105) /*throws error as the row with c1=-101 exists */;

# Insert duplicate value to clustered pk, throws error
--error ER_DUP_ENTRY 
INSERT INTO t2(c1,c6) VALUES(101,106) /*throws error as the row with c1=101,c6=106 exists */;
--error ER_DUP_ENTRY
INSERT INTO t5(c1,c2,c3) VALUES(101,-102,4) /*throws error as the row with c1=101,c2=-102 exists */;

# Insert duplicate value to unique column, throws error
--error ER_DUP_ENTRY
INSERT INTO t5(c3) VALUES(-103) /*throws error as the row with c3=-103 exists */;
--error ER_DUP_ENTRY
# Insert duplicate value to clustered unique column, throws error
INSERT INTO t4(c2,c3) VALUES(102,103) /*throws error as the row with c2=102,c3=103 exists */;

# Test 'INSERT INTO SELECT FROM' 
CREATE TABLE t6(c1 SMALLINT UNSIGNED NOT NULL, c2 SMALLINT NULL, c3 TINYINT, c4 MEDIUMINT , c5 INT, c6 INTEGER, c7 BIGINT);
INSERT INTO t6 SELECT * FROM t2 where c1 >= 109;
--sorted_result
SELECT * FROM t6;
TRUNCATE TABLE t6;
INSERT INTO t6 SELECT * FROM t3 where c2 <= -116;
--sorted_result
SELECT * FROM t6;
TRUNCATE TABLE t6;
# Test 'INSERT INTO SELECT FROM' primary key constraint violation 
CREATE TABLE t7(c1 SMALLINT UNSIGNED NOT NULL PRIMARY KEY, c2 SMALLINT NULL, c3 TINYINT, c4 MEDIUMINT , c5 INT, c6 INTEGER, c7 BIGINT);
CREATE TABLE t8(c1 SMALLINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY, c2 SMALLINT NULL, c3 TINYINT, c4 MEDIUMINT , c5 INT, c6 INTEGER, c7 BIGINT);
INSERT INTO t6 VALUES(1,-2,2,2,2,2,2),(1,-3,3,3,3,3,3);
INSERT INTO t7(c1) SELECT t6.c1 FROM t6 LIMIT 1;
--sorted_result
SELECT * FROM t7;
INSERT INTO t8(c1) SELECT t6.c1 FROM t6 LIMIT 1;
--sorted_result
SELECT * FROM t8;
--error ER_DUP_ENTRY
INSERT INTO t7(c1) SELECT t6.c1 FROM t6 /* throws error as t6 contains duplicate rows for c1=1 */;
--error ER_DUP_ENTRY
INSERT INTO t8(c1) SELECT t6.c1 FROM t6 /* throws error as t6 contains duplicate rows for c1=1 */;
TRUNCATE TABLE t6;

# Test 'INSERT INTO SELECT FROM' with AUTO_INCREMENT and inserting 0,NULL
INSERT INTO t6 VALUES(5,-1,1,1,1,1,1),(6,-2,2,2,2,2,2),(0,-4,4,4,4,4,4),(NULL,-5,5,5,5,5,5);
INSERT INTO t8 SELECT * FROM t6;
SELECT LAST_INSERT_ID() AS last_insert_id;
--sorted_result
SELECT * FROM t8;
TRUNCATE TABLE t8;
INSERT INTO t8(c2) SELECT c2 FROM t6 WHERE c1 >= 6;
--sorted_result
SELECT * FROM t8;
DROP TABLE t6,t7,t8;

# Test 'INSERT SET'
INSERT INTO t2 SET c3=5,c4=6;
SET SQL_MODE=STRICT_ALL_TABLES;
--error ER_NO_DEFAULT_FOR_FIELD
INSERT INTO t2 SET c3=5,c4=6;
SET SQL_MODE='';

# Test insert allowable bigger values to 'SMALLINT' columns
INSERT INTO t1 VALUES(0,-32768,1,2,3,4,5),(255,-128,6,7,8,9,10),(65535,32767,11,12,13,14,15);
INSERT INTO t2 VALUES(0,-32768,1,2,3,4,5),(255,-128,6,7,8,9,10),(65535,32767,11,12,13,14,15);
INSERT INTO t3 VALUES(0,-32768,1,2,3,4,5),(255,-128,6,7,8,9,10),(65535,32767,11,12,13,14,15);
INSERT INTO t4 VALUES(-32768,0,1,2,3,4,5,5),(-128,255,6,7,8,9,10,10),(32767,65535,11,12,13,14,15,15);
INSERT INTO t5 VALUES(0,-32768,1,2,3,4,5,5),(255,-128,6,7,8,9,10,10),(65535,32767,11,12,13,14,15,15);
--error ER_DUP_ENTRY
INSERT INTO t4(c2,c3) VALUES(31,32) /* tries to increment out of range */;
--disable_warnings
INSERT INTO t5(c2,c3) VALUES(33,34) /* tries to increment out of range */;
--enable_warnings
--sorted_result
SELECT * FROM t4;

# Insert signed value to unsigned 'SMALLINT', outside range values to signed/unsigned columns
INSERT INTO t1 VALUES(-255,-32769,26,27,28,29,30),(65536,32768,31,32,33,34,35);
INSERT INTO t2 VALUES(-255,-32769,26,27,28,29,30),(65536,32768,31,32,33,34,35);
INSERT INTO t3 VALUES(-255,-32769,26,27,28,29,30),(65536,32768,31,32,33,34,35);
# Insert into t4, t5 (TBD)

# Now select using various table access methods
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = -32768;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 65535 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 65535 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,65535) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,65535) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 65535 AND c6 = 9 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 65535 AND c6 = 9 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 65535 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 65535 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,65535) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,65535) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 65535 AND c6 = 9 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 65535 AND c6 = 9 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 32767;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 65535 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 65535 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 65535 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 65535 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 65535 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 65535 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 65535 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 65535 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 65535 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 65535 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 65535 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 65535 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 65535 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 65535 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 65535 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 65535 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 65535 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 65535 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,65535) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,65535) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 65535 AND c6 = 9 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 65535 AND c6 = 9 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 65535 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 65535 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 65535 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 65535 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 65535 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 65535 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 65535 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 65535 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 65535 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 65535 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 65535 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 65535 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 65535 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 65535 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,65535) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,65535) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 65535 AND c6 = 9 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 65535 AND c6 = 9 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = -32769;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 65536 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 65536 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,65536) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (-255,65536) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 65536 AND c6 = 29 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 65536 AND c6 = 29 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 65536 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 65536 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,65536) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (-255,65536) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 65536 AND c6 = 29 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 65536 AND c6 = 29 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 32768;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 65536 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 65536 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 65536 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 65536 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 65536 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 65536 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 65536 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 65536 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 65536 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 65536 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 65536 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 65536 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 65536 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 65536 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 65536 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 65536 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 65536 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 65536 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,65536) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (-255,65536) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 65536 AND c6 = 29 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 65536 AND c6 = 29 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 65536 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 65536 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 65536 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 65536 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 65536 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 65536 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 65536 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 65536 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 65536 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 65536 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 65536 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 65536 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 65536 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 65536 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,65536) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (-255,65536) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 65536 AND c6 = 29 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 65536 AND c6 = 29 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 0;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -32768 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -32768 AND 32767 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -32768 AND 32767 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-32768,32767) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-32768,32767) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -32768 AND c2 < 32767 AND c7 = 10 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -32768 AND c2 < 32767 AND c7 = 10 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -32768 AND 32767 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -32768 AND 32767 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-32768,32767) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-32768,32767) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -32768 AND c2 < 32767 AND c7 = 10 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -32768 AND c2 < 32767 AND c7 = 10 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 65535;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = 32767 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = 32767 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = 32767 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = 32767 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> 32767 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> 32767 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > 32767 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > 32767 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 32767 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= 32767 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < 32767 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < 32767 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 32767 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= 32767 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 32767 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> 32767 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -32768 AND 32767 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -32768 AND 32767 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-32768,32767) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-32768,32767) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -32768 AND c2 < 32767 AND c7 = 10 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -32768 AND c2 < 32767 AND c7 = 10 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> 32767 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> 32767 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > 32767 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > 32767 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 32767 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= 32767 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < 32767 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < 32767 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 32767 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= 32767 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 32767 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> 32767 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -32768 AND 32767 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -32768 AND 32767 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-32768,32767) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-32768,32767) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -32768 AND c2 < 32767 AND c7 = 10 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -32768 AND c2 < 32767 AND c7 = 10 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = -255;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -32769 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -32769 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -32769 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -32769 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -32769 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -32769 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -32769 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -32769 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -32769 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -32769 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -32769 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -32769 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -32769 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -32769 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -32769 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -32769 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -32769 AND 32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -32769 AND 32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-32769,32768) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-32769,32768) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -32769 AND c2 < 32768 AND c7 = 30 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -32769 AND c2 < 32768 AND c7 = 30 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -32769 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -32769 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -32769 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -32769 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -32769 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -32769 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -32769 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -32769 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -32769 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -32769 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -32769 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -32769 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -32769 AND 32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -32769 AND 32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-32769,32768) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-32769,32768) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -32769 AND c2 < 32768 AND c7 = 30 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -32769 AND c2 < 32768 AND c7 = 30 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 65536;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = 32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = 32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = 32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = 32768 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> 32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> 32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > 32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > 32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= 32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < 32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < 32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= 32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> 32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -32769 AND 32768 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -32769 AND 32768 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-32769,32768) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-32769,32768) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -32769 AND c2 < 32768 AND c7 = 30 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -32769 AND c2 < 32768 AND c7 = 30 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> 32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> 32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > 32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > 32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= 32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < 32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < 32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= 32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> 32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -32769 AND 32768 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -32769 AND 32768 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-32769,32768) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-32769,32768) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -32769 AND c2 < 32768 AND c7 = 30 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -32769 AND c2 < 32768 AND c7 = 30 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
DROP TABLE t1,t2,t3,t4,t5;

######## Running INSERT tests for MEDIUMINT ########

# Create tables
CREATE TABLE t1(c1 MEDIUMINT UNSIGNED NOT NULL, c2 MEDIUMINT NULL, c3 TINYINT, c4 SMALLINT , c5 INT, c6 INTEGER, c7 BIGINT);
CREATE TABLE t2(c1 MEDIUMINT UNSIGNED NOT NULL, c2 MEDIUMINT NULL, c3 TINYINT, c4 SMALLINT , c5 INT, c6 INTEGER, c7 BIGINT, PRIMARY KEY(c1,c6));
CREATE TABLE t3(c1 MEDIUMINT UNSIGNED NOT NULL, c2 MEDIUMINT NULL, c3 TINYINT, c4 SMALLINT , c5 INT, c6 INTEGER, c7 BIGINT, index idx(c2,c7));

# Insert some rows with targeted values 
INSERT INTO t1 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);
INSERT INTO t2 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);
INSERT INTO t3 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);

# Insert duplicates for parts of the clustered key
INSERT INTO t2 VALUES(108,-109,110,111,112,101,114),(108,-109,110,111,112,102,114),(102,-109,110,111,112,113,114),(103,-109,110,111,112,113,114);
INSERT INTO t3 VALUES(101,-102,103,104,105,106,101),(101,-102,103,104,105,106,102),(101,-103,103,104,105,106,107),(101,-104,103,104,105,106,107);

# Insert permissible NULLs
INSERT INTO t1 VALUES(105,NULL,102,103,104,105,106);
INSERT INTO t2 VALUES(105,NULL,102,103,104,105,106);
INSERT INTO t3 VALUES(105,NULL,102,103,104,105,106);

# Insert empty string ''
INSERT INTO t1 VALUES('','',17,18,19,20,21);
INSERT INTO t2 VALUES('','',17,18,19,20,21);
INSERT INTO t3 VALUES('','',17,18,19,20,21);

# Insert negative value to unsigned integer, positive value to signed integer, zero values
INSERT INTO t1 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t2 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t3 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t1 VALUES(0,124,27,28,29,30,31);
INSERT INTO t2 VALUES(0,124,27,28,29,30,31);
INSERT INTO t3 VALUES(0,124,27,28,29,30,31);
INSERT INTO t1 VALUES(0,0,32,32,34,35,36);
INSERT INTO t2 VALUES(0,0,32,32,34,35,36);
INSERT INTO t3 VALUES(0,0,32,32,34,35,36);

# Insert non-numeric value to numeric column, would be converted to 0 or numeric value (if contains part)
INSERT INTO t1 VALUES('101.34 a','a',37,38,39,40,41);
INSERT INTO t2 VALUES('101.34 a','a',37,38,39,40,41);
INSERT INTO t3 VALUES('101.34 a','a',37,38,39,40,41);

--sorted_result
SELECT * FROM t1;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t1;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t1;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t2;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t2;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t3;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t3;

# Now select using various table access methods (full table scan, range scan, index scan etc.)
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 102;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 102;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 108;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 108;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;

# Using index for group-by
--sorted_result
SELECT c2 FROM t3 GROUP BY c2;
--sorted_result
SELECT DISTINCT c2 FROM t3;
--sorted_result
SELECT c2,MIN(c7) FROM t3 GROUP BY c2;

# Create tables with AUTO_INCREMENT columns and unique indexes
CREATE TABLE t4(c1 MEDIUMINT SIGNED NOT NULL AUTO_INCREMENT, c2 MEDIUMINT UNSIGNED NULL, c3 MEDIUMINT UNSIGNED NOT NULL, c4 TINYINT , c5 SMALLINT, c6 INT, c7 INTEGER, c8 BIGINT, PRIMARY KEY(c1), UNIQUE INDEX idx(c2,c3));
CREATE TABLE t5(c1 MEDIUMINT UNSIGNED NOT NULL AUTO_INCREMENT, c2 MEDIUMINT SIGNED NOT NULL, c3 MEDIUMINT SIGNED NOT NULL, c4 TINYINT, c5 SMALLINT, c6 INT, c7 INTEGER, c8 BIGINT, PRIMARY KEY(c1,c2), UNIQUE INDEX(c3));

# Test with AUTO_INCREMENT columns
# Insert some rows
INSERT INTO t4 VALUES(-101,102,103,104,105,106,107,108),(-108,109,110,111,112,113,114,115);
INSERT INTO t5 VALUES(101,-102,-103,-104,105,106,107,108),(108,-109,-110,111,112,113,114,115);
# Insert 0 values, increments the value
INSERT INTO t4 VALUES(0,32,33,34,35,36,37,38);
--let $auto_value=`SELECT LAST_INSERT_ID()=1 AND c1=1 FROM t4 WHERE c2=32 AND c3=33;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on 0"
}
INSERT INTO t5 VALUES(0,-32,-32,33,34,35,36,37);
--let $auto_value=`SELECT LAST_INSERT_ID()=109 AND c1=109 FROM t5 WHERE c2=-32 AND c3=-32;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on 0"
}
# Insert NULL,  no error, increments the value
INSERT INTO t4(c1,c3,c4) VALUES(NULL,104,LAST_INSERT_ID());
--let $auto_value=`SELECT LAST_INSERT_ID()=2 AND c1=2 FROM t4 WHERE c3=104;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on NULL"
}
INSERT INTO t5(c1,c3,c4) VALUES(NULL,-104,LAST_INSERT_ID());
--let $auto_value=`SELECT LAST_INSERT_ID()=110 AND c1=110 FROM t5 WHERE c3=-104;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on NULL"
}
# Insert empty strings, increments the value 
INSERT INTO t4 VALUES('','',17,18,19,20,21,22);
--let $auto_value=`SELECT LAST_INSERT_ID()=3 AND c1=3 FROM t4 WHERE c2=0 AND c3=17;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on ''"
}
INSERT INTO t5 VALUES('','',-17,18,19,20,21,22);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=111 FROM t5 WHERE c2=0 AND c3=-17;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on ''"
}
# Insert strings with numeric part, inserts the numeric part of the string
INSERT INTO t4 VALUES('102.34 a','a','a',37,38,39,40,41);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=102 FROM t4 WHERE c2=0 AND c3=0;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on invalid strings with numeric part"
}
INSERT INTO t5 VALUES('102.34 a','a','a',37,38,39,40,41);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=102 FROM t5 WHERE c2=0 AND c3=0;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on invalid strings with numeric part"
}
# Insert negative values unsigned & positive value to signed
INSERT INTO t4 VALUES(4,7,8,9,10,11,12,13);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=4 FROM t4 WHERE c2=7 AND c3=8;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work on negative value"
}
INSERT INTO t5 VALUES(-1,-1,-1,8,9,10,11,12);
--let $auto_value=`SELECT LAST_INSERT_ID()=112 AND c1=112 FROM t5 WHERE c2=-1 AND c3=-1;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work for negative value on unsigned"
}
# Insert some rows with no values assigned to AUTO_INCREMENT columns, increments value
INSERT INTO t4(c2,c3,c4,c5,c6,c7,c8) VALUES(101,102,104,105,106,107,108),(108,109,111,112,113,114,115),(115,116,118,119,120,121,122),(122,123,125,126,127,128,128);
--let $auto_value=`SELECT LAST_INSERT_ID()=103 AND c1=103 FROM t4 WHERE c2=101 AND c3=102;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well"
}
INSERT INTO t5(c2,c3,c4,c5,c6,c7,c8) VALUES(-101,-102,104,105,106,107,108),(-108,-109,111,112,113,114,115),(-115,-116,118,119,120,121,122),(-122,-123,125,126,127,128,128);
--let $auto_value=`SELECT LAST_INSERT_ID()=113 AND c1=113 FROM t5 WHERE c2=-101 AND c3=-102;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well"
}

SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t4;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t5;
# Now select using various table access methods on auto-incremented pk value(full table scan, range scan, index scan etc.)
## Full table scan ##
--sorted_result
SELECT * FROM t4;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t4;
--sorted_result
SELECT * FROM t4 WHERE c4 = 104;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t4;

## Backward index scan, covering ##
SELECT c1 FROM t4 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t4;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t4;
--sorted_result
SELECT * FROM t4 WHERE c4 = 104;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t4;

## Backward index scan, covering ##
SELECT c1 FROM t4 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t5;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t5;
--sorted_result
SELECT * FROM t5 WHERE c5 = 112;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t5;

## Backward index scan, covering ##
SELECT c1 FROM t5 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t5 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t5 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;

# Index-merge access
--sorted_result
SELECT * FROM t4 WHERE c1=1 OR c2=109;

# Using index for group-by
--sorted_result
SELECT c2 FROM t4 GROUP BY c2;
--sorted_result
SELECT DISTINCT c2 FROM t4;
--sorted_result
SELECT c2,MIN(c3) FROM t4 GROUP BY c2;

# Test Error conditions- NULL into not null col, PK constraint violation, Unique constraint violation

# Insert NULL to non-null column
--error ER_BAD_NULL_ERROR
INSERT INTO t2(c1) VALUES(NULL);
--error ER_BAD_NULL_ERROR 
INSERT INTO t3 VALUES(NULL,1,2,3,4,5,6);
--error ER_BAD_NULL_ERROR
INSERT INTO t4(c3) VALUES(NULL);
--error ER_BAD_NULL_ERROR
INSERT INTO t5(c3) VALUES(NULL);

# Insert duplicate value to pk
--error ER_DUP_ENTRY
INSERT INTO t4(c1,c3) VALUES(-101,105) /*throws error as the row with c1=-101 exists */;

# Insert duplicate value to clustered pk, throws error
--error ER_DUP_ENTRY 
INSERT INTO t2(c1,c6) VALUES(101,106) /*throws error as the row with c1=101,c6=106 exists */;
--error ER_DUP_ENTRY
INSERT INTO t5(c1,c2,c3) VALUES(101,-102,4) /*throws error as the row with c1=101,c2=-102 exists */;

# Insert duplicate value to unique column, throws error
--error ER_DUP_ENTRY
INSERT INTO t5(c3) VALUES(-103) /*throws error as the row with c3=-103 exists */;
--error ER_DUP_ENTRY
# Insert duplicate value to clustered unique column, throws error
INSERT INTO t4(c2,c3) VALUES(102,103) /*throws error as the row with c2=102,c3=103 exists */;

# Test 'INSERT INTO SELECT FROM' 
CREATE TABLE t6(c1 MEDIUMINT UNSIGNED NOT NULL, c2 MEDIUMINT NULL, c3 TINYINT, c4 SMALLINT , c5 INT, c6 INTEGER, c7 BIGINT);
INSERT INTO t6 SELECT * FROM t2 where c1 >= 109;
--sorted_result
SELECT * FROM t6;
TRUNCATE TABLE t6;
INSERT INTO t6 SELECT * FROM t3 where c2 <= -116;
--sorted_result
SELECT * FROM t6;
TRUNCATE TABLE t6;
# Test 'INSERT INTO SELECT FROM' primary key constraint violation 
CREATE TABLE t7(c1 MEDIUMINT UNSIGNED NOT NULL PRIMARY KEY, c2 MEDIUMINT NULL, c3 TINYINT, c4 SMALLINT , c5 INT, c6 INTEGER, c7 BIGINT);
CREATE TABLE t8(c1 MEDIUMINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY, c2 MEDIUMINT NULL, c3 TINYINT, c4 SMALLINT , c5 INT, c6 INTEGER, c7 BIGINT);
INSERT INTO t6 VALUES(1,-2,2,2,2,2,2),(1,-3,3,3,3,3,3);
INSERT INTO t7(c1) SELECT t6.c1 FROM t6 LIMIT 1;
--sorted_result
SELECT * FROM t7;
INSERT INTO t8(c1) SELECT t6.c1 FROM t6 LIMIT 1;
--sorted_result
SELECT * FROM t8;
--error ER_DUP_ENTRY
INSERT INTO t7(c1) SELECT t6.c1 FROM t6 /* throws error as t6 contains duplicate rows for c1=1 */;
--error ER_DUP_ENTRY
INSERT INTO t8(c1) SELECT t6.c1 FROM t6 /* throws error as t6 contains duplicate rows for c1=1 */;
TRUNCATE TABLE t6;

# Test 'INSERT INTO SELECT FROM' with AUTO_INCREMENT and inserting 0,NULL
INSERT INTO t6 VALUES(5,-1,1,1,1,1,1),(6,-2,2,2,2,2,2),(0,-4,4,4,4,4,4),(NULL,-5,5,5,5,5,5);
INSERT INTO t8 SELECT * FROM t6;
SELECT LAST_INSERT_ID() AS last_insert_id;
--sorted_result
SELECT * FROM t8;
TRUNCATE TABLE t8;
INSERT INTO t8(c2) SELECT c2 FROM t6 WHERE c1 >= 6;
--sorted_result
SELECT * FROM t8;
DROP TABLE t6,t7,t8;

# Test 'INSERT SET'
INSERT INTO t2 SET c3=5,c4=6;
SET SQL_MODE=STRICT_ALL_TABLES;
--error ER_NO_DEFAULT_FOR_FIELD
INSERT INTO t2 SET c3=5,c4=6;
SET SQL_MODE='';

# Test insert allowable bigger values to 'MEDIUMINT' columns
INSERT INTO t1 VALUES(0,-8388608,1,2,3,4,5),(255,-32768,6,7,8,9,10),(65535,-128,11,12,13,14,15),(16777215,8388607,16,17,18,19,20);
INSERT INTO t2 VALUES(0,-8388608,1,2,3,4,5),(255,-32768,6,7,8,9,10),(65535,-128,11,12,13,14,15),(16777215,8388607,16,17,18,19,20);
INSERT INTO t3 VALUES(0,-8388608,1,2,3,4,5),(255,-32768,6,7,8,9,10),(65535,-128,11,12,13,14,15),(16777215,8388607,16,17,18,19,20);
INSERT INTO t4 VALUES(-8388608,0,1,2,3,4,5,5),(-32768,255,6,7,8,9,10,10),(-128,65535,11,12,13,14,15,15),(8388607,16777215,16,17,18,19,20,20);
INSERT INTO t5 VALUES(0,-8388608,1,2,3,4,5,5),(255,-32768,6,7,8,9,10,10),(65535,-128,11,12,13,14,15,15),(16777215,8388607,16,17,18,19,20,20);
--error ER_DUP_ENTRY
INSERT INTO t4(c2,c3) VALUES(31,32) /* tries to increment out of range */;
--disable_warnings
INSERT INTO t5(c2,c3) VALUES(33,34) /* tries to increment out of range */;
--enable_warnings
--sorted_result
SELECT * FROM t4;

# Insert signed value to unsigned 'MEDIUMINT', outside range values to signed/unsigned columns
INSERT INTO t1 VALUES(-255,-8388609,26,27,28,29,30),(16777216,8388608,31,32,33,34,35);
INSERT INTO t2 VALUES(-255,-8388609,26,27,28,29,30),(16777216,8388608,31,32,33,34,35);
INSERT INTO t3 VALUES(-255,-8388609,26,27,28,29,30),(16777216,8388608,31,32,33,34,35);
# Insert into t4, t5 (TBD)

# Now select using various table access methods
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = -8388608;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 16777215 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 16777215 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,16777215) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,16777215) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 16777215 AND c6 = 9 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 16777215 AND c6 = 9 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 16777215 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 16777215 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,16777215) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,16777215) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 16777215 AND c6 = 9 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 16777215 AND c6 = 9 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 8388607;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 16777215 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 16777215 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 16777215 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 16777215 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 16777215 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 16777215 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 16777215 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 16777215 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 16777215 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 16777215 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 16777215 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 16777215 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 16777215 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 16777215 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 16777215 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 16777215 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 16777215 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 16777215 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,16777215) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,16777215) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 16777215 AND c6 = 9 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 16777215 AND c6 = 9 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 16777215 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 16777215 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 16777215 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 16777215 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 16777215 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 16777215 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 16777215 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 16777215 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 16777215 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 16777215 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 16777215 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 16777215 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 16777215 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 16777215 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,16777215) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,16777215) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 16777215 AND c6 = 9 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 16777215 AND c6 = 9 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = -8388609;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 16777216 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 16777216 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,16777216) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (-255,16777216) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 16777216 AND c6 = 29 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 16777216 AND c6 = 29 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 16777216 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 16777216 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,16777216) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (-255,16777216) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 16777216 AND c6 = 29 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 16777216 AND c6 = 29 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 8388608;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 16777216 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 16777216 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 16777216 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 16777216 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 16777216 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 16777216 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 16777216 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 16777216 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 16777216 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 16777216 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 16777216 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 16777216 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 16777216 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 16777216 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 16777216 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 16777216 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 16777216 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 16777216 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,16777216) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (-255,16777216) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 16777216 AND c6 = 29 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 16777216 AND c6 = 29 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 16777216 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 16777216 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 16777216 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 16777216 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 16777216 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 16777216 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 16777216 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 16777216 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 16777216 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 16777216 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 16777216 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 16777216 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 16777216 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 16777216 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,16777216) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (-255,16777216) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 16777216 AND c6 = 29 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 16777216 AND c6 = 29 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 0;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -8388608 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -8388608 AND 8388607 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -8388608 AND 8388607 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-8388608,8388607) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-8388608,8388607) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -8388608 AND c2 < 8388607 AND c7 = 15 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -8388608 AND c2 < 8388607 AND c7 = 15 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -8388608 AND 8388607 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -8388608 AND 8388607 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-8388608,8388607) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-8388608,8388607) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -8388608 AND c2 < 8388607 AND c7 = 15 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -8388608 AND c2 < 8388607 AND c7 = 15 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 16777215;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = 8388607 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = 8388607 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = 8388607 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = 8388607 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> 8388607 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> 8388607 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > 8388607 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > 8388607 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 8388607 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= 8388607 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < 8388607 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < 8388607 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 8388607 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= 8388607 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 8388607 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> 8388607 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -8388608 AND 8388607 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -8388608 AND 8388607 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-8388608,8388607) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-8388608,8388607) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -8388608 AND c2 < 8388607 AND c7 = 15 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -8388608 AND c2 < 8388607 AND c7 = 15 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> 8388607 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> 8388607 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > 8388607 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > 8388607 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 8388607 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= 8388607 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < 8388607 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < 8388607 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 8388607 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= 8388607 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 8388607 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> 8388607 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -8388608 AND 8388607 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -8388608 AND 8388607 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-8388608,8388607) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-8388608,8388607) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -8388608 AND c2 < 8388607 AND c7 = 15 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -8388608 AND c2 < 8388607 AND c7 = 15 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = -255;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -8388609 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -8388609 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -8388609 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -8388609 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -8388609 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -8388609 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -8388609 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -8388609 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -8388609 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -8388609 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -8388609 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -8388609 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -8388609 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -8388609 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -8388609 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -8388609 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -8388609 AND 8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -8388609 AND 8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-8388609,8388608) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-8388609,8388608) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -8388609 AND c2 < 8388608 AND c7 = 30 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -8388609 AND c2 < 8388608 AND c7 = 30 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -8388609 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -8388609 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -8388609 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -8388609 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -8388609 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -8388609 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -8388609 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -8388609 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -8388609 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -8388609 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -8388609 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -8388609 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -8388609 AND 8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -8388609 AND 8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-8388609,8388608) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-8388609,8388608) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -8388609 AND c2 < 8388608 AND c7 = 30 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -8388609 AND c2 < 8388608 AND c7 = 30 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 16777216;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = 8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = 8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = 8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = 8388608 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> 8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> 8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > 8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > 8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= 8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < 8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < 8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= 8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> 8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -8388609 AND 8388608 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -8388609 AND 8388608 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-8388609,8388608) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-8388609,8388608) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -8388609 AND c2 < 8388608 AND c7 = 30 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -8388609 AND c2 < 8388608 AND c7 = 30 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> 8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> 8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > 8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > 8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= 8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < 8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < 8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= 8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> 8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -8388609 AND 8388608 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -8388609 AND 8388608 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-8388609,8388608) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-8388609,8388608) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -8388609 AND c2 < 8388608 AND c7 = 30 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -8388609 AND c2 < 8388608 AND c7 = 30 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
DROP TABLE t1,t2,t3,t4,t5;

######## Running INSERT tests for INT ########

# Create tables
CREATE TABLE t1(c1 INT UNSIGNED NOT NULL, c2 INT NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INTEGER, c7 BIGINT);
CREATE TABLE t2(c1 INT UNSIGNED NOT NULL, c2 INT NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INTEGER, c7 BIGINT, PRIMARY KEY(c1,c6));
CREATE TABLE t3(c1 INT UNSIGNED NOT NULL, c2 INT NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INTEGER, c7 BIGINT, index idx(c2,c7));

# Insert some rows with targeted values 
INSERT INTO t1 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);
INSERT INTO t2 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);
INSERT INTO t3 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);

# Insert duplicates for parts of the clustered key
INSERT INTO t2 VALUES(108,-109,110,111,112,101,114),(108,-109,110,111,112,102,114),(102,-109,110,111,112,113,114),(103,-109,110,111,112,113,114);
INSERT INTO t3 VALUES(101,-102,103,104,105,106,101),(101,-102,103,104,105,106,102),(101,-103,103,104,105,106,107),(101,-104,103,104,105,106,107);

# Insert permissible NULLs
INSERT INTO t1 VALUES(105,NULL,102,103,104,105,106);
INSERT INTO t2 VALUES(105,NULL,102,103,104,105,106);
INSERT INTO t3 VALUES(105,NULL,102,103,104,105,106);

# Insert empty string ''
INSERT INTO t1 VALUES('','',17,18,19,20,21);
INSERT INTO t2 VALUES('','',17,18,19,20,21);
INSERT INTO t3 VALUES('','',17,18,19,20,21);

# Insert negative value to unsigned integer, positive value to signed integer, zero values
INSERT INTO t1 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t2 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t3 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t1 VALUES(0,124,27,28,29,30,31);
INSERT INTO t2 VALUES(0,124,27,28,29,30,31);
INSERT INTO t3 VALUES(0,124,27,28,29,30,31);
INSERT INTO t1 VALUES(0,0,32,32,34,35,36);
INSERT INTO t2 VALUES(0,0,32,32,34,35,36);
INSERT INTO t3 VALUES(0,0,32,32,34,35,36);

# Insert non-numeric value to numeric column, would be converted to 0 or numeric value (if contains part)
INSERT INTO t1 VALUES('101.34 a','a',37,38,39,40,41);
INSERT INTO t2 VALUES('101.34 a','a',37,38,39,40,41);
INSERT INTO t3 VALUES('101.34 a','a',37,38,39,40,41);

--sorted_result
SELECT * FROM t1;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t1;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t1;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t2;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t2;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t3;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t3;

# Now select using various table access methods (full table scan, range scan, index scan etc.)
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 102;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 102;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 108;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 108;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;

# Using index for group-by
--sorted_result
SELECT c2 FROM t3 GROUP BY c2;
--sorted_result
SELECT DISTINCT c2 FROM t3;
--sorted_result
SELECT c2,MIN(c7) FROM t3 GROUP BY c2;

# Create tables with AUTO_INCREMENT columns and unique indexes
CREATE TABLE t4(c1 INT SIGNED NOT NULL AUTO_INCREMENT, c2 INT UNSIGNED NULL, c3 INT UNSIGNED NOT NULL, c4 TINYINT , c5 SMALLINT, c6 MEDIUMINT, c7 INTEGER, c8 BIGINT, PRIMARY KEY(c1), UNIQUE INDEX idx(c2,c3));
CREATE TABLE t5(c1 INT UNSIGNED NOT NULL AUTO_INCREMENT, c2 INT SIGNED NOT NULL, c3 INT SIGNED NOT NULL, c4 TINYINT, c5 SMALLINT, c6 MEDIUMINT, c7 INTEGER, c8 BIGINT, PRIMARY KEY(c1,c2), UNIQUE INDEX(c3));

# Test with AUTO_INCREMENT columns
# Insert some rows
INSERT INTO t4 VALUES(-101,102,103,104,105,106,107,108),(-108,109,110,111,112,113,114,115);
INSERT INTO t5 VALUES(101,-102,-103,-104,105,106,107,108),(108,-109,-110,111,112,113,114,115);
# Insert 0 values, increments the value
INSERT INTO t4 VALUES(0,32,33,34,35,36,37,38);
--let $auto_value=`SELECT LAST_INSERT_ID()=1 AND c1=1 FROM t4 WHERE c2=32 AND c3=33;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on 0"
}
INSERT INTO t5 VALUES(0,-32,-32,33,34,35,36,37);
--let $auto_value=`SELECT LAST_INSERT_ID()=109 AND c1=109 FROM t5 WHERE c2=-32 AND c3=-32;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on 0"
}
# Insert NULL,  no error, increments the value
INSERT INTO t4(c1,c3,c4) VALUES(NULL,104,LAST_INSERT_ID());
--let $auto_value=`SELECT LAST_INSERT_ID()=2 AND c1=2 FROM t4 WHERE c3=104;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on NULL"
}
INSERT INTO t5(c1,c3,c4) VALUES(NULL,-104,LAST_INSERT_ID());
--let $auto_value=`SELECT LAST_INSERT_ID()=110 AND c1=110 FROM t5 WHERE c3=-104;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on NULL"
}
# Insert empty strings, increments the value 
INSERT INTO t4 VALUES('','',17,18,19,20,21,22);
--let $auto_value=`SELECT LAST_INSERT_ID()=3 AND c1=3 FROM t4 WHERE c2=0 AND c3=17;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on ''"
}
INSERT INTO t5 VALUES('','',-17,18,19,20,21,22);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=111 FROM t5 WHERE c2=0 AND c3=-17;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on ''"
}
# Insert strings with numeric part, inserts the numeric part of the string
INSERT INTO t4 VALUES('102.34 a','a','a',37,38,39,40,41);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=102 FROM t4 WHERE c2=0 AND c3=0;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on invalid strings with numeric part"
}
INSERT INTO t5 VALUES('102.34 a','a','a',37,38,39,40,41);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=102 FROM t5 WHERE c2=0 AND c3=0;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on invalid strings with numeric part"
}
# Insert negative values unsigned & positive value to signed
INSERT INTO t4 VALUES(4,7,8,9,10,11,12,13);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=4 FROM t4 WHERE c2=7 AND c3=8;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work on negative value"
}
INSERT INTO t5 VALUES(-1,-1,-1,8,9,10,11,12);
--let $auto_value=`SELECT LAST_INSERT_ID()=112 AND c1=112 FROM t5 WHERE c2=-1 AND c3=-1;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work for negative value on unsigned"
}
# Insert some rows with no values assigned to AUTO_INCREMENT columns, increments value
INSERT INTO t4(c2,c3,c4,c5,c6,c7,c8) VALUES(101,102,104,105,106,107,108),(108,109,111,112,113,114,115),(115,116,118,119,120,121,122),(122,123,125,126,127,128,128);
--let $auto_value=`SELECT LAST_INSERT_ID()=103 AND c1=103 FROM t4 WHERE c2=101 AND c3=102;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well"
}
INSERT INTO t5(c2,c3,c4,c5,c6,c7,c8) VALUES(-101,-102,104,105,106,107,108),(-108,-109,111,112,113,114,115),(-115,-116,118,119,120,121,122),(-122,-123,125,126,127,128,128);
--let $auto_value=`SELECT LAST_INSERT_ID()=113 AND c1=113 FROM t5 WHERE c2=-101 AND c3=-102;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well"
}

SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t4;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t5;
# Now select using various table access methods on auto-incremented pk value(full table scan, range scan, index scan etc.)
## Full table scan ##
--sorted_result
SELECT * FROM t4;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t4;
--sorted_result
SELECT * FROM t4 WHERE c4 = 104;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t4;

## Backward index scan, covering ##
SELECT c1 FROM t4 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t4;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t4;
--sorted_result
SELECT * FROM t4 WHERE c4 = 104;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t4;

## Backward index scan, covering ##
SELECT c1 FROM t4 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t5;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t5;
--sorted_result
SELECT * FROM t5 WHERE c5 = 112;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t5;

## Backward index scan, covering ##
SELECT c1 FROM t5 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t5 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t5 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;

# Index-merge access
--sorted_result
SELECT * FROM t4 WHERE c1=1 OR c2=109;

# Using index for group-by
--sorted_result
SELECT c2 FROM t4 GROUP BY c2;
--sorted_result
SELECT DISTINCT c2 FROM t4;
--sorted_result
SELECT c2,MIN(c3) FROM t4 GROUP BY c2;

# Test Error conditions- NULL into not null col, PK constraint violation, Unique constraint violation

# Insert NULL to non-null column
--error ER_BAD_NULL_ERROR
INSERT INTO t2(c1) VALUES(NULL);
--error ER_BAD_NULL_ERROR 
INSERT INTO t3 VALUES(NULL,1,2,3,4,5,6);
--error ER_BAD_NULL_ERROR
INSERT INTO t4(c3) VALUES(NULL);
--error ER_BAD_NULL_ERROR
INSERT INTO t5(c3) VALUES(NULL);

# Insert duplicate value to pk
--error ER_DUP_ENTRY
INSERT INTO t4(c1,c3) VALUES(-101,105) /*throws error as the row with c1=-101 exists */;

# Insert duplicate value to clustered pk, throws error
--error ER_DUP_ENTRY 
INSERT INTO t2(c1,c6) VALUES(101,106) /*throws error as the row with c1=101,c6=106 exists */;
--error ER_DUP_ENTRY
INSERT INTO t5(c1,c2,c3) VALUES(101,-102,4) /*throws error as the row with c1=101,c2=-102 exists */;

# Insert duplicate value to unique column, throws error
--error ER_DUP_ENTRY
INSERT INTO t5(c3) VALUES(-103) /*throws error as the row with c3=-103 exists */;
--error ER_DUP_ENTRY
# Insert duplicate value to clustered unique column, throws error
INSERT INTO t4(c2,c3) VALUES(102,103) /*throws error as the row with c2=102,c3=103 exists */;

# Test 'INSERT INTO SELECT FROM' 
CREATE TABLE t6(c1 INT UNSIGNED NOT NULL, c2 INT NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INTEGER, c7 BIGINT);
INSERT INTO t6 SELECT * FROM t2 where c1 >= 109;
--sorted_result
SELECT * FROM t6;
TRUNCATE TABLE t6;
INSERT INTO t6 SELECT * FROM t3 where c2 <= -116;
--sorted_result
SELECT * FROM t6;
TRUNCATE TABLE t6;
# Test 'INSERT INTO SELECT FROM' primary key constraint violation 
CREATE TABLE t7(c1 INT UNSIGNED NOT NULL PRIMARY KEY, c2 INT NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INTEGER, c7 BIGINT);
CREATE TABLE t8(c1 INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY, c2 INT NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INTEGER, c7 BIGINT);
INSERT INTO t6 VALUES(1,-2,2,2,2,2,2),(1,-3,3,3,3,3,3);
INSERT INTO t7(c1) SELECT t6.c1 FROM t6 LIMIT 1;
--sorted_result
SELECT * FROM t7;
INSERT INTO t8(c1) SELECT t6.c1 FROM t6 LIMIT 1;
--sorted_result
SELECT * FROM t8;
--error ER_DUP_ENTRY
INSERT INTO t7(c1) SELECT t6.c1 FROM t6 /* throws error as t6 contains duplicate rows for c1=1 */;
--error ER_DUP_ENTRY
INSERT INTO t8(c1) SELECT t6.c1 FROM t6 /* throws error as t6 contains duplicate rows for c1=1 */;
TRUNCATE TABLE t6;

# Test 'INSERT INTO SELECT FROM' with AUTO_INCREMENT and inserting 0,NULL
INSERT INTO t6 VALUES(5,-1,1,1,1,1,1),(6,-2,2,2,2,2,2),(0,-4,4,4,4,4,4),(NULL,-5,5,5,5,5,5);
INSERT INTO t8 SELECT * FROM t6;
SELECT LAST_INSERT_ID() AS last_insert_id;
--sorted_result
SELECT * FROM t8;
TRUNCATE TABLE t8;
INSERT INTO t8(c2) SELECT c2 FROM t6 WHERE c1 >= 6;
--sorted_result
SELECT * FROM t8;
DROP TABLE t6,t7,t8;

# Test 'INSERT SET'
INSERT INTO t2 SET c3=5,c4=6;
SET SQL_MODE=STRICT_ALL_TABLES;
--error ER_NO_DEFAULT_FOR_FIELD
INSERT INTO t2 SET c3=5,c4=6;
SET SQL_MODE='';

# Test insert allowable bigger values to 'INT' columns
INSERT INTO t1 VALUES(0,-2147483648,1,2,3,4,5),(255,-8388608,6,7,8,9,10),(65535,-32768,11,12,13,14,15),(16777215,-128,16,17,18,19,20),(4294967295,2147483647,21,22,23,24,25);
INSERT INTO t2 VALUES(0,-2147483648,1,2,3,4,5),(255,-8388608,6,7,8,9,10),(65535,-32768,11,12,13,14,15),(16777215,-128,16,17,18,19,20),(4294967295,2147483647,21,22,23,24,25);
INSERT INTO t3 VALUES(0,-2147483648,1,2,3,4,5),(255,-8388608,6,7,8,9,10),(65535,-32768,11,12,13,14,15),(16777215,-128,16,17,18,19,20),(4294967295,2147483647,21,22,23,24,25);
INSERT INTO t4 VALUES(-2147483648,0,1,2,3,4,5,5),(-8388608,255,6,7,8,9,10,10),(-32768,65535,11,12,13,14,15,15),(-128,16777215,16,17,18,19,20,20),(2147483647,4294967295,21,22,23,24,25,25);
INSERT INTO t5 VALUES(0,-2147483648,1,2,3,4,5,5),(255,-8388608,6,7,8,9,10,10),(65535,-32768,11,12,13,14,15,15),(16777215,-128,16,17,18,19,20,20),(4294967295,2147483647,21,22,23,24,25,25);
--error ER_DUP_ENTRY
INSERT INTO t4(c2,c3) VALUES(31,32) /* tries to increment out of range */;
--disable_warnings
INSERT INTO t5(c2,c3) VALUES(33,34) /* tries to increment out of range */;
--enable_warnings
--sorted_result
SELECT * FROM t4;

# Insert signed value to unsigned 'INT', outside range values to signed/unsigned columns
INSERT INTO t1 VALUES(-255,-2147483649,26,27,28,29,30),(4294967296,2147483648,31,32,33,34,35);
INSERT INTO t2 VALUES(-255,-2147483649,26,27,28,29,30),(4294967296,2147483648,31,32,33,34,35);
INSERT INTO t3 VALUES(-255,-2147483649,26,27,28,29,30),(4294967296,2147483648,31,32,33,34,35);
# Insert into t4, t5 (TBD)

# Now select using various table access methods
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = -2147483648;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 2147483647;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 4294967295 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = -2147483649;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 2147483648;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 4294967296 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 0;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -2147483648 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 4294967295;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = 2147483647 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = -255;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -2147483649 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -2147483649 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -2147483649 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -2147483649 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -2147483649 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -2147483649 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -2147483649 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 4294967296;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = 2147483648 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
DROP TABLE t1,t2,t3,t4,t5;

######## Running INSERT tests for INTEGER ########

# Create tables
CREATE TABLE t1(c1 INTEGER UNSIGNED NOT NULL, c2 INTEGER NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 BIGINT);
CREATE TABLE t2(c1 INTEGER UNSIGNED NOT NULL, c2 INTEGER NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 BIGINT, PRIMARY KEY(c1,c6));
CREATE TABLE t3(c1 INTEGER UNSIGNED NOT NULL, c2 INTEGER NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 BIGINT, index idx(c2,c7));

# Insert some rows with targeted values 
INSERT INTO t1 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);
INSERT INTO t2 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);
INSERT INTO t3 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);

# Insert duplicates for parts of the clustered key
INSERT INTO t2 VALUES(108,-109,110,111,112,101,114),(108,-109,110,111,112,102,114),(102,-109,110,111,112,113,114),(103,-109,110,111,112,113,114);
INSERT INTO t3 VALUES(101,-102,103,104,105,106,101),(101,-102,103,104,105,106,102),(101,-103,103,104,105,106,107),(101,-104,103,104,105,106,107);

# Insert permissible NULLs
INSERT INTO t1 VALUES(105,NULL,102,103,104,105,106);
INSERT INTO t2 VALUES(105,NULL,102,103,104,105,106);
INSERT INTO t3 VALUES(105,NULL,102,103,104,105,106);

# Insert empty string ''
INSERT INTO t1 VALUES('','',17,18,19,20,21);
INSERT INTO t2 VALUES('','',17,18,19,20,21);
INSERT INTO t3 VALUES('','',17,18,19,20,21);

# Insert negative value to unsigned integer, positive value to signed integer, zero values
INSERT INTO t1 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t2 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t3 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t1 VALUES(0,124,27,28,29,30,31);
INSERT INTO t2 VALUES(0,124,27,28,29,30,31);
INSERT INTO t3 VALUES(0,124,27,28,29,30,31);
INSERT INTO t1 VALUES(0,0,32,32,34,35,36);
INSERT INTO t2 VALUES(0,0,32,32,34,35,36);
INSERT INTO t3 VALUES(0,0,32,32,34,35,36);

# Insert non-numeric value to numeric column, would be converted to 0 or numeric value (if contains part)
INSERT INTO t1 VALUES('101.34 a','a',37,38,39,40,41);
INSERT INTO t2 VALUES('101.34 a','a',37,38,39,40,41);
INSERT INTO t3 VALUES('101.34 a','a',37,38,39,40,41);

--sorted_result
SELECT * FROM t1;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t1;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t1;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t2;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t2;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t3;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t3;

# Now select using various table access methods (full table scan, range scan, index scan etc.)
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 102;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 102;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 108;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 108;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;

# Using index for group-by
--sorted_result
SELECT c2 FROM t3 GROUP BY c2;
--sorted_result
SELECT DISTINCT c2 FROM t3;
--sorted_result
SELECT c2,MIN(c7) FROM t3 GROUP BY c2;

# Create tables with AUTO_INCREMENT columns and unique indexes
CREATE TABLE t4(c1 INTEGER SIGNED NOT NULL AUTO_INCREMENT, c2 INTEGER UNSIGNED NULL, c3 INTEGER UNSIGNED NOT NULL, c4 TINYINT , c5 SMALLINT, c6 MEDIUMINT, c7 INT, c8 BIGINT, PRIMARY KEY(c1), UNIQUE INDEX idx(c2,c3));
CREATE TABLE t5(c1 INTEGER UNSIGNED NOT NULL AUTO_INCREMENT, c2 INTEGER SIGNED NOT NULL, c3 INTEGER SIGNED NOT NULL, c4 TINYINT, c5 SMALLINT, c6 MEDIUMINT, c7 INT, c8 BIGINT, PRIMARY KEY(c1,c2), UNIQUE INDEX(c3));

# Test with AUTO_INCREMENT columns
# Insert some rows
INSERT INTO t4 VALUES(-101,102,103,104,105,106,107,108),(-108,109,110,111,112,113,114,115);
INSERT INTO t5 VALUES(101,-102,-103,-104,105,106,107,108),(108,-109,-110,111,112,113,114,115);
# Insert 0 values, increments the value
INSERT INTO t4 VALUES(0,32,33,34,35,36,37,38);
--let $auto_value=`SELECT LAST_INSERT_ID()=1 AND c1=1 FROM t4 WHERE c2=32 AND c3=33;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on 0"
}
INSERT INTO t5 VALUES(0,-32,-32,33,34,35,36,37);
--let $auto_value=`SELECT LAST_INSERT_ID()=109 AND c1=109 FROM t5 WHERE c2=-32 AND c3=-32;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on 0"
}
# Insert NULL,  no error, increments the value
INSERT INTO t4(c1,c3,c4) VALUES(NULL,104,LAST_INSERT_ID());
--let $auto_value=`SELECT LAST_INSERT_ID()=2 AND c1=2 FROM t4 WHERE c3=104;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on NULL"
}
INSERT INTO t5(c1,c3,c4) VALUES(NULL,-104,LAST_INSERT_ID());
--let $auto_value=`SELECT LAST_INSERT_ID()=110 AND c1=110 FROM t5 WHERE c3=-104;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on NULL"
}
# Insert empty strings, increments the value 
INSERT INTO t4 VALUES('','',17,18,19,20,21,22);
--let $auto_value=`SELECT LAST_INSERT_ID()=3 AND c1=3 FROM t4 WHERE c2=0 AND c3=17;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on ''"
}
INSERT INTO t5 VALUES('','',-17,18,19,20,21,22);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=111 FROM t5 WHERE c2=0 AND c3=-17;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on ''"
}
# Insert strings with numeric part, inserts the numeric part of the string
INSERT INTO t4 VALUES('102.34 a','a','a',37,38,39,40,41);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=102 FROM t4 WHERE c2=0 AND c3=0;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on invalid strings with numeric part"
}
INSERT INTO t5 VALUES('102.34 a','a','a',37,38,39,40,41);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=102 FROM t5 WHERE c2=0 AND c3=0;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on invalid strings with numeric part"
}
# Insert negative values unsigned & positive value to signed
INSERT INTO t4 VALUES(4,7,8,9,10,11,12,13);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=4 FROM t4 WHERE c2=7 AND c3=8;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work on negative value"
}
INSERT INTO t5 VALUES(-1,-1,-1,8,9,10,11,12);
--let $auto_value=`SELECT LAST_INSERT_ID()=112 AND c1=112 FROM t5 WHERE c2=-1 AND c3=-1;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work for negative value on unsigned"
}
# Insert some rows with no values assigned to AUTO_INCREMENT columns, increments value
INSERT INTO t4(c2,c3,c4,c5,c6,c7,c8) VALUES(101,102,104,105,106,107,108),(108,109,111,112,113,114,115),(115,116,118,119,120,121,122),(122,123,125,126,127,128,128);
--let $auto_value=`SELECT LAST_INSERT_ID()=103 AND c1=103 FROM t4 WHERE c2=101 AND c3=102;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well"
}
INSERT INTO t5(c2,c3,c4,c5,c6,c7,c8) VALUES(-101,-102,104,105,106,107,108),(-108,-109,111,112,113,114,115),(-115,-116,118,119,120,121,122),(-122,-123,125,126,127,128,128);
--let $auto_value=`SELECT LAST_INSERT_ID()=113 AND c1=113 FROM t5 WHERE c2=-101 AND c3=-102;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well"
}

SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t4;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t5;
# Now select using various table access methods on auto-incremented pk value(full table scan, range scan, index scan etc.)
## Full table scan ##
--sorted_result
SELECT * FROM t4;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t4;
--sorted_result
SELECT * FROM t4 WHERE c4 = 104;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t4;

## Backward index scan, covering ##
SELECT c1 FROM t4 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t4;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t4;
--sorted_result
SELECT * FROM t4 WHERE c4 = 104;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t4;

## Backward index scan, covering ##
SELECT c1 FROM t4 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t5;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t5;
--sorted_result
SELECT * FROM t5 WHERE c5 = 112;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t5;

## Backward index scan, covering ##
SELECT c1 FROM t5 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t5 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t5 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;

# Index-merge access
--sorted_result
SELECT * FROM t4 WHERE c1=1 OR c2=109;

# Using index for group-by
--sorted_result
SELECT c2 FROM t4 GROUP BY c2;
--sorted_result
SELECT DISTINCT c2 FROM t4;
--sorted_result
SELECT c2,MIN(c3) FROM t4 GROUP BY c2;

# Test Error conditions- NULL into not null col, PK constraint violation, Unique constraint violation

# Insert NULL to non-null column
--error ER_BAD_NULL_ERROR
INSERT INTO t2(c1) VALUES(NULL);
--error ER_BAD_NULL_ERROR 
INSERT INTO t3 VALUES(NULL,1,2,3,4,5,6);
--error ER_BAD_NULL_ERROR
INSERT INTO t4(c3) VALUES(NULL);
--error ER_BAD_NULL_ERROR
INSERT INTO t5(c3) VALUES(NULL);

# Insert duplicate value to pk
--error ER_DUP_ENTRY
INSERT INTO t4(c1,c3) VALUES(-101,105) /*throws error as the row with c1=-101 exists */;

# Insert duplicate value to clustered pk, throws error
--error ER_DUP_ENTRY 
INSERT INTO t2(c1,c6) VALUES(101,106) /*throws error as the row with c1=101,c6=106 exists */;
--error ER_DUP_ENTRY
INSERT INTO t5(c1,c2,c3) VALUES(101,-102,4) /*throws error as the row with c1=101,c2=-102 exists */;

# Insert duplicate value to unique column, throws error
--error ER_DUP_ENTRY
INSERT INTO t5(c3) VALUES(-103) /*throws error as the row with c3=-103 exists */;
--error ER_DUP_ENTRY
# Insert duplicate value to clustered unique column, throws error
INSERT INTO t4(c2,c3) VALUES(102,103) /*throws error as the row with c2=102,c3=103 exists */;

# Test 'INSERT INTO SELECT FROM' 
CREATE TABLE t6(c1 INTEGER UNSIGNED NOT NULL, c2 INTEGER NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 BIGINT);
INSERT INTO t6 SELECT * FROM t2 where c1 >= 109;
--sorted_result
SELECT * FROM t6;
TRUNCATE TABLE t6;
INSERT INTO t6 SELECT * FROM t3 where c2 <= -116;
--sorted_result
SELECT * FROM t6;
TRUNCATE TABLE t6;
# Test 'INSERT INTO SELECT FROM' primary key constraint violation 
CREATE TABLE t7(c1 INTEGER UNSIGNED NOT NULL PRIMARY KEY, c2 INTEGER NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 BIGINT);
CREATE TABLE t8(c1 INTEGER UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY, c2 INTEGER NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 BIGINT);
INSERT INTO t6 VALUES(1,-2,2,2,2,2,2),(1,-3,3,3,3,3,3);
INSERT INTO t7(c1) SELECT t6.c1 FROM t6 LIMIT 1;
--sorted_result
SELECT * FROM t7;
INSERT INTO t8(c1) SELECT t6.c1 FROM t6 LIMIT 1;
--sorted_result
SELECT * FROM t8;
--error ER_DUP_ENTRY
INSERT INTO t7(c1) SELECT t6.c1 FROM t6 /* throws error as t6 contains duplicate rows for c1=1 */;
--error ER_DUP_ENTRY
INSERT INTO t8(c1) SELECT t6.c1 FROM t6 /* throws error as t6 contains duplicate rows for c1=1 */;
TRUNCATE TABLE t6;

# Test 'INSERT INTO SELECT FROM' with AUTO_INCREMENT and inserting 0,NULL
INSERT INTO t6 VALUES(5,-1,1,1,1,1,1),(6,-2,2,2,2,2,2),(0,-4,4,4,4,4,4),(NULL,-5,5,5,5,5,5);
INSERT INTO t8 SELECT * FROM t6;
SELECT LAST_INSERT_ID() AS last_insert_id;
--sorted_result
SELECT * FROM t8;
TRUNCATE TABLE t8;
INSERT INTO t8(c2) SELECT c2 FROM t6 WHERE c1 >= 6;
--sorted_result
SELECT * FROM t8;
DROP TABLE t6,t7,t8;

# Test 'INSERT SET'
INSERT INTO t2 SET c3=5,c4=6;
SET SQL_MODE=STRICT_ALL_TABLES;
--error ER_NO_DEFAULT_FOR_FIELD
INSERT INTO t2 SET c3=5,c4=6;
SET SQL_MODE='';

# Test insert allowable bigger values to 'INTEGER' columns
INSERT INTO t1 VALUES(0,-2147483648,1,2,3,4,5),(255,-8388608,6,7,8,9,10),(65535,-32768,11,12,13,14,15),(16777215,-128,16,17,18,19,20),(4294967295,2147483647,21,22,23,24,25);
INSERT INTO t2 VALUES(0,-2147483648,1,2,3,4,5),(255,-8388608,6,7,8,9,10),(65535,-32768,11,12,13,14,15),(16777215,-128,16,17,18,19,20),(4294967295,2147483647,21,22,23,24,25);
INSERT INTO t3 VALUES(0,-2147483648,1,2,3,4,5),(255,-8388608,6,7,8,9,10),(65535,-32768,11,12,13,14,15),(16777215,-128,16,17,18,19,20),(4294967295,2147483647,21,22,23,24,25);
INSERT INTO t4 VALUES(-2147483648,0,1,2,3,4,5,5),(-8388608,255,6,7,8,9,10,10),(-32768,65535,11,12,13,14,15,15),(-128,16777215,16,17,18,19,20,20),(2147483647,4294967295,21,22,23,24,25,25);
INSERT INTO t5 VALUES(0,-2147483648,1,2,3,4,5,5),(255,-8388608,6,7,8,9,10,10),(65535,-32768,11,12,13,14,15,15),(16777215,-128,16,17,18,19,20,20),(4294967295,2147483647,21,22,23,24,25,25);
--error ER_DUP_ENTRY
INSERT INTO t4(c2,c3) VALUES(31,32) /* tries to increment out of range */;
--disable_warnings
INSERT INTO t5(c2,c3) VALUES(33,34) /* tries to increment out of range */;
--enable_warnings
--sorted_result
SELECT * FROM t4;

# Insert signed value to unsigned 'INTEGER', outside range values to signed/unsigned columns
INSERT INTO t1 VALUES(-255,-2147483649,26,27,28,29,30),(4294967296,2147483648,31,32,33,34,35);
INSERT INTO t2 VALUES(-255,-2147483649,26,27,28,29,30),(4294967296,2147483648,31,32,33,34,35);
INSERT INTO t3 VALUES(-255,-2147483649,26,27,28,29,30),(4294967296,2147483648,31,32,33,34,35);

# Now select using various table access methods
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = -2147483648;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 2147483647;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 4294967295 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 4294967295 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,4294967295) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 4294967295 AND c6 = 9 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = -2147483649;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = -255 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> -255 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 2147483648;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 4294967296 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN -255 AND 4294967296 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (-255,4294967296) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -255 AND c1 < 4294967296 AND c6 = 29 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 0;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -2147483648 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 4294967295;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = 2147483647 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483648 AND 2147483647 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-2147483648,2147483647) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -2147483648 AND c2 < 2147483647 AND c7 = 20 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = -255;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -2147483649 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -2147483649 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -2147483649 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -2147483649 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -2147483649 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -2147483649 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -2147483649 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -2147483649 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -2147483649 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -2147483649 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 4294967296;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = 2147483648 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -2147483649 AND 2147483648 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-2147483649,2147483648) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -2147483649 AND c2 < 2147483648 AND c7 = 30 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
DROP TABLE t1,t2,t3,t4,t5;

######## Running INSERT tests for BIGINT ########

# Create tables
CREATE TABLE t1(c1 BIGINT UNSIGNED NOT NULL, c2 BIGINT NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 INTEGER);
CREATE TABLE t2(c1 BIGINT UNSIGNED NOT NULL, c2 BIGINT NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 INTEGER, PRIMARY KEY(c1,c6));
CREATE TABLE t3(c1 BIGINT UNSIGNED NOT NULL, c2 BIGINT NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 INTEGER, index idx(c2,c7));

# Insert some rows with targeted values 
INSERT INTO t1 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);
INSERT INTO t2 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);
INSERT INTO t3 VALUES(101,-102,103,104,105,106,107),(108,-109,110,111,112,113,114),(115,-116,117,118,119,120,121),(122,-123,124,125,126,127,128);

# Insert duplicates for parts of the clustered key
INSERT INTO t2 VALUES(108,-109,110,111,112,101,114),(108,-109,110,111,112,102,114),(102,-109,110,111,112,113,114),(103,-109,110,111,112,113,114);
INSERT INTO t3 VALUES(101,-102,103,104,105,106,101),(101,-102,103,104,105,106,102),(101,-103,103,104,105,106,107),(101,-104,103,104,105,106,107);

# Insert permissible NULLs
INSERT INTO t1 VALUES(105,NULL,102,103,104,105,106);
INSERT INTO t2 VALUES(105,NULL,102,103,104,105,106);
INSERT INTO t3 VALUES(105,NULL,102,103,104,105,106);

# Insert empty string ''
INSERT INTO t1 VALUES('','',17,18,19,20,21);
INSERT INTO t2 VALUES('','',17,18,19,20,21);
INSERT INTO t3 VALUES('','',17,18,19,20,21);

# Insert negative value to unsigned integer, positive value to signed integer, zero values
INSERT INTO t1 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t2 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t3 VALUES(-1,124,22,23,24,25,26);
INSERT INTO t1 VALUES(0,124,27,28,29,30,31);
INSERT INTO t2 VALUES(0,124,27,28,29,30,31);
INSERT INTO t3 VALUES(0,124,27,28,29,30,31);
INSERT INTO t1 VALUES(0,0,32,32,34,35,36);
INSERT INTO t2 VALUES(0,0,32,32,34,35,36);
INSERT INTO t3 VALUES(0,0,32,32,34,35,36);

# Insert non-numeric value to numeric column, would be converted to 0 or numeric value (if contains part)
INSERT INTO t1 VALUES('101.34 a','a',37,38,39,40,41);
INSERT INTO t2 VALUES('101.34 a','a',37,38,39,40,41);
INSERT INTO t3 VALUES('101.34 a','a',37,38,39,40,41);

--sorted_result
SELECT * FROM t1;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t1;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t1;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t2;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t2;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t3;
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) as max_value, sum(c2) as sum, avg(c2) as avg FROM t3;

# Now select using various table access methods (full table scan, range scan, index scan etc.)
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 102;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 108 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 108 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 101 AND 115 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (101,115) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 101 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 102;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 115 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,115) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 115 AND c6 = 113 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 108;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -102 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -102 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -123 AND 0 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-123,0) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -123 AND c2 < 0 AND c7 = 121 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 108;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = NULL ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN NULL AND -123 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (NULL,-123) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= NULL AND c2 < -123 AND c7 = 121 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;

# Using index for group-by
--sorted_result
SELECT c2 FROM t3 GROUP BY c2;
--sorted_result
SELECT DISTINCT c2 FROM t3;
--sorted_result
SELECT c2,MIN(c7) FROM t3 GROUP BY c2;

# Create tables with AUTO_INCREMENT columns and unique indexes
CREATE TABLE t4(c1 BIGINT SIGNED NOT NULL AUTO_INCREMENT, c2 BIGINT UNSIGNED NULL, c3 BIGINT UNSIGNED NOT NULL, c4 TINYINT , c5 SMALLINT, c6 MEDIUMINT, c7 INT, c8 INTEGER, PRIMARY KEY(c1), UNIQUE INDEX idx(c2,c3));
CREATE TABLE t5(c1 BIGINT UNSIGNED NOT NULL AUTO_INCREMENT, c2 BIGINT SIGNED NOT NULL, c3 BIGINT SIGNED NOT NULL, c4 TINYINT, c5 SMALLINT, c6 MEDIUMINT, c7 INT, c8 INTEGER, PRIMARY KEY(c1,c2), UNIQUE INDEX(c3));

# Test with AUTO_INCREMENT columns
# Insert some rows
INSERT INTO t4 VALUES(-101,102,103,104,105,106,107,108),(-108,109,110,111,112,113,114,115);
INSERT INTO t5 VALUES(101,-102,-103,-104,105,106,107,108),(108,-109,-110,111,112,113,114,115);
# Insert 0 values, increments the value
INSERT INTO t4 VALUES(0,32,33,34,35,36,37,38);
--let $auto_value=`SELECT LAST_INSERT_ID()=1 AND c1=1 FROM t4 WHERE c2=32 AND c3=33;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on 0"
}
INSERT INTO t5 VALUES(0,-32,-32,33,34,35,36,37);
--let $auto_value=`SELECT LAST_INSERT_ID()=109 AND c1=109 FROM t5 WHERE c2=-32 AND c3=-32;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on 0"
}
# Insert NULL,  no error, increments the value
INSERT INTO t4(c1,c3,c4) VALUES(NULL,104,LAST_INSERT_ID());
--let $auto_value=`SELECT LAST_INSERT_ID()=2 AND c1=2 FROM t4 WHERE c3=104;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on NULL"
}
INSERT INTO t5(c1,c3,c4) VALUES(NULL,-104,LAST_INSERT_ID());
--let $auto_value=`SELECT LAST_INSERT_ID()=110 AND c1=110 FROM t5 WHERE c3=-104;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on NULL"
}
# Insert empty strings, increments the value 
INSERT INTO t4 VALUES('','',17,18,19,20,21,22);
--let $auto_value=`SELECT LAST_INSERT_ID()=3 AND c1=3 FROM t4 WHERE c2=0 AND c3=17;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on ''"
}
INSERT INTO t5 VALUES('','',-17,18,19,20,21,22);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=111 FROM t5 WHERE c2=0 AND c3=-17;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on ''"
}
# Insert strings with numeric part, inserts the numeric part of the string
INSERT INTO t4 VALUES('102.34 a','a','a',37,38,39,40,41);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=102 FROM t4 WHERE c2=0 AND c3=0;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on invalid strings with numeric part"
}
INSERT INTO t5 VALUES('102.34 a','a','a',37,38,39,40,41);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=102 FROM t5 WHERE c2=0 AND c3=0;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well on invalid strings with numeric part"
}
# Insert negative values unsigned & positive value to signed
INSERT INTO t4 VALUES(4,7,8,9,10,11,12,13);
--let $auto_value=`SELECT LAST_INSERT_ID()=111 AND c1=4 FROM t4 WHERE c2=7 AND c3=8;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work on negative value"
}
INSERT INTO t5 VALUES(-1,-1,-1,8,9,10,11,12);
--let $auto_value=`SELECT LAST_INSERT_ID()=112 AND c1=112 FROM t5 WHERE c2=-1 AND c3=-1;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work for negative value on unsigned"
}
# Insert some rows with no values assigned to AUTO_INCREMENT columns, increments value
INSERT INTO t4(c2,c3,c4,c5,c6,c7,c8) VALUES(101,102,104,105,106,107,108),(108,109,111,112,113,114,115),(115,116,118,119,120,121,122),(122,123,125,126,127,128,128);
--let $auto_value=`SELECT LAST_INSERT_ID()=103 AND c1=103 FROM t4 WHERE c2=101 AND c3=102;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well"
}
INSERT INTO t5(c2,c3,c4,c5,c6,c7,c8) VALUES(-101,-102,104,105,106,107,108),(-108,-109,111,112,113,114,115),(-115,-116,118,119,120,121,122),(-122,-123,125,126,127,128,128);
--let $auto_value=`SELECT LAST_INSERT_ID()=113 AND c1=113 FROM t5 WHERE c2=-101 AND c3=-102;`
--if (!$auto_value) {
--die "AUTO_INCREMENT didn't work well"
}

SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t4;
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) as max_value, sum(c1) as sum, avg(c1) as avg FROM t5;
# Now select using various table access methods on auto-incremented pk value(full table scan, range scan, index scan etc.)
## Full table scan ##
--sorted_result
SELECT * FROM t4;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t4;
--sorted_result
SELECT * FROM t4 WHERE c4 = 104;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t4;

## Backward index scan, covering ##
SELECT c1 FROM t4 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 = 1 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <> 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 > 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 < 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <= 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <=> 1 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t4;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t4;
--sorted_result
SELECT * FROM t4 WHERE c4 = 104;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t4;

## Backward index scan, covering ##
SELECT c1 FROM t4 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t4 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 = 0 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <> 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 > 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 < 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <= 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 <=> 0 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 BETWEEN -101 AND 4 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IN (-101,4) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 >= -101 AND c1 < 4 AND c3 = 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t4 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t5;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t5;
--sorted_result
SELECT * FROM t5 WHERE c5 = 112;

## Forward index scan, covering ##
--sorted_result
SELECT c1 FROM t5;

## Backward index scan, covering ##
SELECT c1 FROM t5 ORDER BY c1 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t5 ORDER BY c1 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t5 ORDER BY c1 DESC LIMIT 2;

## ref type access
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 = 109 ORDER BY c1 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <> 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 > 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 >= 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 < 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <= 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 <=> 109 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 BETWEEN 0 AND 112 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IN (0,112) ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 >= 0 AND c1 < 112 AND c2 = -108 ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IS NULL ORDER BY c1 DESC LIMIT 2;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 DESC;
SELECT * FROM t5 WHERE c1 IS NOT NULL ORDER BY c1 DESC LIMIT 2;

# Index-merge access
--sorted_result
SELECT * FROM t4 WHERE c1=1 OR c2=109;

# Using index for group-by
--sorted_result
SELECT c2 FROM t4 GROUP BY c2;
--sorted_result
SELECT DISTINCT c2 FROM t4;
--sorted_result
SELECT c2,MIN(c3) FROM t4 GROUP BY c2;

# Test Error conditions- NULL into not null col, PK constraint violation, Unique constraint violation

# Insert NULL to non-null column
--error ER_BAD_NULL_ERROR
INSERT INTO t2(c1) VALUES(NULL);
--error ER_BAD_NULL_ERROR 
INSERT INTO t3 VALUES(NULL,1,2,3,4,5,6);
--error ER_BAD_NULL_ERROR
INSERT INTO t4(c3) VALUES(NULL);
--error ER_BAD_NULL_ERROR
INSERT INTO t5(c3) VALUES(NULL);

# Insert duplicate value to pk
--error ER_DUP_ENTRY
INSERT INTO t4(c1,c3) VALUES(-101,105) /*throws error as the row with c1=-101 exists */;

# Insert duplicate value to clustered pk, throws error
--error ER_DUP_ENTRY 
INSERT INTO t2(c1,c6) VALUES(101,106) /*throws error as the row with c1=101,c6=106 exists */;
--error ER_DUP_ENTRY
INSERT INTO t5(c1,c2,c3) VALUES(101,-102,4) /*throws error as the row with c1=101,c2=-102 exists */;

# Insert duplicate value to unique column, throws error
--error ER_DUP_ENTRY
INSERT INTO t5(c3) VALUES(-103) /*throws error as the row with c3=-103 exists */;
--error ER_DUP_ENTRY
# Insert duplicate value to clustered unique column, throws error
INSERT INTO t4(c2,c3) VALUES(102,103) /*throws error as the row with c2=102,c3=103 exists */;

# Test 'INSERT INTO SELECT FROM' 
CREATE TABLE t6(c1 BIGINT UNSIGNED NOT NULL, c2 BIGINT NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 INTEGER);
INSERT INTO t6 SELECT * FROM t2 where c1 >= 109;
--sorted_result
SELECT * FROM t6;
TRUNCATE TABLE t6;
INSERT INTO t6 SELECT * FROM t3 where c2 <= -116;
--sorted_result
SELECT * FROM t6;
TRUNCATE TABLE t6;
# Test 'INSERT INTO SELECT FROM' primary key constraint violation 
CREATE TABLE t7(c1 BIGINT UNSIGNED NOT NULL PRIMARY KEY, c2 BIGINT NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 INTEGER);
CREATE TABLE t8(c1 BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY, c2 BIGINT NULL, c3 TINYINT, c4 SMALLINT , c5 MEDIUMINT, c6 INT, c7 INTEGER);
INSERT INTO t6 VALUES(1,-2,2,2,2,2,2),(1,-3,3,3,3,3,3);
INSERT INTO t7(c1) SELECT t6.c1 FROM t6 LIMIT 1;
--sorted_result
SELECT * FROM t7;
INSERT INTO t8(c1) SELECT t6.c1 FROM t6 LIMIT 1;
--sorted_result
SELECT * FROM t8;
--error ER_DUP_ENTRY
INSERT INTO t7(c1) SELECT t6.c1 FROM t6 /* throws error as t6 contains duplicate rows for c1=1 */;
--error ER_DUP_ENTRY
INSERT INTO t8(c1) SELECT t6.c1 FROM t6 /* throws error as t6 contains duplicate rows for c1=1 */;
TRUNCATE TABLE t6;

# Test 'INSERT INTO SELECT FROM' with AUTO_INCREMENT and inserting 0,NULL
INSERT INTO t6 VALUES(5,-1,1,1,1,1,1),(6,-2,2,2,2,2,2),(0,-4,4,4,4,4,4),(NULL,-5,5,5,5,5,5);
INSERT INTO t8 SELECT * FROM t6;
SELECT LAST_INSERT_ID() AS last_insert_id;
--sorted_result
SELECT * FROM t8;
TRUNCATE TABLE t8;
INSERT INTO t8(c2) SELECT c2 FROM t6 WHERE c1 >= 6;
--sorted_result
SELECT * FROM t8;
DROP TABLE t6,t7,t8;

# Test 'INSERT SET'
INSERT INTO t2 SET c3=5,c4=6;
SET SQL_MODE=STRICT_ALL_TABLES;
--error ER_NO_DEFAULT_FOR_FIELD
INSERT INTO t2 SET c3=5,c4=6;
SET SQL_MODE='';

# Test insert allowable bigger values to 'BIGINT' columns
INSERT INTO t1 VALUES(0,-9223372036854775808,1,2,3,4,5),(255,-2147483648,6,7,8,9,10),(65535,-8388608,11,12,13,14,15),(16777215,-32768,16,17,18,19,20),(4294967295,-128,21,22,23,24,25),(18446744073709551615,9223372036854775807,26,27,28,29,30);
INSERT INTO t2 VALUES(0,-9223372036854775808,1,2,3,4,5),(255,-2147483648,6,7,8,9,10),(65535,-8388608,11,12,13,14,15),(16777215,-32768,16,17,18,19,20),(4294967295,-128,21,22,23,24,25),(18446744073709551615,9223372036854775807,26,27,28,29,30);
INSERT INTO t3 VALUES(0,-9223372036854775808,1,2,3,4,5),(255,-2147483648,6,7,8,9,10),(65535,-8388608,11,12,13,14,15),(16777215,-32768,16,17,18,19,20),(4294967295,-128,21,22,23,24,25),(18446744073709551615,9223372036854775807,26,27,28,29,30);
INSERT INTO t4 VALUES(-9223372036854775808,0,1,2,3,4,5,5),(-2147483648,255,6,7,8,9,10,10),(-8388608,65535,11,12,13,14,15,15),(-32768,16777215,16,17,18,19,20,20),(-128,4294967295,21,22,23,24,25,25),(9223372036854775807,18446744073709551615,26,27,28,29,30,30);
INSERT INTO t5 VALUES(0,-9223372036854775808,1,2,3,4,5,5),(255,-2147483648,6,7,8,9,10,10),(65535,-8388608,11,12,13,14,15,15),(16777215,-32768,16,17,18,19,20,20),(4294967295,-128,21,22,23,24,25,25),(18446744073709551615,9223372036854775807,26,27,28,29,30,30);
--error ER_DUP_ENTRY
INSERT INTO t4(c2,c3) VALUES(31,32) /* tries to increment out of range */;
--error ER_AUTOINC_READ_FAILED
INSERT INTO t5(c2,c3) VALUES(33,34) /* tries to increment out of range */;
--sorted_result
SELECT * FROM t4;

# Insert signed value to unsigned 'BIGINT', outside range values to signed/unsigned columns
INSERT INTO t1 VALUES(-128,-9223372036854775809,31,32,33,34,35),(18446744073709551616,9223372036854775808,36,37,38,39,40);
INSERT INTO t2 VALUES(-128,-9223372036854775809,31,32,33,34,35),(18446744073709551616,9223372036854775808,36,37,38,39,40);
INSERT INTO t3 VALUES(-128,-9223372036854775809,31,32,33,34,35),(18446744073709551616,9223372036854775808,36,37,38,39,40);

# Now select using various table access methods
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = -9223372036854775808;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 0 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 18446744073709551615 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 18446744073709551615 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,18446744073709551615) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,18446744073709551615) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 18446744073709551615 AND c6 = 14 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 18446744073709551615 AND c6 = 14 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 0 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 18446744073709551615 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 18446744073709551615 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,18446744073709551615) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,18446744073709551615) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 18446744073709551615 AND c6 = 14 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 18446744073709551615 AND c6 = 14 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 9223372036854775807;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = 18446744073709551615 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 18446744073709551615 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 18446744073709551615 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 18446744073709551615 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 18446744073709551615 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 18446744073709551615 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 18446744073709551615 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 18446744073709551615 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 18446744073709551615 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 18446744073709551615 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 18446744073709551615 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 18446744073709551615 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 18446744073709551615 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 18446744073709551615 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 18446744073709551615 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 18446744073709551615 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 18446744073709551615 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 18446744073709551615 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,18446744073709551615) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (0,18446744073709551615) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 18446744073709551615 AND c6 = 14 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 18446744073709551615 AND c6 = 14 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 18446744073709551615 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 18446744073709551615 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 18446744073709551615 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 18446744073709551615 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 18446744073709551615 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 18446744073709551615 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 18446744073709551615 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 18446744073709551615 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 18446744073709551615 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 18446744073709551615 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 18446744073709551615 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 18446744073709551615 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 18446744073709551615 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN 0 AND 18446744073709551615 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (0,18446744073709551615) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (0,18446744073709551615) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 18446744073709551615 AND c6 = 14 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 0 AND c1 < 18446744073709551615 AND c6 = 14 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = -9223372036854775809;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
SELECT * FROM t2 WHERE c1 = -128 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = -128 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = -128 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = -128 ORDER BY c1,c6 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> -128 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> -128 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > -128 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > -128 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -128 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -128 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < -128 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < -128 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= -128 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= -128 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> -128 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> -128 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -128 AND 18446744073709551616 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN -128 AND 18446744073709551616 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-128,18446744073709551616) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (-128,18446744073709551616) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -128 AND c1 < 18446744073709551616 AND c6 = 34 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -128 AND c1 < 18446744073709551616 AND c6 = 34 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> -128 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> -128 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > -128 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > -128 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -128 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -128 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < -128 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < -128 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= -128 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= -128 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> -128 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> -128 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -128 AND 18446744073709551616 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN -128 AND 18446744073709551616 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-128,18446744073709551616) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (-128,18446744073709551616) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -128 AND c1 < 18446744073709551616 AND c6 = 34 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -128 AND c1 < 18446744073709551616 AND c6 = 34 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t2;
--sorted_result
SELECT count(*) as total_rows, min(c1) as min_value, max(c1) FROM t2;
--sorted_result
SELECT * FROM t2 WHERE c2 = 9223372036854775808;

## Forward index scan, covering ##
--sorted_result
SELECT c1,c6 FROM t2;

## Backward index scan, covering ##
SELECT c1,c6 FROM t2 ORDER BY c1,c6 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t2 ORDER BY c1,c6 DESC LIMIT 2;

## ref type access
--disable_warnings
SELECT * FROM t2 WHERE c1 = 18446744073709551616 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 = 18446744073709551616 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 = 18446744073709551616 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 = 18446744073709551616 ORDER BY c1,c6 DESC LIMIT 2;
--enable_warnings

## Range access, ordered ##
SELECT * FROM t2 WHERE c1 <> 18446744073709551616 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <> 18446744073709551616 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 > 18446744073709551616 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 > 18446744073709551616 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 18446744073709551616 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= 18446744073709551616 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 < 18446744073709551616 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 < 18446744073709551616 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 18446744073709551616 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <= 18446744073709551616 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 18446744073709551616 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 <=> 18446744073709551616 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -128 AND 18446744073709551616 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 BETWEEN -128 AND 18446744073709551616 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-128,18446744073709551616) ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IN (-128,18446744073709551616) ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -128 AND c1 < 18446744073709551616 AND c6 = 34 ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 >= -128 AND c1 < 18446744073709551616 AND c6 = 34 ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t2 WHERE c1 <> 18446744073709551616 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <> 18446744073709551616 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 > 18446744073709551616 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 > 18446744073709551616 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= 18446744073709551616 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= 18446744073709551616 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 < 18446744073709551616 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 < 18446744073709551616 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <= 18446744073709551616 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <= 18446744073709551616 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 <=> 18446744073709551616 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 <=> 18446744073709551616 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 BETWEEN -128 AND 18446744073709551616 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 BETWEEN -128 AND 18446744073709551616 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IN (-128,18446744073709551616) ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IN (-128,18446744073709551616) ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 >= -128 AND c1 < 18446744073709551616 AND c6 = 34 ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 >= -128 AND c1 < 18446744073709551616 AND c6 = 34 ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NULL ORDER BY c1,c6 DESC LIMIT 2;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC;
SELECT * FROM t2 WHERE c1 IS NOT NULL ORDER BY c1,c6 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 0;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775808 AND 9223372036854775807 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775808 AND 9223372036854775807 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775808,9223372036854775807) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775808,9223372036854775807) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -9223372036854775808 AND c2 < 9223372036854775807 AND c7 = 20 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -9223372036854775808 AND c2 < 9223372036854775807 AND c7 = 20 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775808 AND 9223372036854775807 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775808 AND 9223372036854775807 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775808,9223372036854775807) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775808,9223372036854775807) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -9223372036854775808 AND c2 < 9223372036854775807 AND c7 = 20 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -9223372036854775808 AND c2 < 9223372036854775807 AND c7 = 20 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 18446744073709551615;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = 9223372036854775807 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = 9223372036854775807 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = 9223372036854775807 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = 9223372036854775807 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> 9223372036854775807 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> 9223372036854775807 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > 9223372036854775807 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > 9223372036854775807 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 9223372036854775807 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= 9223372036854775807 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < 9223372036854775807 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < 9223372036854775807 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 9223372036854775807 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= 9223372036854775807 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 9223372036854775807 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> 9223372036854775807 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775808 AND 9223372036854775807 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775808 AND 9223372036854775807 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775808,9223372036854775807) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775808,9223372036854775807) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -9223372036854775808 AND c2 < 9223372036854775807 AND c7 = 20 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -9223372036854775808 AND c2 < 9223372036854775807 AND c7 = 20 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> 9223372036854775807 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> 9223372036854775807 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > 9223372036854775807 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > 9223372036854775807 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 9223372036854775807 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= 9223372036854775807 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < 9223372036854775807 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < 9223372036854775807 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 9223372036854775807 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= 9223372036854775807 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 9223372036854775807 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> 9223372036854775807 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775808 AND 9223372036854775807 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775808 AND 9223372036854775807 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775808,9223372036854775807) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775808,9223372036854775807) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -9223372036854775808 AND c2 < 9223372036854775807 AND c7 = 20 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -9223372036854775808 AND c2 < 9223372036854775807 AND c7 = 20 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = -128;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = -9223372036854775809 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = -9223372036854775809 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = -9223372036854775809 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = -9223372036854775809 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> -9223372036854775809 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> -9223372036854775809 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > -9223372036854775809 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > -9223372036854775809 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -9223372036854775809 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -9223372036854775809 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < -9223372036854775809 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < -9223372036854775809 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -9223372036854775809 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= -9223372036854775809 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -9223372036854775809 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> -9223372036854775809 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775809 AND 9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775809 AND 9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775809,9223372036854775808) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775809,9223372036854775808) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -9223372036854775809 AND c2 < 9223372036854775808 AND c7 = 35 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -9223372036854775809 AND c2 < 9223372036854775808 AND c7 = 35 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> -9223372036854775809 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> -9223372036854775809 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > -9223372036854775809 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > -9223372036854775809 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -9223372036854775809 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -9223372036854775809 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < -9223372036854775809 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < -9223372036854775809 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= -9223372036854775809 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= -9223372036854775809 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> -9223372036854775809 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> -9223372036854775809 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775809 AND 9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775809 AND 9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775809,9223372036854775808) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775809,9223372036854775808) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -9223372036854775809 AND c2 < 9223372036854775808 AND c7 = 35 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -9223372036854775809 AND c2 < 9223372036854775808 AND c7 = 35 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
## Full table scan ##
--sorted_result
SELECT * FROM t3;
--sorted_result
SELECT count(*) as total_rows, min(c2) as min_value, max(c2) FROM t3;
--sorted_result
SELECT * FROM t3 WHERE c1 = 18446744073709551616;

## Forward index scan, covering ##
--sorted_result
SELECT c2,c7 FROM t3;

## Backward index scan, covering ##
SELECT c2,c7 FROM t3 ORDER BY c2,c7 DESC;

## Forward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 LIMIT 2;

## Backward index scan, non-covering ##
SELECT * FROM t3 ORDER BY c2,c7 DESC LIMIT 2;

## ref type access
SELECT * FROM t3 WHERE c2 = 9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 = 9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 = 9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 = 9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;

## Range access, ordered ##
SELECT * FROM t3 WHERE c2 <> 9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <> 9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 > 9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 > 9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= 9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 < 9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 < 9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <= 9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 <=> 9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775809 AND 9223372036854775808 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775809 AND 9223372036854775808 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775809,9223372036854775808) ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775809,9223372036854775808) ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -9223372036854775809 AND c2 < 9223372036854775808 AND c7 = 35 ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 >= -9223372036854775809 AND c2 < 9223372036854775808 AND c7 = 35 ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 LIMIT 2;

## Range access, backwards scan ##
SELECT * FROM t3 WHERE c2 <> 9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <> 9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 > 9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 > 9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= 9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= 9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 < 9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 < 9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <= 9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <= 9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 <=> 9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 <=> 9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775809 AND 9223372036854775808 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 BETWEEN -9223372036854775809 AND 9223372036854775808 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775809,9223372036854775808) ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IN (-9223372036854775809,9223372036854775808) ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 >= -9223372036854775809 AND c2 < 9223372036854775808 AND c7 = 35 ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 >= -9223372036854775809 AND c2 < 9223372036854775808 AND c7 = 35 ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NULL ORDER BY c2,c7 DESC LIMIT 2;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC;
SELECT * FROM t3 WHERE c2 IS NOT NULL ORDER BY c2,c7 DESC LIMIT 2;
DROP TABLE t1,t2,t3,t4,t5;
SET sql_mode=default;
