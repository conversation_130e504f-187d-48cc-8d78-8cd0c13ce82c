# ==== Purpose ====
#
# Check that the execution of SHOW REPLICA STATUS command is not blocked when IO
# thread is blocked waiting for disk space.
#
# ==== Implementation ====
#
# There are two steps on this test case:
#
# 1st Step
#
# Simulate a scenario where IO thread is waiting for disk space while writing
# into the relay log. Execute SHOW REPLICA STATUS command after IO thread is
# blocked waiting for space. The command should not be blocked.
#
# 2nd Step
#
# The slave server should respond promptly when asked to shutdown. No warnings
# about the "STOP REPLICA" waiting for disk space should be generated.
#
# ==== References ====
#
# WL#10406: Improve usability when receiver thread is waiting for disk space
# Bug#21753696: MAKE SHOW SLAVE STATUS NON BLOCKING IF IO THREAD WAITS FOR
#               DISK SPACE
#
###############################################################################
--source include/have_debug.inc
--source include/rpl/init_source_replica.inc

# Generate events to be replicated to the slave
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES(1);
--source include/rpl/sync_to_replica.inc

# Those errors will only happen in the slave
CALL mtr.add_suppression("Disk is full writing");
CALL mtr.add_suppression("Retry in 60 secs");
CALL mtr.add_suppression("Relay log write failure");
CALL mtr.add_suppression("Recovery from source pos");
CALL mtr.add_suppression("Relay log recovery on channel with GTID_ONLY=1*");
CALL mtr.add_suppression("Waiting until I/O thread .* finish writing to disk before stopping");

--echo #
--echo # Step 1 - Monitor and kill I/O thread while waiting for disk space
--echo #

--let $io_id=`SELECT PROCESSLIST_ID FROM performance_schema.threads WHERE NAME = 'thread/sql/replica_io'`

# Set the debug option that will simulate disk full
--let $debug_point=simulate_io_thd_wait_for_disk_space
--source include/add_debug_point.inc

# Generate events to be replicated to the slave
--source include/rpl/connection_source.inc
INSERT INTO t1 VALUES(2);

--source include/rpl/connection_replica.inc
# Wait until IO thread is waiting for disk space
# Notice that this is performed by querying SHOW REPLICA STATUS
--let $slave_param= Replica_IO_State
--let $slave_param_value= Waiting for disk space
--source include/rpl/wait_for_replica_status.inc

# performance_schema.threads and information_schema.processlist checks
--let $io_state=`SELECT PROCESSLIST_STATE FROM performance_schema.threads WHERE NAME = 'thread/sql/replica_io'`
--let $assert_text= I/O thread PROCESSLIST_STATE should be Waiting for disk space at performance_schema.threads
--let $assert_cond= "$io_state" = "Waiting for disk space"
--source include/assert.inc

--let $io_state=`SELECT STATE FROM information_schema.processlist WHERE ID = $io_id`
--let $assert_text= I/O thread STATE should be Waiting for disk space at information_schema.processlist
--let $assert_cond= "$io_state" = "Waiting for disk space"
--source include/assert.inc

# Get the relay log file name, also using SHOW REPLICA STATUS
--let $relay_log_file= query_get_value(SHOW REPLICA STATUS, Relay_Log_File, 1)

# STOP REPLICA IO_THREAD should be blocked
--source include/rpl/connection_replica1.inc
--send STOP REPLICA IO_THREAD

# But the KILL on it should unblock it
--source include/rpl/connection_replica.inc
SET DEBUG_SYNC="now WAIT_FOR reached_stopping_io_thread";
--replace_result $io_id IO_THREAD
--eval KILL $io_id

--source include/rpl/connection_replica1.inc
--reap

--source include/rpl/connection_replica.inc
--let $slave_io_errno= convert_error(ER_REPLICA_RELAY_LOG_WRITE_FAILURE)
--source include/rpl/wait_for_receiver_error.inc

# Restore the debug options to "simulate" freed space on disk
--source include/remove_debug_point.inc

# There should be a message in the error log of the slave stating
# that it was waiting for space to write on the relay log.
--let $assert_file=$MYSQLTEST_VARDIR/log/mysqld.2.err
# Grep only after the message that the I/O thread has started
--let $assert_only_after= Replica I/O .* connected to source .*replication started in log .* at position
--let $assert_count= 1
--let $assert_select=Disk is full writing .*$relay_log_file.* No space left on device
--let $assert_text= Found the disk full error message on the slave
--source include/assert_grep.inc

# There should be a message in the error log of the slave stating
# that it truncate the relay log file.
--let $assert_file=$MYSQLTEST_VARDIR/log/mysqld.2.err
# Grep only after the message that the I/O thread has started
--let $assert_only_after= Replica receiver thread.* connected to source .* with server_uuid
--let $assert_count= 1
--let $assert_select= Relaylog file .* size was .* but was truncated at
--let $assert_text= Found the relay log truncate message on the slave
--source include/assert_grep.inc

# Start the I/O thread to let the slave to sync
--source include/rpl/start_receiver.inc
--source include/rpl/connection_source.inc
--source include/rpl/sync_to_replica.inc

--echo #
--echo # Step 2 - Restart the slave server while I/O thread is waiting for disk space
--echo #

# Set the debug option that will simulate disk full
--let $debug_point=simulate_io_thd_wait_for_disk_space
--source include/add_debug_point.inc

# Generate events to be replicated to the slave
--source include/rpl/connection_source.inc
INSERT INTO t1 VALUES(3);
INSERT INTO t1 VALUES(4);
INSERT INTO t1 VALUES(5);
INSERT INTO t1 VALUES(6);
INSERT INTO t1 VALUES(7);
INSERT INTO t1 VALUES(8);
INSERT INTO t1 VALUES(9);

--source include/rpl/connection_replica.inc
# Wait until IO thread is waiting for disk space
# Notice that this is performed by querying SHOW REPLICA STATUS
--let $slave_param= Replica_IO_State
--let $slave_param_value= Waiting for disk space
--source include/rpl/wait_for_replica_status.inc

# Restart the slave (relay log recovery is turned on and log error verbosity is 3)
--source include/rpl/stop_applier.inc
--let $rpl_server_number= 2
--let $rpl_server_parameters=
--source include/rpl/restart_server.inc
--source include/rpl/start_replica.inc

# There should not be a message in the error log of the slave stating
# that it was waiting for space to stop the I/O thread.
--let $assert_file=$MYSQLTEST_VARDIR/log/mysqld.2.err
# Grep only after the message that the I/O thread has started
--let $assert_only_after= Replica receiver thread.* connected to source .* with server_uuid
--let $assert_count= 0
--let $assert_select=Waiting until I/O thread for channel
--let $assert_text= Found no warning messages about I/O thread waiting before stopping
--source include/assert_grep.inc

# Cleanup
--source include/rpl/connection_source.inc
DROP TABLE t1;
--source include/rpl/sync_to_replica.inc
--source include/rpl/deinit.inc
