# Copyright (c) 2021, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA

ADD_DEFINITIONS(-DSSL_TEST_DATA_DIR="${CMAKE_SOURCE_DIR}/mysql-test/std_data/")
ADD_DEFINITIONS(-DCMAKE_SOURCE_DIR="${CMAKE_SOURCE_DIR}")
FOREACH(test_file
    test_routing_direct.cc
    test_routing_router_require.cc
    test_routing_sharing.cc
    test_routing_sharing_constrained_pools.cc
    test_routing_sharing_restart.cc
    test_routing_splitting.cc
    test_routing_tracing.cc
    )
  ADD_GOOGLETEST_FILE(${CMAKE_CURRENT_SOURCE_DIR}/${test_file}
    MODULE "integration"
    LIB_DEPENDS
    routertest_helpers
    router_mysqlxclient
    router_mysqlxmessages
    mysql_protocol
    )
ENDFOREACH()

ADD_GOOGLETEST_FILE(${CMAKE_CURRENT_SOURCE_DIR}/test_routing_reuse.cc
  MODULE "integration"
  LIB_DEPENDS
  router_mysqlxclient
  router_mysqlxmessages # for TextFormatParser
  routertest_helpers
  mysql_protocol
)

IF(NOT WITH_AUTHENTICATION_CLIENT_PLUGINS)
  ADD_DEFINITIONS(-DSKIP_AUTHENTICATION_CLIENT_PLUGINS_TESTS)
ENDIF()
