/*****************************************************************************

Copyright (c) 1996, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/

/** @file include/lock0lock.ic
 The transaction lock system

 Created 5/7/1996 Heikki Tuuri
 *******************************************************/

#include "btr0cur.h"
#include "buf0buf.h"
#include "dict0dict.h"
#include "log0recv.h"
#include "page0cur.h"
#include "page0page.h"
#include "que0que.h"
#include "read0read.h"
#include "row0row.h"
#include "row0vers.h"
#include "srv0srv.h"
#include "trx0sys.h"
#include "trx0trx.h"

static inline uint64_t lock_rec_hash_value(const page_id_t &page_id) {
  return page_id.hash();
}

/** Gets the heap_no of the smallest user record on a page.
 @return heap_no of smallest user record, or PAGE_HEAP_NO_SUPREMUM */
static inline ulint lock_get_min_heap_no(
    const buf_block_t *block) /*!< in: buffer block */
{
  const page_t *page = block->frame;

  if (page_is_comp(page)) {
    return (rec_get_heap_no_new(
        page + rec_get_next_offs(page + PAGE_NEW_INFIMUM, true)));
  } else {
    return (rec_get_heap_no_old(
        page + rec_get_next_offs(page + PAGE_OLD_INFIMUM, false)));
  }
}

/** Get the lock hash table */
static inline Locks_hashtable &lock_hash_get(ulint mode) /*!< in: lock mode */
{
  if (mode & LOCK_PREDICATE) {
    return lock_sys->prdt_hash;
  } else if (mode & LOCK_PRDT_PAGE) {
    return lock_sys->prdt_page_hash;
  } else {
    return lock_sys->rec_hash;
  }
}
