Copyright (c) 2010, 2025, Oracle and/or its affiliates.


Helpers
-------

A collection of C++ utility classes useful for benchmarking:

- helpers.hpp           some tracing and debugging macros
- string_helpers.hpp    a few string convenience functions
- Properties.hpp        C++ implementation of java.util.Properties

- hrt_utils.h           high-resolution time measurement utilities
- hrt_stopwatch.h       high-resolution time stopwatch utility
- hrt_gstopwatch.h      global (=singleton) high-resolution time stopwatch
