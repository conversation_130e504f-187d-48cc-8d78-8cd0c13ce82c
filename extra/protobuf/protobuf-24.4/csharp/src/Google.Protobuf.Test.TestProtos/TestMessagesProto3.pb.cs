// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: google/protobuf/test_messages_proto3.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace ProtobufTestMessages.Proto3 {

  /// <summary>Holder for reflection information generated from google/protobuf/test_messages_proto3.proto</summary>
  public static partial class TestMessagesProto3Reflection {

    #region Descriptor
    /// <summary>File descriptor for google/protobuf/test_messages_proto3.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static TestMessagesProto3Reflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cipnb29nbGUvcHJvdG9idWYvdGVzdF9tZXNzYWdlc19wcm90bzMucHJvdG8S",
            "HXByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8zGhlnb29nbGUvcHJvdG9i",
            "dWYvYW55LnByb3RvGh5nb29nbGUvcHJvdG9idWYvZHVyYXRpb24ucHJvdG8a",
            "IGdvb2dsZS9wcm90b2J1Zi9maWVsZF9tYXNrLnByb3RvGhxnb29nbGUvcHJv",
            "dG9idWYvc3RydWN0LnByb3RvGh9nb29nbGUvcHJvdG9idWYvdGltZXN0YW1w",
            "LnByb3RvGh5nb29nbGUvcHJvdG9idWYvd3JhcHBlcnMucHJvdG8isUUKElRl",
            "c3RBbGxUeXBlc1Byb3RvMxIWCg5vcHRpb25hbF9pbnQzMhgBIAEoBRIWCg5v",
            "cHRpb25hbF9pbnQ2NBgCIAEoAxIXCg9vcHRpb25hbF91aW50MzIYAyABKA0S",
            "FwoPb3B0aW9uYWxfdWludDY0GAQgASgEEhcKD29wdGlvbmFsX3NpbnQzMhgF",
            "IAEoERIXCg9vcHRpb25hbF9zaW50NjQYBiABKBISGAoQb3B0aW9uYWxfZml4",
            "ZWQzMhgHIAEoBxIYChBvcHRpb25hbF9maXhlZDY0GAggASgGEhkKEW9wdGlv",
            "bmFsX3NmaXhlZDMyGAkgASgPEhkKEW9wdGlvbmFsX3NmaXhlZDY0GAogASgQ",
            "EhYKDm9wdGlvbmFsX2Zsb2F0GAsgASgCEhcKD29wdGlvbmFsX2RvdWJsZRgM",
            "IAEoARIVCg1vcHRpb25hbF9ib29sGA0gASgIEhcKD29wdGlvbmFsX3N0cmlu",
            "ZxgOIAEoCRIWCg5vcHRpb25hbF9ieXRlcxgPIAEoDBJgChdvcHRpb25hbF9u",
            "ZXN0ZWRfbWVzc2FnZRgSIAEoCzI/LnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMu",
            "cHJvdG8zLlRlc3RBbGxUeXBlc1Byb3RvMy5OZXN0ZWRNZXNzYWdlEk8KGG9w",
            "dGlvbmFsX2ZvcmVpZ25fbWVzc2FnZRgTIAEoCzItLnByb3RvYnVmX3Rlc3Rf",
            "bWVzc2FnZXMucHJvdG8zLkZvcmVpZ25NZXNzYWdlEloKFG9wdGlvbmFsX25l",
            "c3RlZF9lbnVtGBUgASgOMjwucHJvdG9idWZfdGVzdF9tZXNzYWdlcy5wcm90",
            "bzMuVGVzdEFsbFR5cGVzUHJvdG8zLk5lc3RlZEVudW0SSQoVb3B0aW9uYWxf",
            "Zm9yZWlnbl9lbnVtGBYgASgOMioucHJvdG9idWZfdGVzdF9tZXNzYWdlcy5w",
            "cm90bzMuRm9yZWlnbkVudW0SXAoVb3B0aW9uYWxfYWxpYXNlZF9lbnVtGBcg",
            "ASgOMj0ucHJvdG9idWZfdGVzdF9tZXNzYWdlcy5wcm90bzMuVGVzdEFsbFR5",
            "cGVzUHJvdG8zLkFsaWFzZWRFbnVtEiEKFW9wdGlvbmFsX3N0cmluZ19waWVj",
            "ZRgYIAEoCUICCAISGQoNb3B0aW9uYWxfY29yZBgZIAEoCUICCAESTAoRcmVj",
            "dXJzaXZlX21lc3NhZ2UYGyABKAsyMS5wcm90b2J1Zl90ZXN0X21lc3NhZ2Vz",
            "LnByb3RvMy5UZXN0QWxsVHlwZXNQcm90bzMSFgoOcmVwZWF0ZWRfaW50MzIY",
            "HyADKAUSFgoOcmVwZWF0ZWRfaW50NjQYICADKAMSFwoPcmVwZWF0ZWRfdWlu",
            "dDMyGCEgAygNEhcKD3JlcGVhdGVkX3VpbnQ2NBgiIAMoBBIXCg9yZXBlYXRl",
            "ZF9zaW50MzIYIyADKBESFwoPcmVwZWF0ZWRfc2ludDY0GCQgAygSEhgKEHJl",
            "cGVhdGVkX2ZpeGVkMzIYJSADKAcSGAoQcmVwZWF0ZWRfZml4ZWQ2NBgmIAMo",
            "BhIZChFyZXBlYXRlZF9zZml4ZWQzMhgnIAMoDxIZChFyZXBlYXRlZF9zZml4",
            "ZWQ2NBgoIAMoEBIWCg5yZXBlYXRlZF9mbG9hdBgpIAMoAhIXCg9yZXBlYXRl",
            "ZF9kb3VibGUYKiADKAESFQoNcmVwZWF0ZWRfYm9vbBgrIAMoCBIXCg9yZXBl",
            "YXRlZF9zdHJpbmcYLCADKAkSFgoOcmVwZWF0ZWRfYnl0ZXMYLSADKAwSYAoX",
            "cmVwZWF0ZWRfbmVzdGVkX21lc3NhZ2UYMCADKAsyPy5wcm90b2J1Zl90ZXN0",
            "X21lc3NhZ2VzLnByb3RvMy5UZXN0QWxsVHlwZXNQcm90bzMuTmVzdGVkTWVz",
            "c2FnZRJPChhyZXBlYXRlZF9mb3JlaWduX21lc3NhZ2UYMSADKAsyLS5wcm90",
            "b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMy5Gb3JlaWduTWVzc2FnZRJaChRy",
            "ZXBlYXRlZF9uZXN0ZWRfZW51bRgzIAMoDjI8LnByb3RvYnVmX3Rlc3RfbWVz",
            "c2FnZXMucHJvdG8zLlRlc3RBbGxUeXBlc1Byb3RvMy5OZXN0ZWRFbnVtEkkK",
            "FXJlcGVhdGVkX2ZvcmVpZ25fZW51bRg0IAMoDjIqLnByb3RvYnVmX3Rlc3Rf",
            "bWVzc2FnZXMucHJvdG8zLkZvcmVpZ25FbnVtEiEKFXJlcGVhdGVkX3N0cmlu",
            "Z19waWVjZRg2IAMoCUICCAISGQoNcmVwZWF0ZWRfY29yZBg3IAMoCUICCAES",
            "GAoMcGFja2VkX2ludDMyGEsgAygFQgIQARIYCgxwYWNrZWRfaW50NjQYTCAD",
            "KANCAhABEhkKDXBhY2tlZF91aW50MzIYTSADKA1CAhABEhkKDXBhY2tlZF91",
            "aW50NjQYTiADKARCAhABEhkKDXBhY2tlZF9zaW50MzIYTyADKBFCAhABEhkK",
            "DXBhY2tlZF9zaW50NjQYUCADKBJCAhABEhoKDnBhY2tlZF9maXhlZDMyGFEg",
            "AygHQgIQARIaCg5wYWNrZWRfZml4ZWQ2NBhSIAMoBkICEAESGwoPcGFja2Vk",
            "X3NmaXhlZDMyGFMgAygPQgIQARIbCg9wYWNrZWRfc2ZpeGVkNjQYVCADKBBC",
            "AhABEhgKDHBhY2tlZF9mbG9hdBhVIAMoAkICEAESGQoNcGFja2VkX2RvdWJs",
            "ZRhWIAMoAUICEAESFwoLcGFja2VkX2Jvb2wYVyADKAhCAhABElwKEnBhY2tl",
            "ZF9uZXN0ZWRfZW51bRhYIAMoDjI8LnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMu",
            "cHJvdG8zLlRlc3RBbGxUeXBlc1Byb3RvMy5OZXN0ZWRFbnVtQgIQARIaCg51",
            "bnBhY2tlZF9pbnQzMhhZIAMoBUICEAASGgoOdW5wYWNrZWRfaW50NjQYWiAD",
            "KANCAhAAEhsKD3VucGFja2VkX3VpbnQzMhhbIAMoDUICEAASGwoPdW5wYWNr",
            "ZWRfdWludDY0GFwgAygEQgIQABIbCg91bnBhY2tlZF9zaW50MzIYXSADKBFC",
            "AhAAEhsKD3VucGFja2VkX3NpbnQ2NBheIAMoEkICEAASHAoQdW5wYWNrZWRf",
            "Zml4ZWQzMhhfIAMoB0ICEAASHAoQdW5wYWNrZWRfZml4ZWQ2NBhgIAMoBkIC",
            "EAASHQoRdW5wYWNrZWRfc2ZpeGVkMzIYYSADKA9CAhAAEh0KEXVucGFja2Vk",
            "X3NmaXhlZDY0GGIgAygQQgIQABIaCg51bnBhY2tlZF9mbG9hdBhjIAMoAkIC",
            "EAASGwoPdW5wYWNrZWRfZG91YmxlGGQgAygBQgIQABIZCg11bnBhY2tlZF9i",
            "b29sGGUgAygIQgIQABJeChR1bnBhY2tlZF9uZXN0ZWRfZW51bRhmIAMoDjI8",
            "LnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8zLlRlc3RBbGxUeXBlc1By",
            "b3RvMy5OZXN0ZWRFbnVtQgIQABJdCg9tYXBfaW50MzJfaW50MzIYOCADKAsy",
            "RC5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMy5UZXN0QWxsVHlwZXNQ",
            "cm90bzMuTWFwSW50MzJJbnQzMkVudHJ5El0KD21hcF9pbnQ2NF9pbnQ2NBg5",
            "IAMoCzJELnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8zLlRlc3RBbGxU",
            "eXBlc1Byb3RvMy5NYXBJbnQ2NEludDY0RW50cnkSYQoRbWFwX3VpbnQzMl91",
            "aW50MzIYOiADKAsyRi5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMy5U",
            "ZXN0QWxsVHlwZXNQcm90bzMuTWFwVWludDMyVWludDMyRW50cnkSYQoRbWFw",
            "X3VpbnQ2NF91aW50NjQYOyADKAsyRi5wcm90b2J1Zl90ZXN0X21lc3NhZ2Vz",
            "LnByb3RvMy5UZXN0QWxsVHlwZXNQcm90bzMuTWFwVWludDY0VWludDY0RW50",
            "cnkSYQoRbWFwX3NpbnQzMl9zaW50MzIYPCADKAsyRi5wcm90b2J1Zl90ZXN0",
            "X21lc3NhZ2VzLnByb3RvMy5UZXN0QWxsVHlwZXNQcm90bzMuTWFwU2ludDMy",
            "U2ludDMyRW50cnkSYQoRbWFwX3NpbnQ2NF9zaW50NjQYPSADKAsyRi5wcm90",
            "b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMy5UZXN0QWxsVHlwZXNQcm90bzMu",
            "TWFwU2ludDY0U2ludDY0RW50cnkSZQoTbWFwX2ZpeGVkMzJfZml4ZWQzMhg+",
            "IAMoCzJILnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8zLlRlc3RBbGxU",
            "eXBlc1Byb3RvMy5NYXBGaXhlZDMyRml4ZWQzMkVudHJ5EmUKE21hcF9maXhl",
            "ZDY0X2ZpeGVkNjQYPyADKAsySC5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnBy",
            "b3RvMy5UZXN0QWxsVHlwZXNQcm90bzMuTWFwRml4ZWQ2NEZpeGVkNjRFbnRy",
            "eRJpChVtYXBfc2ZpeGVkMzJfc2ZpeGVkMzIYQCADKAsySi5wcm90b2J1Zl90",
            "ZXN0X21lc3NhZ2VzLnByb3RvMy5UZXN0QWxsVHlwZXNQcm90bzMuTWFwU2Zp",
            "eGVkMzJTZml4ZWQzMkVudHJ5EmkKFW1hcF9zZml4ZWQ2NF9zZml4ZWQ2NBhB",
            "IAMoCzJKLnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJvdG8zLlRlc3RBbGxU",
            "eXBlc1Byb3RvMy5NYXBTZml4ZWQ2NFNmaXhlZDY0RW50cnkSXQoPbWFwX2lu",
            "dDMyX2Zsb2F0GEIgAygLMkQucHJvdG9idWZfdGVzdF9tZXNzYWdlcy5wcm90",
            "bzMuVGVzdEFsbFR5cGVzUHJvdG8zLk1hcEludDMyRmxvYXRFbnRyeRJfChBt",
            "YXBfaW50MzJfZG91YmxlGEMgAygLMkUucHJvdG9idWZfdGVzdF9tZXNzYWdl",
            "cy5wcm90bzMuVGVzdEFsbFR5cGVzUHJvdG8zLk1hcEludDMyRG91YmxlRW50",
            "cnkSWQoNbWFwX2Jvb2xfYm9vbBhEIAMoCzJCLnByb3RvYnVmX3Rlc3RfbWVz",
            "c2FnZXMucHJvdG8zLlRlc3RBbGxUeXBlc1Byb3RvMy5NYXBCb29sQm9vbEVu",
            "dHJ5EmEKEW1hcF9zdHJpbmdfc3RyaW5nGEUgAygLMkYucHJvdG9idWZfdGVz",
            "dF9tZXNzYWdlcy5wcm90bzMuVGVzdEFsbFR5cGVzUHJvdG8zLk1hcFN0cmlu",
            "Z1N0cmluZ0VudHJ5El8KEG1hcF9zdHJpbmdfYnl0ZXMYRiADKAsyRS5wcm90",
            "b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMy5UZXN0QWxsVHlwZXNQcm90bzMu",
            "TWFwU3RyaW5nQnl0ZXNFbnRyeRJwChltYXBfc3RyaW5nX25lc3RlZF9tZXNz",
            "YWdlGEcgAygLMk0ucHJvdG9idWZfdGVzdF9tZXNzYWdlcy5wcm90bzMuVGVz",
            "dEFsbFR5cGVzUHJvdG8zLk1hcFN0cmluZ05lc3RlZE1lc3NhZ2VFbnRyeRJy",
            "ChptYXBfc3RyaW5nX2ZvcmVpZ25fbWVzc2FnZRhIIAMoCzJOLnByb3RvYnVm",
            "X3Rlc3RfbWVzc2FnZXMucHJvdG8zLlRlc3RBbGxUeXBlc1Byb3RvMy5NYXBT",
            "dHJpbmdGb3JlaWduTWVzc2FnZUVudHJ5EmoKFm1hcF9zdHJpbmdfbmVzdGVk",
            "X2VudW0YSSADKAsySi5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMy5U",
            "ZXN0QWxsVHlwZXNQcm90bzMuTWFwU3RyaW5nTmVzdGVkRW51bUVudHJ5EmwK",
            "F21hcF9zdHJpbmdfZm9yZWlnbl9lbnVtGEogAygLMksucHJvdG9idWZfdGVz",
            "dF9tZXNzYWdlcy5wcm90bzMuVGVzdEFsbFR5cGVzUHJvdG8zLk1hcFN0cmlu",
            "Z0ZvcmVpZ25FbnVtRW50cnkSFgoMb25lb2ZfdWludDMyGG8gASgNSAASXwoU",
            "b25lb2ZfbmVzdGVkX21lc3NhZ2UYcCABKAsyPy5wcm90b2J1Zl90ZXN0X21l",
            "c3NhZ2VzLnByb3RvMy5UZXN0QWxsVHlwZXNQcm90bzMuTmVzdGVkTWVzc2Fn",
            "ZUgAEhYKDG9uZW9mX3N0cmluZxhxIAEoCUgAEhUKC29uZW9mX2J5dGVzGHIg",
            "ASgMSAASFAoKb25lb2ZfYm9vbBhzIAEoCEgAEhYKDG9uZW9mX3VpbnQ2NBh0",
            "IAEoBEgAEhUKC29uZW9mX2Zsb2F0GHUgASgCSAASFgoMb25lb2ZfZG91Ymxl",
            "GHYgASgBSAASUgoKb25lb2ZfZW51bRh3IAEoDjI8LnByb3RvYnVmX3Rlc3Rf",
            "bWVzc2FnZXMucHJvdG8zLlRlc3RBbGxUeXBlc1Byb3RvMy5OZXN0ZWRFbnVt",
            "SAASNgoQb25lb2ZfbnVsbF92YWx1ZRh4IAEoDjIaLmdvb2dsZS5wcm90b2J1",
            "Zi5OdWxsVmFsdWVIABI6ChVvcHRpb25hbF9ib29sX3dyYXBwZXIYyQEgASgL",
            "MhouZ29vZ2xlLnByb3RvYnVmLkJvb2xWYWx1ZRI8ChZvcHRpb25hbF9pbnQz",
            "Ml93cmFwcGVyGMoBIAEoCzIbLmdvb2dsZS5wcm90b2J1Zi5JbnQzMlZhbHVl",
            "EjwKFm9wdGlvbmFsX2ludDY0X3dyYXBwZXIYywEgASgLMhsuZ29vZ2xlLnBy",
            "b3RvYnVmLkludDY0VmFsdWUSPgoXb3B0aW9uYWxfdWludDMyX3dyYXBwZXIY",
            "zAEgASgLMhwuZ29vZ2xlLnByb3RvYnVmLlVJbnQzMlZhbHVlEj4KF29wdGlv",
            "bmFsX3VpbnQ2NF93cmFwcGVyGM0BIAEoCzIcLmdvb2dsZS5wcm90b2J1Zi5V",
            "SW50NjRWYWx1ZRI8ChZvcHRpb25hbF9mbG9hdF93cmFwcGVyGM4BIAEoCzIb",
            "Lmdvb2dsZS5wcm90b2J1Zi5GbG9hdFZhbHVlEj4KF29wdGlvbmFsX2RvdWJs",
            "ZV93cmFwcGVyGM8BIAEoCzIcLmdvb2dsZS5wcm90b2J1Zi5Eb3VibGVWYWx1",
            "ZRI+ChdvcHRpb25hbF9zdHJpbmdfd3JhcHBlchjQASABKAsyHC5nb29nbGUu",
            "cHJvdG9idWYuU3RyaW5nVmFsdWUSPAoWb3B0aW9uYWxfYnl0ZXNfd3JhcHBl",
            "chjRASABKAsyGy5nb29nbGUucHJvdG9idWYuQnl0ZXNWYWx1ZRI6ChVyZXBl",
            "YXRlZF9ib29sX3dyYXBwZXIY0wEgAygLMhouZ29vZ2xlLnByb3RvYnVmLkJv",
            "b2xWYWx1ZRI8ChZyZXBlYXRlZF9pbnQzMl93cmFwcGVyGNQBIAMoCzIbLmdv",
            "b2dsZS5wcm90b2J1Zi5JbnQzMlZhbHVlEjwKFnJlcGVhdGVkX2ludDY0X3dy",
            "YXBwZXIY1QEgAygLMhsuZ29vZ2xlLnByb3RvYnVmLkludDY0VmFsdWUSPgoX",
            "cmVwZWF0ZWRfdWludDMyX3dyYXBwZXIY1gEgAygLMhwuZ29vZ2xlLnByb3Rv",
            "YnVmLlVJbnQzMlZhbHVlEj4KF3JlcGVhdGVkX3VpbnQ2NF93cmFwcGVyGNcB",
            "IAMoCzIcLmdvb2dsZS5wcm90b2J1Zi5VSW50NjRWYWx1ZRI8ChZyZXBlYXRl",
            "ZF9mbG9hdF93cmFwcGVyGNgBIAMoCzIbLmdvb2dsZS5wcm90b2J1Zi5GbG9h",
            "dFZhbHVlEj4KF3JlcGVhdGVkX2RvdWJsZV93cmFwcGVyGNkBIAMoCzIcLmdv",
            "b2dsZS5wcm90b2J1Zi5Eb3VibGVWYWx1ZRI+ChdyZXBlYXRlZF9zdHJpbmdf",
            "d3JhcHBlchjaASADKAsyHC5nb29nbGUucHJvdG9idWYuU3RyaW5nVmFsdWUS",
            "PAoWcmVwZWF0ZWRfYnl0ZXNfd3JhcHBlchjbASADKAsyGy5nb29nbGUucHJv",
            "dG9idWYuQnl0ZXNWYWx1ZRI1ChFvcHRpb25hbF9kdXJhdGlvbhitAiABKAsy",
            "GS5nb29nbGUucHJvdG9idWYuRHVyYXRpb24SNwoSb3B0aW9uYWxfdGltZXN0",
            "YW1wGK4CIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXASOAoTb3B0",
            "aW9uYWxfZmllbGRfbWFzaxivAiABKAsyGi5nb29nbGUucHJvdG9idWYuRmll",
            "bGRNYXNrEjEKD29wdGlvbmFsX3N0cnVjdBiwAiABKAsyFy5nb29nbGUucHJv",
            "dG9idWYuU3RydWN0EisKDG9wdGlvbmFsX2FueRixAiABKAsyFC5nb29nbGUu",
            "cHJvdG9idWYuQW55Ei8KDm9wdGlvbmFsX3ZhbHVlGLICIAEoCzIWLmdvb2ds",
            "ZS5wcm90b2J1Zi5WYWx1ZRI4ChNvcHRpb25hbF9udWxsX3ZhbHVlGLMCIAEo",
            "DjIaLmdvb2dsZS5wcm90b2J1Zi5OdWxsVmFsdWUSNQoRcmVwZWF0ZWRfZHVy",
            "YXRpb24YtwIgAygLMhkuZ29vZ2xlLnByb3RvYnVmLkR1cmF0aW9uEjcKEnJl",
            "cGVhdGVkX3RpbWVzdGFtcBi4AiADKAsyGi5nb29nbGUucHJvdG9idWYuVGlt",
            "ZXN0YW1wEjcKEnJlcGVhdGVkX2ZpZWxkbWFzaxi5AiADKAsyGi5nb29nbGUu",
            "cHJvdG9idWYuRmllbGRNYXNrEjEKD3JlcGVhdGVkX3N0cnVjdBjEAiADKAsy",
            "Fy5nb29nbGUucHJvdG9idWYuU3RydWN0EisKDHJlcGVhdGVkX2FueRi7AiAD",
            "KAsyFC5nb29nbGUucHJvdG9idWYuQW55Ei8KDnJlcGVhdGVkX3ZhbHVlGLwC",
            "IAMoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZRI4ChNyZXBlYXRlZF9saXN0",
            "X3ZhbHVlGL0CIAMoCzIaLmdvb2dsZS5wcm90b2J1Zi5MaXN0VmFsdWUSEwoK",
            "ZmllbGRuYW1lMRiRAyABKAUSFAoLZmllbGRfbmFtZTIYkgMgASgFEhUKDF9m",
            "aWVsZF9uYW1lMxiTAyABKAUSFgoNZmllbGRfX25hbWU0XxiUAyABKAUSFAoL",
            "ZmllbGQwbmFtZTUYlQMgASgFEhYKDWZpZWxkXzBfbmFtZTYYlgMgASgFEhMK",
            "CmZpZWxkTmFtZTcYlwMgASgFEhMKCkZpZWxkTmFtZTgYmAMgASgFEhQKC2Zp",
            "ZWxkX05hbWU5GJkDIAEoBRIVCgxGaWVsZF9OYW1lMTAYmgMgASgFEhUKDEZJ",
            "RUxEX05BTUUxMRibAyABKAUSFQoMRklFTERfbmFtZTEyGJwDIAEoBRIXCg5f",
            "X2ZpZWxkX25hbWUxMxidAyABKAUSFwoOX19GaWVsZF9uYW1lMTQYngMgASgF",
            "EhYKDWZpZWxkX19uYW1lMTUYnwMgASgFEhYKDWZpZWxkX19OYW1lMTYYoAMg",
            "ASgFEhcKDmZpZWxkX25hbWUxN19fGKEDIAEoBRIXCg5GaWVsZF9uYW1lMThf",
            "XxiiAyABKAUaYgoNTmVzdGVkTWVzc2FnZRIJCgFhGAEgASgFEkYKC2NvcmVj",
            "dXJzaXZlGAIgASgLMjEucHJvdG9idWZfdGVzdF9tZXNzYWdlcy5wcm90bzMu",
            "VGVzdEFsbFR5cGVzUHJvdG8zGjQKEk1hcEludDMySW50MzJFbnRyeRILCgNr",
            "ZXkYASABKAUSDQoFdmFsdWUYAiABKAU6AjgBGjQKEk1hcEludDY0SW50NjRF",
            "bnRyeRILCgNrZXkYASABKAMSDQoFdmFsdWUYAiABKAM6AjgBGjYKFE1hcFVp",
            "bnQzMlVpbnQzMkVudHJ5EgsKA2tleRgBIAEoDRINCgV2YWx1ZRgCIAEoDToC",
            "OAEaNgoUTWFwVWludDY0VWludDY0RW50cnkSCwoDa2V5GAEgASgEEg0KBXZh",
            "bHVlGAIgASgEOgI4ARo2ChRNYXBTaW50MzJTaW50MzJFbnRyeRILCgNrZXkY",
            "ASABKBESDQoFdmFsdWUYAiABKBE6AjgBGjYKFE1hcFNpbnQ2NFNpbnQ2NEVu",
            "dHJ5EgsKA2tleRgBIAEoEhINCgV2YWx1ZRgCIAEoEjoCOAEaOAoWTWFwRml4",
            "ZWQzMkZpeGVkMzJFbnRyeRILCgNrZXkYASABKAcSDQoFdmFsdWUYAiABKAc6",
            "AjgBGjgKFk1hcEZpeGVkNjRGaXhlZDY0RW50cnkSCwoDa2V5GAEgASgGEg0K",
            "BXZhbHVlGAIgASgGOgI4ARo6ChhNYXBTZml4ZWQzMlNmaXhlZDMyRW50cnkS",
            "CwoDa2V5GAEgASgPEg0KBXZhbHVlGAIgASgPOgI4ARo6ChhNYXBTZml4ZWQ2",
            "NFNmaXhlZDY0RW50cnkSCwoDa2V5GAEgASgQEg0KBXZhbHVlGAIgASgQOgI4",
            "ARo0ChJNYXBJbnQzMkZsb2F0RW50cnkSCwoDa2V5GAEgASgFEg0KBXZhbHVl",
            "GAIgASgCOgI4ARo1ChNNYXBJbnQzMkRvdWJsZUVudHJ5EgsKA2tleRgBIAEo",
            "BRINCgV2YWx1ZRgCIAEoAToCOAEaMgoQTWFwQm9vbEJvb2xFbnRyeRILCgNr",
            "ZXkYASABKAgSDQoFdmFsdWUYAiABKAg6AjgBGjYKFE1hcFN0cmluZ1N0cmlu",
            "Z0VudHJ5EgsKA2tleRgBIAEoCRINCgV2YWx1ZRgCIAEoCToCOAEaNQoTTWFw",
            "U3RyaW5nQnl0ZXNFbnRyeRILCgNrZXkYASABKAkSDQoFdmFsdWUYAiABKAw6",
            "AjgBGn4KG01hcFN0cmluZ05lc3RlZE1lc3NhZ2VFbnRyeRILCgNrZXkYASAB",
            "KAkSTgoFdmFsdWUYAiABKAsyPy5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnBy",
            "b3RvMy5UZXN0QWxsVHlwZXNQcm90bzMuTmVzdGVkTWVzc2FnZToCOAEabQoc",
            "TWFwU3RyaW5nRm9yZWlnbk1lc3NhZ2VFbnRyeRILCgNrZXkYASABKAkSPAoF",
            "dmFsdWUYAiABKAsyLS5wcm90b2J1Zl90ZXN0X21lc3NhZ2VzLnByb3RvMy5G",
            "b3JlaWduTWVzc2FnZToCOAEaeAoYTWFwU3RyaW5nTmVzdGVkRW51bUVudHJ5",
            "EgsKA2tleRgBIAEoCRJLCgV2YWx1ZRgCIAEoDjI8LnByb3RvYnVmX3Rlc3Rf",
            "bWVzc2FnZXMucHJvdG8zLlRlc3RBbGxUeXBlc1Byb3RvMy5OZXN0ZWRFbnVt",
            "OgI4ARpnChlNYXBTdHJpbmdGb3JlaWduRW51bUVudHJ5EgsKA2tleRgBIAEo",
            "CRI5CgV2YWx1ZRgCIAEoDjIqLnByb3RvYnVmX3Rlc3RfbWVzc2FnZXMucHJv",
            "dG8zLkZvcmVpZ25FbnVtOgI4ASI5CgpOZXN0ZWRFbnVtEgcKA0ZPTxAAEgcK",
            "A0JBUhABEgcKA0JBWhACEhAKA05FRxD///////////8BIlkKC0FsaWFzZWRF",
            "bnVtEg0KCUFMSUFTX0ZPTxAAEg0KCUFMSUFTX0JBUhABEg0KCUFMSUFTX0JB",
            "WhACEgcKA01PTxACEgcKA21vbxACEgcKA2JBehACGgIQAUINCgtvbmVvZl9m",
            "aWVsZEoGCPUDEP8DIhsKDkZvcmVpZ25NZXNzYWdlEgkKAWMYASABKAUiFgoU",
            "TnVsbEh5cG90aGVzaXNQcm90bzMiLwoORW51bU9ubHlQcm90bzMiHQoEQm9v",
            "bBIKCgZrRmFsc2UQABIJCgVrVHJ1ZRABKkAKC0ZvcmVpZ25FbnVtEg8KC0ZP",
            "UkVJR05fRk9PEAASDwoLRk9SRUlHTl9CQVIQARIPCgtGT1JFSUdOX0JBWhAC",
            "QjgKKGNvbS5nb29nbGUucHJvdG9idWZfdGVzdF9tZXNzYWdlcy5wcm90bzNI",
            "AfgBAaICBlByb3RvM2IGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Google.Protobuf.WellKnownTypes.AnyReflection.Descriptor, global::Google.Protobuf.WellKnownTypes.DurationReflection.Descriptor, global::Google.Protobuf.WellKnownTypes.FieldMaskReflection.Descriptor, global::Google.Protobuf.WellKnownTypes.StructReflection.Descriptor, global::Google.Protobuf.WellKnownTypes.TimestampReflection.Descriptor, global::Google.Protobuf.WellKnownTypes.WrappersReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::ProtobufTestMessages.Proto3.ForeignEnum), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto3.TestAllTypesProto3), global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Parser, new[]{ "OptionalInt32", "OptionalInt64", "OptionalUint32", "OptionalUint64", "OptionalSint32", "OptionalSint64", "OptionalFixed32", "OptionalFixed64", "OptionalSfixed32", "OptionalSfixed64", "OptionalFloat", "OptionalDouble", "OptionalBool", "OptionalString", "OptionalBytes", "OptionalNestedMessage", "OptionalForeignMessage", "OptionalNestedEnum", "OptionalForeignEnum", "OptionalAliasedEnum", "OptionalStringPiece", "OptionalCord", "RecursiveMessage", "RepeatedInt32", "RepeatedInt64", "RepeatedUint32", "RepeatedUint64", "RepeatedSint32", "RepeatedSint64", "RepeatedFixed32", "RepeatedFixed64", "RepeatedSfixed32", "RepeatedSfixed64", "RepeatedFloat", "RepeatedDouble", "RepeatedBool", "RepeatedString", "RepeatedBytes", "RepeatedNestedMessage", "RepeatedForeignMessage", "RepeatedNestedEnum", "RepeatedForeignEnum", "RepeatedStringPiece", "RepeatedCord", "PackedInt32", "PackedInt64", "PackedUint32", "PackedUint64", "PackedSint32", "PackedSint64", "PackedFixed32", "PackedFixed64", "PackedSfixed32", "PackedSfixed64", "PackedFloat", "PackedDouble", "PackedBool", "PackedNestedEnum", "UnpackedInt32", "UnpackedInt64", "UnpackedUint32", "UnpackedUint64", "UnpackedSint32", "UnpackedSint64", "UnpackedFixed32", "UnpackedFixed64", "UnpackedSfixed32", "UnpackedSfixed64", "UnpackedFloat", "UnpackedDouble", "UnpackedBool", "UnpackedNestedEnum", "MapInt32Int32", "MapInt64Int64", "MapUint32Uint32", "MapUint64Uint64", "MapSint32Sint32", "MapSint64Sint64", "MapFixed32Fixed32", "MapFixed64Fixed64", "MapSfixed32Sfixed32", "MapSfixed64Sfixed64", "MapInt32Float", "MapInt32Double", "MapBoolBool", "MapStringString", "MapStringBytes", "MapStringNestedMessage", "MapStringForeignMessage", "MapStringNestedEnum", "MapStringForeignEnum", "OneofUint32", "OneofNestedMessage", "OneofString", "OneofBytes", "OneofBool", "OneofUint64", "OneofFloat", "OneofDouble", "OneofEnum", "OneofNullValue", "OptionalBoolWrapper", "OptionalInt32Wrapper", "OptionalInt64Wrapper", "OptionalUint32Wrapper", "OptionalUint64Wrapper", "OptionalFloatWrapper", "OptionalDoubleWrapper", "OptionalStringWrapper", "OptionalBytesWrapper", "RepeatedBoolWrapper", "RepeatedInt32Wrapper", "RepeatedInt64Wrapper", "RepeatedUint32Wrapper", "RepeatedUint64Wrapper", "RepeatedFloatWrapper", "RepeatedDoubleWrapper", "RepeatedStringWrapper", "RepeatedBytesWrapper", "OptionalDuration", "OptionalTimestamp", "OptionalFieldMask", "OptionalStruct", "OptionalAny", "OptionalValue", "OptionalNullValue", "RepeatedDuration", "RepeatedTimestamp", "RepeatedFieldmask", "RepeatedStruct", "RepeatedAny", "RepeatedValue", "RepeatedListValue", "Fieldname1", "FieldName2", "FieldName3", "FieldName4", "Field0Name5", "Field0Name6", "FieldName7", "FieldName8", "FieldName9", "FieldName10", "FIELDNAME11", "FIELDName12", "FieldName13", "FieldName14", "FieldName15", "FieldName16", "FieldName17", "FieldName18" }, new[]{ "OneofField" }, new[]{ typeof(global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum), typeof(global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.AliasedEnum) }, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage), global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage.Parser, new[]{ "A", "Corecursive" }, null, null, null, null),
            null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto3.ForeignMessage), global::ProtobufTestMessages.Proto3.ForeignMessage.Parser, new[]{ "C" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto3.NullHypothesisProto3), global::ProtobufTestMessages.Proto3.NullHypothesisProto3.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::ProtobufTestMessages.Proto3.EnumOnlyProto3), global::ProtobufTestMessages.Proto3.EnumOnlyProto3.Parser, null, null, new[]{ typeof(global::ProtobufTestMessages.Proto3.EnumOnlyProto3.Types.Bool) }, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum ForeignEnum {
    [pbr::OriginalName("FOREIGN_FOO")] ForeignFoo = 0,
    [pbr::OriginalName("FOREIGN_BAR")] ForeignBar = 1,
    [pbr::OriginalName("FOREIGN_BAZ")] ForeignBaz = 2,
  }

  #endregion

  #region Messages
  /// <summary>
  /// This proto includes every type of field in both singular and repeated
  /// forms.
  ///
  /// Also, crucially, all messages and enums in this file are eventually
  /// submessages of this message.  So for example, a fuzz test of TestAllTypes
  /// could trigger bugs that occur in any message type in this file.  We verify
  /// this stays true in a unit test.
  /// </summary>
  public sealed partial class TestAllTypesProto3 : pb::IMessage<TestAllTypesProto3>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TestAllTypesProto3> _parser = new pb::MessageParser<TestAllTypesProto3>(() => new TestAllTypesProto3());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TestAllTypesProto3> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufTestMessages.Proto3.TestMessagesProto3Reflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestAllTypesProto3() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestAllTypesProto3(TestAllTypesProto3 other) : this() {
      optionalInt32_ = other.optionalInt32_;
      optionalInt64_ = other.optionalInt64_;
      optionalUint32_ = other.optionalUint32_;
      optionalUint64_ = other.optionalUint64_;
      optionalSint32_ = other.optionalSint32_;
      optionalSint64_ = other.optionalSint64_;
      optionalFixed32_ = other.optionalFixed32_;
      optionalFixed64_ = other.optionalFixed64_;
      optionalSfixed32_ = other.optionalSfixed32_;
      optionalSfixed64_ = other.optionalSfixed64_;
      optionalFloat_ = other.optionalFloat_;
      optionalDouble_ = other.optionalDouble_;
      optionalBool_ = other.optionalBool_;
      optionalString_ = other.optionalString_;
      optionalBytes_ = other.optionalBytes_;
      optionalNestedMessage_ = other.optionalNestedMessage_ != null ? other.optionalNestedMessage_.Clone() : null;
      optionalForeignMessage_ = other.optionalForeignMessage_ != null ? other.optionalForeignMessage_.Clone() : null;
      optionalNestedEnum_ = other.optionalNestedEnum_;
      optionalForeignEnum_ = other.optionalForeignEnum_;
      optionalAliasedEnum_ = other.optionalAliasedEnum_;
      optionalStringPiece_ = other.optionalStringPiece_;
      optionalCord_ = other.optionalCord_;
      recursiveMessage_ = other.recursiveMessage_ != null ? other.recursiveMessage_.Clone() : null;
      repeatedInt32_ = other.repeatedInt32_.Clone();
      repeatedInt64_ = other.repeatedInt64_.Clone();
      repeatedUint32_ = other.repeatedUint32_.Clone();
      repeatedUint64_ = other.repeatedUint64_.Clone();
      repeatedSint32_ = other.repeatedSint32_.Clone();
      repeatedSint64_ = other.repeatedSint64_.Clone();
      repeatedFixed32_ = other.repeatedFixed32_.Clone();
      repeatedFixed64_ = other.repeatedFixed64_.Clone();
      repeatedSfixed32_ = other.repeatedSfixed32_.Clone();
      repeatedSfixed64_ = other.repeatedSfixed64_.Clone();
      repeatedFloat_ = other.repeatedFloat_.Clone();
      repeatedDouble_ = other.repeatedDouble_.Clone();
      repeatedBool_ = other.repeatedBool_.Clone();
      repeatedString_ = other.repeatedString_.Clone();
      repeatedBytes_ = other.repeatedBytes_.Clone();
      repeatedNestedMessage_ = other.repeatedNestedMessage_.Clone();
      repeatedForeignMessage_ = other.repeatedForeignMessage_.Clone();
      repeatedNestedEnum_ = other.repeatedNestedEnum_.Clone();
      repeatedForeignEnum_ = other.repeatedForeignEnum_.Clone();
      repeatedStringPiece_ = other.repeatedStringPiece_.Clone();
      repeatedCord_ = other.repeatedCord_.Clone();
      packedInt32_ = other.packedInt32_.Clone();
      packedInt64_ = other.packedInt64_.Clone();
      packedUint32_ = other.packedUint32_.Clone();
      packedUint64_ = other.packedUint64_.Clone();
      packedSint32_ = other.packedSint32_.Clone();
      packedSint64_ = other.packedSint64_.Clone();
      packedFixed32_ = other.packedFixed32_.Clone();
      packedFixed64_ = other.packedFixed64_.Clone();
      packedSfixed32_ = other.packedSfixed32_.Clone();
      packedSfixed64_ = other.packedSfixed64_.Clone();
      packedFloat_ = other.packedFloat_.Clone();
      packedDouble_ = other.packedDouble_.Clone();
      packedBool_ = other.packedBool_.Clone();
      packedNestedEnum_ = other.packedNestedEnum_.Clone();
      unpackedInt32_ = other.unpackedInt32_.Clone();
      unpackedInt64_ = other.unpackedInt64_.Clone();
      unpackedUint32_ = other.unpackedUint32_.Clone();
      unpackedUint64_ = other.unpackedUint64_.Clone();
      unpackedSint32_ = other.unpackedSint32_.Clone();
      unpackedSint64_ = other.unpackedSint64_.Clone();
      unpackedFixed32_ = other.unpackedFixed32_.Clone();
      unpackedFixed64_ = other.unpackedFixed64_.Clone();
      unpackedSfixed32_ = other.unpackedSfixed32_.Clone();
      unpackedSfixed64_ = other.unpackedSfixed64_.Clone();
      unpackedFloat_ = other.unpackedFloat_.Clone();
      unpackedDouble_ = other.unpackedDouble_.Clone();
      unpackedBool_ = other.unpackedBool_.Clone();
      unpackedNestedEnum_ = other.unpackedNestedEnum_.Clone();
      mapInt32Int32_ = other.mapInt32Int32_.Clone();
      mapInt64Int64_ = other.mapInt64Int64_.Clone();
      mapUint32Uint32_ = other.mapUint32Uint32_.Clone();
      mapUint64Uint64_ = other.mapUint64Uint64_.Clone();
      mapSint32Sint32_ = other.mapSint32Sint32_.Clone();
      mapSint64Sint64_ = other.mapSint64Sint64_.Clone();
      mapFixed32Fixed32_ = other.mapFixed32Fixed32_.Clone();
      mapFixed64Fixed64_ = other.mapFixed64Fixed64_.Clone();
      mapSfixed32Sfixed32_ = other.mapSfixed32Sfixed32_.Clone();
      mapSfixed64Sfixed64_ = other.mapSfixed64Sfixed64_.Clone();
      mapInt32Float_ = other.mapInt32Float_.Clone();
      mapInt32Double_ = other.mapInt32Double_.Clone();
      mapBoolBool_ = other.mapBoolBool_.Clone();
      mapStringString_ = other.mapStringString_.Clone();
      mapStringBytes_ = other.mapStringBytes_.Clone();
      mapStringNestedMessage_ = other.mapStringNestedMessage_.Clone();
      mapStringForeignMessage_ = other.mapStringForeignMessage_.Clone();
      mapStringNestedEnum_ = other.mapStringNestedEnum_.Clone();
      mapStringForeignEnum_ = other.mapStringForeignEnum_.Clone();
      OptionalBoolWrapper = other.OptionalBoolWrapper;
      OptionalInt32Wrapper = other.OptionalInt32Wrapper;
      OptionalInt64Wrapper = other.OptionalInt64Wrapper;
      OptionalUint32Wrapper = other.OptionalUint32Wrapper;
      OptionalUint64Wrapper = other.OptionalUint64Wrapper;
      OptionalFloatWrapper = other.OptionalFloatWrapper;
      OptionalDoubleWrapper = other.OptionalDoubleWrapper;
      OptionalStringWrapper = other.OptionalStringWrapper;
      OptionalBytesWrapper = other.OptionalBytesWrapper;
      repeatedBoolWrapper_ = other.repeatedBoolWrapper_.Clone();
      repeatedInt32Wrapper_ = other.repeatedInt32Wrapper_.Clone();
      repeatedInt64Wrapper_ = other.repeatedInt64Wrapper_.Clone();
      repeatedUint32Wrapper_ = other.repeatedUint32Wrapper_.Clone();
      repeatedUint64Wrapper_ = other.repeatedUint64Wrapper_.Clone();
      repeatedFloatWrapper_ = other.repeatedFloatWrapper_.Clone();
      repeatedDoubleWrapper_ = other.repeatedDoubleWrapper_.Clone();
      repeatedStringWrapper_ = other.repeatedStringWrapper_.Clone();
      repeatedBytesWrapper_ = other.repeatedBytesWrapper_.Clone();
      optionalDuration_ = other.optionalDuration_ != null ? other.optionalDuration_.Clone() : null;
      optionalTimestamp_ = other.optionalTimestamp_ != null ? other.optionalTimestamp_.Clone() : null;
      optionalFieldMask_ = other.optionalFieldMask_ != null ? other.optionalFieldMask_.Clone() : null;
      optionalStruct_ = other.optionalStruct_ != null ? other.optionalStruct_.Clone() : null;
      optionalAny_ = other.optionalAny_ != null ? other.optionalAny_.Clone() : null;
      optionalValue_ = other.optionalValue_ != null ? other.optionalValue_.Clone() : null;
      optionalNullValue_ = other.optionalNullValue_;
      repeatedDuration_ = other.repeatedDuration_.Clone();
      repeatedTimestamp_ = other.repeatedTimestamp_.Clone();
      repeatedFieldmask_ = other.repeatedFieldmask_.Clone();
      repeatedStruct_ = other.repeatedStruct_.Clone();
      repeatedAny_ = other.repeatedAny_.Clone();
      repeatedValue_ = other.repeatedValue_.Clone();
      repeatedListValue_ = other.repeatedListValue_.Clone();
      fieldname1_ = other.fieldname1_;
      fieldName2_ = other.fieldName2_;
      FieldName3_ = other.FieldName3_;
      fieldName4_ = other.fieldName4_;
      field0Name5_ = other.field0Name5_;
      field0Name6_ = other.field0Name6_;
      fieldName7_ = other.fieldName7_;
      fieldName8_ = other.fieldName8_;
      fieldName9_ = other.fieldName9_;
      fieldName10_ = other.fieldName10_;
      fIELDNAME11_ = other.fIELDNAME11_;
      fIELDName12_ = other.fIELDName12_;
      FieldName13_ = other.FieldName13_;
      FieldName14_ = other.FieldName14_;
      fieldName15_ = other.fieldName15_;
      fieldName16_ = other.fieldName16_;
      fieldName17_ = other.fieldName17_;
      fieldName18_ = other.fieldName18_;
      switch (other.OneofFieldCase) {
        case OneofFieldOneofCase.OneofUint32:
          OneofUint32 = other.OneofUint32;
          break;
        case OneofFieldOneofCase.OneofNestedMessage:
          OneofNestedMessage = other.OneofNestedMessage.Clone();
          break;
        case OneofFieldOneofCase.OneofString:
          OneofString = other.OneofString;
          break;
        case OneofFieldOneofCase.OneofBytes:
          OneofBytes = other.OneofBytes;
          break;
        case OneofFieldOneofCase.OneofBool:
          OneofBool = other.OneofBool;
          break;
        case OneofFieldOneofCase.OneofUint64:
          OneofUint64 = other.OneofUint64;
          break;
        case OneofFieldOneofCase.OneofFloat:
          OneofFloat = other.OneofFloat;
          break;
        case OneofFieldOneofCase.OneofDouble:
          OneofDouble = other.OneofDouble;
          break;
        case OneofFieldOneofCase.OneofEnum:
          OneofEnum = other.OneofEnum;
          break;
        case OneofFieldOneofCase.OneofNullValue:
          OneofNullValue = other.OneofNullValue;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TestAllTypesProto3 Clone() {
      return new TestAllTypesProto3(this);
    }

    /// <summary>Field number for the "optional_int32" field.</summary>
    public const int OptionalInt32FieldNumber = 1;
    private int optionalInt32_;
    /// <summary>
    /// Singular
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int OptionalInt32 {
      get { return optionalInt32_; }
      set {
        optionalInt32_ = value;
      }
    }

    /// <summary>Field number for the "optional_int64" field.</summary>
    public const int OptionalInt64FieldNumber = 2;
    private long optionalInt64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long OptionalInt64 {
      get { return optionalInt64_; }
      set {
        optionalInt64_ = value;
      }
    }

    /// <summary>Field number for the "optional_uint32" field.</summary>
    public const int OptionalUint32FieldNumber = 3;
    private uint optionalUint32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint OptionalUint32 {
      get { return optionalUint32_; }
      set {
        optionalUint32_ = value;
      }
    }

    /// <summary>Field number for the "optional_uint64" field.</summary>
    public const int OptionalUint64FieldNumber = 4;
    private ulong optionalUint64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong OptionalUint64 {
      get { return optionalUint64_; }
      set {
        optionalUint64_ = value;
      }
    }

    /// <summary>Field number for the "optional_sint32" field.</summary>
    public const int OptionalSint32FieldNumber = 5;
    private int optionalSint32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int OptionalSint32 {
      get { return optionalSint32_; }
      set {
        optionalSint32_ = value;
      }
    }

    /// <summary>Field number for the "optional_sint64" field.</summary>
    public const int OptionalSint64FieldNumber = 6;
    private long optionalSint64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long OptionalSint64 {
      get { return optionalSint64_; }
      set {
        optionalSint64_ = value;
      }
    }

    /// <summary>Field number for the "optional_fixed32" field.</summary>
    public const int OptionalFixed32FieldNumber = 7;
    private uint optionalFixed32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint OptionalFixed32 {
      get { return optionalFixed32_; }
      set {
        optionalFixed32_ = value;
      }
    }

    /// <summary>Field number for the "optional_fixed64" field.</summary>
    public const int OptionalFixed64FieldNumber = 8;
    private ulong optionalFixed64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong OptionalFixed64 {
      get { return optionalFixed64_; }
      set {
        optionalFixed64_ = value;
      }
    }

    /// <summary>Field number for the "optional_sfixed32" field.</summary>
    public const int OptionalSfixed32FieldNumber = 9;
    private int optionalSfixed32_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int OptionalSfixed32 {
      get { return optionalSfixed32_; }
      set {
        optionalSfixed32_ = value;
      }
    }

    /// <summary>Field number for the "optional_sfixed64" field.</summary>
    public const int OptionalSfixed64FieldNumber = 10;
    private long optionalSfixed64_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long OptionalSfixed64 {
      get { return optionalSfixed64_; }
      set {
        optionalSfixed64_ = value;
      }
    }

    /// <summary>Field number for the "optional_float" field.</summary>
    public const int OptionalFloatFieldNumber = 11;
    private float optionalFloat_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float OptionalFloat {
      get { return optionalFloat_; }
      set {
        optionalFloat_ = value;
      }
    }

    /// <summary>Field number for the "optional_double" field.</summary>
    public const int OptionalDoubleFieldNumber = 12;
    private double optionalDouble_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double OptionalDouble {
      get { return optionalDouble_; }
      set {
        optionalDouble_ = value;
      }
    }

    /// <summary>Field number for the "optional_bool" field.</summary>
    public const int OptionalBoolFieldNumber = 13;
    private bool optionalBool_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool OptionalBool {
      get { return optionalBool_; }
      set {
        optionalBool_ = value;
      }
    }

    /// <summary>Field number for the "optional_string" field.</summary>
    public const int OptionalStringFieldNumber = 14;
    private string optionalString_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OptionalString {
      get { return optionalString_; }
      set {
        optionalString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "optional_bytes" field.</summary>
    public const int OptionalBytesFieldNumber = 15;
    private pb::ByteString optionalBytes_ = pb::ByteString.Empty;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString OptionalBytes {
      get { return optionalBytes_; }
      set {
        optionalBytes_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "optional_nested_message" field.</summary>
    public const int OptionalNestedMessageFieldNumber = 18;
    private global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage optionalNestedMessage_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage OptionalNestedMessage {
      get { return optionalNestedMessage_; }
      set {
        optionalNestedMessage_ = value;
      }
    }

    /// <summary>Field number for the "optional_foreign_message" field.</summary>
    public const int OptionalForeignMessageFieldNumber = 19;
    private global::ProtobufTestMessages.Proto3.ForeignMessage optionalForeignMessage_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto3.ForeignMessage OptionalForeignMessage {
      get { return optionalForeignMessage_; }
      set {
        optionalForeignMessage_ = value;
      }
    }

    /// <summary>Field number for the "optional_nested_enum" field.</summary>
    public const int OptionalNestedEnumFieldNumber = 21;
    private global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum optionalNestedEnum_ = global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum.Foo;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum OptionalNestedEnum {
      get { return optionalNestedEnum_; }
      set {
        optionalNestedEnum_ = value;
      }
    }

    /// <summary>Field number for the "optional_foreign_enum" field.</summary>
    public const int OptionalForeignEnumFieldNumber = 22;
    private global::ProtobufTestMessages.Proto3.ForeignEnum optionalForeignEnum_ = global::ProtobufTestMessages.Proto3.ForeignEnum.ForeignFoo;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto3.ForeignEnum OptionalForeignEnum {
      get { return optionalForeignEnum_; }
      set {
        optionalForeignEnum_ = value;
      }
    }

    /// <summary>Field number for the "optional_aliased_enum" field.</summary>
    public const int OptionalAliasedEnumFieldNumber = 23;
    private global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.AliasedEnum optionalAliasedEnum_ = global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.AliasedEnum.AliasFoo;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.AliasedEnum OptionalAliasedEnum {
      get { return optionalAliasedEnum_; }
      set {
        optionalAliasedEnum_ = value;
      }
    }

    /// <summary>Field number for the "optional_string_piece" field.</summary>
    public const int OptionalStringPieceFieldNumber = 24;
    private string optionalStringPiece_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OptionalStringPiece {
      get { return optionalStringPiece_; }
      set {
        optionalStringPiece_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "optional_cord" field.</summary>
    public const int OptionalCordFieldNumber = 25;
    private string optionalCord_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OptionalCord {
      get { return optionalCord_; }
      set {
        optionalCord_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "recursive_message" field.</summary>
    public const int RecursiveMessageFieldNumber = 27;
    private global::ProtobufTestMessages.Proto3.TestAllTypesProto3 recursiveMessage_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto3.TestAllTypesProto3 RecursiveMessage {
      get { return recursiveMessage_; }
      set {
        recursiveMessage_ = value;
      }
    }

    /// <summary>Field number for the "repeated_int32" field.</summary>
    public const int RepeatedInt32FieldNumber = 31;
    private static readonly pb::FieldCodec<int> _repeated_repeatedInt32_codec
        = pb::FieldCodec.ForInt32(250);
    private readonly pbc::RepeatedField<int> repeatedInt32_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// Repeated
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> RepeatedInt32 {
      get { return repeatedInt32_; }
    }

    /// <summary>Field number for the "repeated_int64" field.</summary>
    public const int RepeatedInt64FieldNumber = 32;
    private static readonly pb::FieldCodec<long> _repeated_repeatedInt64_codec
        = pb::FieldCodec.ForInt64(258);
    private readonly pbc::RepeatedField<long> repeatedInt64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> RepeatedInt64 {
      get { return repeatedInt64_; }
    }

    /// <summary>Field number for the "repeated_uint32" field.</summary>
    public const int RepeatedUint32FieldNumber = 33;
    private static readonly pb::FieldCodec<uint> _repeated_repeatedUint32_codec
        = pb::FieldCodec.ForUInt32(266);
    private readonly pbc::RepeatedField<uint> repeatedUint32_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> RepeatedUint32 {
      get { return repeatedUint32_; }
    }

    /// <summary>Field number for the "repeated_uint64" field.</summary>
    public const int RepeatedUint64FieldNumber = 34;
    private static readonly pb::FieldCodec<ulong> _repeated_repeatedUint64_codec
        = pb::FieldCodec.ForUInt64(274);
    private readonly pbc::RepeatedField<ulong> repeatedUint64_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> RepeatedUint64 {
      get { return repeatedUint64_; }
    }

    /// <summary>Field number for the "repeated_sint32" field.</summary>
    public const int RepeatedSint32FieldNumber = 35;
    private static readonly pb::FieldCodec<int> _repeated_repeatedSint32_codec
        = pb::FieldCodec.ForSInt32(282);
    private readonly pbc::RepeatedField<int> repeatedSint32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> RepeatedSint32 {
      get { return repeatedSint32_; }
    }

    /// <summary>Field number for the "repeated_sint64" field.</summary>
    public const int RepeatedSint64FieldNumber = 36;
    private static readonly pb::FieldCodec<long> _repeated_repeatedSint64_codec
        = pb::FieldCodec.ForSInt64(290);
    private readonly pbc::RepeatedField<long> repeatedSint64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> RepeatedSint64 {
      get { return repeatedSint64_; }
    }

    /// <summary>Field number for the "repeated_fixed32" field.</summary>
    public const int RepeatedFixed32FieldNumber = 37;
    private static readonly pb::FieldCodec<uint> _repeated_repeatedFixed32_codec
        = pb::FieldCodec.ForFixed32(298);
    private readonly pbc::RepeatedField<uint> repeatedFixed32_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> RepeatedFixed32 {
      get { return repeatedFixed32_; }
    }

    /// <summary>Field number for the "repeated_fixed64" field.</summary>
    public const int RepeatedFixed64FieldNumber = 38;
    private static readonly pb::FieldCodec<ulong> _repeated_repeatedFixed64_codec
        = pb::FieldCodec.ForFixed64(306);
    private readonly pbc::RepeatedField<ulong> repeatedFixed64_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> RepeatedFixed64 {
      get { return repeatedFixed64_; }
    }

    /// <summary>Field number for the "repeated_sfixed32" field.</summary>
    public const int RepeatedSfixed32FieldNumber = 39;
    private static readonly pb::FieldCodec<int> _repeated_repeatedSfixed32_codec
        = pb::FieldCodec.ForSFixed32(314);
    private readonly pbc::RepeatedField<int> repeatedSfixed32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> RepeatedSfixed32 {
      get { return repeatedSfixed32_; }
    }

    /// <summary>Field number for the "repeated_sfixed64" field.</summary>
    public const int RepeatedSfixed64FieldNumber = 40;
    private static readonly pb::FieldCodec<long> _repeated_repeatedSfixed64_codec
        = pb::FieldCodec.ForSFixed64(322);
    private readonly pbc::RepeatedField<long> repeatedSfixed64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> RepeatedSfixed64 {
      get { return repeatedSfixed64_; }
    }

    /// <summary>Field number for the "repeated_float" field.</summary>
    public const int RepeatedFloatFieldNumber = 41;
    private static readonly pb::FieldCodec<float> _repeated_repeatedFloat_codec
        = pb::FieldCodec.ForFloat(330);
    private readonly pbc::RepeatedField<float> repeatedFloat_ = new pbc::RepeatedField<float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<float> RepeatedFloat {
      get { return repeatedFloat_; }
    }

    /// <summary>Field number for the "repeated_double" field.</summary>
    public const int RepeatedDoubleFieldNumber = 42;
    private static readonly pb::FieldCodec<double> _repeated_repeatedDouble_codec
        = pb::FieldCodec.ForDouble(338);
    private readonly pbc::RepeatedField<double> repeatedDouble_ = new pbc::RepeatedField<double>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<double> RepeatedDouble {
      get { return repeatedDouble_; }
    }

    /// <summary>Field number for the "repeated_bool" field.</summary>
    public const int RepeatedBoolFieldNumber = 43;
    private static readonly pb::FieldCodec<bool> _repeated_repeatedBool_codec
        = pb::FieldCodec.ForBool(346);
    private readonly pbc::RepeatedField<bool> repeatedBool_ = new pbc::RepeatedField<bool>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<bool> RepeatedBool {
      get { return repeatedBool_; }
    }

    /// <summary>Field number for the "repeated_string" field.</summary>
    public const int RepeatedStringFieldNumber = 44;
    private static readonly pb::FieldCodec<string> _repeated_repeatedString_codec
        = pb::FieldCodec.ForString(354);
    private readonly pbc::RepeatedField<string> repeatedString_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> RepeatedString {
      get { return repeatedString_; }
    }

    /// <summary>Field number for the "repeated_bytes" field.</summary>
    public const int RepeatedBytesFieldNumber = 45;
    private static readonly pb::FieldCodec<pb::ByteString> _repeated_repeatedBytes_codec
        = pb::FieldCodec.ForBytes(362);
    private readonly pbc::RepeatedField<pb::ByteString> repeatedBytes_ = new pbc::RepeatedField<pb::ByteString>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<pb::ByteString> RepeatedBytes {
      get { return repeatedBytes_; }
    }

    /// <summary>Field number for the "repeated_nested_message" field.</summary>
    public const int RepeatedNestedMessageFieldNumber = 48;
    private static readonly pb::FieldCodec<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage> _repeated_repeatedNestedMessage_codec
        = pb::FieldCodec.ForMessage(386, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage.Parser);
    private readonly pbc::RepeatedField<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage> repeatedNestedMessage_ = new pbc::RepeatedField<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage> RepeatedNestedMessage {
      get { return repeatedNestedMessage_; }
    }

    /// <summary>Field number for the "repeated_foreign_message" field.</summary>
    public const int RepeatedForeignMessageFieldNumber = 49;
    private static readonly pb::FieldCodec<global::ProtobufTestMessages.Proto3.ForeignMessage> _repeated_repeatedForeignMessage_codec
        = pb::FieldCodec.ForMessage(394, global::ProtobufTestMessages.Proto3.ForeignMessage.Parser);
    private readonly pbc::RepeatedField<global::ProtobufTestMessages.Proto3.ForeignMessage> repeatedForeignMessage_ = new pbc::RepeatedField<global::ProtobufTestMessages.Proto3.ForeignMessage>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::ProtobufTestMessages.Proto3.ForeignMessage> RepeatedForeignMessage {
      get { return repeatedForeignMessage_; }
    }

    /// <summary>Field number for the "repeated_nested_enum" field.</summary>
    public const int RepeatedNestedEnumFieldNumber = 51;
    private static readonly pb::FieldCodec<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum> _repeated_repeatedNestedEnum_codec
        = pb::FieldCodec.ForEnum(410, x => (int) x, x => (global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum) x);
    private readonly pbc::RepeatedField<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum> repeatedNestedEnum_ = new pbc::RepeatedField<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum> RepeatedNestedEnum {
      get { return repeatedNestedEnum_; }
    }

    /// <summary>Field number for the "repeated_foreign_enum" field.</summary>
    public const int RepeatedForeignEnumFieldNumber = 52;
    private static readonly pb::FieldCodec<global::ProtobufTestMessages.Proto3.ForeignEnum> _repeated_repeatedForeignEnum_codec
        = pb::FieldCodec.ForEnum(418, x => (int) x, x => (global::ProtobufTestMessages.Proto3.ForeignEnum) x);
    private readonly pbc::RepeatedField<global::ProtobufTestMessages.Proto3.ForeignEnum> repeatedForeignEnum_ = new pbc::RepeatedField<global::ProtobufTestMessages.Proto3.ForeignEnum>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::ProtobufTestMessages.Proto3.ForeignEnum> RepeatedForeignEnum {
      get { return repeatedForeignEnum_; }
    }

    /// <summary>Field number for the "repeated_string_piece" field.</summary>
    public const int RepeatedStringPieceFieldNumber = 54;
    private static readonly pb::FieldCodec<string> _repeated_repeatedStringPiece_codec
        = pb::FieldCodec.ForString(434);
    private readonly pbc::RepeatedField<string> repeatedStringPiece_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> RepeatedStringPiece {
      get { return repeatedStringPiece_; }
    }

    /// <summary>Field number for the "repeated_cord" field.</summary>
    public const int RepeatedCordFieldNumber = 55;
    private static readonly pb::FieldCodec<string> _repeated_repeatedCord_codec
        = pb::FieldCodec.ForString(442);
    private readonly pbc::RepeatedField<string> repeatedCord_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> RepeatedCord {
      get { return repeatedCord_; }
    }

    /// <summary>Field number for the "packed_int32" field.</summary>
    public const int PackedInt32FieldNumber = 75;
    private static readonly pb::FieldCodec<int> _repeated_packedInt32_codec
        = pb::FieldCodec.ForInt32(602);
    private readonly pbc::RepeatedField<int> packedInt32_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// Packed
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> PackedInt32 {
      get { return packedInt32_; }
    }

    /// <summary>Field number for the "packed_int64" field.</summary>
    public const int PackedInt64FieldNumber = 76;
    private static readonly pb::FieldCodec<long> _repeated_packedInt64_codec
        = pb::FieldCodec.ForInt64(610);
    private readonly pbc::RepeatedField<long> packedInt64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> PackedInt64 {
      get { return packedInt64_; }
    }

    /// <summary>Field number for the "packed_uint32" field.</summary>
    public const int PackedUint32FieldNumber = 77;
    private static readonly pb::FieldCodec<uint> _repeated_packedUint32_codec
        = pb::FieldCodec.ForUInt32(618);
    private readonly pbc::RepeatedField<uint> packedUint32_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> PackedUint32 {
      get { return packedUint32_; }
    }

    /// <summary>Field number for the "packed_uint64" field.</summary>
    public const int PackedUint64FieldNumber = 78;
    private static readonly pb::FieldCodec<ulong> _repeated_packedUint64_codec
        = pb::FieldCodec.ForUInt64(626);
    private readonly pbc::RepeatedField<ulong> packedUint64_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> PackedUint64 {
      get { return packedUint64_; }
    }

    /// <summary>Field number for the "packed_sint32" field.</summary>
    public const int PackedSint32FieldNumber = 79;
    private static readonly pb::FieldCodec<int> _repeated_packedSint32_codec
        = pb::FieldCodec.ForSInt32(634);
    private readonly pbc::RepeatedField<int> packedSint32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> PackedSint32 {
      get { return packedSint32_; }
    }

    /// <summary>Field number for the "packed_sint64" field.</summary>
    public const int PackedSint64FieldNumber = 80;
    private static readonly pb::FieldCodec<long> _repeated_packedSint64_codec
        = pb::FieldCodec.ForSInt64(642);
    private readonly pbc::RepeatedField<long> packedSint64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> PackedSint64 {
      get { return packedSint64_; }
    }

    /// <summary>Field number for the "packed_fixed32" field.</summary>
    public const int PackedFixed32FieldNumber = 81;
    private static readonly pb::FieldCodec<uint> _repeated_packedFixed32_codec
        = pb::FieldCodec.ForFixed32(650);
    private readonly pbc::RepeatedField<uint> packedFixed32_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> PackedFixed32 {
      get { return packedFixed32_; }
    }

    /// <summary>Field number for the "packed_fixed64" field.</summary>
    public const int PackedFixed64FieldNumber = 82;
    private static readonly pb::FieldCodec<ulong> _repeated_packedFixed64_codec
        = pb::FieldCodec.ForFixed64(658);
    private readonly pbc::RepeatedField<ulong> packedFixed64_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> PackedFixed64 {
      get { return packedFixed64_; }
    }

    /// <summary>Field number for the "packed_sfixed32" field.</summary>
    public const int PackedSfixed32FieldNumber = 83;
    private static readonly pb::FieldCodec<int> _repeated_packedSfixed32_codec
        = pb::FieldCodec.ForSFixed32(666);
    private readonly pbc::RepeatedField<int> packedSfixed32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> PackedSfixed32 {
      get { return packedSfixed32_; }
    }

    /// <summary>Field number for the "packed_sfixed64" field.</summary>
    public const int PackedSfixed64FieldNumber = 84;
    private static readonly pb::FieldCodec<long> _repeated_packedSfixed64_codec
        = pb::FieldCodec.ForSFixed64(674);
    private readonly pbc::RepeatedField<long> packedSfixed64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> PackedSfixed64 {
      get { return packedSfixed64_; }
    }

    /// <summary>Field number for the "packed_float" field.</summary>
    public const int PackedFloatFieldNumber = 85;
    private static readonly pb::FieldCodec<float> _repeated_packedFloat_codec
        = pb::FieldCodec.ForFloat(682);
    private readonly pbc::RepeatedField<float> packedFloat_ = new pbc::RepeatedField<float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<float> PackedFloat {
      get { return packedFloat_; }
    }

    /// <summary>Field number for the "packed_double" field.</summary>
    public const int PackedDoubleFieldNumber = 86;
    private static readonly pb::FieldCodec<double> _repeated_packedDouble_codec
        = pb::FieldCodec.ForDouble(690);
    private readonly pbc::RepeatedField<double> packedDouble_ = new pbc::RepeatedField<double>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<double> PackedDouble {
      get { return packedDouble_; }
    }

    /// <summary>Field number for the "packed_bool" field.</summary>
    public const int PackedBoolFieldNumber = 87;
    private static readonly pb::FieldCodec<bool> _repeated_packedBool_codec
        = pb::FieldCodec.ForBool(698);
    private readonly pbc::RepeatedField<bool> packedBool_ = new pbc::RepeatedField<bool>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<bool> PackedBool {
      get { return packedBool_; }
    }

    /// <summary>Field number for the "packed_nested_enum" field.</summary>
    public const int PackedNestedEnumFieldNumber = 88;
    private static readonly pb::FieldCodec<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum> _repeated_packedNestedEnum_codec
        = pb::FieldCodec.ForEnum(706, x => (int) x, x => (global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum) x);
    private readonly pbc::RepeatedField<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum> packedNestedEnum_ = new pbc::RepeatedField<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum> PackedNestedEnum {
      get { return packedNestedEnum_; }
    }

    /// <summary>Field number for the "unpacked_int32" field.</summary>
    public const int UnpackedInt32FieldNumber = 89;
    private static readonly pb::FieldCodec<int> _repeated_unpackedInt32_codec
        = pb::FieldCodec.ForInt32(712);
    private readonly pbc::RepeatedField<int> unpackedInt32_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// Unpacked
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> UnpackedInt32 {
      get { return unpackedInt32_; }
    }

    /// <summary>Field number for the "unpacked_int64" field.</summary>
    public const int UnpackedInt64FieldNumber = 90;
    private static readonly pb::FieldCodec<long> _repeated_unpackedInt64_codec
        = pb::FieldCodec.ForInt64(720);
    private readonly pbc::RepeatedField<long> unpackedInt64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> UnpackedInt64 {
      get { return unpackedInt64_; }
    }

    /// <summary>Field number for the "unpacked_uint32" field.</summary>
    public const int UnpackedUint32FieldNumber = 91;
    private static readonly pb::FieldCodec<uint> _repeated_unpackedUint32_codec
        = pb::FieldCodec.ForUInt32(728);
    private readonly pbc::RepeatedField<uint> unpackedUint32_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> UnpackedUint32 {
      get { return unpackedUint32_; }
    }

    /// <summary>Field number for the "unpacked_uint64" field.</summary>
    public const int UnpackedUint64FieldNumber = 92;
    private static readonly pb::FieldCodec<ulong> _repeated_unpackedUint64_codec
        = pb::FieldCodec.ForUInt64(736);
    private readonly pbc::RepeatedField<ulong> unpackedUint64_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> UnpackedUint64 {
      get { return unpackedUint64_; }
    }

    /// <summary>Field number for the "unpacked_sint32" field.</summary>
    public const int UnpackedSint32FieldNumber = 93;
    private static readonly pb::FieldCodec<int> _repeated_unpackedSint32_codec
        = pb::FieldCodec.ForSInt32(744);
    private readonly pbc::RepeatedField<int> unpackedSint32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> UnpackedSint32 {
      get { return unpackedSint32_; }
    }

    /// <summary>Field number for the "unpacked_sint64" field.</summary>
    public const int UnpackedSint64FieldNumber = 94;
    private static readonly pb::FieldCodec<long> _repeated_unpackedSint64_codec
        = pb::FieldCodec.ForSInt64(752);
    private readonly pbc::RepeatedField<long> unpackedSint64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> UnpackedSint64 {
      get { return unpackedSint64_; }
    }

    /// <summary>Field number for the "unpacked_fixed32" field.</summary>
    public const int UnpackedFixed32FieldNumber = 95;
    private static readonly pb::FieldCodec<uint> _repeated_unpackedFixed32_codec
        = pb::FieldCodec.ForFixed32(765);
    private readonly pbc::RepeatedField<uint> unpackedFixed32_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> UnpackedFixed32 {
      get { return unpackedFixed32_; }
    }

    /// <summary>Field number for the "unpacked_fixed64" field.</summary>
    public const int UnpackedFixed64FieldNumber = 96;
    private static readonly pb::FieldCodec<ulong> _repeated_unpackedFixed64_codec
        = pb::FieldCodec.ForFixed64(769);
    private readonly pbc::RepeatedField<ulong> unpackedFixed64_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> UnpackedFixed64 {
      get { return unpackedFixed64_; }
    }

    /// <summary>Field number for the "unpacked_sfixed32" field.</summary>
    public const int UnpackedSfixed32FieldNumber = 97;
    private static readonly pb::FieldCodec<int> _repeated_unpackedSfixed32_codec
        = pb::FieldCodec.ForSFixed32(781);
    private readonly pbc::RepeatedField<int> unpackedSfixed32_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> UnpackedSfixed32 {
      get { return unpackedSfixed32_; }
    }

    /// <summary>Field number for the "unpacked_sfixed64" field.</summary>
    public const int UnpackedSfixed64FieldNumber = 98;
    private static readonly pb::FieldCodec<long> _repeated_unpackedSfixed64_codec
        = pb::FieldCodec.ForSFixed64(785);
    private readonly pbc::RepeatedField<long> unpackedSfixed64_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> UnpackedSfixed64 {
      get { return unpackedSfixed64_; }
    }

    /// <summary>Field number for the "unpacked_float" field.</summary>
    public const int UnpackedFloatFieldNumber = 99;
    private static readonly pb::FieldCodec<float> _repeated_unpackedFloat_codec
        = pb::FieldCodec.ForFloat(797);
    private readonly pbc::RepeatedField<float> unpackedFloat_ = new pbc::RepeatedField<float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<float> UnpackedFloat {
      get { return unpackedFloat_; }
    }

    /// <summary>Field number for the "unpacked_double" field.</summary>
    public const int UnpackedDoubleFieldNumber = 100;
    private static readonly pb::FieldCodec<double> _repeated_unpackedDouble_codec
        = pb::FieldCodec.ForDouble(801);
    private readonly pbc::RepeatedField<double> unpackedDouble_ = new pbc::RepeatedField<double>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<double> UnpackedDouble {
      get { return unpackedDouble_; }
    }

    /// <summary>Field number for the "unpacked_bool" field.</summary>
    public const int UnpackedBoolFieldNumber = 101;
    private static readonly pb::FieldCodec<bool> _repeated_unpackedBool_codec
        = pb::FieldCodec.ForBool(808);
    private readonly pbc::RepeatedField<bool> unpackedBool_ = new pbc::RepeatedField<bool>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<bool> UnpackedBool {
      get { return unpackedBool_; }
    }

    /// <summary>Field number for the "unpacked_nested_enum" field.</summary>
    public const int UnpackedNestedEnumFieldNumber = 102;
    private static readonly pb::FieldCodec<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum> _repeated_unpackedNestedEnum_codec
        = pb::FieldCodec.ForEnum(816, x => (int) x, x => (global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum) x);
    private readonly pbc::RepeatedField<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum> unpackedNestedEnum_ = new pbc::RepeatedField<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum> UnpackedNestedEnum {
      get { return unpackedNestedEnum_; }
    }

    /// <summary>Field number for the "map_int32_int32" field.</summary>
    public const int MapInt32Int32FieldNumber = 56;
    private static readonly pbc::MapField<int, int>.Codec _map_mapInt32Int32_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForInt32(16, 0), 450);
    private readonly pbc::MapField<int, int> mapInt32Int32_ = new pbc::MapField<int, int>();
    /// <summary>
    /// Map
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> MapInt32Int32 {
      get { return mapInt32Int32_; }
    }

    /// <summary>Field number for the "map_int64_int64" field.</summary>
    public const int MapInt64Int64FieldNumber = 57;
    private static readonly pbc::MapField<long, long>.Codec _map_mapInt64Int64_codec
        = new pbc::MapField<long, long>.Codec(pb::FieldCodec.ForInt64(8, 0L), pb::FieldCodec.ForInt64(16, 0L), 458);
    private readonly pbc::MapField<long, long> mapInt64Int64_ = new pbc::MapField<long, long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, long> MapInt64Int64 {
      get { return mapInt64Int64_; }
    }

    /// <summary>Field number for the "map_uint32_uint32" field.</summary>
    public const int MapUint32Uint32FieldNumber = 58;
    private static readonly pbc::MapField<uint, uint>.Codec _map_mapUint32Uint32_codec
        = new pbc::MapField<uint, uint>.Codec(pb::FieldCodec.ForUInt32(8, 0), pb::FieldCodec.ForUInt32(16, 0), 466);
    private readonly pbc::MapField<uint, uint> mapUint32Uint32_ = new pbc::MapField<uint, uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<uint, uint> MapUint32Uint32 {
      get { return mapUint32Uint32_; }
    }

    /// <summary>Field number for the "map_uint64_uint64" field.</summary>
    public const int MapUint64Uint64FieldNumber = 59;
    private static readonly pbc::MapField<ulong, ulong>.Codec _map_mapUint64Uint64_codec
        = new pbc::MapField<ulong, ulong>.Codec(pb::FieldCodec.ForUInt64(8, 0UL), pb::FieldCodec.ForUInt64(16, 0UL), 474);
    private readonly pbc::MapField<ulong, ulong> mapUint64Uint64_ = new pbc::MapField<ulong, ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<ulong, ulong> MapUint64Uint64 {
      get { return mapUint64Uint64_; }
    }

    /// <summary>Field number for the "map_sint32_sint32" field.</summary>
    public const int MapSint32Sint32FieldNumber = 60;
    private static readonly pbc::MapField<int, int>.Codec _map_mapSint32Sint32_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForSInt32(8, 0), pb::FieldCodec.ForSInt32(16, 0), 482);
    private readonly pbc::MapField<int, int> mapSint32Sint32_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> MapSint32Sint32 {
      get { return mapSint32Sint32_; }
    }

    /// <summary>Field number for the "map_sint64_sint64" field.</summary>
    public const int MapSint64Sint64FieldNumber = 61;
    private static readonly pbc::MapField<long, long>.Codec _map_mapSint64Sint64_codec
        = new pbc::MapField<long, long>.Codec(pb::FieldCodec.ForSInt64(8, 0L), pb::FieldCodec.ForSInt64(16, 0L), 490);
    private readonly pbc::MapField<long, long> mapSint64Sint64_ = new pbc::MapField<long, long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, long> MapSint64Sint64 {
      get { return mapSint64Sint64_; }
    }

    /// <summary>Field number for the "map_fixed32_fixed32" field.</summary>
    public const int MapFixed32Fixed32FieldNumber = 62;
    private static readonly pbc::MapField<uint, uint>.Codec _map_mapFixed32Fixed32_codec
        = new pbc::MapField<uint, uint>.Codec(pb::FieldCodec.ForFixed32(13, 0), pb::FieldCodec.ForFixed32(21, 0), 498);
    private readonly pbc::MapField<uint, uint> mapFixed32Fixed32_ = new pbc::MapField<uint, uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<uint, uint> MapFixed32Fixed32 {
      get { return mapFixed32Fixed32_; }
    }

    /// <summary>Field number for the "map_fixed64_fixed64" field.</summary>
    public const int MapFixed64Fixed64FieldNumber = 63;
    private static readonly pbc::MapField<ulong, ulong>.Codec _map_mapFixed64Fixed64_codec
        = new pbc::MapField<ulong, ulong>.Codec(pb::FieldCodec.ForFixed64(9, 0UL), pb::FieldCodec.ForFixed64(17, 0UL), 506);
    private readonly pbc::MapField<ulong, ulong> mapFixed64Fixed64_ = new pbc::MapField<ulong, ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<ulong, ulong> MapFixed64Fixed64 {
      get { return mapFixed64Fixed64_; }
    }

    /// <summary>Field number for the "map_sfixed32_sfixed32" field.</summary>
    public const int MapSfixed32Sfixed32FieldNumber = 64;
    private static readonly pbc::MapField<int, int>.Codec _map_mapSfixed32Sfixed32_codec
        = new pbc::MapField<int, int>.Codec(pb::FieldCodec.ForSFixed32(13, 0), pb::FieldCodec.ForSFixed32(21, 0), 514);
    private readonly pbc::MapField<int, int> mapSfixed32Sfixed32_ = new pbc::MapField<int, int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, int> MapSfixed32Sfixed32 {
      get { return mapSfixed32Sfixed32_; }
    }

    /// <summary>Field number for the "map_sfixed64_sfixed64" field.</summary>
    public const int MapSfixed64Sfixed64FieldNumber = 65;
    private static readonly pbc::MapField<long, long>.Codec _map_mapSfixed64Sfixed64_codec
        = new pbc::MapField<long, long>.Codec(pb::FieldCodec.ForSFixed64(9, 0L), pb::FieldCodec.ForSFixed64(17, 0L), 522);
    private readonly pbc::MapField<long, long> mapSfixed64Sfixed64_ = new pbc::MapField<long, long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, long> MapSfixed64Sfixed64 {
      get { return mapSfixed64Sfixed64_; }
    }

    /// <summary>Field number for the "map_int32_float" field.</summary>
    public const int MapInt32FloatFieldNumber = 66;
    private static readonly pbc::MapField<int, float>.Codec _map_mapInt32Float_codec
        = new pbc::MapField<int, float>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForFloat(21, 0F), 530);
    private readonly pbc::MapField<int, float> mapInt32Float_ = new pbc::MapField<int, float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, float> MapInt32Float {
      get { return mapInt32Float_; }
    }

    /// <summary>Field number for the "map_int32_double" field.</summary>
    public const int MapInt32DoubleFieldNumber = 67;
    private static readonly pbc::MapField<int, double>.Codec _map_mapInt32Double_codec
        = new pbc::MapField<int, double>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForDouble(17, 0D), 538);
    private readonly pbc::MapField<int, double> mapInt32Double_ = new pbc::MapField<int, double>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, double> MapInt32Double {
      get { return mapInt32Double_; }
    }

    /// <summary>Field number for the "map_bool_bool" field.</summary>
    public const int MapBoolBoolFieldNumber = 68;
    private static readonly pbc::MapField<bool, bool>.Codec _map_mapBoolBool_codec
        = new pbc::MapField<bool, bool>.Codec(pb::FieldCodec.ForBool(8, false), pb::FieldCodec.ForBool(16, false), 546);
    private readonly pbc::MapField<bool, bool> mapBoolBool_ = new pbc::MapField<bool, bool>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<bool, bool> MapBoolBool {
      get { return mapBoolBool_; }
    }

    /// <summary>Field number for the "map_string_string" field.</summary>
    public const int MapStringStringFieldNumber = 69;
    private static readonly pbc::MapField<string, string>.Codec _map_mapStringString_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 554);
    private readonly pbc::MapField<string, string> mapStringString_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> MapStringString {
      get { return mapStringString_; }
    }

    /// <summary>Field number for the "map_string_bytes" field.</summary>
    public const int MapStringBytesFieldNumber = 70;
    private static readonly pbc::MapField<string, pb::ByteString>.Codec _map_mapStringBytes_codec
        = new pbc::MapField<string, pb::ByteString>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForBytes(18, pb::ByteString.Empty), 562);
    private readonly pbc::MapField<string, pb::ByteString> mapStringBytes_ = new pbc::MapField<string, pb::ByteString>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, pb::ByteString> MapStringBytes {
      get { return mapStringBytes_; }
    }

    /// <summary>Field number for the "map_string_nested_message" field.</summary>
    public const int MapStringNestedMessageFieldNumber = 71;
    private static readonly pbc::MapField<string, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage>.Codec _map_mapStringNestedMessage_codec
        = new pbc::MapField<string, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForMessage(18, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage.Parser), 570);
    private readonly pbc::MapField<string, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage> mapStringNestedMessage_ = new pbc::MapField<string, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage> MapStringNestedMessage {
      get { return mapStringNestedMessage_; }
    }

    /// <summary>Field number for the "map_string_foreign_message" field.</summary>
    public const int MapStringForeignMessageFieldNumber = 72;
    private static readonly pbc::MapField<string, global::ProtobufTestMessages.Proto3.ForeignMessage>.Codec _map_mapStringForeignMessage_codec
        = new pbc::MapField<string, global::ProtobufTestMessages.Proto3.ForeignMessage>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForMessage(18, global::ProtobufTestMessages.Proto3.ForeignMessage.Parser), 578);
    private readonly pbc::MapField<string, global::ProtobufTestMessages.Proto3.ForeignMessage> mapStringForeignMessage_ = new pbc::MapField<string, global::ProtobufTestMessages.Proto3.ForeignMessage>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::ProtobufTestMessages.Proto3.ForeignMessage> MapStringForeignMessage {
      get { return mapStringForeignMessage_; }
    }

    /// <summary>Field number for the "map_string_nested_enum" field.</summary>
    public const int MapStringNestedEnumFieldNumber = 73;
    private static readonly pbc::MapField<string, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum>.Codec _map_mapStringNestedEnum_codec
        = new pbc::MapField<string, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForEnum(16, x => (int) x, x => (global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum) x, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum.Foo), 586);
    private readonly pbc::MapField<string, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum> mapStringNestedEnum_ = new pbc::MapField<string, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum> MapStringNestedEnum {
      get { return mapStringNestedEnum_; }
    }

    /// <summary>Field number for the "map_string_foreign_enum" field.</summary>
    public const int MapStringForeignEnumFieldNumber = 74;
    private static readonly pbc::MapField<string, global::ProtobufTestMessages.Proto3.ForeignEnum>.Codec _map_mapStringForeignEnum_codec
        = new pbc::MapField<string, global::ProtobufTestMessages.Proto3.ForeignEnum>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForEnum(16, x => (int) x, x => (global::ProtobufTestMessages.Proto3.ForeignEnum) x, global::ProtobufTestMessages.Proto3.ForeignEnum.ForeignFoo), 594);
    private readonly pbc::MapField<string, global::ProtobufTestMessages.Proto3.ForeignEnum> mapStringForeignEnum_ = new pbc::MapField<string, global::ProtobufTestMessages.Proto3.ForeignEnum>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::ProtobufTestMessages.Proto3.ForeignEnum> MapStringForeignEnum {
      get { return mapStringForeignEnum_; }
    }

    /// <summary>Field number for the "oneof_uint32" field.</summary>
    public const int OneofUint32FieldNumber = 111;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint OneofUint32 {
      get { return HasOneofUint32 ? (uint) oneofField_ : 0; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofUint32;
      }
    }
    /// <summary>Gets whether the "oneof_uint32" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofUint32 {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofUint32; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_uint32" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofUint32() {
      if (HasOneofUint32) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_nested_message" field.</summary>
    public const int OneofNestedMessageFieldNumber = 112;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage OneofNestedMessage {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage ? (global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage) oneofField_ : null; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = value == null ? OneofFieldOneofCase.None : OneofFieldOneofCase.OneofNestedMessage;
      }
    }

    /// <summary>Field number for the "oneof_string" field.</summary>
    public const int OneofStringFieldNumber = 113;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OneofString {
      get { return HasOneofString ? (string) oneofField_ : ""; }
      set {
        oneofField_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
        oneofFieldCase_ = OneofFieldOneofCase.OneofString;
      }
    }
    /// <summary>Gets whether the "oneof_string" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofString {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofString; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_string" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofString() {
      if (HasOneofString) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_bytes" field.</summary>
    public const int OneofBytesFieldNumber = 114;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString OneofBytes {
      get { return HasOneofBytes ? (pb::ByteString) oneofField_ : pb::ByteString.Empty; }
      set {
        oneofField_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
        oneofFieldCase_ = OneofFieldOneofCase.OneofBytes;
      }
    }
    /// <summary>Gets whether the "oneof_bytes" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofBytes {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofBytes; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_bytes" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofBytes() {
      if (HasOneofBytes) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_bool" field.</summary>
    public const int OneofBoolFieldNumber = 115;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool OneofBool {
      get { return HasOneofBool ? (bool) oneofField_ : false; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofBool;
      }
    }
    /// <summary>Gets whether the "oneof_bool" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofBool {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofBool; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_bool" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofBool() {
      if (HasOneofBool) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_uint64" field.</summary>
    public const int OneofUint64FieldNumber = 116;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong OneofUint64 {
      get { return HasOneofUint64 ? (ulong) oneofField_ : 0UL; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofUint64;
      }
    }
    /// <summary>Gets whether the "oneof_uint64" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofUint64 {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofUint64; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_uint64" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofUint64() {
      if (HasOneofUint64) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_float" field.</summary>
    public const int OneofFloatFieldNumber = 117;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float OneofFloat {
      get { return HasOneofFloat ? (float) oneofField_ : 0F; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofFloat;
      }
    }
    /// <summary>Gets whether the "oneof_float" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofFloat {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofFloat; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_float" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofFloat() {
      if (HasOneofFloat) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_double" field.</summary>
    public const int OneofDoubleFieldNumber = 118;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double OneofDouble {
      get { return HasOneofDouble ? (double) oneofField_ : 0D; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofDouble;
      }
    }
    /// <summary>Gets whether the "oneof_double" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofDouble {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofDouble; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_double" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofDouble() {
      if (HasOneofDouble) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_enum" field.</summary>
    public const int OneofEnumFieldNumber = 119;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum OneofEnum {
      get { return HasOneofEnum ? (global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum) oneofField_ : global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum.Foo; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofEnum;
      }
    }
    /// <summary>Gets whether the "oneof_enum" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofEnum {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofEnum; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_enum" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofEnum() {
      if (HasOneofEnum) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "oneof_null_value" field.</summary>
    public const int OneofNullValueFieldNumber = 120;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.NullValue OneofNullValue {
      get { return HasOneofNullValue ? (global::Google.Protobuf.WellKnownTypes.NullValue) oneofField_ : global::Google.Protobuf.WellKnownTypes.NullValue.NullValue; }
      set {
        oneofField_ = value;
        oneofFieldCase_ = OneofFieldOneofCase.OneofNullValue;
      }
    }
    /// <summary>Gets whether the "oneof_null_value" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasOneofNullValue {
      get { return oneofFieldCase_ == OneofFieldOneofCase.OneofNullValue; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "oneof_null_value" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofNullValue() {
      if (HasOneofNullValue) {
        ClearOneofField();
      }
    }

    /// <summary>Field number for the "optional_bool_wrapper" field.</summary>
    public const int OptionalBoolWrapperFieldNumber = 201;
    private static readonly pb::FieldCodec<bool?> _single_optionalBoolWrapper_codec = pb::FieldCodec.ForStructWrapper<bool>(1610);
    private bool? optionalBoolWrapper_;
    /// <summary>
    /// Well-known types
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool? OptionalBoolWrapper {
      get { return optionalBoolWrapper_; }
      set {
        optionalBoolWrapper_ = value;
      }
    }


    /// <summary>Field number for the "optional_int32_wrapper" field.</summary>
    public const int OptionalInt32WrapperFieldNumber = 202;
    private static readonly pb::FieldCodec<int?> _single_optionalInt32Wrapper_codec = pb::FieldCodec.ForStructWrapper<int>(1618);
    private int? optionalInt32Wrapper_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int? OptionalInt32Wrapper {
      get { return optionalInt32Wrapper_; }
      set {
        optionalInt32Wrapper_ = value;
      }
    }


    /// <summary>Field number for the "optional_int64_wrapper" field.</summary>
    public const int OptionalInt64WrapperFieldNumber = 203;
    private static readonly pb::FieldCodec<long?> _single_optionalInt64Wrapper_codec = pb::FieldCodec.ForStructWrapper<long>(1626);
    private long? optionalInt64Wrapper_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long? OptionalInt64Wrapper {
      get { return optionalInt64Wrapper_; }
      set {
        optionalInt64Wrapper_ = value;
      }
    }


    /// <summary>Field number for the "optional_uint32_wrapper" field.</summary>
    public const int OptionalUint32WrapperFieldNumber = 204;
    private static readonly pb::FieldCodec<uint?> _single_optionalUint32Wrapper_codec = pb::FieldCodec.ForStructWrapper<uint>(1634);
    private uint? optionalUint32Wrapper_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint? OptionalUint32Wrapper {
      get { return optionalUint32Wrapper_; }
      set {
        optionalUint32Wrapper_ = value;
      }
    }


    /// <summary>Field number for the "optional_uint64_wrapper" field.</summary>
    public const int OptionalUint64WrapperFieldNumber = 205;
    private static readonly pb::FieldCodec<ulong?> _single_optionalUint64Wrapper_codec = pb::FieldCodec.ForStructWrapper<ulong>(1642);
    private ulong? optionalUint64Wrapper_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong? OptionalUint64Wrapper {
      get { return optionalUint64Wrapper_; }
      set {
        optionalUint64Wrapper_ = value;
      }
    }


    /// <summary>Field number for the "optional_float_wrapper" field.</summary>
    public const int OptionalFloatWrapperFieldNumber = 206;
    private static readonly pb::FieldCodec<float?> _single_optionalFloatWrapper_codec = pb::FieldCodec.ForStructWrapper<float>(1650);
    private float? optionalFloatWrapper_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float? OptionalFloatWrapper {
      get { return optionalFloatWrapper_; }
      set {
        optionalFloatWrapper_ = value;
      }
    }


    /// <summary>Field number for the "optional_double_wrapper" field.</summary>
    public const int OptionalDoubleWrapperFieldNumber = 207;
    private static readonly pb::FieldCodec<double?> _single_optionalDoubleWrapper_codec = pb::FieldCodec.ForStructWrapper<double>(1658);
    private double? optionalDoubleWrapper_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double? OptionalDoubleWrapper {
      get { return optionalDoubleWrapper_; }
      set {
        optionalDoubleWrapper_ = value;
      }
    }


    /// <summary>Field number for the "optional_string_wrapper" field.</summary>
    public const int OptionalStringWrapperFieldNumber = 208;
    private static readonly pb::FieldCodec<string> _single_optionalStringWrapper_codec = pb::FieldCodec.ForClassWrapper<string>(1666);
    private string optionalStringWrapper_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OptionalStringWrapper {
      get { return optionalStringWrapper_; }
      set {
        optionalStringWrapper_ = value;
      }
    }


    /// <summary>Field number for the "optional_bytes_wrapper" field.</summary>
    public const int OptionalBytesWrapperFieldNumber = 209;
    private static readonly pb::FieldCodec<pb::ByteString> _single_optionalBytesWrapper_codec = pb::FieldCodec.ForClassWrapper<pb::ByteString>(1674);
    private pb::ByteString optionalBytesWrapper_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString OptionalBytesWrapper {
      get { return optionalBytesWrapper_; }
      set {
        optionalBytesWrapper_ = value;
      }
    }


    /// <summary>Field number for the "repeated_bool_wrapper" field.</summary>
    public const int RepeatedBoolWrapperFieldNumber = 211;
    private static readonly pb::FieldCodec<bool?> _repeated_repeatedBoolWrapper_codec
        = pb::FieldCodec.ForStructWrapper<bool>(1690);
    private readonly pbc::RepeatedField<bool?> repeatedBoolWrapper_ = new pbc::RepeatedField<bool?>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<bool?> RepeatedBoolWrapper {
      get { return repeatedBoolWrapper_; }
    }

    /// <summary>Field number for the "repeated_int32_wrapper" field.</summary>
    public const int RepeatedInt32WrapperFieldNumber = 212;
    private static readonly pb::FieldCodec<int?> _repeated_repeatedInt32Wrapper_codec
        = pb::FieldCodec.ForStructWrapper<int>(1698);
    private readonly pbc::RepeatedField<int?> repeatedInt32Wrapper_ = new pbc::RepeatedField<int?>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int?> RepeatedInt32Wrapper {
      get { return repeatedInt32Wrapper_; }
    }

    /// <summary>Field number for the "repeated_int64_wrapper" field.</summary>
    public const int RepeatedInt64WrapperFieldNumber = 213;
    private static readonly pb::FieldCodec<long?> _repeated_repeatedInt64Wrapper_codec
        = pb::FieldCodec.ForStructWrapper<long>(1706);
    private readonly pbc::RepeatedField<long?> repeatedInt64Wrapper_ = new pbc::RepeatedField<long?>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long?> RepeatedInt64Wrapper {
      get { return repeatedInt64Wrapper_; }
    }

    /// <summary>Field number for the "repeated_uint32_wrapper" field.</summary>
    public const int RepeatedUint32WrapperFieldNumber = 214;
    private static readonly pb::FieldCodec<uint?> _repeated_repeatedUint32Wrapper_codec
        = pb::FieldCodec.ForStructWrapper<uint>(1714);
    private readonly pbc::RepeatedField<uint?> repeatedUint32Wrapper_ = new pbc::RepeatedField<uint?>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint?> RepeatedUint32Wrapper {
      get { return repeatedUint32Wrapper_; }
    }

    /// <summary>Field number for the "repeated_uint64_wrapper" field.</summary>
    public const int RepeatedUint64WrapperFieldNumber = 215;
    private static readonly pb::FieldCodec<ulong?> _repeated_repeatedUint64Wrapper_codec
        = pb::FieldCodec.ForStructWrapper<ulong>(1722);
    private readonly pbc::RepeatedField<ulong?> repeatedUint64Wrapper_ = new pbc::RepeatedField<ulong?>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong?> RepeatedUint64Wrapper {
      get { return repeatedUint64Wrapper_; }
    }

    /// <summary>Field number for the "repeated_float_wrapper" field.</summary>
    public const int RepeatedFloatWrapperFieldNumber = 216;
    private static readonly pb::FieldCodec<float?> _repeated_repeatedFloatWrapper_codec
        = pb::FieldCodec.ForStructWrapper<float>(1730);
    private readonly pbc::RepeatedField<float?> repeatedFloatWrapper_ = new pbc::RepeatedField<float?>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<float?> RepeatedFloatWrapper {
      get { return repeatedFloatWrapper_; }
    }

    /// <summary>Field number for the "repeated_double_wrapper" field.</summary>
    public const int RepeatedDoubleWrapperFieldNumber = 217;
    private static readonly pb::FieldCodec<double?> _repeated_repeatedDoubleWrapper_codec
        = pb::FieldCodec.ForStructWrapper<double>(1738);
    private readonly pbc::RepeatedField<double?> repeatedDoubleWrapper_ = new pbc::RepeatedField<double?>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<double?> RepeatedDoubleWrapper {
      get { return repeatedDoubleWrapper_; }
    }

    /// <summary>Field number for the "repeated_string_wrapper" field.</summary>
    public const int RepeatedStringWrapperFieldNumber = 218;
    private static readonly pb::FieldCodec<string> _repeated_repeatedStringWrapper_codec
        = pb::FieldCodec.ForClassWrapper<string>(1746);
    private readonly pbc::RepeatedField<string> repeatedStringWrapper_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> RepeatedStringWrapper {
      get { return repeatedStringWrapper_; }
    }

    /// <summary>Field number for the "repeated_bytes_wrapper" field.</summary>
    public const int RepeatedBytesWrapperFieldNumber = 219;
    private static readonly pb::FieldCodec<pb::ByteString> _repeated_repeatedBytesWrapper_codec
        = pb::FieldCodec.ForClassWrapper<pb::ByteString>(1754);
    private readonly pbc::RepeatedField<pb::ByteString> repeatedBytesWrapper_ = new pbc::RepeatedField<pb::ByteString>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<pb::ByteString> RepeatedBytesWrapper {
      get { return repeatedBytesWrapper_; }
    }

    /// <summary>Field number for the "optional_duration" field.</summary>
    public const int OptionalDurationFieldNumber = 301;
    private global::Google.Protobuf.WellKnownTypes.Duration optionalDuration_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.Duration OptionalDuration {
      get { return optionalDuration_; }
      set {
        optionalDuration_ = value;
      }
    }

    /// <summary>Field number for the "optional_timestamp" field.</summary>
    public const int OptionalTimestampFieldNumber = 302;
    private global::Google.Protobuf.WellKnownTypes.Timestamp optionalTimestamp_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.Timestamp OptionalTimestamp {
      get { return optionalTimestamp_; }
      set {
        optionalTimestamp_ = value;
      }
    }

    /// <summary>Field number for the "optional_field_mask" field.</summary>
    public const int OptionalFieldMaskFieldNumber = 303;
    private global::Google.Protobuf.WellKnownTypes.FieldMask optionalFieldMask_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.FieldMask OptionalFieldMask {
      get { return optionalFieldMask_; }
      set {
        optionalFieldMask_ = value;
      }
    }

    /// <summary>Field number for the "optional_struct" field.</summary>
    public const int OptionalStructFieldNumber = 304;
    private global::Google.Protobuf.WellKnownTypes.Struct optionalStruct_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.Struct OptionalStruct {
      get { return optionalStruct_; }
      set {
        optionalStruct_ = value;
      }
    }

    /// <summary>Field number for the "optional_any" field.</summary>
    public const int OptionalAnyFieldNumber = 305;
    private global::Google.Protobuf.WellKnownTypes.Any optionalAny_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.Any OptionalAny {
      get { return optionalAny_; }
      set {
        optionalAny_ = value;
      }
    }

    /// <summary>Field number for the "optional_value" field.</summary>
    public const int OptionalValueFieldNumber = 306;
    private global::Google.Protobuf.WellKnownTypes.Value optionalValue_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.Value OptionalValue {
      get { return optionalValue_; }
      set {
        optionalValue_ = value;
      }
    }

    /// <summary>Field number for the "optional_null_value" field.</summary>
    public const int OptionalNullValueFieldNumber = 307;
    private global::Google.Protobuf.WellKnownTypes.NullValue optionalNullValue_ = global::Google.Protobuf.WellKnownTypes.NullValue.NullValue;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.NullValue OptionalNullValue {
      get { return optionalNullValue_; }
      set {
        optionalNullValue_ = value;
      }
    }

    /// <summary>Field number for the "repeated_duration" field.</summary>
    public const int RepeatedDurationFieldNumber = 311;
    private static readonly pb::FieldCodec<global::Google.Protobuf.WellKnownTypes.Duration> _repeated_repeatedDuration_codec
        = pb::FieldCodec.ForMessage(2490, global::Google.Protobuf.WellKnownTypes.Duration.Parser);
    private readonly pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Duration> repeatedDuration_ = new pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Duration>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Duration> RepeatedDuration {
      get { return repeatedDuration_; }
    }

    /// <summary>Field number for the "repeated_timestamp" field.</summary>
    public const int RepeatedTimestampFieldNumber = 312;
    private static readonly pb::FieldCodec<global::Google.Protobuf.WellKnownTypes.Timestamp> _repeated_repeatedTimestamp_codec
        = pb::FieldCodec.ForMessage(2498, global::Google.Protobuf.WellKnownTypes.Timestamp.Parser);
    private readonly pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Timestamp> repeatedTimestamp_ = new pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Timestamp>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Timestamp> RepeatedTimestamp {
      get { return repeatedTimestamp_; }
    }

    /// <summary>Field number for the "repeated_fieldmask" field.</summary>
    public const int RepeatedFieldmaskFieldNumber = 313;
    private static readonly pb::FieldCodec<global::Google.Protobuf.WellKnownTypes.FieldMask> _repeated_repeatedFieldmask_codec
        = pb::FieldCodec.ForMessage(2506, global::Google.Protobuf.WellKnownTypes.FieldMask.Parser);
    private readonly pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.FieldMask> repeatedFieldmask_ = new pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.FieldMask>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.FieldMask> RepeatedFieldmask {
      get { return repeatedFieldmask_; }
    }

    /// <summary>Field number for the "repeated_struct" field.</summary>
    public const int RepeatedStructFieldNumber = 324;
    private static readonly pb::FieldCodec<global::Google.Protobuf.WellKnownTypes.Struct> _repeated_repeatedStruct_codec
        = pb::FieldCodec.ForMessage(2594, global::Google.Protobuf.WellKnownTypes.Struct.Parser);
    private readonly pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Struct> repeatedStruct_ = new pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Struct>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Struct> RepeatedStruct {
      get { return repeatedStruct_; }
    }

    /// <summary>Field number for the "repeated_any" field.</summary>
    public const int RepeatedAnyFieldNumber = 315;
    private static readonly pb::FieldCodec<global::Google.Protobuf.WellKnownTypes.Any> _repeated_repeatedAny_codec
        = pb::FieldCodec.ForMessage(2522, global::Google.Protobuf.WellKnownTypes.Any.Parser);
    private readonly pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Any> repeatedAny_ = new pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Any>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Any> RepeatedAny {
      get { return repeatedAny_; }
    }

    /// <summary>Field number for the "repeated_value" field.</summary>
    public const int RepeatedValueFieldNumber = 316;
    private static readonly pb::FieldCodec<global::Google.Protobuf.WellKnownTypes.Value> _repeated_repeatedValue_codec
        = pb::FieldCodec.ForMessage(2530, global::Google.Protobuf.WellKnownTypes.Value.Parser);
    private readonly pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Value> repeatedValue_ = new pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Value>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.Value> RepeatedValue {
      get { return repeatedValue_; }
    }

    /// <summary>Field number for the "repeated_list_value" field.</summary>
    public const int RepeatedListValueFieldNumber = 317;
    private static readonly pb::FieldCodec<global::Google.Protobuf.WellKnownTypes.ListValue> _repeated_repeatedListValue_codec
        = pb::FieldCodec.ForMessage(2538, global::Google.Protobuf.WellKnownTypes.ListValue.Parser);
    private readonly pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.ListValue> repeatedListValue_ = new pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.ListValue>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Google.Protobuf.WellKnownTypes.ListValue> RepeatedListValue {
      get { return repeatedListValue_; }
    }

    /// <summary>Field number for the "fieldname1" field.</summary>
    public const int Fieldname1FieldNumber = 401;
    private int fieldname1_;
    /// <summary>
    /// Test field-name-to-JSON-name convention.
    /// (protobuf says names can be any valid C/C++ identifier.)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Fieldname1 {
      get { return fieldname1_; }
      set {
        fieldname1_ = value;
      }
    }

    /// <summary>Field number for the "field_name2" field.</summary>
    public const int FieldName2FieldNumber = 402;
    private int fieldName2_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName2 {
      get { return fieldName2_; }
      set {
        fieldName2_ = value;
      }
    }

    /// <summary>Field number for the "_field_name3" field.</summary>
    public const int FieldName3FieldNumber = 403;
    private int FieldName3_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName3 {
      get { return FieldName3_; }
      set {
        FieldName3_ = value;
      }
    }

    /// <summary>Field number for the "field__name4_" field.</summary>
    public const int FieldName4FieldNumber = 404;
    private int fieldName4_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName4 {
      get { return fieldName4_; }
      set {
        fieldName4_ = value;
      }
    }

    /// <summary>Field number for the "field0name5" field.</summary>
    public const int Field0Name5FieldNumber = 405;
    private int field0Name5_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Field0Name5 {
      get { return field0Name5_; }
      set {
        field0Name5_ = value;
      }
    }

    /// <summary>Field number for the "field_0_name6" field.</summary>
    public const int Field0Name6FieldNumber = 406;
    private int field0Name6_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Field0Name6 {
      get { return field0Name6_; }
      set {
        field0Name6_ = value;
      }
    }

    /// <summary>Field number for the "fieldName7" field.</summary>
    public const int FieldName7FieldNumber = 407;
    private int fieldName7_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName7 {
      get { return fieldName7_; }
      set {
        fieldName7_ = value;
      }
    }

    /// <summary>Field number for the "FieldName8" field.</summary>
    public const int FieldName8FieldNumber = 408;
    private int fieldName8_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName8 {
      get { return fieldName8_; }
      set {
        fieldName8_ = value;
      }
    }

    /// <summary>Field number for the "field_Name9" field.</summary>
    public const int FieldName9FieldNumber = 409;
    private int fieldName9_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName9 {
      get { return fieldName9_; }
      set {
        fieldName9_ = value;
      }
    }

    /// <summary>Field number for the "Field_Name10" field.</summary>
    public const int FieldName10FieldNumber = 410;
    private int fieldName10_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName10 {
      get { return fieldName10_; }
      set {
        fieldName10_ = value;
      }
    }

    /// <summary>Field number for the "FIELD_NAME11" field.</summary>
    public const int FIELDNAME11FieldNumber = 411;
    private int fIELDNAME11_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FIELDNAME11 {
      get { return fIELDNAME11_; }
      set {
        fIELDNAME11_ = value;
      }
    }

    /// <summary>Field number for the "FIELD_name12" field.</summary>
    public const int FIELDName12FieldNumber = 412;
    private int fIELDName12_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FIELDName12 {
      get { return fIELDName12_; }
      set {
        fIELDName12_ = value;
      }
    }

    /// <summary>Field number for the "__field_name13" field.</summary>
    public const int FieldName13FieldNumber = 413;
    private int FieldName13_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName13 {
      get { return FieldName13_; }
      set {
        FieldName13_ = value;
      }
    }

    /// <summary>Field number for the "__Field_name14" field.</summary>
    public const int FieldName14FieldNumber = 414;
    private int FieldName14_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName14 {
      get { return FieldName14_; }
      set {
        FieldName14_ = value;
      }
    }

    /// <summary>Field number for the "field__name15" field.</summary>
    public const int FieldName15FieldNumber = 415;
    private int fieldName15_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName15 {
      get { return fieldName15_; }
      set {
        fieldName15_ = value;
      }
    }

    /// <summary>Field number for the "field__Name16" field.</summary>
    public const int FieldName16FieldNumber = 416;
    private int fieldName16_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName16 {
      get { return fieldName16_; }
      set {
        fieldName16_ = value;
      }
    }

    /// <summary>Field number for the "field_name17__" field.</summary>
    public const int FieldName17FieldNumber = 417;
    private int fieldName17_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName17 {
      get { return fieldName17_; }
      set {
        fieldName17_ = value;
      }
    }

    /// <summary>Field number for the "Field_name18__" field.</summary>
    public const int FieldName18FieldNumber = 418;
    private int fieldName18_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FieldName18 {
      get { return fieldName18_; }
      set {
        fieldName18_ = value;
      }
    }

    private object oneofField_;
    /// <summary>Enum of possible cases for the "oneof_field" oneof.</summary>
    public enum OneofFieldOneofCase {
      None = 0,
      OneofUint32 = 111,
      OneofNestedMessage = 112,
      OneofString = 113,
      OneofBytes = 114,
      OneofBool = 115,
      OneofUint64 = 116,
      OneofFloat = 117,
      OneofDouble = 118,
      OneofEnum = 119,
      OneofNullValue = 120,
    }
    private OneofFieldOneofCase oneofFieldCase_ = OneofFieldOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public OneofFieldOneofCase OneofFieldCase {
      get { return oneofFieldCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearOneofField() {
      oneofFieldCase_ = OneofFieldOneofCase.None;
      oneofField_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TestAllTypesProto3);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TestAllTypesProto3 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (OptionalInt32 != other.OptionalInt32) return false;
      if (OptionalInt64 != other.OptionalInt64) return false;
      if (OptionalUint32 != other.OptionalUint32) return false;
      if (OptionalUint64 != other.OptionalUint64) return false;
      if (OptionalSint32 != other.OptionalSint32) return false;
      if (OptionalSint64 != other.OptionalSint64) return false;
      if (OptionalFixed32 != other.OptionalFixed32) return false;
      if (OptionalFixed64 != other.OptionalFixed64) return false;
      if (OptionalSfixed32 != other.OptionalSfixed32) return false;
      if (OptionalSfixed64 != other.OptionalSfixed64) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(OptionalFloat, other.OptionalFloat)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(OptionalDouble, other.OptionalDouble)) return false;
      if (OptionalBool != other.OptionalBool) return false;
      if (OptionalString != other.OptionalString) return false;
      if (OptionalBytes != other.OptionalBytes) return false;
      if (!object.Equals(OptionalNestedMessage, other.OptionalNestedMessage)) return false;
      if (!object.Equals(OptionalForeignMessage, other.OptionalForeignMessage)) return false;
      if (OptionalNestedEnum != other.OptionalNestedEnum) return false;
      if (OptionalForeignEnum != other.OptionalForeignEnum) return false;
      if (OptionalAliasedEnum != other.OptionalAliasedEnum) return false;
      if (OptionalStringPiece != other.OptionalStringPiece) return false;
      if (OptionalCord != other.OptionalCord) return false;
      if (!object.Equals(RecursiveMessage, other.RecursiveMessage)) return false;
      if(!repeatedInt32_.Equals(other.repeatedInt32_)) return false;
      if(!repeatedInt64_.Equals(other.repeatedInt64_)) return false;
      if(!repeatedUint32_.Equals(other.repeatedUint32_)) return false;
      if(!repeatedUint64_.Equals(other.repeatedUint64_)) return false;
      if(!repeatedSint32_.Equals(other.repeatedSint32_)) return false;
      if(!repeatedSint64_.Equals(other.repeatedSint64_)) return false;
      if(!repeatedFixed32_.Equals(other.repeatedFixed32_)) return false;
      if(!repeatedFixed64_.Equals(other.repeatedFixed64_)) return false;
      if(!repeatedSfixed32_.Equals(other.repeatedSfixed32_)) return false;
      if(!repeatedSfixed64_.Equals(other.repeatedSfixed64_)) return false;
      if(!repeatedFloat_.Equals(other.repeatedFloat_)) return false;
      if(!repeatedDouble_.Equals(other.repeatedDouble_)) return false;
      if(!repeatedBool_.Equals(other.repeatedBool_)) return false;
      if(!repeatedString_.Equals(other.repeatedString_)) return false;
      if(!repeatedBytes_.Equals(other.repeatedBytes_)) return false;
      if(!repeatedNestedMessage_.Equals(other.repeatedNestedMessage_)) return false;
      if(!repeatedForeignMessage_.Equals(other.repeatedForeignMessage_)) return false;
      if(!repeatedNestedEnum_.Equals(other.repeatedNestedEnum_)) return false;
      if(!repeatedForeignEnum_.Equals(other.repeatedForeignEnum_)) return false;
      if(!repeatedStringPiece_.Equals(other.repeatedStringPiece_)) return false;
      if(!repeatedCord_.Equals(other.repeatedCord_)) return false;
      if(!packedInt32_.Equals(other.packedInt32_)) return false;
      if(!packedInt64_.Equals(other.packedInt64_)) return false;
      if(!packedUint32_.Equals(other.packedUint32_)) return false;
      if(!packedUint64_.Equals(other.packedUint64_)) return false;
      if(!packedSint32_.Equals(other.packedSint32_)) return false;
      if(!packedSint64_.Equals(other.packedSint64_)) return false;
      if(!packedFixed32_.Equals(other.packedFixed32_)) return false;
      if(!packedFixed64_.Equals(other.packedFixed64_)) return false;
      if(!packedSfixed32_.Equals(other.packedSfixed32_)) return false;
      if(!packedSfixed64_.Equals(other.packedSfixed64_)) return false;
      if(!packedFloat_.Equals(other.packedFloat_)) return false;
      if(!packedDouble_.Equals(other.packedDouble_)) return false;
      if(!packedBool_.Equals(other.packedBool_)) return false;
      if(!packedNestedEnum_.Equals(other.packedNestedEnum_)) return false;
      if(!unpackedInt32_.Equals(other.unpackedInt32_)) return false;
      if(!unpackedInt64_.Equals(other.unpackedInt64_)) return false;
      if(!unpackedUint32_.Equals(other.unpackedUint32_)) return false;
      if(!unpackedUint64_.Equals(other.unpackedUint64_)) return false;
      if(!unpackedSint32_.Equals(other.unpackedSint32_)) return false;
      if(!unpackedSint64_.Equals(other.unpackedSint64_)) return false;
      if(!unpackedFixed32_.Equals(other.unpackedFixed32_)) return false;
      if(!unpackedFixed64_.Equals(other.unpackedFixed64_)) return false;
      if(!unpackedSfixed32_.Equals(other.unpackedSfixed32_)) return false;
      if(!unpackedSfixed64_.Equals(other.unpackedSfixed64_)) return false;
      if(!unpackedFloat_.Equals(other.unpackedFloat_)) return false;
      if(!unpackedDouble_.Equals(other.unpackedDouble_)) return false;
      if(!unpackedBool_.Equals(other.unpackedBool_)) return false;
      if(!unpackedNestedEnum_.Equals(other.unpackedNestedEnum_)) return false;
      if (!MapInt32Int32.Equals(other.MapInt32Int32)) return false;
      if (!MapInt64Int64.Equals(other.MapInt64Int64)) return false;
      if (!MapUint32Uint32.Equals(other.MapUint32Uint32)) return false;
      if (!MapUint64Uint64.Equals(other.MapUint64Uint64)) return false;
      if (!MapSint32Sint32.Equals(other.MapSint32Sint32)) return false;
      if (!MapSint64Sint64.Equals(other.MapSint64Sint64)) return false;
      if (!MapFixed32Fixed32.Equals(other.MapFixed32Fixed32)) return false;
      if (!MapFixed64Fixed64.Equals(other.MapFixed64Fixed64)) return false;
      if (!MapSfixed32Sfixed32.Equals(other.MapSfixed32Sfixed32)) return false;
      if (!MapSfixed64Sfixed64.Equals(other.MapSfixed64Sfixed64)) return false;
      if (!MapInt32Float.Equals(other.MapInt32Float)) return false;
      if (!MapInt32Double.Equals(other.MapInt32Double)) return false;
      if (!MapBoolBool.Equals(other.MapBoolBool)) return false;
      if (!MapStringString.Equals(other.MapStringString)) return false;
      if (!MapStringBytes.Equals(other.MapStringBytes)) return false;
      if (!MapStringNestedMessage.Equals(other.MapStringNestedMessage)) return false;
      if (!MapStringForeignMessage.Equals(other.MapStringForeignMessage)) return false;
      if (!MapStringNestedEnum.Equals(other.MapStringNestedEnum)) return false;
      if (!MapStringForeignEnum.Equals(other.MapStringForeignEnum)) return false;
      if (OneofUint32 != other.OneofUint32) return false;
      if (!object.Equals(OneofNestedMessage, other.OneofNestedMessage)) return false;
      if (OneofString != other.OneofString) return false;
      if (OneofBytes != other.OneofBytes) return false;
      if (OneofBool != other.OneofBool) return false;
      if (OneofUint64 != other.OneofUint64) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(OneofFloat, other.OneofFloat)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(OneofDouble, other.OneofDouble)) return false;
      if (OneofEnum != other.OneofEnum) return false;
      if (OneofNullValue != other.OneofNullValue) return false;
      if (OptionalBoolWrapper != other.OptionalBoolWrapper) return false;
      if (OptionalInt32Wrapper != other.OptionalInt32Wrapper) return false;
      if (OptionalInt64Wrapper != other.OptionalInt64Wrapper) return false;
      if (OptionalUint32Wrapper != other.OptionalUint32Wrapper) return false;
      if (OptionalUint64Wrapper != other.OptionalUint64Wrapper) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseNullableSingleEqualityComparer.Equals(OptionalFloatWrapper, other.OptionalFloatWrapper)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseNullableDoubleEqualityComparer.Equals(OptionalDoubleWrapper, other.OptionalDoubleWrapper)) return false;
      if (OptionalStringWrapper != other.OptionalStringWrapper) return false;
      if (OptionalBytesWrapper != other.OptionalBytesWrapper) return false;
      if(!repeatedBoolWrapper_.Equals(other.repeatedBoolWrapper_)) return false;
      if(!repeatedInt32Wrapper_.Equals(other.repeatedInt32Wrapper_)) return false;
      if(!repeatedInt64Wrapper_.Equals(other.repeatedInt64Wrapper_)) return false;
      if(!repeatedUint32Wrapper_.Equals(other.repeatedUint32Wrapper_)) return false;
      if(!repeatedUint64Wrapper_.Equals(other.repeatedUint64Wrapper_)) return false;
      if(!repeatedFloatWrapper_.Equals(other.repeatedFloatWrapper_)) return false;
      if(!repeatedDoubleWrapper_.Equals(other.repeatedDoubleWrapper_)) return false;
      if(!repeatedStringWrapper_.Equals(other.repeatedStringWrapper_)) return false;
      if(!repeatedBytesWrapper_.Equals(other.repeatedBytesWrapper_)) return false;
      if (!object.Equals(OptionalDuration, other.OptionalDuration)) return false;
      if (!object.Equals(OptionalTimestamp, other.OptionalTimestamp)) return false;
      if (!object.Equals(OptionalFieldMask, other.OptionalFieldMask)) return false;
      if (!object.Equals(OptionalStruct, other.OptionalStruct)) return false;
      if (!object.Equals(OptionalAny, other.OptionalAny)) return false;
      if (!object.Equals(OptionalValue, other.OptionalValue)) return false;
      if (OptionalNullValue != other.OptionalNullValue) return false;
      if(!repeatedDuration_.Equals(other.repeatedDuration_)) return false;
      if(!repeatedTimestamp_.Equals(other.repeatedTimestamp_)) return false;
      if(!repeatedFieldmask_.Equals(other.repeatedFieldmask_)) return false;
      if(!repeatedStruct_.Equals(other.repeatedStruct_)) return false;
      if(!repeatedAny_.Equals(other.repeatedAny_)) return false;
      if(!repeatedValue_.Equals(other.repeatedValue_)) return false;
      if(!repeatedListValue_.Equals(other.repeatedListValue_)) return false;
      if (Fieldname1 != other.Fieldname1) return false;
      if (FieldName2 != other.FieldName2) return false;
      if (FieldName3 != other.FieldName3) return false;
      if (FieldName4 != other.FieldName4) return false;
      if (Field0Name5 != other.Field0Name5) return false;
      if (Field0Name6 != other.Field0Name6) return false;
      if (FieldName7 != other.FieldName7) return false;
      if (FieldName8 != other.FieldName8) return false;
      if (FieldName9 != other.FieldName9) return false;
      if (FieldName10 != other.FieldName10) return false;
      if (FIELDNAME11 != other.FIELDNAME11) return false;
      if (FIELDName12 != other.FIELDName12) return false;
      if (FieldName13 != other.FieldName13) return false;
      if (FieldName14 != other.FieldName14) return false;
      if (FieldName15 != other.FieldName15) return false;
      if (FieldName16 != other.FieldName16) return false;
      if (FieldName17 != other.FieldName17) return false;
      if (FieldName18 != other.FieldName18) return false;
      if (OneofFieldCase != other.OneofFieldCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (OptionalInt32 != 0) hash ^= OptionalInt32.GetHashCode();
      if (OptionalInt64 != 0L) hash ^= OptionalInt64.GetHashCode();
      if (OptionalUint32 != 0) hash ^= OptionalUint32.GetHashCode();
      if (OptionalUint64 != 0UL) hash ^= OptionalUint64.GetHashCode();
      if (OptionalSint32 != 0) hash ^= OptionalSint32.GetHashCode();
      if (OptionalSint64 != 0L) hash ^= OptionalSint64.GetHashCode();
      if (OptionalFixed32 != 0) hash ^= OptionalFixed32.GetHashCode();
      if (OptionalFixed64 != 0UL) hash ^= OptionalFixed64.GetHashCode();
      if (OptionalSfixed32 != 0) hash ^= OptionalSfixed32.GetHashCode();
      if (OptionalSfixed64 != 0L) hash ^= OptionalSfixed64.GetHashCode();
      if (OptionalFloat != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(OptionalFloat);
      if (OptionalDouble != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(OptionalDouble);
      if (OptionalBool != false) hash ^= OptionalBool.GetHashCode();
      if (OptionalString.Length != 0) hash ^= OptionalString.GetHashCode();
      if (OptionalBytes.Length != 0) hash ^= OptionalBytes.GetHashCode();
      if (optionalNestedMessage_ != null) hash ^= OptionalNestedMessage.GetHashCode();
      if (optionalForeignMessage_ != null) hash ^= OptionalForeignMessage.GetHashCode();
      if (OptionalNestedEnum != global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum.Foo) hash ^= OptionalNestedEnum.GetHashCode();
      if (OptionalForeignEnum != global::ProtobufTestMessages.Proto3.ForeignEnum.ForeignFoo) hash ^= OptionalForeignEnum.GetHashCode();
      if (OptionalAliasedEnum != global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.AliasedEnum.AliasFoo) hash ^= OptionalAliasedEnum.GetHashCode();
      if (OptionalStringPiece.Length != 0) hash ^= OptionalStringPiece.GetHashCode();
      if (OptionalCord.Length != 0) hash ^= OptionalCord.GetHashCode();
      if (recursiveMessage_ != null) hash ^= RecursiveMessage.GetHashCode();
      hash ^= repeatedInt32_.GetHashCode();
      hash ^= repeatedInt64_.GetHashCode();
      hash ^= repeatedUint32_.GetHashCode();
      hash ^= repeatedUint64_.GetHashCode();
      hash ^= repeatedSint32_.GetHashCode();
      hash ^= repeatedSint64_.GetHashCode();
      hash ^= repeatedFixed32_.GetHashCode();
      hash ^= repeatedFixed64_.GetHashCode();
      hash ^= repeatedSfixed32_.GetHashCode();
      hash ^= repeatedSfixed64_.GetHashCode();
      hash ^= repeatedFloat_.GetHashCode();
      hash ^= repeatedDouble_.GetHashCode();
      hash ^= repeatedBool_.GetHashCode();
      hash ^= repeatedString_.GetHashCode();
      hash ^= repeatedBytes_.GetHashCode();
      hash ^= repeatedNestedMessage_.GetHashCode();
      hash ^= repeatedForeignMessage_.GetHashCode();
      hash ^= repeatedNestedEnum_.GetHashCode();
      hash ^= repeatedForeignEnum_.GetHashCode();
      hash ^= repeatedStringPiece_.GetHashCode();
      hash ^= repeatedCord_.GetHashCode();
      hash ^= packedInt32_.GetHashCode();
      hash ^= packedInt64_.GetHashCode();
      hash ^= packedUint32_.GetHashCode();
      hash ^= packedUint64_.GetHashCode();
      hash ^= packedSint32_.GetHashCode();
      hash ^= packedSint64_.GetHashCode();
      hash ^= packedFixed32_.GetHashCode();
      hash ^= packedFixed64_.GetHashCode();
      hash ^= packedSfixed32_.GetHashCode();
      hash ^= packedSfixed64_.GetHashCode();
      hash ^= packedFloat_.GetHashCode();
      hash ^= packedDouble_.GetHashCode();
      hash ^= packedBool_.GetHashCode();
      hash ^= packedNestedEnum_.GetHashCode();
      hash ^= unpackedInt32_.GetHashCode();
      hash ^= unpackedInt64_.GetHashCode();
      hash ^= unpackedUint32_.GetHashCode();
      hash ^= unpackedUint64_.GetHashCode();
      hash ^= unpackedSint32_.GetHashCode();
      hash ^= unpackedSint64_.GetHashCode();
      hash ^= unpackedFixed32_.GetHashCode();
      hash ^= unpackedFixed64_.GetHashCode();
      hash ^= unpackedSfixed32_.GetHashCode();
      hash ^= unpackedSfixed64_.GetHashCode();
      hash ^= unpackedFloat_.GetHashCode();
      hash ^= unpackedDouble_.GetHashCode();
      hash ^= unpackedBool_.GetHashCode();
      hash ^= unpackedNestedEnum_.GetHashCode();
      hash ^= MapInt32Int32.GetHashCode();
      hash ^= MapInt64Int64.GetHashCode();
      hash ^= MapUint32Uint32.GetHashCode();
      hash ^= MapUint64Uint64.GetHashCode();
      hash ^= MapSint32Sint32.GetHashCode();
      hash ^= MapSint64Sint64.GetHashCode();
      hash ^= MapFixed32Fixed32.GetHashCode();
      hash ^= MapFixed64Fixed64.GetHashCode();
      hash ^= MapSfixed32Sfixed32.GetHashCode();
      hash ^= MapSfixed64Sfixed64.GetHashCode();
      hash ^= MapInt32Float.GetHashCode();
      hash ^= MapInt32Double.GetHashCode();
      hash ^= MapBoolBool.GetHashCode();
      hash ^= MapStringString.GetHashCode();
      hash ^= MapStringBytes.GetHashCode();
      hash ^= MapStringNestedMessage.GetHashCode();
      hash ^= MapStringForeignMessage.GetHashCode();
      hash ^= MapStringNestedEnum.GetHashCode();
      hash ^= MapStringForeignEnum.GetHashCode();
      if (HasOneofUint32) hash ^= OneofUint32.GetHashCode();
      if (oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage) hash ^= OneofNestedMessage.GetHashCode();
      if (HasOneofString) hash ^= OneofString.GetHashCode();
      if (HasOneofBytes) hash ^= OneofBytes.GetHashCode();
      if (HasOneofBool) hash ^= OneofBool.GetHashCode();
      if (HasOneofUint64) hash ^= OneofUint64.GetHashCode();
      if (HasOneofFloat) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(OneofFloat);
      if (HasOneofDouble) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(OneofDouble);
      if (HasOneofEnum) hash ^= OneofEnum.GetHashCode();
      if (HasOneofNullValue) hash ^= OneofNullValue.GetHashCode();
      if (optionalBoolWrapper_ != null) hash ^= OptionalBoolWrapper.GetHashCode();
      if (optionalInt32Wrapper_ != null) hash ^= OptionalInt32Wrapper.GetHashCode();
      if (optionalInt64Wrapper_ != null) hash ^= OptionalInt64Wrapper.GetHashCode();
      if (optionalUint32Wrapper_ != null) hash ^= OptionalUint32Wrapper.GetHashCode();
      if (optionalUint64Wrapper_ != null) hash ^= OptionalUint64Wrapper.GetHashCode();
      if (optionalFloatWrapper_ != null) hash ^= pbc::ProtobufEqualityComparers.BitwiseNullableSingleEqualityComparer.GetHashCode(OptionalFloatWrapper);
      if (optionalDoubleWrapper_ != null) hash ^= pbc::ProtobufEqualityComparers.BitwiseNullableDoubleEqualityComparer.GetHashCode(OptionalDoubleWrapper);
      if (optionalStringWrapper_ != null) hash ^= OptionalStringWrapper.GetHashCode();
      if (optionalBytesWrapper_ != null) hash ^= OptionalBytesWrapper.GetHashCode();
      hash ^= repeatedBoolWrapper_.GetHashCode();
      hash ^= repeatedInt32Wrapper_.GetHashCode();
      hash ^= repeatedInt64Wrapper_.GetHashCode();
      hash ^= repeatedUint32Wrapper_.GetHashCode();
      hash ^= repeatedUint64Wrapper_.GetHashCode();
      hash ^= repeatedFloatWrapper_.GetHashCode();
      hash ^= repeatedDoubleWrapper_.GetHashCode();
      hash ^= repeatedStringWrapper_.GetHashCode();
      hash ^= repeatedBytesWrapper_.GetHashCode();
      if (optionalDuration_ != null) hash ^= OptionalDuration.GetHashCode();
      if (optionalTimestamp_ != null) hash ^= OptionalTimestamp.GetHashCode();
      if (optionalFieldMask_ != null) hash ^= OptionalFieldMask.GetHashCode();
      if (optionalStruct_ != null) hash ^= OptionalStruct.GetHashCode();
      if (optionalAny_ != null) hash ^= OptionalAny.GetHashCode();
      if (optionalValue_ != null) hash ^= OptionalValue.GetHashCode();
      if (OptionalNullValue != global::Google.Protobuf.WellKnownTypes.NullValue.NullValue) hash ^= OptionalNullValue.GetHashCode();
      hash ^= repeatedDuration_.GetHashCode();
      hash ^= repeatedTimestamp_.GetHashCode();
      hash ^= repeatedFieldmask_.GetHashCode();
      hash ^= repeatedStruct_.GetHashCode();
      hash ^= repeatedAny_.GetHashCode();
      hash ^= repeatedValue_.GetHashCode();
      hash ^= repeatedListValue_.GetHashCode();
      if (Fieldname1 != 0) hash ^= Fieldname1.GetHashCode();
      if (FieldName2 != 0) hash ^= FieldName2.GetHashCode();
      if (FieldName3 != 0) hash ^= FieldName3.GetHashCode();
      if (FieldName4 != 0) hash ^= FieldName4.GetHashCode();
      if (Field0Name5 != 0) hash ^= Field0Name5.GetHashCode();
      if (Field0Name6 != 0) hash ^= Field0Name6.GetHashCode();
      if (FieldName7 != 0) hash ^= FieldName7.GetHashCode();
      if (FieldName8 != 0) hash ^= FieldName8.GetHashCode();
      if (FieldName9 != 0) hash ^= FieldName9.GetHashCode();
      if (FieldName10 != 0) hash ^= FieldName10.GetHashCode();
      if (FIELDNAME11 != 0) hash ^= FIELDNAME11.GetHashCode();
      if (FIELDName12 != 0) hash ^= FIELDName12.GetHashCode();
      if (FieldName13 != 0) hash ^= FieldName13.GetHashCode();
      if (FieldName14 != 0) hash ^= FieldName14.GetHashCode();
      if (FieldName15 != 0) hash ^= FieldName15.GetHashCode();
      if (FieldName16 != 0) hash ^= FieldName16.GetHashCode();
      if (FieldName17 != 0) hash ^= FieldName17.GetHashCode();
      if (FieldName18 != 0) hash ^= FieldName18.GetHashCode();
      hash ^= (int) oneofFieldCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (OptionalInt32 != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(OptionalInt32);
      }
      if (OptionalInt64 != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(OptionalInt64);
      }
      if (OptionalUint32 != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(OptionalUint32);
      }
      if (OptionalUint64 != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(OptionalUint64);
      }
      if (OptionalSint32 != 0) {
        output.WriteRawTag(40);
        output.WriteSInt32(OptionalSint32);
      }
      if (OptionalSint64 != 0L) {
        output.WriteRawTag(48);
        output.WriteSInt64(OptionalSint64);
      }
      if (OptionalFixed32 != 0) {
        output.WriteRawTag(61);
        output.WriteFixed32(OptionalFixed32);
      }
      if (OptionalFixed64 != 0UL) {
        output.WriteRawTag(65);
        output.WriteFixed64(OptionalFixed64);
      }
      if (OptionalSfixed32 != 0) {
        output.WriteRawTag(77);
        output.WriteSFixed32(OptionalSfixed32);
      }
      if (OptionalSfixed64 != 0L) {
        output.WriteRawTag(81);
        output.WriteSFixed64(OptionalSfixed64);
      }
      if (OptionalFloat != 0F) {
        output.WriteRawTag(93);
        output.WriteFloat(OptionalFloat);
      }
      if (OptionalDouble != 0D) {
        output.WriteRawTag(97);
        output.WriteDouble(OptionalDouble);
      }
      if (OptionalBool != false) {
        output.WriteRawTag(104);
        output.WriteBool(OptionalBool);
      }
      if (OptionalString.Length != 0) {
        output.WriteRawTag(114);
        output.WriteString(OptionalString);
      }
      if (OptionalBytes.Length != 0) {
        output.WriteRawTag(122);
        output.WriteBytes(OptionalBytes);
      }
      if (optionalNestedMessage_ != null) {
        output.WriteRawTag(146, 1);
        output.WriteMessage(OptionalNestedMessage);
      }
      if (optionalForeignMessage_ != null) {
        output.WriteRawTag(154, 1);
        output.WriteMessage(OptionalForeignMessage);
      }
      if (OptionalNestedEnum != global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum.Foo) {
        output.WriteRawTag(168, 1);
        output.WriteEnum((int) OptionalNestedEnum);
      }
      if (OptionalForeignEnum != global::ProtobufTestMessages.Proto3.ForeignEnum.ForeignFoo) {
        output.WriteRawTag(176, 1);
        output.WriteEnum((int) OptionalForeignEnum);
      }
      if (OptionalAliasedEnum != global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.AliasedEnum.AliasFoo) {
        output.WriteRawTag(184, 1);
        output.WriteEnum((int) OptionalAliasedEnum);
      }
      if (OptionalStringPiece.Length != 0) {
        output.WriteRawTag(194, 1);
        output.WriteString(OptionalStringPiece);
      }
      if (OptionalCord.Length != 0) {
        output.WriteRawTag(202, 1);
        output.WriteString(OptionalCord);
      }
      if (recursiveMessage_ != null) {
        output.WriteRawTag(218, 1);
        output.WriteMessage(RecursiveMessage);
      }
      repeatedInt32_.WriteTo(output, _repeated_repeatedInt32_codec);
      repeatedInt64_.WriteTo(output, _repeated_repeatedInt64_codec);
      repeatedUint32_.WriteTo(output, _repeated_repeatedUint32_codec);
      repeatedUint64_.WriteTo(output, _repeated_repeatedUint64_codec);
      repeatedSint32_.WriteTo(output, _repeated_repeatedSint32_codec);
      repeatedSint64_.WriteTo(output, _repeated_repeatedSint64_codec);
      repeatedFixed32_.WriteTo(output, _repeated_repeatedFixed32_codec);
      repeatedFixed64_.WriteTo(output, _repeated_repeatedFixed64_codec);
      repeatedSfixed32_.WriteTo(output, _repeated_repeatedSfixed32_codec);
      repeatedSfixed64_.WriteTo(output, _repeated_repeatedSfixed64_codec);
      repeatedFloat_.WriteTo(output, _repeated_repeatedFloat_codec);
      repeatedDouble_.WriteTo(output, _repeated_repeatedDouble_codec);
      repeatedBool_.WriteTo(output, _repeated_repeatedBool_codec);
      repeatedString_.WriteTo(output, _repeated_repeatedString_codec);
      repeatedBytes_.WriteTo(output, _repeated_repeatedBytes_codec);
      repeatedNestedMessage_.WriteTo(output, _repeated_repeatedNestedMessage_codec);
      repeatedForeignMessage_.WriteTo(output, _repeated_repeatedForeignMessage_codec);
      repeatedNestedEnum_.WriteTo(output, _repeated_repeatedNestedEnum_codec);
      repeatedForeignEnum_.WriteTo(output, _repeated_repeatedForeignEnum_codec);
      repeatedStringPiece_.WriteTo(output, _repeated_repeatedStringPiece_codec);
      repeatedCord_.WriteTo(output, _repeated_repeatedCord_codec);
      mapInt32Int32_.WriteTo(output, _map_mapInt32Int32_codec);
      mapInt64Int64_.WriteTo(output, _map_mapInt64Int64_codec);
      mapUint32Uint32_.WriteTo(output, _map_mapUint32Uint32_codec);
      mapUint64Uint64_.WriteTo(output, _map_mapUint64Uint64_codec);
      mapSint32Sint32_.WriteTo(output, _map_mapSint32Sint32_codec);
      mapSint64Sint64_.WriteTo(output, _map_mapSint64Sint64_codec);
      mapFixed32Fixed32_.WriteTo(output, _map_mapFixed32Fixed32_codec);
      mapFixed64Fixed64_.WriteTo(output, _map_mapFixed64Fixed64_codec);
      mapSfixed32Sfixed32_.WriteTo(output, _map_mapSfixed32Sfixed32_codec);
      mapSfixed64Sfixed64_.WriteTo(output, _map_mapSfixed64Sfixed64_codec);
      mapInt32Float_.WriteTo(output, _map_mapInt32Float_codec);
      mapInt32Double_.WriteTo(output, _map_mapInt32Double_codec);
      mapBoolBool_.WriteTo(output, _map_mapBoolBool_codec);
      mapStringString_.WriteTo(output, _map_mapStringString_codec);
      mapStringBytes_.WriteTo(output, _map_mapStringBytes_codec);
      mapStringNestedMessage_.WriteTo(output, _map_mapStringNestedMessage_codec);
      mapStringForeignMessage_.WriteTo(output, _map_mapStringForeignMessage_codec);
      mapStringNestedEnum_.WriteTo(output, _map_mapStringNestedEnum_codec);
      mapStringForeignEnum_.WriteTo(output, _map_mapStringForeignEnum_codec);
      packedInt32_.WriteTo(output, _repeated_packedInt32_codec);
      packedInt64_.WriteTo(output, _repeated_packedInt64_codec);
      packedUint32_.WriteTo(output, _repeated_packedUint32_codec);
      packedUint64_.WriteTo(output, _repeated_packedUint64_codec);
      packedSint32_.WriteTo(output, _repeated_packedSint32_codec);
      packedSint64_.WriteTo(output, _repeated_packedSint64_codec);
      packedFixed32_.WriteTo(output, _repeated_packedFixed32_codec);
      packedFixed64_.WriteTo(output, _repeated_packedFixed64_codec);
      packedSfixed32_.WriteTo(output, _repeated_packedSfixed32_codec);
      packedSfixed64_.WriteTo(output, _repeated_packedSfixed64_codec);
      packedFloat_.WriteTo(output, _repeated_packedFloat_codec);
      packedDouble_.WriteTo(output, _repeated_packedDouble_codec);
      packedBool_.WriteTo(output, _repeated_packedBool_codec);
      packedNestedEnum_.WriteTo(output, _repeated_packedNestedEnum_codec);
      unpackedInt32_.WriteTo(output, _repeated_unpackedInt32_codec);
      unpackedInt64_.WriteTo(output, _repeated_unpackedInt64_codec);
      unpackedUint32_.WriteTo(output, _repeated_unpackedUint32_codec);
      unpackedUint64_.WriteTo(output, _repeated_unpackedUint64_codec);
      unpackedSint32_.WriteTo(output, _repeated_unpackedSint32_codec);
      unpackedSint64_.WriteTo(output, _repeated_unpackedSint64_codec);
      unpackedFixed32_.WriteTo(output, _repeated_unpackedFixed32_codec);
      unpackedFixed64_.WriteTo(output, _repeated_unpackedFixed64_codec);
      unpackedSfixed32_.WriteTo(output, _repeated_unpackedSfixed32_codec);
      unpackedSfixed64_.WriteTo(output, _repeated_unpackedSfixed64_codec);
      unpackedFloat_.WriteTo(output, _repeated_unpackedFloat_codec);
      unpackedDouble_.WriteTo(output, _repeated_unpackedDouble_codec);
      unpackedBool_.WriteTo(output, _repeated_unpackedBool_codec);
      unpackedNestedEnum_.WriteTo(output, _repeated_unpackedNestedEnum_codec);
      if (HasOneofUint32) {
        output.WriteRawTag(248, 6);
        output.WriteUInt32(OneofUint32);
      }
      if (oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage) {
        output.WriteRawTag(130, 7);
        output.WriteMessage(OneofNestedMessage);
      }
      if (HasOneofString) {
        output.WriteRawTag(138, 7);
        output.WriteString(OneofString);
      }
      if (HasOneofBytes) {
        output.WriteRawTag(146, 7);
        output.WriteBytes(OneofBytes);
      }
      if (HasOneofBool) {
        output.WriteRawTag(152, 7);
        output.WriteBool(OneofBool);
      }
      if (HasOneofUint64) {
        output.WriteRawTag(160, 7);
        output.WriteUInt64(OneofUint64);
      }
      if (HasOneofFloat) {
        output.WriteRawTag(173, 7);
        output.WriteFloat(OneofFloat);
      }
      if (HasOneofDouble) {
        output.WriteRawTag(177, 7);
        output.WriteDouble(OneofDouble);
      }
      if (HasOneofEnum) {
        output.WriteRawTag(184, 7);
        output.WriteEnum((int) OneofEnum);
      }
      if (HasOneofNullValue) {
        output.WriteRawTag(192, 7);
        output.WriteEnum((int) OneofNullValue);
      }
      if (optionalBoolWrapper_ != null) {
        _single_optionalBoolWrapper_codec.WriteTagAndValue(output, OptionalBoolWrapper);
      }
      if (optionalInt32Wrapper_ != null) {
        _single_optionalInt32Wrapper_codec.WriteTagAndValue(output, OptionalInt32Wrapper);
      }
      if (optionalInt64Wrapper_ != null) {
        _single_optionalInt64Wrapper_codec.WriteTagAndValue(output, OptionalInt64Wrapper);
      }
      if (optionalUint32Wrapper_ != null) {
        _single_optionalUint32Wrapper_codec.WriteTagAndValue(output, OptionalUint32Wrapper);
      }
      if (optionalUint64Wrapper_ != null) {
        _single_optionalUint64Wrapper_codec.WriteTagAndValue(output, OptionalUint64Wrapper);
      }
      if (optionalFloatWrapper_ != null) {
        _single_optionalFloatWrapper_codec.WriteTagAndValue(output, OptionalFloatWrapper);
      }
      if (optionalDoubleWrapper_ != null) {
        _single_optionalDoubleWrapper_codec.WriteTagAndValue(output, OptionalDoubleWrapper);
      }
      if (optionalStringWrapper_ != null) {
        _single_optionalStringWrapper_codec.WriteTagAndValue(output, OptionalStringWrapper);
      }
      if (optionalBytesWrapper_ != null) {
        _single_optionalBytesWrapper_codec.WriteTagAndValue(output, OptionalBytesWrapper);
      }
      repeatedBoolWrapper_.WriteTo(output, _repeated_repeatedBoolWrapper_codec);
      repeatedInt32Wrapper_.WriteTo(output, _repeated_repeatedInt32Wrapper_codec);
      repeatedInt64Wrapper_.WriteTo(output, _repeated_repeatedInt64Wrapper_codec);
      repeatedUint32Wrapper_.WriteTo(output, _repeated_repeatedUint32Wrapper_codec);
      repeatedUint64Wrapper_.WriteTo(output, _repeated_repeatedUint64Wrapper_codec);
      repeatedFloatWrapper_.WriteTo(output, _repeated_repeatedFloatWrapper_codec);
      repeatedDoubleWrapper_.WriteTo(output, _repeated_repeatedDoubleWrapper_codec);
      repeatedStringWrapper_.WriteTo(output, _repeated_repeatedStringWrapper_codec);
      repeatedBytesWrapper_.WriteTo(output, _repeated_repeatedBytesWrapper_codec);
      if (optionalDuration_ != null) {
        output.WriteRawTag(234, 18);
        output.WriteMessage(OptionalDuration);
      }
      if (optionalTimestamp_ != null) {
        output.WriteRawTag(242, 18);
        output.WriteMessage(OptionalTimestamp);
      }
      if (optionalFieldMask_ != null) {
        output.WriteRawTag(250, 18);
        output.WriteMessage(OptionalFieldMask);
      }
      if (optionalStruct_ != null) {
        output.WriteRawTag(130, 19);
        output.WriteMessage(OptionalStruct);
      }
      if (optionalAny_ != null) {
        output.WriteRawTag(138, 19);
        output.WriteMessage(OptionalAny);
      }
      if (optionalValue_ != null) {
        output.WriteRawTag(146, 19);
        output.WriteMessage(OptionalValue);
      }
      if (OptionalNullValue != global::Google.Protobuf.WellKnownTypes.NullValue.NullValue) {
        output.WriteRawTag(152, 19);
        output.WriteEnum((int) OptionalNullValue);
      }
      repeatedDuration_.WriteTo(output, _repeated_repeatedDuration_codec);
      repeatedTimestamp_.WriteTo(output, _repeated_repeatedTimestamp_codec);
      repeatedFieldmask_.WriteTo(output, _repeated_repeatedFieldmask_codec);
      repeatedAny_.WriteTo(output, _repeated_repeatedAny_codec);
      repeatedValue_.WriteTo(output, _repeated_repeatedValue_codec);
      repeatedListValue_.WriteTo(output, _repeated_repeatedListValue_codec);
      repeatedStruct_.WriteTo(output, _repeated_repeatedStruct_codec);
      if (Fieldname1 != 0) {
        output.WriteRawTag(136, 25);
        output.WriteInt32(Fieldname1);
      }
      if (FieldName2 != 0) {
        output.WriteRawTag(144, 25);
        output.WriteInt32(FieldName2);
      }
      if (FieldName3 != 0) {
        output.WriteRawTag(152, 25);
        output.WriteInt32(FieldName3);
      }
      if (FieldName4 != 0) {
        output.WriteRawTag(160, 25);
        output.WriteInt32(FieldName4);
      }
      if (Field0Name5 != 0) {
        output.WriteRawTag(168, 25);
        output.WriteInt32(Field0Name5);
      }
      if (Field0Name6 != 0) {
        output.WriteRawTag(176, 25);
        output.WriteInt32(Field0Name6);
      }
      if (FieldName7 != 0) {
        output.WriteRawTag(184, 25);
        output.WriteInt32(FieldName7);
      }
      if (FieldName8 != 0) {
        output.WriteRawTag(192, 25);
        output.WriteInt32(FieldName8);
      }
      if (FieldName9 != 0) {
        output.WriteRawTag(200, 25);
        output.WriteInt32(FieldName9);
      }
      if (FieldName10 != 0) {
        output.WriteRawTag(208, 25);
        output.WriteInt32(FieldName10);
      }
      if (FIELDNAME11 != 0) {
        output.WriteRawTag(216, 25);
        output.WriteInt32(FIELDNAME11);
      }
      if (FIELDName12 != 0) {
        output.WriteRawTag(224, 25);
        output.WriteInt32(FIELDName12);
      }
      if (FieldName13 != 0) {
        output.WriteRawTag(232, 25);
        output.WriteInt32(FieldName13);
      }
      if (FieldName14 != 0) {
        output.WriteRawTag(240, 25);
        output.WriteInt32(FieldName14);
      }
      if (FieldName15 != 0) {
        output.WriteRawTag(248, 25);
        output.WriteInt32(FieldName15);
      }
      if (FieldName16 != 0) {
        output.WriteRawTag(128, 26);
        output.WriteInt32(FieldName16);
      }
      if (FieldName17 != 0) {
        output.WriteRawTag(136, 26);
        output.WriteInt32(FieldName17);
      }
      if (FieldName18 != 0) {
        output.WriteRawTag(144, 26);
        output.WriteInt32(FieldName18);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (OptionalInt32 != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(OptionalInt32);
      }
      if (OptionalInt64 != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(OptionalInt64);
      }
      if (OptionalUint32 != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(OptionalUint32);
      }
      if (OptionalUint64 != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(OptionalUint64);
      }
      if (OptionalSint32 != 0) {
        output.WriteRawTag(40);
        output.WriteSInt32(OptionalSint32);
      }
      if (OptionalSint64 != 0L) {
        output.WriteRawTag(48);
        output.WriteSInt64(OptionalSint64);
      }
      if (OptionalFixed32 != 0) {
        output.WriteRawTag(61);
        output.WriteFixed32(OptionalFixed32);
      }
      if (OptionalFixed64 != 0UL) {
        output.WriteRawTag(65);
        output.WriteFixed64(OptionalFixed64);
      }
      if (OptionalSfixed32 != 0) {
        output.WriteRawTag(77);
        output.WriteSFixed32(OptionalSfixed32);
      }
      if (OptionalSfixed64 != 0L) {
        output.WriteRawTag(81);
        output.WriteSFixed64(OptionalSfixed64);
      }
      if (OptionalFloat != 0F) {
        output.WriteRawTag(93);
        output.WriteFloat(OptionalFloat);
      }
      if (OptionalDouble != 0D) {
        output.WriteRawTag(97);
        output.WriteDouble(OptionalDouble);
      }
      if (OptionalBool != false) {
        output.WriteRawTag(104);
        output.WriteBool(OptionalBool);
      }
      if (OptionalString.Length != 0) {
        output.WriteRawTag(114);
        output.WriteString(OptionalString);
      }
      if (OptionalBytes.Length != 0) {
        output.WriteRawTag(122);
        output.WriteBytes(OptionalBytes);
      }
      if (optionalNestedMessage_ != null) {
        output.WriteRawTag(146, 1);
        output.WriteMessage(OptionalNestedMessage);
      }
      if (optionalForeignMessage_ != null) {
        output.WriteRawTag(154, 1);
        output.WriteMessage(OptionalForeignMessage);
      }
      if (OptionalNestedEnum != global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum.Foo) {
        output.WriteRawTag(168, 1);
        output.WriteEnum((int) OptionalNestedEnum);
      }
      if (OptionalForeignEnum != global::ProtobufTestMessages.Proto3.ForeignEnum.ForeignFoo) {
        output.WriteRawTag(176, 1);
        output.WriteEnum((int) OptionalForeignEnum);
      }
      if (OptionalAliasedEnum != global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.AliasedEnum.AliasFoo) {
        output.WriteRawTag(184, 1);
        output.WriteEnum((int) OptionalAliasedEnum);
      }
      if (OptionalStringPiece.Length != 0) {
        output.WriteRawTag(194, 1);
        output.WriteString(OptionalStringPiece);
      }
      if (OptionalCord.Length != 0) {
        output.WriteRawTag(202, 1);
        output.WriteString(OptionalCord);
      }
      if (recursiveMessage_ != null) {
        output.WriteRawTag(218, 1);
        output.WriteMessage(RecursiveMessage);
      }
      repeatedInt32_.WriteTo(ref output, _repeated_repeatedInt32_codec);
      repeatedInt64_.WriteTo(ref output, _repeated_repeatedInt64_codec);
      repeatedUint32_.WriteTo(ref output, _repeated_repeatedUint32_codec);
      repeatedUint64_.WriteTo(ref output, _repeated_repeatedUint64_codec);
      repeatedSint32_.WriteTo(ref output, _repeated_repeatedSint32_codec);
      repeatedSint64_.WriteTo(ref output, _repeated_repeatedSint64_codec);
      repeatedFixed32_.WriteTo(ref output, _repeated_repeatedFixed32_codec);
      repeatedFixed64_.WriteTo(ref output, _repeated_repeatedFixed64_codec);
      repeatedSfixed32_.WriteTo(ref output, _repeated_repeatedSfixed32_codec);
      repeatedSfixed64_.WriteTo(ref output, _repeated_repeatedSfixed64_codec);
      repeatedFloat_.WriteTo(ref output, _repeated_repeatedFloat_codec);
      repeatedDouble_.WriteTo(ref output, _repeated_repeatedDouble_codec);
      repeatedBool_.WriteTo(ref output, _repeated_repeatedBool_codec);
      repeatedString_.WriteTo(ref output, _repeated_repeatedString_codec);
      repeatedBytes_.WriteTo(ref output, _repeated_repeatedBytes_codec);
      repeatedNestedMessage_.WriteTo(ref output, _repeated_repeatedNestedMessage_codec);
      repeatedForeignMessage_.WriteTo(ref output, _repeated_repeatedForeignMessage_codec);
      repeatedNestedEnum_.WriteTo(ref output, _repeated_repeatedNestedEnum_codec);
      repeatedForeignEnum_.WriteTo(ref output, _repeated_repeatedForeignEnum_codec);
      repeatedStringPiece_.WriteTo(ref output, _repeated_repeatedStringPiece_codec);
      repeatedCord_.WriteTo(ref output, _repeated_repeatedCord_codec);
      mapInt32Int32_.WriteTo(ref output, _map_mapInt32Int32_codec);
      mapInt64Int64_.WriteTo(ref output, _map_mapInt64Int64_codec);
      mapUint32Uint32_.WriteTo(ref output, _map_mapUint32Uint32_codec);
      mapUint64Uint64_.WriteTo(ref output, _map_mapUint64Uint64_codec);
      mapSint32Sint32_.WriteTo(ref output, _map_mapSint32Sint32_codec);
      mapSint64Sint64_.WriteTo(ref output, _map_mapSint64Sint64_codec);
      mapFixed32Fixed32_.WriteTo(ref output, _map_mapFixed32Fixed32_codec);
      mapFixed64Fixed64_.WriteTo(ref output, _map_mapFixed64Fixed64_codec);
      mapSfixed32Sfixed32_.WriteTo(ref output, _map_mapSfixed32Sfixed32_codec);
      mapSfixed64Sfixed64_.WriteTo(ref output, _map_mapSfixed64Sfixed64_codec);
      mapInt32Float_.WriteTo(ref output, _map_mapInt32Float_codec);
      mapInt32Double_.WriteTo(ref output, _map_mapInt32Double_codec);
      mapBoolBool_.WriteTo(ref output, _map_mapBoolBool_codec);
      mapStringString_.WriteTo(ref output, _map_mapStringString_codec);
      mapStringBytes_.WriteTo(ref output, _map_mapStringBytes_codec);
      mapStringNestedMessage_.WriteTo(ref output, _map_mapStringNestedMessage_codec);
      mapStringForeignMessage_.WriteTo(ref output, _map_mapStringForeignMessage_codec);
      mapStringNestedEnum_.WriteTo(ref output, _map_mapStringNestedEnum_codec);
      mapStringForeignEnum_.WriteTo(ref output, _map_mapStringForeignEnum_codec);
      packedInt32_.WriteTo(ref output, _repeated_packedInt32_codec);
      packedInt64_.WriteTo(ref output, _repeated_packedInt64_codec);
      packedUint32_.WriteTo(ref output, _repeated_packedUint32_codec);
      packedUint64_.WriteTo(ref output, _repeated_packedUint64_codec);
      packedSint32_.WriteTo(ref output, _repeated_packedSint32_codec);
      packedSint64_.WriteTo(ref output, _repeated_packedSint64_codec);
      packedFixed32_.WriteTo(ref output, _repeated_packedFixed32_codec);
      packedFixed64_.WriteTo(ref output, _repeated_packedFixed64_codec);
      packedSfixed32_.WriteTo(ref output, _repeated_packedSfixed32_codec);
      packedSfixed64_.WriteTo(ref output, _repeated_packedSfixed64_codec);
      packedFloat_.WriteTo(ref output, _repeated_packedFloat_codec);
      packedDouble_.WriteTo(ref output, _repeated_packedDouble_codec);
      packedBool_.WriteTo(ref output, _repeated_packedBool_codec);
      packedNestedEnum_.WriteTo(ref output, _repeated_packedNestedEnum_codec);
      unpackedInt32_.WriteTo(ref output, _repeated_unpackedInt32_codec);
      unpackedInt64_.WriteTo(ref output, _repeated_unpackedInt64_codec);
      unpackedUint32_.WriteTo(ref output, _repeated_unpackedUint32_codec);
      unpackedUint64_.WriteTo(ref output, _repeated_unpackedUint64_codec);
      unpackedSint32_.WriteTo(ref output, _repeated_unpackedSint32_codec);
      unpackedSint64_.WriteTo(ref output, _repeated_unpackedSint64_codec);
      unpackedFixed32_.WriteTo(ref output, _repeated_unpackedFixed32_codec);
      unpackedFixed64_.WriteTo(ref output, _repeated_unpackedFixed64_codec);
      unpackedSfixed32_.WriteTo(ref output, _repeated_unpackedSfixed32_codec);
      unpackedSfixed64_.WriteTo(ref output, _repeated_unpackedSfixed64_codec);
      unpackedFloat_.WriteTo(ref output, _repeated_unpackedFloat_codec);
      unpackedDouble_.WriteTo(ref output, _repeated_unpackedDouble_codec);
      unpackedBool_.WriteTo(ref output, _repeated_unpackedBool_codec);
      unpackedNestedEnum_.WriteTo(ref output, _repeated_unpackedNestedEnum_codec);
      if (HasOneofUint32) {
        output.WriteRawTag(248, 6);
        output.WriteUInt32(OneofUint32);
      }
      if (oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage) {
        output.WriteRawTag(130, 7);
        output.WriteMessage(OneofNestedMessage);
      }
      if (HasOneofString) {
        output.WriteRawTag(138, 7);
        output.WriteString(OneofString);
      }
      if (HasOneofBytes) {
        output.WriteRawTag(146, 7);
        output.WriteBytes(OneofBytes);
      }
      if (HasOneofBool) {
        output.WriteRawTag(152, 7);
        output.WriteBool(OneofBool);
      }
      if (HasOneofUint64) {
        output.WriteRawTag(160, 7);
        output.WriteUInt64(OneofUint64);
      }
      if (HasOneofFloat) {
        output.WriteRawTag(173, 7);
        output.WriteFloat(OneofFloat);
      }
      if (HasOneofDouble) {
        output.WriteRawTag(177, 7);
        output.WriteDouble(OneofDouble);
      }
      if (HasOneofEnum) {
        output.WriteRawTag(184, 7);
        output.WriteEnum((int) OneofEnum);
      }
      if (HasOneofNullValue) {
        output.WriteRawTag(192, 7);
        output.WriteEnum((int) OneofNullValue);
      }
      if (optionalBoolWrapper_ != null) {
        _single_optionalBoolWrapper_codec.WriteTagAndValue(ref output, OptionalBoolWrapper);
      }
      if (optionalInt32Wrapper_ != null) {
        _single_optionalInt32Wrapper_codec.WriteTagAndValue(ref output, OptionalInt32Wrapper);
      }
      if (optionalInt64Wrapper_ != null) {
        _single_optionalInt64Wrapper_codec.WriteTagAndValue(ref output, OptionalInt64Wrapper);
      }
      if (optionalUint32Wrapper_ != null) {
        _single_optionalUint32Wrapper_codec.WriteTagAndValue(ref output, OptionalUint32Wrapper);
      }
      if (optionalUint64Wrapper_ != null) {
        _single_optionalUint64Wrapper_codec.WriteTagAndValue(ref output, OptionalUint64Wrapper);
      }
      if (optionalFloatWrapper_ != null) {
        _single_optionalFloatWrapper_codec.WriteTagAndValue(ref output, OptionalFloatWrapper);
      }
      if (optionalDoubleWrapper_ != null) {
        _single_optionalDoubleWrapper_codec.WriteTagAndValue(ref output, OptionalDoubleWrapper);
      }
      if (optionalStringWrapper_ != null) {
        _single_optionalStringWrapper_codec.WriteTagAndValue(ref output, OptionalStringWrapper);
      }
      if (optionalBytesWrapper_ != null) {
        _single_optionalBytesWrapper_codec.WriteTagAndValue(ref output, OptionalBytesWrapper);
      }
      repeatedBoolWrapper_.WriteTo(ref output, _repeated_repeatedBoolWrapper_codec);
      repeatedInt32Wrapper_.WriteTo(ref output, _repeated_repeatedInt32Wrapper_codec);
      repeatedInt64Wrapper_.WriteTo(ref output, _repeated_repeatedInt64Wrapper_codec);
      repeatedUint32Wrapper_.WriteTo(ref output, _repeated_repeatedUint32Wrapper_codec);
      repeatedUint64Wrapper_.WriteTo(ref output, _repeated_repeatedUint64Wrapper_codec);
      repeatedFloatWrapper_.WriteTo(ref output, _repeated_repeatedFloatWrapper_codec);
      repeatedDoubleWrapper_.WriteTo(ref output, _repeated_repeatedDoubleWrapper_codec);
      repeatedStringWrapper_.WriteTo(ref output, _repeated_repeatedStringWrapper_codec);
      repeatedBytesWrapper_.WriteTo(ref output, _repeated_repeatedBytesWrapper_codec);
      if (optionalDuration_ != null) {
        output.WriteRawTag(234, 18);
        output.WriteMessage(OptionalDuration);
      }
      if (optionalTimestamp_ != null) {
        output.WriteRawTag(242, 18);
        output.WriteMessage(OptionalTimestamp);
      }
      if (optionalFieldMask_ != null) {
        output.WriteRawTag(250, 18);
        output.WriteMessage(OptionalFieldMask);
      }
      if (optionalStruct_ != null) {
        output.WriteRawTag(130, 19);
        output.WriteMessage(OptionalStruct);
      }
      if (optionalAny_ != null) {
        output.WriteRawTag(138, 19);
        output.WriteMessage(OptionalAny);
      }
      if (optionalValue_ != null) {
        output.WriteRawTag(146, 19);
        output.WriteMessage(OptionalValue);
      }
      if (OptionalNullValue != global::Google.Protobuf.WellKnownTypes.NullValue.NullValue) {
        output.WriteRawTag(152, 19);
        output.WriteEnum((int) OptionalNullValue);
      }
      repeatedDuration_.WriteTo(ref output, _repeated_repeatedDuration_codec);
      repeatedTimestamp_.WriteTo(ref output, _repeated_repeatedTimestamp_codec);
      repeatedFieldmask_.WriteTo(ref output, _repeated_repeatedFieldmask_codec);
      repeatedAny_.WriteTo(ref output, _repeated_repeatedAny_codec);
      repeatedValue_.WriteTo(ref output, _repeated_repeatedValue_codec);
      repeatedListValue_.WriteTo(ref output, _repeated_repeatedListValue_codec);
      repeatedStruct_.WriteTo(ref output, _repeated_repeatedStruct_codec);
      if (Fieldname1 != 0) {
        output.WriteRawTag(136, 25);
        output.WriteInt32(Fieldname1);
      }
      if (FieldName2 != 0) {
        output.WriteRawTag(144, 25);
        output.WriteInt32(FieldName2);
      }
      if (FieldName3 != 0) {
        output.WriteRawTag(152, 25);
        output.WriteInt32(FieldName3);
      }
      if (FieldName4 != 0) {
        output.WriteRawTag(160, 25);
        output.WriteInt32(FieldName4);
      }
      if (Field0Name5 != 0) {
        output.WriteRawTag(168, 25);
        output.WriteInt32(Field0Name5);
      }
      if (Field0Name6 != 0) {
        output.WriteRawTag(176, 25);
        output.WriteInt32(Field0Name6);
      }
      if (FieldName7 != 0) {
        output.WriteRawTag(184, 25);
        output.WriteInt32(FieldName7);
      }
      if (FieldName8 != 0) {
        output.WriteRawTag(192, 25);
        output.WriteInt32(FieldName8);
      }
      if (FieldName9 != 0) {
        output.WriteRawTag(200, 25);
        output.WriteInt32(FieldName9);
      }
      if (FieldName10 != 0) {
        output.WriteRawTag(208, 25);
        output.WriteInt32(FieldName10);
      }
      if (FIELDNAME11 != 0) {
        output.WriteRawTag(216, 25);
        output.WriteInt32(FIELDNAME11);
      }
      if (FIELDName12 != 0) {
        output.WriteRawTag(224, 25);
        output.WriteInt32(FIELDName12);
      }
      if (FieldName13 != 0) {
        output.WriteRawTag(232, 25);
        output.WriteInt32(FieldName13);
      }
      if (FieldName14 != 0) {
        output.WriteRawTag(240, 25);
        output.WriteInt32(FieldName14);
      }
      if (FieldName15 != 0) {
        output.WriteRawTag(248, 25);
        output.WriteInt32(FieldName15);
      }
      if (FieldName16 != 0) {
        output.WriteRawTag(128, 26);
        output.WriteInt32(FieldName16);
      }
      if (FieldName17 != 0) {
        output.WriteRawTag(136, 26);
        output.WriteInt32(FieldName17);
      }
      if (FieldName18 != 0) {
        output.WriteRawTag(144, 26);
        output.WriteInt32(FieldName18);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (OptionalInt32 != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(OptionalInt32);
      }
      if (OptionalInt64 != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(OptionalInt64);
      }
      if (OptionalUint32 != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(OptionalUint32);
      }
      if (OptionalUint64 != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(OptionalUint64);
      }
      if (OptionalSint32 != 0) {
        size += 1 + pb::CodedOutputStream.ComputeSInt32Size(OptionalSint32);
      }
      if (OptionalSint64 != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeSInt64Size(OptionalSint64);
      }
      if (OptionalFixed32 != 0) {
        size += 1 + 4;
      }
      if (OptionalFixed64 != 0UL) {
        size += 1 + 8;
      }
      if (OptionalSfixed32 != 0) {
        size += 1 + 4;
      }
      if (OptionalSfixed64 != 0L) {
        size += 1 + 8;
      }
      if (OptionalFloat != 0F) {
        size += 1 + 4;
      }
      if (OptionalDouble != 0D) {
        size += 1 + 8;
      }
      if (OptionalBool != false) {
        size += 1 + 1;
      }
      if (OptionalString.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(OptionalString);
      }
      if (OptionalBytes.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(OptionalBytes);
      }
      if (optionalNestedMessage_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OptionalNestedMessage);
      }
      if (optionalForeignMessage_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OptionalForeignMessage);
      }
      if (OptionalNestedEnum != global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum.Foo) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) OptionalNestedEnum);
      }
      if (OptionalForeignEnum != global::ProtobufTestMessages.Proto3.ForeignEnum.ForeignFoo) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) OptionalForeignEnum);
      }
      if (OptionalAliasedEnum != global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.AliasedEnum.AliasFoo) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) OptionalAliasedEnum);
      }
      if (OptionalStringPiece.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(OptionalStringPiece);
      }
      if (OptionalCord.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(OptionalCord);
      }
      if (recursiveMessage_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(RecursiveMessage);
      }
      size += repeatedInt32_.CalculateSize(_repeated_repeatedInt32_codec);
      size += repeatedInt64_.CalculateSize(_repeated_repeatedInt64_codec);
      size += repeatedUint32_.CalculateSize(_repeated_repeatedUint32_codec);
      size += repeatedUint64_.CalculateSize(_repeated_repeatedUint64_codec);
      size += repeatedSint32_.CalculateSize(_repeated_repeatedSint32_codec);
      size += repeatedSint64_.CalculateSize(_repeated_repeatedSint64_codec);
      size += repeatedFixed32_.CalculateSize(_repeated_repeatedFixed32_codec);
      size += repeatedFixed64_.CalculateSize(_repeated_repeatedFixed64_codec);
      size += repeatedSfixed32_.CalculateSize(_repeated_repeatedSfixed32_codec);
      size += repeatedSfixed64_.CalculateSize(_repeated_repeatedSfixed64_codec);
      size += repeatedFloat_.CalculateSize(_repeated_repeatedFloat_codec);
      size += repeatedDouble_.CalculateSize(_repeated_repeatedDouble_codec);
      size += repeatedBool_.CalculateSize(_repeated_repeatedBool_codec);
      size += repeatedString_.CalculateSize(_repeated_repeatedString_codec);
      size += repeatedBytes_.CalculateSize(_repeated_repeatedBytes_codec);
      size += repeatedNestedMessage_.CalculateSize(_repeated_repeatedNestedMessage_codec);
      size += repeatedForeignMessage_.CalculateSize(_repeated_repeatedForeignMessage_codec);
      size += repeatedNestedEnum_.CalculateSize(_repeated_repeatedNestedEnum_codec);
      size += repeatedForeignEnum_.CalculateSize(_repeated_repeatedForeignEnum_codec);
      size += repeatedStringPiece_.CalculateSize(_repeated_repeatedStringPiece_codec);
      size += repeatedCord_.CalculateSize(_repeated_repeatedCord_codec);
      size += packedInt32_.CalculateSize(_repeated_packedInt32_codec);
      size += packedInt64_.CalculateSize(_repeated_packedInt64_codec);
      size += packedUint32_.CalculateSize(_repeated_packedUint32_codec);
      size += packedUint64_.CalculateSize(_repeated_packedUint64_codec);
      size += packedSint32_.CalculateSize(_repeated_packedSint32_codec);
      size += packedSint64_.CalculateSize(_repeated_packedSint64_codec);
      size += packedFixed32_.CalculateSize(_repeated_packedFixed32_codec);
      size += packedFixed64_.CalculateSize(_repeated_packedFixed64_codec);
      size += packedSfixed32_.CalculateSize(_repeated_packedSfixed32_codec);
      size += packedSfixed64_.CalculateSize(_repeated_packedSfixed64_codec);
      size += packedFloat_.CalculateSize(_repeated_packedFloat_codec);
      size += packedDouble_.CalculateSize(_repeated_packedDouble_codec);
      size += packedBool_.CalculateSize(_repeated_packedBool_codec);
      size += packedNestedEnum_.CalculateSize(_repeated_packedNestedEnum_codec);
      size += unpackedInt32_.CalculateSize(_repeated_unpackedInt32_codec);
      size += unpackedInt64_.CalculateSize(_repeated_unpackedInt64_codec);
      size += unpackedUint32_.CalculateSize(_repeated_unpackedUint32_codec);
      size += unpackedUint64_.CalculateSize(_repeated_unpackedUint64_codec);
      size += unpackedSint32_.CalculateSize(_repeated_unpackedSint32_codec);
      size += unpackedSint64_.CalculateSize(_repeated_unpackedSint64_codec);
      size += unpackedFixed32_.CalculateSize(_repeated_unpackedFixed32_codec);
      size += unpackedFixed64_.CalculateSize(_repeated_unpackedFixed64_codec);
      size += unpackedSfixed32_.CalculateSize(_repeated_unpackedSfixed32_codec);
      size += unpackedSfixed64_.CalculateSize(_repeated_unpackedSfixed64_codec);
      size += unpackedFloat_.CalculateSize(_repeated_unpackedFloat_codec);
      size += unpackedDouble_.CalculateSize(_repeated_unpackedDouble_codec);
      size += unpackedBool_.CalculateSize(_repeated_unpackedBool_codec);
      size += unpackedNestedEnum_.CalculateSize(_repeated_unpackedNestedEnum_codec);
      size += mapInt32Int32_.CalculateSize(_map_mapInt32Int32_codec);
      size += mapInt64Int64_.CalculateSize(_map_mapInt64Int64_codec);
      size += mapUint32Uint32_.CalculateSize(_map_mapUint32Uint32_codec);
      size += mapUint64Uint64_.CalculateSize(_map_mapUint64Uint64_codec);
      size += mapSint32Sint32_.CalculateSize(_map_mapSint32Sint32_codec);
      size += mapSint64Sint64_.CalculateSize(_map_mapSint64Sint64_codec);
      size += mapFixed32Fixed32_.CalculateSize(_map_mapFixed32Fixed32_codec);
      size += mapFixed64Fixed64_.CalculateSize(_map_mapFixed64Fixed64_codec);
      size += mapSfixed32Sfixed32_.CalculateSize(_map_mapSfixed32Sfixed32_codec);
      size += mapSfixed64Sfixed64_.CalculateSize(_map_mapSfixed64Sfixed64_codec);
      size += mapInt32Float_.CalculateSize(_map_mapInt32Float_codec);
      size += mapInt32Double_.CalculateSize(_map_mapInt32Double_codec);
      size += mapBoolBool_.CalculateSize(_map_mapBoolBool_codec);
      size += mapStringString_.CalculateSize(_map_mapStringString_codec);
      size += mapStringBytes_.CalculateSize(_map_mapStringBytes_codec);
      size += mapStringNestedMessage_.CalculateSize(_map_mapStringNestedMessage_codec);
      size += mapStringForeignMessage_.CalculateSize(_map_mapStringForeignMessage_codec);
      size += mapStringNestedEnum_.CalculateSize(_map_mapStringNestedEnum_codec);
      size += mapStringForeignEnum_.CalculateSize(_map_mapStringForeignEnum_codec);
      if (HasOneofUint32) {
        size += 2 + pb::CodedOutputStream.ComputeUInt32Size(OneofUint32);
      }
      if (oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OneofNestedMessage);
      }
      if (HasOneofString) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(OneofString);
      }
      if (HasOneofBytes) {
        size += 2 + pb::CodedOutputStream.ComputeBytesSize(OneofBytes);
      }
      if (HasOneofBool) {
        size += 2 + 1;
      }
      if (HasOneofUint64) {
        size += 2 + pb::CodedOutputStream.ComputeUInt64Size(OneofUint64);
      }
      if (HasOneofFloat) {
        size += 2 + 4;
      }
      if (HasOneofDouble) {
        size += 2 + 8;
      }
      if (HasOneofEnum) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) OneofEnum);
      }
      if (HasOneofNullValue) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) OneofNullValue);
      }
      if (optionalBoolWrapper_ != null) {
        size += _single_optionalBoolWrapper_codec.CalculateSizeWithTag(OptionalBoolWrapper);
      }
      if (optionalInt32Wrapper_ != null) {
        size += _single_optionalInt32Wrapper_codec.CalculateSizeWithTag(OptionalInt32Wrapper);
      }
      if (optionalInt64Wrapper_ != null) {
        size += _single_optionalInt64Wrapper_codec.CalculateSizeWithTag(OptionalInt64Wrapper);
      }
      if (optionalUint32Wrapper_ != null) {
        size += _single_optionalUint32Wrapper_codec.CalculateSizeWithTag(OptionalUint32Wrapper);
      }
      if (optionalUint64Wrapper_ != null) {
        size += _single_optionalUint64Wrapper_codec.CalculateSizeWithTag(OptionalUint64Wrapper);
      }
      if (optionalFloatWrapper_ != null) {
        size += _single_optionalFloatWrapper_codec.CalculateSizeWithTag(OptionalFloatWrapper);
      }
      if (optionalDoubleWrapper_ != null) {
        size += _single_optionalDoubleWrapper_codec.CalculateSizeWithTag(OptionalDoubleWrapper);
      }
      if (optionalStringWrapper_ != null) {
        size += _single_optionalStringWrapper_codec.CalculateSizeWithTag(OptionalStringWrapper);
      }
      if (optionalBytesWrapper_ != null) {
        size += _single_optionalBytesWrapper_codec.CalculateSizeWithTag(OptionalBytesWrapper);
      }
      size += repeatedBoolWrapper_.CalculateSize(_repeated_repeatedBoolWrapper_codec);
      size += repeatedInt32Wrapper_.CalculateSize(_repeated_repeatedInt32Wrapper_codec);
      size += repeatedInt64Wrapper_.CalculateSize(_repeated_repeatedInt64Wrapper_codec);
      size += repeatedUint32Wrapper_.CalculateSize(_repeated_repeatedUint32Wrapper_codec);
      size += repeatedUint64Wrapper_.CalculateSize(_repeated_repeatedUint64Wrapper_codec);
      size += repeatedFloatWrapper_.CalculateSize(_repeated_repeatedFloatWrapper_codec);
      size += repeatedDoubleWrapper_.CalculateSize(_repeated_repeatedDoubleWrapper_codec);
      size += repeatedStringWrapper_.CalculateSize(_repeated_repeatedStringWrapper_codec);
      size += repeatedBytesWrapper_.CalculateSize(_repeated_repeatedBytesWrapper_codec);
      if (optionalDuration_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OptionalDuration);
      }
      if (optionalTimestamp_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OptionalTimestamp);
      }
      if (optionalFieldMask_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OptionalFieldMask);
      }
      if (optionalStruct_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OptionalStruct);
      }
      if (optionalAny_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OptionalAny);
      }
      if (optionalValue_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(OptionalValue);
      }
      if (OptionalNullValue != global::Google.Protobuf.WellKnownTypes.NullValue.NullValue) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) OptionalNullValue);
      }
      size += repeatedDuration_.CalculateSize(_repeated_repeatedDuration_codec);
      size += repeatedTimestamp_.CalculateSize(_repeated_repeatedTimestamp_codec);
      size += repeatedFieldmask_.CalculateSize(_repeated_repeatedFieldmask_codec);
      size += repeatedStruct_.CalculateSize(_repeated_repeatedStruct_codec);
      size += repeatedAny_.CalculateSize(_repeated_repeatedAny_codec);
      size += repeatedValue_.CalculateSize(_repeated_repeatedValue_codec);
      size += repeatedListValue_.CalculateSize(_repeated_repeatedListValue_codec);
      if (Fieldname1 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(Fieldname1);
      }
      if (FieldName2 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName2);
      }
      if (FieldName3 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName3);
      }
      if (FieldName4 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName4);
      }
      if (Field0Name5 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(Field0Name5);
      }
      if (Field0Name6 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(Field0Name6);
      }
      if (FieldName7 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName7);
      }
      if (FieldName8 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName8);
      }
      if (FieldName9 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName9);
      }
      if (FieldName10 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName10);
      }
      if (FIELDNAME11 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FIELDNAME11);
      }
      if (FIELDName12 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FIELDName12);
      }
      if (FieldName13 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName13);
      }
      if (FieldName14 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName14);
      }
      if (FieldName15 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName15);
      }
      if (FieldName16 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName16);
      }
      if (FieldName17 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName17);
      }
      if (FieldName18 != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(FieldName18);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TestAllTypesProto3 other) {
      if (other == null) {
        return;
      }
      if (other.OptionalInt32 != 0) {
        OptionalInt32 = other.OptionalInt32;
      }
      if (other.OptionalInt64 != 0L) {
        OptionalInt64 = other.OptionalInt64;
      }
      if (other.OptionalUint32 != 0) {
        OptionalUint32 = other.OptionalUint32;
      }
      if (other.OptionalUint64 != 0UL) {
        OptionalUint64 = other.OptionalUint64;
      }
      if (other.OptionalSint32 != 0) {
        OptionalSint32 = other.OptionalSint32;
      }
      if (other.OptionalSint64 != 0L) {
        OptionalSint64 = other.OptionalSint64;
      }
      if (other.OptionalFixed32 != 0) {
        OptionalFixed32 = other.OptionalFixed32;
      }
      if (other.OptionalFixed64 != 0UL) {
        OptionalFixed64 = other.OptionalFixed64;
      }
      if (other.OptionalSfixed32 != 0) {
        OptionalSfixed32 = other.OptionalSfixed32;
      }
      if (other.OptionalSfixed64 != 0L) {
        OptionalSfixed64 = other.OptionalSfixed64;
      }
      if (other.OptionalFloat != 0F) {
        OptionalFloat = other.OptionalFloat;
      }
      if (other.OptionalDouble != 0D) {
        OptionalDouble = other.OptionalDouble;
      }
      if (other.OptionalBool != false) {
        OptionalBool = other.OptionalBool;
      }
      if (other.OptionalString.Length != 0) {
        OptionalString = other.OptionalString;
      }
      if (other.OptionalBytes.Length != 0) {
        OptionalBytes = other.OptionalBytes;
      }
      if (other.optionalNestedMessage_ != null) {
        if (optionalNestedMessage_ == null) {
          OptionalNestedMessage = new global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage();
        }
        OptionalNestedMessage.MergeFrom(other.OptionalNestedMessage);
      }
      if (other.optionalForeignMessage_ != null) {
        if (optionalForeignMessage_ == null) {
          OptionalForeignMessage = new global::ProtobufTestMessages.Proto3.ForeignMessage();
        }
        OptionalForeignMessage.MergeFrom(other.OptionalForeignMessage);
      }
      if (other.OptionalNestedEnum != global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum.Foo) {
        OptionalNestedEnum = other.OptionalNestedEnum;
      }
      if (other.OptionalForeignEnum != global::ProtobufTestMessages.Proto3.ForeignEnum.ForeignFoo) {
        OptionalForeignEnum = other.OptionalForeignEnum;
      }
      if (other.OptionalAliasedEnum != global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.AliasedEnum.AliasFoo) {
        OptionalAliasedEnum = other.OptionalAliasedEnum;
      }
      if (other.OptionalStringPiece.Length != 0) {
        OptionalStringPiece = other.OptionalStringPiece;
      }
      if (other.OptionalCord.Length != 0) {
        OptionalCord = other.OptionalCord;
      }
      if (other.recursiveMessage_ != null) {
        if (recursiveMessage_ == null) {
          RecursiveMessage = new global::ProtobufTestMessages.Proto3.TestAllTypesProto3();
        }
        RecursiveMessage.MergeFrom(other.RecursiveMessage);
      }
      repeatedInt32_.Add(other.repeatedInt32_);
      repeatedInt64_.Add(other.repeatedInt64_);
      repeatedUint32_.Add(other.repeatedUint32_);
      repeatedUint64_.Add(other.repeatedUint64_);
      repeatedSint32_.Add(other.repeatedSint32_);
      repeatedSint64_.Add(other.repeatedSint64_);
      repeatedFixed32_.Add(other.repeatedFixed32_);
      repeatedFixed64_.Add(other.repeatedFixed64_);
      repeatedSfixed32_.Add(other.repeatedSfixed32_);
      repeatedSfixed64_.Add(other.repeatedSfixed64_);
      repeatedFloat_.Add(other.repeatedFloat_);
      repeatedDouble_.Add(other.repeatedDouble_);
      repeatedBool_.Add(other.repeatedBool_);
      repeatedString_.Add(other.repeatedString_);
      repeatedBytes_.Add(other.repeatedBytes_);
      repeatedNestedMessage_.Add(other.repeatedNestedMessage_);
      repeatedForeignMessage_.Add(other.repeatedForeignMessage_);
      repeatedNestedEnum_.Add(other.repeatedNestedEnum_);
      repeatedForeignEnum_.Add(other.repeatedForeignEnum_);
      repeatedStringPiece_.Add(other.repeatedStringPiece_);
      repeatedCord_.Add(other.repeatedCord_);
      packedInt32_.Add(other.packedInt32_);
      packedInt64_.Add(other.packedInt64_);
      packedUint32_.Add(other.packedUint32_);
      packedUint64_.Add(other.packedUint64_);
      packedSint32_.Add(other.packedSint32_);
      packedSint64_.Add(other.packedSint64_);
      packedFixed32_.Add(other.packedFixed32_);
      packedFixed64_.Add(other.packedFixed64_);
      packedSfixed32_.Add(other.packedSfixed32_);
      packedSfixed64_.Add(other.packedSfixed64_);
      packedFloat_.Add(other.packedFloat_);
      packedDouble_.Add(other.packedDouble_);
      packedBool_.Add(other.packedBool_);
      packedNestedEnum_.Add(other.packedNestedEnum_);
      unpackedInt32_.Add(other.unpackedInt32_);
      unpackedInt64_.Add(other.unpackedInt64_);
      unpackedUint32_.Add(other.unpackedUint32_);
      unpackedUint64_.Add(other.unpackedUint64_);
      unpackedSint32_.Add(other.unpackedSint32_);
      unpackedSint64_.Add(other.unpackedSint64_);
      unpackedFixed32_.Add(other.unpackedFixed32_);
      unpackedFixed64_.Add(other.unpackedFixed64_);
      unpackedSfixed32_.Add(other.unpackedSfixed32_);
      unpackedSfixed64_.Add(other.unpackedSfixed64_);
      unpackedFloat_.Add(other.unpackedFloat_);
      unpackedDouble_.Add(other.unpackedDouble_);
      unpackedBool_.Add(other.unpackedBool_);
      unpackedNestedEnum_.Add(other.unpackedNestedEnum_);
      mapInt32Int32_.MergeFrom(other.mapInt32Int32_);
      mapInt64Int64_.MergeFrom(other.mapInt64Int64_);
      mapUint32Uint32_.MergeFrom(other.mapUint32Uint32_);
      mapUint64Uint64_.MergeFrom(other.mapUint64Uint64_);
      mapSint32Sint32_.MergeFrom(other.mapSint32Sint32_);
      mapSint64Sint64_.MergeFrom(other.mapSint64Sint64_);
      mapFixed32Fixed32_.MergeFrom(other.mapFixed32Fixed32_);
      mapFixed64Fixed64_.MergeFrom(other.mapFixed64Fixed64_);
      mapSfixed32Sfixed32_.MergeFrom(other.mapSfixed32Sfixed32_);
      mapSfixed64Sfixed64_.MergeFrom(other.mapSfixed64Sfixed64_);
      mapInt32Float_.MergeFrom(other.mapInt32Float_);
      mapInt32Double_.MergeFrom(other.mapInt32Double_);
      mapBoolBool_.MergeFrom(other.mapBoolBool_);
      mapStringString_.MergeFrom(other.mapStringString_);
      mapStringBytes_.MergeFrom(other.mapStringBytes_);
      mapStringNestedMessage_.MergeFrom(other.mapStringNestedMessage_);
      mapStringForeignMessage_.MergeFrom(other.mapStringForeignMessage_);
      mapStringNestedEnum_.MergeFrom(other.mapStringNestedEnum_);
      mapStringForeignEnum_.MergeFrom(other.mapStringForeignEnum_);
      if (other.optionalBoolWrapper_ != null) {
        if (optionalBoolWrapper_ == null || other.OptionalBoolWrapper != false) {
          OptionalBoolWrapper = other.OptionalBoolWrapper;
        }
      }
      if (other.optionalInt32Wrapper_ != null) {
        if (optionalInt32Wrapper_ == null || other.OptionalInt32Wrapper != 0) {
          OptionalInt32Wrapper = other.OptionalInt32Wrapper;
        }
      }
      if (other.optionalInt64Wrapper_ != null) {
        if (optionalInt64Wrapper_ == null || other.OptionalInt64Wrapper != 0L) {
          OptionalInt64Wrapper = other.OptionalInt64Wrapper;
        }
      }
      if (other.optionalUint32Wrapper_ != null) {
        if (optionalUint32Wrapper_ == null || other.OptionalUint32Wrapper != 0) {
          OptionalUint32Wrapper = other.OptionalUint32Wrapper;
        }
      }
      if (other.optionalUint64Wrapper_ != null) {
        if (optionalUint64Wrapper_ == null || other.OptionalUint64Wrapper != 0UL) {
          OptionalUint64Wrapper = other.OptionalUint64Wrapper;
        }
      }
      if (other.optionalFloatWrapper_ != null) {
        if (optionalFloatWrapper_ == null || other.OptionalFloatWrapper != 0F) {
          OptionalFloatWrapper = other.OptionalFloatWrapper;
        }
      }
      if (other.optionalDoubleWrapper_ != null) {
        if (optionalDoubleWrapper_ == null || other.OptionalDoubleWrapper != 0D) {
          OptionalDoubleWrapper = other.OptionalDoubleWrapper;
        }
      }
      if (other.optionalStringWrapper_ != null) {
        if (optionalStringWrapper_ == null || other.OptionalStringWrapper != "") {
          OptionalStringWrapper = other.OptionalStringWrapper;
        }
      }
      if (other.optionalBytesWrapper_ != null) {
        if (optionalBytesWrapper_ == null || other.OptionalBytesWrapper != pb::ByteString.Empty) {
          OptionalBytesWrapper = other.OptionalBytesWrapper;
        }
      }
      repeatedBoolWrapper_.Add(other.repeatedBoolWrapper_);
      repeatedInt32Wrapper_.Add(other.repeatedInt32Wrapper_);
      repeatedInt64Wrapper_.Add(other.repeatedInt64Wrapper_);
      repeatedUint32Wrapper_.Add(other.repeatedUint32Wrapper_);
      repeatedUint64Wrapper_.Add(other.repeatedUint64Wrapper_);
      repeatedFloatWrapper_.Add(other.repeatedFloatWrapper_);
      repeatedDoubleWrapper_.Add(other.repeatedDoubleWrapper_);
      repeatedStringWrapper_.Add(other.repeatedStringWrapper_);
      repeatedBytesWrapper_.Add(other.repeatedBytesWrapper_);
      if (other.optionalDuration_ != null) {
        if (optionalDuration_ == null) {
          OptionalDuration = new global::Google.Protobuf.WellKnownTypes.Duration();
        }
        OptionalDuration.MergeFrom(other.OptionalDuration);
      }
      if (other.optionalTimestamp_ != null) {
        if (optionalTimestamp_ == null) {
          OptionalTimestamp = new global::Google.Protobuf.WellKnownTypes.Timestamp();
        }
        OptionalTimestamp.MergeFrom(other.OptionalTimestamp);
      }
      if (other.optionalFieldMask_ != null) {
        if (optionalFieldMask_ == null) {
          OptionalFieldMask = new global::Google.Protobuf.WellKnownTypes.FieldMask();
        }
        OptionalFieldMask.MergeFrom(other.OptionalFieldMask);
      }
      if (other.optionalStruct_ != null) {
        if (optionalStruct_ == null) {
          OptionalStruct = new global::Google.Protobuf.WellKnownTypes.Struct();
        }
        OptionalStruct.MergeFrom(other.OptionalStruct);
      }
      if (other.optionalAny_ != null) {
        if (optionalAny_ == null) {
          OptionalAny = new global::Google.Protobuf.WellKnownTypes.Any();
        }
        OptionalAny.MergeFrom(other.OptionalAny);
      }
      if (other.optionalValue_ != null) {
        if (optionalValue_ == null) {
          OptionalValue = new global::Google.Protobuf.WellKnownTypes.Value();
        }
        OptionalValue.MergeFrom(other.OptionalValue);
      }
      if (other.OptionalNullValue != global::Google.Protobuf.WellKnownTypes.NullValue.NullValue) {
        OptionalNullValue = other.OptionalNullValue;
      }
      repeatedDuration_.Add(other.repeatedDuration_);
      repeatedTimestamp_.Add(other.repeatedTimestamp_);
      repeatedFieldmask_.Add(other.repeatedFieldmask_);
      repeatedStruct_.Add(other.repeatedStruct_);
      repeatedAny_.Add(other.repeatedAny_);
      repeatedValue_.Add(other.repeatedValue_);
      repeatedListValue_.Add(other.repeatedListValue_);
      if (other.Fieldname1 != 0) {
        Fieldname1 = other.Fieldname1;
      }
      if (other.FieldName2 != 0) {
        FieldName2 = other.FieldName2;
      }
      if (other.FieldName3 != 0) {
        FieldName3 = other.FieldName3;
      }
      if (other.FieldName4 != 0) {
        FieldName4 = other.FieldName4;
      }
      if (other.Field0Name5 != 0) {
        Field0Name5 = other.Field0Name5;
      }
      if (other.Field0Name6 != 0) {
        Field0Name6 = other.Field0Name6;
      }
      if (other.FieldName7 != 0) {
        FieldName7 = other.FieldName7;
      }
      if (other.FieldName8 != 0) {
        FieldName8 = other.FieldName8;
      }
      if (other.FieldName9 != 0) {
        FieldName9 = other.FieldName9;
      }
      if (other.FieldName10 != 0) {
        FieldName10 = other.FieldName10;
      }
      if (other.FIELDNAME11 != 0) {
        FIELDNAME11 = other.FIELDNAME11;
      }
      if (other.FIELDName12 != 0) {
        FIELDName12 = other.FIELDName12;
      }
      if (other.FieldName13 != 0) {
        FieldName13 = other.FieldName13;
      }
      if (other.FieldName14 != 0) {
        FieldName14 = other.FieldName14;
      }
      if (other.FieldName15 != 0) {
        FieldName15 = other.FieldName15;
      }
      if (other.FieldName16 != 0) {
        FieldName16 = other.FieldName16;
      }
      if (other.FieldName17 != 0) {
        FieldName17 = other.FieldName17;
      }
      if (other.FieldName18 != 0) {
        FieldName18 = other.FieldName18;
      }
      switch (other.OneofFieldCase) {
        case OneofFieldOneofCase.OneofUint32:
          OneofUint32 = other.OneofUint32;
          break;
        case OneofFieldOneofCase.OneofNestedMessage:
          if (OneofNestedMessage == null) {
            OneofNestedMessage = new global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage();
          }
          OneofNestedMessage.MergeFrom(other.OneofNestedMessage);
          break;
        case OneofFieldOneofCase.OneofString:
          OneofString = other.OneofString;
          break;
        case OneofFieldOneofCase.OneofBytes:
          OneofBytes = other.OneofBytes;
          break;
        case OneofFieldOneofCase.OneofBool:
          OneofBool = other.OneofBool;
          break;
        case OneofFieldOneofCase.OneofUint64:
          OneofUint64 = other.OneofUint64;
          break;
        case OneofFieldOneofCase.OneofFloat:
          OneofFloat = other.OneofFloat;
          break;
        case OneofFieldOneofCase.OneofDouble:
          OneofDouble = other.OneofDouble;
          break;
        case OneofFieldOneofCase.OneofEnum:
          OneofEnum = other.OneofEnum;
          break;
        case OneofFieldOneofCase.OneofNullValue:
          OneofNullValue = other.OneofNullValue;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            OptionalInt32 = input.ReadInt32();
            break;
          }
          case 16: {
            OptionalInt64 = input.ReadInt64();
            break;
          }
          case 24: {
            OptionalUint32 = input.ReadUInt32();
            break;
          }
          case 32: {
            OptionalUint64 = input.ReadUInt64();
            break;
          }
          case 40: {
            OptionalSint32 = input.ReadSInt32();
            break;
          }
          case 48: {
            OptionalSint64 = input.ReadSInt64();
            break;
          }
          case 61: {
            OptionalFixed32 = input.ReadFixed32();
            break;
          }
          case 65: {
            OptionalFixed64 = input.ReadFixed64();
            break;
          }
          case 77: {
            OptionalSfixed32 = input.ReadSFixed32();
            break;
          }
          case 81: {
            OptionalSfixed64 = input.ReadSFixed64();
            break;
          }
          case 93: {
            OptionalFloat = input.ReadFloat();
            break;
          }
          case 97: {
            OptionalDouble = input.ReadDouble();
            break;
          }
          case 104: {
            OptionalBool = input.ReadBool();
            break;
          }
          case 114: {
            OptionalString = input.ReadString();
            break;
          }
          case 122: {
            OptionalBytes = input.ReadBytes();
            break;
          }
          case 146: {
            if (optionalNestedMessage_ == null) {
              OptionalNestedMessage = new global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage();
            }
            input.ReadMessage(OptionalNestedMessage);
            break;
          }
          case 154: {
            if (optionalForeignMessage_ == null) {
              OptionalForeignMessage = new global::ProtobufTestMessages.Proto3.ForeignMessage();
            }
            input.ReadMessage(OptionalForeignMessage);
            break;
          }
          case 168: {
            OptionalNestedEnum = (global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum) input.ReadEnum();
            break;
          }
          case 176: {
            OptionalForeignEnum = (global::ProtobufTestMessages.Proto3.ForeignEnum) input.ReadEnum();
            break;
          }
          case 184: {
            OptionalAliasedEnum = (global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.AliasedEnum) input.ReadEnum();
            break;
          }
          case 194: {
            OptionalStringPiece = input.ReadString();
            break;
          }
          case 202: {
            OptionalCord = input.ReadString();
            break;
          }
          case 218: {
            if (recursiveMessage_ == null) {
              RecursiveMessage = new global::ProtobufTestMessages.Proto3.TestAllTypesProto3();
            }
            input.ReadMessage(RecursiveMessage);
            break;
          }
          case 250:
          case 248: {
            repeatedInt32_.AddEntriesFrom(input, _repeated_repeatedInt32_codec);
            break;
          }
          case 258:
          case 256: {
            repeatedInt64_.AddEntriesFrom(input, _repeated_repeatedInt64_codec);
            break;
          }
          case 266:
          case 264: {
            repeatedUint32_.AddEntriesFrom(input, _repeated_repeatedUint32_codec);
            break;
          }
          case 274:
          case 272: {
            repeatedUint64_.AddEntriesFrom(input, _repeated_repeatedUint64_codec);
            break;
          }
          case 282:
          case 280: {
            repeatedSint32_.AddEntriesFrom(input, _repeated_repeatedSint32_codec);
            break;
          }
          case 290:
          case 288: {
            repeatedSint64_.AddEntriesFrom(input, _repeated_repeatedSint64_codec);
            break;
          }
          case 298:
          case 301: {
            repeatedFixed32_.AddEntriesFrom(input, _repeated_repeatedFixed32_codec);
            break;
          }
          case 306:
          case 305: {
            repeatedFixed64_.AddEntriesFrom(input, _repeated_repeatedFixed64_codec);
            break;
          }
          case 314:
          case 317: {
            repeatedSfixed32_.AddEntriesFrom(input, _repeated_repeatedSfixed32_codec);
            break;
          }
          case 322:
          case 321: {
            repeatedSfixed64_.AddEntriesFrom(input, _repeated_repeatedSfixed64_codec);
            break;
          }
          case 330:
          case 333: {
            repeatedFloat_.AddEntriesFrom(input, _repeated_repeatedFloat_codec);
            break;
          }
          case 338:
          case 337: {
            repeatedDouble_.AddEntriesFrom(input, _repeated_repeatedDouble_codec);
            break;
          }
          case 346:
          case 344: {
            repeatedBool_.AddEntriesFrom(input, _repeated_repeatedBool_codec);
            break;
          }
          case 354: {
            repeatedString_.AddEntriesFrom(input, _repeated_repeatedString_codec);
            break;
          }
          case 362: {
            repeatedBytes_.AddEntriesFrom(input, _repeated_repeatedBytes_codec);
            break;
          }
          case 386: {
            repeatedNestedMessage_.AddEntriesFrom(input, _repeated_repeatedNestedMessage_codec);
            break;
          }
          case 394: {
            repeatedForeignMessage_.AddEntriesFrom(input, _repeated_repeatedForeignMessage_codec);
            break;
          }
          case 410:
          case 408: {
            repeatedNestedEnum_.AddEntriesFrom(input, _repeated_repeatedNestedEnum_codec);
            break;
          }
          case 418:
          case 416: {
            repeatedForeignEnum_.AddEntriesFrom(input, _repeated_repeatedForeignEnum_codec);
            break;
          }
          case 434: {
            repeatedStringPiece_.AddEntriesFrom(input, _repeated_repeatedStringPiece_codec);
            break;
          }
          case 442: {
            repeatedCord_.AddEntriesFrom(input, _repeated_repeatedCord_codec);
            break;
          }
          case 450: {
            mapInt32Int32_.AddEntriesFrom(input, _map_mapInt32Int32_codec);
            break;
          }
          case 458: {
            mapInt64Int64_.AddEntriesFrom(input, _map_mapInt64Int64_codec);
            break;
          }
          case 466: {
            mapUint32Uint32_.AddEntriesFrom(input, _map_mapUint32Uint32_codec);
            break;
          }
          case 474: {
            mapUint64Uint64_.AddEntriesFrom(input, _map_mapUint64Uint64_codec);
            break;
          }
          case 482: {
            mapSint32Sint32_.AddEntriesFrom(input, _map_mapSint32Sint32_codec);
            break;
          }
          case 490: {
            mapSint64Sint64_.AddEntriesFrom(input, _map_mapSint64Sint64_codec);
            break;
          }
          case 498: {
            mapFixed32Fixed32_.AddEntriesFrom(input, _map_mapFixed32Fixed32_codec);
            break;
          }
          case 506: {
            mapFixed64Fixed64_.AddEntriesFrom(input, _map_mapFixed64Fixed64_codec);
            break;
          }
          case 514: {
            mapSfixed32Sfixed32_.AddEntriesFrom(input, _map_mapSfixed32Sfixed32_codec);
            break;
          }
          case 522: {
            mapSfixed64Sfixed64_.AddEntriesFrom(input, _map_mapSfixed64Sfixed64_codec);
            break;
          }
          case 530: {
            mapInt32Float_.AddEntriesFrom(input, _map_mapInt32Float_codec);
            break;
          }
          case 538: {
            mapInt32Double_.AddEntriesFrom(input, _map_mapInt32Double_codec);
            break;
          }
          case 546: {
            mapBoolBool_.AddEntriesFrom(input, _map_mapBoolBool_codec);
            break;
          }
          case 554: {
            mapStringString_.AddEntriesFrom(input, _map_mapStringString_codec);
            break;
          }
          case 562: {
            mapStringBytes_.AddEntriesFrom(input, _map_mapStringBytes_codec);
            break;
          }
          case 570: {
            mapStringNestedMessage_.AddEntriesFrom(input, _map_mapStringNestedMessage_codec);
            break;
          }
          case 578: {
            mapStringForeignMessage_.AddEntriesFrom(input, _map_mapStringForeignMessage_codec);
            break;
          }
          case 586: {
            mapStringNestedEnum_.AddEntriesFrom(input, _map_mapStringNestedEnum_codec);
            break;
          }
          case 594: {
            mapStringForeignEnum_.AddEntriesFrom(input, _map_mapStringForeignEnum_codec);
            break;
          }
          case 602:
          case 600: {
            packedInt32_.AddEntriesFrom(input, _repeated_packedInt32_codec);
            break;
          }
          case 610:
          case 608: {
            packedInt64_.AddEntriesFrom(input, _repeated_packedInt64_codec);
            break;
          }
          case 618:
          case 616: {
            packedUint32_.AddEntriesFrom(input, _repeated_packedUint32_codec);
            break;
          }
          case 626:
          case 624: {
            packedUint64_.AddEntriesFrom(input, _repeated_packedUint64_codec);
            break;
          }
          case 634:
          case 632: {
            packedSint32_.AddEntriesFrom(input, _repeated_packedSint32_codec);
            break;
          }
          case 642:
          case 640: {
            packedSint64_.AddEntriesFrom(input, _repeated_packedSint64_codec);
            break;
          }
          case 650:
          case 653: {
            packedFixed32_.AddEntriesFrom(input, _repeated_packedFixed32_codec);
            break;
          }
          case 658:
          case 657: {
            packedFixed64_.AddEntriesFrom(input, _repeated_packedFixed64_codec);
            break;
          }
          case 666:
          case 669: {
            packedSfixed32_.AddEntriesFrom(input, _repeated_packedSfixed32_codec);
            break;
          }
          case 674:
          case 673: {
            packedSfixed64_.AddEntriesFrom(input, _repeated_packedSfixed64_codec);
            break;
          }
          case 682:
          case 685: {
            packedFloat_.AddEntriesFrom(input, _repeated_packedFloat_codec);
            break;
          }
          case 690:
          case 689: {
            packedDouble_.AddEntriesFrom(input, _repeated_packedDouble_codec);
            break;
          }
          case 698:
          case 696: {
            packedBool_.AddEntriesFrom(input, _repeated_packedBool_codec);
            break;
          }
          case 706:
          case 704: {
            packedNestedEnum_.AddEntriesFrom(input, _repeated_packedNestedEnum_codec);
            break;
          }
          case 714:
          case 712: {
            unpackedInt32_.AddEntriesFrom(input, _repeated_unpackedInt32_codec);
            break;
          }
          case 722:
          case 720: {
            unpackedInt64_.AddEntriesFrom(input, _repeated_unpackedInt64_codec);
            break;
          }
          case 730:
          case 728: {
            unpackedUint32_.AddEntriesFrom(input, _repeated_unpackedUint32_codec);
            break;
          }
          case 738:
          case 736: {
            unpackedUint64_.AddEntriesFrom(input, _repeated_unpackedUint64_codec);
            break;
          }
          case 746:
          case 744: {
            unpackedSint32_.AddEntriesFrom(input, _repeated_unpackedSint32_codec);
            break;
          }
          case 754:
          case 752: {
            unpackedSint64_.AddEntriesFrom(input, _repeated_unpackedSint64_codec);
            break;
          }
          case 762:
          case 765: {
            unpackedFixed32_.AddEntriesFrom(input, _repeated_unpackedFixed32_codec);
            break;
          }
          case 770:
          case 769: {
            unpackedFixed64_.AddEntriesFrom(input, _repeated_unpackedFixed64_codec);
            break;
          }
          case 778:
          case 781: {
            unpackedSfixed32_.AddEntriesFrom(input, _repeated_unpackedSfixed32_codec);
            break;
          }
          case 786:
          case 785: {
            unpackedSfixed64_.AddEntriesFrom(input, _repeated_unpackedSfixed64_codec);
            break;
          }
          case 794:
          case 797: {
            unpackedFloat_.AddEntriesFrom(input, _repeated_unpackedFloat_codec);
            break;
          }
          case 802:
          case 801: {
            unpackedDouble_.AddEntriesFrom(input, _repeated_unpackedDouble_codec);
            break;
          }
          case 810:
          case 808: {
            unpackedBool_.AddEntriesFrom(input, _repeated_unpackedBool_codec);
            break;
          }
          case 818:
          case 816: {
            unpackedNestedEnum_.AddEntriesFrom(input, _repeated_unpackedNestedEnum_codec);
            break;
          }
          case 888: {
            OneofUint32 = input.ReadUInt32();
            break;
          }
          case 898: {
            global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage subBuilder = new global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage();
            if (oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage) {
              subBuilder.MergeFrom(OneofNestedMessage);
            }
            input.ReadMessage(subBuilder);
            OneofNestedMessage = subBuilder;
            break;
          }
          case 906: {
            OneofString = input.ReadString();
            break;
          }
          case 914: {
            OneofBytes = input.ReadBytes();
            break;
          }
          case 920: {
            OneofBool = input.ReadBool();
            break;
          }
          case 928: {
            OneofUint64 = input.ReadUInt64();
            break;
          }
          case 941: {
            OneofFloat = input.ReadFloat();
            break;
          }
          case 945: {
            OneofDouble = input.ReadDouble();
            break;
          }
          case 952: {
            oneofField_ = input.ReadEnum();
            oneofFieldCase_ = OneofFieldOneofCase.OneofEnum;
            break;
          }
          case 960: {
            oneofField_ = input.ReadEnum();
            oneofFieldCase_ = OneofFieldOneofCase.OneofNullValue;
            break;
          }
          case 1610: {
            bool? value = _single_optionalBoolWrapper_codec.Read(input);
            if (optionalBoolWrapper_ == null || value != false) {
              OptionalBoolWrapper = value;
            }
            break;
          }
          case 1618: {
            int? value = _single_optionalInt32Wrapper_codec.Read(input);
            if (optionalInt32Wrapper_ == null || value != 0) {
              OptionalInt32Wrapper = value;
            }
            break;
          }
          case 1626: {
            long? value = _single_optionalInt64Wrapper_codec.Read(input);
            if (optionalInt64Wrapper_ == null || value != 0L) {
              OptionalInt64Wrapper = value;
            }
            break;
          }
          case 1634: {
            uint? value = _single_optionalUint32Wrapper_codec.Read(input);
            if (optionalUint32Wrapper_ == null || value != 0) {
              OptionalUint32Wrapper = value;
            }
            break;
          }
          case 1642: {
            ulong? value = _single_optionalUint64Wrapper_codec.Read(input);
            if (optionalUint64Wrapper_ == null || value != 0UL) {
              OptionalUint64Wrapper = value;
            }
            break;
          }
          case 1650: {
            float? value = _single_optionalFloatWrapper_codec.Read(input);
            if (optionalFloatWrapper_ == null || value != 0F) {
              OptionalFloatWrapper = value;
            }
            break;
          }
          case 1658: {
            double? value = _single_optionalDoubleWrapper_codec.Read(input);
            if (optionalDoubleWrapper_ == null || value != 0D) {
              OptionalDoubleWrapper = value;
            }
            break;
          }
          case 1666: {
            string value = _single_optionalStringWrapper_codec.Read(input);
            if (optionalStringWrapper_ == null || value != "") {
              OptionalStringWrapper = value;
            }
            break;
          }
          case 1674: {
            pb::ByteString value = _single_optionalBytesWrapper_codec.Read(input);
            if (optionalBytesWrapper_ == null || value != pb::ByteString.Empty) {
              OptionalBytesWrapper = value;
            }
            break;
          }
          case 1690: {
            repeatedBoolWrapper_.AddEntriesFrom(input, _repeated_repeatedBoolWrapper_codec);
            break;
          }
          case 1698: {
            repeatedInt32Wrapper_.AddEntriesFrom(input, _repeated_repeatedInt32Wrapper_codec);
            break;
          }
          case 1706: {
            repeatedInt64Wrapper_.AddEntriesFrom(input, _repeated_repeatedInt64Wrapper_codec);
            break;
          }
          case 1714: {
            repeatedUint32Wrapper_.AddEntriesFrom(input, _repeated_repeatedUint32Wrapper_codec);
            break;
          }
          case 1722: {
            repeatedUint64Wrapper_.AddEntriesFrom(input, _repeated_repeatedUint64Wrapper_codec);
            break;
          }
          case 1730: {
            repeatedFloatWrapper_.AddEntriesFrom(input, _repeated_repeatedFloatWrapper_codec);
            break;
          }
          case 1738: {
            repeatedDoubleWrapper_.AddEntriesFrom(input, _repeated_repeatedDoubleWrapper_codec);
            break;
          }
          case 1746: {
            repeatedStringWrapper_.AddEntriesFrom(input, _repeated_repeatedStringWrapper_codec);
            break;
          }
          case 1754: {
            repeatedBytesWrapper_.AddEntriesFrom(input, _repeated_repeatedBytesWrapper_codec);
            break;
          }
          case 2410: {
            if (optionalDuration_ == null) {
              OptionalDuration = new global::Google.Protobuf.WellKnownTypes.Duration();
            }
            input.ReadMessage(OptionalDuration);
            break;
          }
          case 2418: {
            if (optionalTimestamp_ == null) {
              OptionalTimestamp = new global::Google.Protobuf.WellKnownTypes.Timestamp();
            }
            input.ReadMessage(OptionalTimestamp);
            break;
          }
          case 2426: {
            if (optionalFieldMask_ == null) {
              OptionalFieldMask = new global::Google.Protobuf.WellKnownTypes.FieldMask();
            }
            input.ReadMessage(OptionalFieldMask);
            break;
          }
          case 2434: {
            if (optionalStruct_ == null) {
              OptionalStruct = new global::Google.Protobuf.WellKnownTypes.Struct();
            }
            input.ReadMessage(OptionalStruct);
            break;
          }
          case 2442: {
            if (optionalAny_ == null) {
              OptionalAny = new global::Google.Protobuf.WellKnownTypes.Any();
            }
            input.ReadMessage(OptionalAny);
            break;
          }
          case 2450: {
            if (optionalValue_ == null) {
              OptionalValue = new global::Google.Protobuf.WellKnownTypes.Value();
            }
            input.ReadMessage(OptionalValue);
            break;
          }
          case 2456: {
            OptionalNullValue = (global::Google.Protobuf.WellKnownTypes.NullValue) input.ReadEnum();
            break;
          }
          case 2490: {
            repeatedDuration_.AddEntriesFrom(input, _repeated_repeatedDuration_codec);
            break;
          }
          case 2498: {
            repeatedTimestamp_.AddEntriesFrom(input, _repeated_repeatedTimestamp_codec);
            break;
          }
          case 2506: {
            repeatedFieldmask_.AddEntriesFrom(input, _repeated_repeatedFieldmask_codec);
            break;
          }
          case 2522: {
            repeatedAny_.AddEntriesFrom(input, _repeated_repeatedAny_codec);
            break;
          }
          case 2530: {
            repeatedValue_.AddEntriesFrom(input, _repeated_repeatedValue_codec);
            break;
          }
          case 2538: {
            repeatedListValue_.AddEntriesFrom(input, _repeated_repeatedListValue_codec);
            break;
          }
          case 2594: {
            repeatedStruct_.AddEntriesFrom(input, _repeated_repeatedStruct_codec);
            break;
          }
          case 3208: {
            Fieldname1 = input.ReadInt32();
            break;
          }
          case 3216: {
            FieldName2 = input.ReadInt32();
            break;
          }
          case 3224: {
            FieldName3 = input.ReadInt32();
            break;
          }
          case 3232: {
            FieldName4 = input.ReadInt32();
            break;
          }
          case 3240: {
            Field0Name5 = input.ReadInt32();
            break;
          }
          case 3248: {
            Field0Name6 = input.ReadInt32();
            break;
          }
          case 3256: {
            FieldName7 = input.ReadInt32();
            break;
          }
          case 3264: {
            FieldName8 = input.ReadInt32();
            break;
          }
          case 3272: {
            FieldName9 = input.ReadInt32();
            break;
          }
          case 3280: {
            FieldName10 = input.ReadInt32();
            break;
          }
          case 3288: {
            FIELDNAME11 = input.ReadInt32();
            break;
          }
          case 3296: {
            FIELDName12 = input.ReadInt32();
            break;
          }
          case 3304: {
            FieldName13 = input.ReadInt32();
            break;
          }
          case 3312: {
            FieldName14 = input.ReadInt32();
            break;
          }
          case 3320: {
            FieldName15 = input.ReadInt32();
            break;
          }
          case 3328: {
            FieldName16 = input.ReadInt32();
            break;
          }
          case 3336: {
            FieldName17 = input.ReadInt32();
            break;
          }
          case 3344: {
            FieldName18 = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            OptionalInt32 = input.ReadInt32();
            break;
          }
          case 16: {
            OptionalInt64 = input.ReadInt64();
            break;
          }
          case 24: {
            OptionalUint32 = input.ReadUInt32();
            break;
          }
          case 32: {
            OptionalUint64 = input.ReadUInt64();
            break;
          }
          case 40: {
            OptionalSint32 = input.ReadSInt32();
            break;
          }
          case 48: {
            OptionalSint64 = input.ReadSInt64();
            break;
          }
          case 61: {
            OptionalFixed32 = input.ReadFixed32();
            break;
          }
          case 65: {
            OptionalFixed64 = input.ReadFixed64();
            break;
          }
          case 77: {
            OptionalSfixed32 = input.ReadSFixed32();
            break;
          }
          case 81: {
            OptionalSfixed64 = input.ReadSFixed64();
            break;
          }
          case 93: {
            OptionalFloat = input.ReadFloat();
            break;
          }
          case 97: {
            OptionalDouble = input.ReadDouble();
            break;
          }
          case 104: {
            OptionalBool = input.ReadBool();
            break;
          }
          case 114: {
            OptionalString = input.ReadString();
            break;
          }
          case 122: {
            OptionalBytes = input.ReadBytes();
            break;
          }
          case 146: {
            if (optionalNestedMessage_ == null) {
              OptionalNestedMessage = new global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage();
            }
            input.ReadMessage(OptionalNestedMessage);
            break;
          }
          case 154: {
            if (optionalForeignMessage_ == null) {
              OptionalForeignMessage = new global::ProtobufTestMessages.Proto3.ForeignMessage();
            }
            input.ReadMessage(OptionalForeignMessage);
            break;
          }
          case 168: {
            OptionalNestedEnum = (global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedEnum) input.ReadEnum();
            break;
          }
          case 176: {
            OptionalForeignEnum = (global::ProtobufTestMessages.Proto3.ForeignEnum) input.ReadEnum();
            break;
          }
          case 184: {
            OptionalAliasedEnum = (global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.AliasedEnum) input.ReadEnum();
            break;
          }
          case 194: {
            OptionalStringPiece = input.ReadString();
            break;
          }
          case 202: {
            OptionalCord = input.ReadString();
            break;
          }
          case 218: {
            if (recursiveMessage_ == null) {
              RecursiveMessage = new global::ProtobufTestMessages.Proto3.TestAllTypesProto3();
            }
            input.ReadMessage(RecursiveMessage);
            break;
          }
          case 250:
          case 248: {
            repeatedInt32_.AddEntriesFrom(ref input, _repeated_repeatedInt32_codec);
            break;
          }
          case 258:
          case 256: {
            repeatedInt64_.AddEntriesFrom(ref input, _repeated_repeatedInt64_codec);
            break;
          }
          case 266:
          case 264: {
            repeatedUint32_.AddEntriesFrom(ref input, _repeated_repeatedUint32_codec);
            break;
          }
          case 274:
          case 272: {
            repeatedUint64_.AddEntriesFrom(ref input, _repeated_repeatedUint64_codec);
            break;
          }
          case 282:
          case 280: {
            repeatedSint32_.AddEntriesFrom(ref input, _repeated_repeatedSint32_codec);
            break;
          }
          case 290:
          case 288: {
            repeatedSint64_.AddEntriesFrom(ref input, _repeated_repeatedSint64_codec);
            break;
          }
          case 298:
          case 301: {
            repeatedFixed32_.AddEntriesFrom(ref input, _repeated_repeatedFixed32_codec);
            break;
          }
          case 306:
          case 305: {
            repeatedFixed64_.AddEntriesFrom(ref input, _repeated_repeatedFixed64_codec);
            break;
          }
          case 314:
          case 317: {
            repeatedSfixed32_.AddEntriesFrom(ref input, _repeated_repeatedSfixed32_codec);
            break;
          }
          case 322:
          case 321: {
            repeatedSfixed64_.AddEntriesFrom(ref input, _repeated_repeatedSfixed64_codec);
            break;
          }
          case 330:
          case 333: {
            repeatedFloat_.AddEntriesFrom(ref input, _repeated_repeatedFloat_codec);
            break;
          }
          case 338:
          case 337: {
            repeatedDouble_.AddEntriesFrom(ref input, _repeated_repeatedDouble_codec);
            break;
          }
          case 346:
          case 344: {
            repeatedBool_.AddEntriesFrom(ref input, _repeated_repeatedBool_codec);
            break;
          }
          case 354: {
            repeatedString_.AddEntriesFrom(ref input, _repeated_repeatedString_codec);
            break;
          }
          case 362: {
            repeatedBytes_.AddEntriesFrom(ref input, _repeated_repeatedBytes_codec);
            break;
          }
          case 386: {
            repeatedNestedMessage_.AddEntriesFrom(ref input, _repeated_repeatedNestedMessage_codec);
            break;
          }
          case 394: {
            repeatedForeignMessage_.AddEntriesFrom(ref input, _repeated_repeatedForeignMessage_codec);
            break;
          }
          case 410:
          case 408: {
            repeatedNestedEnum_.AddEntriesFrom(ref input, _repeated_repeatedNestedEnum_codec);
            break;
          }
          case 418:
          case 416: {
            repeatedForeignEnum_.AddEntriesFrom(ref input, _repeated_repeatedForeignEnum_codec);
            break;
          }
          case 434: {
            repeatedStringPiece_.AddEntriesFrom(ref input, _repeated_repeatedStringPiece_codec);
            break;
          }
          case 442: {
            repeatedCord_.AddEntriesFrom(ref input, _repeated_repeatedCord_codec);
            break;
          }
          case 450: {
            mapInt32Int32_.AddEntriesFrom(ref input, _map_mapInt32Int32_codec);
            break;
          }
          case 458: {
            mapInt64Int64_.AddEntriesFrom(ref input, _map_mapInt64Int64_codec);
            break;
          }
          case 466: {
            mapUint32Uint32_.AddEntriesFrom(ref input, _map_mapUint32Uint32_codec);
            break;
          }
          case 474: {
            mapUint64Uint64_.AddEntriesFrom(ref input, _map_mapUint64Uint64_codec);
            break;
          }
          case 482: {
            mapSint32Sint32_.AddEntriesFrom(ref input, _map_mapSint32Sint32_codec);
            break;
          }
          case 490: {
            mapSint64Sint64_.AddEntriesFrom(ref input, _map_mapSint64Sint64_codec);
            break;
          }
          case 498: {
            mapFixed32Fixed32_.AddEntriesFrom(ref input, _map_mapFixed32Fixed32_codec);
            break;
          }
          case 506: {
            mapFixed64Fixed64_.AddEntriesFrom(ref input, _map_mapFixed64Fixed64_codec);
            break;
          }
          case 514: {
            mapSfixed32Sfixed32_.AddEntriesFrom(ref input, _map_mapSfixed32Sfixed32_codec);
            break;
          }
          case 522: {
            mapSfixed64Sfixed64_.AddEntriesFrom(ref input, _map_mapSfixed64Sfixed64_codec);
            break;
          }
          case 530: {
            mapInt32Float_.AddEntriesFrom(ref input, _map_mapInt32Float_codec);
            break;
          }
          case 538: {
            mapInt32Double_.AddEntriesFrom(ref input, _map_mapInt32Double_codec);
            break;
          }
          case 546: {
            mapBoolBool_.AddEntriesFrom(ref input, _map_mapBoolBool_codec);
            break;
          }
          case 554: {
            mapStringString_.AddEntriesFrom(ref input, _map_mapStringString_codec);
            break;
          }
          case 562: {
            mapStringBytes_.AddEntriesFrom(ref input, _map_mapStringBytes_codec);
            break;
          }
          case 570: {
            mapStringNestedMessage_.AddEntriesFrom(ref input, _map_mapStringNestedMessage_codec);
            break;
          }
          case 578: {
            mapStringForeignMessage_.AddEntriesFrom(ref input, _map_mapStringForeignMessage_codec);
            break;
          }
          case 586: {
            mapStringNestedEnum_.AddEntriesFrom(ref input, _map_mapStringNestedEnum_codec);
            break;
          }
          case 594: {
            mapStringForeignEnum_.AddEntriesFrom(ref input, _map_mapStringForeignEnum_codec);
            break;
          }
          case 602:
          case 600: {
            packedInt32_.AddEntriesFrom(ref input, _repeated_packedInt32_codec);
            break;
          }
          case 610:
          case 608: {
            packedInt64_.AddEntriesFrom(ref input, _repeated_packedInt64_codec);
            break;
          }
          case 618:
          case 616: {
            packedUint32_.AddEntriesFrom(ref input, _repeated_packedUint32_codec);
            break;
          }
          case 626:
          case 624: {
            packedUint64_.AddEntriesFrom(ref input, _repeated_packedUint64_codec);
            break;
          }
          case 634:
          case 632: {
            packedSint32_.AddEntriesFrom(ref input, _repeated_packedSint32_codec);
            break;
          }
          case 642:
          case 640: {
            packedSint64_.AddEntriesFrom(ref input, _repeated_packedSint64_codec);
            break;
          }
          case 650:
          case 653: {
            packedFixed32_.AddEntriesFrom(ref input, _repeated_packedFixed32_codec);
            break;
          }
          case 658:
          case 657: {
            packedFixed64_.AddEntriesFrom(ref input, _repeated_packedFixed64_codec);
            break;
          }
          case 666:
          case 669: {
            packedSfixed32_.AddEntriesFrom(ref input, _repeated_packedSfixed32_codec);
            break;
          }
          case 674:
          case 673: {
            packedSfixed64_.AddEntriesFrom(ref input, _repeated_packedSfixed64_codec);
            break;
          }
          case 682:
          case 685: {
            packedFloat_.AddEntriesFrom(ref input, _repeated_packedFloat_codec);
            break;
          }
          case 690:
          case 689: {
            packedDouble_.AddEntriesFrom(ref input, _repeated_packedDouble_codec);
            break;
          }
          case 698:
          case 696: {
            packedBool_.AddEntriesFrom(ref input, _repeated_packedBool_codec);
            break;
          }
          case 706:
          case 704: {
            packedNestedEnum_.AddEntriesFrom(ref input, _repeated_packedNestedEnum_codec);
            break;
          }
          case 714:
          case 712: {
            unpackedInt32_.AddEntriesFrom(ref input, _repeated_unpackedInt32_codec);
            break;
          }
          case 722:
          case 720: {
            unpackedInt64_.AddEntriesFrom(ref input, _repeated_unpackedInt64_codec);
            break;
          }
          case 730:
          case 728: {
            unpackedUint32_.AddEntriesFrom(ref input, _repeated_unpackedUint32_codec);
            break;
          }
          case 738:
          case 736: {
            unpackedUint64_.AddEntriesFrom(ref input, _repeated_unpackedUint64_codec);
            break;
          }
          case 746:
          case 744: {
            unpackedSint32_.AddEntriesFrom(ref input, _repeated_unpackedSint32_codec);
            break;
          }
          case 754:
          case 752: {
            unpackedSint64_.AddEntriesFrom(ref input, _repeated_unpackedSint64_codec);
            break;
          }
          case 762:
          case 765: {
            unpackedFixed32_.AddEntriesFrom(ref input, _repeated_unpackedFixed32_codec);
            break;
          }
          case 770:
          case 769: {
            unpackedFixed64_.AddEntriesFrom(ref input, _repeated_unpackedFixed64_codec);
            break;
          }
          case 778:
          case 781: {
            unpackedSfixed32_.AddEntriesFrom(ref input, _repeated_unpackedSfixed32_codec);
            break;
          }
          case 786:
          case 785: {
            unpackedSfixed64_.AddEntriesFrom(ref input, _repeated_unpackedSfixed64_codec);
            break;
          }
          case 794:
          case 797: {
            unpackedFloat_.AddEntriesFrom(ref input, _repeated_unpackedFloat_codec);
            break;
          }
          case 802:
          case 801: {
            unpackedDouble_.AddEntriesFrom(ref input, _repeated_unpackedDouble_codec);
            break;
          }
          case 810:
          case 808: {
            unpackedBool_.AddEntriesFrom(ref input, _repeated_unpackedBool_codec);
            break;
          }
          case 818:
          case 816: {
            unpackedNestedEnum_.AddEntriesFrom(ref input, _repeated_unpackedNestedEnum_codec);
            break;
          }
          case 888: {
            OneofUint32 = input.ReadUInt32();
            break;
          }
          case 898: {
            global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage subBuilder = new global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Types.NestedMessage();
            if (oneofFieldCase_ == OneofFieldOneofCase.OneofNestedMessage) {
              subBuilder.MergeFrom(OneofNestedMessage);
            }
            input.ReadMessage(subBuilder);
            OneofNestedMessage = subBuilder;
            break;
          }
          case 906: {
            OneofString = input.ReadString();
            break;
          }
          case 914: {
            OneofBytes = input.ReadBytes();
            break;
          }
          case 920: {
            OneofBool = input.ReadBool();
            break;
          }
          case 928: {
            OneofUint64 = input.ReadUInt64();
            break;
          }
          case 941: {
            OneofFloat = input.ReadFloat();
            break;
          }
          case 945: {
            OneofDouble = input.ReadDouble();
            break;
          }
          case 952: {
            oneofField_ = input.ReadEnum();
            oneofFieldCase_ = OneofFieldOneofCase.OneofEnum;
            break;
          }
          case 960: {
            oneofField_ = input.ReadEnum();
            oneofFieldCase_ = OneofFieldOneofCase.OneofNullValue;
            break;
          }
          case 1610: {
            bool? value = _single_optionalBoolWrapper_codec.Read(ref input);
            if (optionalBoolWrapper_ == null || value != false) {
              OptionalBoolWrapper = value;
            }
            break;
          }
          case 1618: {
            int? value = _single_optionalInt32Wrapper_codec.Read(ref input);
            if (optionalInt32Wrapper_ == null || value != 0) {
              OptionalInt32Wrapper = value;
            }
            break;
          }
          case 1626: {
            long? value = _single_optionalInt64Wrapper_codec.Read(ref input);
            if (optionalInt64Wrapper_ == null || value != 0L) {
              OptionalInt64Wrapper = value;
            }
            break;
          }
          case 1634: {
            uint? value = _single_optionalUint32Wrapper_codec.Read(ref input);
            if (optionalUint32Wrapper_ == null || value != 0) {
              OptionalUint32Wrapper = value;
            }
            break;
          }
          case 1642: {
            ulong? value = _single_optionalUint64Wrapper_codec.Read(ref input);
            if (optionalUint64Wrapper_ == null || value != 0UL) {
              OptionalUint64Wrapper = value;
            }
            break;
          }
          case 1650: {
            float? value = _single_optionalFloatWrapper_codec.Read(ref input);
            if (optionalFloatWrapper_ == null || value != 0F) {
              OptionalFloatWrapper = value;
            }
            break;
          }
          case 1658: {
            double? value = _single_optionalDoubleWrapper_codec.Read(ref input);
            if (optionalDoubleWrapper_ == null || value != 0D) {
              OptionalDoubleWrapper = value;
            }
            break;
          }
          case 1666: {
            string value = _single_optionalStringWrapper_codec.Read(ref input);
            if (optionalStringWrapper_ == null || value != "") {
              OptionalStringWrapper = value;
            }
            break;
          }
          case 1674: {
            pb::ByteString value = _single_optionalBytesWrapper_codec.Read(ref input);
            if (optionalBytesWrapper_ == null || value != pb::ByteString.Empty) {
              OptionalBytesWrapper = value;
            }
            break;
          }
          case 1690: {
            repeatedBoolWrapper_.AddEntriesFrom(ref input, _repeated_repeatedBoolWrapper_codec);
            break;
          }
          case 1698: {
            repeatedInt32Wrapper_.AddEntriesFrom(ref input, _repeated_repeatedInt32Wrapper_codec);
            break;
          }
          case 1706: {
            repeatedInt64Wrapper_.AddEntriesFrom(ref input, _repeated_repeatedInt64Wrapper_codec);
            break;
          }
          case 1714: {
            repeatedUint32Wrapper_.AddEntriesFrom(ref input, _repeated_repeatedUint32Wrapper_codec);
            break;
          }
          case 1722: {
            repeatedUint64Wrapper_.AddEntriesFrom(ref input, _repeated_repeatedUint64Wrapper_codec);
            break;
          }
          case 1730: {
            repeatedFloatWrapper_.AddEntriesFrom(ref input, _repeated_repeatedFloatWrapper_codec);
            break;
          }
          case 1738: {
            repeatedDoubleWrapper_.AddEntriesFrom(ref input, _repeated_repeatedDoubleWrapper_codec);
            break;
          }
          case 1746: {
            repeatedStringWrapper_.AddEntriesFrom(ref input, _repeated_repeatedStringWrapper_codec);
            break;
          }
          case 1754: {
            repeatedBytesWrapper_.AddEntriesFrom(ref input, _repeated_repeatedBytesWrapper_codec);
            break;
          }
          case 2410: {
            if (optionalDuration_ == null) {
              OptionalDuration = new global::Google.Protobuf.WellKnownTypes.Duration();
            }
            input.ReadMessage(OptionalDuration);
            break;
          }
          case 2418: {
            if (optionalTimestamp_ == null) {
              OptionalTimestamp = new global::Google.Protobuf.WellKnownTypes.Timestamp();
            }
            input.ReadMessage(OptionalTimestamp);
            break;
          }
          case 2426: {
            if (optionalFieldMask_ == null) {
              OptionalFieldMask = new global::Google.Protobuf.WellKnownTypes.FieldMask();
            }
            input.ReadMessage(OptionalFieldMask);
            break;
          }
          case 2434: {
            if (optionalStruct_ == null) {
              OptionalStruct = new global::Google.Protobuf.WellKnownTypes.Struct();
            }
            input.ReadMessage(OptionalStruct);
            break;
          }
          case 2442: {
            if (optionalAny_ == null) {
              OptionalAny = new global::Google.Protobuf.WellKnownTypes.Any();
            }
            input.ReadMessage(OptionalAny);
            break;
          }
          case 2450: {
            if (optionalValue_ == null) {
              OptionalValue = new global::Google.Protobuf.WellKnownTypes.Value();
            }
            input.ReadMessage(OptionalValue);
            break;
          }
          case 2456: {
            OptionalNullValue = (global::Google.Protobuf.WellKnownTypes.NullValue) input.ReadEnum();
            break;
          }
          case 2490: {
            repeatedDuration_.AddEntriesFrom(ref input, _repeated_repeatedDuration_codec);
            break;
          }
          case 2498: {
            repeatedTimestamp_.AddEntriesFrom(ref input, _repeated_repeatedTimestamp_codec);
            break;
          }
          case 2506: {
            repeatedFieldmask_.AddEntriesFrom(ref input, _repeated_repeatedFieldmask_codec);
            break;
          }
          case 2522: {
            repeatedAny_.AddEntriesFrom(ref input, _repeated_repeatedAny_codec);
            break;
          }
          case 2530: {
            repeatedValue_.AddEntriesFrom(ref input, _repeated_repeatedValue_codec);
            break;
          }
          case 2538: {
            repeatedListValue_.AddEntriesFrom(ref input, _repeated_repeatedListValue_codec);
            break;
          }
          case 2594: {
            repeatedStruct_.AddEntriesFrom(ref input, _repeated_repeatedStruct_codec);
            break;
          }
          case 3208: {
            Fieldname1 = input.ReadInt32();
            break;
          }
          case 3216: {
            FieldName2 = input.ReadInt32();
            break;
          }
          case 3224: {
            FieldName3 = input.ReadInt32();
            break;
          }
          case 3232: {
            FieldName4 = input.ReadInt32();
            break;
          }
          case 3240: {
            Field0Name5 = input.ReadInt32();
            break;
          }
          case 3248: {
            Field0Name6 = input.ReadInt32();
            break;
          }
          case 3256: {
            FieldName7 = input.ReadInt32();
            break;
          }
          case 3264: {
            FieldName8 = input.ReadInt32();
            break;
          }
          case 3272: {
            FieldName9 = input.ReadInt32();
            break;
          }
          case 3280: {
            FieldName10 = input.ReadInt32();
            break;
          }
          case 3288: {
            FIELDNAME11 = input.ReadInt32();
            break;
          }
          case 3296: {
            FIELDName12 = input.ReadInt32();
            break;
          }
          case 3304: {
            FieldName13 = input.ReadInt32();
            break;
          }
          case 3312: {
            FieldName14 = input.ReadInt32();
            break;
          }
          case 3320: {
            FieldName15 = input.ReadInt32();
            break;
          }
          case 3328: {
            FieldName16 = input.ReadInt32();
            break;
          }
          case 3336: {
            FieldName17 = input.ReadInt32();
            break;
          }
          case 3344: {
            FieldName18 = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the TestAllTypesProto3 message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      public enum NestedEnum {
        [pbr::OriginalName("FOO")] Foo = 0,
        [pbr::OriginalName("BAR")] Bar = 1,
        [pbr::OriginalName("BAZ")] Baz = 2,
        /// <summary>
        /// Intentionally negative.
        /// </summary>
        [pbr::OriginalName("NEG")] Neg = -1,
      }

      public enum AliasedEnum {
        [pbr::OriginalName("ALIAS_FOO")] AliasFoo = 0,
        [pbr::OriginalName("ALIAS_BAR")] AliasBar = 1,
        [pbr::OriginalName("ALIAS_BAZ")] AliasBaz = 2,
        [pbr::OriginalName("MOO", PreferredAlias = false)] Moo = 2,
        [pbr::OriginalName("moo", PreferredAlias = false)] Moo_ = 2,
        [pbr::OriginalName("bAz", PreferredAlias = false)] BAz = 2,
      }

      public sealed partial class NestedMessage : pb::IMessage<NestedMessage>
      #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          , pb::IBufferMessage
      #endif
      {
        private static readonly pb::MessageParser<NestedMessage> _parser = new pb::MessageParser<NestedMessage>(() => new NestedMessage());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pb::MessageParser<NestedMessage> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::ProtobufTestMessages.Proto3.TestAllTypesProto3.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public NestedMessage() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public NestedMessage(NestedMessage other) : this() {
          a_ = other.a_;
          corecursive_ = other.corecursive_ != null ? other.corecursive_.Clone() : null;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public NestedMessage Clone() {
          return new NestedMessage(this);
        }

        /// <summary>Field number for the "a" field.</summary>
        public const int AFieldNumber = 1;
        private int a_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int A {
          get { return a_; }
          set {
            a_ = value;
          }
        }

        /// <summary>Field number for the "corecursive" field.</summary>
        public const int CorecursiveFieldNumber = 2;
        private global::ProtobufTestMessages.Proto3.TestAllTypesProto3 corecursive_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public global::ProtobufTestMessages.Proto3.TestAllTypesProto3 Corecursive {
          get { return corecursive_; }
          set {
            corecursive_ = value;
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override bool Equals(object other) {
          return Equals(other as NestedMessage);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public bool Equals(NestedMessage other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (A != other.A) return false;
          if (!object.Equals(Corecursive, other.Corecursive)) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override int GetHashCode() {
          int hash = 1;
          if (A != 0) hash ^= A.GetHashCode();
          if (corecursive_ != null) hash ^= Corecursive.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void WriteTo(pb::CodedOutputStream output) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          output.WriteRawMessage(this);
        #else
          if (A != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(A);
          }
          if (corecursive_ != null) {
            output.WriteRawTag(18);
            output.WriteMessage(Corecursive);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
          if (A != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(A);
          }
          if (corecursive_ != null) {
            output.WriteRawTag(18);
            output.WriteMessage(Corecursive);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(ref output);
          }
        }
        #endif

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public int CalculateSize() {
          int size = 0;
          if (A != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(A);
          }
          if (corecursive_ != null) {
            size += 1 + pb::CodedOutputStream.ComputeMessageSize(Corecursive);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(NestedMessage other) {
          if (other == null) {
            return;
          }
          if (other.A != 0) {
            A = other.A;
          }
          if (other.corecursive_ != null) {
            if (corecursive_ == null) {
              Corecursive = new global::ProtobufTestMessages.Proto3.TestAllTypesProto3();
            }
            Corecursive.MergeFrom(other.Corecursive);
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        public void MergeFrom(pb::CodedInputStream input) {
        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
          input.ReadRawMessage(this);
        #else
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                A = input.ReadInt32();
                break;
              }
              case 18: {
                if (corecursive_ == null) {
                  Corecursive = new global::ProtobufTestMessages.Proto3.TestAllTypesProto3();
                }
                input.ReadMessage(Corecursive);
                break;
              }
            }
          }
        #endif
        }

        #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
        void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
                break;
              case 8: {
                A = input.ReadInt32();
                break;
              }
              case 18: {
                if (corecursive_ == null) {
                  Corecursive = new global::ProtobufTestMessages.Proto3.TestAllTypesProto3();
                }
                input.ReadMessage(Corecursive);
                break;
              }
            }
          }
        }
        #endif

      }

    }
    #endregion

  }

  public sealed partial class ForeignMessage : pb::IMessage<ForeignMessage>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ForeignMessage> _parser = new pb::MessageParser<ForeignMessage>(() => new ForeignMessage());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ForeignMessage> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufTestMessages.Proto3.TestMessagesProto3Reflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ForeignMessage() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ForeignMessage(ForeignMessage other) : this() {
      c_ = other.c_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ForeignMessage Clone() {
      return new ForeignMessage(this);
    }

    /// <summary>Field number for the "c" field.</summary>
    public const int CFieldNumber = 1;
    private int c_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int C {
      get { return c_; }
      set {
        c_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ForeignMessage);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ForeignMessage other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (C != other.C) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (C != 0) hash ^= C.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (C != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(C);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (C != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(C);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (C != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(C);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ForeignMessage other) {
      if (other == null) {
        return;
      }
      if (other.C != 0) {
        C = other.C;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            C = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            C = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class NullHypothesisProto3 : pb::IMessage<NullHypothesisProto3>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<NullHypothesisProto3> _parser = new pb::MessageParser<NullHypothesisProto3>(() => new NullHypothesisProto3());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<NullHypothesisProto3> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufTestMessages.Proto3.TestMessagesProto3Reflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullHypothesisProto3() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullHypothesisProto3(NullHypothesisProto3 other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullHypothesisProto3 Clone() {
      return new NullHypothesisProto3(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as NullHypothesisProto3);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(NullHypothesisProto3 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(NullHypothesisProto3 other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  public sealed partial class EnumOnlyProto3 : pb::IMessage<EnumOnlyProto3>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<EnumOnlyProto3> _parser = new pb::MessageParser<EnumOnlyProto3>(() => new EnumOnlyProto3());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<EnumOnlyProto3> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::ProtobufTestMessages.Proto3.TestMessagesProto3Reflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public EnumOnlyProto3() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public EnumOnlyProto3(EnumOnlyProto3 other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public EnumOnlyProto3 Clone() {
      return new EnumOnlyProto3(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as EnumOnlyProto3);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(EnumOnlyProto3 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(EnumOnlyProto3 other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the EnumOnlyProto3 message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      public enum Bool {
        [pbr::OriginalName("kFalse")] KFalse = 0,
        [pbr::OriginalName("kTrue")] KTrue = 1,
      }

    }
    #endregion

  }

  #endregion

}

#endregion Designer generated code
