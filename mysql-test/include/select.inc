--source include/elide_costs.inc

#
# Find string "NOTE NOTE NOTE" in order to find some 'unsure' tests
#

-- disable_query_log
-- disable_result_log
# High values set for sampling scanning, effectively forcing full scan for
# statistics.
SET @innodb_stats_transient_sample_pages= @@GLOBAL.innodb_stats_transient_sample_pages;
SET @@global.innodb_stats_transient_sample_pages=5000000000;
SET @innodb_stats_persistent_sample_pages= @@GLOBAL.innodb_stats_persistent_sample_pages;
SET @@global.innodb_stats_persistent_sample_pages=5000000000;
-- enable_result_log
-- enable_query_log

#
# Simple select test
#

--disable_warnings
drop table if exists t1,t2,t3,t4,t11;
# The following may be left from older tests
drop table if exists t1_1,t1_2,t9_1,t9_2,t1aa,t2aa;
drop view if exists v1;
--enable_warnings

CREATE TABLE t1 (
  Period smallint(4) unsigned zerofill DEFAULT '0000' NOT NULL,
  Varor_period smallint(4) unsigned DEFAULT '0' NOT NULL
);

INSERT INTO t1 VALUES (9410,9412);
  
select period from t1;
select * from t1;
select t1.* from t1;

#
# Create test table
#

CREATE TABLE t2 (
  auto int not null auto_increment,
  fld1 int(6) unsigned zerofill DEFAULT '000000' NOT NULL,
  companynr tinyint(2) unsigned zerofill DEFAULT '00' NOT NULL,
  fld3 char(30) DEFAULT '' NOT NULL,
  fld4 char(35) DEFAULT '' NOT NULL,
  fld5 char(35) DEFAULT '' NOT NULL,
  fld6 char(4) DEFAULT '' NOT NULL,
  UNIQUE fld1 (fld1),
  KEY fld3 (fld3),
  PRIMARY KEY (auto)
) charset utf8mb4;  

#
# Populate table
#

--disable_query_log
INSERT INTO t2 VALUES (1,000001,00,'Omaha','teethe','neat','');
INSERT INTO t2 VALUES (2,011401,37,'breaking','dreaded','Steinberg','W');
INSERT INTO t2 VALUES (3,011402,37,'Romans','scholastics','jarring','');
INSERT INTO t2 VALUES (4,011403,37,'intercepted','audiology','tinily','');
INSERT INTO t2 VALUES (5,011501,37,'bewilderingly','wallet','balled','');
INSERT INTO t2 VALUES (6,011701,37,'astound','parters','persist','W');
INSERT INTO t2 VALUES (7,011702,37,'admonishing','eschew','attainments','');
INSERT INTO t2 VALUES (8,011703,37,'sumac','quitter','fanatic','');
INSERT INTO t2 VALUES (9,012001,37,'flanking','neat','measures','FAS');
INSERT INTO t2 VALUES (10,012003,37,'combed','Steinberg','rightfulness','');
INSERT INTO t2 VALUES (11,012004,37,'subjective','jarring','capably','');
INSERT INTO t2 VALUES (12,012005,37,'scatterbrain','tinily','impulsive','');
INSERT INTO t2 VALUES (13,012301,37,'Eulerian','balled','starlet','');
INSERT INTO t2 VALUES (14,012302,36,'dubbed','persist','terminators','');
INSERT INTO t2 VALUES (15,012303,37,'Kane','attainments','untying','');
INSERT INTO t2 VALUES (16,012304,37,'overlay','fanatic','announces','FAS');
INSERT INTO t2 VALUES (17,012305,37,'perturb','measures','featherweight','FAS');
INSERT INTO t2 VALUES (18,012306,37,'goblins','rightfulness','pessimist','FAS');
INSERT INTO t2 VALUES (19,012501,37,'annihilates','capably','daughter','');
INSERT INTO t2 VALUES (20,012602,37,'Wotan','impulsive','decliner','FAS');
INSERT INTO t2 VALUES (21,012603,37,'snatching','starlet','lawgiver','');
INSERT INTO t2 VALUES (22,012604,37,'concludes','terminators','stated','');
INSERT INTO t2 VALUES (23,012605,37,'laterally','untying','readable','');
INSERT INTO t2 VALUES (24,012606,37,'yelped','announces','attrition','');
INSERT INTO t2 VALUES (25,012701,37,'grazing','featherweight','cascade','FAS');
INSERT INTO t2 VALUES (26,012702,37,'Baird','pessimist','motors','FAS');
INSERT INTO t2 VALUES (27,012703,37,'celery','daughter','interrogate','');
INSERT INTO t2 VALUES (28,012704,37,'misunderstander','decliner','pests','W');
INSERT INTO t2 VALUES (29,013601,37,'handgun','lawgiver','stairway','');
INSERT INTO t2 VALUES (30,013602,37,'foldout','stated','dopers','FAS');
INSERT INTO t2 VALUES (31,013603,37,'mystic','readable','testicle','W');
INSERT INTO t2 VALUES (32,013604,37,'succumbed','attrition','Parsifal','W');
INSERT INTO t2 VALUES (33,013605,37,'Nabisco','cascade','leavings','');
INSERT INTO t2 VALUES (34,013606,37,'fingerings','motors','postulation','W');
INSERT INTO t2 VALUES (35,013607,37,'aging','interrogate','squeaking','');
INSERT INTO t2 VALUES (36,013608,37,'afield','pests','contrasted','');
INSERT INTO t2 VALUES (37,013609,37,'ammonium','stairway','leftover','');
INSERT INTO t2 VALUES (38,013610,37,'boat','dopers','whiteners','');
INSERT INTO t2 VALUES (39,013801,37,'intelligibility','testicle','erases','W');
INSERT INTO t2 VALUES (40,013802,37,'Augustine','Parsifal','Punjab','W');
INSERT INTO t2 VALUES (41,013803,37,'teethe','leavings','Merritt','');
INSERT INTO t2 VALUES (42,013804,37,'dreaded','postulation','Quixotism','');
INSERT INTO t2 VALUES (43,013901,37,'scholastics','squeaking','sweetish','FAS');
INSERT INTO t2 VALUES (44,016001,37,'audiology','contrasted','dogging','FAS');
INSERT INTO t2 VALUES (45,016201,37,'wallet','leftover','scornfully','FAS');
INSERT INTO t2 VALUES (46,016202,37,'parters','whiteners','bellow','');
INSERT INTO t2 VALUES (47,016301,37,'eschew','erases','bills','');
INSERT INTO t2 VALUES (48,016302,37,'quitter','Punjab','cupboard','FAS');
INSERT INTO t2 VALUES (49,016303,37,'neat','Merritt','sureties','FAS');
INSERT INTO t2 VALUES (50,016304,37,'Steinberg','Quixotism','puddings','');
INSERT INTO t2 VALUES (51,018001,37,'jarring','sweetish','tapestry','');
INSERT INTO t2 VALUES (52,018002,37,'tinily','dogging','fetters','');
INSERT INTO t2 VALUES (53,018003,37,'balled','scornfully','bivalves','');
INSERT INTO t2 VALUES (54,018004,37,'persist','bellow','incurring','');
INSERT INTO t2 VALUES (55,018005,37,'attainments','bills','Adolph','');
INSERT INTO t2 VALUES (56,018007,37,'fanatic','cupboard','pithed','');
INSERT INTO t2 VALUES (57,018008,37,'measures','sureties','emergency','');
INSERT INTO t2 VALUES (58,018009,37,'rightfulness','puddings','Miles','');
INSERT INTO t2 VALUES (59,018010,37,'capably','tapestry','trimmings','');
INSERT INTO t2 VALUES (60,018012,37,'impulsive','fetters','tragedies','W');
INSERT INTO t2 VALUES (61,018013,37,'starlet','bivalves','skulking','W');
INSERT INTO t2 VALUES (62,018014,37,'terminators','incurring','flint','');
INSERT INTO t2 VALUES (63,018015,37,'untying','Adolph','flopping','W');
INSERT INTO t2 VALUES (64,018016,37,'announces','pithed','relaxing','FAS');
INSERT INTO t2 VALUES (65,018017,37,'featherweight','emergency','offload','FAS');
INSERT INTO t2 VALUES (66,018018,37,'pessimist','Miles','suites','W');
INSERT INTO t2 VALUES (67,018019,37,'daughter','trimmings','lists','FAS');
INSERT INTO t2 VALUES (68,018020,37,'decliner','tragedies','animized','FAS');
INSERT INTO t2 VALUES (69,018021,37,'lawgiver','skulking','multilayer','W');
INSERT INTO t2 VALUES (70,018022,37,'stated','flint','standardizes','FAS');
INSERT INTO t2 VALUES (71,018023,37,'readable','flopping','Judas','');
INSERT INTO t2 VALUES (72,018024,37,'attrition','relaxing','vacuuming','W');
INSERT INTO t2 VALUES (73,018025,37,'cascade','offload','dentally','W');
INSERT INTO t2 VALUES (74,018026,37,'motors','suites','humanness','W');
INSERT INTO t2 VALUES (75,018027,37,'interrogate','lists','inch','W');
INSERT INTO t2 VALUES (76,018028,37,'pests','animized','Weissmuller','W');
INSERT INTO t2 VALUES (77,018029,37,'stairway','multilayer','irresponsibly','W');
INSERT INTO t2 VALUES (78,018030,37,'dopers','standardizes','luckily','FAS');
INSERT INTO t2 VALUES (79,018032,37,'testicle','Judas','culled','W');
INSERT INTO t2 VALUES (80,018033,37,'Parsifal','vacuuming','medical','FAS');
INSERT INTO t2 VALUES (81,018034,37,'leavings','dentally','bloodbath','FAS');
INSERT INTO t2 VALUES (82,018035,37,'postulation','humanness','subschema','W');
INSERT INTO t2 VALUES (83,018036,37,'squeaking','inch','animals','W');
INSERT INTO t2 VALUES (84,018037,37,'contrasted','Weissmuller','Micronesia','');
INSERT INTO t2 VALUES (85,018038,37,'leftover','irresponsibly','repetitions','');
INSERT INTO t2 VALUES (86,018039,37,'whiteners','luckily','Antares','');
INSERT INTO t2 VALUES (87,018040,37,'erases','culled','ventilate','W');
INSERT INTO t2 VALUES (88,018041,37,'Punjab','medical','pityingly','');
INSERT INTO t2 VALUES (89,018042,37,'Merritt','bloodbath','interdependent','');
INSERT INTO t2 VALUES (90,018043,37,'Quixotism','subschema','Graves','FAS');
INSERT INTO t2 VALUES (91,018044,37,'sweetish','animals','neonatal','');
INSERT INTO t2 VALUES (92,018045,37,'dogging','Micronesia','scribbled','FAS');
INSERT INTO t2 VALUES (93,018046,37,'scornfully','repetitions','chafe','W');
INSERT INTO t2 VALUES (94,018048,37,'bellow','Antares','honoring','');
INSERT INTO t2 VALUES (95,018049,37,'bills','ventilate','realtor','');
INSERT INTO t2 VALUES (96,018050,37,'cupboard','pityingly','elite','');
INSERT INTO t2 VALUES (97,018051,37,'sureties','interdependent','funereal','');
INSERT INTO t2 VALUES (98,018052,37,'puddings','Graves','abrogating','');
INSERT INTO t2 VALUES (99,018053,50,'tapestry','neonatal','sorters','');
INSERT INTO t2 VALUES (100,018054,37,'fetters','scribbled','Conley','');
INSERT INTO t2 VALUES (101,018055,37,'bivalves','chafe','lectured','');
INSERT INTO t2 VALUES (102,018056,37,'incurring','honoring','Abraham','');
INSERT INTO t2 VALUES (103,018057,37,'Adolph','realtor','Hawaii','W');
INSERT INTO t2 VALUES (104,018058,37,'pithed','elite','cage','');
INSERT INTO t2 VALUES (105,018059,36,'emergency','funereal','hushes','');
INSERT INTO t2 VALUES (106,018060,37,'Miles','abrogating','Simla','');
INSERT INTO t2 VALUES (107,018061,37,'trimmings','sorters','reporters','');
INSERT INTO t2 VALUES (108,018101,37,'tragedies','Conley','Dutchman','FAS');
INSERT INTO t2 VALUES (109,018102,37,'skulking','lectured','descendants','FAS');
INSERT INTO t2 VALUES (110,018103,37,'flint','Abraham','groupings','FAS');
INSERT INTO t2 VALUES (111,018104,37,'flopping','Hawaii','dissociate','');
INSERT INTO t2 VALUES (112,018201,37,'relaxing','cage','coexist','W');
INSERT INTO t2 VALUES (113,018202,37,'offload','hushes','Beebe','');
INSERT INTO t2 VALUES (114,018402,37,'suites','Simla','Taoism','');
INSERT INTO t2 VALUES (115,018403,37,'lists','reporters','Connally','');
INSERT INTO t2 VALUES (116,018404,37,'animized','Dutchman','fetched','FAS');
INSERT INTO t2 VALUES (117,018405,37,'multilayer','descendants','checkpoints','FAS');
INSERT INTO t2 VALUES (118,018406,37,'standardizes','groupings','rusting','');
INSERT INTO t2 VALUES (119,018409,37,'Judas','dissociate','galling','');
INSERT INTO t2 VALUES (120,018601,37,'vacuuming','coexist','obliterates','');
INSERT INTO t2 VALUES (121,018602,37,'dentally','Beebe','traitor','');
INSERT INTO t2 VALUES (122,018603,37,'humanness','Taoism','resumes','FAS');
INSERT INTO t2 VALUES (123,018801,37,'inch','Connally','analyzable','FAS');
INSERT INTO t2 VALUES (124,018802,37,'Weissmuller','fetched','terminator','FAS');
INSERT INTO t2 VALUES (125,018803,37,'irresponsibly','checkpoints','gritty','FAS');
INSERT INTO t2 VALUES (126,018804,37,'luckily','rusting','firearm','W');
INSERT INTO t2 VALUES (127,018805,37,'culled','galling','minima','');
INSERT INTO t2 VALUES (128,018806,37,'medical','obliterates','Selfridge','');
INSERT INTO t2 VALUES (129,018807,37,'bloodbath','traitor','disable','');
INSERT INTO t2 VALUES (130,018808,37,'subschema','resumes','witchcraft','W');
INSERT INTO t2 VALUES (131,018809,37,'animals','analyzable','betroth','W');
INSERT INTO t2 VALUES (132,018810,37,'Micronesia','terminator','Manhattanize','');
INSERT INTO t2 VALUES (133,018811,37,'repetitions','gritty','imprint','');
INSERT INTO t2 VALUES (134,018812,37,'Antares','firearm','peeked','');
INSERT INTO t2 VALUES (135,019101,37,'ventilate','minima','swelling','');
INSERT INTO t2 VALUES (136,019102,37,'pityingly','Selfridge','interrelationships','W');
INSERT INTO t2 VALUES (137,019103,37,'interdependent','disable','riser','');
INSERT INTO t2 VALUES (138,019201,37,'Graves','witchcraft','Gandhian','W');
INSERT INTO t2 VALUES (139,030501,37,'neonatal','betroth','peacock','A');
INSERT INTO t2 VALUES (140,030502,50,'scribbled','Manhattanize','bee','A');
INSERT INTO t2 VALUES (141,030503,37,'chafe','imprint','kanji','');
INSERT INTO t2 VALUES (142,030504,37,'honoring','peeked','dental','');
INSERT INTO t2 VALUES (143,031901,37,'realtor','swelling','scarf','FAS');
INSERT INTO t2 VALUES (144,036001,37,'elite','interrelationships','chasm','A');
INSERT INTO t2 VALUES (145,036002,37,'funereal','riser','insolence','A');
INSERT INTO t2 VALUES (146,036004,37,'abrogating','Gandhian','syndicate','');
INSERT INTO t2 VALUES (147,036005,37,'sorters','peacock','alike','');
INSERT INTO t2 VALUES (148,038001,37,'Conley','bee','imperial','A');
INSERT INTO t2 VALUES (149,038002,37,'lectured','kanji','convulsion','A');
INSERT INTO t2 VALUES (150,038003,37,'Abraham','dental','railway','A');
INSERT INTO t2 VALUES (151,038004,37,'Hawaii','scarf','validate','A');
INSERT INTO t2 VALUES (152,038005,37,'cage','chasm','normalizes','A');
INSERT INTO t2 VALUES (153,038006,37,'hushes','insolence','comprehensive','');
INSERT INTO t2 VALUES (154,038007,37,'Simla','syndicate','chewing','');
INSERT INTO t2 VALUES (155,038008,37,'reporters','alike','denizen','');
INSERT INTO t2 VALUES (156,038009,37,'Dutchman','imperial','schemer','');
INSERT INTO t2 VALUES (157,038010,37,'descendants','convulsion','chronicle','');
INSERT INTO t2 VALUES (158,038011,37,'groupings','railway','Kline','');
INSERT INTO t2 VALUES (159,038012,37,'dissociate','validate','Anatole','');
INSERT INTO t2 VALUES (160,038013,37,'coexist','normalizes','partridges','');
INSERT INTO t2 VALUES (161,038014,37,'Beebe','comprehensive','brunch','');
INSERT INTO t2 VALUES (162,038015,37,'Taoism','chewing','recruited','');
INSERT INTO t2 VALUES (163,038016,37,'Connally','denizen','dimensions','W');
INSERT INTO t2 VALUES (164,038017,37,'fetched','schemer','Chicana','W');
INSERT INTO t2 VALUES (165,038018,37,'checkpoints','chronicle','announced','');
INSERT INTO t2 VALUES (166,038101,37,'rusting','Kline','praised','FAS');
INSERT INTO t2 VALUES (167,038102,37,'galling','Anatole','employing','');
INSERT INTO t2 VALUES (168,038103,37,'obliterates','partridges','linear','');
INSERT INTO t2 VALUES (169,038104,37,'traitor','brunch','quagmire','');
INSERT INTO t2 VALUES (170,038201,37,'resumes','recruited','western','A');
INSERT INTO t2 VALUES (171,038202,37,'analyzable','dimensions','relishing','');
INSERT INTO t2 VALUES (172,038203,37,'terminator','Chicana','serving','A');
INSERT INTO t2 VALUES (173,038204,37,'gritty','announced','scheduling','');
INSERT INTO t2 VALUES (174,038205,37,'firearm','praised','lore','');
INSERT INTO t2 VALUES (175,038206,37,'minima','employing','eventful','');
INSERT INTO t2 VALUES (176,038208,37,'Selfridge','linear','arteriole','A');
INSERT INTO t2 VALUES (177,042801,37,'disable','quagmire','disentangle','');
INSERT INTO t2 VALUES (178,042802,37,'witchcraft','western','cured','A');
INSERT INTO t2 VALUES (179,046101,37,'betroth','relishing','Fenton','W');
INSERT INTO t2 VALUES (180,048001,37,'Manhattanize','serving','avoidable','A');
INSERT INTO t2 VALUES (181,048002,37,'imprint','scheduling','drains','A');
INSERT INTO t2 VALUES (182,048003,37,'peeked','lore','detectably','FAS');
INSERT INTO t2 VALUES (183,048004,37,'swelling','eventful','husky','');
INSERT INTO t2 VALUES (184,048005,37,'interrelationships','arteriole','impelling','');
INSERT INTO t2 VALUES (185,048006,37,'riser','disentangle','undoes','');
INSERT INTO t2 VALUES (186,048007,37,'Gandhian','cured','evened','');
INSERT INTO t2 VALUES (187,048008,37,'peacock','Fenton','squeezes','');
INSERT INTO t2 VALUES (188,048101,37,'bee','avoidable','destroyer','FAS');
INSERT INTO t2 VALUES (189,048102,37,'kanji','drains','rudeness','');
INSERT INTO t2 VALUES (190,048201,37,'dental','detectably','beaner','FAS');
INSERT INTO t2 VALUES (191,048202,37,'scarf','husky','boorish','');
INSERT INTO t2 VALUES (192,048203,37,'chasm','impelling','Everhart','');
INSERT INTO t2 VALUES (193,048204,37,'insolence','undoes','encompass','A');
INSERT INTO t2 VALUES (194,048205,37,'syndicate','evened','mushrooms','');
INSERT INTO t2 VALUES (195,048301,37,'alike','squeezes','Alison','A');
INSERT INTO t2 VALUES (196,048302,37,'imperial','destroyer','externally','FAS');
INSERT INTO t2 VALUES (197,048303,37,'convulsion','rudeness','pellagra','');
INSERT INTO t2 VALUES (198,048304,37,'railway','beaner','cult','');
INSERT INTO t2 VALUES (199,048305,37,'validate','boorish','creek','A');
INSERT INTO t2 VALUES (200,048401,37,'normalizes','Everhart','Huffman','');
INSERT INTO t2 VALUES (201,048402,37,'comprehensive','encompass','Majorca','FAS');
INSERT INTO t2 VALUES (202,048403,37,'chewing','mushrooms','governing','A');
INSERT INTO t2 VALUES (203,048404,37,'denizen','Alison','gadfly','FAS');
INSERT INTO t2 VALUES (204,048405,37,'schemer','externally','reassigned','FAS');
INSERT INTO t2 VALUES (205,048406,37,'chronicle','pellagra','intentness','W');
INSERT INTO t2 VALUES (206,048407,37,'Kline','cult','craziness','');
INSERT INTO t2 VALUES (207,048408,37,'Anatole','creek','psychic','');
INSERT INTO t2 VALUES (208,048409,37,'partridges','Huffman','squabbled','');
INSERT INTO t2 VALUES (209,048410,37,'brunch','Majorca','burlesque','');
INSERT INTO t2 VALUES (210,048411,37,'recruited','governing','capped','');
INSERT INTO t2 VALUES (211,048412,37,'dimensions','gadfly','extracted','A');
INSERT INTO t2 VALUES (212,048413,37,'Chicana','reassigned','DiMaggio','');
INSERT INTO t2 VALUES (213,048601,37,'announced','intentness','exclamation','FAS');
INSERT INTO t2 VALUES (214,048602,37,'praised','craziness','subdirectory','');
INSERT INTO t2 VALUES (215,048603,37,'employing','psychic','fangs','');
INSERT INTO t2 VALUES (216,048604,37,'linear','squabbled','buyer','A');
INSERT INTO t2 VALUES (217,048801,37,'quagmire','burlesque','pithing','A');
INSERT INTO t2 VALUES (218,050901,37,'western','capped','transistorizing','A');
INSERT INTO t2 VALUES (219,051201,37,'relishing','extracted','nonbiodegradable','');
INSERT INTO t2 VALUES (220,056002,37,'serving','DiMaggio','dislocate','');
INSERT INTO t2 VALUES (221,056003,37,'scheduling','exclamation','monochromatic','FAS');
INSERT INTO t2 VALUES (222,056004,37,'lore','subdirectory','batting','');
INSERT INTO t2 VALUES (223,056102,37,'eventful','fangs','postcondition','A');
INSERT INTO t2 VALUES (224,056203,37,'arteriole','buyer','catalog','FAS');
INSERT INTO t2 VALUES (225,056204,37,'disentangle','pithing','Remus','');
INSERT INTO t2 VALUES (226,058003,37,'cured','transistorizing','devices','A');
INSERT INTO t2 VALUES (227,058004,37,'Fenton','nonbiodegradable','bike','A');
INSERT INTO t2 VALUES (228,058005,37,'avoidable','dislocate','qualify','');
INSERT INTO t2 VALUES (229,058006,37,'drains','monochromatic','detained','');
INSERT INTO t2 VALUES (230,058007,37,'detectably','batting','commended','');
INSERT INTO t2 VALUES (231,058101,37,'husky','postcondition','civilize','');
INSERT INTO t2 VALUES (232,058102,37,'impelling','catalog','Elmhurst','');
INSERT INTO t2 VALUES (233,058103,37,'undoes','Remus','anesthetizing','');
INSERT INTO t2 VALUES (234,058105,37,'evened','devices','deaf','');
INSERT INTO t2 VALUES (235,058111,37,'squeezes','bike','Brigham','');
INSERT INTO t2 VALUES (236,058112,37,'destroyer','qualify','title','');
INSERT INTO t2 VALUES (237,058113,37,'rudeness','detained','coarse','');
INSERT INTO t2 VALUES (238,058114,37,'beaner','commended','combinations','');
INSERT INTO t2 VALUES (239,058115,37,'boorish','civilize','grayness','');
INSERT INTO t2 VALUES (240,058116,37,'Everhart','Elmhurst','innumerable','FAS');
INSERT INTO t2 VALUES (241,058117,37,'encompass','anesthetizing','Caroline','A');
INSERT INTO t2 VALUES (242,058118,37,'mushrooms','deaf','fatty','FAS');
INSERT INTO t2 VALUES (243,058119,37,'Alison','Brigham','eastbound','');
INSERT INTO t2 VALUES (244,058120,37,'externally','title','inexperienced','');
INSERT INTO t2 VALUES (245,058121,37,'pellagra','coarse','hoarder','A');
INSERT INTO t2 VALUES (246,058122,37,'cult','combinations','scotch','W');
INSERT INTO t2 VALUES (247,058123,37,'creek','grayness','passport','A');
INSERT INTO t2 VALUES (248,058124,37,'Huffman','innumerable','strategic','FAS');
INSERT INTO t2 VALUES (249,058125,37,'Majorca','Caroline','gated','');
INSERT INTO t2 VALUES (250,058126,37,'governing','fatty','flog','');
INSERT INTO t2 VALUES (251,058127,37,'gadfly','eastbound','Pipestone','');
INSERT INTO t2 VALUES (252,058128,37,'reassigned','inexperienced','Dar','');
INSERT INTO t2 VALUES (253,058201,37,'intentness','hoarder','Corcoran','');
INSERT INTO t2 VALUES (254,058202,37,'craziness','scotch','flyers','A');
INSERT INTO t2 VALUES (255,058303,37,'psychic','passport','competitions','W');
INSERT INTO t2 VALUES (256,058304,37,'squabbled','strategic','suppliers','FAS');
INSERT INTO t2 VALUES (257,058602,37,'burlesque','gated','skips','');
INSERT INTO t2 VALUES (258,058603,37,'capped','flog','institutes','');
INSERT INTO t2 VALUES (259,058604,37,'extracted','Pipestone','troop','A');
INSERT INTO t2 VALUES (260,058605,37,'DiMaggio','Dar','connective','W');
INSERT INTO t2 VALUES (261,058606,37,'exclamation','Corcoran','denies','');
INSERT INTO t2 VALUES (262,058607,37,'subdirectory','flyers','polka','');
INSERT INTO t2 VALUES (263,060401,36,'fangs','competitions','observations','FAS');
INSERT INTO t2 VALUES (264,061701,36,'buyer','suppliers','askers','');
INSERT INTO t2 VALUES (265,066201,36,'pithing','skips','homeless','FAS');
INSERT INTO t2 VALUES (266,066501,36,'transistorizing','institutes','Anna','');
INSERT INTO t2 VALUES (267,068001,36,'nonbiodegradable','troop','subdirectories','W');
INSERT INTO t2 VALUES (268,068002,36,'dislocate','connective','decaying','FAS');
INSERT INTO t2 VALUES (269,068005,36,'monochromatic','denies','outwitting','W');
INSERT INTO t2 VALUES (270,068006,36,'batting','polka','Harpy','W');
INSERT INTO t2 VALUES (271,068007,36,'postcondition','observations','crazed','');
INSERT INTO t2 VALUES (272,068008,36,'catalog','askers','suffocate','');
INSERT INTO t2 VALUES (273,068009,36,'Remus','homeless','provers','FAS');
INSERT INTO t2 VALUES (274,068010,36,'devices','Anna','technically','');
INSERT INTO t2 VALUES (275,068011,36,'bike','subdirectories','Franklinizations','');
INSERT INTO t2 VALUES (276,068202,36,'qualify','decaying','considered','');
INSERT INTO t2 VALUES (277,068302,36,'detained','outwitting','tinnily','');
INSERT INTO t2 VALUES (278,068303,36,'commended','Harpy','uninterruptedly','');
INSERT INTO t2 VALUES (279,068401,36,'civilize','crazed','whistled','A');
INSERT INTO t2 VALUES (280,068501,36,'Elmhurst','suffocate','automate','');
INSERT INTO t2 VALUES (281,068502,36,'anesthetizing','provers','gutting','W');
INSERT INTO t2 VALUES (282,068503,36,'deaf','technically','surreptitious','');
INSERT INTO t2 VALUES (283,068602,36,'Brigham','Franklinizations','Choctaw','');
INSERT INTO t2 VALUES (284,068603,36,'title','considered','cooks','');
INSERT INTO t2 VALUES (285,068701,36,'coarse','tinnily','millivolt','FAS');
INSERT INTO t2 VALUES (286,068702,36,'combinations','uninterruptedly','counterpoise','');
INSERT INTO t2 VALUES (287,068703,36,'grayness','whistled','Gothicism','');
INSERT INTO t2 VALUES (288,076001,36,'innumerable','automate','feminine','');
INSERT INTO t2 VALUES (289,076002,36,'Caroline','gutting','metaphysically','W');
INSERT INTO t2 VALUES (290,076101,36,'fatty','surreptitious','sanding','A');
INSERT INTO t2 VALUES (291,076102,36,'eastbound','Choctaw','contributorily','');
INSERT INTO t2 VALUES (292,076103,36,'inexperienced','cooks','receivers','FAS');
INSERT INTO t2 VALUES (293,076302,36,'hoarder','millivolt','adjourn','');
INSERT INTO t2 VALUES (294,076303,36,'scotch','counterpoise','straggled','A');
INSERT INTO t2 VALUES (295,076304,36,'passport','Gothicism','druggists','');
INSERT INTO t2 VALUES (296,076305,36,'strategic','feminine','thanking','FAS');
INSERT INTO t2 VALUES (297,076306,36,'gated','metaphysically','ostrich','');
INSERT INTO t2 VALUES (298,076307,36,'flog','sanding','hopelessness','FAS');
INSERT INTO t2 VALUES (299,076402,36,'Pipestone','contributorily','Eurydice','');
INSERT INTO t2 VALUES (300,076501,36,'Dar','receivers','excitation','W');
INSERT INTO t2 VALUES (301,076502,36,'Corcoran','adjourn','presumes','FAS');
INSERT INTO t2 VALUES (302,076701,36,'flyers','straggled','imaginable','FAS');
INSERT INTO t2 VALUES (303,078001,36,'competitions','druggists','concoct','W');
INSERT INTO t2 VALUES (304,078002,36,'suppliers','thanking','peering','W');
INSERT INTO t2 VALUES (305,078003,36,'skips','ostrich','Phelps','FAS');
INSERT INTO t2 VALUES (306,078004,36,'institutes','hopelessness','ferociousness','FAS');
INSERT INTO t2 VALUES (307,078005,36,'troop','Eurydice','sentences','');
INSERT INTO t2 VALUES (308,078006,36,'connective','excitation','unlocks','');
INSERT INTO t2 VALUES (309,078007,36,'denies','presumes','engrossing','W');
INSERT INTO t2 VALUES (310,078008,36,'polka','imaginable','Ruth','');
INSERT INTO t2 VALUES (311,078101,36,'observations','concoct','tying','');
INSERT INTO t2 VALUES (312,078103,36,'askers','peering','exclaimers','');
INSERT INTO t2 VALUES (313,078104,36,'homeless','Phelps','synergy','');
INSERT INTO t2 VALUES (314,078105,36,'Anna','ferociousness','Huey','W');
INSERT INTO t2 VALUES (315,082101,36,'subdirectories','sentences','merging','');
INSERT INTO t2 VALUES (316,083401,36,'decaying','unlocks','judges','A');
INSERT INTO t2 VALUES (317,084001,36,'outwitting','engrossing','Shylock','W');
INSERT INTO t2 VALUES (318,084002,36,'Harpy','Ruth','Miltonism','');
INSERT INTO t2 VALUES (319,086001,36,'crazed','tying','hen','W');
INSERT INTO t2 VALUES (320,086102,36,'suffocate','exclaimers','honeybee','FAS');
INSERT INTO t2 VALUES (321,086201,36,'provers','synergy','towers','');
INSERT INTO t2 VALUES (322,088001,36,'technically','Huey','dilutes','W');
INSERT INTO t2 VALUES (323,088002,36,'Franklinizations','merging','numerals','FAS');
INSERT INTO t2 VALUES (324,088003,36,'considered','judges','democracy','FAS');
INSERT INTO t2 VALUES (325,088004,36,'tinnily','Shylock','Ibero-','');
INSERT INTO t2 VALUES (326,088101,36,'uninterruptedly','Miltonism','invalids','');
INSERT INTO t2 VALUES (327,088102,36,'whistled','hen','behavior','');
INSERT INTO t2 VALUES (328,088103,36,'automate','honeybee','accruing','');
INSERT INTO t2 VALUES (329,088104,36,'gutting','towers','relics','A');
INSERT INTO t2 VALUES (330,088105,36,'surreptitious','dilutes','rackets','');
INSERT INTO t2 VALUES (331,088106,36,'Choctaw','numerals','Fischbein','W');
INSERT INTO t2 VALUES (332,088201,36,'cooks','democracy','phony','W');
INSERT INTO t2 VALUES (333,088203,36,'millivolt','Ibero-','cross','FAS');
INSERT INTO t2 VALUES (334,088204,36,'counterpoise','invalids','cleanup','');
INSERT INTO t2 VALUES (335,088302,37,'Gothicism','behavior','conspirator','');
INSERT INTO t2 VALUES (336,088303,37,'feminine','accruing','label','FAS');
INSERT INTO t2 VALUES (337,088305,37,'metaphysically','relics','university','');
INSERT INTO t2 VALUES (338,088402,37,'sanding','rackets','cleansed','FAS');
INSERT INTO t2 VALUES (339,088501,36,'contributorily','Fischbein','ballgown','');
INSERT INTO t2 VALUES (340,088502,36,'receivers','phony','starlet','');
INSERT INTO t2 VALUES (341,088503,36,'adjourn','cross','aqueous','');
INSERT INTO t2 VALUES (342,098001,58,'straggled','cleanup','portrayal','A');
INSERT INTO t2 VALUES (343,098002,58,'druggists','conspirator','despising','W');
INSERT INTO t2 VALUES (344,098003,58,'thanking','label','distort','W');
INSERT INTO t2 VALUES (345,098004,58,'ostrich','university','palmed','');
INSERT INTO t2 VALUES (346,098005,58,'hopelessness','cleansed','faced','');
INSERT INTO t2 VALUES (347,098006,58,'Eurydice','ballgown','silverware','');
INSERT INTO t2 VALUES (348,141903,29,'excitation','starlet','assessor','');
INSERT INTO t2 VALUES (349,098008,58,'presumes','aqueous','spiders','');
INSERT INTO t2 VALUES (350,098009,58,'imaginable','portrayal','artificially','');
INSERT INTO t2 VALUES (351,098010,58,'concoct','despising','reminiscence','');
INSERT INTO t2 VALUES (352,098011,58,'peering','distort','Mexican','');
INSERT INTO t2 VALUES (353,098012,58,'Phelps','palmed','obnoxious','');
INSERT INTO t2 VALUES (354,098013,58,'ferociousness','faced','fragile','');
INSERT INTO t2 VALUES (355,098014,58,'sentences','silverware','apprehensible','');
INSERT INTO t2 VALUES (356,098015,58,'unlocks','assessor','births','');
INSERT INTO t2 VALUES (357,098016,58,'engrossing','spiders','garages','');
INSERT INTO t2 VALUES (358,098017,58,'Ruth','artificially','panty','');
INSERT INTO t2 VALUES (359,098018,58,'tying','reminiscence','anteater','');
INSERT INTO t2 VALUES (360,098019,58,'exclaimers','Mexican','displacement','A');
INSERT INTO t2 VALUES (361,098020,58,'synergy','obnoxious','drovers','A');
INSERT INTO t2 VALUES (362,098021,58,'Huey','fragile','patenting','A');
INSERT INTO t2 VALUES (363,098022,58,'merging','apprehensible','far','A');
INSERT INTO t2 VALUES (364,098023,58,'judges','births','shrieks','');
INSERT INTO t2 VALUES (365,098024,58,'Shylock','garages','aligning','W');
INSERT INTO t2 VALUES (366,098025,37,'Miltonism','panty','pragmatism','');
INSERT INTO t2 VALUES (367,106001,36,'hen','anteater','fevers','W');
INSERT INTO t2 VALUES (368,108001,36,'honeybee','displacement','reexamines','A');
INSERT INTO t2 VALUES (369,108002,36,'towers','drovers','occupancies','');
INSERT INTO t2 VALUES (370,108003,36,'dilutes','patenting','sweats','FAS');
INSERT INTO t2 VALUES (371,108004,36,'numerals','far','modulators','');
INSERT INTO t2 VALUES (372,108005,36,'democracy','shrieks','demand','W');
INSERT INTO t2 VALUES (373,108007,36,'Ibero-','aligning','Madeira','');
INSERT INTO t2 VALUES (374,108008,36,'invalids','pragmatism','Viennese','W');
INSERT INTO t2 VALUES (375,108009,36,'behavior','fevers','chillier','W');
INSERT INTO t2 VALUES (376,108010,36,'accruing','reexamines','wildcats','FAS');
INSERT INTO t2 VALUES (377,108011,36,'relics','occupancies','gentle','');
INSERT INTO t2 VALUES (378,108012,36,'rackets','sweats','Angles','W');
INSERT INTO t2 VALUES (379,108101,36,'Fischbein','modulators','accuracies','');
INSERT INTO t2 VALUES (380,108102,36,'phony','demand','toggle','');
INSERT INTO t2 VALUES (381,108103,36,'cross','Madeira','Mendelssohn','W');
INSERT INTO t2 VALUES (382,108111,50,'cleanup','Viennese','behaviorally','');
INSERT INTO t2 VALUES (383,108105,36,'conspirator','chillier','Rochford','');
INSERT INTO t2 VALUES (384,108106,36,'label','wildcats','mirror','W');
INSERT INTO t2 VALUES (385,108107,36,'university','gentle','Modula','');
INSERT INTO t2 VALUES (386,108108,50,'cleansed','Angles','clobbering','');
INSERT INTO t2 VALUES (387,108109,36,'ballgown','accuracies','chronography','');
INSERT INTO t2 VALUES (388,108110,36,'starlet','toggle','Eskimoizeds','');
INSERT INTO t2 VALUES (389,108201,36,'aqueous','Mendelssohn','British','W');
INSERT INTO t2 VALUES (390,108202,36,'portrayal','behaviorally','pitfalls','');
INSERT INTO t2 VALUES (391,108203,36,'despising','Rochford','verify','W');
INSERT INTO t2 VALUES (392,108204,36,'distort','mirror','scatter','FAS');
INSERT INTO t2 VALUES (393,108205,36,'palmed','Modula','Aztecan','');
INSERT INTO t2 VALUES (394,108301,36,'faced','clobbering','acuity','W');
INSERT INTO t2 VALUES (395,108302,36,'silverware','chronography','sinking','W');
INSERT INTO t2 VALUES (396,112101,36,'assessor','Eskimoizeds','beasts','FAS');
INSERT INTO t2 VALUES (397,112102,36,'spiders','British','Witt','W');
INSERT INTO t2 VALUES (398,113701,36,'artificially','pitfalls','physicists','FAS');
INSERT INTO t2 VALUES (399,116001,36,'reminiscence','verify','folksong','A');
INSERT INTO t2 VALUES (400,116201,36,'Mexican','scatter','strokes','FAS');
INSERT INTO t2 VALUES (401,116301,36,'obnoxious','Aztecan','crowder','');
INSERT INTO t2 VALUES (402,116302,36,'fragile','acuity','merry','');
INSERT INTO t2 VALUES (403,116601,36,'apprehensible','sinking','cadenced','');
INSERT INTO t2 VALUES (404,116602,36,'births','beasts','alimony','A');
INSERT INTO t2 VALUES (405,116603,36,'garages','Witt','principled','A');
INSERT INTO t2 VALUES (406,116701,36,'panty','physicists','golfing','');
INSERT INTO t2 VALUES (407,116702,36,'anteater','folksong','undiscovered','');
INSERT INTO t2 VALUES (408,118001,36,'displacement','strokes','irritates','');
INSERT INTO t2 VALUES (409,118002,36,'drovers','crowder','patriots','A');
INSERT INTO t2 VALUES (410,118003,36,'patenting','merry','rooms','FAS');
INSERT INTO t2 VALUES (411,118004,36,'far','cadenced','towering','W');
INSERT INTO t2 VALUES (412,118005,36,'shrieks','alimony','displease','');
INSERT INTO t2 VALUES (413,118006,36,'aligning','principled','photosensitive','');
INSERT INTO t2 VALUES (414,118007,36,'pragmatism','golfing','inking','');
INSERT INTO t2 VALUES (415,118008,36,'fevers','undiscovered','gainers','');
INSERT INTO t2 VALUES (416,118101,36,'reexamines','irritates','leaning','A');
INSERT INTO t2 VALUES (417,118102,36,'occupancies','patriots','hydrant','A');
INSERT INTO t2 VALUES (418,118103,36,'sweats','rooms','preserve','');
INSERT INTO t2 VALUES (419,118202,36,'modulators','towering','blinded','A');
INSERT INTO t2 VALUES (420,118203,36,'demand','displease','interactions','A');
INSERT INTO t2 VALUES (421,118204,36,'Madeira','photosensitive','Barry','');
INSERT INTO t2 VALUES (422,118302,36,'Viennese','inking','whiteness','A');
INSERT INTO t2 VALUES (423,118304,36,'chillier','gainers','pastimes','W');
INSERT INTO t2 VALUES (424,118305,36,'wildcats','leaning','Edenization','');
INSERT INTO t2 VALUES (425,118306,36,'gentle','hydrant','Muscat','');
INSERT INTO t2 VALUES (426,118307,36,'Angles','preserve','assassinated','');
INSERT INTO t2 VALUES (427,123101,36,'accuracies','blinded','labeled','');
INSERT INTO t2 VALUES (428,123102,36,'toggle','interactions','glacial','A');
INSERT INTO t2 VALUES (429,123301,36,'Mendelssohn','Barry','implied','W');
INSERT INTO t2 VALUES (430,126001,36,'behaviorally','whiteness','bibliographies','W');
INSERT INTO t2 VALUES (431,126002,36,'Rochford','pastimes','Buchanan','');
INSERT INTO t2 VALUES (432,126003,36,'mirror','Edenization','forgivably','FAS');
INSERT INTO t2 VALUES (433,126101,36,'Modula','Muscat','innuendo','A');
INSERT INTO t2 VALUES (434,126301,36,'clobbering','assassinated','den','FAS');
INSERT INTO t2 VALUES (435,126302,36,'chronography','labeled','submarines','W');
INSERT INTO t2 VALUES (436,126402,36,'Eskimoizeds','glacial','mouthful','A');
INSERT INTO t2 VALUES (437,126601,36,'British','implied','expiring','');
INSERT INTO t2 VALUES (438,126602,36,'pitfalls','bibliographies','unfulfilled','FAS');
INSERT INTO t2 VALUES (439,126702,36,'verify','Buchanan','precession','');
INSERT INTO t2 VALUES (440,128001,36,'scatter','forgivably','nullified','');
INSERT INTO t2 VALUES (441,128002,36,'Aztecan','innuendo','affects','');
INSERT INTO t2 VALUES (442,128003,36,'acuity','den','Cynthia','');
INSERT INTO t2 VALUES (443,128004,36,'sinking','submarines','Chablis','A');
INSERT INTO t2 VALUES (444,128005,36,'beasts','mouthful','betterments','FAS');
INSERT INTO t2 VALUES (445,128007,36,'Witt','expiring','advertising','');
INSERT INTO t2 VALUES (446,128008,36,'physicists','unfulfilled','rubies','A');
INSERT INTO t2 VALUES (447,128009,36,'folksong','precession','southwest','FAS');
INSERT INTO t2 VALUES (448,128010,36,'strokes','nullified','superstitious','A');
INSERT INTO t2 VALUES (449,128011,36,'crowder','affects','tabernacle','W');
INSERT INTO t2 VALUES (450,128012,36,'merry','Cynthia','silk','A');
INSERT INTO t2 VALUES (451,128013,36,'cadenced','Chablis','handsomest','A');
INSERT INTO t2 VALUES (452,128014,36,'alimony','betterments','Persian','A');
INSERT INTO t2 VALUES (453,128015,36,'principled','advertising','analog','W');
INSERT INTO t2 VALUES (454,128016,36,'golfing','rubies','complex','W');
INSERT INTO t2 VALUES (455,128017,36,'undiscovered','southwest','Taoist','');
INSERT INTO t2 VALUES (456,128018,36,'irritates','superstitious','suspend','');
INSERT INTO t2 VALUES (457,128019,36,'patriots','tabernacle','relegated','');
INSERT INTO t2 VALUES (458,128020,36,'rooms','silk','awesome','W');
INSERT INTO t2 VALUES (459,128021,36,'towering','handsomest','Bruxelles','');
INSERT INTO t2 VALUES (460,128022,36,'displease','Persian','imprecisely','A');
INSERT INTO t2 VALUES (461,128023,36,'photosensitive','analog','televise','');
INSERT INTO t2 VALUES (462,128101,36,'inking','complex','braking','');
INSERT INTO t2 VALUES (463,128102,36,'gainers','Taoist','true','FAS');
INSERT INTO t2 VALUES (464,128103,36,'leaning','suspend','disappointing','FAS');
INSERT INTO t2 VALUES (465,128104,36,'hydrant','relegated','navally','W');
INSERT INTO t2 VALUES (466,128106,36,'preserve','awesome','circus','');
INSERT INTO t2 VALUES (467,128107,36,'blinded','Bruxelles','beetles','');
INSERT INTO t2 VALUES (468,128108,36,'interactions','imprecisely','trumps','');
INSERT INTO t2 VALUES (469,128202,36,'Barry','televise','fourscore','W');
INSERT INTO t2 VALUES (470,128203,36,'whiteness','braking','Blackfoots','');
INSERT INTO t2 VALUES (471,128301,36,'pastimes','true','Grady','');
INSERT INTO t2 VALUES (472,128302,36,'Edenization','disappointing','quiets','FAS');
INSERT INTO t2 VALUES (473,128303,36,'Muscat','navally','floundered','FAS');
INSERT INTO t2 VALUES (474,128304,36,'assassinated','circus','profundity','W');
INSERT INTO t2 VALUES (475,128305,36,'labeled','beetles','Garrisonian','W');
INSERT INTO t2 VALUES (476,128307,36,'glacial','trumps','Strauss','');
INSERT INTO t2 VALUES (477,128401,36,'implied','fourscore','cemented','FAS');
INSERT INTO t2 VALUES (478,128502,36,'bibliographies','Blackfoots','contrition','A');
INSERT INTO t2 VALUES (479,128503,36,'Buchanan','Grady','mutations','');
INSERT INTO t2 VALUES (480,128504,36,'forgivably','quiets','exhibits','W');
INSERT INTO t2 VALUES (481,128505,36,'innuendo','floundered','tits','');
INSERT INTO t2 VALUES (482,128601,36,'den','profundity','mate','A');
INSERT INTO t2 VALUES (483,128603,36,'submarines','Garrisonian','arches','');
INSERT INTO t2 VALUES (484,128604,36,'mouthful','Strauss','Moll','');
INSERT INTO t2 VALUES (485,128702,36,'expiring','cemented','ropers','');
INSERT INTO t2 VALUES (486,128703,36,'unfulfilled','contrition','bombast','');
INSERT INTO t2 VALUES (487,128704,36,'precession','mutations','difficultly','A');
INSERT INTO t2 VALUES (488,138001,36,'nullified','exhibits','adsorption','');
INSERT INTO t2 VALUES (489,138002,36,'affects','tits','definiteness','FAS');
INSERT INTO t2 VALUES (490,138003,36,'Cynthia','mate','cultivation','A');
INSERT INTO t2 VALUES (491,138004,36,'Chablis','arches','heals','A');
INSERT INTO t2 VALUES (492,138005,36,'betterments','Moll','Heusen','W');
INSERT INTO t2 VALUES (493,138006,36,'advertising','ropers','target','FAS');
INSERT INTO t2 VALUES (494,138007,36,'rubies','bombast','cited','A');
INSERT INTO t2 VALUES (495,138008,36,'southwest','difficultly','congresswoman','W');
INSERT INTO t2 VALUES (496,138009,36,'superstitious','adsorption','Katherine','');
INSERT INTO t2 VALUES (497,138102,36,'tabernacle','definiteness','titter','A');
INSERT INTO t2 VALUES (498,138103,36,'silk','cultivation','aspire','A');
INSERT INTO t2 VALUES (499,138104,36,'handsomest','heals','Mardis','');
INSERT INTO t2 VALUES (500,138105,36,'Persian','Heusen','Nadia','W');
INSERT INTO t2 VALUES (501,138201,36,'analog','target','estimating','FAS');
INSERT INTO t2 VALUES (502,138302,36,'complex','cited','stuck','A');
INSERT INTO t2 VALUES (503,138303,36,'Taoist','congresswoman','fifteenth','A');
INSERT INTO t2 VALUES (504,138304,36,'suspend','Katherine','Colombo','');
INSERT INTO t2 VALUES (505,138401,29,'relegated','titter','survey','A');
INSERT INTO t2 VALUES (506,140102,29,'awesome','aspire','staffing','');
INSERT INTO t2 VALUES (507,140103,29,'Bruxelles','Mardis','obtain','');
INSERT INTO t2 VALUES (508,140104,29,'imprecisely','Nadia','loaded','');
INSERT INTO t2 VALUES (509,140105,29,'televise','estimating','slaughtered','');
INSERT INTO t2 VALUES (510,140201,29,'braking','stuck','lights','A');
INSERT INTO t2 VALUES (511,140701,29,'true','fifteenth','circumference','');
INSERT INTO t2 VALUES (512,141501,29,'disappointing','Colombo','dull','A');
INSERT INTO t2 VALUES (513,141502,29,'navally','survey','weekly','A');
INSERT INTO t2 VALUES (514,141901,29,'circus','staffing','wetness','');
INSERT INTO t2 VALUES (515,141902,29,'beetles','obtain','visualized','');
INSERT INTO t2 VALUES (516,142101,29,'trumps','loaded','Tannenbaum','');
INSERT INTO t2 VALUES (517,142102,29,'fourscore','slaughtered','moribund','');
INSERT INTO t2 VALUES (518,142103,29,'Blackfoots','lights','demultiplex','');
INSERT INTO t2 VALUES (519,142701,29,'Grady','circumference','lockings','');
INSERT INTO t2 VALUES (520,143001,29,'quiets','dull','thugs','FAS');
INSERT INTO t2 VALUES (521,143501,29,'floundered','weekly','unnerves','');
INSERT INTO t2 VALUES (522,143502,29,'profundity','wetness','abut','');
INSERT INTO t2 VALUES (523,148001,29,'Garrisonian','visualized','Chippewa','A');
INSERT INTO t2 VALUES (524,148002,29,'Strauss','Tannenbaum','stratifications','A');
INSERT INTO t2 VALUES (525,148003,29,'cemented','moribund','signaled','');
INSERT INTO t2 VALUES (526,148004,29,'contrition','demultiplex','Italianizes','A');
INSERT INTO t2 VALUES (527,148005,29,'mutations','lockings','algorithmic','A');
INSERT INTO t2 VALUES (528,148006,29,'exhibits','thugs','paranoid','FAS');
INSERT INTO t2 VALUES (529,148007,29,'tits','unnerves','camping','A');
INSERT INTO t2 VALUES (530,148009,29,'mate','abut','signifying','A');
INSERT INTO t2 VALUES (531,148010,29,'arches','Chippewa','Patrice','W');
INSERT INTO t2 VALUES (532,148011,29,'Moll','stratifications','search','A');
INSERT INTO t2 VALUES (533,148012,29,'ropers','signaled','Angeles','A');
INSERT INTO t2 VALUES (534,148013,29,'bombast','Italianizes','semblance','');
INSERT INTO t2 VALUES (535,148023,36,'difficultly','algorithmic','taxed','');
INSERT INTO t2 VALUES (536,148015,29,'adsorption','paranoid','Beatrice','');
INSERT INTO t2 VALUES (537,148016,29,'definiteness','camping','retrace','');
INSERT INTO t2 VALUES (538,148017,29,'cultivation','signifying','lockout','');
INSERT INTO t2 VALUES (539,148018,29,'heals','Patrice','grammatic','');
INSERT INTO t2 VALUES (540,148019,29,'Heusen','search','helmsman','');
INSERT INTO t2 VALUES (541,148020,29,'target','Angeles','uniform','W');
INSERT INTO t2 VALUES (542,148021,29,'cited','semblance','hamming','');
INSERT INTO t2 VALUES (543,148022,29,'congresswoman','taxed','disobedience','');
INSERT INTO t2 VALUES (544,148101,29,'Katherine','Beatrice','captivated','A');
INSERT INTO t2 VALUES (545,148102,29,'titter','retrace','transferals','A');
INSERT INTO t2 VALUES (546,148201,29,'aspire','lockout','cartographer','A');
INSERT INTO t2 VALUES (547,148401,29,'Mardis','grammatic','aims','FAS');
INSERT INTO t2 VALUES (548,148402,29,'Nadia','helmsman','Pakistani','');
INSERT INTO t2 VALUES (549,148501,29,'estimating','uniform','burglarized','FAS');
INSERT INTO t2 VALUES (550,148502,29,'stuck','hamming','saucepans','A');
INSERT INTO t2 VALUES (551,148503,29,'fifteenth','disobedience','lacerating','A');
INSERT INTO t2 VALUES (552,148504,29,'Colombo','captivated','corny','');
INSERT INTO t2 VALUES (553,148601,29,'survey','transferals','megabytes','FAS');
INSERT INTO t2 VALUES (554,148602,29,'staffing','cartographer','chancellor','');
INSERT INTO t2 VALUES (555,150701,29,'obtain','aims','bulk','A');
INSERT INTO t2 VALUES (556,152101,29,'loaded','Pakistani','commits','A');
INSERT INTO t2 VALUES (557,152102,29,'slaughtered','burglarized','meson','W');
INSERT INTO t2 VALUES (558,155202,36,'lights','saucepans','deputies','');
INSERT INTO t2 VALUES (559,155203,29,'circumference','lacerating','northeaster','A');
INSERT INTO t2 VALUES (560,155204,29,'dull','corny','dipole','');
INSERT INTO t2 VALUES (561,155205,29,'weekly','megabytes','machining','0');
INSERT INTO t2 VALUES (562,156001,29,'wetness','chancellor','therefore','');
INSERT INTO t2 VALUES (563,156002,29,'visualized','bulk','Telefunken','');
INSERT INTO t2 VALUES (564,156102,29,'Tannenbaum','commits','salvaging','');
INSERT INTO t2 VALUES (565,156301,29,'moribund','meson','Corinthianizes','A');
INSERT INTO t2 VALUES (566,156302,29,'demultiplex','deputies','restlessly','A');
INSERT INTO t2 VALUES (567,156303,29,'lockings','northeaster','bromides','');
INSERT INTO t2 VALUES (568,156304,29,'thugs','dipole','generalized','A');
INSERT INTO t2 VALUES (569,156305,29,'unnerves','machining','mishaps','');
INSERT INTO t2 VALUES (570,156306,29,'abut','therefore','quelling','');
INSERT INTO t2 VALUES (571,156501,29,'Chippewa','Telefunken','spiritual','A');
INSERT INTO t2 VALUES (572,158001,29,'stratifications','salvaging','beguiles','FAS');
INSERT INTO t2 VALUES (573,158002,29,'signaled','Corinthianizes','Trobriand','FAS');
INSERT INTO t2 VALUES (574,158101,29,'Italianizes','restlessly','fleeing','A');
INSERT INTO t2 VALUES (575,158102,29,'algorithmic','bromides','Armour','A');
INSERT INTO t2 VALUES (576,158103,29,'paranoid','generalized','chin','A');
INSERT INTO t2 VALUES (577,158201,29,'camping','mishaps','provers','A');
INSERT INTO t2 VALUES (578,158202,29,'signifying','quelling','aeronautic','A');
INSERT INTO t2 VALUES (579,158203,29,'Patrice','spiritual','voltage','W');
INSERT INTO t2 VALUES (580,158204,29,'search','beguiles','sash','');
INSERT INTO t2 VALUES (581,158301,29,'Angeles','Trobriand','anaerobic','A');
INSERT INTO t2 VALUES (582,158302,29,'semblance','fleeing','simultaneous','A');
INSERT INTO t2 VALUES (583,158303,29,'taxed','Armour','accumulating','A');
INSERT INTO t2 VALUES (584,158304,29,'Beatrice','chin','Medusan','A');
INSERT INTO t2 VALUES (585,158305,29,'retrace','provers','shouted','A');
INSERT INTO t2 VALUES (586,158306,29,'lockout','aeronautic','freakish','');
INSERT INTO t2 VALUES (587,158501,29,'grammatic','voltage','index','FAS');
INSERT INTO t2 VALUES (588,160301,29,'helmsman','sash','commercially','');
INSERT INTO t2 VALUES (589,166101,50,'uniform','anaerobic','mistiness','A');
INSERT INTO t2 VALUES (590,166102,50,'hamming','simultaneous','endpoint','');
INSERT INTO t2 VALUES (591,168001,29,'disobedience','accumulating','straight','A');
INSERT INTO t2 VALUES (592,168002,29,'captivated','Medusan','flurried','');
INSERT INTO t2 VALUES (593,168003,29,'transferals','shouted','denotative','A');
INSERT INTO t2 VALUES (594,168101,29,'cartographer','freakish','coming','FAS');
INSERT INTO t2 VALUES (595,168102,29,'aims','index','commencements','FAS');
INSERT INTO t2 VALUES (596,168103,29,'Pakistani','commercially','gentleman','');
INSERT INTO t2 VALUES (597,168104,29,'burglarized','mistiness','gifted','');
INSERT INTO t2 VALUES (598,168202,29,'saucepans','endpoint','Shanghais','');
INSERT INTO t2 VALUES (599,168301,29,'lacerating','straight','sportswriting','A');
INSERT INTO t2 VALUES (600,168502,29,'corny','flurried','sloping','A');
INSERT INTO t2 VALUES (601,168503,29,'megabytes','denotative','navies','');
INSERT INTO t2 VALUES (602,168601,29,'chancellor','coming','leaflet','A');
INSERT INTO t2 VALUES (603,173001,40,'bulk','commencements','shooter','');
INSERT INTO t2 VALUES (604,173701,40,'commits','gentleman','Joplin','FAS');
INSERT INTO t2 VALUES (605,173702,40,'meson','gifted','babies','');
INSERT INTO t2 VALUES (606,176001,40,'deputies','Shanghais','subdivision','FAS');
INSERT INTO t2 VALUES (607,176101,40,'northeaster','sportswriting','burstiness','W');
INSERT INTO t2 VALUES (608,176201,40,'dipole','sloping','belted','FAS');
INSERT INTO t2 VALUES (609,176401,40,'machining','navies','assails','FAS');
INSERT INTO t2 VALUES (610,176501,40,'therefore','leaflet','admiring','W');
INSERT INTO t2 VALUES (611,176601,40,'Telefunken','shooter','swaying','0');
INSERT INTO t2 VALUES (612,176602,40,'salvaging','Joplin','Goldstine','FAS');
INSERT INTO t2 VALUES (613,176603,40,'Corinthianizes','babies','fitting','');
INSERT INTO t2 VALUES (614,178001,40,'restlessly','subdivision','Norwalk','W');
INSERT INTO t2 VALUES (615,178002,40,'bromides','burstiness','weakening','W');
INSERT INTO t2 VALUES (616,178003,40,'generalized','belted','analogy','FAS');
INSERT INTO t2 VALUES (617,178004,40,'mishaps','assails','deludes','');
INSERT INTO t2 VALUES (618,178005,40,'quelling','admiring','cokes','');
INSERT INTO t2 VALUES (619,178006,40,'spiritual','swaying','Clayton','');
INSERT INTO t2 VALUES (620,178007,40,'beguiles','Goldstine','exhausts','');
INSERT INTO t2 VALUES (621,178008,40,'Trobriand','fitting','causality','');
INSERT INTO t2 VALUES (622,178101,40,'fleeing','Norwalk','sating','FAS');
INSERT INTO t2 VALUES (623,178102,40,'Armour','weakening','icon','');
INSERT INTO t2 VALUES (624,178103,40,'chin','analogy','throttles','');
INSERT INTO t2 VALUES (625,178201,40,'provers','deludes','communicants','FAS');
INSERT INTO t2 VALUES (626,178202,40,'aeronautic','cokes','dehydrate','FAS');
INSERT INTO t2 VALUES (627,178301,40,'voltage','Clayton','priceless','FAS');
INSERT INTO t2 VALUES (628,178302,40,'sash','exhausts','publicly','');
INSERT INTO t2 VALUES (629,178401,40,'anaerobic','causality','incidentals','FAS');
INSERT INTO t2 VALUES (630,178402,40,'simultaneous','sating','commonplace','');
INSERT INTO t2 VALUES (631,178403,40,'accumulating','icon','mumbles','');
INSERT INTO t2 VALUES (632,178404,40,'Medusan','throttles','furthermore','W');
INSERT INTO t2 VALUES (633,178501,40,'shouted','communicants','cautioned','W');
INSERT INTO t2 VALUES (634,186002,37,'freakish','dehydrate','parametrized','A');
INSERT INTO t2 VALUES (635,186102,37,'index','priceless','registration','A');
INSERT INTO t2 VALUES (636,186201,40,'commercially','publicly','sadly','FAS');
INSERT INTO t2 VALUES (637,186202,40,'mistiness','incidentals','positioning','');
INSERT INTO t2 VALUES (638,186203,40,'endpoint','commonplace','babysitting','');
INSERT INTO t2 VALUES (639,186302,37,'straight','mumbles','eternal','A');
INSERT INTO t2 VALUES (640,188007,37,'flurried','furthermore','hoarder','');
INSERT INTO t2 VALUES (641,188008,37,'denotative','cautioned','congregates','');
INSERT INTO t2 VALUES (642,188009,37,'coming','parametrized','rains','');
INSERT INTO t2 VALUES (643,188010,37,'commencements','registration','workers','W');
INSERT INTO t2 VALUES (644,188011,37,'gentleman','sadly','sags','A');
INSERT INTO t2 VALUES (645,188012,37,'gifted','positioning','unplug','W');
INSERT INTO t2 VALUES (646,188013,37,'Shanghais','babysitting','garage','A');
INSERT INTO t2 VALUES (647,188014,37,'sportswriting','eternal','boulder','A');
INSERT INTO t2 VALUES (648,188015,37,'sloping','hoarder','hollowly','A');
INSERT INTO t2 VALUES (649,188016,37,'navies','congregates','specifics','');
INSERT INTO t2 VALUES (650,188017,37,'leaflet','rains','Teresa','');
INSERT INTO t2 VALUES (651,188102,37,'shooter','workers','Winsett','');
INSERT INTO t2 VALUES (652,188103,37,'Joplin','sags','convenient','A');
INSERT INTO t2 VALUES (653,188202,37,'babies','unplug','buckboards','FAS');
INSERT INTO t2 VALUES (654,188301,40,'subdivision','garage','amenities','');
INSERT INTO t2 VALUES (655,188302,40,'burstiness','boulder','resplendent','FAS');
INSERT INTO t2 VALUES (656,188303,40,'belted','hollowly','priding','FAS');
INSERT INTO t2 VALUES (657,188401,37,'assails','specifics','configurations','');
INSERT INTO t2 VALUES (658,188402,37,'admiring','Teresa','untidiness','A');
INSERT INTO t2 VALUES (659,188503,37,'swaying','Winsett','Brice','W');
INSERT INTO t2 VALUES (660,188504,37,'Goldstine','convenient','sews','FAS');
INSERT INTO t2 VALUES (661,188505,37,'fitting','buckboards','participated','');
INSERT INTO t2 VALUES (662,190701,37,'Norwalk','amenities','Simon','FAS');
INSERT INTO t2 VALUES (663,190703,50,'weakening','resplendent','certificates','');
INSERT INTO t2 VALUES (664,191701,37,'analogy','priding','Fitzpatrick','');
INSERT INTO t2 VALUES (665,191702,37,'deludes','configurations','Evanston','A');
INSERT INTO t2 VALUES (666,191703,37,'cokes','untidiness','misted','');
INSERT INTO t2 VALUES (667,196001,37,'Clayton','Brice','textures','A');
INSERT INTO t2 VALUES (668,196002,37,'exhausts','sews','save','');
INSERT INTO t2 VALUES (669,196003,37,'causality','participated','count','');
INSERT INTO t2 VALUES (670,196101,37,'sating','Simon','rightful','A');
INSERT INTO t2 VALUES (671,196103,37,'icon','certificates','chaperone','');
INSERT INTO t2 VALUES (672,196104,37,'throttles','Fitzpatrick','Lizzy','A');
INSERT INTO t2 VALUES (673,196201,37,'communicants','Evanston','clenched','A');
INSERT INTO t2 VALUES (674,196202,37,'dehydrate','misted','effortlessly','');
INSERT INTO t2 VALUES (675,196203,37,'priceless','textures','accessed','');
INSERT INTO t2 VALUES (676,198001,37,'publicly','save','beaters','A');
INSERT INTO t2 VALUES (677,198003,37,'incidentals','count','Hornblower','FAS');
INSERT INTO t2 VALUES (678,198004,37,'commonplace','rightful','vests','A');
INSERT INTO t2 VALUES (679,198005,37,'mumbles','chaperone','indulgences','FAS');
INSERT INTO t2 VALUES (680,198006,37,'furthermore','Lizzy','infallibly','A');
INSERT INTO t2 VALUES (681,198007,37,'cautioned','clenched','unwilling','FAS');
INSERT INTO t2 VALUES (682,198008,37,'parametrized','effortlessly','excrete','FAS');
INSERT INTO t2 VALUES (683,198009,37,'registration','accessed','spools','A');
INSERT INTO t2 VALUES (684,198010,37,'sadly','beaters','crunches','FAS');
INSERT INTO t2 VALUES (685,198011,37,'positioning','Hornblower','overestimating','FAS');
INSERT INTO t2 VALUES (686,198012,37,'babysitting','vests','ineffective','');
INSERT INTO t2 VALUES (687,198013,37,'eternal','indulgences','humiliation','A');
INSERT INTO t2 VALUES (688,198014,37,'hoarder','infallibly','sophomore','');
INSERT INTO t2 VALUES (689,198015,37,'congregates','unwilling','star','');
INSERT INTO t2 VALUES (690,198017,37,'rains','excrete','rifles','');
INSERT INTO t2 VALUES (691,198018,37,'workers','spools','dialysis','');
INSERT INTO t2 VALUES (692,198019,37,'sags','crunches','arriving','');
INSERT INTO t2 VALUES (693,198020,37,'unplug','overestimating','indulge','');
INSERT INTO t2 VALUES (694,198021,37,'garage','ineffective','clockers','');
INSERT INTO t2 VALUES (695,198022,37,'boulder','humiliation','languages','');
INSERT INTO t2 VALUES (696,198023,50,'hollowly','sophomore','Antarctica','A');
INSERT INTO t2 VALUES (697,198024,37,'specifics','star','percentage','');
INSERT INTO t2 VALUES (698,198101,37,'Teresa','rifles','ceiling','A');
INSERT INTO t2 VALUES (699,198103,37,'Winsett','dialysis','specification','');
INSERT INTO t2 VALUES (700,198105,37,'convenient','arriving','regimented','A');
INSERT INTO t2 VALUES (701,198106,37,'buckboards','indulge','ciphers','');
INSERT INTO t2 VALUES (702,198201,37,'amenities','clockers','pictures','A');
INSERT INTO t2 VALUES (703,198204,37,'resplendent','languages','serpents','A');
INSERT INTO t2 VALUES (704,198301,53,'priding','Antarctica','allot','A');
INSERT INTO t2 VALUES (705,198302,53,'configurations','percentage','realized','A');
INSERT INTO t2 VALUES (706,198303,53,'untidiness','ceiling','mayoral','A');
INSERT INTO t2 VALUES (707,198304,53,'Brice','specification','opaquely','A');
INSERT INTO t2 VALUES (708,198401,37,'sews','regimented','hostess','FAS');
INSERT INTO t2 VALUES (709,198402,37,'participated','ciphers','fiftieth','');
INSERT INTO t2 VALUES (710,198403,37,'Simon','pictures','incorrectly','');
INSERT INTO t2 VALUES (711,202101,37,'certificates','serpents','decomposition','FAS');
INSERT INTO t2 VALUES (712,202301,37,'Fitzpatrick','allot','stranglings','');
INSERT INTO t2 VALUES (713,202302,37,'Evanston','realized','mixture','FAS');
INSERT INTO t2 VALUES (714,202303,37,'misted','mayoral','electroencephalography','FAS');
INSERT INTO t2 VALUES (715,202304,37,'textures','opaquely','similarities','FAS');
INSERT INTO t2 VALUES (716,202305,37,'save','hostess','charges','W');
INSERT INTO t2 VALUES (717,202601,37,'count','fiftieth','freest','FAS');
INSERT INTO t2 VALUES (718,202602,37,'rightful','incorrectly','Greenberg','FAS');
INSERT INTO t2 VALUES (719,202605,37,'chaperone','decomposition','tinting','');
INSERT INTO t2 VALUES (720,202606,37,'Lizzy','stranglings','expelled','W');
INSERT INTO t2 VALUES (721,202607,37,'clenched','mixture','warm','');
INSERT INTO t2 VALUES (722,202901,37,'effortlessly','electroencephalography','smoothed','');
INSERT INTO t2 VALUES (723,202902,37,'accessed','similarities','deductions','FAS');
INSERT INTO t2 VALUES (724,202903,37,'beaters','charges','Romano','W');
INSERT INTO t2 VALUES (725,202904,37,'Hornblower','freest','bitterroot','');
INSERT INTO t2 VALUES (726,202907,37,'vests','Greenberg','corset','');
INSERT INTO t2 VALUES (727,202908,37,'indulgences','tinting','securing','');
INSERT INTO t2 VALUES (728,203101,37,'infallibly','expelled','environing','FAS');
INSERT INTO t2 VALUES (729,203103,37,'unwilling','warm','cute','');
INSERT INTO t2 VALUES (730,203104,37,'excrete','smoothed','Crays','');
INSERT INTO t2 VALUES (731,203105,37,'spools','deductions','heiress','FAS');
INSERT INTO t2 VALUES (732,203401,37,'crunches','Romano','inform','FAS');
INSERT INTO t2 VALUES (733,203402,37,'overestimating','bitterroot','avenge','');
INSERT INTO t2 VALUES (734,203404,37,'ineffective','corset','universals','');
INSERT INTO t2 VALUES (735,203901,37,'humiliation','securing','Kinsey','W');
INSERT INTO t2 VALUES (736,203902,37,'sophomore','environing','ravines','FAS');
INSERT INTO t2 VALUES (737,203903,37,'star','cute','bestseller','');
INSERT INTO t2 VALUES (738,203906,37,'rifles','Crays','equilibrium','');
INSERT INTO t2 VALUES (739,203907,37,'dialysis','heiress','extents','0');
INSERT INTO t2 VALUES (740,203908,37,'arriving','inform','relatively','');
INSERT INTO t2 VALUES (741,203909,37,'indulge','avenge','pressure','FAS');
INSERT INTO t2 VALUES (742,206101,37,'clockers','universals','critiques','FAS');
INSERT INTO t2 VALUES (743,206201,37,'languages','Kinsey','befouled','');
INSERT INTO t2 VALUES (744,206202,37,'Antarctica','ravines','rightfully','FAS');
INSERT INTO t2 VALUES (745,206203,37,'percentage','bestseller','mechanizing','FAS');
INSERT INTO t2 VALUES (746,206206,37,'ceiling','equilibrium','Latinizes','');
INSERT INTO t2 VALUES (747,206207,37,'specification','extents','timesharing','');
INSERT INTO t2 VALUES (748,206208,37,'regimented','relatively','Aden','');
INSERT INTO t2 VALUES (749,208001,37,'ciphers','pressure','embassies','');
INSERT INTO t2 VALUES (750,208002,37,'pictures','critiques','males','FAS');
INSERT INTO t2 VALUES (751,208003,37,'serpents','befouled','shapelessly','FAS');
INSERT INTO t2 VALUES (752,208004,37,'allot','rightfully','genres','FAS');
INSERT INTO t2 VALUES (753,208008,37,'realized','mechanizing','mastering','');
INSERT INTO t2 VALUES (754,208009,37,'mayoral','Latinizes','Newtonian','');
INSERT INTO t2 VALUES (755,208010,37,'opaquely','timesharing','finishers','FAS');
INSERT INTO t2 VALUES (756,208011,37,'hostess','Aden','abates','');
INSERT INTO t2 VALUES (757,208101,37,'fiftieth','embassies','teem','');
INSERT INTO t2 VALUES (758,208102,37,'incorrectly','males','kiting','FAS');
INSERT INTO t2 VALUES (759,208103,37,'decomposition','shapelessly','stodgy','FAS');
INSERT INTO t2 VALUES (760,208104,37,'stranglings','genres','scalps','FAS');
INSERT INTO t2 VALUES (761,208105,37,'mixture','mastering','feed','FAS');
INSERT INTO t2 VALUES (762,208110,37,'electroencephalography','Newtonian','guitars','');
INSERT INTO t2 VALUES (763,208111,37,'similarities','finishers','airships','');
INSERT INTO t2 VALUES (764,208112,37,'charges','abates','store','');
INSERT INTO t2 VALUES (765,208113,37,'freest','teem','denounces','');
INSERT INTO t2 VALUES (766,208201,37,'Greenberg','kiting','Pyle','FAS');
INSERT INTO t2 VALUES (767,208203,37,'tinting','stodgy','Saxony','');
INSERT INTO t2 VALUES (768,208301,37,'expelled','scalps','serializations','FAS');
INSERT INTO t2 VALUES (769,208302,37,'warm','feed','Peruvian','FAS');
INSERT INTO t2 VALUES (770,208305,37,'smoothed','guitars','taxonomically','FAS');
INSERT INTO t2 VALUES (771,208401,37,'deductions','airships','kingdom','A');
INSERT INTO t2 VALUES (772,208402,37,'Romano','store','stint','A');
INSERT INTO t2 VALUES (773,208403,37,'bitterroot','denounces','Sault','A');
INSERT INTO t2 VALUES (774,208404,37,'corset','Pyle','faithful','');
INSERT INTO t2 VALUES (775,208501,37,'securing','Saxony','Ganymede','FAS');
INSERT INTO t2 VALUES (776,208502,37,'environing','serializations','tidiness','FAS');
INSERT INTO t2 VALUES (777,208503,37,'cute','Peruvian','gainful','FAS');
INSERT INTO t2 VALUES (778,208504,37,'Crays','taxonomically','contrary','FAS');
INSERT INTO t2 VALUES (779,208505,37,'heiress','kingdom','Tipperary','FAS');
INSERT INTO t2 VALUES (780,210101,37,'inform','stint','tropics','W');
INSERT INTO t2 VALUES (781,210102,37,'avenge','Sault','theorizers','');
INSERT INTO t2 VALUES (782,210103,37,'universals','faithful','renew','0');
INSERT INTO t2 VALUES (783,210104,37,'Kinsey','Ganymede','already','');
INSERT INTO t2 VALUES (784,210105,37,'ravines','tidiness','terminal','');
INSERT INTO t2 VALUES (785,210106,37,'bestseller','gainful','Hegelian','');
INSERT INTO t2 VALUES (786,210107,37,'equilibrium','contrary','hypothesizer','');
INSERT INTO t2 VALUES (787,210401,37,'extents','Tipperary','warningly','FAS');
INSERT INTO t2 VALUES (788,213201,37,'relatively','tropics','journalizing','FAS');
INSERT INTO t2 VALUES (789,213203,37,'pressure','theorizers','nested','');
INSERT INTO t2 VALUES (790,213204,37,'critiques','renew','Lars','');
INSERT INTO t2 VALUES (791,213205,37,'befouled','already','saplings','');
INSERT INTO t2 VALUES (792,213206,37,'rightfully','terminal','foothill','');
INSERT INTO t2 VALUES (793,213207,37,'mechanizing','Hegelian','labeled','');
INSERT INTO t2 VALUES (794,216101,37,'Latinizes','hypothesizer','imperiously','FAS');
INSERT INTO t2 VALUES (795,216103,37,'timesharing','warningly','reporters','FAS');
INSERT INTO t2 VALUES (796,218001,37,'Aden','journalizing','furnishings','FAS');
INSERT INTO t2 VALUES (797,218002,37,'embassies','nested','precipitable','FAS');
INSERT INTO t2 VALUES (798,218003,37,'males','Lars','discounts','FAS');
INSERT INTO t2 VALUES (799,218004,37,'shapelessly','saplings','excises','FAS');
INSERT INTO t2 VALUES (800,143503,50,'genres','foothill','Stalin','');
INSERT INTO t2 VALUES (801,218006,37,'mastering','labeled','despot','FAS');
INSERT INTO t2 VALUES (802,218007,37,'Newtonian','imperiously','ripeness','FAS');
INSERT INTO t2 VALUES (803,218008,37,'finishers','reporters','Arabia','');
INSERT INTO t2 VALUES (804,218009,37,'abates','furnishings','unruly','');
INSERT INTO t2 VALUES (805,218010,37,'teem','precipitable','mournfulness','');
INSERT INTO t2 VALUES (806,218011,37,'kiting','discounts','boom','FAS');
INSERT INTO t2 VALUES (807,218020,37,'stodgy','excises','slaughter','A');
INSERT INTO t2 VALUES (808,218021,50,'scalps','Stalin','Sabine','');
INSERT INTO t2 VALUES (809,218022,37,'feed','despot','handy','FAS');
INSERT INTO t2 VALUES (810,218023,37,'guitars','ripeness','rural','');
INSERT INTO t2 VALUES (811,218024,37,'airships','Arabia','organizer','');
INSERT INTO t2 VALUES (812,218101,37,'store','unruly','shipyard','FAS');
INSERT INTO t2 VALUES (813,218102,37,'denounces','mournfulness','civics','FAS');
INSERT INTO t2 VALUES (814,218103,37,'Pyle','boom','inaccuracy','FAS');
INSERT INTO t2 VALUES (815,218201,37,'Saxony','slaughter','rules','FAS');
INSERT INTO t2 VALUES (816,218202,37,'serializations','Sabine','juveniles','FAS');
INSERT INTO t2 VALUES (817,218203,37,'Peruvian','handy','comprised','W');
INSERT INTO t2 VALUES (818,218204,37,'taxonomically','rural','investigations','');
INSERT INTO t2 VALUES (819,218205,37,'kingdom','organizer','stabilizes','A');
INSERT INTO t2 VALUES (820,218301,37,'stint','shipyard','seminaries','FAS');
INSERT INTO t2 VALUES (821,218302,37,'Sault','civics','Hunter','A');
INSERT INTO t2 VALUES (822,218401,37,'faithful','inaccuracy','sporty','FAS');
INSERT INTO t2 VALUES (823,218402,37,'Ganymede','rules','test','FAS');
INSERT INTO t2 VALUES (824,218403,37,'tidiness','juveniles','weasels','');
INSERT INTO t2 VALUES (825,218404,37,'gainful','comprised','CERN','');
INSERT INTO t2 VALUES (826,218407,37,'contrary','investigations','tempering','');
INSERT INTO t2 VALUES (827,218408,37,'Tipperary','stabilizes','afore','FAS');
INSERT INTO t2 VALUES (828,218409,37,'tropics','seminaries','Galatean','');
INSERT INTO t2 VALUES (829,218410,37,'theorizers','Hunter','techniques','W');
INSERT INTO t2 VALUES (830,226001,37,'renew','sporty','error','');
INSERT INTO t2 VALUES (831,226002,37,'already','test','veranda','');
INSERT INTO t2 VALUES (832,226003,37,'terminal','weasels','severely','');
INSERT INTO t2 VALUES (833,226004,37,'Hegelian','CERN','Cassites','FAS');
INSERT INTO t2 VALUES (834,226005,37,'hypothesizer','tempering','forthcoming','');
INSERT INTO t2 VALUES (835,226006,37,'warningly','afore','guides','');
INSERT INTO t2 VALUES (836,226007,37,'journalizing','Galatean','vanish','FAS');
INSERT INTO t2 VALUES (837,226008,37,'nested','techniques','lied','A');
INSERT INTO t2 VALUES (838,226203,37,'Lars','error','sawtooth','FAS');
INSERT INTO t2 VALUES (839,226204,37,'saplings','veranda','fated','FAS');
INSERT INTO t2 VALUES (840,226205,37,'foothill','severely','gradually','');
INSERT INTO t2 VALUES (841,226206,37,'labeled','Cassites','widens','');
INSERT INTO t2 VALUES (842,226207,37,'imperiously','forthcoming','preclude','');
INSERT INTO t2 VALUES (843,226208,37,'reporters','guides','Jobrel','');
INSERT INTO t2 VALUES (844,226209,37,'furnishings','vanish','hooker','');
INSERT INTO t2 VALUES (845,226210,37,'precipitable','lied','rainstorm','');
INSERT INTO t2 VALUES (846,226211,37,'discounts','sawtooth','disconnects','');
INSERT INTO t2 VALUES (847,228001,37,'excises','fated','cruelty','');
INSERT INTO t2 VALUES (848,228004,37,'Stalin','gradually','exponentials','A');
INSERT INTO t2 VALUES (849,228005,37,'despot','widens','affective','A');
INSERT INTO t2 VALUES (850,228006,37,'ripeness','preclude','arteries','');
INSERT INTO t2 VALUES (851,228007,37,'Arabia','Jobrel','Crosby','FAS');
INSERT INTO t2 VALUES (852,228008,37,'unruly','hooker','acquaint','');
INSERT INTO t2 VALUES (853,228009,37,'mournfulness','rainstorm','evenhandedly','');
INSERT INTO t2 VALUES (854,228101,37,'boom','disconnects','percentage','');
INSERT INTO t2 VALUES (855,228108,37,'slaughter','cruelty','disobedience','');
INSERT INTO t2 VALUES (856,228109,37,'Sabine','exponentials','humility','');
INSERT INTO t2 VALUES (857,228110,37,'handy','affective','gleaning','A');
INSERT INTO t2 VALUES (858,228111,37,'rural','arteries','petted','A');
INSERT INTO t2 VALUES (859,228112,37,'organizer','Crosby','bloater','A');
INSERT INTO t2 VALUES (860,228113,37,'shipyard','acquaint','minion','A');
INSERT INTO t2 VALUES (861,228114,37,'civics','evenhandedly','marginal','A');
INSERT INTO t2 VALUES (862,228115,37,'inaccuracy','percentage','apiary','A');
INSERT INTO t2 VALUES (863,228116,37,'rules','disobedience','measures','');
INSERT INTO t2 VALUES (864,228117,37,'juveniles','humility','precaution','');
INSERT INTO t2 VALUES (865,228118,37,'comprised','gleaning','repelled','');
INSERT INTO t2 VALUES (866,228119,37,'investigations','petted','primary','FAS');
INSERT INTO t2 VALUES (867,228120,37,'stabilizes','bloater','coverings','');
INSERT INTO t2 VALUES (868,228121,37,'seminaries','minion','Artemia','A');
INSERT INTO t2 VALUES (869,228122,37,'Hunter','marginal','navigate','');
INSERT INTO t2 VALUES (870,228201,37,'sporty','apiary','spatial','');
INSERT INTO t2 VALUES (871,228206,37,'test','measures','Gurkha','');
INSERT INTO t2 VALUES (872,228207,37,'weasels','precaution','meanwhile','A');
INSERT INTO t2 VALUES (873,228208,37,'CERN','repelled','Melinda','A');
INSERT INTO t2 VALUES (874,228209,37,'tempering','primary','Butterfield','');
INSERT INTO t2 VALUES (875,228210,37,'afore','coverings','Aldrich','A');
INSERT INTO t2 VALUES (876,228211,37,'Galatean','Artemia','previewing','A');
INSERT INTO t2 VALUES (877,228212,37,'techniques','navigate','glut','A');
INSERT INTO t2 VALUES (878,228213,37,'error','spatial','unaffected','');
INSERT INTO t2 VALUES (879,228214,37,'veranda','Gurkha','inmate','');
INSERT INTO t2 VALUES (880,228301,37,'severely','meanwhile','mineral','');
INSERT INTO t2 VALUES (881,228305,37,'Cassites','Melinda','impending','A');
INSERT INTO t2 VALUES (882,228306,37,'forthcoming','Butterfield','meditation','A');
INSERT INTO t2 VALUES (883,228307,37,'guides','Aldrich','ideas','');
INSERT INTO t2 VALUES (884,228308,37,'vanish','previewing','miniaturizes','W');
INSERT INTO t2 VALUES (885,228309,37,'lied','glut','lewdly','');
INSERT INTO t2 VALUES (886,228310,37,'sawtooth','unaffected','title','');
INSERT INTO t2 VALUES (887,228311,37,'fated','inmate','youthfulness','');
INSERT INTO t2 VALUES (888,228312,37,'gradually','mineral','creak','FAS');
INSERT INTO t2 VALUES (889,228313,37,'widens','impending','Chippewa','');
INSERT INTO t2 VALUES (890,228314,37,'preclude','meditation','clamored','');
INSERT INTO t2 VALUES (891,228401,65,'Jobrel','ideas','freezes','');
INSERT INTO t2 VALUES (892,228402,65,'hooker','miniaturizes','forgivably','FAS');
INSERT INTO t2 VALUES (893,228403,65,'rainstorm','lewdly','reduce','FAS');
INSERT INTO t2 VALUES (894,228404,65,'disconnects','title','McGovern','W');
INSERT INTO t2 VALUES (895,228405,65,'cruelty','youthfulness','Nazis','W');
INSERT INTO t2 VALUES (896,228406,65,'exponentials','creak','epistle','W');
INSERT INTO t2 VALUES (897,228407,65,'affective','Chippewa','socializes','W');
INSERT INTO t2 VALUES (898,228408,65,'arteries','clamored','conceptions','');
INSERT INTO t2 VALUES (899,228409,65,'Crosby','freezes','Kevin','');
INSERT INTO t2 VALUES (900,228410,65,'acquaint','forgivably','uncovering','');
INSERT INTO t2 VALUES (901,230301,37,'evenhandedly','reduce','chews','FAS');
INSERT INTO t2 VALUES (902,230302,37,'percentage','McGovern','appendixes','FAS');
INSERT INTO t2 VALUES (903,230303,37,'disobedience','Nazis','raining','');
INSERT INTO t2 VALUES (904,018062,37,'humility','epistle','infest','');
INSERT INTO t2 VALUES (905,230501,37,'gleaning','socializes','compartment','');
INSERT INTO t2 VALUES (906,230502,37,'petted','conceptions','minting','');
INSERT INTO t2 VALUES (907,230503,37,'bloater','Kevin','ducks','');
INSERT INTO t2 VALUES (908,230504,37,'minion','uncovering','roped','A');
INSERT INTO t2 VALUES (909,230505,37,'marginal','chews','waltz','');
INSERT INTO t2 VALUES (910,230506,37,'apiary','appendixes','Lillian','');
INSERT INTO t2 VALUES (911,230507,37,'measures','raining','repressions','A');
INSERT INTO t2 VALUES (912,230508,37,'precaution','infest','chillingly','');
INSERT INTO t2 VALUES (913,230509,37,'repelled','compartment','noncritical','');
INSERT INTO t2 VALUES (914,230901,37,'primary','minting','lithograph','');
INSERT INTO t2 VALUES (915,230902,37,'coverings','ducks','spongers','');
INSERT INTO t2 VALUES (916,230903,37,'Artemia','roped','parenthood','');
INSERT INTO t2 VALUES (917,230904,37,'navigate','waltz','posed','');
INSERT INTO t2 VALUES (918,230905,37,'spatial','Lillian','instruments','');
INSERT INTO t2 VALUES (919,230906,37,'Gurkha','repressions','filial','');
INSERT INTO t2 VALUES (920,230907,37,'meanwhile','chillingly','fixedly','');
INSERT INTO t2 VALUES (921,230908,37,'Melinda','noncritical','relives','');
INSERT INTO t2 VALUES (922,230909,37,'Butterfield','lithograph','Pandora','');
INSERT INTO t2 VALUES (923,230910,37,'Aldrich','spongers','watering','A');
INSERT INTO t2 VALUES (924,230911,37,'previewing','parenthood','ungrateful','');
INSERT INTO t2 VALUES (925,230912,37,'glut','posed','secures','');
INSERT INTO t2 VALUES (926,230913,37,'unaffected','instruments','chastisers','');
INSERT INTO t2 VALUES (927,230914,37,'inmate','filial','icon','');
INSERT INTO t2 VALUES (928,231304,37,'mineral','fixedly','reuniting','A');
INSERT INTO t2 VALUES (929,231305,37,'impending','relives','imagining','A');
INSERT INTO t2 VALUES (930,231306,37,'meditation','Pandora','abiding','A');
INSERT INTO t2 VALUES (931,231307,37,'ideas','watering','omnisciently','');
INSERT INTO t2 VALUES (932,231308,37,'miniaturizes','ungrateful','Britannic','');
INSERT INTO t2 VALUES (933,231309,37,'lewdly','secures','scholastics','A');
INSERT INTO t2 VALUES (934,231310,37,'title','chastisers','mechanics','A');
INSERT INTO t2 VALUES (935,231311,37,'youthfulness','icon','humidly','A');
INSERT INTO t2 VALUES (936,231312,37,'creak','reuniting','masterpiece','');
INSERT INTO t2 VALUES (937,231313,37,'Chippewa','imagining','however','');
INSERT INTO t2 VALUES (938,231314,37,'clamored','abiding','Mendelian','');
INSERT INTO t2 VALUES (939,231315,37,'freezes','omnisciently','jarred','');
INSERT INTO t2 VALUES (940,232102,37,'forgivably','Britannic','scolds','');
INSERT INTO t2 VALUES (941,232103,37,'reduce','scholastics','infatuate','');
INSERT INTO t2 VALUES (942,232104,37,'McGovern','mechanics','willed','A');
INSERT INTO t2 VALUES (943,232105,37,'Nazis','humidly','joyfully','');
INSERT INTO t2 VALUES (944,232106,37,'epistle','masterpiece','Microsoft','');
INSERT INTO t2 VALUES (945,232107,37,'socializes','however','fibrosities','');
INSERT INTO t2 VALUES (946,232108,37,'conceptions','Mendelian','Baltimorean','');
INSERT INTO t2 VALUES (947,232601,37,'Kevin','jarred','equestrian','');
INSERT INTO t2 VALUES (948,232602,37,'uncovering','scolds','Goodrich','');
INSERT INTO t2 VALUES (949,232603,37,'chews','infatuate','apish','A');
INSERT INTO t2 VALUES (950,232605,37,'appendixes','willed','Adlerian','');
INSERT INTO t2 VALUES (5950,1232605,37,'appendixes','willed','Adlerian','');
INSERT INTO t2 VALUES (5951,1232606,37,'appendixes','willed','Adlerian','');
INSERT INTO t2 VALUES (5952,1232607,37,'appendixes','willed','Adlerian','');
INSERT INTO t2 VALUES (5953,1232608,37,'appendixes','willed','Adlerian','');
INSERT INTO t2 VALUES (5954,1232609,37,'appendixes','willed','Adlerian','');
INSERT INTO t2 VALUES (951,232606,37,'raining','joyfully','Tropez','');
INSERT INTO t2 VALUES (952,232607,37,'infest','Microsoft','nouns','');
INSERT INTO t2 VALUES (953,232608,37,'compartment','fibrosities','distracting','');
INSERT INTO t2 VALUES (954,232609,37,'minting','Baltimorean','mutton','');
INSERT INTO t2 VALUES (955,236104,37,'ducks','equestrian','bridgeable','A');
INSERT INTO t2 VALUES (956,236105,37,'roped','Goodrich','stickers','A');
INSERT INTO t2 VALUES (957,236106,37,'waltz','apish','transcontinental','A');
INSERT INTO t2 VALUES (958,236107,37,'Lillian','Adlerian','amateurish','');
INSERT INTO t2 VALUES (959,236108,37,'repressions','Tropez','Gandhian','');
INSERT INTO t2 VALUES (960,236109,37,'chillingly','nouns','stratified','');
INSERT INTO t2 VALUES (961,236110,37,'noncritical','distracting','chamberlains','');
INSERT INTO t2 VALUES (962,236111,37,'lithograph','mutton','creditably','');
INSERT INTO t2 VALUES (963,236112,37,'spongers','bridgeable','philosophic','');
INSERT INTO t2 VALUES (964,236113,37,'parenthood','stickers','ores','');
INSERT INTO t2 VALUES (965,238005,37,'posed','transcontinental','Carleton','');
INSERT INTO t2 VALUES (966,238006,37,'instruments','amateurish','tape','A');
INSERT INTO t2 VALUES (967,238007,37,'filial','Gandhian','afloat','A');
INSERT INTO t2 VALUES (968,238008,37,'fixedly','stratified','goodness','A');
INSERT INTO t2 VALUES (969,238009,37,'relives','chamberlains','welcoming','');
INSERT INTO t2 VALUES (970,238010,37,'Pandora','creditably','Pinsky','FAS');
INSERT INTO t2 VALUES (971,238011,37,'watering','philosophic','halting','');
INSERT INTO t2 VALUES (972,238012,37,'ungrateful','ores','bibliography','');
INSERT INTO t2 VALUES (973,238013,37,'secures','Carleton','decoding','');
INSERT INTO t2 VALUES (974,240401,41,'chastisers','tape','variance','A');
INSERT INTO t2 VALUES (975,240402,41,'icon','afloat','allowed','A');
INSERT INTO t2 VALUES (976,240901,41,'reuniting','goodness','dire','A');
INSERT INTO t2 VALUES (977,240902,41,'imagining','welcoming','dub','A');
INSERT INTO t2 VALUES (978,241801,41,'abiding','Pinsky','poisoning','');
INSERT INTO t2 VALUES (979,242101,41,'omnisciently','halting','Iraqis','A');
INSERT INTO t2 VALUES (980,242102,41,'Britannic','bibliography','heaving','');
INSERT INTO t2 VALUES (981,242201,41,'scholastics','decoding','population','A');
INSERT INTO t2 VALUES (982,242202,41,'mechanics','variance','bomb','A');
INSERT INTO t2 VALUES (983,242501,41,'humidly','allowed','Majorca','A');
INSERT INTO t2 VALUES (984,242502,41,'masterpiece','dire','Gershwins','');
INSERT INTO t2 VALUES (985,246201,41,'however','dub','explorers','');
INSERT INTO t2 VALUES (986,246202,41,'Mendelian','poisoning','libretto','A');
INSERT INTO t2 VALUES (987,246203,41,'jarred','Iraqis','occurred','');
INSERT INTO t2 VALUES (988,246204,41,'scolds','heaving','Lagos','');
INSERT INTO t2 VALUES (989,246205,41,'infatuate','population','rats','');
INSERT INTO t2 VALUES (990,246301,41,'willed','bomb','bankruptcies','A');
INSERT INTO t2 VALUES (991,246302,41,'joyfully','Majorca','crying','');
INSERT INTO t2 VALUES (992,248001,41,'Microsoft','Gershwins','unexpected','');
INSERT INTO t2 VALUES (993,248002,41,'fibrosities','explorers','accessed','A');
INSERT INTO t2 VALUES (994,248003,41,'Baltimorean','libretto','colorful','A');
INSERT INTO t2 VALUES (995,248004,41,'equestrian','occurred','versatility','A');
INSERT INTO t2 VALUES (996,248005,41,'Goodrich','Lagos','cosy','');
INSERT INTO t2 VALUES (997,248006,41,'apish','rats','Darius','A');
INSERT INTO t2 VALUES (998,248007,41,'Adlerian','bankruptcies','mastering','A');
INSERT INTO t2 VALUES (999,248008,41,'Tropez','crying','Asiaticizations','A');
INSERT INTO t2 VALUES (1000,248009,41,'nouns','unexpected','offerers','A');
INSERT INTO t2 VALUES (1001,248010,41,'distracting','accessed','uncles','A');
INSERT INTO t2 VALUES (1002,248011,41,'mutton','colorful','sleepwalk','');
INSERT INTO t2 VALUES (1003,248012,41,'bridgeable','versatility','Ernestine','');
INSERT INTO t2 VALUES (1004,248013,41,'stickers','cosy','checksumming','');
INSERT INTO t2 VALUES (1005,248014,41,'transcontinental','Darius','stopped','');
INSERT INTO t2 VALUES (1006,248015,41,'amateurish','mastering','sicker','');
INSERT INTO t2 VALUES (1007,248016,41,'Gandhian','Asiaticizations','Italianization','');
INSERT INTO t2 VALUES (1008,248017,41,'stratified','offerers','alphabetic','');
INSERT INTO t2 VALUES (1009,248018,41,'chamberlains','uncles','pharmaceutic','');
INSERT INTO t2 VALUES (1010,248019,41,'creditably','sleepwalk','creator','');
INSERT INTO t2 VALUES (1011,248020,41,'philosophic','Ernestine','chess','');
INSERT INTO t2 VALUES (1012,248021,41,'ores','checksumming','charcoal','');
INSERT INTO t2 VALUES (1013,248101,41,'Carleton','stopped','Epiphany','A');
INSERT INTO t2 VALUES (1014,248102,41,'tape','sicker','bulldozes','A');
INSERT INTO t2 VALUES (1015,248201,41,'afloat','Italianization','Pygmalion','A');
INSERT INTO t2 VALUES (1016,248202,41,'goodness','alphabetic','caressing','A');
INSERT INTO t2 VALUES (1017,248203,41,'welcoming','pharmaceutic','Palestine','A');
INSERT INTO t2 VALUES (1018,248204,41,'Pinsky','creator','regimented','A');
INSERT INTO t2 VALUES (1019,248205,41,'halting','chess','scars','A');
INSERT INTO t2 VALUES (1020,248206,41,'bibliography','charcoal','realest','A');
INSERT INTO t2 VALUES (1021,248207,41,'decoding','Epiphany','diffusing','A');
INSERT INTO t2 VALUES (1022,248208,41,'variance','bulldozes','clubroom','A');
INSERT INTO t2 VALUES (1023,248209,41,'allowed','Pygmalion','Blythe','A');
INSERT INTO t2 VALUES (1024,248210,41,'dire','caressing','ahead','');
INSERT INTO t2 VALUES (1025,248211,50,'dub','Palestine','reviver','');
INSERT INTO t2 VALUES (1026,250501,34,'poisoning','regimented','retransmitting','A');
INSERT INTO t2 VALUES (1027,250502,34,'Iraqis','scars','landslide','');
INSERT INTO t2 VALUES (1028,250503,34,'heaving','realest','Eiffel','');
INSERT INTO t2 VALUES (1029,250504,34,'population','diffusing','absentee','');
INSERT INTO t2 VALUES (1030,250505,34,'bomb','clubroom','aye','');
INSERT INTO t2 VALUES (1031,250601,34,'Majorca','Blythe','forked','A');
INSERT INTO t2 VALUES (1032,250602,34,'Gershwins','ahead','Peruvianizes','');
INSERT INTO t2 VALUES (1033,250603,34,'explorers','reviver','clerked','');
INSERT INTO t2 VALUES (1034,250604,34,'libretto','retransmitting','tutor','');
INSERT INTO t2 VALUES (1035,250605,34,'occurred','landslide','boulevard','');
INSERT INTO t2 VALUES (1036,251001,34,'Lagos','Eiffel','shuttered','');
INSERT INTO t2 VALUES (1037,251002,34,'rats','absentee','quotes','A');
INSERT INTO t2 VALUES (1038,251003,34,'bankruptcies','aye','Caltech','');
INSERT INTO t2 VALUES (1039,251004,34,'crying','forked','Mossberg','');
INSERT INTO t2 VALUES (1040,251005,34,'unexpected','Peruvianizes','kept','');
INSERT INTO t2 VALUES (1041,251301,34,'accessed','clerked','roundly','');
INSERT INTO t2 VALUES (1042,251302,34,'colorful','tutor','features','A');
INSERT INTO t2 VALUES (1043,251303,34,'versatility','boulevard','imaginable','A');
INSERT INTO t2 VALUES (1044,251304,34,'cosy','shuttered','controller','');
INSERT INTO t2 VALUES (1045,251305,34,'Darius','quotes','racial','');
INSERT INTO t2 VALUES (1046,251401,34,'mastering','Caltech','uprisings','A');
INSERT INTO t2 VALUES (1047,251402,34,'Asiaticizations','Mossberg','narrowed','A');
INSERT INTO t2 VALUES (1048,251403,34,'offerers','kept','cannot','A');
INSERT INTO t2 VALUES (1049,251404,34,'uncles','roundly','vest','');
INSERT INTO t2 VALUES (1050,251405,34,'sleepwalk','features','famine','');
INSERT INTO t2 VALUES (1051,251406,34,'Ernestine','imaginable','sugars','');
INSERT INTO t2 VALUES (1052,251801,34,'checksumming','controller','exterminated','A');
INSERT INTO t2 VALUES (1053,251802,34,'stopped','racial','belays','');
INSERT INTO t2 VALUES (1054,252101,34,'sicker','uprisings','Hodges','A');
INSERT INTO t2 VALUES (1055,252102,34,'Italianization','narrowed','translatable','');
INSERT INTO t2 VALUES (1056,252301,34,'alphabetic','cannot','duality','A');
INSERT INTO t2 VALUES (1057,252302,34,'pharmaceutic','vest','recording','A');
INSERT INTO t2 VALUES (1058,252303,34,'creator','famine','rouses','A');
INSERT INTO t2 VALUES (1059,252304,34,'chess','sugars','poison','');
INSERT INTO t2 VALUES (1060,252305,34,'charcoal','exterminated','attitude','');
INSERT INTO t2 VALUES (1061,252306,34,'Epiphany','belays','dusted','');
INSERT INTO t2 VALUES (1062,252307,34,'bulldozes','Hodges','encompasses','');
INSERT INTO t2 VALUES (1063,252308,34,'Pygmalion','translatable','presentation','');
INSERT INTO t2 VALUES (1064,252309,34,'caressing','duality','Kantian','');
INSERT INTO t2 VALUES (1065,256001,34,'Palestine','recording','imprecision','A');
INSERT INTO t2 VALUES (1066,256002,34,'regimented','rouses','saving','');
INSERT INTO t2 VALUES (1067,256003,34,'scars','poison','maternal','');
INSERT INTO t2 VALUES (1068,256004,34,'realest','attitude','hewed','');
INSERT INTO t2 VALUES (1069,256005,34,'diffusing','dusted','kerosene','');
INSERT INTO t2 VALUES (1070,258001,34,'clubroom','encompasses','Cubans','');
INSERT INTO t2 VALUES (1071,258002,34,'Blythe','presentation','photographers','');
INSERT INTO t2 VALUES (1072,258003,34,'ahead','Kantian','nymph','A');
INSERT INTO t2 VALUES (1073,258004,34,'reviver','imprecision','bedlam','A');
INSERT INTO t2 VALUES (1074,258005,34,'retransmitting','saving','north','A');
INSERT INTO t2 VALUES (1075,258006,34,'landslide','maternal','Schoenberg','A');
INSERT INTO t2 VALUES (1076,258007,34,'Eiffel','hewed','botany','A');
INSERT INTO t2 VALUES (1077,258008,34,'absentee','kerosene','curs','');
INSERT INTO t2 VALUES (1078,258009,34,'aye','Cubans','solidification','');
INSERT INTO t2 VALUES (1079,258010,34,'forked','photographers','inheritresses','');
INSERT INTO t2 VALUES (1080,258011,34,'Peruvianizes','nymph','stiller','');
INSERT INTO t2 VALUES (1081,258101,68,'clerked','bedlam','t1','A');
INSERT INTO t2 VALUES (1082,258102,68,'tutor','north','suite','A');
INSERT INTO t2 VALUES (1083,258103,34,'boulevard','Schoenberg','ransomer','');
INSERT INTO t2 VALUES (1084,258104,68,'shuttered','botany','Willy','');
INSERT INTO t2 VALUES (1085,258105,68,'quotes','curs','Rena','A');
INSERT INTO t2 VALUES (1086,258106,68,'Caltech','solidification','Seattle','A');
INSERT INTO t2 VALUES (1087,258107,68,'Mossberg','inheritresses','relaxes','A');
INSERT INTO t2 VALUES (1088,258108,68,'kept','stiller','exclaim','');
INSERT INTO t2 VALUES (1089,258109,68,'roundly','t1','implicated','A');
INSERT INTO t2 VALUES (1090,258110,68,'features','suite','distinguish','');
INSERT INTO t2 VALUES (1091,258111,68,'imaginable','ransomer','assayed','');
INSERT INTO t2 VALUES (1092,258112,68,'controller','Willy','homeowner','');
INSERT INTO t2 VALUES (1093,258113,68,'racial','Rena','and','');
INSERT INTO t2 VALUES (1094,258201,34,'uprisings','Seattle','stealth','');
INSERT INTO t2 VALUES (1095,258202,34,'narrowed','relaxes','coinciding','A');
INSERT INTO t2 VALUES (1096,258203,34,'cannot','exclaim','founder','A');
INSERT INTO t2 VALUES (1097,258204,34,'vest','implicated','environing','');
INSERT INTO t2 VALUES (1098,258205,34,'famine','distinguish','jewelry','');
INSERT INTO t2 VALUES (1099,258301,34,'sugars','assayed','lemons','A');
INSERT INTO t2 VALUES (1100,258401,34,'exterminated','homeowner','brokenness','A');
INSERT INTO t2 VALUES (1101,258402,34,'belays','and','bedpost','A');
INSERT INTO t2 VALUES (1102,258403,34,'Hodges','stealth','assurers','A');
INSERT INTO t2 VALUES (1103,258404,34,'translatable','coinciding','annoyers','');
INSERT INTO t2 VALUES (1104,258405,34,'duality','founder','affixed','');
INSERT INTO t2 VALUES (1105,258406,34,'recording','environing','warbling','');
INSERT INTO t2 VALUES (1106,258407,34,'rouses','jewelry','seriously','');
INSERT INTO t2 VALUES (1107,228123,37,'poison','lemons','boasted','');
INSERT INTO t2 VALUES (1108,250606,34,'attitude','brokenness','Chantilly','');
INSERT INTO t2 VALUES (1109,208405,37,'dusted','bedpost','Iranizes','');
INSERT INTO t2 VALUES (1110,212101,37,'encompasses','assurers','violinist','');
INSERT INTO t2 VALUES (1111,218206,37,'presentation','annoyers','extramarital','');
INSERT INTO t2 VALUES (1112,150401,37,'Kantian','affixed','spates','');
INSERT INTO t2 VALUES (1113,248212,41,'imprecision','warbling','cloakroom','');
INSERT INTO t2 VALUES (1114,128026,00,'saving','seriously','gazer','');
INSERT INTO t2 VALUES (1115,128024,00,'maternal','boasted','hand','');
INSERT INTO t2 VALUES (1116,128027,00,'hewed','Chantilly','tucked','');
INSERT INTO t2 VALUES (1117,128025,00,'kerosene','Iranizes','gems','');
INSERT INTO t2 VALUES (1118,128109,00,'Cubans','violinist','clinker','');
INSERT INTO t2 VALUES (1119,128705,00,'photographers','extramarital','refiner','');
INSERT INTO t2 VALUES (1120,126303,00,'nymph','spates','callus','');
INSERT INTO t2 VALUES (1121,128308,00,'bedlam','cloakroom','leopards','');
INSERT INTO t2 VALUES (1122,128204,00,'north','gazer','comfortingly','');
INSERT INTO t2 VALUES (1123,128205,00,'Schoenberg','hand','generically','');
INSERT INTO t2 VALUES (1124,128206,00,'botany','tucked','getters','');
INSERT INTO t2 VALUES (1125,128207,00,'curs','gems','sexually','');
INSERT INTO t2 VALUES (1126,118205,00,'solidification','clinker','spear','');
INSERT INTO t2 VALUES (1127,116801,00,'inheritresses','refiner','serums','');
INSERT INTO t2 VALUES (1128,116803,00,'stiller','callus','Italianization','');
INSERT INTO t2 VALUES (1129,116804,00,'t1','leopards','attendants','');
INSERT INTO t2 VALUES (1130,116802,00,'suite','comfortingly','spies','');
INSERT INTO t2 VALUES (1131,128605,00,'ransomer','generically','Anthony','');
INSERT INTO t2 VALUES (1132,118308,00,'Willy','getters','planar','');
INSERT INTO t2 VALUES (1133,113702,00,'Rena','sexually','cupped','');
INSERT INTO t2 VALUES (1134,113703,00,'Seattle','spear','cleanser','');
INSERT INTO t2 VALUES (1135,112103,00,'relaxes','serums','commuters','');
INSERT INTO t2 VALUES (1136,118009,00,'exclaim','Italianization','honeysuckle','');
INSERT INTO t2 VALUES (5136,1118009,00,'exclaim','Italianization','honeysuckle','');
INSERT INTO t2 VALUES (1137,138011,00,'implicated','attendants','orphanage','');
INSERT INTO t2 VALUES (1138,138010,00,'distinguish','spies','skies','');
INSERT INTO t2 VALUES (1139,138012,00,'assayed','Anthony','crushers','');
INSERT INTO t2 VALUES (1140,068304,00,'homeowner','planar','Puritan','');
INSERT INTO t2 VALUES (1141,078009,00,'and','cupped','squeezer','');
INSERT INTO t2 VALUES (1142,108013,00,'stealth','cleanser','bruises','');
INSERT INTO t2 VALUES (1143,084004,00,'coinciding','commuters','bonfire','');
INSERT INTO t2 VALUES (1144,083402,00,'founder','honeysuckle','Colombo','');
INSERT INTO t2 VALUES (1145,084003,00,'environing','orphanage','nondecreasing','');
INSERT INTO t2 VALUES (1146,088504,00,'jewelry','skies','innocents','');
INSERT INTO t2 VALUES (1147,088005,00,'lemons','crushers','masked','');
INSERT INTO t2 VALUES (1148,088007,00,'brokenness','Puritan','file','');
INSERT INTO t2 VALUES (1149,088006,00,'bedpost','squeezer','brush','');
INSERT INTO t2 VALUES (1150,148025,00,'assurers','bruises','mutilate','');
INSERT INTO t2 VALUES (1151,148024,00,'annoyers','bonfire','mommy','');
INSERT INTO t2 VALUES (1152,138305,00,'affixed','Colombo','bulkheads','');
INSERT INTO t2 VALUES (1153,138306,00,'warbling','nondecreasing','undeclared','');
INSERT INTO t2 VALUES (1154,152701,00,'seriously','innocents','displacements','');
INSERT INTO t2 VALUES (1155,148505,00,'boasted','masked','nieces','');
INSERT INTO t2 VALUES (1156,158003,00,'Chantilly','file','coeducation','');
INSERT INTO t2 VALUES (1157,156201,00,'Iranizes','brush','brassy','');
INSERT INTO t2 VALUES (1158,156202,00,'violinist','mutilate','authenticator','');
INSERT INTO t2 VALUES (1159,158307,00,'extramarital','mommy','Washoe','');
INSERT INTO t2 VALUES (1160,158402,00,'spates','bulkheads','penny','');
INSERT INTO t2 VALUES (1161,158401,00,'cloakroom','undeclared','Flagler','');
INSERT INTO t2 VALUES (1162,068013,00,'gazer','displacements','stoned','');
INSERT INTO t2 VALUES (1163,068012,00,'hand','nieces','cranes','');
INSERT INTO t2 VALUES (1164,068203,00,'tucked','coeducation','masterful','');
INSERT INTO t2 VALUES (1165,088205,00,'gems','brassy','biracial','');
INSERT INTO t2 VALUES (1166,068704,00,'clinker','authenticator','steamships','');
INSERT INTO t2 VALUES (1167,068604,00,'refiner','Washoe','windmills','');
INSERT INTO t2 VALUES (1168,158502,00,'callus','penny','exploit','');
INSERT INTO t2 VALUES (1169,123103,00,'leopards','Flagler','riverfront','');
INSERT INTO t2 VALUES (1170,148026,00,'comfortingly','stoned','sisterly','');
INSERT INTO t2 VALUES (1171,123302,00,'generically','cranes','sharpshoot','');
INSERT INTO t2 VALUES (1172,076503,00,'getters','masterful','mittens','');
INSERT INTO t2 VALUES (1173,126304,00,'sexually','biracial','interdependency','');
INSERT INTO t2 VALUES (1174,068306,00,'spear','steamships','policy','');
INSERT INTO t2 VALUES (1175,143504,00,'serums','windmills','unleashing','');
INSERT INTO t2 VALUES (1176,160201,00,'Italianization','exploit','pretenders','');
INSERT INTO t2 VALUES (1177,148028,00,'attendants','riverfront','overstatements','');
INSERT INTO t2 VALUES (1178,148027,00,'spies','sisterly','birthed','');
INSERT INTO t2 VALUES (1179,143505,00,'Anthony','sharpshoot','opportunism','');
INSERT INTO t2 VALUES (1180,108014,00,'planar','mittens','showroom','');
INSERT INTO t2 VALUES (1181,076104,00,'cupped','interdependency','compromisingly','');
INSERT INTO t2 VALUES (1182,078106,00,'cleanser','policy','Medicare','');
INSERT INTO t2 VALUES (1183,126102,00,'commuters','unleashing','corresponds','');
INSERT INTO t2 VALUES (1184,128029,00,'honeysuckle','pretenders','hardware','');
INSERT INTO t2 VALUES (1185,128028,00,'orphanage','overstatements','implant','');
INSERT INTO t2 VALUES (1186,018410,00,'skies','birthed','Alicia','');
INSERT INTO t2 VALUES (1187,128110,00,'crushers','opportunism','requesting','');
INSERT INTO t2 VALUES (1188,148506,00,'Puritan','showroom','produced','');
INSERT INTO t2 VALUES (1189,123303,00,'squeezer','compromisingly','criticizes','');
INSERT INTO t2 VALUES (1190,123304,00,'bruises','Medicare','backer','');
INSERT INTO t2 VALUES (1191,068504,00,'bonfire','corresponds','positively','');
INSERT INTO t2 VALUES (1192,068305,00,'Colombo','hardware','colicky','');
INSERT INTO t2 VALUES (1193,000000,00,'nondecreasing','implant','thrillingly','');
--enable_query_log
ANALYZE TABLE t2;
#
# Search with a key
#

select t2.fld3 from t2 where companynr = 58 and fld3 like "%imaginable%";
select fld3 from t2 where fld3 like "%cultivation" ;

#
# Search with a key using sorting and limit the same time
#

select t2.fld3,companynr from t2 where companynr = 57+1 order by fld3;
select fld3,companynr from t2 where companynr = 58 order by fld3;

select fld3 from t2 order by fld3 desc limit 10;
select fld3 from t2 order by fld3 desc limit 5;
select fld3 from t2 order by fld3 desc limit 5,5;

#
# Search with a key having a constant with each unique key.
# The table is read directly with read-next on fld3
#

select t2.fld3 from t2 where fld3 = 'honeysuckle';
select t2.fld3 from t2 where fld3 LIKE 'honeysuckl_';
select t2.fld3 from t2 where fld3 LIKE 'hon_ysuckl_';
select t2.fld3 from t2 where fld3 LIKE 'honeysuckle%';
select t2.fld3 from t2 where fld3 LIKE 'h%le';

select t2.fld3 from t2 where fld3 LIKE 'honeysuckle_';
select t2.fld3 from t2 where fld3 LIKE 'don_t_find_me_please%';

#
# Test using INDEX and IGNORE INDEX
#

explain select t2.fld3 from t2 where fld3 = 'honeysuckle';

explain select fld3 from t2 ignore index (fld3) where fld3 = 'honeysuckle';
explain select fld3 from t2 use index (fld1) where fld3 = 'honeysuckle';

explain select fld3 from t2 use index (fld3) where fld3 = 'honeysuckle';
explain select fld3 from t2 use index (fld1,fld3) where fld3 = 'honeysuckle';

#
# NOTE NOTE NOTE
# The next should give an error
#

-- error 1176
explain select fld3 from t2 ignore index (fld3,not_used);
-- error 1176
explain select fld3 from t2 use index (not_used);

#
# Test sorting with a used key (there is no need for sorting)
#

select t2.fld3 from t2 where fld3 >= 'honeysuckle' and fld3 <= 'honoring' order by fld3;
explain select t2.fld3 from t2 where fld3 >= 'honeysuckle' and fld3 <= 'honoring' order by fld3;
select fld1,fld3 from t2 where fld3="Colombo" or fld3 = "nondecreasing" order by fld3;

# 
# Search with a key having a constant with many occurrences
# The table is read directly with read-next having fld3 to get the
# occurrences
#

select fld1,fld3 from t2 where companynr = 37 and fld3 = 'appendixes';

#
# Search with bunched 'or's.
# If one can limit the key to a certain interval only the possible
# alternatives will be gone through
#

select fld1 from t2 where fld1=250501 or fld1="250502";
explain select fld1 from t2 where fld1=250501 or fld1="250502"; 
select fld1 from t2 where fld1=250501 or fld1=250502 or fld1 >= 250505 and fld1 <= 250601 or fld1 between 250501 and 250502;
explain select fld1 from t2 where fld1=250501 or fld1=250502 or fld1 >= 250505 and fld1 <= 250601 or fld1 between 250501 and 250502;

#
# Search with a key with LIKE constant
# If the like starts with a certain letter key will be used.
#

--sorted_result
select fld1,fld3 from t2 where companynr = 37 and fld3 like 'f%';
select fld3 from t2 where fld3 like "L%" and fld3 = "ok";
select fld3 from t2 where (fld3 like "C%" and fld3 = "Chantilly");
select fld1,fld3 from t2 where fld1 like "25050%";
select fld1,fld3 from t2 where fld1 like "25050_";

# 
# Search using distinct. An automatic grouping will be done over all the fields,
# if only distinct is used. In any other case a temporary table will always
# be created. If only the field used for sorting is from the main register,
# it will be sorted first before the distinct table is created.
#

--sorted_result
select distinct companynr from t2;
select distinct companynr from t2 order by companynr;
select distinct companynr from t2 order by companynr desc;
select distinct t2.fld3,period from t2,t1  where companynr=37 and fld3 like "O%" order by fld3;

select distinct fld3 from t2 where companynr = 34 order by fld3;
select distinct fld3 from t2 limit 10;
select distinct fld3 from t2 having fld3 like "A%" limit 10;
--replace_result ann Ann abr Abr art Art ant Ant
select distinct substring(fld3,1,3) from t2 where fld3 like "A%";
--replace_result abr Abr
select distinct substring(fld3,1,3) as a from t2 having a like "A%" order by a limit 10;
--replace_result abr Abr
select distinct substring(fld3,1,3) from t2 where fld3 like "A%" limit 10;
--skip_if_hypergraph # LIMIT without ORDER BY is non-deterministic.
--replace_result abr Abr
select distinct substring(fld3,1,3) as a from t2 having a like "A%" limit 10;

# make a big table.

create table t3 (
 period    int not null,
 name      char(32) not null,
 companynr int not null,
 price     double(11,0),
 price2     double(11,0),
 key (period),
 key (name)
);

--disable_query_log
INSERT INTO t3 (period,name,companynr,price,price2) VALUES (1001,"Iranizes",37,5987435,234724);
INSERT INTO t3 (period,name,companynr,price,price2) VALUES (1002,"violinist",37,28357832,8723648);
INSERT INTO t3 (period,name,companynr,price,price2) VALUES (1003,"extramarital",37,39654943,235872);
INSERT INTO t3 (period,name,companynr,price,price2) VALUES (1004,"spates",78,726498,72987523);
INSERT INTO t3 (period,name,companynr,price,price2) VALUES (1005,"cloakroom",78,98439034,823742);
INSERT INTO t3 (period,name,companynr,price,price2) VALUES (1006,"gazer",101,834598,27348324);
INSERT INTO t3 (period,name,companynr,price,price2) VALUES (1007,"hand",154,*********,29837423);
INSERT INTO t3 (period,name,companynr,price,price2) VALUES (1008,"tucked",311,234298,3275892);
INSERT INTO t3 (period,name,companynr,price,price2) VALUES (1009,"gems",447,2374834,9872392);
INSERT INTO t3 (period,name,companynr,price,price2) VALUES (1010,"clinker",512,786542,76234234);
--enable_query_log

create temporary table tmp  select * from t3;

insert into t3 select * from tmp;
insert into tmp select * from t3;
insert into t3 select * from tmp;
insert into tmp select * from t3;
insert into t3 select * from tmp;
insert into tmp select * from t3;
insert into t3 select * from tmp;
insert into tmp select * from t3;
insert into t3 select * from tmp;
insert into tmp select * from t3;
insert into t3 select * from tmp;
insert into tmp select * from t3;
insert into t3 select * from tmp;
insert into tmp select * from t3;
insert into t3 select * from tmp;
insert into tmp select * from t3;
insert into t3 select * from tmp;
#insert into tmp select * from t3;
#insert into t3 select * from tmp;

# The test assumes a 1-1 correspondence of autoinc values to  fields tested.
set session innodb_parallel_read_threads = 1;
alter table t3 add t2nr int not null auto_increment primary key first;

drop table tmp;

# big table done

SET BIG_TABLES=1;
select distinct concat(fld3," ",fld3) as namn from t2,t3 where t2.fld1=t3.t2nr order by namn limit 10;
SET BIG_TABLES=0;
--source include/turn_off_only_full_group_by.inc
select distinct concat(fld3," ",fld3) from t2,t3 where t2.fld1=t3.t2nr order by fld3 limit 10;
--source include/restore_sql_mode_after_turn_off_only_full_group_by.inc
--skip_if_hypergraph   # Nondeterministic (depends on which rows DISTINCT outputs first).
select distinct fld5 from t2 limit 10;

#
# Force use of remove_dupp
#
# NOTE: Returned records may be selected randomly depending on storage engine
#       used due to bug#27716329 (not all sort columns are selected).
#

select distinct companynr, fld3,count(*) from t2 group by companynr, fld3 order by companynr, fld3 limit 10;
SET BIG_TABLES=1; # Force use of INNODB
select distinct companynr, fld3,count(*) from t2 group by companynr, fld3 order by companynr, fld3 limit 10;
SET BIG_TABLES=0;
select distinct companynr, fld3,repeat("a",length(fld3)),count(*) from t2 group by companynr ,fld3 order by companynr, fld3 limit 100,10;

#
# A big order by that should trigger a merge in filesort
#

select distinct companynr,rtrim(space(512+companynr)) from t3 order by 1,2;

#
# Search with distinct and order by with many table.
#

select distinct fld3 from t2,t3 where t2.companynr = 34 and t2.fld1=t3.t2nr order by fld3;

#
# Here the last fld3 is optimized away from the order by
#

explain select t3.t2nr,fld3 from t2,t3 where t2.companynr = 34 and t2.fld1=t3.t2nr order by t3.t2nr,fld3;

#
# Some test with ORDER BY and limit
#

explain select * from t3 as t1,t3 where t1.period=t3.period order by t3.period;
explain select * from t3 as t1,t3 where t1.period=t3.period order by t3.period limit 10;
explain select * from t3 as t1,t3 where t1.period=t3.period order by t1.period limit 10;

#
# Search with a constant table.
#

select period from t1;
select period from t1 where period=1900;
select fld3,period from t1,t2 where fld1 = 011401 order by period;

#
# Search with a constant table and several keyparts. (Rows are read only once
# in the beginning of the search)
#

select fld3,period from t2,t3 where t2.fld1 = 011401 and t2.fld1=t3.t2nr and t3.period=1001;

explain select fld3,period from t2,t3 where t2.fld1 = 011401 and t3.t2nr=t2.fld1 and 1001 = t3.period;

#
# Search with a constant table and several rows from another table
#

select fld3,period from t2,t1 where companynr*10 = 37*10 order by fld3;

#
# Search with a table reference and without a key.
# t3 will be the main table.
#

analyze table t2, t3;

--skip_if_hypergraph  # Depends on query plan.
EXPLAIN  select fld3,period,price,price2 from t2,t3 where t2.fld1=t3.t2nr and period >= 1001 and period <= 1002 and t2.companynr = 37 order by fld3,period, price;  # Tests how to print out BKA iterators.

select fld3,period,price,price2 from t2,t3 where t2.fld1=t3.t2nr and period >= 1001 and period <= 1002 and t2.companynr = 37 order by fld3,period, price;

#
# Search with an interval on a table with full key on reference table.
# Here t2 will be the main table and only records matching the
# t2nr will be checked.
#

select t2.fld1,fld3,period,price,price2 from t2,t3 where t2.fld1>= 18201 and t2.fld1 <= 18811 and t2.fld1=t3.t2nr and period = 1001 and t2.companynr = 37;

#
# We need another table for join stuff..
#

create table t4 (
  companynr tinyint(2) unsigned zerofill NOT NULL default '00',
  companyname char(30) NOT NULL default '',
  PRIMARY KEY (companynr),
  UNIQUE KEY companyname(companyname)
) ENGINE=INNODB MAX_ROWS=50 PACK_KEYS=1 COMMENT='companynames';

--disable_query_log
INSERT INTO t4 (companynr, companyname) VALUES (29,'company 1');
INSERT INTO t4 (companynr, companyname) VALUES (34,'company 2');
INSERT INTO t4 (companynr, companyname) VALUES (36,'company 3');
INSERT INTO t4 (companynr, companyname) VALUES (37,'company 4');
INSERT INTO t4 (companynr, companyname) VALUES (40,'company 5');
INSERT INTO t4 (companynr, companyname) VALUES (41,'company 6');
INSERT INTO t4 (companynr, companyname) VALUES (53,'company 7');
INSERT INTO t4 (companynr, companyname) VALUES (58,'company 8');
INSERT INTO t4 (companynr, companyname) VALUES (65,'company 9');
INSERT INTO t4 (companynr, companyname) VALUES (68,'company 10');
INSERT INTO t4 (companynr, companyname) VALUES (50,'company 11');
INSERT INTO t4 (companynr, companyname) VALUES (00,'Unknown');
--enable_query_log

#
# Test of stright join to force a full join.
#

# We see the functional dependency implied by WHERE and primary key of t4!

--sorted_result
select STRAIGHT_JOIN t2.companynr,companyname from t4,t2 where t2.companynr=t4.companynr group by t2.companynr;

--sorted_result
select SQL_SMALL_RESULT t2.companynr,companyname from t4,t2 where t2.companynr=t4.companynr group by t2.companynr;

#
# Full join (same alias)
#

select * from t1,t1 t12;
--sorted_result
select t2.fld1,t22.fld1 from t2,t2 t22 where t2.fld1 >= 250501 and t2.fld1 <= 250505 and t22.fld1 >= 250501 and t22.fld1 <= 250505;

#
# Test of left join.
#
insert into t2 (fld1, companynr) values (999999,99);
ANALYZE TABLE t2, t4;

select t2.companynr,companyname from t2 left join t4 using (companynr) where t4.companynr is null;
select count(*) from t2 left join t4 using (companynr) where t4.companynr is not null;
explain select t2.companynr,companyname from t2 left join t4 using (companynr) where t4.companynr is null;
explain select t2.companynr,companyname from t4 left join t2 using (companynr) where t2.companynr is null;

select companynr,companyname from t2 left join t4 using (companynr) where companynr is null;
select count(*) from t2 left join t4 using (companynr) where companynr is not null;
explain select companynr,companyname from t2 left join t4 using (companynr) where companynr is null;
explain select companynr,companyname from t4 left join t2 using (companynr) where companynr is null;
delete from t2 where fld1=999999;

#
# Test left join optimization

explain select t2.companynr,companyname from t4 left join t2 using (companynr) where t2.companynr > 0;
explain select t2.companynr,companyname from t4 left join t2 using (companynr) where t2.companynr > 0 or t2.companynr < 0;
explain select t2.companynr,companyname from t4 left join t2 using (companynr) where t2.companynr > 0 and t4.companynr > 0;

explain select companynr,companyname from t4 left join t2 using (companynr) where companynr > 0;
explain select companynr,companyname from t4 left join t2 using (companynr) where companynr > 0 or companynr < 0;
explain select companynr,companyname from t4 left join t2 using (companynr) where companynr > 0 and companynr > 0;
# Following can't be optimized
explain select t2.companynr,companyname from t4 left join t2 using (companynr) where t2.companynr > 0 or t2.companynr is null;
explain select t2.companynr,companyname from t4 left join t2 using (companynr) where t2.companynr > 0 or t2.companynr < 0 or t4.companynr > 0;
explain select t2.companynr,companyname from t4 left join t2 using (companynr) where ifnull(t2.companynr,1)>0;

explain select companynr,companyname from t4 left join t2 using (companynr) where companynr > 0 or companynr is null;
explain select companynr,companyname from t4 left join t2 using (companynr) where companynr > 0 or companynr < 0 or companynr > 0;
explain select companynr,companyname from t4 left join t2 using (companynr) where ifnull(companynr,1)>0;

#
# Joins with forms.
#

select distinct t2.companynr,t4.companynr from t2,t4 where t2.companynr=t4.companynr+1;
explain select distinct t2.companynr,t4.companynr from t2,t4 where t2.companynr=t4.companynr+1;

#
# Search using 'or' with the same referens group.
# An interval search will be done first with the first table and after that
# the other table is referenced with a key with a 'test if key in use' for
# each record
#

select t2.fld1,t2.companynr,fld3,period from t3,t2 where t2.fld1 = 38208 and t2.fld1=t3.t2nr and period = 1008 or t2.fld1 = 38008 and t2.fld1 =t3.t2nr and period = 1008;

select t2.fld1,t2.companynr,fld3,period from t3,t2 where (t2.fld1 = 38208 or t2.fld1 = 38008) and t2.fld1=t3.t2nr and period>=1008 and period<=1009;

select t2.fld1,t2.companynr,fld3,period from t3,t2 where (t3.t2nr = 38208 or t3.t2nr = 38008) and t2.fld1=t3.t2nr and period>=1008 and period<=1009;

#
# Test of many parenthesis levels
#

select period from t1 where (((period > 0) or period < 10000 or (period = 1900)) and (period=1900 and period <= 1901) or (period=1903 and (period=1903)) and period>=1902) or ((period=1904 or period=1905) or (period=1906 or period>1907)) or (period=1908 and period = 1909);
select period from t1 where ((period > 0 and period < 1) or (((period > 0 and period < 100) and (period > 10)) or (period > 10)) or (period > 0 and (period > 5 or period > 6)));

select a.fld1 from t2 as a,t2 b where ((a.fld1 = 250501 and a.fld1=b.fld1) or a.fld1=250502 or a.fld1=250503 or (a.fld1=250505 and a.fld1<=b.fld1 and b.fld1>=a.fld1)) and a.fld1=b.fld1;

select fld1 from t2 where fld1 in (250502,98005,98006,250503,250605,250606) and fld1 >=250502 and fld1 not in (250605,250606);

select fld1 from t2 where fld1 between 250502 and 250504;

--sorted_result
select fld3 from t2 where (((fld3 like "_%L%" ) or (fld3 like "%ok%")) and ( fld3 like "L%" or fld3 like "G%")) and fld3 like "L%" ;

#
# Group on one table.
# optimizer: sort table by group and send rows.
#

select count(*) from t1;
--sorted_result
select companynr,count(*),sum(fld1) from t2 group by companynr;
select companynr,count(*) from t2 group by companynr order by companynr desc limit 5;
select count(*),min(fld4),max(fld4),sum(fld1),avg(fld1),std(fld1),variance(fld1) from t2 where companynr = 34 and fld4<>"";
explain select count(*),min(fld4),max(fld4),sum(fld1),avg(fld1),std(fld1),variance(fld1) from t2 where companynr = 34 and fld4<>"";
select companynr,count(*),min(fld4),max(fld4),sum(fld1),avg(fld1),std(fld1),variance(fld1) from t2 group by companynr order by companynr limit 3;
select
companynr,t2nr,count(price),sum(price),min(price),max(price),avg(price) from
t3 where companynr = 37 group by companynr,t2nr order by companynr, t2nr limit 10;
select /*! SQL_SMALL_RESULT */
companynr,t2nr,count(price),sum(price),min(price),max(price),avg(price) from
t3 where companynr = 37 group by companynr,t2nr order by companynr, t2nr limit 10;
--sorted_result
select companynr,count(price),sum(price),min(price),max(price),avg(price) from t3 group by companynr ;
--sorted_result
select distinct mod(companynr,10) from t4 group by companynr;
--sorted_result
select distinct 1 from t4 group by companynr;
select count(distinct fld1) from t2;
--sorted_result
select companynr,count(distinct fld1) from t2 group by companynr;
--sorted_result
select companynr,count(*) from t2 group by companynr;
--sorted_result
select companynr,count(distinct concat(fld1,repeat(65,1000))) from t2 group by companynr;
--sorted_result
select companynr,count(distinct concat(fld1,repeat(65,200))) from t2 group by companynr;
--sorted_result
select companynr,count(distinct floor(fld1/100)) from t2 group by companynr;
--sorted_result
select companynr,count(distinct concat(repeat(65,1000),floor(fld1/100))) from t2 group by companynr;

#
# group with where on a key field
#

select sum(fld1),fld3 from t2 where fld3="Romans" group by fld1 limit 10;
select name,count(*) from t3 where name='cloakroom' group by name;
select name,count(*) from t3 where name='cloakroom' and price>10 group by name;
select count(*) from t3 where name='cloakroom' and price2=823742;
select name,count(*) from t3 where name='cloakroom' and price2=823742 group by name;
--sorted_result
select name,count(*) from t3 where name >= "extramarital" and price <= 39654943 group by name;

# We see the functional dependency implied by WHERE!

select t2.fld3,count(*) from t2,t3 where t2.fld1=158402 and t3.name=t2.fld3 group by t3.name;

#
# Group with extra not group fields.
#

--source include/turn_off_only_full_group_by.inc
--sorted_result
select companynr|0,companyname from t4 group by 1;
--source include/restore_sql_mode_after_turn_off_only_full_group_by.inc

select t2.companynr,companyname,count(*) from t2,t4 where t2.companynr=t4.companynr group by t2.companynr order by companyname;
select t2.fld1,count(*) from t2,t3 where t2.fld1=158402 and t3.name=t2.fld3 group by t3.name;


#
# Calculation with group functions
#

select sum(Period)/count(*) from t1;
--sorted_result
select companynr,count(price) as "count",sum(price) as "sum" ,abs(sum(price)/count(price)-avg(price)) as "diff",(0+count(price))*companynr as func from t3 group by companynr;
select companynr,sum(price)/count(price) as avg from t3 group by companynr having avg > 70000000 order by avg;

#
# Group with order on not first table
# optimizer: sort table by group and write group records to tmp table.
#            sort tmp_table and send rows.
#

select companynr,count(*) from t2 group by companynr order by 2 desc;
select companynr,count(*) from t2 where companynr > 40 group by companynr order by 2 desc;
--sorted_result
select t2.fld4,t2.fld1,count(price),sum(price),min(price),max(price),avg(price) from t3,t2 where t3.companynr = 37 and t2.fld1 = t3.t2nr group by fld1,t2.fld4;

#
# group by with many tables
# optimizer: create tmp table with group-by uniq index.
#           write with update to tmp table.
#           sort tmp table according to order (or group if no order)
#	    send rows
#

--sorted_result
select t3.companynr,fld3,sum(price) from t3,t2 where t2.fld1 = t3.t2nr and t3.companynr = 512 group by companynr,fld3;
--sorted_result
select t2.companynr,count(*),min(fld3),max(fld3),sum(price),avg(price) from t2,t3 where t3.companynr >= 30 and t3.companynr <= 58 and t3.t2nr = t2.fld1 and 1+1=2 group by t2.companynr;

#
# group with many tables and long group on many tables. group on formula
# optimizer: create tmp table with neaded fields
#           sort tmp table by group and calculate sums to new table
#	    if different order by than group, sort tmp table
#	    send rows
#

# We see the functional dependency implied by WHERE, for "ORDER BY fld1"

select t3.companynr+0,t3.t2nr,fld3,sum(price) from t3,t2 where t2.fld1 = t3.t2nr and t3.companynr = 37 group by 1,t3.t2nr,fld3,fld3,fld3,fld3,fld3 order by fld1;

#
# WHERE const folding
# optimize: If there is a "field = const" part in the where, change all
#           instances of field in the and level to const.
#	    All instances of const = const are checked once and removed.
#

#
# Where -> t3.t2nr = 98005 and t2.fld1 = 98005
#

select sum(price) from t3,t2 where t2.fld1 = t3.t2nr and t3.companynr = 512 and t3.t2nr = 38008 and t2.fld1 = 38008 or t2.fld1= t3.t2nr and t3.t2nr = 38008 and t2.fld1 = 38008;

select t2.fld1,sum(price) from t3,t2 where t2.fld1 = t3.t2nr and t3.companynr = 512 and t3.t2nr = 38008 and t2.fld1 = 38008 or t2.fld1 = t3.t2nr and t3.t2nr = 38008 and t2.fld1 = 38008 or t3.t2nr = t2.fld1 and t2.fld1 = 38008 group by t2.fld1;

explain select fld3 from t2 where 1>2 or 2>3;
explain select fld3 from t2 where fld1=fld1;

#
# HAVING
#

--sorted_result
select companynr,fld1 from t2 HAVING fld1=250501 or fld1=250502; 
--sorted_result
select companynr,fld1 from t2 WHERE fld1>=250501 HAVING fld1<=250502;
--sorted_result
select companynr,count(*) as count,sum(fld1) as sum from t2 group by companynr having count > 40 and sum/count >= 120000;
--sorted_result
select companynr from t2 group by companynr having count(*) > 40 and sum(fld1)/count(*) >= 120000 ;

--sorted_result
select t2.companynr,companyname,count(*) from t2,t4 where t2.companynr=t4.companynr group by companyname having t2.companynr >= 40;

#
# MIN(), MAX() and COUNT() optimizing
#

select count(*) from t2;
select count(*) from t2 where fld1 < 098024;
# PS does correct pre-zero here. MySQL can't do it as it returns a number.
--disable_ps_protocol
select min(fld1) from t2 where fld1>= 098024;
--enable_ps_protocol
select max(fld1) from t2 where fld1>= 098024;
select count(*) from t3 where price2=76234234;
select count(*) from t3 where companynr=512 and price2=76234234;
explain select min(fld1),max(fld1),count(*) from t2;
--skip_if_hypergraph  # Depends on query plan.
explain format=tree select min(fld1),max(fld1),count(*) from t2;  # Should be computed at optimize time.
ANALYZE TABLE t2;
--skip_if_hypergraph  # Depends on query plan.
--replace_regex $elide_costs
explain format=tree select min(fld1),max(fld1),count(*) from t2 where rand() > 0.5;  # Should _not_ be computed at optimize time.
# PS does correct pre-zero here. MySQL can't do it as it returns a number.
--disable_ps_protocol
select min(fld1),max(fld1),count(*) from t2;
--enable_ps_protocol
select min(t2nr),max(t2nr) from t3 where t2nr=2115 and price2=823742;
select count(*),min(t2nr),max(t2nr) from t3 where name='spates' and companynr=78;
select t2nr,count(*) from t3 where name='gems' group by t2nr limit 20;
select max(t2nr) from t3 where price=*********;

#
# Test of alias
#

select t1.period from t3 t1 limit 1;
select t1.period from t1 as t1 limit 1;
select t1.period as "Nuvarande period" from t1 as t1 limit 1;
select period as ok_period from t1 limit 1;
select period as ok_period from t1 group by ok_period limit 1;
select 1+1 as summa from t1 group by summa limit 1;

# Note that grouping expression is a literal (constant), not an aliased column.
--source include/turn_off_only_full_group_by.inc
select period as "Nuvarande period" from t1 group by "Nuvarande period" limit 1;
--source include/restore_sql_mode_after_turn_off_only_full_group_by.inc

#
# Some simple show commands
#

show tables;
show tables from test like "s%";
show tables from test like "t?";
show full columns from t2;
show full columns from t2 from test like 'f%';
show full columns from t2 from test like 's%';
analyze table t2;
show keys from t2;

drop table t4, t3, t2, t1;

#
# Test of DO
#

DO 1;
DO benchmark(100,1+1),1,1;

#
# Bug #6449: do default;
#

--error ER_PARSE_ERROR
do default;
--error ER_BAD_FIELD_ERROR
do foobar;

#
# random in WHERE clause
#

CREATE TABLE t1 (
  id mediumint(8) unsigned NOT NULL auto_increment,
  pseudo varchar(35) NOT NULL default '',
  PRIMARY KEY  (id),
  UNIQUE KEY pseudo (pseudo)
);
INSERT INTO t1 (pseudo) VALUES ('test');
INSERT INTO t1 (pseudo) VALUES ('test1');
SELECT 1 as rnd1 from t1 where rand() > 2;
DROP TABLE t1;

#
# Test of bug with SUM(CASE...)
#

CREATE TABLE t1 (gvid int(10) unsigned default NULL,  hmid int(10) unsigned default NULL,  volid int(10) unsigned default NULL,  mmid int(10) unsigned default NULL,  hdid int(10) unsigned default NULL,  fsid int(10) unsigned default NULL,  ctid int(10) unsigned default NULL,  dtid int(10) unsigned default NULL,  cost int(10) unsigned default NULL,  performance int(10) unsigned default NULL,  serialnumber bigint(20) unsigned default NULL,  monitored tinyint(3) unsigned default '1',  removed tinyint(3) unsigned default '0',  target tinyint(3) unsigned default '0',  dt_modified timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,  name varchar(255) binary default NULL,  description varchar(255) default NULL,  UNIQUE KEY hmid (hmid,volid)) ENGINE=INNODB;
INSERT INTO t1 VALUES (200001,2,1,1,100,1,1,1,0,0,0,1,0,1,20020425060057,'\\\\ARKIVIO-TESTPDC\\E$',''),(200002,2,2,1,101,1,1,1,0,0,0,1,0,1,20020425060057,'\\\\ARKIVIO-TESTPDC\\C$',''),(200003,1,3,2,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,0,1,20020425060427,'c:',NULL);
CREATE TABLE t2 (  hmid int(10) unsigned default NULL,  volid int(10) unsigned default NULL,  sampletid smallint(5) unsigned default NULL,  sampletime datetime default NULL,  samplevalue bigint(20) unsigned default NULL,  KEY idx1 (hmid,volid,sampletid,sampletime)) ENGINE=INNODB;
INSERT INTO t2 VALUES (1,3,10,'2002-06-01 08:00:00',35),(1,3,1010,'2002-06-01 12:00:01',35);
# Disable PS becasue we get more warnings from PS than from normal execution
--disable_ps_protocol
--error ER_WRONG_VALUE
SELECT a.gvid, (SUM(CASE b.sampletid WHEN 140 THEN b.samplevalue ELSE 0 END)) as the_success,(SUM(CASE b.sampletid WHEN 141 THEN b.samplevalue ELSE 0 END)) as the_fail,(SUM(CASE b.sampletid WHEN 142 THEN b.samplevalue ELSE 0 END)) as the_size,(SUM(CASE b.sampletid WHEN 143 THEN b.samplevalue ELSE 0 END)) as the_time FROM t1 a, t2 b WHERE a.hmid = b.hmid AND a.volid = b.volid AND b.sampletime >= 'wrong-date-value' AND b.sampletime < 'wrong-date-value' AND b.sampletid IN (140, 141, 142, 143) GROUP BY a.gvid;
--enable_ps_protocol
# Testing the same select with NULL's instead of invalid datetime values
SELECT a.gvid, (SUM(CASE b.sampletid WHEN 140 THEN b.samplevalue ELSE 0 END)) as the_success,(SUM(CASE b.sampletid WHEN 141 THEN b.samplevalue ELSE 0 END)) as the_fail,(SUM(CASE b.sampletid WHEN 142 THEN b.samplevalue ELSE 0 END)) as the_size,(SUM(CASE b.sampletid WHEN 143 THEN b.samplevalue ELSE 0 END)) as the_time FROM t1 a, t2 b WHERE a.hmid = b.hmid AND a.volid = b.volid AND b.sampletime >= NULL AND b.sampletime < NULL AND b.sampletid IN (140, 141, 142, 143) GROUP BY a.gvid;
DROP TABLE t1,t2;

#
# Test of bigint comparision
#

create table  t1 (  A_Id bigint(20) NOT NULL default '0',  A_UpdateBy char(10) NOT NULL default '',  A_UpdateDate bigint(20) NOT NULL default '0',  A_UpdateSerial int(11) NOT NULL default '0',  other_types bigint(20) NOT NULL default '0',  wss_type bigint(20) NOT NULL default '0');
INSERT INTO t1 VALUES (102935998719055004,'brade',1029359987,2,102935229116544068,102935229216544093);
select wss_type from t1 where wss_type ='102935229*********';
select wss_type from t1 where wss_type ='102935229216544105';
select wss_type from t1 where wss_type ='102935229216544104';
select wss_type from t1 where wss_type ='102935229216544093';
select wss_type from t1 where wss_type =102935229216544093;
drop table t1;
select 1+2,"aaaa",3.13*2.0 into @a,@b,@c;
select @a;
select @b;
select @c;

#
# Test of removing redundant braces in the FROM part
# (We test each construct with the braced join to the left and right;
#  the latter case used to cause a syntax errors.)
#

create table t1 (a int not null auto_increment primary key);
insert into t1 values ();
insert into t1 values ();
insert into t1 values ();
# ,
--sorted_result
select * from (t1 as t2 left join t1 as t3 using (a)), t1;
--sorted_result
select * from t1, (t1 as t2 left join t1 as t3 using (a));
# stright_join
--sorted_result
select * from (t1 as t2 left join t1 as t3 using (a)) straight_join t1;
--sorted_result
select * from t1 straight_join (t1 as t2 left join t1 as t3 using (a));
# inner join on
--sorted_result
select * from (t1 as t2 left join t1 as t3 using (a)) inner join t1 on t1.a>1;
--sorted_result
select * from t1 inner join (t1 as t2 left join t1 as t3 using (a)) on t1.a>1;
# inner join using
--sorted_result
select * from (t1 as t2 left join t1 as t3 using (a)) inner join t1 using ( a );
--sorted_result
select * from t1 inner join (t1 as t2 left join t1 as t3 using (a)) using ( a );
# left [outer] join on
--sorted_result
select * from (t1 as t2 left join t1 as t3 using (a)) left outer join t1 on t1.a>1;
--sorted_result
select * from t1 left outer join (t1 as t2 left join t1 as t3 using (a)) on t1.a>1;
# left join using
--sorted_result
select * from (t1 as t2 left join t1 as t3 using (a)) left join t1 using ( a );
--sorted_result
select * from t1 left join (t1 as t2 left join t1 as t3 using (a)) using ( a );
# natural left join
select * from (t1 as t2 left join t1 as t3 using (a)) natural left join t1;
--sorted_result
select * from t1 natural left join (t1 as t2 left join t1 as t3 using (a));
# right join on
--sorted_result
select * from (t1 as t2 left join t1 as t3 using (a)) right join t1 on t1.a>1;
--sorted_result
select * from t1 right join (t1 as t2 left join t1 as t3 using (a)) on t1.a>1;
# right [outer] joing using
--sorted_result
select * from (t1 as t2 left join t1 as t3 using (a)) right outer join t1 using ( a );
--sorted_result
select * from t1 right outer join (t1 as t2 left join t1 as t3 using (a)) using ( a );
# natural right join
--sorted_result
select * from (t1 as t2 left join t1 as t3 using (a)) natural right join t1;
--sorted_result
select * from t1 natural right join (t1 as t2 left join t1 as t3 using (a));
# natural join
--sorted_result
select * from t1 natural join (t1 as t2 left join t1 as t3 using (a));
--sorted_result
select * from (t1 as t2 left join t1 as t3 using (a)) natural join t1;
drop table t1;

CREATE TABLE t1 (  aa char(2),  id int(11) NOT NULL auto_increment,  t2_id int(11) NOT NULL default '0',  PRIMARY KEY  (id),  KEY replace_id (t2_id));
INSERT INTO t1 VALUES ("1",8264,2506),("2",8299,2517),("3",8301,2518),("4",8302,2519),("5",8303,2520),("6",8304,2521),("7",8305,2522);
CREATE TABLE t2 ( id int(11) NOT NULL auto_increment,  PRIMARY KEY  (id)) ENGINE=INNODB;
INSERT INTO t2 VALUES (2517), (2518), (2519), (2520), (2521), (2522);
select * from t1, t2 WHERE t1.t2_id = t2.id and t1.t2_id > 0   order by t1.id   LIMIT 0, 5;
drop table t1,t2;

#
# outer join, impossible on condition, where, and usable key for range
#
create table t1 (id1 int NOT NULL);
create table t2 (id2 int NOT NULL);
create table t3 (id3 int NOT NULL);
create table t4 (id4 int NOT NULL, id44 int NOT NULL, KEY (id4));

insert into t1 values (1);
insert into t1 values (2);
insert into t2 values (1);
insert into t4 values (1,1);

explain select * from t1 left join t2 on id1 = id2 left join t3 on id1 = id3
left join t4 on id3 = id4 where id2 = 1 or id4 = 1;
select * from t1 left join t2 on id1 = id2 left join t3 on id1 = id3
left join t4 on id3 = id4 where id2 = 1 or id4 = 1;

drop table t1,t2,t3,t4;
#
# Bug #2298
#

create table t1(s varchar(10) not null);
create table t2(s varchar(10) not null primary key);
create table t3(s varchar(10) not null primary key);
insert into t1 values ('one\t'), ('two\t');
insert into t2 values ('one\r'), ('two\t');
insert into t3 values ('one\b'), ('two\t');
select * from t1 where s = 'one';
select * from t2 where s = 'one';
select * from t3 where s = 'one';
select * from t1,t2 where t1.s = t2.s;
select * from t2,t3 where t2.s = t3.s;
drop table t1, t2, t3;

#
# Bug #3759
# Both queries should produce identical plans and results.
#
create table t1 (a integer,  b integer, index(a), index(b));
create table t2 (c integer,  d integer, index(c), index(d));
insert into t1 values (1,2), (2,2), (3,2), (4,2);
insert into t2 values (1,3), (2,3), (3,4), (4,4);
ANALYZE TABLE t1, t2;
explain select * from t1 left join t2 on a=c where d in (4);
select * from t1 left join t2 on a=c where d in (4);
explain select * from t1 left join t2 on a=c where d = 4;
select * from t1 left join t2 on a=c where d = 4;
drop table t1, t2;

#
# Covering index is mentioned in EXPLAIN output for const tables (bug #5333)
#

CREATE TABLE t1 (
  i int(11) NOT NULL default '0',
  c char(10) NOT NULL default '',
  PRIMARY KEY  (i),
  UNIQUE KEY c (c)
) ;

INSERT INTO t1 VALUES (1,'a');
INSERT INTO t1 VALUES (2,'b');
INSERT INTO t1 VALUES (3,'c');

EXPLAIN SELECT i FROM t1 WHERE i=1;

DROP TABLE t1;

#
# Test case for bug 7520: a wrong cost of the index for a BLOB field
#

CREATE TABLE t1 ( a BLOB, INDEX (a(20)) );
CREATE TABLE t2 ( a BLOB, INDEX (a(20)) );

INSERT INTO t1 VALUES ('one'),('two'),('three'),('four'),('five');
INSERT INTO t2 VALUES ('one'),('two'),('three'),('four'),('five');
ANALYZE TABLE t1, t2;
EXPLAIN SELECT * FROM t1 LEFT JOIN t2 USE INDEX (a) ON t1.a=t2.a;
EXPLAIN SELECT * FROM t1 LEFT JOIN t2 FORCE INDEX (a) ON t1.a=t2.a;

DROP TABLE t1, t2;

#
# Test case for bug 7098: substitution of a constant for a string field 
#

CREATE TABLE t1 ( city char(30) ) charset utf8mb4;
INSERT INTO t1 VALUES ('London');
INSERT INTO t1 VALUES ('Paris');

SELECT * FROM t1 WHERE city='London';
SELECT * FROM t1 WHERE city='london';
EXPLAIN SELECT * FROM t1 WHERE city='London' AND city='london';
SELECT * FROM t1 WHERE city='London' AND city='london';
EXPLAIN SELECT * FROM t1 WHERE city LIKE '%london%' AND city='London';
SELECT * FROM t1 WHERE city LIKE '%london%' AND city='London';

DROP TABLE t1;

#
# Bug#7425 inconsistent sort order on unsigned columns result of substraction
#

create table t1 (a int(11) unsigned, b int(11) unsigned);
insert into t1 values (1,0), (1,1), (4294967295,1);
select a-b  from t1 order by 1;
select a-b , (a-b < 0)  from t1 order by 1;
--source include/turn_off_only_full_group_by.inc
select a-b as d, (a-b >= 0), b from t1 group by b having d >= 0;
--source include/restore_sql_mode_after_turn_off_only_full_group_by.inc
select cast((a - b) as unsigned) from t1 order by 1;
drop table t1;


#
# Bug#8733 server accepts malformed query (multiply mentioned distinct)
#
create table t1 (a int(11));
select all all * from t1;
select distinct distinct * from t1;
--error 1221
select all distinct * from t1;
--error 1221
select distinct all * from t1;
drop table t1;

#
# Test for BUG#10095
#
CREATE TABLE t1 (
  kunde_intern_id int(10) unsigned NOT NULL default '0',
  kunde_id int(10) unsigned NOT NULL default '0',
  FK_firma_id int(10) unsigned NOT NULL default '0',
  aktuell enum('Ja','Nein') NOT NULL default 'Ja',
  vorname varchar(128) NOT NULL default '',
  nachname varchar(128) NOT NULL default '',
  geloescht enum('Ja','Nein') NOT NULL default 'Nein',
  firma varchar(128) NOT NULL default ''
);

INSERT INTO t1 VALUES 
  (3964,3051,1,'Ja','Vorname1','1Nachname','Nein','Print Schau XXXX'),
  (3965,3051111,1,'Ja','Vorname1111','1111Nachname','Nein','Print Schau XXXX');


SELECT kunde_id ,FK_firma_id ,aktuell, vorname, nachname, geloescht FROM t1
  WHERE
   (
      (
         ( '' != '' AND firma LIKE CONCAT('%', '', '%'))
         OR
         (vorname LIKE CONCAT('%', 'Vorname1', '%') AND 
          nachname LIKE CONCAT('%', '1Nachname', '%') AND 
          'Vorname1' != '' AND 'xxxx' != '')
      )
      AND
      (
        aktuell = 'Ja' AND geloescht = 'Nein' AND FK_firma_id = 2
      )
   )
 ;

SELECT kunde_id ,FK_firma_id ,aktuell, vorname, nachname,
geloescht FROM t1
  WHERE
   (
     (
       aktuell = 'Ja' AND geloescht = 'Nein' AND FK_firma_id = 2
     )
     AND
     (
         ( '' != '' AND firma LIKE CONCAT('%', '', '%')  )
         OR
         (  vorname LIKE CONCAT('%', 'Vorname1', '%') AND
nachname LIKE CONCAT('%', '1Nachname', '%') AND 'Vorname1' != '' AND
'xxxx' != '')
     )
   )
 ;

SELECT COUNT(*) FROM t1 WHERE 
( 0 OR (vorname LIKE '%Vorname1%' AND nachname LIKE '%1Nachname%' AND 1)) 
AND FK_firma_id = 2;

drop table t1;

#
#
# Test for Bug#8009, SELECT failed on bigint unsigned when using HEX
#

CREATE TABLE t1 (b BIGINT(20) UNSIGNED NOT NULL, PRIMARY KEY (b));
INSERT INTO t1 VALUES (0x8000000000000000);
SELECT b FROM t1 WHERE b=0x8000000000000000;
DROP TABLE t1;

#
# IN with outer join condition (BUG#9393)
#
CREATE TABLE `t1` ( `gid` int(11) default NULL, `uid` int(11) default NULL);

CREATE TABLE `t2` ( `ident` int(11) default NULL, `level` char(16) default NULL);
INSERT INTO `t2` VALUES (0,'READ');

CREATE TABLE `t3` ( `id` int(11) default NULL, `name` char(16) default NULL);
INSERT INTO `t3` VALUES (1,'fs');

select * from t3 left join t1 on t3.id = t1.uid, t2 where t2.ident in (0, t1.gid, t3.id, 0);

drop table t1,t2,t3;

# Test for BUG#11700
CREATE TABLE t1 (
  acct_id int(11) NOT NULL default '0',
  profile_id smallint(6) default NULL,
  UNIQUE KEY t1$acct_id (acct_id),
  KEY t1$profile_id (profile_id)
);
INSERT INTO t1 VALUES (132,17),(133,18);

CREATE TABLE t2 (
  profile_id smallint(6) default NULL,
  queue_id int(11) default NULL,
  seq int(11) default NULL,
  KEY t2$queue_id (queue_id)
);
INSERT INTO t2 VALUES (17,31,4),(17,30,3),(17,36,2),(17,37,1);

CREATE TABLE t3 (
  id int(11) NOT NULL default '0',
  qtype int(11) default NULL,
  seq int(11) default NULL,
  warn_lvl int(11) default NULL,
  crit_lvl int(11) default NULL,
  rr1 tinyint(4) NOT NULL default '0',
  rr2 int(11) default NULL,
  default_queue tinyint(4) NOT NULL default '0',
  KEY t3$qtype (qtype),
  KEY t3$id (id)
);

INSERT INTO t3 VALUES (30,1,29,NULL,NULL,0,NULL,0),(31,1,28,NULL,NULL,0,NULL,0),
  (36,1,34,NULL,NULL,0,NULL,0),(37,1,35,NULL,NULL,0,121,0);

SELECT COUNT(*) FROM t1 a STRAIGHT_JOIN t2 pq STRAIGHT_JOIN t3 q 
WHERE 
  (pq.profile_id = a.profile_id) AND (a.acct_id = 132) AND 
  (pq.queue_id = q.id) AND (q.rr1 <> 1);

drop table t1,t2,t3;

#
# Bug #11482 Wrongly applied optimization was erroneously rejecting valid
#            rows 
create table t1 (f1 int);
insert into t1 values (1),(NULL);
create table t2 (f2 int, f3 int, f4 int);
create index idx1 on t2 (f4);
insert into t2 values (1,2,3),(2,4,6);
select A.f2 from t1 left join t2 A on A.f2 = f1 where A.f3=(select min(f3)
from  t2 C where A.f4 = C.f4) or A.f3 IS NULL; 
drop table t1,t2;

#
# Bug #11521 Negative integer keys incorrectly substituted for 0 during
#            range analysis.

create table t2 (a tinyint unsigned);
create index t2i on t2(a);
insert into t2 values (0), (254), (255);
ANALYZE TABLE t2;
explain select * from t2 where a > -1;
select * from t2 where a > -1;
drop table t2;

#
# Bug #11745: SELECT ... FROM DUAL with WHERE condition
#

CREATE TABLE t1 (a int, b int, c int);
INSERT INTO t1
  SELECT 50, 3, 3 FROM DUAL
    WHERE NOT EXISTS
      (SELECT * FROM t1 WHERE a = 50 AND b = 3);
SELECT * FROM t1;
INSERT INTO t1
  SELECT 50, 3, 3 FROM DUAL
    WHERE NOT EXISTS
      (SELECT * FROM t1 WHERE a = 50 AND b = 3);
select found_rows();
SELECT * FROM t1;
select count(*) from t1;
select found_rows();
select count(*) from t1 limit 2,3;
select found_rows();
select SQL_CALC_FOUND_ROWS count(*) from t1 limit 2,3;
select found_rows();

DROP TABLE t1;

#
# Bug 7672 Unknown column error in order clause
#
CREATE TABLE t1 (a INT, b INT);
(SELECT a, b AS c FROM t1) ORDER BY c+1;
(SELECT a, b AS c FROM t1) ORDER BY b+1;
SELECT a, b AS c FROM t1 ORDER BY c+1;
SELECT a, b AS c FROM t1 ORDER BY b+1;
drop table t1;

#
# Bug #13356 assertion failed in resolve_const_item()
#
create table t1(f1 int, f2 int);
create table t2(f3 int);
select f1 from t1,t2 where f1=f2 and (f1,f2) = ((1,1));
select f1 from t1,t2 where f1=f2 and (f1,NULL) = ((1,1));
select f1 from t1,t2 where f1=f2 and (f1,f2) = ((1,NULL));
insert into t1 values(1,1),(2,null);
insert into t2 values(2);
select * from t1,t2 where f1=f3 and (f1,f2) = (2,null);
select * from t1,t2 where f1=f3 and (f1,f2) <=> (2,null);
drop table t1,t2; 

#
# Bug #13535
#
create table t1 (f1 int not null auto_increment primary key, f2 varchar(10));
create table t11 like t1;
insert into t1 values(1,""),(2,"");
analyze table t1, t11;
--replace_column 7 X 8 X 9 X 10 X 11 X 12 X 13 X 14 X 15 X
show table status like 't1%';
select 123 as a from t1 where f1 is null;
drop table t1,t11;

#
# Bug #3874 (function in GROUP and LEFT JOIN)
#

CREATE TABLE t1 ( a INT NOT NULL, b INT NOT NULL, UNIQUE idx (a,b) );
INSERT INTO t1 VALUES (1,1),(1,2),(1,3),(1,4);
CREATE TABLE t2 ( a INT NOT NULL, b INT NOT NULL, e INT );
INSERT INTO t2 VALUES ( 1,10,1), (1,10,2), (1,11,1), (1,11,2), (1,2,1), (1,2,2),(1,2,3);
--sorted_result
SELECT t2.a, t2.b, IF(t1.b IS NULL,'',e) AS c, COUNT(*) AS d FROM t2 LEFT JOIN
t1 ON t2.a = t1.a AND t2.b = t1.b GROUP BY a, b, c;
--source include/turn_off_only_full_group_by.inc
--sorted_result
SELECT t2.a, t2.b, IF(t1.b IS NULL,'',e) AS c, COUNT(*) AS d FROM t2 LEFT JOIN
t1 ON t2.a = t1.a AND t2.b = t1.b GROUP BY t1.a, t1.b, c;
--source include/restore_sql_mode_after_turn_off_only_full_group_by.inc
--sorted_result
SELECT t2.a, t2.b, IF(t1.b IS NULL,'',e) AS c, COUNT(*) AS d FROM t2 LEFT JOIN
t1 ON t2.a = t1.a AND t2.b = t1.b GROUP BY t2.a, t2.b, c;
--sorted_result
SELECT t2.a, t2.b, IF(t1.b IS NULL,'',e) AS c, COUNT(*) AS d FROM t2,t1
WHERE t2.a = t1.a AND t2.b = t1.b GROUP BY a, b, c;
DROP TABLE IF EXISTS t1, t2;

#
# Bug #13855 select distinct with group by caused server crash
#
create table t1 (f1 int primary key, f2 int);
create table t2 (f3 int, f4 int, primary key(f3,f4));
insert into t1 values (1,1);
insert into t2 values (1,1),(1,2);
select distinct count(f2) >0 from t1 left join t2 on f1=f3 group by f1;
drop table t1,t2;

#
# Bug #14482 Server crash when subselecting from the same table
#
create table t1 (f1 int,f2 int);
insert into t1 values(1,1);
create table t2 (f3 int, f4 int, primary key(f3,f4));
insert into t2 values(1,1);
select * from t1 where f1 in (select f3 from t2 where (f3,f4)= (select f3,f4 from t2)); 
drop table t1,t2;

#
# Bug #4981: 4.x and 5.x produce non-optimal execution path, 3.23 regression test failure
#
CREATE TABLE t1(a int, b int, c int, KEY b(b), KEY c(c));
insert into t1 values (1,0,0),(2,0,0);
CREATE TABLE t2 (a int, b varchar(2), c varchar(2), PRIMARY KEY(a));
insert into t2 values (1,'',''), (2,'','');
CREATE TABLE t3 (a int, b int, PRIMARY KEY (a,b), KEY a (a), KEY b (b));
insert into t3 values (1,1),(1,2);
# must have "range checked" for t2
explain select straight_join DISTINCT t2.a,t2.b, t1.c from t1, t3, t2 
 where (t1.c=t2.a or (t1.c=t3.a and t2.a=t3.b)) and t1.b=556476786 and 
       t2.b like '%%' order by t2.b limit 0,1;
DROP TABLE t1,t2,t3;

#
# Bug #17873: confusing error message when IGNORE INDEX refers a column name
#

CREATE TABLE t1 (a int, INDEX idx(a));
INSERT INTO t1 VALUES (2), (3), (1);
ANALYZE TABLE t1;
EXPLAIN SELECT * FROM t1 IGNORE INDEX (idx);
--error 1176
EXPLAIN SELECT * FROM t1 IGNORE INDEX (a);
--error 1176
EXPLAIN SELECT * FROM t1 FORCE INDEX (a);

DROP TABLE t1;

#
# Bug #21019: First result of SELECT COUNT(*) different than consecutive runs
#
CREATE TABLE t1 (a int, b int);
INSERT INTO t1 VALUES (1,1), (2,1), (4,10);

CREATE TABLE t2 (a int PRIMARY KEY, b int, KEY b (b));
INSERT INTO t2 VALUES (1,NULL), (2,10);
ALTER TABLE t1 ENABLE KEYS;

ANALYZE TABLE t1, t2;

EXPLAIN SELECT STRAIGHT_JOIN COUNT(*) FROM t2, t1 WHERE t1.b = t2.b OR t2.b IS NULL;
--sorted_result
SELECT STRAIGHT_JOIN * FROM t2, t1 WHERE t1.b = t2.b OR t2.b IS NULL;
EXPLAIN SELECT STRAIGHT_JOIN COUNT(*) FROM t2, t1 WHERE t1.b = t2.b OR t2.b IS NULL;
--sorted_result
SELECT STRAIGHT_JOIN * FROM t2, t1 WHERE t1.b = t2.b OR t2.b IS NULL;
DROP TABLE IF EXISTS t1,t2;

#
# Bug #20954 "avg(keyval) retuns 0.38 but max(keyval) returns an empty set"
#
# TODO: Change double in the table definition back to float after bug #29931596 is fixed.
#
CREATE TABLE t1 (key1 double default NULL, UNIQUE KEY key1 (key1));
CREATE TABLE t2 (key2 double default NULL, UNIQUE KEY key2 (key2));
INSERT INTO t1 VALUES (0.3762),(0.3845),(0.6158),(0.7941);
INSERT INTO t2 VALUES (1.3762),(1.3845),(1.6158),(1.7941);

ANALYZE TABLE t1, t2;
explain select max(key1) from t1 where key1 <= 0.6158;
explain select max(key2) from t2 where key2 <= 1.6158;
explain select min(key1) from t1 where key1 >= 0.3762;
explain select min(key2) from t2 where key2 >= 1.3762;
explain select max(key1), min(key2) from t1, t2
where key1 <= 0.6158 and key2 >= 1.3762;
explain select max(key1) from t1 where key1 <= 0.6158 and rand() + 0.5 >= 0.5;
explain select min(key1) from t1 where key1 >= 0.3762 and rand() + 0.5 >= 0.5;

select max(key1) from t1 where key1 <= 0.6158;
select max(key2) from t2 where key2 <= 1.6158;
select min(key1) from t1 where key1 >= 0.3762;
select min(key2) from t2 where key2 >= 1.3762;
select max(key1), min(key2) from t1, t2
where key1 <= 0.6158 and key2 >= 1.3762;
select max(key1) from t1 where key1 <= 0.6158 and rand() + 0.5 >= 0.5;
select min(key1) from t1 where key1 >= 0.3762 and rand() + 0.5 >= 0.5;

DROP TABLE t1,t2;

#
# Bug #18759 "Incorrect string to numeric conversion"
#
# This test is here so that the behavior will not be changed to 4.1
# and not to 5.0 either. In 4.1 and 5.0 sending an integer as a string
# will be converted internally to real (double) value and it is not
# as accurate as bigint (longlong) for integers. Thus the results may
# vary. In 5.1 internally it is decimal, which is a string type and
# will be more accurate. Due to rather big changes needed to fix this
# in 4.1 or 5.0 it is not desired to do it in the stable versions.
#
# This test is here only to make sure that behavior is not changed in
# 4.1 and 5.0
#
CREATE TABLE t1 (i BIGINT UNSIGNED NOT NULL);
INSERT INTO t1 VALUES (10);
SELECT i='1e+01',i=1e+01, i in (1e+01,1e+01), i in ('1e+01','1e+01') FROM t1;
DROP TABLE t1;

#
# Bug #22533: storing large hex strings
#

create table t1(a bigint unsigned, b bigint);
insert ignore into t1 values (0xfffffffffffffffff, 0xfffffffffffffffff), 
  (0x10000000000000000, 0x10000000000000000), 
  (0x8fffffffffffffff, 0x8fffffffffffffff);
select hex(a), hex(b) from t1;
drop table t1;

#
# Bug #32103: optimizer crash when join on int and mediumint with variable in 
#             where clause
#

CREATE TABLE t1 (c0 int);
CREATE TABLE t2 (c0 int);

# We need any variable that:
# 1. has integer type, 
# 2. can be used with the "@@name" syntax
# 3. available in every server build
INSERT INTO t1 VALUES(@@connect_timeout);
INSERT INTO t2 VALUES(@@connect_timeout);

# We only need to ensure 1 row is returned to validate the results
--replace_column 1 X 2 X
SELECT * FROM t1 JOIN t2 ON t1.c0 = t2.c0 WHERE (t1.c0 <=> @@connect_timeout);

DROP TABLE t1, t2;

--echo End of 4.1 tests

#
# Test for bug #6474
#

CREATE TABLE t1 ( 
K2C4 varchar(4) character set latin1 collate latin1_bin NOT NULL default '', 
K4N4 varchar(4) character set latin1 collate latin1_bin NOT NULL default '0000', 
F2I4 int(11) NOT NULL default '0' 
) DEFAULT CHARSET=latin1;

INSERT INTO t1 VALUES 
('W%RT', '0100',  1), 
('W-RT', '0100', 1), 
('WART', '0100', 1), 
('WART', '0200', 1), 
('WERT', '0100', 2), 
('WORT','0200', 2), 
('WT', '0100', 2), 
('W_RT', '0100', 2), 
('WaRT', '0100', 3), 
('WART', '0300', 3), 
('WRT' , '0400', 3), 
('WURM', '0500', 3), 
('W%T', '0600', 4), 
('WA%T', '0700', 4), 
('WA_T', '0800', 4);

SELECT K2C4, K4N4, F2I4 FROM t1
  WHERE  K2C4 = 'WART' AND 
        (F2I4 = 2 AND K2C4 = 'WART' OR (F2I4 = 2 OR K4N4 = '0200'));
SELECT K2C4, K4N4, F2I4 FROM t1
  WHERE  K2C4 = 'WART' AND (K2C4 = 'WART' OR K4N4 = '0200');
DROP TABLE t1;

#
# Bug#8670
#
create table t1 (a int, b int);
create table t2 like t1;
select t1.a from (t1 inner join t2 on t1.a=t2.a) where t2.a=1;
select t1.a from ((t1 inner join t2 on t1.a=t2.a)) where t2.a=1;
select x.a, y.a, z.a from ( (t1 x inner join t2 y on x.a=y.a) inner join t2 z on y.a=z.a) WHERE x.a=1;
drop table t1,t2;

#
# Bug#9820
#

create table t1 (s1 varchar(5));
insert into t1 values ('Wall');
select min(s1) from t1 group by s1 with rollup;
drop table t1;

#
# Bug#9799
#

create table t1 (s1 int);
insert into t1 values (0);
select avg(distinct s1) from t1 group by s1 with rollup;
drop table t1;

#
# Bug#9800
#

create table t1 (s1 int);
insert into t1 values (null),(1);
select avg(s1) as x from t1 group by s1 with rollup;
select distinct avg(s1) as x from t1 group by s1 with rollup;
drop table t1;


#
# Test for bug #10084: STRAIGHT_JOIN with ON expression 
#

CREATE TABLE t1 (a int);
CREATE TABLE t2 (a int);
INSERT INTO t1 VALUES (1), (2), (3), (4), (5);
INSERT INTO t2 VALUES (2), (4), (6);
ANALYZE TABLE t1, t2;
SELECT t1.a FROM t1 STRAIGHT_JOIN t2 ON t1.a=t2.a;

EXPLAIN SELECT t1.a FROM t1 STRAIGHT_JOIN t2 ON t1.a=t2.a;
EXPLAIN SELECT t1.a FROM t1 INNER JOIN t2 ON t1.a=t2.a;

DROP TABLE t1,t2;

#
# Bug #10650
#

select x'10' + 0, X'10' + 0, b'10' + 0, B'10' + 0;

#
# Bug #11398 Bug in field_conv() results in wrong result of join with index
#
create table t1 (f1 varchar(6) default NULL, f2 int(6) primary key not null);
create table t2 (f3 varchar(5) not null, f4 varchar(5) not null, UNIQUE KEY UKEY (f3,f4));
insert into t1 values (" 2", 2);
insert into t2 values (" 2", " one "),(" 2", " two ");
--sorted_result
select * from t1 left join t2 on f1 = f3;
drop table t1,t2;

#
# Bug #6558 Views: CREATE VIEW fails with JOIN ... USING
#

create table t1 (empnum smallint, grp int);
create table t2 (empnum int, name char(5));
insert into t1 values(1,1);
insert into t2 values(1,'bob');
create view v1 as select * from t2 inner join t1 using (empnum);
--sorted_result
select * from v1;
drop table t1,t2;
drop view v1;

#
# Bug #10646 Columns included in the join between two tables are ambigious
# in the select
#

create table t1 (pk int primary key, b int);
create table t2 (pk int primary key, c int);
--sorted_result
select pk from t1 inner join t2 using (pk);
drop table t1,t2;

#
# Bug #10972 Natural join of view and underlying table gives wrong result
#

create table t1 (s1 int, s2 char(5), s3 decimal(10));
create view v1 as select s1, s2, 'x' as s3 from t1;
select * from t1 natural join v1;
insert into t1 values (1,'x',5);
select * from t1 natural join v1;
drop table t1;
drop view v1;

#
# Bug #6276 A SELECT that does a NATURAL OUTER JOIN without common
#           columns crashes server because of empty ON condition
#

create table t1(a1 int);
create table t2(a2 int);
insert into t1 values(1),(2);
insert into t2 values(1),(2);
create view v2 (c) as select a1 from t1;

--sorted_result
select * from t1 natural left join t2;
--sorted_result
select * from t1 natural right join t2;

--sorted_result
select * from v2 natural left join t2;
--sorted_result
select * from v2 natural right join t2;

drop table t1, t2;
drop view v2;


#
# Bug #4789 Incosistent results of more than 2-way natural joins due to
#           incorrect transformation to join ... on.
#

create table t1 (a int(10), t1_val int(10));
create table t2 (b int(10), t2_val int(10));
create table t3 (a int(10), b int(10));
insert into t1 values (1,1),(2,2);
insert into t2 values (1,1),(2,2),(3,3);
insert into t3 values (1,1),(2,1),(3,1),(4,1);
# the following two queries must return the same result
--sorted_result
select * from t1 natural join t2 natural join t3;
--sorted_result
select * from t1 natural join t3 natural join t2;
drop table t1, t2, t3;


#
# Bug #12841: Server crash on DO IFNULL(NULL,NULL)
#
# (testing returning of int, decimal, real, string)
DO IFNULL(NULL, NULL);
SELECT CAST(IFNULL(NULL, NULL) AS DECIMAL);
SELECT ABS(IFNULL(NULL, NULL));
SELECT IFNULL(NULL, NULL);

#
# BUG #12595 (ESCAPE must be exactly one)
#
SET @OLD_SQL_MODE12595=@@SQL_MODE, @@SQL_MODE='';
SHOW LOCAL VARIABLES LIKE 'SQL_MODE';

CREATE TABLE BUG_12595(a varchar(100)) charset latin1;
INSERT INTO BUG_12595 VALUES ('hakan%'), ('hakank'), ("ha%an");
SELECT * FROM BUG_12595 WHERE a LIKE 'hakan\%';
SELECT * FROM BUG_12595 WHERE a LIKE 'hakan*%' ESCAPE '*';
-- error 1210
SELECT * FROM BUG_12595 WHERE a LIKE 'hakan**%' ESCAPE '**';
# this should work when sql_mode is not NO_BACKSLASH_ESCAPES
SELECT * FROM BUG_12595 WHERE a LIKE 'hakan%' ESCAPE '';
SELECT * FROM BUG_12595 WHERE a LIKE 'hakan\%' ESCAPE '';
SELECT * FROM BUG_12595 WHERE a LIKE 'ha\%an' ESCAPE 0x5c;
SELECT * FROM BUG_12595 WHERE a LIKE 'ha%%an' ESCAPE '%';
SELECT * FROM BUG_12595 WHERE a LIKE 'ha\%an' ESCAPE '\\';
SELECT * FROM BUG_12595 WHERE a LIKE 'ha|%an' ESCAPE '|';

SET @@SQL_MODE='NO_BACKSLASH_ESCAPES';
SHOW LOCAL VARIABLES LIKE 'SQL_MODE';
SELECT * FROM BUG_12595 WHERE a LIKE 'hakan\%';
SELECT * FROM BUG_12595 WHERE a LIKE 'hakan*%' ESCAPE '*';
-- error 1210
SELECT * FROM BUG_12595 WHERE a LIKE 'hakan**%' ESCAPE '**';
-- error 1210
SELECT * FROM BUG_12595 WHERE a LIKE 'hakan\%' ESCAPE '\\';
#this gives an error when NO_BACKSLASH_ESCAPES is set
-- error 1210
SELECT * FROM BUG_12595 WHERE a LIKE 'hakan%' ESCAPE '';
SELECT * FROM BUG_12595 WHERE a LIKE 'ha\%an' ESCAPE 0x5c;
SELECT * FROM BUG_12595 WHERE a LIKE 'ha|%an' ESCAPE '|';
-- error 1210
SELECT * FROM BUG_12595 WHERE a LIKE 'hakan\n%' ESCAPE '\n';

SET @@SQL_MODE=@OLD_SQL_MODE12595;
DROP TABLE BUG_12595;

#
# Bug #6495 Illogical requirement for column qualification in NATURAL join
#

create table t1 (a char(1));
create table t2 (a char(1));
insert into t1 values ('a'),('b'),('c');
insert into t2 values ('b'),('c'),('d');
select a from t1 natural join t2;
select * from t1 natural join t2 where a = 'b';
drop table t1, t2;

#
# Bug #12977 Compare table names with qualifying field tables only
# for base tables, search all nested join operands of natural joins.
#

CREATE TABLE t1 (`id` TINYINT);
CREATE TABLE t2 (`id` TINYINT);
CREATE TABLE t3 (`id` TINYINT);
INSERT INTO t1 VALUES (1),(2),(3);
INSERT INTO t2 VALUES (2);
INSERT INTO t3 VALUES (3);
-- error 1052
SELECT t1.id,t3.id FROM t1 JOIN t2 ON (t2.id=t1.id) LEFT JOIN t3 USING (id);
-- error 1052
SELECT t1.id,t3.id FROM t1 JOIN t2 ON (t2.notacolumn=t1.id) LEFT JOIN t3 USING (id);
-- error 1052
SELECT id,t3.id FROM t1 JOIN t2 ON (t2.id=t1.id) LEFT JOIN t3 USING (id);
-- error 1052
SELECT id,t3.id FROM (t1 JOIN t2 ON (t2.id=t1.id)) LEFT JOIN t3 USING (id);

drop table t1, t2, t3;

#
# Bug #13067 JOIN xxx USING is case sensitive
#

create table t1 (a int(10),b int(10));
create table t2 (a int(10),b int(10));
insert into t1 values (1,10),(2,20),(3,30);
insert into t2 values (1,10);
# both queries should produce the same result
select * from t1 inner join t2 using (A);
select * from t1 inner join t2 using (a);
drop table t1, t2;

#
# Bug #12943 Incorrect nesting of [INNER| CROSS] JOIN due to unspecified
#            associativity in the parser.
#

create table t1 (a int, c int);
create table t2 (b int);
create table t3 (b int, a int);
create table t4 (c int);
insert into t1 values (1,1);
insert into t2 values (1);
insert into t3 values (1,1);
insert into t4 values (1);

select * from t1 join t2 join t3 on (t2.b = t3.b and t1.a = t3.a);
# Notice that ',' has lower priority than 'join', thus we have that:
# t1, t2 join t3 <==> t1, (t2 join t3).
-- error 1054
select * from t1, t2 join t3 on (t2.b = t3.b and t1.a = t3.a);
select * from t1 join t2 join t3 join t4 on (t1.a = t4.c and t2.b = t4.c);
select * from t1 join t2 join t4 using (c);
drop table t1, t2, t3, t4;

#
# Bug #12291 Table wasn't reinited for index scan after sequential scan 
#
create table t1(x int, y int);
create table t2(x int, y int);
create table t3(x int, primary key(x));
insert into t1 values (1, 1), (2, 1), (3, 1), (4, 3), (5, 6), (6, 6);
insert into t2 values (1, 1), (2, 1), (3, 3), (4, 6), (5, 6);
insert into t3 values (1), (2), (3), (4), (5);
--sorted_result
select t1.x, t3.x from t1, t2, t3  where t1.x = t2.x and t3.x >= t1.y and t3.x <= t2.y;
drop table t1,t2,t3;

#
# Bug #13127 LEFT JOIN against a VIEW returns NULL instead of correct value
#

create table t1 (id char(16) not null default '', primary key  (id));
insert into t1 values ('100'),('101'),('102');
create table t2 (id char(16) default null);
insert into t2 values (1);
create view v1 as select t1.id from t1;
create view v2 as select t2.id from t2;
create view v3 as select (t1.id+2) as id from t1 natural left join t2;

# all queries must return the same result
select t1.id from t1 left join v2 using (id);
select t1.id from v2 right join t1 using (id);
--sorted_result
select t1.id from t1 left join v3 using (id);
select * from t1 left join v2 using (id);
select * from v2 right join t1 using (id);
--sorted_result
select * from t1 left join v3 using (id);

select v1.id from v1 left join v2 using (id);
select v1.id from v2 right join v1 using (id);
--sorted_result
select v1.id from v1 left join v3 using (id);
select * from v1 left join v2 using (id);
select * from v2 right join v1 using (id);
--sorted_result
select * from v1 left join v3 using (id);

drop table t1, t2;
drop view v1, v2, v3;

#
# Bug #13597 Column in ON condition not resolved if references a table in
# nested right join.
#

create table t1 (id int(11) not null default '0');
insert into t1 values (123),(191),(192);
create table t2 (id char(16) character set utf8mb3 not null);
insert into t2 values ('58013'),('58014'),('58015'),('58016');
create table t3 (a_id int(11) not null, b_id char(16) character set utf8mb3);
insert into t3 values (123,null),(123,null),(123,null),(123,null),(123,null),(123,'58013');

# both queries are equivalent
select count(*)
from t1 inner join (t3 left join t2 on t2.id = t3.b_id) on t1.id = t3.a_id;

select count(*)
from t1 inner join (t2 right join t3 on t2.id = t3.b_id) on t1.id = t3.a_id;

drop table t1,t2,t3;

#
# Bug #13832 Incorrect parse order of join productions due to unspecified
# operator priorities results in incorrect join tree.
#

create table t1 (a int);
create table t2 (b int);
create table t3 (c int);
select * from t1 join t2 join t3 on (t1.a=t3.c);
select * from t1 join t2 left join t3 on (t1.a=t3.c);
select * from t1 join t2 right join t3 on (t1.a=t3.c);
select * from t1 join t2 straight_join t3 on (t1.a=t3.c);
drop table t1, t2 ,t3;

#
# Bug #14093 Query takes a lot of time when date format is not valid
# fix optimizes execution. so here we just check that returned set is
# correct.
create table t1(f1 int, f2 date);
insert into t1 values(1,'2005-01-01'),(2,'2005-09-01'),(3,'2005-09-30'),
  (4,'2005-10-01'),(5,'2005-12-30');
# should return all records
select * from t1 where f2 >= 0            order by f2;
--error ER_WRONG_VALUE
select * from t1 where f2 >= '0000-00-00' order by f2;
# should return 4,5
--error ER_WRONG_VALUE
select * from t1 where f2 >= '2005-09-31' order by f2;
select * from t1 where f2 >= '2005-09-3a' order by f2;
# should return 1,2,3
--error ER_WRONG_VALUE
select * from t1 where f2 <= '2005-09-31' order by f2;
select * from t1 where f2 <= '2005-09-3a' order by f2;
drop table t1;

#
# Bug ##14662  	ORDER BY on column of a view, with an alias of the same
# column causes ambiguous
#

create table t1 (f1 int, f2 int);
insert into t1 values (1, 30), (2, 20), (3, 10);
create algorithm=merge view v1 as select f1, f2 from t1;
create algorithm=merge view v2 (f2, f1) as select f1, f2 from t1;
create algorithm=merge view v3 as select t1.f1 as f2, t1.f2 as f1 from t1;
select t1.f1 as x1, f1 from t1 order by t1.f1;
select v1.f1 as x1, f1 from v1 order by v1.f1;
select v2.f1 as x1, f1 from v2 order by v2.f1;
select v3.f1 as x1, f1 from v3 order by v3.f1;
select f1, f2, v1.f1 as x1 from v1 order by v1.f1;
select f1, f2, v2.f1 as x1 from v2 order by v2.f1;
select f1, f2, v3.f1 as x1 from v3 order by v3.f1;
drop table t1;
drop view v1, v2, v3;

#
# Bug #15106: lost equality predicate of the form field=const in a join query
#

CREATE TABLE t1(key_a int4 NOT NULL, optimus varchar(32), PRIMARY KEY(key_a));
CREATE TABLE t2(key_a int4 NOT NULL, prime varchar(32), PRIMARY KEY(key_a));
CREATE table t3(key_a int4 NOT NULL, key_b int4 NOT NULL, foo varchar(32),
                PRIMARY KEY(key_a,key_b));

INSERT INTO t1 VALUES (0,'');
INSERT INTO t1 VALUES (1,'i');
INSERT INTO t1 VALUES (2,'j');
INSERT INTO t1 VALUES (3,'k');

INSERT INTO t2 VALUES (1,'r');
INSERT INTO t2 VALUES (2,'s');
INSERT INTO t2 VALUES (3,'t');

INSERT INTO t3 VALUES (1,5,'x');
INSERT INTO t3 VALUES (1,6,'y');
INSERT INTO t3 VALUES (2,5,'xx');
INSERT INTO t3 VALUES (2,6,'yy');
INSERT INTO t3 VALUES (2,7,'zz');
INSERT INTO t3 VALUES (3,5,'xxx');

SELECT t2.key_a,foo 
  FROM t1 INNER JOIN t2 ON t1.key_a = t2.key_a
          INNER JOIN t3 ON t1.key_a = t3.key_a
    WHERE t2.key_a=2 and key_b=5;
EXPLAIN SELECT t2.key_a,foo 
  FROM t1 INNER JOIN t2 ON t1.key_a = t2.key_a
          INNER JOIN t3 ON t1.key_a = t3.key_a
    WHERE t2.key_a=2 and key_b=5;

SELECT t2.key_a,foo 
  FROM t1 INNER JOIN t2 ON t2.key_a = t1.key_a
          INNER JOIN t3 ON t1.key_a = t3.key_a
    WHERE t2.key_a=2 and key_b=5;
EXPLAIN SELECT t2.key_a,foo 
  FROM t1 INNER JOIN t2 ON t2.key_a = t1.key_a
          INNER JOIN t3 ON t1.key_a = t3.key_a
    WHERE t2.key_a=2 and key_b=5;

DROP TABLE t1,t2,t3;

#
# Bug#15347 Wrong result of subselect when records cache and set functions
#           are involved
#
create  table t1 (f1 int);
insert into t1 values(1),(2);
create table t2 (f2 int, f3 int, key(f2));
insert into t2 values(1,1),(2,2);
create table t3 (f4 int not null);
insert into t3 values (2),(2),(2);
select f1,(select count(*) from t2,t3 where f2=f1 and f3=f4) as count from t1;
drop table t1,t2,t3;

#
# Bug #15633 Evaluation of Item_equal for non-const table caused wrong
#            select result
#
create table t1 (f1 int unique);
create table t2 (f2 int unique);
create table t3 (f3 int unique);
insert into t1 values(1),(2);
insert into t2 values(1),(2);
insert into t3 values(1),(NULL);
select * from t3 where f3 is null;
select t2.f2 from t1 left join t2 on f1=f2 join t3 on f1=f3 where f1=1;
drop table t1,t2,t3;

#
# Bug#15268 Unchecked null value caused server crash
#
create table t1(f1 char, f2 char not null);
insert into t1 values(null,'a');
create table t2 (f2 char not null);
insert into t2 values('b');
select * from t1 left join t2 on f1=t2.f2 where t1.f2='a';
drop table t1,t2;

#
# Bug#15538 unchecked table absense caused server crash.
#
--error 1064
select * from (select * left join t on f1=f2) tt;

#
# Bug #16504: re-evaluation of Item_equal object after reading const table
#

CREATE TABLE t1 (sku int PRIMARY KEY, pr int);
CREATE TABLE t2 (sku int PRIMARY KEY, sppr int, name varchar(255));

INSERT INTO t1 VALUES
  (10, 10), (20, 10), (30, 20), (40, 30), (50, 10), (60, 10);

INSERT INTO t2 VALUES 
  (10, 10, 'aaa'), (20, 10, 'bbb'), (30, 10, 'ccc'), (40, 20, 'ddd'),
  (50, 10, 'eee'), (60, 20, 'fff'), (70, 20, 'ggg'), (80, 30, 'hhh');

--sorted_result
SELECT t2.sku, t2.sppr, t2.name, t1.sku, t1.pr
  FROM t2, t1 WHERE t2.sku=20 AND (t2.sku=t1.sku OR t2.sppr=t1.sku);
EXPLAIN
SELECT t2.sku, t2.sppr, t2.name, t1.sku, t1.pr
  FROM t2, t1 WHERE t2.sku=20 AND (t2.sku=t1.sku OR t2.sppr=t1.sku);


DROP TABLE t1,t2;

#
# Bug#18712: Truncation problem (needs just documenting and test
# cases to prevent fixing this accidently. It is intended behaviour)
#

SET SQL_MODE='NO_UNSIGNED_SUBTRACTION';
CREATE TABLE t1 (i TINYINT UNSIGNED NOT NULL);
INSERT t1 SET i = 0;
UPDATE t1 SET i = -1;
SELECT * FROM t1;
UPDATE t1 SET i = CAST(i - 1 AS SIGNED);
SELECT * FROM t1;
UPDATE t1 SET i = i - 1;
SELECT * FROM t1;
DROP TABLE t1;
SET SQL_MODE=default;

# BUG#17379

create table t1 (a int);
insert into t1 values (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
create table t2 (a int, b int, c int, e int, primary key(a,b,c));

--echo # The "ANALYZE TABLE"-command that is executed further down will get
--echo # different results depending on the order of rows in table t2. Since the
--echo # INSERT INTO ... SELECT may be executed using different execution plans,
--echo # we've added ORDER BY to ensure that we rows has the same order every
--echo # time. If not, the estimated number of rows for t2 (alias 'a') in the
--echo # EXPLAIN may change on different platforms. Note that both table t1 and
--echo # t2 may be MYISAM, since many of the test files that includes this file
--echo # forces MYISAM as the default storage engine.

insert into t2 select A.a, B.a, C.a, C.a from t1 A, t1 B, t1 C
  ORDER BY A.a, B.a, C.a;
analyze table t2;
select 'In next EXPLAIN, B.rows must be exactly 10:' Z;

explain select * from t2 a, t2 b where a.a=5 and a.b=5 and a.c<5
          and b.a=5 and b.b=a.e and (b.b =1 or b.b = 3 or b.b=5);
drop table t1, t2;

#
#Bug #18940: selection of optimal execution plan caused by equality
#            propagation (the bug was fixed by the patch for bug #17379)

CREATE TABLE t1 (a int PRIMARY KEY, b int, INDEX(b));
INSERT INTO t1 VALUES (1, 3), (9,4), (7,5), (4,5), (6,2),
                      (3,1), (5,1), (8,9), (2,2), (0,9);

CREATE TABLE t2 (c int, d int, f int, INDEX(c,f));
INSERT INTO t2 VALUES
 (1,0,0), (1,0,1), (2,0,0), (2,0,1), (3,0,0), (4,0,1),
 (5,0,0), (5,0,1), (6,0,0), (0,0,1), (7,0,0), (7,0,1),
 (0,0,0), (0,0,1), (8,0,0), (8,0,1), (9,0,0), (9,0,1);
ANALYZE TABLE t1, t2;
EXPLAIN
SELECT a, c, d, f FROM t1,t2 WHERE a=c AND b BETWEEN 4 AND 6;
EXPLAIN
SELECT a, c, d, f FROM t1,t2 WHERE a=c AND b BETWEEN 4 AND 6 AND a > 0;

DROP TABLE t1, t2;

#
# Bug #18895: BIT values cause joins to fail
#
create table t1 (
    a int unsigned    not null auto_increment primary key,
    b bit             not null,
    c bit             not null
);

create table t2 (
    a int unsigned    not null auto_increment primary key,
    b bit             not null,
    c int unsigned    not null,
    d varchar(50)
);

insert into t1 (b,c) values (0,1), (0,1);
insert into t2 (b,c) values (0,1);

# Row 1 should succeed.  Row 2 should fail.  Both fail.
select t1.a, t1.b + 0, t1.c + 0, t2.a, t2.b + 0, t2.c, t2.d
from t1 left outer join t2 on t1.a = t2.c and t2.b <> 1
where t1.b <> 1 order by t1.a;

drop table t1,t2;

#
# Bug #20569: Garbage in DECIMAL results from some mathematical functions
#
SELECT 0.9888889889 * 1.011111411911;

#
# Bug #10977: No warning issued if a column name is truncated
#
prepare stmt from 'select 1 as " a "';
execute stmt;

#
# Bug #21390: wrong estimate of rows after elimination of const tables
#

CREATE TABLE t1 (a int NOT NULL PRIMARY KEY, b int NOT NULL);
INSERT INTO t1 VALUES (1,1), (2,2), (3,3), (4,4);

CREATE TABLE t2 (c int NOT NULL, INDEX idx(c));
INSERT INTO t2 VALUES
  (1), (1), (1), (1), (1), (1), (1), (1),
  (2), (2), (2), (2),
  (3), (3),
  (4);
ANALYZE TABLE t1, t2;

EXPLAIN SELECT b FROM t1, t2 WHERE b=c AND a=1;
EXPLAIN SELECT b FROM t1, t2 WHERE b=c AND a=4;

DROP TABLE t1, t2;

#
# No matches for a join after substitution of a const table
#

CREATE TABLE t1 (id int NOT NULL PRIMARY KEY, a int);
INSERT INTO t1 VALUES (1,2), (2,NULL), (3,2);

CREATE TABLE t2 (b int, c INT, INDEX idx1(b));
INSERT INTO t2 VALUES (2,1), (3,2);

CREATE TABLE t3 (d int,  e int, INDEX idx1(d));
INSERT INTO t3 VALUES (2,10), (2,20), (1,30), (2,40), (2,50);

EXPLAIN
SELECT * FROM t1 LEFT JOIN t2 ON t2.b=t1.a INNER JOIN t3 ON t3.d=t1.id
  WHERE t1.id=2;
--sorted_result
SELECT * FROM t1 LEFT JOIN t2 ON t2.b=t1.a INNER JOIN t3 ON t3.d=t1.id
  WHERE t1.id=2;

DROP TABLE t1,t2,t3;

#
# Bug#20503: Server crash due to the ORDER clause isn't taken into account
#            while space allocation
#
create table t1 (c1 varchar(1), c2 int, c3 int, c4 int, c5 int, c6 int,
c7 int, c8 int, c9 int, fulltext key (`c1`));
select distinct match (`c1`) against ('z') , c2, c3, c4,c5, c6,c7, c8 
  from t1 where c9=1 order by c2, c2;
drop table t1;

#
# Bug #22735: no equality propagation for BETWEEN and IN with STRING arguments
#

CREATE TABLE t1 (pk varchar(10) PRIMARY KEY, fk varchar(16)) charset utf8mb4;
CREATE TABLE t2 (pk varchar(16) PRIMARY KEY, fk varchar(10)) charset utf8mb4;

INSERT INTO t1 VALUES
  ('d','dddd'), ('i','iii'), ('a','aa'), ('b','bb'), ('g','gg'), 
  ('e','eee'), ('c','cccc'), ('h','hhh'), ('j','jjj'), ('f','fff');
INSERT INTO t2 VALUES
  ('jjj', 'j'), ('cc','c'), ('ccc','c'), ('aaa', 'a'), ('jjjj','j'),
  ('hhh','h'), ('gg','g'), ('fff','f'), ('ee','e'), ('ffff','f'),
  ('bbb','b'), ('ff','f'), ('cccc','c'), ('dddd','d'), ('jj','j'),
  ('aaaa','a'), ('bb','b'), ('eeee','e'), ('aa','a'), ('hh','h');

ANALYZE TABLE t1, t2;
EXPLAIN SELECT t2.* 
  FROM t1 JOIN t2 ON t2.fk=t1.pk
    WHERE t2.fk < 'c' AND t2.pk=t1.fk;
EXPLAIN SELECT t2.* 
  FROM t1 JOIN t2 ON t2.fk=t1.pk 
    WHERE t2.fk BETWEEN 'a' AND 'b' AND t2.pk=t1.fk;
EXPLAIN SELECT t2.* 
  FROM t1 JOIN t2 ON t2.fk=t1.pk 
    WHERE t2.fk IN ('a','b') AND t2.pk=t1.fk;

DROP TABLE t1,t2;

#
# Bug #22367: Optimizer uses ref join type instead of eq_ref for simple 
#               join on strings
#
CREATE TABLE t1 (a int, b varchar(20) NOT NULL, PRIMARY KEY(a)) charset utf8mb4;
CREATE TABLE t2 (a int, b varchar(20) NOT NULL,
                 PRIMARY KEY (a), UNIQUE KEY (b)) charset utf8mb4;
INSERT INTO t1 VALUES (1,'a'),(2,'b'),(3,'c');
INSERT INTO t2 VALUES (1,'a'),(2,'b'),(3,'c');

EXPLAIN SELECT t1.a FROM t1 LEFT JOIN t2 ON t2.b=t1.b WHERE t1.a=3;

DROP TABLE t1,t2;

#
# Bug #19579: predicates that become sargable after reading const tables
#             are not taken into account by optimizer
#

CREATE TABLE t1(id int PRIMARY KEY, b int, e int);
CREATE TABLE t2(i int, a int, INDEX si(i), INDEX ai(a));
CREATE TABLE t3(a int PRIMARY KEY, c char(4), INDEX ci(c));

INSERT INTO t1 VALUES 
  (1,10,19), (2,20,22), (4,41,42), (9,93,95), (7, 77,79),
  (6,63,67), (5,55,58), (3,38,39), (8,81,89);
INSERT INTO t2 VALUES
  (21,210), (41,410), (82,820), (83,830), (84,840),
  (65,650), (51,510), (37,370), (94,940), (76,760),
  (22,220), (33,330), (40,400), (95,950), (38,380),
  (67,670), (88,880), (57,570), (96,960), (97,970);
INSERT INTO t3 VALUES
  (210,'bb'), (950,'ii'), (400,'ab'), (500,'ee'), (220,'gg'),
  (440,'gg'), (310,'eg'), (380,'ee'), (840,'bb'), (830,'ff'),
  (230,'aa'), (960,'ii'), (410,'aa'), (510,'ee'), (290,'bb'),
  (450,'gg'), (320,'dd'), (390,'hh'), (850,'jj'), (860,'ff');
ANALYZE TABLE t1, t2, t3;

EXPLAIN
SELECT t3.a FROM t1,t2 FORCE INDEX (si),t3
  WHERE t1.id = 8 AND t2.i BETWEEN t1.b AND t1.e AND 
        t3.a=t2.a AND t3.c IN ('bb','ee');

EXPLAIN
SELECT t3.a FROM t1,t2,t3
  WHERE t1.id = 8 AND t2.i BETWEEN t1.b AND t1.e AND
        t3.a=t2.a AND t3.c IN ('bb','ee') ;

EXPLAIN 
SELECT t3.a FROM t1,t2 FORCE INDEX (si),t3
  WHERE t1.id = 8 AND (t2.i=t1.b OR t2.i=t1.e) AND t3.a=t2.a AND
        t3.c IN ('bb','ee');
EXPLAIN 
SELECT t3.a FROM t1,t2,t3
  WHERE t1.id = 8 AND (t2.i=t1.b OR t2.i=t1.e) AND t3.a=t2.a AND
        t3.c IN ('bb','ee');

DROP TABLE t1,t2,t3;
 
#
# Bug#25172: Not checked buffer size leads to a server crash
#
CREATE TABLE t1 ( f1 int primary key, f2 int, f3 int, f4 int, f5 int, f6 int, checked_out int);
CREATE TABLE t2 ( f11 int PRIMARY KEY );
INSERT INTO t1 VALUES (1,1,1,0,0,0,0),(2,1,1,3,8,1,0),(3,1,1,4,12,1,0);
INSERT INTO t2 VALUES (62);

# We see the functional dependency implied by ON and primary key of t1!

SELECT * FROM t1 LEFT JOIN t2 ON f11 = t1.checked_out GROUP BY f1 ORDER BY f2, f3, f4, f5 LIMIT 0, 1;

DROP TABLE t1, t2;

#
# Bug#6298: LIMIT #, -1 no longer works to set start with no end limit
#

--disable_warnings
DROP TABLE IF EXISTS t1;
--enable_warnings

CREATE TABLE t1(a int);
INSERT into t1 values (1), (2), (3);

# LIMIT N, -1 was accepted by accident in 4.0, but was not intended.
# This test verifies that this illegal construct is now properly detected.

--error ER_PARSE_ERROR
SELECT * FROM t1 LIMIT 2, -1;

DROP TABLE t1;

#
# 25407: wrong estimate of NULL keys for unique indexes
#
set optimizer_switch='index_merge=off';
CREATE TABLE t1 (
  ID_with_null int NULL,
  ID_better int NOT NULL,
  INDEX idx1 (ID_with_null),
  INDEX idx2 (ID_better)
);


INSERT INTO t1 VALUES (1,1), (2,1), (null,3), (null,3), (null,3), (null,3);
INSERT INTO t1 SELECT * FROM t1 WHERE ID_with_null IS NULL;
INSERT INTO t1 SELECT * FROM t1 WHERE ID_with_null IS NULL;
INSERT INTO t1 SELECT * FROM t1 WHERE ID_with_null IS NULL;
INSERT INTO t1 SELECT * FROM t1 WHERE ID_with_null IS NULL;
INSERT INTO t1 SELECT * FROM t1 WHERE ID_with_null IS NULL;
ANALYZE TABLE t1;
SELECT COUNT(*) FROM t1 WHERE ID_with_null IS NULL;
SELECT COUNT(*) FROM t1 WHERE ID_better=1;

EXPLAIN SELECT * FROM t1 WHERE ID_better=1 AND ID_with_null IS NULL;

DROP INDEX idx1 ON t1;
CREATE UNIQUE INDEX idx1 ON t1(ID_with_null);
ANALYZE TABLE t1;

EXPLAIN SELECT * FROM t1 WHERE ID_better=1 AND ID_with_null IS NULL;

DROP TABLE t1;

CREATE TABLE t1 (
  ID1_with_null int NULL,
  ID2_with_null int NULL,
  ID_better int NOT NULL,
  INDEX idx1 (ID1_with_null, ID2_with_null),
  INDEX idx2 (ID_better)
) ;

INSERT INTO t1 VALUES (1,1,1), (2,2,1), (3,null,3), (null,3,3), (null,null,3),
  (3,null,3), (null,3,3), (null,null,3), (3,null,3), (null,3,3), (null,null,3);

INSERT INTO t1 SELECT * FROM t1 WHERE ID1_with_null IS NULL;
INSERT INTO t1 SELECT * FROM t1 WHERE ID2_with_null IS NULL;
INSERT INTO t1 SELECT * FROM t1 WHERE ID1_with_null IS NULL;
INSERT INTO t1 SELECT * FROM t1 WHERE ID2_with_null IS NULL;
INSERT INTO t1 SELECT * FROM t1 WHERE ID1_with_null IS NULL;
INSERT INTO t1 SELECT * FROM t1 WHERE ID2_with_null IS NULL;

SELECT COUNT(*) FROM t1 WHERE ID1_with_null IS NULL AND ID2_with_null=3;
SELECT COUNT(*) FROM t1 WHERE ID1_with_null=3 AND ID2_with_null IS NULL;
SELECT COUNT(*) FROM t1 WHERE ID1_with_null IS NULL AND ID2_with_null IS NULL;
SELECT COUNT(*) FROM t1 WHERE ID_better=1;
ANALYZE TABLE t1;
EXPLAIN SELECT * FROM t1
  WHERE ID_better=1 AND ID1_with_null IS NULL AND ID2_with_null=3 ;
EXPLAIN SELECT * FROM t1
  WHERE ID_better=1 AND ID1_with_null=3 AND ID2_with_null=3 IS NULL ;
EXPLAIN SELECT * FROM t1
  WHERE ID_better=1 AND ID1_with_null IS NULL AND ID2_with_null IS NULL;

DROP INDEX idx1 ON t1;
CREATE UNIQUE INDEX idx1 ON t1(ID1_with_null,ID2_with_null);
ANALYZE TABLE t1;
EXPLAIN SELECT * FROM t1
  WHERE ID_better=1 AND ID1_with_null IS NULL AND ID2_with_null=3 ;
EXPLAIN SELECT * FROM t1
  WHERE ID_better=1 AND ID1_with_null=3 AND ID2_with_null IS NULL ;
EXPLAIN SELECT * FROM t1
  WHERE ID_better=1 AND ID1_with_null IS NULL AND ID2_with_null IS NULL;
EXPLAIN SELECT * FROM t1
  WHERE ID_better=1 AND ID1_with_null IS NULL AND 
        (ID2_with_null=1 OR ID2_with_null=2);

DROP TABLE t1;
set optimizer_switch='index_merge=on';

#
# Bug #22344: InnoDB keys act strange on datetime vs timestamp comparison
#
CREATE TABLE t1 (a INT, ts TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, KEY ts(ts));
INSERT INTO t1 VALUES (30,"2006-01-03 23:00:00"), (31,"2006-01-03 23:00:00");
ANALYZE TABLE t1;

CREATE TABLE t2 (a INT, dt1 DATETIME, dt2 DATETIME, PRIMARY KEY (a));
INSERT INTO t2 VALUES (30, "2006-01-01 00:00:00", "2999-12-31 00:00:00");
INSERT INTO t2 SELECT a+1,dt1,dt2 FROM t2;
ANALYZE TABLE t2;

EXPLAIN
SELECT * FROM t1 LEFT JOIN t2 ON (t1.a=t2.a) WHERE t1.a=30
  AND t1.ts BETWEEN t2.dt1 AND t2.dt2
  AND t1.ts BETWEEN "2006-01-01" AND "2006-12-31";

--skip_if_hypergraph   # Different warnings.
SELECT * FROM t1 LEFT JOIN t2 ON (t1.a=t2.a) WHERE t1.a=30
  AND t1.ts BETWEEN t2.dt1 AND t2.dt2
  AND t1.ts BETWEEN "2006-01-01" AND "2006-12-31";

DROP TABLE t1,t2;
# Bug #22026: Warning when using IF statement and large unsigned bigint
#

create table t1 (a bigint unsigned);
insert into t1 values
  (if(1, 9223372036854775808, 1)),
  (case when 1 then 9223372036854775808 else 1 end),
  (coalesce(9223372036854775808, 1));
select * from t1;
drop table t1;
create table t1 charset utf8mb4 select
  if(1, 9223372036854775808, 1) i,
  case when 1 then 9223372036854775808 else 1 end c,
  coalesce(9223372036854775808, 1) co;
show create table t1;
drop table t1;
# Ensure we handle big values properly
select 
  if(1, cast(1111111111111111111 as unsigned), 1) i,
  case when 1 then cast(1111111111111111111 as unsigned) else 1 end c,
  coalesce(cast(1111111111111111111 as unsigned), 1) co;

#
# Bug #22971: indexes on text columns are ignored for ref accesses 
#

CREATE TABLE t1 (name varchar(255)) charset latin1;
CREATE TABLE t2 (name varchar(255), n int, KEY (name(3))) charset latin1;
INSERT INTO t1 VALUES ('ccc'), ('bb'), ('cc '), ('aa  '), ('aa');
INSERT INTO t2 VALUES ('bb',1), ('aa',2), ('cc   ',3);
INSERT INTO t2 VALUES (concat('cc ', '!'), 4);
INSERT INTO t2 VALUES ('cc',5), ('bb ',6), ('cc ',7);
ANALYZE TABLE t1, t2;
SELECT * FROM t2;
SELECT * FROM t2 ORDER BY name;
SELECT name, LENGTH(name), n FROM t2 ORDER BY name;

EXPLAIN SELECT name, LENGTH(name), n FROM t2 WHERE name='cc '; 
SELECT name, LENGTH(name), n FROM t2 WHERE name='cc '; 
EXPLAIN SELECT name , LENGTH(name), n FROM t2 WHERE name LIKE 'cc%';
SELECT name , LENGTH(name), n FROM t2 WHERE name LIKE 'cc%';
EXPLAIN SELECT name , LENGTH(name), n FROM t2 WHERE name LIKE 'cc%' ORDER BY name;
SELECT name , LENGTH(name), n FROM t2 WHERE name LIKE 'cc%' ORDER BY name;
EXPLAIN SELECT * FROM t1 LEFT JOIN t2 ON t1.name=t2.name;
--sorted_result
SELECT * FROM t1 LEFT JOIN t2 ON t1.name=t2.name;

DROP TABLE t1,t2;

CREATE TABLE t1 (name text) charset latin1;
CREATE TABLE t2 (name text, n int, KEY (name(3))) charset latin1;
INSERT INTO t1 VALUES ('ccc'), ('bb'), ('cc '), ('aa  '), ('aa');
INSERT INTO t2 VALUES ('bb',1), ('aa',2), ('cc   ',3);
INSERT INTO t2 VALUES (concat('cc ', '!'), 4);
INSERT INTO t2 VALUES ('cc',5), ('bb ',6), ('cc ',7);
ANALYZE TABLE t1, t2;
SELECT * FROM t2;
SELECT * FROM t2 ORDER BY name;
SELECT name, LENGTH(name), n FROM t2 ORDER BY name;

EXPLAIN SELECT name, LENGTH(name), n FROM t2 WHERE name='cc '; 
SELECT name, LENGTH(name), n FROM t2 WHERE name='cc '; 
EXPLAIN SELECT name , LENGTH(name), n FROM t2 WHERE name LIKE 'cc%';
SELECT name , LENGTH(name), n FROM t2 WHERE name LIKE 'cc%';
EXPLAIN SELECT name , LENGTH(name), n FROM t2 WHERE name LIKE 'cc%' ORDER BY name;
SELECT name , LENGTH(name), n FROM t2 WHERE name LIKE 'cc%' ORDER BY name;
EXPLAIN SELECT * FROM t1 LEFT JOIN t2 ON t1.name=t2.name;
--sorted_result
SELECT * FROM t1 LEFT JOIN t2 ON t1.name=t2.name;

DROP TABLE t1,t2;


#
# Bug #26963: join with predicates that contain fields from equalities evaluated
#             to constants after constant table substitution
#

CREATE TABLE t1 (
 access_id int NOT NULL default '0',
 name varchar(20) default NULL,
 `rank` int NOT NULL default '0',
 KEY idx (access_id)
);

CREATE TABLE t2 (
  faq_group_id int NOT NULL default '0',
  faq_id int NOT NULL default '0',
  access_id int default NULL,
  UNIQUE KEY idx1 (faq_id),
  KEY idx2 (faq_group_id,faq_id)
);

INSERT INTO t1 VALUES 
  (1,'Everyone',2),(2,'Help',3),(3,'Technical Support',1),(4,'Chat User',4);
INSERT INTO t2 VALUES
  (261,265,1),(490,494,1);


SELECT t2.faq_id 
  FROM t1 INNER JOIN t2 IGNORE INDEX (idx1)
       ON (t1.access_id = t2.access_id)
       LEFT JOIN t2 t
       ON (t.faq_group_id = t2.faq_group_id AND
           find_in_set(t.access_id, '1,4') < find_in_set(t2.access_id, '1,4'))
   WHERE
     t2.access_id IN (1,4) AND t.access_id IS NULL AND t2.faq_id in (265);

SELECT t2.faq_id 
  FROM t1 INNER JOIN t2
       ON (t1.access_id = t2.access_id)
       LEFT JOIN t2 t
       ON (t.faq_group_id = t2.faq_group_id AND
           find_in_set(t.access_id, '1,4') < find_in_set(t2.access_id, '1,4'))
   WHERE
     t2.access_id IN (1,4) AND t.access_id IS NULL AND t2.faq_id in (265);

DROP TABLE t1,t2;


#
# Bug #19372: Optimizer does not use index anymore when WHERE index NOT IN
# () is added
#
CREATE TABLE t1 (a INT, b INT, KEY inx (b,a));

INSERT INTO t1 VALUES (1,1), (1,2), (1,3), (1,4), (1,5), (1, 6), (1,7);
ANALYZE TABLE t1;
EXPLAIN SELECT COUNT(*) FROM t1 f1 INNER JOIN t1 f2
    ON ( f1.b=f2.b AND f1.a<f2.a ) 
    WHERE 1 AND f1.b NOT IN (100,2232,3343,51111);
DROP TABLE t1;    

#
# Bug #27352: Incorrect result of nested selects instead of error reporting
#

CREATE TABLE t1 (c1 INT, c2 INT);
INSERT INTO t1 VALUES (1,11), (2,22), (2,22);

let $n= 29;
let $q= COUNT(c2);
while ($n)
{
  let $q= (SELECT $q);
  dec $n;
}
--disable_warnings
--disable_result_log
--error 0,1436
eval EXPLAIN SELECT c1 FROM t1 WHERE $q > 0;
--enable_result_log
--enable_warnings

let $n= 64;
let $q= COUNT(c2);
while ($n)
{
  let $q= (SELECT $q);
  dec $n;
}
--error ER_TOO_HIGH_LEVEL_OF_NESTING_FOR_SELECT
eval EXPLAIN SELECT c1 FROM t1 WHERE $q > 0;

DROP TABLE t1;

#
# Bug #30396: crash for a join with equalities and sargable predicates
#             in disjunctive parts of the WHERE condition 
#

CREATE TABLE t1 (
  c1 int(11) NOT NULL AUTO_INCREMENT,
  c2 varchar(1000) DEFAULT NULL,
  c3 bigint(20) DEFAULT NULL,
  c4 bigint(20) DEFAULT NULL,
  PRIMARY KEY (c1)
) charset utf8mb4;

ANALYZE TABLE t1;
--replace_column 9 # 12 #
EXPLAIN
SELECT  join_2.c1  
FROM 
	t1 AS join_0, 
	t1 AS join_1, 
	t1 AS join_2, 
	t1 AS join_3, 
	t1 AS join_4, 
	t1 AS join_5, 
	t1 AS join_6, 
	t1 AS join_7
WHERE 
	join_0.c1=join_1.c1  AND 
	join_1.c1=join_2.c1  AND 
	join_2.c1=join_3.c1  AND 
	join_3.c1=join_4.c1  AND 
	join_4.c1=join_5.c1  AND 
	join_5.c1=join_6.c1  AND 
	join_6.c1=join_7.c1 
         OR 
	join_0.c2 < '?'  AND 
	join_1.c2 < '?'  AND
	join_2.c2 > '?'  AND
	join_2.c2 < '!'  AND
	join_3.c2 > '?'  AND 
	join_4.c2 = '?'  AND 
	join_5.c2 <> '?' AND
	join_6.c2 <> '?' AND 
	join_7.c2 >= '?' AND
        join_0.c1=join_1.c1  AND 
	join_1.c1=join_2.c1  AND 
        join_2.c1=join_3.c1  AND
	join_3.c1=join_4.c1  AND 
	join_4.c1=join_5.c1  AND 
	join_5.c1=join_6.c1  AND 
	join_6.c1=join_7.c1
GROUP BY 
	join_3.c1,
	join_2.c1,
	join_7.c1,
	join_1.c1,
	join_0.c1;

--skip_if_hypergraph   # Non-tree EXPLAIN is skipped, so need to skip this, too.
SHOW WARNINGS;

DROP TABLE t1;

#
# Bug #27695: Misleading warning when declaring all space column names and
#             truncation of one-space column names to zero length names.
#

--disable_ps_protocol
SELECT 1 AS ` `;
SELECT 1 AS `  `;
SELECT 1 AS ` x`;
--enable_ps_protocol

--error 1166
CREATE VIEW v1 AS SELECT 1 AS ``;

--error 1166
CREATE VIEW v1 AS SELECT 1 AS ` `;

--error 1166
CREATE VIEW v1 AS SELECT 1 AS `  `;

--error 1166
CREATE VIEW v1 AS SELECT (SELECT 1 AS `  `);

CREATE VIEW v1 AS SELECT 1 AS ` x`;
SELECT `x` FROM v1;

--error 1166
ALTER VIEW v1 AS SELECT 1 AS ` `;

DROP VIEW v1;

#
# Bug#31800: Date comparison fails with timezone and slashes for greater
#            than comparison
#

# On DATETIME-like literals with trailing garbage, BETWEEN fudged in a
# DATETIME comparator, while greater/less-than used bin-string comparisons.
# Should correctly be compared as DATE or DATETIME, but throw a warning:

select str_to_date('2007-10-09','%Y-%m-%d') between '2007/10/01 00:00:00 GMT'
                                                and '2007/10/20 00:00:00 GMT';
select str_to_date('2007-10-09','%Y-%m-%d') > '2007/10/01 00:00:00 GMT-6';
--error ER_WRONG_VALUE
select str_to_date('2007-10-09','%Y-%m-%d') <= '2007/10/2000:00:00 GMT-6';

# We have all we need -- and trailing garbage:
# (leaving out a leading zero in first example to prove it's a
# value-comparison, not a string-comparison!)
select str_to_date('2007-10-01','%Y-%m-%d') = '2007-10-1 00:00:00 GMT-6';
select str_to_date('2007-10-01','%Y-%m-%d') = '2007-10-01 x00:00:00 GMT-6';
select str_to_date('2007-10-01','%Y-%m-%d %H:%i:%s') = '2007-10-01 00:00:00 GMT-6';
select str_to_date('2007-10-01','%Y-%m-%d %H:%i:%s') = '2007-10-01 00:x00:00 GMT-6';
# no time at all:
select str_to_date('2007-10-01','%Y-%m-%d %H:%i:%s') = '2007-10-01 x12:34:56 GMT-6';
# partial time:
select str_to_date('2007-10-01 12:34:00','%Y-%m-%d %H:%i:%s') = '2007-10-01 12:34x:56 GMT-6';
# fail, different second part:
select str_to_date('2007-10-01 12:34:56','%Y-%m-%d %H:%i:%s') = '2007-10-01 12:34x:56 GMT-6';
# correct syntax, no trailing nonsense -- this one must throw no warning:
select str_to_date('2007-10-01 12:34:56','%Y-%m-%d %H:%i:%s') = '2007-10-01 12:34:56';
# no warning, but failure (different hour parts):
select str_to_date('2007-10-01','%Y-%m-%d') = '2007-10-01 12:00:00';
# succeed:
select str_to_date('2007-10-01 12','%Y-%m-%d %H') = '2007-10-01 12:00:00';
# succeed, but warn for "trailing garbage" (":34"):
select str_to_date('2007-10-01 12:34','%Y-%m-%d %H') = '2007-10-01 12:00:00';
# invalid date (Feb 30) succeeds because date comparison fails and
# it falls back to string comparison
--error ER_WRONG_VALUE
select str_to_date('2007-02-30 12:34','%Y-%m-%d %H:%i') = '2007-02-30 12:34:00';
# 0-day for both, just works in default SQL mode.
--error ER_WRONG_VALUE
select str_to_date('2007-10-00 12:34','%Y-%m-%d %H:%i') = '2007-10-00 12:34';
# 0-day, succeed
select str_to_date('2007-10-00','%Y-%m-%d') between '2007/09/01 00:00:00'
                                                and '2007/10/20 00:00:00';
set SQL_MODE=TRADITIONAL;
# 0-day throws warning in traditional mode, and fails
--error ER_WRONG_VALUE
select str_to_date('2007-10-00 12:34','%Y-%m-%d %H:%i') = '2007-10-00 12:34';
--error ER_WRONG_VALUE
select str_to_date('2007-10-01 12:34','%Y-%m-%d %H:%i') = '2007-10-00 12:34';
# different code-path: get_datetime_value() with 0-day
select str_to_date('2007-10-00 12:34','%Y-%m-%d %H:%i') = '2007-10-01 12:34';
select str_to_date('2007-10-00','%Y-%m-%d') between '2007/09/01'
                                                and '2007/10/20';
set SQL_MODE=DEFAULT;
select str_to_date('2007-10-00','%Y-%m-%d') between '' and '2007/10/20';
select str_to_date('','%Y-%m-%d') between '2007/10/01' and '2007/10/20';
select str_to_date('','%Y-%m-%d %H:%i') = '2007-10-01 12:34';
select str_to_date(NULL,'%Y-%m-%d %H:%i') = '2007-10-01 12:34';
--error ER_WRONG_VALUE
select str_to_date('2007-10-00 12:34','%Y-%m-%d %H:%i') = '';

--error ER_WRONG_VALUE
select str_to_date('1','%Y-%m-%d') = '1';
--error ER_WRONG_VALUE
select str_to_date('1','%Y-%m-%d') = '1';
--error ER_WRONG_VALUE
select str_to_date('','%Y-%m-%d') = '';

# these three should work!
select str_to_date('1000-01-01','%Y-%m-%d') between '0000-00-00' and NULL;
select str_to_date('1000-01-01','%Y-%m-%d') between NULL and '2000-00-00';
select str_to_date('1000-01-01','%Y-%m-%d') between NULL and NULL;

#
# Bug #30666: Incorrect order when using range conditions on 2 tables or more
#

CREATE TABLE t1 (c11 INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY);
CREATE TABLE t2 (c21 INT UNSIGNED NOT NULL, 
                 c22 INT DEFAULT NULL, 
                 KEY(c21, c22));
CREATE TABLE t3 (c31 INT UNSIGNED NOT NULL DEFAULT 0, 
                 c32 INT DEFAULT NULL, 
                 c33 INT NOT NULL, 
                 c34 INT UNSIGNED DEFAULT 0,
                 KEY (c33, c34, c32));

INSERT INTO t1 values (),(),(),(),();
INSERT INTO t2 SELECT a.c11, b.c11 FROM t1 a, t1 b;
INSERT INTO t3 VALUES (1, 1, 1, 0), 
                      (2, 2, 0, 0), 
                      (3, 3, 1, 0), 
                      (4, 4, 0, 0), 
                      (5, 5, 1, 0);

# Show that ORDER BY produces the correct results order
SELECT c32 FROM t1, t2, t3 WHERE t1.c11 IN (1, 3, 5) AND 
                                 t3.c31 = t1.c11 AND t2.c21 = t1.c11 AND 
                                 t3.c33 = 1 AND t2.c22 in (1, 3) 
                           ORDER BY c32; 

# Show that ORDER BY DESC produces the correct results order
SELECT c32 FROM t1, t2, t3 WHERE t1.c11 IN (1, 3, 5) AND 
                                 t3.c31 = t1.c11 AND t2.c21 = t1.c11 AND 
                                 t3.c33 = 1 AND t2.c22 in (1, 3) 
                           ORDER BY c32 DESC; 

DROP TABLE t1, t2, t3;
###########################################################################

--echo
--echo #
--echo # Bug#30736: Row Size Too Large Error Creating a Table and
--echo # Inserting Data.
--echo #

--disable_warnings
DROP TABLE IF EXISTS t1;
DROP TABLE IF EXISTS t2;
--enable_warnings

--echo

CREATE TABLE t1(
  c1 DECIMAL(10, 2),
  c2 FLOAT);

--echo

INSERT INTO t1 VALUES (0, 1), (2, 3), (4, 5);

--echo

CREATE TABLE t2(
  c3 DECIMAL(10, 2))
  SELECT
    c1 * c2 AS c3
  FROM t1;

--echo

SELECT * FROM t1;

--echo

SELECT * FROM t2;

--echo

DROP TABLE t1;
DROP TABLE t2;

--echo

###########################################################################

#
# Bug #32335: Error on BIGINT > NULL + 1 
#

CREATE TABLE t1 (c1 BIGINT NOT NULL);
INSERT INTO t1 (c1) VALUES (1);
SELECT * FROM t1 WHERE c1 > NULL + 1;
DROP TABLE t1;

--echo

###########################################################################

#
# Bug #33764: Wrong result with IN(), CONCAT() and implicit type conversion
#

CREATE TABLE t1 (a VARCHAR(10) NOT NULL PRIMARY KEY);
INSERT INTO t1 (a) VALUES ('foo0'), ('bar0'), ('baz0');
SELECT * FROM t1 WHERE a IN (CONCAT('foo', 0), 'bar');
DROP TABLE t1;

#
# Bug #32942 now() - interval '7200' second is NOT pre-calculated, causing "full table scan"
#

CREATE TABLE t1 (a INT, b INT);
CREATE TABLE t2 (a INT, c INT, KEY(a));

INSERT INTO t1 VALUES (1, 1), (2, 2);
INSERT INTO t2 VALUES (1, 1), (1, 2), (1, 3), (1, 4), (1, 5),
                      (2, 1), (2, 2), (2, 3), (2, 4), (2, 5),
                      (3, 1), (3, 2), (3, 3), (3, 4), (3, 5),
                      (4, 1), (4, 2), (4, 3), (4, 4), (4, 5);

FLUSH STATUS;
SELECT DISTINCT b FROM t1 LEFT JOIN t2 USING(a) WHERE c <= 3;
--skip_if_hypergraph  # Depends on the query plan.
SHOW STATUS LIKE 'Handler_read%';
DROP TABLE t1, t2;

#
# Bug#40953 SELECT query throws "ERROR 1062 (23000): Duplicate entry..." error
#
CREATE TABLE t1 (f1 bigint(20) NOT NULL default '0',
                 f2 int(11) NOT NULL default '0',
                 f3 bigint(20) NOT NULL default '0',
                 f4 varchar(255) NOT NULL default '',
                 PRIMARY KEY (f1),
                 KEY key1 (f4),
                 KEY key2 (f2)) charset latin1;
CREATE TABLE t2 (f1 int(11) NOT NULL default '0',
                 f2 enum('A1','A2','A3') NOT NULL default 'A1',
                 f3 int(11) NOT NULL default '0',
                 PRIMARY KEY (f1),
                 KEY key1 (f3)) charset latin1;
CREATE TABLE t3 (f1 bigint(20) NOT NULL default '0',
                 f2 datetime NOT NULL default '1980-01-01 00:00:00',
                 PRIMARY KEY (f1)) charset latin1;

insert into t1 values (1, 1, 1, 'abc');
insert into t1 values (2, 1, 2, 'def');
insert into t1 values (3, 1, 2, 'def');
insert into t2 values (1, 'A1', 1);
insert into t3 values (1, '1980-01-01');

--sorted_result
SELECT a.f3, cr.f4, count(*) count
FROM t2 a
STRAIGHT_JOIN t1 cr ON cr.f2 = a.f1
LEFT JOIN
(t1 cr2
  JOIN t3 ae2 ON cr2.f3 = ae2.f1
) ON a.f1 = cr2.f2 AND ae2.f2 < now() - INTERVAL 7 DAY AND
cr.f4 = cr2.f4
GROUP BY a.f3, cr.f4;

drop table t1, t2, t3;


#
# Bug #40925: Equality propagation takes non indexed attribute
#

CREATE TABLE t1 (a INT KEY, b INT);
INSERT INTO t1 VALUES (1,1), (2,2), (3,3), (4,4);
ANALYZE TABLE t1;
EXPLAIN SELECT a, b FROM t1 WHERE a > 1 AND a = b LIMIT 2;
EXPLAIN SELECT a, b FROM t1 WHERE a > 1 AND b = a LIMIT 2;

DROP TABLE t1;


--echo #
--echo # Bug#47019: Assertion failed: 0, file .\rt_mbr.c, line 138 when 
--echo # forcing a spatial index
--echo #
CREATE TABLE t1(a LINESTRING NOT NULL SRID 0, SPATIAL KEY(a));
INSERT INTO t1 VALUES
  (ST_GEOMFROMTEXT('LINESTRING(-1 -1, 1 -1, -1 -1, -1 1, 1 1)')),
  (ST_GEOMFROMTEXT('LINESTRING(-1 -1, 1 -1, -1 -1, -1 1, 1 1)'));
EXPLAIN SELECT 1 FROM t1 NATURAL LEFT JOIN t1 AS t2;
SELECT 1 FROM t1 NATURAL LEFT JOIN t1 AS t2;
EXPLAIN SELECT 1 FROM t1 NATURAL LEFT JOIN t1 AS t2 FORCE INDEX(a);
SELECT 1 FROM t1 NATURAL LEFT JOIN t1 AS t2 FORCE INDEX(a);
DROP TABLE t1;


--echo #
--echo # Bug #48291 : crash with row() operator,select into @var, and 
--echo #   subquery returning multiple rows
--echo #

CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES (2),(3);

--echo # Should not crash
--error ER_SUBQUERY_NO_1_ROW
SELECT 1 FROM t1 WHERE a <> 1 AND NOT
ROW(1,a) <=> ROW(1,(SELECT 1 FROM t1))
INTO @var0;

DROP TABLE t1;
 
--echo #
--echo # Bug #48458: simple query tries to allocate enormous amount of
--echo #   memory
--echo #
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1(a INT NOT NULL, b YEAR);
INSERT INTO t1 VALUES ();
CREATE TABLE t2(c INT);
--echo # Should not err out because of out-of-memory
SELECT 1 FROM t2 JOIN t1 ON 1=1
  WHERE a != '1' AND NOT a >= b OR NOT ROW(b,a )<> ROW(a,a);
DROP TABLE t1,t2;
SET sql_mode = default;

--echo #
--echo # Bug #49199: Optimizer handles incorrectly: 
--echo # field='const1' AND field='const2' in some cases
--echo
CREATE TABLE t1(a DATETIME NOT NULL);
INSERT INTO t1 VALUES('2001-01-01');
SELECT * FROM t1 WHERE a='2001-01-01' AND a='2001-01-01 00:00:00';
EXPLAIN SELECT * FROM t1 WHERE a='2001-01-01' AND a='2001-01-01 00:00:00';
DROP TABLE t1;

CREATE TABLE t1(a DATE NOT NULL);
INSERT INTO t1 VALUES('2001-01-01');
SELECT * FROM t1 WHERE a='2001-01-01' AND a='2001-01-01 00:00:00';
EXPLAIN SELECT * FROM t1 WHERE a='2001-01-01' AND a='2001-01-01 00:00:00';
DROP TABLE t1;

CREATE TABLE t1(a TIMESTAMP NOT NULL NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP);
INSERT INTO t1 VALUES('2001-01-01');
SELECT * FROM t1 WHERE a='2001-01-01' AND a='2001-01-01 00:00:00';
EXPLAIN SELECT * FROM t1 WHERE a='2001-01-01' AND a='2001-01-01 00:00:00';
DROP TABLE t1;

CREATE TABLE t1(a DATETIME NOT NULL, b DATE NOT NULL);
INSERT INTO t1 VALUES('2001-01-01', '2001-01-01');
SELECT * FROM t1 WHERE a='2001-01-01' AND a=b AND b='2001-01-01 00:00:00';
EXPLAIN SELECT * FROM t1 WHERE a='2001-01-01' AND a=b AND b='2001-01-01 00:00:00';
DROP TABLE t1;

CREATE TABLE t1(a DATETIME NOT NULL, b VARCHAR(20) NOT NULL) charset utf8mb4;
INSERT INTO t1 VALUES('2001-01-01', '2001-01-01');
SELECT * FROM t1 WHERE a='2001-01-01' AND a=b AND b='2001-01-01 00:00:00';
EXPLAIN SELECT * FROM t1 WHERE a='2001-01-01' AND a=b AND b='2001-01-01 00:00:00';

SELECT * FROM t1 WHERE a='2001-01-01 00:00:00' AND a=b AND b='2001-01-01';
EXPLAIN SELECT * FROM t1 WHERE a='2001-01-01 00:00:00' AND a=b AND b='2001-01-01';
DROP TABLE t1;

CREATE TABLE t1(a DATETIME NOT NULL, b DATE NOT NULL);
INSERT INTO t1 VALUES('2001-01-01', '2001-01-01');
SELECT x.a, y.a, z.a FROM t1 x 
  JOIN t1 y ON x.a=y.a 
  JOIN t1 z ON y.a=z.a 
  WHERE x.a='2001-01-01' AND z.a='2001-01-01 00:00:00';
EXPLAIN SELECT x.a, y.a, z.a FROM t1 x
  JOIN t1 y ON x.a=y.a 
  JOIN t1 z ON y.a=z.a 
  WHERE x.a='2001-01-01' AND z.a='2001-01-01 00:00:00';
DROP TABLE t1;


--echo #
--echo # Bug #49897: crash in ptr_compare when char(0) NOT NULL 
--echo # column is used for ORDER BY
--echo #
SET @old_sort_buffer_size= @@session.sort_buffer_size;
SET @@sort_buffer_size= 40000;
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1(a CHAR(0) NOT NULL);
--disable_warnings
INSERT INTO t1 VALUES (0), (0), (0);
--enable_warnings
INSERT INTO t1 SELECT t11.a FROM t1 t11, t1 t12;
INSERT INTO t1 SELECT t11.a FROM t1 t11, t1 t12;
INSERT INTO t1 SELECT t11.a FROM t1 t11, t1 t12;
ANALYZE TABLE t1;
EXPLAIN SELECT a FROM t1 ORDER BY a;
--disable_result_log
SELECT a FROM t1 ORDER BY a;
--enable_result_log
DROP TABLE t1;

CREATE TABLE t1(a CHAR(0) NOT NULL, b CHAR(0) NOT NULL, c int);
--disable_warnings
INSERT INTO t1 VALUES (0, 0, 0), (0, 0, 2), (0, 0, 1);
--enable_warnings

--echo # Since ANALYZE TABLE only reads a subset of the data, the statistics for
--echo # table t1 depends on the row order. And since the INSERT INTO ... SELECT
--echo # may be executed using different execution plans, we've added ORDER BY
--echo # to ensure that we rows has the same order every time. If not, the
--echo # estimated number of rows in EXPLAIN may change on different platforms.
--echo # Note that the tables may MYISAM, since many of the test files that
--echo # includes this file forces MYISAM as the default storage engine.

INSERT INTO t1 SELECT t11.a, t11.b, t11.c FROM t1 t11, t1 t12
  ORDER BY t11.a, t11.b, t11.c;
INSERT INTO t1 SELECT t11.a, t11.b, t11.c FROM t1 t11, t1 t12
  ORDER BY t11.a, t11.b, t11.c;
INSERT INTO t1 SELECT t11.a, t11.b, t11.c FROM t1 t11, t1 t12
  ORDER BY t11.a, t11.b, t11.c;
ANALYZE TABLE t1;
EXPLAIN SELECT a FROM t1 ORDER BY a LIMIT 5;
SELECT a FROM t1 ORDER BY a LIMIT 5;
EXPLAIN SELECT * FROM t1 ORDER BY a, b LIMIT 5;
--replace_regex /0$/2/ /1$/2/  # The order of the rows along the c column is undefined.
SELECT * FROM t1 ORDER BY a, b LIMIT 5;
EXPLAIN SELECT * FROM t1 ORDER BY a, b, c LIMIT 5;
SELECT * FROM t1 ORDER BY a, b, c LIMIT 5;
EXPLAIN SELECT * FROM t1 ORDER BY c, a LIMIT 5;
SELECT * FROM t1 ORDER BY c, a LIMIT 5;

SET @@sort_buffer_size= @old_sort_buffer_size;
DROP TABLE t1;
SET sql_mode = default;

--echo End of 5.0 tests

#
# Bug #30639: limit offset,rowcount wraps when rowcount >= 2^32 in windows
#
create table t1(a INT, KEY (a));
INSERT INTO t1 VALUES (1),(2),(3),(4),(5);
SELECT a FROM t1 ORDER BY a LIMIT 2;
SELECT a FROM t1 ORDER BY a LIMIT 2,4294967296;
SELECT a FROM t1 ORDER BY a LIMIT 2,4294967297;
DROP TABLE t1;

#
# Bug #37936: ASSERT_COLUMN_MARKED_FOR_WRITE in Field_datetime::store ,
# Field_varstring::store
#

CREATE TABLE A (date_key date);

CREATE TABLE C (
  pk int,
  int_nokey int,
  int_key int,
  date_key date NOT NULL,
  date_nokey date,
  varchar_key varchar(1)
);

INSERT IGNORE INTO C VALUES 
(1,1,1,'0000-00-00',NULL,NULL),
(1,1,1,'0000-00-00',NULL,NULL);

SELECT 1 FROM C WHERE pk > ANY (SELECT 1 FROM C);

SELECT COUNT(DISTINCT 1) FROM C 
  WHERE date_key = (SELECT 1 FROM A WHERE C.date_key IS NULL) GROUP BY pk;
--error ER_WRONG_VALUE
SELECT date_nokey FROM C 
  WHERE int_key IN (SELECT 1 FROM A) 
  HAVING date_nokey = '10:41:7' 
  ORDER BY date_key;

DROP TABLE A,C;

#
# Bug #42957: no results from 
# select where .. (col=col and col=col) or ... (false expression)
#
CREATE TABLE t1 (a INT NOT NULL, b INT);
INSERT INTO t1 VALUES (1, 1);
EXPLAIN SELECT * FROM t1 WHERE (a=a AND a=a) OR b > 2;
SELECT * FROM t1 WHERE (a=a AND a=a) OR b > 2;
DROP TABLE t1;

CREATE TABLE t1 (a INT NOT NULL, b INT NOT NULL, c INT NOT NULL);
EXPLAIN SELECT * FROM t1 WHERE (a=a AND b=b AND c=c) OR b > 20;
EXPLAIN SELECT * FROM t1 WHERE (a=a AND a=a AND b=b) OR b > 20;
EXPLAIN SELECT * FROM t1 WHERE (a=a AND b=b AND a=a) OR b > 20;
DROP TABLE t1;


--echo #
--echo # Bug#45266: Uninitialized variable lead to an empty result.
--echo #
--disable_warnings
drop table if exists A,AA,B,BB;
CREATE TABLE `A` (
  `pk` int(11) NOT NULL AUTO_INCREMENT,
  `date_key` date NOT NULL,
  `date_nokey` date NOT NULL,
  `datetime_key` datetime NOT NULL,
  `int_nokey` int(11) NOT NULL,
  `time_key` time NOT NULL,
  `time_nokey` time NOT NULL,
  PRIMARY KEY (`pk`),
  KEY `date_key` (`date_key`),
  KEY `time_key` (`time_key`),
  KEY `datetime_key` (`datetime_key`)
);

CREATE TABLE `AA` (
  `pk` int(11) NOT NULL AUTO_INCREMENT,
  `int_nokey` int(11) NOT NULL,
  `time_key` time NOT NULL,
  KEY `time_key` (`time_key`),
  PRIMARY KEY (`pk`)
);

CREATE TABLE `B` (
  `date_nokey` date NOT NULL,
  `date_key` date NOT NULL,
  `time_key` time NOT NULL,
  `datetime_nokey` datetime NOT NULL,
  `varchar_key` varchar(1) NOT NULL,
  KEY `date_key` (`date_key`),
  KEY `time_key` (`time_key`),
  KEY `varchar_key` (`varchar_key`)
);

INSERT IGNORE INTO `B` VALUES ('2003-07-28','2003-07-28','15:13:38','0000-00-00 00:00:00','f'),('0000-00-00','0000-00-00','00:05:48','2004-07-02 14:34:13','x');

CREATE TABLE `BB` (
  `pk` int(11) NOT NULL AUTO_INCREMENT,
  `int_nokey` int(11) NOT NULL,
  `date_key` date NOT NULL,
  `varchar_nokey` varchar(1) NOT NULL,
  `date_nokey` date NOT NULL,
  PRIMARY KEY (`pk`),
  KEY `date_key` (`date_key`)
);

INSERT IGNORE INTO `BB` VALUES (10,8,'0000-00-00','i','0000-00-00'),(11,0,'2005-08-18','','2005-08-18');
# Test #1
SELECT table1 . `pk` AS field1 
  FROM 
    (BB AS table1 INNER JOIN 
      (AA AS table2 STRAIGHT_JOIN A AS table3 
        ON ( table3 . `date_key` = table2 . `pk` ))
       ON ( table3 . `datetime_key` = table2 . `int_nokey` ))
  WHERE  ( table3 . `date_key` <= 4 AND table2 . `pk` = table1 . `varchar_nokey`)
  GROUP BY field1 ;

SELECT table3 .`date_key` field1
  FROM
    B table1 LEFT JOIN B table3 JOIN
      (BB table6 JOIN A table7 ON table6 .`varchar_nokey`)
       ON table6 .`int_nokey` ON table6 .`date_key`
  WHERE  NOT ( table1 .`varchar_key`  AND table7 .`pk`) GROUP  BY field1;

# Test #2
SELECT table4 . `time_nokey` AS field1 FROM 
  (AA AS table1 CROSS JOIN 
    (AA AS table2 STRAIGHT_JOIN 
      (B AS table3 STRAIGHT_JOIN A AS table4 
       ON ( table4 . `date_key` = table3 . `time_key` ))
     ON ( table4 . `pk` = table3 . `date_nokey` ))
   ON ( table4 . `time_key` = table3 . `datetime_nokey` ))
  WHERE  ( table4 . `time_key` < table1 . `time_key` AND
            table1 . `int_nokey` != 'f')
  GROUP BY field1  ORDER BY field1 , field1;

--sorted_result
SELECT table1 .`time_key` field2  FROM B table1  LEFT JOIN  BB JOIN A table5 ON table5 .`date_nokey`  ON table5 .`int_nokey` GROUP  BY field2;
--enable_warnings

drop table A,AA,B,BB;
--echo #end of test for bug#45266

--echo #
--echo # Bug#33546: Slowdown on re-evaluation of constant expressions.
--echo #
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3), (4), (5), (6), (7), (8), (9), (10);
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (2);
SELECT * FROM t1 WHERE a = 1 + 1;
ANALYZE TABLE t1, t2;
EXPLAIN SELECT * FROM t1 WHERE a = 1 + 1;
SELECT * FROM t1 HAVING a = 1 + 1;
EXPLAIN SELECT * FROM t1 HAVING a = 1 + 1;
SELECT * FROM t1, t2 WHERE a = b + (1 + 1);
EXPLAIN SELECT * FROM t1, t2 WHERE a = b + (1 + 1);
SELECT * FROM t2 LEFT JOIN t1 ON a = b + 1;
EXPLAIN SELECT * FROM t2 LEFT JOIN t1 ON a = b + 1;
EXPLAIN SELECT * FROM t1 WHERE a > UNIX_TIMESTAMP('2009-03-10 00:00:00');

delimiter |;
CREATE FUNCTION f1() RETURNS INT DETERMINISTIC
BEGIN
  SET @cnt := @cnt + 1;
  RETURN 1;
END;|
delimiter ;|

SET @cnt := 0;
SELECT * FROM t1 WHERE a = f1();
--skip_if_hypergraph  # Depends on the query plan.
SELECT @cnt;
EXPLAIN SELECT * FROM t1 WHERE a = f1();
DROP TABLE t1, t2;
DROP FUNCTION f1;
--echo # End of bug#33546

--echo # 
--echo # BUG#48052: Valgrind warning - uninitialized value in init_read_record()
--echo # 

--echo # Disable Index condition pushdown
--replace_column 1 #
SELECT @old_optimizer_switch:=@@optimizer_switch;
--disable_query_log
if (`select locate('index_condition_pushdown', @@optimizer_switch) > 0`) 
{
  set optimizer_switch='index_condition_pushdown=off';
}
--enable_query_log

CREATE TABLE t1 (
  pk int(11) NOT NULL,
  i int(11) DEFAULT NULL,
  v varchar(1) DEFAULT NULL,
  PRIMARY KEY (pk)
);

INSERT INTO t1 VALUES (2,7,'m');
INSERT INTO t1 VALUES (3,9,'m');

SELECT  v
FROM t1
WHERE NOT pk > 0  
HAVING v <= 't' 
ORDER BY pk;

--echo # Restore old value for Index condition pushdown
SET SESSION optimizer_switch=@old_optimizer_switch;

DROP TABLE t1;

--echo #
--echo # Bug#49489 Uninitialized cache led to a wrong result.
--echo #
CREATE TABLE t1(c1 DOUBLE(5,4));
INSERT INTO t1 VALUES (9.1234);
SELECT * FROM t1 WHERE c1 < 9.12345;
DROP TABLE t1;
--echo # End of test for bug#49489.


--echo #
--echo # Bug #49517: Inconsistent behavior while using 
--echo # NULLable BIGINT and INT columns in comparison
--echo #
CREATE TABLE t1(a BIGINT UNSIGNED NOT NULL, b BIGINT NULL, c INT NULL);
INSERT INTO t1 VALUES(105, NULL, NULL);
SELECT * FROM t1 WHERE b < 102;
SELECT * FROM t1 WHERE c < 102;
SELECT * FROM t1 WHERE 102 < b;
SELECT * FROM t1 WHERE 102 < c;
DROP TABLE t1;


--echo #
--echo # Bug #54459: Assertion failed: param.sort_length, 
--echo # file .\filesort.cc, line 149 (part II)
--echo #
CREATE TABLE t1(a ENUM('') NOT NULL) charset latin1;
INSERT INTO t1 VALUES (), (), ();
ANALYZE TABLE t1;
EXPLAIN SELECT 1 FROM t1 ORDER BY a COLLATE latin1_german2_ci;
SELECT 1 FROM t1 ORDER BY a COLLATE latin1_german2_ci;
DROP TABLE t1;



--echo #
--echo # Bug #58422: Incorrect result when OUTER JOIN'ing 
--echo # with an empty table
--echo #

CREATE TABLE t_empty(pk INT PRIMARY KEY, i INT);
CREATE TABLE t1(pk INT PRIMARY KEY, i INT);
INSERT INTO t1 VALUES (1,1), (2,2), (3,3);
CREATE TABLE t2(pk INT PRIMARY KEY, i INT) ;
INSERT INTO t2 VALUES (1,1), (2,2), (3,3);
ANALYZE TABLE t_empty, t1, t2;
EXPLAIN
SELECT *
  FROM 
    t1
  LEFT OUTER JOIN
    (t2 INNER JOIN t_empty ON TRUE)
  ON t1.pk=t2.pk
  WHERE t2.pk <> 2;

SELECT *
  FROM 
    t1
  LEFT OUTER JOIN
    (t2 INNER JOIN t_empty ON TRUE)
  ON t1.pk=t2.pk
  WHERE t2.pk <> 2;


EXPLAIN
SELECT *
  FROM 
    t1
  LEFT OUTER JOIN
    (t2 CROSS JOIN t_empty)
  ON t1.pk=t2.pk
  WHERE t2.pk <> 2;

SELECT *
  FROM 
    t1
  LEFT OUTER JOIN
    (t2 CROSS JOIN t_empty)
  ON t1.pk=t2.pk
  WHERE t2.pk <> 2;


EXPLAIN
SELECT *
  FROM 
    t1
  LEFT OUTER JOIN
    (t2 INNER JOIN t_empty ON t_empty.i=t2.i)
  ON t1.pk=t2.pk
  WHERE t2.pk <> 2;

SELECT *
  FROM 
    t1
  LEFT OUTER JOIN
    (t2 INNER JOIN t_empty ON t_empty.i=t2.i)
  ON t1.pk=t2.pk
  WHERE t2.pk <> 2;



DROP TABLE t1,t2,t_empty;


--echo End of 5.1 tests

--echo #
--echo # Bug#45227: Lost HAVING clause led to a wrong result.
--echo #
CREATE TABLE `cc` (
  `int_nokey` int(11) NOT NULL,
  `int_key` int(11) NOT NULL,
  `varchar_key` varchar(1) NOT NULL,
  `varchar_nokey` varchar(1) NOT NULL,
  KEY `int_key` (`int_key`),
  KEY `varchar_key` (`varchar_key`)
);
INSERT INTO `cc` VALUES
(0,8,'q','q'),(5,8,'m','m'),(7,3,'j','j'),(1,2,'z','z'),(8,2,'a','a'),(2,6,'',''),(1,8,'e'
,'e'),(8,9,'t','t'),(5,2,'q','q'),(4,6,'b','b'),(5,5,'w','w'),(3,2,'m','m'),(0,4,'x','x'),
(8,9,'',''),(0,6,'w','w'),(4,5,'x','x'),(0,0,'e','e'),(0,0,'e','e'),(2,8,'p','p'),(0,0,'x'
,'x');
EXPLAIN SELECT `varchar_nokey` g1  FROM cc  WHERE `int_nokey` AND `int_key`  <= 4
  HAVING g1  ORDER  BY `varchar_key` LIMIT  6   ;

--sorted_result  # Actually just to sort the warnings.
SELECT `varchar_nokey` g1  FROM cc  WHERE `int_nokey` AND `int_key`  <= 4
  HAVING g1  ORDER  BY `varchar_key` LIMIT  6   ;

DROP TABLE cc;
--echo # End of test#45227

--echo #
--echo # Bug#54515: Crash in opt_range.cc::get_best_group_min_max on 
--echo #            SELECT from VIEW with GROUP BY
--echo #

CREATE TABLE t1 (
  col_int_key int DEFAULT NULL,
  KEY int_key (col_int_key)
) ;

INSERT INTO t1 VALUES (1),(2);

CREATE VIEW view_t1 AS 
  SELECT t1.col_int_key AS col_int_key
  FROM t1;

SELECT col_int_key FROM view_t1 GROUP BY col_int_key;

DROP VIEW view_t1;
DROP TABLE t1;

--echo # End of test BUG#54515

--echo #
--echo # Bug #57203 Assertion `field_length <= 255' failed.
--echo #
--error ER_WRONG_ARGUMENTS
SELECT coalesce((avg(distinct (ST_geomfromtext("point(25379 -22010)"))))) 
UNION ALL 
SELECT coalesce((avg(distinct (ST_geomfromtext("point(25379 -22010)")))))
AS foo
;

CREATE table t1(a text);
INSERT INTO t1 VALUES (''), ('');

--source include/turn_off_only_full_group_by.inc
SELECT avg(distinct(t1.a)) FROM t1, t1 t2
GROUP BY t2.a ORDER BY t1.a;
--source include/restore_sql_mode_after_turn_off_only_full_group_by.inc

DROP TABLE t1;

--echo # End of test BUG#57203

--echo #
--echo # Bug#63020: Function "format"'s 'locale' argument is not considered
--echo #	     when creating a "view'
--echo #

CREATE TABLE t1 (f1 DECIMAL(10,2));
INSERT INTO t1 VALUES (11.67),(17865.3),(12345678.92);
CREATE VIEW view_t1 AS SELECT FORMAT(f1,1,'sk_SK') AS f1 FROM t1;
SHOW CREATE VIEW view_t1;
SELECT * FROM view_t1;

DROP TABLE t1;
DROP VIEW view_t1;

--echo # End of test  BUG#63020

--echo #
--echo # Bug #13571700 TINYBLOB NOT NULL, CRASH IN PROTOCOL::NET_STORE_DATA
--echo #

CREATE TABLE t1 (a TINYBLOB NOT NULL);

--source include/turn_off_only_full_group_by.inc
SELECT a, COUNT(*) FROM t1 WHERE 0;
--source include/restore_sql_mode_after_turn_off_only_full_group_by.inc

DROP TABLE t1;

--echo # End of test BUG#13571700

--echo #
--echo # Bug #18766378: CRASH IN ITEM_SUM_BIT::RESET_FIELD
--echo #
CREATE TABLE t1(b int);
CREATE TABLE t2(a int);
INSERT INTO t1 VALUES (),();
INSERT INTO t2 VALUES (),();
SELECT
( SELECT 1 FROM t1 GROUP BY a
  HAVING avg(distinct 1) ORDER BY max(a)
)
FROM  t1, t2
GROUP BY a;

DROP TABLE t1,t2;
--echo # End of test BUG#18766378

--echo #
--echo # WL#13002: RESULTSET DIFFERENT NUMBER OF ROWS
--echo #

#
# Verify that BKA does proper NULL rejection. Normally, we insert NULL checks in WHERE
# before the join, but occasionally, they can be optimized away as redundant due to
# other conditions.
#
# Note that this test will also be run under non-BKA conditions, since
# select.inc is run under many different optimizer switches. If so, naturally
# the query plan won't show BKA, but the test should of course still pass.
#

CREATE TABLE t1 (
  f1 INTEGER,
  f2 INTEGER,
  INDEX i1 (f2)
);

INSERT INTO t1 VALUES (NULL,1);
INSERT INTO t1 VALUES (2,NULL);
INSERT INTO t1 VALUES (3,1);
INSERT INTO t1 VALUES (4,6);
INSERT INTO t1 VALUES (NULL,5);
INSERT INTO t1 VALUES (NULL,NULL);
INSERT INTO t1 VALUES (13,1);
INSERT INTO t1 VALUES (NULL,0);
INSERT INTO t1 VALUES (5,2);
INSERT INTO t1 VALUES (NULL,8);
INSERT INTO t1 VALUES (NULL,7);
INSERT INTO t1 VALUES (NULL,NULL);
INSERT INTO t1 VALUES (NULL,NULL);
INSERT INTO t1 VALUES (NULL,4);
ANALYZE TABLE t1;

let $query =
  SELECT *
    FROM t1 AS alias1 LEFT JOIN t1 AS alias2 ON alias1.f1 = alias2.f2
    WHERE alias2.f1 NOT BETWEEN 4 AND 12;

--skip_if_hypergraph  # Depends on query plan.
eval EXPLAIN FORMAT=tree $query;  # Verify that we get BKA, and no NULL rejection in WHERE on alias1.
eval $query;

DROP TABLE t1;

-- disable_query_log
-- disable_result_log
SET @@GLOBAL.innodb_stats_transient_sample_pages= @innodb_stats_transient_sample_pages;
SET @@GLOBAL.innodb_stats_persistent_sample_pages= @innodb_stats_persistent_sample_pages;
-- enable_result_log
-- enable_query_log
