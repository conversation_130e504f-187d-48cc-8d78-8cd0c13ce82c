include/group_replication.inc [rpl_server_count=3]
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection server1]
[Setup XPlugin user on connection 'server1']
[Setup XPlugin user on connection 'server2']
[Setup XPlugin user on connection 'server3']

## Protocol GR notices enabled - verify delivery of the notices
##########################################################################

###
### Sets up the group with two servers: server1 and server2
###
connecting...
active session is now 'verify_1'
connecting...
active session is now 'verify_2'
connecting...
active session is now 'verify_3'
[connection verify_1]
[Enabling GR notifications]
[connection verify_2]
[Enabling GR notifications]
[connection verify_3]
[Enabling GR notifications]
connecting...
active session is now 'server_1'
connecting...
active session is now 'server_2'
connecting...
active session is now 'server_3'
[connection server_1]
RUN SET GLOBAL group_replication_enforce_update_everywhere_checks= OFF
RUN SET GLOBAL group_replication_single_primary_mode= ON
RUN SET GLOBAL group_replication_force_members=''
[connection server_2]
RUN SET GLOBAL group_replication_enforce_update_everywhere_checks= OFF
RUN SET GLOBAL group_replication_single_primary_mode= ON
RUN SET GLOBAL group_replication_force_members=''
[connection server_3]
RUN SET GLOBAL group_replication_enforce_update_everywhere_checks= OFF
RUN SET GLOBAL group_replication_single_primary_mode= ON
RUN SET GLOBAL group_replication_force_members=''
[connection server_3]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_2]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_1]
RUN SET GLOBAL group_replication_bootstrap_group= 0

### Scenario 1: SERVER BOOTSTRAPS+RECOVERS+PRIMARY ELECTION
### Expected:
### - Myself: 1 VIEW CHANGE, 2 STATE CHANGED, 1 ROLE CHANGED
### - Others: N/A
##########################################################################
[connection server_1]
RUN SET GLOBAL group_replication_bootstrap_group= 1
RUN SET GLOBAL group_replication_group_name='%GR_NAME%'
RUN START GROUP_REPLICATION
[waiting for member_state to be equal 'ONLINE']
[connection server_3]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_2]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_1]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_3]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_2]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_1]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[ASSERT view_change on 'verify_1' connection]
[ASSERT state_change on 'verify_1' connection]
[ASSERT state_change on 'verify_1' connection]
[ASSERT role_change on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_3' connection]

### Scenario 2: PRIMARY LEAVES the GROUP
### Expected:
### - Myself (old primary): 1 VIEW CHANGED, 1 STATE CHANGED, 1 ROLE CHANGED
### - Others (new primary): 1 VIEW CHANGED, 1 ROLE CHANGED
### - Others (secondary):   1 VIEW CHANGED, 1 ROLE CHANGED
##########################################################################
[connection server_2]
RUN SET GLOBAL group_replication_group_name='%GR_NAME%'
RUN START GROUP_REPLICATION
[waiting for member_state to be equal 'ONLINE']
[ASSERT view_change on 'verify_1' connection]
[ASSERT state_change on 'verify_1' connection]
[ASSERT state_change on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT view_change on 'verify_2' connection]
[ASSERT state_change on 'verify_2' connection]
[ASSERT state_change on 'verify_2' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_3' connection]
[connection server_3]
RUN SET GLOBAL group_replication_group_name='%GR_NAME%'
RUN START GROUP_REPLICATION
[waiting for member_state to be equal 'ONLINE']
[ASSERT view_change on 'verify_1' connection]
[ASSERT state_change on 'verify_1' connection]
[ASSERT state_change on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT view_change on 'verify_2' connection]
[ASSERT state_change on 'verify_2' connection]
[ASSERT state_change on 'verify_2' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT view_change on 'verify_3' connection]
[ASSERT state_change on 'verify_3' connection]
[ASSERT state_change on 'verify_3' connection]
[ASSERT no notices on 'verify_3' connection]
[connection server_1]
RUN STOP GROUP_REPLICATION
RUN SET GLOBAL read_only= FALSE
[ASSERT view_change on 'verify_1' connection]
[ASSERT state_change on 'verify_1' connection]
[ASSERT role_change on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[connection server_2]
[waiting for '2' members]
[ASSERT view_change on 'verify_2' connection]
[ASSERT role_change on 'verify_2' connection]
[ASSERT no notices on 'verify_2' connection]
[connection server_3]
[waiting for '2' members]
[ASSERT view_change on 'verify_3' connection]
[ASSERT role_change on 'verify_3' connection]
[ASSERT no notices on 'verify_3' connection]

###
### In X test cleanup
###
[connection server_2]
RUN STOP GROUP_REPLICATION
RUN SET GLOBAL read_only= FALSE
[connection server_3]
RUN STOP GROUP_REPLICATION
RUN SET GLOBAL read_only= FALSE
closing session server_3
Mysqlx.Ok {
  msg: "bye!"
}
ok

## X Protocol GR notices disabled - verify that X Plugin doesn't send the notices
##########################################################################

###
### Sets up the group with two servers: server1 and server2
###
connecting...
active session is now 'verify_1'
connecting...
active session is now 'verify_2'
connecting...
active session is now 'verify_3'
connecting...
active session is now 'server_1'
connecting...
active session is now 'server_2'
connecting...
active session is now 'server_3'
[connection server_1]
RUN SET GLOBAL group_replication_enforce_update_everywhere_checks= OFF
RUN SET GLOBAL group_replication_single_primary_mode= ON
RUN SET GLOBAL group_replication_force_members=''
[connection server_2]
RUN SET GLOBAL group_replication_enforce_update_everywhere_checks= OFF
RUN SET GLOBAL group_replication_single_primary_mode= ON
RUN SET GLOBAL group_replication_force_members=''
[connection server_3]
RUN SET GLOBAL group_replication_enforce_update_everywhere_checks= OFF
RUN SET GLOBAL group_replication_single_primary_mode= ON
RUN SET GLOBAL group_replication_force_members=''
[connection server_3]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_2]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_1]
RUN SET GLOBAL group_replication_bootstrap_group= 0

### Scenario 1: SERVER BOOTSTRAPS+RECOVERS+PRIMARY ELECTION
### Expected:
### - Myself: 1 VIEW CHANGE, 2 STATE CHANGED, 1 ROLE CHANGED
### - Others: N/A
##########################################################################
[connection server_1]
RUN SET GLOBAL group_replication_bootstrap_group= 1
RUN SET GLOBAL group_replication_group_name='%GR_NAME%'
RUN START GROUP_REPLICATION
[waiting for member_state to be equal 'ONLINE']
[connection server_3]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_2]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_1]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_3]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_2]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[connection server_1]
RUN SET GLOBAL group_replication_bootstrap_group= 0
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_3' connection]

### Scenario 2: PRIMARY LEAVES the GROUP
### Expected:
### - Myself (old primary): 1 VIEW CHANGED, 1 STATE CHANGED, 1 ROLE CHANGED
### - Others (new primary): 1 VIEW CHANGED, 1 ROLE CHANGED
### - Others (secondary):   1 VIEW CHANGED, 1 ROLE CHANGED
##########################################################################
[connection server_2]
RUN SET GLOBAL group_replication_group_name='%GR_NAME%'
RUN START GROUP_REPLICATION
[waiting for member_state to be equal 'ONLINE']
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_3' connection]
[connection server_3]
RUN SET GLOBAL group_replication_group_name='%GR_NAME%'
RUN START GROUP_REPLICATION
[waiting for member_state to be equal 'ONLINE']
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_3' connection]
[ASSERT no notices on 'verify_3' connection]
[ASSERT no notices on 'verify_3' connection]
[ASSERT no notices on 'verify_3' connection]
[connection server_1]
RUN STOP GROUP_REPLICATION
RUN SET GLOBAL read_only= FALSE
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[ASSERT no notices on 'verify_1' connection]
[connection server_2]
[waiting for '2' members]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_2' connection]
[ASSERT no notices on 'verify_2' connection]
[connection server_3]
[waiting for '2' members]
[ASSERT no notices on 'verify_3' connection]
[ASSERT no notices on 'verify_3' connection]
[ASSERT no notices on 'verify_3' connection]

###
### In X test cleanup
###
[connection server_2]
RUN STOP GROUP_REPLICATION
RUN SET GLOBAL read_only= FALSE
[connection server_3]
RUN STOP GROUP_REPLICATION
RUN SET GLOBAL read_only= FALSE
closing session server_3
Mysqlx.Ok {
  msg: "bye!"
}
ok
include/group_replication_end.inc
