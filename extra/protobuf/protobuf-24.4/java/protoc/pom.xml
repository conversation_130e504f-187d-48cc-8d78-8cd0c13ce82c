<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.google</groupId>
    <artifactId>google</artifactId>
    <version>5</version>
  </parent>
  <groupId>com.google.protobuf</groupId>
  <artifactId>protoc</artifactId>
  <version>3.24.4</version>
  <packaging>pom</packaging>
  <name>Protobuf Compiler</name>
  <description>
    Protobuf Compiler (protoc) is a compiler for .proto files. It generates
    language-specific code for Protobuf messages and RPC interfaces.
  </description>
  <inceptionYear>2008</inceptionYear>
  <url>https://developers.google.com/protocol-buffers/</url>
  <licenses>
    <license>
      <name>BSD-3-Clause</name>
      <url>https://opensource.org/licenses/BSD-3-Clause</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <url>https://github.com/protocolbuffers/protobuf</url>
    <connection>
      scm:git:https://github.com/protocolbuffers/protobuf.git
    </connection>
  </scm>
</project>
