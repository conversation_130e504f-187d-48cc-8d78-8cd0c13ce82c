<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.google.protobuf</groupId>
    <artifactId>protobuf-parent</artifactId>
    <version>3.24.4</version>
  </parent>

  <artifactId>protobuf-javalite</artifactId>
  <packaging>bundle</packaging>

  <name>Protocol Buffers [Lite]</name>
  <description>
    Lite version of Protocol Buffers library. This version is optimized for code size, but does
    not guarantee API/ABI stability.
  </description>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.truth</groupId>
      <artifactId>truth</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
     <!-- Include core protos in the bundle as resources -->
     <resources>
      <resource>
        <directory>${protobuf.source.dir}</directory>
        <includes>
          <include>google/protobuf/any.proto</include>
          <include>google/protobuf/api.proto</include>
          <include>google/protobuf/duration.proto</include>
          <include>google/protobuf/empty.proto</include>
          <include>google/protobuf/field_mask.proto</include>
          <include>google/protobuf/source_context.proto</include>
          <include>google/protobuf/struct.proto</include>
          <include>google/protobuf/timestamp.proto</include>
          <include>google/protobuf/type.proto</include>
          <include>google/protobuf/wrappers.proto</include>
        </includes>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>${protobuf.source.dir}</directory>
        <includes>
          <include>google/protobuf/testdata/golden_message_oneof_implemented</include>
          <include>google/protobuf/testdata/golden_packed_fields_message</include>
        </includes>
      </testResource>
    </testResources>

    <plugins>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.1.0</version>
        <executions>
          <execution>
            <id>copy-source-files</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${generated.sources.dir}/com/google/protobuf</outputDirectory>
              <resources>
                <resource>
                  <directory>${basedir}/../core/src/main/java/com/google/protobuf</directory>
                  <includes>
                    <!-- Keep in sync with //java/core:BUILD  -->
                    <include>AbstractMessageLite.java</include>
                    <include>AbstractParser.java</include>
                    <include>AbstractProtobufList.java</include>
                    <include>AllocatedBuffer.java</include>
                    <include>Android.java</include>
                    <include>ArrayDecoders.java</include>
                    <include>BinaryReader.java</include>
                    <include>BinaryWriter.java</include>
                    <include>BooleanArrayList.java</include>
                    <include>BufferAllocator.java</include>
                    <include>ByteBufferWriter.java</include>
                    <include>ByteOutput.java</include>
                    <include>ByteString.java</include>
                    <include>CanIgnoreReturnValue.java</include>
                    <include>CheckReturnValue.java</include>
                    <include>CodedInputStream.java</include>
                    <include>CodedInputStreamReader.java</include>
                    <include>CodedOutputStream.java</include>
                    <include>CodedOutputStreamWriter.java</include>
                    <include>CompileTimeConstant.java</include>
                    <include>DoubleArrayList.java</include>
                    <include>ExperimentalApi.java</include>
                    <include>ExtensionLite.java</include>
                    <include>ExtensionRegistryFactory.java</include>
                    <include>ExtensionRegistryLite.java</include>
                    <include>ExtensionSchema.java</include>
                    <include>ExtensionSchemaLite.java</include>
                    <include>ExtensionSchemas.java</include>
                    <include>FieldInfo.java</include>
                    <include>FieldSet.java</include>
                    <include>FieldType.java</include>
                    <include>FloatArrayList.java</include>
                    <include>GeneratedMessageInfoFactory.java</include>
                    <include>GeneratedMessageLite.java</include>
                    <include>InlineMe.java</include>
                    <include>IntArrayList.java</include>
                    <include>Internal.java</include>
                    <include>InvalidProtocolBufferException.java</include>
                    <include>IterableByteBufferInputStream.java</include>
                    <include>Java8Compatibility.java</include>
                    <include>JavaType.java</include>
                    <include>LazyField.java</include>
                    <include>LazyFieldLite.java</include>
                    <include>LazyStringArrayList.java</include>
                    <include>LazyStringList.java</include>
                    <include>ListFieldSchema.java</include>
                    <include>LongArrayList.java</include>
                    <include>ManifestSchemaFactory.java</include>
                    <include>MapEntryLite.java</include>
                    <include>MapFieldLite.java</include>
                    <include>MapFieldSchema.java</include>
                    <include>MapFieldSchemaLite.java</include>
                    <include>MapFieldSchemas.java</include>
                    <include>MessageInfo.java</include>
                    <include>MessageInfoFactory.java</include>
                    <include>MessageLite.java</include>
                    <include>MessageLiteOrBuilder.java</include>
                    <include>MessageLiteToString.java</include>
                    <include>MessageSchema.java</include>
                    <include>MessageSetSchema.java</include>
                    <include>MutabilityOracle.java</include>
                    <include>NewInstanceSchema.java</include>
                    <include>NewInstanceSchemaLite.java</include>
                    <include>NewInstanceSchemas.java</include>
                    <include>NioByteString.java</include>
                    <include>OneofInfo.java</include>
                    <include>Parser.java</include>
                    <include>PrimitiveNonBoxingCollection.java</include>
                    <include>ProtoSyntax.java</include>
                    <include>Protobuf.java</include>
                    <include>ProtobufArrayList.java</include>
                    <include>ProtocolStringList.java</include>
                    <include>RawMessageInfo.java</include>
                    <include>Reader.java</include>
                    <include>RopeByteString.java</include>
                    <include>Schema.java</include>
                    <include>SchemaFactory.java</include>
                    <include>SchemaUtil.java</include>
                    <include>SmallSortedMap.java</include>
                    <include>StructuralMessageInfo.java</include>
                    <include>TextFormatEscaper.java</include>
                    <include>UninitializedMessageException.java</include>
                    <include>UnknownFieldSchema.java</include>
                    <include>UnknownFieldSetLite.java</include>
                    <include>UnknownFieldSetLiteSchema.java</include>
                    <include>UnmodifiableLazyStringList.java</include>
                    <include>UnsafeByteOperations.java</include>
                    <include>UnsafeUtil.java</include>
                    <include>Utf8.java</include>
                    <include>WireFormat.java</include>
                    <include>Writer.java</include>
                  </includes>
                </resource>
              </resources>
            </configuration>
          </execution>
          <execution>
            <id>copy-test-source-files</id>
            <phase>generate-test-sources</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${generated.testsources.dir}/com/google/protobuf</outputDirectory>
              <resources>
                <resource>
                  <directory>${basedir}/../core/src/test/java/com/google/protobuf</directory>
                  <!-- For sources, we want to be explicit about what we add so we don't accidentally
                  increase the size of the lite runtime library. For tests, we want to be defensive
                  and exclude only the full runtime exclusive tests so we don't accidentally miss
                  any test. -->
                  <excludes>
                    <!-- Keep in sync with //java/core:lite_tests BUILD rule. -->
                    <exclude>AbstractMessageTest.java</exclude>
                    <exclude>AbstractProto2SchemaTest.java</exclude>
                    <exclude>AnyTest.java</exclude>
                    <exclude>CodedInputStreamTest.java</exclude>
                    <exclude>DeprecatedFieldTest.java</exclude>
                    <exclude>DescriptorsTest.java</exclude>
                    <exclude>DiscardUnknownFieldsTest.java</exclude>
                    <exclude>DynamicMessageTest.java</exclude>
                    <exclude>ExtensionRegistryFactoryTest.java</exclude>
                    <exclude>FieldPresenceTest.java</exclude>
                    <exclude>ForceFieldBuildersPreRun.java</exclude>
                    <exclude>GeneratedMessageTest.java</exclude>
                    <exclude>LazyFieldTest.java</exclude>
                    <exclude>LazyStringEndToEndTest.java</exclude>
                    <exclude>MapForProto2Test.java</exclude>
                    <exclude>MapTest.java</exclude>
                    <exclude>MessageTest.java</exclude>
                    <exclude>NestedBuildersTest.java</exclude>
                    <exclude>PackedFieldTest.java</exclude>
                    <exclude>ParserTest.java</exclude>
                    <exclude>ParseExceptionsTest.java</exclude>
                    <exclude>Proto2ExtensionLookupSchemaTest.java</exclude>
                    <exclude>Proto2SchemaTest.java</exclude>
                    <exclude>Proto2UnknownEnumValueTest.java</exclude>
                    <exclude>RepeatedFieldBuilderV3Test.java</exclude>
                    <exclude>ServiceTest.java</exclude>
                    <exclude>SingleFieldBuilderV3Test.java</exclude>
                    <exclude>TestBadIdentifiers.java</exclude>
                    <exclude>TextFormatParseInfoTreeTest.java</exclude>
                    <exclude>TextFormatParseLocationTest.java</exclude>
                    <exclude>TextFormatPerformanceTest.java</exclude>
                    <exclude>TextFormatTest.java</exclude>
                    <exclude>TestUtil.java</exclude>
                    <exclude>TypeRegistryTest.java</exclude>
                    <exclude>UnknownEnumValueTest.java</exclude>
                    <exclude>UnknownFieldSetLiteTest.java</exclude>
                    <exclude>UnknownFieldSetPerformanceTest.java</exclude>
                    <exclude>UnknownFieldSetTest.java</exclude>
                    <exclude>WellKnownTypesTest.java</exclude>
                    <exclude>WireFormatTest.java</exclude>
                  </excludes>
                </resource>
              </resources>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Use Antrun plugin to generate sources with protoc -->
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <!-- Generate core protos -->
          <execution>
            <id>generate-sources</id>
            <phase>generate-sources</phase>
            <configuration>
              <target>
                <ant antfile="generate-sources-build.xml"/>
              </target>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>

          <!-- Generate the test protos -->
          <execution>
            <id>generate-test-sources</id>
            <phase>generate-test-sources</phase>
            <configuration>
              <target>
                <ant antfile="generate-test-sources-build.xml"/>
              </target>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>

          <execution>
            <id>process-lite-sources</id>
            <phase>generate-test-sources</phase>
            <configuration>
              <target>
                <ant antfile="process-lite-sources-build.xml"/>
              </target>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>add-generated-sources</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>add-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${generated.sources.dir}</source>
              </sources>
            </configuration>
          </execution>

          <execution>
            <id>add-generated-test-sources</id>
            <phase>generate-test-sources</phase>
            <goals>
              <goal>add-test-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${generated.testsources.dir}</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- OSGI bundle configuration -->
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <configuration>
          <instructions>
            <Automatic-Module-Name>com.google.protobuf</Automatic-Module-Name> <!-- Java9+ Jigsaw module name -->
            <Bundle-DocURL>https://developers.google.com/protocol-buffers/</Bundle-DocURL>
            <Bundle-SymbolicName>com.google.protobuf</Bundle-SymbolicName>
            <Export-Package>com.google.protobuf;version=${project.version}</Export-Package>
            <Import-Package>sun.misc;resolution:=optional,*</Import-Package>
          </instructions>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
