select @@session.optimizer_switch;
@@session.optimizer_switch
index_merge=on,index_merge_union=on,index_merge_sort_union=on,index_merge_intersection=on,engine_condition_pushdown=on,index_condition_pushdown=on,mrr=on,mrr_cost_based=on,block_nested_loop=on,batched_key_access=off,materialization=on,semijoin=on,loosescan=on,firstmatch=on,duplicateweedout=on,subquery_materialization_cost_based=on,use_index_extensions=on,condition_fanout_filter=on,derived_merge=on,use_invisible_indexes=off,skip_scan=on,hash_join=on,subquery_to_derived=off,prefer_ordering_index=on,hypergraph_optimizer=off,derived_condition_pushdown=on,hash_set_operations=on
create temporary table server_counts_at_startup
select * from performance_schema.global_status 
where variable_name in 
('Ndb_pruned_scan_count',
'Ndb_sorted_scan_count',
'Ndb_pushed_queries_defined',
'Ndb_pushed_queries_dropped');
set @save_debug = @@global.debug;
set @save_ndb_join_pushdown = @@session.ndb_join_pushdown;
set ndb_join_pushdown = true;
create table t1 (
a int not null,
b int not null,
c int not null,
d int not null,
primary key (`a`,`b`)
) engine=ndbcluster
partition by key() partitions 8;
insert into t1 values
(1,1,1,1), (2,2,2,2), (3,3,3,3), (4,4,4,4),
(1,2,5,1), (1,3,1,2), (1,4,2,3),
(2,1,3,4), (2,3,4,5), (2,4,5,1),
(3,1,1,2), (3,2,2,3), (3,4,3,4),
(4,1,4,5), (4,2,5,1), (4,3,1,2);
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`))
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c;
a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1
1	3	1	2	3	1	1	2
1	4	2	3	4	2	5	1
2	1	3	4	1	3	1	2
2	2	2	2	2	2	2	2
2	3	4	5	3	4	3	4
3	1	1	2	1	1	1	1
3	2	2	3	2	2	2	2
3	3	3	3	3	3	3	3
3	4	3	4	4	3	1	2
4	1	4	5	1	4	2	3
4	3	1	2	3	1	1	2
4	4	4	4	4	4	4	4
explain format=JSON
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "4007.20"
    },
    "nested_loop": [
      {
        "table": {
          "table_name": "t1",
          "partitions": [
            "p0",
            "p1",
            "p2",
            "p3",
            "p4",
            "p5",
            "p6",
            "p7"
          ],
          "access_type": "ALL",
          "rows_examined_per_scan": 16,
          "rows_produced_per_join": 16,
          "filtered": "100.00",
          "pushed_join": "Parent of 2 pushed join@1",
          "cost_info": {
            "read_cost": "4000.00",
            "eval_cost": "1.60",
            "prefix_cost": "4001.60",
            "data_read_per_join": "384"
          },
          "used_columns": [
            "a",
            "b",
            "c",
            "d"
          ]
        }
      },
      {
        "table": {
          "table_name": "t2",
          "partitions": [
            "p0",
            "p1",
            "p2",
            "p3",
            "p4",
            "p5",
            "p6",
            "p7"
          ],
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ],
          "key": "PRIMARY",
          "used_key_parts": [
            "a",
            "b"
          ],
          "key_length": "8",
          "ref": [
            "test.t1.b",
            "test.t1.c"
          ],
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 16,
          "filtered": "100.00",
          "pushed_join": "Child of 't1' in pushed join@1",
          "cost_info": {
            "read_cost": "4.00",
            "eval_cost": "1.60",
            "prefix_cost": "4007.20",
            "data_read_per_join": "384"
          },
          "used_columns": [
            "a",
            "b",
            "c",
            "d"
          ]
        }
      }
    ]
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`))
explain
select straight_join count(*) 
from t1 as x1 
join t1 as x2 on x1.d > x2.a + 1000 
join t1 as x3 on x1.c=x3.a and x1.d=x3.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	NULL
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Using where; Using join buffer (hash join)
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x1.c,test.x1.d	1	100.00	NULL
Warnings:
Note	1003	Can't push table 'x2' as child, 'type' must be a 'ref' access
Note	1003	Can't push table 'x3' as child of 'x1', it is in a hash-bucket-branch which can't be referred.
Note	1003	Can't push table 'x3' as child of 'x2', column 'x1.c' is in a hash-bucket-branch which can't be referred
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t1` `x3` where ((`test`.`x3`.`b` = `test`.`x1`.`d`) and (`test`.`x3`.`a` = `test`.`x1`.`c`) and (`test`.`x1`.`d` > (`test`.`x2`.`a` + 1000)))
select straight_join count(*) 
from t1 as x1 
join t1 as x2 on x1.d > x2.a + 1000 
join t1 as x3 on x1.c=x3.a and x1.d=x3.b;
count(*)
0
explain select *
from t1 as x1 
join t1 as x2 on x1.a=1 and x1.c=x2.a and x1.d=x2.b 
join t1 as x3 
join t1 as x4 where x4.a=x3.c and x4.b=x1.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x1.c,test.x1.d	1	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Using join buffer (hash join)
1	SIMPLE	x4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x3.c,test.x1.d	1	100.00	NULL
Warnings:
Note	1003	Can't push table 'x3' as child, 'type' must be a 'ref' access
Note	1003	Can't push table 'x4' as child of 'x1', it is in a hash-bucket-branch which can't be referred.
Note	1003	Can't push table 'x4' as child of 'x3', column 'x1.d' is in a hash-bucket-branch which can't be referred
Note	1003	/* select#1 */ select `test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x1`.`c` AS `c`,`test`.`x1`.`d` AS `d`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b`,`test`.`x2`.`c` AS `c`,`test`.`x2`.`d` AS `d`,`test`.`x3`.`a` AS `a`,`test`.`x3`.`b` AS `b`,`test`.`x3`.`c` AS `c`,`test`.`x3`.`d` AS `d`,`test`.`x4`.`a` AS `a`,`test`.`x4`.`b` AS `b`,`test`.`x4`.`c` AS `c`,`test`.`x4`.`d` AS `d` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t1` `x3` join `test`.`t1` `x4` where ((`test`.`x2`.`a` = `test`.`x1`.`c`) and (`test`.`x1`.`a` = 1) and (`test`.`x2`.`b` = `test`.`x1`.`d`) and (`test`.`x4`.`b` = `test`.`x1`.`d`) and (`test`.`x4`.`a` = `test`.`x3`.`c`))
select * 
from t1 as x1 
join t1 as x2 on x1.a=1 and x1.c=x2.a and x1.d=x2.b 
join t1 as x3 
join t1 as x4 where x4.a=x3.c and x4.b=x1.d;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	1	1	1	1	1	1	1	1	3	1	2	1	1	1	1
1	1	1	1	1	1	1	1	1	4	2	3	2	1	3	4
1	1	1	1	1	1	1	1	2	1	3	4	3	1	1	2
1	1	1	1	1	1	1	1	2	2	2	2	2	1	3	4
1	1	1	1	1	1	1	1	2	3	4	5	4	1	4	5
1	1	1	1	1	1	1	1	3	1	1	2	1	1	1	1
1	1	1	1	1	1	1	1	3	2	2	3	2	1	3	4
1	1	1	1	1	1	1	1	3	3	3	3	3	1	1	2
1	1	1	1	1	1	1	1	3	4	3	4	3	1	1	2
1	1	1	1	1	1	1	1	4	1	4	5	4	1	4	5
1	1	1	1	1	1	1	1	4	3	1	2	1	1	1	1
1	1	1	1	1	1	1	1	4	4	4	4	4	1	4	5
1	3	1	2	1	2	5	1	1	1	1	1	1	2	5	1
1	3	1	2	1	2	5	1	1	3	1	2	1	2	5	1
1	3	1	2	1	2	5	1	1	4	2	3	2	2	2	2
1	3	1	2	1	2	5	1	2	1	3	4	3	2	2	3
1	3	1	2	1	2	5	1	2	2	2	2	2	2	2	2
1	3	1	2	1	2	5	1	2	3	4	5	4	2	5	1
1	3	1	2	1	2	5	1	3	1	1	2	1	2	5	1
1	3	1	2	1	2	5	1	3	2	2	3	2	2	2	2
1	3	1	2	1	2	5	1	3	3	3	3	3	2	2	3
1	3	1	2	1	2	5	1	3	4	3	4	3	2	2	3
1	3	1	2	1	2	5	1	4	1	4	5	4	2	5	1
1	3	1	2	1	2	5	1	4	3	1	2	1	2	5	1
1	3	1	2	1	2	5	1	4	4	4	4	4	2	5	1
1	4	2	3	2	3	4	5	1	1	1	1	1	3	1	2
1	4	2	3	2	3	4	5	1	3	1	2	1	3	1	2
1	4	2	3	2	3	4	5	1	4	2	3	2	3	4	5
1	4	2	3	2	3	4	5	2	1	3	4	3	3	3	3
1	4	2	3	2	3	4	5	2	2	2	2	2	3	4	5
1	4	2	3	2	3	4	5	2	3	4	5	4	3	1	2
1	4	2	3	2	3	4	5	3	1	1	2	1	3	1	2
1	4	2	3	2	3	4	5	3	2	2	3	2	3	4	5
1	4	2	3	2	3	4	5	3	3	3	3	3	3	3	3
1	4	2	3	2	3	4	5	3	4	3	4	3	3	3	3
1	4	2	3	2	3	4	5	4	1	4	5	4	3	1	2
1	4	2	3	2	3	4	5	4	3	1	2	1	3	1	2
1	4	2	3	2	3	4	5	4	4	4	4	4	3	1	2
explain
select *
from t1
left join t1 as t2 on t2.a = t1.b and t2.b = t1.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` left join `test`.`t1` `t2` on(((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`))) where true
select *
from t1
left join t1 as t2 on t2.a = t1.b and t2.b = t1.c;
a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1
1	2	5	1	NULL	NULL	NULL	NULL
1	3	1	2	3	1	1	2
1	4	2	3	4	2	5	1
2	1	3	4	1	3	1	2
2	2	2	2	2	2	2	2
2	3	4	5	3	4	3	4
2	4	5	1	NULL	NULL	NULL	NULL
3	1	1	2	1	1	1	1
3	2	2	3	2	2	2	2
3	3	3	3	3	3	3	3
3	4	3	4	4	3	1	2
4	1	4	5	1	4	2	3
4	2	5	1	NULL	NULL	NULL	NULL
4	3	1	2	3	1	1	2
4	4	4	4	4	4	4	4
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
where t1.a = 1 and t1.b = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p7	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	const,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	const,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t1`.`b` = 1) and (`test`.`t2`.`a` = 1) and (`test`.`t3`.`a` = 1) and (`test`.`t1`.`a` = 1))
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
where t1.a = 1 and t1.b = 1;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`))
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	3	1	2	3	1	1	2	3	1	1	2
1	4	2	3	4	2	5	1	4	2	5	1
2	1	3	4	1	3	1	2	1	3	1	2
2	2	2	2	2	2	2	2	2	2	2	2
2	3	4	5	3	4	3	4	3	4	3	4
3	1	1	2	1	1	1	1	1	1	1	1
3	2	2	3	2	2	2	2	2	2	2	2
3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	4	3	1	2	4	3	1	2
4	1	4	5	1	4	2	3	1	4	2	3
4	3	1	2	3	1	1	2	3	1	1	2
4	4	4	4	4	4	4	4	4	4	4	4
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
left join t1 as t3 on t3.a = t2.a and t3.b = t2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` left join `test`.`t1` `t3` on(((`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`a` = `test`.`t1`.`b`))) where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`))
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
left join t1 as t3 on t3.a = t2.a and t3.b = t2.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	3	1	2	3	1	1	2	3	1	1	2
1	4	2	3	4	2	5	1	4	2	5	1
2	1	3	4	1	3	1	2	1	3	1	2
2	2	2	2	2	2	2	2	2	2	2	2
2	3	4	5	3	4	3	4	3	4	3	4
3	1	1	2	1	1	1	1	1	1	1	1
3	2	2	3	2	2	2	2	2	2	2	2
3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	4	3	1	2	4	3	1	2
4	1	4	5	1	4	2	3	1	4	2	3
4	3	1	2	3	1	1	2	3	1	1	2
4	4	4	4	4	4	4	4	4	4	4	4
explain
select *
from t1
left join t1 as t2 on t2.a = t1.b and t2.b = t1.c
left join t1 as t3 on t3.a = t2.a and t3.b = t2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.a,test.t2.b	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` left join `test`.`t1` `t2` on(((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`))) left join `test`.`t1` `t3` on(((`test`.`t3`.`b` = `test`.`t2`.`b`) and (`test`.`t3`.`a` = `test`.`t2`.`a`))) where true
select *
from t1
left join t1 as t2 on t2.a = t1.b and t2.b = t1.c
left join t1 as t3 on t3.a = t2.a and t3.b = t2.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	2	5	1	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
1	3	1	2	3	1	1	2	3	1	1	2
1	4	2	3	4	2	5	1	4	2	5	1
2	1	3	4	1	3	1	2	1	3	1	2
2	2	2	2	2	2	2	2	2	2	2	2
2	3	4	5	3	4	3	4	3	4	3	4
2	4	5	1	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
3	1	1	2	1	1	1	1	1	1	1	1
3	2	2	3	2	2	2	2	2	2	2	2
3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	4	3	1	2	4	3	1	2
4	1	4	5	1	4	2	3	1	4	2	3
4	2	5	1	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
4	3	1	2	3	1	1	2	3	1	1	2
4	4	4	4	4	4	4	4	4	4	4	4
set ndb_join_pushdown=true;
explain
select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 2 and t1.b = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pX	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	pXYZ	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`b` = `test`.`t1`.`d`) and (`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t1`.`b` = 3) and (`test`.`t1`.`a` = 2))
select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 2 and t1.b = 3;
a	b	c	d	a	b	c	d
LOCK TABLES t1 read, t1 as t2 read;
explain
select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 2 and t1.b = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pX	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	pXYZ	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`b` = `test`.`t1`.`d`) and (`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t1`.`b` = 3) and (`test`.`t1`.`a` = 2))
select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 2 and t1.b = 3;
a	b	c	d	a	b	c	d
select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 3 and t1.b = 3;
a	b	c	d	a	b	c	d
3	3	3	3	3	3	3	3
UNLOCK TABLES;
explain
select *
from t1
left join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 2 and t1.b = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pX	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	pXYZ	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` left join `test`.`t1` `t2` on(((`test`.`t2`.`b` = `test`.`t1`.`d`) and (`test`.`t2`.`a` = `test`.`t1`.`c`))) where ((`test`.`t1`.`b` = 3) and (`test`.`t1`.`a` = 2))
select *
from t1
left join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 2 and t1.b = 3;
a	b	c	d	a	b	c	d
2	3	4	5	NULL	NULL	NULL	NULL
explain
select *
from t1
left join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 2 and t1.b = 3
order by t1.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pX	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	pXYZ	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` left join `test`.`t1` `t2` on(((`test`.`t2`.`b` = `test`.`t1`.`d`) and (`test`.`t2`.`a` = `test`.`t1`.`c`))) where ((`test`.`t1`.`b` = 3) and (`test`.`t1`.`a` = 2)) order by `test`.`t1`.`c`
select *
from t1
left join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 2 and t1.b = 3
order by t1.c;
a	b	c	d	a	b	c	d
2	3	4	5	NULL	NULL	NULL	NULL
set ndb_join_pushdown=false;
explain
select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 2 and t1.b = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
Warnings:
Note	1003	/* select#1 */ select '2' AS `a`,'3' AS `b`,'4' AS `c`,'5' AS `d`,NULL AS `a`,NULL AS `b`,NULL AS `c`,NULL AS `d` from `test`.`t1` join `test`.`t1` `t2` where (multiple equal(2) and multiple equal(3) and multiple equal('4', NULL) and multiple equal('5', NULL))
select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 2 and t1.b = 3;
a	b	c	d	a	b	c	d
explain
select *
from t1
left join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 2 and t1.b = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	pX	const	PRIMARY	PRIMARY	8	const,const	1	100.00	NULL
1	SIMPLE	t2	pXYZ	const	PRIMARY	PRIMARY	8	const,const	0	0.00	unique row not found
Warnings:
Note	1003	/* select#1 */ select '2' AS `a`,'3' AS `b`,'4' AS `c`,'5' AS `d`,NULL AS `a`,NULL AS `b`,NULL AS `c`,NULL AS `d` from `test`.`t1` left join `test`.`t1` `t2` on((multiple equal('4', NULL) and multiple equal('5', NULL))) where true
select *
from t1
left join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 2 and t1.b = 3;
a	b	c	d	a	b	c	d
2	3	4	5	NULL	NULL	NULL	NULL
set ndb_join_pushdown=true;
explain
select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 1 and t1.b = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p7	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`b` = `test`.`t1`.`d`) and (`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t1`.`b` = 1) and (`test`.`t1`.`a` = 1))
select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 1 and t1.b = 1;
a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1
explain
select *
from t1
left join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 1 and t1.b = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p7	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` left join `test`.`t1` `t2` on(((`test`.`t2`.`b` = `test`.`t1`.`d`) and (`test`.`t2`.`a` = `test`.`t1`.`c`))) where ((`test`.`t1`.`b` = 1) and (`test`.`t1`.`a` = 1))
select *
from t1
left join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 1 and t1.b = 1;
a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1
set ndb_join_pushdown=false;
explain
select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 1 and t1.b = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p7	const	PRIMARY	PRIMARY	8	const,const	1	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	const	PRIMARY	PRIMARY	8	const,const	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select '1' AS `a`,'1' AS `b`,'1' AS `c`,'1' AS `d`,'1' AS `a`,'1' AS `b`,'1' AS `c`,'1' AS `d` from `test`.`t1` join `test`.`t1` `t2` where true
select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 1 and t1.b = 1;
a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1
explain
select *
from t1
left join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 1 and t1.b = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p7	const	PRIMARY	PRIMARY	8	const,const	1	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	const	PRIMARY	PRIMARY	8	const,const	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select '1' AS `a`,'1' AS `b`,'1' AS `c`,'1' AS `d`,'1' AS `a`,'1' AS `b`,'1' AS `c`,'1' AS `d` from `test`.`t1` left join `test`.`t1` `t2` on((multiple equal('1', '1') and multiple equal('1', '1'))) where true
select *
from t1
left join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.a = 1 and t1.b = 1;
a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1
set ndb_join_pushdown=true;
explain
select *
from t1
join t1 as t2 on t2.a = t1.c
join t1 as t3 on t3.a = t1.c and t3.b = t2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t2.b	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t3`.`b` = `test`.`t2`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t3`.`a` = `test`.`t1`.`c`))
select *
from t1
join t1 as t2 on t2.a = t1.c
join t1 as t3 on t3.a = t1.c and t3.b = t2.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	1	1	1	1	2	5	1	1	2	5	1
1	1	1	1	1	3	1	2	1	3	1	2
1	1	1	1	1	4	2	3	1	4	2	3
1	3	1	2	1	1	1	1	1	1	1	1
1	3	1	2	1	2	5	1	1	2	5	1
1	3	1	2	1	3	1	2	1	3	1	2
1	3	1	2	1	4	2	3	1	4	2	3
1	4	2	3	2	1	3	4	2	1	3	4
1	4	2	3	2	2	2	2	2	2	2	2
1	4	2	3	2	3	4	5	2	3	4	5
1	4	2	3	2	4	5	1	2	4	5	1
2	1	3	4	3	1	1	2	3	1	1	2
2	1	3	4	3	2	2	3	3	2	2	3
2	1	3	4	3	3	3	3	3	3	3	3
2	1	3	4	3	4	3	4	3	4	3	4
2	2	2	2	2	1	3	4	2	1	3	4
2	2	2	2	2	2	2	2	2	2	2	2
2	2	2	2	2	3	4	5	2	3	4	5
2	2	2	2	2	4	5	1	2	4	5	1
2	3	4	5	4	1	4	5	4	1	4	5
2	3	4	5	4	2	5	1	4	2	5	1
2	3	4	5	4	3	1	2	4	3	1	2
2	3	4	5	4	4	4	4	4	4	4	4
3	1	1	2	1	1	1	1	1	1	1	1
3	1	1	2	1	2	5	1	1	2	5	1
3	1	1	2	1	3	1	2	1	3	1	2
3	1	1	2	1	4	2	3	1	4	2	3
3	2	2	3	2	1	3	4	2	1	3	4
3	2	2	3	2	2	2	2	2	2	2	2
3	2	2	3	2	3	4	5	2	3	4	5
3	2	2	3	2	4	5	1	2	4	5	1
3	3	3	3	3	1	1	2	3	1	1	2
3	3	3	3	3	2	2	3	3	2	2	3
3	3	3	3	3	3	3	3	3	3	3	3
3	3	3	3	3	4	3	4	3	4	3	4
3	4	3	4	3	1	1	2	3	1	1	2
3	4	3	4	3	2	2	3	3	2	2	3
3	4	3	4	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	3	4	3	4
4	1	4	5	4	1	4	5	4	1	4	5
4	1	4	5	4	2	5	1	4	2	5	1
4	1	4	5	4	3	1	2	4	3	1	2
4	1	4	5	4	4	4	4	4	4	4	4
4	3	1	2	1	1	1	1	1	1	1	1
4	3	1	2	1	2	5	1	1	2	5	1
4	3	1	2	1	3	1	2	1	3	1	2
4	3	1	2	1	4	2	3	1	4	2	3
4	4	4	4	4	1	4	5	4	1	4	5
4	4	4	4	4	2	5	1	4	2	5	1
4	4	4	4	4	3	1	2	4	3	1	2
4	4	4	4	4	4	4	4	4	4	4	4
explain
select straight_join *
from (t1 as x cross join t1 as y)
join t1 as z on z.a=x.a and z.b=y.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	NULL
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Using join buffer (hash join)
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.a,test.y.b	1	100.00	NULL
Warnings:
Note	1003	Can't push table 'y' as child, 'type' must be a 'ref' access
Note	1003	Can't push table 'z' as child of 'x', it is in a hash-bucket-branch which can't be referred.
Note	1003	Can't push table 'z' as child of 'y', column 'x.a' is in a hash-bucket-branch which can't be referred
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`x`.`c` AS `c`,`test`.`x`.`d` AS `d`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b`,`test`.`y`.`c` AS `c`,`test`.`y`.`d` AS `d`,`test`.`z`.`a` AS `a`,`test`.`z`.`b` AS `b`,`test`.`z`.`c` AS `c`,`test`.`z`.`d` AS `d` from `test`.`t1` `x` join `test`.`t1` `y` join `test`.`t1` `z` where ((`test`.`z`.`b` = `test`.`y`.`b`) and (`test`.`z`.`a` = `test`.`x`.`a`))
select straight_join *
from (t1 as x cross join t1 as y)
join t1 as z on z.a=x.a and z.b=y.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	1	1	1	1	2	5	1	1	2	5	1
1	1	1	1	1	3	1	2	1	3	1	2
1	1	1	1	1	4	2	3	1	4	2	3
1	1	1	1	2	1	3	4	1	1	1	1
1	1	1	1	2	2	2	2	1	2	5	1
1	1	1	1	2	3	4	5	1	3	1	2
1	1	1	1	2	4	5	1	1	4	2	3
1	1	1	1	3	1	1	2	1	1	1	1
1	1	1	1	3	2	2	3	1	2	5	1
1	1	1	1	3	3	3	3	1	3	1	2
1	1	1	1	3	4	3	4	1	4	2	3
1	1	1	1	4	1	4	5	1	1	1	1
1	1	1	1	4	2	5	1	1	2	5	1
1	1	1	1	4	3	1	2	1	3	1	2
1	1	1	1	4	4	4	4	1	4	2	3
1	2	5	1	1	1	1	1	1	1	1	1
1	2	5	1	1	2	5	1	1	2	5	1
1	2	5	1	1	3	1	2	1	3	1	2
1	2	5	1	1	4	2	3	1	4	2	3
1	2	5	1	2	1	3	4	1	1	1	1
1	2	5	1	2	2	2	2	1	2	5	1
1	2	5	1	2	3	4	5	1	3	1	2
1	2	5	1	2	4	5	1	1	4	2	3
1	2	5	1	3	1	1	2	1	1	1	1
1	2	5	1	3	2	2	3	1	2	5	1
1	2	5	1	3	3	3	3	1	3	1	2
1	2	5	1	3	4	3	4	1	4	2	3
1	2	5	1	4	1	4	5	1	1	1	1
1	2	5	1	4	2	5	1	1	2	5	1
1	2	5	1	4	3	1	2	1	3	1	2
1	2	5	1	4	4	4	4	1	4	2	3
1	3	1	2	1	1	1	1	1	1	1	1
1	3	1	2	1	2	5	1	1	2	5	1
1	3	1	2	1	3	1	2	1	3	1	2
1	3	1	2	1	4	2	3	1	4	2	3
1	3	1	2	2	1	3	4	1	1	1	1
1	3	1	2	2	2	2	2	1	2	5	1
1	3	1	2	2	3	4	5	1	3	1	2
1	3	1	2	2	4	5	1	1	4	2	3
1	3	1	2	3	1	1	2	1	1	1	1
1	3	1	2	3	2	2	3	1	2	5	1
1	3	1	2	3	3	3	3	1	3	1	2
1	3	1	2	3	4	3	4	1	4	2	3
1	3	1	2	4	1	4	5	1	1	1	1
1	3	1	2	4	2	5	1	1	2	5	1
1	3	1	2	4	3	1	2	1	3	1	2
1	3	1	2	4	4	4	4	1	4	2	3
1	4	2	3	1	1	1	1	1	1	1	1
1	4	2	3	1	2	5	1	1	2	5	1
1	4	2	3	1	3	1	2	1	3	1	2
1	4	2	3	1	4	2	3	1	4	2	3
1	4	2	3	2	1	3	4	1	1	1	1
1	4	2	3	2	2	2	2	1	2	5	1
1	4	2	3	2	3	4	5	1	3	1	2
1	4	2	3	2	4	5	1	1	4	2	3
1	4	2	3	3	1	1	2	1	1	1	1
1	4	2	3	3	2	2	3	1	2	5	1
1	4	2	3	3	3	3	3	1	3	1	2
1	4	2	3	3	4	3	4	1	4	2	3
1	4	2	3	4	1	4	5	1	1	1	1
1	4	2	3	4	2	5	1	1	2	5	1
1	4	2	3	4	3	1	2	1	3	1	2
1	4	2	3	4	4	4	4	1	4	2	3
2	1	3	4	1	1	1	1	2	1	3	4
2	1	3	4	1	2	5	1	2	2	2	2
2	1	3	4	1	3	1	2	2	3	4	5
2	1	3	4	1	4	2	3	2	4	5	1
2	1	3	4	2	1	3	4	2	1	3	4
2	1	3	4	2	2	2	2	2	2	2	2
2	1	3	4	2	3	4	5	2	3	4	5
2	1	3	4	2	4	5	1	2	4	5	1
2	1	3	4	3	1	1	2	2	1	3	4
2	1	3	4	3	2	2	3	2	2	2	2
2	1	3	4	3	3	3	3	2	3	4	5
2	1	3	4	3	4	3	4	2	4	5	1
2	1	3	4	4	1	4	5	2	1	3	4
2	1	3	4	4	2	5	1	2	2	2	2
2	1	3	4	4	3	1	2	2	3	4	5
2	1	3	4	4	4	4	4	2	4	5	1
2	2	2	2	1	1	1	1	2	1	3	4
2	2	2	2	1	2	5	1	2	2	2	2
2	2	2	2	1	3	1	2	2	3	4	5
2	2	2	2	1	4	2	3	2	4	5	1
2	2	2	2	2	1	3	4	2	1	3	4
2	2	2	2	2	2	2	2	2	2	2	2
2	2	2	2	2	3	4	5	2	3	4	5
2	2	2	2	2	4	5	1	2	4	5	1
2	2	2	2	3	1	1	2	2	1	3	4
2	2	2	2	3	2	2	3	2	2	2	2
2	2	2	2	3	3	3	3	2	3	4	5
2	2	2	2	3	4	3	4	2	4	5	1
2	2	2	2	4	1	4	5	2	1	3	4
2	2	2	2	4	2	5	1	2	2	2	2
2	2	2	2	4	3	1	2	2	3	4	5
2	2	2	2	4	4	4	4	2	4	5	1
2	3	4	5	1	1	1	1	2	1	3	4
2	3	4	5	1	2	5	1	2	2	2	2
2	3	4	5	1	3	1	2	2	3	4	5
2	3	4	5	1	4	2	3	2	4	5	1
2	3	4	5	2	1	3	4	2	1	3	4
2	3	4	5	2	2	2	2	2	2	2	2
2	3	4	5	2	3	4	5	2	3	4	5
2	3	4	5	2	4	5	1	2	4	5	1
2	3	4	5	3	1	1	2	2	1	3	4
2	3	4	5	3	2	2	3	2	2	2	2
2	3	4	5	3	3	3	3	2	3	4	5
2	3	4	5	3	4	3	4	2	4	5	1
2	3	4	5	4	1	4	5	2	1	3	4
2	3	4	5	4	2	5	1	2	2	2	2
2	3	4	5	4	3	1	2	2	3	4	5
2	3	4	5	4	4	4	4	2	4	5	1
2	4	5	1	1	1	1	1	2	1	3	4
2	4	5	1	1	2	5	1	2	2	2	2
2	4	5	1	1	3	1	2	2	3	4	5
2	4	5	1	1	4	2	3	2	4	5	1
2	4	5	1	2	1	3	4	2	1	3	4
2	4	5	1	2	2	2	2	2	2	2	2
2	4	5	1	2	3	4	5	2	3	4	5
2	4	5	1	2	4	5	1	2	4	5	1
2	4	5	1	3	1	1	2	2	1	3	4
2	4	5	1	3	2	2	3	2	2	2	2
2	4	5	1	3	3	3	3	2	3	4	5
2	4	5	1	3	4	3	4	2	4	5	1
2	4	5	1	4	1	4	5	2	1	3	4
2	4	5	1	4	2	5	1	2	2	2	2
2	4	5	1	4	3	1	2	2	3	4	5
2	4	5	1	4	4	4	4	2	4	5	1
3	1	1	2	1	1	1	1	3	1	1	2
3	1	1	2	1	2	5	1	3	2	2	3
3	1	1	2	1	3	1	2	3	3	3	3
3	1	1	2	1	4	2	3	3	4	3	4
3	1	1	2	2	1	3	4	3	1	1	2
3	1	1	2	2	2	2	2	3	2	2	3
3	1	1	2	2	3	4	5	3	3	3	3
3	1	1	2	2	4	5	1	3	4	3	4
3	1	1	2	3	1	1	2	3	1	1	2
3	1	1	2	3	2	2	3	3	2	2	3
3	1	1	2	3	3	3	3	3	3	3	3
3	1	1	2	3	4	3	4	3	4	3	4
3	1	1	2	4	1	4	5	3	1	1	2
3	1	1	2	4	2	5	1	3	2	2	3
3	1	1	2	4	3	1	2	3	3	3	3
3	1	1	2	4	4	4	4	3	4	3	4
3	2	2	3	1	1	1	1	3	1	1	2
3	2	2	3	1	2	5	1	3	2	2	3
3	2	2	3	1	3	1	2	3	3	3	3
3	2	2	3	1	4	2	3	3	4	3	4
3	2	2	3	2	1	3	4	3	1	1	2
3	2	2	3	2	2	2	2	3	2	2	3
3	2	2	3	2	3	4	5	3	3	3	3
3	2	2	3	2	4	5	1	3	4	3	4
3	2	2	3	3	1	1	2	3	1	1	2
3	2	2	3	3	2	2	3	3	2	2	3
3	2	2	3	3	3	3	3	3	3	3	3
3	2	2	3	3	4	3	4	3	4	3	4
3	2	2	3	4	1	4	5	3	1	1	2
3	2	2	3	4	2	5	1	3	2	2	3
3	2	2	3	4	3	1	2	3	3	3	3
3	2	2	3	4	4	4	4	3	4	3	4
3	3	3	3	1	1	1	1	3	1	1	2
3	3	3	3	1	2	5	1	3	2	2	3
3	3	3	3	1	3	1	2	3	3	3	3
3	3	3	3	1	4	2	3	3	4	3	4
3	3	3	3	2	1	3	4	3	1	1	2
3	3	3	3	2	2	2	2	3	2	2	3
3	3	3	3	2	3	4	5	3	3	3	3
3	3	3	3	2	4	5	1	3	4	3	4
3	3	3	3	3	1	1	2	3	1	1	2
3	3	3	3	3	2	2	3	3	2	2	3
3	3	3	3	3	3	3	3	3	3	3	3
3	3	3	3	3	4	3	4	3	4	3	4
3	3	3	3	4	1	4	5	3	1	1	2
3	3	3	3	4	2	5	1	3	2	2	3
3	3	3	3	4	3	1	2	3	3	3	3
3	3	3	3	4	4	4	4	3	4	3	4
3	4	3	4	1	1	1	1	3	1	1	2
3	4	3	4	1	2	5	1	3	2	2	3
3	4	3	4	1	3	1	2	3	3	3	3
3	4	3	4	1	4	2	3	3	4	3	4
3	4	3	4	2	1	3	4	3	1	1	2
3	4	3	4	2	2	2	2	3	2	2	3
3	4	3	4	2	3	4	5	3	3	3	3
3	4	3	4	2	4	5	1	3	4	3	4
3	4	3	4	3	1	1	2	3	1	1	2
3	4	3	4	3	2	2	3	3	2	2	3
3	4	3	4	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	3	4	3	4
3	4	3	4	4	1	4	5	3	1	1	2
3	4	3	4	4	2	5	1	3	2	2	3
3	4	3	4	4	3	1	2	3	3	3	3
3	4	3	4	4	4	4	4	3	4	3	4
4	1	4	5	1	1	1	1	4	1	4	5
4	1	4	5	1	2	5	1	4	2	5	1
4	1	4	5	1	3	1	2	4	3	1	2
4	1	4	5	1	4	2	3	4	4	4	4
4	1	4	5	2	1	3	4	4	1	4	5
4	1	4	5	2	2	2	2	4	2	5	1
4	1	4	5	2	3	4	5	4	3	1	2
4	1	4	5	2	4	5	1	4	4	4	4
4	1	4	5	3	1	1	2	4	1	4	5
4	1	4	5	3	2	2	3	4	2	5	1
4	1	4	5	3	3	3	3	4	3	1	2
4	1	4	5	3	4	3	4	4	4	4	4
4	1	4	5	4	1	4	5	4	1	4	5
4	1	4	5	4	2	5	1	4	2	5	1
4	1	4	5	4	3	1	2	4	3	1	2
4	1	4	5	4	4	4	4	4	4	4	4
4	2	5	1	1	1	1	1	4	1	4	5
4	2	5	1	1	2	5	1	4	2	5	1
4	2	5	1	1	3	1	2	4	3	1	2
4	2	5	1	1	4	2	3	4	4	4	4
4	2	5	1	2	1	3	4	4	1	4	5
4	2	5	1	2	2	2	2	4	2	5	1
4	2	5	1	2	3	4	5	4	3	1	2
4	2	5	1	2	4	5	1	4	4	4	4
4	2	5	1	3	1	1	2	4	1	4	5
4	2	5	1	3	2	2	3	4	2	5	1
4	2	5	1	3	3	3	3	4	3	1	2
4	2	5	1	3	4	3	4	4	4	4	4
4	2	5	1	4	1	4	5	4	1	4	5
4	2	5	1	4	2	5	1	4	2	5	1
4	2	5	1	4	3	1	2	4	3	1	2
4	2	5	1	4	4	4	4	4	4	4	4
4	3	1	2	1	1	1	1	4	1	4	5
4	3	1	2	1	2	5	1	4	2	5	1
4	3	1	2	1	3	1	2	4	3	1	2
4	3	1	2	1	4	2	3	4	4	4	4
4	3	1	2	2	1	3	4	4	1	4	5
4	3	1	2	2	2	2	2	4	2	5	1
4	3	1	2	2	3	4	5	4	3	1	2
4	3	1	2	2	4	5	1	4	4	4	4
4	3	1	2	3	1	1	2	4	1	4	5
4	3	1	2	3	2	2	3	4	2	5	1
4	3	1	2	3	3	3	3	4	3	1	2
4	3	1	2	3	4	3	4	4	4	4	4
4	3	1	2	4	1	4	5	4	1	4	5
4	3	1	2	4	2	5	1	4	2	5	1
4	3	1	2	4	3	1	2	4	3	1	2
4	3	1	2	4	4	4	4	4	4	4	4
4	4	4	4	1	1	1	1	4	1	4	5
4	4	4	4	1	2	5	1	4	2	5	1
4	4	4	4	1	3	1	2	4	3	1	2
4	4	4	4	1	4	2	3	4	4	4	4
4	4	4	4	2	1	3	4	4	1	4	5
4	4	4	4	2	2	2	2	4	2	5	1
4	4	4	4	2	3	4	5	4	3	1	2
4	4	4	4	2	4	5	1	4	4	4	4
4	4	4	4	3	1	1	2	4	1	4	5
4	4	4	4	3	2	2	3	4	2	5	1
4	4	4	4	3	3	3	3	4	3	1	2
4	4	4	4	3	4	3	4	4	4	4	4
4	4	4	4	4	1	4	5	4	1	4	5
4	4	4	4	4	2	5	1	4	2	5	1
4	4	4	4	4	3	1	2	4	3	1	2
4	4	4	4	4	4	4	4	4	4	4	4
explain
select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0 and t2.b = t1.c
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	func,test.t1.c	1	100.00	Using where
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	Can't push table 't2' as child, column 'a' does neither 'ref' a column nor a constant
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` straight_join `test`.`t1` `t2` straight_join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`a` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = (`test`.`t1`.`b` + 0)))
select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0 and t2.b = t1.c
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	3	1	2	3	1	1	2	3	1	1	2
1	4	2	3	4	2	5	1	4	2	5	1
2	1	3	4	1	3	1	2	1	3	1	2
2	2	2	2	2	2	2	2	2	2	2	2
2	3	4	5	3	4	3	4	3	4	3	4
3	1	1	2	1	1	1	1	1	1	1	1
3	2	2	3	2	2	2	2	2	2	2	2
3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	4	3	1	2	4	3	1	2
4	1	4	5	1	4	2	3	1	4	2	3
4	3	1	2	3	1	1	2	3	1	1	2
4	4	4	4	4	4	4	4	4	4	4	4
explain
select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0 and t2.b = t1.c
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b
where t1.a=1 and t1.d=1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`t1`.`d` = 1)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	func,test.t1.c	#	#	Using where
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	#	#	Child of 't1' in pushed join@1
Warnings:
Note	1003	Can't push table 't2' as child, column 'a' does neither 'ref' a column nor a constant
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` straight_join `test`.`t1` `t2` straight_join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`a` = `test`.`t1`.`b`) and (`test`.`t1`.`d` = 1) and (`test`.`t1`.`a` = 1) and (`test`.`t2`.`a` = (`test`.`t1`.`b` + 0)))
select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0 and t2.b = t1.c
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b
where t1.a=1 and t1.d=1;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
explain
select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	func	1	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`t2`.`a` = (`test`.`t1`.`b` + 0))
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t2.b	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	Can't push table 't2' as child, column 'a' does neither 'ref' a column nor a constant
Note	1003	Can't push table 't3' as child of 't1', column 't2.b' refers a table which was not pushed
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` straight_join `test`.`t1` `t2` straight_join `test`.`t1` `t3` where ((`test`.`t3`.`b` = `test`.`t2`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = (`test`.`t1`.`b` + 0)))
select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	1	1	1	1	2	5	1	1	2	5	1
1	1	1	1	1	3	1	2	1	3	1	2
1	1	1	1	1	4	2	3	1	4	2	3
1	2	5	1	2	1	3	4	2	1	3	4
1	2	5	1	2	2	2	2	2	2	2	2
1	2	5	1	2	3	4	5	2	3	4	5
1	2	5	1	2	4	5	1	2	4	5	1
1	3	1	2	3	1	1	2	3	1	1	2
1	3	1	2	3	2	2	3	3	2	2	3
1	3	1	2	3	3	3	3	3	3	3	3
1	3	1	2	3	4	3	4	3	4	3	4
1	4	2	3	4	1	4	5	4	1	4	5
1	4	2	3	4	2	5	1	4	2	5	1
1	4	2	3	4	3	1	2	4	3	1	2
1	4	2	3	4	4	4	4	4	4	4	4
2	1	3	4	1	1	1	1	1	1	1	1
2	1	3	4	1	2	5	1	1	2	5	1
2	1	3	4	1	3	1	2	1	3	1	2
2	1	3	4	1	4	2	3	1	4	2	3
2	2	2	2	2	1	3	4	2	1	3	4
2	2	2	2	2	2	2	2	2	2	2	2
2	2	2	2	2	3	4	5	2	3	4	5
2	2	2	2	2	4	5	1	2	4	5	1
2	3	4	5	3	1	1	2	3	1	1	2
2	3	4	5	3	2	2	3	3	2	2	3
2	3	4	5	3	3	3	3	3	3	3	3
2	3	4	5	3	4	3	4	3	4	3	4
2	4	5	1	4	1	4	5	4	1	4	5
2	4	5	1	4	2	5	1	4	2	5	1
2	4	5	1	4	3	1	2	4	3	1	2
2	4	5	1	4	4	4	4	4	4	4	4
3	1	1	2	1	1	1	1	1	1	1	1
3	1	1	2	1	2	5	1	1	2	5	1
3	1	1	2	1	3	1	2	1	3	1	2
3	1	1	2	1	4	2	3	1	4	2	3
3	2	2	3	2	1	3	4	2	1	3	4
3	2	2	3	2	2	2	2	2	2	2	2
3	2	2	3	2	3	4	5	2	3	4	5
3	2	2	3	2	4	5	1	2	4	5	1
3	3	3	3	3	1	1	2	3	1	1	2
3	3	3	3	3	2	2	3	3	2	2	3
3	3	3	3	3	3	3	3	3	3	3	3
3	3	3	3	3	4	3	4	3	4	3	4
3	4	3	4	4	1	4	5	4	1	4	5
3	4	3	4	4	2	5	1	4	2	5	1
3	4	3	4	4	3	1	2	4	3	1	2
3	4	3	4	4	4	4	4	4	4	4	4
4	1	4	5	1	1	1	1	1	1	1	1
4	1	4	5	1	2	5	1	1	2	5	1
4	1	4	5	1	3	1	2	1	3	1	2
4	1	4	5	1	4	2	3	1	4	2	3
4	2	5	1	2	1	3	4	2	1	3	4
4	2	5	1	2	2	2	2	2	2	2	2
4	2	5	1	2	3	4	5	2	3	4	5
4	2	5	1	2	4	5	1	2	4	5	1
4	3	1	2	3	1	1	2	3	1	1	2
4	3	1	2	3	2	2	3	3	2	2	3
4	3	1	2	3	3	3	3	3	3	3	3
4	3	1	2	3	4	3	4	3	4	3	4
4	4	4	4	4	1	4	5	4	1	4	5
4	4	4	4	4	2	5	1	4	2	5	1
4	4	4	4	4	3	1	2	4	3	1	2
4	4	4	4	4	4	4	4	4	4	4	4
create table t1_myisam (
a int not null,
b int not null,
c int not null,
d int not null,
primary key (`a`,`b`)
) engine=myisam;
insert into t1_myisam values
(1,1,1,1), (2,2,1,1), (3,3,1,1), (4,4,1,1);
set ndb_join_pushdown=true;
explain
select *
from t1_myisam as t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
where t1.a=2 and t1.b=2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	const	PRIMARY	PRIMARY	8	const,const	1	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.a,test.t2.b	1	100.00	Child of 't2' in pushed join@1; Using pushed condition ((`test`.`t3`.`b` = '1') and (`test`.`t3`.`a` = '1'))
Warnings:
Note	1003	/* select#1 */ select '2' AS `a`,'2' AS `b`,'1' AS `c`,'1' AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1_myisam` `t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = '1') and (`test`.`t3`.`b` = '1') and (`test`.`t2`.`a` = '1') and (`test`.`t3`.`a` = '1'))
select *
from t1_myisam as t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
where t1.a=2 and t1.b=2;
a	b	c	d	a	b	c	d	a	b	c	d
2	2	1	1	1	1	1	1	1	1	1	1
explain
select *
from t1_myisam as t1
join t1 as t2 on t2.a = t1.c
join t1 as t3 on t3.a = t2.b and t3.b = t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.c	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.b,test.t1.b	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1_myisam` `t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t2`.`b`))
select *
from t1_myisam as t1
join t1 as t2 on t2.a = t1.c
join t1 as t3 on t3.a = t2.b and t3.b = t1.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	1	1	1	1	2	5	1	2	1	3	4
1	1	1	1	1	3	1	2	3	1	1	2
1	1	1	1	1	4	2	3	4	1	4	5
2	2	1	1	1	1	1	1	1	2	5	1
2	2	1	1	1	2	5	1	2	2	2	2
2	2	1	1	1	3	1	2	3	2	2	3
2	2	1	1	1	4	2	3	4	2	5	1
3	3	1	1	1	1	1	1	1	3	1	2
3	3	1	1	1	2	5	1	2	3	4	5
3	3	1	1	1	3	1	2	3	3	3	3
3	3	1	1	1	4	2	3	4	3	1	2
4	4	1	1	1	1	1	1	1	4	2	3
4	4	1	1	1	2	5	1	2	4	5	1
4	4	1	1	1	3	1	2	3	4	3	4
4	4	1	1	1	4	2	3	4	4	4	4
drop table t1_myisam;
set ndb_join_pushdown=true;
explain select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.d = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`t1`.`d` = 3)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,const	#	#	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t1`.`d` = 3) and (`test`.`t2`.`b` = 3))
select *
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
where t1.d = 3;
a	b	c	d	a	b	c	d
1	4	2	3	2	3	4	5
3	2	2	3	2	3	4	5
3	3	3	3	3	3	3	3
explain select *
from t1 
join t1 as t2 on t2.a = t1.c and t2.b = t1.d 
where t1.a > 2 and t1.d = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	range	PRIMARY	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`t1`.`d` = 3) and (`test`.`t1`.`a` > 2)); Using MRR
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,const	#	#	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t1`.`d` = 3) and (`test`.`t2`.`b` = 3) and (`test`.`t1`.`a` > 2))
select * 
from t1 
join t1 as t2 on t2.a = t1.c and t2.b = t1.d 
where t1.a > 2 and t1.d = 3;
a	b	c	d	a	b	c	d
3	2	2	3	2	3	4	5
3	3	3	3	3	3	3	3
explain select *
from t1 join t1 as t2 on t2.a = t1.c and t2.b = t1.d 
where t1.d = 3 
order by t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	8	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`t1`.`d` = 3)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,const	#	#	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t1`.`d` = 3) and (`test`.`t2`.`b` = 3)) order by `test`.`t1`.`a`
select * 
from t1 join t1 as t2 on t2.a = t1.c and t2.b = t1.d 
where t1.d = 3 
order by t1.a;
a	b	c	d	a	b	c	d
1	4	2	3	2	3	4	5
3	2	2	3	2	3	4	5
3	3	3	3	3	3	3	3
set ndb_join_pushdown=true;
explain
select *
from t1
join t1 as t2 on t2.a = t1.c
join t1 as t3 on t3.a = t2.c and t3.b = t2.d
where t1.a = 1 and t1.b = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p7	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.c	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	Push of table 't2' as scan-child with lookup-root 't1' not implemented
Note	1003	Can't push table 't3' as child of 't1', column 't2.c' refers a table which was not pushed
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t3`.`a` = `test`.`t2`.`c`) and (`test`.`t1`.`b` = 1) and (`test`.`t1`.`a` = 1))
select *
from t1
join t1 as t2 on t2.a = t1.c
join t1 as t3 on t3.a = t2.c and t3.b = t2.d
where t1.a = 1 and t1.b = 1;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	1	1	1	1	3	1	2	1	2	5	1
1	1	1	1	1	4	2	3	2	3	4	5
explain
select *
from t1
left join t1 as t2 on t2.a = t1.c
left join t1 as t3 on t3.a = t2.c and t3.b = t2.d
where t1.a = 1 and t1.b = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p7	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.c	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	Push of table 't2' as scan-child with lookup-root 't1' not implemented
Note	1003	Can't push table 't3' as child of 't1', column 't2.c' refers a table which was not pushed
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` left join `test`.`t1` `t2` on((`test`.`t2`.`a` = `test`.`t1`.`c`)) left join `test`.`t1` `t3` on(((`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t3`.`a` = `test`.`t2`.`c`))) where ((`test`.`t1`.`b` = 1) and (`test`.`t1`.`a` = 1))
select *
from t1
left join t1 as t2 on t2.a = t1.c
left join t1 as t3 on t3.a = t2.c and t3.b = t2.d
where t1.a = 1 and t1.b = 1;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	1	1	1	1	2	5	1	NULL	NULL	NULL	NULL
1	1	1	1	1	3	1	2	1	2	5	1
1	1	1	1	1	4	2	3	2	3	4	5
set ndb_join_pushdown=false;
explain
select *
from t1
join t1 as t2 on t2.a = t1.c
join t1 as t3 on t3.a = t2.c and t3.b = t2.d
where t1.a = 1 and t1.b = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p7	const	PRIMARY	PRIMARY	8	const,const	1	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select '1' AS `a`,'1' AS `b`,'1' AS `c`,'1' AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`a` = '1') and (`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t3`.`a` = `test`.`t2`.`c`))
select *
from t1
join t1 as t2 on t2.a = t1.c
join t1 as t3 on t3.a = t2.c and t3.b = t2.d
where t1.a = 1 and t1.b = 1;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	1	1	1	1	3	1	2	1	2	5	1
1	1	1	1	1	4	2	3	2	3	4	5
explain
select *
from t1
left join t1 as t2 on t2.a = t1.c
left join t1 as t3 on t3.a = t2.c and t3.b = t2.d
where t1.a = 1 and t1.b = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p7	const	PRIMARY	PRIMARY	8	const,const	1	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	NULL
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select '1' AS `a`,'1' AS `b`,'1' AS `c`,'1' AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` left join `test`.`t1` `t2` on((`test`.`t2`.`a` = '1')) left join `test`.`t1` `t3` on(((`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t3`.`a` = `test`.`t2`.`c`))) where true
select *
from t1
left join t1 as t2 on t2.a = t1.c
left join t1 as t3 on t3.a = t2.c and t3.b = t2.d
where t1.a = 1 and t1.b = 1;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	1	1	1	1	2	5	1	NULL	NULL	NULL	NULL
1	1	1	1	1	3	1	2	1	2	5	1
1	1	1	1	1	4	2	3	2	3	4	5
set ndb_join_pushdown=true;
explain
select *
from t1 as t2
join t1 as t3 on t3.a = t2.c and t3.b = t2.d
where t2.a = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t3`.`a` = `test`.`t2`.`c`) and (`test`.`t2`.`a` = 1))
select *
from t1 as t2
join t1 as t3 on t3.a = t2.c and t3.b = t2.d
where t2.a = 1;
a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1
1	3	1	2	1	2	5	1
1	4	2	3	2	3	4	5
set ndb_join_pushdown=true;
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t2.c and t3.b = t1.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t1.c	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`a` = `test`.`t2`.`c`))
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t2.c and t3.b = t1.c;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	3	1	2	1	3	1	2	1	1	1	1
1	4	2	3	1	4	2	3	2	2	2	2
2	1	3	4	2	1	3	4	3	3	3	3
2	2	2	2	2	2	2	2	2	2	2	2
2	3	4	5	2	3	4	5	4	4	4	4
3	1	1	2	3	1	1	2	1	1	1	1
3	2	2	3	3	2	2	3	2	2	2	2
3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	3	3	3	3
4	1	4	5	4	1	4	5	4	4	4	4
4	3	1	2	4	3	1	2	1	1	1	1
4	4	4	4	4	4	4	4	4	4	4	4
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t3.c and t4.b = t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t2.c	1	100.00	Child of 't3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` join `test`.`t1` `t4` where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t3`.`b` = `test`.`t1`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t4`.`b` = `test`.`t2`.`c`) and (`test`.`t4`.`a` = `test`.`t3`.`c`))
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t3.c and t4.b = t2.c;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	4	2	3	1	4	2	3	2	3	4	5	4	2	5	1
2	1	3	4	2	1	3	4	3	4	3	4	3	3	3	3
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
3	2	2	3	3	2	2	3	2	3	4	5	4	2	5	1
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	3	4	3	4	3	3	3	3
4	4	4	4	4	4	4	4	4	4	4	4	4	4	4	4
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t2.d
join t1 as t4 on t4.a = t3.c and t4.b = t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t2.c	1	100.00	Child of 't3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` join `test`.`t1` `t4` where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t4`.`b` = `test`.`t2`.`c`) and (`test`.`t4`.`a` = `test`.`t3`.`c`))
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t2.d
join t1 as t4 on t4.a = t3.c and t4.b = t2.c;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	4	2	3	1	4	2	3	2	3	4	5	4	2	5	1
2	1	3	4	2	1	3	4	3	4	3	4	3	3	3	3
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
3	2	2	3	3	2	2	3	2	3	4	5	4	2	5	1
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	3	4	3	4	3	3	3	3
4	4	4	4	4	4	4	4	4	4	4	4	4	4	4	4
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t2.d
join t1 as t4 on t4.a = t3.c and t4.b = t1.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t1.d	1	100.00	Child of 't3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` join `test`.`t1` `t4` where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t4`.`b` = `test`.`t1`.`d`) and (`test`.`t4`.`a` = `test`.`t3`.`c`))
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t2.d
join t1 as t4 on t4.a = t3.c and t4.b = t1.d;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	4	2	3	1	4	2	3	2	3	4	5	4	3	1	2
2	1	3	4	2	1	3	4	3	4	3	4	3	4	3	4
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
3	2	2	3	3	2	2	3	2	3	4	5	4	3	1	2
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	3	4	3	4	3	4	3	4
4	4	4	4	4	4	4	4	4	4	4	4	4	4	4	4
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t3.a and t4.b = t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t2.c	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` join `test`.`t1` `t4` where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t3`.`b` = `test`.`t1`.`d`) and (`test`.`t4`.`b` = `test`.`t2`.`c`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t4`.`a` = `test`.`t1`.`c`))
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t3.a and t4.b = t2.c;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	3	1	2	1	3	1	2	1	2	5	1	1	1	1	1
1	4	2	3	1	4	2	3	2	3	4	5	2	2	2	2
2	1	3	4	2	1	3	4	3	4	3	4	3	3	3	3
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
3	1	1	2	3	1	1	2	1	2	5	1	1	1	1	1
3	2	2	3	3	2	2	3	2	3	4	5	2	2	2	2
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	3	4	3	4	3	3	3	3
4	3	1	2	4	3	1	2	1	2	5	1	1	1	1	1
4	4	4	4	4	4	4	4	4	4	4	4	4	4	4	4
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t3.b and t4.b = t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.d,test.t2.c	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` join `test`.`t1` `t4` where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t4`.`b` = `test`.`t2`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`d`) and (`test`.`t4`.`a` = `test`.`t1`.`d`))
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t3.b and t4.b = t2.c;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	3	1	2	1	3	1	2	1	2	5	1	2	1	3	4
1	4	2	3	1	4	2	3	2	3	4	5	3	2	2	3
2	1	3	4	2	1	3	4	3	4	3	4	4	3	1	2
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
3	1	1	2	3	1	1	2	1	2	5	1	2	1	3	4
3	2	2	3	3	2	2	3	2	3	4	5	3	2	2	3
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	3	4	3	4	4	3	1	2
4	3	1	2	4	3	1	2	1	2	5	1	2	1	3	4
4	4	4	4	4	4	4	4	4	4	4	4	4	4	4	4
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t3.c and t4.b = t2.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t1.a	1	100.00	Child of 't3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` join `test`.`t1` `t4` where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t3`.`b` = `test`.`t1`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t4`.`b` = `test`.`t1`.`a`) and (`test`.`t4`.`a` = `test`.`t3`.`c`))
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t3.c and t4.b = t2.a;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	4	2	3	1	4	2	3	2	3	4	5	4	1	4	5
2	1	3	4	2	1	3	4	3	4	3	4	3	2	2	3
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
3	2	2	3	3	2	2	3	2	3	4	5	4	3	1	2
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	3	4	3	4	3	3	3	3
4	4	4	4	4	4	4	4	4	4	4	4	4	4	4	4
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t3.c and t4.b = t2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t1.b	1	100.00	Child of 't3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` join `test`.`t1` `t4` where ((`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t3`.`b` = `test`.`t1`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t4`.`b` = `test`.`t1`.`b`) and (`test`.`t4`.`a` = `test`.`t3`.`c`))
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t3.c and t4.b = t2.b;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	4	2	3	1	4	2	3	2	3	4	5	4	4	4	4
2	1	3	4	2	1	3	4	3	4	3	4	3	1	1	2
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
3	2	2	3	3	2	2	3	2	3	4	5	4	2	5	1
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	3	4	3	4	3	4	3	4
4	4	4	4	4	4	4	4	4	4	4	4	4	4	4	4
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t1.c and t4.b = t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t2.c	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` join `test`.`t1` `t4` where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t3`.`b` = `test`.`t1`.`d`) and (`test`.`t4`.`b` = `test`.`t2`.`c`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t4`.`a` = `test`.`t1`.`c`))
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t1.c and t4.b = t2.c;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	3	1	2	1	3	1	2	1	2	5	1	1	1	1	1
1	4	2	3	1	4	2	3	2	3	4	5	2	2	2	2
2	1	3	4	2	1	3	4	3	4	3	4	3	3	3	3
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
3	1	1	2	3	1	1	2	1	2	5	1	1	1	1	1
3	2	2	3	3	2	2	3	2	3	4	5	2	2	2	2
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	3	4	3	4	3	3	3	3
4	3	1	2	4	3	1	2	1	2	5	1	1	1	1	1
4	4	4	4	4	4	4	4	4	4	4	4	4	4	4	4
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t3.c and t4.b = t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t1.b	1	100.00	Child of 't3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` join `test`.`t1` `t4` where ((`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t3`.`b` = `test`.`t1`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t4`.`b` = `test`.`t1`.`b`) and (`test`.`t4`.`a` = `test`.`t3`.`c`))
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
join t1 as t4 on t4.a = t3.c and t4.b = t1.b;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	4	2	3	1	4	2	3	2	3	4	5	4	4	4	4
2	1	3	4	2	1	3	4	3	4	3	4	3	1	1	2
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
3	2	2	3	3	2	2	3	2	3	4	5	4	2	5	1
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	3	4	3	4	3	4	3	4
4	4	4	4	4	4	4	4	4	4	4	4	4	4	4	4
explain
select straight_join *
from t1
join t1 as t2  on t2.a = t1.a and t2.b = t1.b
join t1 as t2x on t2x.a = t2.c and t2x.b = t2.d
join t1 as t3x on t3x.a = t1.c and t3x.b = t1.d
join t1 as t4  on t4.a = t3x.c and t4.b = t2x.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 5 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t2x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t3x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't2x' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3x.c,test.t2x.c	1	100.00	Child of 't3x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t2x`.`a` AS `a`,`test`.`t2x`.`b` AS `b`,`test`.`t2x`.`c` AS `c`,`test`.`t2x`.`d` AS `d`,`test`.`t3x`.`a` AS `a`,`test`.`t3x`.`b` AS `b`,`test`.`t3x`.`c` AS `c`,`test`.`t3x`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t2x` join `test`.`t1` `t3x` join `test`.`t1` `t4` where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t2x`.`b` = `test`.`t2`.`d`) and (`test`.`t2x`.`a` = `test`.`t2`.`c`) and (`test`.`t3x`.`b` = `test`.`t1`.`d`) and (`test`.`t3x`.`a` = `test`.`t1`.`c`) and (`test`.`t4`.`b` = `test`.`t2x`.`c`) and (`test`.`t4`.`a` = `test`.`t3x`.`c`))
explain
select straight_join *
from t1
join t1 as t2  on t2.a = t1.a and t2.b = t1.b
join t1 as t2x on t2x.a = t2.c and t2x.b = t2.d
join t1 as t3  on t3.a = t1.c and t3.b = t1.d
join t1 as t3x on t3x.a = t3.c and t3x.b = t3.d
join t1 as t4  on t4.a = t3x.c and t4.b = t2x.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 6 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t2x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't2x' in pushed join@1
1	SIMPLE	t3x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t3.d	1	100.00	Child of 't3' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3x.c,test.t2x.c	1	100.00	Child of 't3x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t2x`.`a` AS `a`,`test`.`t2x`.`b` AS `b`,`test`.`t2x`.`c` AS `c`,`test`.`t2x`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t3x`.`a` AS `a`,`test`.`t3x`.`b` AS `b`,`test`.`t3x`.`c` AS `c`,`test`.`t3x`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t2x` join `test`.`t1` `t3` join `test`.`t1` `t3x` join `test`.`t1` `t4` where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t2x`.`b` = `test`.`t2`.`d`) and (`test`.`t2x`.`a` = `test`.`t2`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t3x`.`b` = `test`.`t3`.`d`) and (`test`.`t3x`.`a` = `test`.`t3`.`c`) and (`test`.`t4`.`b` = `test`.`t2x`.`c`) and (`test`.`t4`.`a` = `test`.`t3x`.`c`))
explain
select straight_join *
from t1
join t1 as t2  on t2.a = t1.a and t2.b = t1.b
join t1 as t3  on t3.a = t1.c and t3.b = t1.d
join t1 as t2x on t2x.a = t2.c and t2x.b = t2.d
join t1 as t3x on t3x.a = t3.c and t3x.b = t3.d
join t1 as t4  on t4.a = t3x.c and t4.b = t2x.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 6 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t2x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	Child of 't3' in pushed join@1
1	SIMPLE	t3x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t3.d	1	100.00	Child of 't2x' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3x.c,test.t2x.c	1	100.00	Child of 't3x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t2x`.`a` AS `a`,`test`.`t2x`.`b` AS `b`,`test`.`t2x`.`c` AS `c`,`test`.`t2x`.`d` AS `d`,`test`.`t3x`.`a` AS `a`,`test`.`t3x`.`b` AS `b`,`test`.`t3x`.`c` AS `c`,`test`.`t3x`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` join `test`.`t1` `t2x` join `test`.`t1` `t3x` join `test`.`t1` `t4` where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t3`.`b` = `test`.`t1`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t2x`.`b` = `test`.`t2`.`d`) and (`test`.`t2x`.`a` = `test`.`t2`.`c`) and (`test`.`t3x`.`b` = `test`.`t3`.`d`) and (`test`.`t3x`.`a` = `test`.`t3`.`c`) and (`test`.`t4`.`b` = `test`.`t2x`.`c`) and (`test`.`t4`.`a` = `test`.`t3x`.`c`))
explain
select straight_join *
from t1
join t1 as t2  on t2.a = t1.a and t2.b = t1.b
join t1 as t2x on t2x.a = t2.c and t2x.b = t2.d
join t1 as t3  on t3.a = t1.c and t3.b = t1.d
join t1 as t3x on t3x.a = t1.c and t3x.b = t1.d
join t1 as t4  on t4.a = t3x.c and t4.b = t2x.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 6 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t2x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't2x' in pushed join@1
1	SIMPLE	t3x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't3' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3x.c,test.t2x.c	1	100.00	Child of 't3x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t2x`.`a` AS `a`,`test`.`t2x`.`b` AS `b`,`test`.`t2x`.`c` AS `c`,`test`.`t2x`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t3x`.`a` AS `a`,`test`.`t3x`.`b` AS `b`,`test`.`t3x`.`c` AS `c`,`test`.`t3x`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t2x` join `test`.`t1` `t3` join `test`.`t1` `t3x` join `test`.`t1` `t4` where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t2x`.`b` = `test`.`t2`.`d`) and (`test`.`t2x`.`a` = `test`.`t2`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`d`) and (`test`.`t3x`.`b` = `test`.`t1`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t3x`.`a` = `test`.`t1`.`c`) and (`test`.`t4`.`b` = `test`.`t2x`.`c`) and (`test`.`t4`.`a` = `test`.`t3x`.`c`))
explain
select straight_join *
from t1
join t1 as t2  on t2.a = t1.a and t2.b = t1.b
join t1 as t2x on t2x.a = t2.c and t2x.b = t2.d
join t1 as t3  on t3.a = t1.c and t3.b = t1.b
join t1 as t3x on t3x.a = t1.c and t3x.b = t1.d
join t1 as t4  on t4.a = t3x.c and t4.b = t2x.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 6 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t2x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.b	1	100.00	Child of 't2x' in pushed join@1
1	SIMPLE	t3x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't3' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3x.c,test.t2x.c	1	100.00	Child of 't3x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t2x`.`a` AS `a`,`test`.`t2x`.`b` AS `b`,`test`.`t2x`.`c` AS `c`,`test`.`t2x`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t3x`.`a` AS `a`,`test`.`t3x`.`b` AS `b`,`test`.`t3x`.`c` AS `c`,`test`.`t3x`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t2x` join `test`.`t1` `t3` join `test`.`t1` `t3x` join `test`.`t1` `t4` where ((`test`.`t2`.`a` = `test`.`t1`.`a`) and (`test`.`t2x`.`b` = `test`.`t2`.`d`) and (`test`.`t2x`.`a` = `test`.`t2`.`c`) and (`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t3`.`b` = `test`.`t1`.`b`) and (`test`.`t3x`.`b` = `test`.`t1`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`c`) and (`test`.`t3x`.`a` = `test`.`t1`.`c`) and (`test`.`t4`.`b` = `test`.`t2x`.`c`) and (`test`.`t4`.`a` = `test`.`t3x`.`c`))
explain
select straight_join *
from t1
left join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.c and t3.b = t1.d
left join t1 as t4 on t4.a = t3.c and t4.b = t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t2.c	1	100.00	NULL
Warnings:
Note	1003	Can't push table 't4' as child of 't1', as it would make the parent table 't3' depend on table(s) outside of its join-nest
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` left join `test`.`t1` `t2` on(((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`))) join `test`.`t1` `t3` left join `test`.`t1` `t4` on(((`test`.`t4`.`b` = `test`.`t2`.`c`) and (`test`.`t4`.`a` = `test`.`t3`.`c`))) where ((`test`.`t3`.`b` = `test`.`t1`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`c`))
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
left join t1 as t3 on t3.a = t1.c and t3.b = t1.d
left join t1 as t4 on t4.a = t3.c and t4.b = t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t2.c	1	100.00	Child of 't3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` left join `test`.`t1` `t3` on(((`test`.`t3`.`b` = `test`.`t1`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`c`))) left join `test`.`t1` `t4` on(((`test`.`t4`.`b` = `test`.`t2`.`c`) and (`test`.`t4`.`a` = `test`.`t3`.`c`))) where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`))
explain
select straight_join *
from t1
left join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.a
left join t1 as t4 on t4.a = t3.c and t4.b = t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.a	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t2.c	1	100.00	NULL
Warnings:
Note	1003	Can't push table 't4' as child of 't1', as it would make the parent table 't3' depend on table(s) outside of its join-nest
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` left join `test`.`t1` `t2` on(((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`))) join `test`.`t1` `t3` left join `test`.`t1` `t4` on(((`test`.`t4`.`b` = `test`.`t2`.`c`) and (`test`.`t4`.`a` = `test`.`t3`.`c`))) where (`test`.`t3`.`a` = `test`.`t1`.`a`)
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
left join t1 as t3 on t3.a = t1.a
left join t1 as t4 on t4.a = t3.c and t4.b = t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.a	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t2.c	1	100.00	Child of 't3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` left join `test`.`t1` `t3` on((`test`.`t3`.`a` = `test`.`t1`.`a`)) left join `test`.`t1` `t4` on(((`test`.`t4`.`b` = `test`.`t2`.`c`) and (`test`.`t4`.`a` = `test`.`t3`.`c`))) where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`))
explain
select straight_join *
from t1
left join t1 as t2 on t2.a = t1.a and t2.b = t1.b
join t1 as t3 on t3.a = t1.a
left join t1 as t4 on t4.a = t3.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.a	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t3.c	1	100.00	Child of 't3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` left join `test`.`t1` `t2` on(((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`))) join `test`.`t1` `t3` left join `test`.`t1` `t4` on((`test`.`t4`.`a` = `test`.`t3`.`c`)) where (`test`.`t3`.`a` = `test`.`t1`.`a`)
explain
select straight_join *
from t1
join t1 as t2 on t2.a = t1.a and t2.b = t1.b
left join t1 as t3 on t3.a = t1.a
left join t1 as t4 on t4.a = t3.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.a	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t3.c	1	100.00	Child of 't3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` left join `test`.`t1` `t3` on((`test`.`t3`.`a` = `test`.`t1`.`a`)) left join `test`.`t1` `t4` on((`test`.`t4`.`a` = `test`.`t3`.`c`)) where ((`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`))
explain
select straight_join *
from
( t1 as t0 left join t1 as t1 on t1.a = t0.a and t1.b = t0.b
)
left join
( t1 as t2 join t1 as t3 on t3.a = t2.c and t3.b = t2.d
join t1 as t4 on t4.a = t3.c and t4.b = t2.c
)
on t2.a = t1.a and t2.b = t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t0	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 5 pushed join@1
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t0.a,test.t0.b	1	100.00	Child of 't0' in pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.a,test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t3.c,test.t2.c	1	100.00	Child of 't3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t0`.`a` AS `a`,`test`.`t0`.`b` AS `b`,`test`.`t0`.`c` AS `c`,`test`.`t0`.`d` AS `d`,`test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` `t0` left join `test`.`t1` on(((`test`.`t1`.`b` = `test`.`t0`.`b`) and (`test`.`t1`.`a` = `test`.`t0`.`a`))) left join (`test`.`t1` `t2` join `test`.`t1` `t3` join `test`.`t1` `t4`) on(((`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t3`.`a` = `test`.`t2`.`c`) and (`test`.`t4`.`b` = `test`.`t2`.`c`) and (`test`.`t4`.`a` = `test`.`t3`.`c`) and (`test`.`t2`.`b` = `test`.`t1`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`a`))) where true
explain
select straight_join *
from
t1 as x1
left join 
( t1 as x2 join t1 as x3 on x3.a=x2.c
join t1 as x4 on x4.a=x2.d
join t1 as x5 on x5.a=x3.d and x5.b=x4.d
)
on x2.a=x1.c and x2.b=x1.c and 
x3.b=x1.d and
x4.b=x1.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 5 pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x1.c,test.x1.c	1	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x2.c,test.x1.d	1	100.00	Child of 'x2' in pushed join@1
1	SIMPLE	x4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x2.d,test.x3.b	1	100.00	Child of 'x3' in pushed join@1; Using pushed condition (`test`.`x4`.`b` = `test`.`x1`.`d`)
1	SIMPLE	x5	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x3.d,test.x4.d	1	100.00	Child of 'x4' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x1`.`c` AS `c`,`test`.`x1`.`d` AS `d`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b`,`test`.`x2`.`c` AS `c`,`test`.`x2`.`d` AS `d`,`test`.`x3`.`a` AS `a`,`test`.`x3`.`b` AS `b`,`test`.`x3`.`c` AS `c`,`test`.`x3`.`d` AS `d`,`test`.`x4`.`a` AS `a`,`test`.`x4`.`b` AS `b`,`test`.`x4`.`c` AS `c`,`test`.`x4`.`d` AS `d`,`test`.`x5`.`a` AS `a`,`test`.`x5`.`b` AS `b`,`test`.`x5`.`c` AS `c`,`test`.`x5`.`d` AS `d` from `test`.`t1` `x1` left join (`test`.`t1` `x2` join `test`.`t1` `x3` join `test`.`t1` `x4` join `test`.`t1` `x5`) on(((`test`.`x3`.`a` = `test`.`x2`.`c`) and (`test`.`x4`.`a` = `test`.`x2`.`d`) and (`test`.`x5`.`b` = `test`.`x4`.`d`) and (`test`.`x5`.`a` = `test`.`x3`.`d`) and (`test`.`x3`.`b` = `test`.`x1`.`d`) and (`test`.`x4`.`b` = `test`.`x1`.`d`) and (`test`.`x2`.`a` = `test`.`x1`.`c`) and (`test`.`x2`.`b` = `test`.`x1`.`c`))) where true
explain
select straight_join * from
(t1 left join t1 as t2 on t2.a = t1.c and t2.b = t1.d)
inner join t1 as t3 on t3.a = t1.b and t3.b = t1.c
left join t1 as t4 on t4.a = t2.c and t4.b = t1.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t1.c	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` left join `test`.`t1` `t2` on(((`test`.`t2`.`b` = `test`.`t1`.`d`) and (`test`.`t2`.`a` = `test`.`t1`.`c`))) join `test`.`t1` `t3` left join `test`.`t1` `t4` on(((`test`.`t4`.`b` = `test`.`t1`.`c`) and (`test`.`t4`.`a` = `test`.`t2`.`c`))) where ((`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`a` = `test`.`t1`.`b`))
select straight_join * from
(t1 left join t1 as t2 on t2.a = t1.c and t2.b = t1.d)
inner join t1 as t3 on t3.a = t1.b and t3.b = t1.c
left join t1 as t4 on t4.a = t2.c and t4.b = t1.c;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	3	1	2	1	2	5	1	3	1	1	2	NULL	NULL	NULL	NULL
1	4	2	3	2	3	4	5	4	2	5	1	4	2	5	1
2	1	3	4	3	4	3	4	1	3	1	2	3	3	3	3
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
2	3	4	5	NULL	NULL	NULL	NULL	3	4	3	4	NULL	NULL	NULL	NULL
3	1	1	2	1	2	5	1	1	1	1	1	NULL	NULL	NULL	NULL
3	2	2	3	2	3	4	5	2	2	2	2	4	2	5	1
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	4	3	1	2	3	3	3	3
4	1	4	5	NULL	NULL	NULL	NULL	1	4	2	3	NULL	NULL	NULL	NULL
4	3	1	2	1	2	5	1	3	1	1	2	NULL	NULL	NULL	NULL
4	4	4	4	4	4	4	4	4	4	4	4	4	4	4	4
explain
select straight_join * from
(t1 left join t1 as t2 on t2.a = t1.c and t2.b = t1.d)
inner join t1 as t3 on t3.a = t2.b and t3.b = t1.c
left join t1 as t4 on t4.a = t2.c and t4.b = t1.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.d,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t1.c	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c`,`test`.`t4`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` left join `test`.`t1` `t4` on(((`test`.`t4`.`b` = `test`.`t1`.`c`) and (`test`.`t4`.`a` = `test`.`t2`.`c`))) where ((`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`b` = `test`.`t1`.`d`) and (`test`.`t3`.`a` = `test`.`t1`.`d`))
select straight_join * from
(t1 left join t1 as t2 on t2.a = t1.c and t2.b = t1.d)
inner join t1 as t3 on t3.a = t2.b and t3.b = t1.c
left join t1 as t4 on t4.a = t2.c and t4.b = t1.c;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	3	1	2	1	2	5	1	2	1	3	4	NULL	NULL	NULL	NULL
1	4	2	3	2	3	4	5	3	2	2	3	4	2	5	1
2	1	3	4	3	4	3	4	4	3	1	2	3	3	3	3
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
3	1	1	2	1	2	5	1	2	1	3	4	NULL	NULL	NULL	NULL
3	2	2	3	2	3	4	5	3	2	2	3	4	2	5	1
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	3	4	3	4	4	3	1	2	3	3	3	3
4	3	1	2	1	2	5	1	2	1	3	4	NULL	NULL	NULL	NULL
4	4	4	4	4	4	4	4	4	4	4	4	4	4	4	4
set ndb_join_pushdown=true;
explain
select * from t1 x, t1 y, t1 z, t1 where 
y.a=x.d and y.b=x.b and 
z.a=y.d and 
t1.a = z.d and t1.b=z.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d,test.x.b	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.y.d	1	100.00	Child of 'y' in pushed join@1
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.z.d,test.z.b	1	100.00	Child of 'z' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`x`.`c` AS `c`,`test`.`x`.`d` AS `d`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b`,`test`.`y`.`c` AS `c`,`test`.`y`.`d` AS `d`,`test`.`z`.`a` AS `a`,`test`.`z`.`b` AS `b`,`test`.`z`.`c` AS `c`,`test`.`z`.`d` AS `d`,`test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` `x` join `test`.`t1` `y` join `test`.`t1` `z` join `test`.`t1` where ((`test`.`t1`.`b` = `test`.`z`.`b`) and (`test`.`t1`.`a` = `test`.`z`.`d`) and (`test`.`z`.`a` = `test`.`y`.`d`) and (`test`.`y`.`b` = `test`.`x`.`b`) and (`test`.`y`.`a` = `test`.`x`.`d`))
select * from t1 x, t1 y, t1 z, t1 where 
y.a=x.d and y.b=x.b and 
z.a=y.d and 
t1.a = z.d and t1.b=z.b;
a	b	c	d	a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
1	1	1	1	1	1	1	1	1	2	5	1	1	2	5	1
1	1	1	1	1	1	1	1	1	3	1	2	2	3	4	5
1	1	1	1	1	1	1	1	1	4	2	3	3	4	3	4
1	2	5	1	1	2	5	1	1	1	1	1	1	1	1	1
1	2	5	1	1	2	5	1	1	2	5	1	1	2	5	1
1	2	5	1	1	2	5	1	1	3	1	2	2	3	4	5
1	2	5	1	1	2	5	1	1	4	2	3	3	4	3	4
1	4	2	3	3	4	3	4	4	2	5	1	1	2	5	1
1	4	2	3	3	4	3	4	4	3	1	2	2	3	4	5
1	4	2	3	3	4	3	4	4	4	4	4	4	4	4	4
2	2	2	2	2	2	2	2	2	1	3	4	4	1	4	5
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
2	2	2	2	2	2	2	2	2	4	5	1	1	4	2	3
2	4	5	1	1	4	2	3	3	1	1	2	2	1	3	4
2	4	5	1	1	4	2	3	3	2	2	3	3	2	2	3
2	4	5	1	1	4	2	3	3	3	3	3	3	3	3	3
2	4	5	1	1	4	2	3	3	4	3	4	4	4	4	4
3	1	1	2	2	1	3	4	4	2	5	1	1	2	5	1
3	1	1	2	2	1	3	4	4	3	1	2	2	3	4	5
3	1	1	2	2	1	3	4	4	4	4	4	4	4	4	4
3	2	2	3	3	2	2	3	3	1	1	2	2	1	3	4
3	2	2	3	3	2	2	3	3	2	2	3	3	2	2	3
3	2	2	3	3	2	2	3	3	3	3	3	3	3	3	3
3	2	2	3	3	2	2	3	3	4	3	4	4	4	4	4
3	3	3	3	3	3	3	3	3	1	1	2	2	1	3	4
3	3	3	3	3	3	3	3	3	2	2	3	3	2	2	3
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
3	3	3	3	3	3	3	3	3	4	3	4	4	4	4	4
3	4	3	4	4	4	4	4	4	2	5	1	1	2	5	1
3	4	3	4	4	4	4	4	4	3	1	2	2	3	4	5
3	4	3	4	4	4	4	4	4	4	4	4	4	4	4	4
4	2	5	1	1	2	5	1	1	1	1	1	1	1	1	1
4	2	5	1	1	2	5	1	1	2	5	1	1	2	5	1
4	2	5	1	1	2	5	1	1	3	1	2	2	3	4	5
4	2	5	1	1	2	5	1	1	4	2	3	3	4	3	4
4	4	4	4	4	4	4	4	4	2	5	1	1	2	5	1
4	4	4	4	4	4	4	4	4	3	1	2	2	3	4	5
4	4	4	4	4	4	4	4	4	4	4	4	4	4	4	4
explain
select * from t1 x, t1 y where
x.a <= 2 and
y.a=x.d and y.b=x.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	range	PRIMARY	PRIMARY	4	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`a` <= 2); Using MRR
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d,test.x.b	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`x`.`c` AS `c`,`test`.`x`.`d` AS `d`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b`,`test`.`y`.`c` AS `c`,`test`.`y`.`d` AS `d` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`b` = `test`.`x`.`b`) and (`test`.`y`.`a` = `test`.`x`.`d`) and (`test`.`x`.`a` <= 2))
select * from t1 x, t1 y where
x.a <= 2 and
y.a=x.d and y.b=x.b;
a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1
1	2	5	1	1	2	5	1
1	3	1	2	2	3	4	5
1	4	2	3	3	4	3	4
2	1	3	4	4	1	4	5
2	2	2	2	2	2	2	2
2	4	5	1	1	4	2	3
explain
select * from t1 x, t1 y where
(x.a <= 2 or x.a > 3) and
y.a=x.d and y.b=x.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	range	PRIMARY	PRIMARY	4	NULL	6	100.00	Parent of 2 pushed join@1; Using pushed condition ((`test`.`x`.`a` <= 2) or (`test`.`x`.`a` > 3)); Using MRR
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d,test.x.b	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`x`.`c` AS `c`,`test`.`x`.`d` AS `d`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b`,`test`.`y`.`c` AS `c`,`test`.`y`.`d` AS `d` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`b` = `test`.`x`.`b`) and (`test`.`y`.`a` = `test`.`x`.`d`) and ((`test`.`x`.`a` <= 2) or (`test`.`x`.`a` > 3)))
select * from t1 x, t1 y where
(x.a <= 2 or x.a > 3) and
y.a=x.d and y.b=x.b;
a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1
1	2	5	1	1	2	5	1
1	3	1	2	2	3	4	5
1	4	2	3	3	4	3	4
2	1	3	4	4	1	4	5
2	2	2	2	2	2	2	2
2	4	5	1	1	4	2	3
4	2	5	1	1	2	5	1
4	3	1	2	2	3	4	5
4	4	4	4	4	4	4	4
explain
select * from t1 x, t1 y where
(x.a >= 2 or x.a < 3) and
y.a=x.d and y.b=x.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`x`.`a` >= 2) or (`test`.`x`.`a` < 3))
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d,test.x.b	#	#	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`x`.`c` AS `c`,`test`.`x`.`d` AS `d`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b`,`test`.`y`.`c` AS `c`,`test`.`y`.`d` AS `d` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`b` = `test`.`x`.`b`) and (`test`.`y`.`a` = `test`.`x`.`d`) and ((`test`.`x`.`a` >= 2) or (`test`.`x`.`a` < 3)))
select * from t1 x, t1 y where
(x.a >= 2 or x.a < 3) and
y.a=x.d and y.b=x.b;
a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1
1	2	5	1	1	2	5	1
1	3	1	2	2	3	4	5
1	4	2	3	3	4	3	4
2	1	3	4	4	1	4	5
2	2	2	2	2	2	2	2
2	4	5	1	1	4	2	3
3	1	1	2	2	1	3	4
3	2	2	3	3	2	2	3
3	3	3	3	3	3	3	3
3	4	3	4	4	4	4	4
4	2	5	1	1	2	5	1
4	3	1	2	2	3	4	5
4	4	4	4	4	4	4	4
explain
select * from t1 x, t1 y where
(x.a <= 2 or x.a in (0,5,4)) and
y.a=x.d and y.b=x.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	range	PRIMARY	PRIMARY	4	NULL	9	100.00	Parent of 2 pushed join@1; Using pushed condition ((`test`.`x`.`a` <= 2) or (`test`.`x`.`a` in (0,5,4))); Using MRR
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d,test.x.b	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`x`.`c` AS `c`,`test`.`x`.`d` AS `d`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b`,`test`.`y`.`c` AS `c`,`test`.`y`.`d` AS `d` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`b` = `test`.`x`.`b`) and (`test`.`y`.`a` = `test`.`x`.`d`) and ((`test`.`x`.`a` <= 2) or (`test`.`x`.`a` in (0,5,4))))
select * from t1 x, t1 y where
(x.a <= 2 or x.a in (0,5,4)) and
y.a=x.d and y.b=x.b;
a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1
1	2	5	1	1	2	5	1
1	3	1	2	2	3	4	5
1	4	2	3	3	4	3	4
2	1	3	4	4	1	4	5
2	2	2	2	2	2	2	2
2	4	5	1	1	4	2	3
4	2	5	1	1	2	5	1
4	3	1	2	2	3	4	5
4	4	4	4	4	4	4	4
explain
select * from t1 x, t1 y where
(x.a <= 2 or (x.a,x.b) in ((0,0),(5,0),(4,3))) and
y.a=x.d and y.b=x.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	range	PRIMARY	PRIMARY	8	NULL	5	100.00	Parent of 2 pushed join@1; Using where; Using MRR
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d,test.x.b	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`x`.`c` AS `c`,`test`.`x`.`d` AS `d`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b`,`test`.`y`.`c` AS `c`,`test`.`y`.`d` AS `d` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`b` = `test`.`x`.`b`) and (`test`.`y`.`a` = `test`.`x`.`d`) and ((`test`.`x`.`a` <= 2) or ((`test`.`x`.`a`,`test`.`x`.`b`) in ((0,0),(5,0),(4,3)))))
select * from t1 x, t1 y where
(x.a <= 2 or (x.a,x.b) in ((0,0),(5,0),(4,3))) and
y.a=x.d and y.b=x.b;
a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1
1	2	5	1	1	2	5	1
1	3	1	2	2	3	4	5
1	4	2	3	3	4	3	4
2	1	3	4	4	1	4	5
2	2	2	2	2	2	2	2
2	4	5	1	1	4	2	3
4	3	1	2	2	3	4	5
explain format=tree
select straight_join t1.b, count(distinct t2.b) as cnt
from t1
join t1 as t2 on t2.a = t1.c
group by t1.b
order by t1.b, cnt;
EXPLAIN
-> Sort: t1.b, cnt
    -> Stream results  (cost=4015 rows=4)
        -> Group aggregate: count(distinct t2.b)  (cost=4015 rows=4)
            -> Nested loop inner join  (cost=4011 rows=16)
                -> Sort: t1.b  (cost=4002 rows=16)
                    -> Table scan on t1  (cost=4002 rows=16)
                -> Index lookup on t2 using PRIMARY (a = t1.c)  (cost=0.506 rows=1)

Warnings:
Note	1003	Can't push table 't2' as child of 't1', it is in a sorted-branch which can't be referred.
select straight_join t1.b, count(distinct t2.b) as cnt
from t1
join t1 as t2 on t2.a = t1.c
group by t1.b
order by t1.b, cnt;
b	cnt
1	4
2	4
3	4
4	4
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.c,t1.d,
t1.a, t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Using filesort
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	Can't push table 't2' as child of 't1', it is in a sorted-branch which can't be referred.
Note	1003	Can't push table 't3' as child of 't1', it is in a sorted-branch which can't be referred.
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`)) order by `test`.`t1`.`c`,`test`.`t1`.`d`,`test`.`t1`.`a`,`test`.`t1`.`b`
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.c,t1.d,
t1.a, t1.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	3	1	2	3	1	1	2	3	1	1	2
3	1	1	2	1	1	1	1	1	1	1	1
4	3	1	2	3	1	1	2	3	1	1	2
2	2	2	2	2	2	2	2	2	2	2	2
1	4	2	3	4	2	5	1	4	2	5	1
3	2	2	3	2	2	2	2	2	2	2	2
3	3	3	3	3	3	3	3	3	3	3	3
2	1	3	4	1	3	1	2	1	3	1	2
3	4	3	4	4	3	1	2	4	3	1	2
4	4	4	4	4	4	4	4	4	4	4	4
2	3	4	5	3	4	3	4	3	4	3	4
4	1	4	5	1	4	2	3	1	4	2	3
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.c,t2.d,
t1.a, t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1; Using temporary; Using filesort
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`)) order by `test`.`t1`.`c`,`test`.`t2`.`d`,`test`.`t1`.`a`,`test`.`t1`.`b`
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.c,t2.d,
t1.a, t1.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
3	1	1	2	1	1	1	1	1	1	1	1
1	3	1	2	3	1	1	2	3	1	1	2
4	3	1	2	3	1	1	2	3	1	1	2
1	4	2	3	4	2	5	1	4	2	5	1
2	2	2	2	2	2	2	2	2	2	2	2
3	2	2	3	2	2	2	2	2	2	2	2
2	1	3	4	1	3	1	2	1	3	1	2
3	4	3	4	4	3	1	2	4	3	1	2
3	3	3	3	3	3	3	3	3	3	3	3
4	1	4	5	1	4	2	3	1	4	2	3
2	3	4	5	3	4	3	4	3	4	3	4
4	4	4	4	4	4	4	4	4	4	4	4
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.a,t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	8	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`)) order by `test`.`t1`.`a`,`test`.`t1`.`b`
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.a,t1.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	3	1	2	3	1	1	2	3	1	1	2
1	4	2	3	4	2	5	1	4	2	5	1
2	1	3	4	1	3	1	2	1	3	1	2
2	2	2	2	2	2	2	2	2	2	2	2
2	3	4	5	3	4	3	4	3	4	3	4
3	1	1	2	1	1	1	1	1	1	1	1
3	2	2	3	2	2	2	2	2	2	2	2
3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	4	3	1	2	4	3	1	2
4	1	4	5	1	4	2	3	1	4	2	3
4	3	1	2	3	1	1	2	3	1	1	2
4	4	4	4	4	4	4	4	4	4	4	4
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.a,t2.b,
t1.a, t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1; Using temporary; Using filesort
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`)) order by `test`.`t1`.`a`,`test`.`t2`.`b`,`test`.`t1`.`b`
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.a,t2.b,
t1.a, t1.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	3	1	2	3	1	1	2	3	1	1	2
1	4	2	3	4	2	5	1	4	2	5	1
2	2	2	2	2	2	2	2	2	2	2	2
2	1	3	4	1	3	1	2	1	3	1	2
2	3	4	5	3	4	3	4	3	4	3	4
3	1	1	2	1	1	1	1	1	1	1	1
3	2	2	3	2	2	2	2	2	2	2	2
3	3	3	3	3	3	3	3	3	3	3	3
3	4	3	4	4	3	1	2	4	3	1	2
4	3	1	2	3	1	1	2	3	1	1	2
4	1	4	5	1	4	2	3	1	4	2	3
4	4	4	4	4	4	4	4	4	4	4	4
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.a desc,t1.b desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	8	NULL	16	100.00	Parent of 3 pushed join@1; Backward index scan
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`)) order by `test`.`t1`.`a` desc,`test`.`t1`.`b` desc
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.a desc,t1.b desc;
a	b	c	d	a	b	c	d	a	b	c	d
4	4	4	4	4	4	4	4	4	4	4	4
4	3	1	2	3	1	1	2	3	1	1	2
4	1	4	5	1	4	2	3	1	4	2	3
3	4	3	4	4	3	1	2	4	3	1	2
3	3	3	3	3	3	3	3	3	3	3	3
3	2	2	3	2	2	2	2	2	2	2	2
3	1	1	2	1	1	1	1	1	1	1	1
2	3	4	5	3	4	3	4	3	4	3	4
2	2	2	2	2	2	2	2	2	2	2	2
2	1	3	4	1	3	1	2	1	3	1	2
1	4	2	3	4	2	5	1	4	2	5	1
1	3	1	2	3	1	1	2	3	1	1	2
1	1	1	1	1	1	1	1	1	1	1	1
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.b,t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Using filesort
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	Can't push table 't2' as child of 't1', it is in a sorted-branch which can't be referred.
Note	1003	Can't push table 't3' as child of 't1', it is in a sorted-branch which can't be referred.
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`)) order by `test`.`t1`.`b`,`test`.`t1`.`a`
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.b,t1.a;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
2	1	3	4	1	3	1	2	1	3	1	2
3	1	1	2	1	1	1	1	1	1	1	1
4	1	4	5	1	4	2	3	1	4	2	3
2	2	2	2	2	2	2	2	2	2	2	2
3	2	2	3	2	2	2	2	2	2	2	2
1	3	1	2	3	1	1	2	3	1	1	2
2	3	4	5	3	4	3	4	3	4	3	4
3	3	3	3	3	3	3	3	3	3	3	3
4	3	1	2	3	1	1	2	3	1	1	2
1	4	2	3	4	2	5	1	4	2	5	1
3	4	3	4	4	3	1	2	4	3	1	2
4	4	4	4	4	4	4	4	4	4	4	4
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	8	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`)) order by `test`.`t1`.`a`
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
order by t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Using filesort
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	Can't push table 't2' as child of 't1', it is in a sorted-branch which can't be referred.
Note	1003	Can't push table 't3' as child of 't1', it is in a sorted-branch which can't be referred.
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t3`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`)) order by `test`.`t1`.`b`
explain
select t1.a, t1.b, count(*)
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
group by t1.a, t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	index	PRIMARY	PRIMARY	8	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`)) group by `test`.`t1`.`a`,`test`.`t1`.`b`
select t1.a, t1.b, count(*)
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
group by t1.a, t1.b;
a	b	count(*)
1	1	1
1	3	1
1	4	1
2	1	1
2	2	1
2	3	1
3	1	1
3	2	1
3	3	1
3	4	1
4	1	1
4	3	1
4	4	1
explain
select t1.a, count(*)
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
group by t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	index	PRIMARY	PRIMARY	8	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`)) group by `test`.`t1`.`a`
select t1.a, count(*)
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
group by t1.a;
a	count(*)
1	3
2	3
3	4
4	3
explain
select t1.b, count(*)
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
group by t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1; Using temporary
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`b` AS `b`,count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t1`.`b`)) group by `test`.`t1`.`b`
select t1.b, count(*)
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
join t1 as t3 on t3.a = t2.a and t3.b = t2.b
group by t1.b;
b	count(*)
1	4
2	2
3	4
4	3
explain
select t2.c, count(distinct t2.a)
from t1
join t1 as t2 on t1.a = t2.c and t1.b = t2.d
where t2.a = 4 and t2.b=4
group by t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	pX	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t1	pXYZ	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`c` AS `c`,count(distinct `test`.`t2`.`a`) AS `count(distinct t2.a)` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t1`.`b` = `test`.`t2`.`d`) and (`test`.`t1`.`a` = `test`.`t2`.`c`) and (`test`.`t2`.`b` = 4) and (`test`.`t2`.`a` = 4)) group by `test`.`t2`.`c`
select t2.c, count(distinct t2.a)
from t1
join t1 as t2 on t1.a = t2.c and t1.b = t2.d
where t2.a = 4 and t2.b=4
group by t2.c;
c	count(distinct t2.a)
4	1
explain
select t2.c, count(distinct t2.a)
from t1
join t1 as t2 on t1.a = t2.c and t1.b = t2.d
where t2.a = 4
group by t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Using filesort
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	NULL
Warnings:
Note	1003	Can't push table 't1' as child of 't2', it is in a sorted-branch which can't be referred.
Note	1003	/* select#1 */ select `test`.`t2`.`c` AS `c`,count(distinct `test`.`t2`.`a`) AS `count(distinct t2.a)` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t1`.`b` = `test`.`t2`.`d`) and (`test`.`t1`.`a` = `test`.`t2`.`c`) and (`test`.`t2`.`a` = 4)) group by `test`.`t2`.`c`
select t2.c, count(distinct t2.a)
from t1
join t1 as t2 on t1.a = t2.c and t1.b = t2.d
where t2.a = 4
group by t2.c;
c	count(distinct t2.a)
1	1
4	1
explain
select t2.c, count(distinct t2.a)
from t1
join t1 as t2 on t1.a = t2.c and t1.b = t2.d
where t2.a = 4 and t2.b=4
group by t2.c order by t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	pX	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t1	pXYZ	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`c` AS `c`,count(distinct `test`.`t2`.`a`) AS `count(distinct t2.a)` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t1`.`b` = `test`.`t2`.`d`) and (`test`.`t1`.`a` = `test`.`t2`.`c`) and (`test`.`t2`.`b` = 4) and (`test`.`t2`.`a` = 4)) group by `test`.`t2`.`c` order by `test`.`t2`.`c`
select t2.c, count(distinct t2.a)
from t1
join t1 as t2 on t1.a = t2.c and t1.b = t2.d
where t2.a = 4 and t2.b=4
group by t2.c order by t2.c;
c	count(distinct t2.a)
4	1
create table tx like t1;
insert into tx 
select x1.a+x2.a*16, x1.b+x2.b*16, x1.c+x2.c*16, x1.d+x2.d*16 
from t1 as x1 cross join t1 as x2;
explain select count(*) from tx as x1 
left join tx as x2 on x1.c=x2.a and x1.d=x2.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	256	100.00	Parent of 2 pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x1.c	2	100.00	Child of 'x1' in pushed join@1; Using pushed condition (`test`.`x2`.`d` = `test`.`x1`.`d`)
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`tx` `x1` left join `test`.`tx` `x2` on(((`test`.`x2`.`d` = `test`.`x1`.`d`) and (`test`.`x2`.`a` = `test`.`x1`.`c`))) where true
select count(*) from tx as x1 
left join tx as x2 on x1.c=x2.a and x1.d=x2.d;
count(*)
304
drop table tx;
alter table t1 partition by key(a);
explain select count(*) from t1 
join t1 as t2 on t2.a = t1.c 
join t1 as t3 on t3.a = t1.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.c	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.d	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t3`.`a` = `test`.`t1`.`d`))
select count(*) from t1 
join t1 as t2 on t2.a = t1.c 
join t1 as t3 on t3.a = t1.d;
count(*)
176
CREATE TABLE tx (
a int NOT NULL,
PRIMARY KEY (`a`)
) comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
delete from t1;
insert into tx values (0), (1), (2), (3), (4), (5), (6), (7), (8), (9);
insert into t1 select 1, x1.a * 10+x2.a, 1, 1 from tx as x1 cross join tx as x2;
set global debug='+d,max_64rows_in_spj_batches';
explain select count(*) from t1 as x1
join t1 as x2 on x2.a = x1.c and x1.b < 2 
join t1 as x3 on x3.a = x1.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 3 pushed join@1; Using pushed condition (`test`.`x1`.`b` < 2)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x1.c	#	#	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x1.d	#	#	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t1` `x3` where ((`test`.`x2`.`a` = `test`.`x1`.`c`) and (`test`.`x3`.`a` = `test`.`x1`.`d`) and (`test`.`x1`.`b` < 2))
select count(*) from t1 as x1
join t1 as x2 on x2.a = x1.c and x1.b < 2 
join t1 as x3 on x3.a = x1.d;
count(*)
20000
set global debug=@save_debug;
drop table t1;
drop table tx;
create table t1 (
a int not null,
b int not null,
c int not null,
d int not null,
primary key (`a`,`b`)
) engine=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM"
  partition by key(a);
insert into t1 values
(1,1,1,1), (2,2,2,2), (3,3,3,3), (4,4,4,4),
(1,2,5,1), (1,3,1,2), (1,4,2,3),
(2,1,3,4), (2,3,4,5), (2,4,5,1),
(3,1,1,2), (3,2,2,3), (3,4,3,4),
(4,1,4,5), (4,2,5,1), (4,3,1,2);
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`))
alter table t1 partition by hash(a);
Warnings:
Warning	6035	'PARTITION BY HASH' for 'ndbcluster' storage engine is deprecated and will be removed in a future release.
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	NULL
Warnings:
Note	1003	Table 't1' is not pushable: has user defined partioning
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`))
alter table t1 partition by list(a) (
partition p1 values in (1),
partition p2 values in (2),
partition p3 values in (3),
partition p4 values in (4)
);
Warnings:
Warning	6035	'PARTITION BY LIST' for 'ndbcluster' storage engine is deprecated and will be removed in a future release.
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	16	100.00	NULL
1	SIMPLE	t2	p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	NULL
Warnings:
Note	1003	Table 't1' is not pushable: has user defined partioning
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`))
alter table t1 partition by range(a) partitions 4 (
partition p1 values less than (0),
partition p2 values less than (2),
partition p3 values less than (4),
partition p4 values less than (99999)
);
Warnings:
Warning	6035	'PARTITION BY RANGE' for 'ndbcluster' storage engine is deprecated and will be removed in a future release.
explain
select *
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p1,p2,p3,p4	ALL	NULL	NULL	NULL	NULL	16	100.00	NULL
1	SIMPLE	t2	p1,p2,p3,p4	eq_ref	PRIMARY	PRIMARY	8	test.t1.b,test.t1.c	1	100.00	NULL
Warnings:
Note	1003	Table 't1' is not pushable: has user defined partioning
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`b` = `test`.`t1`.`c`) and (`test`.`t2`.`a` = `test`.`t1`.`b`))
drop table t1;
create table t1 (a int, b int, primary key(a) using hash) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values (1, 2);
insert into t1 values (2, 3);
insert into t1 values (3, 1);
set ndb_join_pushdown=true;
set autocommit=off;
explain
select *
from t1, t1 as t2
where t1.a in (1,3,5)
and t2.a = t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	range	PRIMARY	PRIMARY	4	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition ((`test`.`t1`.`a` in (1,3,5)) and (`test`.`t1`.`b` is not null)); Using MRR
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t1`.`a` in (1,3,5)))
select *
from t1, t1 as t2
where t1.a in (1,3,5)
and t2.a = t1.b;
a	b	a	b
1	2	2	3
3	1	1	2
ndb_execute_count
3
This should yield 3 executes (for now...buh)
set autocommit=on;
drop table t1;
create table t1 (a int, b int, primary key(a)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values (1, 2);
insert into t1 values (2, 3);
insert into t1 values (3, 1);
set ndb_join_pushdown=true;
set autocommit=off;
explain
select *
from t1, t1 as t2
where t1.a in (1,3,5)
and t2.a = t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	range	PRIMARY	PRIMARY	4	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition ((`test`.`t1`.`a` in (1,3,5)) and (`test`.`t1`.`b` is not null)); Using MRR
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t1`.`a` in (1,3,5)))
select *
from t1, t1 as t2
where t1.a in (1,3,5)
and t2.a = t1.b;
a	b	a	b
1	2	2	3
3	1	1	2
ndb_execute_count
1
This should yield 1 execute (but inefficient since it's based on scan)
set autocommit=on;
explain
select *
from t1, t1 as t2
where t1.a in (1,3,5)
and t2.a = t1.b
order by t1.a desc;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	range	PRIMARY	PRIMARY	4	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition ((`test`.`t1`.`a` in (1,3,5)) and (`test`.`t1`.`b` is not null)); Backward index scan; Using MRR
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t1`.`a` in (1,3,5))) order by `test`.`t1`.`a` desc
select *
from t1, t1 as t2
where t1.a in (1,3,5)
and t2.a = t1.b
order by t1.a desc;
a	b	a	b
3	1	1	2
1	2	2	3
Warnings:
Warning	1296	Prepared pushed join could not be executed, prepared with incompatible access type
Warning	1296	Prepared pushed join could not be executed, prepared with incompatible access type
Warning	1296	Prepared pushed join could not be executed, prepared with incompatible access type
drop table t1;
set ndb_join_pushdown=true;
create table t1 (a int, b int, primary key(a)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
create table t2 (c int, d int, primary key(c)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
create table t3 (a3 int, b3 int, c3 int not null, d3 int not null,
primary key(a3, b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
create table t3_hash (a3 int, b3 int, c3 int not null, d3 int not null,
primary key(a3, b3) using hash) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values (0x1f, 0x2f);
insert into t1 values (0x2f, 0x3f);
insert into t1 values (0x3f, 0x1f);
insert into t2 values (0x1f, 0x2f);
insert into t2 values (0x2f, 0x3f);
insert into t2 values (0x3f, 0x1f);
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
insert into t3_hash values (0x1f, 0x2f, 1, 0x1f);
insert into t3_hash values (0x2f, 0x3f, 2, 0x2f);
insert into t3_hash values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y, t1 where y.a3=x.d3 and y.b3=x.b3 and t1.a = y.d3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	3	100.00	Parent of 3 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d3,test.x.b3	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.y.d3	1	100.00	Child of 'y' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3`,`test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t3` `x` join `test`.`t3` `y` join `test`.`t1` where ((`test`.`t1`.`a` = `test`.`y`.`d3`) and (`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`a3` = `test`.`x`.`d3`))
select * from t3 x, t3 y, t1 where y.a3=x.d3 and y.b3=x.b3 and t1.a = y.d3;
a3	b3	c3	d3	a3	b3	c3	d3	a	b
31	47	1	31	31	47	1	31	31	47
47	63	2	47	47	63	2	47	47	63
63	31	3	63	63	31	3	63	63	31
explain
select *
from t3 x, t3 y, t3 z, t3 z2, t1
where y.a3=x.d3 and y.b3=x.b3 and
z.a3=y.d3 and z.b3=y.b3 and
z2.a3=z.d3 and z2.b3=z.b3 and
t1.a = z2.d3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	3	100.00	Parent of 5 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d3,test.x.b3	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.y.d3,test.x.b3	1	100.00	Child of 'y' in pushed join@1
1	SIMPLE	z2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.z.d3,test.x.b3	1	100.00	Child of 'z' in pushed join@1
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.z2.d3	1	100.00	Child of 'z2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3`,`test`.`z`.`a3` AS `a3`,`test`.`z`.`b3` AS `b3`,`test`.`z`.`c3` AS `c3`,`test`.`z`.`d3` AS `d3`,`test`.`z2`.`a3` AS `a3`,`test`.`z2`.`b3` AS `b3`,`test`.`z2`.`c3` AS `c3`,`test`.`z2`.`d3` AS `d3`,`test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t3` `x` join `test`.`t3` `y` join `test`.`t3` `z` join `test`.`t3` `z2` join `test`.`t1` where ((`test`.`t1`.`a` = `test`.`z2`.`d3`) and (`test`.`z2`.`a3` = `test`.`z`.`d3`) and (`test`.`z`.`a3` = `test`.`y`.`d3`) and (`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`z`.`b3` = `test`.`x`.`b3`) and (`test`.`z2`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`a3` = `test`.`x`.`d3`))
select *
from t3 x, t3 y, t3 z, t3 z2, t1
where y.a3=x.d3 and y.b3=x.b3 and
z.a3=y.d3 and z.b3=y.b3 and
z2.a3=z.d3 and z2.b3=z.b3 and
t1.a = z2.d3;
a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3	a	b
31	47	1	31	31	47	1	31	31	47	1	31	31	47	1	31	31	47
47	63	2	47	47	63	2	47	47	63	2	47	47	63	2	47	47	63
63	31	3	63	63	31	3	63	63	31	3	63	63	31	3	63	63	31
explain
select straight_join * from t1 x, t1 y where y.a=0x1f and x.b = 0x1f;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Using pushed condition (`test`.`x`.`b` = 0x1f)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	Can't push table 'y' as child of 'x', no parent-child dependency exists between these tables
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`a` = 0x1f) and (`test`.`x`.`b` = 0x1f))
select straight_join * from t1 x, t1 y where y.a=0x1f and x.b = 0x1f;
a	b	a	b
63	31	31	47
explain
select straight_join * from t1 x, t1 y where y.a=x.b and x.b = 0x1f;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Using pushed condition (`test`.`x`.`b` = 0x1f)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	#	#	Using where
Warnings:
Note	1003	Can't push table 'y' as child of 'x', no parent-child dependency exists between these tables
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`a` = `test`.`x`.`b`) and (`test`.`x`.`b` = 0x1f))
select straight_join * from t1 x, t1 y where y.a=x.b and x.b = 0x1f;
a	b	a	b
63	31	31	47
create unique index t3_d3 on t3(d3);
create unique index t3_d3 on t3_hash(d3);
commit;
explain
select * from t3 x, t3 y where x.d3=31 and y.a3=x.d3 and y.b3=x.b3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	const,test.x.b3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`x`.`d3` = 31) and (`test`.`y`.`a3` = 31))
select * from t3 x, t3 y where x.d3=31 and y.a3=x.d3 and y.b3=x.b3;
a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	31	47	1	31
explain
select * from t3 x, t3 y where x.d3=0 and y.a3=x.d3 and y.b3=x.b3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	const,test.x.b3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`x`.`d3` = 0) and (`test`.`y`.`a3` = 0))
select * from t3 x, t3 y where x.d3=0 and y.a3=x.d3 and y.b3=x.b3;
a3	b3	c3	d3	a3	b3	c3	d3
explain
select * from t1 x, t3 y where y.d3=x.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`b` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x.b	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t1` `x` join `test`.`t3` `y` where (`test`.`y`.`d3` = `test`.`x`.`b`)
select * from t1 x, t3 y where y.d3=x.b;
a	b	a3	b3	c3	d3
31	47	47	63	2	47
47	63	63	31	3	63
63	31	31	47	1	31
explain
select * from t3 x, t3 y where x.d3=31 and y.d3=x.b3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x.b3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`d3` = `test`.`x`.`b3`) and (`test`.`x`.`d3` = 31))
select * from t3 x, t3 y where x.d3=31 and y.d3=x.b3;
a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	47	63	2	47
explain
select * from t3 x, t3 y where x.d3=31 and y.d3=x.c3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x.c3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`d3` = `test`.`x`.`c3`) and (`test`.`x`.`d3` = 31))
select * from t3 x, t3 y where x.d3=31 and y.d3=x.c3;
a3	b3	c3	d3	a3	b3	c3	d3
explain
select * from t3 x, t3 y 
where ((x.a3=0x2f and x.b3=0x3f) or x.d3=0x1f)
and  (y.a3=x.d3 and y.b3=x.b3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	index_merge	PRIMARY,t3_d3	t3_d3,PRIMARY	4,8	NULL	2	100.00	Parent of 2 pushed join@1; Using sort_union(t3_d3,PRIMARY); Using pushed condition (((`test`.`x`.`a3` = 0x2f) and (`test`.`x`.`b3` = 0x3f)) or (`test`.`x`.`d3` = 0x1f))
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d3,test.x.b3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (((`test`.`x`.`a3` = 0x2f) and (`test`.`x`.`b3` = 0x3f)) or (`test`.`x`.`d3` = 0x1f)))
select * from t3 x, t3 y
where ((x.a3=0x2f and x.b3=0x3f) or x.d3=0x1f)
and  (y.a3=x.d3 and y.b3=x.b3);
a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	31	47	1	31
47	63	2	47	47	63	2	47
explain
select * from t3_hash x, t3_hash y
where ((x.a3=0x2f and x.b3=0x3f) or x.d3=0x1f)
and  (y.a3=x.d3 and y.b3=x.b3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	index_merge	PRIMARY,t3_d3	PRIMARY,t3_d3	8,4	NULL	2	100.00	Parent of 2 pushed join@1; Using sort_union(PRIMARY,t3_d3); Using pushed condition (((`test`.`x`.`a3` = 0x2f) and (`test`.`x`.`b3` = 0x3f)) or (`test`.`x`.`d3` = 0x1f))
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d3,test.x.b3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3_hash` `x` join `test`.`t3_hash` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (((`test`.`x`.`a3` = 0x2f) and (`test`.`x`.`b3` = 0x3f)) or (`test`.`x`.`d3` = 0x1f)))
select * from t3_hash x, t3_hash y
where ((x.a3=0x2f and x.b3=0x3f) or x.d3=0x1f)
and  (y.a3=x.d3 and y.b3=x.b3);
a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	31	47	1	31
47	63	2	47	47	63	2	47
explain
select * from t3 x, t3 y where x.d3>=31 and y.d3=x.b3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	range	t3_d3	t3_d3	4	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`d3` >= 31); Using MRR
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x.b3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`d3` = `test`.`x`.`b3`) and (`test`.`x`.`d3` >= 31))
select * from t3 x, t3 y where x.d3>=31 and y.d3=x.b3;
a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	47	63	2	47
47	63	2	47	63	31	3	63
63	31	3	63	31	47	1	31
insert into t1 values (0x4f, null);
select * from t1 left join t1 as t2 on t2.a = t1.b;
a	b	a	b
31	47	47	63
47	63	63	31
63	31	31	47
79	NULL	NULL	NULL
insert into t3 values (8,8,8,8);
explain select count(*) from t3 as x0
join t3 as x1 on x0.b3=x1.d3 and x0.d3=8
join t3 as x2 on x1.b3=x2.d3
join t3 as x3 on x2.b3=x3.d3
join t3 as x4 on x3.b3=x4.d3
join t3 as x5 on x4.b3=x5.d3
join t3 as x6 on x5.b3=x6.d3
join t3 as x7 on x6.b3=x7.d3
join t3 as x8 on x7.b3=x8.d3
join t3 as x9 on x8.b3=x9.d3
join t3 as x10 on x9.b3=x10.d3
join t3 as x11 on x10.b3=x11.d3
join t3 as x12 on x11.b3=x12.d3
join t3 as x13 on x12.b3=x13.d3
join t3 as x14 on x13.b3=x14.d3
join t3 as x15 on x14.b3=x15.d3
join t3 as x16 on x15.b3=x16.d3
join t3 as x17 on x16.b3=x17.d3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x0	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	const	1	100.00	Parent of 16 pushed join@1
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x0.b3	1	100.00	Child of 'x0' in pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x1.b3	1	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x2.b3	1	100.00	Child of 'x2' in pushed join@1
1	SIMPLE	x4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x3.b3	1	100.00	Child of 'x3' in pushed join@1
1	SIMPLE	x5	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x4.b3	1	100.00	Child of 'x4' in pushed join@1
1	SIMPLE	x6	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x5.b3	1	100.00	Child of 'x5' in pushed join@1
1	SIMPLE	x7	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x6.b3	1	100.00	Child of 'x6' in pushed join@1
1	SIMPLE	x8	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x7.b3	1	100.00	Child of 'x7' in pushed join@1
1	SIMPLE	x9	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x8.b3	1	100.00	Child of 'x8' in pushed join@1
1	SIMPLE	x10	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x9.b3	1	100.00	Child of 'x9' in pushed join@1
1	SIMPLE	x11	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x10.b3	1	100.00	Child of 'x10' in pushed join@1
1	SIMPLE	x12	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x11.b3	1	100.00	Child of 'x11' in pushed join@1
1	SIMPLE	x13	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x12.b3	1	100.00	Child of 'x12' in pushed join@1
1	SIMPLE	x14	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x13.b3	1	100.00	Child of 'x13' in pushed join@1
1	SIMPLE	x15	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x14.b3	1	100.00	Child of 'x14' in pushed join@1
1	SIMPLE	x16	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x15.b3	1	100.00	Parent of 2 pushed join@2
1	SIMPLE	x17	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x16.b3	1	100.00	Child of 'x16' in pushed join@2
Warnings:
Note	1003	Can't push table 'x16' as child of 'x0'. Max number of pushable tables exceeded.
Note	1003	Can't push table 'x17' as child of 'x0'. Max number of pushable tables exceeded.
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t3` `x0` join `test`.`t3` `x1` join `test`.`t3` `x2` join `test`.`t3` `x3` join `test`.`t3` `x4` join `test`.`t3` `x5` join `test`.`t3` `x6` join `test`.`t3` `x7` join `test`.`t3` `x8` join `test`.`t3` `x9` join `test`.`t3` `x10` join `test`.`t3` `x11` join `test`.`t3` `x12` join `test`.`t3` `x13` join `test`.`t3` `x14` join `test`.`t3` `x15` join `test`.`t3` `x16` join `test`.`t3` `x17` where ((`test`.`x0`.`d3` = 8) and (`test`.`x1`.`d3` = `test`.`x0`.`b3`) and (`test`.`x2`.`d3` = `test`.`x1`.`b3`) and (`test`.`x3`.`d3` = `test`.`x2`.`b3`) and (`test`.`x4`.`d3` = `test`.`x3`.`b3`) and (`test`.`x5`.`d3` = `test`.`x4`.`b3`) and (`test`.`x6`.`d3` = `test`.`x5`.`b3`) and (`test`.`x7`.`d3` = `test`.`x6`.`b3`) and (`test`.`x8`.`d3` = `test`.`x7`.`b3`) and (`test`.`x9`.`d3` = `test`.`x8`.`b3`) and (`test`.`x10`.`d3` = `test`.`x9`.`b3`) and (`test`.`x11`.`d3` = `test`.`x10`.`b3`) and (`test`.`x12`.`d3` = `test`.`x11`.`b3`) and (`test`.`x13`.`d3` = `test`.`x12`.`b3`) and (`test`.`x14`.`d3` = `test`.`x13`.`b3`) and (`test`.`x15`.`d3` = `test`.`x14`.`b3`) and (`test`.`x16`.`d3` = `test`.`x15`.`b3`) and (`test`.`x17`.`d3` = `test`.`x16`.`b3`))
select count(*) from t3 as x0
join t3 as x1 on x0.b3=x1.d3 and x0.d3=8
join t3 as x2 on x1.b3=x2.d3
join t3 as x3 on x2.b3=x3.d3
join t3 as x4 on x3.b3=x4.d3
join t3 as x5 on x4.b3=x5.d3
join t3 as x6 on x5.b3=x6.d3
join t3 as x7 on x6.b3=x7.d3
join t3 as x8 on x7.b3=x8.d3
join t3 as x9 on x8.b3=x9.d3
join t3 as x10 on x9.b3=x10.d3
join t3 as x11 on x10.b3=x11.d3
join t3 as x12 on x11.b3=x12.d3
join t3 as x13 on x12.b3=x13.d3
join t3 as x14 on x13.b3=x14.d3
join t3 as x15 on x14.b3=x15.d3
join t3 as x16 on x15.b3=x16.d3
join t3 as x17 on x16.b3=x17.d3;
count(*)
1
explain select count(*) from t3 as x0
join t3 as x1 on x0.c3=x1.a3
join t3 as x2 on x1.c3=x2.a3
join t3 as x3 on x2.c3=x3.a3
join t3 as x4 on x3.c3=x4.a3
join t3 as x5 on x4.c3=x5.a3
join t3 as x6 on x5.c3=x6.a3
join t3 as x7 on x6.c3=x7.a3
join t3 as x8 on x7.c3=x8.a3
join t3 as x9 on x8.c3=x9.a3
join t3 as x10 on x9.c3=x10.a3
join t3 as x11 on x10.c3=x11.a3
join t3 as x12 on x11.c3=x12.a3
join t3 as x13 on x12.c3=x13.a3
join t3 as x14 on x13.c3=x14.a3
join t3 as x15 on x14.c3=x15.a3
join t3 as x16 on x15.c3=x16.a3
join t3 as x17 on x16.c3=x17.a3
join t3 as x18 on x17.c3=x18.a3
join t3 as x19 on x18.c3=x19.a3
join t3 as x20 on x19.c3=x20.a3
join t3 as x21 on x20.c3=x21.a3
join t3 as x22 on x21.c3=x22.a3
join t3 as x23 on x22.c3=x23.a3
join t3 as x24 on x23.c3=x24.a3
join t3 as x25 on x24.c3=x25.a3
join t3 as x26 on x25.c3=x26.a3
join t3 as x27 on x26.c3=x27.a3
join t3 as x28 on x27.c3=x28.a3
join t3 as x29 on x28.c3=x29.a3
join t3 as x30 on x29.c3=x30.a3
join t3 as x31 on x30.c3=x31.a3
join t3 as x32 on x31.c3=x32.a3
join t3 as x33 on x32.c3=x33.a3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x0	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	Parent of 32 pushed join@1
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x0.c3	1	100.00	Child of 'x0' in pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x1.c3	1	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x2.c3	1	100.00	Child of 'x2' in pushed join@1
1	SIMPLE	x4	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x3.c3	1	100.00	Child of 'x3' in pushed join@1
1	SIMPLE	x5	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x4.c3	1	100.00	Child of 'x4' in pushed join@1
1	SIMPLE	x6	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x5.c3	1	100.00	Child of 'x5' in pushed join@1
1	SIMPLE	x7	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x6.c3	1	100.00	Child of 'x6' in pushed join@1
1	SIMPLE	x8	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x7.c3	1	100.00	Child of 'x7' in pushed join@1
1	SIMPLE	x9	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x8.c3	1	100.00	Child of 'x8' in pushed join@1
1	SIMPLE	x10	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x9.c3	1	100.00	Child of 'x9' in pushed join@1
1	SIMPLE	x11	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x10.c3	1	100.00	Child of 'x10' in pushed join@1
1	SIMPLE	x12	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x11.c3	1	100.00	Child of 'x11' in pushed join@1
1	SIMPLE	x13	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x12.c3	1	100.00	Child of 'x12' in pushed join@1
1	SIMPLE	x14	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x13.c3	1	100.00	Child of 'x13' in pushed join@1
1	SIMPLE	x15	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x14.c3	1	100.00	Child of 'x14' in pushed join@1
1	SIMPLE	x16	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x15.c3	1	100.00	Child of 'x15' in pushed join@1
1	SIMPLE	x17	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x16.c3	1	100.00	Child of 'x16' in pushed join@1
1	SIMPLE	x18	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x17.c3	1	100.00	Child of 'x17' in pushed join@1
1	SIMPLE	x19	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x18.c3	1	100.00	Child of 'x18' in pushed join@1
1	SIMPLE	x20	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x19.c3	1	100.00	Child of 'x19' in pushed join@1
1	SIMPLE	x21	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x20.c3	1	100.00	Child of 'x20' in pushed join@1
1	SIMPLE	x22	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x21.c3	1	100.00	Child of 'x21' in pushed join@1
1	SIMPLE	x23	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x22.c3	1	100.00	Child of 'x22' in pushed join@1
1	SIMPLE	x24	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x23.c3	1	100.00	Child of 'x23' in pushed join@1
1	SIMPLE	x25	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x24.c3	1	100.00	Child of 'x24' in pushed join@1
1	SIMPLE	x26	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x25.c3	1	100.00	Child of 'x25' in pushed join@1
1	SIMPLE	x27	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x26.c3	1	100.00	Child of 'x26' in pushed join@1
1	SIMPLE	x28	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x27.c3	1	100.00	Child of 'x27' in pushed join@1
1	SIMPLE	x29	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x28.c3	1	100.00	Child of 'x28' in pushed join@1
1	SIMPLE	x30	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x29.c3	1	100.00	Child of 'x29' in pushed join@1
1	SIMPLE	x31	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x30.c3	1	100.00	Child of 'x30' in pushed join@1
1	SIMPLE	x32	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x31.c3	1	100.00	Parent of 2 pushed join@2
1	SIMPLE	x33	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x32.c3	1	100.00	Child of 'x32' in pushed join@2
Warnings:
Note	1003	Can't push table 'x32' as child of 'x0'. Max number of pushable tables exceeded.
Note	1003	Can't push table 'x33' as child of 'x0'. Max number of pushable tables exceeded.
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t3` `x0` join `test`.`t3` `x1` join `test`.`t3` `x2` join `test`.`t3` `x3` join `test`.`t3` `x4` join `test`.`t3` `x5` join `test`.`t3` `x6` join `test`.`t3` `x7` join `test`.`t3` `x8` join `test`.`t3` `x9` join `test`.`t3` `x10` join `test`.`t3` `x11` join `test`.`t3` `x12` join `test`.`t3` `x13` join `test`.`t3` `x14` join `test`.`t3` `x15` join `test`.`t3` `x16` join `test`.`t3` `x17` join `test`.`t3` `x18` join `test`.`t3` `x19` join `test`.`t3` `x20` join `test`.`t3` `x21` join `test`.`t3` `x22` join `test`.`t3` `x23` join `test`.`t3` `x24` join `test`.`t3` `x25` join `test`.`t3` `x26` join `test`.`t3` `x27` join `test`.`t3` `x28` join `test`.`t3` `x29` join `test`.`t3` `x30` join `test`.`t3` `x31` join `test`.`t3` `x32` join `test`.`t3` `x33` where ((`test`.`x1`.`a3` = `test`.`x0`.`c3`) and (`test`.`x2`.`a3` = `test`.`x1`.`c3`) and (`test`.`x3`.`a3` = `test`.`x2`.`c3`) and (`test`.`x4`.`a3` = `test`.`x3`.`c3`) and (`test`.`x5`.`a3` = `test`.`x4`.`c3`) and (`test`.`x6`.`a3` = `test`.`x5`.`c3`) and (`test`.`x7`.`a3` = `test`.`x6`.`c3`) and (`test`.`x8`.`a3` = `test`.`x7`.`c3`) and (`test`.`x9`.`a3` = `test`.`x8`.`c3`) and (`test`.`x10`.`a3` = `test`.`x9`.`c3`) and (`test`.`x11`.`a3` = `test`.`x10`.`c3`) and (`test`.`x12`.`a3` = `test`.`x11`.`c3`) and (`test`.`x13`.`a3` = `test`.`x12`.`c3`) and (`test`.`x14`.`a3` = `test`.`x13`.`c3`) and (`test`.`x15`.`a3` = `test`.`x14`.`c3`) and (`test`.`x16`.`a3` = `test`.`x15`.`c3`) and (`test`.`x17`.`a3` = `test`.`x16`.`c3`) and (`test`.`x18`.`a3` = `test`.`x17`.`c3`) and (`test`.`x19`.`a3` = `test`.`x18`.`c3`) and (`test`.`x20`.`a3` = `test`.`x19`.`c3`) and (`test`.`x21`.`a3` = `test`.`x20`.`c3`) and (`test`.`x22`.`a3` = `test`.`x21`.`c3`) and (`test`.`x23`.`a3` = `test`.`x22`.`c3`) and (`test`.`x24`.`a3` = `test`.`x23`.`c3`) and (`test`.`x25`.`a3` = `test`.`x24`.`c3`) and (`test`.`x26`.`a3` = `test`.`x25`.`c3`) and (`test`.`x27`.`a3` = `test`.`x26`.`c3`) and (`test`.`x28`.`a3` = `test`.`x27`.`c3`) and (`test`.`x29`.`a3` = `test`.`x28`.`c3`) and (`test`.`x30`.`a3` = `test`.`x29`.`c3`) and (`test`.`x31`.`a3` = `test`.`x30`.`c3`) and (`test`.`x32`.`a3` = `test`.`x31`.`c3`) and (`test`.`x33`.`a3` = `test`.`x32`.`c3`))
select count(*) from t3 as x0
join t3 as x1 on x0.c3=x1.a3
join t3 as x2 on x1.c3=x2.a3
join t3 as x3 on x2.c3=x3.a3
join t3 as x4 on x3.c3=x4.a3
join t3 as x5 on x4.c3=x5.a3
join t3 as x6 on x5.c3=x6.a3
join t3 as x7 on x6.c3=x7.a3
join t3 as x8 on x7.c3=x8.a3
join t3 as x9 on x8.c3=x9.a3
join t3 as x10 on x9.c3=x10.a3
join t3 as x11 on x10.c3=x11.a3
join t3 as x12 on x11.c3=x12.a3
join t3 as x13 on x12.c3=x13.a3
join t3 as x14 on x13.c3=x14.a3
join t3 as x15 on x14.c3=x15.a3
join t3 as x16 on x15.c3=x16.a3
join t3 as x17 on x16.c3=x17.a3
join t3 as x18 on x17.c3=x18.a3
join t3 as x19 on x18.c3=x19.a3
join t3 as x20 on x19.c3=x20.a3
join t3 as x21 on x20.c3=x21.a3
join t3 as x22 on x21.c3=x22.a3
join t3 as x23 on x22.c3=x23.a3
join t3 as x24 on x23.c3=x24.a3
join t3 as x25 on x24.c3=x25.a3
join t3 as x26 on x25.c3=x26.a3
join t3 as x27 on x26.c3=x27.a3
join t3 as x28 on x27.c3=x28.a3
join t3 as x29 on x28.c3=x29.a3
join t3 as x30 on x29.c3=x30.a3
join t3 as x31 on x30.c3=x31.a3
join t3 as x32 on x31.c3=x32.a3
join t3 as x33 on x32.c3=x33.a3;
count(*)
1
explain select count(*) from t3 as x0
join t3 as x1 on x0.b3=x1.d3
join t3 as x2 on x1.b3=x2.d3
join t3 as x3 on x2.b3=x3.d3
join t3 as x4 on x3.b3=x4.d3
join t3 as x5 on x4.b3=x5.d3
join t3 as x6 on x5.b3=x6.d3
join t3 as x7 on x6.b3=x7.d3
join t3 as x8 on x7.b3=x8.d3
join t3 as x9 on x8.b3=x9.d3
join t3 as x10 on x9.b3=x10.d3
join t3 as x11 on x10.b3=x11.d3
join t3 as x12 on x11.b3=x12.d3
join t3 as x13 on x12.b3=x13.d3
join t3 as x14 on x13.b3=x14.d3
join t3 as x15 on x14.b3=x15.d3
join t3 as x16 on x15.b3=x16.d3
join t3 as x17 on x15.b3=x17.a3
join t3 as x18 on x16.b3=x18.d3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x0	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	Parent of 17 pushed join@1
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x0.b3	1	100.00	Child of 'x0' in pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x1.b3	1	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x2.b3	1	100.00	Child of 'x2' in pushed join@1
1	SIMPLE	x4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x3.b3	1	100.00	Child of 'x3' in pushed join@1
1	SIMPLE	x5	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x4.b3	1	100.00	Child of 'x4' in pushed join@1
1	SIMPLE	x6	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x5.b3	1	100.00	Child of 'x5' in pushed join@1
1	SIMPLE	x7	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x6.b3	1	100.00	Child of 'x6' in pushed join@1
1	SIMPLE	x8	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x7.b3	1	100.00	Child of 'x7' in pushed join@1
1	SIMPLE	x9	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x8.b3	1	100.00	Child of 'x8' in pushed join@1
1	SIMPLE	x10	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x9.b3	1	100.00	Child of 'x9' in pushed join@1
1	SIMPLE	x11	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x10.b3	1	100.00	Child of 'x10' in pushed join@1
1	SIMPLE	x12	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x11.b3	1	100.00	Child of 'x11' in pushed join@1
1	SIMPLE	x13	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x12.b3	1	100.00	Child of 'x12' in pushed join@1
1	SIMPLE	x14	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x13.b3	1	100.00	Child of 'x13' in pushed join@1
1	SIMPLE	x15	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x14.b3	1	100.00	Child of 'x14' in pushed join@1
1	SIMPLE	x16	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x15.b3	1	100.00	Parent of 2 pushed join@2
1	SIMPLE	x18	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	t3_d3	t3_d3	4	test.x16.b3	1	100.00	Child of 'x16' in pushed join@2
1	SIMPLE	x17	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x15.b3	1	100.00	Child of 'x15' in pushed join@1
Warnings:
Note	1003	Can't push table 'x16' as child of 'x0'. Max number of pushable tables exceeded.
Note	1003	Can't push table 'x18' as child of 'x0'. Max number of pushable tables exceeded.
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t3` `x0` join `test`.`t3` `x1` join `test`.`t3` `x2` join `test`.`t3` `x3` join `test`.`t3` `x4` join `test`.`t3` `x5` join `test`.`t3` `x6` join `test`.`t3` `x7` join `test`.`t3` `x8` join `test`.`t3` `x9` join `test`.`t3` `x10` join `test`.`t3` `x11` join `test`.`t3` `x12` join `test`.`t3` `x13` join `test`.`t3` `x14` join `test`.`t3` `x15` join `test`.`t3` `x16` join `test`.`t3` `x17` join `test`.`t3` `x18` where ((`test`.`x1`.`d3` = `test`.`x0`.`b3`) and (`test`.`x2`.`d3` = `test`.`x1`.`b3`) and (`test`.`x3`.`d3` = `test`.`x2`.`b3`) and (`test`.`x4`.`d3` = `test`.`x3`.`b3`) and (`test`.`x5`.`d3` = `test`.`x4`.`b3`) and (`test`.`x6`.`d3` = `test`.`x5`.`b3`) and (`test`.`x7`.`d3` = `test`.`x6`.`b3`) and (`test`.`x8`.`d3` = `test`.`x7`.`b3`) and (`test`.`x9`.`d3` = `test`.`x8`.`b3`) and (`test`.`x10`.`d3` = `test`.`x9`.`b3`) and (`test`.`x11`.`d3` = `test`.`x10`.`b3`) and (`test`.`x12`.`d3` = `test`.`x11`.`b3`) and (`test`.`x13`.`d3` = `test`.`x12`.`b3`) and (`test`.`x14`.`d3` = `test`.`x13`.`b3`) and (`test`.`x15`.`d3` = `test`.`x14`.`b3`) and (`test`.`x16`.`d3` = `test`.`x15`.`b3`) and (`test`.`x17`.`a3` = `test`.`x15`.`b3`) and (`test`.`x18`.`d3` = `test`.`x16`.`b3`))
select count(*) from t3 as x0
join t3 as x1 on x0.b3=x1.d3
join t3 as x2 on x1.b3=x2.d3
join t3 as x3 on x2.b3=x3.d3
join t3 as x4 on x3.b3=x4.d3
join t3 as x5 on x4.b3=x5.d3
join t3 as x6 on x5.b3=x6.d3
join t3 as x7 on x6.b3=x7.d3
join t3 as x8 on x7.b3=x8.d3
join t3 as x9 on x8.b3=x9.d3
join t3 as x10 on x9.b3=x10.d3
join t3 as x11 on x10.b3=x11.d3
join t3 as x12 on x11.b3=x12.d3
join t3 as x13 on x12.b3=x13.d3
join t3 as x14 on x13.b3=x14.d3
join t3 as x15 on x14.b3=x15.d3
join t3 as x16 on x15.b3=x16.d3
join t3 as x17 on x15.b3=x17.a3
join t3 as x18 on x16.b3=x18.d3;
count(*)
4
drop table t1,t2,t3, t3_hash;
create table t3 (a3 int, b3 int, c3 int, d3 int,
primary key(b3, a3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
create table t3_hash (a3 int, b3 int, c3 int, d3 int,
primary key(b3,a3) using hash) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
create table t3_unq (pk int, a3 int not null, b3 int not null, c3 int, d3 int,
primary key(pk) using hash, unique key(b3,a3) using hash) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
insert into t3_hash values (0x1f, 0x2f, 1, 0x1f);
insert into t3_hash values (0x2f, 0x3f, 2, 0x2f);
insert into t3_hash values (0x3f, 0x1f, 3, 0x3f);
insert into t3_unq values (1001, 0x1f, 0x2f, 1, 0x1f);
insert into t3_unq values (1002, 0x2f, 0x3f, 2, 0x2f);
insert into t3_unq values (1003, 0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where y.a3=x.d3 and y.b3=x.b3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`d3` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.b3,test.x.d3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`a3` = `test`.`x`.`d3`))
select * from t3 x, t3 y where y.a3=x.d3 and y.b3=x.b3;
a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	31	47	1	31
47	63	2	47	47	63	2	47
63	31	3	63	63	31	3	63
explain
select * from t3_hash x, t3_hash y where y.a3=x.d3 and y.b3=x.b3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`d3` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.b3,test.x.d3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3_hash` `x` join `test`.`t3_hash` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`a3` = `test`.`x`.`d3`))
select * from t3_hash x, t3_hash y where y.a3=x.d3 and y.b3=x.b3;
a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	31	47	1	31
47	63	2	47	47	63	2	47
63	31	3	63	63	31	3	63
explain
select * from t3_unq x, t3_unq y where y.a3=x.d3 and y.b3=x.b3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3	NULL	NULL	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`d3` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	8	test.x.b3,test.x.d3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`pk` AS `pk`,`test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`pk` AS `pk`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3_unq` `x` join `test`.`t3_unq` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`a3` = `test`.`x`.`d3`))
select * from t3_unq x, t3_unq y where y.a3=x.d3 and y.b3=x.b3;
pk	a3	b3	c3	d3	pk	a3	b3	c3	d3
1001	31	47	1	31	1001	31	47	1	31
1002	47	63	2	47	1002	47	63	2	47
1003	63	31	3	63	1003	63	31	3	63
explain
select * from t3 x, t3 y where y.a3=x.d3 and y.b3=x.b3
and x.a3=0x2f and x.b3=0x3f;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`d3` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	const,test.x.d3	1	100.00	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`b3` = `test`.`x`.`b3`)
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`x`.`b3` = 0x3f))
select * from t3 x, t3 y where y.a3=x.d3 and y.b3=x.b3
and x.a3=0x2f and x.b3=0x3f;
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
explain
select * from t3_hash x, t3_hash y where y.a3=x.d3 and y.b3=x.b3
and x.a3=0x2f and x.b3=0x3f;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	const,const	1	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`d3` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	const,test.x.d3	1	100.00	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`b3` = `test`.`x`.`b3`)
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3_hash` `x` join `test`.`t3_hash` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`x`.`b3` = 0x3f))
select * from t3_hash x, t3_hash y where y.a3=x.d3 and y.b3=x.b3
and x.a3=0x2f and x.b3=0x3f;
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
explain
select * from t3_unq x, t3_unq y where y.a3=x.d3 and y.b3=x.b3
and x.a3=0x2f and x.b3=0x3f;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	8	const,const	1	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`d3` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	8	const,test.x.d3	1	100.00	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`b3` = `test`.`x`.`b3`)
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`pk` AS `pk`,`test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`pk` AS `pk`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3_unq` `x` join `test`.`t3_unq` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`x`.`b3` = 0x3f))
select * from t3_unq x, t3_unq y where y.a3=x.d3 and y.b3=x.b3
and x.a3=0x2f and x.b3=0x3f;
pk	a3	b3	c3	d3	pk	a3	b3	c3	d3
1002	47	63	2	47	1002	47	63	2	47
drop table t3, t3_hash, t3_unq;
create table t3 (a3 int, b3 int, c3 int, d3 int,
primary key(a3), unique key(d3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
insert into t3 values (0x4f, 0,    null, null);
explain
select * from t3 as t1
left outer join t3 as t2 on t2.d3 = t1.d3
left outer join t3 as t3 on t3.a3 = t2.d3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	d3	d3	5	test.t1.d3	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.d3	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a3` AS `a3`,`test`.`t1`.`b3` AS `b3`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`d3` AS `d3`,`test`.`t2`.`a3` AS `a3`,`test`.`t2`.`b3` AS `b3`,`test`.`t2`.`c3` AS `c3`,`test`.`t2`.`d3` AS `d3`,`test`.`t3`.`a3` AS `a3`,`test`.`t3`.`b3` AS `b3`,`test`.`t3`.`c3` AS `c3`,`test`.`t3`.`d3` AS `d3` from `test`.`t3` `t1` left join `test`.`t3` `t2` on((`test`.`t2`.`d3` = `test`.`t1`.`d3`)) left join `test`.`t3` on((`test`.`t3`.`a3` = `test`.`t2`.`d3`)) where true
select * from t3 as t1
left outer join t3 as t2 on t2.d3 = t1.d3
left outer join t3 as t3 on t3.a3 = t2.d3;
a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	31	47	1	31	31	47	1	31
47	63	2	47	47	63	2	47	47	63	2	47
63	31	3	63	63	31	3	63	63	31	3	63
79	0	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
explain
select * from t3 as t1
left outer join t3 as t2 on t2.d3 = t1.a3
left outer join t3 as t3 on t3.a3 = t2.d3
where t1.d3 = 47;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	d3	d3	5	const	1	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	d3	d3	5	test.t1.a3	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.d3	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a3` AS `a3`,`test`.`t1`.`b3` AS `b3`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`d3` AS `d3`,`test`.`t2`.`a3` AS `a3`,`test`.`t2`.`b3` AS `b3`,`test`.`t2`.`c3` AS `c3`,`test`.`t2`.`d3` AS `d3`,`test`.`t3`.`a3` AS `a3`,`test`.`t3`.`b3` AS `b3`,`test`.`t3`.`c3` AS `c3`,`test`.`t3`.`d3` AS `d3` from `test`.`t3` `t1` left join `test`.`t3` `t2` on((`test`.`t2`.`d3` = `test`.`t1`.`a3`)) left join `test`.`t3` on((`test`.`t3`.`a3` = `test`.`t2`.`d3`)) where (`test`.`t1`.`d3` = 47)
select * from t3 as t1
left outer join t3 as t2 on t2.d3 = t1.a3
left outer join t3 as t3 on t3.a3 = t2.d3
where t1.d3 = 47;
a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47	47	63	2	47
explain
select * from t3 as t1
left outer join t3 as t2 on t2.d3 = t1.a3
left outer join t3 as t3 on t3.a3 = t2.d3
where t1.d3 >= 47;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	range	d3	d3	5	NULL	3	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`t1`.`d3` >= 47); Using MRR
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	d3	d3	5	test.t1.a3	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.d3	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a3` AS `a3`,`test`.`t1`.`b3` AS `b3`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`d3` AS `d3`,`test`.`t2`.`a3` AS `a3`,`test`.`t2`.`b3` AS `b3`,`test`.`t2`.`c3` AS `c3`,`test`.`t2`.`d3` AS `d3`,`test`.`t3`.`a3` AS `a3`,`test`.`t3`.`b3` AS `b3`,`test`.`t3`.`c3` AS `c3`,`test`.`t3`.`d3` AS `d3` from `test`.`t3` `t1` left join `test`.`t3` `t2` on((`test`.`t2`.`d3` = `test`.`t1`.`a3`)) left join `test`.`t3` on((`test`.`t3`.`a3` = `test`.`t2`.`d3`)) where (`test`.`t1`.`d3` >= 47)
select * from t3 as t1
left outer join t3 as t2 on t2.d3 = t1.a3
left outer join t3 as t3 on t3.a3 = t2.d3
where t1.d3 >= 47;
a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47	47	63	2	47
63	31	3	63	63	31	3	63	63	31	3	63
explain
select * from t3 as t1
left outer join t3 as t2 on t2.d3 = t1.a3
left outer join t3 as t3 on t3.a3 = t2.d3
where t1.d3 is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ref	d3	d3	5	const	3	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`t1`.`d3` is null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	d3	d3	5	test.t1.a3	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.d3	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a3` AS `a3`,`test`.`t1`.`b3` AS `b3`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`d3` AS `d3`,`test`.`t2`.`a3` AS `a3`,`test`.`t2`.`b3` AS `b3`,`test`.`t2`.`c3` AS `c3`,`test`.`t2`.`d3` AS `d3`,`test`.`t3`.`a3` AS `a3`,`test`.`t3`.`b3` AS `b3`,`test`.`t3`.`c3` AS `c3`,`test`.`t3`.`d3` AS `d3` from `test`.`t3` `t1` left join `test`.`t3` `t2` on((`test`.`t2`.`d3` = `test`.`t1`.`a3`)) left join `test`.`t3` on((`test`.`t3`.`a3` = `test`.`t2`.`d3`)) where (`test`.`t1`.`d3` is null)
select * from t3 as t1
left outer join t3 as t2 on t2.d3 = t1.a3
left outer join t3 as t3 on t3.a3 = t2.d3
where t1.d3 is null;
a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3
79	0	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
explain
select * from t3 as t1
left outer join t3 as t2 on t2.d3 = t1.a3
left outer join t3 as t3 on t3.a3 = t2.d3
where t1.d3 is not null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	range	d3	d3	5	NULL	3	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`t1`.`d3` is not null); Using MRR
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	d3	d3	5	test.t1.a3	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.d3	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a3` AS `a3`,`test`.`t1`.`b3` AS `b3`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`d3` AS `d3`,`test`.`t2`.`a3` AS `a3`,`test`.`t2`.`b3` AS `b3`,`test`.`t2`.`c3` AS `c3`,`test`.`t2`.`d3` AS `d3`,`test`.`t3`.`a3` AS `a3`,`test`.`t3`.`b3` AS `b3`,`test`.`t3`.`c3` AS `c3`,`test`.`t3`.`d3` AS `d3` from `test`.`t3` `t1` left join `test`.`t3` `t2` on((`test`.`t2`.`d3` = `test`.`t1`.`a3`)) left join `test`.`t3` on((`test`.`t3`.`a3` = `test`.`t2`.`d3`)) where (`test`.`t1`.`d3` is not null)
select * from t3 as t1
left outer join t3 as t2 on t2.d3 = t1.a3
left outer join t3 as t3 on t3.a3 = t2.d3
where t1.d3 is not null;
a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	31	47	1	31	31	47	1	31
47	63	2	47	47	63	2	47	47	63	2	47
63	31	3	63	63	31	3	63	63	31	3	63
insert into t3 values (0x5f, 0x5f, 4, null);
alter table t3 add index ix(b3);
explain select straight_join *
from t3 as t1
left join t3 as t2 on t2.b3 = t1.a3
left join t3 as t3 on t3.b3 = t2.d3
where t1.d3 is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ref	d3	d3	5	const	3	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`t1`.`d3` is null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix	ix	5	test.t1.a3	2	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix	ix	5	test.t2.d3	2	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a3` AS `a3`,`test`.`t1`.`b3` AS `b3`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`d3` AS `d3`,`test`.`t2`.`a3` AS `a3`,`test`.`t2`.`b3` AS `b3`,`test`.`t2`.`c3` AS `c3`,`test`.`t2`.`d3` AS `d3`,`test`.`t3`.`a3` AS `a3`,`test`.`t3`.`b3` AS `b3`,`test`.`t3`.`c3` AS `c3`,`test`.`t3`.`d3` AS `d3` from `test`.`t3` `t1` left join `test`.`t3` `t2` on((`test`.`t2`.`b3` = `test`.`t1`.`a3`)) left join `test`.`t3` on((`test`.`t3`.`b3` = `test`.`t2`.`d3`)) where (`test`.`t1`.`d3` is null)
select straight_join *
from t3 as t1
left join t3 as t2 on t2.b3 = t1.a3
left join t3 as t3 on t3.b3 = t2.d3
where t1.d3 is null;
a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3
79	0	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
95	95	4	NULL	95	95	4	NULL	NULL	NULL	NULL	NULL
explain select straight_join *
from t3 as t1
left join t3 as t2 on t2.d3 = t1.a3
left join t3 as t3 on t3.d3 = t2.d3
where t1.d3 is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ref	d3	d3	5	const	3	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`t1`.`d3` is null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	d3	d3	5	test.t1.a3	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	d3	d3	5	test.t2.d3	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a3` AS `a3`,`test`.`t1`.`b3` AS `b3`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`d3` AS `d3`,`test`.`t2`.`a3` AS `a3`,`test`.`t2`.`b3` AS `b3`,`test`.`t2`.`c3` AS `c3`,`test`.`t2`.`d3` AS `d3`,`test`.`t3`.`a3` AS `a3`,`test`.`t3`.`b3` AS `b3`,`test`.`t3`.`c3` AS `c3`,`test`.`t3`.`d3` AS `d3` from `test`.`t3` `t1` left join `test`.`t3` `t2` on((`test`.`t2`.`d3` = `test`.`t1`.`a3`)) left join `test`.`t3` on((`test`.`t3`.`d3` = `test`.`t2`.`d3`)) where (`test`.`t1`.`d3` is null)
select straight_join *
from t3 as t1
left join t3 as t2 on t2.d3 = t1.a3
left join t3 as t3 on t3.d3 = t2.d3
where t1.d3 is null;
a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3
79	0	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
95	95	4	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
alter table t3 drop index d3;
alter table t3 add unique key(d3) using hash;
Warnings:
Warning	1121	Ndb does not support unique index on NULL valued attributes, index access with NULL value will become full table scan
explain select straight_join *
from t3 as t1
left join t3 as t2 on t2.b3 = t1.a3
left join t3 as t3 on t3.b3 = t2.d3
where t1.d3 is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	d3	NULL	NULL	NULL	5	20.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`t1`.`d3` is null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix	ix	5	test.t1.a3	2	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix	ix	5	test.t2.d3	2	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a3` AS `a3`,`test`.`t1`.`b3` AS `b3`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`d3` AS `d3`,`test`.`t2`.`a3` AS `a3`,`test`.`t2`.`b3` AS `b3`,`test`.`t2`.`c3` AS `c3`,`test`.`t2`.`d3` AS `d3`,`test`.`t3`.`a3` AS `a3`,`test`.`t3`.`b3` AS `b3`,`test`.`t3`.`c3` AS `c3`,`test`.`t3`.`d3` AS `d3` from `test`.`t3` `t1` left join `test`.`t3` `t2` on((`test`.`t2`.`b3` = `test`.`t1`.`a3`)) left join `test`.`t3` on((`test`.`t3`.`b3` = `test`.`t2`.`d3`)) where (`test`.`t1`.`d3` is null)
select straight_join *
from t3 as t1
left join t3 as t2 on t2.b3 = t1.a3
left join t3 as t3 on t3.b3 = t2.d3
where t1.d3 is null;
a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3
79	0	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
95	95	4	NULL	95	95	4	NULL	NULL	NULL	NULL	NULL
explain select straight_join *
from t3 as t1
left join t3 as t2 on t2.d3 = t1.a3
left join t3 as t3 on t3.d3 = t2.d3
where t1.d3 is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	d3	NULL	NULL	NULL	5	20.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`t1`.`d3` is null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	d3	d3	5	test.t1.a3	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	d3	d3	5	test.t2.d3	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a3` AS `a3`,`test`.`t1`.`b3` AS `b3`,`test`.`t1`.`c3` AS `c3`,`test`.`t1`.`d3` AS `d3`,`test`.`t2`.`a3` AS `a3`,`test`.`t2`.`b3` AS `b3`,`test`.`t2`.`c3` AS `c3`,`test`.`t2`.`d3` AS `d3`,`test`.`t3`.`a3` AS `a3`,`test`.`t3`.`b3` AS `b3`,`test`.`t3`.`c3` AS `c3`,`test`.`t3`.`d3` AS `d3` from `test`.`t3` `t1` left join `test`.`t3` `t2` on((`test`.`t2`.`d3` = `test`.`t1`.`a3`)) left join `test`.`t3` on((`test`.`t3`.`d3` = `test`.`t2`.`d3`)) where (`test`.`t1`.`d3` is null)
select straight_join *
from t3 as t1
left join t3 as t2 on t2.d3 = t1.a3
left join t3 as t3 on t3.d3 = t2.d3
where t1.d3 is null;
a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3
79	0	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
95	95	4	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
drop table t3;
create table t3 (a3 int not null, b3 int not null, c3 int, d3 int,
primary key(a3), unique key(b3,d3), unique key(c3,b3), unique key(c3,d3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1,    0x1f);
insert into t3 values (0x2f, 0x3f, 2,    0x2f);
insert into t3 values (0x3f, 0x1f, 3,    0x3f);
insert into t3 values (0x40, 0,    null, null);
insert into t3 values (0x41, 0,    null, null);
insert into t3 values (0x42, 0,    4,    null);
insert into t3 values (0x43, 0,    null, 0x43);
explain
select straight_join * 
from t3 as x join t3 as y on x.b3 = y.b3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3	NULL	NULL	NULL	7	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	ref	b3	b3	4	test.x.b3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where (`test`.`y`.`b3` = `test`.`x`.`b3`)
select straight_join * 
from t3 as x join t3 as y on x.b3 = y.b3;
a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	31	47	1	31
47	63	2	47	47	63	2	47
63	31	3	63	63	31	3	63
64	0	NULL	NULL	64	0	NULL	NULL
64	0	NULL	NULL	65	0	NULL	NULL
64	0	NULL	NULL	66	0	4	NULL
64	0	NULL	NULL	67	0	NULL	67
65	0	NULL	NULL	64	0	NULL	NULL
65	0	NULL	NULL	65	0	NULL	NULL
65	0	NULL	NULL	66	0	4	NULL
65	0	NULL	NULL	67	0	NULL	67
66	0	4	NULL	64	0	NULL	NULL
66	0	4	NULL	65	0	NULL	NULL
66	0	4	NULL	66	0	4	NULL
66	0	4	NULL	67	0	NULL	67
67	0	NULL	67	64	0	NULL	NULL
67	0	NULL	67	65	0	NULL	NULL
67	0	NULL	67	66	0	4	NULL
67	0	NULL	67	67	0	NULL	67
explain
select straight_join * 
from t3 as x join t3 as y on x.b3 = y.b3
where y.d3 = 0x2f;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3	NULL	NULL	NULL	7	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	test.x.b3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`d3` = 0x2f))
select straight_join * 
from t3 as x join t3 as y on x.b3 = y.b3
where y.d3 = 0x2f;
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
explain
select straight_join * 
from t3 as x join t3 as y on x.c3 = y.c3
where y.d3 = 0x2f;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	c3,c3_2	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`c3` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	c3,c3_2	c3_2	10	test.x.c3,const	#	#	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`c3` = `test`.`x`.`c3`) and (`test`.`y`.`d3` = 0x2f))
select straight_join * 
from t3 as x join t3 as y on x.c3 = y.c3
where y.d3 = 0x2f;
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
explain
select straight_join * 
from t3 as x join t3 as y on x.d3 = y.d3
where y.b3 = 0x2f;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	7	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`d3` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	const,test.x.d3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`d3` = `test`.`x`.`d3`) and (`test`.`y`.`b3` = 0x2f))
select straight_join * 
from t3 as x join t3 as y on x.d3 = y.d3
where y.b3 = 0x2f;
a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	31	47	1	31
explain
select straight_join * 
from t3 as x join t3 as y on x.d3 = y.d3
where y.b3 = 0x20+0x2f;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	7	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`d3` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	const,test.x.d3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`d3` = `test`.`x`.`d3`) and (`test`.`y`.`b3` = 79))
select straight_join * 
from t3 as x join t3 as y on x.d3 = y.d3
where y.b3 = 0x20+0x2f;
a3	b3	c3	d3	a3	b3	c3	d3
explain
select straight_join * 
from t3 as x join t3 as y on x.b3 = y.b3
where y.d3 is not null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	ref	b3	b3	4	test.x.b3	#	#	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`d3` is not null)
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`d3` is not null))
select straight_join * 
from t3 as x join t3 as y on x.b3 = y.b3
where y.d3 is not null;
a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	31	47	1	31
47	63	2	47	47	63	2	47
63	31	3	63	63	31	3	63
64	0	NULL	NULL	67	0	NULL	67
65	0	NULL	NULL	67	0	NULL	67
66	0	4	NULL	67	0	NULL	67
67	0	NULL	67	67	0	NULL	67
explain
select straight_join * 
from t3 as x join t3 as y on x.b3 = y.b3
where y.d3 is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3	NULL	NULL	NULL	7	100.00	NULL
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	ref	b3	b3	9	test.x.b3,const	1	100.00	Using pushed condition (`test`.`y`.`d3` is null)
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`d3` is null))
select straight_join * 
from t3 as x join t3 as y on x.b3 = y.b3
where y.d3 is null;
a3	b3	c3	d3	a3	b3	c3	d3
64	0	NULL	NULL	64	0	NULL	NULL
64	0	NULL	NULL	65	0	NULL	NULL
64	0	NULL	NULL	66	0	4	NULL
65	0	NULL	NULL	64	0	NULL	NULL
65	0	NULL	NULL	65	0	NULL	NULL
65	0	NULL	NULL	66	0	4	NULL
66	0	4	NULL	64	0	NULL	NULL
66	0	4	NULL	65	0	NULL	NULL
66	0	4	NULL	66	0	4	NULL
67	0	NULL	67	64	0	NULL	NULL
67	0	NULL	67	65	0	NULL	NULL
67	0	NULL	67	66	0	4	NULL
explain
select straight_join * 
from t3 as x join t3 as y on x.c3 = y.c3
where y.b3 = 0;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	c3,c3_2	NULL	NULL	NULL	7	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`c3` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3,c3,c3_2	c3	9	test.x.c3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`c3` = `test`.`x`.`c3`) and (`test`.`y`.`b3` = 0))
select straight_join * 
from t3 as x join t3 as y on x.c3 = y.c3
where y.b3 = 0;
a3	b3	c3	d3	a3	b3	c3	d3
66	0	4	NULL	66	0	4	NULL
explain
select straight_join * 
from t3 as x join t3 as y on x.c3 = y.c3
where y.b3 is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where false
select straight_join * 
from t3 as x join t3 as y on x.c3 = y.c3
where y.b3 is null;
a3	b3	c3	d3	a3	b3	c3	d3
explain
select straight_join * from
t3 as x1
join t3 as y1 on y1.b3 = x1.b3 and y1.d3 = x1.d3
join t3 as x2 on x2.b3 = y1.b3+0
join t3 as y2 on y2.b3 = x2.c3 and y2.d3 = x1.c3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3,c3,c3_2	NULL	NULL	NULL	7	100.00	Parent of 2 pushed join@1; Using pushed condition ((`test`.`x1`.`d3` is not null) and (`test`.`x1`.`c3` is not null))
1	SIMPLE	y1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	test.x1.b3,test.x1.d3	1	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	b3,c3,c3_2	b3	4	func	1	100.00	Parent of 2 pushed join@2; Using pushed condition ((`test`.`x2`.`b3` = (`test`.`x1`.`b3` + 0)) and (`test`.`x2`.`c3` is not null))
1	SIMPLE	y2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	test.x2.c3,test.x1.c3	1	100.00	Child of 'x2' in pushed join@2
Warnings:
Note	1003	Can't push table 'x2' as child, column 'b3' does neither 'ref' a column nor a constant
Note	1003	Can't push table 'y2' as child of 'x1', column 'x2.c3' refers a table which was not pushed
Note	1003	/* select#1 */ select straight_join `test`.`x1`.`a3` AS `a3`,`test`.`x1`.`b3` AS `b3`,`test`.`x1`.`c3` AS `c3`,`test`.`x1`.`d3` AS `d3`,`test`.`y1`.`a3` AS `a3`,`test`.`y1`.`b3` AS `b3`,`test`.`y1`.`c3` AS `c3`,`test`.`y1`.`d3` AS `d3`,`test`.`x2`.`a3` AS `a3`,`test`.`x2`.`b3` AS `b3`,`test`.`x2`.`c3` AS `c3`,`test`.`x2`.`d3` AS `d3`,`test`.`y2`.`a3` AS `a3`,`test`.`y2`.`b3` AS `b3`,`test`.`y2`.`c3` AS `c3`,`test`.`y2`.`d3` AS `d3` from `test`.`t3` `x1` join `test`.`t3` `y1` join `test`.`t3` `x2` join `test`.`t3` `y2` where ((`test`.`y1`.`d3` = `test`.`x1`.`d3`) and (`test`.`y1`.`b3` = `test`.`x1`.`b3`) and (`test`.`y2`.`d3` = `test`.`x1`.`c3`) and (`test`.`y2`.`b3` = `test`.`x2`.`c3`) and (`test`.`x2`.`b3` = (`test`.`x1`.`b3` + 0)))
select straight_join * from
t3 as x1
join t3 as y1 on y1.b3 = x1.b3 and y1.d3 = x1.d3
join t3 as x2 on x2.b3 = y1.b3+0
join t3 as y2 on y2.b3 = x2.c3 and y2.d3 = x1.c3;
a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3	a3	b3	c3	d3
prepare stmt1 from
'select straight_join * 
  from t3 as x join t3 as y on x.b3 = y.b3
  where y.d3 = 0x2f';
execute stmt1;
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
execute stmt1;
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
drop prepare stmt1;
execute stmt1;
ERROR HY000: Unknown prepared statement handler (stmt1) given to EXECUTE
prepare stmt1 from
'select straight_join * 
  from t3 as x join t3 as y on x.b3 = y.b3
  where y.d3 = 0x2f';
prepare stmt1 from
'select straight_join * 
  from t3 as x join t3 as y on x.b3 = y.b3
  where y.d3 = 0x2f';
drop prepare stmt1;
prepare stmt1 from
'explain select straight_join * 
  from t3 as x join t3 as y on x.b3 = y.b3
  where y.d3 = 0x2f';
execute stmt1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3	NULL	NULL	NULL	7	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	test.x.b3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`d3` = 0x2f))
execute stmt1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3	NULL	NULL	NULL	7	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	test.x.b3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`d3` = 0x2f))
commit;
execute stmt1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3	NULL	NULL	NULL	7	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	test.x.b3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`d3` = 0x2f))
drop index b3 on t3;
execute stmt1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	NULL
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Using pushed condition (`test`.`y`.`d3` = 0x2f); Using join buffer (hash join)
Warnings:
Note	1003	Can't push table 'y' as child, 'type' must be a 'ref' access
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`d3` = 0x2f))
create unique index b3 on t3(b3,d3);
execute stmt1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3	NULL	NULL	NULL	7	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	test.x.b3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`d3` = 0x2f))
drop prepare stmt1;
prepare stmt1 from
'explain select straight_join * 
  from t3 as x join t3 as y on x.b3 = y.b3
  where y.d3 = ?';
set @a=47;
execute stmt1 using @a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3	NULL	NULL	NULL	7	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	test.x.b3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`d3` = 47))
set @a=0;
execute stmt1 using @a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3	NULL	NULL	NULL	7	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	test.x.b3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`d3` = 0))
set @a=null;
execute stmt1 using @a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	b3	NULL	NULL	NULL	7	100.00	NULL
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	test.x.b3,const	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`y`.`d3` = NULL))
prepare stmt1 from
'select straight_join * 
  from t3 as x join t3 as y on x.b3 = y.b3
  where y.d3 = ?';
set @a=47;
execute stmt1 using @a;
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
set @a=0;
execute stmt1 using @a;
a3	b3	c3	d3	a3	b3	c3	d3
set @a=null;
execute stmt1 using @a;
a3	b3	c3	d3	a3	b3	c3	d3
prepare stmt1 from
'explain select straight_join * 
  from t3 as x join t3 as y on x.b3 = y.b3 and x.d3 = y.d3
  where x.a3 = ?';
set @a=47;
execute stmt1 using @a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,b3	PRIMARY	4	const	1	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`d3` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	test.x.b3,test.x.d3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`d3` = `test`.`x`.`d3`) and (`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`x`.`a3` = 47))
set @a=0;
execute stmt1 using @a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,b3	PRIMARY	4	const	1	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`d3` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	b3	b3	9	test.x.b3,test.x.d3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`d3` = `test`.`x`.`d3`) and (`test`.`y`.`b3` = `test`.`x`.`b3`) and (`test`.`x`.`a3` = 0))
set @a=null;
execute stmt1 using @a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where (multiple equal(NULL, `test`.`x`.`a3`) and multiple equal(`test`.`x`.`b3`, `test`.`y`.`b3`) and multiple equal(`test`.`x`.`d3`, `test`.`y`.`d3`))
prepare stmt1 from
'select straight_join * 
  from t3 as x join t3 as y on x.b3 = y.b3 and x.d3 = y.d3
  where x.a3 = ?';
set @a=47;
execute stmt1 using @a;
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
set @a=0;
execute stmt1 using @a;
a3	b3	c3	d3	a3	b3	c3	d3
set @a=null;
execute stmt1 using @a;
a3	b3	c3	d3	a3	b3	c3	d3
drop table t3;
set @a=47;
execute stmt1 using @a;
ERROR 42S02: Table 'test.t3' doesn't exist
create table t1 (a int primary key, b int, c int, index(b,c)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values (1,null, 2);
insert into t1 values (2,1, null);
insert into t1 values (3,2,2);
insert into t1 values (4,null, 2);
insert into t1 values (5,1, null);
insert into t1 values (6,2,2);
set ndb_join_pushdown=false;
select *
from t1
join t1 as t2 on (t2.b = t1.b or t2.b = t1.a)
join t1 as t3 on t3.a = t2.a
join t1 as t4 on t4.a = t3.b /* index scan disguised as JT_ALL, pushdown=off */;
a	b	c	a	b	c	a	b	c	a	b	c
1	NULL	2	2	1	NULL	2	1	NULL	1	NULL	2
1	NULL	2	5	1	NULL	5	1	NULL	1	NULL	2
2	1	NULL	2	1	NULL	2	1	NULL	1	NULL	2
2	1	NULL	3	2	2	3	2	2	2	1	NULL
2	1	NULL	5	1	NULL	5	1	NULL	1	NULL	2
2	1	NULL	6	2	2	6	2	2	2	1	NULL
3	2	2	3	2	2	3	2	2	2	1	NULL
3	2	2	6	2	2	6	2	2	2	1	NULL
5	1	NULL	2	1	NULL	2	1	NULL	1	NULL	2
5	1	NULL	5	1	NULL	5	1	NULL	1	NULL	2
6	2	2	3	2	2	3	2	2	2	1	NULL
6	2	2	6	2	2	6	2	2	2	1	NULL
set ndb_join_pushdown=true;
explain
select *
from t1
join t1 as t2 on (t2.b = t1.b or t2.b = t1.a)
join t1 as t3 on t3.a = t2.a
join t1 as t4 on t4.a = t3.b /* index scan disguised as JT_ALL, pushdown=on */;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY,b	NULL	NULL	NULL	6	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY,b	NULL	NULL	NULL	6	30.56	Range checked for each record (index map: 0x3); Using pushed condition ((`test`.`t2`.`b` = `test`.`t1`.`b`) or (`test`.`t2`.`b` = `test`.`t1`.`a`))
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,b	PRIMARY	4	test.t2.a	1	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`t3`.`b` is not null)
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t3.b	1	100.00	Child of 't3' in pushed join@1
Warnings:
Note	1003	Table 't2' is not pushable: Access type was not chosen at 'prepare' time
Note	1003	Can't push table 't3' as child of 't1', column 't2.a' refers a table which was not pushed
Note	1003	Can't push table 't4' as child of 't1', column 't3.b' refers a table which was not pushed
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b`,`test`.`t3`.`c` AS `c`,`test`.`t4`.`a` AS `a`,`test`.`t4`.`b` AS `b`,`test`.`t4`.`c` AS `c` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` join `test`.`t1` `t4` where ((`test`.`t3`.`a` = `test`.`t2`.`a`) and (`test`.`t4`.`a` = `test`.`t3`.`b`) and ((`test`.`t2`.`b` = `test`.`t1`.`b`) or (`test`.`t2`.`b` = `test`.`t1`.`a`)))
select *
from t1
join t1 as t2 on (t2.b = t1.b or t2.b = t1.a)
join t1 as t3 on t3.a = t2.a
join t1 as t4 on t4.a = t3.b /* index scan disguised as JT_ALL, pushdown=on */;
a	b	c	a	b	c	a	b	c	a	b	c
1	NULL	2	2	1	NULL	2	1	NULL	1	NULL	2
1	NULL	2	5	1	NULL	5	1	NULL	1	NULL	2
2	1	NULL	2	1	NULL	2	1	NULL	1	NULL	2
2	1	NULL	3	2	2	3	2	2	2	1	NULL
2	1	NULL	5	1	NULL	5	1	NULL	1	NULL	2
2	1	NULL	6	2	2	6	2	2	2	1	NULL
3	2	2	3	2	2	3	2	2	2	1	NULL
3	2	2	6	2	2	6	2	2	2	1	NULL
5	1	NULL	2	1	NULL	2	1	NULL	1	NULL	2
5	1	NULL	5	1	NULL	5	1	NULL	1	NULL	2
6	2	2	3	2	2	3	2	2	2	1	NULL
6	2	2	6	2	2	6	2	2	2	1	NULL
explain
select *
from t1 where b in 
(select x.a from t1 as x join t1 as y on (y.a = x.b))
xor c > 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	6	100.00	Using where
2	DEPENDENT SUBQUERY	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,b	PRIMARY	4	func	1	100.00	Parent of 2 pushed join@1; Using where; Using pushed condition (`test`.`x`.`b` is not null); Full scan on NULL key
2	DEPENDENT SUBQUERY	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.b	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` where (<in_optimizer>(`test`.`t1`.`b`,<exists>(/* select#2 */ select `test`.`x`.`a` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`a` = `test`.`x`.`b`) and <if>(outer_field_is_not_null, (<cache>(`test`.`t1`.`b`) = `test`.`x`.`a`), true)))) xor (`test`.`t1`.`c` > 5))
select *
from t1 where b in 
(select x.a from t1 as x join t1 as y on (y.a = x.b))
xor c > 5;
a	b	c
3	2	2
6	2	2
Warning	1296	Prepared pushed join could not be executed, prepared with incompatible access type
Warnings:
explain
select t1.a, (select straight_join x.a from t1 as x join t1 as y on x.a=y.b where y.a = t1.b) from t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	NULL
2	DEPENDENT SUBQUERY	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	#	#	NULL
2	DEPENDENT SUBQUERY	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,b	PRIMARY	4	test.t1.b	#	#	Using where
Warnings:
Note	1276	Field or reference 'test.t1.b' of SELECT #2 was resolved in SELECT #1
Note	1003	Can't push table 'y' as child of 'x', column 't1.b' is in a subquery-branch which can't be referred
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,(/* select#2 */ select straight_join `test`.`x`.`a` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`b` = `test`.`x`.`a`) and (`test`.`y`.`a` = `test`.`t1`.`b`))) AS `(select straight_join x.a from t1 as x join t1 as y on x.a=y.b where y.a = t1.b)` from `test`.`t1`
select t1.a, (select straight_join x.a from t1 as x join t1 as y on x.a=y.b where y.a = t1.b) from t1;
a	(select straight_join x.a from t1 as x join t1 as y on x.a=y.b where y.a = t1.b)
1	NULL
2	NULL
3	1
4	NULL
5	NULL
6	1
drop table t1;
create table t1 (a int primary key, b int) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
create table t2 (a int primary key, b int) engine = myisam;
insert into t1 values(1,1), (2,2), (3,3), (4,4);
insert into t2 values(1,1), (2,2), (3,3), (4,4);
explain
select * from t1, t2, t1 as t3
where t2.a = t1.b
and t3.a = t2.b /* mixed engines */;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	Using pushed condition (`test`.`t1`.`b` is not null)
1	SIMPLE	t2	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Using where
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.b	1	100.00	NULL
Warnings:
Note	1003	Can't push table 't3' as child of 't1', column 't2.b' refers a table which was not pushed
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t3`.`a` AS `a`,`test`.`t3`.`b` AS `b` from `test`.`t1` join `test`.`t2` join `test`.`t1` `t3` where ((`test`.`t3`.`a` = `test`.`t2`.`b`) and (`test`.`t2`.`a` = `test`.`t1`.`b`))
select * from t1, t2, t1 as t3
where t2.a = t1.b
and t3.a = t2.b /* mixed engines */;
a	b	a	b	a	b
1	1	1	1	1	1
2	2	2	2	2	2
3	3	3	3	3	3
4	4	4	4	4	4
drop table t1, t2;
create table t1 (a int primary key, b int, c blob) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
create table t2 (a int primary key, b int) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values (1,1, 'kalle');
insert into t1 values (2,1, 'kalle');
insert into t1 values (3,3, 'kalle');
insert into t1 values (4,1, 'kalle');
insert into t2 values (1,1);
insert into t2 values (2,1);
insert into t2 values (3,3);
insert into t2 values (4,1);
set ndb_join_pushdown=true;
explain
select t1.a, t1.b, t2.a, t2.b 
from t1, t2
where t2.a = t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`t1`.`b` is not null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`a` = `test`.`t1`.`b`)
select t1.a, t1.b, t2.a, t2.b 
from t1, t2
where t2.a = t1.b;
a	b	a	b
1	1	1	1
2	1	1	1
3	3	3	3
4	1	1	1
explain
select t1.a, t1.b, t2.a, t2.b 
from t1, t2
where t2.a = t1.b
and t1.a = 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`t1`.`b` is not null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`a` = 2) and (`test`.`t2`.`a` = `test`.`t1`.`b`))
select t1.a, t1.b, t2.a, t2.b 
from t1, t2
where t2.a = t1.b
and t1.a = 2;
a	b	a	b
2	1	1	1
explain
select t1.a, t1.b, t2.a, t2.b
from t1, t2
where t1.a = t2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`t2`.`b` is not null)
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.b	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t1`.`a` = `test`.`t2`.`b`)
select t1.a, t1.b, t2.a, t2.b 
from t1, t2
where t2.a = t1.b;
a	b	a	b
1	1	1	1
2	1	1	1
3	3	3	3
4	1	1	1
explain
select t1.a, t1.b, t2.a, t2.b
from t1, t2
where t1.a = t2.b
and t2.a = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`t2`.`b` is not null)
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.b	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`a` = 3) and (`test`.`t1`.`a` = `test`.`t2`.`b`))
select t1.a, t1.b, t2.a, t2.b 
from t1, t2
where t1.a = t2.b
and t2.a = 3;
a	b	a	b
3	3	3	3
explain
select *
from t1, t2
where t2.a = t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	Using pushed condition (`test`.`t1`.`b` is not null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	NULL
Warnings:
Note	1003	Table 't1' is not pushable: select list can't contain BLOB columns
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`a` = `test`.`t1`.`b`)
select *
from t1, t2
where t2.a = t1.b;
a	b	c	a	b
1	1	kalle	1	1
2	1	kalle	1	1
3	3	kalle	3	3
4	1	kalle	1	1
explain
select *
from t1, t2
where t2.a = t1.b
and t1.a = 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	Using where
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	NULL
Warnings:
Note	1003	Table 't1' is not pushable: select list can't contain BLOB columns
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where ((`test`.`t1`.`a` = 2) and (`test`.`t2`.`a` = `test`.`t1`.`b`))
select *
from t1, t2
where t2.a = t1.b
and t1.a = 2;
a	b	c	a	b
2	1	kalle	1	1
explain
select *
from t1, t2
where t1.a = t2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	Using pushed condition (`test`.`t2`.`b` is not null)
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.b	1	100.00	NULL
Warnings:
Note	1003	Table 't1' is not pushable: select list can't contain BLOB columns
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t1`.`a` = `test`.`t2`.`b`)
select *
from t1, t2
where t2.a = t1.b;
a	b	c	a	b
1	1	kalle	1	1
2	1	kalle	1	1
3	3	kalle	3	3
4	1	kalle	1	1
explain
select *
from t1, t2
where t1.a = t2.b
and t2.a = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	Using where
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.b	1	100.00	NULL
Warnings:
Note	1003	Table 't1' is not pushable: select list can't contain BLOB columns
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`a` = 3) and (`test`.`t1`.`a` = `test`.`t2`.`b`))
select *
from t1, t2
where t1.a = t2.b
and t2.a = 3;
a	b	c	a	b
3	3	kalle	3	3
drop table t1, t2;
create table t3 (a3 int, b3 tinyint, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3="63";
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	5	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`y`.`b3` = 63))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3="63";
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
drop table t3;
create table t3 (a3 int, b3 tinyint unsigned, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	5	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`y`.`b3` = 63))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
drop table t3;
create table t3 (a3 int, b3 smallint, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	6	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = (60 + 3)) and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
drop table t3;
create table t3 (a3 int, b3 smallint unsigned, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	6	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`y`.`b3` = 63))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
drop table t3;
create table t3 (a3 int, b3 mediumint, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	7	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = (60 + 3)) and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
drop table t3;
create table t3 (a3 int, b3 mediumint unsigned, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	7	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`y`.`b3` = 63))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
drop table t3;
create table t3 (a3 int, b3 int, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = (60 + 3)) and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
drop table t3;
create table t3 (a3 int, b3 int unsigned, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`y`.`b3` = 63))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
drop table t3;
create table t3 (a3 int, b3 bigint, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	12	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`b3` = 63)
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = 63) and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
drop table t3;
create table t3 (a3 int, b3 bigint unsigned, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	12	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`b3` = 63)
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = 63) and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=(60+3);
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
drop table t3;
create table t3 (a3 int, b3 boolean, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0, 1, 0x1f);
insert into t3 values (0x2f, 1, 2, 0x2f);
insert into t3 values (0x3f, 0, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	5	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = 1) and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=1;
a3	b3	c3	d3	a3	b3	c3	d3
47	1	2	47	47	1	2	47
drop table t3;
create table t3 (a3 int, b3 float, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 2.71, 1, 0x1f);
insert into t3 values (0x2f, 3.00, 2, 0x2f);
insert into t3 values (0x3f, 0.50, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=3.0;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`b3` = 3)
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`y`.`b3` = 3))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=3.0;
a3	b3	c3	d3	a3	b3	c3	d3
47	3	2	47	47	3	2	47
drop table t3;
create table t3 (a3 int, b3 float unsigned, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
Warnings:
Warning	1681	UNSIGNED for decimal and floating point data types is deprecated and support for it will be removed in a future release.
insert into t3 values (0x1f, 2.71, 1, 0x1f);
insert into t3 values (0x2f, 3.00, 2, 0x2f);
insert into t3 values (0x3f, 0.50, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=3.0;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`b3` = 3)
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`y`.`b3` = 3))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=3.0;
a3	b3	c3	d3	a3	b3	c3	d3
47	3	2	47	47	3	2	47
drop table t3;
create table t3 (a3 int, b3 double, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 2.71, 1, 0x1f);
insert into t3 values (0x2f, 3.14, 2, 0x2f);
insert into t3 values (0x3f, 0.50, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=3.14;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	12	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`y`.`b3` = 3.14))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=3.14;
a3	b3	c3	d3	a3	b3	c3	d3
47	3.14	2	47	47	3.14	2	47
drop table t3;
create table t3 (a3 int, b3 double unsigned, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
Warnings:
Warning	1681	UNSIGNED for decimal and floating point data types is deprecated and support for it will be removed in a future release.
insert into t3 values (0x1f, 2.71, 1, 0x1f);
insert into t3 values (0x2f, 3.14, 2, 0x2f);
insert into t3 values (0x3f, 0.50, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=3.14;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	12	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`y`.`b3` = 3.14))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=3.14;
a3	b3	c3	d3	a3	b3	c3	d3
47	3.14	2	47	47	3.14	2	47
drop table t3;
create table t3 (a3 int, b3 decimal, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=63;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	9	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f) and (`test`.`y`.`b3` = 63))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=63;
a3	b3	c3	d3	a3	b3	c3	d3
47	63	2	47	47	63	2	47
drop table t3;
create table t3 (a3 int, b3 decimal(12,4), c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 2.71, 1, 0x1f);
insert into t3 values (0x2f, 3.14, 2, 0x2f);
insert into t3 values (0x3f, 0.50, 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=3.14;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	10	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = 3.1400) and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3=3.14;
a3	b3	c3	d3	a3	b3	c3	d3
47	3.1400	2	47	47	3.1400	2	47
drop table t3;
create table t3 (a3 int, b3 date, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, '1905-05-17', 1, 0x1f);
insert into t3 values (0x2f, '2000-02-28', 2, 0x2f);
insert into t3 values (0x3f, '2000-02-29', 3, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3='2000-02-28';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	7	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = DATE'2000-02-28') and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3='2000-02-28';
a3	b3	c3	d3	a3	b3	c3	d3
47	2000-02-28	2	47	47	2000-02-28	2	47
drop table t3;
create table t3 (a3 int, b3 char(16), c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 'Ole', 1, 0x1f);
insert into t3 values (0x2f, 'Dole', 2, 0x2f);
insert into t3 values (0x3f, 'Doffen', 2, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3='Dole';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	68	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`b3` = 'Dole')
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = 'Dole') and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3='Dole';
a3	b3	c3	d3	a3	b3	c3	d3
47	Dole	2	47	47	Dole	2	47
drop table t3;
create table t3 (a3 int, b3 varchar(16), c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 'Ole', 1, 0x1f);
insert into t3 values (0x2f, 'Dole', 2, 0x2f);
insert into t3 values (0x3f, 'Doffen', 2, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3='Dole';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	70	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = 'Dole') and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3='Dole';
a3	b3	c3	d3	a3	b3	c3	d3
47	Dole	2	47	47	Dole	2	47
drop table t3;
create table t3 (a3 int, b3 varchar(512), c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 'Ole', 1, 0x1f);
insert into t3 values (0x2f, 'Dole', 2, 0x2f);
insert into t3 values (0x3f, 'Doffen', 2, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3='Dole';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	2054	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = 'Dole') and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3='Dole';
a3	b3	c3	d3	a3	b3	c3	d3
47	Dole	2	47	47	Dole	2	47
drop table t3;
create table t3 (a3 int, b3 binary(16), c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 'Ole', 1, 0x1f);
insert into t3 values (0x2f, 'Dole', 2, 0x2f);
insert into t3 values (0x3f, 'Doffen', 2, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3='Dole';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	20	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1; Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = 'Dole') and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3='Dole';
a3	b3	c3	d3	a3	b3	c3	d3
drop table t3;
create table t3 (a3 int, b3 varbinary(16), c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 'Ole', 1, 0x1f);
insert into t3 values (0x2f, 'Dole', 2, 0x2f);
insert into t3 values (0x3f, 'Doffen', 2, 0x3f);
explain
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3='Dole';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	22	test.x.d3,const	1	100.00	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`b3` = 'Dole')
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = 'Dole') and (`test`.`y`.`a3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 0x2f))
select * from t3 x, t3 y where x.a3=0x2f and y.a3=x.d3 and y.b3='Dole';
a3	b3	c3	d3	a3	b3	c3	d3
47	Dole	2	47	47	Dole	2	47
drop table t3;
create table t3 (a3 int, b3 tinyint, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values (0x1f, 0x2f, 1, 0x1f);
insert into t3 values (0x2f, 0x3f, 2, 0x2f);
insert into t3 values (0x3f, 0x1f, 3, 0x3f);
explain
select * from t3 x, t3 y where y.a3=x.b3 and y.b3="63";
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	5	test.x.b3,const	1	100.00	Using where
Warnings:
Note	1003	Can't push table 'y' as child, column 'a3' does not have same datatype as ref'ed column 'x.b3'
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`a3` = `test`.`x`.`b3`) and (`test`.`y`.`b3` = 63))
select * from t3 x, t3 y where y.a3=x.b3 and y.b3="63";
a3	b3	c3	d3	a3	b3	c3	d3
31	47	1	31	47	63	2	47
drop table t3;
create table t3 (a3 varchar(16), b3 int, c3 int not null, d3 int not null, 
primary key(a3,b3)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t3 values ('Ole', 0x1f, 1, 0x1f);
insert into t3 values ('Dole', 0x2f, 2, 0x2f);
insert into t3 values ('Doffen', 0x3f, 2, 0x3f);
explain
select * from t3 x, t3 y where x.a3='Dole' and x.b3=0x2f and y.a3=x.a3 and y.b3=x.d3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	70	const,const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	70	const,test.x.d3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 'Dole') and (`test`.`y`.`a3` = 'Dole') and (`test`.`x`.`b3` = 0x2f))
select * from t3 x, t3 y where x.a3='Dole' and x.b3=0x2f and y.a3=x.a3 and y.b3=x.d3;
a3	b3	c3	d3	a3	b3	c3	d3
Dole	47	2	47	Dole	47	2	47
explain
select * from t3 x, t3 y where x.a3='Dole' and y.a3=x.a3 and y.b3=x.d3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	66	const	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	70	const,test.x.d3	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a3` AS `a3`,`test`.`x`.`b3` AS `b3`,`test`.`x`.`c3` AS `c3`,`test`.`x`.`d3` AS `d3`,`test`.`y`.`a3` AS `a3`,`test`.`y`.`b3` AS `b3`,`test`.`y`.`c3` AS `c3`,`test`.`y`.`d3` AS `d3` from `test`.`t3` `x` join `test`.`t3` `y` where ((`test`.`y`.`b3` = `test`.`x`.`d3`) and (`test`.`x`.`a3` = 'Dole') and (`test`.`y`.`a3` = 'Dole'))
select * from t3 x, t3 y where x.a3='Dole' and y.a3=x.a3 and y.b3=x.d3;
a3	b3	c3	d3	a3	b3	c3	d3
Dole	47	2	47	Dole	47	2	47
drop table t3;
create table t1 (k int primary key, b int) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values (1,1), (2,1), (3,1), (4,1);
explain
select *
from t1
straight_join t1 as t2 on t2.k = t1.b+0
straight_join t1 as t3 on t3.k = t2.b
straight_join t1 as t4 on t4.k = t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`t1`.`b` is not null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	func	1	100.00	Parent of 2 pushed join@2; Using pushed condition ((`test`.`t2`.`k` = (`test`.`t1`.`b` + 0)) and (`test`.`t2`.`b` is not null))
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.b	1	100.00	Child of 't2' in pushed join@2
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	Can't push table 't2' as child, column 'k' does neither 'ref' a column nor a constant
Note	1003	Can't push table 't3' as child of 't1', column 't2.b' refers a table which was not pushed
Note	1003	/* select#1 */ select `test`.`t1`.`k` AS `k`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`k` AS `k`,`test`.`t2`.`b` AS `b`,`test`.`t3`.`k` AS `k`,`test`.`t3`.`b` AS `b`,`test`.`t4`.`k` AS `k`,`test`.`t4`.`b` AS `b` from `test`.`t1` straight_join `test`.`t1` `t2` straight_join `test`.`t1` `t3` straight_join `test`.`t1` `t4` where ((`test`.`t3`.`k` = `test`.`t2`.`b`) and (`test`.`t4`.`k` = `test`.`t1`.`b`) and (`test`.`t2`.`k` = (`test`.`t1`.`b` + 0)))
select *
from t1
straight_join t1 as t2 on t2.k = t1.b+0
straight_join t1 as t3 on t3.k = t2.b
straight_join t1 as t4 on t4.k = t1.b;
k	b	k	b	k	b	k	b
1	1	1	1	1	1	1	1
2	1	1	1	1	1	1	1
3	1	1	1	1	1	1	1
4	1	1	1	1	1	1	1
explain
select *
from t1
straight_join t1 as t2 on t2.k = t1.b+0
straight_join t1 as t3 on t3.k = t2.b
straight_join t1 as t4 on t4.k = t1.b
where t2.k = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	Parent of 2 pushed join@1; Using where; Using pushed condition (`test`.`t1`.`b` is not null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	Parent of 2 pushed join@2; Using pushed condition (`test`.`t2`.`b` is not null)
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.b	1	100.00	Child of 't2' in pushed join@2
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	Can't push table 't2' as child of 't1', no parent-child dependency exists between these tables
Note	1003	Can't push table 't3' as child of 't1', column 't2.b' refers a table which was not pushed
Note	1003	/* select#1 */ select `test`.`t1`.`k` AS `k`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`k` AS `k`,`test`.`t2`.`b` AS `b`,`test`.`t3`.`k` AS `k`,`test`.`t3`.`b` AS `b`,`test`.`t4`.`k` AS `k`,`test`.`t4`.`b` AS `b` from `test`.`t1` straight_join `test`.`t1` `t2` straight_join `test`.`t1` `t3` straight_join `test`.`t1` `t4` where ((`test`.`t3`.`k` = `test`.`t2`.`b`) and (`test`.`t4`.`k` = `test`.`t1`.`b`) and (`test`.`t2`.`k` = 1) and (1 = (`test`.`t1`.`b` + 0)))
select *
from t1
straight_join t1 as t2 on t2.k = t1.b+0
straight_join t1 as t3 on t3.k = t2.b
straight_join t1 as t4 on t4.k = t1.b
where t2.k = 1;
k	b	k	b	k	b	k	b
1	1	1	1	1	1	1	1
2	1	1	1	1	1	1	1
3	1	1	1	1	1	1	1
4	1	1	1	1	1	1	1
drop table t1;
set global debug='+d,max_64rows_in_spj_batches';
create table t1 (
a int not null auto_increment,
b char(255) not null,
c int not null,
d char(255) not null,
primary key (`a`,`b`)
) engine=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
explain
select count(*)
from t1 
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
join t1 as t3 on t3.a = t2.c and t3.b = t2.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	3000	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	1024	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	1024	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`d`) and (`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t3`.`a` = `test`.`t2`.`c`))
select count(*)
from t1 
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
join t1 as t3 on t3.a = t2.c and t3.b = t2.d;
count(*)
2996
explain
select count(*)
from t1
join t1 as t2 on t2.a = t1.c
join t1 as t3 on t3.a = t2.c and t3.b = t2.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	3000	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.c	30	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	1024	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t3`.`a` = `test`.`t2`.`c`))
select count(*)
from t1
join t1 as t2 on t2.a = t1.c
join t1 as t3 on t3.a = t2.c and t3.b = t2.d;
count(*)
8990
explain
select count(*)
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
join t1 as t3 on t3.a = t2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	3000	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	1024	test.t1.c,test.t1.d	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t2.c	30	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`b` = `test`.`t1`.`d`) and (`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t3`.`a` = `test`.`t2`.`c`))
select count(*)
from t1
join t1 as t2 on t2.a = t1.c and t2.b = t1.d
join t1 as t3 on t3.a = t2.c;
count(*)
8988
alter table t1 partition by key(a);
explain
select count(*)
from t1
join t1 as t2 on t2.a = t1.c
join t1 as t3 on t3.a = t2.c and t3.b = t2.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	3000	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.c	30	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	1024	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` where ((`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t3`.`a` = `test`.`t2`.`c`))
select count(*)
from t1
join t1 as t2 on t2.a = t1.c
join t1 as t3 on t3.a = t2.c and t3.b = t2.d;
count(*)
8990
drop table t1;
set global debug=@save_debug;
create logfile group lg1
add undofile 'undofile.dat'
initial_size 1m
undo_buffer_size = 1m
engine=ndb;
create tablespace ts1
add datafile 'datafile.dat'
use logfile group lg1
initial_size 6m
engine ndb;
create table t1 (a int not null, 
b int not null storage disk, 
c int not null storage memory, 
primary key(a)) 
tablespace ts1 storage disk engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values (10, 11, 11);
insert into t1 values (11, 12, 12);
insert into t1 values (12, 13, 13);
create table t2 (a int not null, 
b int not null, primary key(a)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t2 values (10, 11);
insert into t2 values (11, 12);
insert into t2 values (12, 13);
explain select * from t1, t2 where t1.c = t2.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`a` = `test`.`t1`.`c`)
select * from t1, t2 where t1.c = t2.a;
a	b	c	a	b
10	11	11	11	12
11	12	12	12	13
explain select * from t1, t2 where t1.a=11 and t1.c = t2.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.c	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t1`.`a` = 11))
select * from t1, t2 where t1.a=11 and t1.c = t2.a;
a	b	c	a	b
11	12	12	12	13
explain select * from t2, t1 where t2.b = t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.b	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t2` join `test`.`t1` where (`test`.`t1`.`a` = `test`.`t2`.`b`)
select * from t2, t1 where t2.b = t1.a;
a	b	a	b	c
10	11	11	12	12
11	12	12	13	13
explain select * from t2, t1 where t2.a=11 and t2.b = t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t2.b	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t2` join `test`.`t1` where ((`test`.`t1`.`a` = `test`.`t2`.`b`) and (`test`.`t2`.`a` = 11))
select * from t2, t1 where t2.a=11 and t2.b = t1.a;
a	b	a	b	c
11	12	12	13	13
explain select t1.a, t1.c, t2.a, t2.b from t1, t2 where t1.b = t2.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	3	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`a` = `test`.`t1`.`b`)
select t1.a, t1.c, t2.a, t2.b from t1, t2 where t1.b = t2.a;
a	c	a	b
10	11	11	12
11	12	12	13
explain select t1.a, t1.c, t2.a, t2.b from t1, t2 where t1.a=11 and t1.b = t2.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`c` AS `c`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t1`.`a` = 11))
select t1.a, t1.c, t2.a, t2.b from t1, t2 where t1.a=11 and t1.b = t2.a;
a	c	a	b
11	12	12	13
drop table t1;
drop table t2;
alter tablespace ts1
drop datafile 'datafile.dat';
drop tablespace ts1;
drop logfile group lg1
engine ndb;
create temporary table old_count 
select counter_name, sum(val) as val 
from ndbinfo.counters 
where block_name='DBSPJ' 
group by counter_name;
create table t1 (a int not null, 
b int not null,
c int not null,
primary key(a)) 
engine = ndb
partition by key() partitions 8;
insert into t1 values (1, 2, 2);
insert into t1 values (2, 3, 3);
insert into t1 values (3, 4, 4);
select * from t1 t1, t1 t2 where t1.a = 2 and t2.a = t1.b;
a	b	c	a	b	c
2	3	3	3	4	4
select count(*) from t1 t1, t1 t2 where t2.a = t1.b;
count(*)
2
select count(*) from t1 t1, t1 t2 where t1.a >= 2 and t2.a = t1.b;
count(*)
1
create temporary table new_count 
select counter_name, sum(val) as val 
from ndbinfo.counters 
where block_name='DBSPJ' 
group by counter_name;
select new_count.counter_name, new_count.val - old_count.val 
from new_count, old_count 
where new_count.counter_name = old_count.counter_name
and new_count.counter_name <> 'LOCAL_READS_SENT'
       and new_count.counter_name <> 'REMOTE_READS_SENT';
counter_name	new_count.val - old_count.val
CONST_PRUNED_RANGE_SCANS_RECEIVED	0
LOCAL_RANGE_SCANS_SENT	8
LOCAL_TABLE_SCANS_SENT	8
PRUNED_RANGE_SCANS_RECEIVED	0
RANGE_SCANS_RECEIVED	2
READS_NOT_FOUND	2
READS_RECEIVED	1
REMOTE_RANGE_SCANS_SENT	0
SCAN_BATCHES_RETURNED	4
SCAN_ROWS_RETURNED	8
TABLE_SCANS_RECEIVED	2
select 'READS_SENT', sum(new_count.val - old_count.val) 
from new_count, old_count 
where new_count.counter_name = old_count.counter_name
and (new_count.counter_name = 'LOCAL_READS_SENT'
       or new_count.counter_name = 'REMOTE_READS_SENT');
READS_SENT	sum(new_count.val - old_count.val)
READS_SENT	7
drop table old_count;
drop table new_count;
drop table t1;
create table t1 (
a int primary key, 
b int,
c int) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values (1, 2, 3);
insert into t1 values (2, 3, 4);
insert into t1 values (3, 4, 5);
explain select * from t1 x, t1 y where x.b=y.a and x.c=4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`x`.`c` = 4) and (`test`.`x`.`b` is not null))
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.b	#	#	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`x`.`c` AS `c`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b`,`test`.`y`.`c` AS `c` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`x`.`c` = 4) and (`test`.`y`.`a` = `test`.`x`.`b`))
select * from t1 x, t1 y where x.b=y.a and x.c=4;
a	b	c	a	b	c
2	3	4	3	4	5
lookups
1
explain select * from t1 x, t1 y, t1 z where x.b=y.a and y.c=4 and y.b=z.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 3 pushed join@1; Using pushed condition (`test`.`x`.`b` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.b	#	#	Child of 'x' in pushed join@1; Using pushed condition ((`test`.`y`.`c` = 4) and (`test`.`y`.`b` is not null))
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.y.b	#	#	Child of 'y' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`x`.`c` AS `c`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b`,`test`.`y`.`c` AS `c`,`test`.`z`.`a` AS `a`,`test`.`z`.`b` AS `b`,`test`.`z`.`c` AS `c` from `test`.`t1` `x` join `test`.`t1` `y` join `test`.`t1` `z` where ((`test`.`z`.`a` = `test`.`y`.`b`) and (`test`.`y`.`c` = 4) and (`test`.`y`.`a` = `test`.`x`.`b`))
select * from t1 x, t1 y, t1 z where x.b=y.a and y.c=4 and y.b=z.a;
a	b	c	a	b	c	a	b	c
1	2	3	2	3	4	3	4	5
lookups
5
drop table t1;
create table t1(
a int not null,
b int not null,
c int not null,
primary key(a,b))
engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM"
partition by key (a);
insert into t1 values (10, 10, 11);
insert into t1 values (11, 11, 12);
insert into t1 values (12, 12, 13);
select * from t1 t1, t1 t2 
where t1.a = 10 and t1.b = 10 and 
t2.a = t1.c and t2.b = t1.c;
a	b	c	a	b	c
10	10	11	11	11	12
create temporary table server_counts
select * from performance_schema.global_status 
where variable_name in 
('Ndb_scan_count',
'Ndb_pruned_scan_count',
'Ndb_sorted_scan_count',
'Ndb_pushed_queries_defined',
'Ndb_pushed_queries_dropped',
'Ndb_pushed_reads');
select * from t1 t1, t1 t2 
where t1.a = 11 and t1.b = 11 and 
t2.a = t1.c and t2.b = t1.c;
a	b	c	a	b	c
11	11	12	12	12	13
select * from t1 t1, t1 t2 
where t2.a = t1.c and t2.b = t1.c
order by t1.a;
a	b	c	a	b	c
10	10	11	11	11	12
11	11	12	12	12	13
select count(*) from t1 t1, t1 t2 
where t1.a = 11 and 
t2.a = t1.c and t2.b = t1.c;
count(*)
1
select new.variable_name, new.variable_value - old.variable_value
from server_counts as old,
performance_schema.global_status as new
where new.variable_name = old.variable_name
order by new.variable_name;
variable_name	new.variable_value - old.variable_value
Ndb_pruned_scan_count	1
Ndb_pushed_queries_defined	3
Ndb_pushed_queries_dropped	0
Ndb_pushed_reads	4
Ndb_scan_count	2
Ndb_sorted_scan_count	1
drop table server_counts;
drop table t1;
create table t1(
d int not null,
c int not null,
a int not null,
b int not null,
primary key using hash (a,b))
engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM"
partition by key (a);
insert into t1(a,b,c,d) values (10, 10, 11, 11);
insert into t1(a,b,c,d) values (11, 11, 12, 12);
insert into t1(a,b,c,d) values (12, 12, 13, 13);
create index i1 on t1(c,a);
select count(*) from t1 t1, t1 t2 where t1.c = 12 and t1.a = 11 and t2.a = t1.d and t2.b = t1.d;
count(*)
1
drop index i1 on t1;
pruned_scan_count
1
create index i2 on t1(a,b);
select count(*) from t1 t1, t1 t2 where t1.a = 11 and t1.b<13 and t2.a = t1.c and t2.b = t1.c;
count(*)
1
pruned_scan_count
1
select count(*) from t1 t1, t1 t2 where t1.a >= 12 and t1.a<=12 and t2.a = t1.c and t2.b = t1.c;
count(*)
0
pruned_scan_count
1
select count(*) from t1 t1, t1 t2 where t1.a >= 11 and t1.a<=12 and t2.a = t1.c and t2.b = t1.c;
count(*)
1
pruned_scan_count
0
select count(*) from t1 t1, t1 t2 where (t1.a = 10 or t1.a=12) and t1.b<13 and t2.a = t1.c and t2.b = t1.c;
count(*)
1
pruned_scan_count
0
select count(*) from t1 t1, t1 t2 where t1.a = 10 and (t1.b<11 or t1.b>11) and t2.a = t1.c and t2.b = t1.c;
count(*)
1
pruned_scan_count
1
drop table t1;
create table t2(
d int not null,
e int not null,
f int not null,
a int not null,
b int not null,
c int not null,
primary key using hash (a,b,c))
engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM"
partition by key (b,a);
insert into t2(a,b,c,d,e,f) values (1, 2, 3, 1, 2, 3);
insert into t2(a,b,c,d,e,f) values (1, 2, 4, 1, 2, 3);
insert into t2(a,b,c,d,e,f) values (2, 3, 4, 1, 2, 3);
insert into t2(a,b,c,d,e,f) values (3, 4, 5, 1, 2, 3);
insert into t2(a,b,c,d,e,f) values (4, 5, 6, 1, 2, 3);
insert into t2(a,b,c,d,e,f) values (5, 6, 7, 1, 2, 3);
insert into t2(a,b,c,d,e,f) values (6, 7, 8, 1, 2, 3);
insert into t2(a,b,c,d,e,f) values (7, 8, 9, 1, 2, 3);
create index i2_1 on t2(d, a, b, e);
select count(*) from t2 x, t2 y where x.d=1 and x.a=1 and x.b=2 and y.a=x.d and y.b=x.e and y.c=3;
count(*)
2
pruned_scan_count
1
drop index i2_1 on t2;
create index i2_3 on t2(a, d, b, e);
set optimizer_switch='condition_fanout_filter=off';
select count(*) from t2 x, t2 y where x.d=1 and x.a=1 and x.b=2 and y.a=x.d and y.b=x.e and y.c=3;
count(*)
2
set optimizer_switch='condition_fanout_filter=default';
pruned_scan_count
1
drop table t2;
create table t1 (a binary(10) primary key, b binary(10) not null) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values ('\0123456789', '1234567890');
insert into t1 values ('1234567890', '\0123456789');
explain
select count(*)
from t1 join t1 as t2 on t2.a = t1.b
where t1.a = '\0123456789';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	10	const	1	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`t1`.`a` = '\0123456789')
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	10	test.t1.b	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t1`.`a` = '\0123456789'))
select count(*)
from t1 join t1 as t2 on t2.a = t1.b
where t1.a = '\0123456789';
count(*)
1
drop table t1;
create table t1 (pk int primary key, a int unique key) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values (1,10), (2,20), (3,30);
set ndb_join_pushdown = false;
explain
select * from t1 as x right join t1 as y
on x.pk = y.pk
and x.pk = y.a
and x.a = y.pk
where y.pk = 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	const	PRIMARY	PRIMARY	4	const	1	100.00	NULL
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	const	PRIMARY,a	PRIMARY	4	const	1	100.00	Impossible ON condition
Warnings:
Note	1003	/* select#1 */ select NULL AS `pk`,NULL AS `a`,'2' AS `pk`,'20' AS `a` from `test`.`t1` `y` left join `test`.`t1` `x` on((multiple equal(2, NULL, NULL))) where true
select * from t1 as x right join t1 as y
on x.pk = y.pk
and x.pk = y.a
and x.a = y.pk
where y.pk = 2;
pk	a	pk	a
NULL	NULL	2	20
set ndb_join_pushdown = true;
explain
select * from t1 as x right join t1 as y
on x.pk = y.pk
and x.pk = y.a
and x.a = y.pk
where y.pk = 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	NULL
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,a	PRIMARY	4	const	1	100.00	Using where
Warnings:
Note	1003	Can't push table 'x' as child of 'y', no parent-child dependency exists between these tables
Note	1003	/* select#1 */ select `test`.`x`.`pk` AS `pk`,`test`.`x`.`a` AS `a`,`test`.`y`.`pk` AS `pk`,`test`.`y`.`a` AS `a` from `test`.`t1` `y` left join `test`.`t1` `x` on(((`test`.`y`.`a` = 2) and (`test`.`x`.`pk` = 2) and (`test`.`x`.`a` = 2))) where (`test`.`y`.`pk` = 2)
select * from t1 as x right join t1 as y
on x.pk = y.pk
and x.pk = y.a
and x.a = y.pk
where y.pk = 2;
pk	a	pk	a
NULL	NULL	2	20
drop table t1;
create table t1 (pk int primary key, u int not null, a int, b int) engine=ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
create index ix1 on t1(b,a);
insert into t1 values (0,1,10,20);
insert into t1 values (1,2,20,30);
insert into t1 values (2,3,30,40);
explain select * from t1 as x join t1 as y join t1 as z on x.u=y.pk and y.a=z.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 3 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.u	#	#	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`a` is not null)
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.y.a	#	#	Child of 'y' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`pk` AS `pk`,`test`.`x`.`u` AS `u`,`test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`y`.`pk` AS `pk`,`test`.`y`.`u` AS `u`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b`,`test`.`z`.`pk` AS `pk`,`test`.`z`.`u` AS `u`,`test`.`z`.`a` AS `a`,`test`.`z`.`b` AS `b` from `test`.`t1` `x` join `test`.`t1` `y` join `test`.`t1` `z` where ((`test`.`z`.`b` = `test`.`y`.`a`) and (`test`.`y`.`pk` = `test`.`x`.`u`))
select * from t1 as x join t1 as y join t1 as z on x.u=y.pk and y.a=z.b;
pk	u	a	b	pk	u	a	b	pk	u	a	b
0	1	10	20	1	2	20	30	0	1	10	20
1	2	20	30	2	3	30	40	1	2	20	30
drop table t1;
create table t1 (pk int primary key, u int not null) engine=ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values (0,-1), (1,-1), (2,-1), (3,-1), (4,-1), (5,-1), (6,-1), 
(7,-1), (8,-1), (9,-1), (10,-1), (11,-1), (12,-1), (13,-1), (14,-1), (15,-1), 
(16,-1), (17,-1), (18,-1), (19,-1), (20,-1), (21,-1), (22,-1), (23,-1), 
(24,-1), (25,-1), (26,-1), (27,-1), (28,-1), (29,-1), (30,-1), (31,-1), 
(32,-1), (33,-1), (34,-1), (35,-1), (36,-1), (37,-1), (38,-1), (39,-1), 
(40,-1), (41,-1), (42,-1), (43,-1), (44,-1), (45,-1), (46,-1), (47,-1), 
(48,-1), (49,-1), (50,-1), (51,-1), (52,-1), (53,-1), (54,-1), (55,-1), 
(56,-1), (57,-1), (58,-1), (59,-1), (60,-1), (61,-1), (62,-1), (63,-1), 
(64,-1), (65,-1), (66,-1), (67,-1), (68,-1), (69,-1), (70,-1), (71,-1), 
(72,-1), (73,-1), (74,-1), (75,-1), (76,-1), (77,-1), (78,-1), (79,-1), 
(80,-1), (81,-1), (82,-1), (83,-1), (84,-1), (85,-1), (86,-1), (87,-1), 
(88,-1), (89,-1), (90,-1), (91,-1), (92,-1), (93,-1), (94,-1), (95,-1), 
(96,-1), (97,-1), (98,-1), (99,-1), (100,-1), (101,-1), (102,-1), (103,-1), 
(104,-1), (105,-1), (106,-1), (107,-1), (108,-1), (109,-1), (110,-1), 
(111,-1), (112,-1), (113,-1), (114,-1), (115,-1), (116,-1), (117,-1), 
(118,-1), (119,-1), (120,-1), (121,-1), (122,-1), (123,-1), (124,-1), 
(125,-1), (126,-1), (127,-1), (128,-1), (129,-1), (130,-1), (131,-1), 
(132,-1), (133,-1), (134,-1), (135,-1), (136,-1), (137,-1), (138,-1), (139,-1);
set global debug='+d,max_64rows_in_spj_batches';
explain select * from t1 as x join t1 as y on x.u=y.pk order by(x.pk);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	4	NULL	140	100.00	Parent of 2 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.u	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`pk` AS `pk`,`test`.`x`.`u` AS `u`,`test`.`y`.`pk` AS `pk`,`test`.`y`.`u` AS `u` from `test`.`t1` `x` join `test`.`t1` `y` where (`test`.`y`.`pk` = `test`.`x`.`u`) order by `test`.`x`.`pk`
select * from t1 as x join t1 as y on x.u=y.pk order by(x.pk);
pk	u	pk	u
set global debug=@save_debug;
drop table t1;
create table t1 (pk int primary key, u int not null, a int, b int) engine=ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
create index ix1 on t1(b,a);
create unique index ix2 on t1(u);
insert into t1 values (0,0,10,10);
insert into t1 values (1,1,10,10);
insert into t1 values (2,2,10,10);
insert into t1 values (3,3,10,10);
insert into t1 values (4,4,10,10);
insert into t1 values (5,5,10,10);
insert into t1 values (6,6,10,10);
insert into t1 values (7,7,10,10);
insert into t1 values (8,8,10,10);
insert into t1 values (9,9,10,10);
insert into t1 values (10,10,10,10);
insert into t1 values (11,11,10,10);
explain select count(*) from t1 as x1 join t1 as x2 join t1 as x3
on x1.a=x2.u and x2.a = x3.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	12	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`x1`.`a` is not null)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	ix2	ix2	4	test.x1.a	1	100.00	Child of 'x1' in pushed join@1; Using pushed condition (`test`.`x2`.`a` is not null)
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x2.a	2	100.00	Child of 'x2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t1` `x3` where ((`test`.`x3`.`b` = `test`.`x2`.`a`) and (`test`.`x2`.`u` = `test`.`x1`.`a`))
select count(*) from t1 as x1 join t1 as x2 join t1 as x3 
on x1.a=x2.u and x2.a = x3.b;
count(*)
144
explain select count(*) from t1 as x1, t1 as x2, t1 as x3
where x1.u=x2.pk and x1.a=x3.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	ix2	NULL	NULL	NULL	12	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`x1`.`a` is not null)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x1.u	1	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	2	100.00	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t1` `x3` where ((`test`.`x3`.`b` = `test`.`x1`.`a`) and (`test`.`x2`.`pk` = `test`.`x1`.`u`))
select count(*) from t1 as x1, t1 as x2, t1 as x3 
where x1.u=x2.pk and x1.a=x3.b;
count(*)
144
insert into t1 values (12,12,20,10);
explain select count(*) from t1 as x1 left join t1 as x2 on x1.a=x2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 2 pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	2	100.00	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` `x1` left join `test`.`t1` `x2` on((`test`.`x2`.`b` = `test`.`x1`.`a`)) where true
select count(*) from t1 as x1 left join t1 as x2 on x1.a=x2.b;
count(*)
157
set ndb_join_pushdown=off;
select count(*) from t1 as x1 left join t1 as x2 on x1.a=x2.b;
count(*)
157
set ndb_join_pushdown=on;
explain select count(*) from t1 as x1
left join t1 as x2 on x1.u=x2.pk 
left join t1 as x3 on x2.a=x3.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 3 pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x1.u	1	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x2.a	2	100.00	Child of 'x2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` `x1` left join `test`.`t1` `x2` on((`test`.`x2`.`pk` = `test`.`x1`.`u`)) left join `test`.`t1` `x3` on((`test`.`x3`.`b` = `test`.`x2`.`a`)) where true
select count(*) from t1 as x1 
left join t1 as x2 on x1.u=x2.pk 
left join t1 as x3 on x2.a=x3.b;
count(*)
157
set ndb_join_pushdown=off;
select count(*) from t1 as x1 
left join t1 as x2 on x1.u=x2.pk 
left join t1 as x3 on x2.a=x3.b;
count(*)
157
set ndb_join_pushdown=on;
explain select count(*) from t1 as x1
left join t1 as x2 on x1.u=x2.pk
left join t1 as x3 on x2.a=x3.b
left join t1 as x4 on x3.u=x4.pk
left join t1 as x5 on x4.a=x5.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 5 pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x1.u	1	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x2.a	2	100.00	Child of 'x2' in pushed join@1
1	SIMPLE	x4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x3.u	1	100.00	Child of 'x3' in pushed join@1
1	SIMPLE	x5	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x4.a	2	100.00	Child of 'x4' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` `x1` left join `test`.`t1` `x2` on((`test`.`x2`.`pk` = `test`.`x1`.`u`)) left join `test`.`t1` `x3` on((`test`.`x3`.`b` = `test`.`x2`.`a`)) left join `test`.`t1` `x4` on((`test`.`x4`.`pk` = `test`.`x3`.`u`)) left join `test`.`t1` `x5` on((`test`.`x5`.`b` = `test`.`x4`.`a`)) where true
select count(*) from t1 as x1
left join t1 as x2 on x1.u=x2.pk
left join t1 as x3 on x2.a=x3.b
left join t1 as x4 on x3.u=x4.pk
left join t1 as x5 on x4.a=x5.b;
count(*)
1885
set ndb_join_pushdown=off;
select count(*) from t1 as x1
left join t1 as x2 on x1.u=x2.pk
left join t1 as x3 on x2.a=x3.b
left join t1 as x4 on x3.u=x4.pk
left join t1 as x5 on x4.a=x5.b;
count(*)
1885
set ndb_join_pushdown=on;
explain select count(*) from t1 as x1
join t1 as x2 on x1.a=x2.b
where x1.pk = 1 or x1.u=1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	index_merge	PRIMARY,ix2	ix2,PRIMARY	4,4	NULL	2	100.00	Using sort_union(ix2,PRIMARY); Using pushed condition (((`test`.`x1`.`pk` = 1) or (`test`.`x1`.`u` = 1)) and (`test`.`x1`.`a` is not null))
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	2	100.00	NULL
Warnings:
Note	1003	Push of table 'x2' as scan-child with lookup-root 'x1' not implemented
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` `x1` join `test`.`t1` `x2` where ((`test`.`x2`.`b` = `test`.`x1`.`a`) and ((`test`.`x1`.`pk` = 1) or (`test`.`x1`.`u` = 1)))
select count(*) from t1 as x1
join t1 as x2 on x1.a=x2.b
where x1.pk = 1 or x1.u=1;
count(*)
13
set global debug='+d,max_4rows_in_spj_batches';
set ndb_join_pushdown=on;
explain
select straight_join * from t1 as table1
left join 
(t1 as table2  join t1 as table3 on table2.pk = table3.b)
on table1.pk = table2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	table1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 3 pushed join@1
1	SIMPLE	table2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY,ix1	ix1	5	test.table1.pk	2	100.00	Child of 'table1' in pushed join@1
1	SIMPLE	table3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.table2.pk	2	100.00	Child of 'table2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`table1`.`pk` AS `pk`,`test`.`table1`.`u` AS `u`,`test`.`table1`.`a` AS `a`,`test`.`table1`.`b` AS `b`,`test`.`table2`.`pk` AS `pk`,`test`.`table2`.`u` AS `u`,`test`.`table2`.`a` AS `a`,`test`.`table2`.`b` AS `b`,`test`.`table3`.`pk` AS `pk`,`test`.`table3`.`u` AS `u`,`test`.`table3`.`a` AS `a`,`test`.`table3`.`b` AS `b` from `test`.`t1` `table1` left join (`test`.`t1` `table2` join `test`.`t1` `table3`) on(((`test`.`table3`.`b` = `test`.`table2`.`pk`) and (`test`.`table2`.`b` = `test`.`table1`.`pk`))) where true
select straight_join * from t1 as table1
left join 
(t1 as table2  join t1 as table3 on table2.pk = table3.b)
on table1.pk = table2.b;
pk	u	a	b	pk	u	a	b	pk	u	a	b
0	0	10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
1	1	10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
10	10	10	10	10	10	10	10	0	0	10	10
10	10	10	10	10	10	10	10	1	1	10	10
10	10	10	10	10	10	10	10	10	10	10	10
10	10	10	10	10	10	10	10	11	11	10	10
10	10	10	10	10	10	10	10	12	12	20	10
10	10	10	10	10	10	10	10	2	2	10	10
10	10	10	10	10	10	10	10	3	3	10	10
10	10	10	10	10	10	10	10	4	4	10	10
10	10	10	10	10	10	10	10	5	5	10	10
10	10	10	10	10	10	10	10	6	6	10	10
10	10	10	10	10	10	10	10	7	7	10	10
10	10	10	10	10	10	10	10	8	8	10	10
10	10	10	10	10	10	10	10	9	9	10	10
11	11	10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
12	12	20	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
2	2	10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
3	3	10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
4	4	10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
5	5	10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
6	6	10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
7	7	10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
8	8	10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
9	9	10	10	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
set global debug=@save_debug;
explain select straight_join * from t1 as x1
inner join t1 as x2 on x2.b = x1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x1`.`a` is not null)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	2	100.00	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x1`.`pk` AS `pk`,`test`.`x1`.`u` AS `u`,`test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x2`.`pk` AS `pk`,`test`.`x2`.`u` AS `u`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b` from `test`.`t1` `x1` join `test`.`t1` `x2` where (`test`.`x2`.`b` = `test`.`x1`.`a`)
explain select straight_join * from t1 as x1
left join t1 as x2 on x2.b = x1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 2 pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	2	100.00	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x1`.`pk` AS `pk`,`test`.`x1`.`u` AS `u`,`test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x2`.`pk` AS `pk`,`test`.`x2`.`u` AS `u`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b` from `test`.`t1` `x1` left join `test`.`t1` `x2` on((`test`.`x2`.`b` = `test`.`x1`.`a`)) where true
explain select straight_join * from t1 as x1
right join t1 as x2 on x2.b = x1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	NULL
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	Can't push table 'x1' as child, 'type' must be a 'ref' access
Note	1003	/* select#1 */ select straight_join `test`.`x1`.`pk` AS `pk`,`test`.`x1`.`u` AS `u`,`test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x2`.`pk` AS `pk`,`test`.`x2`.`u` AS `u`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b` from `test`.`t1` `x2` left join `test`.`t1` `x1` on((`test`.`x1`.`a` = `test`.`x2`.`b`)) where true
explain select straight_join * from
t1 as x1 inner join
(t1 as x2 inner join t1 as x3 on x3.b = x2.a)
on x2.pk = x1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`x1`.`a` is not null)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x1.a	1	100.00	Child of 'x1' in pushed join@1; Using pushed condition (`test`.`x2`.`a` is not null)
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x2.a	2	100.00	Child of 'x2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x1`.`pk` AS `pk`,`test`.`x1`.`u` AS `u`,`test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x2`.`pk` AS `pk`,`test`.`x2`.`u` AS `u`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b`,`test`.`x3`.`pk` AS `pk`,`test`.`x3`.`u` AS `u`,`test`.`x3`.`a` AS `a`,`test`.`x3`.`b` AS `b` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t1` `x3` where ((`test`.`x3`.`b` = `test`.`x2`.`a`) and (`test`.`x2`.`pk` = `test`.`x1`.`a`))
explain select straight_join * from
t1 as x1 left join
(t1 as x2 inner join t1 as x3 on x3.b = x2.a)
on x2.pk = x1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 3 pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x1.a	1	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x2.a	2	100.00	Child of 'x2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x1`.`pk` AS `pk`,`test`.`x1`.`u` AS `u`,`test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x2`.`pk` AS `pk`,`test`.`x2`.`u` AS `u`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b`,`test`.`x3`.`pk` AS `pk`,`test`.`x3`.`u` AS `u`,`test`.`x3`.`a` AS `a`,`test`.`x3`.`b` AS `b` from `test`.`t1` `x1` left join (`test`.`t1` `x2` join `test`.`t1` `x3`) on(((`test`.`x3`.`b` = `test`.`x2`.`a`) and (`test`.`x2`.`pk` = `test`.`x1`.`a`))) where true
explain select straight_join count(*) from t1 as x1
join t1 as x2 on x2.b = x1.a
join t1 as x3 on x3.b = x1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	ix1	NULL	NULL	NULL	13	100.00	Parent of 3 pushed join@1; Using pushed condition ((`test`.`x1`.`a` is not null) and (`test`.`x1`.`b` is not null))
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	2	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.b	2	100.00	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t1` `x3` where ((`test`.`x2`.`b` = `test`.`x1`.`a`) and (`test`.`x3`.`b` = `test`.`x1`.`b`))
set ndb_join_pushdown=off;
select straight_join count(*) from t1 as x1 
join t1 as x2 on x2.b = x1.a
join t1 as x3 on x3.b = x1.b;
count(*)
2028
set ndb_join_pushdown=on;
select straight_join count(*) from t1 as x1 
join t1 as x2 on x2.b = x1.a
join t1 as x3 on x3.b = x1.b;
count(*)
2028
explain select straight_join count(*) from t1 as x1
join t1 as x2 on x2.b = x1.a
join t1 as x3 on x3.b = x1.a
join t1 as x4 on x4.b = x1.a
join t1 as x5 on x5.b = x1.a
join t1 as x6 on x6.b = x1.a
join t1 as x7 on x7.b = x1.a
where x3.a < x2.pk and x4.a < x3.pk;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 7 pushed join@1; Using pushed condition (`test`.`x1`.`a` is not null)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY,ix1	ix1	5	test.x1.a	#	#	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY,ix1	ix1	5	test.x1.a	#	#	Child of 'x2' in pushed join@1; Using pushed condition (`test`.`x3`.`a` < `test`.`x2`.`pk`)
1	SIMPLE	x4	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	#	#	Child of 'x3' in pushed join@1; Using pushed condition (`test`.`x4`.`a` < `test`.`x3`.`pk`)
1	SIMPLE	x5	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	#	#	Child of 'x1' in pushed join@1
1	SIMPLE	x6	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	#	#	Child of 'x1' in pushed join@1
1	SIMPLE	x7	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	#	#	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t1` `x3` join `test`.`t1` `x4` join `test`.`t1` `x5` join `test`.`t1` `x6` join `test`.`t1` `x7` where ((`test`.`x2`.`b` = `test`.`x1`.`a`) and (`test`.`x3`.`b` = `test`.`x1`.`a`) and (`test`.`x4`.`b` = `test`.`x1`.`a`) and (`test`.`x5`.`b` = `test`.`x1`.`a`) and (`test`.`x6`.`b` = `test`.`x1`.`a`) and (`test`.`x7`.`b` = `test`.`x1`.`a`) and (`test`.`x3`.`a` < `test`.`x2`.`pk`) and (`test`.`x4`.`a` < `test`.`x3`.`pk`))
set global debug='+d,max_64rows_in_spj_batches';
select straight_join count(*) from t1 as x1 
join t1 as x2 on x2.b = x1.a
join t1 as x3 on x3.b = x1.a
join t1 as x4 on x4.b = x1.a
join t1 as x5 on x5.b = x1.a
join t1 as x6 on x6.b = x1.a
join t1 as x7 on x7.b = x1.a
where x3.a < x2.pk and x4.a < x3.pk;
count(*)
632736
set global debug=@save_debug;
explain select straight_join count(*) from t1 as x1
left join t1 as x2 on x2.b = x1.a
join t1 as x3 on x3.b = x1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	ix1	NULL	NULL	NULL	13	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`x1`.`b` is not null)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	2	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.b	2	100.00	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x1` left join `test`.`t1` `x2` on((`test`.`x2`.`b` = `test`.`x1`.`a`)) join `test`.`t1` `x3` where (`test`.`x3`.`b` = `test`.`x1`.`b`)
set ndb_join_pushdown=off;
select straight_join count(*) from t1 as x1 
left join t1 as x2 on x2.b = x1.a
join t1 as x3 on x3.b = x1.b;
count(*)
2041
set ndb_join_pushdown=on;
select straight_join count(*) from t1 as x1 
left join t1 as x2 on x2.b = x1.a
join t1 as x3 on x3.b = x1.b;
count(*)
2041
explain select straight_join count(*) from t1 as x1
join t1 as x2 on x2.b = x1.a
left join t1 as x3 on x3.b = x1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`x1`.`a` is not null)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	2	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.b	2	100.00	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x1` join `test`.`t1` `x2` left join `test`.`t1` `x3` on((`test`.`x3`.`b` = `test`.`x1`.`b`)) where (`test`.`x2`.`b` = `test`.`x1`.`a`)
set ndb_join_pushdown=off;
select straight_join count(*) from t1 as x1 
join t1 as x2 on x2.b = x1.a
left join t1 as x3 on x3.b = x1.b;
count(*)
2028
set ndb_join_pushdown=on;
select straight_join count(*) from t1 as x1 
join t1 as x2 on x2.b = x1.a
left join t1 as x3 on x3.b = x1.b;
count(*)
2028
explain
select straight_join count(*) from t1 as x1
join t1 as x2 on x2.b = x1.a
join t1 as x3 on x3.pk = x1.a join t1 as x4 on x4.b = x3.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 4 pushed join@1; Using pushed condition (`test`.`x1`.`a` is not null)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	2	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x1.a	1	100.00	Child of 'x1' in pushed join@1; Using pushed condition (`test`.`x3`.`a` is not null)
1	SIMPLE	x4	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x3.a	2	100.00	Child of 'x3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t1` `x3` join `test`.`t1` `x4` where ((`test`.`x2`.`b` = `test`.`x1`.`a`) and (`test`.`x3`.`pk` = `test`.`x1`.`a`) and (`test`.`x4`.`b` = `test`.`x3`.`a`))
set ndb_join_pushdown=off;
select straight_join count(*) from t1 as x1
join t1 as x2 on x2.b = x1.a
join t1 as x3 on x3.pk = x1.a join t1 as x4 on x4.b = x3.a;
count(*)
2028
set ndb_join_pushdown=on;
select straight_join count(*) from t1 as x1
join t1 as x2 on x2.b = x1.a
join t1 as x3 on x3.pk = x1.a join t1 as x4 on x4.b = x3.a;
count(*)
2028
explain select straight_join count(*) from t1 as x1
left join t1 as x3 on x3.b = x1.a
join t1 as x2 on x2.pk = x1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`x1`.`a` is not null)
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	2	100.00	Child of 'x1' in pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x1.a	1	100.00	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x1` left join `test`.`t1` `x3` on((`test`.`x3`.`b` = `test`.`x1`.`a`)) join `test`.`t1` `x2` where (`test`.`x2`.`pk` = `test`.`x1`.`a`)
select straight_join count(*) from t1 as x1 
left join t1 as x3 on x3.b = x1.a
join t1 as x2 on x2.pk = x1.a;
count(*)
156
update t1 set b=b+10;
select straight_join count(*) from t1 as x1 
left join t1 as x3 on x3.b = x1.a
join t1 as x2 on x2.pk = x1.a;
count(*)
12
update t1 set b=b-10;
update t1 set u=u+100;
set ndb_join_pushdown=on;
set global debug='+d,max_64rows_in_spj_batches';
explain select straight_join count(*) from
(t1 as x join t1 as y on y.b = x.a)
left outer join t1 as z on z.u = x.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`x`.`a` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x.a	2	100.00	Child of 'x' in pushed join@1
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	ix2	ix2	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x` join `test`.`t1` `y` left join `test`.`t1` `z` on((`test`.`z`.`u` = `test`.`x`.`a`)) where (`test`.`y`.`b` = `test`.`x`.`a`)
select straight_join count(*) from 
(t1 as x join t1 as y on y.b = x.a)
left outer join t1 as z on z.u = x.a;
count(*)
156
set global debug=@save_debug;
update t1 set u=u-100;
update t1 set a=a+pk;
set ndb_join_pushdown=on;
explain
select straight_join count(*) from t1 as x
left join t1 as y on y.pk = x.a
join t1 as z on z.b = x.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	ix1	NULL	NULL	NULL	13	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`x`.`b` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x.b	2	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x` left join `test`.`t1` `y` on((`test`.`y`.`pk` = `test`.`x`.`a`)) join `test`.`t1` `z` where (`test`.`z`.`b` = `test`.`x`.`b`)
select straight_join count(*) from t1 as x
left join t1 as y on y.pk = x.a
join t1 as z on z.b = x.b;
count(*)
169
explain
select straight_join count(*) from t1 as x
left join t1 as y1 on y1.pk = x.a
left join t1 as y2 on y2.pk = x.a
join t1 as z on z.b = x.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	ix1	NULL	NULL	NULL	13	100.00	Parent of 4 pushed join@1; Using pushed condition (`test`.`x`.`b` is not null)
1	SIMPLE	y1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	y2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x.b	2	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x` left join `test`.`t1` `y1` on((`test`.`y1`.`pk` = `test`.`x`.`a`)) left join `test`.`t1` `y2` on((`test`.`y2`.`pk` = `test`.`x`.`a`)) join `test`.`t1` `z` where (`test`.`z`.`b` = `test`.`x`.`b`)
select straight_join count(*) from t1 as x
left join t1 as y1 on y1.pk = x.a
left join t1 as y2 on y2.pk = x.a
join t1 as z on z.b = x.b;
count(*)
169
explain
select straight_join count(*) from t1 as x
join t1 as y1 on y1.pk = x.a
left join t1 as y2 on y2.pk = x.a
join t1 as z on z.b = x.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	ix1	NULL	NULL	NULL	13	100.00	Parent of 4 pushed join@1; Using pushed condition ((`test`.`x`.`a` is not null) and (`test`.`x`.`b` is not null))
1	SIMPLE	y1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	y2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x.b	2	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x` join `test`.`t1` `y1` left join `test`.`t1` `y2` on((`test`.`y2`.`pk` = `test`.`x`.`a`)) join `test`.`t1` `z` where ((`test`.`y1`.`pk` = `test`.`x`.`a`) and (`test`.`z`.`b` = `test`.`x`.`b`))
select straight_join count(*) from t1 as x
join t1 as y1 on y1.pk = x.a
left join t1 as y2 on y2.pk = x.a
join t1 as z on z.b = x.b;
count(*)
39
explain
select straight_join count(*) from t1 as x
left join t1 as y1 on y1.pk = x.a
join t1 as y2 on y2.pk = x.a
join t1 as z on z.b = x.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	ix1	NULL	NULL	NULL	13	100.00	Parent of 4 pushed join@1; Using pushed condition ((`test`.`x`.`a` is not null) and (`test`.`x`.`b` is not null))
1	SIMPLE	y1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	y2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x.b	2	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x` left join `test`.`t1` `y1` on((`test`.`y1`.`pk` = `test`.`x`.`a`)) join `test`.`t1` `y2` join `test`.`t1` `z` where ((`test`.`y2`.`pk` = `test`.`x`.`a`) and (`test`.`z`.`b` = `test`.`x`.`b`))
select straight_join count(*) from t1 as x
left join t1 as y1 on y1.pk = x.a
join t1 as y2 on y2.pk = x.a
join t1 as z on z.b = x.b;
count(*)
39
explain
select straight_join count(*) from t1 as x
left join (t1 as y join t1 as yy on yy.pk=y.u) on y.pk = x.a
join t1 as z on z.b = x.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	ix1	NULL	NULL	NULL	13	100.00	Parent of 4 pushed join@1; Using pushed condition (`test`.`x`.`b` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,ix2	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	yy	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.y.u	1	100.00	Child of 'y' in pushed join@1
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x.b	2	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x` left join (`test`.`t1` `y` join `test`.`t1` `yy`) on(((`test`.`yy`.`pk` = `test`.`y`.`u`) and (`test`.`y`.`pk` = `test`.`x`.`a`))) join `test`.`t1` `z` where (`test`.`z`.`b` = `test`.`x`.`b`)
select straight_join count(*) from t1 as x
left join (t1 as y join t1 as yy on yy.pk=y.u) on y.pk = x.a
join t1 as z on z.b = x.b;
count(*)
169
explain
select straight_join count(*) from t1 as x
join t1 as z on z.b = x.b
left join t1 as y on y.pk = x.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	ix1	NULL	NULL	NULL	13	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`x`.`b` is not null)
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x.b	2	100.00	Child of 'x' in pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x` join `test`.`t1` `z` left join `test`.`t1` `y` on((`test`.`y`.`pk` = `test`.`x`.`a`)) where (`test`.`z`.`b` = `test`.`x`.`b`)
select straight_join count(*) from t1 as x
join t1 as z on z.b = x.b
left join t1 as y on y.pk = x.a;
count(*)
169
explain
select straight_join count(*) from t1 as x
left join t1 as y1 on y1.pk = x.a
left join t1 as y2 on y2.pk = x.a
left join t1 as y3 on y3.pk = x.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 4 pushed join@1
1	SIMPLE	y1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	y2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	y3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x` left join `test`.`t1` `y1` on((`test`.`y1`.`pk` = `test`.`x`.`a`)) left join `test`.`t1` `y2` on((`test`.`y2`.`pk` = `test`.`x`.`a`)) left join `test`.`t1` `y3` on((`test`.`y3`.`pk` = `test`.`x`.`a`)) where true
select straight_join count(*) from t1 as x
left join t1 as y1 on y1.pk = x.a
left join t1 as y2 on y2.pk = x.a
left join t1 as y3 on y3.pk = x.a;
count(*)
13
explain
select straight_join count(*) from t1 as x
left join t1 as y on y.pk = x.a
left join t1 as z on z.pk = x.a
left join t1 as y1 on y1.pk = y.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 4 pushed join@1
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	y1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.y.b	1	100.00	Child of 'y' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x` left join `test`.`t1` `y` on((`test`.`y`.`pk` = `test`.`x`.`a`)) left join `test`.`t1` `z` on((`test`.`z`.`pk` = `test`.`x`.`a`)) left join `test`.`t1` `y1` on((`test`.`y1`.`pk` = `test`.`y`.`b`)) where true
select straight_join count(*) from t1 as x
left join t1 as y on y.pk = x.a
left join t1 as z on z.pk = x.a
left join t1 as y1 on y1.pk = y.b;
count(*)
13
explain
select straight_join count(*) from t1 as x
join t1 as y on y.pk = x.a
left join t1 as y1 on y1.pk = y.b
left join t1 as z on z.pk = x.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	Parent of 4 pushed join@1; Using pushed condition (`test`.`x`.`a` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	y1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.y.b	1	100.00	Child of 'y' in pushed join@1
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.b	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x` join `test`.`t1` `y` left join `test`.`t1` `y1` on((`test`.`y1`.`pk` = `test`.`y`.`b`)) left join `test`.`t1` `z` on((`test`.`z`.`pk` = `test`.`x`.`b`)) where (`test`.`y`.`pk` = `test`.`x`.`a`)
select straight_join count(*) from t1 as x
join t1 as y on y.pk = x.a
left join t1 as y1 on y1.pk = y.b
left join t1 as z on z.pk = x.b;
count(*)
3
explain
select straight_join count(*) from t1 as x
left join t1 as y1 on y1.pk = x.a
join t1 as y2 on y2.pk = x.b
join t1 as z on z.b = y2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	ix1	NULL	NULL	NULL	13	100.00	Parent of 4 pushed join@1; Using pushed condition (`test`.`x`.`b` is not null)
1	SIMPLE	y1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.a	1	100.00	Child of 'x' in pushed join@1
1	SIMPLE	y2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,ix1	PRIMARY	4	test.x.b	1	100.00	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y2`.`b` is not null)
1	SIMPLE	z	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.y2.b	2	100.00	Child of 'y2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x` left join `test`.`t1` `y1` on((`test`.`y1`.`pk` = `test`.`x`.`a`)) join `test`.`t1` `y2` join `test`.`t1` `z` where ((`test`.`y2`.`pk` = `test`.`x`.`b`) and (`test`.`z`.`b` = `test`.`y2`.`b`))
select straight_join count(*) from t1 as x
left join t1 as y1 on y1.pk = x.a
join t1 as y2 on y2.pk = x.b
join t1 as z on z.b = y2.b;
count(*)
169
update t1 set a=a-pk;
drop index ix2 on t1;
create unique index ix2 on t1(a,u);
set ndb_join_pushdown=on;
explain
select straight_join * from
t1 as table1 join 
(t1 as table2 join t1 as table3 on table3.a = table2.a)
on table3.u = table1.u
where table2.pk = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	table1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	13	100.00	NULL
1	SIMPLE	table2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,ix2	PRIMARY	4	const	1	100.00	Using where
1	SIMPLE	table3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	ix2	ix2	9	test.table2.a,test.table1.u	1	100.00	NULL
Warnings:
Note	1003	Can't push table 'table2' as child of 'table1', no parent-child dependency exists between these tables
Note	1003	Can't push table 'table3' as child of 'table1', column 'table2.a' refers a table which was not pushed
Note	1003	Can't push table 'table3' as child of 'table2', since it referes to column 'table1.u' prior to a potential 'const' root.
Note	1003	/* select#1 */ select straight_join `test`.`table1`.`pk` AS `pk`,`test`.`table1`.`u` AS `u`,`test`.`table1`.`a` AS `a`,`test`.`table1`.`b` AS `b`,`test`.`table2`.`pk` AS `pk`,`test`.`table2`.`u` AS `u`,`test`.`table2`.`a` AS `a`,`test`.`table2`.`b` AS `b`,`test`.`table3`.`pk` AS `pk`,`test`.`table3`.`u` AS `u`,`test`.`table3`.`a` AS `a`,`test`.`table3`.`b` AS `b` from `test`.`t1` `table1` join `test`.`t1` `table2` join `test`.`t1` `table3` where ((`test`.`table3`.`a` = `test`.`table2`.`a`) and (`test`.`table3`.`u` = `test`.`table1`.`u`) and (`test`.`table2`.`pk` = 3))
select straight_join * from
t1 as table1 join 
(t1 as table2 join t1 as table3 on table3.a = table2.a)
on table3.u = table1.u
where table2.pk = 3;
pk	u	a	b	pk	u	a	b	pk	u	a	b
0	0	10	10	3	3	10	10	0	0	10	10
1	1	10	10	3	3	10	10	1	1	10	10
10	10	10	10	3	3	10	10	10	10	10	10
11	11	10	10	3	3	10	10	11	11	10	10
2	2	10	10	3	3	10	10	2	2	10	10
3	3	10	10	3	3	10	10	3	3	10	10
4	4	10	10	3	3	10	10	4	4	10	10
5	5	10	10	3	3	10	10	5	5	10	10
6	6	10	10	3	3	10	10	6	6	10	10
7	7	10	10	3	3	10	10	7	7	10	10
8	8	10	10	3	3	10	10	8	8	10	10
9	9	10	10	3	3	10	10	9	9	10	10
drop table t1;
CREATE TABLE t1 (
a int NOT NULL,
b int NOT NULL,
c int NOT NULL,
d int NOT NULL,
PRIMARY KEY (`a`,`b`)
) ENGINE=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values (1,1,1,1), (1,2,1,1), (1,3,1,1), (1,4,1,2);
CREATE TABLE t2 (
a int NOT NULL,
PRIMARY KEY (`a`)
) ENGINE=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
CREATE TABLE t3 (
a int NOT NULL,
b int NOT NULL,
PRIMARY KEY (`a`,`b`)
) ENGINE=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t2 values (0), (1), (2), (3), (4), (5), (6), (7), (8), (9);
insert into t3 select 1, x1.a * 10+x2.a from t2 as x1 cross join t2 as x2;
set global debug='+d,max_64rows_in_spj_batches';
explain select straight_join count(*) from t1 as x0  
join t3 as x1 on x1.a=x0.c
join t1 as x2 on x2.a=x0.d
join t3 as x3 on x3.a=x2.c
join t1 as x4 on x4.a=x0.d and x4.b=x3.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x0	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	Parent of 5 pushed join@1
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x0.c	1	100.00	Child of 'x0' in pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x0.d	1	100.00	Child of 'x0' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x2.c	1	100.00	Child of 'x2' in pushed join@1
1	SIMPLE	x4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.x0.d,test.x3.b	1	100.00	Child of 'x3' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x0` join `test`.`t3` `x1` join `test`.`t1` `x2` join `test`.`t3` `x3` join `test`.`t1` `x4` where ((`test`.`x1`.`a` = `test`.`x0`.`c`) and (`test`.`x3`.`a` = `test`.`x2`.`c`) and (`test`.`x4`.`b` = `test`.`x3`.`b`) and (`test`.`x2`.`a` = `test`.`x0`.`d`) and (`test`.`x4`.`a` = `test`.`x0`.`d`))
select straight_join count(*) from t1 as x0  
join t3 as x1 on x1.a=x0.c
join t1 as x2 on x2.a=x0.d
join t3 as x3 on x3.a=x2.c
join t1 as x4 on x4.a=x0.d and x4.b=x3.b;
count(*)
4800
explain select straight_join count(*) from t1 as x1  
join t1 as x2 on x1.c=x2.a and x2.d=2
join t3 as x3 on x1.d=x3.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 3 pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x1.c	#	#	Child of 'x1' in pushed join@1; Using pushed condition (`test`.`x2`.`d` = 2)
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x1.d	#	#	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t3` `x3` where ((`test`.`x2`.`d` = 2) and (`test`.`x2`.`a` = `test`.`x1`.`c`) and (`test`.`x3`.`a` = `test`.`x1`.`d`))
select straight_join count(*) from t1 as x1  
join t1 as x2 on x1.c=x2.a and x2.d=2
join t3 as x3 on x1.d=x3.a;
count(*)
300
set global debug=@save_debug;
drop table t1;
drop table t2;
drop table t3;
create table t1(
d int not null,
e int     null,
f int     null,
a int not null,
b int not null,
c int not null,
primary key (a,b,c))
engine = ndb partition by key (b) partitions 8;
insert into t1(a,b,c,d,e,f) values
(1, 2, 3, 1, 2, 3),
(1, 2, 4, 1, 2, 3),
(2, 3, 4, 1, 2, 3),
(3, 4, 5, 1, 2, 3),
(4, 5, 6, 1, 2, 3),
(5, 6, 7, 1, 2, 3),
(6, 7, 8, 1, 2, 3),
(7, 8, 9, 1, 2, 3);
set ndb_join_pushdown=on;
explain
select straight_join * from t1 x, t1 y where y.a=x.d and y.b=x.e;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`e` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	8	test.x.d,test.x.e	#	#	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`d` AS `d`,`test`.`x`.`e` AS `e`,`test`.`x`.`f` AS `f`,`test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`x`.`c` AS `c`,`test`.`y`.`d` AS `d`,`test`.`y`.`e` AS `e`,`test`.`y`.`f` AS `f`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b`,`test`.`y`.`c` AS `c` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`b` = `test`.`x`.`e`) and (`test`.`y`.`a` = `test`.`x`.`d`))
select straight_join * from t1 x, t1 y where y.a=x.d and y.b=x.e;
d	e	f	a	b	c	d	e	f	a	b	c
1	2	3	1	2	3	1	2	3	1	2	3
1	2	3	1	2	3	1	2	3	1	2	4
1	2	3	1	2	4	1	2	3	1	2	3
1	2	3	1	2	4	1	2	3	1	2	4
1	2	3	2	3	4	1	2	3	1	2	3
1	2	3	2	3	4	1	2	3	1	2	4
1	2	3	3	4	5	1	2	3	1	2	3
1	2	3	3	4	5	1	2	3	1	2	4
1	2	3	4	5	6	1	2	3	1	2	3
1	2	3	4	5	6	1	2	3	1	2	4
1	2	3	5	6	7	1	2	3	1	2	3
1	2	3	5	6	7	1	2	3	1	2	4
1	2	3	6	7	8	1	2	3	1	2	3
1	2	3	6	7	8	1	2	3	1	2	4
1	2	3	7	8	9	1	2	3	1	2	3
1	2	3	7	8	9	1	2	3	1	2	4
alter table t1 partition by key (a) partitions 8;
select straight_join * from t1 x, t1 y where y.a=x.d and y.b=x.e;
d	e	f	a	b	c	d	e	f	a	b	c
1	2	3	1	2	3	1	2	3	1	2	3
1	2	3	1	2	3	1	2	3	1	2	4
1	2	3	1	2	4	1	2	3	1	2	3
1	2	3	1	2	4	1	2	3	1	2	4
1	2	3	2	3	4	1	2	3	1	2	3
1	2	3	2	3	4	1	2	3	1	2	4
1	2	3	3	4	5	1	2	3	1	2	3
1	2	3	3	4	5	1	2	3	1	2	4
1	2	3	4	5	6	1	2	3	1	2	3
1	2	3	4	5	6	1	2	3	1	2	4
1	2	3	5	6	7	1	2	3	1	2	3
1	2	3	5	6	7	1	2	3	1	2	4
1	2	3	6	7	8	1	2	3	1	2	3
1	2	3	6	7	8	1	2	3	1	2	4
1	2	3	7	8	9	1	2	3	1	2	3
1	2	3	7	8	9	1	2	3	1	2	4
alter table t1 partition by key (a,b) partitions 8;
select straight_join * from t1 x, t1 y where y.a=x.d and y.b=x.e;
d	e	f	a	b	c	d	e	f	a	b	c
1	2	3	1	2	3	1	2	3	1	2	3
1	2	3	1	2	3	1	2	3	1	2	4
1	2	3	1	2	4	1	2	3	1	2	3
1	2	3	1	2	4	1	2	3	1	2	4
1	2	3	2	3	4	1	2	3	1	2	3
1	2	3	2	3	4	1	2	3	1	2	4
1	2	3	3	4	5	1	2	3	1	2	3
1	2	3	3	4	5	1	2	3	1	2	4
1	2	3	4	5	6	1	2	3	1	2	3
1	2	3	4	5	6	1	2	3	1	2	4
1	2	3	5	6	7	1	2	3	1	2	3
1	2	3	5	6	7	1	2	3	1	2	4
1	2	3	6	7	8	1	2	3	1	2	3
1	2	3	6	7	8	1	2	3	1	2	4
1	2	3	7	8	9	1	2	3	1	2	3
1	2	3	7	8	9	1	2	3	1	2	4
alter table t1 partition by key (b,a) partitions 8;
select straight_join * from t1 x, t1 y where y.a=x.d and y.b=x.e;
d	e	f	a	b	c	d	e	f	a	b	c
1	2	3	1	2	3	1	2	3	1	2	3
1	2	3	1	2	3	1	2	3	1	2	4
1	2	3	1	2	4	1	2	3	1	2	3
1	2	3	1	2	4	1	2	3	1	2	4
1	2	3	2	3	4	1	2	3	1	2	3
1	2	3	2	3	4	1	2	3	1	2	4
1	2	3	3	4	5	1	2	3	1	2	3
1	2	3	3	4	5	1	2	3	1	2	4
1	2	3	4	5	6	1	2	3	1	2	3
1	2	3	4	5	6	1	2	3	1	2	4
1	2	3	5	6	7	1	2	3	1	2	3
1	2	3	5	6	7	1	2	3	1	2	4
1	2	3	6	7	8	1	2	3	1	2	3
1	2	3	6	7	8	1	2	3	1	2	4
1	2	3	7	8	9	1	2	3	1	2	3
1	2	3	7	8	9	1	2	3	1	2	4
alter table t1 partition by key (b) partitions 8;
select straight_join * from t1 x, t1 y where y.a=x.d and y.b=2;
d	e	f	a	b	c	d	e	f	a	b	c
1	2	3	1	2	3	1	2	3	1	2	3
1	2	3	1	2	3	1	2	3	1	2	4
1	2	3	1	2	4	1	2	3	1	2	3
1	2	3	1	2	4	1	2	3	1	2	4
1	2	3	2	3	4	1	2	3	1	2	3
1	2	3	2	3	4	1	2	3	1	2	4
1	2	3	3	4	5	1	2	3	1	2	3
1	2	3	3	4	5	1	2	3	1	2	4
1	2	3	4	5	6	1	2	3	1	2	3
1	2	3	4	5	6	1	2	3	1	2	4
1	2	3	5	6	7	1	2	3	1	2	3
1	2	3	5	6	7	1	2	3	1	2	4
1	2	3	6	7	8	1	2	3	1	2	3
1	2	3	6	7	8	1	2	3	1	2	4
1	2	3	7	8	9	1	2	3	1	2	3
1	2	3	7	8	9	1	2	3	1	2	4
alter table t1 partition by key (a) partitions 8;
select straight_join * from t1 x, t1 y where y.a=1 and y.b=x.e;
d	e	f	a	b	c	d	e	f	a	b	c
1	2	3	1	2	3	1	2	3	1	2	3
1	2	3	1	2	3	1	2	3	1	2	4
1	2	3	1	2	4	1	2	3	1	2	3
1	2	3	1	2	4	1	2	3	1	2	4
1	2	3	2	3	4	1	2	3	1	2	3
1	2	3	2	3	4	1	2	3	1	2	4
1	2	3	3	4	5	1	2	3	1	2	3
1	2	3	3	4	5	1	2	3	1	2	4
1	2	3	4	5	6	1	2	3	1	2	3
1	2	3	4	5	6	1	2	3	1	2	4
1	2	3	5	6	7	1	2	3	1	2	3
1	2	3	5	6	7	1	2	3	1	2	4
1	2	3	6	7	8	1	2	3	1	2	3
1	2	3	6	7	8	1	2	3	1	2	4
1	2	3	7	8	9	1	2	3	1	2	3
1	2	3	7	8	9	1	2	3	1	2	4
select straight_join * from t1 x, t1 y where y.a=0 and y.b=x.e;
d	e	f	a	b	c	d	e	f	a	b	c
alter table t1 partition by key (a,b) partitions 8;
select straight_join * from t1 x, t1 y where y.a=1 and y.b=x.e;
d	e	f	a	b	c	d	e	f	a	b	c
1	2	3	1	2	3	1	2	3	1	2	3
1	2	3	1	2	3	1	2	3	1	2	4
1	2	3	1	2	4	1	2	3	1	2	3
1	2	3	1	2	4	1	2	3	1	2	4
1	2	3	2	3	4	1	2	3	1	2	3
1	2	3	2	3	4	1	2	3	1	2	4
1	2	3	3	4	5	1	2	3	1	2	3
1	2	3	3	4	5	1	2	3	1	2	4
1	2	3	4	5	6	1	2	3	1	2	3
1	2	3	4	5	6	1	2	3	1	2	4
1	2	3	5	6	7	1	2	3	1	2	3
1	2	3	5	6	7	1	2	3	1	2	4
1	2	3	6	7	8	1	2	3	1	2	3
1	2	3	6	7	8	1	2	3	1	2	4
1	2	3	7	8	9	1	2	3	1	2	3
1	2	3	7	8	9	1	2	3	1	2	4
select straight_join * from t1 x, t1 y where y.a=x.d and y.b=2;
d	e	f	a	b	c	d	e	f	a	b	c
1	2	3	1	2	3	1	2	3	1	2	3
1	2	3	1	2	3	1	2	3	1	2	4
1	2	3	1	2	4	1	2	3	1	2	3
1	2	3	1	2	4	1	2	3	1	2	4
1	2	3	2	3	4	1	2	3	1	2	3
1	2	3	2	3	4	1	2	3	1	2	4
1	2	3	3	4	5	1	2	3	1	2	3
1	2	3	3	4	5	1	2	3	1	2	4
1	2	3	4	5	6	1	2	3	1	2	3
1	2	3	4	5	6	1	2	3	1	2	4
1	2	3	5	6	7	1	2	3	1	2	3
1	2	3	5	6	7	1	2	3	1	2	4
1	2	3	6	7	8	1	2	3	1	2	3
1	2	3	6	7	8	1	2	3	1	2	4
1	2	3	7	8	9	1	2	3	1	2	3
1	2	3	7	8	9	1	2	3	1	2	4
alter table t1 drop primary key, add primary key using hash (d,b,a,c);
alter table t1 partition by key (b) partitions 8;
create index ix1 on t1(b,d,a);
explain
select straight_join * from t1 x, t1 y where y.a=x.d and y.b=x.e;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`e` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	4	test.x.e	#	#	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`a` = `test`.`x`.`d`)
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`x`.`d` AS `d`,`test`.`x`.`e` AS `e`,`test`.`x`.`f` AS `f`,`test`.`x`.`a` AS `a`,`test`.`x`.`b` AS `b`,`test`.`x`.`c` AS `c`,`test`.`y`.`d` AS `d`,`test`.`y`.`e` AS `e`,`test`.`y`.`f` AS `f`,`test`.`y`.`a` AS `a`,`test`.`y`.`b` AS `b`,`test`.`y`.`c` AS `c` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`b` = `test`.`x`.`e`) and (`test`.`y`.`a` = `test`.`x`.`d`))
insert into t1(a,b,c,d,e,f) values
(8, 9, 0, 1,  null, 3),
(9, 9, 0, 1,  2,    null);
alter table t1 partition by key (b) partitions 8;
select straight_join * from t1 x, t1 y where y.a=x.d and y.b=x.e;
d	e	f	a	b	c	d	e	f	a	b	c
1	2	3	1	2	3	1	2	3	1	2	3
1	2	3	1	2	3	1	2	3	1	2	4
1	2	3	1	2	4	1	2	3	1	2	3
1	2	3	1	2	4	1	2	3	1	2	4
1	2	3	2	3	4	1	2	3	1	2	3
1	2	3	2	3	4	1	2	3	1	2	4
1	2	3	3	4	5	1	2	3	1	2	3
1	2	3	3	4	5	1	2	3	1	2	4
1	2	3	4	5	6	1	2	3	1	2	3
1	2	3	4	5	6	1	2	3	1	2	4
1	2	3	5	6	7	1	2	3	1	2	3
1	2	3	5	6	7	1	2	3	1	2	4
1	2	3	6	7	8	1	2	3	1	2	3
1	2	3	6	7	8	1	2	3	1	2	4
1	2	3	7	8	9	1	2	3	1	2	3
1	2	3	7	8	9	1	2	3	1	2	4
1	2	NULL	9	9	0	1	2	3	1	2	3
1	2	NULL	9	9	0	1	2	3	1	2	4
pruned
14
const_pruned
6
drop table t1;
create table t1 (pk int primary key, a int, b int) engine=ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
create index ix1 on t1(b,a);
insert into t1 values (0,10,10);
insert into t1 values (1,10,20);
insert into t1 values (2,20,20);
insert into t1 values (3,10,10);
insert into t1 values (4,10,20);
insert into t1 values (5,10,20);
insert into t1 values (6,10,10);
insert into t1 values (7,10,10);
insert into t1 values (8,10,20);
insert into t1 values (9,10,10);
explain select x1.pk,x1.a,x1.b from t1 as x1
join t1 as x2 on x1.a=x2.b 
join t1 as x3 on x2.a=x3.b 
order by x1.pk limit 70;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	4	NULL	10	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`x1`.`a` is not null)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	2	100.00	Child of 'x1' in pushed join@1; Using pushed condition (`test`.`x2`.`a` is not null)
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x2.a	2	100.00	Child of 'x2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x1`.`pk` AS `pk`,`test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t1` `x3` where ((`test`.`x2`.`b` = `test`.`x1`.`a`) and (`test`.`x3`.`b` = `test`.`x2`.`a`)) order by `test`.`x1`.`pk` limit 70
select x1.pk,x1.a,x1.b from t1 as x1 
join t1 as x2 on x1.a=x2.b 
join t1 as x3 on x2.a=x3.b 
order by x1.pk limit 70;
pk	a	b
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
0	10	10
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
1	10	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
2	20	20
explain select * from t1 as x1, t1 as x2 where x1.a=x2.b and x1.b = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	const	3	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x1`.`a` is not null)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	ix1	ix1	5	test.x1.a	2	100.00	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x1`.`pk` AS `pk`,`test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x2`.`pk` AS `pk`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b` from `test`.`t1` `x1` join `test`.`t1` `x2` where ((`test`.`x1`.`b` = 3) and (`test`.`x2`.`b` = `test`.`x1`.`a`))
select * from t1 as x1, t1 as x2 where x1.a=x2.b and x1.b = 3;
pk	a	b	pk	a	b
drop table t1;
create table t (pk int primary key, a int) engine=ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t values 
(1,1), (2,1),
(4,3), (6,3),
(7,4), (8,4);
explain
select distinct straight_join table1.pk FROM 
t as table1  join
(t as table2  join  
(t as table3  join t as table4 on table3.pk = table4.a)
on table2.pk =  table3.pk )
on table1.a =  table4.pk
where  table2.pk != 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	table1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	#	#	Using pushed condition (`test`.`table1`.`a` is not null); Using temporary
1	SIMPLE	table2	p0,p1,p2,p3,p4,p5,p6,p7	range	PRIMARY	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`table2`.`pk` <> 6); Using MRR; Distinct; Using join buffer (hash join)
1	SIMPLE	table3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.table2.pk	#	#	Child of 'table2' in pushed join@1; Distinct
1	SIMPLE	table4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.table1.a	#	#	Using where; Distinct
Warnings:
Note	1003	Can't push table 'table2' as child, access type 'Range-scan' not implemented
Note	1003	Can't push table 'table3' as child of 'table1', it is in a hash-bucket-branch which can't be referred.
Note	1003	Can't push table 'table4' as child of 'table1', it is in a hash-bucket-branch which can't be referred.
Note	1003	Can't push table 'table4' as child of 'table2', column 'table1.a' is in a hash-bucket-branch which can't be referred
Note	1003	/* select#1 */ select straight_join distinct `test`.`table1`.`pk` AS `pk` from `test`.`t` `table1` join `test`.`t` `table2` join `test`.`t` `table3` join `test`.`t` `table4` where ((`test`.`table3`.`pk` = `test`.`table2`.`pk`) and (`test`.`table4`.`a` = `test`.`table2`.`pk`) and (`test`.`table4`.`pk` = `test`.`table1`.`a`) and (`test`.`table2`.`pk` <> 6))
select distinct straight_join table1.pk FROM 
t as table1  join
(t as table2  join  
(t as table3  join t as table4 on table3.pk = table4.a)
on table2.pk =  table3.pk )
on table1.a =  table4.pk
where  table2.pk != 6;
pk
1
2
drop table t;
create table t (b int, a int, primary key (a,b)) engine=ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t values(0,0);
explain
select * from t as t1 join t as t2 on t2.a=t1.a where t1.a < 8 or t1.a >= 8;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`t1`.`a` < 8) or (`test`.`t1`.`a` >= 8))
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.a	#	#	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`b` AS `b`,`test`.`t1`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`a` AS `a` from `test`.`t` `t1` join `test`.`t` `t2` where ((`test`.`t2`.`a` = `test`.`t1`.`a`) and ((`test`.`t1`.`a` < 8) or (`test`.`t1`.`a` >= 8)))
select * from t as t1 join t as t2 on t2.a=t1.a where t1.a < 8 or t1.a >= 8;
b	a	b	a
0	0	0	0
drop table t;
create table t (pk1 int, pk2 int, primary key(pk1,pk2)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t values (1,3), (3,6), (6,9), (9,1);
explain
select * from t as t1 join t as t2
on t1.pk2 = t2.pk1 
where t1.pk1 != 6
order by t1.pk1 DESC;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	range	PRIMARY	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`t1`.`pk1` <> 6); Backward index scan; Using MRR
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.pk2	#	#	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk1` AS `pk1`,`test`.`t1`.`pk2` AS `pk2`,`test`.`t2`.`pk1` AS `pk1`,`test`.`t2`.`pk2` AS `pk2` from `test`.`t` `t1` join `test`.`t` `t2` where ((`test`.`t2`.`pk1` = `test`.`t1`.`pk2`) and (`test`.`t1`.`pk1` <> 6)) order by `test`.`t1`.`pk1` desc
select * from t as t1 join t as t2
on t1.pk2 = t2.pk1 
where t1.pk1 != 6
order by t1.pk1 DESC;
pk1	pk2	pk1	pk2
9	1	1	3
3	6	6	9
1	3	3	6
drop table t;
create table t (k int, uq int, unique key ix1 (uq)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t values (1,3), (3,NULL), (6,9), (9,1);
explain
select straight_join * from t as a join t as b 
on a.uq=b.uq or b.uq is null;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	p0,p1,p2,p3,p4,p5,p6,p7	ALL	ix1	NULL	NULL	NULL	4	100.00	NULL
1	SIMPLE	b	p0,p1,p2,p3,p4,p5,p6,p7	ref_or_null	ix1	ix1	5	test.a.uq	2	100.00	Using pushed condition ((`test`.`b`.`uq` = `test`.`a`.`uq`) or (`test`.`b`.`uq` is null))
Warnings:
Note	1003	Table 'b' is not pushable: Access type was not chosen at 'prepare' time
Note	1003	/* select#1 */ select straight_join `test`.`a`.`k` AS `k`,`test`.`a`.`uq` AS `uq`,`test`.`b`.`k` AS `k`,`test`.`b`.`uq` AS `uq` from `test`.`t` `a` join `test`.`t` `b` where ((`test`.`b`.`uq` = `test`.`a`.`uq`) or (`test`.`b`.`uq` is null))
select straight_join * from t as a join t as b 
on a.uq=b.uq or b.uq is null;
k	uq	k	uq
1	3	1	3
1	3	3	NULL
3	NULL	3	NULL
6	9	3	NULL
6	9	6	9
9	1	3	NULL
9	1	9	1
drop table t;
create table t (k int primary key, uq int) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t values (1,3), (3,NULL), (6,9), (9,1);
#############
# WL#14370: AccessPath will contain a 'zero rows'-AccessPath in place
#           of the known 'false' condition.
explain select * from t as a left join t as b
on a.k is null and a.uq=b.uq;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	NULL
1	SIMPLE	b	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	100.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`k` AS `k`,`test`.`a`.`uq` AS `uq`,`test`.`b`.`k` AS `k`,`test`.`b`.`uq` AS `uq` from `test`.`t` `a` left join `test`.`t` `b` on(((`test`.`b`.`uq` = `test`.`a`.`uq`) and (`test`.`a`.`k` is null))) where true
explain format=tree select * from t as a left join t as b
on a.k is null and a.uq=b.uq;
EXPLAIN
-> Left hash join (no condition)
    -> Table scan on a
    -> Hash
        -> Zero rows (Impossible filter)

select * from t as a left join t as b
on a.k is null and a.uq=b.uq;
k	uq	k	uq
1	3	NULL	NULL
3	NULL	NULL	NULL
6	9	NULL	NULL
9	1	NULL	NULL
drop table t;
create table tc(
a varchar(10) not null,
b varchar(10),
c varchar(10),
primary key (a),
unique key uk1 (b, c)
) engine=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into tc values ('aa','bb', 'x'), ('bb','cc', 'x'), ('cc', 'dd', 'x');
explain select * from tc as x1
right outer join tc as x2 on x1.b=x2.a   
left outer join tc as x3 on x2.b = x3.b and x1.c=x3.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	3	100.00	Parent of 3 pushed join@1
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ref	uk1	uk1	43	test.x2.a	1	100.00	Child of 'x2' in pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	uk1	uk1	86	test.x2.b,test.x1.c	1	100.00	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x1`.`c` AS `c`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b`,`test`.`x2`.`c` AS `c`,`test`.`x3`.`a` AS `a`,`test`.`x3`.`b` AS `b`,`test`.`x3`.`c` AS `c` from `test`.`tc` `x2` left join `test`.`tc` `x1` on((`test`.`x1`.`b` = `test`.`x2`.`a`)) left join `test`.`tc` `x3` on(((`test`.`x3`.`c` = `test`.`x1`.`c`) and (`test`.`x3`.`b` = `test`.`x2`.`b`))) where true
select * from tc as x1 
right outer join tc as x2 on x1.b=x2.a   
left outer join tc as x3 on x2.b = x3.b and x1.c=x3.c;
a	b	c	a	b	c	a	b	c
NULL	NULL	NULL	aa	bb	x	NULL	NULL	NULL
aa	bb	x	bb	cc	x	bb	cc	x
bb	cc	x	cc	dd	x	cc	dd	x
explain select * from tc as x1, tc as x2 where x1.b=x2.a for update;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	uk1	NULL	NULL	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x1`.`b` is not null)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	42	test.x1.b	1	100.00	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x1`.`c` AS `c`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b`,`test`.`x2`.`c` AS `c` from `test`.`tc` `x1` join `test`.`tc` `x2` where (`test`.`x2`.`a` = `test`.`x1`.`b`)
explain select * from tc as x1, tc as x2 where x1.b=x2.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	uk1	NULL	NULL	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x1`.`b` is not null)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	42	test.x1.b	1	100.00	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x1`.`c` AS `c`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b`,`test`.`x2`.`c` AS `c` from `test`.`tc` `x1` join `test`.`tc` `x2` where (`test`.`x2`.`a` = `test`.`x1`.`b`)
drop table tc;
create table t1 (
a varchar(16) not null,
b int not null,
c varchar(16) not null,
d int not null,
primary key (a,b)
) engine ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM"
  partition by key (a);
insert into t1  values ('aaa', 1, 'aaa', 1);
select * from t1 as q1, t1 as q2 where q1.a = 'aaa' and q1.c=q2.a;
a	b	c	d	a	b	c	d
aaa	1	aaa	1	aaa	1	aaa	1
drop table t1;
CREATE TABLE t1 (
id int NOT NULL AUTO_INCREMENT,
t2_id int,
PRIMARY KEY (id)
) ENGINE=ndbcluster DEFAULT CHARSET=latin1
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
CREATE TABLE t2 (
id int NOT NULL AUTO_INCREMENT,
t3_id varchar(20) DEFAULT NULL,
PRIMARY KEY (id)
) ENGINE=ndbcluster DEFAULT CHARSET=latin1
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
CREATE TABLE t3 (
id varchar(20) NOT NULL,
PRIMARY KEY (`id`)
) ENGINE=ndbcluster DEFAULT CHARSET=latin1
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
CREATE TABLE t4 (
pk int NOT NULL,
id varchar(20) NOT NULL,
PRIMARY KEY (pk),
UNIQUE KEY (`id`)
) ENGINE=ndbcluster DEFAULT CHARSET=latin1
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
INSERT INTO t1 VALUES (20, NULL);
INSERT INTO t1 VALUES (23, 24);
INSERT INTO t2 VALUES (24, NULL);
EXPLAIN
SELECT *
FROM t1
INNER JOIN t2 ON t2.id = t1.t2_id
LEFT OUTER JOIN t3 ON t3.id = t2.t3_id
WHERE t1.id = 20;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`t1`.`t2_id` is not null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.t2_id	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	22	test.t2.t3_id	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`t2_id` AS `t2_id`,`test`.`t2`.`id` AS `id`,`test`.`t2`.`t3_id` AS `t3_id`,`test`.`t3`.`id` AS `id` from `test`.`t1` join `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`id` = `test`.`t2`.`t3_id`)) where ((`test`.`t2`.`id` = `test`.`t1`.`t2_id`) and (`test`.`t1`.`id` = 20))
SELECT *
FROM t1
INNER JOIN t2 ON t2.id = t1.t2_id
LEFT OUTER JOIN t3 ON t3.id = t2.t3_id
WHERE t1.id = 20;
id	t2_id	id	t3_id	id
EXPLAIN
SELECT *
FROM t1
INNER JOIN t2 ON t2.id = t1.t2_id
LEFT OUTER JOIN t3 ON t3.id = t2.t3_id
WHERE t1.id = 23;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`t1`.`t2_id` is not null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.t2_id	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	22	test.t2.t3_id	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`t2_id` AS `t2_id`,`test`.`t2`.`id` AS `id`,`test`.`t2`.`t3_id` AS `t3_id`,`test`.`t3`.`id` AS `id` from `test`.`t1` join `test`.`t2` left join `test`.`t3` on((`test`.`t3`.`id` = `test`.`t2`.`t3_id`)) where ((`test`.`t2`.`id` = `test`.`t1`.`t2_id`) and (`test`.`t1`.`id` = 23))
SELECT *
FROM t1
INNER JOIN t2 ON t2.id = t1.t2_id
LEFT OUTER JOIN t3 ON t3.id = t2.t3_id
WHERE t1.id = 23;
id	t2_id	id	t3_id	id
23	24	24	NULL	NULL
EXPLAIN
SELECT *
FROM t1
INNER JOIN t2 ON t2.id = t1.t2_id
LEFT OUTER JOIN t4 ON t4.id = t2.t3_id
WHERE t1.id = 23;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	Parent of 3 pushed join@1; Using pushed condition (`test`.`t1`.`t2_id` is not null)
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.t2_id	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	id	id	22	test.t2.t3_id	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`t2_id` AS `t2_id`,`test`.`t2`.`id` AS `id`,`test`.`t2`.`t3_id` AS `t3_id`,`test`.`t4`.`pk` AS `pk`,`test`.`t4`.`id` AS `id` from `test`.`t1` join `test`.`t2` left join `test`.`t4` on((`test`.`t4`.`id` = `test`.`t2`.`t3_id`)) where ((`test`.`t2`.`id` = `test`.`t1`.`t2_id`) and (`test`.`t1`.`id` = 23))
SELECT *
FROM t1
INNER JOIN t2 ON t2.id = t1.t2_id
LEFT OUTER JOIN t4 ON t4.id = t2.t3_id
WHERE t1.id = 23;
id	t2_id	id	t3_id	pk	id
23	24	24	NULL	NULL	NULL
EXPLAIN
SELECT *
FROM t1
LEFT OUTER JOIN t2 ON t2.id = t1.t2_id
LEFT OUTER JOIN t2 as t3 ON t3.id = t1.t2_id;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	2	100.00	Parent of 3 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.t2_id	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.t2_id	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id`,`test`.`t1`.`t2_id` AS `t2_id`,`test`.`t2`.`id` AS `id`,`test`.`t2`.`t3_id` AS `t3_id`,`test`.`t3`.`id` AS `id`,`test`.`t3`.`t3_id` AS `t3_id` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`id` = `test`.`t1`.`t2_id`)) left join `test`.`t2` `t3` on((`test`.`t3`.`id` = `test`.`t1`.`t2_id`)) where true
SELECT *
FROM t1
LEFT OUTER JOIN t2 ON t2.id = t1.t2_id
LEFT OUTER JOIN t2 as t3 ON t3.id = t1.t2_id;
id	t2_id	id	t3_id	id	t3_id
20	NULL	NULL	NULL	NULL	NULL
23	24	24	NULL	24	NULL
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (
a int NOT NULL,
b int NOT NULL,
c int NOT NULL,
d int,
PRIMARY KEY (`a`,`b`),
unique key(c)
) ENGINE=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values
(1,1,1,1), 
(1,2,2,1), 
(1,3,3,1), 
(1,4,4,1), 
(1,5,5,2), 
(1,6,6,2), 
(1,7,7,2), 
(1,8,8,2);
explain select count(*) from t1 as x1
join (t1 as x2 
left join (t1 as x3 
cross join t1 as x4) 
on x2.d=x3.a) 
on x2.c is null or x1.a=x4.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	8	100.00	NULL
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x2.d	1	100.00	NULL
1	SIMPLE	x4	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	8	100.00	Parent of 2 pushed join@1; Using where
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x4.d	1	100.00	Child of 'x4' in pushed join@1
Warnings:
Note	1003	Can't push table 'x4' as child, 'type' must be a 'ref' access
Note	1003	Can't push outer joined table 'x3' as child of 'x2', some tables in embedding join-nest(s) are not part of pushed join
Note	1003	Can't push table 'x1' as child of 'x2', column 'x4.d' refers a table which was not pushed
Note	1003	Can't push table 'x1' as child of 'x3', column 'x4.d' refers a table which was not pushed
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` `x1` join `test`.`t1` `x2` left join (`test`.`t1` `x3` join `test`.`t1` `x4`) on((`test`.`x3`.`a` = `test`.`x2`.`d`)) where (`test`.`x1`.`a` = `test`.`x4`.`d`)
select count(*) from t1 as x1 
join (t1 as x2 
left join (t1 as x3 
cross join t1 as x4) 
on x2.d=x3.a) 
on x2.c is null or x1.a=x4.d;
count(*)
1024
explain select count(*) from t1 as x1
left join (t1 as x2 
cross join t1 as x3) 
on x1.d=x2.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	8	100.00	NULL
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x1.d	1	100.00	NULL
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	8	100.00	NULL
Warnings:
Note	1003	Can't push table 'x3' as child, 'type' must be a 'ref' access
Note	1003	Can't push outer joined table 'x2' as child of 'x1', some tables in embedding join-nest(s) are not part of pushed join
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` `x1` left join (`test`.`t1` `x2` join `test`.`t1` `x3`) on((`test`.`x2`.`a` = `test`.`x1`.`d`)) where true
select count(*) from t1 as x1 
left join (t1 as x2 
cross join t1 as x3) 
on x1.d=x2.a;
count(*)
260
#################
# Prior to WL#14370 we did 'pending condition' analysis inside the
# join pushdown handler. That code took a too conservative approach
# in identifying such pending conditions, somethimes resulting in pushdown
# to be rejected due to 'table condition can not be fully evaluated...'
# The AccessPath construction code does a much better job of identifying
# these pending condition, and placing them at the correct branch level.
# As this information is now available to us through the AccessPath
# integration, we eliminated our own (incomplete) code for doing such
# analysis and could avoid such false rejections at the same time.
#
# Note the diff in the traditional and tree format explain below:
# The traditional explain says table x4 is 'using where', which was
# incorrectly identified as a trigger condition on join(x2,x4).
# The tree format explain shows that it has been liftet out of that
# join scope as a 'filter' on the entire x1..x4 join - Thus there is
# noting preventing the left-join(x2,x4)
#################
explain select count(*) from t1 as x0
left join (t1 as x1
join (t1 as x2
left join (t1 as x3
join t1 as x4 on x3.d=x4.a)
on x2.d=x3.a)
on x2.c is null or x1.a=x4.d)
on x0.d=x1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x0	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	8	100.00	NULL
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x0.d	1	100.00	NULL
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	c	NULL	NULL	NULL	8	100.00	Parent of 3 pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x2.d	1	100.00	Child of 'x2' in pushed join@1
1	SIMPLE	x4	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.x3.d	1	100.00	Child of 'x3' in pushed join@1; Using where
Warnings:
Note	1003	Can't push outer joined table 'x1' as child of 'x0', join-nest containing the table has FILTER conditions
Note	1003	Can't push table 'x2' as child, 'type' must be a 'ref' access
Note	1003	Can't push table 'x3' as child of 'x0', column 'x2.d' refers a table which was not pushed
Note	1003	Can't push table 'x4' as child of 'x0', column 'x3.d' refers a table which was not pushed
Note	1003	Can't push table 'x3' as child of 'x1', column 'x2.d' refers a table which was not pushed
Note	1003	Can't push table 'x4' as child of 'x1', column 'x3.d' refers a table which was not pushed
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` `x0` left join (`test`.`t1` `x1` join `test`.`t1` `x2` left join (`test`.`t1` `x3` join `test`.`t1` `x4`) on(((`test`.`x4`.`a` = `test`.`x3`.`d`) and (`test`.`x3`.`a` = `test`.`x2`.`d`)))) on(((`test`.`x1`.`a` = `test`.`x0`.`d`) and ((`test`.`x2`.`c` is null) or (`test`.`x4`.`d` = `test`.`x0`.`d`)))) where true
explain format=tree select count(*) from t1 as x0
left join (t1 as x1
join (t1 as x2
left join (t1 as x3
join t1 as x4 on x3.d=x4.a)
on x2.d=x3.a)
on x2.c is null or x1.a=x4.d)
on x0.d=x1.a;
EXPLAIN
-> Aggregate: count(0)
    -> Nested loop left join
        -> Table scan on x0
        -> Filter: ((x2.c is null) or (x4.d = x0.d))
            -> Nested loop left join
                -> Nested loop inner join
                    -> Index lookup on x1 using PRIMARY (a = x0.d)
                    -> Table scan on x2, activating pushed join of 3 tables
                -> Nested loop inner join
                    -> Index lookup on x3 using PRIMARY (a = x2.d), child of x2 in pushed join
                    -> Index lookup on x4 using PRIMARY (a = x3.d), child of x3 in pushed join

Warnings:
Note	1003	Can't push outer joined table 'x1' as child of 'x0', join-nest containing the table has FILTER conditions
Note	1003	Can't push table 'x2' as child, 'type' must be a 'ref' access
Note	1003	Can't push table 'x3' as child of 'x0', column 'x2.d' refers a table which was not pushed
Note	1003	Can't push table 'x4' as child of 'x0', column 'x3.d' refers a table which was not pushed
Note	1003	Can't push table 'x3' as child of 'x1', column 'x2.d' refers a table which was not pushed
Note	1003	Can't push table 'x4' as child of 'x1', column 'x3.d' refers a table which was not pushed
select count(*) from t1 as x0
left join (t1 as x1
join (t1 as x2
left join (t1 as x3
join t1 as x4 on x3.d=x4.a)
on x2.d=x3.a)
on x2.c is null or x1.a=x4.d)
on x0.d=x1.a;
count(*)
2052
drop table t1;
create table t1 (pk char(10) primary key, u int not null) engine=ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
create table t2 (pk int primary key, u int not null) engine=ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values ('wh',1);
insert into t1 values ('ik',2);
insert into t1 values ('cu',3);
insert into t1 values ('pw',4);
insert into t1 values ('cq',4);
insert into t2 values (1,2), (2,3), (3,4), (4,5);
explain select * from t1 join t2 on t1.u = t2.pk order by t1.pk;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	40	NULL	5	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.t1.u	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`u` AS `u`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`u` AS `u` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`pk` = `test`.`t1`.`u`) order by `test`.`t1`.`pk`
select * from t1 join t2 on t1.u = t2.pk order by t1.pk;
pk	u	pk	u
cq	4	4	5
cu	3	3	4
ik	2	2	3
pw	4	4	5
wh	1	1	2
drop table t1;
drop table t2;
create table t1 (
a char(10) primary key,
b char(10) not null,
c char(10) not null,
l00 char(255) not null,
l01 char(255) not null,
l02 char(255) not null,
l03 char(255) not null,
l04 char(255) not null,
l05 char(255) not null,
l06 char(255) not null,
l07 char(255) not null,
l08 char(255) not null,
l09 char(255) not null,
l10 char(255) not null,
l11 char(255) not null,
l12 char(255) not null,
l13 char(255) not null,
l14 char(255) not null,
l15 char(255) not null,
l16 char(255) not null,
l17 char(255) not null,
l18 char(255) not null,
l19 char(255) not null,
l20 char(255) not null,
l21 char(255) not null,
l22 char(255) not null,
l23 char(255) not null,
l24 char(255) not null,
l25 char(255) not null,
l26 char(255) not null,
l27 char(255) not null,
l28 char(255) not null,
l29 char(255) not null,
l30 char(255) not null,
l31 char(255) not null,
index(c, b)
) engine=ndb character set latin1 partition by key(a) partitions 8;
insert into t1 values ('a','a','a','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x');
insert into t1 values ('b','b','b','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x');
insert into t1 values ('c','c','c','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x','x');
explain select count(*) from t1 as x1 join t1 as x2 on x1.b = x2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	10	test.x1.b	#	#	Child of 'x1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` `x1` join `test`.`t1` `x2` where (`test`.`x2`.`c` = `test`.`x1`.`b`)
select count(*) from t1 as x1 join t1 as x2 on x1.b = x2.c;
count(*)
3
drop table t1;
create table t1 
(a int not null,
b int not null, 
c int not null,
d int not null,
primary key(a,b,c,d)) engine=ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM"
       partition by key (b,c);
insert into t1 values (0x4f, 0x4f, 0x4f, 0x4f);
explain select * from t1 as x1 
join t1 as x2 on x1.c=0x4f and x2.a=0+x1.b and x2.b=x1.b 
join t1 as x3 on x3.a=x2.d and x3.b=x1.d and x3.c=x2.c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Using pushed condition (`test`.`x1`.`c` = 0x4f)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	8	func,test.x1.b	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`x2`.`a` = (0 + `test`.`x1`.`b`))
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	12	test.x2.d,test.x1.d,test.x2.c	#	#	Child of 'x2' in pushed join@1
Warnings:
Note	1003	Can't push table 'x2' as child, column 'a' does neither 'ref' a column nor a constant
Note	1003	Can't push table 'x3' as child of 'x1', column 'x2.d' refers a table which was not pushed
Note	1003	/* select#1 */ select `test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x1`.`c` AS `c`,`test`.`x1`.`d` AS `d`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b`,`test`.`x2`.`c` AS `c`,`test`.`x2`.`d` AS `d`,`test`.`x3`.`a` AS `a`,`test`.`x3`.`b` AS `b`,`test`.`x3`.`c` AS `c`,`test`.`x3`.`d` AS `d` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t1` `x3` where ((`test`.`x2`.`b` = `test`.`x1`.`b`) and (`test`.`x3`.`c` = `test`.`x2`.`c`) and (`test`.`x3`.`b` = `test`.`x1`.`d`) and (`test`.`x3`.`a` = `test`.`x2`.`d`) and (`test`.`x1`.`c` = 0x4f) and (`test`.`x2`.`a` = (0 + `test`.`x1`.`b`)))
select * from t1 as x1 
join t1 as x2 on x1.c=0x4f and x2.a=0+x1.b and x2.b=x1.b 
join t1 as x3 on x3.a=x2.c and x3.b=x1.d and x3.c=x2.c;
a	b	c	d	a	b	c	d	a	b	c	d
79	79	79	79	79	79	79	79	79	79	79	79
explain select * from t1 as x1 
join t1 as x2 on x1.c=0x4f and x2.a=0+x1.b and x2.b=x1.b 
join t1 as x3 on x3.a=x2.d and x3.b=x1.d and x3.c=0x4f;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	#	#	Using pushed condition (`test`.`x1`.`c` = 0x4f)
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	8	func,test.x1.b	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`x2`.`a` = (0 + `test`.`x1`.`b`))
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	12	test.x2.d,test.x1.d,const	#	#	Child of 'x2' in pushed join@1
Warnings:
Note	1003	Can't push table 'x2' as child, column 'a' does neither 'ref' a column nor a constant
Note	1003	Can't push table 'x3' as child of 'x1', column 'x2.d' refers a table which was not pushed
Note	1003	/* select#1 */ select `test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x1`.`c` AS `c`,`test`.`x1`.`d` AS `d`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b`,`test`.`x2`.`c` AS `c`,`test`.`x2`.`d` AS `d`,`test`.`x3`.`a` AS `a`,`test`.`x3`.`b` AS `b`,`test`.`x3`.`c` AS `c`,`test`.`x3`.`d` AS `d` from `test`.`t1` `x1` join `test`.`t1` `x2` join `test`.`t1` `x3` where ((`test`.`x2`.`b` = `test`.`x1`.`b`) and (`test`.`x3`.`b` = `test`.`x1`.`d`) and (`test`.`x3`.`a` = `test`.`x2`.`d`) and (`test`.`x3`.`c` = 0x4f) and (`test`.`x1`.`c` = 0x4f) and (`test`.`x2`.`a` = (0 + `test`.`x1`.`b`)))
select * from t1 as x1 
join t1 as x2 on x1.c=0x4f and x2.a=0+x1.b and x2.b=x1.b 
join t1 as x3 on x3.a=x2.c and x3.b=x1.d and x3.c=0x4f;
a	b	c	d	a	b	c	d	a	b	c	d
79	79	79	79	79	79	79	79	79	79	79	79
drop table t1;
create table t1 (
k1 int primary key,
i int,
name varchar(32),
key (name)
)
default charset = utf8mb3
engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (1, 1, 'Ole');
insert into t1 values (2, 2, 'Dole');
insert into t1 values (3, 3, 'Doffen');
insert into t1 values (4, 4, 'row# 999');
set @save_range_opt_max = @@session.range_optimizer_max_mem_size;
set range_optimizer_max_mem_size = 64*1024;
------------------------------------------------------------------

explain select * from t1 x, t1 y where y.k1=x.i and x.name in ('foo', .... 'row# 1999' );
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	name	NULL	NULL	NULL	4	50.00	Parent of 2 pushed join@1; Using pushed condition ((`test`.`x`.`name` in ('foo', .... 'row# 1999')) and (`test`.`x`.`i` is not null))
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.i	1	100.00	Child of 'x' in pushed join@1
Warnings:
Warning	3170	Memory capacity of 65536 bytes for 'range_optimizer_max_mem_size' exceeded. Range optimization was not done for this query.
Note	1003	/* select#1 */ select `test`.`x`.`k1` AS `k1`,`test`.`x`.`i` AS `i`,`test`.`x`.`name` AS `name`,`test`.`y`.`k1` AS `k1`,`test`.`y`.`i` AS `i`,`test`.`y`.`name` AS `name` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`k1` = `test`.`x`.`i`) and (`test`.`x`.`name` in ('foo', .... 'row# 1999')))
select * from t1 x, t1 y where y.k1=x.i and x.name in ('foo', .... 'row# 1999' );
k1	i	name	k1	i	name
4	4	row# 999	4	4	row# 999
Warnings:
Warning	3170	Memory capacity of 65536 bytes for 'range_optimizer_max_mem_size' exceeded. Range optimization was not done for this query.
------------------------------------------------------------------

explain select * from t1 x, t1 y where y.k1=x.i and x.name in ('foo', .... 'row# 3999' );
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	name	NULL	NULL	NULL	4	50.00	Parent of 2 pushed join@1; Using where
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.i	1	100.00	Child of 'x' in pushed join@1
Warnings:
Warning	3170	Memory capacity of 65536 bytes for 'range_optimizer_max_mem_size' exceeded. Range optimization was not done for this query.
Warning	4294	Scan filter is too large, discarded
Note	1003	/* select#1 */ select `test`.`x`.`k1` AS `k1`,`test`.`x`.`i` AS `i`,`test`.`x`.`name` AS `name`,`test`.`y`.`k1` AS `k1`,`test`.`y`.`i` AS `i`,`test`.`y`.`name` AS `name` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`k1` = `test`.`x`.`i`) and (`test`.`x`.`name` in ('foo', .... 'row# 3999')))
select * from t1 x, t1 y where y.k1=x.i and x.name in ('foo', .... 'row# 3999' );
k1	i	name	k1	i	name
4	4	row# 999	4	4	row# 999
Warnings:
Warning	3170	Memory capacity of 65536 bytes for 'range_optimizer_max_mem_size' exceeded. Range optimization was not done for this query.
Warning	4294	Scan filter is too large, discarded
------------------------------------------------------------------

explain select * from t1 x where x.name in ('foo', .... 'row# 3999' );
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	ALL	name	NULL	NULL	NULL	4	50.00	Using where
Warnings:
Warning	3170	Memory capacity of 65536 bytes for 'range_optimizer_max_mem_size' exceeded. Range optimization was not done for this query.
Warning	4294	Scan filter is too large, discarded
Note	1003	/* select#1 */ select `test`.`x`.`k1` AS `k1`,`test`.`x`.`i` AS `i`,`test`.`x`.`name` AS `name` from `test`.`t1` `x` where (`test`.`x`.`name` in ('foo', .... 'row# 3999'))
select * from t1 x where x.name in ('foo', .... 'row# 3999' );
k1	i	name
4	4	row# 999
Warnings:
Warning	3170	Memory capacity of 65536 bytes for 'range_optimizer_max_mem_size' exceeded. Range optimization was not done for this query.
Warning	4294	Scan filter is too large, discarded
insert into t1 values (5, 4, 'row# 1');
insert into t1 values (6, 4, 'row# 2');
insert into t1 values (7, 4, 'row# 3');
insert into t1 values (8, 4, 'row# 4');
------------------------------------------------------------------

explain select straight_join * from t1 x, t1 y where y.k1=x.i and y.name in ('foo', .... 'row# 999' ) order by x.k1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	4	NULL	8	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`i` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,name	PRIMARY	4	test.x.i	1	50.00	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`name` in ('foo', .... 'row# 999'))
Warnings:
Warning	3170	Memory capacity of 65536 bytes for 'range_optimizer_max_mem_size' exceeded. Range optimization was not done for this query.
Note	1003	/* select#1 */ select straight_join `test`.`x`.`k1` AS `k1`,`test`.`x`.`i` AS `i`,`test`.`x`.`name` AS `name`,`test`.`y`.`k1` AS `k1`,`test`.`y`.`i` AS `i`,`test`.`y`.`name` AS `name` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`k1` = `test`.`x`.`i`) and (`test`.`y`.`name` in ('foo', .... 'row# 999'))) order by `test`.`x`.`k1`
select straight_join * from t1 x, t1 y where y.k1=x.i and y.name in ('foo', .... 'row# 999' ) order by x.k1;
k1	i	name	k1	i	name
4	4	row# 999	4	4	row# 999
5	4	row# 1	4	4	row# 999
6	4	row# 2	4	4	row# 999
7	4	row# 3	4	4	row# 999
8	4	row# 4	4	4	row# 999
Warnings:
Warning	3170	Memory capacity of 65536 bytes for 'range_optimizer_max_mem_size' exceeded. Range optimization was not done for this query.
------------------------------------------------------------------

explain select straight_join * from t1 x, t1 y where y.k1=x.i and y.name in ('foo', .... 'row# 3999' ) order by x.k1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	4	NULL	8	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`i` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,name	PRIMARY	4	test.x.i	1	50.00	Child of 'x' in pushed join@1; Using where
Warnings:
Warning	3170	Memory capacity of 65536 bytes for 'range_optimizer_max_mem_size' exceeded. Range optimization was not done for this query.
Warning	4294	Scan filter is too large, discarded
Note	1003	/* select#1 */ select straight_join `test`.`x`.`k1` AS `k1`,`test`.`x`.`i` AS `i`,`test`.`x`.`name` AS `name`,`test`.`y`.`k1` AS `k1`,`test`.`y`.`i` AS `i`,`test`.`y`.`name` AS `name` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`k1` = `test`.`x`.`i`) and (`test`.`y`.`name` in ('foo', .... 'row# 3999'))) order by `test`.`x`.`k1`
select straight_join * from t1 x, t1 y where y.k1=x.i and y.name in ('foo', .... 'row# 3999' ) order by x.k1;
k1	i	name	k1	i	name
4	4	row# 999	4	4	row# 999
5	4	row# 1	4	4	row# 999
6	4	row# 2	4	4	row# 999
7	4	row# 3	4	4	row# 999
8	4	row# 4	4	4	row# 999
Warnings:
Warning	3170	Memory capacity of 65536 bytes for 'range_optimizer_max_mem_size' exceeded. Range optimization was not done for this query.
Warning	4294	Scan filter is too large, discarded
------------------------------------------------------------------

explain select straight_join * from t1 x, t1 y where y.k1=x.i and y.name in ('foo', .... 'row# 1999' ) order by x.k1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	4	NULL	8	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`i` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,name	PRIMARY	4	test.x.i	1	50.00	Child of 'x' in pushed join@1; Using pushed condition (`test`.`y`.`name` in ('foo', .... 'row# 1999'))
Warnings:
Warning	3170	Memory capacity of 65536 bytes for 'range_optimizer_max_mem_size' exceeded. Range optimization was not done for this query.
Note	1003	/* select#1 */ select straight_join `test`.`x`.`k1` AS `k1`,`test`.`x`.`i` AS `i`,`test`.`x`.`name` AS `name`,`test`.`y`.`k1` AS `k1`,`test`.`y`.`i` AS `i`,`test`.`y`.`name` AS `name` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`k1` = `test`.`x`.`i`) and (`test`.`y`.`name` in ('foo', .... 'row# 1999'))) order by `test`.`x`.`k1`
select straight_join * from t1 x, t1 y where y.k1=x.i and y.name in ('foo', .... 'row# 1999' ) order by x.k1;
k1	i	name	k1	i	name
4	4	row# 999	4	4	row# 999
5	4	row# 1	4	4	row# 999
6	4	row# 2	4	4	row# 999
7	4	row# 3	4	4	row# 999
8	4	row# 4	4	4	row# 999
Warnings:
Warning	3170	Memory capacity of 65536 bytes for 'range_optimizer_max_mem_size' exceeded. Range optimization was not done for this query.
set range_optimizer_max_mem_size = @save_range_opt_max;
drop table t1;
------------------------------------------------------------------
Bug#35185670 'mysqld crashes on expensive query'
or we may assert, or just hang until timeout...

create table t1 (
k1 varchar(32) not null,
k2 int not null default 0,
primary key(k1,k2),
i int default 0
) engine = ndbcluster
partition by key() partitions 32;
create table t2 (
k1 int not null primary key
) engine = ndbcluster
partition by key() partitions 32;

Inserting 2000 rows in table t1
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK

Expect query and IN-condition to be pushed
(If join_pushdown is enabled)
explain select * from t1 left join t2 on t2.k1=t1.i where t1.k1 in ('foo', .... 'row# 1999' ) order by t1.k1, t1.k2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7,p8,p9,p10,p11,p12,p13,p14,p15,p16,p17,p18,p19,p20,p21,p22,p23,p24,p25,p26,p27,p28,p29,p30,p31	range	PRIMARY	PRIMARY	130	NULL	2001	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`t1`.`k1` in ('foo', .... 'row# 1999')); Using MRR
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7,p8,p9,p10,p11,p12,p13,p14,p15,p16,p17,p18,p19,p20,p21,p22,p23,p24,p25,p26,p27,p28,p29,p30,p31	eq_ref	PRIMARY	PRIMARY	4	test.t1.i	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`k1` AS `k1`,`test`.`t1`.`k2` AS `k2`,`test`.`t1`.`i` AS `i`,`test`.`t2`.`k1` AS `k1` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`k1` = `test`.`t1`.`i`)) where (`test`.`t1`.`k1` in ('foo', .... 'row# 1999')) order by `test`.`t1`.`k1`,`test`.`t1`.`k2`
select * from t1 left join t2 on t2.k1=t1.i where t1.k1 in ('foo', .... 'row# 1999' ) order by t1.k1, t1.k2;
drop table t1;
drop table t2;
create table t(
pk int primary key auto_increment,
i int, 
j int,
k int,
index(i,j),
index(i),
index(j),
index(k)
) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t(i,j,k) values
(1,1,1), (1,1,1), (1,1,1),
(2,2,2), (2,2,2), (2,2,2);
set global debug='+d,max_4rows_in_spj_batches';
explain
select straight_join count(*) from 
t as t1
join t as t2 on t2.i = t1.i
join (t as t3 join t as t4 on t4.k=t3.k join t as t5 on t5.i=t4.i and t5.j=t3.j) on t3.pk=t1.j
join t as t6 on t6.k = t1.k
where t1.i < 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	range	i,i_2,j,k	i	5	NULL	#	#	Parent of 6 pushed join@1; Using pushed condition ((`test`.`t1`.`i` < 2) and (`test`.`t1`.`i` is not null) and (`test`.`t1`.`j` is not null) and (`test`.`t1`.`k` is not null)); Using MRR
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	i,i_2	i	5	test.t1.i	#	#	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,j,k	PRIMARY	4	test.t1.j	#	#	Child of 't1' in pushed join@1; Using pushed condition ((`test`.`t3`.`k` is not null) and (`test`.`t3`.`j` is not null))
1	SIMPLE	t4	p0,p1,p2,p3,p4,p5,p6,p7	ref	i,i_2,k	k	5	test.t3.k	#	#	Child of 't3' in pushed join@1; Using pushed condition (`test`.`t4`.`i` is not null)
1	SIMPLE	t5	p0,p1,p2,p3,p4,p5,p6,p7	ref	i,i_2,j	i	10	test.t4.i,test.t3.j	#	#	Child of 't4' in pushed join@1
1	SIMPLE	t6	p0,p1,p2,p3,p4,p5,p6,p7	ref	k	k	5	test.t1.k	#	#	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join count(0) AS `count(*)` from `test`.`t` `t1` join `test`.`t` `t2` join `test`.`t` `t3` join `test`.`t` `t4` join `test`.`t` `t5` join `test`.`t` `t6` where ((`test`.`t2`.`i` = `test`.`t1`.`i`) and (`test`.`t4`.`k` = `test`.`t3`.`k`) and (`test`.`t5`.`j` = `test`.`t3`.`j`) and (`test`.`t5`.`i` = `test`.`t4`.`i`) and (`test`.`t3`.`pk` = `test`.`t1`.`j`) and (`test`.`t6`.`k` = `test`.`t1`.`k`) and (`test`.`t1`.`i` < 2))
select straight_join count(*) from 
t as t1
join t as t2 on t2.i = t1.i
join (t as t3 join t as t4 on t4.k=t3.k join t as t5 on t5.i=t4.i and t5.j=t3.j) on t3.pk=t1.j
join t as t6 on t6.k = t1.k
where t1.i < 2;
count(*)
243
set global debug=@save_debug;
drop table t;
create table t1 (a int primary key, b int, c int, index(b,c)) engine = ndb
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values (4,null, 2);
explain
select x.a from t1 as x join t1 as y on y.a = x.b where x.a=4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY,b	PRIMARY	4	const	1	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`x`.`b` is not null)
1	SIMPLE	y	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.x.b	1	100.00	Child of 'x' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`x`.`a` AS `a` from `test`.`t1` `x` join `test`.`t1` `y` where ((`test`.`y`.`a` = `test`.`x`.`b`) and (`test`.`x`.`a` = 4))
select x.a from t1 as x join t1 as y on y.a = x.b where x.a=4;
a
drop table t1;
CREATE TABLE t1 (
a int NOT NULL,
b int DEFAULT NULL,
c int NOT NULL,
d int NOT NULL,
PRIMARY KEY (`a`)
) ENGINE=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
create unique index ix1 on t1(b,c) using hash;
Warnings:
Warning	1121	Ndb does not support unique index on NULL valued attributes, index access with NULL value will become full table scan
insert into t1 values (1,NULL,1,1);
explain select * from t1 as x1 left join (t1 as x2 join t1 as x3 on x2.d=x3.c) on x1.b=x3.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	x1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
1	SIMPLE	x2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	2	100.00	Parent of 2 pushed join@1
1	SIMPLE	x3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	ix1	ix1	9	test.x1.b,test.x2.d	1	100.00	Child of 'x2' in pushed join@1
Warnings:
Note	1003	Can't push table 'x2' as child, 'type' must be a 'ref' access
Note	1003	Can't push table 'x3' as child of 'x1', column 'x2.d' refers a table which was not pushed
Note	1003	/* select#1 */ select `test`.`x1`.`a` AS `a`,`test`.`x1`.`b` AS `b`,`test`.`x1`.`c` AS `c`,`test`.`x1`.`d` AS `d`,`test`.`x2`.`a` AS `a`,`test`.`x2`.`b` AS `b`,`test`.`x2`.`c` AS `c`,`test`.`x2`.`d` AS `d`,`test`.`x3`.`a` AS `a`,`test`.`x3`.`b` AS `b`,`test`.`x3`.`c` AS `c`,`test`.`x3`.`d` AS `d` from `test`.`t1` `x1` left join (`test`.`t1` `x2` join `test`.`t1` `x3`) on(((`test`.`x3`.`c` = `test`.`x2`.`d`) and (`test`.`x3`.`b` = `test`.`x1`.`b`))) where true
create temporary table scan_count
select * from performance_schema.global_status 
where variable_name = 'Ndb_scan_count';
select * from t1 as x1 left join (t1 as x2 join t1 as x3 on x2.d=x3.c) on x1.b=x3.b;
a	b	c	d	a	b	c	d	a	b	c	d
1	NULL	1	1	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Warning	1296	Prepared pushed join could not be executed, a paramValue was NULL
select scan_count.VARIABLE_NAME,
old.VARIABLE_VALUE - scan_count.VARIABLE_VALUE  
from scan_count, performance_schema.global_status as old 
where old.variable_name = 'Ndb_scan_count';
VARIABLE_NAME	old.VARIABLE_VALUE - scan_count.VARIABLE_VALUE
Ndb_scan_count	2
drop table scan_count;
drop table t1;
CREATE TABLE table1 (
col_int_unique int(11), 
PRIMARY KEY (col_int_unique)
) ENGINE=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE table3 (
col_int int(11) NOT NULL DEFAULT '0',
KEY (col_int)
) ENGINE=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE table4 (
col_int int(11) DEFAULT NULL,
pk int(11) NOT NULL,
PRIMARY KEY (pk)
) ENGINE=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE table6 (
col_int int(11) DEFAULT NULL,
col_int_unique int(11) DEFAULT NULL
) ENGINE=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into table6 values
(2,NULL), 
(2,NULL), 
(2,NULL), 
(2,NULL), 
(2,NULL), 
(2,NULL), 
(2,NULL), 
(2,NULL);
set global debug='+d,max_4rows_in_spj_batches';
EXPLAIN
SELECT * FROM
table1 RIGHT JOIN 
table3 LEFT JOIN table4 ON table3.col_int = table4.col_int 
JOIN
table6 ON table4.pk = table6.col_int_unique 
ON table1.col_int_unique = table6.col_int;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	table6	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	8	100.00	Parent of 4 pushed join@1
1	SIMPLE	table4	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.table6.col_int_unique	1	100.00	Child of 'table6' in pushed join@1; Using pushed condition (`test`.`table4`.`col_int` is not null)
1	SIMPLE	table1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.table6.col_int	1	100.00	Child of 'table6' in pushed join@1
1	SIMPLE	table3	p0,p1,p2,p3,p4,p5,p6,p7	ref	col_int	col_int	4	test.table4.col_int	2	100.00	Child of 'table4' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`table1`.`col_int_unique` AS `col_int_unique`,`test`.`table3`.`col_int` AS `col_int`,`test`.`table4`.`col_int` AS `col_int`,`test`.`table4`.`pk` AS `pk`,`test`.`table6`.`col_int` AS `col_int`,`test`.`table6`.`col_int_unique` AS `col_int_unique` from `test`.`table3` join `test`.`table4` join `test`.`table6` left join `test`.`table1` on((`test`.`table1`.`col_int_unique` = `test`.`table6`.`col_int`)) where ((`test`.`table3`.`col_int` = `test`.`table4`.`col_int`) and (`test`.`table4`.`pk` = `test`.`table6`.`col_int_unique`))
SELECT * FROM
table1 RIGHT JOIN 
table3 LEFT JOIN table4 ON table3.col_int = table4.col_int 
JOIN
table6 ON table4.pk = table6.col_int_unique 
ON table1.col_int_unique = table6.col_int;
col_int_unique	col_int	col_int	pk	col_int	col_int_unique
set global debug=@save_debug;
drop table table6;
drop table table4;
drop table table3;
drop table table1;
5.6 tests
create table t1 (
a int not null,
b int not null,
c int not null,
d int not null,
primary key (`a`,`b`),
key(c), key(d)
) engine=ndbcluster
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
insert into t1 values
(1,1,1,1), (2,2,2,2), (3,3,3,3), (4,4,4,4),
(1,2,5,1), (1,3,1,2), (1,4,2,3),
(2,1,3,4), (2,3,4,5), (2,4,5,1),
(3,1,1,2), (3,2,2,3), (3,4,3,4),
(4,1,4,5), (4,2,5,1), (4,3,1,2);
set global debug='+d,max_4rows_in_spj_batches';
set @save_optimizer_switch = @@optimizer_switch;
set optimizer_switch='materialization=off';
explain
select count(*) from t1 where 
t1.c in (select c from t1 as subq);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	c	NULL	NULL	NULL	16	100.00	Parent of 2 pushed join@1
1	SIMPLE	subq	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	4	test.t1.c	2	100.00	Child of 't1' in pushed join@1; FirstMatch(t1)
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` semi join (`test`.`t1` `subq`) where (`test`.`subq`.`c` = `test`.`t1`.`c`)
select count(*) from t1 where
t1.c in (select c from t1 as subq);
count(*)
16
explain
select count(*) from t1 where 
t1.c in (select c from t1 as subq1) and
t1.d in (select d from t1 as subq2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	c,d	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	subq1	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	4	test.t1.c	2	100.00	Child of 't1' in pushed join@1; FirstMatch(t1)
1	SIMPLE	subq2	p0,p1,p2,p3,p4,p5,p6,p7	ref	d	d	4	test.t1.d	2	100.00	Child of 't1' in pushed join@1; FirstMatch(subq1)
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` semi join (`test`.`t1` `subq1`) semi join (`test`.`t1` `subq2`) where ((`test`.`subq2`.`d` = `test`.`t1`.`d`) and (`test`.`subq1`.`c` = `test`.`t1`.`c`))
select count(*) from t1 where 
t1.c in (select c from t1 as subq1) and
t1.d in (select d from t1 as subq2);
count(*)
16
explain
select count(*) from t1 where 
t1.c in (select c from t1 as subq1 where 
subq1.c in (select c from t1 as subq2));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	c	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	subq1	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	4	test.t1.c	2	100.00	Child of 't1' in pushed join@1
1	SIMPLE	subq2	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	4	test.t1.c	2	100.00	Child of 't1' in pushed join@1; FirstMatch(t1)
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` semi join (`test`.`t1` `subq1` join `test`.`t1` `subq2`) where ((`test`.`subq1`.`c` = `test`.`t1`.`c`) and (`test`.`subq2`.`c` = `test`.`t1`.`c`))
select count(*) from t1 where 
t1.c in (select c from t1 as subq1 where 
subq1.c in (select c from t1 as subq2));
count(*)
16
explain
select count(*) from t1 where 
t1.c in (select subq1.c from t1 as subq1 straight_join t1 as subq2 on subq1.a = subq2.c);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	c	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	subq1	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY,c	c	4	test.t1.c	2	100.00	Child of 't1' in pushed join@1
1	SIMPLE	subq2	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	4	test.subq1.a	2	100.00	Child of 'subq1' in pushed join@1; FirstMatch(t1)
Warnings:
Note	1003	/* select#1 */ select count(0) AS `count(*)` from `test`.`t1` semi join (`test`.`t1` `subq1` straight_join `test`.`t1` `subq2`) where ((`test`.`subq2`.`c` = `test`.`subq1`.`a`) and (`test`.`subq1`.`c` = `test`.`t1`.`c`))
select count(*) from t1 where 
t1.c in (select subq1.c from t1 as subq1 straight_join t1 as subq2 on subq1.a = subq2.c);
count(*)
16
set optimizer_switch=@save_optimizer_switch;
set global debug=@save_debug;
#
# Bug#29860378
#   RESULTS NOT SORTED AS SPECIFIED WHEN QUERY IS A 'PUSHED JOIN'
#
set global debug='+d,max_4rows_in_spj_batches';
explain
select straight_join t1.* from
t1 join t1 as t2 on t2.d = t1.a
where t1.a > 0
order by t1.a,t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	range	PRIMARY	PRIMARY	4	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`t1`.`a` > 0); Using MRR
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	d	d	4	test.t1.a	2	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`d` = `test`.`t1`.`a`) and (`test`.`t1`.`a` > 0)) order by `test`.`t1`.`a`,`test`.`t1`.`b`
select straight_join t1.* from
t1 join t1 as t2 on t2.d = t1.a
where t1.a > 0
order by t1.a,t1.b;
a	b	c	d
1	1	1	1
1	1	1	1
1	1	1	1
1	1	1	1
1	2	5	1
1	2	5	1
1	2	5	1
1	2	5	1
1	3	1	2
1	3	1	2
1	3	1	2
1	3	1	2
1	4	2	3
1	4	2	3
1	4	2	3
1	4	2	3
2	1	3	4
2	1	3	4
2	1	3	4
2	1	3	4
2	2	2	2
2	2	2	2
2	2	2	2
2	2	2	2
2	3	4	5
2	3	4	5
2	3	4	5
2	3	4	5
2	4	5	1
2	4	5	1
2	4	5	1
2	4	5	1
3	1	1	2
3	1	1	2
3	1	1	2
3	2	2	3
3	2	2	3
3	2	2	3
3	3	3	3
3	3	3	3
3	3	3	3
3	4	3	4
3	4	3	4
3	4	3	4
4	1	4	5
4	1	4	5
4	1	4	5
4	2	5	1
4	2	5	1
4	2	5	1
4	3	1	2
4	3	1	2
4	3	1	2
4	4	4	4
4	4	4	4
4	4	4	4
explain
select straight_join t1.a,t1.b,count(*) from
t1 join t1 as t2 on t2.d = t1.a
where t1.a > 0
group by t1.a,t1.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	range	PRIMARY	PRIMARY	4	NULL	3	100.00	Parent of 2 pushed join@1; Using pushed condition (`test`.`t1`.`a` > 0); Using MRR
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	d	d	4	test.t1.a	2	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select straight_join `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` where ((`test`.`t2`.`d` = `test`.`t1`.`a`) and (`test`.`t1`.`a` > 0)) group by `test`.`t1`.`a`,`test`.`t1`.`b`
select straight_join t1.a,t1.b,count(*) from
t1 join t1 as t2 on t2.d = t1.a
where t1.a > 0
group by t1.a,t1.b;
a	b	count(*)
1	1	4
1	2	4
1	3	4
1	4	4
2	1	4
2	2	4
2	3	4
2	4	4
3	1	3
3	2	3
3	3	3
3	4	3
4	1	3
4	2	3
4	3	3
4	4	3
set global debug=@save_debug;
set @save_optimizer_switch = @@optimizer_switch;
set @@optimizer_switch='semijoin=on';
set @@optimizer_switch='loosescan=on';
explain SELECT count(*)
FROM (t1 AS table1 JOIN t1 AS table2 USING(a,b))
WHERE table1.b IN
(SELECT /*+ SEMIJOIN(LOOSESCAN)*/ table1s.c FROM t1 as table1s);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	table2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	table1s	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	4	test.table2.b	2	100.00	Child of 'table2' in pushed join@1; LooseScan
1	SIMPLE	table1	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.table2.a,test.table2.b	1	100.00	Child of 'table2' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select /*+ SEMIJOIN(@`select#2` LOOSESCAN) */ count(0) AS `count(*)` from `test`.`t1` `table1` join `test`.`t1` `table2` semi join (`test`.`t1` `table1s`) where ((`test`.`table1`.`a` = `test`.`table2`.`a`) and (`test`.`table1s`.`c` = `test`.`table2`.`b`) and (`test`.`table1`.`b` = `test`.`table2`.`b`))
SELECT count(*)
FROM (t1 AS table1 JOIN t1 AS table2 USING(a,b))
WHERE table1.b IN
(SELECT /*+ SEMIJOIN(LOOSESCAN)*/ table1s.c FROM t1 as table1s);
count(*)
16
set @@optimizer_switch='firstmatch=on';
explain SELECT count(*)
FROM (t1 AS table1 JOIN t1 AS table2 USING(a,b))
WHERE table1.b IN
(SELECT /*+ SEMIJOIN(FIRSTMATCH)*/ table1s.c FROM t1 as table1s);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	table1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	PRIMARY	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1
1	SIMPLE	table2	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.table1.a,test.table1.b	1	100.00	Child of 'table1' in pushed join@1
1	SIMPLE	table1s	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	4	test.table1.b	2	100.00	Child of 'table1' in pushed join@1; FirstMatch(table2)
Warnings:
Note	1003	/* select#1 */ select /*+ SEMIJOIN(@`select#2` FIRSTMATCH) */ count(0) AS `count(*)` from `test`.`t1` `table1` join `test`.`t1` `table2` semi join (`test`.`t1` `table1s`) where ((`test`.`table2`.`a` = `test`.`table1`.`a`) and (`test`.`table2`.`b` = `test`.`table1`.`b`) and (`test`.`table1s`.`c` = `test`.`table1`.`b`))
SELECT count(*)
FROM (t1 AS table1 JOIN t1 AS table2 USING(a,b))
WHERE table1.b IN
(SELECT /*+ SEMIJOIN(FIRSTMATCH)*/ table1s.c FROM t1 as table1s);
count(*)
16
set optimizer_switch=@save_optimizer_switch;
####################################################################
# WL#14370: Change EXPLAIN to use 'format=tree'. Traditional explain
# didn't correctly represent the order of tables in MATERIALIZED
# sub queries. That resulted in some confusing 'EXPLAIN_NO_PUSH' diffs
# where the reported root/child order didn't make sense.
# As the tree-format explain will show, the changed output
# make more sense related to the query plan represented by the
# AccessPath tree.
# WL#14370: Materialized semijoin's is not the only SJ-strategi
# where we do no pushdown across main-query -> sub-query border.
# (The sub-query itself may be pushed though)
###################################################################
set @save_optimizer_switch = @@optimizer_switch;
set @@optimizer_switch='semijoin=on';
set @@optimizer_switch='materialization=on';
explain format=tree select count(*)
from t1
join t1 as t2 on t2.a = t1.b
where t1.c IN (select /*+ SEMIJOIN(MATERIALIZATION)*/ c from t1 as t3);
EXPLAIN
-> Aggregate: count(0)
    -> Nested loop inner join
        -> Nested loop inner join
            -> Table scan on <subquery2>
                -> Materialize with deduplication
                    -> Table scan on t3
            -> Index lookup on t1 using c (c = `<subquery2>`.c), activating pushed join of 2 tables
        -> Index lookup on t2 using PRIMARY (a = t1.b), child of t1 in pushed join

Warnings:
Note	1003	Can't push table 't1' as child of 't3', it is in a materialized-branch which can't be referred.
Note	1003	Can't push table 't2' as child of 't3', it is in a materialized-branch which can't be referred.
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
where t1.c IN (select /*+ SEMIJOIN(MATERIALIZATION)*/ c from t1 as t3);
count(*)
64
explain format=tree select count(*)
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
where t1.c IN (select /*+ SEMIJOIN(MATERIALIZATION)*/ c from t1 as t3);
EXPLAIN
-> Aggregate: count(0)
    -> Nested loop inner join
        -> Nested loop inner join
            -> Table scan on <subquery2>
                -> Materialize with deduplication
                    -> Table scan on t3
            -> Index lookup on t1 using c (c = `<subquery2>`.c), activating pushed join of 2 tables
        -> Single-row index lookup on t2 using PRIMARY (a = t1.b, b = `<subquery2>`.c), child of t1 in pushed join

Warnings:
Note	1003	Can't push table 't1' as child of 't3', it is in a materialized-branch which can't be referred.
Note	1003	Can't push table 't2' as child of 't3', it is in a materialized-branch which can't be referred.
select count(*)
from t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
where t1.c IN (select /*+ SEMIJOIN(MATERIALIZATION)*/ c from t1 as t3);
count(*)
13
explain format=tree select count(*)
from (select * from t1 where t1.c IN (select /*+ SEMIJOIN(MATERIALIZATION)*/ c from t1 as t3)) as t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c;
EXPLAIN
-> Aggregate: count(0)
    -> Nested loop inner join
        -> Nested loop inner join
            -> Table scan on <subquery3>
                -> Materialize with deduplication
                    -> Table scan on t3
            -> Index lookup on t1 using c (c = `<subquery3>`.c), activating pushed join of 2 tables
        -> Single-row index lookup on t2 using PRIMARY (a = t1.b, b = `<subquery3>`.c), child of t1 in pushed join

Warnings:
Note	1003	Can't push table 't1' as child of 't3', it is in a materialized-branch which can't be referred.
Note	1003	Can't push table 't2' as child of 't3', it is in a materialized-branch which can't be referred.
select count(*)
from (select * from t1 where t1.c IN (select /*+ SEMIJOIN(MATERIALIZATION)*/ c from t1 as t3)) as t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c;
count(*)
13
explain format=tree select count(*)
from (select * from t1 where t1.c IN (select /*+ SEMIJOIN(MATERIALIZATION)*/ c from t1 as t3)) as t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
where t1.c IN (select /*+ SEMIJOIN(MATERIALIZATION)*/ c from t1 as t4);
EXPLAIN
-> Aggregate: count(0)
    -> Nested loop inner join
        -> Nested loop inner join
            -> Nested loop inner join
                -> Table scan on <subquery3>
                    -> Materialize with deduplication
                        -> Table scan on t3
                -> Index lookup on t1 using c (c = `<subquery3>`.c), activating pushed join of 2 tables
            -> Single-row index lookup on t2 using PRIMARY (a = t1.b, b = `<subquery3>`.c), child of t1 in pushed join
        -> Filter: (`<subquery4>`.c = `<subquery3>`.c)
            -> Single-row index lookup on <subquery4> using <auto_distinct_key> (c = t1.c)
                -> Materialize with deduplication
                    -> Table scan on t4

Warnings:
Note	1003	Can't push table 't1' as child of 't3', it is in a materialized-branch which can't be referred.
Note	1003	Can't push table 't2' as child of 't3', it is in a materialized-branch which can't be referred.
Note	1003	Can't push table 't4' as child, 'type' must be a 'ref' access
select count(*)
from (select * from t1 where t1.c IN (select /*+ SEMIJOIN(MATERIALIZATION)*/ c from t1 as t3)) as t1
join t1 as t2 on t2.a = t1.b and t2.b = t1.c
where t1.c IN (select /*+ SEMIJOIN(MATERIALIZATION)*/ c from t1 as t4);
count(*)
13
set optimizer_switch='block_nested_loop=off';
explain format=tree select count(*)
from t1 cross join t1 as t2
where t2.c IN (select /*+ SEMIJOIN(MATERIALIZATION)*/ t4s.c
from t1 as t3s join t1 as t4s on t4s.a = t3s.b);
EXPLAIN
-> Aggregate: count(0)
    -> Nested loop inner join
        -> Nested loop inner join
            -> Table scan on t1
            -> Table scan on <subquery2>
                -> Materialize with deduplication
                    -> Nested loop inner join
                        -> Table scan on t3s, activating pushed join of 2 tables
                        -> Index lookup on t4s using PRIMARY (a = t3s.b), child of t3s in pushed join
        -> Index lookup on t2 using c (c = `<subquery2>`.c)

Warnings:
Note	1003	Can't push table 't3s' as child, 'type' must be a 'ref' access
Note	1003	Can't push table 't4s' as child of 't1', it is in a aggregated-branch which can't be referred.
Note	1003	Can't push table 't2' as child of 't1', column '<subquery2>.c' is in a subquery-branch which can't be referred
Note	1003	Can't push table 't2' as child of 't3s', it is in a materialized-branch which can't be referred.
select count(*)
from t1 cross join t1 as t2
where t2.c IN (select /*+ SEMIJOIN(MATERIALIZATION)*/ t4s.c
from t1 as t3s join t1 as t4s on t4s.a = t3s.b);
count(*)
256
set optimizer_switch=@save_optimizer_switch;
set global debug='+d,max_4rows_in_spj_batches';
set @save_optimizer_switch = @@optimizer_switch;
set @@optimizer_switch='semijoin=on';
explain
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
where t1.c IN (
select /*+ SEMIJOIN(DUPSWEEDOUT)*/ t3s.c from t1 as t3s
join t1 as t4s on t4s.a = t3s.b);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3s	p0,p1,p2,p3,p4,p5,p6,p7	ALL	c	NULL	NULL	NULL	16	100.00	Parent of 3 pushed join@1; Start temporary
1	SIMPLE	t4s	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t3s.b	1	100.00	Child of 't3s' in pushed join@1
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	4	test.t3s.c	2	100.00	Child of 't3s' in pushed join@1; End temporary
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	NULL
Warnings:
Note	1003	Can't push table 't2' as child of 't3s', it is in a duplicate-weedout-branch which can't be referred.
Note	1003	/* select#1 */ select /*+ SEMIJOIN(@`select#2` DUPSWEEDOUT) */ count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` semi join (`test`.`t1` `t3s` join `test`.`t1` `t4s`) where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t4s`.`a` = `test`.`t3s`.`b`) and (`test`.`t1`.`c` = `test`.`t3s`.`c`))
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
where t1.c IN (
select /*+ SEMIJOIN(DUPSWEEDOUT)*/ t3s.c from t1 as t3s
join t1 as t4s on t4s.a = t3s.b);
count(*)
64
explain
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
where t2.c IN (
select /*+ SEMIJOIN(DUPSWEEDOUT)*/ t3s.c from t1 as t3s
join t1 as t4s on t4s.a = t3s.b);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY,c	PRIMARY	4	test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3s	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	4	test.t2.c	2	100.00	Child of 't2' in pushed join@1; Start temporary
1	SIMPLE	t4s	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t3s.b	1	100.00	Child of 't3s' in pushed join@1; End temporary
Warnings:
Note	1003	/* select#1 */ select /*+ SEMIJOIN(@`select#2` DUPSWEEDOUT) */ count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` semi join (`test`.`t1` `t3s` join `test`.`t1` `t4s`) where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t4s`.`a` = `test`.`t3s`.`b`) and (`test`.`t3s`.`c` = `test`.`t2`.`c`))
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
where t2.c IN (
select /*+ SEMIJOIN(DUPSWEEDOUT)*/ t3s.c from t1 as t3s
join t1 as t4s on t4s.a = t3s.b);
count(*)
64
explain
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
join t1 as t3 on t3.a = t2.b
where t1.c IN (
select /*+ SEMIJOIN(DUPSWEEDOUT)*/ t4s.c from t1 as t4s
join t1 as t5s on t5s.a = t4s.b
join t1 as t6s on t6s.a = t5s.b
);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t4s	p0,p1,p2,p3,p4,p5,p6,p7	ALL	c	NULL	NULL	NULL	16	100.00	Parent of 4 pushed join@1; Start temporary
1	SIMPLE	t5s	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t4s.b	1	100.00	Child of 't4s' in pushed join@1
1	SIMPLE	t6s	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t5s.b	1	100.00	Child of 't5s' in pushed join@1
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	4	test.t4s.c	2	100.00	Child of 't4s' in pushed join@1; End temporary
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Parent of 2 pushed join@2
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t2.b	1	100.00	Child of 't2' in pushed join@2
Warnings:
Note	1003	Can't push table 't2' as child of 't4s', it is in a duplicate-weedout-branch which can't be referred.
Note	1003	Can't push table 't3' as child of 't4s', it is in a duplicate-weedout-branch which can't be referred.
Note	1003	/* select#1 */ select /*+ SEMIJOIN(@`select#2` DUPSWEEDOUT) */ count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` semi join (`test`.`t1` `t4s` join `test`.`t1` `t5s` join `test`.`t1` `t6s`) where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t2`.`b`) and (`test`.`t5s`.`a` = `test`.`t4s`.`b`) and (`test`.`t6s`.`a` = `test`.`t5s`.`b`) and (`test`.`t1`.`c` = `test`.`t4s`.`c`))
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
join t1 as t3 on t3.a = t2.b
where t1.c IN (
select /*+ SEMIJOIN(DUPSWEEDOUT)*/ t4s.c from t1 as t4s
join t1 as t5s on t5s.a = t4s.b
join t1 as t6s on t6s.a = t5s.b
);
count(*)
256
explain
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
join t1 as t3 on t3.a = t2.b
where t2.c IN (
select /*+ SEMIJOIN(DUPSWEEDOUT)*/ t4s.c from t1 as t4s
join t1 as t5s on t5s.a = t4s.b
join t1 as t6s on t6s.a = t5s.b
);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 6 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY,c	PRIMARY	4	test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t2.b	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t4s	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	4	test.t2.c	2	100.00	Child of 't2' in pushed join@1; Start temporary
1	SIMPLE	t5s	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t4s.b	1	100.00	Child of 't4s' in pushed join@1
1	SIMPLE	t6s	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t5s.b	1	100.00	Child of 't5s' in pushed join@1; End temporary
Warnings:
Note	1003	/* select#1 */ select /*+ SEMIJOIN(@`select#2` DUPSWEEDOUT) */ count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` semi join (`test`.`t1` `t4s` join `test`.`t1` `t5s` join `test`.`t1` `t6s`) where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t2`.`b`) and (`test`.`t5s`.`a` = `test`.`t4s`.`b`) and (`test`.`t6s`.`a` = `test`.`t5s`.`b`) and (`test`.`t4s`.`c` = `test`.`t2`.`c`))
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
join t1 as t3 on t3.a = t2.b
where t2.c IN (
select /*+ SEMIJOIN(DUPSWEEDOUT)*/ t4s.c from t1 as t4s
join t1 as t5s on t5s.a = t4s.b
join t1 as t6s on t6s.a = t5s.b
);
count(*)
256
explain
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
join t1 as t3 on t3.a = t2.b
where t3.c IN (
select /*+ SEMIJOIN(DUPSWEEDOUT)*/ t4s.c from t1 as t4s
join t1 as t5s on t5s.a = t4s.b
join t1 as t6s on t6s.a = t5s.b
);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	16	100.00	Parent of 6 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.b	1	100.00	Child of 't1' in pushed join@1
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY,c	PRIMARY	4	test.t2.b	1	100.00	Child of 't2' in pushed join@1
1	SIMPLE	t4s	p0,p1,p2,p3,p4,p5,p6,p7	ref	c	c	4	test.t3.c	2	100.00	Child of 't3' in pushed join@1; Start temporary
1	SIMPLE	t5s	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t4s.b	1	100.00	Child of 't4s' in pushed join@1
1	SIMPLE	t6s	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t5s.b	1	100.00	Child of 't5s' in pushed join@1; End temporary
Warnings:
Note	1003	/* select#1 */ select /*+ SEMIJOIN(@`select#2` DUPSWEEDOUT) */ count(0) AS `count(*)` from `test`.`t1` join `test`.`t1` `t2` join `test`.`t1` `t3` semi join (`test`.`t1` `t4s` join `test`.`t1` `t5s` join `test`.`t1` `t6s`) where ((`test`.`t2`.`a` = `test`.`t1`.`b`) and (`test`.`t3`.`a` = `test`.`t2`.`b`) and (`test`.`t5s`.`a` = `test`.`t4s`.`b`) and (`test`.`t6s`.`a` = `test`.`t5s`.`b`) and (`test`.`t4s`.`c` = `test`.`t3`.`c`))
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
join t1 as t3 on t3.a = t2.b
where t3.c IN (
select /*+ SEMIJOIN(DUPSWEEDOUT)*/ t4s.c from t1 as t4s
join t1 as t5s on t5s.a = t4s.b
join t1 as t6s on t6s.a = t5s.b
);
count(*)
256
set global debug=@save_debug;
set optimizer_switch=@save_optimizer_switch;
###########
# WL#14370: EXPLAIN need to use 'format=tree as table access order
# in MATERIALIZED execution is not currectly represented by
# the traditional explain.
###########
explain format=tree select count(*)
from t1
join t1 as t2 on t2.a = t1.b
where t1.c IN (
select /*+ SEMIJOIN(MATERIALIZATION)*/ t3s.c from t1 as t3s
join t1 as t4s on t4s.a = t3s.b);
EXPLAIN
-> Aggregate: count(0)
    -> Nested loop inner join
        -> Nested loop inner join
            -> Table scan on <subquery2>
                -> Materialize with deduplication
                    -> Nested loop inner join
                        -> Table scan on t3s, activating pushed join of 2 tables
                        -> Index lookup on t4s using PRIMARY (a = t3s.b), child of t3s in pushed join
            -> Index lookup on t1 using c (c = `<subquery2>`.c), activating pushed join of 2 tables
        -> Index lookup on t2 using PRIMARY (a = t1.b), child of t1 in pushed join

Warnings:
Note	1003	Can't push table 't1' as child of 't3s', it is in a materialized-branch which can't be referred.
Note	1003	Can't push table 't2' as child of 't3s', it is in a materialized-branch which can't be referred.
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
where t1.c IN (
select /*+ SEMIJOIN(MATERIALIZATION)*/ t3s.c from t1 as t3s
join t1 as t4s on t4s.a = t3s.b);
count(*)
64
explain format=tree select count(*)
from t1
join t1 as t2 on t2.a = t1.b
where t1.c IN(
select /*+ SEMIJOIN(MATERIALIZATION)*/ t3s1.c from t1 as t3s1
join t1 as t4s1 on t4s1.a = t3s1.b
)
and t1.d IN (
select /*+ SEMIJOIN(MATERIALIZATION)*/ t3s2.c from t1 as t3s2
join t1 as t4s2 on t4s2.a = t3s2.b
);
EXPLAIN
-> Aggregate: count(0)
    -> Nested loop inner join
        -> Nested loop inner join
            -> Nested loop inner join
                -> Table scan on <subquery2>
                    -> Materialize with deduplication
                        -> Nested loop inner join
                            -> Table scan on t3s1, activating pushed join of 2 tables
                            -> Index lookup on t4s1 using PRIMARY (a = t3s1.b), child of t3s1 in pushed join
                -> Index lookup on t1 using c (c = `<subquery2>`.c), activating pushed join of 2 tables
            -> Index lookup on t2 using PRIMARY (a = t1.b), child of t1 in pushed join
        -> Single-row index lookup on <subquery3> using <auto_distinct_key> (c = t1.d)
            -> Materialize with deduplication
                -> Nested loop inner join
                    -> Table scan on t3s2, activating pushed join of 2 tables
                    -> Index lookup on t4s2 using PRIMARY (a = t3s2.b), child of t3s2 in pushed join

Warnings:
Note	1003	Can't push table 't1' as child of 't3s1', it is in a materialized-branch which can't be referred.
Note	1003	Can't push table 't2' as child of 't3s1', it is in a materialized-branch which can't be referred.
Note	1003	Can't push table 't3s2' as child, 'type' must be a 'ref' access
Note	1003	Can't push table 't4s2' as child of 't3s1', it is in a materialized-branch which can't be referred.
Note	1003	Can't push table 't4s2' as child of 't1', it is in a aggregated-branch which can't be referred.
select count(*)
from t1
join t1 as t2 on t2.a = t1.b
where t1.c IN(
select /*+ SEMIJOIN(MATERIALIZATION)*/ t3s1.c from t1 as t3s1
join t1 as t4s1 on t4s1.a = t3s1.b
)
and t1.d IN (
select /*+ SEMIJOIN(MATERIALIZATION)*/ t3s2.c from t1 as t3s2
join t1 as t4s2 on t4s2.a = t3s2.b
);
count(*)
64
explain
select /*+ BKA(t2) NO_BKA(t3) */ t1.a, t2.a, t3.a
from t1
straight_join t1 as t2 on t2.a = t1.c
straight_join t1 as t3 on t3.a = t2.c and t3.b=t2.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	c	NULL	NULL	NULL	16	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY,c,d	PRIMARY	4	test.t1.c	1	100.00	Parent of 2 pushed join@1; Using join buffer (Batched Key Access)
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t2.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	Can't push table 't2' as child, access type 'Batched-key' not implemented
Note	1003	Can't push table 't3' as child of 't1', it is in a batched-keys-branch which can't be referred.
Note	1003	/* select#1 */ select /*+ BKA(`t2`@`select#1`) NO_BKA(`t3`@`select#1`) */ `test`.`t1`.`a` AS `a`,`test`.`t2`.`a` AS `a`,`test`.`t3`.`a` AS `a` from `test`.`t1` straight_join `test`.`t1` `t2` straight_join `test`.`t1` `t3` where ((`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t3`.`a` = `test`.`t2`.`c`))
select /*+ BKA(t2) NO_BKA(t3) */ t1.a, t2.a, t3.a
from t1
straight_join t1 as t2 on t2.a = t1.c
straight_join t1 as t3 on t3.a = t2.c and t3.b=t2.d;
a	a	a
1	1	1
1	1	1
1	1	1
1	1	1
1	1	2
1	1	2
1	2	2
1	2	3
2	2	2
2	2	3
2	3	1
2	3	2
2	3	3
2	3	3
2	4	1
2	4	4
3	1	1
3	1	1
3	1	2
3	2	2
3	2	3
3	3	1
3	3	1
3	3	2
3	3	2
3	3	3
3	3	3
3	3	3
3	3	3
4	1	1
4	1	1
4	1	2
4	4	1
4	4	1
4	4	4
4	4	4
explain
select /*+ BKA(t2) NO_BKA(t3) */ t1.a, t2.a, t3.a
from t1
straight_join t1 as t2 on t2.a = t1.c
straight_join t1 as t3 on t3.a = t1.c and t3.b=t2.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	c	NULL	NULL	NULL	16	100.00	NULL
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY,d	PRIMARY	4	test.t1.c	1	100.00	Parent of 2 pushed join@1; Using join buffer (Batched Key Access)
1	SIMPLE	t3	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	8	test.t1.c,test.t2.d	1	100.00	Child of 't2' in pushed join@1
Warnings:
Note	1003	Can't push table 't2' as child, access type 'Batched-key' not implemented
Note	1003	Can't push table 't3' as child of 't1', it is in a batched-keys-branch which can't be referred.
Note	1003	/* select#1 */ select /*+ BKA(`t2`@`select#1`) NO_BKA(`t3`@`select#1`) */ `test`.`t1`.`a` AS `a`,`test`.`t2`.`a` AS `a`,`test`.`t3`.`a` AS `a` from `test`.`t1` straight_join `test`.`t1` `t2` straight_join `test`.`t1` `t3` where ((`test`.`t3`.`b` = `test`.`t2`.`d`) and (`test`.`t2`.`a` = `test`.`t1`.`c`) and (`test`.`t3`.`a` = `test`.`t1`.`c`))
select /*+ BKA(t2) NO_BKA(t3) */ t1.a, t2.a, t3.a
from t1
straight_join t1 as t2 on t2.a = t1.c
straight_join t1 as t3 on t3.a = t1.c and t3.b=t2.d;
a	a	a
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	2	2
1	2	2
1	2	2
2	2	2
2	2	2
2	2	2
2	3	3
2	3	3
2	3	3
2	3	3
2	4	4
2	4	4
2	4	4
3	1	1
3	1	1
3	1	1
3	1	1
3	2	2
3	2	2
3	2	2
3	3	3
3	3	3
3	3	3
3	3	3
3	3	3
3	3	3
3	3	3
3	3	3
4	1	1
4	1	1
4	1	1
4	1	1
4	4	4
4	4	4
4	4	4
4	4	4
4	4	4
4	4	4
drop table t1;
CREATE TABLE ndb_order_test (
node_id int(10) unsigned NOT NULL,
user_id int(10) unsigned NOT NULL,
sort_number int(10) unsigned NOT NULL,
KEY node_id (node_id,sort_number)
) ENGINE=ndbcluster DEFAULT CHARSET=utf8mb3
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
CREATE TABLE ndb_user_test (
user_id int(10) unsigned NOT NULL AUTO_INCREMENT,
name varchar(20) NOT NULL,
PRIMARY KEY (user_id)
) ENGINE=ndbcluster DEFAULT CHARSET=utf8mb3 AUTO_INCREMENT=2
comment="NDB_TABLE=PARTITION_BALANCE=FOR_RP_BY_LDM";
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO ndb_order_test (node_id, user_id, sort_number) VALUES
(68, 1, 1398029),
(68, 1, 549053);
INSERT INTO ndb_user_test (user_id, name) VALUES
(1, 'Shawn');
EXPLAIN 
SELECT *
FROM ndb_order_test JOIN ndb_user_test USING (user_id)
WHERE node_id = 68
ORDER BY sort_number DESC;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	ndb_order_test	p0,p1,p2,p3,p4,p5,p6,p7	ref	node_id	node_id	4	const	2	100.00	Parent of 2 pushed join@1; Backward index scan
1	SIMPLE	ndb_user_test	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.ndb_order_test.user_id	1	100.00	Child of 'ndb_order_test' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`ndb_order_test`.`user_id` AS `user_id`,`test`.`ndb_order_test`.`node_id` AS `node_id`,`test`.`ndb_order_test`.`sort_number` AS `sort_number`,`test`.`ndb_user_test`.`name` AS `name` from `test`.`ndb_order_test` join `test`.`ndb_user_test` where ((`test`.`ndb_user_test`.`user_id` = `test`.`ndb_order_test`.`user_id`) and (`test`.`ndb_order_test`.`node_id` = 68)) order by `test`.`ndb_order_test`.`sort_number` desc
SELECT *
FROM ndb_order_test JOIN ndb_user_test USING (user_id)
WHERE node_id = 68
ORDER BY sort_number DESC;
user_id	node_id	sort_number	name
1	68	1398029	Shawn
1	68	549053	Shawn
EXPLAIN 
SELECT *
FROM ndb_order_test JOIN ndb_user_test USING (user_id)
WHERE node_id = 68
ORDER BY sort_number ASC;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	ndb_order_test	p0,p1,p2,p3,p4,p5,p6,p7	ref	node_id	node_id	4	const	2	100.00	Parent of 2 pushed join@1
1	SIMPLE	ndb_user_test	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	test.ndb_order_test.user_id	1	100.00	Child of 'ndb_order_test' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`ndb_order_test`.`user_id` AS `user_id`,`test`.`ndb_order_test`.`node_id` AS `node_id`,`test`.`ndb_order_test`.`sort_number` AS `sort_number`,`test`.`ndb_user_test`.`name` AS `name` from `test`.`ndb_order_test` join `test`.`ndb_user_test` where ((`test`.`ndb_user_test`.`user_id` = `test`.`ndb_order_test`.`user_id`) and (`test`.`ndb_order_test`.`node_id` = 68)) order by `test`.`ndb_order_test`.`sort_number`
SELECT *
FROM ndb_order_test JOIN ndb_user_test USING (user_id)
WHERE node_id = 68
ORDER BY sort_number ASC;
user_id	node_id	sort_number	name
1	68	549053	Shawn
1	68	1398029	Shawn
DROP TABLE ndb_order_test, ndb_user_test;
##################
#
# Bug #29501263 CRASH IN SPJ BLOCK, ILLEGAL ACCESS
#               TO ARRAYPOOL<T>::GETPTR
#
##################
create table t1 (
pk int primary key,
i1 int
) engine = ndb;
insert into t1 values (1,1),(2,2),(3,3);
alter table t1 add column (i2 int, i3 int, i4 int, i5 int);
Warnings:
Warning	1478	Converted FIXED field 'i2' to DYNAMIC to enable online ADD COLUMN
Warning	1478	Converted FIXED field 'i3' to DYNAMIC to enable online ADD COLUMN
Warning	1478	Converted FIXED field 'i4' to DYNAMIC to enable online ADD COLUMN
Warning	1478	Converted FIXED field 'i5' to DYNAMIC to enable online ADD COLUMN
update t1 set i2=i1, i3=i1, i4=i1, i5=i1;
alter table t1 add index ix1(i1);
alter table t1 add index ix2(i2);
alter table t1 add index ix3(i3);
alter table t1 add index ix4(i4);
alter table t1 add index ix5(i5);
alter table t1 add unique index uix(i4,i5);
select straight_join * from
t1
join t1 as t2 on t2.i1 = t1.i1
join t1 as t3 on t3.pk = t2.i2
left join t1 as t4 on t4.pk = t3.i3
left join t1 as t5 on t5.i4 = t2.i4 and t5.i5 = t1.i5;
pk	i1	i2	i3	i4	i5	pk	i1	i2	i3	i4	i5	pk	i1	i2	i3	i4	i5	pk	i1	i2	i3	i4	i5	pk	i1	i2	i3	i4	i5
1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1	1
2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2	2
3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3	3
drop table t1;
##################
#
# Bug#32203548 REQUIRE_FAILED WHEN COMPARE_NDBRECORD()
#                SORT-MERGE SPJ RESULTS FROM INDEX
#
##################
create table t1 (
a int not null,
b int not null,
c varchar(1) not null,
d varchar(1) not null,
primary key (a,b),
index ix2 (d,c)
) engine=ndbcluster;
insert into t1 values
(1,1,"1","1"), (2,2,"2","1"), (3,3,"3","1"), (4,4,"4","1"),
(1,2,"5","1"), (1,3,"1","1"), (1,4,"2","1"),
(2,1,"3","1"), (2,3,"4","1"), (2,4,"5","1");
explain
select t1.d from t1 FORCE INDEX FOR ORDER BY (ix2)
straight_join t1 as t2 on t2.a = t1.a
order by t1.d;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	index	PRIMARY	ix2	12	NULL	10	100.00	Parent of 2 pushed join@1
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ref	PRIMARY	PRIMARY	4	test.t1.a	1	100.00	Child of 't1' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`d` AS `d` from `test`.`t1` FORCE INDEX FOR ORDER BY (`ix2`) straight_join `test`.`t1` `t2` where (`test`.`t2`.`a` = `test`.`t1`.`a`) order by `test`.`t1`.`d`
select t1.d from t1 FORCE INDEX FOR ORDER BY (ix2)
straight_join t1 as t2 on t2.a = t1.a
order by t1.d;
d
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
1
drop table t1;
##################
#
# Specific WL-14370, 'AccessPath' tests
# (Unknown if/how this worked pre-wl-14370)
#
##################
Testcase for AccessPath::NESTED_LOOP_SEMIJOIN_WITH_DUPLICATE_REMOVAL
Expect explain: 'Nested loop semijoin with duplicate removal on...'
(Hardly ever used unless forced to...)
Testcase for AccessPath::WINDOWING
create table t2(a int) engine=ndbcluster;
insert into t2 values(1), (2);
create table t3(a int, b int) engine=ndbcluster;
insert into t3 values(1, 3), (2, 3);
explain format=tree select a,
(select sum(a) over w from t2 window w as(order by t3.a) limit 1)
from t3;
ERROR HY000: Outer reference test.t3.a in window's ORDER BY or PARTITION BY clause not allowed.
select a,
(select sum(a) over w from t2 window w as(order by t3.a) limit 1)
from t3;
ERROR HY000: Outer reference test.t3.a in window's ORDER BY or PARTITION BY clause not allowed.
explain format=tree select a,
(select sum(a) over w from t2 window w as(order by "whatever") limit 1)
from t3;
EXPLAIN
-> Table scan on t3
-> Select #2 (subquery in projection; run only once)
    -> Limit: 1 row(s)
        -> Window aggregate with buffering: sum(t2.a) OVER w
            -> Sort: whatever
                -> Table scan on t2

select a,
(select sum(a) over w from t2 window w as(order by "whatever") limit 1)
from t3;
a	(select sum(a) over w from t2 window w as(order by "whatever") limit 1)
1	3
2	3
drop table t2,t3;
create table t1 (
a int not null,
b int not null,
c int not null,
d int not null,
primary key (`a`,`b`)
) engine=ndbcluster
partition by key() partitions 8;
insert into t1 values
(1,1,1,1), (2,2,2,2), (3,3,3,3), (4,4,4,4),
(1,2,5,1), (1,3,1,2), (1,4,2,3),
(2,1,3,4), (2,3,4,5), (2,4,5,1),
(3,1,1,2), (3,2,2,3), (3,4,3,4),
(4,1,4,5), (4,2,5,1), (4,3,1,2);
explain format=tree select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0 and t2.b = t1.c
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b
where t1.a=1 and t1.d=1 union all select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0 and t2.b = t1.c
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b
where t1.a=1 and t1.d=1;
EXPLAIN
-> Append
    -> Stream results
        -> Nested loop inner join
            -> Nested loop inner join
                -> Index lookup on t1 using PRIMARY (a = 1), activating pushed join of 2 tables, with pushed condition: (t1.d = 1)
                -> Filter: (t2.a = (t1.b + 0))
                    -> Single-row index lookup on t2 using PRIMARY (a = (t1.b + 0), b = t1.c)
            -> Single-row index lookup on t3 using PRIMARY (a = t1.b, b = t1.c), child of t1 in pushed join
    -> Stream results
        -> Nested loop inner join
            -> Nested loop inner join
                -> Index lookup on t1 using PRIMARY (a = 1), activating pushed join of 2 tables, with pushed condition: (t1.d = 1)
                -> Filter: (t2.a = (t1.b + 0))
                    -> Single-row index lookup on t2 using PRIMARY (a = (t1.b + 0), b = t1.c)
            -> Single-row index lookup on t3 using PRIMARY (a = t1.b, b = t1.c), child of t1 in pushed join

Warnings:
Note	1003	Can't push table 't2' as child, column 'a' does neither 'ref' a column nor a constant
Note	1003	Can't push table 't2' as child, column 'a' does neither 'ref' a column nor a constant
select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0 and t2.b = t1.c
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b
where t1.a=1 and t1.d=1 union all select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0 and t2.b = t1.c
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b
where t1.a=1 and t1.d=1;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
1	1	1	1	1	1	1	1	1	1	1	1
explain format=tree select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0 and t2.b = t1.c
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b
where t1.a=1 and t1.d=1 union select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0 and t2.b = t1.c
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b
where t1.a=1 and t1.d=1;
EXPLAIN
-> Table scan on <union temporary>
    -> Union materialize with deduplication
        -> Nested loop inner join
            -> Nested loop inner join
                -> Index lookup on t1 using PRIMARY (a = 1), activating pushed join of 2 tables, with pushed condition: (t1.d = 1)
                -> Filter: (t2.a = (t1.b + 0))
                    -> Single-row index lookup on t2 using PRIMARY (a = (t1.b + 0), b = t1.c)
            -> Single-row index lookup on t3 using PRIMARY (a = t1.b, b = t1.c), child of t1 in pushed join
        -> Nested loop inner join
            -> Nested loop inner join
                -> Index lookup on t1 using PRIMARY (a = 1), activating pushed join of 2 tables, with pushed condition: (t1.d = 1)
                -> Filter: (t2.a = (t1.b + 0))
                    -> Single-row index lookup on t2 using PRIMARY (a = (t1.b + 0), b = t1.c)
            -> Single-row index lookup on t3 using PRIMARY (a = t1.b, b = t1.c), child of t1 in pushed join

Warnings:
Note	1003	Can't push table 't2' as child, column 'a' does neither 'ref' a column nor a constant
Note	1003	Can't push table 't2' as child, column 'a' does neither 'ref' a column nor a constant
select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0 and t2.b = t1.c
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b
where t1.a=1 and t1.d=1 union select *
from t1
straight_join t1 as t2 on t2.a = t1.b+0 and t2.b = t1.c
straight_join t1 as t3 on t3.a = t1.b and t3.b = t2.b
where t1.a=1 and t1.d=1;
a	b	c	d	a	b	c	d	a	b	c	d
1	1	1	1	1	1	1	1	1	1	1	1
drop table t1;
##################
#
# Bug#33669039 Stale rows returned from SPJ API,
#              hit assert(key_cmp_if_same(...)) in debug build
#
##################
create table t (
pk int primary key,
a int,
b int,
c int,
d int
) engine = ndbcluster;
insert into t values (0,0,0,0,0);
insert into t values (1,1,1,1,1);
insert into t values (2,2,2,2,2);
insert into t values (3,3,3,3,3);
insert into t values (4,4,4,4,4);
insert into t values (5,5,5,5,5);
insert into t values (6,6,6,6,6);
insert into t values (7,-1,7,7,7);
insert into t values (8,8,8,8,8);
insert into t values (9,9,9,9,9);
insert into t values (10,10,10,10,10);
insert into t values (11,11,11,11,11);
insert into t values (12,12,12,12,12);
insert into t values (13,13,13,13,13);
insert into t values (14,14,14,14,14);
insert into t values (15,15,15,15,15);
explain format=tree select t1.pk, t2.pk, t3.pk, t4.pk, t5.pk
from t as t1
left join (
t as t2
straight_join t as t3 on t3.pk = t2.b
) on t2.pk = t1.a
left join (
t as t4
straight_join t as t5
) on t4.pk = t1.c and t5.pk = t3.d;
EXPLAIN
-> Nested loop left join
    -> Nested loop left join
        -> Table scan on t1, activating pushed join of 5 tables
        -> Nested loop inner join
            -> Single-row index lookup on t2 using PRIMARY (pk = t1.a), child of t1 in pushed join
            -> Single-row index lookup on t3 using PRIMARY (pk = t2.b), child of t2 in pushed join
    -> Nested loop inner join
        -> Single-row index lookup on t4 using PRIMARY (pk = t1.c), child of t3 in pushed join
        -> Single-row index lookup on t5 using PRIMARY (pk = t3.d), child of t3 in pushed join

select t1.pk, t2.pk, t3.pk, t4.pk, t5.pk
from t as t1
left join (
t as t2
straight_join t as t3 on t3.pk = t2.b
) on t2.pk = t1.a
left join (
t as t4
straight_join t as t5
) on t4.pk = t1.c and t5.pk = t3.d;
pk	pk	pk	pk	pk
0	0	0	0	0
1	1	1	1	1
10	10	10	10	10
11	11	11	11	11
12	12	12	12	12
13	13	13	13	13
14	14	14	14	14
15	15	15	15	15
2	2	2	2	2
3	3	3	3	3
4	4	4	4	4
5	5	5	5	5
6	6	6	6	6
7	NULL	NULL	NULL	NULL
8	8	8	8	8
9	9	9	9	9
drop table t;
##################
#
# Bug#34482783 NDB: mysqld crash in ::is_pushable_within_nest()
#
##################
create table t (
pk int primary key,
col_int_key int not null,
col_int int not null,
key(col_int_key)
) engine = ndbcluster;
explain format=tree SELECT *
FROM t AS table1
LEFT JOIN t AS table2
JOIN t AS table3 ON table2.col_int IS NULL  # <- 'zero rows'
  ON table1.col_int = table2.col_int AND
table1.col_int_key = table2.col_int_key AND
table1.pk = table3.pk;
EXPLAIN
-> Nested loop left join
    -> Table scan on table1
    -> Nested loop inner join
        -> Zero rows (Impossible filter)
        -> Index lookup on table2 using col_int_key (col_int_key = table1.col_int_key), with pushed condition: (table2.col_int = table1.col_int)

Warnings:
Note	1003	Can't push outer joined table 'table2' as child of 'table1', a table in its dependant join-nest(s) is not part of the pushed join
SELECT *
FROM t AS table1
LEFT JOIN t AS table2
JOIN t AS table3 ON table2.col_int IS NULL  # <- 'zero rows'
  ON table1.col_int = table2.col_int AND
table1.col_int_key = table2.col_int_key AND
table1.pk = table3.pk;
pk	col_int_key	col_int	pk	col_int_key	col_int	pk	col_int_key	col_int
drop table t;
##################
#
# Bug#34723413 A 'pushed join' should not start with a scan-table
#              returning very few rows .
#
##################
create table t10 (
k int not null auto_increment,
i int,
primary key(k),
key(i)
) engine = ndbcluster;
insert into t10(i) values (1),(2),(3),(4),(5),(6),(7),(8),(9),(0);
create table t100 like t10;
insert into t100(i)
select x.i from t10 as x, t10 as y;
create table t1000 like t10;
insert into t1000(i)
select x.i from t10 as x, t10 as y, t10 as z;
delete from t10 where i > 2;
update t100 set i = i/3;
update t1000 set i = i/3;
analyze table t10,t100,t1000;
Table	Op	Msg_type	Msg_text
test.t10	analyze	status	OK
test.t100	analyze	status	OK
test.t1000	analyze	status	OK
explain format=tree
select *
from t10
straight_join t1000 on t1000.i = t10.i
straight_join t100 on t100.k = t1000.k;
EXPLAIN
-> Nested loop inner join
    -> Nested loop inner join
        -> Table scan on t10, with pushed condition: (t10.i is not null)
        -> Index lookup on t1000 using i (i = t10.i), activating pushed join of 2 tables
    -> Single-row index lookup on t100 using PRIMARY (k = t1000.k), child of t1000 in pushed join

Warnings:
Note	1003	Didn't push table 't10' as root, too few rows to enable full parallelism
explain format=tree select *
from t10
straight_join t1000 on t1000.i = t10.i
straight_join t100 on t100.k = t10.k;
EXPLAIN
-> Nested loop inner join
    -> Nested loop inner join
        -> Table scan on t10, activating pushed join of 3 tables, with pushed condition: (t10.i is not null)
        -> Index lookup on t1000 using i (i = t10.i), child of t10 in pushed join
    -> Single-row index lookup on t100 using PRIMARY (k = t10.k), child of t10 in pushed join

drop table t10,t100,t1000;
##################
#
# Bug#34782276:
#   Improve SPJ-internal execution plan, evaluate inner-joins first
#
##################
create table t (
k int,
a int,
key ix1(k),
key ix2(a)
) engine = ndb;
insert into t values
(0,0), (1,1), (2,2),
(3,0), (4,1), (5,2),
(6,0), (7,1), (8,2);
explain format=tree select straight_join *
from t as t1
inner join t as t2 on t2.a = t1.k
inner join t as t3 on t3.k = t1.k;
EXPLAIN
-> Nested loop inner join
    -> Nested loop inner join
        -> Table scan on t1, activating pushed join of 3 tables, with pushed condition: (t1.k is not null)
        -> Index lookup on t2 using ix2 (a = t1.k), child of t1 in pushed join
    -> Index lookup on t3 using ix1 (k = t1.k), child of t1 in pushed join

select straight_join *
from t as t1
inner join t as t2 on t2.a = t1.k
inner join t as t3 on t3.k = t1.k;
############
# Verify the expect 21 rows were returned from SPJ:
# - 2 x 9 rows from t1 inner join t2
# - 3 rows from t1 join t3
# -> 21 rows (0 *SPJ* rows when not join-pushed (BKA))
#
SPJ_rows_returned
21
explain format=tree select straight_join *
from t as t1
inner join t as t2 on t2.a = t1.k
left join t as t3 on t3.k = t1.k;
EXPLAIN
-> Nested loop left join
    -> Nested loop inner join
        -> Table scan on t1, activating pushed join of 3 tables, with pushed condition: (t1.k is not null)
        -> Index lookup on t2 using ix2 (a = t1.k), child of t1 in pushed join
    -> Index lookup on t3 using ix1 (k = t1.k), child of t1 in pushed join

select straight_join *
from t as t1
inner join t as t2 on t2.a = t1.k
left join t as t3 on t3.k = t1.k;
############
# As above, t1 inner join t2 is evaluated first
# - 2 x 9 rows from join(t1,t2)
# - Only the 3 matching t1 rows are requested from t3!
# -> still 21 rows (and still 0 *SPJ* rows when not join-pushed (BKA))
#
SPJ_rows_returned
21
explain format=tree select straight_join *
from t as t1
left join t as t2 on t2.a = t1.k
inner join t as t3 on t3.k = t1.k;
EXPLAIN
-> Nested loop inner join
    -> Nested loop left join
        -> Table scan on t1, activating pushed join of 3 tables, with pushed condition: (t1.k is not null)
        -> Index lookup on t2 using ix2 (a = t1.k), child of t1 in pushed join
    -> Index lookup on t3 using ix1 (k = t1.k), child of t1 in pushed join

select straight_join *
from t as t1
left join t as t2 on t2.a = t1.k
inner join t as t3 on t3.k = t1.k;
############
# Verify that all 3 x 9 rows were returned from SPJ:
# - 2 x 9 rows from t1 outer join t2
#   (No t1-match elimination in an outer-join)
# - 9 rows from t1 inner join t3
# -> 27 rows
#
SPJ_rows_returned
27
drop table t;
select new.variable_name, new.variable_value - old.variable_value
from server_counts_at_startup as old,
performance_schema.global_status as new
where new.variable_name = old.variable_name
order by new.variable_name;
variable_name	new.variable_value - old.variable_value
Ndb_pruned_scan_count	8
Ndb_pushed_queries_defined	511
Ndb_pushed_queries_dropped	5
Ndb_sorted_scan_count	15
drop table server_counts_at_startup;
set ndb_join_pushdown = @save_ndb_join_pushdown;
