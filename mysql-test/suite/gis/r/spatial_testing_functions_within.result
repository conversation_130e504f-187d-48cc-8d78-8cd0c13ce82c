#Creating the spatial Geometry object
USE test;
CREATE TABLE gis_geometrycollection (fid INTEGER NOT NULL PRIMARY KEY, g GEOMETRY);
SET @star_elem_vertical= 'POLYGON((5 0,15 25,25 0,15 5,5 0))';
SET @star_elem_horizontal= 'POLYGON((25 0,0 15,30 15,22 10,25 0))';
SET @star_center= 'POINT(15 10)';
SET @star_top= 'POINT(15 25)';
SET @star_bottom_left= 'POINT(5 0)';
SET @star_bottom_right= 'POINT(25 0)';
SET @star_bottom_points= 'MULTIPOINT(5 0,25 0)';
SET @star_all_points= 'MULTIPOINT(5 0,25 0,15 10,15 25)';
SET @star_line_horizontal= 'LINESTRING(10 15,20 15)';
SET @star_line_vertical= 'LINESTRING(15 5,15 25)';
SET @star_top_to_center= 'LINESTRING(15 25,15 10)';
SET @star_lines_near_horizontal= 'MULTILINESTRING((25 0,0 15,15 30,0 5))';
SET @star_lines_near_vertical= 'MULTILINESTRING((0 5,15 25,0 25))';
SET @star= 'POLYGON((5 0,7 10,0 15,10 15,15 25,20 15,30 15,22 10,25 0,15 5,5 0))';
SET @star_elem_vertical_val= '((5 0,15 25,25 0,15 5,5 0))';
SET @star_elem_horizontal_val= '((25 0,0 15,30 15,22 10,25 0))';
SET @star_of_elems='MULTIPOLYGON(((5 0,15 25,25 0,15 5,5 0)),((25 0,0 15,30 15,22 10,25 0)))';
SET @star_collection_elems='GEOMETRYCOLLECTION(MULTIPOLYGON(((5 0,15 25,25 0,15 5,5 0)),((25 0,0 15,30 15,22 10,25 0))),POLYGON((5 0,7 10,0 15,10 15,15 25,20 15,30 15,22 10,25 0,15 5,5 0)),LINESTRING(15 25,15 10),MULTIPOINT(5 0,25 0),POINT(15 25))';
SET @star_collection_multilinestr='GEOMETRYCOLLECTION(MULTIPOLYGON(((5 0,15 25,25 0,15 5,5 0)),((25 0,0 15,30 15,22 10,25 0))),POLYGON((5 0,7 10,0 15,10 15,15 25,20 15,30 15,22 10,25 0,15 5,5 0)),MULTILINESTRING((25 0,0 15,15 30,0 5)),LINESTRING(15 25,15 10),MULTIPOINT(5 0,25 0),POINT(15 25))';
SET @star_elem_vertical_1= 'POLYGON((5 0,15 25,25 0,15 5,5 0))';
SET @star_elem_horizontal_1= 'POLYGON((25 0,0 15,30 15,22 10,25 0))';
SET @star_center_1= 'POINT(15 10)';
SET @star_top_1= 'POINT(15 25)';
SET @star_bottom_left_1= 'POINT(5 0)';
SET @star_bottom_right_1= 'POINT(25 0)';
SET @star_bottom_points_1= 'MULTIPOINT(5 0,25 0)';
SET @star_all_points_1= 'MULTIPOINT(5 0,25 0,15 10,15 25)';
SET @star_line_horizontal_1= 'LINESTRING(10 15,20 15)';
SET @star_line_vertical_1= 'LINESTRING(15 5,15 25)';
SET @star_top_to_center_1= 'LINESTRING(15 25,15 10)';
SET @star_lines_near_horizontal_1= 'MULTILINESTRING((25 0,0 15,15 30,0 5))';
SET @star_lines_near_vertical_1= 'MULTILINESTRING((0 5,15 25,0 25))';
SET @star_1= 'POLYGON((5 0,7 10,0 15,10 15,15 25,20 15,30 15,22 10,25 0,15 5,5 0))';
SET @star_elem_vertical_val_1= '((5 0,15 25,25 0,15 5,5 0))';
SET @star_elem_horizontal_val_1= '((25 0,0 15,30 15,22 10,25 0))';
SET @star_of_elems_1='MULTIPOLYGON(((5 0,15 25,25 0,15 5,5 0)),((25 0,0 15,30 15,22 10,25 0)))';
SET @star_collection_elems_1='GEOMETRYCOLLECTION(MULTIPOLYGON(((5 0,15 25,25 0,15 5,5 0)),((25 0,0 15,30 15,22 10,25 0))),POLYGON((5 0,7 10,0 15,10 15,15 25,20 15,30 15,22 10,25 0,15 5,5 0)),LINESTRING(15 25,15 10),MULTIPOINT(5 0,25 0),POINT(15 25))';
SET @star_elem_vertical_2= 'POLYGON((15 0,25 25,35 0,25 5,15 0))';
SET @star_elem_horizontal_2= 'POLYGON((35 0,10 15,40 15,32 10,35 0))';
SET @star_center_2= 'POINT(25 10)';
SET @star_top_2= 'POINT(25 25)';
SET @star_bottom_left_2= 'POINT(15 0)';
SET @star_bottom_right_2= 'POINT(35 0)';
SET @star_bottom_points_2= 'MULTIPOINT(15 0,35 0)';
SET @star_all_points_2= 'MULTIPOINT(15 0,35 0,35 10,25 25)';
SET @star_line_horizontal_2= 'LINESTRING(20 15,30 15)';
SET @star_line_vertical_2= 'LINESTRING(25 5,25 25)';
SET @star_top_to_center_2= 'LINESTRING(25 25,25 10)';
SET @star_lines_near_horizontal_2= 'MULTILINESTRING((35 0,10 15,25 30,10 5))';
SET @star_lines_near_vertical_2= 'MULTILINESTRING((10 5,25 25,10 25))';
SET @star_2= 'POLYGON((15 0,17 10,10 15,20 15,25 25,30 15,40 15,32 10,35 0,25 5,15 0))';
SET @star_elem_vertical_val_2= '((15 0,25 25,35 0,25 5,15 0))';
SET @star_elem_horizontal_val_2= '((35 0,10 15,40 15,32 10,35 0))';
SET @star_of_elems_2='MULTIPOLYGON(((15 0,25 25,35 0,25 5,15 0)),((35 0,10 15,40 15,32 10,35 0)))';
SET @star_collection_elems_2='GEOMETRYCOLLECTION(MULTIPOLYGON(((15 0,25 25,35 0,25 5,15 0)),((35 0,10 15,40 15,32 10,35 0))),POLYGON((15 0,17 10,10 15,20 15,25 25,30 15,40 15,32 10,35 0,25 5,15 0)),LINESTRING(25 25,25 10),MULTIPOINT(15 0,35 0),POINT(25 25))';
SET @star_elem_vertical_3= 'POLYGON((65 0,75 25,85 0,75 5,65 0))';
SET @star_elem_horizontal_3= 'POLYGON((85 0,60 15,90 15,82 10,85 0))';
SET @star_center_3= 'POINT(75 10)';
SET @star_top_3= 'POINT(75 25)';
SET @star_bottom_left_3= 'POINT(65 0)';
SET @star_bottom_right_3= 'POINT(85 0)';
SET @star_bottom_points_3= 'MULTIPOINT(65 0,85 0)';
SET @star_all_points_3= 'MULTIPOINT(65 0,85 0,75 10,75 25)';
SET @star_line_horizontal_3= 'LINESTRING(70 15,80 15)';
SET @star_line_vertical_3= 'LINESTRING(75 5,75 25)';
SET @star_top_to_center_3= 'LINESTRING(75 25,75 10)';
SET @star_lines_near_horizontal_3= 'MULTILINESTRING((85 0,60 15,85 30,60 5))';
SET @star_lines_near_vertical_3= 'MULTILINESTRING((60 5,75 25,60 25))';
SET @star_3= 'POLYGON((65 0,67 10,60 15,70 15,75 25,80 15,90 15,82 10,85 0,75 5,65 0))';
SET @star_elem_vertical_val_3= '((65 0,75 25,85 0,75 5,65 0))';
SET @star_elem_horizontal_val_3= '((85 0,60 15,90 15,82 10,85 0))';
SET @star_of_elems_3='MULTIPOLYGON(((65 0,75 25,85 0,75 5,65 0)),((85 0,60 15,90 15,82 10,85 0)))';
SET @star_collection_elems_3='GEOMETRYCOLLECTION(MULTIPOLYGON(((65 0,75 25,85 0,75 5,65 0)),((85 0,60 15,90 15,82 10,85 0))),POLYGON((65 0,67 10,60 15,70 15,75 25,80 15,90 15,82 10,85 0,75 5,65 0)),LINESTRING(75 25,75 10),MULTIPOINT(65 0,85 0),POINT(75 25))';
#INSERT base star
INSERT INTO gis_geometrycollection VALUES
(100,ST_GEOMFROMTEXT(@star)),
(101,ST_GEOMFROMTEXT(@star_elem_vertical)),
(102,ST_GEOMFROMTEXT(@star_elem_horizontal)),
(103,ST_GEOMFROMTEXT(@star_of_elems)),
(104,ST_GEOMFROMTEXT(@star_top)),
(105,ST_GEOMFROMTEXT(@star_center)),
(106,ST_GEOMFROMTEXT(@star_bottom_left)),
(107,ST_GEOMFROMTEXT(@star_bottom_right)),
(108,ST_GEOMFROMTEXT(@star_bottom_points)),
(109,ST_GEOMFROMTEXT(@star_all_points)),
(110,ST_GEOMFROMTEXT(@star_line_horizontal)),
(111,ST_GEOMFROMTEXT(@star_line_vertical)),
(112,ST_GEOMFROMTEXT(@star_top_to_center)),
(113,ST_GEOMFROMTEXT(@star_lines_near_horizontal)),
(114,ST_GEOMFROMTEXT(@star_lines_near_vertical)),
(115,ST_GEOMFROMTEXT(@star_collection_elems));
#INSERT identical (to base) star
INSERT INTO gis_geometrycollection VALUES
(200,ST_GEOMFROMTEXT(@star_1)),
(201,ST_GEOMFROMTEXT(@star_elem_vertical_1)),
(202,ST_GEOMFROMTEXT(@star_elem_horizontal_1)),
(203,ST_GEOMFROMTEXT(@star_of_elems_1)),
(204,ST_GEOMFROMTEXT(@star_top_1)),
(205,ST_GEOMFROMTEXT(@star_center_1)),
(206,ST_GEOMFROMTEXT(@star_bottom_left_1)),
(207,ST_GEOMFROMTEXT(@star_bottom_right_1)),
(208,ST_GEOMFROMTEXT(@star_bottom_points_1)),
(209,ST_GEOMFROMTEXT(@star_all_points_1)),
(210,ST_GEOMFROMTEXT(@star_line_horizontal_1)),
(211,ST_GEOMFROMTEXT(@star_line_vertical_1)),
(212,ST_GEOMFROMTEXT(@star_top_to_center_1)),
(213,ST_GEOMFROMTEXT(@star_lines_near_horizontal_1)),
(214,ST_GEOMFROMTEXT(@star_lines_near_vertical_1)),
(215,ST_GEOMFROMTEXT(@star_collection_elems_1));
#INSERT overlapping star
INSERT INTO gis_geometrycollection VALUES
(300,ST_GEOMFROMTEXT(@star_2)),
(301,ST_GEOMFROMTEXT(@star_elem_vertical_2)),
(302,ST_GEOMFROMTEXT(@star_elem_horizontal_2)),
(303,ST_GEOMFROMTEXT(@star_of_elems_2)),
(304,ST_GEOMFROMTEXT(@star_top_2)),
(305,ST_GEOMFROMTEXT(@star_center_2)),
(306,ST_GEOMFROMTEXT(@star_bottom_left_2)),
(307,ST_GEOMFROMTEXT(@star_bottom_right_2)),
(308,ST_GEOMFROMTEXT(@star_bottom_points_2)),
(309,ST_GEOMFROMTEXT(@star_all_points_2)),
(310,ST_GEOMFROMTEXT(@star_line_horizontal_2)),
(311,ST_GEOMFROMTEXT(@star_line_vertical_2)),
(312,ST_GEOMFROMTEXT(@star_top_to_center_2)),
(313,ST_GEOMFROMTEXT(@star_lines_near_horizontal_2)),
(314,ST_GEOMFROMTEXT(@star_lines_near_vertical_2)),
(315,ST_GEOMFROMTEXT(@star_collection_elems_2));
#INSERT separate star
INSERT INTO gis_geometrycollection VALUES
(400,ST_GEOMFROMTEXT(@star_3)),
(401,ST_GEOMFROMTEXT(@star_elem_vertical_3)),
(402,ST_GEOMFROMTEXT(@star_elem_horizontal_3)),
(403,ST_GEOMFROMTEXT(@star_of_elems_3)),
(404,ST_GEOMFROMTEXT(@star_top_3)),
(405,ST_GEOMFROMTEXT(@star_center_3)),
(406,ST_GEOMFROMTEXT(@star_bottom_left_3)),
(407,ST_GEOMFROMTEXT(@star_bottom_right_3)),
(408,ST_GEOMFROMTEXT(@star_bottom_points_3)),
(409,ST_GEOMFROMTEXT(@star_all_points_3)),
(410,ST_GEOMFROMTEXT(@star_line_horizontal_3)),
(411,ST_GEOMFROMTEXT(@star_line_vertical_3)),
(412,ST_GEOMFROMTEXT(@star_top_to_center_3)),
(413,ST_GEOMFROMTEXT(@star_lines_near_horizontal_3)),
(414,ST_GEOMFROMTEXT(@star_lines_near_vertical_3)),
(415,ST_GEOMFROMTEXT(@star_collection_elems_3));
CREATE TABLE gis_geometrycollection_2 SELECT fid as fid2,g as g2 FROM gis_geometrycollection;
#Checking the integrity of the above create/insert statements 
#64 rows. 
SELECT count(ST_ASTEXT(g) != 'NULL') FROM gis_geometrycollection;
count(ST_ASTEXT(g) != 'NULL')
64
#64 rows. 
SELECT count(ST_ASTEXT(g2) != 'NULL') FROM gis_geometrycollection_2;
count(ST_ASTEXT(g2) != 'NULL')
64
#####################################################################################
# MBRWITHIN(g1,g2)
#####################################################################################
#====================================================================================
# point,point
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_center),ST_GEOMFROMTEXT(@star_center));
MBRWITHIN(ST_GEOMFROMTEXT(@star_center),ST_GEOMFROMTEXT(@star_center))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_center));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_center))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=105;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_center))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POINT(0 0)'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POINT(0 0)'))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POINT(10 0)'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POINT(10 0)'))
0
#====================================================================================
# point,multipoint
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_bottom_left),ST_GEOMFROMTEXT(@star_bottom_points));
MBRWITHIN(ST_GEOMFROMTEXT(@star_bottom_left),ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_bottom_points));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=106;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0)'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0)'))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,10 0)'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,10 0)'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'))
0
#====================================================================================
# point,linestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_top_to_center));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_line_horizontal));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_line_horizontal))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=104;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0,20 0)'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0,20 0)'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'))
0
#====================================================================================
# point,multilinestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_lines_near_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_lines_near_horizontal));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_lines_near_horizontal))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=104;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,10 0))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,10 0))'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(10 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0),(5 0,10 0))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(10 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0),(5 0,10 0))'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0,10 0))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0,10 0))'))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0),(5 0,10 0))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0),(5 0,10 0))'))
0
#====================================================================================
# point,polygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_center),ST_GEOMFROMTEXT(@star));
MBRWITHIN(ST_GEOMFROMTEXT(@star_center),ST_GEOMFROMTEXT(@star))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_elem_horizontal));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_elem_horizontal))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_elem_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star)) FROM gis_geometrycollection WHERE fid=104;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4))'))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'))
0
#====================================================================================
# point,multipolygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_of_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(-0.01 0)'),ST_GEOMFROMTEXT(@star_of_elems));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(-0.01 0)'),ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=104;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)))'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)),((10 10,10 20,20 20,20 10,10 10)))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)),((10 10,10 20,20 20,20 10,10 10)))'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))'))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))'))
0
#====================================================================================
# point,geometrycollection
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_collection_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(-0.01 0)'),ST_GEOMFROMTEXT(@star_collection_elems));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(-0.01 0)'),ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=104;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_collection_multilinestr));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_collection_multilinestr))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'))
NULL
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(5 5))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(5 5))'))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(0 0,5 5,10 10),GEOMETRYCOLLECTION())'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(0 0,5 5,10 10),GEOMETRYCOLLECTION())'))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,10 0,10 10,0 10,0 0))))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,10 0,10 10,0 10,0 0))))'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,10 0,10 10,0 10,0 0))))'));
MBRWITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,10 0,10 10,0 10,0 0))))'))
0
#====================================================================================
# multipoint,point
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_top));
MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_top))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_bottom_points),ST_GEOMFROMTEXT(@star_center));
MBRWITHIN(ST_GEOMFROMTEXT(@star_bottom_points),ST_GEOMFROMTEXT(@star_center))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_left)) FROM gis_geometrycollection WHERE fid=108;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_left))
0
#====================================================================================
# multipoint,multipoint
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_bottom_points));
MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_bottom_points),ST_GEOMFROMTEXT(@star_all_points));
MBRWITHIN(ST_GEOMFROMTEXT(@star_bottom_points),ST_GEOMFROMTEXT(@star_all_points))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=208;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_all_points))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=308;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_all_points))
0
#====================================================================================
# multipoint,linestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_top_to_center));
MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=109;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=209;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=309;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
#====================================================================================
# multipoint,multilinestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_lines_near_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_lines_near_horizontal));
MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_lines_near_horizontal))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_horizontal)) FROM gis_geometrycollection WHERE fid=109;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_horizontal))
1
#====================================================================================
# multipoint,polygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_elem_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_elem_vertical))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_elem_horizontal));
MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_elem_horizontal))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=109;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_horizontal))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=209;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_horizontal))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=309;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_horizontal))
0
#====================================================================================
# multipoint,multipolygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 30,30 25)'),ST_GEOMFROMTEXT(@star_of_elems));
MBRWITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 30,30 25)'),ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_of_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_of_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=109;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=209;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=309;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
#====================================================================================
# multipoint,geometrycollection
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 30,30 25)'),ST_GEOMFROMTEXT(@star_collection_elems));
MBRWITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 30,30 25)'),ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_collection_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=109;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=209;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=309;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_collection_multilinestr));
MBRWITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_collection_multilinestr))
1
#====================================================================================
# linestring,point
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_center));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_center))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_bottom_left));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_bottom_left))
0
SELECT MBRWITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=110 and fid2=105;
MBRWITHIN(g,g2)
0
#====================================================================================
# linestring,multipoint
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT(@star_all_points));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT(@star_all_points))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)')) FROM gis_geometrycollection WHERE fid=111;
MBRWITHIN(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)')) FROM gis_geometrycollection WHERE fid=311;
MBRWITHIN(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)')) FROM gis_geometrycollection WHERE fid=411;
MBRWITHIN(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)'))
0
#====================================================================================
# linestring,linestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT(@star_top_to_center));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_line_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_line_vertical))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=111;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=311;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=411;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
#====================================================================================
# linestring,multilinestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_lines_near_horizontal));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_lines_near_horizontal))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_lines_near_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_horizontal)) FROM gis_geometrycollection WHERE fid=110;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_horizontal))
1
#====================================================================================
# linestring,polygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT('POLYGON((11 15,19 15,19 10,11 15))'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT('POLYGON((11 15,19 15,19 10,11 15))'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal_2));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal_2))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal_3));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal_3))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT('POLYGON((11 15,19 15,11 15))')) FROM gis_geometrycollection WHERE fid=110;
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
#====================================================================================
# linestring,multipolygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT('MULTIPOLYGON(((11 15,19 15,11 15)),((25 0,0 15,25 0)))'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems_2));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems_2))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems_3));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems_3))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT('MULTIPOLYGON(((11 15,19 15,11 15)),((25 0,0 15,25 0)))')) FROM gis_geometrycollection WHERE fid=110;
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
#====================================================================================
# linestring,geometrycollection
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems_2));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems_2))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems_3));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems_3))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=110;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_multilinestr));
MBRWITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_multilinestr))
1
#====================================================================================
# multilinestring,point
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_top));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_top))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center_2));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center_2))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center_3));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center_3))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=114;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top))
0
#====================================================================================
# multilinestring,multipoint
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_bottom_points));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_all_points));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_all_points))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=114;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT MBRWITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=114 and fid2=108;
MBRWITHIN(g,g2)
0
#====================================================================================
# multilinestring,linestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal_2));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal_2))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal_3));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal_3))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=114;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical))
0
#====================================================================================
# multilinestring,multilinestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT('MULTILINESTRING((0 5,15 25,0 15))'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT('MULTILINESTRING((0 5,15 25,0 15))'))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal_3));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal_3))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT('MULTILINESTRING((0 5,15 25,0 15))')) FROM gis_geometrycollection WHERE fid=114;
MBRWITHIN(g,ST_GEOMFROMTEXT('MULTILINESTRING((0 5,15 25,0 15))'))
1
#====================================================================================
# multilinestring,polygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal_2));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal_2))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal_3));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal_3))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=114;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
#====================================================================================
# multilinestring,multipolygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT('MULTILINESTRING((0 25,25 0,25 30))'),ST_GEOMFROMTEXT(@star_elem_vertical));
MBRWITHIN(ST_GEOMFROMTEXT('MULTILINESTRING((0 25,25 0,25 30))'),ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_of_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_of_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=114;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
1
#====================================================================================
# multilinestring,geometrycollection
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT('MULTILINESTRING((0 25,25 0,25 30))'),ST_GEOMFROMTEXT(@star_collection_elems));
MBRWITHIN(ST_GEOMFROMTEXT('MULTILINESTRING((0 25,25 0,25 30))'),ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_collection_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=114;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_collection_multilinestr));
MBRWITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_collection_multilinestr))
1
#====================================================================================
# polygon,point
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_bottom_left));
MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_bottom_left))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT('POINT(0 0)'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT('POINT(0 0)'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=100;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=200;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_center))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=300;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_center))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=400;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_center))
0
#====================================================================================
# polygon,multipoint
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_all_points));
MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_all_points))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT('MULTIPOINT(0 0,30 25)'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT('MULTIPOINT(0 0,30 25)'))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=100;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_all_points))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=300;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_all_points))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=400;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_all_points))
0
#====================================================================================
# polygon,linestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_line_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_line_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_line_horizontal));
MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_line_horizontal))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=100;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=300;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=400;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical))
0
SELECT MBRWITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=100 and fid2=111;
MBRWITHIN(g,g2)
0
#====================================================================================
# polygon,multilinestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_lines_near_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal));
MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=100;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=300;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=400;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
#====================================================================================
# polygon,polygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_elem_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star));
MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT('POLYGON((25 0,25 5,30 0,25 0))')) FROM gis_geometrycollection WHERE fid=100;
MBRWITHIN(g,ST_GEOMFROMTEXT('POLYGON((25 0,25 5,30 0,25 0))'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT('POLYGON((25 15,25 20,30 15,25 15))')) FROM gis_geometrycollection WHERE fid=100;
MBRWITHIN(g,ST_GEOMFROMTEXT('POLYGON((25 15,25 20,30 15,25 15))'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=400;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
#====================================================================================
# polygon,multipolygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_of_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_of_elems))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_of_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_of_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT('MULTIPOLYGON(((25 0,25 5,30 0,25 0)),((25 15,25 20,30 15,25 15)))')) FROM gis_geometrycollection WHERE fid=100;
MBRWITHIN(g,ST_GEOMFROMTEXT('MULTIPOLYGON(((25 0,25 5,30 0,25 0)),((25 15,25 20,30 15,25 15)))'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=300;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=400;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
#====================================================================================
# polygon,geometrycollection
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_collection_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_collection_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=300;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=400;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_collection_multilinestr));
MBRWITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_collection_multilinestr))
1
#====================================================================================
# multipolygon,point
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POINT(30 30)'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POINT(30 30)'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=103;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=403;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top))
0
#====================================================================================
# multipolygon,multipoint
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_bottom_points));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOINT(30 30,0 35)'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOINT(30 30,0 35)'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=103;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=403;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points))
0
#====================================================================================
# multipolygon,linestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top_to_center));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('LINESTRING(30 30,0 35)'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('LINESTRING(30 30,0 35)'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=103;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=403;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
#====================================================================================
# multipolygon,multilinestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_lines_near_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTILINESTRING((0 30,30 30,0 35))'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTILINESTRING((0 30,30 30,0 35))'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=103;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=403;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
#====================================================================================
# multipolygon,polygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_elem_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POLYGON((25 0,25 5,30 0,25 0))'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POLYGON((25 0,25 5,30 0,25 0))'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POLYGON((0 0,15 25,35 0,0 0,0 0))'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POLYGON((0 0,15 25,35 0,0 0,0 0))'))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=103;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=303;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=403;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
#====================================================================================
# multipolygon,multipolygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((25 0,25 5,30 0,25 0)),((25 15,25 20,30 15,25 15)))'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((25 0,25 5,30 0,25 0)),((25 15,25 20,30 15,25 15)))'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,15 25,35 0,0 0,0 0),(5 0,15 25,25 0,15 5,5 0)))'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,15 25,35 0,0 0,0 0),(5 0,15 25,25 0,15 5,5 0)))'))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=103;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=303;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=403;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT MBRWITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;
MBRWITHIN(g,g2)
1
#====================================================================================
# multipolygon,geometrycollection
#====================================================================================
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=103;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=303;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=403;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=115;
MBRWITHIN(g,g2)
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_collection_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_collection_elems))
1
#====================================================================================
# geometrycollection,point
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_top));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_top))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POINT(30 30)'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POINT(30 30)'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=115;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=415;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top))
0
#====================================================================================
# geometrycollection,multipoint
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_bottom_points));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOINT(30 30,0 35)'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOINT(30 30,0 35)'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=115;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=415;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points))
0
#====================================================================================
# geometrycollection,linestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_top_to_center));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('LINESTRING(30 30,0 35)'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('LINESTRING(30 30,0 35)'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=115;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=415;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
#====================================================================================
# geometrycollection,multilinestring
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_lines_near_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTILINESTRING((0 30,30 30,0 35))'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTILINESTRING((0 30,30 30,0 35))'))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=115;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=415;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
#====================================================================================
# geometrycollection,polygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_elem_vertical));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POLYGON((25 0,25 5,30 0,25 0))'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POLYGON((25 0,25 5,30 0,25 0))'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POLYGON((0 0,15 25,35 0,0 0,0 0))'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POLYGON((0 0,15 25,35 0,0 0,0 0))'))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=115;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=315;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=415;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
#====================================================================================
# geometrycollection,multipolygon
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((25 0,25 5,30 0,25 0)),((25 15,25 20,30 15,25 15)))'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((25 0,25 5,30 0,25 0)),((25 15,25 20,30 15,25 15)))'))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,15 25,35 0,0 0,0 0),(5 0,15 25,25 0,15 5,5 0)))'));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,15 25,35 0,0 0,0 0),(5 0,15 25,25 0,15 5,5 0)))'))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=115;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=315;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=415;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=115 and fid2=103;
MBRWITHIN(g,g2)
1
#====================================================================================
# geometrycollection,geometrycollection
#====================================================================================
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=115;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=315;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=415;
MBRWITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT MBRWITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=115 and fid2=115;
MBRWITHIN(g,g2)
1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_collection_elems));
MBRWITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_collection_elems))
1
#####################################################################################
# ST_WITHIN(g1,g2)
#####################################################################################
#====================================================================================
# point,point
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_center),ST_GEOMFROMTEXT(@star_center));
ST_WITHIN(ST_GEOMFROMTEXT(@star_center),ST_GEOMFROMTEXT(@star_center))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_center));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_center))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=105;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_center))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POINT(0 0)'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POINT(0 0)'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POINT(10 0)'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POINT(10 0)'))
0
#====================================================================================
# point,multipoint
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_bottom_left),ST_GEOMFROMTEXT(@star_bottom_points));
ST_WITHIN(ST_GEOMFROMTEXT(@star_bottom_left),ST_GEOMFROMTEXT(@star_bottom_points))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_bottom_points));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=106;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0)'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0)'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,10 0)'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,10 0)'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,10 0)'))
0
#====================================================================================
# point,linestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_top_to_center));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_line_horizontal));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_line_horizontal))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=104;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0,20 0)'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0,20 0)'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 0)'))
0
#====================================================================================
# point,multilinestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_lines_near_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_lines_near_vertical))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_lines_near_horizontal));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_lines_near_horizontal))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=104;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,10 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,10 0))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(10 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0),(5 0,10 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(10 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0),(5 0,10 0))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0,10 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0,10 0))'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0),(5 0,10 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 0)'),ST_GEOMFROMTEXT('MULTILINESTRING((0 0,5 0),(5 0,10 0))'))
0
#====================================================================================
# point,polygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_center),ST_GEOMFROMTEXT(@star));
ST_WITHIN(ST_GEOMFROMTEXT(@star_center),ST_GEOMFROMTEXT(@star))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_elem_horizontal));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_elem_horizontal))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_elem_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star)) FROM gis_geometrycollection WHERE fid=104;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 0)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'))
0
#====================================================================================
# point,multipolygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_of_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(-0.01 0)'),ST_GEOMFROMTEXT(@star_of_elems));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(-0.01 0)'),ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=104;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)),((10 10,10 20,20 20,20 10,10 10)))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)),((10 10,10 20,20 20,20 10,10 10)))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0)))'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,10 0,10 10,0 10,0 0),(4 4,4 6,6 6,6 4,4 4)))'))
0
#====================================================================================
# point,geometrycollection
#====================================================================================
# Invalid geometry in input.
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_collection_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_collection_elems))
1
# Invalid geometry in input.
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(-0.01 0)'),ST_GEOMFROMTEXT(@star_collection_elems));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(-0.01 0)'),ST_GEOMFROMTEXT(@star_collection_elems))
0
# Invalid geometry in input.
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=104;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
# Invalid geometry in input.
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_collection_multilinestr));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_collection_multilinestr))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'))
NULL
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(5 5))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(5 5))'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(0 0,5 5,10 10),GEOMETRYCOLLECTION())'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(5 5)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(0 0,5 5,10 10),GEOMETRYCOLLECTION())'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,10 0,10 10,0 10,0 0))))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(0 0)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,10 0,10 10,0 10,0 0))))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,10 0,10 10,0 10,0 0))))'));
ST_WITHIN(ST_GEOMFROMTEXT('POINT(20 20)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,10 0,10 10,0 10,0 0))))'))
0
#====================================================================================
# multipoint,point
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_top));
ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_top))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_bottom_points),ST_GEOMFROMTEXT(@star_center));
ST_WITHIN(ST_GEOMFROMTEXT(@star_bottom_points),ST_GEOMFROMTEXT(@star_center))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_left)) FROM gis_geometrycollection WHERE fid=108;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_left))
0
#====================================================================================
# multipoint,multipoint
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_bottom_points));
ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_bottom_points),ST_GEOMFROMTEXT(@star_all_points));
ST_WITHIN(ST_GEOMFROMTEXT(@star_bottom_points),ST_GEOMFROMTEXT(@star_all_points))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=208;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_all_points))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=308;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_all_points))
0
#====================================================================================
# multipoint,linestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_top_to_center));
ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=109;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=209;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=309;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
#====================================================================================
# multipoint,multilinestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_lines_near_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_lines_near_horizontal));
ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_lines_near_horizontal))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_horizontal)) FROM gis_geometrycollection WHERE fid=109;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_horizontal))
0
#====================================================================================
# multipoint,polygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_elem_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_elem_vertical))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_elem_horizontal));
ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_elem_horizontal))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=109;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_horizontal))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=209;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_horizontal))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_horizontal)) FROM gis_geometrycollection WHERE fid=309;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_horizontal))
0
#====================================================================================
# multipoint,multipolygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 30,30 25)'),ST_GEOMFROMTEXT(@star_of_elems));
ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 30,30 25)'),ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_of_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_of_elems))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=109;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=209;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=309;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
#====================================================================================
# multipoint,geometrycollection
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 30,30 25)'),ST_GEOMFROMTEXT(@star_collection_elems));
ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 30,30 25)'),ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_collection_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=109;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=209;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=309;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_collection_multilinestr));
ST_WITHIN(ST_GEOMFROMTEXT(@star_all_points),ST_GEOMFROMTEXT(@star_collection_multilinestr))
1
#====================================================================================
# linestring,point
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_center));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_center))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_bottom_left));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_bottom_left))
0
SELECT ST_WITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=110 and fid2=105;
ST_WITHIN(g,g2)
0
#====================================================================================
# linestring,multipoint
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT(@star_all_points));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT(@star_all_points))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)')) FROM gis_geometrycollection WHERE fid=111;
ST_WITHIN(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)')) FROM gis_geometrycollection WHERE fid=311;
ST_WITHIN(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)')) FROM gis_geometrycollection WHERE fid=411;
ST_WITHIN(g,ST_GEOMFROMTEXT('MULTIPOINT(15 10,15 25)'))
0
#====================================================================================
# linestring,linestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT(@star_top_to_center));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_vertical),ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_line_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top_to_center),ST_GEOMFROMTEXT(@star_line_vertical))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=111;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=311;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=411;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
#====================================================================================
# linestring,multilinestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_lines_near_horizontal));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_lines_near_horizontal))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_lines_near_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_horizontal)) FROM gis_geometrycollection WHERE fid=110;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_horizontal))
0
#====================================================================================
# linestring,polygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT('POLYGON((11 15,19 15,19 10,11 15))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT('POLYGON((11 15,19 15,19 10,11 15))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal_2));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal_2))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal_3));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_elem_horizontal_3))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT('POLYGON((11 15,19 15,19 25,11 15,11 15))')) FROM gis_geometrycollection WHERE fid=110;
ST_WITHIN(g,ST_GEOMFROMTEXT('POLYGON((11 15,19 15,19 25,11 15,11 15))'))
0
#====================================================================================
# linestring,multipolygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT('MULTIPOLYGON(((11 15,19 15,19 25,11 15,11 15)),((25 0,0 15,25 10,25 0)))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT('MULTIPOLYGON(((11 15,19 15,19 25,11 15,11 15)),((25 0,0 15,25 10,25 0)))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems_2));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems_2))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems_3));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_of_elems_3))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT('MULTIPOLYGON(((11 15,19 15,19 25,11 15,11 15)),((25 0,0 15,25 10,25 0)))')) FROM gis_geometrycollection WHERE fid=110;
ST_WITHIN(g,ST_GEOMFROMTEXT('MULTIPOLYGON(((11 15,19 15,19 25,11 15,11 15)),((25 0,0 15,25 10,25 0)))'))
0
#====================================================================================
# linestring,geometrycollection
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems_2));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems_2))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems_3));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_elems_3))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=110;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_multilinestr));
ST_WITHIN(ST_GEOMFROMTEXT(@star_line_horizontal),ST_GEOMFROMTEXT(@star_collection_multilinestr))
1
#====================================================================================
# multilinestring,point
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_top));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_top))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center_2));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center_2))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center_3));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_center_3))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=114;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top))
0
#====================================================================================
# multilinestring,multipoint
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_bottom_points));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_all_points));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_all_points))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=114;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT ST_WITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=114 and fid2=108;
ST_WITHIN(g,g2)
0
#====================================================================================
# multilinestring,linestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_vertical))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal_2));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal_2))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal_3));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_line_horizontal_3))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=114;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical))
0
#====================================================================================
# multilinestring,multilinestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT('MULTILINESTRING((0 5,15 25,0 15))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT('MULTILINESTRING((0 5,15 25,0 15))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal_3));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal_3))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT('MULTILINESTRING((0 5,15 25,0 15))')) FROM gis_geometrycollection WHERE fid=114;
ST_WITHIN(g,ST_GEOMFROMTEXT('MULTILINESTRING((0 5,15 25,0 15))'))
0
#====================================================================================
# multilinestring,polygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal_2));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal_2))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal_3));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_elem_horizontal_3))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=114;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTILINESTRING((4 10, 3 10, 10 6),(5 0, 7 5, 9 10))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,5 5,0 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('MULTILINESTRING((4 10, 3 10, 10 6),(5 0, 7 5, 9 10))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,5 5,0 0))'))
1
#====================================================================================
# multilinestring,multipolygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTILINESTRING((0 25,25 0,25 30))'),ST_GEOMFROMTEXT(@star_elem_vertical));
ST_WITHIN(ST_GEOMFROMTEXT('MULTILINESTRING((0 25,25 0,25 30))'),ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_of_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=114;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
#====================================================================================
# multilinestring,geometrycollection
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTILINESTRING((0 25,25 0,25 30))'),ST_GEOMFROMTEXT(@star_collection_elems));
ST_WITHIN(ST_GEOMFROMTEXT('MULTILINESTRING((0 25,25 0,25 30))'),ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_collection_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=114;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_collection_multilinestr));
ST_WITHIN(ST_GEOMFROMTEXT(@star_lines_near_vertical),ST_GEOMFROMTEXT(@star_collection_multilinestr))
0
#====================================================================================
# polygon,point
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_bottom_left));
ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_bottom_left))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT('POINT(0 0)'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT('POINT(0 0)'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=100;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=200;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_center))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=300;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_center))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_center)) FROM gis_geometrycollection WHERE fid=400;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_center))
0
#====================================================================================
# polygon,multipoint
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_all_points));
ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_all_points))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT('MULTIPOINT(0 0,30 25)'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT('MULTIPOINT(0 0,30 25)'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=100;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_all_points))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=300;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_all_points))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_all_points)) FROM gis_geometrycollection WHERE fid=400;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_all_points))
0
#====================================================================================
# polygon,linestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_line_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_line_vertical))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_line_horizontal));
ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_line_horizontal))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=100;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=300;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical)) FROM gis_geometrycollection WHERE fid=400;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_line_vertical))
0
SELECT ST_WITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=100 and fid2=111;
ST_WITHIN(g,g2)
0
#====================================================================================
# polygon,multilinestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_lines_near_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal));
ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_lines_near_horizontal))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=100;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=300;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=400;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
#====================================================================================
# polygon,polygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_elem_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star));
ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT('POLYGON((25 0,25 5,30 0,25 0))')) FROM gis_geometrycollection WHERE fid=100;
ST_WITHIN(g,ST_GEOMFROMTEXT('POLYGON((25 0,25 5,30 0,25 0))'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT('POLYGON((25 15,25 20,30 15,25 15))')) FROM gis_geometrycollection WHERE fid=100;
ST_WITHIN(g,ST_GEOMFROMTEXT('POLYGON((25 15,25 20,30 15,25 15))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2))'));
ST_WITHIN(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))'));
ST_WITHIN(ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=400;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
#====================================================================================
# polygon,multipolygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_of_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_of_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT('MULTIPOLYGON(((25 0,25 5,30 0,25 0)),((25 15,25 20,30 15,25 15)))')) FROM gis_geometrycollection WHERE fid=100;
ST_WITHIN(g,ST_GEOMFROMTEXT('MULTIPOLYGON(((25 0,25 5,30 0,25 0)),((25 15,25 20,30 15,25 15)))'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=300;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=400;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
#====================================================================================
# polygon,geometrycollection
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_collection_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_collection_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star_elem_vertical),ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elem)) FROM gis_geometrycollection WHERE fid=100;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elem))
NULL
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=300;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=400;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_collection_multilinestr));
ST_WITHIN(ST_GEOMFROMTEXT(@star),ST_GEOMFROMTEXT(@star_collection_multilinestr))
1
#====================================================================================
# multipolygon,point
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POINT(30 30)'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POINT(30 30)'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=103;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=403;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top))
0
#====================================================================================
# multipolygon,multipoint
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_bottom_points));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOINT(30 30,0 35)'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOINT(30 30,0 35)'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=103;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=403;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points))
0
#====================================================================================
# multipolygon,linestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top_to_center));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('LINESTRING(30 30,0 35)'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('LINESTRING(30 30,0 35)'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=103;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=403;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
#====================================================================================
# multipolygon,multilinestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_lines_near_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTILINESTRING((0 30,30 30,0 35))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTILINESTRING((0 30,30 30,0 35))'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=103;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=403;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
#====================================================================================
# multipolygon,polygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_elem_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POLYGON((25 0,25 5,30 0,25 0))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POLYGON((25 0,25 5,30 0,25 0))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POLYGON((0 0,15 25,35 0,0 0,0 0))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('POLYGON((0 0,15 25,35 0,0 0,0 0))'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=103;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=303;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=403;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
#====================================================================================
# multipolygon,multipolygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((25 0,25 5,30 0,25 0)),((25 15,25 20,30 15,25 15)))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((25 0,25 5,30 0,25 0)),((25 15,25 20,30 15,25 15)))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,15 25,35 0,0 0,0 0),(5 0,15 25,25 0,15 5,5 0)))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,15 25,35 0,0 0,0 0),(5 0,15 25,25 0,15 5,5 0)))'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=103;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=303;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems)) FROM gis_geometrycollection WHERE fid=403;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_of_elems))
0
SELECT ST_WITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;
ST_WITHIN(g,g2)
0
#====================================================================================
# multipolygon,geometrycollection
#====================================================================================
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=103;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=303;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=403;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=115;
ST_WITHIN(g,g2)
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_collection_multilinestr));
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_collection_multilinestr))
1
#====================================================================================
# geometrycollection,point
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_top));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_top))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POINT(30 30)'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POINT(30 30)'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=115;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection WHERE fid=415;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top))
0
#====================================================================================
# geometrycollection,multipoint
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_bottom_points));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOINT(30 30,0 35)'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOINT(30 30,0 35)'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=115;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points)) FROM gis_geometrycollection WHERE fid=415;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_bottom_points))
0
#====================================================================================
# geometrycollection,linestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_top_to_center));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('LINESTRING(30 30,0 35)'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('LINESTRING(30 30,0 35)'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=115;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center)) FROM gis_geometrycollection WHERE fid=415;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_top_to_center))
0
#====================================================================================
# geometrycollection,multilinestring
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_lines_near_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTILINESTRING((0 30,30 30,0 35))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTILINESTRING((0 30,30 30,0 35))'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=115;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical)) FROM gis_geometrycollection WHERE fid=415;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_lines_near_vertical))
0
#====================================================================================
# geometrycollection,polygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_elem_vertical));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POLYGON((25 0,25 5,30 0,25 0))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POLYGON((25 0,25 5,30 0,25 0))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POLYGON((0 0,15 25,35 0,0 0,0 0))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('POLYGON((0 0,15 25,35 0,0 0,0 0))'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=115;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=315;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical)) FROM gis_geometrycollection WHERE fid=415;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_elem_vertical))
0
#====================================================================================
# geometrycollection,multipolygon
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((25 0,25 5,30 0,25 0)),((25 15,25 20,30 15,25 15)))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((25 0,25 5,30 0,25 0)),((25 15,25 20,30 15,25 15)))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,15 25,35 0,0 0,0 0),(5 0,15 25,25 0,15 5,5 0)))'));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,15 25,35 0,0 0,0 0),(5 0,15 25,25 0,15 5,5 0)))'))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=115;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=315;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=415;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=115 and fid2=103;
ST_WITHIN(g,g2)
0
#====================================================================================
# geometrycollection,geometrycollection
#====================================================================================
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=115;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
1
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=315;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems)) FROM gis_geometrycollection WHERE fid=415;
ST_WITHIN(g,ST_GEOMFROMTEXT(@star_collection_elems))
0
SELECT ST_WITHIN(g,g2) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=115 and fid2=115;
ST_WITHIN(g,g2)
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_collection_elems));
ST_WITHIN(ST_GEOMFROMTEXT(@star_collection_elems),ST_GEOMFROMTEXT(@star_collection_elems))
1
#####################################################################################
# Testing with 2 geometries of same SRID
#####################################################################################
#====================================================================================
# MBRWITHIN(g1,g2)
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top,0),ST_GEOMFROMTEXT(@star_center,0));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top,0),ST_GEOMFROMTEXT(@star_center,0))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4145));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4145))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top,4326),ST_GEOMFROMTEXT(@star_center,4326));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top,4326),ST_GEOMFROMTEXT(@star_center,4326))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top,4135),ST_GEOMFROMTEXT(@star_center,4135));
MBRWITHIN(ST_GEOMFROMTEXT(@star_top,4135),ST_GEOMFROMTEXT(@star_center,4135))
0
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top,-1),ST_GEOMFROMTEXT(@star_center,-1));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top,-1024),ST_GEOMFROMTEXT(@star_center,-1024));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top,4294967295000),ST_GEOMFROMTEXT(@star_center,4294967295000));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
#====================================================================================
# ST_WITHIN(g1,g2)
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top,0),ST_GEOMFROMTEXT(@star_center,0));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top,0),ST_GEOMFROMTEXT(@star_center,0))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4145));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4145))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4326),ST_GEOMFROMTEXT(@star_center,4326));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4326),ST_GEOMFROMTEXT(@star_center,4326))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4135),ST_GEOMFROMTEXT(@star_center,4135));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4135),ST_GEOMFROMTEXT(@star_center,4135))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4145));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4145))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4326),ST_GEOMFROMTEXT(@star_center,4326));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4326),ST_GEOMFROMTEXT(@star_center,4326))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4135),ST_GEOMFROMTEXT(@star_center,4135));
ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4135),ST_GEOMFROMTEXT(@star_center,4135))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top,-1),ST_GEOMFROMTEXT(@star_center,-1));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top,-1024),ST_GEOMFROMTEXT(@star_center,-1024));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4294967295000),ST_GEOMFROMTEXT(@star_center,4294967295000));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
#####################################################################################
# Invalid function calls
#####################################################################################
#====================================================================================
# ST_WITHIN(g1,g2)
#====================================================================================
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_center,4326));
ERROR HY000: Binary geometry function st_within given two geometries of different srids: 0 and 4326, which should have been identical.
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4326));
ERROR HY000: Binary geometry function st_within given two geometries of different srids: 4145 and 4326, which should have been identical.
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4326));
ERROR HY000: Binary geometry function st_within given two geometries of different srids: 4145 and 4326, which should have been identical.
SELECT ST_WITHIN(NULL,ST_GEOMFROMTEXT(@star_top));
ST_WITHIN(NULL,ST_GEOMFROMTEXT(@star_top))
NULL
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),NULL);
ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),NULL)
NULL
SELECT ST_WITHIN(g,NULL) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;
ST_WITHIN(g,NULL)
NULL
SELECT ST_WITHIN(NULL,NULL);
ST_WITHIN(NULL,NULL)
NULL
SELECT ST_WITHIN(fid,NULL) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;
ST_WITHIN(fid,NULL)
NULL
SELECT ST_WITHIN(fid,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;
ERROR 22023: Invalid GIS data provided to function st_within.
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),fid) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;
ERROR 22023: Invalid GIS data provided to function st_within.
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(a 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(! 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT('!' 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '!' 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'))' at line 1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POINT(12,34 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_WITHIN(ST_GEOMFROMTEXT('LINESTRING(a 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_WITHIN(ST_GEOMFROMTEXT('LINESTRING(0 0,! 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_WITHIN(ST_GEOMFROMTEXT('LINESTRING('!' 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '!' 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'))' at line 1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('LINESTRING(12,34 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POLYGON((a 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POLYGON((0 0,! 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POLYGON(('!' 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '!' 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'))' at line 1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POLYGON((12,34 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POLYGON((12,34 0,10 10,10 0,0 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_WITHIN(,);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ',)' at line 1
SELECT ST_WITHIN(ST_GEOMFROMTEXT(@star_of_elems),);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
SELECT ST_WITHIN(,ST_GEOMFROMTEXT(@star_top));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ',ST_GEOMFROMTEXT(@star_top))' at line 1
#====================================================================================
# MBRWITHIN(g1,g2)
#====================================================================================
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_center,4326));
ERROR HY000: Binary geometry function mbrwithin given two geometries of different srids: 0 and 4326, which should have been identical.
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4326));
ERROR HY000: Binary geometry function mbrwithin given two geometries of different srids: 4145 and 4326, which should have been identical.
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_top,4145),ST_GEOMFROMTEXT(@star_center,4326));
ERROR HY000: Binary geometry function mbrwithin given two geometries of different srids: 4145 and 4326, which should have been identical.
SELECT MBRWITHIN(NULL,ST_GEOMFROMTEXT(@star_top));
MBRWITHIN(NULL,ST_GEOMFROMTEXT(@star_top))
NULL
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),NULL);
MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),NULL)
NULL
SELECT MBRWITHIN(g,NULL) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;
MBRWITHIN(g,NULL)
NULL
SELECT MBRWITHIN(NULL,NULL);
MBRWITHIN(NULL,NULL)
NULL
SELECT MBRWITHIN(fid,NULL) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;
MBRWITHIN(fid,NULL)
NULL
SELECT MBRWITHIN(fid,ST_GEOMFROMTEXT(@star_top)) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;
ERROR 22023: Invalid GIS data provided to function mbrwithin.
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),fid) FROM gis_geometrycollection,gis_geometrycollection_2 WHERE fid=103 and fid2=103;
ERROR 22023: Invalid GIS data provided to function mbrwithin.
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(a 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(! 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT('!' 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '!' 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'))' at line 1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POINT(12,34 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT MBRWITHIN(ST_GEOMFROMTEXT('LINESTRING(a 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT MBRWITHIN(ST_GEOMFROMTEXT('LINESTRING(0 0,! 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT MBRWITHIN(ST_GEOMFROMTEXT('LINESTRING('!' 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '!' 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'))' at line 1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('LINESTRING(12,34 0,10 10)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POLYGON((a 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POLYGON((0 0,! 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POLYGON(('!' 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '!' 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'))' at line 1
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POLYGON((12,34 0,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT MBRWITHIN(ST_GEOMFROMTEXT('POLYGON((12,34 0,10 10,10 0,0 0)'),ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT MBRWITHIN(,);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ',)' at line 1
SELECT MBRWITHIN(ST_GEOMFROMTEXT(@star_of_elems),);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
SELECT MBRWITHIN(,ST_GEOMFROMTEXT(@star_top));
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ',ST_GEOMFROMTEXT(@star_top))' at line 1
CREATE TABLE t1 (g GEOMETRY NOT NULL);
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('POINT(1 1)'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('LINESTRING(1 1, 2 2)'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('POLYGON((0 0, 1 0, 1 1, 0 1, 0 0))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
'POLYGON((0 0, 10 0, 10 10, 0 10, 0 0), (4 4, 6 4, 6 6, 4 6, 4 4))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('MULTIPOINT(1 1, 2 2)'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('MULTILINESTRING((1 1, 2 2),'
    '(3 3, 4 4))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
'MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)),((5 5, 10 5, 10 10, 5 10, 5 5)))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
'MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), ((5 5, 10 5, 10 10, 5 10, 5 5), '
    '(6 6, 7 6, 7 7, 6 7, 6 6)))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
'MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), ((1 1, 10 1, 10 10, 1 10, 1 1), '
    '(4 4, 6 4, 6 6, 4 6, 4 4)))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(1 1))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(LINESTRING(1 1, 2 2))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0, 1 0, 1'
    ' 1, 0 1, 0 0)))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(POLYGON((0 0, 10 0, 10 10, 0 10, 0 0), '
    '(4 4, 6 4, 6 6, 4 6, 4 4)))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(MULTIPOINT(1 1, 2 2))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(MULTILINESTRING((1 1, 2 2), (3 3, 4 4)))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), '
    '((5 5, 10 5, 10 10, 5 10, 5 5))))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), '
    '((5 5, 10 5, 10 10, 5 10, 5 5), (6 6, 7 6, 7 7, 6 7, 6 6))))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), '
    '((1 1, 10 1, 10 10, 1 10, 1 1), (4 4, 6 4, 6 6, 4 6, 4 4))))'));
INSERT INTO t1 VALUES (ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(POINT(1 1),LINESTRING(1 1, 2 2),'
    'POLYGON((0 0, 1 0, 1 1, 0 1, 0 0)),'
    'POLYGON((0 0, 10 0, 10 10, 0 10, 0 0), (4 4, 6 4, 6 6, 4 6, 4 4)),'
    'MULTIPOINT(1 1, 2 2),MULTILINESTRING((1 1, 2 2), (3 3, 4 4)),'
    'MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), ((5 5, 10 5, 10 10, 5 10, 5 5))),'
    'MULTIPOLYGON(((0 0, 1 0, 1 1, 0 1, 0 0)), ((5 5, 10 5, 10 10, 5 10, 5 5), '
    '(6 6, 7 6, 7 7, 6 7, 6 6))))'));
INSERT INTO t1 VALUES(
GEOMETRYCOLLECTION(POINT(1,1),LINESTRING(POINT(1,1),POINT(2,2)),
POLYGON(LINESTRING(POINT(0,0),POINT(1,0),POINT(1,1),POINT(0,1),POINT(0,0))),
POLYGON(LINESTRING(POINT(0,0),POINT(10,0),POINT(10,10),POINT(0,10),
POINT(0,0)),LINESTRING(POINT(4,4),POINT(6,4),POINT(6,6),POINT(4,6),
POINT(4,4))),MULTIPOINT(POINT(1,1),POINT(2,2)),
MultiLINESTRING(LINESTRING(POINT(1,1),POINT(2,2)),LINESTRING(POINT(3,3),
POINT(4,4))),MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0),POINT(1,0),POINT(1,1),
POINT(0,1),POINT(0,0))),POLYGON(LINESTRING(POINT(5,5),POINT(10,5),
POINT(10,10),POINT(5,10),POINT(5,5)))),
MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0),POINT(1,0),POINT(1,1),POINT(0,1),
POINT(0,0))),POLYGON(LINESTRING(POINT(5,5),POINT(10,5),POINT(10,10),
POINT(5,10),POINT(5,5)),LINESTRING(POINT(6,6),POINT(7,6),POINT(7,7),
POINT(6,7),POINT(6,6)))),GEOMETRYCOLLECTION(POINT(1,1)),
GEOMETRYCOLLECTION(LINESTRING(POINT(1,1),POINT(2,2))),
GEOMETRYCOLLECTION(POLYGON(LINESTRING(POINT(0,0),POINT(1,0),POINT(1,1),
POINT(0,1),POINT(0,0)))),GEOMETRYCOLLECTION(POLYGON(LINESTRING(POINT(0,0),
POINT(10,0),POINT(10,10),POINT(0,10),POINT(0,0)),LINESTRING(POINT(4,4),
POINT(6,4),POINT(6,6),POINT(4,6),POINT(4,4)))),
GEOMETRYCOLLECTION(MULTIPOINT(POINT(1,1),POINT(2,2))),
GEOMETRYCOLLECTION(MultiLINESTRING(LINESTRING(POINT(1,1),POINT(2,2)),
LINESTRING(POINT(3,3),POINT(4,4)))),
GEOMETRYCOLLECTION(MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0),POINT(1,0),
POINT(1,1),POINT(0,1),POINT(0,0))),POLYGON(LINESTRING(POINT(5,5),POINT(10,5),
POINT(10,10),POINT(5,10),POINT(5,5))))),
GEOMETRYCOLLECTION(MULTIPOLYGON(POLYGON(LINESTRING(POINT(0,0),POINT(1,0),
POINT(1,1),POINT(0,1),POINT(0,0))),POLYGON(LINESTRING(POINT(5,5),POINT(10,5),
POINT(10,10),POINT(5,10),POINT(5,5)),LINESTRING(POINT(6,6),POINT(7,6),
POINT(7,7),POINT(6,7),POINT(6,6)))))));
INSERT INTO t1 VALUES (
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1,1)),
GEOMETRYCOLLECTION(LINESTRING(POINT(1,1),POINT(2,2))),
GEOMETRYCOLLECTION(POLYGON(LINESTRING(POINT(1,1), POINT(10,1), POINT(10,10),
POINT(1,10), POINT(1,1)), LINESTRING(POINT(4,4),POINT(6,4),POINT(6,6),
POINT(4,6),POINT(4,4))))));
SELECT ST_WITHIN(a.g, b.g) FROM t1 AS a JOIN t1 AS b;
SELECT MBRWITHIN(a.g, b.g) FROM t1 AS a JOIN t1 AS b;
SELECT ST_WITHIN(ST_SRID(a.g, 4326), ST_SRID(b.g, 4326))
FROM t1 AS a JOIN t1 AS b;
SELECT MBRWITHIN(ST_SRID(a.g, 4326), ST_SRID(b.g, 4326))
FROM t1 AS a JOIN t1 AS b;
# clean up
DROP TABLE t1;
DROP TABLE gis_geometrycollection;
DROP TABLE gis_geometrycollection_2;
#
# WL#8579 Spatial Reference Systems
#
# SRID 0 (should pass)
SELECT ST_WITHIN(
ST_GEOMFROMTEXT('POINT(0 0)', 0),
ST_GEOMFROMTEXT('POINT(0 0)', 0)
);
ST_WITHIN(
ST_GEOMFROMTEXT('POINT(0 0)', 0),
ST_GEOMFROMTEXT('POINT(0 0)', 0)
)
1
SELECT MBRWITHIN(
ST_GEOMFROMTEXT('POINT(0 0)', 0),
ST_GEOMFROMTEXT('POINT(0 0)', 0)
);
MBRWITHIN(
ST_GEOMFROMTEXT('POINT(0 0)', 0),
ST_GEOMFROMTEXT('POINT(0 0)', 0)
)
1
# Projected SRS (should pass)
SELECT ST_WITHIN(
ST_GEOMFROMTEXT('POINT(0 0)', 2000),
ST_GEOMFROMTEXT('POINT(0 0)', 2000)
);
ST_WITHIN(
ST_GEOMFROMTEXT('POINT(0 0)', 2000),
ST_GEOMFROMTEXT('POINT(0 0)', 2000)
)
1
SELECT MBRWITHIN(
ST_GEOMFROMTEXT('POINT(0 0)', 2000),
ST_GEOMFROMTEXT('POINT(0 0)', 2000)
);
MBRWITHIN(
ST_GEOMFROMTEXT('POINT(0 0)', 2000),
ST_GEOMFROMTEXT('POINT(0 0)', 2000)
)
1
# Geographic SRS (should pass)
SELECT ST_WITHIN(
ST_GEOMFROMTEXT('POINT(0 0)', 4326),
ST_GEOMFROMTEXT('POINT(0 0)', 4326)
);
ST_WITHIN(
ST_GEOMFROMTEXT('POINT(0 0)', 4326),
ST_GEOMFROMTEXT('POINT(0 0)', 4326)
)
1
SELECT MBRWITHIN(
ST_GEOMFROMTEXT('POINT(0 0)', 4326),
ST_GEOMFROMTEXT('POINT(0 0)', 4326)
);
MBRWITHIN(
ST_GEOMFROMTEXT('POINT(0 0)', 4326),
ST_GEOMFROMTEXT('POINT(0 0)', 4326)
)
1
#
# WL#10827 Ellipsoidal R-tree support functions
#
SET @pt = ST_GeomFromText('POINT(0 0)', 4326);
SET @pt_lat_minus_91 = x'E6100000010100000000000000000000000000000000C056C0';
SET @pt_lat_plus_91 = x'E6100000010100000000000000000000000000000000C05640';
SET @pt_long_minus_181 = x'E610000001010000000000000000A066C00000000000000000';
SET @pt_long_plus_181 = x'E610000001010000000000000000A066400000000000000000';
SELECT ST_Within(@pt_lat_minus_91, @pt);
ERROR 22S03: A parameter of function st_within contains a geometry with latitude -91.000000, which is out of range. It must be within [-90.000000, 90.000000].
SELECT ST_Within(@pt_lat_plus_91, @pt);
ERROR 22S03: A parameter of function st_within contains a geometry with latitude 91.000000, which is out of range. It must be within [-90.000000, 90.000000].
SELECT ST_Within(@pt_long_minus_181, @pt);
ERROR 22S02: A parameter of function st_within contains a geometry with longitude -181.000000, which is out of range. It must be within (-180.000000, 180.000000].
SELECT ST_Within(@pt_long_plus_181, @pt);
ERROR 22S02: A parameter of function st_within contains a geometry with longitude 181.000000, which is out of range. It must be within (-180.000000, 180.000000].
SELECT MBRWithin(@pt_lat_minus_91, @pt);
ERROR 22S03: A parameter of function mbrwithin contains a geometry with latitude -91.000000, which is out of range. It must be within [-90.000000, 90.000000].
SELECT MBRWithin(@pt_lat_plus_91, @pt);
ERROR 22S03: A parameter of function mbrwithin contains a geometry with latitude 91.000000, which is out of range. It must be within [-90.000000, 90.000000].
SELECT MBRWithin(@pt_long_minus_181, @pt);
ERROR 22S02: A parameter of function mbrwithin contains a geometry with longitude -181.000000, which is out of range. It must be within (-180.000000, 180.000000].
SELECT MBRWithin(@pt_long_plus_181, @pt);
ERROR 22S02: A parameter of function mbrwithin contains a geometry with longitude 181.000000, which is out of range. It must be within (-180.000000, 180.000000].
CREATE TABLE t1 (
id INT,
c GEOMETRY NOT NULL SRID 0,
g GEOMETRY AS (ST_SRID(c, 4326)) STORED NOT NULL SRID 4326,
SPATIAL INDEX c_idx (c),
SPATIAL INDEX g_idx (g)
);
INSERT INTO t1 (id, c) VALUES
(0, ST_GeomFromText('POINT(0 0)')),
(1, ST_GeomFromText('POINT(1 1)')),
(2, ST_GeomFromText('POINT(2 2)')),
(3, ST_GeomFromText('POINT(3 3)')),
(4, ST_GeomFromText('POINT(4 4)')),
(5, ST_GeomFromText('POINT(5 5.00001)')),
(6, ST_GeomFromText('POINT(6 6)')),
(7, ST_GeomFromText('POINT(7 7)'));
SET @py = 'POLYGON((0 0, 10 0, 10 5, 0 5, 0 0))';
SET @cart_py = ST_GeomFromText(@py);
SET @geo_py = ST_GeomFromText(@py, 4326, 'axis-order=long-lat');
SET @mpt = 'MULTIPOINT((0 0), (10 5))';
SET @cart_mpt = ST_GeomFromText(@mpt);
SET @geo_mpt = ST_GeomFromText(@mpt, 4326, 'axis-order=long-lat');
# Cartesian ST_Within
SELECT id, ST_AsText(c)
FROM t1 IGNORE INDEX(c_idx)
WHERE ST_Within(c, @cart_py)
ORDER BY id;
id	ST_AsText(c)
1	POINT(1 1)
2	POINT(2 2)
3	POINT(3 3)
4	POINT(4 4)
SELECT id, ST_AsText(c)
FROM t1 FORCE INDEX(c_idx)
WHERE ST_Within(c, @cart_py)
ORDER BY id;
id	ST_AsText(c)
1	POINT(1 1)
2	POINT(2 2)
3	POINT(3 3)
4	POINT(4 4)
# Geographic ST_Within
SELECT id, ST_AsText(g)
FROM t1 IGNORE INDEX(g_idx)
WHERE ST_Within(g, @geo_py)
ORDER BY id;
id	ST_AsText(g)
1	POINT(1 1)
2	POINT(2 2)
3	POINT(3 3)
4	POINT(4 4)
5	POINT(5.00001 5)
SELECT id, ST_AsText(g)
FROM t1 FORCE INDEX(g_idx)
WHERE ST_Within(g, @geo_py)
ORDER BY id;
id	ST_AsText(g)
1	POINT(1 1)
2	POINT(2 2)
3	POINT(3 3)
4	POINT(4 4)
5	POINT(5.00001 5)
# Cartesian MBRWithin
SELECT id, ST_AsText(c)
FROM t1 IGNORE INDEX(c_idx)
WHERE MBRWithin(c, @cart_py)
ORDER BY id;
id	ST_AsText(c)
1	POINT(1 1)
2	POINT(2 2)
3	POINT(3 3)
4	POINT(4 4)
SELECT id, ST_AsText(c)
FROM t1 FORCE INDEX(c_idx)
WHERE MBRWithin(c, @cart_py)
ORDER BY id;
id	ST_AsText(c)
1	POINT(1 1)
2	POINT(2 2)
3	POINT(3 3)
4	POINT(4 4)
SELECT id, ST_AsText(c)
FROM t1 IGNORE INDEX(c_idx)
WHERE MBRWithin(c, @cart_mpt)
ORDER BY id;
id	ST_AsText(c)
1	POINT(1 1)
2	POINT(2 2)
3	POINT(3 3)
4	POINT(4 4)
SELECT id, ST_AsText(c)
FROM t1 FORCE INDEX(c_idx)
WHERE MBRWithin(c, @cart_mpt)
ORDER BY id;
id	ST_AsText(c)
1	POINT(1 1)
2	POINT(2 2)
3	POINT(3 3)
4	POINT(4 4)
# Geographic MBRWithin
SELECT id, ST_AsText(g)
FROM t1 IGNORE INDEX(g_idx)
WHERE MBRWithin(g, @geo_py)
ORDER BY id;
id	ST_AsText(g)
1	POINT(1 1)
2	POINT(2 2)
3	POINT(3 3)
4	POINT(4 4)
5	POINT(5.00001 5)
SELECT id, ST_AsText(g)
FROM t1 FORCE INDEX(g_idx)
WHERE MBRWithin(g, @geo_py)
ORDER BY id;
id	ST_AsText(g)
1	POINT(1 1)
2	POINT(2 2)
3	POINT(3 3)
4	POINT(4 4)
5	POINT(5.00001 5)
SELECT id, ST_AsText(g)
FROM t1 IGNORE INDEX(g_idx)
WHERE MBRWithin(g, @geo_mpt)
ORDER BY id;
id	ST_AsText(g)
1	POINT(1 1)
2	POINT(2 2)
3	POINT(3 3)
4	POINT(4 4)
SELECT id, ST_AsText(g)
FROM t1 FORCE INDEX(g_idx)
WHERE MBRWithin(g, @geo_mpt)
ORDER BY id;
id	ST_AsText(g)
1	POINT(1 1)
2	POINT(2 2)
3	POINT(3 3)
4	POINT(4 4)
DROP TABLE t1;
#
# WL#11096 Don't do Cartesian computations on geographic geometries
#
# Assume SRID 10 is not defined.
DO MBRWITHIN(
x'0A000000010100000000000000000000000000000000000000',
x'0A000000010100000000000000000000000000000000000000'
);
ERROR SR001: There's no spatial reference system with SRID 10.
DO ST_WITHIN(
x'0A000000010100000000000000000000000000000000000000',
x'0A000000010100000000000000000000000000000000000000'
);
ERROR SR001: There's no spatial reference system with SRID 10.
