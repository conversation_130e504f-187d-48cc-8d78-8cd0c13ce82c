drop table if exists t1, t2;
Warnings:
Note	1051	Unknown table 'test.t1'
Note	1051	Unknown table 'test.t2'
create  table t1 (a int, b varchar(20));
create  table t2 (a int, b varchar(20));
insert into t1 values(1, 'a'), (2, 'b'), (3, 'c'), (3, 'c'), (4, 'c');
insert into t2 values(2, 'd'), (3, 'e'), (4, 'f'), (4, 'f'), (5, 'e');
drop function if exists func1;
Warnings:
Note	1305	FUNCTION test.func1 does not exist
create function func1(x int) returns int deterministic
begin
declare z1, z2 int;
set z1 = x;
set z2 = z1 + 2;
return z2;
end|
drop table if exists t3;
Warnings:
Note	1051	Unknown table 'test.t3'
create  table t3 like t1;
insert into t3 select 5 + 10000 * rand(), '5' from t3;
insert into t3 select 5 + 10000 * rand(), '5' from t3;
insert into t3 select 5 + 10000 * rand(), '5' from t3;
insert into t3 select 5 + 10000 * rand(), '5' from t3;
insert into t3 select 5 + 10000 * rand(), '5' from t3;
insert into t3 select 5 + 10000 * rand(), '5' from t3;
insert into t3 select 5 + 10000 * rand(), '5' from t3;
insert into t3 select 5 + 10000 * rand(), '5' from t3;
drop table if exists t4;
Warnings:
Note	1051	Unknown table 'test.t4'
create  table t4 (a int, b int, c int, d char, e blob);
insert into t4 values (1, 2, 3, '4', '5');
drop table if exists t5;
Warnings:
Note	1051	Unknown table 'test.t5'
create  table t5 (a int, b int primary key);
insert into t5 values (1,7), (1,8);
drop table if exists t6;
Warnings:
Note	1051	Unknown table 'test.t6'
create  table t6 (a int, b int, c int, d char);
insert into t6 values(1, 2, 3, '4');
create index t6_indx1 on t6(a);
drop table if exists t7;
Warnings:
Note	1051	Unknown table 'test.t7'
create  table t7 (a int, b varchar(20));
insert into t7 values(1, 'a'), (2, 'b'), (3, 'c'), (3, 'c'), (4, 'c');
create index indx7 on t7(b);
drop table if exists t8;
Warnings:
Note	1051	Unknown table 'test.t8'
create  table t8 (
pk integer auto_increment, col_int_key integer,
col_varchar_key varchar(1), primary key (pk),
key (col_int_key), key (col_varchar_key, col_int_key)) engine = innodb;
insert into t8 values (1, 2, 'v'), (2, 150, 'v');
drop table if exists t9, t10;
Warnings:
Note	1051	Unknown table 'test.t9'
Note	1051	Unknown table 'test.t10'
create  table t9 (
pk int(11) not null auto_increment,
col_int_nokey int(11) default null,
col_int_key int(11) default null,
col_date_key date default null,
col_date_nokey date default null,
primary key (pk),
key col_int_key (col_int_key),
key col_date_key (col_date_key),
key col_varchar_key (col_int_key)
) engine=innodb;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1831	Duplicate index 'col_varchar_key' defined on the table 'test.t9'. This is deprecated and will be disallowed in a future release.
create  table t10 (
pk int(11) not null auto_increment,
col_int_nokey int(11) default null,
col_int_key int(11) default null,
col_date_key date default null,
col_date_nokey date default null,
primary key (pk),
key col_int_key (col_int_key),
key col_date_key (col_date_key),
key col_varchar_key (col_int_key)
) engine=innodb;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1831	Duplicate index 'col_varchar_key' defined on the table 'test.t10'. This is deprecated and will be disallowed in a future release.
insert into t9 values
(1, 2, 4, '2008-12-05', '2008-12-05'),
(2, 150, 62, '2005-03-27', '2005-03-27'),
(3, NULL, 7, '2004-04-09', '2004-04-09'),
(4, 2, 1, '2006-05-13', '2006-05-13'),
(5, 5, 0, '2001-05-06', '2001-05-06'),
(6, 3, 7, '2006-03-03', '2006-03-03'),
(7, 1, 7, '2007-12-28', '2007-12-28'),
(8, 4, 1, '2004-10-20', '2004-10-20'),
(9, NULL, 7, '2008-04-09', '2008-04-09'),
(10, 2, 1, '2005-12-25', '2005-12-25'),
(11, 6, 5, '1900-01-01', '1900-01-01'),
(12, 6, 2, NULL, NULL),
(13, 8, 0, '1900-01-01', '1900-01-01'),
(14, 2, 1, '2001-01-16', '2001-01-16'),
(15, 6, 8, '1900-01-01', '1900-01-01'),
(16, 8, 1, '2001-11-23', '2001-11-23'),
(17, 3, 1, '2004-11-04', '2004-11-04'),
(18, 3, 9, '2003-03-12', '2003-03-12'),
(19, 9, 1, '2002-06-22', '2002-06-22'),
(20, 6, 5, '2004-10-10', '2004-10-10');
insert into t10 values
(1, 8, 4, '2002-01-03', '2002-01-03'),
(2, 3, 5, '2007-07-08', '2007-07-08'),
(3, 3, 8, '2000-08-02', '2000-08-02'),
(4, NULL, 4, '2000-03-06', '2000-03-06'),
(5, 7, 8, '2002-06-05', '2002-06-05'),
(6, 4, 2, '2009-08-09', '2009-08-09'),
(7, 7, 9, '2001-03-22', '2001-03-22'),
(8, 7, 6, '2002-10-08', '2002-10-08'),
(9, 8, NULL, NULL, NULL),
(10, 6, NULL, '2007-09-13', '2007-09-13'),
(11, 3, 48, '2003-12-08', '2003-12-08'),
(12, 210, 228, '2006-06-01', '2006-06-01'),
(13, 1, 3, '2001-10-04', '2001-10-04'),
(14, 2, 5, '2004-11-16', '2004-11-16'),
(15, 251, 39, NULL, NULL),
(16, 4, 6, '2005-06-22', '2005-06-22'),
(17, 4, 8, '2003-04-19', '2003-04-19'),
(18, 9, 3, '2006-03-23', '2006-03-23'),
(19, 4, NULL, NULL, NULL),
(20, NULL, 2, '2008-10-22', '2008-10-22'),
(21, 4, 6, '2009-04-04', '2009-04-04'),
(22, NULL, 3, NULL, NULL),
(23, 1, 1, '2001-07-15', '2001-07-15'),
(24, 6, 4, '2004-09-25', '2004-09-25'),
(25, 2, 3, '2001-02-20', '2001-02-20'),
(26, NULL, 1, '1900-01-01', '1900-01-01'),
(27, 4, NULL, '2004-08-02', '2004-08-02'),
(28, 248, 97, '2009-02-26', '2009-02-26'),
(29, 4, 0, '2002-11-23', '2002-11-23'),
(30, 8, 0, '2008-12-17', '2008-12-17'),
(31, 4, 9, '2005-08-26', '2005-08-26'),
(32, 5, 5, '2001-03-10', '2001-03-10'),
(33, 9, 9, '1900-01-01', '1900-01-01'),
(34, 2, 0, '2003-08-04', '2003-08-04'),
(35, 4, 2, '2007-11-06', '2007-11-06'),
(36, 211, 172, '2009-04-23', '2009-04-23'),
(37, 2, NULL, '2002-10-06', '2002-10-06'),
(38, 4, 5, '2008-02-12', '2008-02-12'),
(39, 125, 119, '2007-09-18', '2007-09-18'),
(40, 9, 1, '2007-06-26', '2007-06-26'),
(41, 4, 4, NULL, NULL),
(42, 8, 8, '2009-05-05', '2009-05-05'),
(43, 8, NULL, '2003-05-04', '2003-05-04'),
(44, NULL, 6, '2003-11-10', '2003-11-10'),
(45, 8, 5, '2009-02-19', '2009-02-19'),
(46, 4, 5, '2006-11-17', '2006-11-17'),
(47, 8, 1, '2000-02-23', '2000-02-23'),
(48, NULL, 7, '1900-01-01', '1900-01-01'),
(49, 1, 2, '2003-04-08', '2003-04-08'),
(50, 3, 8, '2006-07-08', '2006-07-08'),
(51, 5, 9, '2001-05-21', '2001-05-21'),
(52, 8, NULL, NULL, NULL),
(53, 7, NULL, '2009-01-05', '2009-01-05'),
(54, 2, 3, '2001-08-05', '2001-08-05'),
(55, NULL, 172, '2000-11-01', '2000-11-01'),
(56, 3, NULL, '2004-12-16', '2004-12-16'),
(57, NULL, 6, '2003-02-06', '2003-02-06'),
(58, 9, 6, '2008-04-23', '2008-04-23'),
(59, 1, 5, '2005-12-20', '2005-12-20'),
(60, 0, 4, '2002-03-13', '2002-03-13'),
(61, 0, 3, NULL, NULL),
(62, 8, 2, '2006-07-20', '2006-07-20'),
(63, NULL, 7, '2002-12-19', '2002-12-19'),
(64, 9, 4, '2001-07-09', '2001-07-09'),
(65, 1, 6, '2006-05-27', '2006-05-27'),
(66, 9, 0, '2007-02-26', '2007-02-26'),
(67, 7, 8, NULL, NULL),
(68, 2, 5, '2007-02-07', '2007-02-07'),
(69, 1, 8, '2005-01-22', '2005-01-22'),
(70, 9, 2, '2006-04-25', '2006-04-25'),
(71, 5, 9, '2002-11-13', '2002-11-13'),
(72, 4, 7, '2007-10-26', '2007-10-26'),
(73, 6, 5, '2003-06-16', '2003-06-16'),
(74, 5, 7, '2006-01-18', '2006-01-18'),
(75, 2, 0, '1900-01-01', '1900-01-01'),
(76, 4, 4, '2000-09-13', '2000-09-13'),
(77, 0, 3, '2003-01-26', '2003-01-26'),
(78, 3, 1, '2002-09-09', '2002-09-09'),
(79, 0, 0, '2001-09-06', '2001-09-06'),
(80, 6, 6, '2006-02-23', '2006-02-23'),
(81, 1, 2, '2004-06-21', '2004-06-21'),
(82, 9, NULL, '2006-12-02', '2006-12-02'),
(83, 4, 8, '2005-05-17', '2005-05-17'),
(84, 9, NULL, '2001-12-27', '2001-12-27'),
(85, 4, NULL, '2008-04-11', '2008-04-11'),
(86, 1, NULL, '2000-09-24', '2000-09-24'),
(87, 2, 3, '2004-04-20', '2004-04-20'),
(88, 8, 7, '2008-07-10', '2008-07-10'),
(89, 1, 3, '2007-08-14', '2007-08-14'),
(90, 0, 5, '2008-06-08', '2008-06-08'),
(91, 2, 5, '2001-07-26', '2001-07-26'),
(92, 0, 1, '2008-09-17', '2008-09-17'),
(93, 1, 2, NULL, NULL),
(94, 2, 1, '2004-02-25', '2004-02-25'),
(95, NULL, 7, '2009-11-02', '2009-11-02'),
(96, 3, 1, '2001-12-14', '2001-12-14'),
(97, 8, 9, NULL, NULL),
(98, 4, 9, '1900-01-01', '1900-01-01'),
(99, 4, 8, '2002-04-12', '2002-04-12'),
(100, NULL, 3, '2000-02-09', '2000-02-09');
create  table t11 (
col_date_key date default null,
col_varchar_10_utf8 varchar(10) CHARACTER SET utf8mb3 default null,
col_varchar_10_latin1 varchar(10) default null,
col_varchar_10_latin1_key varchar(10) default null,
col_datetime datetime default null,
col_varchar_10_utf8_key varchar(10) CHARACTER SET utf8mb3 default null,
col_varchar_1024_latin1_key varchar(1024) default null,
col_varchar_1024_latin1 varchar(1024) default null,
col_varchar_1024_utf8 varchar(1024) CHARACTER SET utf8mb3 default null,
col_int_key int(11) default null,
col_date date default null,
col_varchar_1024_utf8_key varchar(1024) CHARACTER SET utf8mb3 default null,
col_int int(11) default null,
pk int(11) not null auto_increment,
col_datetime_key datetime default null,
primary key (pk),
key col_date_key (col_date_key),
key col_varchar_10_latin1_key (col_varchar_10_latin1_key),
key col_varchar_10_utf8_key (col_varchar_10_utf8_key),
key col_varchar_1024_latin1_key (col_varchar_1024_latin1_key(1000)),
key col_int_key (col_int_key),
key col_varchar_1024_utf8_key (col_varchar_1024_utf8_key(333)),
key col_datetime_key (col_datetime_key)
) engine=myisam auto_increment=76 default charset=latin1;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert ignore into t11 values
('2005-03-10','have','o','gryvuwdllyhymuwyypoiuyeqbnaanbur','2004','going','h','well','f',1140785152,'2003','YTLDF',NULL,NULL,'20050908213850.042201'),
('2003', 'g', 'ELTFL', 'got', '20:07:16.048352', 'ryvuwdllyhymuwyypoiuyeqbnaanbursgddnqhyvxnnpadsjiqdkajofferjfslatroorycttbiuhlubvmoikwwnalqyewgthdmfvsedatazrflwenzeiwdxtheqppbmqfydsawvanehgvfvbsqxzkqzhtjkybvikmpexipoddmoulsnzfchusfgkdazecevrpuakfioamyyqyribcnydfxmmzsxcpkydyczmeajaebsaskneaqwxjbpjptiwgiizoxfygnbjojpifpzldsdhrukdbwpzribxgafunywmmcfpjugozduylotsugqocbadhcnxjqlugncbebwsbobhxgghyyphxfqqnpliazusgjswynfotwdonpbkllfdbuoqceirksegypasuuxnrjumrkaryhkgkbdfgoqbdmrdrmpvwitqzswgossxltimkbtrxitnmigcxgfaqmrnvspyaftisgdbkzlevczyrlossygtnatwcobbtsxqhjrdykmthpyzmdgyirxwlidiknxwsufkyexucekyhivscjdcouswuiyltrpmrngriwvrxgovfyewdsvlwirfzkwexxzoccufvuvhsjfvuwjsrrzguntudxxhblzbescayrbbrijnvucxxmbyltlojlgcweddzrfxsmwnxxewigapgrowtmrlqsknuaegzfvdwfdnnbfyubwckwfdrwmrymagyswwvvyeqhaaskgxogthhrzsdlsyqoeuvhwxquwbnivpowtybzehvbsoppuefqngkqohtdeylosjyvtxuziotnfpbqxkpxvzcjbgxokjzitakfevxduhtudsslluhzgcwgrcrtpnczgxchnmmgnubjzvvuklklfhiupbsjzhcqyadhskxtjzabzesulhgpykga', 'she', 'have', 'PYJKD', 9, '2008-06-10', 'yvuwdllyhymuwyypoiuyeqbnaanbursgddnqhyvxnnpadsjiqdkajofferjfslatroorycttbiuhlubvmoikwwnalqyewgthdmfvsedatazrflwenzeiwdxtheqppbmqfydsawvanehgvfvbsqxzkqzhtjkybvikmpexipoddmoulsnzfchusfgkdazecevrpuakfioamyyqyribcnydfxmmzsxcpkydyczmeajaebsaskneaqwxjbpjptiwgiizoxfygnbjojpifpzldsdhrukdbwpzribxgafunywmmcfpjugozduylotsugqocbadhcnxjqlugncbebwsbobhxgghyyphxfqqnpliazusgjswynfotwdonpbkllfdbuoqceirksegypasuuxnrjumrkaryhkgkbdfgoqbdmrdrmpvwitqzswgossxltimkbtrxitnmigcxgfaqmrnv', NULL, NULL, '2008-01-09');
create  table t12 (t text, c char(10), b blob, d varbinary(10)) engine=innodb;
insert into t12 values (NULL, NULL, NULL,NULL);
insert into t12 values ("", "", "", "");
insert into t12 values ("hello", "hello", "hello", "hello");
insert into t12 values ("HELLO", "HELLO","HELLO", "HELLO");
insert into t12 values ("HELLO MY", "HELLO MY", "HELLO MY", "HELLO MY");
insert into t12 values ("a", "a", "a", "a");
insert into t12 values (1,1,1,1);
insert into t12 values (NULL,NULL,NULL,NULL);
create  table t13 (
pk int(11) not null auto_increment,
col_int_nokey int(11) default null,
col_int_key int(11) default null,
col_date_key date default null,
col_time_key time default null,
col_varchar_key varchar(1) default null,
col_varchar_nokey varchar(1) default null,
primary key (pk),
key col_int_key (col_int_key),
key col_date_key (col_date_key),
key col_time_key (col_time_key),
key col_varchar_key (col_varchar_key, col_int_key)
) engine=myisam auto_increment=21;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t13 values
(2, 150, 62, '2005-03-27', '14:26:02', 'v', 'v'),
(3, NULL, 7, '2004-04-09', '14:03:03', 'c', 'c'),
(4, 2, 1, '2006-05-13', '01:46:09', NULL, NULL),
(5, 5, 0, '2001-05-06', '16:21:18', 'x', 'x'),
(6, 3, 7, '2006-03-03', '18:56:33', 'i', 'i'),
(7, 1, 7, '2007-12-28', NULL, 'e', 'e'),
(8, 4, 1, '2004-10-20', '09:29:08', 'p', 'p'),
(9, NULL, 7, '2008-04-09', '19:11:10', 's', 's'),
(10, 2, 1, '2005-12-25', '11:57:26', 'j', 'j'),
(11, 6, 5, '1900-01-01', '00:39:46', 'z', 'z'),
(12, 6, 2, NULL, '03:28:15', 'c', 'c'),
(13, 8, 0, '1900-01-01', '06:44:18', 'a', 'a'),
(14, 2, 1, '2001-01-16', '14:36:39', 'q', 'q'),
(15, 6, 8, '1900-01-01', '18:42:45', 'y', 'y'),
(16, 8, 1, '2001-11-23', '02:57:29', NULL, NULL),
(17, 3, 1, '2004-11-04', '16:46:13', 'r', 'r'),
(18, 3, 9, '2003-03-12', '19:39:02', 'v', 'v'),
(19, 9, 1, '2002-06-22', NULL, NULL, NULL),
(20, 6, 5, '2004-10-10', '20:58:33', 'r', 'r');
create  table t14 (
pk int(11) not null auto_increment,
col_int_nokey int(11) default null,
col_int_key int(11) default null,
col_date_key date default null,
col_time_key time default null,
col_varchar_key varchar(1) default null,
col_varchar_nokey varchar(1) default null,
primary key (pk),
key col_int_key (col_int_key),
key col_date_key (col_date_key),
key col_time_key (col_time_key),
key col_varchar_key (col_varchar_key,col_int_key)
) ENGINE=myisam auto_increment=30;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t14 values
(10, NULL, 8, '2000-12-03', '22:55:23', 'x', 'x'),
(11, 8, 7, '2008-05-03', '10:19:31', 'd', 'd'),
(12, 1, 1, '2005-12-06', '14:40:36', 'r', 'r'),
(13, 9, 7, '2000-04-10', '04:37:47', 'f', 'f'),
(14, 4, 9, '2002-11-05', '19:34:06', 'y', 'y'),
(15, 3, NULL, '2000-09-06', '20:35:33', 'u', 'u'),
(16, 2, 1, NULL, NULL, 'm', 'm'),
(17, NULL, 9, '2007-06-14', '14:43:37', NULL, NULL),
(18, 2, 2, '2007-11-17', '02:23:09', 'o', 'o'),
(19, NULL, 9, '2009-02-23', '01:22:45', 'w', 'w'),
(20, 6, 2, '2007-01-08', '00:00:00', 'm', 'm'),
(21, 7, 4, '2008-06-10', '00:13:25', 'q', 'q'),
(22, 2, 0, '2002-10-20', '03:47:16', NULL, NULL),
(23, 5, 4, '2008-09-12', '01:41:48', 'd', 'd'),
(24, 7, 8, '2006-06-16', '00:00:00', 'g', 'g'),
(25, 6, NULL, '2004-09-18', '22:32:04', 'x', 'x'),
(26, 6, NULL, '1900-01-01', '16:44:14', 'f', 'f'),
(27, 2, 0, '2005-09-13', '17:38:37', 'p', 'p'),
(28, 9, NULL, '2007-04-09', '08:46:48', 'j', 'j'),
(29, 6, 8, '2000-09-20', '14:11:27', 'c', 'c');
analyze table t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11, t12, t13, t14;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
test.t4	analyze	status	OK
test.t5	analyze	status	OK
test.t6	analyze	status	OK
test.t7	analyze	status	OK
test.t8	analyze	status	OK
test.t9	analyze	status	OK
test.t10	analyze	status	OK
test.t11	analyze	status	OK
test.t12	analyze	status	OK
test.t13	analyze	status	OK
test.t14	analyze	status	OK
# restart: --big-tables=1
SET @optimizer_switch_saved= @@optimizer_switch;
SET @@optimizer_switch="derived_merge=off";
explain format = json select sql_buffer_result * from t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "buffer_result": {
      "using_temporary_table": true,
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 5,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.50",
          "prefix_cost": "0.75",
          "data_read_per_join": "440"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sql_buffer_result `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1`
select sql_buffer_result * from t1;
a	b
1	a
2	b
3	c
3	c
4	c
explain format = json select sql_buffer_result * from t2;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "buffer_result": {
      "using_temporary_table": true,
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 5,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.50",
          "prefix_cost": "0.75",
          "data_read_per_join": "440"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sql_buffer_result `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2`
select sql_buffer_result * from t2;
a	b
2	d
3	e
4	f
4	f
5	e
explain format = json select * from t1 left join t2 on t1.b = t2.b order by t1.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "28.50"
    },
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "25.00"
      },
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 25,
            "filtered": "100.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "2.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "2K"
            },
            "used_columns": [
              "a",
              "b"
            ],
            "attached_condition": "<if>(is_not_null_compl(t2), (`test`.`t2`.`b` = `test`.`t1`.`b`), true)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`b` = `test`.`t1`.`b`)) where true order by `test`.`t1`.`a`
select * from t1 left join t2 on t1.b = t2.b order by t1.a;
a	b	a	b
1	a	NULL	NULL
2	b	NULL	NULL
3	c	NULL	NULL
3	c	NULL	NULL
4	c	NULL	NULL
explain format = json select * from t1 order by func1(a);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "5.75"
    },
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "5.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 5,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.50",
          "prefix_cost": "0.75",
          "data_read_per_join": "440"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` order by `func1`(`test`.`t1`.`a`)
select * from t1 order by func1(a);
a	b
1	a
2	b
3	c
3	c
4	c
explain format = json select a from t1 group by b order by sum(a);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "ordering_operation": {
      "using_filesort": true,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` group by `test`.`t1`.`b` order by sum(`test`.`t1`.`a`)
select a from t1 group by b order by sum(a);
a
1
2
3
explain format = json select a from t7 group by b order by sum(a);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "grouping_operation": {
        "using_filesort": false,
        "table": {
          "table_name": "t7",
          "access_type": "index",
          "possible_keys": [
            "indx7"
          ],
          "key": "indx7",
          "used_key_parts": [
            "b"
          ],
          "key_length": "83",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` group by `test`.`t7`.`b` order by sum(`test`.`t7`.`a`)
select a from t7 group by b order by sum(a);
a
1
2
3
explain format = json select sql_big_result distinct * from t1, t2;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "duplicates_removal": {
      "using_temporary_table": true,
      "using_filesort": false,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 25,
            "filtered": "100.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "2.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "2K"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct sql_big_result `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2`
select sql_big_result distinct * from t1, t2;
a	b	a	b
1	a	2	d
1	a	3	e
1	a	4	f
1	a	5	e
2	b	2	d
2	b	3	e
2	b	4	f
2	b	5	e
3	c	2	d
3	c	3	e
3	c	4	f
3	c	5	e
4	c	2	d
4	c	3	e
4	c	4	f
4	c	5	e
explain format = json select sql_big_result t1.b from t1, t2 group by t1.b;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "grouping_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 25,
            "filtered": "100.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "2.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "2K"
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sql_big_result `test`.`t1`.`b` AS `b` from `test`.`t1` join `test`.`t2` group by `test`.`t1`.`b`
select sql_big_result t1.b from t1, t2 group by t1.b;
b
a
b
c
explain format = json select sql_big_result distinct t1.a from t1, t2 group by t1.a, t1.b;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "duplicates_removal": {
      "using_filesort": false,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "nested_loop": [
          {
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.50",
                "prefix_cost": "0.75",
                "data_read_per_join": "440"
              },
              "used_columns": [
                "a",
                "b"
              ]
            }
          },
          {
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 25,
              "filtered": "100.00",
              "using_join_buffer": "hash join",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "2.50",
                "prefix_cost": "3.50",
                "data_read_per_join": "2K"
              }
            }
          }
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct sql_big_result `test`.`t1`.`a` AS `a` from `test`.`t1` join `test`.`t2` group by `test`.`t1`.`a`,`test`.`t1`.`b`
select sql_big_result distinct t1.a from t1, t2 group by t1.a, t1.b;
a
1
2
3
4
explain format = json select t1.* from t1 inner join t2 on t1.a = t2.a order by t2.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "8.50"
    },
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "5.00"
      },
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "20.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a"
            ],
            "attached_condition": "(`test`.`t2`.`a` = `test`.`t1`.`a`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`a` = `test`.`t1`.`a`) order by `test`.`t2`.`a`
select t1.* from t1 inner join t2 on t1.a = t2.a order by t2.a;
a	b
2	b
3	c
3	c
4	c
4	c
explain format = json select t1.* from t1, t2 where t1.a = t2.a order by t1.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "8.50"
    },
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "5.00"
      },
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "20.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a"
            ],
            "attached_condition": "(`test`.`t2`.`a` = `test`.`t1`.`a`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`a` = `test`.`t1`.`a`) order by `test`.`t1`.`a`
select t1.* from t1, t2 where t1.a = t2.a order by t1.a;
a	b
2	b
3	c
3	c
4	c
4	c
explain format = json select t1.* from t1 inner join t2 where t1.a = t2.a group by t1.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "grouping_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "20.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a"
            ],
            "attached_condition": "(`test`.`t2`.`a` = `test`.`t1`.`a`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`a` = `test`.`t1`.`a`) group by `test`.`t1`.`a`
select t1.* from t1 inner join t2 where t1.a = t2.a group by t1.a;
a	b
2	b
3	c
4	c
explain format = json select t1.* from t1 inner join t2 where t1.a = t2.a group by t2.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "grouping_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "20.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a"
            ],
            "attached_condition": "(`test`.`t2`.`a` = `test`.`t1`.`a`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`a` = `test`.`t1`.`a`) group by `test`.`t2`.`a`
select t1.* from t1 inner join t2 where t1.a = t2.a group by t2.a;
a	b
2	b
3	c
4	c
explain format = json select a from t1 group by a order by a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "5.75"
    },
    "ordering_operation": {
      "using_filesort": false,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": true,
        "cost_info": {
          "sort_cost": "5.00"
        },
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` group by `test`.`t1`.`a` order by `test`.`t1`.`a`
select a from t1 group by a order by a;
a
1
2
3
4
explain format = json select b from t1 group by b order by a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "ordering_operation": {
      "using_filesort": true,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`b` AS `b` from `test`.`t1` group by `test`.`t1`.`b` order by `test`.`t1`.`a`
select b from t1 group by b order by a;
b
a
b
c
explain format = json select distinct t1.a from t1, t2 where  t1.b = t2.b;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "duplicates_removal": {
      "using_temporary_table": true,
      "using_filesort": false,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "20.00",
            "distinct": true,
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "b"
            ],
            "attached_condition": "(`test`.`t2`.`b` = `test`.`t1`.`b`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t1`.`a` AS `a` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`b` = `test`.`t1`.`b`)
select distinct t1.a from t1, t2 where  t1.b = t2.b;
a
explain format = json select distinct a from t1 group by b,a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "duplicates_removal": {
      "using_filesort": false,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t1`.`a` AS `a` from `test`.`t1` group by `test`.`t1`.`b`,`test`.`t1`.`a`
select distinct a from t1 group by b,a;
a
1
2
3
4
set @old_sort_buffer_size = @@sort_buffer_size;
set @@sort_buffer_size = 32804;
explain format = json select distinct b from t3 group by a having count(*) > 1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.35"
    },
    "duplicates_removal": {
      "using_filesort": false,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t3",
          "access_type": "ALL",
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 1,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.10",
            "prefix_cost": "0.35",
            "data_read_per_join": "88"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t3`.`b` AS `b` from `test`.`t3` group by `test`.`t3`.`a` having (count(0) > 1)
select distinct b from t3 group by a having count(*) > 1;
b
select distinct b from t3 group by a;
b
set @@sort_buffer_size = @old_sort_buffer_size;
explain format = json select distinct t4.a,e from t4, t1 order by t4.b;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.11"
    },
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "duplicates_removal": {
        "using_temporary_table": true,
        "using_filesort": false,
        "cost_info": {
          "sort_cost": "5.00"
        },
        "nested_loop": [
          {
            "table": {
              "table_name": "t4",
              "access_type": "ALL",
              "rows_examined_per_scan": 1,
              "rows_produced_per_join": 1,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.10",
                "prefix_cost": "0.35",
                "data_read_per_join": "32"
              },
              "used_columns": [
                "a",
                "b",
                "e"
              ]
            }
          },
          {
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "distinct": true,
              "using_join_buffer": "hash join",
              "cost_info": {
                "read_cost": "0.27",
                "eval_cost": "0.50",
                "prefix_cost": "1.12",
                "data_read_per_join": "440"
              }
            }
          }
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t4`.`a` AS `a`,`test`.`t4`.`e` AS `e` from `test`.`t4` join `test`.`t1` order by `test`.`t4`.`b`
select distinct t4.a,e from t4, t1 order by t4.b;
a	e
1	5
explain format = json select * from t1 union all select * from t2;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": false,
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "0.75"
            },
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.50",
                "prefix_cost": "0.75",
                "data_read_per_join": "440"
              },
              "used_columns": [
                "a",
                "b"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.75"
            },
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.50",
                "prefix_cost": "0.75",
                "data_read_per_join": "440"
              },
              "used_columns": [
                "a",
                "b"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` union all /* select#2 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2`
select * from t1 union all select * from t2;
a	b
1	a
2	b
3	c
3	c
4	c
2	d
3	e
4	f
4	f
5	e
explain format = json select * from t1 union select * from t2 order by b;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<union1,2>",
      "access_type": "ALL",
      "using_filesort": true,
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "0.75"
            },
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.50",
                "prefix_cost": "0.75",
                "data_read_per_join": "440"
              },
              "used_columns": [
                "a",
                "b"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.75"
            },
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.50",
                "prefix_cost": "0.75",
                "data_read_per_join": "440"
              },
              "used_columns": [
                "a",
                "b"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` union /* select#2 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` order by `b`
select * from t1 union select * from t2 order by b;
a	b
1	a
2	b
3	c
4	c
2	d
3	e
5	e
4	f
explain format = json select tt.* from (select * from t1) as tt;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.00"
    },
    "table": {
      "table_name": "tt",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "rows_produced_per_join": 5,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.50",
        "prefix_cost": "1.00",
        "data_read_per_join": "480"
      },
      "used_columns": [
        "a",
        "b"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "0.75"
          },
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `tt`.`a` AS `a`,`tt`.`b` AS `b` from (/* select#2 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1`) `tt`
select tt.* from (select * from t1) as tt;
a	b
1	a
2	b
3	c
3	c
4	c
select * from t5;
a	b
1	7
1	8
explain format = json select b from (select b from t5 where b > 5) tt;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    },
    "table": {
      "table_name": "tt",
      "access_type": "ALL",
      "rows_examined_per_scan": 2,
      "rows_produced_per_join": 2,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.20",
        "prefix_cost": "0.70",
        "data_read_per_join": "32"
      },
      "used_columns": [
        "b"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "0.45"
          },
          "table": {
            "table_name": "t5",
            "access_type": "index",
            "possible_keys": [
              "PRIMARY"
            ],
            "key": "PRIMARY",
            "used_key_parts": [
              "b"
            ],
            "key_length": "4",
            "rows_examined_per_scan": 2,
            "rows_produced_per_join": 2,
            "filtered": "100.00",
            "using_index": true,
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.20",
              "prefix_cost": "0.45",
              "data_read_per_join": "32"
            },
            "used_columns": [
              "b"
            ],
            "attached_condition": "(`test`.`t5`.`b` > 5)"
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `tt`.`b` AS `b` from (/* select#2 */ select `test`.`t5`.`b` AS `b` from `test`.`t5` where (`test`.`t5`.`b` > 5)) `tt`
select b from (select b from t5 where b > 5) tt;
b
7
8
explain format = json select * from (select b from t1) as t1, (select b from t2) as t2;
EXPLAIN
                  "b"
                  "b"
                  "data_read_per_join": "440"
                  "data_read_per_join": "440"
                  "eval_cost": "0.50",
                  "eval_cost": "0.50",
                  "prefix_cost": "0.75",
                  "prefix_cost": "0.75",
                  "read_cost": "0.25",
                  "read_cost": "0.25",
                "access_type": "ALL",
                "access_type": "ALL",
                "cost_info": {
                "cost_info": {
                "filtered": "100.00",
                "filtered": "100.00",
                "query_cost": "0.75"
                "query_cost": "0.75"
                "rows_examined_per_scan": 5,
                "rows_examined_per_scan": 5,
                "rows_produced_per_join": 5,
                "rows_produced_per_join": 5,
                "table_name": "t1",
                "table_name": "t2",
                "used_columns": [
                "used_columns": [
                ]
                ]
                },
                },
              "cost_info": {
              "cost_info": {
              "select_id": 2,
              "select_id": 3,
              "table": {
              "table": {
              }
              }
              },
              },
            "b"
            "b"
            "cacheable": true,
            "cacheable": true,
            "data_read_per_join": "2K"
            "data_read_per_join": "440"
            "dependent": false,
            "dependent": false,
            "eval_cost": "0.50",
            "eval_cost": "2.50",
            "prefix_cost": "1.00",
            "prefix_cost": "4.00",
            "query_block": {
            "query_block": {
            "read_cost": "0.50",
            "read_cost": "0.50",
            "using_temporary_table": true,
            "using_temporary_table": true,
            }
            }
          "access_type": "ALL",
          "access_type": "ALL",
          "cost_info": {
          "cost_info": {
          "filtered": "100.00",
          "filtered": "100.00",
          "materialized_from_subquery": {
          "materialized_from_subquery": {
          "rows_examined_per_scan": 5,
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 25,
          "rows_produced_per_join": 5,
          "table_name": "t1",
          "table_name": "t2",
          "used_columns": [
          "used_columns": [
          "using_join_buffer": "hash join",
          ],
          ],
          }
          }
          },
          },
        "table": {
        "table": {
        }
        }
      "query_cost": "4.00"
      {
      {
      }
      },
    "cost_info": {
    "nested_loop": [
    "select_id": 1,
    ]
    },
  "query_block": {
  }
Note	1003	/* select#1 */ select `t1`.`b` AS `b`,`t2`.`b` AS `b` from (/* select#2 */ select `test`.`t1`.`b` AS `b` from `test`.`t1`) `t1` join (/* select#3 */ select `test`.`t2`.`b` AS `b` from `test`.`t2`) `t2`
Warnings:
{
}
select * from (select b from t1) as t1, (select b from t2) as t2;
b	b
a	d
a	e
a	e
a	f
a	f
b	d
b	e
b	e
b	f
b	f
c	d
c	d
c	d
c	e
c	e
c	e
c	e
c	e
c	e
c	f
c	f
c	f
c	f
c	f
c	f
explain format = json select * from t1, (select * from t6) as t where t.a =5;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.95"
    },
    "nested_loop": [
      {
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.20",
            "prefix_cost": "0.70",
            "data_read_per_join": "48"
          },
          "used_columns": [
            "a",
            "b",
            "c",
            "d"
          ],
          "materialized_from_subquery": {
            "using_temporary_table": true,
            "dependent": false,
            "cacheable": true,
            "query_block": {
              "select_id": 2,
              "cost_info": {
                "query_cost": "0.35"
              },
              "table": {
                "table_name": "t6",
                "access_type": "ref",
                "possible_keys": [
                  "t6_indx1"
                ],
                "key": "t6_indx1",
                "used_key_parts": [
                  "a"
                ],
                "key_length": "5",
                "ref": [
                  "const"
                ],
                "rows_examined_per_scan": 1,
                "rows_produced_per_join": 1,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.10",
                  "prefix_cost": "0.35",
                  "data_read_per_join": "24"
                },
                "used_columns": [
                  "a",
                  "b",
                  "c",
                  "d"
                ]
              }
            }
          }
        }
      },
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 10,
          "filtered": "100.00",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "1.00",
            "prefix_cost": "1.95",
            "data_read_per_join": "880"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    ]
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`t`.`a` AS `a`,`t`.`b` AS `b`,`t`.`c` AS `c`,`t`.`d` AS `d` from `test`.`t1` join (/* select#2 */ select `test`.`t6`.`a` AS `a`,`test`.`t6`.`b` AS `b`,`test`.`t6`.`c` AS `c`,`test`.`t6`.`d` AS `d` from `test`.`t6` where (`test`.`t6`.`a` = 5)) `t`
select * from t1, (select * from t6) as t where t.a =5;
a	b	a	b	c	d
explain format = json select * from t1 where t1.a in (select a from t2) ;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "nested_loop": [
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a",
            "b"
          ],
          "attached_condition": "(`test`.`t1`.`a` is not null)"
        }
      },
      {
        "table": {
          "table_name": "<subquery2>",
          "access_type": "eq_ref",
          "key": "<auto_distinct_key>",
          "key_length": "5",
          "ref": [
            "test.t1.a"
          ],
          "rows_examined_per_scan": 1,
          "materialized_from_subquery": {
            "using_temporary_table": true,
            "query_block": {
              "table": {
                "table_name": "t2",
                "access_type": "ALL",
                "rows_examined_per_scan": 5,
                "rows_produced_per_join": 5,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.50",
                  "prefix_cost": "0.75",
                  "data_read_per_join": "440"
                },
                "used_columns": [
                  "a"
                ]
              }
            }
          }
        }
      }
    ]
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` semi join (`test`.`t2`) where (`<subquery2>`.`a` = `test`.`t1`.`a`)
select * from t1 where t1.a in (select a from t2) ;
a	b
2	b
3	c
3	c
4	c
set optimizer_switch='materialization=off';
set optimizer_switch='firstmatch=off';
set optimizer_switch='loosescan=off';
explain format = json select * from t1 where t1.a in (select a from t2) ;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "5.50"
    },
    "duplicates_removal": {
      "using_temporary_table": true,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "20.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "5.50",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a"
            ],
            "attached_condition": "(`test`.`t2`.`a` = `test`.`t1`.`a`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` semi join (`test`.`t2`) where (`test`.`t2`.`a` = `test`.`t1`.`a`)
select * from t1 where t1.a in (select a from t2) ;
a	b
2	b
3	c
3	c
4	c
set optimizer_switch='default,derived_merge=off';
explain format = json update t1, t2 set t1.a = 9, t2.a = 10 where t1.b = 'a';
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.50"
    },
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 1,
          "filtered": "20.00",
          "cost_info": {
            "read_cost": "0.65",
            "eval_cost": "0.10",
            "prefix_cost": "0.75",
            "data_read_per_join": "88"
          },
          "used_columns": [
            "a",
            "b"
          ],
          "attached_condition": "(`test`.`t1`.`b` = 'a')"
        }
      },
      {
        "table": {
          "update": true,
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "1.50",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    ]
  }
}
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`a` = 9,`test`.`t2`.`a` = 10 where (`test`.`t1`.`b` = 'a')
update t1, t2 set t1.a = 9, t2.a = 10 where t1.b = 'a';
explain format = json select count(distinct a) from t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "rows_produced_per_join": 5,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.50",
        "prefix_cost": "0.75",
        "data_read_per_join": "440"
      },
      "used_columns": [
        "a"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select count(distinct `test`.`t1`.`a`) AS `count(distinct a)` from `test`.`t1`
select count(distinct a) from t1;
count(distinct a)
4
explain format = json select b, group_concat(a) from t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "rows_produced_per_join": 5,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.50",
        "prefix_cost": "0.75",
        "data_read_per_join": "440"
      },
      "used_columns": [
        "a",
        "b"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`b` AS `b`,group_concat(`test`.`t1`.`a` separator ',') AS `group_concat(a)` from `test`.`t1`
select b, group_concat(a) from t1;
b	group_concat(a)
a	9,2,3,3,4
select * from t1;
a	b
9	a
2	b
3	c
3	c
4	c
explain format = json select b, count(a), sum(a), avg(a), std(a),
min(a), max(a), bit_and(a), bit_or(a) from t1 group by b;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "grouping_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 5,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.50",
          "prefix_cost": "0.75",
          "data_read_per_join": "440"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`b` AS `b`,count(`test`.`t1`.`a`) AS `count(a)`,sum(`test`.`t1`.`a`) AS `sum(a)`,avg(`test`.`t1`.`a`) AS `avg(a)`,std(`test`.`t1`.`a`) AS `std(a)`,min(`test`.`t1`.`a`) AS `min(a)`,max(`test`.`t1`.`a`) AS `max(a)`,bit_and(`test`.`t1`.`a`) AS `bit_and(a)`,bit_or(`test`.`t1`.`a`) AS `bit_or(a)` from `test`.`t1` group by `test`.`t1`.`b`
select b, count(a), sum(a), avg(a), std(a), min(a),
max(a), bit_and(a), bit_or(a) from t1 group by b;
b	count(a)	sum(a)	avg(a)	std(a)	min(a)	max(a)	bit_and(a)	bit_or(a)
a	1	9	9.0000	0	9	9	9	9
b	1	2	2.0000	0	2	2	2	2
c	3	10	3.3333	0.4714045207910316	3	4	0	7
select sql_big_result * from performance_schema.global_variables where variable_name like 'internal_tmp_mem_storage_engine';
VARIABLE_NAME	VARIABLE_VALUE
internal_tmp_mem_storage_engine	TempTable
select 1 union distinct select 1 union all select 1;
1
1
1
select alias1.`col_int_key` AS field1 from (t8 as alias1) where (5) in
(select 7 union select 1) order by alias1.`col_varchar_key` asc;
field1
select count(table2.col_date_key) as field1 from
(t10 as table1 inner join
((t10 as table2 right join
t9 as table3 on
((table3.pk = table2.pk) or
(table3.pk = table2.col_int_key))))
on ((table3.pk < table2.col_int_nokey)))
order by field1 ASC;
field1
2200
select distinct table1.col_varchar_1024_utf8_key as field1
from t11 as table1 left join t11 as table2 on
table1.col_varchar_1024_utf8 = table2.col_varchar_1024_latin1_key
order by field1;
field1
YTLDF
yvuwdllyhymuwyypoiuyeqbnaanbursgddnqhyvxnnpadsjiqdkajofferjfslatroorycttbiuhlubvmoikwwnalqyewgthdmfvsedatazrflwenzeiwdxtheqppbmqfydsawvanehgvfvbsqxzkqzhtjkybvikmpexipoddmoulsnzfchusfgkdazecevrpuakfioamyyqyribcnydfxmmzsxcpkydyczmeajaebsaskneaqwxjbpjptiwgiizoxfygnbjojpifpzldsdhrukdbwpzribxgafunywmmcfpjugozduylotsugqocbadhcnxjqlugncbebwsbobhxgghyyphxfqqnpliazusgjswynfotwdonpbkllfdbuoqceirksegypasuuxnrjumrkaryhkgkbdfgoqbdmrdrmpvwitqzswgossxltimkbtrxitnmigcxgfaqmrnv
select t from t12 group by c;
t
NULL

1
a
hello
HELLO MY
select count(outr.col_varchar_nokey) AS x
from t13 as outr2 left join
(select * from t13) as outr
on (outr2.col_time_key > outr.col_date_key)
where outr.pk in
(select innr.col_int_nokey AS y from
(select * from t14) as innr2 left join
(select * from t14) AS innr
ON (innr2.col_int_nokey = innr.col_int_key)
where innr.col_varchar_nokey > innr.col_varchar_key
or not outr.pk > 9)
or outr.col_varchar_key is null;
x
102
SET @@optimizer_switch=@optimizer_switch_saved;
SET GLOBAL innodb_fast_shutdown = 0;
# restart: --innodb-read-only --big-tables=1
SET @optimizer_switch_saved= @@optimizer_switch;
SET @@optimizer_switch="derived_merge=off";
explain format = json select sql_buffer_result * from t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "buffer_result": {
      "using_temporary_table": true,
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 5,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.50",
          "prefix_cost": "0.75",
          "data_read_per_join": "440"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sql_buffer_result `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1`
select sql_buffer_result * from t1;
a	b
9	a
2	b
3	c
3	c
4	c
explain format = json select sql_buffer_result * from t2;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "buffer_result": {
      "using_temporary_table": true,
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 5,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.50",
          "prefix_cost": "0.75",
          "data_read_per_join": "440"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sql_buffer_result `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2`
select sql_buffer_result * from t2;
a	b
10	d
10	e
10	f
10	f
10	e
explain format = json select * from t1 left join t2 on t1.b = t2.b order by t1.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "28.50"
    },
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "25.00"
      },
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 25,
            "filtered": "100.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "2.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "2K"
            },
            "used_columns": [
              "a",
              "b"
            ],
            "attached_condition": "<if>(is_not_null_compl(t2), (`test`.`t2`.`b` = `test`.`t1`.`b`), true)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` left join `test`.`t2` on((`test`.`t2`.`b` = `test`.`t1`.`b`)) where true order by `test`.`t1`.`a`
select * from t1 left join t2 on t1.b = t2.b order by t1.a;
a	b	a	b
2	b	NULL	NULL
3	c	NULL	NULL
3	c	NULL	NULL
4	c	NULL	NULL
9	a	NULL	NULL
explain format = json select * from t1 order by func1(a);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "5.75"
    },
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "5.00"
      },
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 5,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.50",
          "prefix_cost": "0.75",
          "data_read_per_join": "440"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` order by `func1`(`test`.`t1`.`a`)
select * from t1 order by func1(a);
a	b
2	b
3	c
3	c
4	c
9	a
explain format = json select a from t1 group by b order by sum(a);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "ordering_operation": {
      "using_filesort": true,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` group by `test`.`t1`.`b` order by sum(`test`.`t1`.`a`)
select a from t1 group by b order by sum(a);
a
2
9
3
explain format = json select a from t7 group by b order by sum(a);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "grouping_operation": {
        "using_filesort": false,
        "table": {
          "table_name": "t7",
          "access_type": "index",
          "possible_keys": [
            "indx7"
          ],
          "key": "indx7",
          "used_key_parts": [
            "b"
          ],
          "key_length": "83",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t7`.`a` AS `a` from `test`.`t7` group by `test`.`t7`.`b` order by sum(`test`.`t7`.`a`)
select a from t7 group by b order by sum(a);
a
1
2
3
explain format = json select sql_big_result distinct * from t1, t2;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "duplicates_removal": {
      "using_temporary_table": true,
      "using_filesort": false,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 25,
            "filtered": "100.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "2.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "2K"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct sql_big_result `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2`
select sql_big_result distinct * from t1, t2;
a	b	a	b
2	b	10	d
2	b	10	e
2	b	10	f
3	c	10	d
3	c	10	e
3	c	10	f
4	c	10	d
4	c	10	e
4	c	10	f
9	a	10	d
9	a	10	e
9	a	10	f
explain format = json select sql_big_result t1.b from t1, t2 group by t1.b;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "grouping_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 25,
            "filtered": "100.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "2.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "2K"
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select sql_big_result `test`.`t1`.`b` AS `b` from `test`.`t1` join `test`.`t2` group by `test`.`t1`.`b`
select sql_big_result t1.b from t1, t2 group by t1.b;
b
a
b
c
explain format = json select sql_big_result distinct t1.a from t1, t2 group by t1.a, t1.b;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "duplicates_removal": {
      "using_filesort": false,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "nested_loop": [
          {
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.50",
                "prefix_cost": "0.75",
                "data_read_per_join": "440"
              },
              "used_columns": [
                "a",
                "b"
              ]
            }
          },
          {
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 25,
              "filtered": "100.00",
              "using_join_buffer": "hash join",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "2.50",
                "prefix_cost": "3.50",
                "data_read_per_join": "2K"
              }
            }
          }
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct sql_big_result `test`.`t1`.`a` AS `a` from `test`.`t1` join `test`.`t2` group by `test`.`t1`.`a`,`test`.`t1`.`b`
select sql_big_result distinct t1.a from t1, t2 group by t1.a, t1.b;
a
2
3
4
9
explain format = json select t1.* from t1 inner join t2 on t1.a = t2.a order by t2.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "8.50"
    },
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "5.00"
      },
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "20.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a"
            ],
            "attached_condition": "(`test`.`t2`.`a` = `test`.`t1`.`a`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`a` = `test`.`t1`.`a`) order by `test`.`t2`.`a`
select t1.* from t1 inner join t2 on t1.a = t2.a order by t2.a;
a	b
explain format = json select t1.* from t1, t2 where t1.a = t2.a order by t1.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "8.50"
    },
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "5.00"
      },
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "20.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a"
            ],
            "attached_condition": "(`test`.`t2`.`a` = `test`.`t1`.`a`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`a` = `test`.`t1`.`a`) order by `test`.`t1`.`a`
select t1.* from t1, t2 where t1.a = t2.a order by t1.a;
a	b
explain format = json select t1.* from t1 inner join t2 where t1.a = t2.a group by t1.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "grouping_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "20.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a"
            ],
            "attached_condition": "(`test`.`t2`.`a` = `test`.`t1`.`a`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`a` = `test`.`t1`.`a`) group by `test`.`t1`.`a`
select t1.* from t1 inner join t2 where t1.a = t2.a group by t1.a;
a	b
explain format = json select t1.* from t1 inner join t2 where t1.a = t2.a group by t2.a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "grouping_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "20.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a"
            ],
            "attached_condition": "(`test`.`t2`.`a` = `test`.`t1`.`a`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`a` = `test`.`t1`.`a`) group by `test`.`t2`.`a`
select t1.* from t1 inner join t2 where t1.a = t2.a group by t2.a;
a	b
explain format = json select a from t1 group by a order by a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "5.75"
    },
    "ordering_operation": {
      "using_filesort": false,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": true,
        "cost_info": {
          "sort_cost": "5.00"
        },
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` group by `test`.`t1`.`a` order by `test`.`t1`.`a`
select a from t1 group by a order by a;
a
2
3
4
9
explain format = json select b from t1 group by b order by a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "ordering_operation": {
      "using_filesort": true,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`b` AS `b` from `test`.`t1` group by `test`.`t1`.`b` order by `test`.`t1`.`a`
select b from t1 group by b order by a;
b
b
c
a
explain format = json select distinct t1.a from t1, t2 where  t1.b = t2.b;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "duplicates_removal": {
      "using_temporary_table": true,
      "using_filesort": false,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "20.00",
            "distinct": true,
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "3.50",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "b"
            ],
            "attached_condition": "(`test`.`t2`.`b` = `test`.`t1`.`b`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t1`.`a` AS `a` from `test`.`t1` join `test`.`t2` where (`test`.`t2`.`b` = `test`.`t1`.`b`)
select distinct t1.a from t1, t2 where  t1.b = t2.b;
a
explain format = json select distinct a from t1 group by b,a;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "duplicates_removal": {
      "using_filesort": false,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t1`.`a` AS `a` from `test`.`t1` group by `test`.`t1`.`b`,`test`.`t1`.`a`
select distinct a from t1 group by b,a;
a
2
3
4
9
set @old_sort_buffer_size = @@sort_buffer_size;
set @@sort_buffer_size = 32804;
explain format = json select distinct b from t3 group by a having count(*) > 1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.35"
    },
    "duplicates_removal": {
      "using_filesort": false,
      "grouping_operation": {
        "using_temporary_table": true,
        "using_filesort": false,
        "table": {
          "table_name": "t3",
          "access_type": "ALL",
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 1,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.10",
            "prefix_cost": "0.35",
            "data_read_per_join": "88"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t3`.`b` AS `b` from `test`.`t3` group by `test`.`t3`.`a` having (count(0) > 1)
select distinct b from t3 group by a having count(*) > 1;
b
select distinct b from t3 group by a;
b
set @@sort_buffer_size = @old_sort_buffer_size;
explain format = json select distinct t4.a,e from t4, t1 order by t4.b;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.11"
    },
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": true,
      "duplicates_removal": {
        "using_temporary_table": true,
        "using_filesort": false,
        "cost_info": {
          "sort_cost": "5.00"
        },
        "nested_loop": [
          {
            "table": {
              "table_name": "t4",
              "access_type": "ALL",
              "rows_examined_per_scan": 1,
              "rows_produced_per_join": 1,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.10",
                "prefix_cost": "0.35",
                "data_read_per_join": "32"
              },
              "used_columns": [
                "a",
                "b",
                "e"
              ]
            }
          },
          {
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "distinct": true,
              "using_join_buffer": "hash join",
              "cost_info": {
                "read_cost": "0.27",
                "eval_cost": "0.50",
                "prefix_cost": "1.12",
                "data_read_per_join": "440"
              }
            }
          }
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select distinct `test`.`t4`.`a` AS `a`,`test`.`t4`.`e` AS `e` from `test`.`t4` join `test`.`t1` order by `test`.`t4`.`b`
select distinct t4.a,e from t4, t1 order by t4.b;
a	e
1	5
explain format = json select * from t1 union all select * from t2;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": false,
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "0.75"
            },
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.50",
                "prefix_cost": "0.75",
                "data_read_per_join": "440"
              },
              "used_columns": [
                "a",
                "b"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.75"
            },
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.50",
                "prefix_cost": "0.75",
                "data_read_per_join": "440"
              },
              "used_columns": [
                "a",
                "b"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` union all /* select#2 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2`
select * from t1 union all select * from t2;
a	b
9	a
2	b
3	c
3	c
4	c
10	d
10	e
10	f
10	f
10	e
explain format = json select * from t1 union select * from t2 order by b;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<union1,2>",
      "access_type": "ALL",
      "using_filesort": true,
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "0.75"
            },
            "table": {
              "table_name": "t1",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.50",
                "prefix_cost": "0.75",
                "data_read_per_join": "440"
              },
              "used_columns": [
                "a",
                "b"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.75"
            },
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 5,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "0.25",
                "eval_cost": "0.50",
                "prefix_cost": "0.75",
                "data_read_per_join": "440"
              },
              "used_columns": [
                "a",
                "b"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` union /* select#2 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t2` order by `b`
select * from t1 union select * from t2 order by b;
a	b
9	a
2	b
3	c
4	c
10	d
10	e
10	f
explain format = json select tt.* from (select * from t1) as tt;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.00"
    },
    "table": {
      "table_name": "tt",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "rows_produced_per_join": 5,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.50",
        "prefix_cost": "1.00",
        "data_read_per_join": "480"
      },
      "used_columns": [
        "a",
        "b"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "0.75"
          },
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `tt`.`a` AS `a`,`tt`.`b` AS `b` from (/* select#2 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1`) `tt`
select tt.* from (select * from t1) as tt;
a	b
9	a
2	b
3	c
3	c
4	c
select * from t5;
a	b
1	7
1	8
explain format = json select b from (select b from t5 where b > 5) tt;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    },
    "table": {
      "table_name": "tt",
      "access_type": "ALL",
      "rows_examined_per_scan": 2,
      "rows_produced_per_join": 2,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.20",
        "prefix_cost": "0.70",
        "data_read_per_join": "32"
      },
      "used_columns": [
        "b"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "0.45"
          },
          "table": {
            "table_name": "t5",
            "access_type": "index",
            "possible_keys": [
              "PRIMARY"
            ],
            "key": "PRIMARY",
            "used_key_parts": [
              "b"
            ],
            "key_length": "4",
            "rows_examined_per_scan": 2,
            "rows_produced_per_join": 2,
            "filtered": "100.00",
            "using_index": true,
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.20",
              "prefix_cost": "0.45",
              "data_read_per_join": "32"
            },
            "used_columns": [
              "b"
            ],
            "attached_condition": "(`test`.`t5`.`b` > 5)"
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `tt`.`b` AS `b` from (/* select#2 */ select `test`.`t5`.`b` AS `b` from `test`.`t5` where (`test`.`t5`.`b` > 5)) `tt`
select b from (select b from t5 where b > 5) tt;
b
7
8
explain format = json select * from (select b from t1) as t1, (select b from t2) as t2;
EXPLAIN
                  "b"
                  "b"
                  "data_read_per_join": "440"
                  "data_read_per_join": "440"
                  "eval_cost": "0.50",
                  "eval_cost": "0.50",
                  "prefix_cost": "0.75",
                  "prefix_cost": "0.75",
                  "read_cost": "0.25",
                  "read_cost": "0.25",
                "access_type": "ALL",
                "access_type": "ALL",
                "cost_info": {
                "cost_info": {
                "filtered": "100.00",
                "filtered": "100.00",
                "query_cost": "0.75"
                "query_cost": "0.75"
                "rows_examined_per_scan": 5,
                "rows_examined_per_scan": 5,
                "rows_produced_per_join": 5,
                "rows_produced_per_join": 5,
                "table_name": "t1",
                "table_name": "t2",
                "used_columns": [
                "used_columns": [
                ]
                ]
                },
                },
              "cost_info": {
              "cost_info": {
              "select_id": 2,
              "select_id": 3,
              "table": {
              "table": {
              }
              }
              },
              },
            "b"
            "b"
            "cacheable": true,
            "cacheable": true,
            "data_read_per_join": "2K"
            "data_read_per_join": "440"
            "dependent": false,
            "dependent": false,
            "eval_cost": "0.50",
            "eval_cost": "2.50",
            "prefix_cost": "1.00",
            "prefix_cost": "4.00",
            "query_block": {
            "query_block": {
            "read_cost": "0.50",
            "read_cost": "0.50",
            "using_temporary_table": true,
            "using_temporary_table": true,
            }
            }
          "access_type": "ALL",
          "access_type": "ALL",
          "cost_info": {
          "cost_info": {
          "filtered": "100.00",
          "filtered": "100.00",
          "materialized_from_subquery": {
          "materialized_from_subquery": {
          "rows_examined_per_scan": 5,
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 25,
          "rows_produced_per_join": 5,
          "table_name": "t1",
          "table_name": "t2",
          "used_columns": [
          "used_columns": [
          "using_join_buffer": "hash join",
          ],
          ],
          }
          }
          },
          },
        "table": {
        "table": {
        }
        }
      "query_cost": "4.00"
      {
      {
      }
      },
    "cost_info": {
    "nested_loop": [
    "select_id": 1,
    ]
    },
  "query_block": {
  }
Note	1003	/* select#1 */ select `t1`.`b` AS `b`,`t2`.`b` AS `b` from (/* select#2 */ select `test`.`t1`.`b` AS `b` from `test`.`t1`) `t1` join (/* select#3 */ select `test`.`t2`.`b` AS `b` from `test`.`t2`) `t2`
Warnings:
{
}
select * from (select b from t1) as t1, (select b from t2) as t2;
b	b
a	d
a	e
a	e
a	f
a	f
b	d
b	e
b	e
b	f
b	f
c	d
c	d
c	d
c	e
c	e
c	e
c	e
c	e
c	e
c	f
c	f
c	f
c	f
c	f
c	f
explain format = json select * from t1, (select * from t6) as t where t.a =5;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.95"
    },
    "nested_loop": [
      {
        "table": {
          "table_name": "t",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.20",
            "prefix_cost": "0.70",
            "data_read_per_join": "48"
          },
          "used_columns": [
            "a",
            "b",
            "c",
            "d"
          ],
          "materialized_from_subquery": {
            "using_temporary_table": true,
            "dependent": false,
            "cacheable": true,
            "query_block": {
              "select_id": 2,
              "cost_info": {
                "query_cost": "0.35"
              },
              "table": {
                "table_name": "t6",
                "access_type": "ref",
                "possible_keys": [
                  "t6_indx1"
                ],
                "key": "t6_indx1",
                "used_key_parts": [
                  "a"
                ],
                "key_length": "5",
                "ref": [
                  "const"
                ],
                "rows_examined_per_scan": 1,
                "rows_produced_per_join": 1,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.10",
                  "prefix_cost": "0.35",
                  "data_read_per_join": "24"
                },
                "used_columns": [
                  "a",
                  "b",
                  "c",
                  "d"
                ]
              }
            }
          }
        }
      },
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 10,
          "filtered": "100.00",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "1.00",
            "prefix_cost": "1.95",
            "data_read_per_join": "880"
          },
          "used_columns": [
            "a",
            "b"
          ]
        }
      }
    ]
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`t`.`a` AS `a`,`t`.`b` AS `b`,`t`.`c` AS `c`,`t`.`d` AS `d` from `test`.`t1` join (/* select#2 */ select `test`.`t6`.`a` AS `a`,`test`.`t6`.`b` AS `b`,`test`.`t6`.`c` AS `c`,`test`.`t6`.`d` AS `d` from `test`.`t6` where (`test`.`t6`.`a` = 5)) `t`
select * from t1, (select * from t6) as t where t.a =5;
a	b	a	b	c	d
explain format = json select * from t1 where t1.a in (select a from t2) ;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.50"
    },
    "nested_loop": [
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 5,
          "rows_produced_per_join": 5,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.25",
            "eval_cost": "0.50",
            "prefix_cost": "0.75",
            "data_read_per_join": "440"
          },
          "used_columns": [
            "a",
            "b"
          ],
          "attached_condition": "(`test`.`t1`.`a` is not null)"
        }
      },
      {
        "table": {
          "table_name": "<subquery2>",
          "access_type": "eq_ref",
          "key": "<auto_distinct_key>",
          "key_length": "5",
          "ref": [
            "test.t1.a"
          ],
          "rows_examined_per_scan": 1,
          "materialized_from_subquery": {
            "using_temporary_table": true,
            "query_block": {
              "table": {
                "table_name": "t2",
                "access_type": "ALL",
                "rows_examined_per_scan": 5,
                "rows_produced_per_join": 5,
                "filtered": "100.00",
                "cost_info": {
                  "read_cost": "0.25",
                  "eval_cost": "0.50",
                  "prefix_cost": "0.75",
                  "data_read_per_join": "440"
                },
                "used_columns": [
                  "a"
                ]
              }
            }
          }
        }
      }
    ]
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` semi join (`test`.`t2`) where (`<subquery2>`.`a` = `test`.`t1`.`a`)
select * from t1 where t1.a in (select a from t2) ;
a	b
set optimizer_switch='materialization=off';
set optimizer_switch='firstmatch=off';
set optimizer_switch='loosescan=off';
explain format = json select * from t1 where t1.a in (select a from t2) ;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "5.50"
    },
    "duplicates_removal": {
      "using_temporary_table": true,
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "0.75",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a",
              "b"
            ]
          }
        },
        {
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 5,
            "rows_produced_per_join": 5,
            "filtered": "20.00",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.50",
              "prefix_cost": "5.50",
              "data_read_per_join": "440"
            },
            "used_columns": [
              "a"
            ],
            "attached_condition": "(`test`.`t2`.`a` = `test`.`t1`.`a`)"
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` semi join (`test`.`t2`) where (`test`.`t2`.`a` = `test`.`t1`.`a`)
select * from t1 where t1.a in (select a from t2) ;
a	b
set optimizer_switch='default,derived_merge=off';
explain format = json select count(distinct a) from t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "rows_produced_per_join": 5,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.50",
        "prefix_cost": "0.75",
        "data_read_per_join": "440"
      },
      "used_columns": [
        "a"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select count(distinct `test`.`t1`.`a`) AS `count(distinct a)` from `test`.`t1`
select count(distinct a) from t1;
count(distinct a)
4
explain format = json select b, group_concat(a) from t1;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "rows_produced_per_join": 5,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.25",
        "eval_cost": "0.50",
        "prefix_cost": "0.75",
        "data_read_per_join": "440"
      },
      "used_columns": [
        "a",
        "b"
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`b` AS `b`,group_concat(`test`.`t1`.`a` separator ',') AS `group_concat(a)` from `test`.`t1`
select b, group_concat(a) from t1;
b	group_concat(a)
a	9,2,3,3,4
select * from t1;
a	b
9	a
2	b
3	c
3	c
4	c
explain format = json select b, count(a), sum(a), avg(a), std(a),
min(a), max(a), bit_and(a), bit_or(a) from t1 group by b;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.75"
    },
    "grouping_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 5,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.25",
          "eval_cost": "0.50",
          "prefix_cost": "0.75",
          "data_read_per_join": "440"
        },
        "used_columns": [
          "a",
          "b"
        ]
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`b` AS `b`,count(`test`.`t1`.`a`) AS `count(a)`,sum(`test`.`t1`.`a`) AS `sum(a)`,avg(`test`.`t1`.`a`) AS `avg(a)`,std(`test`.`t1`.`a`) AS `std(a)`,min(`test`.`t1`.`a`) AS `min(a)`,max(`test`.`t1`.`a`) AS `max(a)`,bit_and(`test`.`t1`.`a`) AS `bit_and(a)`,bit_or(`test`.`t1`.`a`) AS `bit_or(a)` from `test`.`t1` group by `test`.`t1`.`b`
select b, count(a), sum(a), avg(a), std(a), min(a),
max(a), bit_and(a), bit_or(a) from t1 group by b;
b	count(a)	sum(a)	avg(a)	std(a)	min(a)	max(a)	bit_and(a)	bit_or(a)
a	1	9	9.0000	0	9	9	9	9
b	1	2	2.0000	0	2	2	2	2
c	3	10	3.3333	0.4714045207910316	3	4	0	7
select sql_big_result * from performance_schema.global_variables where variable_name like 'internal_tmp_mem_storage_engine';
VARIABLE_NAME	VARIABLE_VALUE
internal_tmp_mem_storage_engine	TempTable
select 1 union distinct select 1 union all select 1;
1
1
1
select alias1.`col_int_key` AS field1 from (t8 as alias1) where (5) in
(select 7 union select 1) order by alias1.`col_varchar_key` asc;
field1
select count(table2.col_date_key) as field1 from
(t10 as table1 inner join
((t10 as table2 right join
t9 as table3 on
((table3.pk = table2.pk) or
(table3.pk = table2.col_int_key))))
on ((table3.pk < table2.col_int_nokey)))
order by field1 ASC;
field1
2200
select distinct table1.col_varchar_1024_utf8_key as field1
from t11 as table1 left join t11 as table2 on
table1.col_varchar_1024_utf8 = table2.col_varchar_1024_latin1_key
order by field1;
field1
YTLDF
yvuwdllyhymuwyypoiuyeqbnaanbursgddnqhyvxnnpadsjiqdkajofferjfslatroorycttbiuhlubvmoikwwnalqyewgthdmfvsedatazrflwenzeiwdxtheqppbmqfydsawvanehgvfvbsqxzkqzhtjkybvikmpexipoddmoulsnzfchusfgkdazecevrpuakfioamyyqyribcnydfxmmzsxcpkydyczmeajaebsaskneaqwxjbpjptiwgiizoxfygnbjojpifpzldsdhrukdbwpzribxgafunywmmcfpjugozduylotsugqocbadhcnxjqlugncbebwsbobhxgghyyphxfqqnpliazusgjswynfotwdonpbkllfdbuoqceirksegypasuuxnrjumrkaryhkgkbdfgoqbdmrdrmpvwitqzswgossxltimkbtrxitnmigcxgfaqmrnv
select t from t12 group by c;
t
NULL

1
a
hello
HELLO MY
select count(outr.col_varchar_nokey) AS x
from t13 as outr2 left join
(select * from t13) as outr
on (outr2.col_time_key > outr.col_date_key)
where outr.pk in
(select innr.col_int_nokey AS y from
(select * from t14) as innr2 left join
(select * from t14) AS innr
ON (innr2.col_int_nokey = innr.col_int_key)
where innr.col_varchar_nokey > innr.col_varchar_key
or not outr.pk > 9)
or outr.col_varchar_key is null;
x
102
SET @@optimizer_switch=@optimizer_switch_saved;
# restart
select @@big_tables;
@@big_tables
1
create table t0 (m int, n int, key(m)) engine=innodb;
begin;
select count(*) from t0;
count(*)
0
set @@session.sql_mode = ANSI;
set @@session.tmp_table_size = 0;
Warnings:
Warning	1292	Truncated incorrect tmp_table_size value: '0'
set @@session.big_tables = 1;
select @@big_tables;
@@big_tables
1
select event_name, e.min_timer_wait, min(t.min_timer_wait) from
performance_schema.events_waits_summary_global_by_event_name as e JOIN
performance_schema.events_waits_summary_by_thread_by_event_name as t USING
(event_name) group by event_name;
select * from t0;
m	n
commit;
drop table t0;
set session big_tables = 1;
select @@big_tables;
@@big_tables
1
drop function func1;
drop table t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11, t12, t13, t14;
#
# BUG#20762059 - innodb_thread_concurrency=1 and queries using intrinsic
#		temp tables, causes hang
#
# Scenario 1: Without timezone tables
#
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES(1);
# On default connection
SET @@session.max_heap_table_size=0;
Warnings:
Warning	1292	Truncated incorrect max_heap_table_size value: '0'
SET @@global.innodb_thread_concurrency=1;
# Executing I_S query which will create and do index read on
# intrinsic temporary tables
# Switch to connection con1
SELECT * FROM t1;
a
1
# Switch to connection default
DROP TABLE t1;
SET @@session.max_heap_table_size = default;
SET @@global.innodb_thread_concurrency = default;
SET @@global.innodb_thread_sleep_delay = default;
#
# Scenario 2: with I_S query and timezone table(uses attachable
# transaction)
#
SET @@session.max_heap_table_size=0;
Warnings:
Warning	1292	Truncated incorrect max_heap_table_size value: '0'
SET @@global.innodb_thread_concurrency=1;
# Executing I_S query which will create and do index read on
# intrinsic temporary tables
select convert_tz(0,'a','a');
convert_tz(0,'a','a')
NULL
SET @@session.max_heap_table_size = default;
SET @@global.innodb_thread_concurrency = default;
SET @@global.innodb_thread_sleep_delay = default;
#
# Scenario 3: with I_S query and help table(uses attachable
# transaction)
#
SET @@global.innodb_thread_concurrency=1;
# Executing I_S query which will create and do index read on
# intrinsic temporary tables
HELP '%function_2';
name	is_it_category
SET @@global.innodb_thread_concurrency = default;
SET @@global.innodb_thread_sleep_delay = default;
#
# Bug#21871451: INNODB: FAILING ASSERTION: !(&BUF_POOL->MUTEX)
#
SELECT 'SUCCESS_INNODB_CMPMEM' FROM DUAL WHERE EXISTS
(SELECT * FROM INFORMATION_SCHEMA.INNODB_CMPMEM);
SUCCESS_INNODB_CMPMEM
SUCCESS_INNODB_CMPMEM
SELECT 'SUCCESS_INNODB_CMP' FROM DUAL WHERE EXISTS
(SELECT * FROM INFORMATION_SCHEMA.INNODB_CMPMEM_RESET);
SUCCESS_INNODB_CMP
SUCCESS_INNODB_CMP
