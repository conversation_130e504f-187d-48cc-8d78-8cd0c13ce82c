drop table if exists t1;
create table t1 (
a1 char(64), a2 char(64), b char(16), c char(16) not null, d char(16), dummy char(248) default ' '
) charset latin1;
insert into t1 (a1, a2, b, c, d) values
('a','a','a','a111','xy1'),('a','a','a','b111','xy2'),('a','a','a','c111','xy3'),('a','a','a','d111','xy4'),
('a','a','b','e112','xy1'),('a','a','b','f112','xy2'),('a','a','b','g112','xy3'),('a','a','b','h112','xy4'),
('a','b','a','i121','xy1'),('a','b','a','j121','xy2'),('a','b','a','k121','xy3'),('a','b','a','l121','xy4'),
('a','b','b','m122','xy1'),('a','b','b','n122','xy2'),('a','b','b','o122','xy3'),('a','b','b','p122','xy4'),
('b','a','a','a211','xy1'),('b','a','a','b211','xy2'),('b','a','a','c211','xy3'),('b','a','a','d211','xy4'),
('b','a','b','e212','xy1'),('b','a','b','f212','xy2'),('b','a','b','g212','xy3'),('b','a','b','h212','xy4'),
('b','b','a','i221','xy1'),('b','b','a','j221','xy2'),('b','b','a','k221','xy3'),('b','b','a','l221','xy4'),
('b','b','b','m222','xy1'),('b','b','b','n222','xy2'),('b','b','b','o222','xy3'),('b','b','b','p222','xy4'),
('c','a','a','a311','xy1'),('c','a','a','b311','xy2'),('c','a','a','c311','xy3'),('c','a','a','d311','xy4'),
('c','a','b','e312','xy1'),('c','a','b','f312','xy2'),('c','a','b','g312','xy3'),('c','a','b','h312','xy4'),
('c','b','a','i321','xy1'),('c','b','a','j321','xy2'),('c','b','a','k321','xy3'),('c','b','a','l321','xy4'),
('c','b','b','m322','xy1'),('c','b','b','n322','xy2'),('c','b','b','o322','xy3'),('c','b','b','p322','xy4'),
('d','a','a','a411','xy1'),('d','a','a','b411','xy2'),('d','a','a','c411','xy3'),('d','a','a','d411','xy4'),
('d','a','b','e412','xy1'),('d','a','b','f412','xy2'),('d','a','b','g412','xy3'),('d','a','b','h412','xy4'),
('d','b','a','i421','xy1'),('d','b','a','j421','xy2'),('d','b','a','k421','xy3'),('d','b','a','l421','xy4'),
('d','b','b','m422','xy1'),('d','b','b','n422','xy2'),('d','b','b','o422','xy3'),('d','b','b','p422','xy4'),
('a','a','a','a111','xy1'),('a','a','a','b111','xy2'),('a','a','a','c111','xy3'),('a','a','a','d111','xy4'),
('a','a','b','e112','xy1'),('a','a','b','f112','xy2'),('a','a','b','g112','xy3'),('a','a','b','h112','xy4'),
('a','b','a','i121','xy1'),('a','b','a','j121','xy2'),('a','b','a','k121','xy3'),('a','b','a','l121','xy4'),
('a','b','b','m122','xy1'),('a','b','b','n122','xy2'),('a','b','b','o122','xy3'),('a','b','b','p122','xy4'),
('b','a','a','a211','xy1'),('b','a','a','b211','xy2'),('b','a','a','c211','xy3'),('b','a','a','d211','xy4'),
('b','a','b','e212','xy1'),('b','a','b','f212','xy2'),('b','a','b','g212','xy3'),('b','a','b','h212','xy4'),
('b','b','a','i221','xy1'),('b','b','a','j221','xy2'),('b','b','a','k221','xy3'),('b','b','a','l221','xy4'),
('b','b','b','m222','xy1'),('b','b','b','n222','xy2'),('b','b','b','o222','xy3'),('b','b','b','p222','xy4'),
('c','a','a','a311','xy1'),('c','a','a','b311','xy2'),('c','a','a','c311','xy3'),('c','a','a','d311','xy4'),
('c','a','b','e312','xy1'),('c','a','b','f312','xy2'),('c','a','b','g312','xy3'),('c','a','b','h312','xy4'),
('c','b','a','i321','xy1'),('c','b','a','j321','xy2'),('c','b','a','k321','xy3'),('c','b','a','l321','xy4'),
('c','b','b','m322','xy1'),('c','b','b','n322','xy2'),('c','b','b','o322','xy3'),('c','b','b','p322','xy4'),
('d','a','a','a411','xy1'),('d','a','a','b411','xy2'),('d','a','a','c411','xy3'),('d','a','a','d411','xy4'),
('d','a','b','e412','xy1'),('d','a','b','f412','xy2'),('d','a','b','g412','xy3'),('d','a','b','h412','xy4'),
('d','b','a','i421','xy1'),('d','b','a','j421','xy2'),('d','b','a','k421','xy3'),('d','b','a','l421','xy4'),
('d','b','b','m422','xy1'),('d','b','b','n422','xy2'),('d','b','b','o422','xy3'),('d','b','b','p422','xy4');
create index idx_t1_0 on t1 (a1);
create index idx_t1_1 on t1 (a1,a2,b,c);
create index idx_t1_2 on t1 (a1,a2,b);
analyze table t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	Table is already up to date
drop table if exists t2;
create table t2 (
a1 char(64), a2 char(64) not null, b char(16), c char(16), d char(16), dummy char(248) default ' '
) charset latin1;
insert into t2 select * from t1;
insert into t2 (a1, a2, b, c, d) values
('a','a',NULL,'a777','xyz'),('a','a',NULL,'a888','xyz'),('a','a',NULL,'a999','xyz'),
('a','a','a',NULL,'xyz'),
('a','a','b',NULL,'xyz'),
('a','b','a',NULL,'xyz'),
('c','a',NULL,'c777','xyz'),('c','a',NULL,'c888','xyz'),('c','a',NULL,'c999','xyz'),
('d','b','b',NULL,'xyz'),
('e','a','a',NULL,'xyz'),('e','a','a',NULL,'xyz'),('e','a','a',NULL,'xyz'),('e','a','a',NULL,'xyz'),
('e','a','b',NULL,'xyz'),('e','a','b',NULL,'xyz'),('e','a','b',NULL,'xyz'),('e','a','b',NULL,'xyz'),
('a','a',NULL,'a777','xyz'),('a','a',NULL,'a888','xyz'),('a','a',NULL,'a999','xyz'),
('a','a','a',NULL,'xyz'),
('a','a','b',NULL,'xyz'),
('a','b','a',NULL,'xyz'),
('c','a',NULL,'c777','xyz'),('c','a',NULL,'c888','xyz'),('c','a',NULL,'c999','xyz'),
('d','b','b',NULL,'xyz'),
('e','a','a',NULL,'xyz'),('e','a','a',NULL,'xyz'),('e','a','a',NULL,'xyz'),('e','a','a',NULL,'xyz'),
('e','a','b',NULL,'xyz'),('e','a','b',NULL,'xyz'),('e','a','b',NULL,'xyz'),('e','a','b',NULL,'xyz');
create index idx_t2_0 on t2 (a1);
create index idx_t2_1 on t2 (a1,a2,b,c);
create index idx_t2_2 on t2 (a1,a2,b);
analyze table t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	Table is already up to date
drop table if exists t3;
create table t3 (
a1 char(1), a2 char(1), b char(1), c char(4) not null, d char(3), dummy char(1) default ' '
) charset latin1;
insert into t3 (a1, a2, b, c, d) values
('a','a','a','a111','xy1'),('a','a','a','b111','xy2'),('a','a','a','c111','xy3'),('a','a','a','d111','xy4'),
('a','a','b','e112','xy1'),('a','a','b','f112','xy2'),('a','a','b','g112','xy3'),('a','a','b','h112','xy4'),
('a','b','a','i121','xy1'),('a','b','a','j121','xy2'),('a','b','a','k121','xy3'),('a','b','a','l121','xy4'),
('a','b','b','m122','xy1'),('a','b','b','n122','xy2'),('a','b','b','o122','xy3'),('a','b','b','p122','xy4'),
('b','a','a','a211','xy1'),('b','a','a','b211','xy2'),('b','a','a','c211','xy3'),('b','a','a','d211','xy4'),
('b','a','b','e212','xy1'),('b','a','b','f212','xy2'),('b','a','b','g212','xy3'),('b','a','b','h212','xy4'),
('b','b','a','i221','xy1'),('b','b','a','j221','xy2'),('b','b','a','k221','xy3'),('b','b','a','l221','xy4'),
('b','b','b','m222','xy1'),('b','b','b','n222','xy2'),('b','b','b','o222','xy3'),('b','b','b','p222','xy4'),
('c','a','a','a311','xy1'),('c','a','a','b311','xy2'),('c','a','a','c311','xy3'),('c','a','a','d311','xy4'),
('c','a','b','e312','xy1'),('c','a','b','f312','xy2'),('c','a','b','g312','xy3'),('c','a','b','h312','xy4'),
('c','b','a','i321','xy1'),('c','b','a','j321','xy2'),('c','b','a','k321','xy3'),('c','b','a','l321','xy4'),
('c','b','b','m322','xy1'),('c','b','b','n322','xy2'),('c','b','b','o322','xy3'),('c','b','b','p322','xy4');
insert into t3 (a1, a2, b, c, d) values
('a','a','a','a111','xy1'),('a','a','a','b111','xy2'),('a','a','a','c111','xy3'),('a','a','a','d111','xy4'),
('a','a','b','e112','xy1'),('a','a','b','f112','xy2'),('a','a','b','g112','xy3'),('a','a','b','h112','xy4'),
('a','b','a','i121','xy1'),('a','b','a','j121','xy2'),('a','b','a','k121','xy3'),('a','b','a','l121','xy4'),
('a','b','b','m122','xy1'),('a','b','b','n122','xy2'),('a','b','b','o122','xy3'),('a','b','b','p122','xy4'),
('b','a','a','a211','xy1'),('b','a','a','b211','xy2'),('b','a','a','c211','xy3'),('b','a','a','d211','xy4'),
('b','a','b','e212','xy1'),('b','a','b','f212','xy2'),('b','a','b','g212','xy3'),('b','a','b','h212','xy4'),
('b','b','a','i221','xy1'),('b','b','a','j221','xy2'),('b','b','a','k221','xy3'),('b','b','a','l221','xy4'),
('b','b','b','m222','xy1'),('b','b','b','n222','xy2'),('b','b','b','o222','xy3'),('b','b','b','p222','xy4'),
('c','a','a','a311','xy1'),('c','a','a','b311','xy2'),('c','a','a','c311','xy3'),('c','a','a','d311','xy4'),
('c','a','b','e312','xy1'),('c','a','b','f312','xy2'),('c','a','b','g312','xy3'),('c','a','b','h312','xy4'),
('c','b','a','i321','xy1'),('c','b','a','j321','xy2'),('c','b','a','k321','xy3'),('c','b','a','l321','xy4'),
('c','b','b','m322','xy1'),('c','b','b','n322','xy2'),('c','b','b','o322','xy3'),('c','b','b','p322','xy4');
insert into t3 (a1, a2, b, c, d) values
('a','a','a','a111','xy1'),('a','a','a','b111','xy2'),('a','a','a','c111','xy3'),('a','a','a','d111','xy4'),
('a','a','b','e112','xy1'),('a','a','b','f112','xy2'),('a','a','b','g112','xy3'),('a','a','b','h112','xy4'),
('a','b','a','i121','xy1'),('a','b','a','j121','xy2'),('a','b','a','k121','xy3'),('a','b','a','l121','xy4'),
('a','b','b','m122','xy1'),('a','b','b','n122','xy2'),('a','b','b','o122','xy3'),('a','b','b','p122','xy4'),
('b','a','a','a211','xy1'),('b','a','a','b211','xy2'),('b','a','a','c211','xy3'),('b','a','a','d211','xy4'),
('b','a','b','e212','xy1'),('b','a','b','f212','xy2'),('b','a','b','g212','xy3'),('b','a','b','h212','xy4'),
('b','b','a','i221','xy1'),('b','b','a','j221','xy2'),('b','b','a','k221','xy3'),('b','b','a','l221','xy4'),
('b','b','b','m222','xy1'),('b','b','b','n222','xy2'),('b','b','b','o222','xy3'),('b','b','b','p222','xy4'),
('c','a','a','a311','xy1'),('c','a','a','b311','xy2'),('c','a','a','c311','xy3'),('c','a','a','d311','xy4'),
('c','a','b','e312','xy1'),('c','a','b','f312','xy2'),('c','a','b','g312','xy3'),('c','a','b','h312','xy4'),
('c','b','a','i321','xy1'),('c','b','a','j321','xy2'),('c','b','a','k321','xy3'),('c','b','a','l321','xy4'),
('c','b','b','m322','xy1'),('c','b','b','n322','xy2'),('c','b','b','o322','xy3'),('c','b','b','p322','xy4');
insert into t3 (a1, a2, b, c, d) values
('a','a','a','a111','xy1'),('a','a','a','b111','xy2'),('a','a','a','c111','xy3'),('a','a','a','d111','xy4'),
('a','a','b','e112','xy1'),('a','a','b','f112','xy2'),('a','a','b','g112','xy3'),('a','a','b','h112','xy4'),
('a','b','a','i121','xy1'),('a','b','a','j121','xy2'),('a','b','a','k121','xy3'),('a','b','a','l121','xy4'),
('a','b','b','m122','xy1'),('a','b','b','n122','xy2'),('a','b','b','o122','xy3'),('a','b','b','p122','xy4'),
('b','a','a','a211','xy1'),('b','a','a','b211','xy2'),('b','a','a','c211','xy3'),('b','a','a','d211','xy4'),
('b','a','b','e212','xy1'),('b','a','b','f212','xy2'),('b','a','b','g212','xy3'),('b','a','b','h212','xy4'),
('b','b','a','i221','xy1'),('b','b','a','j221','xy2'),('b','b','a','k221','xy3'),('b','b','a','l221','xy4'),
('b','b','b','m222','xy1'),('b','b','b','n222','xy2'),('b','b','b','o222','xy3'),('b','b','b','p222','xy4'),
('c','a','a','a311','xy1'),('c','a','a','b311','xy2'),('c','a','a','c311','xy3'),('c','a','a','d311','xy4'),
('c','a','b','e312','xy1'),('c','a','b','f312','xy2'),('c','a','b','g312','xy3'),('c','a','b','h312','xy4'),
('c','b','a','i321','xy1'),('c','b','a','j321','xy2'),('c','b','a','k321','xy3'),('c','b','a','l321','xy4'),
('c','b','b','m322','xy1'),('c','b','b','n322','xy2'),('c','b','b','o322','xy3'),('c','b','b','p322','xy4');
create index idx_t3_0 on t3 (a1);
create index idx_t3_1 on t3 (a1,a2,b,c);
create index idx_t3_2 on t3 (a1,a2,b);
analyze table t3;
Table	Op	Msg_type	Msg_text
test.t3	analyze	status	Table is already up to date
explain format=tree select a1, min(a2) from t1 group by a1;
EXPLAIN
-> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=5)

explain format=tree select a1, min(a2) from t1 group by a1;
EXPLAIN
-> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=5)

explain format=tree select a1, max(a2) from t1 group by a1;
EXPLAIN
-> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=5)

explain format=tree select a1, min(a2), max(a2) from t1 group by a1;
EXPLAIN
-> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=5)

explain format=tree select a1, a2, b, min(c), max(c) from t1 group by a1,a2,b;
EXPLAIN
-> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=17)

explain format=tree select a1,a2,b,max(c),min(c) from t1 group by a1,a2,b;
EXPLAIN
-> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=17)

explain format=tree select a1,a2,b,max(c),min(c) from t2 group by a1,a2,b;
EXPLAIN
-> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=33)

explain format=tree select min(a2), a1, max(a2), min(a2), a1 from t1 group by a1;
EXPLAIN
-> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=5)

explain format=tree select a1, b, min(c), a1, max(c), b, a2, max(c), max(c) from t1 group by a1, a2, b;
EXPLAIN
-> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=17)

explain format=tree select min(a2) from t1 group by a1;
EXPLAIN
-> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=5)

explain format=tree select a2, min(c), max(c) from t1 group by a1,a2,b;
EXPLAIN
-> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=17)

select a1, min(a2) from t1 group by a1;
a1	min(a2)
a	a
b	a
c	a
d	a
select a1, max(a2) from t1 group by a1;
a1	max(a2)
a	b
b	b
c	b
d	b
select a1, min(a2), max(a2) from t1 group by a1;
a1	min(a2)	max(a2)
a	a	b
b	a	b
c	a	b
d	a	b
select a1, a2, b, min(c), max(c) from t1 group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	a111	d111
a	a	b	e112	h112
a	b	a	i121	l121
a	b	b	m122	p122
b	a	a	a211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	a	a311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	a411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,max(c),min(c) from t1 group by a1,a2,b;
a1	a2	b	max(c)	min(c)
a	a	a	d111	a111
a	a	b	h112	e112
a	b	a	l121	i121
a	b	b	p122	m122
b	a	a	d211	a211
b	a	b	h212	e212
b	b	a	l221	i221
b	b	b	p222	m222
c	a	a	d311	a311
c	a	b	h312	e312
c	b	a	l321	i321
c	b	b	p322	m322
d	a	a	d411	a411
d	a	b	h412	e412
d	b	a	l421	i421
d	b	b	p422	m422
select a1,a2,b,max(c),min(c) from t2 group by a1,a2,b;
a1	a2	b	max(c)	min(c)
a	a	NULL	a999	a777
a	a	a	d111	a111
a	a	b	h112	e112
a	b	a	l121	i121
a	b	b	p122	m122
b	a	a	d211	a211
b	a	b	h212	e212
b	b	a	l221	i221
b	b	b	p222	m222
c	a	NULL	c999	c777
c	a	a	d311	a311
c	a	b	h312	e312
c	b	a	l321	i321
c	b	b	p322	m322
d	a	a	d411	a411
d	a	b	h412	e412
d	b	a	l421	i421
d	b	b	p422	m422
e	a	a	NULL	NULL
e	a	b	NULL	NULL
select min(a2), a1, max(a2), min(a2), a1 from t1 group by a1;
min(a2)	a1	max(a2)	min(a2)	a1
a	a	b	a	a
a	b	b	a	b
a	c	b	a	c
a	d	b	a	d
select a1, b, min(c), a1, max(c), b, a2, max(c), max(c) from t1 group by a1, a2, b;
a1	b	min(c)	a1	max(c)	b	a2	max(c)	max(c)
a	a	a111	a	d111	a	a	d111	d111
a	b	e112	a	h112	b	a	h112	h112
a	a	i121	a	l121	a	b	l121	l121
a	b	m122	a	p122	b	b	p122	p122
b	a	a211	b	d211	a	a	d211	d211
b	b	e212	b	h212	b	a	h212	h212
b	a	i221	b	l221	a	b	l221	l221
b	b	m222	b	p222	b	b	p222	p222
c	a	a311	c	d311	a	a	d311	d311
c	b	e312	c	h312	b	a	h312	h312
c	a	i321	c	l321	a	b	l321	l321
c	b	m322	c	p322	b	b	p322	p322
d	a	a411	d	d411	a	a	d411	d411
d	b	e412	d	h412	b	a	h412	h412
d	a	i421	d	l421	a	b	l421	l421
d	b	m422	d	p422	b	b	p422	p422
select min(a2) from t1 group by a1;
min(a2)
a
a
a
a
select a2, min(c), max(c) from t1 group by a1,a2,b;
a2	min(c)	max(c)
a	a111	d111
a	e112	h112
b	i121	l121
b	m122	p122
a	a211	d211
a	e212	h212
b	i221	l221
b	m222	p222
a	a311	d311
a	e312	h312
b	i321	l321
b	m322	p322
a	a411	d411
a	e412	h412
b	i421	l421
b	m422	p422
explain format=tree select a1,a2,b,min(c),max(c) from t1 where a1 < 'd' group by a1,a2,b;
EXPLAIN
-> Filter: (t1.a1 < 'd')  (rows=10)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (NULL < a1 < 'd')  (rows=10)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where a1 >= 'b' group by a1,a2,b;
EXPLAIN
-> Filter: (t1.a1 >= 'b')  (rows=14)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over ('b' <= a1)  (rows=14)

explain format=tree select a1,a2,b,       max(c) from t1 where a1 >= 'c' or a1 < 'b' group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.a1 >= 'c') or (t1.a1 < 'b'))  (rows=14)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (NULL < a1 < 'b') OR ('c' <= a1)  (rows=14)

explain format=tree select a1, max(c)            from t1 where a1 >= 'c' or a1 < 'b' group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.a1 >= 'c') or (t1.a1 < 'b'))  (rows=14)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (NULL < a1 < 'b') OR ('c' <= a1)  (rows=14)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where a1 >= 'c' or a2 < 'b' group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.a1 >= 'c') or (t1.a2 < 'b'))  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=17)

explain format=tree select a1,a2,b,       max(c) from t1 where a1 = 'z' or a1 = 'b' or a1 = 'd' group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.a1 = 'z') or (t1.a1 = 'b') or (t1.a1 = 'd'))  (rows=10)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (a1 = 'b') OR (a1 = 'd') OR (a1 = 'z')  (rows=10)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where a1 = 'z' or a1 = 'b' or a1 = 'd' group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.a1 = 'z') or (t1.a1 = 'b') or (t1.a1 = 'd'))  (rows=10)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (a1 = 'b') OR (a1 = 'd') OR (a1 = 'z')  (rows=10)

explain format=tree select a1,a2,b,       max(c) from t1 where (a1 = 'b' or a1 = 'd' or a1 = 'a' or a1 = 'c') and (a2 > 'a') group by a1,a2,b;
EXPLAIN
-> Filter: (((t1.a1 = 'b') or (t1.a1 = 'd') or (t1.a1 = 'a') or (t1.a1 = 'c')) and (t1.a2 > 'a'))  (rows=10)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (a1 = 'a' AND 'a' < a2) OR (a1 = 'b' AND 'a' < a2) OR (2 more)  (rows=10)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (a1 = 'b' or a1 = 'd' or a1 = 'a' or a1 = 'c') and (a2 > 'a') group by a1,a2,b;
EXPLAIN
-> Filter: (((t1.a1 = 'b') or (t1.a1 = 'd') or (t1.a1 = 'a') or (t1.a1 = 'c')) and (t1.a2 > 'a'))  (rows=10)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (a1 = 'a' AND 'a' < a2) OR (a1 = 'b' AND 'a' < a2) OR (2 more)  (rows=10)

explain format=tree select a1,min(c),max(c)      from t1 where a1 >= 'b' group by a1,a2,b;
EXPLAIN
-> Filter: (t1.a1 >= 'b')  (rows=14)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over ('b' <= a1)  (rows=14)

explain format=tree select a1,  max(c)           from t1 where a1 in ('a','b','d') group by a1,a2,b;
EXPLAIN
-> Filter: (t1.a1 in ('a','b','d'))  (rows=14)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (a1 = 'a') OR (a1 = 'b') OR (a1 = 'd')  (rows=14)

explain format=tree select a1,a2,b,       max(c) from t2 where a1 < 'd' group by a1,a2,b;
EXPLAIN
-> Filter: (t2.a1 < 'd')  (rows=22)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < a1 < 'd')  (rows=22)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where a1 < 'd' group by a1,a2,b;
EXPLAIN
-> Filter: (t2.a1 < 'd')  (rows=22)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < a1 < 'd')  (rows=22)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where a1 >= 'b' group by a1,a2,b;
EXPLAIN
-> Filter: (t2.a1 >= 'b')  (rows=25)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over ('b' <= a1)  (rows=25)

explain format=tree select a1,a2,b,       max(c) from t2 where a1 >= 'c' or a1 < 'b' group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.a1 >= 'c') or (t2.a1 < 'b'))  (rows=27)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < a1 < 'b') OR ('c' <= a1)  (rows=27)

explain format=tree select a1, max(c)            from t2 where a1 >= 'c' or a1 < 'b' group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.a1 >= 'c') or (t2.a1 < 'b'))  (rows=27)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < a1 < 'b') OR ('c' <= a1)  (rows=27)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where a1 >= 'c' or a2 < 'b' group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.a1 >= 'c') or (t2.a2 < 'b'))  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=33)

explain format=tree select a1,a2,b,       max(c) from t2 where a1 = 'z' or a1 = 'b' or a1 = 'd' group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.a1 = 'z') or (t2.a1 = 'b') or (t2.a1 = 'd'))  (rows=14)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (a1 = 'b') OR (a1 = 'd') OR (a1 = 'z')  (rows=14)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where a1 = 'z' or a1 = 'b' or a1 = 'd' group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.a1 = 'z') or (t2.a1 = 'b') or (t2.a1 = 'd'))  (rows=14)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (a1 = 'b') OR (a1 = 'd') OR (a1 = 'z')  (rows=14)

explain format=tree select a1,a2,b,       max(c) from t2 where (a1 = 'b' or a1 = 'd' or a1 = 'a' or a1 = 'c') and (a2 > 'a') group by a1,a2,b;
EXPLAIN
-> Filter: (((t2.a1 = 'b') or (t2.a1 = 'd') or (t2.a1 = 'a') or (t2.a1 = 'c')) and (t2.a2 > 'a'))  (rows=13)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (a1 = 'a' AND 'a' < a2) OR (a1 = 'b' AND 'a' < a2) OR (2 more)  (rows=13)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (a1 = 'b' or a1 = 'd' or a1 = 'a' or a1 = 'c') and (a2 > 'a') group by a1,a2,b;
EXPLAIN
-> Filter: (((t2.a1 = 'b') or (t2.a1 = 'd') or (t2.a1 = 'a') or (t2.a1 = 'c')) and (t2.a2 > 'a'))  (rows=13)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (a1 = 'a' AND 'a' < a2) OR (a1 = 'b' AND 'a' < a2) OR (2 more)  (rows=13)

explain format=tree select a1,min(c),max(c)      from t2 where a1 >= 'b' group by a1,a2,b;
EXPLAIN
-> Filter: (t2.a1 >= 'b')  (rows=25)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over ('b' <= a1)  (rows=25)

explain format=tree select a1,  max(c)           from t2 where a1 in ('a','b','d') group by a1,a2,b;
EXPLAIN
-> Filter: (t2.a1 in ('a','b','d'))  (rows=22)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (a1 = 'a') OR (a1 = 'b') OR (a1 = 'd')  (rows=22)

select a1,a2,b,min(c),max(c) from t1 where a1 < 'd' group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	a111	d111
a	a	b	e112	h112
a	b	a	i121	l121
a	b	b	m122	p122
b	a	a	a211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	a	a311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
select a1,a2,b,min(c),max(c) from t1 where a1 >= 'b' group by a1,a2,b;
a1	a2	b	min(c)	max(c)
b	a	a	a211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	a	a311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	a411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,       max(c) from t1 where a1 >= 'c' or a1 < 'b' group by a1,a2,b;
a1	a2	b	max(c)
a	a	a	d111
a	a	b	h112
a	b	a	l121
a	b	b	p122
c	a	a	d311
c	a	b	h312
c	b	a	l321
c	b	b	p322
d	a	a	d411
d	a	b	h412
d	b	a	l421
d	b	b	p422
select a1, max(c)            from t1 where a1 >= 'c' or a1 < 'b' group by a1,a2,b;
a1	max(c)
a	d111
a	h112
a	l121
a	p122
c	d311
c	h312
c	l321
c	p322
d	d411
d	h412
d	l421
d	p422
select a1,a2,b,min(c),max(c) from t1 where a1 >= 'c' or a2 < 'b' group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	a111	d111
a	a	b	e112	h112
b	a	a	a211	d211
b	a	b	e212	h212
c	a	a	a311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	a411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,       max(c) from t1 where a1 = 'z' or a1 = 'b' or a1 = 'd' group by a1,a2,b;
a1	a2	b	max(c)
b	a	a	d211
b	a	b	h212
b	b	a	l221
b	b	b	p222
d	a	a	d411
d	a	b	h412
d	b	a	l421
d	b	b	p422
select a1,a2,b,min(c),max(c) from t1 where a1 = 'z' or a1 = 'b' or a1 = 'd' group by a1,a2,b;
a1	a2	b	min(c)	max(c)
b	a	a	a211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
d	a	a	a411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,       max(c) from t1 where (a1 = 'b' or a1 = 'd' or a1 = 'a' or a1 = 'c') and (a2 > 'a') group by a1,a2,b;
a1	a2	b	max(c)
a	b	a	l121
a	b	b	p122
b	b	a	l221
b	b	b	p222
c	b	a	l321
c	b	b	p322
d	b	a	l421
d	b	b	p422
select a1,a2,b,min(c),max(c) from t1 where (a1 = 'b' or a1 = 'd' or a1 = 'a' or a1 = 'c') and (a2 > 'a') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	b	a	i121	l121
a	b	b	m122	p122
b	b	a	i221	l221
b	b	b	m222	p222
c	b	a	i321	l321
c	b	b	m322	p322
d	b	a	i421	l421
d	b	b	m422	p422
select a1,min(c),max(c)      from t1 where a1 >= 'b' group by a1,a2,b;
a1	min(c)	max(c)
b	a211	d211
b	e212	h212
b	i221	l221
b	m222	p222
c	a311	d311
c	e312	h312
c	i321	l321
c	m322	p322
d	a411	d411
d	e412	h412
d	i421	l421
d	m422	p422
select a1,  max(c)           from t1 where a1 in ('a','b','d') group by a1,a2,b;
a1	max(c)
a	d111
a	h112
a	l121
a	p122
b	d211
b	h212
b	l221
b	p222
d	d411
d	h412
d	l421
d	p422
select a1,a2,b,       max(c) from t2 where a1 < 'd' group by a1,a2,b;
a1	a2	b	max(c)
a	a	NULL	a999
a	a	a	d111
a	a	b	h112
a	b	a	l121
a	b	b	p122
b	a	a	d211
b	a	b	h212
b	b	a	l221
b	b	b	p222
c	a	NULL	c999
c	a	a	d311
c	a	b	h312
c	b	a	l321
c	b	b	p322
select a1,a2,b,min(c),max(c) from t2 where a1 < 'd' group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	NULL	a777	a999
a	a	a	a111	d111
a	a	b	e112	h112
a	b	a	i121	l121
a	b	b	m122	p122
b	a	a	a211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	NULL	c777	c999
c	a	a	a311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
select a1,a2,b,min(c),max(c) from t2 where a1 >= 'b' group by a1,a2,b;
a1	a2	b	min(c)	max(c)
b	a	a	a211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	NULL	c777	c999
c	a	a	a311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	a411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
e	a	a	NULL	NULL
e	a	b	NULL	NULL
select a1,a2,b,       max(c) from t2 where a1 >= 'c' or a1 < 'b' group by a1,a2,b;
a1	a2	b	max(c)
a	a	NULL	a999
a	a	a	d111
a	a	b	h112
a	b	a	l121
a	b	b	p122
c	a	NULL	c999
c	a	a	d311
c	a	b	h312
c	b	a	l321
c	b	b	p322
d	a	a	d411
d	a	b	h412
d	b	a	l421
d	b	b	p422
e	a	a	NULL
e	a	b	NULL
select a1, max(c)            from t2 where a1 >= 'c' or a1 < 'b' group by a1,a2,b;
a1	max(c)
a	a999
a	d111
a	h112
a	l121
a	p122
c	c999
c	d311
c	h312
c	l321
c	p322
d	d411
d	h412
d	l421
d	p422
e	NULL
e	NULL
select a1,a2,b,min(c),max(c) from t2 where a1 >= 'c' or a2 < 'b' group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	NULL	a777	a999
a	a	a	a111	d111
a	a	b	e112	h112
b	a	a	a211	d211
b	a	b	e212	h212
c	a	NULL	c777	c999
c	a	a	a311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	a411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
e	a	a	NULL	NULL
e	a	b	NULL	NULL
select a1,a2,b,       max(c) from t2 where a1 = 'z' or a1 = 'b' or a1 = 'd' group by a1,a2,b;
a1	a2	b	max(c)
b	a	a	d211
b	a	b	h212
b	b	a	l221
b	b	b	p222
d	a	a	d411
d	a	b	h412
d	b	a	l421
d	b	b	p422
select a1,a2,b,min(c),max(c) from t2 where a1 = 'z' or a1 = 'b' or a1 = 'd' group by a1,a2,b;
a1	a2	b	min(c)	max(c)
b	a	a	a211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
d	a	a	a411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,       max(c) from t2 where (a1 = 'b' or a1 = 'd' or a1 = 'a' or a1 = 'c') and (a2 > 'a') group by a1,a2,b;
a1	a2	b	max(c)
a	b	a	l121
a	b	b	p122
b	b	a	l221
b	b	b	p222
c	b	a	l321
c	b	b	p322
d	b	a	l421
d	b	b	p422
select a1,a2,b,min(c),max(c) from t2 where (a1 = 'b' or a1 = 'd' or a1 = 'a' or a1 = 'c') and (a2 > 'a') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	b	a	i121	l121
a	b	b	m122	p122
b	b	a	i221	l221
b	b	b	m222	p222
c	b	a	i321	l321
c	b	b	m322	p322
d	b	a	i421	l421
d	b	b	m422	p422
select a1,min(c),max(c)      from t2 where a1 >= 'b' group by a1,a2,b;
a1	min(c)	max(c)
b	a211	d211
b	e212	h212
b	i221	l221
b	m222	p222
c	c777	c999
c	a311	d311
c	e312	h312
c	i321	l321
c	m322	p322
d	a411	d411
d	e412	h412
d	i421	l421
d	m422	p422
e	NULL	NULL
e	NULL	NULL
select a1,  max(c)           from t2 where a1 in ('a','b','d') group by a1,a2,b;
a1	max(c)
a	a999
a	d111
a	h112
a	l121
a	p122
b	d211
b	h212
b	l221
b	p222
d	d411
d	h412
d	l421
d	p422
explain format=tree select a1,a2,b,max(c),min(c) from t1 where (a2 = 'a') and (b = 'b') group by a1;
EXPLAIN
-> Filter: ((t1.b = 'b') and (t1.a2 = 'a'))  (rows=5)
    -> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=5)

explain format=tree select a1,max(c),min(c)      from t1 where (a2 = 'a') and (b = 'b') group by a1;
EXPLAIN
-> Filter: ((t1.b = 'b') and (t1.a2 = 'a'))  (rows=5)
    -> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=5)

explain format=tree select a1,a2,b,       max(c) from t1 where (b = 'b') group by a1,a2;
EXPLAIN
-> Filter: (t1.b = 'b')  (rows=9)
    -> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=9)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (b = 'b') group by a1,a2;
EXPLAIN
-> Filter: (t1.b = 'b')  (rows=9)
    -> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=9)

explain format=tree select a1,a2, max(c)         from t1 where (b = 'b') group by a1,a2;
EXPLAIN
-> Filter: (t1.b = 'b')  (rows=9)
    -> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=9)

explain format=tree select a1,a2,b,max(c),min(c) from t2 where (a2 = 'a') and (b = 'b') group by a1;
EXPLAIN
-> Filter: ((t2.b = 'b') and (t2.a2 = 'a'))  (rows=5)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=5)

explain format=tree select a1,max(c),min(c)      from t2 where (a2 = 'a') and (b = 'b') group by a1;
EXPLAIN
-> Filter: ((t2.b = 'b') and (t2.a2 = 'a'))  (rows=5)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=5)

explain format=tree select a1,a2,b,       max(c) from t2 where (b = 'b') group by a1,a2;
EXPLAIN
-> Filter: (t2.b = 'b')  (rows=10)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=10)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (b = 'b') group by a1,a2;
EXPLAIN
-> Filter: (t2.b = 'b')  (rows=10)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=10)

explain format=tree select a1,a2, max(c)         from t2 where (b = 'b') group by a1,a2;
EXPLAIN
-> Filter: (t2.b = 'b')  (rows=10)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=10)

explain format=tree select a1,a2,b,max(c),min(c) from t3 where (a2 = 'a') and (b = 'b') group by a1;
EXPLAIN
-> Filter: ((t3.b = 'b') and (t3.a2 = 'a'))  (rows=4)
    -> Covering index skip scan for grouping on t3 using idx_t3_1  (rows=4)

explain format=tree select a1,max(c),min(c)      from t3 where (a2 = 'a') and (b = 'b') group by a1;
EXPLAIN
-> Filter: ((t3.b = 'b') and (t3.a2 = 'a'))  (rows=4)
    -> Covering index skip scan for grouping on t3 using idx_t3_1  (rows=4)

select a1,a2,b,max(c),min(c) from t1 where (a2 = 'a') and (b = 'b') group by a1;
a1	a2	b	max(c)	min(c)
a	a	b	h112	e112
b	a	b	h212	e212
c	a	b	h312	e312
d	a	b	h412	e412
select a1,max(c),min(c)      from t1 where (a2 = 'a') and (b = 'b') group by a1;
a1	max(c)	min(c)
a	h112	e112
b	h212	e212
c	h312	e312
d	h412	e412
select a1,a2,b,       max(c) from t1 where (b = 'b') group by a1,a2;
a1	a2	b	max(c)
a	a	b	h112
a	b	b	p122
b	a	b	h212
b	b	b	p222
c	a	b	h312
c	b	b	p322
d	a	b	h412
d	b	b	p422
select a1,a2,b,min(c),max(c) from t1 where (b = 'b') group by a1,a2;
a1	a2	b	min(c)	max(c)
a	a	b	e112	h112
a	b	b	m122	p122
b	a	b	e212	h212
b	b	b	m222	p222
c	a	b	e312	h312
c	b	b	m322	p322
d	a	b	e412	h412
d	b	b	m422	p422
select a1,a2, max(c)         from t1 where (b = 'b') group by a1,a2;
a1	a2	max(c)
a	a	h112
a	b	p122
b	a	h212
b	b	p222
c	a	h312
c	b	p322
d	a	h412
d	b	p422
select a1,a2,b,max(c),min(c) from t2 where (a2 = 'a') and (b = 'b') group by a1;
a1	a2	b	max(c)	min(c)
a	a	b	h112	e112
b	a	b	h212	e212
c	a	b	h312	e312
d	a	b	h412	e412
e	a	b	NULL	NULL
select a1,max(c),min(c)      from t2 where (a2 = 'a') and (b = 'b') group by a1;
a1	max(c)	min(c)
a	h112	e112
b	h212	e212
c	h312	e312
d	h412	e412
e	NULL	NULL
select a1,a2,b,       max(c) from t2 where (b = 'b') group by a1,a2;
a1	a2	b	max(c)
a	a	b	h112
a	b	b	p122
b	a	b	h212
b	b	b	p222
c	a	b	h312
c	b	b	p322
d	a	b	h412
d	b	b	p422
e	a	b	NULL
select a1,a2,b,min(c),max(c) from t2 where (b = 'b') group by a1,a2;
a1	a2	b	min(c)	max(c)
a	a	b	e112	h112
a	b	b	m122	p122
b	a	b	e212	h212
b	b	b	m222	p222
c	a	b	e312	h312
c	b	b	m322	p322
d	a	b	e412	h412
d	b	b	m422	p422
e	a	b	NULL	NULL
select a1,a2, max(c)         from t2 where (b = 'b') group by a1,a2;
a1	a2	max(c)
a	a	h112
a	b	p122
b	a	h212
b	b	p222
c	a	h312
c	b	p322
d	a	h412
d	b	p422
e	a	NULL
select a1,a2,b,max(c),min(c) from t3 where (a2 = 'a') and (b = 'b') group by a1;
a1	a2	b	max(c)	min(c)
a	a	b	h112	e112
b	a	b	h212	e212
c	a	b	h312	e312
select a1,max(c),min(c)      from t3 where (a2 = 'a') and (b = 'b') group by a1;
a1	max(c)	min(c)
a	h112	e112
b	h212	e212
c	h312	e312
explain format=tree select a1,a2,b,min(c) from t2 where (a2 = 'a') and b is NULL group by a1;
EXPLAIN
-> Filter: ((t2.a2 = 'a') and (t2.b is null))  (rows=5)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=5)

explain format=tree select a1,a2,b,max(c) from t2 where (a2 = 'a') and b is NULL group by a1;
EXPLAIN
-> Filter: ((t2.a2 = 'a') and (t2.b is null))  (rows=5)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=5)

explain format=tree select a1,a2,b,min(c) from t2 where b is NULL group by a1,a2;
EXPLAIN
-> Filter: (t2.b is null)  (rows=10)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=10)

explain format=tree select a1,a2,b,max(c) from t2 where b is NULL group by a1,a2;
EXPLAIN
-> Filter: (t2.b is null)  (rows=10)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=10)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where b is NULL group by a1,a2;
EXPLAIN
-> Filter: (t2.b is null)  (rows=10)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=10)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where b is NULL group by a1,a2;
EXPLAIN
-> Filter: (t2.b is null)  (rows=10)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=10)

select a1,a2,b,min(c) from t2 where (a2 = 'a') and b is NULL group by a1;
a1	a2	b	min(c)
a	a	NULL	a777
c	a	NULL	c777
select a1,a2,b,max(c) from t2 where (a2 = 'a') and b is NULL group by a1;
a1	a2	b	max(c)
a	a	NULL	a999
c	a	NULL	c999
select a1,a2,b,min(c) from t2 where b is NULL group by a1,a2;
a1	a2	b	min(c)
a	a	NULL	a777
c	a	NULL	c777
select a1,a2,b,max(c) from t2 where b is NULL group by a1,a2;
a1	a2	b	max(c)
a	a	NULL	a999
c	a	NULL	c999
select a1,a2,b,min(c),max(c) from t2 where b is NULL group by a1,a2;
a1	a2	b	min(c)	max(c)
a	a	NULL	a777	a999
c	a	NULL	c777	c999
select a1,a2,b,min(c),max(c) from t2 where b is NULL group by a1,a2;
a1	a2	b	min(c)	max(c)
a	a	NULL	a777	a999
c	a	NULL	c777	c999
explain format=tree select a1,a2,b,       max(c) from t1 where (c > 'b1') group by a1,a2,b;
EXPLAIN
-> Filter: (t1.c > 'b1')  (rows=5.67)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over ('b1' < c)  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (c > 'b1') group by a1,a2,b;
EXPLAIN
-> Filter: (t1.c > 'b1')  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over ('b1' < c)  (rows=17)

explain format=tree select a1,a2,b,       max(c) from t1 where (c > 'f123') group by a1,a2,b;
EXPLAIN
-> Filter: (t1.c > 'f123')  (rows=5.67)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over ('f123' < c)  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (c > 'f123') group by a1,a2,b;
EXPLAIN
-> Filter: (t1.c > 'f123')  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over ('f123' < c)  (rows=17)

explain format=tree select a1,a2,b,       max(c) from t1 where (c < 'a0') group by a1,a2,b;
EXPLAIN
-> Filter: (t1.c < 'a0')  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (c < 'a0')  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (c < 'a0') group by a1,a2,b;
EXPLAIN
-> Filter: (t1.c < 'a0')  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (c < 'a0')  (rows=17)

explain format=tree select a1,a2,b,       max(c) from t1 where (c < 'k321') group by a1,a2,b;
EXPLAIN
-> Filter: (t1.c < 'k321')  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (c < 'k321')  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (c < 'k321') group by a1,a2,b;
EXPLAIN
-> Filter: (t1.c < 'k321')  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (c < 'k321')  (rows=17)

explain format=tree select a1,a2,b,       max(c) from t1 where (c < 'a0') or (c > 'b1') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.c < 'a0') or (t1.c > 'b1'))  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (c < 'a0') OR ('b1' < c)  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (c < 'a0') or (c > 'b1') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.c < 'a0') or (t1.c > 'b1'))  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (c < 'a0') OR ('b1' < c)  (rows=17)

explain format=tree select a1,a2,b,       max(c) from t1 where (c > 'b1') or (c <= 'g1') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.c > 'b1') or (t1.c <= 'g1'))  (rows=9.44)
    -> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (c > 'b1') or (c <= 'g1') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.c > 'b1') or (t1.c <= 'g1'))  (rows=9.44)
    -> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (c > 'b111') and (c <= 'g112') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.c > 'b111') and (t1.c <= 'g112'))  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over ('b111' < c <= 'g112')  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (c < 'c5') or (c = 'g412') or (c = 'k421') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.c < 'c5') or (t1.c = 'g412') or (t1.c = 'k421'))  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (c < 'c5') OR (c = 'g412') OR (c = 'k421')  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where ((c > 'b111') and (c <= 'g112')) or ((c > 'd000') and (c <= 'i110')) group by a1,a2,b;
EXPLAIN
-> Filter: (((t1.c > 'b111') and (t1.c <= 'g112')) or ((t1.c > 'd000') and (t1.c <= 'i110')))  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over ('b111' < c <= 'i110')  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (c between 'b111' and 'g112') or (c between 'd000' and 'i110') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.c between 'b111' and 'g112') or (t1.c between 'd000' and 'i110'))  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over ('b111' <= c <= 'i110')  (rows=17)

explain format=tree select a1,a2,b,       max(c) from t2 where (c > 'b1') group by a1,a2,b;
EXPLAIN
-> Filter: (t2.c > 'b1')  (rows=11)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over ('b1' < c)  (rows=33)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (c > 'b1') group by a1,a2,b;
EXPLAIN
-> Filter: (t2.c > 'b1')  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over ('b1' < c)  (rows=33)

explain format=tree select a1,a2,b,       max(c) from t2 where (c > 'f123') group by a1,a2,b;
EXPLAIN
-> Filter: (t2.c > 'f123')  (rows=11)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over ('f123' < c)  (rows=33)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (c > 'f123') group by a1,a2,b;
EXPLAIN
-> Filter: (t2.c > 'f123')  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over ('f123' < c)  (rows=33)

explain format=tree select a1,a2,b,       max(c) from t2 where (c < 'a0') group by a1,a2,b;
EXPLAIN
-> Filter: (t2.c < 'a0')  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < c < 'a0')  (rows=33)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (c < 'a0') group by a1,a2,b;
EXPLAIN
-> Filter: (t2.c < 'a0')  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < c < 'a0')  (rows=33)

explain format=tree select a1,a2,b,       max(c) from t2 where (c < 'k321') group by a1,a2,b;
EXPLAIN
-> Filter: (t2.c < 'k321')  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < c < 'k321')  (rows=33)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (c < 'k321') group by a1,a2,b;
EXPLAIN
-> Filter: (t2.c < 'k321')  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < c < 'k321')  (rows=33)

explain format=tree select a1,a2,b,       max(c) from t2 where (c < 'a0') or (c > 'b1') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.c < 'a0') or (t2.c > 'b1'))  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < c < 'a0') OR ('b1' < c)  (rows=33)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (c < 'a0') or (c > 'b1') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.c < 'a0') or (t2.c > 'b1'))  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < c < 'a0') OR ('b1' < c)  (rows=33)

explain format=tree select a1,a2,b,       max(c) from t2 where (c > 'b1') or (c <= 'g1') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.c > 'b1') or (t2.c <= 'g1'))  (rows=18.3)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < c)  (rows=33)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (c > 'b1') or (c <= 'g1') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.c > 'b1') or (t2.c <= 'g1'))  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < c)  (rows=33)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (c > 'b111') and (c <= 'g112') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.c > 'b111') and (t2.c <= 'g112'))  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over ('b111' < c <= 'g112')  (rows=33)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (c < 'c5') or (c = 'g412') or (c = 'k421') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.c < 'c5') or (t2.c = 'g412') or (t2.c = 'k421'))  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < c < 'c5') OR (c = 'g412') OR (c = 'k421')  (rows=33)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where ((c > 'b111') and (c <= 'g112')) or ((c > 'd000') and (c <= 'i110')) group by a1,a2,b;
EXPLAIN
-> Filter: (((t2.c > 'b111') and (t2.c <= 'g112')) or ((t2.c > 'd000') and (t2.c <= 'i110')))  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over ('b111' < c <= 'i110')  (rows=33)

select a1,a2,b,       max(c) from t1 where (c > 'b1') group by a1,a2,b;
a1	a2	b	max(c)
a	a	a	d111
a	a	b	h112
a	b	a	l121
a	b	b	p122
b	a	a	d211
b	a	b	h212
b	b	a	l221
b	b	b	p222
c	a	a	d311
c	a	b	h312
c	b	a	l321
c	b	b	p322
d	a	a	d411
d	a	b	h412
d	b	a	l421
d	b	b	p422
select a1,a2,b,min(c),max(c) from t1 where (c > 'b1') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	b111	d111
a	a	b	e112	h112
a	b	a	i121	l121
a	b	b	m122	p122
b	a	a	b211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	a	b311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	b411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,       max(c) from t1 where (c > 'f123') group by a1,a2,b;
a1	a2	b	max(c)
a	a	b	h112
a	b	a	l121
a	b	b	p122
b	a	b	h212
b	b	a	l221
b	b	b	p222
c	a	b	h312
c	b	a	l321
c	b	b	p322
d	a	b	h412
d	b	a	l421
d	b	b	p422
select a1,a2,b,min(c),max(c) from t1 where (c > 'f123') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	b	g112	h112
a	b	a	i121	l121
a	b	b	m122	p122
b	a	b	f212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	b	f312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	b	f412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,       max(c) from t1 where (c < 'a0') group by a1,a2,b;
a1	a2	b	max(c)
select a1,a2,b,min(c),max(c) from t1 where (c < 'a0') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
select a1,a2,b,       max(c) from t1 where (c < 'k321') group by a1,a2,b;
a1	a2	b	max(c)
a	a	a	d111
a	a	b	h112
a	b	a	k121
b	a	a	d211
b	a	b	h212
b	b	a	k221
c	a	a	d311
c	a	b	h312
c	b	a	j321
d	a	a	d411
d	a	b	h412
d	b	a	j421
select a1,a2,b,min(c),max(c) from t1 where (c < 'k321') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	a111	d111
a	a	b	e112	h112
a	b	a	i121	k121
b	a	a	a211	d211
b	a	b	e212	h212
b	b	a	i221	k221
c	a	a	a311	d311
c	a	b	e312	h312
c	b	a	i321	j321
d	a	a	a411	d411
d	a	b	e412	h412
d	b	a	i421	j421
select a1,a2,b,       max(c) from t1 where (c < 'a0') or (c > 'b1') group by a1,a2,b;
a1	a2	b	max(c)
a	a	a	d111
a	a	b	h112
a	b	a	l121
a	b	b	p122
b	a	a	d211
b	a	b	h212
b	b	a	l221
b	b	b	p222
c	a	a	d311
c	a	b	h312
c	b	a	l321
c	b	b	p322
d	a	a	d411
d	a	b	h412
d	b	a	l421
d	b	b	p422
select a1,a2,b,min(c),max(c) from t1 where (c < 'a0') or (c > 'b1') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	b111	d111
a	a	b	e112	h112
a	b	a	i121	l121
a	b	b	m122	p122
b	a	a	b211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	a	b311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	b411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,       max(c) from t1 where (c > 'b1') or (c <= 'g1') group by a1,a2,b;
a1	a2	b	max(c)
a	a	a	d111
a	a	b	h112
a	b	a	l121
a	b	b	p122
b	a	a	d211
b	a	b	h212
b	b	a	l221
b	b	b	p222
c	a	a	d311
c	a	b	h312
c	b	a	l321
c	b	b	p322
d	a	a	d411
d	a	b	h412
d	b	a	l421
d	b	b	p422
select a1,a2,b,min(c),max(c) from t1 where (c > 'b1') or (c <= 'g1') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	a111	d111
a	a	b	e112	h112
a	b	a	i121	l121
a	b	b	m122	p122
b	a	a	a211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	a	a311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	a411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,min(c),max(c) from t1 where (c > 'b111') and (c <= 'g112') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	c111	d111
a	a	b	e112	g112
b	a	a	b211	d211
b	a	b	e212	f212
c	a	a	b311	d311
c	a	b	e312	f312
d	a	a	b411	d411
d	a	b	e412	f412
select a1,a2,b,min(c),max(c) from t1 where (c < 'c5') or (c = 'g412') or (c = 'k421') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	a111	c111
b	a	a	a211	c211
c	a	a	a311	c311
d	a	a	a411	c411
d	a	b	g412	g412
d	b	a	k421	k421
select a1,a2,b,min(c),max(c) from t1 where ((c > 'b111') and (c <= 'g112')) or ((c > 'd000') and (c <= 'i110')) group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	c111	d111
a	a	b	e112	h112
b	a	a	b211	d211
b	a	b	e212	h212
c	a	a	b311	d311
c	a	b	e312	h312
d	a	a	b411	d411
d	a	b	e412	h412
select a1,a2,b,min(c),max(c) from t1 where (c between 'b111' and 'g112') or (c between 'd000' and 'i110') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	b111	d111
a	a	b	e112	h112
b	a	a	b211	d211
b	a	b	e212	h212
c	a	a	b311	d311
c	a	b	e312	h312
d	a	a	b411	d411
d	a	b	e412	h412
select a1,a2,b,       max(c) from t2 where (c > 'b1') group by a1,a2,b;
a1	a2	b	max(c)
a	a	a	d111
a	a	b	h112
a	b	a	l121
a	b	b	p122
b	a	a	d211
b	a	b	h212
b	b	a	l221
b	b	b	p222
c	a	NULL	c999
c	a	a	d311
c	a	b	h312
c	b	a	l321
c	b	b	p322
d	a	a	d411
d	a	b	h412
d	b	a	l421
d	b	b	p422
select a1,a2,b,min(c),max(c) from t2 where (c > 'b1') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	b111	d111
a	a	b	e112	h112
a	b	a	i121	l121
a	b	b	m122	p122
b	a	a	b211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	NULL	c777	c999
c	a	a	b311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	b411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,       max(c) from t2 where (c > 'f123') group by a1,a2,b;
a1	a2	b	max(c)
a	a	b	h112
a	b	a	l121
a	b	b	p122
b	a	b	h212
b	b	a	l221
b	b	b	p222
c	a	b	h312
c	b	a	l321
c	b	b	p322
d	a	b	h412
d	b	a	l421
d	b	b	p422
select a1,a2,b,min(c),max(c) from t2 where (c > 'f123') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	b	g112	h112
a	b	a	i121	l121
a	b	b	m122	p122
b	a	b	f212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	b	f312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	b	f412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,       max(c) from t2 where (c < 'a0') group by a1,a2,b;
a1	a2	b	max(c)
select a1,a2,b,min(c),max(c) from t2 where (c < 'a0') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
select a1,a2,b,       max(c) from t2 where (c < 'k321') group by a1,a2,b;
a1	a2	b	max(c)
a	a	NULL	a999
a	a	a	d111
a	a	b	h112
a	b	a	k121
b	a	a	d211
b	a	b	h212
b	b	a	k221
c	a	NULL	c999
c	a	a	d311
c	a	b	h312
c	b	a	j321
d	a	a	d411
d	a	b	h412
d	b	a	j421
select a1,a2,b,min(c),max(c) from t2 where (c < 'k321') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	NULL	a777	a999
a	a	a	a111	d111
a	a	b	e112	h112
a	b	a	i121	k121
b	a	a	a211	d211
b	a	b	e212	h212
b	b	a	i221	k221
c	a	NULL	c777	c999
c	a	a	a311	d311
c	a	b	e312	h312
c	b	a	i321	j321
d	a	a	a411	d411
d	a	b	e412	h412
d	b	a	i421	j421
select a1,a2,b,       max(c) from t2 where (c < 'a0') or (c > 'b1') group by a1,a2,b;
a1	a2	b	max(c)
a	a	a	d111
a	a	b	h112
a	b	a	l121
a	b	b	p122
b	a	a	d211
b	a	b	h212
b	b	a	l221
b	b	b	p222
c	a	NULL	c999
c	a	a	d311
c	a	b	h312
c	b	a	l321
c	b	b	p322
d	a	a	d411
d	a	b	h412
d	b	a	l421
d	b	b	p422
select a1,a2,b,min(c),max(c) from t2 where (c < 'a0') or (c > 'b1') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	b111	d111
a	a	b	e112	h112
a	b	a	i121	l121
a	b	b	m122	p122
b	a	a	b211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	NULL	c777	c999
c	a	a	b311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	b411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,       max(c) from t2 where (c > 'b1') or (c <= 'g1') group by a1,a2,b;
a1	a2	b	max(c)
a	a	NULL	a999
a	a	a	d111
a	a	b	h112
a	b	a	l121
a	b	b	p122
b	a	a	d211
b	a	b	h212
b	b	a	l221
b	b	b	p222
c	a	NULL	c999
c	a	a	d311
c	a	b	h312
c	b	a	l321
c	b	b	p322
d	a	a	d411
d	a	b	h412
d	b	a	l421
d	b	b	p422
select a1,a2,b,min(c),max(c) from t2 where (c > 'b1') or (c <= 'g1') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	NULL	a777	a999
a	a	a	a111	d111
a	a	b	e112	h112
a	b	a	i121	l121
a	b	b	m122	p122
b	a	a	a211	d211
b	a	b	e212	h212
b	b	a	i221	l221
b	b	b	m222	p222
c	a	NULL	c777	c999
c	a	a	a311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	a411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,min(c),max(c) from t2 where (c > 'b111') and (c <= 'g112') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	c111	d111
a	a	b	e112	g112
b	a	a	b211	d211
b	a	b	e212	f212
c	a	NULL	c777	c999
c	a	a	b311	d311
c	a	b	e312	f312
d	a	a	b411	d411
d	a	b	e412	f412
select a1,a2,b,min(c),max(c) from t2 where (c < 'c5') or (c = 'g412') or (c = 'k421') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	NULL	a777	a999
a	a	a	a111	c111
b	a	a	a211	c211
c	a	a	a311	c311
d	a	a	a411	c411
d	a	b	g412	g412
d	b	a	k421	k421
select a1,a2,b,min(c),max(c) from t2 where ((c > 'b111') and (c <= 'g112')) or ((c > 'd000') and (c <= 'i110')) group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	c111	d111
a	a	b	e112	h112
b	a	a	b211	d211
b	a	b	e212	h212
c	a	NULL	c777	c999
c	a	a	b311	d311
c	a	b	e312	h312
d	a	a	b411	d411
d	a	b	e412	h412
explain format=tree select a1,a2,b,min(c),max(c) from t1
where exists ( select * from t2 where t2.c = t1.c )
group by a1,a2,b;
EXPLAIN
-> Group aggregate: min(t1.c), max(t1.c)  (rows=16)
    -> Nested loop inner join  (rows=20992)
        -> Covering index scan on t1 using idx_t1_1  (rows=128)
        -> Single-row index lookup on <subquery2> using <auto_distinct_key> (c = t1.c)  (rows=1)
            -> Materialize with deduplication  (rows=164)
                -> Filter: (t2.c is not null)  (rows=164)
                    -> Covering index scan on t2 using idx_t2_1  (rows=164)

Warnings:
Note	1276	Field or reference 'test.t1.c' of SELECT #2 was resolved in SELECT #1
explain format=tree select a1,a2,b,min(c),max(c) from t1
where exists ( select * from t2 where t2.c > 'b1' )
group by a1,a2,b;
EXPLAIN
-> Table scan on <temporary>
    -> Aggregate using temporary table
        -> Inner hash join (FirstMatch)  (rows=128)
            -> Covering index scan on t1 using idx_t1_1  (rows=128)
            -> Hash
                -> Limit: 1 row(s)  (rows=1)
                    -> Filter: (t2.c > 'b1')  (rows=54.7)
                        -> Covering index scan on t2 using idx_t2_1  (rows=164)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (a1 >= 'c' or a2 < 'b') and (b > 'a') group by a1,a2,b;
EXPLAIN
-> Filter: (((t1.a1 >= 'c') or (t1.a2 < 'b')) and (t1.b > 'a'))  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (a1 >= 'c' or a2 < 'b') and (c > 'b111') group by a1,a2,b;
EXPLAIN
-> Filter: (((t1.a1 >= 'c') or (t1.a2 < 'b')) and (t1.c > 'b111'))  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over ('b111' < c)  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t1 where (a2 >= 'b') and (b = 'a') and (c > 'b111') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.b = 'a') and (t1.a2 >= 'b') and (t1.c > 'b111'))  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over ('b111' < c)  (rows=17)

explain format=tree select a1,a2,b,min(c) from t1 where ((a1 > 'a') or (a1 < '9'))  and ((a2 >= 'b') and (a2 < 'z')) and (b = 'a') and ((c < 'h112') or (c = 'j121') or (c > 'k121' and c < 'm122') or (c > 'o122')) group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.b = 'a') and ((t1.a1 > 'a') or (t1.a1 < '9')) and (t1.a2 >= 'b') and (t1.a2 < 'z') and ((t1.c < 'h112') or (t1.c = 'j121') or ((t1.c > 'k121') and (t1.c < 'm122')) or (t1.c > 'o122')))  (rows=14)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (c < 'h112') OR (c = 'j121') OR (2 more)  (rows=14)

explain format=tree select a1,a2,b,min(c) from t1 where ((a1 > 'a') or (a1 < '9'))  and ((a2 >= 'b') and (a2 < 'z')) and (b = 'a') and ((c = 'j121') or (c > 'k121' and c < 'm122') or (c > 'o122') or (c < 'h112') or (c = 'c111')) group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.b = 'a') and ((t1.a1 > 'a') or (t1.a1 < '9')) and (t1.a2 >= 'b') and (t1.a2 < 'z') and ((t1.c = 'j121') or ((t1.c > 'k121') and (t1.c < 'm122')) or (t1.c > 'o122') or (t1.c < 'h112') or (t1.c = 'c111')))  (rows=14)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (c < 'h112') OR (c = 'j121') OR (2 more)  (rows=14)

explain format=tree select a1,a2,b,min(c) from t1 where (a1 > 'a') and (a2 > 'a') and (b = 'c') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.b = 'c') and (t1.a1 > 'a') and (t1.a2 > 'a'))  (rows=14)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over ('a' < a1)  (rows=14)

explain format=tree select a1,a2,b,min(c) from t1 where (ord(a1) > 97) and (ord(a2) + ord(a1) > 194) and (b = 'c') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.b = 'c') and (ord(t1.a1) > 97) and ((ord(t1.a2) + ord(t1.a1)) > 194))  (rows=17)
    -> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=17)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (a1 >= 'c' or a2 < 'b') and (b > 'a') group by a1,a2,b;
EXPLAIN
-> Filter: (((t2.a1 >= 'c') or (t2.a2 < 'b')) and (t2.b > 'a'))  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1  (rows=33)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (a1 >= 'c' or a2 < 'b') and (c > 'b111') group by a1,a2,b;
EXPLAIN
-> Filter: (((t2.a1 >= 'c') or (t2.a2 < 'b')) and (t2.c > 'b111'))  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over ('b111' < c)  (rows=33)

explain format=tree select a1,a2,b,min(c),max(c) from t2 where (a2 >= 'b') and (b = 'a') and (c > 'b111') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.b = 'a') and (t2.a2 >= 'b') and (t2.c > 'b111'))  (rows=33)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over ('b111' < c)  (rows=33)

explain format=tree select a1,a2,b,min(c) from t2 where ((a1 > 'a') or (a1 < '9'))  and ((a2 >= 'b') and (a2 < 'z')) and (b = 'a') and ((c < 'h112') or (c = 'j121') or (c > 'k121' and c < 'm122') or (c > 'o122')) group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.b = 'a') and ((t2.a1 > 'a') or (t2.a1 < '9')) and (t2.a2 >= 'b') and (t2.a2 < 'z') and ((t2.c < 'h112') or (t2.c = 'j121') or ((t2.c > 'k121') and (t2.c < 'm122')) or (t2.c > 'o122')))  (rows=25)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < c < 'h112') OR (c = 'j121') OR (2 more)  (rows=25)

explain format=tree select a1,a2,b,min(c) from t2 where ((a1 > 'a') or (a1 < '9'))  and ((a2 >= 'b') and (a2 < 'z')) and (b = 'a') and ((c = 'j121') or (c > 'k121' and c < 'm122') or (c > 'o122') or (c < 'h112') or (c = 'c111')) group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.b = 'a') and ((t2.a1 > 'a') or (t2.a1 < '9')) and (t2.a2 >= 'b') and (t2.a2 < 'z') and ((t2.c = 'j121') or ((t2.c > 'k121') and (t2.c < 'm122')) or (t2.c > 'o122') or (t2.c < 'h112') or (t2.c = 'c111')))  (rows=25)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over (NULL < c < 'h112') OR (c = 'j121') OR (2 more)  (rows=25)

explain format=tree select a1,a2,b,min(c) from t2 where (a1 > 'a') and (a2 > 'a') and (b = 'c') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.b = 'c') and (t2.a1 > 'a') and (t2.a2 > 'a'))  (rows=25)
    -> Covering index skip scan for grouping on t2 using idx_t2_1 over ('a' < a1)  (rows=25)

select a1,a2,b,min(c),max(c) from t1 where (a1 >= 'c' or a2 < 'b') and (b > 'a') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	b	e112	h112
b	a	b	e212	h212
c	a	b	e312	h312
c	b	b	m322	p322
d	a	b	e412	h412
d	b	b	m422	p422
select a1,a2,b,min(c),max(c) from t1 where (a1 >= 'c' or a2 < 'b') and (c > 'b111') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	c111	d111
a	a	b	e112	h112
b	a	a	b211	d211
b	a	b	e212	h212
c	a	a	b311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	b411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,min(c),max(c) from t1 where (a2 >= 'b') and (b = 'a') and (c > 'b111') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	b	a	i121	l121
b	b	a	i221	l221
c	b	a	i321	l321
d	b	a	i421	l421
select a1,a2,b,min(c) from t1 where ((a1 > 'a') or (a1 < '9'))  and ((a2 >= 'b') and (a2 < 'z')) and (b = 'a') and ((c < 'h112') or (c = 'j121') or (c > 'k121' and c < 'm122') or (c > 'o122')) group by a1,a2,b;
a1	a2	b	min(c)
b	b	a	k221
c	b	a	k321
d	b	a	k421
select a1,a2,b,min(c) from t1 where ((a1 > 'a') or (a1 < '9'))  and ((a2 >= 'b') and (a2 < 'z')) and (b = 'a') and ((c = 'j121') or (c > 'k121' and c < 'm122') or (c > 'o122') or (c < 'h112') or (c = 'c111')) group by a1,a2,b;
a1	a2	b	min(c)
b	b	a	k221
c	b	a	k321
d	b	a	k421
select a1,a2,b,min(c) from t1 where (a1 > 'a') and (a2 > 'a') and (b = 'c') group by a1,a2,b;
a1	a2	b	min(c)
select a1,a2,b,min(c) from t1 where (ord(a1) > 97) and (ord(a2) + ord(a1) > 194) and (b = 'c') group by a1,a2,b;
a1	a2	b	min(c)
select a1,a2,b,min(c),max(c) from t2 where (a1 >= 'c' or a2 < 'b') and (b > 'a') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	b	e112	h112
b	a	b	e212	h212
c	a	b	e312	h312
c	b	b	m322	p322
d	a	b	e412	h412
d	b	b	m422	p422
e	a	b	NULL	NULL
select a1,a2,b,min(c),max(c) from t2 where (a1 >= 'c' or a2 < 'b') and (c > 'b111') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	a	a	c111	d111
a	a	b	e112	h112
b	a	a	b211	d211
b	a	b	e212	h212
c	a	NULL	c777	c999
c	a	a	b311	d311
c	a	b	e312	h312
c	b	a	i321	l321
c	b	b	m322	p322
d	a	a	b411	d411
d	a	b	e412	h412
d	b	a	i421	l421
d	b	b	m422	p422
select a1,a2,b,min(c),max(c) from t2 where (a2 >= 'b') and (b = 'a') and (c > 'b111') group by a1,a2,b;
a1	a2	b	min(c)	max(c)
a	b	a	i121	l121
b	b	a	i221	l221
c	b	a	i321	l321
d	b	a	i421	l421
select a1,a2,b,min(c) from t2 where ((a1 > 'a') or (a1 < '9'))  and ((a2 >= 'b') and (a2 < 'z')) and (b = 'a') and ((c < 'h112') or (c = 'j121') or (c > 'k121' and c < 'm122') or (c > 'o122')) group by a1,a2,b;
a1	a2	b	min(c)
b	b	a	k221
c	b	a	k321
d	b	a	k421
select a1,a2,b,min(c) from t2 where ((a1 > 'a') or (a1 < '9'))  and ((a2 >= 'b') and (a2 < 'z')) and (b = 'a') and ((c = 'j121') or (c > 'k121' and c < 'm122') or (c > 'o122') or (c < 'h112') or (c = 'c111')) group by a1,a2,b;
a1	a2	b	min(c)
b	b	a	k221
c	b	a	k321
d	b	a	k421
select a1,a2,b,min(c) from t2 where (a1 > 'a') and (a2 > 'a') and (b = 'c') group by a1,a2,b;
a1	a2	b	min(c)
explain format=tree select a1,a2,b from t1 where (a1 >= 'c' or a2 < 'b') and (b > 'a') group by a1,a2,b;
EXPLAIN
-> Filter: (((t1.a1 >= 'c') or (t1.a2 < 'b')) and (t1.b > 'a'))  (rows=17)
    -> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=17)

explain format=tree select a1,a2,b from t1 where (a1 >= 'c' or a2 < 'b') and (b > 'a') group by a1,a2,b;
EXPLAIN
-> Filter: (((t1.a1 >= 'c') or (t1.a2 < 'b')) and (t1.b > 'a'))  (rows=17)
    -> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=17)

explain format=tree select a1,a2,b from t1 where (a2 >= 'b') and (b = 'a') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.b = 'a') and (t1.a2 >= 'b'))  (rows=17)
    -> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=17)

explain format=tree select a1,a2,b,c from t1 where (a2 >= 'b') and (b = 'a') and (c = 'i121') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.c = 'i121') and (t1.b = 'a') and (t1.a2 >= 'b'))  (rows=17)
    -> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=17)

explain format=tree select a1,a2,b from t1 where (a1 > 'a') and (a2 > 'a') and (b = 'c') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.b = 'c') and (t1.a1 > 'a') and (t1.a2 > 'a'))  (rows=14)
    -> Covering index skip scan for deduplication on t1 using idx_t1_1 over ('a' < a1)  (rows=14)

explain format=tree select a1,a2,b from t2 where (a1 >= 'c' or a2 < 'b') and (b > 'a') group by a1,a2,b;
EXPLAIN
-> Filter: (((t2.a1 >= 'c') or (t2.a2 < 'b')) and (t2.b > 'a'))  (rows=33)
    -> Covering index skip scan for deduplication on t2 using idx_t2_1  (rows=33)

explain format=tree select a1,a2,b from t2 where (a2 >= 'b') and (b = 'a') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.b = 'a') and (t2.a2 >= 'b'))  (rows=33)
    -> Covering index skip scan for deduplication on t2 using idx_t2_1  (rows=33)

explain format=tree select a1,a2,b,c from t2 where (a2 >= 'b') and (b = 'a') and (c = 'i121') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.c = 'i121') and (t2.b = 'a') and (t2.a2 >= 'b'))  (rows=33)
    -> Covering index skip scan for deduplication on t2 using idx_t2_1  (rows=33)

explain format=tree select a1,a2,b from t2 where (a1 > 'a') and (a2 > 'a') and (b = 'c') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.b = 'c') and (t2.a1 > 'a') and (t2.a2 > 'a'))  (rows=25)
    -> Covering index skip scan for deduplication on t2 using idx_t2_1 over ('a' < a1)  (rows=25)

select a1,a2,b from t1 where (a1 >= 'c' or a2 < 'b') and (b > 'a') group by a1,a2,b;
a1	a2	b
a	a	b
b	a	b
c	a	b
c	b	b
d	a	b
d	b	b
select a1,a2,b from t1 where (a2 >= 'b') and (b = 'a') group by a1,a2,b;
a1	a2	b
a	b	a
b	b	a
c	b	a
d	b	a
select a1,a2,b,c from t1 where (a2 >= 'b') and (b = 'a') and (c = 'i121') group by a1,a2,b;
a1	a2	b	c
a	b	a	i121
select a1,a2,b from t1 where (a1 > 'a') and (a2 > 'a') and (b = 'c') group by a1,a2,b;
a1	a2	b
select a1,a2,b from t2 where (a1 >= 'c' or a2 < 'b') and (b > 'a') group by a1,a2,b;
a1	a2	b
a	a	b
b	a	b
c	a	b
c	b	b
d	a	b
d	b	b
e	a	b
select a1,a2,b from t2 where (a2 >= 'b') and (b = 'a') group by a1,a2,b;
a1	a2	b
a	b	a
b	b	a
c	b	a
d	b	a
select a1,a2,b,c from t2 where (a2 >= 'b') and (b = 'a') and (c = 'i121') group by a1,a2,b;
a1	a2	b	c
a	b	a	i121
select a1,a2,b from t2 where (a1 > 'a') and (a2 > 'a') and (b = 'c') group by a1,a2,b;
a1	a2	b
explain format=tree select sql_big_result distinct a1,a2,b from t1;
EXPLAIN
-> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=17)

explain format=tree select distinct a1,a2,b from t1 where (a2 >= 'b') and (b = 'a');
EXPLAIN
-> Filter: ((t1.b = 'a') and (t1.a2 >= 'b'))  (rows=17)
    -> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=17)

explain format=tree select distinct a1,a2,b,c from t1 where (a2 >= 'b') and (b = 'a') and (c = 'i121');
EXPLAIN
-> Group (no aggregates)  (rows=1)
    -> Filter: ((t1.c = 'i121') and (t1.b = 'a') and (t1.a2 >= 'b'))  (rows=1)
        -> Covering index scan on t1 using idx_t1_1  (rows=128)

explain format=tree select distinct a1,a2,b from t1 where (a1 > 'a') and (a2 > 'a') and (b = 'c');
EXPLAIN
-> Filter: ((t1.b = 'c') and (t1.a1 > 'a') and (t1.a2 > 'a'))  (rows=14)
    -> Covering index skip scan for deduplication on t1 using idx_t1_1 over ('a' < a1)  (rows=14)

explain format=tree select distinct b from t1 where (a2 >= 'b') and (b = 'a');
EXPLAIN
-> Group (no aggregates)  (rows=1)
    -> Filter: ((t1.b = 'a') and (t1.a2 >= 'b'))  (rows=4.27)
        -> Covering index scan on t1 using idx_t1_2  (rows=128)

explain format=tree select sql_big_result distinct a1,a2,b from t2;
EXPLAIN
-> Covering index skip scan for deduplication on t2 using idx_t2_1  (rows=33)

explain format=tree select sql_big_result distinct a1,a2,b from t2 where (a2 >= 'b') and (b = 'a');
EXPLAIN
-> Filter: ((t2.b = 'a') and (t2.a2 >= 'b'))  (rows=33)
    -> Covering index skip scan for deduplication on t2 using idx_t2_1  (rows=33)

explain format=tree select distinct a1,a2,b,c from t2 where (a2 >= 'b') and (b = 'a') and (c = 'i121');
EXPLAIN
-> Group (no aggregates)  (rows=1)
    -> Filter: ((t2.c = 'i121') and (t2.b = 'a') and (t2.a2 >= 'b'))  (rows=1)
        -> Covering index scan on t2 using idx_t2_1  (rows=164)

explain format=tree select distinct a1,a2,b from t2 where (a1 > 'a') and (a2 > 'a') and (b = 'c');
EXPLAIN
-> Filter: ((t2.b = 'c') and (t2.a1 > 'a') and (t2.a2 > 'a'))  (rows=25)
    -> Covering index skip scan for deduplication on t2 using idx_t2_1 over ('a' < a1)  (rows=25)

explain format=tree select distinct b from t2 where (a2 >= 'b') and (b = 'a');
EXPLAIN
-> Group (no aggregates)  (rows=1)
    -> Filter: ((t2.b = 'a') and (t2.a2 >= 'b'))  (rows=5.47)
        -> Covering index scan on t2 using idx_t2_2  (rows=164)

select sql_big_result distinct a1,a2,b from t1;
a1	a2	b
a	a	a
a	a	b
a	b	a
a	b	b
b	a	a
b	a	b
b	b	a
b	b	b
c	a	a
c	a	b
c	b	a
c	b	b
d	a	a
d	a	b
d	b	a
d	b	b
select sql_big_result distinct a1,a2,b from t1 where (a2 >= 'b') and (b = 'a');
a1	a2	b
a	b	a
b	b	a
c	b	a
d	b	a
select distinct a1,a2,b,c from t1 where (a2 >= 'b') and (b = 'a') and (c = 'i121');
a1	a2	b	c
a	b	a	i121
select distinct a1,a2,b from t1 where (a1 > 'a') and (a2 > 'a') and (b = 'c');
a1	a2	b
select distinct b from t1 where (a2 >= 'b') and (b = 'a');
b
a
select sql_big_result distinct a1,a2,b from t2;
a1	a2	b
a	a	NULL
a	a	a
a	a	b
a	b	a
a	b	b
b	a	a
b	a	b
b	b	a
b	b	b
c	a	NULL
c	a	a
c	a	b
c	b	a
c	b	b
d	a	a
d	a	b
d	b	a
d	b	b
e	a	a
e	a	b
select distinct a1,a2,b from t2 where (a2 >= 'b') and (b = 'a');
a1	a2	b
a	b	a
b	b	a
c	b	a
d	b	a
select distinct a1,a2,b,c from t2 where (a2 >= 'b') and (b = 'a') and (c = 'i121');
a1	a2	b	c
a	b	a	i121
select distinct a1,a2,b from t2 where (a1 > 'a') and (a2 > 'a') and (b = 'c');
a1	a2	b
select distinct b from t2 where (a2 >= 'b') and (b = 'a');
b
a
select distinct t_00.a1
from t1 t_00
where exists ( select * from t2 where a1 = t_00.a1 );
a1
a
b
c
d
select distinct a1,a1 from t1;
a1	a1
a	a
b	b
c	c
d	d
select distinct a2,a1,a2,a1 from t1;
a2	a1	a2	a1
a	a	a	a
a	b	a	b
a	c	a	c
a	d	a	d
b	a	b	a
b	b	b	b
b	c	b	c
b	d	b	d
select distinct t1.a1,t2.a1 from t1,t2;
a1	a1
a	a
a	b
a	c
a	d
a	e
b	a
b	b
b	c
b	d
b	e
c	a
c	b
c	c
c	d
c	e
d	a
d	b
d	c
d	d
d	e
explain format=tree select sql_big_result distinct a1,a2,b from t1;
EXPLAIN
-> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=17)

explain format=tree select distinct a1,a2,b from t1 where (a2 >= 'b') and (b = 'a') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.b = 'a') and (t1.a2 >= 'b'))  (rows=17)
    -> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=17)

explain format=tree select distinct a1,a2,b,c from t1 where (a2 >= 'b') and (b = 'a') and (c = 'i121') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.c = 'i121') and (t1.b = 'a') and (t1.a2 >= 'b'))  (rows=17)
    -> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=17)

explain format=tree select distinct a1,a2,b from t1 where (a1 > 'a') and (a2 > 'a') and (b = 'c') group by a1,a2,b;
EXPLAIN
-> Filter: ((t1.b = 'c') and (t1.a1 > 'a') and (t1.a2 > 'a'))  (rows=14)
    -> Covering index skip scan for deduplication on t1 using idx_t1_1 over ('a' < a1)  (rows=14)

explain format=tree select distinct b from t1 where (a2 >= 'b') and (b = 'a') group by a1,a2,b;
EXPLAIN
-> Sort with duplicate removal: t1.b
    -> Table scan on <temporary>  (rows=17)
        -> Temporary table with deduplication  (rows=17)
            -> Filter: ((t1.b = 'a') and (t1.a2 >= 'b'))  (rows=17)
                -> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=17)

explain format=tree select sql_big_result distinct a1,a2,b from t2;
EXPLAIN
-> Covering index skip scan for deduplication on t2 using idx_t2_1  (rows=33)

explain format=tree select distinct a1,a2,b from t2 where (a2 >= 'b') and (b = 'a') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.b = 'a') and (t2.a2 >= 'b'))  (rows=33)
    -> Covering index skip scan for deduplication on t2 using idx_t2_1  (rows=33)

explain format=tree select distinct a1,a2,b,c from t2 where (a2 >= 'b') and (b = 'a') and (c = 'i121') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.c = 'i121') and (t2.b = 'a') and (t2.a2 >= 'b'))  (rows=33)
    -> Covering index skip scan for deduplication on t2 using idx_t2_1  (rows=33)

explain format=tree select distinct a1,a2,b from t2 where (a1 > 'a') and (a2 > 'a') and (b = 'c') group by a1,a2,b;
EXPLAIN
-> Filter: ((t2.b = 'c') and (t2.a1 > 'a') and (t2.a2 > 'a'))  (rows=25)
    -> Covering index skip scan for deduplication on t2 using idx_t2_1 over ('a' < a1)  (rows=25)

explain format=tree select distinct b from t2 where (a2 >= 'b') and (b = 'a') group by a1,a2,b;
EXPLAIN
-> Sort with duplicate removal: t2.b
    -> Table scan on <temporary>  (rows=33)
        -> Temporary table with deduplication  (rows=33)
            -> Filter: ((t2.b = 'a') and (t2.a2 >= 'b'))  (rows=33)
                -> Covering index skip scan for deduplication on t2 using idx_t2_1  (rows=33)

select distinct sql_big_result a1,a2,b from t1;
a1	a2	b
a	a	a
a	a	b
a	b	a
a	b	b
b	a	a
b	a	b
b	b	a
b	b	b
c	a	a
c	a	b
c	b	a
c	b	b
d	a	a
d	a	b
d	b	a
d	b	b
select distinct a1,a2,b from t1 where (a2 >= 'b') and (b = 'a') group by a1,a2,b;
a1	a2	b
a	b	a
b	b	a
c	b	a
d	b	a
select distinct a1,a2,b,c from t1 where (a2 >= 'b') and (b = 'a') and (c = 'i121') group by a1,a2,b;
a1	a2	b	c
a	b	a	i121
select distinct a1,a2,b from t1 where (a1 > 'a') and (a2 > 'a') and (b = 'c') group by a1,a2,b;
a1	a2	b
select distinct b from t1 where (a2 >= 'b') and (b = 'a') group by a1,a2,b;
b
a
select sql_big_result distinct a1,a2,b from t2;
a1	a2	b
a	a	NULL
a	a	a
a	a	b
a	b	a
a	b	b
b	a	a
b	a	b
b	b	a
b	b	b
c	a	NULL
c	a	a
c	a	b
c	b	a
c	b	b
d	a	a
d	a	b
d	b	a
d	b	b
e	a	a
e	a	b
select distinct a1,a2,b from t2 where (a2 >= 'b') and (b = 'a') group by a1,a2,b;
a1	a2	b
a	b	a
b	b	a
c	b	a
d	b	a
select distinct a1,a2,b,c from t2 where (a2 >= 'b') and (b = 'a') and (c = 'i121') group by a1,a2,b;
a1	a2	b	c
a	b	a	i121
select distinct a1,a2,b from t2 where (a1 > 'a') and (a2 > 'a') and (b = 'c') group by a1,a2,b;
a1	a2	b
select distinct b from t2 where (a2 >= 'b') and (b = 'a') group by a1,a2,b;
b
a
explain format=tree select count(distinct a1,a2,b) from t1 where (a2 >= 'b') and (b = 'a');
EXPLAIN
-> Aggregate: count(distinct t1.a1,t1.a2,t1.b)  (rows=1)
    -> Filter: ((t1.b = 'a') and (t1.a2 >= 'b'))  (rows=17)
        -> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=17)

explain format=tree select count(distinct a1,a2,b,c) from t1 where (a2 >= 'b') and (b = 'a') and (c = 'i121');
EXPLAIN
-> Aggregate: count(distinct t1.a1,t1.a2,t1.b,t1.c)  (rows=1)
    -> Filter: ((t1.c = 'i121') and (t1.b = 'a') and (t1.a2 >= 'b'))  (rows=65)
        -> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=65)

explain format=tree select count(distinct a1,a2,b) from t1 where (a1 > 'a') and (a2 > 'a') and (b = 'c');
EXPLAIN
-> Aggregate: count(distinct t1.a1,t1.a2,t1.b)  (rows=1)
    -> Filter: ((t1.b = 'c') and (t1.a1 > 'a') and (t1.a2 > 'a'))  (rows=14)
        -> Covering index skip scan for deduplication on t1 using idx_t1_1 over ('a' < a1)  (rows=14)

explain format=tree select count(distinct b) from t1 where (a2 >= 'b') and (b = 'a');
EXPLAIN
-> Aggregate: count(distinct t1.b)  (rows=1)
    -> Filter: ((t1.b = 'a') and (t1.a2 >= 'b'))  (rows=4.27)
        -> Covering index scan on t1 using idx_t1_2  (rows=128)

explain format=tree select 98 + count(distinct a1,a2,b) from t1 where (a1 > 'a') and (a2 > 'a');
EXPLAIN
-> Aggregate: count(distinct t1.a1,t1.a2,t1.b)  (rows=1)
    -> Filter: ((t1.a1 > 'a') and (t1.a2 > 'a'))  (rows=14)
        -> Covering index skip scan for deduplication on t1 using idx_t1_1 over ('a' < a1)  (rows=14)

select count(distinct a1,a2,b) from t1 where (a2 >= 'b') and (b = 'a');
count(distinct a1,a2,b)
4
select count(distinct a1,a2,b,c) from t1 where (a2 >= 'b') and (b = 'a') and (c = 'i121');
count(distinct a1,a2,b,c)
1
select count(distinct a1,a2,b) from t1 where (a1 > 'a') and (a2 > 'a') and (b = 'c');
count(distinct a1,a2,b)
0
select count(distinct b) from t1 where (a2 >= 'b') and (b = 'a');
count(distinct b)
1
select 98 + count(distinct a1,a2,b) from t1 where (a1 > 'a') and (a2 > 'a');
98 + count(distinct a1,a2,b)
104
explain format=tree select a1,a2,b, concat(min(c), max(c)) from t1 where a1 < 'd' group by a1,a2,b;
EXPLAIN
-> Filter: (t1.a1 < 'd')  (rows=10)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (NULL < a1 < 'd')  (rows=10)

explain format=tree select concat(a1,min(c)),b from t1 where a1 < 'd' group by a1,a2,b;
EXPLAIN
-> Filter: (t1.a1 < 'd')  (rows=10)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (NULL < a1 < 'd')  (rows=10)

explain format=tree select concat(a1,min(c)),b,max(c) from t1 where a1 < 'd' group by a1,a2,b;
EXPLAIN
-> Filter: (t1.a1 < 'd')  (rows=10)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (NULL < a1 < 'd')  (rows=10)

explain format=tree select concat(a1,a2),b,min(c),max(c) from t1 where a1 < 'd' group by a1,a2,b;
EXPLAIN
-> Filter: (t1.a1 < 'd')  (rows=10)
    -> Covering index skip scan for grouping on t1 using idx_t1_1 over (NULL < a1 < 'd')  (rows=10)

explain format=tree select concat(ord(min(b)),ord(max(b))),min(b),max(b) from t1 group by a1,a2;
EXPLAIN
-> Covering index skip scan for grouping on t1 using idx_t1_1  (rows=9)

select a1,a2,b, concat(min(c), max(c)) from t1 where a1 < 'd' group by a1,a2,b;
a1	a2	b	concat(min(c), max(c))
a	a	a	a111d111
a	a	b	e112h112
a	b	a	i121l121
a	b	b	m122p122
b	a	a	a211d211
b	a	b	e212h212
b	b	a	i221l221
b	b	b	m222p222
c	a	a	a311d311
c	a	b	e312h312
c	b	a	i321l321
c	b	b	m322p322
select concat(a1,min(c)),b from t1 where a1 < 'd' group by a1,a2,b;
concat(a1,min(c))	b
aa111	a
ae112	b
ai121	a
am122	b
ba211	a
be212	b
bi221	a
bm222	b
ca311	a
ce312	b
ci321	a
cm322	b
select concat(a1,min(c)),b,max(c) from t1 where a1 < 'd' group by a1,a2,b;
concat(a1,min(c))	b	max(c)
aa111	a	d111
ae112	b	h112
ai121	a	l121
am122	b	p122
ba211	a	d211
be212	b	h212
bi221	a	l221
bm222	b	p222
ca311	a	d311
ce312	b	h312
ci321	a	l321
cm322	b	p322
select concat(a1,a2),b,min(c),max(c) from t1 where a1 < 'd' group by a1,a2,b;
concat(a1,a2)	b	min(c)	max(c)
aa	a	a111	d111
aa	b	e112	h112
ab	a	i121	l121
ab	b	m122	p122
ba	a	a211	d211
ba	b	e212	h212
bb	a	i221	l221
bb	b	m222	p222
ca	a	a311	d311
ca	b	e312	h312
cb	a	i321	l321
cb	b	m322	p322
select concat(ord(min(b)),ord(max(b))),min(b),max(b) from t1 group by a1,a2;
concat(ord(min(b)),ord(max(b)))	min(b)	max(b)
9798	a	b
9798	a	b
9798	a	b
9798	a	b
9798	a	b
9798	a	b
9798	a	b
9798	a	b
explain format=tree select a1,a2,b,d,min(c),max(c) from t1 group by a1,a2,b;
EXPLAIN
-> Group aggregate: min(t1.c), max(t1.c)  (rows=16)
    -> Index scan on t1 using idx_t1_2  (rows=128)

explain format=tree select a1,a2,b,d from t1 group by a1,a2,b;
EXPLAIN
-> Group (no aggregates)  (rows=16)
    -> Index scan on t1 using idx_t1_2  (rows=128)

explain format=tree select a1,a2,min(b),max(b) from t1
where (a1 = 'b' or a1 = 'd' or a1 = 'a' or a1 = 'c') and (a2 > 'a') and (c > 'a111') group by a1,a2;
EXPLAIN
-> Group aggregate: min(t1.b), max(t1.b)  (rows=8)
    -> Filter: (((t1.a1 = 'b') or (t1.a1 = 'd') or (t1.a1 = 'a') or (t1.a1 = 'c')) and (t1.a2 > 'a') and (t1.c > 'a111'))  (rows=25.3)
        -> Covering index range scan on t1 using idx_t1_1 over (a1 = 'a' AND 'a' < a2) OR (a1 = 'b' AND 'a' < a2) OR (2 more)  (rows=76)

explain format=tree select a1,a2,b,min(c),max(c) from t1
where (a1 = 'b' or a1 = 'd' or a1 = 'a' or a1 = 'c') and (a2 > 'a') and (d > 'xy2') group by a1,a2,b;
EXPLAIN
-> Group aggregate: min(t1.c), max(t1.c)  (rows=14.1)
    -> Filter: (((t1.a1 = 'b') or (t1.a1 = 'd') or (t1.a1 = 'a') or (t1.a1 = 'c')) and (t1.a2 > 'a') and (t1.d > 'xy2'))  (rows=14.1)
        -> Index scan on t1 using idx_t1_2  (rows=128)

explain format=tree select a1,a2,b,c from t1
where (a1 = 'b' or a1 = 'd' or a1 = 'a' or a1 = 'c') and (a2 > 'a') and (d > 'xy2') group by a1,a2,b,c;
EXPLAIN
-> Group (no aggregates)  (rows=14.1)
    -> Filter: (((t1.a1 = 'b') or (t1.a1 = 'd') or (t1.a1 = 'a') or (t1.a1 = 'c')) and (t1.a2 > 'a') and (t1.d > 'xy2'))  (rows=14.1)
        -> Index scan on t1 using idx_t1_1  (rows=128)

explain format=tree select a1,a2,b,max(c),min(c) from t2 where (a2 = 'a') and (b = 'b') or (b < 'b') group by a1;
EXPLAIN
-> Group aggregate: max(t2.c), min(t2.c)  (rows=4.97)
    -> Filter: (((t2.b = 'b') and (t2.a2 = 'a')) or (t2.b < 'b'))  (rows=55.8)
        -> Covering index scan on t2 using idx_t2_1  (rows=164)

explain format=tree select a1,a2,b from t1 where (a1 = 'b' or a1 = 'd' or a1 = 'a' or a1 = 'c') and (a2 > 'a') and (c > 'a111') group by a1,a2,b;
EXPLAIN
-> Group (no aggregates)  (rows=16)
    -> Filter: (((t1.a1 = 'b') or (t1.a1 = 'd') or (t1.a1 = 'a') or (t1.a1 = 'c')) and (t1.a2 > 'a') and (t1.c > 'a111'))  (rows=25.3)
        -> Covering index range scan on t1 using idx_t1_1 over (a1 = 'a' AND 'a' < a2) OR (a1 = 'b' AND 'a' < a2) OR (2 more)  (rows=76)

explain format=tree select a1,a2,min(b),c from t2 where (a2 = 'a') and (c = 'a111') group by a1;
EXPLAIN
-> Group aggregate: min(t2.b)  (rows=1.28)
    -> Filter: ((t2.c = 'a111') and (t2.a2 = 'a'))  (rows=1.64)
        -> Covering index scan on t2 using idx_t2_1  (rows=164)

select a1,a2,min(b),c from t2 where (a2 = 'a') and (c = 'a111') group by a1;
a1	a2	min(b)	c
a	a	a	a111
explain format=tree select a1,a2,b,max(c),min(c) from t2 where (a2 = 'a') and (b = 'b') or (b = 'a') group by a1;
EXPLAIN
-> Group aggregate: max(t2.c), min(t2.c)  (rows=4.97)
    -> Filter: (((t2.b = 'b') and (t2.a2 = 'a')) or (t2.b = 'a'))  (rows=17.9)
        -> Covering index scan on t2 using idx_t2_1  (rows=164)

explain format=tree select a1,a2,b,min(c),max(c) from t2
where (c > 'a000') and (c <= 'd999') and (c like '_8__') group by a1,a2,b;
EXPLAIN
-> Group aggregate: min(t2.c), max(t2.c)  (rows=1.42)
    -> Filter: ((t2.c > 'a000') and (t2.c <= 'd999') and (t2.c like '_8__'))  (rows=2.02)
        -> Covering index scan on t2 using idx_t2_1  (rows=164)

explain format=tree select a1, a2, b, c, min(d), max(d) from t1 group by a1,a2,b,c;
EXPLAIN
-> Group aggregate: min(t1.d), max(t1.d)  (rows=64)
    -> Index scan on t1 using idx_t1_1  (rows=128)

explain format=tree select a1,a2,count(a2) from t1 group by a1,a2,b;
EXPLAIN
-> Group aggregate: count(t1.a2)  (rows=16)
    -> Covering index scan on t1 using idx_t1_2  (rows=128)

explain format=tree select a1,a2,count(a2) from t1 where (a1 > 'a') group by a1,a2,b;
EXPLAIN
-> Group aggregate: count(t1.a2)  (rows=16)
    -> Filter: (t1.a1 > 'a')  (rows=102)
        -> Covering index range scan on t1 using idx_t1_2 over ('a' < a1)  (rows=102)

explain format=tree select sum(ord(a1)) from t1 where (a1 > 'a') group by a1,a2,b;
EXPLAIN
-> Group aggregate: sum(ord(t1.a1))  (rows=16)
    -> Filter: (t1.a1 > 'a')  (rows=102)
        -> Covering index range scan on t1 using idx_t1_2 over ('a' < a1)  (rows=102)

explain format=tree select sql_big_result distinct(a1) from t1 where ord(a2) = 98;
EXPLAIN
-> Group (no aggregates)  (rows=4)
    -> Sort: t1.a1  (rows=128)
        -> Filter: (ord(t1.a2) = 98)  (rows=128)
            -> Covering index scan on t1 using idx_t1_2  (rows=128)

select sql_big_result distinct(a1) from t1 where ord(a2) = 98;
a1
a
b
c
d
explain format=tree select a1 from t1 where a2 = 'b' group by a1;
EXPLAIN
-> Filter: (t1.a2 = 'b')  (rows=5)
    -> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=5)

select a1 from t1 where a2 = 'b' group by a1;
a1
a
b
c
d
explain format=tree select distinct a1 from t1 where a2 = 'b';
EXPLAIN
-> Filter: (t1.a2 = 'b')  (rows=5)
    -> Covering index skip scan for deduplication on t1 using idx_t1_1  (rows=5)

select distinct a1 from t1 where a2 = 'b';
a1
a
b
c
d
drop table t1,t2,t3;
create table t1 (c1 int not null,c2 int not null, primary key(c1,c2));
insert into t1 (c1,c2) values
(10,1),(10,2),(10,3),(20,4),(20,5),(20,6),(30,7),(30,8),(30,9);
select distinct c1, c2 from t1 order by c2;
c1	c2
10	1
10	2
10	3
20	4
20	5
20	6
30	7
30	8
30	9
select c1,min(c2) as c2 from t1 group by c1 order by c2;
c1	c2
10	1
20	4
30	7
select c1,c2 from t1 group by c1,c2 order by c2;
c1	c2
10	1
10	2
10	3
20	4
20	5
20	6
30	7
30	8
30	9
drop table t1;
CREATE TABLE t1 (a varchar(5), b int(11), PRIMARY KEY (a,b)) charset utf8mb4;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
INSERT INTO t1 VALUES ('AA',1), ('AA',2), ('AA',3), ('BB',1), ('AA',4);
OPTIMIZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	optimize	status	OK
SELECT a FROM t1 WHERE a='AA' GROUP BY a;
a
AA
SELECT a FROM t1 WHERE a='BB' GROUP BY a;
a
BB
EXPLAIN FORMAT=TREE SELECT a FROM t1 WHERE a='AA' GROUP BY a;
EXPLAIN
-> Filter: (t1.a = 'AA')  (rows=1)
    -> Covering index skip scan for deduplication on t1 using PRIMARY over (a = 'AA')  (rows=1)

EXPLAIN FORMAT=TREE SELECT a FROM t1 WHERE a='BB' GROUP BY a;
EXPLAIN
-> Group (no aggregates)  (rows=1)
    -> Covering index lookup on t1 using PRIMARY (a = 'BB')  (rows=1)

SELECT DISTINCT a FROM t1 WHERE a='BB';
a
BB
SELECT DISTINCT a FROM t1 WHERE a LIKE 'B%';
a
BB
SELECT a FROM t1 WHERE a LIKE 'B%' GROUP BY a;
a
BB
DROP TABLE t1;
CREATE TABLE t1 (
a int(11) NOT NULL DEFAULT '0',
b varchar(16) COLLATE latin1_general_ci NOT NULL DEFAULT '',
PRIMARY KEY  (a,b)
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_general_ci;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE PROCEDURE a(x INT)
BEGIN
DECLARE rnd INT;
DECLARE cnt INT;
WHILE x > 0 DO
SET rnd= x % 100;
SET cnt = (SELECT COUNT(*) FROM t1 WHERE a = rnd);
INSERT INTO t1(a,b) VALUES (rnd, CAST(cnt AS CHAR));
SET x= x - 1;
END WHILE;
END|
CALL a(1000);
SELECT a FROM t1 WHERE a=0;
a
0
0
0
0
0
0
0
0
0
0
SELECT DISTINCT a FROM t1 WHERE a=0;
a
0
SELECT COUNT(DISTINCT a) FROM t1 WHERE a=0;
COUNT(DISTINCT a)
1
DROP TABLE t1;
DROP PROCEDURE a;
CREATE TABLE t1 (a varchar(64) NOT NULL default '', PRIMARY KEY(a)) charset utf8mb4;
INSERT INTO t1 (a) VALUES
(''), ('CENTRAL'), ('EASTERN'), ('GREATER LONDON'),
('NORTH CENTRAL'), ('NORTH EAST'), ('NORTH WEST'), ('SCOTLAND'),
('SOUTH EAST'), ('SOUTH WEST'), ('WESTERN');
EXPLAIN FORMAT=TREE SELECT DISTINCT a,a FROM t1 ORDER BY a;
EXPLAIN
-> Covering index scan on t1 using PRIMARY  (rows=11)

SELECT DISTINCT a,a FROM t1 ORDER BY a;
a	a
	
CENTRAL	CENTRAL
EASTERN	EASTERN
GREATER LONDON	GREATER LONDON
NORTH CENTRAL	NORTH CENTRAL
NORTH EAST	NORTH EAST
NORTH WEST	NORTH WEST
SCOTLAND	SCOTLAND
SOUTH EAST	SOUTH EAST
SOUTH WEST	SOUTH WEST
WESTERN	WESTERN
DROP TABLE t1;
CREATE TABLE t1 (id1 INT, id2 INT);
CREATE TABLE t2 (id2 INT, id3 INT, id5 INT);
CREATE TABLE t3 (id3 INT, id4 INT);
CREATE TABLE t4 (id4 INT);
CREATE TABLE t5 (id5 INT, id6 INT);
CREATE TABLE t6 (id6 INT);
INSERT INTO t1 VALUES(1,1);
INSERT INTO t2 VALUES(1,1,1);
INSERT INTO t3 VALUES(1,1);
INSERT INTO t4 VALUES(1);
INSERT INTO t5 VALUES(1,1);
INSERT INTO t6 VALUES(1);
SELECT * FROM
t1
NATURAL JOIN
(t2 JOIN (t3 NATURAL JOIN t4, t5 NATURAL JOIN t6)
ON (t3.id3 = t2.id3 AND t5.id5 = t2.id5));
id2	id1	id3	id5	id4	id3	id6	id5
1	1	1	1	1	1	1	1
SELECT * FROM
t1
NATURAL JOIN
(((t3 NATURAL JOIN t4) join (t5 NATURAL JOIN t6) on t3.id4 = t5.id5) JOIN t2
ON (t3.id3 = t2.id3 AND t5.id5 = t2.id5));
id2	id1	id4	id3	id6	id5	id3	id5
1	1	1	1	1	1	1	1
SELECT * FROM t1 NATURAL JOIN ((t3 join (t5 NATURAL JOIN t6)) JOIN t2);
id2	id1	id3	id4	id6	id5	id3	id5
1	1	1	1	1	1	1	1
SELECT * FROM
(t2 JOIN (t3 NATURAL JOIN t4, t5 NATURAL JOIN t6)
ON (t3.id3 = t2.id3 AND t5.id5 = t2.id5))
NATURAL JOIN
t1;
id2	id3	id5	id4	id3	id6	id5	id1
1	1	1	1	1	1	1	1
SELECT * FROM
(t2 JOIN ((t3 NATURAL JOIN t4) join (t5 NATURAL JOIN t6)))
NATURAL JOIN
t1;
id2	id3	id5	id4	id3	id6	id5	id1
1	1	1	1	1	1	1	1
DROP TABLE t1,t2,t3,t4,t5,t6;
CREATE TABLE t1 (a int, b int, PRIMARY KEY (a,b), KEY b (b));
INSERT INTO t1 VALUES (1,1),(1,2),(1,0),(1,3);
INSERT INTO t1 VALUES (2,1),(2,2),(2,0),(2,3);
INSERT INTO t1 VALUES (3,1),(3,2),(3,0),(3,3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
explain format=tree SELECT MAX(b), a FROM t1 WHERE b < 2 AND a = 1 GROUP BY a;
EXPLAIN
-> Filter: ((t1.a = 1) and (t1.b < 2))  (rows=1)
    -> Covering index skip scan for grouping on t1 using PRIMARY over (b < 2)  (rows=1)

SELECT MAX(b), a FROM t1 WHERE b < 2 AND a = 1 GROUP BY a;
MAX(b)	a
1	1
SELECT MIN(b), a FROM t1 WHERE b > 1 AND a = 1 GROUP BY a;
MIN(b)	a
2	1
CREATE TABLE t2 (a int, b int, c int, PRIMARY KEY (a,b,c));
INSERT INTO t2 SELECT a,b,b FROM t1;
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
explain format=tree SELECT MIN(c) FROM t2 WHERE b = 2 and a = 1 and c > 1 GROUP BY a;
EXPLAIN
-> Group aggregate: min(t2.c)  (rows=1)
    -> Filter: ((t2.a = 1) and (t2.b = 2) and (t2.c > 1))  (rows=1)
        -> Covering index range scan on t2 using PRIMARY over (a = 1 AND b = 2 AND 1 < c)  (rows=1)

SELECT MIN(c) FROM t2 WHERE b = 2 and a = 1 and c > 1 GROUP BY a;
MIN(c)
2
DROP TABLE t1,t2;
CREATE TABLE t1 (a INT, b INT, INDEX (a,b));
INSERT INTO t1 (a, b) VALUES (1,1), (1,2), (1,3), (1,4), (1,5),
(2,2), (2,3), (2,1), (3,1), (4,1), (4,2), (4,3), (4,4), (4,5), (4,6),
(5,1), (5,2), (5,3), (5,4), (5,5);
EXPLAIN FORMAT=TREE SELECT max(b), a FROM t1 GROUP BY a;
EXPLAIN
-> Covering index skip scan for grouping on t1 using a  (rows=11)

FLUSH STATUS;
SELECT max(b), a FROM t1 GROUP BY a;
max(b)	a
5	1
3	2
1	3
6	4
5	5
SHOW STATUS LIKE 'handler_read__e%';
Variable_name	Value
Handler_read_key	10
Handler_read_next	0
EXPLAIN FORMAT=TREE SELECT max(b), a FROM t1 GROUP BY a;
EXPLAIN
-> Covering index skip scan for grouping on t1 using a  (rows=11)

FLUSH STATUS;
CREATE TABLE t2 SELECT max(b), a FROM t1 GROUP BY a;
SHOW STATUS LIKE 'handler_read__e%';
Variable_name	Value
Handler_read_key	24
Handler_read_next	2
FLUSH STATUS;
SELECT * FROM (SELECT max(b), a FROM t1 GROUP BY a) b;
max(b)	a
5	1
3	2
1	3
6	4
5	5
SHOW STATUS LIKE 'handler_read__e%';
Variable_name	Value
Handler_read_key	10
Handler_read_next	0
FLUSH STATUS;
(SELECT max(b), a FROM t1 GROUP BY a) UNION
(SELECT max(b), a FROM t1 GROUP BY a);
max(b)	a
5	1
3	2
1	3
6	4
5	5
SHOW STATUS LIKE 'handler_read__e%';
Variable_name	Value
Handler_read_key	20
Handler_read_next	0
EXPLAIN FORMAT=TREE (SELECT max(b), a FROM t1 GROUP BY a) UNION
(SELECT max(b), a FROM t1 GROUP BY a);
EXPLAIN
-> Table scan on <union temporary>  (rows=22)
    -> Union materialize with deduplication  (rows=22)
        -> Covering index skip scan for grouping on t1 using a  (rows=11)
        -> Covering index skip scan for grouping on t1 using a  (rows=11)

EXPLAIN FORMAT=TREE SELECT (SELECT max(b) FROM t1 GROUP BY a HAVING a < 2) x
FROM t1 AS t1_outer;
EXPLAIN
-> Covering index scan on t1_outer using a  (rows=20)
-> Select #2 (subquery in projection; run only once)
    -> Filter: (t1.a < 2)  (rows=11)
        -> Covering index skip scan for grouping on t1 using a  (rows=11)

EXPLAIN FORMAT=TREE SELECT 1 FROM t1 AS t1_outer WHERE EXISTS
(SELECT max(b) FROM t1 GROUP BY a HAVING a < 2);
EXPLAIN
-> Covering index scan on t1_outer using a  (rows=20)

EXPLAIN FORMAT=TREE SELECT 1 FROM t1 AS t1_outer WHERE
(SELECT max(b) FROM t1 GROUP BY a HAVING a < 2) > 12;
EXPLAIN
-> Zero rows (Impossible WHERE)  (rows=0)

EXPLAIN FORMAT=TREE SELECT 1 FROM t1 AS t1_outer WHERE
a IN (SELECT max(b) FROM t1 GROUP BY a HAVING a < 2);
EXPLAIN
-> Filter: <in_optimizer>(t1_outer.a,t1_outer.a in (select #2))  (rows=20)
    -> Covering index scan on t1_outer using a  (rows=20)
    -> Select #2 (subquery in condition; run only once)
        -> Filter: ((t1_outer.a = `<materialized_subquery>`.`max(b)`))  (rows=1)
            -> Limit: 1 row(s)  (rows=1)
                -> Index lookup on <materialized_subquery> using <auto_distinct_key> (max(b) = t1_outer.a)
                    -> Materialize with deduplication  (rows=11)
                        -> Filter: (t1.a < 2)  (rows=11)
                            -> Covering index skip scan for grouping on t1 using a  (rows=11)

EXPLAIN FORMAT=TREE SELECT 1 FROM t1 AS t1_outer GROUP BY a HAVING
a > (SELECT max(b) FROM t1 GROUP BY a HAVING a < 2);
EXPLAIN
-> Filter: (t1_outer.a > (select #2))  (rows=11)
    -> Covering index skip scan for deduplication on t1_outer using a  (rows=11)
    -> Select #2 (subquery in condition; run only once)
        -> Filter: (t1.a < 2)  (rows=11)
            -> Covering index skip scan for grouping on t1 using a  (rows=11)

EXPLAIN FORMAT=TREE SELECT 1 FROM t1 AS t1_outer1 JOIN t1 AS t1_outer2
ON t1_outer1.a = (SELECT max(b) FROM t1 GROUP BY a HAVING a < 2)
AND t1_outer1.b = t1_outer2.b;
EXPLAIN
-> Inner hash join (t1_outer2.b = t1_outer1.b)  (rows=10)
    -> Covering index scan on t1_outer2 using a  (rows=20)
    -> Hash
        -> Covering index lookup on t1_outer1 using a (a = (select #2))  (rows=5)

EXPLAIN FORMAT=TREE SELECT (SELECT (SELECT max(b) FROM t1 GROUP BY a HAVING a < 2) x
FROM t1 AS t1_outer) x2 FROM t1 AS t1_outer2;
EXPLAIN
-> Covering index scan on t1_outer2 using a  (rows=20)
-> Select #2 (subquery in projection; run only once)
    -> Covering index scan on t1_outer using a  (rows=20)
    -> Select #3 (subquery in projection; run only once)
        -> Filter: (t1.a < 2)  (rows=11)
            -> Covering index skip scan for grouping on t1 using a  (rows=11)

CREATE TABLE t3 LIKE t1;
# Warm-up data-dictionary cache.
FLUSH STATUS;
INSERT INTO t3 SELECT a,MAX(b) FROM t1 GROUP BY a;
SHOW STATUS LIKE 'handler_read__e%';
Variable_name	Value
Handler_read_key	10
Handler_read_next	0
DELETE FROM t3;
FLUSH STATUS;
INSERT INTO t3 SELECT 1, (SELECT MAX(b) FROM t1 GROUP BY a HAVING a < 2)
FROM t1 LIMIT 1;
SHOW STATUS LIKE 'handler_read__e%';
Variable_name	Value
Handler_read_key	10
Handler_read_next	0
FLUSH STATUS;
DELETE FROM t3 WHERE (SELECT MAX(b) FROM t1 GROUP BY a HAVING a < 2) > 10000;
SHOW STATUS LIKE 'handler_read__e%';
Variable_name	Value
Handler_read_key	10
Handler_read_next	0
FLUSH STATUS;
DELETE FROM t3 WHERE (SELECT (SELECT MAX(b) FROM t1 GROUP BY a HAVING a < 2) x
FROM t1) > 10000;
ERROR 21000: Subquery returns more than 1 row
SHOW STATUS LIKE 'handler_read__e%';
Variable_name	Value
Handler_read_key	10
Handler_read_next	1
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (a int, INDEX idx(a));
INSERT INTO t1 VALUES
(4), (2), (1), (2), (4), (2), (1), (4),
(4), (2), (1), (2), (2), (4), (1), (4),
(4), (2), (1), (2), (2), (4), (1), (4);
EXPLAIN FORMAT=TREE SELECT DISTINCT(a) FROM t1;
EXPLAIN
-> Covering index skip scan for deduplication on t1 using idx  (rows=10)

SELECT DISTINCT(a) FROM t1;
a
1
2
4
EXPLAIN FORMAT=TREE SELECT SQL_BIG_RESULT DISTINCT(a) FROM t1;
EXPLAIN
-> Covering index skip scan for deduplication on t1 using idx  (rows=10)

SELECT SQL_BIG_RESULT DISTINCT(a) FROM t1;
a
1
2
4
DROP TABLE t1;
CREATE TABLE t1 (a INT, b INT);
INSERT INTO t1 (a, b) VALUES (1,1), (1,2), (1,3);
INSERT INTO t1 SELECT a + 1, b FROM t1;
INSERT INTO t1 SELECT a + 2, b FROM t1;
INSERT INTO t1 SELECT a + 4, b FROM t1;
EXPLAIN FORMAT=TREE
SELECT a, MIN(b), MAX(b) FROM t1 GROUP BY a ORDER BY a DESC;
EXPLAIN
-> Sort: t1.a DESC
    -> Table scan on <temporary>
        -> Aggregate using temporary table
            -> Table scan on t1  (rows=24)

SELECT a, MIN(b), MAX(b) FROM t1 GROUP BY a ORDER BY a DESC;
a	MIN(b)	MAX(b)
8	1	3
7	1	3
6	1	3
5	1	3
4	1	3
3	1	3
2	1	3
1	1	3
CREATE INDEX break_it ON t1 (a, b);
EXPLAIN FORMAT=TREE
SELECT a, MIN(b), MAX(b) FROM t1 GROUP BY a ORDER BY a;
EXPLAIN
-> Covering index skip scan for grouping on t1 using break_it  (rows=10)

SELECT a, MIN(b), MAX(b) FROM t1 GROUP BY a ORDER BY a;
a	MIN(b)	MAX(b)
1	1	3
2	1	3
3	1	3
4	1	3
5	1	3
6	1	3
7	1	3
8	1	3
EXPLAIN FORMAT=TREE
SELECT a, MIN(b), MAX(b) FROM t1 GROUP BY a ORDER BY a DESC;
EXPLAIN
-> Sort: t1.a DESC
    -> Table scan on <temporary>  (rows=10)
        -> Temporary table with deduplication  (rows=10)
            -> Covering index skip scan for grouping on t1 using break_it  (rows=10)

SELECT a, MIN(b), MAX(b) FROM t1 GROUP BY a ORDER BY a DESC;
a	MIN(b)	MAX(b)
8	1	3
7	1	3
6	1	3
5	1	3
4	1	3
3	1	3
2	1	3
1	1	3
EXPLAIN FORMAT=TREE
SELECT a, MIN(b), MAX(b), AVG(b) FROM t1 GROUP BY a ORDER BY a DESC;
EXPLAIN
-> Group aggregate: min(t1.b), max(t1.b), avg(t1.b)  (rows=4.9)
    -> Covering index scan on t1 using break_it (reverse)  (rows=24)

SELECT a, MIN(b), MAX(b), AVG(b) FROM t1 GROUP BY a ORDER BY a DESC;
a	MIN(b)	MAX(b)	AVG(b)
8	1	3	2.0000
7	1	3	2.0000
6	1	3	2.0000
5	1	3	2.0000
4	1	3	2.0000
3	1	3	2.0000
2	1	3	2.0000
1	1	3	2.0000
DROP TABLE t1;
create table t1 (a int, b int, primary key (a,b), key `index` (a,b)) engine=MyISAM;
insert into  t1 (a,b) values
(0,0),(0,1),(0,2),(0,3),(0,4),(0,5),(0,6),
(0,7),(0,8),(0,9),(0,10),(0,11),(0,12),(0,13),
(1,0),(1,1),(1,2),(1,3),(1,4),(1,5),(1,6),
(1,7),(1,8),(1,9),(1,10),(1,11),(1,12),(1,13),
(2,0),(2,1),(2,2),(2,3),(2,4),(2,5),(2,6),
(2,7),(2,8),(2,9),(2,10),(2,11),(2,12),(2,13),
(3,0),(3,1),(3,2),(3,3),(3,4),(3,5),(3,6),
(3,7),(3,8),(3,9),(3,10),(3,11),(3,12),(3,13);
insert into t1 (a,b) select a, max(b)+1 from t1 where a = 0 group by a;
select * from t1;
a	b
0	0
0	1
0	10
0	11
0	12
0	13
0	14
0	2
0	3
0	4
0	5
0	6
0	7
0	8
0	9
1	0
1	1
1	10
1	11
1	12
1	13
1	2
1	3
1	4
1	5
1	6
1	7
1	8
1	9
2	0
2	1
2	10
2	11
2	12
2	13
2	2
2	3
2	4
2	5
2	6
2	7
2	8
2	9
3	0
3	1
3	10
3	11
3	12
3	13
3	2
3	3
3	4
3	5
3	6
3	7
3	8
3	9
explain format=tree select sql_buffer_result a, max(b)+1 from t1 where a = 0 group by a;
EXPLAIN
-> Table scan on <temporary>  (rows=1)
    -> Temporary table  (rows=1)
        -> Filter: (t1.a = 0)  (rows=1)
            -> Covering index skip scan for grouping on t1 using PRIMARY over (a = 0)  (rows=1)

drop table t1;
CREATE TABLE t1 (a int, b int, c int, d int,
KEY foo (c,d,a,b), KEY bar (c,a,b,d));
INSERT INTO t1 VALUES (1, 1, 1, 1), (1, 1, 1, 2), (1, 1, 1, 3), (1, 1, 1, 4);
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT a,b,c+1,d FROM t1;
EXPLAIN FORMAT=TREE SELECT DISTINCT c FROM t1 WHERE d=4;
EXPLAIN
-> Filter: (t1.d = 4)  (rows=10)
    -> Covering index skip scan for deduplication on t1 using foo  (rows=10)

SELECT DISTINCT c FROM t1 WHERE d=4;
c
1
2
DROP TABLE t1;
#
# Bug #45386: Wrong query result with MIN function in field list,
#  WHERE and GROUP BY clause
#
CREATE TABLE t (a INT, b INT, INDEX (a,b));
INSERT INTO t VALUES (2,0), (2,0), (2,1), (2,1);
INSERT INTO t SELECT * FROM t;
INSERT INTO t SELECT * FROM t;
INSERT INTO t SELECT * FROM t;
# test MIN
#should use range with index for group by
EXPLAIN FORMAT=TREE
SELECT a, MIN(b) FROM t WHERE b <> 0 GROUP BY a;
EXPLAIN
-> Filter: (t.b <> 0)  (rows=10)
    -> Covering index skip scan for grouping on t using a over (NULL < b < 0) OR (0 < b)  (rows=10)

#should return 1 row
SELECT a, MIN(b) FROM t WHERE b <> 0 GROUP BY a;
a	MIN(b)
2	1
# test MAX
#should use range with index for group by
EXPLAIN FORMAT=TREE
SELECT a, MAX(b) FROM t WHERE b <> 1 GROUP BY a;
EXPLAIN
-> Filter: (t.b <> 1)  (rows=10)
    -> Covering index skip scan for grouping on t using a over (NULL < b < 1) OR (1 < b)  (rows=10)

#should return 1 row
SELECT a, MAX(b) FROM t WHERE b <> 1 GROUP BY a;
a	MAX(b)
2	0
# test 3 ranges and use the middle one
INSERT INTO t SELECT a, 2 FROM t;
#should use range with index for group by
EXPLAIN FORMAT=TREE
SELECT a, MAX(b) FROM t WHERE b > 0 AND b < 2 GROUP BY a;
EXPLAIN
-> Filter: ((t.b > 0) and (t.b < 2))  (rows=10)
    -> Covering index skip scan for grouping on t using a over (0 < b < 2)  (rows=10)

#should return 1 row
SELECT a, MAX(b) FROM t WHERE b > 0 AND b < 2 GROUP BY a;
a	MAX(b)
2	1
DROP TABLE t;
#
# Bug #48472: Loose index scan inappropriately chosen for some WHERE
#             conditions
#
CREATE TABLE t (a INT, b INT, INDEX (a,b));
INSERT INTO t VALUES (2,0), (2,0), (2,1), (2,1);
INSERT INTO t SELECT * FROM t;
SELECT a, MAX(b) FROM t WHERE 0=b+0 GROUP BY a;
a	MAX(b)
2	0
DROP TABLE t;
End of 5.0 tests
#
# Bug #46607: Assertion failed: (cond_type == Item::FUNC_ITEM) results in
#              server crash
#
CREATE TABLE t (a INT, b INT, INDEX (a,b));
INSERT INTO t VALUES (2,0), (2,0), (2,1), (2,1);
INSERT INTO t SELECT * FROM t;
SELECT a, MAX(b) FROM t WHERE b GROUP BY a;
a	MAX(b)
2	1
DROP TABLE t;
CREATE TABLE t1(a INT NOT NULL, b INT NOT NULL, KEY (b));
INSERT INTO t1 VALUES(1,1),(2,1);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT 1 AS c, b FROM t1 WHERE b IN (1,2) GROUP BY c, b;
c	b
1	1
SELECT a FROM t1 WHERE b=1;
a
1
2
DROP TABLE t1;
#
# Bug#53859: Valgrind: opt_sum_query(TABLE_LIST*, List<Item>&, Item*) at
# opt_sum.cc:305
#
CREATE TABLE t1 ( a INT, KEY (a) );
INSERT INTO t1 VALUES (1), (2), (3);
SELECT MIN( a ) AS min_a
FROM t1
WHERE a > 1 AND a IS NULL
ORDER BY min_a;
min_a
NULL
DROP TABLE t1;
End of 5.1 tests
#
# WL#3220 (Loose index scan for COUNT DISTINCT)
#
CREATE TABLE t1 (a INT, b INT, c INT, KEY (a,b));
INSERT INTO t1 VALUES (1,1,1), (1,2,1), (1,3,1), (1,4,1);
INSERT INTO t1 SELECT a, b + 4, 1 FROM t1;
INSERT INTO t1 SELECT a + 1, b, 1 FROM t1;
INSERT INTO t1 SELECT a + 2, b + 8, 1 FROM t1;
CREATE TABLE t2 (a INT, b INT, c INT, d INT, e INT, f INT, KEY (a,b,c));
INSERT INTO t2 VALUES (1,1,1,1,1,1), (1,2,1,1,1,1), (1,3,1,1,1,1),
(1,4,1,1,1,1);
INSERT INTO t2 SELECT a, b + 4, c,d,e,f FROM t2;
INSERT INTO t2 SELECT a + 1, b, c,d,e,f FROM t2;
INSERT INTO t2 SELECT a + 2, b + 8, c,d,e,f FROM t2;
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a) FROM t1;
EXPLAIN
-> Aggregate: count(distinct t1.a)  (rows=1)
    -> Covering index skip scan for deduplication on t1 using a  (rows=10)

SELECT COUNT(DISTINCT a) FROM t1;
COUNT(DISTINCT a)
4
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a,b) FROM t1;
EXPLAIN
-> Aggregate: count(distinct t1.a,t1.b)  (rows=1)
    -> Covering index skip scan for deduplication on t1 using a  (rows=10)

SELECT COUNT(DISTINCT a,b) FROM t1;
COUNT(DISTINCT a,b)
32
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT b,a) FROM t1;
EXPLAIN
-> Aggregate: count(distinct t1.b,t1.a)  (rows=1)
    -> Covering index skip scan for deduplication on t1 using a  (rows=10)

SELECT COUNT(DISTINCT b,a) FROM t1;
COUNT(DISTINCT b,a)
32
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT b) FROM t1;
EXPLAIN
-> Aggregate: count(distinct t1.b)  (rows=1)
    -> Covering index scan on t1 using a  (rows=32)

SELECT COUNT(DISTINCT b) FROM t1;
COUNT(DISTINCT b)
16
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a) FROM t1 GROUP BY a;
EXPLAIN
-> Group aggregate: count(distinct t1.a)  (rows=5.66)
    -> Covering index skip scan for deduplication on t1 using a  (rows=10)

SELECT COUNT(DISTINCT a) FROM t1 GROUP BY a;
COUNT(DISTINCT a)
1
1
1
1
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT b) FROM t1 GROUP BY a;
EXPLAIN
-> Group aggregate: count(distinct t1.b)  (rows=5.66)
    -> Covering index skip scan for deduplication on t1 using a  (rows=10)

SELECT COUNT(DISTINCT b) FROM t1 GROUP BY a;
COUNT(DISTINCT b)
8
8
8
8
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a) FROM t1 GROUP BY b;
EXPLAIN
-> Group aggregate: count(distinct t1.a)  (rows=5.66)
    -> Sort: t1.b  (rows=32)
        -> Covering index scan on t1 using a  (rows=32)

SELECT COUNT(DISTINCT a) FROM t1 GROUP BY b;
COUNT(DISTINCT a)
2
2
2
2
2
2
2
2
2
2
2
2
2
2
2
2
EXPLAIN FORMAT=TREE SELECT DISTINCT COUNT(DISTINCT a) FROM t1;
EXPLAIN
-> Aggregate: count(distinct t1.a)  (rows=1)
    -> Covering index skip scan for deduplication on t1 using a  (rows=10)

SELECT DISTINCT COUNT(DISTINCT a) FROM t1;
COUNT(DISTINCT a)
4
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a, b + 0) FROM t1;
EXPLAIN
-> Aggregate: count(distinct t1.a,(t1.b + 0))  (rows=1)
    -> Covering index scan on t1 using a  (rows=32)

SELECT COUNT(DISTINCT a, b + 0) FROM t1;
COUNT(DISTINCT a, b + 0)
32
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a) FROM t1 HAVING COUNT(DISTINCT b) < 20;
EXPLAIN
-> Filter: (count(distinct t1.b) < 20)  (rows=1)
    -> Aggregate: count(distinct t1.b), count(distinct t1.a)  (rows=1)
        -> Covering index scan on t1 using a  (rows=32)

SELECT COUNT(DISTINCT a) FROM t1 HAVING COUNT(DISTINCT b) < 20;
COUNT(DISTINCT a)
4
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a) FROM t1 HAVING COUNT(DISTINCT c) < 10;
EXPLAIN
-> Filter: (count(distinct t1.c) < 10)  (rows=1)
    -> Aggregate: count(distinct t1.c), count(distinct t1.a)  (rows=1)
        -> Table scan on t1  (rows=32)

SELECT COUNT(DISTINCT a) FROM t1 HAVING COUNT(DISTINCT c) < 10;
COUNT(DISTINCT a)
4
EXPLAIN FORMAT=TREE SELECT 1 FROM t1 HAVING COUNT(DISTINCT a) < 10;
EXPLAIN
-> Filter: (count(distinct t1.a) < 10)  (rows=1)
    -> Aggregate: count(distinct t1.a)  (rows=1)
        -> Covering index skip scan for deduplication on t1 using a  (rows=10)

SELECT 1 FROM t1 HAVING COUNT(DISTINCT a) < 10;
1
1
EXPLAIN FORMAT=TREE SELECT 1 FROM t1 GROUP BY a HAVING COUNT(DISTINCT b) > 1;
EXPLAIN
-> Filter: (count(distinct t1.b) > 1)  (rows=5.66)
    -> Group aggregate: count(distinct t1.b)  (rows=5.66)
        -> Covering index skip scan for deduplication on t1 using a  (rows=10)

SELECT 1 FROM t1 GROUP BY a HAVING COUNT(DISTINCT b) > 1;
1
1
1
1
1
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT t1_1.a) FROM t1 t1_1, t1 t1_2 GROUP BY t1_1.a;
EXPLAIN
-> Group aggregate: count(distinct t1.a)
    -> Sort: t1_1.a
        -> Stream results  (rows=1024)
            -> Inner hash join (no condition)  (rows=1024)
                -> Covering index scan on t1_2 using a  (rows=32)
                -> Hash
                    -> Covering index scan on t1_1 using a  (rows=32)

SELECT COUNT(DISTINCT t1_1.a) FROM t1 t1_1, t1 t1_2 GROUP BY t1_1.a;
COUNT(DISTINCT t1_1.a)
1
1
1
1
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a), 12 FROM t1;
EXPLAIN
-> Aggregate: count(distinct t1.a)  (rows=1)
    -> Covering index skip scan for deduplication on t1 using a  (rows=10)

SELECT COUNT(DISTINCT a), 12 FROM t1;
COUNT(DISTINCT a)	12
4	12
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a, b, c) FROM t2;
EXPLAIN
-> Aggregate: count(distinct t2.a,t2.b,t2.c)  (rows=1)
    -> Covering index skip scan for deduplication on t2 using a  (rows=10)

SELECT COUNT(DISTINCT a, b, c) FROM t2;
COUNT(DISTINCT a, b, c)
32
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a), SUM(DISTINCT a), AVG(DISTINCT a) FROM t2;
EXPLAIN
-> Aggregate: count(distinct t2.a), sum(distinct t2.a), avg(distinct t2.a)  (rows=1)
    -> Covering index skip scan for deduplication on t2 using a  (rows=10)

SELECT COUNT(DISTINCT a), SUM(DISTINCT a), AVG(DISTINCT a) FROM t2;
COUNT(DISTINCT a)	SUM(DISTINCT a)	AVG(DISTINCT a)
4	10	2.5000
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a), SUM(DISTINCT a), AVG(DISTINCT f) FROM t2;
EXPLAIN
-> Aggregate: count(distinct t2.a), sum(distinct t2.a), avg(distinct t2.f)  (rows=1)
    -> Table scan on t2  (rows=32)

SELECT COUNT(DISTINCT a), SUM(DISTINCT a), AVG(DISTINCT f) FROM t2;
COUNT(DISTINCT a)	SUM(DISTINCT a)	AVG(DISTINCT f)
4	10	1.0000
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a, b), COUNT(DISTINCT b, a) FROM t2;
EXPLAIN
-> Aggregate: count(distinct t2.a,t2.b), count(distinct t2.b,t2.a)  (rows=1)
    -> Covering index skip scan for deduplication on t2 using a  (rows=10)

SELECT COUNT(DISTINCT a, b), COUNT(DISTINCT b, a) FROM t2;
COUNT(DISTINCT a, b)	COUNT(DISTINCT b, a)
32	32
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a, b), COUNT(DISTINCT b, f) FROM t2;
EXPLAIN
-> Aggregate: count(distinct t2.a,t2.b), count(distinct t2.b,t2.f)  (rows=1)
    -> Table scan on t2  (rows=32)

SELECT COUNT(DISTINCT a, b), COUNT(DISTINCT b, f) FROM t2;
COUNT(DISTINCT a, b)	COUNT(DISTINCT b, f)
32	16
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a, b), COUNT(DISTINCT b, d) FROM t2;
EXPLAIN
-> Aggregate: count(distinct t2.a,t2.b), count(distinct t2.b,t2.d)  (rows=1)
    -> Table scan on t2  (rows=32)

SELECT COUNT(DISTINCT a, b), COUNT(DISTINCT b, d) FROM t2;
COUNT(DISTINCT a, b)	COUNT(DISTINCT b, d)
32	16
EXPLAIN FORMAT=TREE SELECT a, c, COUNT(DISTINCT c, a, b) FROM t2 GROUP BY a, b, c;
EXPLAIN
-> Group aggregate: count(distinct t2.c,t2.a,t2.b)  (rows=8.65)
    -> Covering index skip scan for deduplication on t2 using a  (rows=10)

SELECT a, c, COUNT(DISTINCT c, a, b) FROM t2 GROUP BY a, b, c;
a	c	COUNT(DISTINCT c, a, b)
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
1	1	1
2	1	1
2	1	1
2	1	1
2	1	1
2	1	1
2	1	1
2	1	1
2	1	1
3	1	1
3	1	1
3	1	1
3	1	1
3	1	1
3	1	1
3	1	1
3	1	1
4	1	1
4	1	1
4	1	1
4	1	1
4	1	1
4	1	1
4	1	1
4	1	1
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT c, a, b) FROM t2
WHERE a > 5 AND b BETWEEN 10 AND 20 GROUP BY a, b, c;
EXPLAIN
-> Group aggregate: count(distinct t2.c,t2.a,t2.b)  (rows=0.111)
    -> Filter: ((t2.a > 5) and (t2.b between 10 and 20))  (rows=0.111)
        -> Covering index range scan on t2 using a over (5 < a)  (rows=1)

SELECT COUNT(DISTINCT c, a, b) FROM t2
WHERE a > 5 AND b BETWEEN 10 AND 20 GROUP BY a, b, c;
COUNT(DISTINCT c, a, b)
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT b), SUM(DISTINCT b) FROM t2 WHERE a = 5
GROUP BY b;
EXPLAIN
-> Group aggregate: count(distinct t2.b), sum(distinct t2.b)  (rows=1)
    -> Covering index lookup on t2 using a (a = 5)  (rows=1)

SELECT COUNT(DISTINCT b), SUM(DISTINCT b) FROM t2 WHERE a = 5
GROUP BY b;
COUNT(DISTINCT b)	SUM(DISTINCT b)
EXPLAIN FORMAT=TREE SELECT a, COUNT(DISTINCT b), SUM(DISTINCT b) FROM t2 GROUP BY a;
EXPLAIN
-> Group aggregate: count(distinct t2.b), sum(distinct t2.b)  (rows=5.66)
    -> Covering index skip scan for deduplication on t2 using a  (rows=10)

SELECT a, COUNT(DISTINCT b), SUM(DISTINCT b) FROM t2 GROUP BY a;
a	COUNT(DISTINCT b)	SUM(DISTINCT b)
1	8	36
2	8	36
3	8	100
4	8	100
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT b), SUM(DISTINCT b) FROM t2 GROUP BY a;
EXPLAIN
-> Group aggregate: count(distinct t2.b), sum(distinct t2.b)  (rows=5.66)
    -> Covering index skip scan for deduplication on t2 using a  (rows=10)

SELECT COUNT(DISTINCT b), SUM(DISTINCT b) FROM t2 GROUP BY a;
COUNT(DISTINCT b)	SUM(DISTINCT b)
8	36
8	36
8	100
8	100
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a, b) FROM t2 WHERE c = 13 AND d = 42;
EXPLAIN
-> Aggregate: count(distinct t2.a,t2.b)  (rows=1)
    -> Filter: ((t2.d = 42) and (t2.c = 13))  (rows=1)
        -> Table scan on t2  (rows=32)

SELECT COUNT(DISTINCT a, b) FROM t2 WHERE c = 13 AND d = 42;
COUNT(DISTINCT a, b)
0
EXPLAIN FORMAT=TREE SELECT a, COUNT(DISTINCT a), SUM(DISTINCT a) FROM t2
WHERE b = 13 AND c = 42 GROUP BY a;
EXPLAIN
-> Group aggregate: count(distinct t2.a), sum(distinct t2.a)  (rows=5.66)
    -> Filter: ((t2.c = 42) and (t2.b = 13))  (rows=10)
        -> Covering index skip scan for deduplication on t2 using a  (rows=10)

SELECT a, COUNT(DISTINCT a), SUM(DISTINCT a) FROM t2
WHERE b = 13 AND c = 42 GROUP BY a;
a	COUNT(DISTINCT a)	SUM(DISTINCT a)
# This query could have been resolved using loose index scan since
# the second part of count(..) is defined by a constant predicate
EXPLAIN FORMAT=TREE SELECT COUNT(DISTINCT a, b), SUM(DISTINCT a) FROM t2 WHERE b = 42;
EXPLAIN
-> Aggregate: count(distinct t2.a,t2.b), sum(distinct t2.a)  (rows=1)
    -> Filter: (t2.b = 42)  (rows=3.2)
        -> Covering index scan on t2 using a  (rows=32)

SELECT COUNT(DISTINCT a, b), SUM(DISTINCT a) FROM t2 WHERE b = 42;
COUNT(DISTINCT a, b)	SUM(DISTINCT a)
0	NULL
EXPLAIN FORMAT=TREE SELECT SUM(DISTINCT a), MAX(b) FROM t2 GROUP BY a;
EXPLAIN
-> Group aggregate: sum(distinct t2.a), max(t2.b)  (rows=5.66)
    -> Covering index scan on t2 using a  (rows=32)

SELECT SUM(DISTINCT a), MAX(b) FROM t2 GROUP BY a;
SUM(DISTINCT a)	MAX(b)
1	8
2	8
3	16
4	16
EXPLAIN FORMAT=TREE SELECT 42 * (a + c + COUNT(DISTINCT c, a, b)) FROM t2 GROUP BY a, b, c;
EXPLAIN
-> Group aggregate: count(distinct t2.c,t2.a,t2.b)  (rows=8.65)
    -> Covering index skip scan for deduplication on t2 using a  (rows=10)

SELECT 42 * (a + c + COUNT(DISTINCT c, a, b)) FROM t2 GROUP BY a, b, c;
42 * (a + c + COUNT(DISTINCT c, a, b))
126
126
126
126
126
126
126
126
168
168
168
168
168
168
168
168
210
210
210
210
210
210
210
210
252
252
252
252
252
252
252
252
EXPLAIN FORMAT=TREE SELECT (SUM(DISTINCT a) + MAX(b)) FROM t2 GROUP BY a;
EXPLAIN
-> Group aggregate: max(t2.b), sum(distinct t2.a)  (rows=5.66)
    -> Covering index scan on t2 using a  (rows=32)

SELECT (SUM(DISTINCT a) + MAX(b)) FROM t2 GROUP BY a;
(SUM(DISTINCT a) + MAX(b))
9
10
19
20
DROP TABLE t1,t2;
# end of WL#3220 tests
#
# Bug#50539: Wrong result when loose index scan is used for an aggregate
#            function with distinct
#
CREATE TABLE t1 (
f1 int(11) NOT NULL DEFAULT '0',
f2 char(1) NOT NULL DEFAULT '',
PRIMARY KEY (f1,f2)
)  charset utf8mb4;
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
insert into t1 values(1,'A'),(1 , 'B'), (1, 'C'), (2, 'A'),
(3, 'A'), (3, 'B'), (3, 'C'), (3, 'D');
SELECT f1, COUNT(DISTINCT f2) FROM t1 GROUP BY f1;
f1	COUNT(DISTINCT f2)
1	3
2	1
3	4
explain format=tree SELECT f1, COUNT(DISTINCT f2) FROM t1 GROUP BY f1;
EXPLAIN
-> Group aggregate: count(distinct t1.f2)  (rows=3)
    -> Covering index skip scan for deduplication on t1 using PRIMARY  (rows=9)

drop table t1;
# End of test#50539.
#
# Bug#18497308 WRONG COST ESTIMATE FOR LOOSE INDEX SCAN WHEN
#              INDEX STATISTICS IS MISSING
#
CREATE TABLE t1 (
a INTEGER,
b INTEGER,
c INTEGER,
d INTEGER,
KEY foo (a,b,c,d)
) ENGINE=MyISAM;
INSERT INTO t1 VALUES (1, 1, 1, 1), (1, 2, 1, 2), (1, 3, 1, 3), (1, 4, 1, 4);
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
INSERT INTO t1 SELECT * FROM t1;
EXPLAIN FORMAT=TREE SELECT DISTINCT a FROM t1 WHERE b=4;
EXPLAIN
-> Filter: (t1.b = 4)  (rows=101)
    -> Covering index skip scan for deduplication on t1 using foo  (rows=101)

SELECT DISTINCT a FROM t1 WHERE b=4;
a
1
DROP TABLE t1;
#
# Bug#17217128 -  BAD INTERACTION BETWEEN MIN/MAX AND
#                 "HAVING SUM(DISTINCT)": WRONG RESULTS.
#
CREATE TABLE t (a INT, b INT, KEY(a,b));
INSERT INTO t VALUES (1,1), (2,2), (3,3), (4,4), (1,0), (3,2), (4,5);
set optimizer_trace_max_mem_size=1048576;
set @@session.optimizer_trace='enabled=on';
set end_markers_in_json=on;
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
SELECT a, SUM(DISTINCT a), MIN(b) FROM t GROUP BY a;
a	SUM(DISTINCT a)	MIN(b)
1	1	0
2	2	2
3	3	2
4	4	4
EXPLAIN FORMAT=TREE SELECT a, SUM(DISTINCT a), MIN(b) FROM t GROUP BY a;
EXPLAIN
-> Group aggregate: sum(distinct t.a), min(t.b)  (rows=2.65)
    -> Covering index scan on t using a  (rows=7)

SELECT TRACE RLIKE 'have_both_agg_distinct_and_min_max' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
SELECT a, SUM(DISTINCT a), MAX(b) FROM t GROUP BY a;
a	SUM(DISTINCT a)	MAX(b)
1	1	1
2	2	2
3	3	3
4	4	5
EXPLAIN FORMAT=TREE SELECT a, SUM(DISTINCT a), MAX(b) FROM t GROUP BY a;
EXPLAIN
-> Group aggregate: sum(distinct t.a), max(t.b)  (rows=2.65)
    -> Covering index scan on t using a  (rows=7)

SELECT TRACE RLIKE 'have_both_agg_distinct_and_min_max' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
SELECT a, MAX(b) FROM t GROUP BY a HAVING SUM(DISTINCT a);
a	MAX(b)
1	1
2	2
3	3
4	5
EXPLAIN FORMAT=TREE SELECT a, MAX(b) FROM t GROUP BY a HAVING SUM(DISTINCT a);
EXPLAIN
-> Filter: (0 <> sum(distinct t.a))  (rows=2.65)
    -> Group aggregate: sum(distinct t.a), max(t.b)  (rows=2.65)
        -> Covering index scan on t using a  (rows=7)

SELECT TRACE RLIKE 'have_both_agg_distinct_and_min_max' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
SELECT SUM(DISTINCT a), MIN(b), MAX(b) FROM t;
SUM(DISTINCT a)	MIN(b)	MAX(b)
10	0	5
EXPLAIN FORMAT=TREE SELECT SUM(DISTINCT a), MIN(b), MAX(b) FROM t;
EXPLAIN
-> Aggregate: sum(distinct t.a), min(t.b), max(t.b)  (rows=1)
    -> Covering index scan on t using a  (rows=7)

SELECT TRACE RLIKE 'have_both_agg_distinct_and_min_max' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
SELECT a, SUM(DISTINCT a), MIN(b), MAX(b) FROM t GROUP BY a;
a	SUM(DISTINCT a)	MIN(b)	MAX(b)
1	1	0	1
2	2	2	2
3	3	2	3
4	4	4	5
EXPLAIN FORMAT=TREE SELECT a, SUM(DISTINCT a), MIN(b), MAX(b) FROM t GROUP BY a;
EXPLAIN
-> Group aggregate: sum(distinct t.a), min(t.b), max(t.b)  (rows=2.65)
    -> Covering index scan on t using a  (rows=7)

SELECT TRACE RLIKE 'have_both_agg_distinct_and_min_max' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
SET optimizer_trace_max_mem_size=DEFAULT;
SET optimizer_trace=DEFAULT;
SET end_markers_in_json=DEFAULT;
DROP TABLE t;
#
# Bug #18066518: THE COST VALUE IS A NEGATIVE NUMBER FOR MERGE ENGINE
#                TABLE
#
CREATE TABLE t(a INT PRIMARY KEY)
ENGINE = MERGE;
EXPLAIN FORMAT=TREE SELECT DISTINCT(a) FROM t;
EXPLAIN
-> Covering index scan on t using PRIMARY  (rows=0)

SELECT DISTINCT(a) FROM t;
a
DROP TABLE t;
# End of test#18066518.
#
# Bug #18486293: ASSERTION FAILED: KEYS >= 0.0 IN
#                COST_MODEL_TABLE::KEY_COMPARE_COST
#
CREATE TABLE t (b INT, KEY b_key (b)) ENGINE=INNODB
PARTITION BY RANGE COLUMNS(b) (PARTITION p_part VALUES LESS THAN (0));
SELECT 1 FROM t WHERE b IN ('') GROUP BY  b ;
1
DROP TABLE t;
# End of test#18486293.
#
# Bug#18109609: LOOSE INDEX SCAN IS NOT USED WHEN IT SHOULD
#
CREATE TABLE t1 (
id INT AUTO_INCREMENT PRIMARY KEY,
c1 INT,
c2 INT,
KEY(c1,c2));
INSERT INTO t1(c1,c2) VALUES
(1, 1), (1,2), (2,1), (2,2), (3,1), (3,2), (3,3), (4,1), (4,2), (4,3),
(4,4), (4,5), (4,6), (4,7), (4,8), (4,9), (4,10), (4,11), (4,12), (4,13),
(4,14), (4,15), (4,16), (4,17), (4,18), (4,19), (4,20),(5,5);
EXPLAIN FORMAT=TREE SELECT MAX(c2), c1 FROM t1 WHERE c1 = 4 GROUP BY c1;
EXPLAIN
-> Filter: (t1.c1 = 4)  (rows=1)
    -> Covering index skip scan for grouping on t1 using c1 over (c1 = 4)  (rows=1)

FLUSH STATUS;
SELECT MAX(c2), c1 FROM t1 WHERE c1 = 4 GROUP BY c1;
MAX(c2)	c1
20	4
SHOW SESSION STATUS LIKE 'Handler_read%';
Variable_name	Value
Handler_read_first	0
Handler_read_key	3
Handler_read_last	1
Handler_read_next	0
Handler_read_prev	0
Handler_read_rnd	0
Handler_read_rnd_next	0
DROP TABLE t1;
# End of test for Bug#18109609
#
# Bug#22661012 - "USING INDEX FOR GROUP-BY" QUERIES CAN RETURN
#                NO DATA WITH LE/LEQ AND ROUNDING
#
CREATE TABLE t1 (a INT, b INT, KEY(a,b));
INSERT INTO t1 VALUES (1,1000), (1,1001), (1,2000),
(1,2001), (1,3000), (1,3002);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN FORMAT=TREE SELECT a, MAX(b) FROM t1 WHERE a = 1 AND b < 1999.5 GROUP BY a;
EXPLAIN
-> Filter: ((t1.a = 1) and (t1.b < 2000))  (rows=1)
    -> Covering index skip scan for grouping on t1 using a over (NULL < b < 2000)  (rows=1)

SELECT a, MAX(b) FROM t1 WHERE a = 1 AND b < 1999.5 GROUP BY a;
a	MAX(b)
1	1001
EXPLAIN FORMAT=TREE SELECT a, MAX(b) FROM t1 WHERE a = 1 AND b <= 1999.5 GROUP BY a;
EXPLAIN
-> Filter: ((t1.a = 1) and (t1.b < 2000))  (rows=1)
    -> Covering index skip scan for grouping on t1 using a over (NULL < b < 2000)  (rows=1)

SELECT a, MAX(b) FROM t1 WHERE a = 1 AND b <= 1999.5 GROUP BY a;
a	MAX(b)
1	1001
EXPLAIN FORMAT=TREE SELECT a, MAX(b) FROM t1 WHERE a = 1 AND b between 0 and 1999.5
GROUP BY a;
EXPLAIN
-> Filter: ((t1.a = 1) and (t1.b between 0 and 1999.5))  (rows=1)
    -> Covering index skip scan for grouping on t1 using a over (0 <= b < 2000)  (rows=1)

SELECT a, MAX(b) FROM t1 WHERE a = 1 AND b between 0 and 1999.5
GROUP BY a;
a	MAX(b)
1	1001
EXPLAIN FORMAT=TREE SELECT a, MIN(b) FROM t1 WHERE a = 1 AND b > 2000.1 GROUP BY a;
EXPLAIN
-> Filter: ((t1.a = 1) and (t1.b > 2000))  (rows=1)
    -> Covering index skip scan for grouping on t1 using a over (2000 < b)  (rows=1)

SELECT a, MIN(b) FROM t1 WHERE a = 1 AND b > 2000.1 GROUP BY a;
a	MIN(b)
1	2001
EXPLAIN FORMAT=TREE SELECT a, MIN(b) FROM t1 WHERE a = 1 AND b >= 2000.1 GROUP BY a;
EXPLAIN
-> Filter: ((t1.a = 1) and (t1.b > 2000))  (rows=1)
    -> Covering index skip scan for grouping on t1 using a over (2000 < b)  (rows=1)

SELECT a, MIN(b) FROM t1 WHERE a = 1 AND b >= 2000.1 GROUP BY a;
a	MIN(b)
1	2001
DROP TABLE t1;
# End of test for Bug#22661012
#
# Bug#24484060 INCORRECT EVALUATION OF MIN/MAX REFERRING
# TO AN OUTER QUERY BLOCK
#
CREATE TABLE t1 (pk INT PRIMARY KEY, nk INT, k INT UNIQUE);
INSERT INTO t1 VALUES (1,1,1),(2,2,2),(4,4,4);
CREATE TABLE t2 (k INT);
INSERT INTO t2 VALUES (1),(2),(4);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
explain format=tree SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk<>3 AND out1.nk = (SELECT MAX(out1.k) FROM t1);
EXPLAIN
-> Nested loop inner join  (rows=3)
    -> Filter: (t2.k is not null)  (rows=3)
        -> Table scan on t2  (rows=3)
    -> Filter: ((out1.pk <> 3) and (out1.nk = (select #2)))  (rows=1)
        -> Single-row index lookup on out1 using k (k = t2.k)  (rows=1)
        -> Select #2 (subquery in condition; dependent)
            -> Aggregate: max(out1.k)  (rows=1)
                -> Covering index scan on t1 using PRIMARY  (rows=3)

Warnings:
Note	1276	Field or reference 'test.out1.k' of SELECT #2 was resolved in SELECT #1
SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk<>3 AND out1.nk = (SELECT MAX(out1.k) FROM t1);
k
1
2
4
# Repeat the test with PRIMARY KEY instead of the non-indexed column.
ALTER TABLE t1 DROP COLUMN nk;
explain format=tree SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT MAX(out1.k) FROM t1);
EXPLAIN
-> Nested loop inner join  (rows=3)
    -> Filter: (t2.k is not null)  (rows=3)
        -> Table scan on t2  (rows=3)
    -> Filter: (out1.pk = (select #2))  (rows=1)
        -> Single-row index lookup on out1 using k (k = t2.k)  (rows=1)
        -> Select #2 (subquery in condition; dependent)
            -> Aggregate: max(out1.k)  (rows=1)
                -> Covering index scan on t1 using PRIMARY  (rows=3)

Warnings:
Note	1276	Field or reference 'test.out1.k' of SELECT #2 was resolved in SELECT #1
SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT MAX(out1.k) FROM t1);
k
1
2
4
# Inner reference should be optimized.
explain format=tree SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT MAX(k) FROM t1);
EXPLAIN
-> Filter: (t2.k = '4')  (rows=1)
    -> Table scan on t2  (rows=3)

SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT MAX(k) FROM t1);
k
4
explain format=tree SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT 2*MIN(k) FROM t1);
EXPLAIN
-> Filter: (t2.k = '2')  (rows=1)
    -> Table scan on t2  (rows=3)

SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT 2*MIN(k) FROM t1);
k
2
explain format=tree SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT SUM(k) - COUNT(k) FROM t1);
EXPLAIN
-> Filter: ((t2.k = '4') and <cache>(('4' = (select #2))))  (rows=1)
    -> Table scan on t2  (rows=3)
    -> Select #2 (subquery in condition; run only once)
        -> Aggregate: count(t1.k), sum(t1.k)  (rows=1)
            -> Covering index scan on t1 using k  (rows=3)

SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT SUM(k) - COUNT(k) FROM t1);
k
4
# Combinations of outer and inner references should not be optimized.
explain format=tree SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT MIN(out1.k + k) - 1 FROM t1);
EXPLAIN
-> Nested loop inner join  (rows=3)
    -> Filter: (t2.k is not null)  (rows=3)
        -> Table scan on t2  (rows=3)
    -> Filter: (out1.pk = (select #2))  (rows=1)
        -> Single-row index lookup on out1 using k (k = t2.k)  (rows=1)
        -> Select #2 (subquery in condition; dependent)
            -> Aggregate: min((out1.k + t1.k))  (rows=1)
                -> Covering index scan on t1 using k  (rows=3)

Warnings:
Note	1276	Field or reference 'test.out1.k' of SELECT #2 was resolved in SELECT #1
SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT MIN(out1.k + k) - 1 FROM t1);
k
1
2
4
explain format=tree SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT out1.k + MIN(k) - 1 FROM t1);
EXPLAIN
-> Nested loop inner join  (rows=3)
    -> Filter: (t2.k is not null)  (rows=3)
        -> Table scan on t2  (rows=3)
    -> Filter: (out1.pk = (select #2))  (rows=1)
        -> Single-row index lookup on out1 using k (k = t2.k)  (rows=1)
        -> Select #2 (subquery in condition; dependent)
            -> Aggregate:   (rows=1)
                -> Covering index scan on t1 using k  (rows=3)

Warnings:
Note	1276	Field or reference 'test.out1.k' of SELECT #2 was resolved in SELECT #1
SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT out1.k + MIN(k) - 1 FROM t1);
k
1
2
4
explain format=tree SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT MIN(out1.k) + MIN(k) - 1 FROM t1);
EXPLAIN
-> Nested loop inner join  (rows=3)
    -> Filter: (t2.k is not null)  (rows=3)
        -> Table scan on t2  (rows=3)
    -> Filter: (out1.pk = (select #2))  (rows=1)
        -> Single-row index lookup on out1 using k (k = t2.k)  (rows=1)
        -> Select #2 (subquery in condition; dependent)
            -> Aggregate: min(out1.k)  (rows=1)
                -> Covering index scan on t1 using k  (rows=3)

Warnings:
Note	1276	Field or reference 'test.out1.k' of SELECT #2 was resolved in SELECT #1
SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT MIN(out1.k) + MIN(k) - 1 FROM t1);
k
1
2
4
explain format=tree SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT SUM(out1.k) / COUNT(k) FROM t1);
EXPLAIN
-> Nested loop inner join  (rows=3)
    -> Filter: (t2.k is not null)  (rows=3)
        -> Table scan on t2  (rows=3)
    -> Filter: (out1.pk = (select #2))  (rows=1)
        -> Single-row index lookup on out1 using k (k = t2.k)  (rows=1)
        -> Select #2 (subquery in condition; dependent)
            -> Aggregate: count(t1.k), sum(out1.k)  (rows=1)
                -> Covering index scan on t1 using k  (rows=3)

Warnings:
Note	1276	Field or reference 'test.out1.k' of SELECT #2 was resolved in SELECT #1
SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT SUM(out1.k) / COUNT(k) FROM t1);
k
1
2
4
explain format=tree SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT SUM(out1.k) / COUNT(*) FROM t1);
EXPLAIN
-> Nested loop inner join  (rows=3)
    -> Filter: (t2.k is not null)  (rows=3)
        -> Table scan on t2  (rows=3)
    -> Filter: (out1.pk = (select #2))  (rows=1)
        -> Single-row index lookup on out1 using k (k = t2.k)  (rows=1)
        -> Select #2 (subquery in condition; dependent)
            -> Aggregate: count(0), sum(out1.k)  (rows=1)
                -> Covering index scan on t1 using PRIMARY  (rows=3)

Warnings:
Note	1276	Field or reference 'test.out1.k' of SELECT #2 was resolved in SELECT #1
SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT SUM(out1.k) / COUNT(*) FROM t1);
k
1
2
4
explain format=tree SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT AVG(out1.k) FROM t1);
EXPLAIN
-> Nested loop inner join  (rows=3)
    -> Filter: (t2.k is not null)  (rows=3)
        -> Table scan on t2  (rows=3)
    -> Filter: (out1.pk = (select #2))  (rows=1)
        -> Single-row index lookup on out1 using k (k = t2.k)  (rows=1)
        -> Select #2 (subquery in condition; dependent)
            -> Aggregate: avg(out1.k)  (rows=1)
                -> Covering index scan on t1 using PRIMARY  (rows=3)

Warnings:
Note	1276	Field or reference 'test.out1.k' of SELECT #2 was resolved in SELECT #1
SELECT t2.k FROM t1 out1, t2 WHERE t2.k = out1.k
AND out1.pk = (SELECT AVG(out1.k) FROM t1);
k
1
2
4
DROP TABLE t1, t2;
# End of test for Bug#24484060
#
# Bug#24657798 HANDLER::HA_INDEX_INIT() TRIES TO USE
# AN UNLOCKED CONST TABLE IN OPT_SUM_QUERY()
#
CREATE TABLE t1 (k INT, KEY(k)) ENGINE=MyISAM;
INSERT INTO t1 VALUES (1);
CREATE TABLE t2 (h INT) ENGINE=MyISAM;
INSERT INTO t2 VALUES (3);
explain format=tree SELECT * FROM t1 WHERE (SELECT MIN(t1.k) FROM t2);
EXPLAIN
-> Rows fetched before execution  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.k' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 WHERE (SELECT MIN(t1.k) FROM t2);
k
1
explain format=tree SELECT * FROM t1 WHERE EXISTS (SELECT MIN(t1.k) FROM t1 s1);
EXPLAIN
-> Rows fetched before execution  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.k' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 WHERE EXISTS (SELECT MIN(t1.k) FROM t1 s1);
k
1
explain format=tree SELECT * FROM t1 WHERE EXISTS (SELECT MIN(t1.k) FROM t2);
EXPLAIN
-> Rows fetched before execution  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.k' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 WHERE EXISTS (SELECT MIN(t1.k) FROM t2);
k
1
UPDATE t1 SET k=0;
explain format=tree SELECT * FROM t1 WHERE (SELECT MIN(t1.k) FROM t2);
EXPLAIN
-> Zero rows (Impossible WHERE noticed after reading const tables)  (rows=0)

Warnings:
Note	1276	Field or reference 'test.t1.k' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 WHERE (SELECT MIN(t1.k) FROM t2);
k
explain format=tree SELECT * FROM t1 WHERE EXISTS (SELECT MIN(t1.k) FROM t1 s1);
EXPLAIN
-> Rows fetched before execution  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.k' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 WHERE EXISTS (SELECT MIN(t1.k) FROM t1 s1);
k
0
explain format=tree SELECT * FROM t1 WHERE EXISTS (SELECT MIN(t1.k) FROM t2);
EXPLAIN
-> Rows fetched before execution  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.k' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 WHERE EXISTS (SELECT MIN(t1.k) FROM t2);
k
0
DROP TABLE t1;
CREATE TABLE t1 (nk INT, k INT, KEY(k)) ENGINE=MyISAM;
INSERT INTO t1 VALUES (1,1);
explain format=tree SELECT * FROM t1 WHERE nk = (SELECT MIN(t1.k) FROM t1 s1);
EXPLAIN
-> Rows fetched before execution  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.k' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 WHERE nk = (SELECT MIN(t1.k) FROM t1 s1);
nk	k
1	1
explain format=tree SELECT * FROM t1 WHERE nk = (SELECT MIN(t1.k) FROM t2);
EXPLAIN
-> Rows fetched before execution  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.k' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 WHERE nk = (SELECT MIN(t1.k) FROM t2);
nk	k
1	1
UPDATE t1 SET nk=0, k=0;
explain format=tree SELECT * FROM t1 WHERE nk = (SELECT MIN(t1.k) FROM t1 s1);
EXPLAIN
-> Rows fetched before execution  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.k' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 WHERE nk = (SELECT MIN(t1.k) FROM t1 s1);
nk	k
0	0
explain format=tree SELECT * FROM t1 WHERE nk = (SELECT MIN(t1.k) FROM t2);
EXPLAIN
-> Rows fetched before execution  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.k' of SELECT #2 was resolved in SELECT #1
SELECT * FROM t1 WHERE nk = (SELECT MIN(t1.k) FROM t2);
nk	k
0	0
DROP TABLE t1;
DROP TABLE t2;
# End of test for Bug#24657798
#
# Bug#24423143 - WRONG RESULTS FOR AGGREGATE QUERY
#
# Test index merge tree scenario
CREATE TABLE a (
aggr_col int,
group_by_col int,
KEY aggr_col_key (aggr_col),
KEY group_by_col_key (group_by_col, aggr_col)
) ENGINE=InnoDB;
set optimizer_trace_max_mem_size=1048576;
set @@session.optimizer_trace='enabled=on';
set end_markers_in_json=on;
INSERT INTO a VALUES (2,3),(5,6),(6,3),(7,NULL),(9,NULL),(10,6);
ANALYZE TABLE a;
Table	Op	Msg_type	Msg_text
test.a	analyze	status	OK
SELECT group_by_col, MIN(aggr_col) FROM a
WHERE (group_by_col IN (70, 9)) OR (aggr_col > 2) GROUP BY group_by_col;
group_by_col	MIN(aggr_col)
NULL	7
3	6
6	5
EXPLAIN FORMAT=TREE SELECT group_by_col, MIN(aggr_col) FROM a
WHERE (group_by_col IN (70 ,9)) OR (aggr_col > 2) GROUP BY group_by_col;
EXPLAIN
-> Group aggregate: min(a.aggr_col)  (rows=1.83)
    -> Filter: ((a.group_by_col in (70,9)) or (a.aggr_col > 2))  (rows=3.33)
        -> Covering index scan on a using group_by_col_key  (rows=6)

SELECT TRACE RLIKE 'disjuntive_predicate_present' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
SELECT group_by_col, MAX(aggr_col) FROM a
WHERE (group_by_col IN (70, 9)) OR (aggr_col < 9) GROUP BY group_by_col;
group_by_col	MAX(aggr_col)
NULL	7
3	6
6	5
EXPLAIN FORMAT=TREE SELECT group_by_col, MAX(aggr_col) FROM a
WHERE (group_by_col IN (70 , 9)) OR (aggr_col < 9) GROUP BY group_by_col;
EXPLAIN
-> Group aggregate: max(a.aggr_col)  (rows=1.83)
    -> Filter: ((a.group_by_col in (70,9)) or (a.aggr_col < 9))  (rows=3.33)
        -> Covering index scan on a using group_by_col_key  (rows=6)

SELECT TRACE RLIKE 'disjuntive_predicate_present' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
# Test IMPOSSIBLE TREE scenario
ALTER TABLE a DROP KEY aggr_col_key;
SELECT group_by_col, MIN(aggr_col) FROM a
WHERE (group_by_col IN (70 ,9)) OR (aggr_col > 2) GROUP BY group_by_col;
group_by_col	MIN(aggr_col)
NULL	7
3	6
6	5
EXPLAIN FORMAT=TREE SELECT group_by_col, MIN(aggr_col) FROM a
WHERE (group_by_col IN (70, 9)) OR (aggr_col > 2) GROUP BY group_by_col;
EXPLAIN
-> Group aggregate: min(a.aggr_col)  (rows=1.83)
    -> Filter: ((a.group_by_col in (70,9)) or (a.aggr_col > 2))  (rows=3.33)
        -> Covering index scan on a using group_by_col_key  (rows=6)

SELECT TRACE RLIKE 'minmax_keypart_in_disjunctive_query' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
SELECT group_by_col, MAX(aggr_col) FROM a
WHERE (group_by_col IN (70, 9)) OR (aggr_col < 9) GROUP BY group_by_col;
group_by_col	MAX(aggr_col)
NULL	7
3	6
6	5
EXPLAIN FORMAT=TREE SELECT group_by_col, MAX(aggr_col) FROM a
WHERE (group_by_col IN (70, 9)) OR (aggr_col < 9) GROUP BY group_by_col;
EXPLAIN
-> Group aggregate: max(a.aggr_col)  (rows=1.83)
    -> Filter: ((a.group_by_col in (70,9)) or (a.aggr_col < 9))  (rows=3.33)
        -> Covering index scan on a using group_by_col_key  (rows=6)

SELECT TRACE RLIKE 'minmax_keypart_in_disjunctive_query' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
# Scenario 3: aggregate field used as equal expression.
SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE (group_by_col IN (3, 9)) OR (aggr_col = 9) GROUP BY group_by_col;
group_by_col	MIN(aggr_col)	MAX(aggr_col)
NULL	9	9
3	2	6
EXPLAIN FORMAT=TREE SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE (group_by_col IN (3, 9)) OR (aggr_col = 9) GROUP BY group_by_col;
EXPLAIN
-> Group aggregate: min(a.aggr_col), max(a.aggr_col)  (rows=1.63)
    -> Filter: ((a.group_by_col in (3,9)) or (a.aggr_col = 9))  (rows=2.67)
        -> Covering index scan on a using group_by_col_key  (rows=6)

SELECT TRACE RLIKE 'minmax_keypart_in_disjunctive_query' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
# Scenario 4: non aggregate field used as equal expression.
SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE (group_by_col = 3) OR (aggr_col > 8) GROUP BY group_by_col;
group_by_col	MIN(aggr_col)	MAX(aggr_col)
NULL	9	9
3	2	6
6	10	10
EXPLAIN FORMAT=TREE SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE (group_by_col = 3) OR (aggr_col > 8) GROUP BY group_by_col;
EXPLAIN
-> Group aggregate: min(a.aggr_col), max(a.aggr_col)  (rows=1.83)
    -> Filter: ((a.group_by_col = 3) or (a.aggr_col > 8))  (rows=3.33)
        -> Covering index scan on a using group_by_col_key  (rows=6)

SELECT TRACE RLIKE 'minmax_keypart_in_disjunctive_query' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
# Scenario 5: aggregate field used as non-zero expression.
INSERT INTO a VALUES(0, 3);
INSERT INTO a VALUES(0, 9);
INSERT INTO a VALUES(8, 0);
ANALYZE TABLE a;
Table	Op	Msg_type	Msg_text
test.a	analyze	status	OK
SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE (group_by_col = 9) OR aggr_col GROUP BY group_by_col;
group_by_col	MIN(aggr_col)	MAX(aggr_col)
NULL	7	9
0	8	8
3	2	6
6	5	10
9	0	0
EXPLAIN FORMAT=TREE SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE group_by_col = 9 OR aggr_col GROUP BY group_by_col;
EXPLAIN
-> Group aggregate: min(a.aggr_col), max(a.aggr_col)  (rows=2.86)
    -> Filter: ((a.group_by_col = 9) or (0 <> a.aggr_col))  (rows=8.2)
        -> Covering index scan on a using group_by_col_key  (rows=9)

SELECT TRACE RLIKE 'minmax_keypart_in_disjunctive_query' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
# Scenario 6: non aggregate field used as non-zero expression.
SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE group_by_col OR (aggr_col < 9) GROUP BY group_by_col;
group_by_col	MIN(aggr_col)	MAX(aggr_col)
NULL	7	7
0	8	8
3	0	6
6	5	10
9	0	0
EXPLAIN FORMAT=TREE SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE group_by_col OR (aggr_col < 9) GROUP BY group_by_col;
EXPLAIN
-> Group aggregate: min(a.aggr_col), max(a.aggr_col)  (rows=2.89)
    -> Filter: ((0 <> a.group_by_col) or (a.aggr_col < 9))  (rows=8.33)
        -> Covering index scan on a using group_by_col_key  (rows=9)

SELECT TRACE RLIKE 'minmax_keypart_in_disjunctive_query' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
# Scenario 7: aggregate field used in equal exp without a CONST
INSERT INTO a VALUES(1,1),(1,2),(2,1);
SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE aggr_col = group_by_col GROUP BY group_by_col;
group_by_col	MIN(aggr_col)	MAX(aggr_col)
1	1	1
EXPLAIN FORMAT=TREE SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE aggr_col = group_by_col GROUP BY group_by_col;
EXPLAIN
-> Group aggregate: min(a.aggr_col), max(a.aggr_col)  (rows=6.67)
    -> Filter: (a.group_by_col = a.aggr_col)  (rows=12)
        -> Covering index scan on a using group_by_col_key  (rows=12)

SELECT TRACE RLIKE 'minmax_keypart_in_disjunctive_query' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
# Scenario 8: aggregate field used in a non-eq exp without a CONST
SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE aggr_col < group_by_col GROUP BY group_by_col;
group_by_col	MIN(aggr_col)	MAX(aggr_col)
2	1	1
3	0	2
6	5	5
9	0	0
EXPLAIN FORMAT=TREE SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE aggr_col < group_by_col GROUP BY group_by_col;
EXPLAIN
-> Group aggregate: min(a.aggr_col), max(a.aggr_col)  (rows=2)
    -> Filter: (a.aggr_col < a.group_by_col)  (rows=4)
        -> Covering index scan on a using group_by_col_key  (rows=12)

SELECT TRACE RLIKE 'minmax_keypart_in_disjunctive_query' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
# Scenario 8
INSERT INTO a VALUES(0,1),(1,0),(0,0);
SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE aggr_col OR group_by_col GROUP BY group_by_col;
group_by_col	MIN(aggr_col)	MAX(aggr_col)
NULL	7	9
0	1	8
1	0	2
2	1	1
3	0	6
6	5	10
9	0	0
EXPLAIN FORMAT=TREE SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE aggr_col OR group_by_col GROUP BY group_by_col;
EXPLAIN
-> Group aggregate: min(a.aggr_col), max(a.aggr_col)  (rows=8.33)
    -> Filter: ((0 <> a.aggr_col) or (0 <> a.group_by_col))  (rows=14.9)
        -> Covering index scan on a using group_by_col_key  (rows=15)

SELECT TRACE RLIKE 'minmax_keypart_in_disjunctive_query' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
# Scenario 9
SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE aggr_col AND group_by_col GROUP BY group_by_col;
group_by_col	MIN(aggr_col)	MAX(aggr_col)
1	1	2
2	1	1
3	2	6
6	5	10
EXPLAIN FORMAT=TREE SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE aggr_col AND group_by_col GROUP BY group_by_col;
EXPLAIN
-> Filter: ((0 <> a.aggr_col) and (0 <> a.group_by_col))  (rows=7)
    -> Covering index skip scan for grouping on a using group_by_col_key over (NULL < aggr_col < 0) OR (0 < aggr_col)  (rows=7)

SELECT TRACE RLIKE 'minmax_keypart_in_disjunctive_query' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
0
# Scenario 10: Added for completion. This fix does not have an impact.
SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE aggr_col<>0 AND group_by_col<>0 GROUP BY group_by_col;
group_by_col	MIN(aggr_col)	MAX(aggr_col)
1	1	2
2	1	1
3	2	6
6	5	10
EXPLAIN FORMAT=TREE SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE aggr_col<>0 AND group_by_col<>0 GROUP BY group_by_col;
EXPLAIN
-> Filter: ((a.aggr_col <> 0) and (a.group_by_col <> 0))  (rows=7)
    -> Covering index skip scan for grouping on a using group_by_col_key over (NULL < aggr_col < 0) OR (0 < aggr_col)  (rows=7)

SELECT TRACE RLIKE 'minmax_keypart_in_disjunctive_query' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
0
# Scenario 11: ITEM_FUNC as an argument of ITEM_FUNC
SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE group_by_col OR (group_by_col < (aggr_col = 1)) GROUP BY group_by_col;
group_by_col	MIN(aggr_col)	MAX(aggr_col)
0	1	1
1	0	2
2	1	1
3	0	6
6	5	10
9	0	0
EXPLAIN FORMAT=TREE SELECT group_by_col, MIN(aggr_col), MAX(aggr_col) FROM a
WHERE group_by_col OR (group_by_col < (aggr_col = 1)) GROUP BY group_by_col;
EXPLAIN
-> Group aggregate: min(a.aggr_col), max(a.aggr_col)  (rows=8.33)
    -> Filter: ((0 <> a.group_by_col) or (a.group_by_col < (a.aggr_col = 1)))  (rows=14)
        -> Covering index scan on a using group_by_col_key  (rows=15)

SELECT TRACE RLIKE 'minmax_keypart_in_disjunctive_query' AS OK
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
OK
1
SET optimizer_trace_max_mem_size=DEFAULT;
SET optimizer_trace=DEFAULT;
SET end_markers_in_json=DEFAULT;
DROP TABLE a;
# End of test for Bug#24423143
#
# Bug #31475700 REGRESSION: MEMORY LEAK AFTER
#               ITEM_SUM_HYBRID::MIN_MAX_UPDATE_STR_FIELD
#
CREATE TABLE t(a TEXT);
INSERT t VALUES('a'),('a');
SELECT a, MAX(TRIM('1' FROM ~1)) FROM t GROUP BY a;
a	MAX(TRIM('1' FROM ~1))
a	8446744073709551614
DROP TABLE t;
#
# Bug#30242457 ASSERTION "!TABLE->CONST_TABLE"
#
CREATE TABLE t2(id1 INT, id2 CHAR (1), id3 INT, value INT,
KEY (id1,id2,id3)) COLLATE LATIN1_BIN;
CREATE TABLE t1(a INT,b INT, PRIMARY KEY(a)) ENGINE=MEMORY;
# One row in an in-memory table: row will be read before
# optimizing MAX in subquery. No result set, either when t2 is
# empty or not, since t1.a <> t1.b
INSERT INTO t1 VALUES(1,0);
SELECT * FROM t1 WHERE t1.a IN (SELECT MAX(t1.b) FROM t2);
a	b
INSERT INTO t2 VALUES (0, 'a', 3, 4);
SELECT * FROM t1 WHERE t1.a IN (SELECT MAX(t1.b) FROM t2);
a	b
EXPLAIN FORMAT=TREE SELECT * FROM t1 WHERE t1.a IN (SELECT MAX(t1.b) FROM t2);
EXPLAIN
-> Zero rows (Impossible WHERE noticed after reading const tables)  (rows=0)

Warnings:
Note	1276	Field or reference 'test.t1.b' of SELECT #2 was resolved in SELECT #1
DELETE FROM t1;
DELETE FROM t2;
# Now t1.a = t1.b, still not result if t2 is empty
INSERT INTO t1 VALUES(1,1);
SELECT * FROM t1 WHERE t1.a IN (SELECT MAX(t1.b) FROM t2);
a	b
# If t2 has at least one row, we will get a result row
INSERT INTO t2 VALUES (0, 'a', 3, 4);
SELECT * FROM t1 WHERE t1.a IN (SELECT MAX(t1.b) FROM t2);
a	b
1	1
EXPLAIN FORMAT=TREE SELECT * FROM t1 WHERE t1.a IN (SELECT MAX(t1.b) FROM t2);
EXPLAIN
-> Rows fetched before execution  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.b' of SELECT #2 was resolved in SELECT #1
# If we have two rows in t1, t1 will not be read before
# optimization of the subquery, so this didn't use to fail
# before this patch.
INSERT INTO t1 VALUES(0,0);
SELECT * FROM t1 WHERE t1.a IN (SELECT MAX(t1.b) FROM t2);
a	b
1	1
0	0
EXPLAIN FORMAT=TREE SELECT * FROM t1 WHERE t1.a IN (SELECT MAX(t1.b) FROM t2);
EXPLAIN
-> Filter: <in_optimizer>(t1.a,<exists>(select #2))  (rows=2)
    -> Table scan on t1  (rows=2)
    -> Select #2 (subquery in condition; dependent)
        -> Limit: 1 row(s)  (rows=1)
            -> Filter: (<cache>(t1.a) = <ref_null_helper>(max(t1.b)))  (rows=1)
                -> Aggregate: max(t1.b)  (rows=1)
                    -> Rows fetched before execution  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.b' of SELECT #2 was resolved in SELECT #1
DROP TABLE t1, t2;
# End of test for Bug#30242457
#
# Bug #32769369: SIG11 IN ITEM_FIELD::ITEM_FIELD|SQL/ITEM.CC WITH HYPERGRAPH
#
CREATE TABLE t1 (a INTEGER, KEY (a));
INSERT INTO t1 VALUES (1);
SELECT SQL_BUFFER_RESULT MIN(t1.a) FROM t1, t1 AS t2;
MIN(t1.a)
1
DROP TABLE t1;
# Bug#32266286 : ADDING GROUP BY SLOWS DOWN A QUERY.
#
CREATE TABLE t0 (i0 INTEGER);
INSERT INTO t0 VALUES (1), (2), (3), (4), (5);
INSERT INTO t0 VALUES (6), (7), (8), (9);
CREATE TABLE t1 (f1 INTEGER, f2 INTEGER, KEY (f1,f2));
INSERT INTO t1 SELECT 1, i0 from t0;
INSERT INTO t1 SELECT i0+1, i0*rand()*10 from t0;
INSERT INTO t1 VALUES (1,0);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN FORMAT=TREE SELECT MIN(f2) FROM t1 WHERE f1=1 GROUP BY f1;
EXPLAIN
-> Filter: (t1.f1 = 1)  (rows=1)
    -> Covering index skip scan for grouping on t1 using f1 over (f1 = 1)  (rows=1)

SELECT MIN(f2) FROM t1 WHERE f1=1 GROUP BY f1;
MIN(f2)
0
INSERT INTO t1 VALUES (NULL, 1);
INSERT INTO t1 VALUES (NULL, 2);
EXPLAIN FORMAT=TREE SELECT MIN(f2) FROM t1 WHERE f1 IS NULL GROUP BY f1;
EXPLAIN
-> Table scan on <temporary>  (rows=1)
    -> Temporary table with deduplication  (rows=1)
        -> Filter: (t1.f1 is null)  (rows=1)
            -> Covering index skip scan for grouping on t1 using f1 over (f1 = NULL)  (rows=1)

SELECT MIN(f2) FROM t1 WHERE f1 IS NULL GROUP BY f1;
MIN(f2)
1
DROP TABLE t0,t1;
#
# Bug#33139598: REGRESSION: CRASH IN GET_BEST_GROUP_MIN_MAX
#
CREATE TABLE t1 (f1 ENUM(''), KEY(f1));
INSERT INTO t1 VALUES(''),('');
SELECT 1 FROM t1 WHERE f1=UUID() GROUP BY f1 LIMIT 1;
1
DROP TABLE t1;
#
# Bug#33139598: Assertion `table_independent_conds == nullptr' failed.
#
CREATE TABLE t1 (f1 INTEGER, KEY(f1));
INSERT INTO t1 VALUES (1), (2);
CREATE FUNCTION f()
RETURNS INT DETERMINISTIC
BEGIN
SET @f:=1;
RETURN @f;
END$
SELECT MAX(f1) FROM t1 WHERE f() = 1;
MAX(f1)
2
SELECT MAX(f1) FROM t1 WHERE f() = 0;
MAX(f1)
NULL
DROP TABLE t1;
DROP FUNCTION f;
# Bug#34317267: Assertion `!table->file->inited' failed in Mysql 8.0.29.
CREATE TABLE t1 (k INT, KEY(k)) ENGINE=MyISAM;
INSERT INTO t1 VALUES (1);
SELECT *
FROM t1 AS ra0 LEFT JOIN t1 AS ra1
ON ra0.k IN (SELECT MAX(ra0.k) FROM t1);
k	k
1	1
SELECT *
FROM t1 AS ra0 LEFT JOIN t1 AS ra1
ON ra0.k IN (SELECT MIN(ra0.k) FROM t1);
k	k
1	1
SELECT *
FROM t1 AS ra0 LEFT JOIN t1 AS ra1
ON ra0.k IN (SELECT COUNT(ra0.k) FROM t1);
k	k
1	1
DROP TABLE t1;
#
# Bug#35842412 WHERE NOT IN with subquery is much slower on 8.1 than 5.7
#
CREATE TABLE t1( b_id INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
val VARCHAR(100) NOT NULL, val_n VARCHAR(100),
KEY ix_val (val, b_id), KEY ix_val_n (val_n, b_id));
CREATE TABLE t2( a_id INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
val VARCHAR(100) NOT NULL, val_n VARCHAR(100),
UNIQUE KEY ix_val (val), UNIQUE KEY ix_val_n (val_n));
INSERT INTO t1(val, val_n)
WITH RECURSIVE DataSource(num, c1, c2) AS (
SELECT 1, lpad(1,25,'0'), lpad(1,25,'0')
UNION ALL
SELECT num + 1, lpad(mod(num+1,30),25,'0'),
lpad(ELT(num%400 != 0, mod(num+1,30)), 25, '0')
FROM DataSource
WHERE num < 1000
)
SELECT c1, c2 from DataSource;
INSERT INTO t2(val, val_n)
WITH RECURSIVE DataSource(num, c1, c2) AS (
SELECT 1, lpad(1,25,'0'), lpad(1,25,'0')
UNION ALL
SELECT num + 1, lpad(num+1,25,'0'), lpad(ELT(num%13 != 0, num+1), 25, '0')
FROM DataSource
WHERE num < 30
)
SELECT c1, c2 from DataSource;
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	Table is already up to date
test.t2	analyze	status	OK
# Should use group skip scan on t1
EXPLAIN FORMAT=TREE SELECT DISTINCT val FROM t1 WHERE EXISTS
(SELECT * FROM t2 WHERE t2.val = t1.val);
EXPLAIN
-> Nested loop inner join  (rows=31)
    -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
    -> Single-row covering index lookup on t2 using ix_val (val = t1.val)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.val' of SELECT #2 was resolved in SELECT #1
SELECT DISTINCT val FROM t1 WHERE EXISTS
(SELECT * FROM t2 WHERE t2.val = t1.val);
val
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000014
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000027
0000000000000000000000028
0000000000000000000000029
EXPLAIN FORMAT=TREE SELECT val FROM t1 WHERE EXISTS
(SELECT * FROM t2 WHERE t2.val = t1.val) GROUP BY val;
EXPLAIN
-> Nested loop inner join  (rows=31)
    -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
    -> Single-row covering index lookup on t2 using ix_val (val = t1.val)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.val' of SELECT #2 was resolved in SELECT #1
SELECT val FROM t1 WHERE EXISTS
(SELECT * FROM t2 WHERE t2.val = t1.val) GROUP BY val;
val
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000014
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000027
0000000000000000000000028
0000000000000000000000029
EXPLAIN FORMAT=TREE SELECT DISTINCT val FROM t1 WHERE val IN (SELECT val FROM t2);
EXPLAIN
-> Nested loop inner join  (rows=31)
    -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
    -> Single-row covering index lookup on t2 using ix_val (val = t1.val)  (rows=1)

SELECT DISTINCT val FROM t1 WHERE val IN (SELECT val FROM t2);
val
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000014
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000027
0000000000000000000000028
0000000000000000000000029
EXPLAIN FORMAT=TREE SELECT DISTINCT val FROM t1 WHERE val IN
(SELECT val FROM t2 WHERE t2.val = t1.val);
EXPLAIN
-> Nested loop inner join  (rows=31)
    -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
    -> Single-row covering index lookup on t2 using ix_val (val = t1.val)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.val' of SELECT #2 was resolved in SELECT #1
SELECT DISTINCT val FROM t1 WHERE val IN
(SELECT val FROM t2 WHERE t2.val = t1.val);
val
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000014
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000027
0000000000000000000000028
0000000000000000000000029
EXPLAIN FORMAT=TREE SELECT val FROM t1 WHERE val IN (SELECT val FROM t2 ) GROUP BY val;
EXPLAIN
-> Nested loop inner join  (rows=31)
    -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
    -> Single-row covering index lookup on t2 using ix_val (val = t1.val)  (rows=1)

SELECT val FROM t1 WHERE val IN (SELECT val FROM t2 ) GROUP BY val;
val
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000014
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000027
0000000000000000000000028
0000000000000000000000029
EXPLAIN FORMAT=TREE SELECT val FROM t1 WHERE val IN
(SELECT val FROM t2 WHERE t2.val = t1.val ) GROUP BY val;
EXPLAIN
-> Nested loop inner join  (rows=31)
    -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
    -> Single-row covering index lookup on t2 using ix_val (val = t1.val)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.val' of SELECT #2 was resolved in SELECT #1
SELECT val FROM t1 WHERE val IN
(SELECT val FROM t2 WHERE t2.val = t1.val ) GROUP BY val;
val
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000014
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000027
0000000000000000000000028
0000000000000000000000029
EXPLAIN FORMAT=TREE SELECT DISTINCT val FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE val = 'asd');
EXPLAIN
-> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)

SELECT DISTINCT val FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE val = 'asd');
val
0000000000000000000000000
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000014
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000027
0000000000000000000000028
0000000000000000000000029
EXPLAIN FORMAT=TREE SELECT val FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE val = 'asd' ) GROUP BY val;
EXPLAIN
-> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)

SELECT val FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE val = 'asd' ) GROUP BY val;
val
0000000000000000000000000
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000014
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000027
0000000000000000000000028
0000000000000000000000029
# Should use group skip scan on t1
EXPLAIN FORMAT=TREE SELECT DISTINCT val FROM t1 WHERE val NOT IN (SELECT val FROM t2);
EXPLAIN
-> Nested loop antijoin  (rows=930)
    -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
    -> Single-row index lookup on <subquery2> using <auto_distinct_key> (val = t1.val)  (rows=1)
        -> Materialize with deduplication  (rows=30)
            -> Filter: (t2.val is not null)  (rows=30)
                -> Covering index scan on t2 using ix_val  (rows=30)

SELECT DISTINCT val FROM t1 WHERE val NOT IN (SELECT val FROM t2);
val
0000000000000000000000000
EXPLAIN FORMAT=TREE SELECT DISTINCT val FROM t1 WHERE val NOT IN
(SELECT val FROM t2 WHERE t2.val = t1.val);
EXPLAIN
-> Nested loop antijoin  (rows=930)
    -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
    -> Single-row index lookup on <subquery2> using <auto_distinct_key> (val = t1.val, val = t1.val)  (rows=1)
        -> Materialize with deduplication  (rows=30)
            -> Filter: ((t2.val is not null) and (t2.val is not null))  (rows=30)
                -> Covering index scan on t2 using ix_val  (rows=30)

Warnings:
Note	1276	Field or reference 'test.t1.val' of SELECT #2 was resolved in SELECT #1
SELECT DISTINCT val FROM t1 WHERE val NOT IN
(SELECT val FROM t2 WHERE t2.val = t1.val);
val
0000000000000000000000000
EXPLAIN FORMAT=TREE SELECT val FROM t1 WHERE val NOT IN
(SELECT val FROM t2 ) GROUP BY val;
EXPLAIN
-> Nested loop antijoin  (rows=930)
    -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
    -> Single-row index lookup on <subquery2> using <auto_distinct_key> (val = t1.val)  (rows=1)
        -> Materialize with deduplication  (rows=30)
            -> Filter: (t2.val is not null)  (rows=30)
                -> Covering index scan on t2 using ix_val  (rows=30)

SELECT val FROM t1 WHERE val NOT IN
(SELECT val FROM t2 ) GROUP BY val;
val
0000000000000000000000000
EXPLAIN FORMAT=TREE SELECT val FROM t1 WHERE val NOT IN
(SELECT val FROM t2 WHERE t2.val = t1.val ) GROUP BY val;
EXPLAIN
-> Nested loop antijoin  (rows=930)
    -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
    -> Single-row index lookup on <subquery2> using <auto_distinct_key> (val = t1.val, val = t1.val)  (rows=1)
        -> Materialize with deduplication  (rows=30)
            -> Filter: ((t2.val is not null) and (t2.val is not null))  (rows=30)
                -> Covering index scan on t2 using ix_val  (rows=30)

Warnings:
Note	1276	Field or reference 'test.t1.val' of SELECT #2 was resolved in SELECT #1
SELECT val FROM t1 WHERE val NOT IN
(SELECT val FROM t2 WHERE t2.val = t1.val ) GROUP BY val;
val
0000000000000000000000000
EXPLAIN FORMAT=TREE SELECT DISTINCT val FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE t2.val = t1.val);
EXPLAIN
-> Nested loop antijoin  (rows=930)
    -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
    -> Single-row index lookup on <subquery2> using <auto_distinct_key> (val = t1.val)  (rows=1)
        -> Materialize with deduplication  (rows=30)
            -> Filter: (t2.val is not null)  (rows=30)
                -> Covering index scan on t2 using ix_val  (rows=30)

Warnings:
Note	1276	Field or reference 'test.t1.val' of SELECT #2 was resolved in SELECT #1
SELECT DISTINCT val FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE t2.val = t1.val);
val
0000000000000000000000000
EXPLAIN FORMAT=TREE SELECT val FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE t2.val = t1.val) GROUP BY val;
EXPLAIN
-> Nested loop antijoin  (rows=930)
    -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
    -> Single-row index lookup on <subquery2> using <auto_distinct_key> (val = t1.val)  (rows=1)
        -> Materialize with deduplication  (rows=30)
            -> Filter: (t2.val is not null)  (rows=30)
                -> Covering index scan on t2 using ix_val  (rows=30)

Warnings:
Note	1276	Field or reference 'test.t1.val' of SELECT #2 was resolved in SELECT #1
SELECT val FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE t2.val = t1.val) GROUP BY val;
val
0000000000000000000000000
# Should use group skip scan on t1
EXPLAIN FORMAT=TREE SELECT DISTINCT val FROM t1 WHERE EXISTS (SELECT * FROM t2);
EXPLAIN
-> Table scan on <temporary>  (rows=31)
    -> Temporary table with deduplication  (rows=31)
        -> Inner hash join (FirstMatch)  (rows=31)
            -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
            -> Hash
                -> Limit: 1 row(s)  (rows=1)
                    -> Covering index scan on t2 using PRIMARY  (rows=30)

SELECT DISTINCT val FROM t1 WHERE EXISTS (SELECT * FROM t2);
val
0000000000000000000000000
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000014
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000027
0000000000000000000000028
0000000000000000000000029
EXPLAIN FORMAT=TREE SELECT val FROM t1 WHERE EXISTS (SELECT * FROM t2) GROUP BY val;
EXPLAIN
-> Table scan on <temporary>  (rows=31)
    -> Temporary table with deduplication  (rows=31)
        -> Inner hash join (FirstMatch)  (rows=31)
            -> Covering index skip scan for deduplication on t1 using ix_val  (rows=31)
            -> Hash
                -> Limit: 1 row(s)  (rows=1)
                    -> Covering index scan on t2 using PRIMARY  (rows=30)

SELECT val FROM t1 WHERE EXISTS (SELECT * FROM t2) GROUP BY val;
val
0000000000000000000000000
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000014
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000027
0000000000000000000000028
0000000000000000000000029
# Uses group skip scan with aggregate functions since "t2" is a const
# table. Hypergraph optimizer does not support const table optimization.
# So the plan chosen is different.
EXPLAIN FORMAT=TREE SELECT val, MIN(b_id), MAX(b_id) FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE val = 'asd') GROUP BY val;
EXPLAIN
-> Covering index skip scan for grouping on t1 using ix_val  (rows=31)

SELECT val, MIN(b_id), MAX(b_id) FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE val = 'asd') GROUP BY val;
val	MIN(b_id)	MAX(b_id)
0000000000000000000000000	30	990
0000000000000000000000001	1	991
0000000000000000000000002	2	992
0000000000000000000000003	3	993
0000000000000000000000004	4	994
0000000000000000000000005	5	995
0000000000000000000000006	6	996
0000000000000000000000007	7	997
0000000000000000000000008	8	998
0000000000000000000000009	9	999
0000000000000000000000010	10	1000
0000000000000000000000011	11	971
0000000000000000000000012	12	972
0000000000000000000000013	13	973
0000000000000000000000014	14	974
0000000000000000000000015	15	975
0000000000000000000000016	16	976
0000000000000000000000017	17	977
0000000000000000000000018	18	978
0000000000000000000000019	19	979
0000000000000000000000020	20	980
0000000000000000000000021	21	981
0000000000000000000000022	22	982
0000000000000000000000023	23	983
0000000000000000000000024	24	984
0000000000000000000000025	25	985
0000000000000000000000026	26	986
0000000000000000000000027	27	987
0000000000000000000000028	28	988
0000000000000000000000029	29	989
# Should not use group skip scan on t1. Group skip scan is not supported
# on multi-table queries with aggregate functions.
EXPLAIN FORMAT=TREE SELECT val, MIN(b_id), MAX(b_id) FROM t1 WHERE val
NOT IN (SELECT val FROM t2) GROUP BY val;
EXPLAIN
-> Group aggregate: min(t1.b_id), max(t1.b_id)  (rows=30.3)
    -> Nested loop antijoin  (rows=30000)
        -> Covering index scan on t1 using ix_val  (rows=1000)
        -> Single-row index lookup on <subquery2> using <auto_distinct_key> (val = t1.val)  (rows=1)
            -> Materialize with deduplication  (rows=30)
                -> Filter: (t2.val is not null)  (rows=30)
                    -> Covering index scan on t2 using ix_val  (rows=30)

SELECT val, MIN(b_id), MAX(b_id) FROM t1 WHERE val
NOT IN (SELECT val FROM t2) GROUP BY val;
val	MIN(b_id)	MAX(b_id)
0000000000000000000000000	30	990
EXPLAIN FORMAT=TREE SELECT val, MIN(b_id), MAX(b_id) FROM t1 WHERE EXISTS
(SELECT * FROM t2) GROUP BY val;
EXPLAIN
-> Table scan on <temporary>
    -> Aggregate using temporary table
        -> Inner hash join (FirstMatch)  (rows=1000)
            -> Covering index scan on t1 using ix_val  (rows=1000)
            -> Hash
                -> Limit: 1 row(s)  (rows=1)
                    -> Covering index scan on t2 using PRIMARY  (rows=30)

SELECT val, MIN(b_id), MAX(b_id) FROM t1 WHERE EXISTS
(SELECT * FROM t2) GROUP BY val;
val	MIN(b_id)	MAX(b_id)
0000000000000000000000000	30	990
0000000000000000000000001	1	991
0000000000000000000000002	2	992
0000000000000000000000003	3	993
0000000000000000000000004	4	994
0000000000000000000000005	5	995
0000000000000000000000006	6	996
0000000000000000000000007	7	997
0000000000000000000000008	8	998
0000000000000000000000009	9	999
0000000000000000000000010	10	1000
0000000000000000000000011	11	971
0000000000000000000000012	12	972
0000000000000000000000013	13	973
0000000000000000000000014	14	974
0000000000000000000000015	15	975
0000000000000000000000016	16	976
0000000000000000000000017	17	977
0000000000000000000000018	18	978
0000000000000000000000019	19	979
0000000000000000000000020	20	980
0000000000000000000000021	21	981
0000000000000000000000022	22	982
0000000000000000000000023	23	983
0000000000000000000000024	24	984
0000000000000000000000025	25	985
0000000000000000000000026	26	986
0000000000000000000000027	27	987
0000000000000000000000028	28	988
0000000000000000000000029	29	989
# Cover nullable column scenarios
# Should use group skip scan on t1
EXPLAIN FORMAT=TREE SELECT DISTINCT val_n FROM t1 WHERE EXISTS
(SELECT * FROM t2 WHERE t2.val_n = t1.val_n);
EXPLAIN
-> Nested loop inner join  (rows=33)
    -> Filter: (t1.val_n is not null)  (rows=33)
        -> Covering index skip scan for deduplication on t1 using ix_val_n  (rows=33)
    -> Single-row covering index lookup on t2 using ix_val_n (val_n = t1.val_n)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.val_n' of SELECT #2 was resolved in SELECT #1
SELECT DISTINCT val_n FROM t1 WHERE EXISTS
(SELECT * FROM t2 WHERE t2.val_n = t1.val_n);
val_n
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000028
0000000000000000000000029
EXPLAIN FORMAT=TREE SELECT DISTINCT val_n FROM t1 WHERE val_n IN (SELECT val_n FROM t2);
EXPLAIN
-> Nested loop inner join  (rows=33)
    -> Filter: (t1.val_n is not null)  (rows=33)
        -> Covering index skip scan for deduplication on t1 using ix_val_n  (rows=33)
    -> Single-row covering index lookup on t2 using ix_val_n (val_n = t1.val_n)  (rows=1)

SELECT DISTINCT val_n FROM t1 WHERE val_n IN (SELECT val_n FROM t2);
val_n
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000028
0000000000000000000000029
EXPLAIN FORMAT=TREE SELECT DISTINCT val_n FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE val_n = 'asd');
EXPLAIN
-> Covering index skip scan for deduplication on t1 using ix_val_n  (rows=33)

SELECT DISTINCT val_n FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE val_n = 'asd');
val_n
NULL
0000000000000000000000000
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000014
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000027
0000000000000000000000028
0000000000000000000000029
# Should use group skip scan on t1
EXPLAIN FORMAT=TREE SELECT DISTINCT val_n FROM t1 WHERE val_n NOT IN
(SELECT val_n FROM t2);
EXPLAIN
-> Filter: <in_optimizer>(t1.val_n,t1.val_n in (select #2) is false)  (rows=33)
    -> Covering index skip scan for deduplication on t1 using ix_val_n  (rows=33)
    -> Select #2 (subquery in condition; run only once)
        -> Filter: ((t1.val_n = `<materialized_subquery>`.val_n))  (rows=1)
            -> Limit: 1 row(s)  (rows=1)
                -> Index lookup on <materialized_subquery> using <auto_distinct_key> (val_n = t1.val_n)
                    -> Materialize with deduplication  (rows=30)
                        -> Covering index scan on t2 using ix_val_n  (rows=30)

SELECT DISTINCT val_n FROM t1 WHERE val_n NOT IN
(SELECT val_n FROM t2);
val_n
EXPLAIN FORMAT=TREE SELECT DISTINCT val_n FROM t1 WHERE val_n NOT IN
(SELECT val_n FROM t2 WHERE t2.val_n = t1.val_n);
EXPLAIN
-> Filter: <in_optimizer>(t1.val_n,<exists>(select #2) is false)  (rows=33)
    -> Covering index skip scan for deduplication on t1 using ix_val_n  (rows=33)
    -> Select #2 (subquery in condition; dependent)
        -> Limit: 1 row(s)  (rows=1)
            -> Filter: <if>(outer_field_is_not_null, <is_not_null_test>(t2.val_n), true)  (rows=1)
                -> Filter: <if>(outer_field_is_not_null, ((<cache>(t1.val_n) = t2.val_n) or (t2.val_n is null)), true)  (rows=1)
                    -> Single-row covering index lookup on t2 using ix_val_n (val_n = t1.val_n)  (rows=1)

Warnings:
Note	1276	Field or reference 'test.t1.val_n' of SELECT #2 was resolved in SELECT #1
SELECT DISTINCT val_n FROM t1 WHERE val_n NOT IN
(SELECT val_n FROM t2 WHERE t2.val_n = t1.val_n);
val_n
NULL
0000000000000000000000000
0000000000000000000000014
0000000000000000000000027
EXPLAIN FORMAT=TREE SELECT DISTINCT val_n FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE t2.val_n = t1.val_n);
EXPLAIN
-> Nested loop antijoin  (rows=990)
    -> Covering index skip scan for deduplication on t1 using ix_val_n  (rows=33)
    -> Single-row index lookup on <subquery2> using <auto_distinct_key> (val_n = t1.val_n)  (rows=1)
        -> Materialize with deduplication  (rows=30)
            -> Filter: (t2.val_n is not null)  (rows=30)
                -> Covering index scan on t2 using ix_val_n  (rows=30)

Warnings:
Note	1276	Field or reference 'test.t1.val_n' of SELECT #2 was resolved in SELECT #1
SELECT DISTINCT val_n FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE t2.val_n = t1.val_n);
val_n
NULL
0000000000000000000000000
0000000000000000000000014
0000000000000000000000027
EXPLAIN FORMAT=TREE SELECT val_n FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE t2.val_n = t1.val_n) GROUP BY val_n;
EXPLAIN
-> Nested loop antijoin  (rows=990)
    -> Covering index skip scan for deduplication on t1 using ix_val_n  (rows=33)
    -> Single-row index lookup on <subquery2> using <auto_distinct_key> (val_n = t1.val_n)  (rows=1)
        -> Materialize with deduplication  (rows=30)
            -> Filter: (t2.val_n is not null)  (rows=30)
                -> Covering index scan on t2 using ix_val_n  (rows=30)

Warnings:
Note	1276	Field or reference 'test.t1.val_n' of SELECT #2 was resolved in SELECT #1
SELECT val_n FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE t2.val_n = t1.val_n) GROUP BY val_n;
val_n
NULL
0000000000000000000000000
0000000000000000000000014
0000000000000000000000027
# Should use group skip scan on t1
EXPLAIN FORMAT=TREE SELECT DISTINCT val_n FROM t1 WHERE EXISTS (SELECT * FROM t2);
EXPLAIN
-> Table scan on <temporary>  (rows=33)
    -> Temporary table with deduplication  (rows=33)
        -> Inner hash join (FirstMatch)  (rows=33)
            -> Covering index skip scan for deduplication on t1 using ix_val_n  (rows=33)
            -> Hash
                -> Limit: 1 row(s)  (rows=1)
                    -> Covering index scan on t2 using PRIMARY  (rows=30)

SELECT DISTINCT val_n FROM t1 WHERE EXISTS (SELECT * FROM t2);
val_n
NULL
0000000000000000000000000
0000000000000000000000001
0000000000000000000000002
0000000000000000000000003
0000000000000000000000004
0000000000000000000000005
0000000000000000000000006
0000000000000000000000007
0000000000000000000000008
0000000000000000000000009
0000000000000000000000010
0000000000000000000000011
0000000000000000000000012
0000000000000000000000013
0000000000000000000000014
0000000000000000000000015
0000000000000000000000016
0000000000000000000000017
0000000000000000000000018
0000000000000000000000019
0000000000000000000000020
0000000000000000000000021
0000000000000000000000022
0000000000000000000000023
0000000000000000000000024
0000000000000000000000025
0000000000000000000000026
0000000000000000000000027
0000000000000000000000028
0000000000000000000000029
# Uses group skip scan with aggregate functions since "t2" is a const
# table. Hypergraph optimizer does not support const table optimization.
# So the plan chosen is different.
EXPLAIN FORMAT=TREE SELECT val_n, MIN(b_id), MAX(b_id) FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE val_n = 'asd') GROUP BY val_n;
EXPLAIN
-> Covering index skip scan for grouping on t1 using ix_val_n  (rows=33)

SELECT val_n, MIN(b_id), MAX(b_id) FROM t1 WHERE NOT EXISTS
(SELECT * FROM t2 WHERE val_n = 'asd') GROUP BY val_n;
val_n	MIN(b_id)	MAX(b_id)
NULL	401	801
0000000000000000000000000	30	990
0000000000000000000000001	1	991
0000000000000000000000002	2	992
0000000000000000000000003	3	993
0000000000000000000000004	4	994
0000000000000000000000005	5	995
0000000000000000000000006	6	996
0000000000000000000000007	7	997
0000000000000000000000008	8	998
0000000000000000000000009	9	999
0000000000000000000000010	10	1000
0000000000000000000000011	11	971
0000000000000000000000012	12	972
0000000000000000000000013	13	973
0000000000000000000000014	14	974
0000000000000000000000015	15	975
0000000000000000000000016	16	976
0000000000000000000000017	17	977
0000000000000000000000018	18	978
0000000000000000000000019	19	979
0000000000000000000000020	20	980
0000000000000000000000021	21	981
0000000000000000000000022	22	982
0000000000000000000000023	23	983
0000000000000000000000024	24	984
0000000000000000000000025	25	985
0000000000000000000000026	26	986
0000000000000000000000027	27	987
0000000000000000000000028	28	988
0000000000000000000000029	29	989
# Should not use group skip scan on t1. Group skip scan is not supported
# on multi-table queries with aggregate functions.
EXPLAIN FORMAT=TREE SELECT val_n, MIN(b_id), MAX(b_id) FROM t1 WHERE val_n
NOT IN (SELECT val_n FROM t2) GROUP BY val_n;
EXPLAIN
-> Group aggregate: min(t1.b_id), max(t1.b_id)  (rows=32.3)
    -> Filter: <in_optimizer>(t1.val_n,t1.val_n in (select #2) is false)  (rows=1000)
        -> Covering index scan on t1 using ix_val_n  (rows=1000)
        -> Select #2 (subquery in condition; run only once)
            -> Filter: ((t1.val_n = `<materialized_subquery>`.val_n))  (rows=1)
                -> Limit: 1 row(s)  (rows=1)
                    -> Index lookup on <materialized_subquery> using <auto_distinct_key> (val_n = t1.val_n)
                        -> Materialize with deduplication  (rows=30)
                            -> Covering index scan on t2 using ix_val_n  (rows=30)

SELECT val_n, MIN(b_id), MAX(b_id) FROM t1 WHERE val_n
NOT IN (SELECT val_n FROM t2) GROUP BY val_n;
val_n	MIN(b_id)	MAX(b_id)
EXPLAIN FORMAT=TREE SELECT val_n, MIN(b_id), MAX(b_id) FROM t1 WHERE EXISTS
(SELECT * FROM t2) GROUP BY val_n;
EXPLAIN
-> Table scan on <temporary>
    -> Aggregate using temporary table
        -> Inner hash join (FirstMatch)  (rows=1000)
            -> Covering index scan on t1 using ix_val_n  (rows=1000)
            -> Hash
                -> Limit: 1 row(s)  (rows=1)
                    -> Covering index scan on t2 using PRIMARY  (rows=30)

SELECT val_n, MIN(b_id), MAX(b_id) FROM t1 WHERE EXISTS
(SELECT * FROM t2) GROUP BY val_n;
val_n	MIN(b_id)	MAX(b_id)
NULL	401	801
0000000000000000000000000	30	990
0000000000000000000000001	1	991
0000000000000000000000002	2	992
0000000000000000000000003	3	993
0000000000000000000000004	4	994
0000000000000000000000005	5	995
0000000000000000000000006	6	996
0000000000000000000000007	7	997
0000000000000000000000008	8	998
0000000000000000000000009	9	999
0000000000000000000000010	10	1000
0000000000000000000000011	11	971
0000000000000000000000012	12	972
0000000000000000000000013	13	973
0000000000000000000000014	14	974
0000000000000000000000015	15	975
0000000000000000000000016	16	976
0000000000000000000000017	17	977
0000000000000000000000018	18	978
0000000000000000000000019	19	979
0000000000000000000000020	20	980
0000000000000000000000021	21	981
0000000000000000000000022	22	982
0000000000000000000000023	23	983
0000000000000000000000024	24	984
0000000000000000000000025	25	985
0000000000000000000000026	26	986
0000000000000000000000027	27	987
0000000000000000000000028	28	988
0000000000000000000000029	29	989
DROP TABLE t1, t2;
#
# Bug#47762: Incorrect result from MIN() when WHERE tests NOT NULL column
#            for NULL
#
## Test for NULLs allowed
CREATE TABLE t1 ( a INT, KEY (a) );
INSERT INTO t1 VALUES (1), (2), (3);
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a = NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a = NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a <> NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a <> NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a > NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a > NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a < NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a < NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a <=> NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	No matching min/max row
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a <=> NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a BETWEEN NULL AND 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a BETWEEN NULL AND 10;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a BETWEEN NULL AND NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a BETWEEN NULL AND NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a BETWEEN 10 AND NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a BETWEEN 10 AND NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a = (SELECT a FROM t1 WHERE a < 0);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
x	x	x	NULL	x	x	x	x	x	x	100.00	Using where; Using index
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a = (SELECT a FROM t1 WHERE a < 0);
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a IS NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	No matching min/max row
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a IS NULL;
MIN( a )
NULL
INSERT INTO t1 VALUES (NULL), (NULL);
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a = NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a = NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a <> NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a <> NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a > NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a > NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a < NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a < NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a <=> NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	Select tables optimized away
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a <=> NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a BETWEEN NULL AND 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a BETWEEN NULL AND 10;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a BETWEEN NULL AND NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a BETWEEN NULL AND NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a BETWEEN 10 AND NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a BETWEEN 10 AND NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a = (SELECT a FROM t1 WHERE a < 0);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
x	x	x	NULL	x	x	x	x	x	x	100.00	Using where; Using index
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a = (SELECT a FROM t1 WHERE a < 0);
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a IS NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	Select tables optimized away
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a IS NULL;
MIN( a )
NULL
DROP TABLE t1;
## Test for NOT NULLs
CREATE TABLE t1 ( a INT NOT NULL PRIMARY KEY);
INSERT INTO t1 VALUES (1), (2), (3);
#
# NULL-safe operator test disabled for non-NULL indexed columns.
#
# See bugs
#
# - Bug#52174: Sometimes wrong plan when reading a MAX value from
#   non-NULL index
#
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a = NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a = NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a <> NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a <> NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a > NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a > NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a < NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a < NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a <=> NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a <=> NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a BETWEEN NULL AND 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a BETWEEN NULL AND 10;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a BETWEEN NULL AND NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a BETWEEN NULL AND NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a BETWEEN 10 AND NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a BETWEEN 10 AND NULL;
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a = (SELECT a FROM t1 WHERE a < 0);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	no matching row in const table
x	x	x	NULL	x	x	x	x	x	x	100.00	Using where; Using index
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a = (SELECT a FROM t1 WHERE a < 0);
MIN( a )
NULL
EXPLAIN
SELECT MIN( a ) FROM t1 WHERE a IS NULL;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
x	x	x	NULL	x	x	x	x	x	x	NULL	Impossible WHERE
Warnings:
x	x	x
SELECT MIN( a ) FROM t1 WHERE a IS NULL;
MIN( a )
NULL
DROP TABLE t1;
