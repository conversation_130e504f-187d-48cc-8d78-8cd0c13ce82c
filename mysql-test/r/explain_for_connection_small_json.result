"WL6369 Explain for Connection"
QUERY: DELETE from t1 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -4,10 +4,3 @@
     "select_id": 1,
-    "table": {
-      "delete": true,
-      "table_name": "t1",
-      "access_type": "ALL",
-      "rows_examined_per_scan": 1,
-      "filtered": "100.00",
-      "message": "Deleting all rows"
-    }
+    "message": "Plan isn't ready yet"
   }
QUERY: SELECT * from t1,t2,t3 where t1.a=t2.a AND t2.b=t3.a and t1.b=t3.b 
STATUS: EQUAL
QUERY: SELECT 1 FROM ( SELECT 1, 2 FROM DUAL WHERE EXISTS  ( SELECT f1 FROM t1 )) AS tt 
STATUS:NOT_EQUAL
SYNC_POINT:after_join_optimize
Diff: EXPLAIN NORMAL vs <PERSON><PERSON><PERSON>
@@ -1,28 +0,0 @@
-EXPLAIN
-{
-  "query_block": {
-    "select_id": 1,
-    "message": "no matching row in const table",
-    "table": {
-      "materialized_from_subquery": {
-        "using_temporary_table": true,
-        "dependent": false,
-        "cacheable": true,
-        "query_block": {
-          "select_id": 2,
-          "message": "Impossible WHERE",
-          "optimized_away_subqueries": [
-            {
-              "dependent": false,
-              "cacheable": true,
-              "query_block": {
-                "select_id": 3,
-                "message": "no matching row in const table"
-              }
-            }
-          ]
-        }
-      }
-    }
-  }
-}
QUERY: SELECT STRAIGHT_JOIN * FROM t3  JOIN t1 ON t3.a=t1.a  JOIN t2 ON t3.a=t2.a JOIN t4 WHERE t4.a IN (t1.b, t2.b) 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -98,3 +98,3 @@
           "table_name": "t4",
-          "access_type": "ALL",
+          "access_type": "range",
           "possible_keys": [
@@ -102,5 +102,10 @@
           ],
+          "key": "PRIMARY",
+          "used_key_parts": [
+            "a"
+          ],
+          "key_length": "4",
           "rows_examined_per_scan": 7,
-          "rows_produced_per_join": 8,
-          "filtered": "28.57",
+          "rows_produced_per_join": 28,
+          "filtered": "100.00",
           "range_checked_for_each_record": "index map: 0x1",
@@ -108,5 +113,5 @@
             "read_cost": "0.50",
-            "eval_cost": "0.80",
+            "eval_cost": "2.80",
             "prefix_cost": "7.01",
-            "data_read_per_join": "128"
+            "data_read_per_join": "448"
           },
QUERY: SELECT STRAIGHT_JOIN  (SELECT SUM(t4.a) FROM t4 WHERE t4.a IN (t1.b, t2.b))  FROM t3, t1, t2 WHERE t3.a=t1.a AND t3.a=t2.a 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -116,2 +116,3 @@
             "filtered": "28.57",
+            "using_where": true,
             "using_index": true,
@@ -125,4 +126,3 @@
               "a"
-            ],
-            "attached_condition": "(`test`.`t4`.`a` in (`test`.`t1`.`b`,`test`.`t2`.`b`))"
+            ]
           }
QUERY: SELECT * FROM (SELECT max(b), a FROM t1 GROUP BY a) b 
STATUS: EQUAL
QUERY: DELETE FROM t3 WHERE (SELECT MAX(b) FROM t1 GROUP BY a HAVING a < 2) > 10000 
STATUS:NOT_EQUAL
SYNC_POINT:planned_single_delete
Diff: EXPLAIN NORMAL vs OTHER
@@ -1,48 +0,0 @@
-EXPLAIN
-{
-  "query_block": {
-    "select_id": 1,
-    "message": "Impossible WHERE",
-    "optimized_away_subqueries": [
-      {
-        "dependent": false,
-        "cacheable": true,
-        "query_block": {
-          "select_id": 2,
-          "cost_info": {
-            "query_cost": "3.00"
-          },
-          "grouping_operation": {
-            "using_filesort": false,
-            "table": {
-              "table_name": "t1",
-              "access_type": "range",
-              "possible_keys": [
-                "a"
-              ],
-              "key": "a",
-              "used_key_parts": [
-                "a"
-              ],
-              "key_length": "5",
-              "rows_examined_per_scan": 11,
-              "rows_produced_per_join": 11,
-              "filtered": "100.00",
-              "using_index_for_group_by": true,
-              "cost_info": {
-                "read_cost": "1.90",
-                "eval_cost": "1.10",
-                "prefix_cost": "3.00",
-                "data_read_per_join": "176"
-              },
-              "used_columns": [
-                "a",
-                "b"
-              ]
-            }
-          }
-        }
-      }
-    ]
-  }
-}
QUERY: SELECT * FROM t1 WHERE key1=1130 AND col1 IS NULL ORDER BY text1 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -24,9 +24,10 @@
         "rows_examined_per_scan": 4,
-        "rows_produced_per_join": 0,
-        "filtered": "20.00",
+        "rows_produced_per_join": 4,
+        "filtered": "100.00",
+        "using_where": true,
         "cost_info": {
           "read_cost": "0.50",
-          "eval_cost": "0.08",
+          "eval_cost": "0.40",
           "prefix_cost": "0.90",
-          "data_read_per_join": "38"
+          "data_read_per_join": "192"
         },
@@ -42,4 +43,3 @@
           "col4"
-        ],
-        "attached_condition": "(`test`.`t1`.`col1` is null)"
+        ]
       }
QUERY: SELECT t1.c2   FROM t2 STRAIGHT_JOIN t1 ON t1.c1 < t2.c1 
STATUS: EQUAL
QUERY: SELECT STRAIGHT_JOIN * FROM (t1 LEFT JOIN  (t2 LEFT JOIN (t3 LEFT OUTER JOIN t4 ON t3.c1 <= t4.c1_key) ON t2.c1 = t4.c3)  ON t1.c1 = t4.c2) RIGHT OUTER JOIN t5 ON t2.c2 <= t5.c1 WHERE t1.i1 = 1 
STATUS:NOT_EQUAL
SYNC_POINT:after_join_optimize
Diff: EXPLAIN NORMAL vs OTHER
@@ -34,2 +34,3 @@
           "filtered": "10.00",
+          "using_where": true,
           "using_join_buffer": "hash join",
@@ -45,4 +46,3 @@
             "c1"
-          ],
-          "attached_condition": "(`test`.`t1`.`i1` = 1)"
+          ]
         }
@@ -56,2 +56,3 @@
           "filtered": "33.33",
+          "using_where": true,
           "using_join_buffer": "hash join",
@@ -67,4 +68,3 @@
             "c2"
-          ],
-          "attached_condition": "(`test`.`t2`.`c2` <= `test`.`t5`.`c1`)"
+          ]
         }
QUERY: SELECT * FROM t1 LEFT JOIN t2 ON t2.f1 = t1.f1 WHERE t1.f1 = 4 AND t2.f1 IS NOT NULL AND t2.f2 IS NOT NULL GROUP BY t2.f1, t2.f2 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -47,4 +47,5 @@
             "rows_examined_per_scan": 1,
-            "rows_produced_per_join": 0,
-            "filtered": "50.00",
+            "rows_produced_per_join": 1,
+            "filtered": "100.00",
+            "using_where": true,
             "using_index": true,
@@ -52,5 +53,5 @@
               "read_cost": "0.25",
-              "eval_cost": "0.05",
+              "eval_cost": "0.10",
               "prefix_cost": "0.35",
-              "data_read_per_join": "8"
+              "data_read_per_join": "16"
             },
@@ -59,4 +60,3 @@
               "f2"
-            ],
-            "attached_condition": "(`test`.`t2`.`f2` is not null)"
+            ]
           }
QUERY: SELECT * FROM t3 AS a INNER JOIN t3 AS b USING (id) WHERE a.ref < b.ref 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -46,9 +46,10 @@
           "rows_examined_per_scan": 1,
-          "rows_produced_per_join": 3,
-          "filtered": "33.33",
+          "rows_produced_per_join": 10,
+          "filtered": "100.00",
+          "using_where": true,
           "cost_info": {
             "read_cost": "2.50",
-            "eval_cost": "0.33",
+            "eval_cost": "1.00",
             "prefix_cost": "4.76",
-            "data_read_per_join": "53"
+            "data_read_per_join": "160"
           },
@@ -57,4 +58,3 @@
             "ref"
-          ],
-          "attached_condition": "(`test`.`a`.`ref` < `test`.`b`.`ref`)"
+          ]
         }
QUERY: DELETE FROM a USING t3 AS a INNER JOIN t3 AS b USING (id) WHERE a.ref < b.ref 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -47,9 +47,10 @@
           "rows_examined_per_scan": 1,
-          "rows_produced_per_join": 3,
-          "filtered": "33.33",
+          "rows_produced_per_join": 10,
+          "filtered": "100.00",
+          "using_where": true,
           "cost_info": {
             "read_cost": "2.50",
-            "eval_cost": "0.33",
+            "eval_cost": "1.00",
             "prefix_cost": "4.76",
-            "data_read_per_join": "53"
+            "data_read_per_join": "160"
           },
@@ -58,4 +59,3 @@
             "ref"
-          ],
-          "attached_condition": "(`test`.`a`.`ref` < `test`.`b`.`ref`)"
+          ]
         }
QUERY: SELECT * from t2 left outer join t1  using (n) 
STATUS: EQUAL
QUERY: DELETE  t1,t2 from t2 left outer join t1  using (n) 
STATUS: EQUAL
QUERY: UPDATE t1 left join t2  on t1.a=t2.a set t1.b=2, t2.b=2 where t1.b=1 and t2.b=1 or t2.a is NULL 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -36,2 +36,3 @@
           "filtered": "75.00",
+          "using_where": true,
           "cost_info": {
@@ -45,4 +46,3 @@
             "b"
-          ],
-          "attached_condition": "(<if>(found_match(t2), (((`test`.`t2`.`b` = 1) and (`test`.`t1`.`b` = 1)) or (`test`.`t2`.`a` is null)), true) and <if>(is_not_null_compl(t2), (`test`.`t2`.`a` = `test`.`t1`.`a`), true))"
+          ]
         }
QUERY: UPDATE t1 LEFT JOIN t2 USING(id) SET s1 = 'b' 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -40,2 +40,3 @@
           "filtered": "100.00",
+          "using_where": true,
           "cost_info": {
@@ -49,4 +50,3 @@
             "s1"
-          ],
-          "attached_condition": "<if>(is_not_null_compl(t2), (`test`.`t2`.`id` = `test`.`t1`.`id`), true)"
+          ]
         }
QUERY: SELECT * from t1 where a = 1 and b is null order by a desc, b desc 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -28,2 +28,3 @@
         "filtered": "100.00",
+        "using_where": true,
         "using_index": true,
@@ -39,4 +40,3 @@
           "c"
-        ],
-        "attached_condition": "(`test`.`t1`.`b` is null)"
+        ]
       }
QUERY: SELECT * from t1 where b=1 or b is null order by a 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -26,3 +26,3 @@
         "filtered": "100.00",
-        "index_condition": "((`test`.`t1`.`b` = 1) or (`test`.`t1`.`b` is null))",
+        "index_condition": true,
         "cost_info": {
QUERY: SELECT 1 AS col FROM t1 WHERE a=2 AND (c=10 OR c IS NULL) ORDER BY c 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -25,9 +25,10 @@
         "rows_examined_per_scan": 1,
-        "rows_produced_per_join": 0,
-        "filtered": "75.00",
+        "rows_produced_per_join": 1,
+        "filtered": "100.00",
+        "using_where": true,
         "cost_info": {
           "read_cost": "0.25",
-          "eval_cost": "0.08",
+          "eval_cost": "0.10",
           "prefix_cost": "0.35",
-          "data_read_per_join": "12"
+          "data_read_per_join": "16"
         },
@@ -36,4 +37,3 @@
           "c"
-        ],
-        "attached_condition": "((`test`.`t1`.`c` = 10) or (`test`.`t1`.`c` is null))"
+        ]
       }
QUERY: SELECT 1 AS col FROM t1 WHERE a=2 AND (c=10 OR c IS NULL) ORDER BY c DESC 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -25,9 +25,10 @@
         "rows_examined_per_scan": 1,
-        "rows_produced_per_join": 0,
-        "filtered": "75.00",
+        "rows_produced_per_join": 1,
+        "filtered": "100.00",
+        "using_where": true,
         "cost_info": {
           "read_cost": "0.25",
-          "eval_cost": "0.08",
+          "eval_cost": "0.10",
           "prefix_cost": "0.35",
-          "data_read_per_join": "12"
+          "data_read_per_join": "16"
         },
@@ -36,4 +37,3 @@
           "c"
-        ],
-        "attached_condition": "((`test`.`t1`.`c` = 10) or (`test`.`t1`.`c` is null))"
+        ]
       }
QUERY: UPDATE t1 SET b = CONCAT(b, ', max(a) in t2: ', (SELECT MAX(a) FROM t2)) WHERE a = 5 
STATUS:NOT_EQUAL
SYNC_POINT:planned_single_update
Diff: EXPLAIN NORMAL vs OTHER
@@ -24,3 +24,3 @@
       "filtered": "100.00",
-      "attached_condition": "(`test`.`t1`.`a` = 5)"
+      "using_where": true
     },
@@ -32,3 +32,3 @@
           "select_id": 2,
-          "message": "Select tables optimized away"
+          "message": "Plan isn't ready yet"
         }
QUERY: UPDATE t1, t2 SET t1.b = CONCAT(t1.b, ', t2.b:', t2.b), t2.b = CONCAT(t2.b, ', t1.b:', t1.b) WHERE t2.b = t1.b and t2.a = 4 
STATUS: EQUAL
QUERY: SELECT s.oxid FROM t1 v, t1 s  WHERE s.oxrootid = 'd8c4177d09f8b11f5.52725521' AND v.oxrootid ='d8c4177d09f8b11f5.52725521' AND s.oxleft > v.oxleft AND s.oxleft < v.oxright 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -58,9 +58,10 @@
           "rows_examined_per_scan": 5,
-          "rows_produced_per_join": 4,
-          "filtered": "16.67",
+          "rows_produced_per_join": 25,
+          "filtered": "100.00",
+          "using_where": true,
           "cost_info": {
             "read_cost": "2.50",
-            "eval_cost": "0.42",
+            "eval_cost": "2.50",
             "prefix_cost": "6.00",
-            "data_read_per_join": "466"
+            "data_read_per_join": "2K"
           },
@@ -70,4 +71,3 @@
             "OXROOTID"
-          ],
-          "attached_condition": "((`test`.`s`.`OXLEFT` > `test`.`v`.`OXLEFT`) and (`test`.`s`.`OXLEFT` < `test`.`v`.`OXRIGHT`))"
+          ]
         }
QUERY: SELECT * from t1 where b = (select b from t2 where t1.a = t2.a) 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -13,2 +13,3 @@
       "filtered": "100.00",
+      "using_where": true,
       "cost_info": {
@@ -23,3 +24,2 @@
       ],
-      "attached_condition": "(`test`.`t1`.`b` = (/* select#2 */ select `test`.`t2`.`b` from `test`.`t2` where (`test`.`t1`.`a` = `test`.`t2`.`a`)))",
       "attached_subqueries": [
QUERY: DELETE from t1 where b = (select b from t2 where t1.a = t2.a) 
STATUS:NOT_EQUAL
SYNC_POINT:planned_single_delete
Diff: EXPLAIN NORMAL vs OTHER
@@ -10,40 +10,10 @@
       "filtered": "100.00",
-      "attached_condition": "(`test`.`t1`.`b` = (/* select#2 */ select `test`.`t2`.`b` from `test`.`t2` where (`test`.`t1`.`a` = `test`.`t2`.`a`)))",
+      "using_where": true,
       "attached_subqueries": [
         {
-          "dependent": true,
-          "cacheable": false,
+          "dependent": false,
+          "cacheable": true,
           "query_block": {
             "select_id": 2,
-            "cost_info": {
-              "query_cost": "0.35"
-            },
-            "table": {
-              "table_name": "t2",
-              "access_type": "eq_ref",
-              "possible_keys": [
-                "PRIMARY"
-              ],
-              "key": "PRIMARY",
-              "used_key_parts": [
-                "a"
-              ],
-              "key_length": "4",
-              "ref": [
-                "test.t1.a"
-              ],
-              "rows_examined_per_scan": 1,
-              "rows_produced_per_join": 1,
-              "filtered": "100.00",
-              "cost_info": {
-                "read_cost": "0.25",
-                "eval_cost": "0.10",
-                "prefix_cost": "0.35",
-                "data_read_per_join": "16"
-              },
-              "used_columns": [
-                "a",
-                "b"
-              ]
-            }
+            "message": "Plan isn't ready yet"
           }
QUERY: SELECT DISTINCT topic FROM t2 WHERE NOT EXISTS(SELECT * FROM t3 WHERE numeropost=topic) 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -57,2 +57,3 @@
             "filtered": "100.00",
+            "using_where": true,
             "not_exists": true,
@@ -67,4 +68,3 @@
               "numeropost"
-            ],
-            "attached_condition": "<if>(is_not_null_compl(t3), <if>(found_match(t3), false, true), true)"
+            ]
           }
QUERY: DELETE FROM t1 WHERE topic IN (SELECT DISTINCT topic FROM t2 WHERE NOT EXISTS(SELECT * FROM t3 WHERE numeropost=topic)) 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -14,2 +14,3 @@
       "filtered": "100.00",
+      "using_where": true,
       "cost_info": {
@@ -23,3 +24,2 @@
       ],
-      "attached_condition": "<in_optimizer>(`test`.`t1`.`topic`,<exists>(/* select#2 */ select `test`.`t2`.`topic` from `test`.`t2` anti join (`test`.`t3`) on((`test`.`t3`.`numeropost` = `test`.`t2`.`topic`)) where (<cache>(`test`.`t1`.`topic`) = `test`.`t2`.`topic`)))",
       "attached_subqueries": [
@@ -82,2 +82,3 @@
                   "filtered": "100.00",
+                  "using_where": true,
                   "not_exists": true,
@@ -92,4 +93,3 @@
                     "numeropost"
-                  ],
-                  "attached_condition": "<if>(is_not_null_compl(t3), <if>(found_match(t3), false, true), true)"
+                  ]
                 }
QUERY: SELECT cns.id, cns.max_anno_dep, cns.max_anno_dep = (SELECT s.anno_dep FROM t1 AS s WHERE s.id_cns = cns.id ORDER BY s.anno_dep DESC LIMIT 1) AS PIPPO FROM t2 AS cns 
STATUS: EQUAL
QUERY: SELECT (SELECT 1 FROM  t1 WHERE t1.a=t2.a ORDER BY t1.b LIMIT 1) AS d1 FROM t2 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -51,2 +51,3 @@
               "filtered": "100.00",
+              "using_where": true,
               "using_index": true,
@@ -61,4 +62,3 @@
                 "b"
-              ],
-              "attached_condition": "(`test`.`t1`.`a` = `test`.`t2`.`a`)"
+              ]
             }
QUERY: SELECT t1.pk FROM t1 WHERE t1.col_varchar_key < ALL ( SELECT * FROM v1 ) 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -11,3 +11,3 @@
           "select_id": 2,
-          "message": "Not optimized, outer query is empty",
+          "message": "Plan isn't ready yet",
           "table": {
@@ -20,3 +20,3 @@
                   "using_temporary_table": true,
-                  "message": "Not optimized, outer query is empty",
+                  "message": "Plan isn't ready yet",
                   "query_specifications": [
@@ -27,3 +27,3 @@
                         "select_id": 3,
-                        "message": "Not optimized, outer query is empty"
+                        "message": "Plan isn't ready yet"
                       }
@@ -35,3 +35,3 @@
                         "select_id": 4,
-                        "message": "Not optimized, outer query is empty"
+                        "message": "Plan isn't ready yet"
                       }
QUERY: SELECT t1.a, (SELECT 1 FROM t2 WHERE t2.b=t3.c AND t2.c=t1.a ORDER BY t2.d LIMIT 1) AS incorrect FROM t1, t3 WHERE t3.b=t1.a 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -25,2 +25,3 @@
           "filtered": "100.00",
+          "using_where": true,
           "using_index": true,
@@ -35,4 +36,3 @@
             "c"
-          ],
-          "attached_condition": "(`test`.`t3`.`b` is not null)"
+          ]
         }
QUERY: SELECT alias2.col_varchar_nokey FROM v1 AS alias1 RIGHT JOIN t1 AS alias2 ON 1 WHERE alias2.col_varchar_key IN ( SELECT sq2_alias1.col_varchar_nokey FROM v1 AS sq2_alias1 LEFT JOIN t1 AS sq2_alias2 ON (sq2_alias2.col_int_key = sq2_alias1.pk) WHERE sq2_alias1.pk != alias1.col_int_key AND sq2_alias1.col_varchar_key > alias1.col_varchar_key ) 
STATUS:NOT_EQUAL
SYNC_POINT:after_join_optimize
Diff: EXPLAIN NORMAL vs OTHER
@@ -43,2 +43,3 @@
             "filtered": "20.00",
+            "using_where": true,
             "using_join_buffer": "hash join",
@@ -53,4 +54,3 @@
               "col_varchar_nokey"
-            ],
-            "attached_condition": "(`test`.`alias2`.`col_varchar_key` = `test`.`t1`.`col_varchar_nokey`)"
+            ]
           }
@@ -66,3 +66,3 @@
             "rows_produced_per_join": 6,
-            "filtered": "26.66",
+            "filtered": "100.00",
             "range_checked_for_each_record": "index map: 0x4",
@@ -98,2 +98,3 @@
             "filtered": "100.00",
+            "using_where": true,
             "using_index": true,
@@ -107,4 +108,3 @@
               "col_int_key"
-            ],
-            "attached_condition": "<if>(is_not_null_compl(sq2_alias2), (`test`.`sq2_alias2`.`col_int_key` = `test`.`t1`.`pk`), true)"
+            ]
           }
QUERY: SELECT alias2.col_varchar_nokey FROM t1 AS alias2 LEFT JOIN v1 AS alias1 ON 1 WHERE alias2.col_varchar_key IN ( SELECT sq2_alias1.col_varchar_nokey FROM v1 AS sq2_alias1 LEFT JOIN t1 AS sq2_alias2 ON (sq2_alias2.col_int_key = sq2_alias1.pk) WHERE sq2_alias1.pk != alias1.col_int_key AND sq2_alias1.col_varchar_key > alias1.col_varchar_key ) 
STATUS:NOT_EQUAL
SYNC_POINT:after_join_optimize
Diff: EXPLAIN NORMAL vs OTHER
@@ -43,2 +43,3 @@
             "filtered": "20.00",
+            "using_where": true,
             "using_join_buffer": "hash join",
@@ -53,4 +54,3 @@
               "col_varchar_nokey"
-            ],
-            "attached_condition": "(`test`.`alias2`.`col_varchar_key` = `test`.`t1`.`col_varchar_nokey`)"
+            ]
           }
@@ -66,3 +66,3 @@
             "rows_produced_per_join": 6,
-            "filtered": "26.66",
+            "filtered": "100.00",
             "range_checked_for_each_record": "index map: 0x4",
@@ -98,2 +98,3 @@
             "filtered": "100.00",
+            "using_where": true,
             "using_index": true,
@@ -107,4 +108,3 @@
               "col_int_key"
-            ],
-            "attached_condition": "<if>(is_not_null_compl(sq2_alias2), (`test`.`sq2_alias2`.`col_int_key` = `test`.`t1`.`pk`), true)"
+            ]
           }
QUERY: SELECT d FROM (SELECT * FROM t2) AS a2 RIGHT JOIN (SELECT * FROM t1) AS a1 ON c = a WHERE d LIKE '_' ORDER BY d 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -38,2 +38,3 @@
             "filtered": "50.00",
+            "using_where": true,
             "cost_info": {
@@ -47,4 +48,3 @@
               "d"
-            ],
-            "attached_condition": "((`test`.`t2`.`c` = NULL) and (`test`.`t2`.`d` like '_'))"
+            ]
           }
QUERY: SELECT (SELECT rs.t2_id FROM t2 rs WHERE rs.t1_id= (SELECT lt.t1_id FROM t1 lt WHERE lt.t3_id=a.t3_id) ORDER BY b DESC LIMIT 1) from t3 AS a 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -51,2 +51,3 @@
               "filtered": "100.00",
+              "using_where": true,
               "using_index": true,
@@ -63,3 +64,2 @@
               ],
-              "attached_condition": "(`test`.`rs`.`t1_id` = (/* select#3 */ select NULL from `test`.`t1` `lt` where multiple equal('3', NULL)))",
               "attached_subqueries": [
QUERY: UPDATE t1 join t2 on (t1.a=t2.a) set t1.id=t2.id 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -15,2 +15,3 @@
           "filtered": "100.00",
+          "using_where": true,
           "cost_info": {
@@ -24,4 +25,3 @@
             "a"
-          ],
-          "attached_condition": "(`test`.`t2`.`a` is not null)"
+          ]
         }
QUERY: SELECT DISTINCT   alias2.col_int AS field1 , alias2.col_int AS field2 FROM  a1 AS alias1 LEFT  JOIN aa1 AS alias2 ON  alias1.pk =  alias2.pk WHERE ( alias1.col_int = 8 OR alias1 .pk <= alias1.col_int ) GROUP BY field1, field2  ORDER BY field1, field2 DESC
STATUS: EQUAL
QUERY: SELECT DISTINCT   alias1.pk AS field1 FROM  f1 AS alias1 LEFT  JOIN a2 AS alias2 ON  alias1.col_varchar_1024_latin1_key =  alias2 .col_varchar_10_utf8_key WHERE alias1.pk  IN (5, 5) GROUP BY field1 HAVING field1 <= 3
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -47,3 +47,3 @@
             "filtered": "100.00",
-            "index_condition": "(`test`.`alias1`.`pk` in (5,5))",
+            "index_condition": true,
             "cost_info": {
QUERY: SELECT alias1.pk AS field1 FROM  h2 AS alias1 LEFT  JOIN aa3 AS alias2 ON  alias1.pk =  alias2.col_int_key WHERE alias1.pk <> 9 GROUP BY field1 ORDER BY field1 LIMIT 1 OFFSET 3
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -27,2 +27,3 @@
               "filtered": "100.00",
+              "using_where": true,
               "using_index": true,
@@ -36,4 +37,3 @@
                 "pk"
-              ],
-              "attached_condition": "(`test`.`alias1`.`pk` <> 9)"
+              ]
             }
QUERY: SELECT    alias1.pk AS field1 FROM  e1 AS alias1 RIGHT  JOIN cc2 AS alias2 ON  alias1.col_int =  alias2.col_int WHERE alias1.col_int_key  IN (8) 
STATUS:NOT_EQUAL
SYNC_POINT:before_reset_query_plan
Diff: EXPLAIN NORMAL vs OTHER
@@ -44,4 +44,5 @@
           "rows_examined_per_scan": 2,
-          "rows_produced_per_join": 1,
-          "filtered": "50.00",
+          "rows_produced_per_join": 2,
+          "filtered": "100.00",
+          "using_where": true,
           "using_join_buffer": "hash join",
@@ -49,5 +50,5 @@
             "read_cost": "0.25",
-            "eval_cost": "0.10",
+            "eval_cost": "0.20",
             "prefix_cost": "0.80",
-            "data_read_per_join": "8"
+            "data_read_per_join": "16"
           },
@@ -55,4 +56,3 @@
             "col_int"
-          ],
-          "attached_condition": "(`test`.`alias2`.`col_int` = `test`.`alias1`.`col_int`)"
+          ]
         }
QUERY: SELECT DISTINCT alias2.pk AS field1, alias2.pk AS field2, alias2.pk AS field3 FROM t1 AS alias1 RIGHT JOIN t2 AS alias2  ON  alias1.col_varchar_key =  alias2.col_varchar_key WHERE alias1.col_int >alias1.col_int HAVING field3 = 7 ORDER BY field1, field2, field3 LIMIT 10 OFFSET 1
STATUS: EQUAL
