include/rpl/init_source_replica.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection master]
###################################################################################
#                               PREPARE EXECUTION
###################################################################################
include/rpl/stop_replica.inc
SHOW CREATE TABLE mysql.slave_relay_log_info;
Table	Create Table
slave_relay_log_info	CREATE TABLE `slave_relay_log_info` (
  `Number_of_lines` int unsigned NOT NULL COMMENT 'Number of lines in the file or rows in the table. Used to version table definitions.',
  `Relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the current relay log file.',
  `Relay_log_pos` bigint unsigned DEFAULT NULL COMMENT 'The relay log position of the last executed event.',
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the master binary log file from which the events in the relay log file were read.',
  `Master_log_pos` bigint unsigned DEFAULT NULL COMMENT 'The master log position of the last executed event.',
  `Sql_delay` int DEFAULT NULL COMMENT 'The number of seconds that the slave must lag behind the master.',
  `Number_of_workers` int unsigned DEFAULT NULL,
  `Id` int unsigned DEFAULT NULL COMMENT 'Internal Id that uniquely identifies this record.',
  `Channel_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'The channel on which the replica is connected to a source. Used in Multisource Replication',
  `Privilege_checks_username` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'Username part of PRIVILEGE_CHECKS_USER.',
  `Privilege_checks_hostname` varchar(255) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL COMMENT 'Hostname part of PRIVILEGE_CHECKS_USER.',
  `Require_row_format` tinyint(1) NOT NULL COMMENT 'Indicates whether the channel shall only accept row based events.',
  `Require_table_primary_key_check` enum('STREAM','ON','OFF','GENERATE') NOT NULL DEFAULT 'STREAM' COMMENT 'Indicates what is the channel policy regarding tables without primary keys on create and alter table queries',
  `Assign_gtids_to_anonymous_transactions_type` enum('OFF','LOCAL','UUID') NOT NULL DEFAULT 'OFF' COMMENT 'Indicates whether the channel will generate a new GTID for anonymous transactions. OFF means that anonymous transactions will remain anonymous. LOCAL means that anonymous transactions will be assigned a newly generated GTID based on server_uuid. UUID indicates that anonymous transactions will be assigned a newly generated GTID based on Assign_gtids_to_anonymous_transactions_value',
  `Assign_gtids_to_anonymous_transactions_value` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Indicates the UUID used while generating GTIDs for anonymous transactions',
  PRIMARY KEY (`Channel_name`)
) /*!50100 TABLESPACE `mysql` */ ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 ROW_FORMAT=DYNAMIC COMMENT='Relay Log Information'
SHOW CREATE TABLE mysql.slave_worker_info;
Table	Create Table
slave_worker_info	CREATE TABLE `slave_worker_info` (
  `Id` int unsigned NOT NULL,
  `Relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Relay_log_pos` bigint unsigned NOT NULL,
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Master_log_pos` bigint unsigned NOT NULL,
  `Checkpoint_relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Checkpoint_relay_log_pos` bigint unsigned NOT NULL,
  `Checkpoint_master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Checkpoint_master_log_pos` bigint unsigned NOT NULL,
  `Checkpoint_seqno` int unsigned NOT NULL,
  `Checkpoint_group_size` int unsigned NOT NULL,
  `Checkpoint_group_bitmap` blob NOT NULL,
  `Channel_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'The channel on which the replica is connected to a source. Used in Multisource Replication',
  PRIMARY KEY (`Channel_name`,`Id`)
) /*!50100 TABLESPACE `mysql` */ ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 ROW_FORMAT=DYNAMIC COMMENT='Worker Information'
ALTER TABLE mysql.slave_relay_log_info ENGINE= Innodb;
ALTER TABLE mysql.slave_worker_info ENGINE= Innodb;
SHOW CREATE TABLE mysql.slave_relay_log_info;
Table	Create Table
slave_relay_log_info	CREATE TABLE `slave_relay_log_info` (
  `Number_of_lines` int unsigned NOT NULL COMMENT 'Number of lines in the file or rows in the table. Used to version table definitions.',
  `Relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the current relay log file.',
  `Relay_log_pos` bigint unsigned DEFAULT NULL COMMENT 'The relay log position of the last executed event.',
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the master binary log file from which the events in the relay log file were read.',
  `Master_log_pos` bigint unsigned DEFAULT NULL COMMENT 'The master log position of the last executed event.',
  `Sql_delay` int DEFAULT NULL COMMENT 'The number of seconds that the slave must lag behind the master.',
  `Number_of_workers` int unsigned DEFAULT NULL,
  `Id` int unsigned DEFAULT NULL COMMENT 'Internal Id that uniquely identifies this record.',
  `Channel_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'The channel on which the replica is connected to a source. Used in Multisource Replication',
  `Privilege_checks_username` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'Username part of PRIVILEGE_CHECKS_USER.',
  `Privilege_checks_hostname` varchar(255) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL COMMENT 'Hostname part of PRIVILEGE_CHECKS_USER.',
  `Require_row_format` tinyint(1) NOT NULL COMMENT 'Indicates whether the channel shall only accept row based events.',
  `Require_table_primary_key_check` enum('STREAM','ON','OFF','GENERATE') NOT NULL DEFAULT 'STREAM' COMMENT 'Indicates what is the channel policy regarding tables without primary keys on create and alter table queries',
  `Assign_gtids_to_anonymous_transactions_type` enum('OFF','LOCAL','UUID') NOT NULL DEFAULT 'OFF' COMMENT 'Indicates whether the channel will generate a new GTID for anonymous transactions. OFF means that anonymous transactions will remain anonymous. LOCAL means that anonymous transactions will be assigned a newly generated GTID based on server_uuid. UUID indicates that anonymous transactions will be assigned a newly generated GTID based on Assign_gtids_to_anonymous_transactions_value',
  `Assign_gtids_to_anonymous_transactions_value` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Indicates the UUID used while generating GTIDs for anonymous transactions',
  PRIMARY KEY (`Channel_name`)
) /*!50100 TABLESPACE `mysql` */ ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 ROW_FORMAT=DYNAMIC COMMENT='Relay Log Information'
SHOW CREATE TABLE mysql.slave_worker_info;
Table	Create Table
slave_worker_info	CREATE TABLE `slave_worker_info` (
  `Id` int unsigned NOT NULL,
  `Relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Relay_log_pos` bigint unsigned NOT NULL,
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Master_log_pos` bigint unsigned NOT NULL,
  `Checkpoint_relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Checkpoint_relay_log_pos` bigint unsigned NOT NULL,
  `Checkpoint_master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Checkpoint_master_log_pos` bigint unsigned NOT NULL,
  `Checkpoint_seqno` int unsigned NOT NULL,
  `Checkpoint_group_size` int unsigned NOT NULL,
  `Checkpoint_group_bitmap` blob NOT NULL,
  `Channel_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'The channel on which the replica is connected to a source. Used in Multisource Replication',
  PRIMARY KEY (`Channel_name`,`Id`)
) /*!50100 TABLESPACE `mysql` */ ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 ROW_FORMAT=DYNAMIC COMMENT='Worker Information'
include/rpl/start_replica.inc
common/rpl/mixing_engines.inc [commands=configure]
CREATE TABLE nt_1 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_2 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_3 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_4 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_5 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE nt_6 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = MyISAM;
CREATE TABLE tt_1 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = Innodb;
CREATE TABLE tt_2 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = Innodb;
CREATE TABLE tt_3 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = Innodb;
CREATE TABLE tt_4 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = Innodb;
CREATE TABLE tt_5 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = Innodb;
CREATE TABLE tt_6 (trans_id INT, stmt_id INT, info VARCHAR(64), PRIMARY KEY(trans_id, stmt_id)) ENGINE = Innodb;
INSERT INTO nt_1(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_2(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_3(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_4(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_5(trans_id, stmt_id) VALUES(1,1);
INSERT INTO nt_6(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_1(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_2(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_3(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_4(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_5(trans_id, stmt_id) VALUES(1,1);
INSERT INTO tt_6(trans_id, stmt_id) VALUES(1,1);
CREATE PROCEDURE pc_i_tt_5_suc (IN p_trans_id INTEGER, IN p_stmt_id INTEGER)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
END|
CREATE PROCEDURE pc_i_nt_5_suc (IN p_trans_id INTEGER, IN p_stmt_id INTEGER)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
END|
CREATE FUNCTION fc_i_tt_5_suc (p_trans_id INTEGER, p_stmt_id INTEGER) RETURNS VARCHAR(64)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO tt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
RETURN "fc_i_tt_5_suc";
END|
CREATE FUNCTION fc_i_nt_5_suc (p_trans_id INTEGER, p_stmt_id INTEGER) RETURNS VARCHAR(64)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_5 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
INSERT INTO nt_5(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id + 1);
RETURN "fc_i_nt_5_suc";
END|
CREATE FUNCTION fc_i_nt_3_tt_3_suc (p_trans_id INTEGER, p_stmt_id INTEGER) RETURNS VARCHAR(64)
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_3 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_3(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
SELECT max(stmt_id) INTO in_stmt_id FROM tt_3 WHERE trans_id= p_trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, p_stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_3(trans_id, stmt_id) VALUES (p_trans_id, in_stmt_id);
RETURN "fc_i_nt_3_tt_3_suc";
END|
CREATE TRIGGER tr_i_tt_3_to_nt_3 AFTER INSERT ON tt_3 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_3 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_3(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO nt_3(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
CREATE TRIGGER tr_i_nt_4_to_tt_4 AFTER INSERT ON nt_4 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_4 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id), 1) INTO in_stmt_id;
INSERT INTO tt_4(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO tt_4(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
CREATE TRIGGER tr_i_tt_5_to_tt_6 AFTER INSERT ON tt_5 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM tt_6 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id, 1), 1) INTO in_stmt_id;
INSERT INTO tt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO tt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
CREATE TRIGGER tr_i_nt_5_to_nt_6 AFTER INSERT ON nt_5 FOR EACH ROW
BEGIN
DECLARE in_stmt_id INTEGER;
SELECT max(stmt_id) INTO in_stmt_id FROM nt_6 WHERE trans_id= NEW.trans_id;
SELECT COALESCE(greatest(in_stmt_id + 1, NEW.stmt_id), 1) INTO in_stmt_id;
INSERT INTO nt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id);
INSERT INTO nt_6(trans_id, stmt_id) VALUES (NEW.trans_id, in_stmt_id + 1);
END|
###################################################################################
#                         EXECUTE CASES CRASHING THE XID
###################################################################################



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_apply";;
FAILURE d,crash_after_apply and OUTCOME O2
common/rpl/mixing_engines.inc [commands=T]
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (7, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (7, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (7, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_apply";;
FAILURE d,crash_after_apply and OUTCOME O2
common/rpl/mixing_engines.inc [commands=T-trig]
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_5(trans_id, stmt_id) VALUES (8, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (8, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (8, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_apply";;
FAILURE d,crash_after_apply and OUTCOME O2
common/rpl/mixing_engines.inc [commands=T-func]
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_tt_5_suc (9, 1);
fc_i_tt_5_suc (9, 1)
fc_i_tt_5_suc
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(9,1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(9,1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_apply";;
FAILURE d,crash_after_apply and OUTCOME O2
common/rpl/mixing_engines.inc [commands=T-proc]
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
CALL pc_i_tt_5_suc (10, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',10),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',10),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',10),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',10),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_apply";;
FAILURE d,crash_after_apply and OUTCOME O2
common/rpl/mixing_engines.inc [commands=B T T-trig C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (11, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_5(trans_id, stmt_id) VALUES (11, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (11, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (11, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T T-trig C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (11, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (11, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T-trig C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_apply";;
FAILURE d,crash_after_apply and OUTCOME O2
common/rpl/mixing_engines.inc [commands=B T T-func C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (12, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_tt_5_suc (12, 4);
fc_i_tt_5_suc (12, 4)
fc_i_tt_5_suc
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (12, 2)
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(12,4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T T-func C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (12, 2)
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(12,4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T-func C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_apply";;
FAILURE d,crash_after_apply and OUTCOME O2
common/rpl/mixing_engines.inc [commands=B T T-proc C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (13, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
CALL pc_i_tt_5_suc (13, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (13, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',13),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',13),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T T-proc C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (13, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',13),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',13),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T-proc C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_apply";;
FAILURE d,crash_after_apply and OUTCOME O2
common/rpl/mixing_engines.inc [commands=B T-trig T C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_5(trans_id, stmt_id) VALUES (14, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (14, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (14, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (14, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T-trig T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (14, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (14, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T-trig T C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_apply";;
FAILURE d,crash_after_apply and OUTCOME O2
common/rpl/mixing_engines.inc [commands=B T-func T C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_tt_5_suc (15, 2);
fc_i_tt_5_suc (15, 2)
fc_i_tt_5_suc
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (15, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(15,2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (15, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T-func T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(15,2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (15, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T-func T C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_apply";;
FAILURE d,crash_after_apply and OUTCOME O2
common/rpl/mixing_engines.inc [commands=B T-proc T C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
CALL pc_i_tt_5_suc (16, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (16, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',16),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',16),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (16, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T-proc T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',16),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',16),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (16, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T-proc T C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_before_update_pos";;
FAILURE d,crash_before_update_pos and OUTCOME O1
common/rpl/mixing_engines.inc [commands=T]
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (17, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (17, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (17, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_before_update_pos";;
FAILURE d,crash_before_update_pos and OUTCOME O1
common/rpl/mixing_engines.inc [commands=T-trig]
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_5(trans_id, stmt_id) VALUES (18, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (18, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (18, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_before_update_pos";;
FAILURE d,crash_before_update_pos and OUTCOME O1
common/rpl/mixing_engines.inc [commands=T-func]
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_tt_5_suc (19, 1);
fc_i_tt_5_suc (19, 1)
fc_i_tt_5_suc
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(19,1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(19,1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_before_update_pos";;
FAILURE d,crash_before_update_pos and OUTCOME O1
common/rpl/mixing_engines.inc [commands=T-proc]
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
CALL pc_i_tt_5_suc (20, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',20),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',20),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',20),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',20),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_before_update_pos";;
FAILURE d,crash_before_update_pos and OUTCOME O1
common/rpl/mixing_engines.inc [commands=B T T-trig C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (21, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_5(trans_id, stmt_id) VALUES (21, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (21, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (21, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T T-trig C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (21, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (21, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T-trig C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_before_update_pos";;
FAILURE d,crash_before_update_pos and OUTCOME O1
common/rpl/mixing_engines.inc [commands=B T T-func C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (22, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_tt_5_suc (22, 4);
fc_i_tt_5_suc (22, 4)
fc_i_tt_5_suc
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (22, 2)
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(22,4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T T-func C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (22, 2)
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(22,4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T-func C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_before_update_pos";;
FAILURE d,crash_before_update_pos and OUTCOME O1
common/rpl/mixing_engines.inc [commands=B T T-proc C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (23, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
CALL pc_i_tt_5_suc (23, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (23, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',23),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',23),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T T-proc C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (23, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',23),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',23),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T-proc C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_before_update_pos";;
FAILURE d,crash_before_update_pos and OUTCOME O1
common/rpl/mixing_engines.inc [commands=B T-trig T C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_5(trans_id, stmt_id) VALUES (24, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (24, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (24, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (24, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T-trig T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (24, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (24, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T-trig T C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_before_update_pos";;
FAILURE d,crash_before_update_pos and OUTCOME O1
common/rpl/mixing_engines.inc [commands=B T-func T C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_tt_5_suc (25, 2);
fc_i_tt_5_suc (25, 2)
fc_i_tt_5_suc
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (25, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(25,2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (25, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T-func T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(25,2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (25, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T-func T C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_before_update_pos";;
FAILURE d,crash_before_update_pos and OUTCOME O1
common/rpl/mixing_engines.inc [commands=B T-proc T C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
CALL pc_i_tt_5_suc (26, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (26, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',26),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',26),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (26, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T-proc T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',26),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',26),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (26, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T-proc T C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_update_pos_before_apply";;
FAILURE d,crash_after_update_pos_before_apply and OUTCOME O1
common/rpl/mixing_engines.inc [commands=T]
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (27, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (27, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (27, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_update_pos_before_apply";;
FAILURE d,crash_after_update_pos_before_apply and OUTCOME O1
common/rpl/mixing_engines.inc [commands=T-trig]
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_5(trans_id, stmt_id) VALUES (28, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (28, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (28, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_update_pos_before_apply";;
FAILURE d,crash_after_update_pos_before_apply and OUTCOME O1
common/rpl/mixing_engines.inc [commands=T-func]
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_tt_5_suc (29, 1);
fc_i_tt_5_suc (29, 1)
fc_i_tt_5_suc
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(29,1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(29,1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_update_pos_before_apply";;
FAILURE d,crash_after_update_pos_before_apply and OUTCOME O1
common/rpl/mixing_engines.inc [commands=T-proc]
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
CALL pc_i_tt_5_suc (30, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',30),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',30),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',30),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',30),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_update_pos_before_apply";;
FAILURE d,crash_after_update_pos_before_apply and OUTCOME O1
common/rpl/mixing_engines.inc [commands=B T T-trig C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (31, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_5(trans_id, stmt_id) VALUES (31, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (31, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (31, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T T-trig C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (31, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (31, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T-trig C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_update_pos_before_apply";;
FAILURE d,crash_after_update_pos_before_apply and OUTCOME O1
common/rpl/mixing_engines.inc [commands=B T T-func C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (32, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_tt_5_suc (32, 4);
fc_i_tt_5_suc (32, 4)
fc_i_tt_5_suc
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (32, 2)
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(32,4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T T-func C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (32, 2)
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(32,4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T-func C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_update_pos_before_apply";;
FAILURE d,crash_after_update_pos_before_apply and OUTCOME O1
common/rpl/mixing_engines.inc [commands=B T T-proc C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (33, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
CALL pc_i_tt_5_suc (33, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (33, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',33),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',33),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T T-proc C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (33, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',33),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',33),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T-proc C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_update_pos_before_apply";;
FAILURE d,crash_after_update_pos_before_apply and OUTCOME O1
common/rpl/mixing_engines.inc [commands=B T-trig T C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_5(trans_id, stmt_id) VALUES (34, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (34, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (34, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (34, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T-trig T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (34, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (34, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T-trig T C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_update_pos_before_apply";;
FAILURE d,crash_after_update_pos_before_apply and OUTCOME O1
common/rpl/mixing_engines.inc [commands=B T-func T C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_tt_5_suc (35, 2);
fc_i_tt_5_suc (35, 2)
fc_i_tt_5_suc
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (35, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(35,2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (35, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T-func T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(35,2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (35, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T-func T C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_update_pos_before_apply";;
FAILURE d,crash_after_update_pos_before_apply and OUTCOME O1
common/rpl/mixing_engines.inc [commands=B T-proc T C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
CALL pc_i_tt_5_suc (36, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (36, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',36),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',36),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (36, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T-proc T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',36),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',36),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (36, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T-proc T C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_before_update_pos";;
FAILURE d,crash_after_commit_before_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=T]
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (37, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (37, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (37, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_before_update_pos";;
FAILURE d,crash_after_commit_before_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=T-trig]
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_5(trans_id, stmt_id) VALUES (38, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (38, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (38, 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_before_update_pos";;
FAILURE d,crash_after_commit_before_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=T-func]
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_tt_5_suc (39, 1);
fc_i_tt_5_suc (39, 1)
fc_i_tt_5_suc
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(39,1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(39,1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_before_update_pos";;
FAILURE d,crash_after_commit_before_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=T-proc]
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
CALL pc_i_tt_5_suc (40, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',40),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',40),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',40),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',40),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_before_update_pos";;
FAILURE d,crash_after_commit_before_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=B T T-trig C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (41, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_5(trans_id, stmt_id) VALUES (41, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (41, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (41, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T T-trig C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (41, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (41, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T-trig C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_before_update_pos";;
FAILURE d,crash_after_commit_before_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=B T T-func C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (42, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_tt_5_suc (42, 4);
fc_i_tt_5_suc (42, 4)
fc_i_tt_5_suc
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (42, 2)
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(42,4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T T-func C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (42, 2)
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(42,4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T-func C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_before_update_pos";;
FAILURE d,crash_after_commit_before_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=B T T-proc C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (43, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
CALL pc_i_tt_5_suc (43, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (43, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',43),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',43),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T T-proc C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (43, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',43),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',43),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T T-proc C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_before_update_pos";;
FAILURE d,crash_after_commit_before_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=B T-trig T C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_5(trans_id, stmt_id) VALUES (44, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (44, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (44, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (44, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T-trig T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES (44, 2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (44, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T-trig T C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_before_update_pos";;
FAILURE d,crash_after_commit_before_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=B T-func T C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_tt_5_suc (45, 2);
fc_i_tt_5_suc (45, 2)
fc_i_tt_5_suc
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (45, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(45,2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (45, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T-func T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_tt_5_suc`(45,2)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (45, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T-func T C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_before_update_pos";;
FAILURE d,crash_after_commit_before_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=B T-proc T C]
-b-b-b-b-b-b-b-b-b-b-b- >> B << -b-b-b-b-b-b-b-b-b-b-b-
BEGIN;
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> B << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T-proc << -b-b-b-b-b-b-b-b-b-b-b-
CALL pc_i_tt_5_suc (46, 2);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T-proc << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> T << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO tt_1(trans_id, stmt_id) VALUES (46, 4);
include/rpl/deprecated/show_binlog_events.inc
-e-e-e-e-e-e-e-e-e-e-e- >> T << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> C << -b-b-b-b-b-b-b-b-b-b-b-
COMMIT;
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',46),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',46),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (46, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> C << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> B T-proc T C << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',46),  NAME_CONST('in_stmt_id',1))
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_5(trans_id, stmt_id) VALUES ( NAME_CONST('p_trans_id',46),  NAME_CONST('in_stmt_id',1) + 1)
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO tt_1(trans_id, stmt_id) VALUES (46, 4)
master-bin.000001	#	Xid	#	#	COMMIT /* XID */
-e-e-e-e-e-e-e-e-e-e-e- >> B T-proc T C << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc
###################################################################################
#                      EXECUTE CASES CRASHING THE BEGIN/COMMIT
###################################################################################



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_and_update_pos";;
FAILURE d,crash_after_commit_and_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=N]
-b-b-b-b-b-b-b-b-b-b-b- >> N << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO nt_1(trans_id, stmt_id) VALUES (47, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO nt_1(trans_id, stmt_id) VALUES (47, 1)
master-bin.000001	#	Query	#	#	COMMIT
-e-e-e-e-e-e-e-e-e-e-e- >> N << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> N << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO nt_1(trans_id, stmt_id) VALUES (47, 1)
master-bin.000001	#	Query	#	#	COMMIT
-e-e-e-e-e-e-e-e-e-e-e- >> N << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_and_update_pos";;
FAILURE d,crash_after_commit_and_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=N-trig]
-b-b-b-b-b-b-b-b-b-b-b- >> N-trig << -b-b-b-b-b-b-b-b-b-b-b-
INSERT INTO nt_5(trans_id, stmt_id) VALUES (48, 1);
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO nt_5(trans_id, stmt_id) VALUES (48, 1)
master-bin.000001	#	Query	#	#	COMMIT
-e-e-e-e-e-e-e-e-e-e-e- >> N-trig << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> N-trig << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; INSERT INTO nt_5(trans_id, stmt_id) VALUES (48, 1)
master-bin.000001	#	Query	#	#	COMMIT
-e-e-e-e-e-e-e-e-e-e-e- >> N-trig << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc



STOP REPLICA SQL_THREAD;
include/rpl/wait_for_applier_to_stop.inc
SET GLOBAL debug="d,crash_after_commit_and_update_pos";;
FAILURE d,crash_after_commit_and_update_pos and OUTCOME O2
common/rpl/mixing_engines.inc [commands=N-func]
-b-b-b-b-b-b-b-b-b-b-b- >> N-func << -b-b-b-b-b-b-b-b-b-b-b-
SELECT fc_i_nt_5_suc (49, 1);
fc_i_nt_5_suc (49, 1)
fc_i_nt_5_suc
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_nt_5_suc`(49,1)
master-bin.000001	#	Query	#	#	COMMIT
-e-e-e-e-e-e-e-e-e-e-e- >> N-func << -e-e-e-e-e-e-e-e-e-e-e-
-b-b-b-b-b-b-b-b-b-b-b- >> N-func << -b-b-b-b-b-b-b-b-b-b-b-
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	#	#	BEGIN
master-bin.000001	#	Query	#	#	use `test`; SELECT `test`.`fc_i_nt_5_suc`(49,1)
master-bin.000001	#	Query	#	#	COMMIT
-e-e-e-e-e-e-e-e-e-e-e- >> N-func << -e-e-e-e-e-e-e-e-e-e-e-

include/rpl/reconnect.inc
START REPLICA;
include/rpl/wait_for_replica_to_start.inc
###################################################################################
#                               CHECK CONSISTENCY
###################################################################################
include/rpl/sync_to_replica.inc
###################################################################################
#                                        CLEAN
###################################################################################
common/rpl/mixing_engines.inc [commands=clean]
DROP TABLE tt_1;
DROP TABLE tt_2;
DROP TABLE tt_3;
DROP TABLE tt_4;
DROP TABLE tt_5;
DROP TABLE tt_6;
DROP TABLE nt_1;
DROP TABLE nt_2;
DROP TABLE nt_3;
DROP TABLE nt_4;
DROP TABLE nt_5;
DROP TABLE nt_6;
DROP PROCEDURE pc_i_tt_5_suc;
DROP PROCEDURE pc_i_nt_5_suc;
DROP FUNCTION fc_i_tt_5_suc;
DROP FUNCTION fc_i_nt_5_suc;
DROP FUNCTION fc_i_nt_3_tt_3_suc;
include/rpl/deinit.inc
