# Copyright (c) 2011, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

[atrt]
basedir = CHOOSE_dir
baseport = 14000
clusters = .4node
fix-nodeid=1
mt = 2

[ndb_mgmd]

[mysqld]
innodb
loose-skip-bdb
skip-grant-tables
socket=mysql.sock

ndbcluster=1
ndb-force-send=1
ndb-use-exact-count=0
ndb-extra-logging=1
ndb-autoincrement-prefetch-sz=256
engine-condition-pushdown=1
ndb-cluster-connection-pool=4

key_buffer = 256M
max_allowed_packet = 16M
sort_buffer_size = 512K
read_buffer_size = 256K
read_rnd_buffer_size = 512K
myisam_sort_buffer_size = 8M
max-connections=200
thread-cache-size=128

table_open_cache=1024
table_definition_cache=256

loose-ndb-join-pushdown=1
loose-ndb-index-stat-enable=1

[client]
protocol=tcp

[cluster_config.4node]
ndb_mgmd = CHOOSE_host1
ndbd = CHOOSE_host5,CHOOSE_host6,CHOOSE_host7,CHOOSE_host8
ndbapi= CHOOSE_host2,CHOOSE_host3,CHOOSE_host4,,,,,,,,
mysqld = CHOOSE_host1
Checksum=1

NoOfReplicas = 2
DataMemory = 1750M
BackupMemory = 64M
MaxNoOfConcurrentScans = 100
MaxNoOfSavedMessages= 5
NoOfFragmentLogFiles = 8
FragmentLogFileSize = 64M
ODirect=1
MaxNoOfExecutionThreads=8

SharedGlobalMemory=256M
DiskPageBufferMemory=256M
FileSystemPath=/data0/autotest
FileSystemPathDataFiles=/data1/autotest
FileSystemPathUndoFiles=/data2/autotest
InitialLogfileGroup=undo_buffer_size=64M;undofile01.dat:256M;undofile02.dat:256M
InitialTablespace=datafile01.dat:256M;datafile02.dat:256M
