#-----------------------------------------------------------------------
# Setup
# Install component_connection_control
INSTALL COMPONENT 'file://component_connection_control';
CREATE USER no_privs@localhost IDENTIFIED BY 'abcd';
#-----------------------------------------------------------------------
# Case 1 : component_connection_control.failed_connections_threshold
SHOW GRANTS;
Grants for root@localhost
GRANT <ALL_STATIC_PRIVILEGES> ON *.* TO `root`@`localhost` WITH GRANT OPTION
GRANT <ALL_DYNAMIC_PRIVILEGES> ON *.* TO `root`@`localhost` WITH GRANT OPTION
GRANT PROXY ON ``@`` TO `root`@`localhost` WITH GRANT OPTION
SET @saved_value = @@global.component_connection_control.failed_connections_threshold;
SELECT @saved_value;
@saved_value
3
SET @@global.component_connection_control.failed_connections_threshold = @saved_value;
# 1.1 : Setting component_connection_control.failed_connections_threshold to valid
#       value
SET @@global.component_connection_control.failed_connections_threshold = 20;
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
20
SET @@global.component_connection_control.failed_connections_threshold = 2000;
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
2000
SET @@global.component_connection_control.failed_connections_threshold = 2147483647;
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
2147483647
SET @@global.component_connection_control.failed_connections_threshold = DEFAULT;
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
3
# 1.2 : Setting component_connection_control.failed_connections_threshold to
#       invalid value
SET @@global.component_connection_control.failed_connections_threshold = NULL;
ERROR 42000: Incorrect argument type to variable 'component_connection_control.failed_connections_threshold'
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
3
SET @@global.component_connection_control.failed_connections_threshold = `SELECT * FROM mysql.user`;
ERROR 42000: Incorrect argument type to variable 'component_connection_control.failed_connections_threshold'
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
3
SET @@global.component_connection_control.failed_connections_threshold = -20;
ERROR 42000: Variable 'component_connection_control.failed_connections_threshold' can't be set to the value of '-20'
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
3
SET @@global.component_connection_control.failed_connections_threshold = 9223372036854775808;
ERROR 42000: Variable 'component_connection_control.failed_connections_threshold' can't be set to the value of '9223372036854775808'
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
3
SET @@global.component_connection_control.failed_connections_threshold = -9223372036854775808;
ERROR 42000: Variable 'component_connection_control.failed_connections_threshold' can't be set to the value of '-9223372036854775808'
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
3
# Switch to conn_no_privs
# 1.3 : Use no_privs@localhost to set
#       component_connection_control.failed_connections_threshold to valid value
SET @@global.component_connection_control.failed_connections_threshold = 2147483647;
ERROR 42000: Access denied; you need (at least one of) the SUPER or SYSTEM_VARIABLES_ADMIN privilege(s) for this operation
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
3
SET @@global.component_connection_control.failed_connections_threshold = DEFAULT;
ERROR 42000: Access denied; you need (at least one of) the SUPER or SYSTEM_VARIABLES_ADMIN privilege(s) for this operation
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
3
# 1.4 : Use no_privs@localhost to set
#       component_connection_control.failed_connections_threshold to invalid value
SET @@global.component_connection_control.failed_connections_threshold = NULL;
ERROR 42000: Access denied; you need (at least one of) the SUPER or SYSTEM_VARIABLES_ADMIN privilege(s) for this operation
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
3
SET @@global.component_connection_control.failed_connections_threshold = 9223372036854775808;
ERROR 42000: Access denied; you need (at least one of) the SUPER or SYSTEM_VARIABLES_ADMIN privilege(s) for this operation
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
3
SET @@global.component_connection_control.failed_connections_threshold = @saved_value;
SELECT @@global.component_connection_control.failed_connections_threshold;
@@global.component_connection_control.failed_connections_threshold
3
#-----------------------------------------------------------------------
# Case 2 : component_connection_control.min_connection_delay
SET @saved_value= @@global.component_connection_control.min_connection_delay;
SELECT @saved_value;
@saved_value
1000
# 2.1 : Setting component_connection_control.min_connection_delay to valid
#       value
SET @@global.component_connection_control.min_connection_delay = 20000;
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
20000
SET @@global.component_connection_control.min_connection_delay = 2000;
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
2000
SET @@global.component_connection_control.min_connection_delay = 2147483647;
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
2147483647
SET @@global.component_connection_control.min_connection_delay = DEFAULT;
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
# 2.2 : Setting component_connection_control.min_connection_delay to
#       invalid value
SET @@global.component_connection_control.min_connection_delay = NULL;
ERROR 42000: Incorrect argument type to variable 'component_connection_control.min_connection_delay'
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
SET @@global.component_connection_control.min_connection_delay = `SELECT * FROM mysql.user`;
ERROR 42000: Incorrect argument type to variable 'component_connection_control.min_connection_delay'
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
SET @@global.component_connection_control.min_connection_delay = -20;
ERROR 42000: Variable 'component_connection_control.min_connection_delay' can't be set to the value of '-20'
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
SET @@global.component_connection_control.min_connection_delay = 9223372036854775808;
ERROR 42000: Variable 'component_connection_control.min_connection_delay' can't be set to the value of '9223372036854775808'
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
SET @@global.component_connection_control.min_connection_delay = -9223372036854775808;
ERROR 42000: Variable 'component_connection_control.min_connection_delay' can't be set to the value of '-9223372036854775808'
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
SET@@global.component_connection_control.min_connection_delay = 20;
ERROR 42000: Variable 'component_connection_control.min_connection_delay' can't be set to the value of '20'
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
# Switch to conn_no_privs
# 2.3 : Use no_privs@localhost to set
#       component_connection_control.min_connection_delay to valid value
SET @@global.component_connection_control.min_connection_delay = 2147483647;
ERROR 42000: Access denied; you need (at least one of) the SUPER or SYSTEM_VARIABLES_ADMIN privilege(s) for this operation
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
SET @@global.component_connection_control.min_connection_delay = DEFAULT;
ERROR 42000: Access denied; you need (at least one of) the SUPER or SYSTEM_VARIABLES_ADMIN privilege(s) for this operation
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
# 2.4 : Use no_privs@localhost to set
#       component_connection_control.min_connection_delay to invalid value
SET @@global.component_connection_control.min_connection_delay = NULL;
ERROR 42000: Access denied; you need (at least one of) the SUPER or SYSTEM_VARIABLES_ADMIN privilege(s) for this operation
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
SET @@global.component_connection_control.min_connection_delay = 9223372036854775808;
ERROR 42000: Access denied; you need (at least one of) the SUPER or SYSTEM_VARIABLES_ADMIN privilege(s) for this operation
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
# Switch to default connection
# 2.5 : Setting component_connection_control.min_connection_delay to a value
#       greater than component_connection_control.max_connection_delay
SET @saved_max_delay= @@global.component_connection_control.max_connection_delay;
SET @@global.component_connection_control.max_connection_delay= 10000;
SET @@global.component_connection_control.min_connection_delay= 11000;
ERROR 42000: Variable 'component_connection_control.min_connection_delay' can't be set to the value of '11000'
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
SET @@global.component_connection_control.max_connection_delay= @saved_max_delay;
SET @@global.component_connection_control.min_connection_delay = @saved_value;
SELECT @@global.component_connection_control.min_connection_delay;
@@global.component_connection_control.min_connection_delay
1000
#-----------------------------------------------------------------------
# Case 3 : component_connection_control.max_connection_delay
SET @saved_value= @@global.component_connection_control.max_connection_delay;
SELECT @saved_value;
@saved_value
2147483647
# 3.1 : Setting component_connection_control.max_connection_delay to valid
#       value
SET @@global.component_connection_control.max_connection_delay = 20000;
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
20000
SET @@global.component_connection_control.max_connection_delay = 2000;
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2000
SET @@global.component_connection_control.max_connection_delay = 2147483647;
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
SET @@global.component_connection_control.max_connection_delay = DEFAULT;
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
# 3.2 : Setting component_connection_control.max_connection_delay to
#       invalid value
SET @@global.component_connection_control.max_connection_delay = NULL;
ERROR 42000: Incorrect argument type to variable 'component_connection_control.max_connection_delay'
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
SET @@global.component_connection_control.max_connection_delay = `SELECT * FROM mysql.user`;
ERROR 42000: Incorrect argument type to variable 'component_connection_control.max_connection_delay'
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
SET @@global.component_connection_control.max_connection_delay = -20;
ERROR 42000: Variable 'component_connection_control.max_connection_delay' can't be set to the value of '-20'
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
SET @@global.component_connection_control.max_connection_delay = 9223372036854775808;
ERROR 42000: Variable 'component_connection_control.max_connection_delay' can't be set to the value of '9223372036854775808'
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
SET @@global.component_connection_control.max_connection_delay = -9223372036854775808;
ERROR 42000: Variable 'component_connection_control.max_connection_delay' can't be set to the value of '-9223372036854775808'
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
SET @@global.component_connection_control.max_connection_delay = 20;
ERROR 42000: Variable 'component_connection_control.max_connection_delay' can't be set to the value of '20'
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
# Switch to conn_no_privs
# 3.3 : Use no_privs@localhost to set
#       component_connection_control.max_connection_delay to valid value
SET @@global.component_connection_control.max_connection_delay = 2147483647;
ERROR 42000: Access denied; you need (at least one of) the SUPER or SYSTEM_VARIABLES_ADMIN privilege(s) for this operation
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
SET @@global.component_connection_control.max_connection_delay = DEFAULT;
ERROR 42000: Access denied; you need (at least one of) the SUPER or SYSTEM_VARIABLES_ADMIN privilege(s) for this operation
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
# 3.4 : Use no_privs@localhost to set
#       component_connection_control.max_connection_delay to invalid value
SET @@global.component_connection_control.max_connection_delay = NULL;
ERROR 42000: Access denied; you need (at least one of) the SUPER or SYSTEM_VARIABLES_ADMIN privilege(s) for this operation
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
SET @@global.component_connection_control.max_connection_delay = 9223372036854775808;
ERROR 42000: Access denied; you need (at least one of) the SUPER or SYSTEM_VARIABLES_ADMIN privilege(s) for this operation
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
# Switch to default connection
# 3.5 : Setting component_connection_control.min_connection_delay to a value
#       greater than component_connection_control.max_connection_delay
SET @saved_min_delay= @@global.component_connection_control.min_connection_delay;
SET @@global.component_connection_control.min_connection_delay= 11000;
SET @@global.component_connection_control.max_connection_delay= 10000;
ERROR 42000: Variable 'component_connection_control.max_connection_delay' can't be set to the value of '10000'
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
SET @@global.component_connection_control.min_connection_delay= @saved_min_delay;
SET @@global.component_connection_control.max_connection_delay = @saved_value;
SELECT @@global.component_connection_control.max_connection_delay;
@@global.component_connection_control.max_connection_delay
2147483647
#-----------------------------------------------------------------------
# Cleanup
DROP USER no_privs@localhost;
# Uninstall connection_control plugin
UNINSTALL COMPONENT 'file://component_connection_control';
#-----------------------------------------------------------------------
