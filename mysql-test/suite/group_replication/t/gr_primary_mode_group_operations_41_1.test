##############################################################################
#
# All actions shall allow the DBA to check its progress.
#
# Test:
#   0. This test starts in multi primary mode with 2 servers.
#
#   Phase 1: Changes to single primary mode
#    1.1 Block the action on validation with a debug point
#        Change to single primary mode appoint server 2 as the new primary
#        Check the action is stuck in the check pre conditions stage
#    1.2 Pause transactions on the new primary and execute a transaction on server1
#        Unblock the debug point
#        Check the action is stuck in the Primary Election stage
#    1.3 Block the stop process of the action using a debug point
#        Resume transactions on the new primary
#        Check it is stuck in the wait for other member to finish stage
#    1.4 Unblock the debug point
#        Check the action terminates and no stages appear
#
#   Phase 2: Changes to another primary
#    2.1 Block the action on validation with a debug point
#        Change the current primary to server 1
#        Check the action is stuck in the check pre conditions stage
#    2.2 Start some transaction on the old primary (server2)
#        Unblock the debug point
#        Check the action is stuck waiting for old transactions to finish
#        Commit a transaction and see there is reported progress
#    2.3 Lock a table t1 and execute a transaction on server2.
#        This will block the read mode during election
#        Commit the last transaction blocking phase 2.2 (table 2)
#        Check the action is stuck in the Primary Election stage
#    2.4 Block the stop process of the action using a debug point
#        Unlock the table on the old primary (server2)
#        Check it is stuck in the wait for other member to finish stage
#    2.5 Unblock the debug point
#        Check the action terminates and no stages appear
#
#   3.Cleanup
##############################################################################

--source include/big_test.inc
--source include/have_debug_sync.inc
--source include/have_group_replication_plugin.inc
--source include/group_replication.inc

--let $server1_uuid= query_get_value(SELECT @@SERVER_UUID, @@SERVER_UUID, 1)

CREATE TABLE test.t1 (a INT PRIMARY KEY);
--source include/rpl/sync.inc

--let $rpl_connection_name= server2
--source include/connection.inc

--let $server2_uuid= query_get_value(SELECT @@SERVER_UUID, @@SERVER_UUID, 1)

--echo
--echo # 1.1 Block the action on validation with a debug point
--echo #     Change to single primary mode appoint server 2 as the new primary
--echo #     Check the action is stuck in the check pre conditions stage

--let $rpl_connection_name= server1
--source include/connection.inc

SET @@GLOBAL.DEBUG= '+d,group_replication_block_primary_action_validation';

--replace_result $server2_uuid MEMBER2_UUID
--send_eval SELECT group_replication_switch_to_single_primary_mode("$server2_uuid")

--let $rpl_connection_name= server_1
--source include/connection.inc

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%"
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The stage should be "Single-primary Switch: checking group pre-conditions"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Single-primary Switch: checking group pre-conditions"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The estimated work is 1
--let $assert_cond= "$work_estimated" = "1"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The completed work is 0
--let $assert_cond= "$work_completed" = "0"
--source include/assert.inc

--let $rpl_connection_name= server_2
--source include/connection.inc

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%" and work_completed=1
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The stage should be "Single-primary Switch: checking group pre-conditions"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Single-primary Switch: checking group pre-conditions"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The estimated work is 1
--let $assert_cond= "$work_estimated" = "1"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The completed work is 1
--let $assert_cond= "$work_completed" = "1"
--source include/assert.inc

--echo
--echo # 1.2 Pause transactions on the new primary and execute a transaction on server1
--echo #     Unblock the debug point
--echo #     Check the action is stuck in the Primary Election stage

--let $rpl_connection_name= server_2
--source include/connection.inc

SET @@GLOBAL.DEBUG= '+d,group_replication_wait_on_observer_trans';

--let $rpl_connection_name= server_1
--source include/connection.inc

INSERT INTO t1 VALUES (1);

SET DEBUG_SYNC= "now SIGNAL signal.primary_action_continue";
SET @@GLOBAL.DEBUG= '-d,group_replication_block_primary_action_validation';

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch: executing Primary%" and work_completed=1
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch:%";`
--let $assert_text= The stage should be "Single-primary Switch: executing Primary election"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Single-primary Switch: executing Primary election"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch:%";`
--let $assert_text= The estimated work is 2
--let $assert_cond= "$work_estimated" = "2"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch:%";`
--let $assert_text= The completed work is 1
--let $assert_cond= "$work_completed" = "1"
--source include/assert.inc

--let $rpl_connection_name= server2
--source include/connection.inc

SET DEBUG_SYNC= "now WAIT_FOR signal.group_replication_wait_on_observer_trans_waiting";

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch: executing Primary%" and work_completed=1
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch:%";`
--let $assert_text= The stage should be "Single-primary Switch: executing Primary election"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Single-primary Switch: executing Primary election"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch:%";`
--let $assert_text= The estimated work is 2
--let $assert_cond= "$work_estimated" = "2"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch:%";`
--let $assert_text= The completed work is 1
--let $assert_cond= "$work_completed" = "1"
--source include/assert.inc

--echo
--echo # 1.3 Block the stop process of the action using a debug point
--echo #     Resume transactions on the new primary
--echo #     Check it is stuck in the wait for other member to finish stage

--let $rpl_connection_name= server_2
--source include/connection.inc

SET @@GLOBAL.DEBUG= '-d,group_replication_wait_on_observer_trans';
SET @@GLOBAL.DEBUG= '+d,group_replication_block_group_action_stop';
SET DEBUG_SYNC= 'now SIGNAL signal.group_replication_wait_on_observer_trans_continue';

--source include/rpl/sync.inc

--let $rpl_connection_name= server_1
--source include/connection.inc

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch: waiting for%" and work_completed=1
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch:%";`
--let $assert_text= The stage should be "Single-primary Switch: waiting for operation to complete on all members"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Single-primary Switch: waiting for operation to complete on all members"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch:%";`
--let $assert_text= The estimated work is 2
--let $assert_cond= "$work_estimated" = "2"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch:%";`
--let $assert_text= The completed work is 1
--let $assert_cond= "$work_completed" = "1"
--source include/assert.inc

--let $rpl_connection_name= server2
--source include/connection.inc

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch: waiting for%" and work_completed=1
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch:%";`
--let $assert_text= The stage should be "Single-primary Switch: waiting for operation to complete on all members"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Single-primary Switch: waiting for operation to complete on all members"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch:%";`
--let $assert_text= The estimated work is 2
--let $assert_cond= "$work_estimated" = "2"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%Single-primary Switch:%";`
--let $assert_text= The completed work is 1
--let $assert_cond= "$work_completed" = "1"
--source include/assert.inc

SET DEBUG_SYNC= "now SIGNAL signal.action_stop_continue";
SET @@GLOBAL.DEBUG= '-d,group_replication_block_group_action_stop';

--echo
--echo # 1.4 Unblock the debug point
--echo #     Check the action terminates and no stages appear

--let $rpl_connection_name= server1
--source include/connection.inc

--replace_result $server2_uuid MEMBER2_UUID
--reap

#Stages sometimes linger for a brief time after the action terminated
--let $wait_condition=SELECT COUNT(*)=0 FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%"
--source include/wait_condition.inc

--let $stages_present= `SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= There is no stage present
--let $assert_cond= "$stages_present" = "0"
--source include/assert.inc

--let $rpl_connection_name= server2
--source include/connection.inc

#Stages sometimes linger for a brief time after the action terminated
--let $wait_condition=SELECT COUNT(*)=0 FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%"
--source include/wait_condition.inc

--let $stages_present= `SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= There is no stage present
--let $assert_cond= "$stages_present" = "0"
--source include/assert.inc

--echo
--echo # 2.1 Block the action on validation with a debug point
--echo #     Change the current primary to server 1
--echo #     Check the action is stuck in the check pre conditions stage

#Block also 2 so it wont go for primary election
--let $rpl_connection_name= server2
--source include/connection.inc

SET DEBUG_SYNC= 'RESET';
SET @@GLOBAL.DEBUG= '+d,group_replication_block_primary_action_validation';

--let $rpl_connection_name= server1
--source include/connection.inc

SET DEBUG_SYNC= 'RESET';
SET @@GLOBAL.DEBUG= '+d,group_replication_block_primary_action_validation';

--replace_result $server1_uuid MEMBER1_UUID
--send_eval SELECT group_replication_set_as_primary("$server1_uuid")

--let $rpl_connection_name= server_1
--source include/connection.inc

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%"
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The stage should be "Primary switch: checking current primary pre-conditions"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Primary switch: checking current primary pre-conditions"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The estimated work is 1
--let $assert_cond= "$work_estimated" = "1"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The completed work is 0
--let $assert_cond= "$work_completed" = "0"
--source include/assert.inc

--let $rpl_connection_name= server2
--source include/connection.inc

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%"
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The stage should be "Primary switch: checking current primary pre-conditions"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Primary switch: checking current primary pre-conditions"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The estimated work is 1
--let $assert_cond= "$work_estimated" = "1"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The completed work is 0
--let $assert_cond= "$work_completed" = "0"
--source include/assert.inc

--echo
--echo # 2.2 Start some transaction on the old primary (server2)
--echo #     Unblock the debug point
--echo #     Check the action is stuck waiting for old transactions to finish
--echo #     Commit a transaction and see there is reported progress

--let $rpl_connection_name= server_1
--source include/connection.inc

SET DEBUG_SYNC= "now SIGNAL signal.primary_action_continue";
SET @@GLOBAL.DEBUG= '-d,group_replication_block_primary_action_validation';

--let $rpl_connection_name= server2
--source include/connection.inc

CREATE TABLE test.t2 (a INT PRIMARY KEY);

BEGIN;
INSERT INTO t1 VALUES (2);

--let $rpl_connection_name= server_2_1
--source include/connection.inc

BEGIN;
INSERT INTO t2 VALUES (2);

--let $rpl_connection_name= server_2
--source include/connection.inc

SET DEBUG_SYNC= "now SIGNAL signal.primary_action_continue";
SET @@GLOBAL.DEBUG= '-d,group_replication_block_primary_action_validation';

# The SELECT from this wait_condition can be accounted thence the work_estimated=3
--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%" and (work_estimated=2 or work_estimated=3)
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The stage should be "Primary Switch: waiting for pending transactions to finish"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Primary Switch: waiting for pending transactions to finish"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The estimated work is 2
--let $assert_cond= "$work_estimated" = "2"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The completed work is 0
--let $assert_cond= "$work_completed" = "0"
--source include/assert.inc

--let $rpl_connection_name= server_1
--source include/connection.inc

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch: waiting on another%"
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The stage should be "Primary Switch: waiting on another member step completion"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Primary Switch: waiting on another member step completion"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The estimated work is 1
--let $assert_cond= "$work_estimated" = "1"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The completed work is 0
--let $assert_cond= "$work_completed" = "0"
--source include/assert.inc

--let $rpl_connection_name= server2
--source include/connection.inc

COMMIT;

--let $rpl_connection_name= server_2
--source include/connection.inc

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%" and work_completed=1
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The stage should be "Primary Switch: waiting for pending transactions to finish"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Primary Switch: waiting for pending transactions to finish"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The estimated work is 2
--let $assert_cond= "$work_estimated" = "2"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The completed work is 1
--let $assert_cond= "$work_completed" = "1"
--source include/assert.inc

--echo
--echo # 2.3 Lock a table t1 and execute a transaction on server2.
--echo #     This will block the read mode during election
--echo #     Commit the last transaction blocking phase 2.2 (table 2)
--echo #     Check the action is stuck in the Primary Election stage

--let $rpl_connection_name= server_2
--source include/connection.inc

LOCK TABLE t1 WRITE;

--let $rpl_connection_name= slave
--source include/connection.inc

--send INSERT INTO t1 VALUES (3)

--let $rpl_connection_name= server_2_1
--source include/connection.inc

COMMIT;

--let $rpl_connection_name= server_1
--source include/connection.inc

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch: executing Primary%"
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The stage should be "Primary Switch: executing Primary election"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Primary Switch: executing Primary election"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The estimated work is 2
--let $assert_cond= "$work_estimated" = "2"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The completed work is 0
--let $assert_cond= "$work_completed" = "0"
--source include/assert.inc

--let $rpl_connection_name= server2
--source include/connection.inc

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch: executing Primary%"
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The stage should be "Primary Switch: executing Primary election"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Primary Switch: executing Primary election"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The estimated work is 2
--let $assert_cond= "$work_estimated" = "2"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch:%";`
--let $assert_text= The completed work is 0
--let $assert_cond= "$work_completed" = "0"
--source include/assert.inc

--echo
--echo # 2.4 Block the stop process of the action using a debug point
--echo #     Unlock the table on the old primary (server2)
--echo #     Check it is stuck in the wait for other member to finish stage

--let $rpl_connection_name= server_1
--source include/connection.inc

SET @@GLOBAL.DEBUG= '+d,group_replication_block_group_action_stop';

--let $rpl_connection_name= server_2
--source include/connection.inc

UNLOCK TABLES;

--let $rpl_connection_name= slave
--source include/connection.inc

--reap

--let $rpl_connection_name= server_1
--source include/connection.inc

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch: waiting for%" and work_completed=1
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The stage should be "Primary Switch: waiting for operation to complete on all members"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Primary Switch: waiting for operation to complete on all members"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The estimated work is 2
--let $assert_cond= "$work_estimated" = "2"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The completed work is 1
--let $assert_cond= "$work_completed" = "1"
--source include/assert.inc

--let $rpl_connection_name= server2
--source include/connection.inc

--let $wait_condition=SELECT COUNT(*)=1 FROM performance_schema.events_stages_current WHERE event_name LIKE "%Primary Switch: waiting for%" and work_completed=1
--source include/wait_condition.inc

--let $stage_name= `SELECT event_name FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The stage should be "Primary Switch: waiting for operation to complete on all members"
--let $assert_cond= "$stage_name" = "stage/group_rpl/Primary Switch: waiting for operation to complete on all members"
--source include/assert.inc

--let $work_estimated= `SELECT work_estimated FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The estimated work is 2
--let $assert_cond= "$work_estimated" = "2"
--source include/assert.inc

--let $work_completed= `SELECT work_completed FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= The completed work is 1
--let $assert_cond= "$work_completed" = "1"
--source include/assert.inc

--let $rpl_connection_name= server_1
--source include/connection.inc

SET DEBUG_SYNC= "now SIGNAL signal.action_stop_continue";
SET @@GLOBAL.DEBUG= '-d,group_replication_block_group_action_stop';

--echo
--echo # 2.5 Unblock the debug point
--echo #     Check the action terminates and no stages appear

--let $rpl_connection_name= server1
--source include/connection.inc

--replace_result $server1_uuid MEMBER1_UUID
--reap

#Stages sometimes linger for a brief time after the action terminated
--let $wait_condition=SELECT COUNT(*)=0 FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%"
--source include/wait_condition.inc

--let $stages_present= `SELECT COUNT(*) FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= There is no stage present
--let $assert_cond= "$stages_present" = "0"
--source include/assert.inc

--let $rpl_connection_name= server2
--source include/connection.inc

#Stages sometimes linger for a brief time after the action terminated
--let $wait_condition=SELECT COUNT(*)=0 FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%"
--source include/wait_condition.inc

--let $stages_present= `SELECT COUNT(*) FROM performance_schema.events_stages_current WHERE event_name LIKE "%stage/group_rpl%" AND event_name NOT LIKE "%stage/group_rpl/Group Replication%";`
--let $assert_text= There is no stage present
--let $assert_cond= "$stages_present" = "0"
--source include/assert.inc

--echo
--echo # 3. Clean up

--let $rpl_connection_name= server1
--source include/connection.inc

DROP TABLE t1;
DROP TABLE t2;

--let $rpl_group_replication_single_primary_mode=1
--let $rpl_group_replication_reset_persistent_vars=1
--source include/group_replication_end.inc
