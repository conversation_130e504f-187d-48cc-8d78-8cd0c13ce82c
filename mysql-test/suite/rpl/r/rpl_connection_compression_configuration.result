include/rpl/init_source_replica.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection master]
[connection slave]
##### TEST FOR NEW CHANNEL #####
# case 1: ALGORITHM = uncompressed, LEVEL not specified
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='uncompressed', SOURCE_ZSTD_COMPRESSION_LEVEL= 0 FOR CHANNEL 'C1';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C1';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
uncompressed	3
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C1';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
uncompressed	3
# case 2: ALGORITHM = uncompressed, LEVEL = 2
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='uncompressed', SOURCE_ZSTD_COMPRESSION_LEVEL= 2 FOR CHANNEL 'C2';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C2';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
uncompressed	2
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C2';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
uncompressed	2
# case 3: ALGORITHM not specified, LEVEL = 2
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_ZSTD_COMPRESSION_LEVEL= 2 FOR CHANNEL 'C3';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C3';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
uncompressed	2
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C3';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
uncompressed	2
# case 4: ALGORITHM = ZSTD, LEVEL = not specified
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zstd' FOR CHANNEL 'C4';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# algorithm and level should be set to zstd and 0
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C4';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zstd	3
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C4';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zstd	3
# case 5: ALGORITHM = ZSTD, LEVEL = 9
# report error as part of change replication source
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zstd', SOURCE_ZSTD_COMPRESSION_LEVEL= 9 FOR CHANNEL 'C5';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C5';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zstd	9
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C5';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zstd	9
# case 6: ALGORITHM = ZSTD, LEVEL = 24
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zstd', SOURCE_ZSTD_COMPRESSION_LEVEL= 24 FOR CHANNEL 'C6';
ERROR HY000: Invalid SOURCE_ZSTD_COMPRESSION_LEVEL 24 for channel 'c6'.
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C6';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C6';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zstd	3
# case 7: ALGORITHM = invalid value, LEVEL = 2
# report error as part of change replication source
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='xyz', SOURCE_ZSTD_COMPRESSION_LEVEL= 2 FOR CHANNEL 'C7';
ERROR HY000: Invalid SOURCE_COMPRESSION_ALGORITHMS 'xyz' for channel 'c7'.
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C7';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C7';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
uncompressed	3
# case 8: ALGORITHM = invalid value, LEVEL = 29
# report error as part of change replication source
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='xyz', SOURCE_ZSTD_COMPRESSION_LEVEL= 29 FOR CHANNEL 'C8';
ERROR HY000: Invalid SOURCE_COMPRESSION_ALGORITHMS 'xyz' for channel 'c8'.
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C8';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C8';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
uncompressed	3
# case 9: ALGORITHM = invalid value, LEVEL not specified
# report error as part of change replication source
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='xyz' FOR CHANNEL 'C9';
ERROR HY000: Invalid SOURCE_COMPRESSION_ALGORITHMS 'xyz' for channel 'c9'.
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C9';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C9';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
uncompressed	3
# case 10: ALGORITHM = invalid value 
# report error as part of change replication source
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS= 'zlib,zstd,abc' FOR CHANNEL 'C10';
ERROR HY000: Invalid SOURCE_COMPRESSION_ALGORITHMS 'abc' for channel 'c10'.
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C10';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C10';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
uncompressed	3
# case 11: ALGORITHM specified to more than 3 values
# report error as part of change replication source
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS= 'zlib,zstd,abc,123' FOR CHANNEL 'C11';
ERROR HY000: Specified compression algorithm list 'zlib,zstd,abc,123' exceeds total count of 3 for channel 'c11'.
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C11';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C11';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
uncompressed	3
# case 12: ALGORITHM specified to upper case, LEVEL not specified
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root',SOURCE_COMPRESSION_ALGORITHMS= 'ZLIB,ZSTD' FOR CHANNEL 'C12';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# algorithm and level should be set to ZLIB,ZSTD/0
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C12';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
ZLIB,ZSTD	3
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C12';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
ZLIB,ZSTD	3
# case 13: ALGORITHM = 'zlib,uncompressed', LEVEL not specified
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zlib,uncompressed' FOR CHANNEL 'C13';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# algorithm and level should be set to 'zlib,uncompressed'/0
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C13';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zlib,uncompressed	3
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C13';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zlib,uncompressed	3
# case 14: ALGORITHM = 'zlib,uncompressed', LEVEL = 4
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zlib,uncompressed', SOURCE_ZSTD_COMPRESSION_LEVEL=4 FOR CHANNEL 'C14';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# algorithm and level should be set to 'zlib,uncompressed'/4
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C14';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zlib,uncompressed	4
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C14';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zlib,uncompressed	4
# case 15: ALGORITHM = 'zlib,uncompressed,zstd', LEVEL = 0
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zlib,uncompressed,zstd', SOURCE_ZSTD_COMPRESSION_LEVEL=0 FOR CHANNEL 'C15';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# algorithm and level should be set to 'zlib,uncompressed,zstd'/0
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C15';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zlib,uncompressed,zstd	3
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C15';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zlib,uncompressed,zstd	3
# case 16: ALGORITHM = 'uncompressed,zstd', LEVEL = 14
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='uncompressed,zstd', SOURCE_ZSTD_COMPRESSION_LEVEL=14 FOR CHANNEL 'C16';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# algorithm and level should be set to 'uncompressed,zstd'/14
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C16';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
uncompressed,zstd	14
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C16';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
uncompressed,zstd	14
# case 17: ALGORITHM = 'zlib,uncompressed,zlib', LEVEL = 14
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zlib,uncompressed,zlib', SOURCE_ZSTD_COMPRESSION_LEVEL=14 FOR CHANNEL 'C17';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C17';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zlib,uncompressed,zlib	14
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C17';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zlib,uncompressed,zlib	14
# case 18: ALGORITHM = 'uncompressed,uncompressed,uncompressed', LEVEL = 14
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='uncompressed,uncompressed,uncompressed', SOURCE_ZSTD_COMPRESSION_LEVEL=14 FOR CHANNEL 'C17';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# algorithm and level should be set to  'uncompressed,uncompressed,uncompressed'/14
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C17';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
uncompressed,uncompressed,uncompressed	14
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C17';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
uncompressed,uncompressed,uncompressed	14
# case 19: ALGORITHM = 'zstd,zlib,uncompressed', LEVEL = 2
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zstd,zlib,uncompressed', SOURCE_ZSTD_COMPRESSION_LEVEL=2 FOR CHANNEL 'C17';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# algorithm and level should be set to 'zstd,zlib,uncompressed'/2
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C17';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zstd,zlib,uncompressed	2
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C17';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zstd,zlib,uncompressed	2
##### TEST FOR EXISTING CHANNEL #####
# create a new channel
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zstd', SOURCE_ZSTD_COMPRESSION_LEVEL= 2 FOR CHANNEL 'C21';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C21';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zstd	2
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C21';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zstd	2
# case 1: ALGORITHM not specified, LEVEL = 7
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_ZSTD_COMPRESSION_LEVEL = 7 FOR CHANNEL 'C21';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# algorithm and level should be set to 'zstd'/7
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C21';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zstd	7
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C21';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zstd	7
# case 2: ALGORITHM not specified, LEVEL 37
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_ZSTD_COMPRESSION_LEVEL = 37 FOR CHANNEL 'C21';
ERROR HY000: Invalid SOURCE_ZSTD_COMPRESSION_LEVEL 37 for channel 'c21'.
# level should be same as previous which is 7
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C21';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zstd	7
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C21';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zstd	7
# case 3: ALGORITHM not specified, LEVEL is not specified
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root' FOR CHANNEL 'C21';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# algorithm and level should not change
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C21';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zstd	7
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C21';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zstd	7
# case 4: ALGORITHM changed from zstd to zlib, LEVEL = 6
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zlib', SOURCE_ZSTD_COMPRESSION_LEVEL = 6 FOR CHANNEL 'C21';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# level should be 6 and algorithm should be zlib
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C21';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zlib	6
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C21';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zlib	6
# case 5: ALGORITHM changed from zlib to zstd/uncompressed, LEVEL = 27
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zlib,uncompressed', SOURCE_ZSTD_COMPRESSION_LEVEL = 27 FOR CHANNEL 'C21';
ERROR HY000: Invalid SOURCE_ZSTD_COMPRESSION_LEVEL 27 for channel 'c21'.
# level should be 6 and algorithm should be zlib
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C21';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zlib	6
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C21';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zlib,uncompressed	6
# case 6: ALGORITHM changed from zstd to zlib, LEVEL not specified
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zlib' FOR CHANNEL 'C21';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# level should not change
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C21';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zlib	6
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C21';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zlib	6
# case 7: ALGORITHM = invalid values, LEVEL = 6
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='xyz', SOURCE_ZSTD_COMPRESSION_LEVEL = 6 FOR CHANNEL 'C21';
ERROR HY000: Invalid SOURCE_COMPRESSION_ALGORITHMS 'xyz' for channel 'c21'.
# case 8: ALGORITHM = invalid values, LEVEL = 26
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='xyz', SOURCE_ZSTD_COMPRESSION_LEVEL = 26 FOR CHANNEL 'C21';
ERROR HY000: Invalid SOURCE_COMPRESSION_ALGORITHMS 'xyz' for channel 'c21'.
# case 9: ALGORITHM = invalid values, LEVEL is not specified
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='xyz' FOR CHANNEL 'C21';
ERROR HY000: Invalid SOURCE_COMPRESSION_ALGORITHMS 'xyz' for channel 'c21'.
# case 10: ALGORITHM changed from zlib to 'zstd,zlib,uncompressed', LEVEL is not specified
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zstd,zlib,uncompressed' FOR CHANNEL 'C21';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# level should be 0 and algorithm should be 'zstd,zlib,uncompressed' and warning is reported
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C21';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zstd,zlib,uncompressed	6
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C21';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zstd,zlib,uncompressed	6
# case 11: ALGORITHM changed from 'zstd,zlib,uncompressed' to 'zstd', LEVEL = 3
CHANGE REPLICATION SOURCE TO SOURCE_HOST='127.0.0.1', SOURCE_PORT=SOURCE_PORT, SOURCE_USER='root', SOURCE_COMPRESSION_ALGORITHMS='zstd', SOURCE_ZSTD_COMPRESSION_LEVEL = 3 FOR CHANNEL 'C21';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
# level should be 3 and algorithm should be 'zstd'
SELECT MASTER_COMPRESSION_ALGORITHM, MASTER_ZSTD_COMPRESSION_LEVEL FROM mysql.slave_master_info WHERE CHANNEL_NAME = 'C21';
MASTER_COMPRESSION_ALGORITHM	MASTER_ZSTD_COMPRESSION_LEVEL
zstd	3
SELECT COMPRESSION_ALGORITHM, ZSTD_COMPRESSION_LEVEL FROM performance_schema.replication_connection_configuration where CHANNEL_NAME = 'C21';
COMPRESSION_ALGORITHM	ZSTD_COMPRESSION_LEVEL
zstd	3

# cleanup

include/rpl/stop_replica.inc
Warnings:
Note	3084	Replication thread(s) for channel 'c1' are already stopped.
Note	3084	Replication thread(s) for channel 'c10' are already stopped.
Note	3084	Replication thread(s) for channel 'c11' are already stopped.
Note	3084	Replication thread(s) for channel 'c12' are already stopped.
Note	3084	Replication thread(s) for channel 'c13' are already stopped.
Note	3084	Replication thread(s) for channel 'c14' are already stopped.
Note	3084	Replication thread(s) for channel 'c15' are already stopped.
Note	3084	Replication thread(s) for channel 'c16' are already stopped.
Note	3084	Replication thread(s) for channel 'c17' are already stopped.
Note	3084	Replication thread(s) for channel 'c2' are already stopped.
Note	3084	Replication thread(s) for channel 'c21' are already stopped.
Note	3084	Replication thread(s) for channel 'c3' are already stopped.
Note	3084	Replication thread(s) for channel 'c4' are already stopped.
Note	3084	Replication thread(s) for channel 'c5' are already stopped.
Note	3084	Replication thread(s) for channel 'c6' are already stopped.
Note	3084	Replication thread(s) for channel 'c7' are already stopped.
Note	3084	Replication thread(s) for channel 'c8' are already stopped.
Note	3084	Replication thread(s) for channel 'c9' are already stopped.
RESET REPLICA ALL FOR CHANNEL 'c1';
RESET REPLICA ALL FOR CHANNEL 'c10';
RESET REPLICA ALL FOR CHANNEL 'c11';
RESET REPLICA ALL FOR CHANNEL 'c12';
RESET REPLICA ALL FOR CHANNEL 'c13';
RESET REPLICA ALL FOR CHANNEL 'c14';
RESET REPLICA ALL FOR CHANNEL 'c15';
RESET REPLICA ALL FOR CHANNEL 'c16';
RESET REPLICA ALL FOR CHANNEL 'c17';
RESET REPLICA ALL FOR CHANNEL 'c2';
RESET REPLICA ALL FOR CHANNEL 'c21';
RESET REPLICA ALL FOR CHANNEL 'c3';
RESET REPLICA ALL FOR CHANNEL 'c4';
RESET REPLICA ALL FOR CHANNEL 'c5';
RESET REPLICA ALL FOR CHANNEL 'c6';
RESET REPLICA ALL FOR CHANNEL 'c7';
RESET REPLICA ALL FOR CHANNEL 'c8';
RESET REPLICA ALL FOR CHANNEL 'c9';
include/rpl/start_replica.inc
include/rpl/deinit.inc
