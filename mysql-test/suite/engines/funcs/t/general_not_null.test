--disable_warnings
DROP TABLE IF EXISTS t2;
--enable_warnings
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT MAX(c1) AS value FROM t2;
SELECT MAX(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT MAX(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT MAX(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT MAX(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT MAX(c1) AS value FROM t2;
SELECT MAX(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT MAX(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT MAX(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT MAX(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT MAX(c1) AS value FROM t2;
SELECT MAX(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT MAX(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT MAX(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT MAX(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT MAX(c1) AS value FROM t2;
SELECT MAX(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT MAX(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT MAX(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT MAX(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT MAX(c1) AS value FROM t2;
SELECT MAX(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT MAX(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT MAX(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT MAX(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT MAX(c1) AS value FROM t2;
SELECT MAX(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT MAX(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT MAX(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT MAX(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT MIN(c1) AS value FROM t2;
SELECT MIN(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT MIN(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT MIN(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT MIN(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT MIN(c1) AS value FROM t2;
SELECT MIN(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT MIN(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT MIN(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT MIN(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT MIN(c1) AS value FROM t2;
SELECT MIN(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT MIN(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT MIN(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT MIN(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT MIN(c1) AS value FROM t2;
SELECT MIN(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT MIN(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT MIN(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT MIN(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT MIN(c1) AS value FROM t2;
SELECT MIN(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT MIN(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT MIN(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT MIN(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT MIN(c1) AS value FROM t2;
SELECT MIN(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT MIN(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT MIN(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT MIN(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT AVG(c1) AS value FROM t2;
SELECT AVG(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT AVG(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT AVG(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT AVG(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT AVG(c1) AS value FROM t2;
SELECT AVG(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT AVG(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT AVG(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT AVG(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT AVG(c1) AS value FROM t2;
SELECT AVG(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT AVG(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT AVG(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT AVG(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT AVG(c1) AS value FROM t2;
SELECT AVG(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT AVG(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT AVG(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT AVG(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT AVG(c1) AS value FROM t2;
SELECT AVG(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT AVG(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT AVG(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT AVG(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT AVG(c1) AS value FROM t2;
SELECT AVG(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT AVG(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT AVG(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT AVG(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT SUM(c1) AS value FROM t2;
SELECT SUM(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT SUM(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT SUM(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT SUM(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT SUM(c1) AS value FROM t2;
SELECT SUM(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT SUM(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT SUM(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT SUM(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT SUM(c1) AS value FROM t2;
SELECT SUM(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT SUM(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT SUM(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT SUM(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT SUM(c1) AS value FROM t2;
SELECT SUM(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT SUM(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT SUM(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT SUM(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT SUM(c1) AS value FROM t2;
SELECT SUM(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT SUM(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT SUM(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT SUM(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT SUM(c1) AS value FROM t2;
SELECT SUM(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT SUM(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT SUM(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT SUM(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 TINYINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT COUNT(c1) AS value FROM t2;
SELECT COUNT(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT COUNT(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT COUNT(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT COUNT(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 SMALLINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT COUNT(c1) AS value FROM t2;
SELECT COUNT(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT COUNT(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT COUNT(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT COUNT(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 MEDIUMINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT COUNT(c1) AS value FROM t2;
SELECT COUNT(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT COUNT(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT COUNT(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT COUNT(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 INT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT COUNT(c1) AS value FROM t2;
SELECT COUNT(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT COUNT(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT COUNT(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT COUNT(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 INTEGER NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT COUNT(c1) AS value FROM t2;
SELECT COUNT(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT COUNT(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT COUNT(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT COUNT(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;
CREATE TABLE t2(c1 BIGINT NOT NULL);
INSERT INTO t2 (c1) VALUES(0);
INSERT INTO t2 (c1) VALUES(1);
INSERT INTO t2 (c1) VALUES(16);
INSERT INTO t2 (c1) VALUES(-4);
INSERT INTO t2 (c1) VALUES(-9);
SELECT COUNT(c1) AS value FROM t2;
SELECT COUNT(c1) AS postive_value FROM t2 WHERE c1 > 0;
SELECT COUNT(c1) AS negative_value FROM t2 WHERE c1 < 0;
SELECT COUNT(c1) AS zero_value FROM t2 WHERE c1 = 0;
SELECT COUNT(c1) AS no_results FROM t2 WHERE c1 = 2;
DROP TABLE t2;

