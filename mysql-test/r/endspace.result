drop table if exists t1;
set names utf8mb4 collate utf8mb4_unicode_ci;
select 'a' = 'a', 'a' = 'a ', 'a ' = 'a';
'a' = 'a'	'a' = 'a '	'a ' = 'a'
1	1	1
select 'a\0' = 'a', 'a\0' < 'a', 'a\0' > 'a';
'a\0' = 'a'	'a\0' < 'a'	'a\0' > 'a'
1	0	0
select 'a' = 'a\0', 'a' < 'a\0', 'a' > 'a\0';
'a' = 'a\0'	'a' < 'a\0'	'a' > 'a\0'
1	0	0
select 'a\0' = 'a ', 'a\0' < 'a ', 'a\0' > 'a ';
'a\0' = 'a '	'a\0' < 'a '	'a\0' > 'a '
1	0	0
select 'a ' = 'a\0', 'a ' < 'a\0', 'a ' > 'a\0';
'a ' = 'a\0'	'a ' < 'a\0'	'a ' > 'a\0'
1	0	0
select 'a  a' > 'a', 'a  \0' < 'a';
'a  a' > 'a'	'a  \0' < 'a'
1	0
select binary 'a  a' > 'a', binary 'a  \0' > 'a', binary 'a\0' > 'a';
binary 'a  a' > 'a'	binary 'a  \0' > 'a'	binary 'a\0' > 'a'
1	1	1
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
set names default;
create table t1 (text1 varchar(32) not NULL, KEY key1 (text1)) charset latin1;
insert into t1 values ('teststring'), ('nothing'), ('teststring\t');
analyze table t1;
check table t1;
Table	Op	Msg_type	Msg_text
test.t1	check	status	OK
select * from t1 ignore key (key1) where text1='teststring' or 
text1 like 'teststring_%' ORDER BY text1;
text1
teststring	
teststring
select * from t1 where text1='teststring' or text1 like 'teststring_%';
text1
teststring
teststring	
select * from t1 where text1='teststring' or text1 > 'teststring\t';
text1
teststring
select * from t1 order by text1;
text1
nothing
teststring	
teststring
explain select * from t1 order by text1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	key1	34	NULL	3	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`text1` AS `text1` from `test`.`t1` order by `test`.`t1`.`text1`
alter table t1 modify text1 char(32) binary not null;
Warnings:
Warning	1287	'BINARY as attribute of a type' is deprecated and will be removed in a future release. Please use a CHARACTER SET clause with _bin collation instead
check table t1;
Table	Op	Msg_type	Msg_text
test.t1	check	status	OK
select * from t1 ignore key (key1) where text1='teststring' or 
text1 like 'teststring_%' ORDER BY text1;
text1
teststring	
teststring
select concat('|', text1, '|') as c from t1 where text1='teststring' or text1 like 'teststring_%' order by c;
c
|teststring	|
|teststring|
select concat('|', text1, '|') from t1 where text1='teststring' or text1 > 'teststring\t';
concat('|', text1, '|')
|teststring|
select text1, length(text1) from t1 order by text1;
text1	length(text1)
nothing	7
teststring		11
teststring	10
select text1, length(text1) from t1 order by binary text1;
text1	length(text1)
nothing	7
teststring	10
teststring		11
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
alter table t1 modify text1 blob not null, drop key key1, add key key1 (text1(20));
insert into t1 values ('teststring ');
select concat('|', text1, '|') from t1 order by text1;
concat('|', text1, '|')
|nothing|
|teststring|
|teststring	|
|teststring |
select concat('|', text1, '|') from t1 where text1='teststring' or text1 > 'teststring\t';
concat('|', text1, '|')
|teststring |
|teststring|
select concat('|', text1, '|') from t1 where text1='teststring';
concat('|', text1, '|')
|teststring|
select concat('|', text1, '|') from t1 where text1='teststring ';
concat('|', text1, '|')
|teststring |
alter table t1 modify text1 text not null, pack_keys=1;
analyze table t1;
select concat('|', text1, '|') from t1 where text1='teststring';
concat('|', text1, '|')
|teststring |
|teststring|
select concat('|', text1, '|') from t1 where text1='teststring ';
concat('|', text1, '|')
|teststring |
|teststring|
explain select concat('|', text1, '|') from t1 where text1='teststring ';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	key1	key1	22	const	2	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select concat('|',`test`.`t1`.`text1`,'|') AS `concat('|', text1, '|')` from `test`.`t1` where (`test`.`t1`.`text1` = 'teststring ')
select concat('|', text1, '|') from t1 where text1 like 'teststring_%';
concat('|', text1, '|')
|teststring	|
|teststring |
select concat('|', text1, '|') as c from t1 where text1='teststring' or text1 like 'teststring_%' order by c;
c
|teststring	|
|teststring |
|teststring|
select concat('|', text1, '|') from t1 where text1='teststring' or text1 > 'teststring\t';
concat('|', text1, '|')
|teststring |
|teststring|
select concat('|', text1, '|') from t1 order by text1;
concat('|', text1, '|')
|nothing|
|teststring	|
|teststring|
|teststring |
drop table t1;
create table t1 (text1 varchar(32) not NULL, KEY key1 (text1)) charset latin1 pack_keys=0;
insert into t1 values ('teststring'), ('nothing'), ('teststring\t');
select concat('|', text1, '|') as c from t1 where text1='teststring' or text1 like 'teststring_%' order by c;
c
|teststring	|
|teststring|
select concat('|', text1, '|') from t1 where text1='teststring' or text1 >= 'teststring\t';
concat('|', text1, '|')
|teststring	|
|teststring|
drop table t1;
create table t1 (text1 varchar(32) not NULL, KEY key1 using BTREE (text1))
charset latin1 engine=heap;
insert into t1 values ('teststring'), ('nothing'), ('teststring\t');
select * from t1 ignore key (key1) where text1='teststring' or 
text1 like 'teststring_%' ORDER BY text1;
text1
teststring	
teststring
select * from t1 where text1='teststring' or text1 like 'teststring_%';
text1
teststring
teststring	
select * from t1 where text1='teststring' or text1 >= 'teststring\t';
text1
teststring
teststring	
select * from t1 order by text1;
text1
nothing
teststring	
teststring
explain select * from t1 order by text1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`text1` AS `text1` from `test`.`t1` order by `test`.`t1`.`text1`
alter table t1 modify text1 char(32) binary not null;
Warnings:
Warning	1287	'BINARY as attribute of a type' is deprecated and will be removed in a future release. Please use a CHARACTER SET clause with _bin collation instead
select * from t1 order by text1;
text1
nothing
teststring	
teststring
drop table t1;
create table t1 (text1 varchar(32) not NULL, KEY key1 (text1))
charset latin1 engine=innodb;
insert into t1 values ('teststring'), ('nothing'), ('teststring\t');
analyze table t1;
check table t1;
Table	Op	Msg_type	Msg_text
test.t1	check	status	OK
select * from t1 where text1='teststring' or text1 like 'teststring_%';
text1
teststring
teststring	
select * from t1 where text1='teststring' or text1 > 'teststring\t';
text1
teststring
select * from t1 order by text1;
text1
nothing
teststring	
teststring
explain select * from t1 order by text1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	key1	34	NULL	3	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`text1` AS `text1` from `test`.`t1` order by `test`.`t1`.`text1`
alter table t1 modify text1 char(32) binary not null;
Warnings:
Warning	1287	'BINARY as attribute of a type' is deprecated and will be removed in a future release. Please use a CHARACTER SET clause with _bin collation instead
select * from t1 order by text1;
text1
nothing
teststring	
teststring
alter table t1 modify text1 blob not null, drop key key1, add key key1 (text1(20));
insert into t1 values ('teststring ');
select concat('|', text1, '|') from t1 order by text1;
concat('|', text1, '|')
|nothing|
|teststring	|
|teststring |
|teststring|
alter table t1 modify text1 text not null, pack_keys=1;
select * from t1 where text1 like 'teststring_%';
text1
teststring	
teststring 
select text1, length(text1) from t1 where text1='teststring' or text1 like 'teststring_%';
text1	length(text1)
teststring		11
teststring	10
teststring 	11
select text1, length(text1) from t1 where text1='teststring' or text1 >= 'teststring\t';
text1	length(text1)
teststring		11
teststring	10
teststring 	11
select concat('|', text1, '|') from t1 order by text1;
concat('|', text1, '|')
|nothing|
|teststring	|
|teststring|
|teststring |
drop table t1;
