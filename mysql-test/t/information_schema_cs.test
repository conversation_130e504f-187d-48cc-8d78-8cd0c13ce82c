# Result differences depending on FS case sensitivity.
if (!$require_case_insensitive_file_system)
{
  --source include/have_case_sensitive_file_system.inc
}

--source include/no_valgrind_without_big.inc

#Don't run this test when thread_pool active
--source include/not_threadpool.inc

# Save the initial number of concurrent sessions
--source include/count_sessions.inc

--source include/elide_costs.inc

set @orig_sql_mode= @@sql_mode;

# Test for information_schema.schemata &
# show databases

--disable_warnings
DROP TABLE IF EXISTS t0,t1,t2,t3,t4,t5;
DROP VIEW IF EXISTS v1;
--enable_warnings

SET SESSION information_schema_stats_expiry=0;

--echo #
--echo # Bug#11763174 INFORMATION_SCHEMA.PARAMETERS.NUMERIC_PRECISION SHOULD BE BIGINT 
--echo #
--sorted_result
select table_name, data_type, column_type from information_schema.columns  where column_name = 'numeric_precision' and table_schema = 'information_schema';

--disable_warnings
show variables where variable_name like "skip_show_database";
--enable_warnings
create user mysqltest_1@localhost, mysqltest_2@localhost;
grant select, update, execute on test.* to mysqltest_2@localhost;
grant select, update on test.* to mysqltest_1@localhost;
create user mysqltest_3@localhost;
create user mysqltest_3;


select * from information_schema.SCHEMATA where schema_name > 'm' ORDER BY SCHEMA_NAME;
select schema_name from information_schema.schemata ORDER BY schema_name;
show databases like 't%';
show databases;
show databases where `database` = 't%';

# Test for information_schema.tables &
# show tables

create database mysqltest;
create table mysqltest.t1(a int, b VARCHAR(30), KEY string_data (b));
create table test.t2(a int);
create table t3(a int, KEY a_data (a));
create table mysqltest.t4(a int);
create table t5 (id int auto_increment primary key);
insert into t5 values (10);
create view v1 (c) as
 SELECT table_name FROM information_schema.TABLES
  WHERE table_schema IN ('mysql', 'information_schema', 'test', 'mysqltest') AND
        table_name COLLATE utf8mb3_general_ci not like 'ndb_%' AND
        table_name COLLATE utf8mb3_general_ci not like 'innodb_%';
--sorted_result
select * from v1;

select c,table_name from v1
inner join information_schema.TABLES v2 on (v1.c=v2.table_name)
where v1.c rlike "t[1-5]{1}$" order by c;

select c,table_name from v1
left join information_schema.TABLES v2 on (v1.c=v2.table_name)
where v1.c rlike "t[1-5]{1}$" order by c;

select c, v2.table_name from v1
right join information_schema.TABLES v2 on (v1.c=v2.table_name)
where v1.c rlike "t[1-5]{1}$" order by c;

select table_name from information_schema.TABLES
where table_schema = "mysqltest" and
table_name rlike "t[1-5]{1}$" order by table_name;
select * from information_schema.STATISTICS where TABLE_SCHEMA = "mysqltest" order by table_name, index_name;
show keys from t3 where Key_name = "a_data";

--sorted_result
show tables like 't%';
analyze table t2, t3, t5;
--replace_column 6 # 7 # 8 # 9 # 12 # 13 #
show table status;
show full columns from t3 like "a%";
show full columns from mysql.db like "Insert%";
--replace_result utf8mb3_tolower_ci utf8mb3_bin
show full columns from v1;
select * from information_schema.COLUMNS where table_name="t1"
and column_name= "a" order by table_name;
show columns from mysqltest.t1 where field like "%a%";

create view mysqltest.v1 (c) as select a from mysqltest.t1;
grant select (a) on mysqltest.t1 to mysqltest_2@localhost;
grant select on mysqltest.v1 to mysqltest_3;
connect (user3,localhost,mysqltest_2,,);
connection user3;
select table_name, column_name, privileges from information_schema.columns
where table_schema = 'mysqltest' and table_name = 't1' order by table_name, column_name;
show columns from mysqltest.t1;
connect (user4,localhost,mysqltest_3,,mysqltest);
connection user4;
select table_name, column_name, privileges from information_schema.columns
where table_schema = 'mysqltest' and table_name = 'v1' order by table_name, column_name;
--error ER_VIEW_NO_EXPLAIN
explain select * from v1;
connection default;
disconnect user4;

drop view v1, mysqltest.v1;
drop tables mysqltest.t4, mysqltest.t1, t2, t3, t5;
drop database mysqltest;

# Test for information_schema.CHARACTER_SETS &
# SHOW CHARACTER SET

select * from information_schema.CHARACTER_SETS
where CHARACTER_SET_NAME like 'latin1%' order by character_set_name;
SHOW CHARACTER SET LIKE 'latin1%';
SHOW CHARACTER SET WHERE charset like 'latin1%';

# Test for information_schema.COLLATIONS &
# SHOW COLLATION

--replace_column 5 #
select * from information_schema.COLLATIONS
where COLLATION_NAME like 'latin1%' order by collation_name;
--replace_column 5 #
SHOW COLLATION LIKE 'latin1%';
--replace_column 5 #
SHOW COLLATION WHERE collation like 'latin1%';

select * from information_schema.COLLATION_CHARACTER_SET_APPLICABILITY
where COLLATION_NAME like 'latin1%' ORDER BY COLLATION_NAME;

# Test for information_schema.ROUTINES &
#

--disable_warnings
drop procedure if exists sel2;
drop function if exists sub1;
drop function if exists sub2;
--enable_warnings

create function sub1(i int) returns int
  return i+1;
delimiter |;
create procedure sel2()
begin
  select * from t1;
  select * from t2;
end|
delimiter ;|

#
# Bug#7222 information_schema: errors in "routines"
#
--sorted_result
select parameter_style, sql_data_access, dtd_identifier
from information_schema.routines where routine_schema='test';

--replace_column 6 # 7 #
show procedure status where db='test';
--replace_column 6 # 7 #
show function status where db='test';
select a.ROUTINE_NAME from information_schema.ROUTINES a,
information_schema.SCHEMATA b where
a.ROUTINE_SCHEMA = b.SCHEMA_NAME AND b.SCHEMA_NAME='test'
ORDER BY a.ROUTINE_NAME;

select count(*) from information_schema.ROUTINES where routine_schema='test';

create view v1 as select routine_schema, routine_name from information_schema.routines where routine_schema='test'
order by routine_schema, routine_name;
select * from v1;
drop view v1;

connect (user1,localhost,mysqltest_1,,);
connection user1;
select ROUTINE_NAME, ROUTINE_DEFINITION from information_schema.ROUTINES
WHERE ROUTINE_SCHEMA='test' ORDER BY ROUTINE_NAME;
--error ER_SP_DOES_NOT_EXIST
show create function sub1;
connection user3;
select ROUTINE_NAME, ROUTINE_DEFINITION from information_schema.ROUTINES
WHERE ROUTINE_SCHEMA='test' ORDER BY ROUTINE_NAME;
connection default;
grant all privileges on test.* to mysqltest_1@localhost;
connect (user2,localhost,mysqltest_1,,);
connection user2;
select ROUTINE_NAME, ROUTINE_DEFINITION from information_schema.ROUTINES
WHERE ROUTINE_SCHEMA='test' ORDER BY ROUTINE_NAME;
create function sub2(i int) returns int
  return i+1;
select ROUTINE_NAME, ROUTINE_DEFINITION from information_schema.ROUTINES
WHERE ROUTINE_SCHEMA='test' ORDER BY ROUTINE_NAME;
show create procedure sel2;
show create function sub1;
show create function sub2;
--replace_column 6 # 7 #
show function status like "sub2";
connection default;
disconnect user1;
disconnect user3;
drop function sub2;
show create procedure sel2;

#
# Test for views
#
create view v0 (c) as select schema_name from information_schema.schemata order by schema_name;
select * from v0;
--replace_column 3 # 10 # 11 #
explain select * from v0;
create view v1 (c) as select table_name from information_schema.tables
where table_name="v1" order by table_name;
select * from v1;
create view v2 (c) as select column_name from information_schema.columns
where table_name="v2" order by column_name;
select * from v2;
create view v3 (c) as select CHARACTER_SET_NAME from information_schema.character_sets
where CHARACTER_SET_NAME like "latin1%" order by character_set_name;
select * from v3;
create view v4 (c) as select COLLATION_NAME from information_schema.collations
where COLLATION_NAME like "latin1%" order by collation_name;
select * from v4;
show keys from v4;
--replace_column 4 #
select * from information_schema.views where TABLE_SCHEMA != 'sys' and
TABLE_NAME rlike "v[0-4]{1}$" order by table_name;
drop view v0, v1, v2, v3, v4;

#
# Test for privileges tables
#
create table t1 (a int);
grant select,update,insert on t1 to mysqltest_1@localhost;
grant select (a), update (a),insert(a), references(a) on t1 to mysqltest_1@localhost;
grant all on test.* to mysqltest_1@localhost with grant option;
select * from information_schema.USER_PRIVILEGES where grantee like '%mysqltest_1%';
select * from information_schema.SCHEMA_PRIVILEGES where grantee like '%mysqltest_1%';
select * from information_schema.TABLE_PRIVILEGES where grantee like '%mysqltest_1%';
select * from information_schema.COLUMN_PRIVILEGES where grantee like '%mysqltest_1%';
delete from mysql.user where user like 'mysqltest%';
delete from mysql.db where user like 'mysqltest%';
delete from mysql.tables_priv where user like 'mysqltest%';
delete from mysql.columns_priv where user like 'mysqltest%';
flush privileges;
drop table t1;


#
# Test for KEY_COLUMN_USAGE & TABLE_CONSTRAINTS tables
#

create table t1 (a int not null, primary key(a));
alter table t1 add constraint constraint_1 unique (a);
alter table t1 add constraint unique key_1(a);
alter table t1 add constraint constraint_2 unique key_2(a);
show create table t1;
select * from information_schema.TABLE_CONSTRAINTS where
TABLE_SCHEMA= "test" order by constraint_name;
select * from information_schema.key_column_usage where
TABLE_SCHEMA= "test" order by constraint_name;

connection user2;
select table_name from information_schema.TABLES where table_schema like "test%" order by table_name;
select table_name,column_name from information_schema.COLUMNS
where table_schema like "test%" order by table_name, column_name;
SELECT ROUTINE_NAME FROM information_schema.ROUTINES
WHERE ROUTINE_SCHEMA != 'sys' ORDER BY ROUTINE_NAME;
disconnect user2;
connection default;
delete from mysql.user where user='mysqltest_1';
drop table t1;
drop procedure sel2;
drop function sub1;

create table t1(a int);
create view v1 (c) as select a from t1 with check option;
create view v2 (c) as select a from t1 WITH LOCAL CHECK OPTION;
create view v3 (c) as select a from t1 WITH CASCADED CHECK OPTION;
create user joe@localhost;
select * from information_schema.views where table_schema !=
'sys' order by table_name;
grant select (a) on test.t1 to joe@localhost with grant option;
select * from INFORMATION_SCHEMA.COLUMN_PRIVILEGES WHERE table_schema != 'sys';
select * from INFORMATION_SCHEMA.TABLE_PRIVILEGES WHERE table_schema NOT IN ('sys','mysql');
drop view v1, v2, v3;
drop table t1;
delete from mysql.user where user='joe';
delete from mysql.db where user='joe';
delete from mysql.tables_priv where user='joe';
delete from mysql.columns_priv where user='joe';
flush privileges;

# QQ This results in NULLs instead of the version numbers when
# QQ a LOCK TABLES is in effect when selecting from
# QQ information_schema.tables.

# Until bug is fixed
--disable_testcase BUG#0000
delimiter //;
create procedure px5 ()
begin
declare v int;
declare c cursor for select version from
information_schema.tables where table_schema <> 'information_schema';
open c;
fetch c into v;
select v;
close c;
end;//

call px5()//
call px5()//
delimiter ;//
select sql_mode from information_schema.ROUTINES where ROUTINE_SCHEMA != 'sys';
drop procedure px5;
--enable_testcase

create table t1 (a int not null auto_increment,b int, primary key (a));
insert into t1 values (1,1),(NULL,3),(NULL,4);
analyze table t1;
select AUTO_INCREMENT from information_schema.tables where table_name = 't1';
drop table t1;
create table t1 (s1 int);
insert into t1 values (0),(9),(0);
select s1 from t1 where s1 in (select version from
information_schema.tables) union select version from
information_schema.tables;
drop table t1;

SHOW CREATE TABLE INFORMATION_SCHEMA.character_sets;
set names latin2;
SHOW CREATE TABLE INFORMATION_SCHEMA.character_sets;
set names latin1;

create table t1 select * from information_schema.CHARACTER_SETS
where CHARACTER_SET_NAME like "latin1" order by character_set_name;
select * from t1;
alter table t1 default character set utf8mb3;
show create table t1;
drop table t1;

create view v1 as select * from information_schema.TABLES;
drop view v1;
create table t1(a NUMERIC(5,3), b NUMERIC(5,1), c float(5,2),
 d NUMERIC(6,4), e float, f DECIMAL(6,3), g int(11), h DOUBLE(10,3),
 i DOUBLE);
--sorted_result
select COLUMN_NAME,COLUMN_TYPE, CHARACTER_MAXIMUM_LENGTH,
 CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE
from information_schema.columns where table_name= 't1';
drop table t1;

delimiter //;
create procedure p108 () begin declare c cursor for select data_type
from information_schema.columns;  open c; open c; end;//
--error ER_SP_CURSOR_ALREADY_OPEN
call p108()//
delimiter ;//
drop procedure p108;

create view v1 as select A1.table_name from information_schema.TABLES A1
where table_name= "user" order by table_name;
select * from v1;
drop view v1;

create view vo as select 'a' union select 'a';
show index from vo;
select * from information_schema.TABLE_CONSTRAINTS where
TABLE_NAME= "vo";
select * from information_schema.key_column_usage where
TABLE_NAME= "vo";
drop view vo;

select TABLE_NAME,TABLE_TYPE,ENGINE
from information_schema.tables
where table_schema='information_schema'
order by table_name COLLATE utf8mb3_general_ci limit 2;
--sorted_result
show tables from information_schema like "T%";

--error ER_DBACCESS_DENIED_ERROR
create database information_schema;
use information_schema;
--sorted_result
show full tables like "T%";
--error ER_DBACCESS_DENIED_ERROR
create table t1(a int);
use test;
show tables;
use information_schema;
--sorted_result
show tables like "T%";

#
# Bug#7210 information_schema: can't access when table-name = reserved word
#
select table_name from tables where table_name='user';
select column_name, privileges from columns
where table_name='user' and column_name like '%o%' order by column_name;

#
# Bug#7212 information_schema: "Can't find file" errors if storage engine gone
# Bug#7211 information_schema: crash if bad view
#
use test;
create function sub1(i int) returns int
  return i+1;
create table t1(f1 int);
create view v2 (c) as select f1 from t1;
create view v3 (c) as select sub1(1);
create table t4(f1 int, KEY f1_key (f1));
drop table t1;
drop function sub1;
select table_name from information_schema.views
where table_schema='test' order by table_name;
select table_name from information_schema.views
where table_schema='test' order by table_name;
select column_name from information_schema.columns
where table_schema='test' order by column_name;
select index_name from information_schema.statistics where
table_schema='test' order by index_name;
select constraint_name from information_schema.table_constraints
where table_schema='test' order by constraint_name;
show create view v2;
show create table v3;
drop view v2;
drop view v3;
drop table t4;

#
# Bug#7213 information_schema: redundant non-standard TABLE_NAMES table
#
--error ER_UNKNOWN_TABLE
select * from information_schema.table_names;

#
# Bug#2719 information_schema: errors in "columns"
#
select column_type from information_schema.columns
where table_schema="information_schema" and table_name="COLUMNS" and
(column_name="character_set_name" or column_name="collation_name");

#
# Bug#2718 information_schema: errors in "tables"
#
select TABLE_ROWS from information_schema.tables where
table_schema="information_schema" and table_name="COLUMNS";
select table_type from information_schema.tables
where table_schema="mysql" and table_name="user";

# test for 'show open tables ... where'
show open tables where `table` like "user";
# test for 'show status ... where'
--disable_warnings
show status where variable_name like "%database%";
# test for 'show variables ... where'
show variables where variable_name like "skip_show_databas";
--enable_warnings

#
# Bug#7981 SHOW GLOBAL STATUS crashes server
#
# We don't actually care about the value, just that it doesn't crash.
--replace_column 2 #
show global status like "Threads_running";

#
# Bug#7915 crash,JOIN VIEW, subquery,
# SELECT .. FROM INFORMATION_SCHEMA.COLUMNS
#
create table t1(f1 int);
create table t2(f2 int);
create view v1 as select * from t1, t2;
set @got_val= (select count(*) from information_schema.columns);
drop view v1;
drop table t1, t2;

#
# Bug#7476 crash on SELECT * FROM INFORMATION_SCHEMA.TABLES
#
use test;
CREATE TABLE t_crashme ( f1 BIGINT);
CREATE VIEW a1 (t_CRASHME) AS SELECT f1 FROM t_crashme GROUP BY f1;
CREATE VIEW a2 AS SELECT t_CRASHME FROM a1;
let $tab_count= 65;
--disable_query_log
while ($tab_count)
{
     EVAL CREATE TABLE t_$tab_count (f1 BIGINT);
     dec $tab_count ;
}
--disable_result_log
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES;
--enable_result_log
SELECT count(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='test';
let $tab_count= 65;
while ($tab_count)
{
     EVAL DROP TABLE t_$tab_count;
     dec $tab_count ;
}
--enable_query_log
drop view a2, a1;
drop table t_crashme;

#
# Bug#7215 information_schema: columns are longtext instead of varchar
# Bug#7217 information_schema: columns are varbinary() instead of timestamp
#
select table_schema, table_name, column_name from information_schema.columns
where table_schema not in ('performance_schema', 'sys', 'mysql')
  and data_type = 'longtext' order by table_name, column_name;

select table_name, column_name, data_type from information_schema.columns
where table_schema not in ('performance_schema', 'sys')
  and data_type = 'datetime'
  and table_name COLLATE utf8mb3_general_ci not like 'innodb_%' order by table_name, column_name;

#
# Bug#8164 subquery with INFORMATION_SCHEMA.COLUMNS, 100 % CPU
#
SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES A
WHERE NOT EXISTS
(SELECT * FROM INFORMATION_SCHEMA.COLUMNS B
  WHERE A.TABLE_SCHEMA = B.TABLE_SCHEMA
  AND A.TABLE_NAME = B.TABLE_NAME);

#
# Bug#9344 INFORMATION_SCHEMA, wrong content, numeric columns
#

create table t1
( x_bigint BIGINT,
  x_integer INTEGER,
  x_smallint SMALLINT,
  x_decimal DECIMAL(5,3),
  x_numeric NUMERIC(5,3),
  x_real REAL,
  x_float FLOAT,
  x_double_precision DOUBLE PRECISION );
--sorted_result
SELECT COLUMN_NAME, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME= 't1' ORDER BY COLUMN_NAME;
drop table t1;

#
# Bug#10261 INFORMATION_SCHEMA.COLUMNS, incomplete result for non root user
#

create user mysqltest_4@localhost;
grant select on test.* to mysqltest_4@localhost;
connect (user10261,localhost,mysqltest_4,,);
connection user10261;
SELECT TABLE_NAME, COLUMN_NAME, PRIVILEGES FROM INFORMATION_SCHEMA.COLUMNS
where COLUMN_NAME='TABLE_NAME' ORDER BY TABLE_NAME COLLATE utf8mb3_GENERAL_CI;
connection default;
disconnect user10261;
delete from mysql.user where user='mysqltest_4';
delete from mysql.db where user='mysqltest_4';
flush privileges;

#
# Bug#9404 information_schema: Weird error messages
# with SELECT SUM() ... GROUP BY queries
#
--sorted_result
SELECT table_schema, count(*) FROM information_schema.TABLES WHERE
table_schema IN ('mysql', 'information_schema', 'test', 'mysqltest')
AND table_name not like 'ndb%' AND table_name COLLATE utf8mb3_general_ci not like 'innodb_%'
GROUP BY TABLE_SCHEMA;



#
# TRIGGERS table test
#
create table t1 (i int, j int);

delimiter |;
create trigger trg1 before insert on t1 for each row
begin
  if new.j > 10 then
    set new.j := 10;
  end if;
end|
create trigger trg2 before update on t1 for each row
begin
  if old.i % 2 = 0 then
    set new.j := -1;
  end if;
end|
create trigger trg3 after update on t1 for each row
begin
  if new.j = -1 then
    set @fired:= "Yes";
  end if;
end|
delimiter ;|

--replace_column 6 #
show triggers;
--replace_column 17 #
select * from information_schema.triggers where trigger_schema in ('mysql', 'information_schema', 'test', 'mysqltest')
order by trigger_name;

drop trigger trg1;
drop trigger trg2;
drop trigger trg3;
drop table t1;


#
# Bug#10964 Information Schema:Authorization check on privilege tables is improper
#

create database mysqltest;
create table mysqltest.t1 (f1 int, f2 int);
create table mysqltest.t2 (f1 int);
create user user1@localhost, user2@localhost, user3@localhost, user4@localhost;
grant select (f1) on mysqltest.t1 to user1@localhost;
grant select on mysqltest.t2 to user2@localhost;
grant select on mysqltest.* to user3@localhost;
grant select on *.* to user4@localhost;

connect (con1,localhost,user1,,mysqltest);
connect (con2,localhost,user2,,mysqltest);
connect (con3,localhost,user3,,mysqltest);
connect (con4,localhost,user4,,);
connection con1;
select * from information_schema.column_privileges order by grantee;
select * from information_schema.table_privileges order by grantee;
select * from information_schema.schema_privileges order by grantee;
select * from information_schema.user_privileges order by grantee;
show grants;
connection con2;
select * from information_schema.column_privileges order by grantee;
select * from information_schema.table_privileges order by grantee;
select * from information_schema.schema_privileges order by grantee;
select * from information_schema.user_privileges order by grantee;
show grants;
connection con3;
select * from information_schema.column_privileges order by grantee;
select * from information_schema.table_privileges order by grantee;
select * from information_schema.schema_privileges order by grantee;
select * from information_schema.user_privileges order by grantee;
show grants;
connection con4;
select * from information_schema.column_privileges where grantee like '%user%'
order by grantee;
select * from information_schema.table_privileges where grantee like '%user%'
and table_schema !='mysql' order by grantee;
select * from information_schema.schema_privileges where grantee like '%user%'
and table_schema !='performance_schema' order by grantee;
select * from information_schema.user_privileges where grantee like '%user%' and grantee not like '%session%'
order by grantee;
show grants;
connection default;
disconnect con1;
disconnect con2;
disconnect con3;
disconnect con4;
drop user user1@localhost, user2@localhost, user3@localhost, user4@localhost;
use test;
drop database mysqltest;

#
# Bug#11055 information_schema: routines.sql_data_access has wrong value
#
--disable_warnings
drop procedure if exists p1;
drop procedure if exists p2;
--enable_warnings

create procedure p1 () modifies sql data set @a = 5;
create procedure p2 () set @a = 5;
--sorted_result
select sql_data_access from information_schema.routines
where specific_name like 'p%' and ROUTINE_SCHEMA != 'sys';
drop procedure p1;
drop procedure p2;

#
# Bug#9434 SHOW CREATE DATABASE information_schema;
#
show create database information_schema;

#
# Bug#11057 information_schema: columns table has some questionable contents
# Bug#12301 information_schema: NUMERIC_SCALE must be 0 for integer columns
#
create table t1(f1 LONGBLOB, f2 LONGTEXT);
select column_name,data_type,CHARACTER_OCTET_LENGTH,
       CHARACTER_MAXIMUM_LENGTH
from information_schema.columns
where table_name='t1' order by column_name;
drop table t1;
create table t1(f1 tinyint, f2 SMALLINT, f3 mediumint, f4 int,
                f5 BIGINT, f6 BIT, f7 bit(64));
select column_name, NUMERIC_PRECISION, NUMERIC_SCALE
from information_schema.columns
where table_name='t1' order by column_name;
drop table t1;

#
# Bug#12127 triggers do not show in info_schema before they are used if set to the database
#
create table t1 (f1 integer);
create trigger tr1 after insert on t1 for each row set @test_var=42;
use information_schema;
select trigger_schema, trigger_name from triggers where
trigger_name='tr1';
use test;
drop table t1;

#
# Bug#12518 COLUMN_DEFAULT has wrong value if NOT NULL is set
#
create table t1 (a int not null, b int);
use information_schema;
--sorted_result
select column_name, column_default from columns
  where table_schema='test' and table_name='t1';
use test;
show columns from t1;
drop table t1;

#
# Bug#12636 SHOW TABLE STATUS with where condition containing a subquery
#           over information schema
#

CREATE TABLE t1 (a int);
CREATE TABLE t2 (b int);

analyze table t1, t2;
--replace_column 7 # 8 # 12 # 13 #
SHOW TABLE STATUS FROM test
  WHERE name IN ( SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_SCHEMA='test' AND TABLE_TYPE='BASE TABLE');

DROP TABLE t1,t2;

#
# Bug#12905 show fields from view behaving erratically with current database
#
create table t1(f1 int);
create view v1 (c) as select f1 from t1;
connect (con5,localhost,root,,*NO-ONE*);
select database();
show fields from test.v1;
connection default;
disconnect con5;
drop view v1;
drop table t1;

#
# Bug#9846 Inappropriate error displayed while dropping table from 'INFORMATION_SCHEMA'
#
--error ER_PARSE_ERROR
alter database information_schema;
--error ER_DBACCESS_DENIED_ERROR
drop database information_schema;
--error ER_DBACCESS_DENIED_ERROR
drop table information_schema.tables;
--error ER_DBACCESS_DENIED_ERROR
alter table information_schema.tables;
#
# Bug#9683 INFORMATION_SCH: Creation of temporary table allowed in Information_schema DB
#
use information_schema;
--error ER_DBACCESS_DENIED_ERROR
create temporary table schemata(f1 char(10));
#
# Bug#10708 SP's can use INFORMATION_SCHEMA as ROUTINE_SCHEMA
#
delimiter |;
--error ER_DBACCESS_DENIED_ERROR
CREATE PROCEDURE p1 ()
BEGIN
  SELECT 'foo' FROM DUAL;
END |
delimiter ;|
select ROUTINE_NAME from routines where ROUTINE_SCHEMA='information_schema';
#
# Bug#10734 Grant of privileges other than 'select' and 'create view' should fail on schema
#
--error ER_DBACCESS_DENIED_ERROR
grant all on information_schema.* to 'user1'@'localhost';
--error ER_DBACCESS_DENIED_ERROR
grant select on information_schema.* to 'user1'@'localhost';

#
# Bug#14089 FROM list subquery always fails when information_schema is current database
#
use test;
create table t1(id int);
insert into t1(id) values (1);
select 1 from (select 1 from test.t1) a;
use information_schema;
select 1 from (select 1 from test.t1) a;
use test;
drop table t1;

#
# Bug#14476 `information_schema`.`TABLES`.`TABLE_TYPE` with empty value
#
create table t1 (f1 int(11));
create view v1 as select * from t1;
drop table t1;
select table_type from information_schema.tables
where table_name="v1";
drop view v1;

#
# Bug#14387 SHOW COLUMNS doesn't work on temporary tables
# Bug#15224 SHOW INDEX from temporary table doesn't work
# Bug#12770 DESC cannot display the info. about temporary table
#
create temporary table t1(f1 int, index(f1));
show columns from t1;
describe t1;
show indexes from t1;
drop table t1;

#
# Bug#14271 I_S: columns has no size for (var)binary columns
#
create table t1(f1 binary(32), f2 varbinary(64));
--sorted_result
select character_maximum_length, character_octet_length
from information_schema.columns where table_name='t1';
drop table t1;

#
# Bug#15533 crash, information_schema, function, view
#
CREATE TABLE t1 (f1 BIGINT, f2 VARCHAR(20), f3 BIGINT);
INSERT INTO t1 SET f1 = 1, f2 = 'Schoenenbourg', f3 = 1;

CREATE FUNCTION func2() RETURNS BIGINT RETURN 1;

delimiter //;
CREATE FUNCTION func1() RETURNS BIGINT
BEGIN
  RETURN ( SELECT COUNT(*) FROM information_schema.views WHERE TABLE_SCHEMA != 'sys' AND
           TABLE_SCHEMA != 'information_schema');
END//
delimiter ;//

CREATE VIEW v1 AS SELECT 1 FROM t1
                    WHERE f3 = (SELECT func2 ());
SELECT func1();
DROP TABLE t1;
DROP VIEW v1;
DROP FUNCTION func1;
DROP FUNCTION func2;


#
# Bug#15307 GROUP_CONCAT() with ORDER BY returns empty set on information_schema
#
SELECT column_type, GROUP_CONCAT(table_schema, '.', table_name ORDER BY table_name), COUNT(*) AS num
FROM information_schema.columns WHERE
table_schema='information_schema' AND
(column_type = 'varchar(7)' OR column_type = 'varchar(20)'
 OR column_type = 'varchar(30)')
GROUP BY column_type ORDER BY num, column_type;

#
# Bug#19236 bad COLUMNS.CHARACTER_MAXIMUM_LENGHT and CHARACTER_OCTET_LENGTH
#
create table t1(f1 char(1) not null, f2 char(9) not null)
default character set utf8mb3;
--sorted_result
select CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH from
information_schema.columns where table_schema='test' and table_name = 't1';
drop table t1;

#
# Bug#16681 information_schema shows forbidden VIEW details
#
create user mysqltest_1@localhost;
grant select on test.* to mysqltest_1@localhost;
create table t1 (id int);
create view v1 as select * from t1;
create definer = mysqltest_1@localhost
sql security definer view v2 as select 1;

connect (con16681,localhost,mysqltest_1,,test);
connection con16681;

--sorted_result
select * from information_schema.views
where table_name='v1' or table_name='v2';
connection default;
disconnect con16681;
drop view v1, v2;
drop table t1;
drop user mysqltest_1@localhost;

#
# Bug#19599 duplication of information_schema column value in a CONCAT expr with user var
#
set @a:= '.';
create table t1(f1 char(5));
create table t2(f1 char(5));
select concat(@a, table_name), @a, table_name
from information_schema.tables where table_schema = 'test' order by table_name;
drop table t1,t2;


#
# Bug#20230 routine_definition is not null
#
--disable_warnings
DROP PROCEDURE IF EXISTS p1;
DROP FUNCTION IF EXISTS f1;
--enable_warnings

CREATE PROCEDURE p1() SET @a= 1;
CREATE FUNCTION f1() RETURNS INT RETURN @a + 1;
CREATE USER mysql_bug20230@localhost;
GRANT EXECUTE ON PROCEDURE p1 TO mysql_bug20230@localhost;
GRANT EXECUTE ON FUNCTION f1 TO mysql_bug20230@localhost;

SELECT ROUTINE_NAME, ROUTINE_DEFINITION FROM INFORMATION_SCHEMA.ROUTINES
WHERE ROUTINE_SCHEMA='test' ORDER BY ROUTINE_NAME;
SHOW CREATE PROCEDURE p1;
SHOW CREATE FUNCTION f1;

connect (conn1, localhost, mysql_bug20230,,);

SELECT ROUTINE_NAME, ROUTINE_DEFINITION FROM INFORMATION_SCHEMA.ROUTINES
WHERE ROUTINE_SCHEMA='test' ORDER BY ROUTINE_NAME;
SHOW CREATE PROCEDURE p1;
SHOW CREATE FUNCTION f1;
CALL p1();
SELECT f1();

disconnect conn1;
connection default;

DROP FUNCTION f1;
DROP PROCEDURE p1;
DROP USER mysql_bug20230@localhost;

#
# Bug#21231 query with a simple non-correlated subquery over
#           INFORMARTION_SCHEMA.TABLES
#

SELECT MAX(table_name)
  FROM information_schema.tables
  WHERE table_schema IN ('mysql', 'information_schema', 'test');
SELECT table_name FROM information_schema.tables
  WHERE table_name=(SELECT MAX(table_name)
                      FROM information_schema.tables WHERE
                      table_schema IN ('mysql',
                                       'information_schema',
                                       'test')) order by table_name;
#
# Bug#23037 Bug in field "Default" of query "SHOW COLUMNS FROM table"
#
# Note, MyISAM/InnoDB can't take more that 65532 chars, because the row
# size is limited to 65535 bytes (BLOBs not counted)
#
--disable_warnings
DROP TABLE IF EXISTS bug23037;
DROP FUNCTION IF EXISTS get_value;
--enable_warnings
--disable_query_log
DELIMITER |;
CREATE FUNCTION get_value()
  RETURNS TEXT
  DETERMINISTIC
BEGIN
  DECLARE col1, col2, col3, col4, col6 CHAR(255);
  DECLARE default_val VARCHAR(65532) CHARACTER SET latin1;
  DECLARE done INT DEFAULT 0;
  DECLARE cur1 CURSOR FOR SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, COLUMN_KEY, COLUMN_DEFAULT, EXTRA FROM INFORMATION_SCHEMA.COLUMNS where TABLE_NAME='bug23037';
  DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
  OPEN cur1;
  FETCH cur1 INTO col1, col2, col3, col4, default_val, col6;
  CLOSE cur1;
  RETURN default_val;
end|
DELIMITER ;|

let $body=`SELECT REPEAT('A', 65532)`;
eval CREATE TABLE bug23037(fld1 VARCHAR(65532) CHARACTER SET latin1 DEFAULT "$body");
--enable_query_log

SELECT COLUMN_NAME, SHA2(COLUMN_DEFAULT, 0), LENGTH(COLUMN_DEFAULT)
  FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='bug23037'
  ORDER BY COLUMN_NAME;

SELECT SHA2(get_value(), 0);

SELECT COLUMN_NAME, SHA2(COLUMN_DEFAULT, 0), LENGTH(COLUMN_DEFAULT), COLUMN_DEFAULT=get_value() FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='bug23037' ORDER BY COLUMN_NAME;

DROP TABLE bug23037;
DROP FUNCTION get_value;

#
# Bug#22413 EXPLAIN SELECT FROM view with ORDER BY yield server crash
#
create view v1 as
select table_schema as object_schema,
       table_name   as object_name,
       table_type   as object_type
from information_schema.tables
order by object_schema;
--disable_result_log
explain select * from v1;
explain select * from (select table_name from information_schema.tables) as a;
--enable_result_log
drop view v1;

#
# Bug#23299 Some queries against INFORMATION_SCHEMA with subqueries fail
#
create table t1 (f1 int(11));
create table t2 (f1 int(11), f2 int(11));

select table_name from information_schema.tables
where table_schema = 'test' and table_name not in
(select table_name from information_schema.columns
 where table_schema = 'test' and column_name = 'f3') order by table_name;
drop table t1,t2;

#
# Bug#24630 Subselect query crashes mysqld
#
select 1 as f1 from information_schema.tables  where "COLUMN_PRIVILEGES"=
(select cast(table_name as char)  from information_schema.tables
 where table_schema != 'performance_schema' order by table_name limit 1) limit 1;

select t.table_name, group_concat(t.table_schema, '.', t.table_name),
       count(*) as num1
from information_schema.tables t
inner join information_schema.columns c1
on t.table_schema = c1.table_schema AND t.table_name = c1.table_name
where t.table_name not like 'ndb%' and
      t.table_schema = 'information_schema' and
        c1.ordinal_position =
        (select isnull(c2.column_type) -
         isnull(group_concat(c2.table_schema, '.', c2.table_name)) +
         count(*) as num
         from information_schema.columns c2 where
         c2.table_schema='information_schema' and
         (c2.column_type = 'varchar(7)' or c2.column_type = 'varchar(20)')
          group by c2.column_type order by num limit 1)
        and t.table_name not like 'INNODB_%'
group by t.table_name order by num1, t.table_name COLLATE utf8mb3_general_ci;
#
# Bug#28266 IS_UPDATABLE field on VIEWS table in I_S database is wrong
#
create table t1(f1 int);
create view v1 as select f1+1 as a from t1;
create table t2 (f1 int, f2 int);
create view v2 as select f1+1 as a, f2 as b from t2;
select table_name, is_updatable from information_schema.views where table_schema != 'sys' order by table_name;
#
# Note: we can perform 'delete' for non updatable view.
#
delete from v1;
drop view v1,v2;
drop table t1,t2;

#
# Bug#25859 ALTER DATABASE works w/o parameters
#
--error ER_PARSE_ERROR
alter database;
--error ER_PARSE_ERROR
alter database test;

#
# Bug#27629 Possible security flaw in INFORMATION_SCHEMA and SHOW statements
#

create user mysqltest_1@localhost;
create database mysqltest;
create table mysqltest.t1(a int, b int, c int);
create trigger mysqltest.t1_ai after insert on mysqltest.t1
  for each row set @a = new.a + new.b + new.c;
grant select(b) on mysqltest.t1 to mysqltest_1@localhost;

select trigger_name from information_schema.triggers
where event_object_table='t1';
--replace_column 6 #
show triggers from mysqltest;

connect (con27629,localhost,mysqltest_1,,mysqltest);
show columns from t1;
select column_name from information_schema.columns where table_name='t1' order by column_name;

--replace_column 6 #
show triggers;
select trigger_name from information_schema.triggers
where event_object_table='t1';
connection default;
disconnect con27629;
drop user mysqltest_1@localhost;
drop database mysqltest;

#
# Bug#27747 database metadata doesn't return sufficient column default info
#
create table t1 (
  f1 varchar(50),
  f2 varchar(50) not null,
  f3 varchar(50) default '',
  f4 varchar(50) default NULL,
  f5 bigint not null,
  f6 bigint not null default 10,
  f7 datetime not null,
  f8 datetime default '2006-01-01'
);
--sorted_result
select column_default from information_schema.columns where table_name= 't1';
show columns from t1;
drop table t1;

#
# Bug#30079 A check for "hidden" I_S tables is flawed
#
--error ER_UNKNOWN_TABLE
show fields from information_schema.table_names;
--error ER_UNKNOWN_TABLE
show keys from information_schema.table_names;

#
# Bug#34529 Crash on complex Falcon I_S select after ALTER .. PARTITION BY
#
USE information_schema;
SET max_heap_table_size = 16384;

CREATE TABLE test.t1( a INT );

# What we need to create here is a bit of a corner case:
# We need a star query with information_schema tables, where the first
# branch of the star join produces zero rows, so that reading of the
# second branch never happens. At the same time we have to make sure
# that data for at least the last table is swapped from MEMORY/HEAP to
# MyISAM. This and only this triggers the bug.
SELECT *
FROM tables ta
JOIN collations co ON ( co.collation_name = CONVERT(ta.table_catalog using utf8mb3))
JOIN character_sets cs ON ( cs.character_set_name = CONVERT(ta.table_catalog using utf8mb3));

DROP TABLE test.t1;
SET max_heap_table_size = DEFAULT;
USE test;

--echo End of 5.0 tests.

#
# Show engines
#

select * from information_schema.engines WHERE ENGINE="MyISAM";

# Tests printing of MaterializeInformationSchemaTableIterator with nontrivial
# table iterators.
--replace_regex $elide_metrics
explain format=tree select * from information_schema.engines WHERE ENGINE="MyISAM";

#
# INFORMATION_SCHEMA.PROCESSLIST
#

create user user3148@localhost;
grant select on *.* to user3148@localhost;
connect (con3148,localhost,user3148,,test);
connection con3148;
select user,db from information_schema.processlist;
connection default;
disconnect con3148;
drop user user3148@localhost;

#
# Bug#26174 Server Crash: INSERT ... SELECT ... FROM I_S.GLOBAL_STATUS
# in Event (see also openssl_1.test)
#
--disable_warnings
DROP TABLE IF EXISTS server_status;
DROP EVENT IF EXISTS event_status;
--enable_warnings

# Make Sure Event scheduler is ON (by default)
SELECT COUNT(*) = 1 FROM information_schema.processlist
  WHERE user = 'event_scheduler' AND command = 'Daemon';

DELIMITER $$;

CREATE EVENT event_status
 ON SCHEDULE AT NOW()
 ON COMPLETION NOT PRESERVE
 DO
BEGIN
  CREATE TABLE server_status
  SELECT variable_name
  FROM performance_schema.global_status
  WHERE variable_name LIKE 'ABORTED_CONNECTS' OR
  variable_name LIKE 'BINLOG_CACHE_DISK_USE';
END$$

DELIMITER ;$$

let $wait_timeout= 300;
let $wait_condition=select count(*) = 0 from information_schema.events where event_name='event_status';
--source include/wait_condition.inc

SELECT variable_name FROM server_status;

DROP TABLE server_status;

#
# Bug#30310 wrong result on SELECT * FROM INFORMATION_SCHEMA.SCHEMATA WHERE ..
#
SELECT * FROM INFORMATION_SCHEMA.SCHEMATA
WHERE SCHEMA_NAME = 'mysqltest';

SELECT * FROM INFORMATION_SCHEMA.SCHEMATA
WHERE SCHEMA_NAME = '';

SELECT * FROM INFORMATION_SCHEMA.SCHEMATA
WHERE SCHEMA_NAME = 'test';

select count(*) from INFORMATION_SCHEMA.TABLES where TABLE_SCHEMA='mysql' AND TABLE_NAME='nonexisting';
select count(*) from INFORMATION_SCHEMA.TABLES where TABLE_SCHEMA='mysql' AND TABLE_NAME='';
select count(*) from INFORMATION_SCHEMA.TABLES where TABLE_SCHEMA='' AND TABLE_NAME='';
select count(*) from INFORMATION_SCHEMA.TABLES where TABLE_SCHEMA='' AND TABLE_NAME='nonexisting';

#
# Bug#30689 Wrong content in I_S.VIEWS.VIEW_DEFINITION if VIEW is based on I_S
#
CREATE VIEW v1
AS SELECT *
FROM information_schema.TABLES;
--replace_result `tables` `TABLES`
SELECT VIEW_DEFINITION FROM INFORMATION_SCHEMA.VIEWS where TABLE_NAME = 'v1';
DROP VIEW v1;

#
# Bug#30795 Query on INFORMATION_SCHEMA.SCHEMATA, wrong result
#
SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA
WHERE SCHEMA_NAME ='information_schema';

#
# Bug#31381 Error in retrieving Data from INFORMATION_SCHEMA
#
SELECT TABLE_COLLATION FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA='mysql' and TABLE_NAME= 'db';

#
# Bug#31633 Information schema = NULL queries crash the server
#
select * from information_schema.columns where table_schema = NULL;
select * from `information_schema`.`COLUMNS` where `TABLE_NAME` = NULL;
select * from `information_schema`.`key_column_usage` where `TABLE_SCHEMA` = NULL;
select * from `information_schema`.`key_column_usage` where `TABLE_NAME` = NULL;
select * from `information_schema`.`PARTITIONS` where `TABLE_SCHEMA` = NULL;
select * from `information_schema`.`PARTITIONS` where `TABLE_NAME` = NULL;
select * from `information_schema`.`REFERENTIAL_CONSTRAINTS` where `CONSTRAINT_SCHEMA` = NULL;
select * from `information_schema`.`REFERENTIAL_CONSTRAINTS` where `TABLE_NAME` = NULL;
select * from information_schema.schemata where schema_name = NULL;
select * from `information_schema`.`STATISTICS` where `TABLE_SCHEMA` = NULL;
select * from `information_schema`.`STATISTICS` where `TABLE_NAME` = NULL;
select * from information_schema.tables where table_schema = NULL;
select * from information_schema.tables where table_catalog = NULL;
select * from information_schema.tables where table_name = NULL;
select * from `information_schema`.`TABLE_CONSTRAINTS` where `TABLE_SCHEMA` = NULL;
select * from `information_schema`.`TABLE_CONSTRAINTS` where `TABLE_NAME` = NULL;
--replace_column 17 #
select * from `information_schema`.`TRIGGERS` where `EVENT_OBJECT_SCHEMA` = NULL;
--replace_column 17 #
select * from `information_schema`.`TRIGGERS` where `EVENT_OBJECT_TABLE` = NULL;
select * from `information_schema`.`VIEWS` where `TABLE_SCHEMA` = NULL;
select * from `information_schema`.`VIEWS` where `TABLE_NAME` = NULL;

#
# Bug#31630 debug assert with explain select ... from i_s
#
--disable_result_log
explain select 1 from information_schema.tables;
--enable_result_log

#
# Bug#32775 problems with SHOW EVENTS and Information_Schema
#
use information_schema;
show events;
show events from information_schema;
show events where Db= 'information_schema';
use test;

--echo #
--echo # Bug#34166 Server crash in SHOW OPEN TABLES and prelocking
--echo #
--disable_warnings
drop table if exists t1;
drop function if exists f1;
--enable_warnings
create table t1 (a int);
delimiter |;
create function f1() returns int
begin
  insert into t1 (a) values (1);
  return 0;
end|
delimiter ;|
--disable_result_log
show open tables where f1()=0;
show open tables where f1()=0;
--enable_result_log
drop table t1;
drop function f1;

#
# Bug#34656 KILL a query = Assertion failed: m_status == DA_ERROR ||
#           m_status == DA_OK
#
connect (conn1, localhost, root,,);
connection conn1;
let $ID= `select connection_id()`;
send select * from information_schema.tables where 1=sleep(100000);
connection default;
let $wait_timeout= 10;
let $wait_condition=select count(*)=1 from information_schema.processlist
where state='User sleep' and
info='select * from information_schema.tables where 1=sleep(100000)';
--source include/wait_condition.inc
disable_query_log;
eval kill $ID;
enable_query_log;
disconnect conn1;
let $wait_timeout= 10;
let $wait_condition=select count(*)=0 from information_schema.processlist
where state='User sleep' and
info='select * from information_schema.tables where 1=sleep(100000)';
--source include/wait_condition.inc

connect (conn1, localhost, root,,);
connection conn1;
let $ID= `select connection_id()`;
send select * from information_schema.columns where 1=sleep(100000);
connection default;
let $wait_timeout= 10;
let $wait_condition=select count(*)=1 from information_schema.processlist
where state='User sleep' and
info='select * from information_schema.columns where 1=sleep(100000)';
--source include/wait_condition.inc
disable_query_log;
eval kill $ID;
enable_query_log;
disconnect conn1;
let $wait_timeout= 10;
let $wait_condition=select count(*)=0 from information_schema.processlist
where state='User sleep' and
info='select * from information_schema.columns where 1=sleep(100000)';
--source include/wait_condition.inc


#
# Bug#39955 SELECT on INFORMATION_SCHEMA.GLOBAL_VARIABLES takes too long
#
set global init_connect="drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;\
drop table if exists t1;drop table if exists t1;";
select * from performance_schema.global_variables where variable_name='init_connect';
set global init_connect="";

--disable_warnings
create table t0 select * from performance_schema.global_status where VARIABLE_NAME='COM_SELECT';
SELECT 1;
select a.VARIABLE_VALUE - b.VARIABLE_VALUE from t0 b, performance_schema.global_status a
   where a.VARIABLE_NAME = b.VARIABLE_NAME;
--enable_warnings
drop table t0;

#
# Bug#35275 INFORMATION_SCHEMA.TABLES.CREATE_OPTIONS omits KEY_BLOCK_SIZE
#
CREATE TABLE t1(a INT) KEY_BLOCK_SIZE=1;
SELECT CREATE_OPTIONS FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='t1';
DROP TABLE t1;

#
# Bug #22047: Time in SHOW PROCESSLIST for SQL thread in replication seems
# to become negative
#

SET TIMESTAMP=@@TIMESTAMP + 10000000;
SELECT 'OK' AS TEST_RESULT FROM INFORMATION_SCHEMA.PROCESSLIST WHERE time < 0;
SET TIMESTAMP=DEFAULT;


--echo #
--echo # Bug #50276: Security flaw in INFORMATION_SCHEMA.TABLES
--echo #
CREATE DATABASE db1;
USE db1;
CREATE TABLE t1 (id INT);
CREATE USER nonpriv;
USE test;

connect (nonpriv_con, localhost, nonpriv,,);
connection nonpriv_con;
--echo # connected as nonpriv
--echo # Should return 0
SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='t1';
USE INFORMATION_SCHEMA;
--echo # Should return 0
SELECT COUNT(*) FROM TABLES WHERE TABLE_NAME='t1';

connection default;
--echo # connected as root
disconnect nonpriv_con;
DROP USER nonpriv;
DROP TABLE db1.t1;
DROP DATABASE db1;

--echo
--echo Bug#54422 query with = 'variables'
--echo

CREATE TABLE variables(f1 INT);
SELECT COLUMN_DEFAULT, TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS
WHERE information_schema.COLUMNS.TABLE_NAME = 'variables';
DROP TABLE variables;

--echo #
--echo # Bug #53814: NUMERIC_PRECISION for unsigned bigint field is 19, 
--echo # should be 20
--echo #

CREATE TABLE ubig (a BIGINT, b BIGINT UNSIGNED);

--sorted_result
SELECT TABLE_NAME, COLUMN_NAME, NUMERIC_PRECISION 
  FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='ubig';

INSERT IGNORE INTO ubig VALUES (0xFFFFFFFFFFFFFFFF,0xFFFFFFFFFFFFFFFF);
SELECT length(CAST(b AS CHAR)) FROM ubig;

DROP TABLE ubig;


--echo End of 5.1 tests.

#
# WL#2003 INFORMATION_SCHEMA: PARAMETERS view
# WL#2822 INFORMATION_SCHEMA.ROUTINES: Add missing columns
#
create function f1 (p1 int, p2 datetime, p3 decimal(10,2))
returns char(10) return null;
create procedure p1 (p1 float(8,5), p2 char(32), p3 varchar(10)) begin end;
create procedure p2 (p1 enum('c', 's'), p2 blob, p3 text) begin end;
--sorted_result
select * from information_schema.parameters where specific_schema='test';
--sorted_result
select data_type, character_maximum_length,
       character_octet_length, numeric_precision,
       numeric_scale, character_set_name,
       collation_name, dtd_identifier
from information_schema.routines where routine_schema='test';
drop procedure p1;
drop procedure p2;
drop function f1;

--echo #
--echo # Additional test for WL#3726 "DDL locking for all metadata objects"
--echo # To avoid possible deadlocks process of filling of I_S tables should
--echo # use high-priority metadata lock requests when opening tables.
--echo # Below we just test that we really use high-priority lock request
--echo # since reproducing a deadlock will require much more complex test.
--echo #
--disable_warnings
drop tables if exists t1, t2, t3;
--enable_warnings
create table t1 (i int);
create table t2 (j int primary key auto_increment);
analyze table t1, t2;
connect (con3726_1,localhost,root,,test);
--echo # Switching to connection 'con3726_1'
connection con3726_1;
lock table t2 read;
connect (con3726_2,localhost,root,,test);
--echo # Switching to connection 'con3726_2'
connection con3726_2;
--echo # RENAME below will be blocked by 'lock table t2 read' above but
--echo # will add two pending requests for exclusive metadata locks.
--send rename table t2 to t3
--echo # Switching to connection 'default'
connection default;
let $wait_condition=
  select count(*) = 1 from information_schema.processlist
  where state = "Waiting for table metadata lock" and
        info like "rename table t2 to t3";
--source include/wait_condition.inc
--echo # These statements should not be blocked by pending lock requests
--sorted_result
select table_name, column_name, data_type from information_schema.columns
  where table_schema = 'test' and table_name in ('t1', 't2');
--sorted_result
select table_name, auto_increment from information_schema.tables
  where table_schema = 'test' and table_name in ('t1', 't2');
--echo # Switching to connection 'con3726_1'
connection con3726_1;
unlock tables;
--echo # Switching to connection 'con3726_2'
connection con3726_2;
--reap
--echo # Switching to connection 'default'
connection default;
disconnect con3726_1;
disconnect con3726_2;
drop tables t1, t3;

#
# Bug#24062 Incorrect error msg after execute DROP TABLE IF EXISTS on information_schema
#
--error ER_DBACCESS_DENIED_ERROR
create table information_schema.t1 (f1 INT);
--error ER_DBACCESS_DENIED_ERROR
drop table information_schema.t1;
--error ER_DBACCESS_DENIED_ERROR
drop temporary table if exists information_schema.t1;
--error ER_DBACCESS_DENIED_ERROR
create temporary table information_schema.t1 (f1 INT);
--error ER_DBACCESS_DENIED_ERROR
drop view information_schema.v1;
--error ER_DBACCESS_DENIED_ERROR
create view information_schema.v1;
--error ER_DBACCESS_DENIED_ERROR
create trigger mysql.trg1 after insert on information_schema.t1 for each row set @a=1;
--error 1109
create table t1 select * from information_schema.t1;

CREATE TABLE t1(f1 char(100));
--error ER_DBACCESS_DENIED_ERROR
REPAIR TABLE t1, information_schema.processlist;
CHECKSUM TABLE t1, information_schema.processlist;
--error ER_DBACCESS_DENIED_ERROR
ANALYZE TABLE t1, information_schema.processlist;
CHECK TABLE t1, information_schema.processlist;
--error ER_DBACCESS_DENIED_ERROR
OPTIMIZE TABLE t1, information_schema.processlist;
--error ER_DBACCESS_DENIED_ERROR
RENAME TABLE v1 to v2, information_schema.processlist to t2;
--error ER_DBACCESS_DENIED_ERROR
DROP TABLE t1, information_schema.processlist;

--error ER_DBACCESS_DENIED_ERROR
LOCK TABLES t1 READ, information_schema.processlist READ;
DROP TABLE t1;

#
# Bug#38916 Select from I_S.ROUTINES results in "No database selected" error
#
create function f1() returns int return 1;
--connect (con7,localhost,root,,*NO-ONE*)
select routine_name, routine_type from information_schema.routines
where routine_schema = 'test';
connection default;
drop function f1;
disconnect con7;


#
# Bug #43834    Assertion in Natural_join_column::db_name() on an I_S query
#

SELECT *
FROM INFORMATION_SCHEMA.key_column_usage
LEFT JOIN INFORMATION_SCHEMA.COLUMNS
USING (TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME)
WHERE COLUMNS.TABLE_SCHEMA = 'test'
AND COLUMNS.TABLE_NAME = 't1';


--echo #
--echo # A test case for Bug#56540 "Exception (crash) in sql_show.cc
--echo # during rqg_info_schema test on Windows"
--echo # Ensure that we never access memory of a closed table,
--echo # in particular, never access table->field[] array.
--echo # Before the fix, the below test case, produced
--echo # valgrind errors.
--echo #

--disable_warnings
drop table if exists t1;
drop view if exists v1;
--enable_warnings

create table t1 (a int, b int);
create view v1 as select t1.a, t1.b from t1;
alter table t1 change b c int;
lock table t1 read;
connect(con1, localhost, root,,);
--echo # --> connection con1
connection con1;
send flush tables;
--echo # --> connection default
connection default;
let $wait_condition=
  select count(*) = 1 from information_schema.processlist
    where state = "Waiting for table flush" and
          info = "flush tables";
--source include/wait_condition.inc
--vertical_results
select * from information_schema.views
  where table_schema != 'sys' order by table_schema, table_name;
--horizontal_results
unlock tables;

--echo #
--echo # Cleanup.
--echo #

--echo # --> connection con1
connection con1;
--echo # Reaping 'flush tables'
reap;
disconnect con1;
--source include/wait_until_disconnected.inc
--echo # --> connection default
connection default;
drop table t1;
drop view v1;


--echo #
--echo # Test for bug #12828477 - "MDL SUBSYSTEM CREATES BIG OVERHEAD FOR
--echo #                           CERTAIN QUERIES TO INFORMATION_SCHEMA".
--echo #
--echo # Check that metadata locks which are acquired during the process
--echo # of opening tables/.FRMs/.TRG files while filling I_S table are
--echo # not kept to the end of statement. Keeping the locks has caused
--echo # performance problems in cases when big number of tables (.FRMs
--echo # or .TRG files) were scanned as cost of new lock acquisition has
--echo # increased linearly.
--echo #
--echo # With WL#9494, queries using I_S.TRIGGERS will not open the under
--echo # lying tables. The I_S query will not take metadata locks on
--echo # table owning the triggers and hence the query will not be blocked.
--echo #
--disable_warnings
drop database if exists mysqltest;
--enable_warnings
create database mysqltest;
use mysqltest;
create table t0 (i int);
create table t1 (j int);
create table t2 (k int);

--echo #
--echo # Test that we don't keep locks in case when we to fill
--echo # I_S table we read .TRG file only (this was the case
--echo # for which problem existed).
--echo #

--echo # Acquire lock on 't2' so upcoming RENAME is
--echo # blocked.
lock tables t2 read;

--echo #
--echo # Switching to connection 'con12828477_1'. 
--echo #
connect (con12828477_1, localhost, root,,mysqltest);
--echo # The below RENAME should wait on 't2' while
--echo # keeping X lock on 't1'.
--send rename table t1 to t3, t2 to t1, t3 to t2

--echo #
--echo # Switching to connection 'con12828477_2'. 
--echo #
connect (con12828477_2, localhost, root,,mysqltest);
--echo # Wait while the above RENAME is blocked.
let $wait_condition=
  select count(*) = 1 from information_schema.processlist
  where state = "Waiting for table metadata lock" and
        info = "rename table t1 to t3, t2 to t1, t3 to t2";
--source include/wait_condition.inc

SET SESSION information_schema_stats_expiry=0;
--echo # Without WL9494, issuing a query to I_S will open 't0' and get
--echo # blocked on 't1' because of RENAME. With WL9494, I_S query is just
--echo # a scan over dictionary tables and do not really open the table,
--echo # so the below query does not block.
--send select event_object_table, trigger_name from information_schema.triggers where event_object_schema='mysqltest'

--echo #
--echo # Switching to connection 'con12828477_3'. 
--echo #
connect (con12828477_3, localhost, root,,mysqltest);
--echo # Wait while the above SELECT is blocked.
let $wait_condition=
  select count(*) = 0 from information_schema.processlist
  where state = "Waiting for table metadata lock" and
        info = "select event_object_table, trigger_name from information_schema.triggers where event_object_schema='mysqltest'";
--source include/wait_condition.inc

--echo #
--echo # Check that it holds no lock on 't0' so it can be renamed.
rename table t0 to t4;

--echo #
--echo # Switching to connection 'default'.
--echo #
connection default;
--echo #
--echo # Unblock the first RENAME.
unlock tables;

--echo #
--echo # Switching to connection 'con12828477_1'. 
--echo #
connection con12828477_1;
--echo # Reap the first RENAME
--reap

--echo #
--echo # Switching to connection 'con12828477_2'. 
--echo #
connection con12828477_2;
--echo # Reap SELECT to I_S.
--reap

--echo #
--echo # Switching to connection 'default'.
--echo #
connection default;
disconnect con12828477_1;
disconnect con12828477_2;
disconnect con12828477_3;

--echo #
--echo # Test case to test DATETIME_PRECISION of information_schema.columns table
--echo # 
--disable_warnings
drop database if exists mysqltest;
--enable_warnings
create database mysqltest;
use mysqltest;
create table mysqltest.t(a int, b date, c time, d datetime, e timestamp);
create table mysqltest.t0(a int, b date, c time(0), d datetime(0), e timestamp(0));
create table mysqltest.t1(a int, b date, c time(1), d datetime(1), e timestamp(1));
create table mysqltest.t2(a int, b date, c time(2), d datetime(2), e timestamp(2));
create table mysqltest.t3(a int, b date, c time(3), d datetime(3), e timestamp(3));
create table mysqltest.t4(a int, b date, c time(4), d datetime(4), e timestamp(4));
create table mysqltest.t5(a int, b date, c time(5), d datetime(5), e timestamp(5));
create table mysqltest.t6(a int, b date, c time(6), d datetime(6), e timestamp(6));
select TABLE_NAME,COLUMN_NAME,DATA_TYPE,DATETIME_PRECISION from
information_schema.columns where TABLE_SCHEMA='mysqltest' order by table_name, column_name;

--echo #
--echo # Clean-up.
drop database mysqltest;
use test;

--echo #
--echo # Test for bug #16869534 - "QUERYING SUBSET OF COLUMNS DOESN'T USE TABLE
--echo #                           CACHE; OPENED_TABLES INCREASES"
--echo #
--disable_result_log
SELECT * FROM INFORMATION_SCHEMA.TABLES;
--enable_result_log
--disable_warnings
SELECT VARIABLE_VALUE INTO @val1 FROM performance_schema.global_status WHERE
  VARIABLE_NAME LIKE 'Opened_tables';
--enable_warnings
--disable_result_log
SELECT ENGINE FROM INFORMATION_SCHEMA.TABLES;
--enable_result_log
--disable_warnings
--echo # The below SELECT query should give same output as above SELECT query.
SELECT VARIABLE_VALUE INTO @val2 FROM performance_schema.global_status WHERE
  VARIABLE_NAME LIKE 'Opened_tables';
--enable_warnings
--echo # The below select should return '1'
SELECT @val1 = @val2;

--echo #
--echo # End of 5.5 tests
--echo #

--echo #
--echo # Bug #13966514 : CRASH IN GET_SCHEMA_TABLES_RESULT WITH MIN/MAX, 
--echo #   LEFT/RIGHT JOIN ON I_S TABLE
--echo #

CREATE TABLE t1(a INT PRIMARY KEY);
INSERT INTO t1 VALUES (1);
--echo # must not crash
SELECT MAX(a) FROM information_schema.engines RIGHT JOIN t1 ON 1;
DROP TABLE t1;

--echo #
--echo # BUG#13463397 - 63562: UNKNOWN DATABASE INFORMATION_SCHEMA 
--echo #

--error ER_DBACCESS_DENIED_ERROR
CREATE PROCEDURE information_schema.is() BEGIN END;


--echo #
--echo # Bug#26877788 SELECT FROM INFORMATION_SCHEMA.FILES RETURNS NO RECORDS WHEN ORDER BY IS USED
--echo #

SELECT ENGINE, SUPPORT, TRANSACTIONS FROM INFORMATION_SCHEMA.ENGINES
WHERE
SUPPORT IN (
SELECT DISTINCT SUPPORT
FROM INFORMATION_SCHEMA.ENGINES
WHERE
ENGINE IN (
SELECT DISTINCT ENGINE FROM INFORMATION_SCHEMA.ENGINES
WHERE ENGINE IN ('MEMORY')))
ORDER BY ENGINE
LIMIT 1;


--echo #
--echo # End of 5.6 tests
--echo #

# Wait till all disconnects are completed
--source include/wait_until_count_sessions.inc

--echo #
--echo # Bug#20665051 SQL_SHOW.CC:7764: ASSERTION `QEP_TAB->CONDITION() == QEP_TAB->CONDITION_OPTIM()
--echo #

let query=
SELECT 1
FROM DUAL
WHERE (SELECT 1 FROM information_schema.tables
        WHERE table_schema
        ORDER BY table_name
        LIMIT 1);
--disable_result_log
eval EXPLAIN $query;
--enable_result_log
--disable_warnings
eval $query;
--enable_warnings

let $query=
SELECT 1 AS F1 FROM information_schema.tables
WHERE "COLUMN_PRIVILEGES"=
      (SELECT CAST(TABLE_NAME AS CHAR)
      FROM information_schema.tables
      WHERE table_schema != 'PERFORMANCE_SCHEMA'
      ORDER BY table_name LIMIT 1)
LIMIT 1;

--disable_result_log
eval EXPLAIN $query;
--enable_result_log
eval $query;

--echo #
--echo # WL#2284: Increase the length of a user name
--echo #

connection default;

--character_set utf8mb3
set names utf8mb3;

CREATE USER user_name_len_22_01234@localhost;
GRANT SELECT ON *.* TO user_name_len_22_01234@localhost;
connect (con_user22,localhost,user_name_len_22_01234,,test);
connection con_user22;
SELECT user,db FROM information_schema.processlist;
connection default;

# 32 characters user name
CREATE USER очень_очень_очень_длинный_юзер__@localhost;
GRANT SELECT ON *.* TO очень_очень_очень_длинный_юзер__@localhost;
connect (con_user32_utf8,localhost,очень_очень_очень_длинный_юзер__,,test);
connection con_user32_utf8;
SELECT user,db FROM information_schema.processlist;
connection default;

# cleanup
disconnect con_user22;
disconnect con_user32_utf8;
DROP USER user_name_len_22_01234@localhost;
DROP USER очень_очень_очень_длинный_юзер__@localhost;
set names default;

SET SESSION information_schema_stats_expiry=default;
set sql_mode= @orig_sql_mode;

--echo #
--echo # WL#6599: New Data Dictionary and I_S Integration.
--echo #

CREATE VIEW v1 AS SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_NAME= "users";
LOCK TABLE v1 READ;

--enable_connect_log
connect(con1, localhost, root,,);
send FLUSH TABLES;

connection default;
let $wait_condition=
  SELECT COUNT(*) = 1 FROM INFORMATION_SCHEMA.PROCESSLIST
  WHERE state = "Waiting for table flush" AND
  info = "flush tables";
--source include/wait_condition.inc

SELECT * FROM v1;
UNLOCK TABLES;

connection con1;
reap;
disconnect con1;
--source include/wait_until_disconnected.inc

--echo # Clean up.
connection default;
DROP VIEW v1;
--disable_connect_log


--echo #
--echo # WL#6599: New Data Dictionary and I_S Integration.
--echo #
--echo # Test case to check if ndbinfo schema is listed by I_S when ndbcluster
--echo # is not running.
--echo #
#'ndbinfo' schema should not be listed when ndbcluster is not running.
SELECT schema_name FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = 'ndbinfo';
# tables of ndbinfo schema should not be listed.
SELECT table_name FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'ndbinfo';


--echo #
--echo # WL#6599: New Data Dictionary and I_S Integration.
--echo #
--echo # Test case to check INFORMATION_SCHEMA.COLUMNS.COLUMN_KEY
--echo # value if reported for FULLTEXT and SPATIAL index.
--echo #
CREATE TABLE t1 (
c1 INT,
c2 INT,
c3 CHAR(255),
c4 CHAR(255),
c5 CHAR(255),
c6 POINT NOT NULL,
c7 GEOMETRY NOT NULL SRID 0,
SPATIAL INDEX(c7),
KEY c2_key (c2),
FULLTEXT KEY c3_fts (c3),
FULLTEXT KEY c4_fts (c4, c5));

SELECT COLUMN_NAME, IS_NULLABLE,
       DATA_TYPE, COLLATION_NAME, COLUMN_KEY
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_SCHEMA='test' ORDER BY COLUMN_NAME;
SHOW COLUMNS FROM t1;

DROP TABLE t1;

--echo #
--echo # Bug#24749248 - NATIVE METHODS INTRODUCED FOR I_S SHOULD NOT BE USED BY A USER DIRECTLY.
--echo #

--echo # Case 1: Invoking I_S native methods directly by a user should not be
--echo #         allowed.
--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_TABLE_ROWS(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_AVG_ROW_LENGTH(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_DATA_LENGTH(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_MAX_DATA_LENGTH(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_INDEX_LENGTH(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_DATA_FREE(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_AUTO_INCREMENT(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_UPDATE_TIME(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_CHECK_TIME(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_CHECKSUM(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_DD_CHAR_LENGTH(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_INDEX_COLUMN_CARDINALITY(NULL, NULL, NULL, NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT GET_DD_INDEX_SUB_PART_LENGTH(NULL, NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT GET_DD_COLUMN_PRIVILEGES(NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_GET_VIEW_WARNING_OR_ERROR(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_GET_COMMENT_OR_ERROR(NULL, NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT INTERNAL_KEYS_DISABLED(NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT CAN_ACCESS_DATABASE(NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT CAN_ACCESS_TABLE(NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT CAN_ACCESS_VIEW(NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT CAN_ACCESS_COLUMN(NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT GET_DD_CREATE_OPTIONS(NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT CAN_ACCESS_TRIGGER(NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT CAN_ACCESS_ROUTINE(NULL, NULL, NULL, NULL, NULL);

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT CAN_ACCESS_EVENT(NULL);

CREATE TABLE t1(f1 INT);
--error ER_NO_ACCESS_TO_NATIVE_FCT
CREATE TABLE t3 AS SELECT CAN_ACCESS_TABLE("test", "t1");

--error ER_NO_ACCESS_TO_NATIVE_FCT
CREATE VIEW v2 AS SELECT CAN_ACCESS_TABLE("test", "t1");

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT * FROM t1 WHERE CAN_ACCESS_TABLE("test", "t1") = 1;

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT * FROM INFORMARTION_SCHEMA.TABLES WHERE CAN_ACCESS_TABLE("test", "t1") = 1;

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT CAN_ACCESS_TABLE("test", "t1") AS f1, COLUMN_NAME AS F2  FROM INFORMATION_SCHEMA.COLUMNS;

--error ER_NO_ACCESS_TO_NATIVE_FCT
PREPARE stmt FROM 'SELECT CAN_ACCESS_TABLE("test", "t1") AS f1, COLUMN_NAME AS F2  FROM INFORMATION_SCHEMA.COLUMNS';

--error ER_NO_ACCESS_TO_NATIVE_FCT
SELECT CAN_ACCESS_USER(NULL, NULL);

--echo # Case 2: Invoking I_S native methods should be allowed through I_S queries.
--replace_column 6 # 7 # 8 # 9 # 10 # 11 # 12 # 13 # 14 # 15 # 16 # 17 # 18 # 19 # 20 # 21 #
SELECT * FROM INFORMATION_SCHEMA.TABLES where table_name='t1';

SELECT t.table_schema, t.table_name, c.column_name
  FROM INFORMATION_SCHEMA.TABLES t,
       INFORMATION_SCHEMA.COLUMNS c
       WHERE t.table_schema = c.table_schema
       AND t.table_name = c.table_name
       AND t.table_name = 't1' limit 1;

CREATE VIEW v1 AS
SELECT t.table_schema, t.table_name, c.column_name
  FROM INFORMATION_SCHEMA.TABLES t,
       INFORMATION_SCHEMA.COLUMNS c
       WHERE t.table_schema = c.table_schema
       AND t.table_name = c.table_name
       AND t.table_name = 't1' limit 1;
SHOW CREATE VIEW v1;

CREATE TABLE t2 AS
SELECT t.table_schema, t.table_name, c.column_name
  FROM INFORMATION_SCHEMA.TABLES t,
       INFORMATION_SCHEMA.COLUMNS c
       WHERE t.table_schema = c.table_schema
       AND t.table_name = c.table_name
       AND t.table_name = 't1' limit 1;
SHOW CREATE TABLE t2;

# Cleanup
DROP TABLE t1, t2;
DROP VIEW v1;

--echo #
--echo # WL#8582 INFORMATION_SCHEMA.ST_GEOMETRY_COLUMNS
--echo #

SELECT * FROM information_schema.ST_GEOMETRY_COLUMNS
  WHERE TABLE_SCHEMA='test' AND TABLE_NAME='t1';
CREATE TABLE t1(g GEOMETRY, pt POINT, ls LINESTRING, py POLYGON, mpt MULTIPOINT,
  mls MULTILINESTRING, mpy MULTIPOLYGON, gc GEOMETRYCOLLECTION);
SELECT * FROM information_schema.ST_GEOMETRY_COLUMNS
  WHERE TABLE_SCHEMA='test' AND TABLE_NAME='t1'
  ORDER BY COLUMN_NAME;
DROP TABLE t1;

--echo #
--echo # BUG#24751177: ASSERTION `STRLEN(DB_NAME) <= (64*3) &&
--echo #               STRLEN(TABLE_NAME) <= (64*3)' FAILED
--echo #

--echo # Without patch, the below queries triggers an assertion.
--error ER_WRONG_TABLE_NAME
DESCRIBE `aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaa`;

--error ER_WRONG_TABLE_NAME
EXPLAIN `aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaa`;

--error ER_TOO_LONG_IDENT
SHOW KEYS FROM `aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaa`;

--error ER_TOO_LONG_IDENT
SHOW COLUMNS FROM `aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaa`;

--error ER_WRONG_DB_NAME
DESCRIBE `aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaa`.`t1`;

--error ER_WRONG_DB_NAME
EXPLAIN `aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaa`.`t1`;

--error ER_TOO_LONG_IDENT
SHOW KEYS FROM `aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaa`.`t1`;

--error ER_TOO_LONG_IDENT
SHOW COLUMNS FROM `aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
aaaaaaaaaaaaaaaa`.`t1`;

--echo #
--echo # Bug#25583588 `M_PREBUILT->TABLE->N_REF_COUNT > 0' AT
--echo # HA_INNOBASE::UPDATE_THD IN HANDLER
--echo #
--echo # This test would crash especially with a FULLTEXT
--echo # index and hidden FTS tables that are created by InnoDB.
--echo #

CREATE TABLE t1 (a VARCHAR(200), b TEXT, FULLTEXT (a,b))
ENGINE = InnoDB charset utf8mb4;
SELECT * FROM INFORMATION_SCHEMA.`REFERENTIAL_CONSTRAINTS`;
DROP TABLE t1;


--echo #
--echo # Bug#25576205 WRONG COLUMN CARDINALITY PASSED TO GET STATS
--echo #
--echo # The test case crashes if server requests for cardinality for
--echo # InnoDB SE even for hidden index.
--echo #

SET SESSION information_schema_stats_expiry= 0;
SELECT INDEX_NAME FROM INFORMATION_SCHEMA.`STATISTICS` WHERE `TABLE_NAME` = 'innodb_table_stats' AND Cardinality ;
SET SESSION information_schema_stats_expiry= default;

--echo #
--echo # Bug#25188540 FIELD_TINY::VAL_INT(): ASSERTION `!TABLE || (!TABLE->READ_SET |......)' FAILED
--echo #

CREATE TABLE t1 (a INT);
SET SESSION optimizer_switch='derived_merge=off';
SHOW FIELDS FROM t1;
SET SESSION optimizer_switch=DEFAULT;
DROP TABLE t1;

--echo #
--echo # Bug#25824297 INFORMATION_SCHEMA.CHARACTER_SETS DESCRIPTION COLUMN IS
--echo # CASE SENSITIVE IN 8.0
--echo # The following SELECT does case sensitive comparision without the
--echo # fix, returning no rows.
--echo #

SELECT * FROM INFORMATION_SCHEMA.CHARACTER_SETS WHERE DESCRIPTION LIKE '%japanese%';

--echo # WL#9495 Update schema tables of dynamic plugins
--echo # into data dictionary.
--echo #
--echo # Make sure user does not access following
--echo # INFORMATION_SCHEMA tables:
--echo #   SHOW_STATISTICS,
--echo #
--error ER_NO_SYSTEM_VIEW_ACCESS
SELECT * FROM INFORMATION_SCHEMA.SHOW_STATISTICS;

--echo #
--echo # Bug#25793429 CAN'T RETURN
--echo # INFORMATION_SCHEMA.TABLES.CREATE_OPTION IN UPPERCASE
--echo #
--echo # The UPPER() function fails to convert strings without the fix.
--echo #
CREATE TABLE t1 (a CHAR(40) NOT NULL, UNIQUE idx1(a(2)))
  COMMENT="testing a fix" MAX_ROWS=10;
SELECT create_options, UPPER(create_options),
       table_comment, UPPER(table_comment)
  FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 't1';
SELECT privileges, UPPER(privileges)
  FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 't1';
DROP TABLE t1;


--echo #
--echo # Bug#18925: subqueries with MIN/MAX functions on INFORMARTION_SCHEMA 
--echo #

SELECT t.table_name, c1.column_name
  FROM information_schema.tables t
       INNER JOIN
       information_schema.columns c1
       ON t.table_schema = c1.table_schema AND
          t.table_name = c1.table_name
  WHERE t.table_schema = 'information_schema' AND
        c1.ordinal_position =
        ( SELECT COALESCE(MIN(c2.ordinal_position),1)
            FROM information_schema.columns c2
            WHERE c2.table_schema = t.table_schema AND
                  c2.table_name = t.table_name AND
                  c2.column_name LIKE '%SCHEMA%'
        )
  AND t.table_name NOT LIKE 'ndb%'
  AND t.table_name NOT LIKE 'INNODB%'
  ORDER BY t.table_name COLLATE utf8mb3_general_ci,
           c1.column_name COLLATE utf8mb3_general_ci;

SELECT t.table_name, c1.column_name
  FROM information_schema.tables t
       INNER JOIN
       information_schema.columns c1
       ON t.table_schema = c1.table_schema AND
          t.table_name = c1.table_name
  WHERE t.table_schema = 'information_schema' AND
        c1.ordinal_position =
        ( SELECT COALESCE(MIN(c2.ordinal_position),1)
            FROM information_schema.columns c2
            WHERE c2.table_schema = 'information_schema' AND
                  c2.table_name = t.table_name AND
                  c2.column_name LIKE '%SCHEMA%'
        )
  AND t.table_name NOT LIKE 'ndb%'
  AND t.table_name NOT LIKE 'INNODB%'
  ORDER BY t.table_name COLLATE utf8mb3_general_ci,
           c1.column_name COLLATE utf8mb3_general_ci;


--echo #
--echo # BUG#24679166: TABLE CREATION WITH BINARY TYPE COLUMN IS RESULTING IN
--echo #               ASSERT CONDITION FAILURE.

--echo # Without patch, the server exits in debug build and
--echo # an error is reported in release build.
CREATE TABLE t1(fld1 BINARY(10) NOT NULL DEFAULT 'a',
                fld2 BINARY(10) NOT NULL DEFAULT 0xAA,
                fld3 BINARY(10) NOT NULL DEFAULT 0xA,
                fld4 BINARY(10) NOT NULL DEFAULT b'1001',
                fld5 VARBINARY(5) NOT NULL DEFAULT 'a',
                fld6 VARBINARY(5) NOT NULL DEFAULT 0xAA,
                fld7 VARBINARY(5) NOT NULL DEFAULT 0xA,
                fld8 VARBINARY(5) NOT NULL DEFAULT b'1001');

--echo # With the patch, default value is displayed in the hex format.
SHOW COLUMNS from t1;
--sorted_result
SELECT COLUMN_DEFAULT FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME= 't1';

--echo # Cleanup.
DROP TABLE t1;

--echo #
--echo # BUG#25185947 -- issues with renaming root user
--echo #

RENAME USER root@localhost TO master@localhost;
FLUSH PRIVILEGES;

--echo # Without patch, SHOW DATABASES returns error ERROR 1449 (HY000):
--echo # The user specified as a definer ('root'@'localhost') does not exist
SHOW DATABASES;

RENAME USER master@localhost TO root@localhost;
FLUSH PRIVILEGES;


--echo #
--echo # Bug#27041452 assertion
--echo # `ticket->m_lock->m_obtrusive_locks_granted_waiting_count != 0' failed.
--echo #

CREATE TABLE t1(c1 INT);
LOCK TABLES t1 WRITE;

--echo # The following statement asserts while trying to acquire lock
--echo # on TABLESPACE, without the fix.
SELECT file_name, initial_size!=0 FROM information_schema.files
  WHERE tablespace_name='test/t1';
UNLOCK TABLES;

DROP TABLE t1;

--echo #
--echo # Bug#27041526 assertion `mon > 0 && mon < 13' failed.
--echo #

CREATE TABLE t1(c1 int);
SET TIMESTAMP=UNIX_TIMESTAMP('2017-11-20 10:44:01');
SET SESSION TIME_ZONE='-10:00';
SHOW VARIABLES LIKE 'collation_connection';
SELECT table_rows FROM information_schema.tables
  WHERE table_name='t1';

--echo # This SELECT does not return any row without the fix.
SELECT table_rows FROM information_schema.tables
  WHERE table_name='t1' AND table_rows>=0;

--echo # The following statement asserts or throws a warning without the fix.
SET SESSION collation_connection='utf32_general_ci';
SELECT table_rows FROM information_schema.tables WHERE table_name='t1';
DROP TABLE t1;
SET SESSION TIMESTAMP=default;
SET SESSION collation_connection=default;

--echo #
--echo # Bug#27041323 INNODB: ASSERTION FAILURE:
--echo # REM0REC.CC:372:LEN <= COL->LEN || ((COL->MTYPE)==5...
--echo #

CREATE TABLE t1(c1 INT,c2 CHAR (1)COMMENT'')
COMMENT='abcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghi
jabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefg
hijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcde
fghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabc
defghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghija
bcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghi
jabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefg
hijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcde
fghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabc
defghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghija
bcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghi
jabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefg
hijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcdefghijabcde
fghijabcdefghijabcde';
SET big_tables=1;
SET character_set_connection=ucs2;

--echo # The below statement asserts or shows comment text in ucs2 instead of
--echo # using system charset, when run without the fix.
--replace_column 7 # 8 # 12 # 13 #
SHOW TABLE STATUS;

SET big_tables=default;
SET character_set_connection=default;
DROP TABLE t1;

--echo #
--echo # Bug#27945704 UNABLE TO JOIN TABLE_CONSTRAINTS AND REFERENTIAL_CONSTRAINTS
--echo #

SELECT COUNT(*) = 0
  FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS AS rcons
  LEFT OUTER JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS AS tcons
  ON tcons.constraint_catalog = rcons.constraint_catalog AND
  tcons.constraint_schema = rcons.constraint_schema AND
  tcons.constraint_name = rcons.unique_constraint_name;

--echo #
--echo # Bug#27729859 QUERYING THE KEYWORDS TABLE FAILS UNLESS THERE IS A
--echo #              DEFAULT DATABASE
--echo #

--echo # Connect without a schema name, and see that we can query the KEYWORDS
--echo # view.
connect (conn1,localhost,root,,*NO-ONE*);
connection conn1;
SELECT COUNT(*) > 0 FROM INFORMATION_SCHEMA.KEYWORDS;
connection default;
disconnect conn1;

--echo #
--echo # Bug#28499603 SHOW INDEXES PRINTS WRONG OUTPUT FOR PREFIX INDEXES
--echo #
CREATE TABLE t1 (col1 BLOB(33), INDEX (col1(8)));
SHOW INDEXES FROM t1;
SELECT SUB_PART FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_NAME = "t1";
DROP TABLE t1;

CREATE TABLE t1 (col1 TEXT(33) CHARACTER SET utf8mb4, INDEX (col1(2)));
SHOW INDEXES FROM t1;
SELECT SUB_PART FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_NAME = "t1";
DROP TABLE t1;

CREATE TABLE t1 (col1 BLOB(33), INDEX (col1(33)));
SHOW INDEXES FROM t1;
SELECT SUB_PART FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_NAME = "t1";
DROP TABLE t1;

CREATE TABLE t1 (col1 TEXT(2) CHARACTER SET utf8mb4, INDEX (col1(2)));
SHOW INDEXES FROM t1;
SELECT SUB_PART FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_NAME = "t1";
DROP TABLE t1;

--echo #
--echo # Bug#29014272  THE TABLE COMMENT IS CUT DOWN ON SELECTING WITH ORDER BY
--echo #

CREATE DATABASE db1;
CREATE TABLE db1.t1 ( id int(11) DEFAULT NULL) ENGINE = INNODB COMMENT =
'123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234__100_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234__200_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234__300_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234__400_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234__500_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234__600_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234__700_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234__800_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234__900_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234_1000_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234_1100_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234_1200_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234_1300_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234_1400_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234_1500_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234_1600_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234_1700_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234_1800_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234_1900_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_123456789_1234_2000_123456789_123456789_123456789_123456789_123_2048';

--echo # This query would show just 256 characters of comments, without the fix.
SELECT TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES
  WHERE TABLE_NAME = 't1' ORDER BY TABLE_SCHEMA;
DROP DATABASE db1;

--echo #
--echo # Bug#29031684 HANDLE_FATAL_SIGNAL (SIG=11) IN
--echo # SQL_CONDITION::MESSAGE_TEXT
--echo #

--echo # Create a invalid view.
CREATE TABLE t1 (f1 TIMESTAMP);
CREATE VIEW v1 AS SELECT 1 FROM t1 WHERE f1;
ALTER TABLE t1 CHANGE COLUMN f1 f0 BINARY;

--echo # Check warning from information_schema.
--sorted_result
SELECT TABLE_NAME, LENGTH(VIEW_DEFINITION) > 0
  FROM INFORMATION_SCHEMA.VIEWS
  WHERE TABLE_SCHEMA='test';

--echo # The SELECT causes unexpected error without the fix.
SET SESSION max_error_count=0;
--sorted_result
SELECT TABLE_NAME, LENGTH(VIEW_DEFINITION) > 0
  FROM INFORMATION_SCHEMA.VIEWS
  WHERE TABLE_SCHEMA='test';
SHOW WARNINGS;
SET SESSION max_error_count=default;

DROP VIEW v1;
DROP TABLE t1;

--echo #
--echo # Bug#28901919 INFORMATION_SCHEMA.TABLES COPIES
--echo # TABLE_COMMENTS TO OTHER TABLES
--echo #

--echo # Create a invalid view.
CREATE TABLE t1 (f1 TIMESTAMP);
CREATE VIEW v1 AS SELECT 1 FROM t1 WHERE f1;
CREATE VIEW v2 AS SELECT 1 FROM t1 WHERE f1;
ALTER TABLE t1 CHANGE COLUMN f1 f0 BINARY;

--echo # The TABLE_COMMENT shows error message with view name 'v1'
--echo # for view 'v2' without the fix.
--sorted_result
SELECT TABLE_NAME, TABLE_COMMENT
  FROM INFORMATION_SCHEMA.TABLES
  WHERE TABLE_SCHEMA='test';

--echo # The behavior with max_error_count=0 should be unchanged after fix.
SET SESSION max_error_count=0;
--sorted_result
SELECT TABLE_NAME, TABLE_COMMENT
  FROM INFORMATION_SCHEMA.TABLES
  WHERE TABLE_SCHEMA='test';
SHOW WARNINGS;
SET SESSION max_error_count=default;

DROP VIEW v1, v2;
DROP TABLE t1;

--echo #
--echo # WL#12984 INFORMATION_SCHEMA and metadata related to secondary engine.
--echo #

--echo # Verify that I_S.COLUMNS.EXTRA shows the 'NOT SECONDARY' clause.

CREATE TABLE t1 (f1 DATE NOT SECONDARY);
SELECT TABLE_NAME, COLUMN_NAME, EXTRA FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_NAME='t1';
DROP TABLE t1;

CREATE TABLE t1 (f1 DATE);
SELECT TABLE_NAME, COLUMN_NAME, EXTRA FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_NAME='t1';
ALTER TABLE  t1 MODIFY f1 DATE NOT SECONDARY;
SELECT TABLE_NAME, COLUMN_NAME, EXTRA FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_NAME='t1';
DROP TABLE t1;

--echo # Verify that I_S.TABLES.CREATE_OPTIONS shows 'SECONDARY_ENGINE' clause.

CREATE TABLE t1 (f1 INT) SECONDARY_ENGINE=myisam;
SELECT TABLE_NAME, CREATE_OPTIONS
  FROM INFORMATION_SCHEMA.TABLES
  WHERE TABLE_NAME = 't1';
DROP TABLE t1;

CREATE TABLE t1 (f1 INT);
SELECT TABLE_NAME, CREATE_OPTIONS FROM INFORMATION_SCHEMA.TABLES
  WHERE TABLE_NAME = 't1';
ALTER TABLE t1 SECONDARY_ENGINE=myisam;
SELECT TABLE_NAME, CREATE_OPTIONS FROM INFORMATION_SCHEMA.TABLES
  WHERE TABLE_NAME = 't1';
DROP TABLE t1;

--echo #
--echo # BUG#29406053: OPTIMIZER_SWITCH DERIVED_MERGE=OFF CAUSES TABLE COMMENTS
--echo #               "... IS NOT BASE TABLE"
--echo #

let $query =
  SELECT TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, TABLE_COMMENT
  FROM INFORMATION_SCHEMA.TABLES
  WHERE TABLE_SCHEMA = 'sys' AND TABLE_NAME = 'sys_config';

SET OPTIMIZER_SWITCH='DERIVED_MERGE=ON';
--eval $query
--eval $query

SET OPTIMIZER_SWITCH='DERIVED_MERGE=OFF';
--eval $query
--echo # Without the patch, TABLE_COMMENT field in INFORMATION_SCHEMA.TABLES will
--echo # contain error information mentioning that one of the views in sys schema
--echo # is not a BASE TABLE.
--eval $query

# For views, all the columns in I_S.TABLES except TABLE_CATALOG, TABLE_SCHEMA,
# TABLE_NAME, TABLE_TYPE, CREATE_TIME and TABLE_COMMENT are NULL.
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES(1);
CREATE VIEW v1 AS SELECT * FROM t1;
ANALYZE TABLE t1;

--replace_column 15 #
SELECT * FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = 'test' AND TABLE_NAME = 'v1';

SET OPTIMIZER_SWITCH=default;
DROP VIEW v1;
DROP TABLE t1;


--echo #
--echo # Bug#30476213 ASSERTION TEMPTABLE..HANDLER..GET_ERROR_MESSAGE
--echo #

SET @@SESSION.optimizer_switch="derived_merge=off";
LOCK TABLE mysql.user READ;
--echo # Without the fix, this statement fails.
SELECT * FROM information_schema.ST_GEOMETRY_COLUMNS;
UNLOCK TABLES;


--echo #
--echo # Bug#30764651 ASSERTION "TEMPTABLE..HANDLER..INIT_TABLE_HANDLE_FOR_HANDLER"
--echo #

CREATE TABLE t1 (f1 INT);
FLUSH TABLES t1 FOR EXPORT;
--echo # Without the fix, this statement fails in debug build.
SELECT * FROM INFORMATION_SCHEMA.APPLICABLE_ROLES;
UNLOCK TABLES;
DROP TABLE t1;

--echo #
--echo # Bug #30624990 NO UTF8MB3 IN INFORMATION_SCHEMA.CHARACTER_SETS
--echo #

SELECT * FROM information_schema.CHARACTER_SETS
WHERE CHARACTER_SET_NAME LIKE 'utf8%'
ORDER BY character_set_name;

SHOW CHARACTER SET LIKE 'utf8%';

--echo
--echo Bug #33787300 Rename utf8_xxx collations to utf8mb3_xxx
--echo

SELECT * FROM information_schema.COLLATIONS
WHERE COLLATION_NAME LIKE "utf8\_%"
ORDER BY collation_name;

SELECT * FROM information_schema.COLLATIONS
WHERE COLLATION_NAME LIKE "utf8mb3\_%"
ORDER BY collation_name;

SHOW COLLATION LIKE 'utf8\_%';
SHOW COLLATION LIKE 'utf8mb3\_%';
