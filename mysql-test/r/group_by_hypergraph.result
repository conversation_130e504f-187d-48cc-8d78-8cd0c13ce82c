#
# Bug#32980875: WL#14422: ASSERTION `FALSE' FAILED|SQL/BASIC_ROW_ITERATORS.H
#
CREATE TABLE t (x INTEGER);
INSERT INTO t VALUES (1), (2), (3);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=TREE
SELECT DISTINCT MIN(t2.x), t1.x
FROM t t1 JOIN t t2 USING (x)
GROUP BY t2.x;
EXPLAIN
-> Remove duplicates from input grouped on t1.x, min(t2.x)
    -> Group aggregate: min(t2.x)
        -> Sort: t2.x
            -> Inner hash join (t1.x = t2.x)
                -> Table scan on t1
                -> Hash
                    -> Table scan on t2

DROP TABLE t;
#
# Bug #34670701 Too many ROLLUP rows with hypergraph
#
CREATE TABLE t1(
a INT,
b INT,
c INT,
d INT,
e INT,
PRIMARY KEY(a,b),
KEY ix1 (c,d)
);
INSERT INTO t1 VALUES (0,0,0,0,1), (1,0,1,0,1), (0,1,2,0,1), (2,0,2,0,1), (4,0,0,0,1);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN FORMAT=TREE SELECT a,c,d,sum(e) FROM t1 GROUP BY a,c,d WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=6.25)
    -> Sort: t1.a, t1.c, t1.d  (rows=5)
        -> Table scan on t1  (rows=5)

EXPLAIN FORMAT=TREE SELECT a,d,c,sum(e) FROM t1 GROUP BY a,d,c WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=6.25)
    -> Sort: t1.a, t1.d, t1.c  (rows=5)
        -> Table scan on t1  (rows=5)

EXPLAIN FORMAT=TREE SELECT c,a,d,sum(e) FROM t1 GROUP BY c,a,d WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=6.25)
    -> Sort: t1.c, t1.a, t1.d  (rows=5)
        -> Table scan on t1  (rows=5)

EXPLAIN FORMAT=TREE SELECT d,a,c,sum(e) FROM t1 GROUP BY d,a,c WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=6.25)
    -> Sort: t1.d, t1.a, t1.c  (rows=5)
        -> Table scan on t1  (rows=5)

EXPLAIN FORMAT=TREE SELECT c,d,b,sum(e) FROM t1 GROUP BY c,d,b WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=6.25)
    -> Sort: t1.c, t1.d, t1.b  (rows=5)
        -> Table scan on t1  (rows=5)

EXPLAIN FORMAT=TREE SELECT d,c,a,sum(e) FROM t1 GROUP BY d,c,a WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=6.25)
    -> Sort: t1.d, t1.c, t1.a  (rows=5)
        -> Table scan on t1  (rows=5)

EXPLAIN FORMAT=TREE SELECT b,a,sum(e) FROM t1 GROUP BY b,a WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=4.73)
    -> Sort: t1.b, t1.a  (rows=5)
        -> Table scan on t1  (rows=5)

EXPLAIN FORMAT=TREE SELECT b,sum(e) FROM t1 GROUP BY b WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=3.24)
    -> Sort: t1.b  (rows=5)
        -> Table scan on t1  (rows=5)

EXPLAIN FORMAT=TREE SELECT c,sum(e) FROM t1 GROUP BY c WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=3.24)
    -> Sort: t1.c  (rows=5)
        -> Table scan on t1  (rows=5)

SELECT c,sum(e) FROM t1 GROUP BY c WITH ROLLUP;
c	sum(e)
0	2
1	1
2	2
NULL	5
EXPLAIN FORMAT=TREE SELECT c,d,sum(e) FROM t1 GROUP BY c,d WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=4.73)
    -> Sort: t1.c, t1.d  (rows=5)
        -> Table scan on t1  (rows=5)

SELECT c,d,sum(e) FROM t1 GROUP BY c,d WITH ROLLUP;
c	d	sum(e)
0	0	2
0	NULL	2
1	0	1
1	NULL	1
2	0	2
2	NULL	2
NULL	NULL	5
EXPLAIN FORMAT=TREE SELECT c,d,a,sum(e) FROM t1 GROUP BY c,d,a WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=6.25)
    -> Sort: t1.c, t1.d, t1.a  (rows=5)
        -> Table scan on t1  (rows=5)

SELECT c,d,a,sum(e) FROM t1 GROUP BY c,d,a WITH ROLLUP;
c	d	a	sum(e)
0	0	0	1
0	0	4	1
0	0	NULL	2
0	NULL	NULL	2
1	0	1	1
1	0	NULL	1
1	NULL	NULL	1
2	0	0	1
2	0	2	1
2	0	NULL	2
2	NULL	NULL	2
NULL	NULL	NULL	5
EXPLAIN FORMAT=TREE SELECT c,d,a,b,sum(e) FROM t1 GROUP BY c,d,a,b WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=7.78)
    -> Sort: t1.c, t1.d, t1.a, t1.b  (rows=5)
        -> Table scan on t1  (rows=5)

SELECT c,d,a,b,sum(e) FROM t1 GROUP BY c,d,a,b WITH ROLLUP;
c	d	a	b	sum(e)
0	0	0	0	1
0	0	0	NULL	1
0	0	4	0	1
0	0	4	NULL	1
0	0	NULL	NULL	2
0	NULL	NULL	NULL	2
1	0	1	0	1
1	0	1	NULL	1
1	0	NULL	NULL	1
1	NULL	NULL	NULL	1
2	0	0	1	1
2	0	0	NULL	1
2	0	2	0	1
2	0	2	NULL	1
2	0	NULL	NULL	2
2	NULL	NULL	NULL	2
NULL	NULL	NULL	NULL	5
EXPLAIN FORMAT=TREE SELECT a,sum(e) FROM t1 GROUP BY a WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=3.24)
    -> Index scan on t1 using PRIMARY  (rows=5)

SELECT a,sum(e) FROM t1 GROUP BY a WITH ROLLUP;
a	sum(e)
0	2
1	1
2	1
4	1
NULL	5
EXPLAIN FORMAT=TREE SELECT a,b,sum(e) FROM t1 GROUP BY a,b WITH ROLLUP;
EXPLAIN
-> Group aggregate with rollup: sum(t1.e)  (rows=4.73)
    -> Index scan on t1 using PRIMARY  (rows=5)

SELECT a,b,sum(e) FROM t1 GROUP BY a,b WITH ROLLUP;
a	b	sum(e)
0	0	1
0	1	1
0	NULL	2
1	0	1
1	NULL	1
2	0	1
2	NULL	1
4	0	1
4	NULL	1
NULL	NULL	5
EXPLAIN FORMAT=TREE SELECT d,a,c,sum(e) FROM t1 GROUP BY d,a,c;
EXPLAIN
-> Group aggregate: sum(t1.e)  (rows=2.24)
    -> Sort: t1.d, t1.a, t1.c  (rows=5)
        -> Table scan on t1  (rows=5)

SELECT d,a,c,sum(e) FROM t1 GROUP BY d,a,c;
d	a	c	sum(e)
0	0	0	1
0	0	2	1
0	1	1	1
0	2	2	1
0	4	0	1
EXPLAIN FORMAT=TREE SELECT a,d,c,sum(e) FROM t1 GROUP BY a,d,c;
EXPLAIN
-> Group aggregate: sum(t1.e)  (rows=2.24)
    -> Sort: t1.a, t1.d, t1.c  (rows=5)
        -> Table scan on t1  (rows=5)

SELECT a,d,c,sum(e) FROM t1 GROUP BY a,d,c;
a	d	c	sum(e)
0	0	0	1
0	0	2	1
1	0	1	1
2	0	2	1
4	0	0	1
EXPLAIN FORMAT=TREE SELECT b,a,sum(e) FROM t1 GROUP BY b,a;
EXPLAIN
-> Group aggregate: sum(t1.e)  (rows=2.24)
    -> Index scan on t1 using PRIMARY  (rows=5)

SELECT b,a,sum(e) FROM t1 GROUP BY b,a;
b	a	sum(e)
0	0	1
1	0	1
0	1	1
0	2	1
0	4	1
EXPLAIN FORMAT=TREE SELECT a,c,e,sum(d) FROM t1 GROUP BY a,c,e;
EXPLAIN
-> Group aggregate: sum(t1.d)  (rows=2.24)
    -> Sort: t1.a, t1.c, t1.e  (rows=5)
        -> Table scan on t1  (rows=5)

DROP TABLE t1;
#
# Bug #33968442: Hypergraph gives too high row estimates for GROUP BY
#
CREATE TABLE num10 (n INT);
INSERT INTO num10 VALUES (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE VIEW num1000 AS
SELECT d1.n+d2.n*10+d3.n*100 n FROM num10 d1, num10 d2, num10 d3;
CREATE TABLE t1(
a INT,
b INT,
c INT,
d INT,
e INT,
f INT,
g INT,
h INT,
i INT,
j INT,
k INT,
l INT,
PRIMARY KEY(a,b),
KEY ix1 (c,d),
KEY ix2 (d,a,c),
KEY ix3 (g,h,i,j),
KEY ix4 (k,j,l),
KEY ix5 (k,l)
);
INSERT INTO t1
SELECT n/100,n%100,n%5,n%7,n%11,n%13,n%10,n%10,n%10,n%10,n%10,n%10
FROM num1000;
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY a;
EXPLAIN
-> Covering index skip scan for deduplication on t1 using PRIMARY  (rows=11) (actual rows=11 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY c;
EXPLAIN
-> Covering index skip scan for deduplication on t1 using ix1  (rows=2.24) (actual rows=5 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY d,c;
EXPLAIN
-> Group (no aggregates)  (rows=35) (actual rows=35 loops=1)
    -> Covering index scan on t1 using ix1  (rows=1000) (actual rows=1000 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY d,a;
EXPLAIN
-> Covering index skip scan for deduplication on t1 using ix2  (rows=77) (actual rows=77 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY c,d,a;
EXPLAIN
-> Covering index skip scan for deduplication on t1 using ix1  (rows=385) (actual rows=385 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY d,a,c WITH ROLLUP;
EXPLAIN
-> Group (no aggregates)  (rows=470) (actual rows=470 loops=1)
    -> Covering index scan on t1 using ix2  (rows=1000) (actual rows=1000 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY c,d,a WITH ROLLUP;
EXPLAIN
-> Group (no aggregates)  (rows=426) (actual rows=426 loops=1)
    -> Covering index scan on t1 using ix1  (rows=1000) (actual rows=1000 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY c,a,d WITH ROLLUP;
EXPLAIN
-> Group (no aggregates)  (rows=423) (actual rows=446 loops=1)
    -> Sort: t1.c, t1.a, t1.d  (rows=1000) (actual rows=1000 loops=1)
        -> Covering index scan on t1 using ix1  (rows=1000) (actual rows=1000 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY c,a;
EXPLAIN
-> Table scan on <temporary>  (rows=32.3) (actual rows=55 loops=1)
    -> Temporary table with deduplication  (rows=32.3) (actual rows=55 loops=1)
        -> Covering index scan on t1 using ix1  (rows=1000) (actual rows=1000 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY c,b;
EXPLAIN
-> Table scan on <temporary>  (rows=93) (actual rows=100 loops=1)
    -> Temporary table with deduplication  (rows=93) (actual rows=100 loops=1)
        -> Covering index scan on t1 using ix1  (rows=1000) (actual rows=1000 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY e,f;
EXPLAIN
-> Table scan on <temporary>  (rows=320) (actual rows=143 loops=1)
    -> Temporary table with deduplication  (rows=320) (actual rows=143 loops=1)
        -> Table scan on t1  (rows=1000) (actual rows=1000 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY g,h,i,j,k,l;
EXPLAIN
-> Table scan on <temporary>  (rows=46.8) (actual rows=10 loops=1)
    -> Temporary table with deduplication  (rows=46.8) (actual rows=10 loops=1)
        -> Table scan on t1  (rows=1000) (actual rows=1000 loops=1)

ANALYZE TABLE t1 UPDATE HISTOGRAM ON a,b,c,d,e,f,g,h,i;
Table	Op	Msg_type	Msg_text
test.t1	histogram	status	Histogram statistics created for column 'a'.
test.t1	histogram	status	Histogram statistics created for column 'b'.
test.t1	histogram	status	Histogram statistics created for column 'c'.
test.t1	histogram	status	Histogram statistics created for column 'd'.
test.t1	histogram	status	Histogram statistics created for column 'e'.
test.t1	histogram	status	Histogram statistics created for column 'f'.
test.t1	histogram	status	Histogram statistics created for column 'g'.
test.t1	histogram	status	Histogram statistics created for column 'h'.
test.t1	histogram	status	Histogram statistics created for column 'i'.
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY e;
EXPLAIN
-> Table scan on <temporary>  (rows=11) (actual rows=11 loops=1)
    -> Temporary table with deduplication  (rows=11) (actual rows=11 loops=1)
        -> Table scan on t1  (rows=1000) (actual rows=1000 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY e,f;
EXPLAIN
-> Table scan on <temporary>  (rows=64.8) (actual rows=143 loops=1)
    -> Temporary table with deduplication  (rows=64.8) (actual rows=143 loops=1)
        -> Table scan on t1  (rows=1000) (actual rows=1000 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 WHERE b>95 GROUP BY e,f;
EXPLAIN
-> Group (no aggregates)  (rows=27.7) (actual rows=40 loops=1)
    -> Sort: t1.e, t1.f  (rows=40) (actual rows=40 loops=1)
        -> Filter: (t1.b > 95)  (rows=40) (actual rows=40 loops=1)
            -> Table scan on t1  (rows=1000) (actual rows=1000 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY c+0,e+0;
EXPLAIN
-> Table scan on <temporary>  (rows=100) (actual rows=55 loops=1)
    -> Temporary table with deduplication  (rows=100) (actual rows=55 loops=1)
        -> Table scan on t1  (rows=1000) (actual rows=1000 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 WHERE b>95 GROUP BY c+0,e+0;
EXPLAIN
-> Group (no aggregates)  (rows=11.7) (actual rows=40 loops=1)
    -> Sort: (t1.c + 0), (t1.e + 0)  (rows=40) (actual rows=40 loops=1)
        -> Filter: (t1.b > 95)  (rows=40) (actual rows=40 loops=1)
            -> Table scan on t1  (rows=1000) (actual rows=1000 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1 GROUP BY a,e,c+0;
EXPLAIN
-> Table scan on <temporary>  (rows=501) (actual rows=595 loops=1)
    -> Temporary table with deduplication  (rows=501) (actual rows=595 loops=1)
        -> Table scan on t1  (rows=1000) (actual rows=1000 loops=1)

CREATE TABLE t2 (
c1 INT,
c2 INT,
c3 INT,
PRIMARY KEY(c1,c2)
);
INSERT INTO t2 SELECT n%5,n/5,n%3 FROM num10;
ANALYZE TABLE t2 UPDATE HISTOGRAM ON c3;
Table	Op	Msg_type	Msg_text
test.t2	histogram	status	Histogram statistics created for column 'c3'.
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
EXPLAIN ANALYZE SELECT 1 FROM t1,t2 GROUP BY c1;
EXPLAIN
-> Table scan on <temporary>  (rows=5) (actual rows=5 loops=1)
    -> Temporary table with deduplication  (rows=5) (actual rows=5 loops=1)
        -> Inner hash join (no condition)  (rows=10000) (actual rows=10000 loops=1)
            -> Covering index scan on t1 using ix1  (rows=1000) (actual rows=1000 loops=1)
            -> Hash
                -> Table scan on t2  (rows=10) (actual rows=10 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1,t2 GROUP BY c2;
EXPLAIN
-> Table scan on <temporary>  (rows=3.16) (actual rows=3 loops=1)
    -> Temporary table with deduplication  (rows=3.16) (actual rows=3 loops=1)
        -> Inner hash join (no condition)  (rows=10000) (actual rows=10000 loops=1)
            -> Covering index scan on t1 using ix1  (rows=1000) (actual rows=1000 loops=1)
            -> Hash
                -> Table scan on t2  (rows=10) (actual rows=10 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1,t2 GROUP BY c3;
EXPLAIN
-> Table scan on <temporary>  (rows=3) (actual rows=3 loops=1)
    -> Temporary table with deduplication  (rows=3) (actual rows=3 loops=1)
        -> Inner hash join (no condition)  (rows=10000) (actual rows=10000 loops=1)
            -> Covering index scan on t1 using ix1  (rows=1000) (actual rows=1000 loops=1)
            -> Hash
                -> Table scan on t2  (rows=10) (actual rows=10 loops=1)

EXPLAIN ANALYZE SELECT 1 FROM t1,t2 GROUP BY a,c3;
EXPLAIN
-> Table scan on <temporary>  (rows=23) (actual rows=33 loops=1)
    -> Temporary table with deduplication  (rows=23) (actual rows=33 loops=1)
        -> Inner hash join (no condition)  (rows=10000) (actual rows=10000 loops=1)
            -> Covering index scan on t1 using ix1  (rows=1000) (actual rows=1000 loops=1)
            -> Hash
                -> Table scan on t2  (rows=10) (actual rows=10 loops=1)

DROP VIEW num1000;
DROP TABLE num10, t1, t2;
#
# Bug#34844509: Assertion `receiver.HasSecondaryEngineCostHook()' failed
#
CREATE TABLE t (
col1 INT, col2 INT, col3 INT, col4 INT, col5 INT, col6 INT,
col7 INT, col8 INT, col9 INT, col10 INT, col11 INT, col12 INT,
col13 INT, col14 INT, col15 INT, col16 INT, col17 INT, col18 INT,
col19 INT, col20 INT, col21 INT, col22 INT, col23 INT, col24 INT,
col25 INT, col26 INT, col27 INT, col28 INT, col29 INT, col30 INT,
col31 INT, col32 INT, col33 INT, col34 INT, col35 INT, col36 INT,
col37 INT, col38 INT, col39 INT, col40 INT, col41 INT, col42 INT,
col43 INT, col44 INT, col45 INT, col46 INT, col47 INT, col48 INT,
col49 INT, col50 INT, col51 INT, col52 INT, col53 INT, col54 INT,
col55 INT, col56 INT, col57 INT, col58 INT, col59 INT, col60 INT,
col61 INT, col62 INT, col63 INT, col64 INT,
KEY (col1, col2), KEY (col2, col3), KEY (col3), KEY (col4), KEY (col5),
KEY (col6), KEY (col7), KEY (col8), KEY (col9), KEY (col10),
KEY (col11), KEY (col12), KEY (col13), KEY (col14), KEY (col15),
KEY (col16), KEY (col17), KEY (col18), KEY (col19), KEY (col20),
KEY (col21), KEY (col22), KEY (col23), KEY (col24), KEY (col25),
KEY (col26), KEY (col27), KEY (col28), KEY (col29), KEY (col30),
KEY (col31), KEY (col32), KEY (col33), KEY (col34), KEY (col35),
KEY (col36), KEY (col37), KEY (col38), KEY (col39), KEY (col40),
KEY (col41), KEY (col42), KEY (col43), KEY (col44), KEY (col45),
KEY (col46), KEY (col47), KEY (col48), KEY (col49), KEY (col50),
KEY (col51), KEY (col52), KEY (col53), KEY (col54), KEY (col55),
KEY (col56), KEY (col57), KEY (col58), KEY (col59), KEY (col60),
KEY (col61), KEY (col62), KEY (col63), KEY (col64));
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=TREE SELECT DISTINCT * FROM t GROUP BY col1 + col2;
EXPLAIN
-> Sort with duplicate removal: t.col1, t.col2, t.col3, t.col4, t.col5, t.col6, t.col7, t.col8, t.col9, t.col10, t.col11, t.col12, t.col13, t.col14, t.col15, t.col16, t.col17, t.col18, t.col19, t.col20, t.col21, t.col22, t.col23, t.col24, t.col25, t.col26, t.col27, t.col28, t.col29, t.col30, t.col31, t.col32, t.col33, t.col34, t.col35, t.col36, t.col37, t.col38, t.col39, t.col40, t.col41, t.col42, t.col43, t.col44, t.col45, t.col46, t.col47, t.col48, t.col49, t.col50, t.col51, t.col52, t.col53, t.col54, t.col55, t.col56, t.col57, t.col58, t.col59, t.col60, t.col61, t.col62, t.col63, t.col64  (rows=1)
    -> Group (no aggregates)  (rows=1)
        -> Sort: (t.col1 + t.col2)  (rows=1)
            -> Table scan on t  (rows=1)

DROP TABLE t;
# Note: Some of the query results below are susceptible to plan changes.
# This can happen when selecting columns that we do not group by or aggregate over.
create table tb1(id int , id2 int, primary key (id));
insert into tb1 values (1, 10), (2, 11), (3, 12), (4, 10), (5, 11), (6, 12), (7, 10), (8, 11), (9, 12), (10, 10), (11, 11), (12, 12);
analyze table tb1;
Table	Op	Msg_type	Msg_text
test.tb1	analyze	status	OK
select sql_small_result avg(id) , id2 from tb1 where id2 != 5 group by id2;
avg(id)	id2
5.5000	10
6.5000	11
7.5000	12
select sql_small_result avg(id) , id2 + 2 from tb1 group by id2;
avg(id)	id2 + 2
5.5000	12
6.5000	13
7.5000	14
select sql_small_result avg(id), any_value(id) from tb1 group by id2;
avg(id)	any_value(id)
5.5000	1
6.5000	2
7.5000	3
select sql_small_result 2000 + avg(id) * 100 , avg(id*2) a , id2 * 4 i from tb1 group by id2 * 4 order by a desc;
2000 + avg(id) * 100	a	i
2750.0000	15.0000	48
2650.0000	13.0000	44
2550.0000	11.0000	40
select sql_small_result 2000 + avg(id) * 100 , avg(id*2) a , id2 * 4 i from tb1 group by id2 * 4 having i  < 48 order by a desc;
2000 + avg(id) * 100	a	i
2650.0000	13.0000	44
2550.0000	11.0000	40
EXPLAIN FORMAT=TREE select sql_small_result 2000 + avg(id) * 100 , avg(id*2) a , id2 * 4 i from tb1 group by id2 * 4 having i  < 48 order by a desc;
EXPLAIN
-> Sort: a DESC  (rows=3.46)
    -> Filter: (i < 48)  (rows=3.46)
        -> Table scan on <temporary>  (rows=3.46)
            -> Aggregate using temporary table  (rows=3.46)
                -> Table scan on tb1  (rows=12)

create table tb2(id int , id3 int, primary key (id));
insert into tb2 select * from tb1;
analyze table tb2;
Table	Op	Msg_type	Msg_text
test.tb2	analyze	status	OK
select sql_small_result id2, (select avg(tb1.id) + id3 from tb2 where tb2.id = tb1.id2) from tb1 group by id2;
id2	(select avg(tb1.id) + id3 from tb2 where tb2.id = tb1.id2)
10	15.5000
11	17.5000
12	19.5000
select sql_small_result tb2.id, tb2.id3, id2, avg(tb1.id) from tb1, tb2 group by id2;
id	id3	id2	avg(tb1.id)
12	12	10	5.5000
12	12	11	6.5000
12	12	12	7.5000
drop table tb2;
select sql_small_result ROUND(RAND(100)*10) r2, sum(1) r1 from tb1 group by id2  HAVING r1 = 4 and r2=2 ;
r2	r1
2	4
select sql_small_result ROUND(RAND(100)*10) r2, sum(1) r1 from tb1 group by id2  HAVING r1 = 4 and (select r2=2);
r2	r1
2	4
select sql_small_result ROUND(RAND(100)*any_value(id)) r2, sum(1) r1 from tb1 group by id2  HAVING r1 = 4 and r2=2 ;
r2	r1
select sql_small_result id, ROUND(RAND(100)*10) r2, sum(1) r1 from tb1 group by id2  HAVING r1 = 4 and any_value(id)=2;
id	r2	r1
2	7	4
select sql_small_result ROUND(RAND(100)*10) r2, sum(1) r1 , id from tb1 group by id2  HAVING r1 = 4 and (select any_value(id)=2) ;
r2	r1	id
7	4	2
select sql_small_result id2, avg(id) + id, (select avg(id) + id), avg(id) + id2, (select avg(id) + id2) from tb1 group by id2;
id2	avg(id) + id	(select avg(id) + id)	avg(id) + id2	(select avg(id) + id2)
10	6.5000	6.5000	15.5000	15.5000
11	8.5000	8.5000	17.5000	17.5000
12	10.5000	10.5000	19.5000	19.5000
select sql_small_result distinct round(avg(id)/10-.2) from tb1 group by id2 order by id;
round(avg(id)/10-.2)
0
1
select * from (select * from tb1 where id %3 = 0) even,
lateral (select sql_small_result avg(id2) a from tb1 where id > even.id group by id2 ) l
where even.id != l.a;
id	id2	a
3	12	10.0000
3	12	11.0000
3	12	12.0000
6	12	10.0000
6	12	11.0000
6	12	12.0000
9	12	10.0000
9	12	11.0000
9	12	12.0000
create table tb2(a int , b int);
insert into tb2 values (1, 10), (2, 11), (1, 13);
select sql_small_result (select count(*) from tb2 where b + 5 > avg(tb1.id) + tb1.id2) e from tb1 group by id2 ;
e
2
1
0
drop table tb2;
select sql_small_result SQL_BUFFER_RESULT 2000 + avg(id) * 100 , avg(id*2) a , id2 * 4 i from tb1 group by id2 * 4 having i  < 48 order by a desc;
2000 + avg(id) * 100	a	i
2650.0000	13.0000	44
2550.0000	11.0000	40
EXPLAIN FORMAT=TREE select sql_small_result SQL_BUFFER_RESULT 2000 + avg(id) * 100 , avg(id*2) a , id2 * 4 i from tb1 group by id2 * 4 having i  < 48 order by a desc;
EXPLAIN
-> Table scan on <temporary>  (rows=3.46)
    -> Temporary table  (rows=3.46)
        -> Sort: a DESC  (rows=3.46)
            -> Filter: (i < 48)  (rows=3.46)
                -> Table scan on <temporary>  (rows=3.46)
                    -> Aggregate using temporary table  (rows=3.46)
                        -> Table scan on tb1  (rows=12)

CREATE TABLE t(txt TEXT, i INT);
INSERT INTO t VALUES ('z', 12), ('m', 81), ('q', 9), ('e', 7),
('z', 2), ('m', 8), ('q', 19), ('e', 17),
('a', 92), ('b', 8), ('b', 0), ('c', 2);
select sql_small_result txt, AVG(i) a, row_number() over() r FROM t GROUP BY txt order by a, txt;
txt	a	r
c	2.0000	7
b	4.0000	6
z	7.0000	1
e	12.0000	4
q	14.0000	3
m	44.5000	2
a	92.0000	5
select sql_small_result txt, AVG(i), row_number() over() FROM t GROUP BY txt order by AVG(i);
txt	AVG(i)	row_number() over()
c	2.0000	7
b	4.0000	6
z	7.0000	1
e	12.0000	4
q	14.0000	3
m	44.5000	2
a	92.0000	5
select sql_small_result txt, AVG(i)+2 a , row_number() over() FROM t GROUP BY txt order by -a;
txt	a	row_number() over()
a	94.0000	5
m	46.5000	2
q	16.0000	3
e	14.0000	4
z	9.0000	1
b	6.0000	6
c	4.0000	7
select sql_small_result txt, AVG(i)+2 a, (SELECT AVG(i) + 2) b, row_number() over() FROM t GROUP BY txt order by -b;
txt	a	b	row_number() over()
a	94.0000	94.0000	5
m	46.5000	46.5000	2
q	16.0000	16.0000	3
e	14.0000	14.0000	4
z	9.0000	9.0000	1
b	6.0000	6.0000	6
c	4.0000	4.0000	7
select sql_small_result txt, AVG(i)+2 a, (SELECT AVG(i) + 2) b, row_number() over() FROM t GROUP BY txt order by b;
txt	a	b	row_number() over()
c	4.0000	4.0000	7
b	6.0000	6.0000	6
z	9.0000	9.0000	1
e	14.0000	14.0000	4
q	16.0000	16.0000	3
m	46.5000	46.5000	2
a	94.0000	94.0000	5
select sql_small_result txt, AVG(i)+2 a, row_number() over()  FROM t GROUP BY txt order by a;
txt	a	row_number() over()
c	4.0000	7
b	6.0000	6
z	9.0000	1
e	14.0000	4
q	16.0000	3
m	46.5000	2
a	94.0000	5
select sql_small_result txt, AVG(i) a, row_number() over()  FROM t GROUP BY txt order by a;
txt	a	row_number() over()
c	2.0000	7
b	4.0000	6
z	7.0000	1
e	12.0000	4
q	14.0000	3
m	44.5000	2
a	92.0000	5
select sql_small_result txt, AVG(i) * 2 a, row_number() over()  FROM t GROUP BY txt order by txt;
txt	a	row_number() over()
a	184.0000	5
b	8.0000	6
c	4.0000	7
e	24.0000	4
m	89.0000	2
q	28.0000	3
z	14.0000	1
select sql_small_result txt, AVG(i) a, row_number() over()  FROM t GROUP BY txt order by txt;
txt	a	row_number() over()
a	92.0000	5
b	4.0000	6
c	2.0000	7
e	12.0000	4
m	44.5000	2
q	14.0000	3
z	7.0000	1
select sql_small_result txt, AVG(i) a , row_number() over() r FROM t GROUP BY txt order by 0-a;
txt	a	r
a	92.0000	5
m	44.5000	2
q	14.0000	3
e	12.0000	4
z	7.0000	1
b	4.0000	6
c	2.0000	7
select sql_small_result txt, AVG(i) a , row_number() over() r FROM t GROUP BY txt order by a;
txt	a	r
c	2.0000	7
b	4.0000	6
z	7.0000	1
e	12.0000	4
q	14.0000	3
m	44.5000	2
a	92.0000	5
select sql_small_result txt, AVG(i) a , row_number() over() r FROM t GROUP BY txt order by txt;
txt	a	r
a	92.0000	5
b	4.0000	6
c	2.0000	7
e	12.0000	4
m	44.5000	2
q	14.0000	3
z	7.0000	1
drop table t;
select sql_small_result round(stddev(id2),5) from tb1 t1 group by (id + id2)%5;
round(stddev(id2),5)
0.8165
0.5
1
0.8165
0.5
select sql_small_result round(stddev(id2),5) from tb1 t1 group by (select (id + id2)%5);
round(stddev(id2),5)
0.8165
0.5
1
0.8165
0.5
select sql_small_result round(stddev(id2),5) from tb1 t1 group by (select (t2.id + t2.id2)%5 from tb1 t2 where t2.id = t1.id);
round(stddev(id2),5)
0.8165
0.5
1
0.8165
0.5
create table t1 (oref int, grp int, ie int) ;
insert into t1 (oref, grp, ie) values (1, 1, 1), (1, 1, 1), (1, 2, NULL), (2, 1, 3), (3, 1, 4), (3, 2, NULL);
create table t2 (oref int, a int);
insert into t2 values (1, 1), (2, 2), (3, 3), (4, NULL), (2, NULL);
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
select a, oref, a in (select sql_small_result max(ie)  from t1
where oref=t2.oref group by grp) Z from t2;
a	oref	Z
1	1	1
2	2	0
3	3	NULL
NULL	4	0
NULL	2	NULL
select a, oref, a in (select sql_small_result any_value(ie) from t1 where oref=t2.oref
group by grp having max(ie) is null or max(ie) is not null) Z from t2;
a	oref	Z
1	1	1
2	2	0
3	3	NULL
NULL	4	0
NULL	2	NULL
drop table t1, t2;
create table tb2(pk int, col_int int, col_varchar varchar(1), PRIMARY KEY (pk));
select count(alias2.col_int) as field1, alias1.pk as field2 from tb2 as alias1
right join tb2 as alias2 on alias1.col_varchar = alias2.col_varchar
where (alias2.col_int = alias1.col_int and alias1.pk IS NULL) group by field2;
field1	field2
drop table tb2;
#
# Bug#36388816: 'm_index_cursor.is_positioned()' in temptable::Handler::position at src/handler
#
CREATE TABLE t1 (pk INTEGER, f1 INTEGER, f2 LONGTEXT, f3 VARCHAR(10),
f4 TIMESTAMP, PRIMARY KEY(pk));
INSERT INTO t1 VALUES (1, NULL,  'fsfd', NULL, NULL),
(2, 34, 'fsfsfd', NULL, "1993-07-20 08:10:46");
SELECT MIN(t1.f1) AS field1 FROM (t1 JOIN (t1 t2) ON (TRUE),
LATERAL (SELECT t2.f3 AS field2 FROM t1 t3 WHERE (t2.f3 <=> t2.f4)
GROUP BY field2 ORDER BY t1.f2) AS t4)
GROUP BY field2 ORDER BY field1;
field1
34
DROP TABLE t1;
#
# Bug#36668257: Assertion `!OrderItemsReferenceUnavailableTables(path....
#
SELECT SQL_SMALL_RESULT avg(id) - 6.6 AS field1, id2  FROM tb1 GROUP BY id2
ORDER BY abs(field1);
field1	id2
-0.1000	11
0.9000	12
-1.1000	10
SELECT SQL_BIG_RESULT   avg(id) - 6.6 AS field1, id2  FROM tb1 GROUP BY id2
ORDER BY abs(field1);
field1	id2
-0.1000	11
0.9000	12
-1.1000	10
select avg(id) , id2 from tb1 where id2 != 5 group by id2 with rollup;
avg(id)	id2
5.5000	10
6.5000	11
7.5000	12
6.5000	NULL
EXPLAIN FORMAT=TREE select avg(id) , id2 from tb1 where id2 != 5 group by id2 with rollup;
EXPLAIN
-> Group aggregate with rollup: avg(tb1.id)  (rows=4.43)
    -> Sort: tb1.id2  (rows=10.8)
        -> Filter: (tb1.id2 <> 5)  (rows=10.8)
            -> Table scan on tb1  (rows=12)

select avg(id2*2)  over(), id2 from tb1 group by id2;
avg(id2*2)  over()	id2
22.0000	10
22.0000	11
22.0000	12
EXPLAIN FORMAT=TREE select avg(id2*2)  over(), id2 from tb1 group by id2;
EXPLAIN
-> Window aggregate with buffering: avg(`(tb1.id2 * 2)`) OVER ()   (rows=3.46)
    -> Stream results  (rows=3.46)
        -> Group (no aggregates)  (rows=3.46)
            -> Sort: tb1.id2  (rows=12)
                -> Table scan on tb1  (rows=12)

EXPLAIN FORMAT=TREE select sql_small_result avg(id), JSON_ARRAYAGG(id) from tb1 t1 group by id2 ;
EXPLAIN
-> Group aggregate: avg(t1.id), json_arrayagg(t1.id)  (rows=3.46)
    -> Sort: t1.id2  (rows=12)
        -> Table scan on t1  (rows=12)

EXPLAIN FORMAT=TREE select sql_small_result avg(id) from tb1 group by 'abcd';
EXPLAIN
-> Group aggregate: avg(tb1.id)  (rows=1)
    -> Table scan on tb1  (rows=12)

EXPLAIN FORMAT=TREE select sql_big_result avg(id) from tb1 t1 group by id2;
EXPLAIN
-> Group aggregate: avg(t1.id)  (rows=3.46)
    -> Sort: t1.id2  (rows=12)
        -> Table scan on t1  (rows=12)

EXPLAIN FORMAT=TREE select sql_small_result avg(id2) from tb1 t1 group by id;
EXPLAIN
-> Table scan on <temporary>  (rows=12)
    -> Aggregate using temporary table  (rows=12)
        -> Table scan on t1  (rows=12)

truncate tb1;
analyze table tb1;
Table	Op	Msg_type	Msg_text
test.tb1	analyze	status	OK
EXPLAIN FORMAT=TREE select sql_small_result avg(id) from tb1 t1 group by id2;
EXPLAIN
-> Table scan on <temporary>  (rows=1)
    -> Aggregate using temporary table  (rows=1)
        -> Table scan on t1  (rows=1)

#
# Bug#3767052: mysqld crash at Field::is_nullable while querying...
#
create table t1(id int, id2 int);
insert into t1 values (1, 10), (2, 11), (3, 12), (4, 10), (5, 11), (6, 12);
SELECT sql_small_result 1 col1 FROM t1 GROUP BY @a := id LIMIT 1;
col1
1
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
SELECT * FROM (SELECT sql_small_result 1 col1 FROM t1 GROUP BY @a := id) s LIMIT 1;
col1
1
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
drop table t1;
drop table tb1;
