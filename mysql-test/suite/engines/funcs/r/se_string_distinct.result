DROP TABLE IF EXISTS t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 CHAR(1) NULL, c2 CHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARCHAR(1) NULL, c2 VARCHAR(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BINARY(1) NULL, c2 BINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 VARBINARY(1) NULL, c2 VARBINARY(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 BLOB(1) NULL, c2 BLOB(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
CREATE TABLE t1(c1 TEXT(1) NULL, c2 TEXT(1) NULL, c3 INT NULL);
TRUNCATE TABLE t1;
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',1), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',1), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',1), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',1), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',3), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',3), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',3), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',3), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',2), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',2), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',2), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',2), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',7), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',7), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',7), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',7), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',6), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',6), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',6), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',6), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',4), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',4), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',4), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',4), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',5), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',5), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',5), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',5), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',9), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',9), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',9), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',9), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','a',8), ('a','a',2), ('a','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','a',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','a',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('a','b',8), ('a','b',2), ('b','b',3), ('a','c',4), ('a','d',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('b','b',1), ('b','a',8), ('b','c',6), ('b','e',4), ('b','z',5);
INSERT INTO t1 (c1,c2,c3) VALUES ('c','b',5), ('c','c',7), ('c','c',8), ('c','d',9), ('c','f',5);
SELECT DISTINCT c1 FROM t1;
c1
a
b
c
SELECT DISTINCT c2 FROM t1;
c2
a
b
c
d
e
z
f
SELECT DISTINCT c3 FROM t1;
c3
1
2
3
4
5
6
7
9
8
SELECT DISTINCT c1,c3 FROM t1;
c1	c3
a	1
a	2
a	3
a	4
a	5
b	1
b	6
b	4
b	5
c	5
c	7
c	1
c	9
b	3
c	3
b	2
c	2
a	7
b	7
a	6
c	6
c	4
a	9
b	9
a	8
b	8
c	8
SELECT DISTINCT c1,c2,c3 FROM t1;
c1	c2	c3
a	a	1
a	a	2
a	b	3
a	c	4
a	d	5
b	a	1
b	c	6
b	e	4
b	z	5
c	a	5
c	c	7
c	c	1
c	d	9
c	f	5
a	b	1
a	b	2
b	b	3
b	b	1
c	b	5
a	a	3
b	a	3
c	c	3
b	a	2
c	c	2
a	a	7
b	a	7
a	b	7
a	a	6
b	a	6
c	c	6
a	b	6
a	a	4
b	a	4
c	c	4
a	b	4
a	a	5
b	a	5
c	c	5
a	b	5
a	a	9
b	a	9
c	c	9
a	b	9
a	a	8
b	a	8
c	c	8
a	b	8
SELECT DISTINCT c3,c2,c1 FROM t1;
c3	c2	c1
1	a	a
2	a	a
3	b	a
4	c	a
5	d	a
1	a	b
6	c	b
4	e	b
5	z	b
5	a	c
7	c	c
1	c	c
9	d	c
5	f	c
1	b	a
2	b	a
3	b	b
1	b	b
5	b	c
3	a	a
3	a	b
3	c	c
2	a	b
2	c	c
7	a	a
7	a	b
7	b	a
6	a	a
6	a	b
6	c	c
6	b	a
4	a	a
4	a	b
4	c	c
4	b	a
5	a	a
5	a	b
5	c	c
5	b	a
9	a	a
9	a	b
9	c	c
9	b	a
8	a	a
8	a	b
8	c	c
8	b	a
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2';
c3	c2	c1
SELECT DISTINCT c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1
SELECT DISTINCT c2 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c2
SELECT DISTINCT c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3
SELECT DISTINCT c1,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c3
SELECT DISTINCT c1,c2,c3 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c1	c2	c3
SELECT DISTINCT c3,c2,c1 FROM t1 WHERE t1.c1='#C2' AND t1.c2='#C2';
c3	c2	c1
DROP TABLE t1;
