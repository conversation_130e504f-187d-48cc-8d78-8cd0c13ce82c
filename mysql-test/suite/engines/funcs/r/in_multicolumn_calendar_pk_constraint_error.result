DROP TABLE IF EXISTS t1;
SET sql_mode='NO_ENGINE_SUBSTITUTION';
SET TIME_ZONE="+03:00";
CREATE TABLE t1(c1 DATE NOT NULL, c2 DATE NOT NULL, c3 DATE NOT NULL, PRIMARY KEY(c1,c2,c3));
SET TIMESTAMP=1171346973;
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),NOW());
Warnings:
Note	1265	Data truncated for column 'c1' at row 1
Note	1265	Data truncated for column 'c2' at row 1
Note	1265	Data truncated for column 'c3' at row 1
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),ADDTIME(NOW(),'1 01:01:01'));
Warnings:
Note	1265	Data truncated for column 'c1' at row 1
Note	1265	Data truncated for column 'c2' at row 1
Note	1265	Data truncated for column 'c3' at row 1
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),ADDTIME(NOW(),'1 01:01:01'),NOW());
Warnings:
Note	1265	Data truncated for column 'c1' at row 1
Note	1265	Data truncated for column 'c2' at row 1
Note	1265	Data truncated for column 'c3' at row 1
INSERT INTO t1 (c1,c2,c3) VALUES(ADDTIME(NOW(),'1 01:01:01'),NOW(),NOW());
Warnings:
Note	1265	Data truncated for column 'c1' at row 1
Note	1265	Data truncated for column 'c2' at row 1
Note	1265	Data truncated for column 'c3' at row 1
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),NOW());
ERROR 23000: Duplicate entry '2007-02-13-2007-02-13-2007-02-13' for key 't1.PRIMARY'
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),ADDTIME(NOW(),'1 01:01:01'));
ERROR 23000: Duplicate entry '2007-02-13-2007-02-13-2007-02-14' for key 't1.PRIMARY'
SELECT * FROM t1;
c1	c2	c3
2007-02-13	2007-02-13	2007-02-13
2007-02-13	2007-02-13	2007-02-14
2007-02-13	2007-02-14	2007-02-13
2007-02-14	2007-02-13	2007-02-13
DROP TABLE t1;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 YEAR NOT NULL, c3 YEAR NOT NULL, PRIMARY KEY(c1,c2,c3));
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,1999);
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,2000);
INSERT INTO t1 (c1,c2,c3) VALUES(1999,2000,1999);
INSERT INTO t1 (c1,c2,c3) VALUES(2000,1999,1999);
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,1999);
ERROR 23000: Duplicate entry '1999-1999-1999' for key 't1.PRIMARY'
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,2000);
ERROR 23000: Duplicate entry '1999-1999-2000' for key 't1.PRIMARY'
SELECT * FROM t1;
c1	c2	c3
1999	1999	1999
1999	1999	2000
1999	2000	1999
2000	1999	1999
DROP TABLE t1;
CREATE TABLE t1(c1 TIME NOT NULL, c2 TIME NOT NULL, c3 TIME NOT NULL, PRIMARY KEY(c1,c2,c3));
SET TIMESTAMP=1171346973;
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),NOW());
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),ADDTIME(NOW(),'1 01:01:01'));
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),ADDTIME(NOW(),'1 01:01:01'),NOW());
INSERT INTO t1 (c1,c2,c3) VALUES(ADDTIME(NOW(),'1 01:01:01'),NOW(),NOW());
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),NOW());
ERROR 23000: Duplicate entry '09:09:33-09:09:33-09:09:33' for key 't1.PRIMARY'
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),ADDTIME(NOW(),'1 01:01:01'));
ERROR 23000: Duplicate entry '09:09:33-09:09:33-10:10:34' for key 't1.PRIMARY'
SELECT * FROM t1;
c1	c2	c3
09:09:33	09:09:33	09:09:33
09:09:33	09:09:33	10:10:34
09:09:33	10:10:34	09:09:33
10:10:34	09:09:33	09:09:33
DROP TABLE t1;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 YEAR NOT NULL, c3 YEAR NOT NULL, PRIMARY KEY(c1,c2,c3));
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,1999);
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,2000);
INSERT INTO t1 (c1,c2,c3) VALUES(1999,2000,1999);
INSERT INTO t1 (c1,c2,c3) VALUES(2000,1999,1999);
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,1999);
ERROR 23000: Duplicate entry '1999-1999-1999' for key 't1.PRIMARY'
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,2000);
ERROR 23000: Duplicate entry '1999-1999-2000' for key 't1.PRIMARY'
SELECT * FROM t1;
c1	c2	c3
1999	1999	1999
1999	1999	2000
1999	2000	1999
2000	1999	1999
DROP TABLE t1;
CREATE TABLE t1(c1 DATETIME NOT NULL, c2 DATETIME NOT NULL, c3 DATETIME NOT NULL, PRIMARY KEY(c1,c2,c3));
SET TIMESTAMP=1171346973;
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),NOW());
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),ADDTIME(NOW(),'1 01:01:01'));
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),ADDTIME(NOW(),'1 01:01:01'),NOW());
INSERT INTO t1 (c1,c2,c3) VALUES(ADDTIME(NOW(),'1 01:01:01'),NOW(),NOW());
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),NOW());
ERROR 23000: Duplicate entry '2007-02-13 09:09:33-2007-02-13 09:09:33-2007-02-13 09:09:33' for key 't1.PRIMARY'
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),ADDTIME(NOW(),'1 01:01:01'));
ERROR 23000: Duplicate entry '2007-02-13 09:09:33-2007-02-13 09:09:33-2007-02-14 10:10:34' for key 't1.PRIMARY'
SELECT * FROM t1;
c1	c2	c3
2007-02-13 09:09:33	2007-02-13 09:09:33	2007-02-13 09:09:33
2007-02-13 09:09:33	2007-02-13 09:09:33	2007-02-14 10:10:34
2007-02-13 09:09:33	2007-02-14 10:10:34	2007-02-13 09:09:33
2007-02-14 10:10:34	2007-02-13 09:09:33	2007-02-13 09:09:33
DROP TABLE t1;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 YEAR NOT NULL, c3 YEAR NOT NULL, PRIMARY KEY(c1,c2,c3));
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,1999);
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,2000);
INSERT INTO t1 (c1,c2,c3) VALUES(1999,2000,1999);
INSERT INTO t1 (c1,c2,c3) VALUES(2000,1999,1999);
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,1999);
ERROR 23000: Duplicate entry '1999-1999-1999' for key 't1.PRIMARY'
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,2000);
ERROR 23000: Duplicate entry '1999-1999-2000' for key 't1.PRIMARY'
SELECT * FROM t1;
c1	c2	c3
1999	1999	1999
1999	1999	2000
1999	2000	1999
2000	1999	1999
DROP TABLE t1;
CREATE TABLE t1(c1 TIMESTAMP NOT NULL, c2 TIMESTAMP NOT NULL, c3 TIMESTAMP NOT NULL, PRIMARY KEY(c1,c2,c3));
SET TIMESTAMP=1171346973;
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),NOW());
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),ADDTIME(NOW(),'1 01:01:01'));
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),ADDTIME(NOW(),'1 01:01:01'),NOW());
INSERT INTO t1 (c1,c2,c3) VALUES(ADDTIME(NOW(),'1 01:01:01'),NOW(),NOW());
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),NOW());
ERROR 23000: Duplicate entry '2007-02-13 09:09:33-2007-02-13 09:09:33-2007-02-13 09:09:33' for key 't1.PRIMARY'
INSERT INTO t1 (c1,c2,c3) VALUES(NOW(),NOW(),ADDTIME(NOW(),'1 01:01:01'));
ERROR 23000: Duplicate entry '2007-02-13 09:09:33-2007-02-13 09:09:33-2007-02-14 10:10:34' for key 't1.PRIMARY'
SELECT * FROM t1;
c1	c2	c3
2007-02-13 09:09:33	2007-02-13 09:09:33	2007-02-13 09:09:33
2007-02-13 09:09:33	2007-02-13 09:09:33	2007-02-14 10:10:34
2007-02-13 09:09:33	2007-02-14 10:10:34	2007-02-13 09:09:33
2007-02-14 10:10:34	2007-02-13 09:09:33	2007-02-13 09:09:33
DROP TABLE t1;
CREATE TABLE t1(c1 YEAR NOT NULL, c2 YEAR NOT NULL, c3 YEAR NOT NULL, PRIMARY KEY(c1,c2,c3));
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,1999);
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,2000);
INSERT INTO t1 (c1,c2,c3) VALUES(1999,2000,1999);
INSERT INTO t1 (c1,c2,c3) VALUES(2000,1999,1999);
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,1999);
ERROR 23000: Duplicate entry '1999-1999-1999' for key 't1.PRIMARY'
INSERT INTO t1 (c1,c2,c3) VALUES(1999,1999,2000);
ERROR 23000: Duplicate entry '1999-1999-2000' for key 't1.PRIMARY'
SELECT * FROM t1;
c1	c2	c3
1999	1999	1999
1999	1999	2000
1999	2000	1999
2000	1999	1999
DROP TABLE t1;
SET TIME_ZONE= @@global.time_zone;
SET sql_mode=default;
