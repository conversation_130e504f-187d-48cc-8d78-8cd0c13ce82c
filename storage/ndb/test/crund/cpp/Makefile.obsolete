# Copyright (c) 2004, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
#------------------------------------------------------------------------------

# read the generic settings
include	../../Makefile.defaults
include	../../env.properties

#------------------------------------------------------------------------------

  DDEFINES 	=

  DELIVERABLES	= crund crundAB

  CLEAN		= core *.o

  MOSTLYCLEAN	= $(DELIVERABLES) *.dSYM *% log_*

  DISTCLEAN	= *~ *% */*~ .depend.* _dummy.*

  INCLUDES	= -I. -I"$(HELPERS)" \
		  $(NDB_INCLUDEOPT0) $(NDB_INCLUDEOPT1) \
		  $(NDB_INCLUDEOPT2) $(NDB_INCLUDEOPT3)

# for library dependencies, LOADLIBES and LDLIBS have been used historically
# this convention is used sometimes: use
#   LDLIBS for project-wide libs (like -lc or -lm)
#   LOADLIBES for libs for the individual case
# check with non-gnu makes: use of LOADLIBES, LDLIBS
  LDLIBS	= -L$(NDB_LIBDIR0) -L$(NDB_LIBDIR1) -lndbclient
# undefined symbols when linking with
#  LOADLIBES	= -L$(HELPERS) -lutils
# which seems to be the same as
#  LOADLIBES	= $(HELPERS)/libutils.dylib
# but linking statically is fine:
  LOADLIBES	= $(HELPERS)/libutils.a

#------------------------------------------------------------------------------

.PHONY:	all depend dep \
	run.crund run.ndbapi grind.crund

all:	$(DELIVERABLES)

dep depend:

run.crund:	crund
	./crund -p ../../crundRun.properties -p ../../crundNdb.properties

run.ndbapi:	crundAB
	./crundAB -p ../../crundRun.properties -p ../../crundNdb.properties

# for JVM processes, try running valgrind with
# --smc-check=all --trace-children=yes --leak-check=full --show-reachable=yes
grind.crund:	crund
	valgrind \
	--leak-check=full --show-reachable=yes --trace-children=yes \
	./crund -p ../../crundRun.properties -p ../../crundNdb.properties

crund:	Driver.o CrundDriver.o CrundLoad.o NdbapiAB.o

crundAB:	Driver.o CrundDriver.o CrundLoad.o NdbapiAB.o

#tws:	Driver.o TwsDriver.o NdbapiTwsDriver.o
#	$(LINK.cpp) $^ $(LOADLIBES) $(LDLIBS) -o $@
#
#run.tws:	tws
#	./tws -p ../../twsRun.properties -p ../../crundNdb.properties
#
# for JVM processes, try running valgrind with
# --smc-check=all --trace-children=yes --leak-check=full --show-reachable=yes
#grind.tws:	tws
#	valgrind \
#	--leak-check=full --show-reachable=yes --trace-children=yes \
#	./tws -p ../../twsRun.properties -p ../../crundNdb.properties

#------------------------------------------------------------------------------

.depend:
	touch $@

# read local dependencies
-include	.depend

#------------------------------------------------------------------------------
