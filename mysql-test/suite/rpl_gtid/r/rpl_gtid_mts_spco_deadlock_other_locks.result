include/rpl/init_source_replica.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection master]
CALL mtr.add_suppression(".*Unsafe statement written to the binary log using statement format since BINLOG_FORMAT = STATEMENT.*");
CREATE TABLE t1(c1 INT PRIMARY KEY, c2 INT, INDEX(c2)) ENGINE = InnoDB;
INSERT INTO t1 VALUES
(1, NULL),
(2, 2),
(3, NULL),
(4, 4),
(5, NULL),
(6, 6);
CREATE TABLE t2(a INT PRIMARY KEY);
include/rpl/sync_to_replica.inc
[connection slave]
include/rpl/stop_applier.inc
set session sql_log_bin=0;
CALL mtr.add_suppression(".*Worker.*failed executing transaction.*at source log .*, end_log_pos.*<PERSON><PERSON> found when trying to get lock.*");
CALL mtr.add_suppression(".*Worker.*failed executing transaction.*at source log .*, end_log_pos.*Can not lock user management caches for processing.*");
CALL mtr.add_suppression(".*Worker.*failed executing transaction.*at source log .*, end_log_pos.*Replica worker has stopped after at least one previous worker.*");
CALL mtr.add_suppression(".*worker thread retried transaction.*time.*in vain, giving up.*");
CALL mtr.add_suppression(".*The replica coordinator and worker threads are stopped.*");
CALL mtr.add_suppression(".*Replica worker has stopped after at least one previous worker encountered an error when replica-preserve-commit-order was enabled.*");
set session sql_log_bin=1;
SET GLOBAL replica_parallel_type = LOGICAL_CLOCK;
Warnings:
Warning	1287	'@@replica_parallel_type' is deprecated and will be removed in a future release.
SET GLOBAL replica_parallel_workers = 3;
SET GLOBAL replica_preserve_commit_order = ON;
SET GLOBAL replica_transaction_retries = REPLICA_TRANSACTION_RETRIES;
SET GLOBAL innodb_lock_wait_timeout = INNODB_LOCK_WAIT_TIMEOUT;
include/rpl/connect.inc [creating rpl_slave_connection_2]
include/rpl/connect.inc [creating rpl_slave_connection_3]

#
# TC1. `GLOBAL READ_ONLY=ON` issued by client and DDL replicated
#      from source.
#------------------------------------------------------------------------
[connection master]
INSERT INTO t1 VALUES(10, 10);
[connection slave]
[connection master]
# Adding debug point 'set_commit_parent_100' to @@GLOBAL.debug
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:1";
ALTER TABLE t2 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=1;
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:2";
INSERT INTO t1 VALUES(11, NULL);
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:3";
INSERT INTO t1 VALUES(12, NULL);
SET GTID_NEXT = AUTOMATIC;
# Removing debug point 'set_commit_parent_100' from @@GLOBAL.debug
[connection slave]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:1";
BEGIN;
[connection rpl_slave_connection_2]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:2";
BEGIN;
[connection rpl_slave_connection_3]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:3";
BEGIN;
[connection slave1]
include/rpl/start_applier.inc
include/assert.inc [Replica parallel type is LOGICAL_CLOCK]
include/assert.inc [MTS worker thread count is correct]
include/assert.inc [Replica preserve commit order is 1]
include/assert.inc [Replica transaction retries has correct configured value]
include/assert.inc [InnoDB lock wait timeout has correct configured value]
[connection rpl_slave_connection_2]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
include/wait_condition.inc [First worker must wait on commit order]
[connection rpl_slave_connection_3]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
include/wait_condition.inc [Second worker must wait on commit order]
[connection slave1]
SET GLOBAL read_only = ON;
[connection slave]
include/wait_condition.inc [Client connection must wait for state]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave1]
[connection slave]
include/rpl/wait_for_applier_error.inc [errno=1213]
[connection slave1]
SET GLOBAL read_only = OFF;
[connection slave]
include/rpl/start_applier.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/diff.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/stop_applier.inc
[connection master]
INSERT INTO t1 VALUES(13, 13);
[connection slave]
[connection master]
# Adding debug point 'set_commit_parent_100' to @@GLOBAL.debug
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:4";
ALTER TABLE t2 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=1;
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:5";
INSERT INTO t1 VALUES(14, NULL);
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:6";
INSERT INTO t1 VALUES(15, NULL);
SET GTID_NEXT = AUTOMATIC;
# Removing debug point 'set_commit_parent_100' from @@GLOBAL.debug
[connection slave]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:4";
BEGIN;
[connection rpl_slave_connection_2]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:5";
BEGIN;
[connection rpl_slave_connection_3]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:6";
BEGIN;
[connection slave1]
include/rpl/start_applier.inc
include/assert.inc [Replica parallel type is LOGICAL_CLOCK]
include/assert.inc [MTS worker thread count is correct]
include/assert.inc [Replica preserve commit order is 1]
include/assert.inc [Replica transaction retries has correct configured value]
include/assert.inc [InnoDB lock wait timeout has correct configured value]
[connection rpl_slave_connection_2]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
include/wait_condition.inc [First worker must wait on commit order]
[connection slave1]
SET GLOBAL read_only = ON;
[connection slave]
include/wait_condition.inc [Client connection must wait for state]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave1]
include/wait_condition.inc [Coordinator must wait for workers to stop]
[connection rpl_slave_connection_3]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave]
include/rpl/wait_for_applier_error.inc [errno=3030]
[connection slave1]
SET GLOBAL read_only = OFF;
[connection slave]
include/rpl/start_applier.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/diff.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/stop_applier.inc

#
# TC2. `GLOBAL READ_ONLY=ON` issued by client and DML replicated
#      from source.
#------------------------------------------------------------------------
[connection master]
INSERT INTO t1 VALUES(16, 16);
[connection slave]
[connection master]
# Adding debug point 'set_commit_parent_100' to @@GLOBAL.debug
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:7";
INSERT INTO t1 VALUES(17, NULL);
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:8";
INSERT INTO t1 VALUES(18, NULL);
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:9";
INSERT INTO t1 VALUES(19, NULL);
SET GTID_NEXT = AUTOMATIC;
# Removing debug point 'set_commit_parent_100' from @@GLOBAL.debug
[connection slave]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:7";
BEGIN;
[connection rpl_slave_connection_2]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:8";
BEGIN;
[connection rpl_slave_connection_3]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:9";
BEGIN;
[connection slave1]
include/rpl/start_applier.inc
include/assert.inc [Replica parallel type is LOGICAL_CLOCK]
include/assert.inc [MTS worker thread count is correct]
include/assert.inc [Replica preserve commit order is 1]
include/assert.inc [Replica transaction retries has correct configured value]
include/assert.inc [InnoDB lock wait timeout has correct configured value]
[connection rpl_slave_connection_2]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
include/wait_condition.inc [First worker must wait on commit order]
[connection rpl_slave_connection_3]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
include/wait_condition.inc [Second worker must wait on commit order]
[connection slave1]
SET GLOBAL read_only = ON;
[connection slave]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave1]
[connection slave]
include/rpl/wait_for_applier_error.inc [errno=1213]
[connection slave1]
SET GLOBAL read_only = OFF;
[connection slave]
include/rpl/start_applier.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/diff.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/stop_applier.inc
[connection master]
INSERT INTO t1 VALUES(20, 20);
[connection slave]
[connection master]
# Adding debug point 'set_commit_parent_100' to @@GLOBAL.debug
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:10";
INSERT INTO t1 VALUES(21, NULL);
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:11";
INSERT INTO t1 VALUES(22, NULL);
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:12";
INSERT INTO t1 VALUES(23, NULL);
SET GTID_NEXT = AUTOMATIC;
# Removing debug point 'set_commit_parent_100' from @@GLOBAL.debug
[connection slave]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:10";
BEGIN;
[connection rpl_slave_connection_2]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:11";
BEGIN;
[connection rpl_slave_connection_3]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:12";
BEGIN;
[connection slave1]
include/rpl/start_applier.inc
include/assert.inc [Replica parallel type is LOGICAL_CLOCK]
include/assert.inc [MTS worker thread count is correct]
include/assert.inc [Replica preserve commit order is 1]
include/assert.inc [Replica transaction retries has correct configured value]
include/assert.inc [InnoDB lock wait timeout has correct configured value]
[connection rpl_slave_connection_2]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
include/wait_condition.inc [First worker must wait on commit order]
[connection slave1]
SET GLOBAL read_only = ON;
[connection slave]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave1]
include/wait_condition.inc [Coordinator must wait for workers to stop]
[connection rpl_slave_connection_3]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave]
include/rpl/wait_for_applier_error.inc [errno=3030]
[connection slave1]
SET GLOBAL read_only = OFF;
[connection slave]
include/rpl/start_applier.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/diff.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/stop_applier.inc

#
# TC3. `FLUSH TABLES WITH READ LOCK` issued by client and DDL replicated
#      from source.
#------------------------------------------------------------------------
[connection master]
INSERT INTO t1 VALUES(24, 24);
[connection slave]
[connection master]
# Adding debug point 'set_commit_parent_100' to @@GLOBAL.debug
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:13";
ALTER TABLE t2 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=1;
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:14";
INSERT INTO t1 VALUES(25, NULL);
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:15";
INSERT INTO t1 VALUES(26, NULL);
SET GTID_NEXT = AUTOMATIC;
# Removing debug point 'set_commit_parent_100' from @@GLOBAL.debug
[connection slave]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:13";
BEGIN;
[connection rpl_slave_connection_2]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:14";
BEGIN;
[connection rpl_slave_connection_3]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:15";
BEGIN;
[connection slave1]
include/rpl/start_applier.inc
include/assert.inc [Replica parallel type is LOGICAL_CLOCK]
include/assert.inc [MTS worker thread count is correct]
include/assert.inc [Replica preserve commit order is 1]
include/assert.inc [Replica transaction retries has correct configured value]
include/assert.inc [InnoDB lock wait timeout has correct configured value]
[connection rpl_slave_connection_2]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
include/wait_condition.inc [First worker must wait on commit order]
[connection rpl_slave_connection_3]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
include/wait_condition.inc [Second worker must wait on commit order]
[connection slave1]
FLUSH TABLES WITH READ LOCK;
[connection slave]
include/wait_condition.inc [Client connection must wait for state]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave1]
include/rpl/wait_for_replica_status.inc [Replica_SQL_Running_State]
[connection slave1]
UNLOCK TABLES;
[connection slave]
include/rpl/wait_for_applier_error.inc [errno=1213]
[connection slave1]
UNLOCK TABLES;
[connection slave]
include/rpl/start_applier.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/diff.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/stop_applier.inc
[connection master]
INSERT INTO t1 VALUES(27, 27);
[connection slave]
[connection master]
# Adding debug point 'set_commit_parent_100' to @@GLOBAL.debug
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:16";
ALTER TABLE t2 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=1;
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:17";
INSERT INTO t1 VALUES(28, NULL);
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:18";
INSERT INTO t1 VALUES(29, NULL);
SET GTID_NEXT = AUTOMATIC;
# Removing debug point 'set_commit_parent_100' from @@GLOBAL.debug
[connection slave]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:16";
BEGIN;
[connection rpl_slave_connection_2]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:17";
BEGIN;
[connection rpl_slave_connection_3]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:18";
BEGIN;
[connection slave1]
include/rpl/start_applier.inc
include/assert.inc [Replica parallel type is LOGICAL_CLOCK]
include/assert.inc [MTS worker thread count is correct]
include/assert.inc [Replica preserve commit order is 1]
include/assert.inc [Replica transaction retries has correct configured value]
include/assert.inc [InnoDB lock wait timeout has correct configured value]
[connection rpl_slave_connection_2]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
include/wait_condition.inc [First worker must wait on commit order]
[connection slave1]
FLUSH TABLES WITH READ LOCK;
[connection slave]
include/wait_condition.inc [Client connection must wait for state]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave1]
include/rpl/wait_for_replica_status.inc [Replica_SQL_Running_State]
[connection slave1]
UNLOCK TABLES;
include/wait_condition.inc [Coordinator must wait for workers to stop]
[connection rpl_slave_connection_3]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave]
include/rpl/wait_for_applier_error.inc [errno=3030]
[connection slave1]
UNLOCK TABLES;
[connection slave]
include/rpl/start_applier.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/diff.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/stop_applier.inc

#
# TC4. `FLUSH TABLES WITH READ LOCK` issued by client and DML replicated
#      from source.
#------------------------------------------------------------------------
[connection master]
INSERT INTO t1 VALUES(30, 30);
[connection slave]
[connection master]
# Adding debug point 'set_commit_parent_100' to @@GLOBAL.debug
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:19";
INSERT INTO t1 VALUES(31, NULL);
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:20";
INSERT INTO t1 VALUES(32, NULL);
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:21";
INSERT INTO t1 VALUES(33, NULL);
SET GTID_NEXT = AUTOMATIC;
# Removing debug point 'set_commit_parent_100' from @@GLOBAL.debug
[connection slave]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:19";
BEGIN;
[connection rpl_slave_connection_2]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:20";
BEGIN;
[connection rpl_slave_connection_3]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:21";
BEGIN;
[connection slave1]
include/rpl/start_applier.inc
include/assert.inc [Replica parallel type is LOGICAL_CLOCK]
include/assert.inc [MTS worker thread count is correct]
include/assert.inc [Replica preserve commit order is 1]
include/assert.inc [Replica transaction retries has correct configured value]
include/assert.inc [InnoDB lock wait timeout has correct configured value]
[connection rpl_slave_connection_2]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
include/wait_condition.inc [First worker must wait on commit order]
[connection rpl_slave_connection_3]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
include/wait_condition.inc [Second worker must wait on commit order]
[connection slave1]
FLUSH TABLES WITH READ LOCK;
[connection slave]
include/wait_condition.inc [Client connection must wait for state]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave1]
include/rpl/wait_for_replica_status.inc [Replica_SQL_Running_State]
[connection slave1]
UNLOCK TABLES;
[connection slave]
include/rpl/wait_for_applier_error.inc [errno=1213]
[connection slave1]
UNLOCK TABLES;
[connection slave]
include/rpl/start_applier.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/diff.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/stop_applier.inc
[connection master]
INSERT INTO t1 VALUES(34, 34);
[connection slave]
[connection master]
# Adding debug point 'set_commit_parent_100' to @@GLOBAL.debug
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:22";
INSERT INTO t1 VALUES(35, NULL);
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:23";
INSERT INTO t1 VALUES(36, NULL);
SET GTID_NEXT = AUTOMATIC;
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:24";
INSERT INTO t1 VALUES(37, NULL);
SET GTID_NEXT = AUTOMATIC;
# Removing debug point 'set_commit_parent_100' from @@GLOBAL.debug
[connection slave]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:22";
BEGIN;
[connection rpl_slave_connection_2]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:23";
BEGIN;
[connection rpl_slave_connection_3]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:24";
BEGIN;
[connection slave1]
include/rpl/start_applier.inc
include/assert.inc [Replica parallel type is LOGICAL_CLOCK]
include/assert.inc [MTS worker thread count is correct]
include/assert.inc [Replica preserve commit order is 1]
include/assert.inc [Replica transaction retries has correct configured value]
include/assert.inc [InnoDB lock wait timeout has correct configured value]
[connection rpl_slave_connection_2]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
include/wait_condition.inc [First worker must wait on commit order]
[connection slave1]
FLUSH TABLES WITH READ LOCK;
[connection slave]
include/wait_condition.inc [Client connection must wait for state]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave1]
include/rpl/wait_for_replica_status.inc [Replica_SQL_Running_State]
[connection slave1]
UNLOCK TABLES;
include/wait_condition.inc [Coordinator must wait for workers to stop]
[connection rpl_slave_connection_3]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave]
include/rpl/wait_for_applier_error.inc [errno=3030]
[connection slave1]
UNLOCK TABLES;
[connection slave]
include/rpl/start_applier.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/diff.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/stop_applier.inc

#
# TC5. DML issued by client and `XA PREPARE` replicated from source.
#------------------------------------------------------------------------
[connection master]
INSERT INTO t1 VALUES(41, 41);
[connection slave]
[connection master]
# Adding debug point 'set_commit_parent_100' to @@GLOBAL.debug
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:25";
XA START 'xa1'; INSERT INTO t1 VALUES(42, NULL); XA END 'xa1'; XA PREPARE 'xa1';
SET GTID_NEXT = AUTOMATIC;
# Removing debug point 'set_commit_parent_100' from @@GLOBAL.debug
[connection slave]
SET GTID_NEXT = "aaaaaaaa-1111-bbbb-2222-cccccccccccc:25";
BEGIN;
[connection slave1]
include/rpl/start_applier.inc
include/assert.inc [Replica parallel type is LOGICAL_CLOCK]
include/assert.inc [MTS worker thread count is correct]
include/assert.inc [Replica preserve commit order is 1]
include/assert.inc [Replica transaction retries has correct configured value]
include/assert.inc [InnoDB lock wait timeout has correct configured value]
[connection slave1]
BEGIN; INSERT INTO t1 VALUES(42, NULL);
[connection slave]
include/wait_condition.inc [Client connection must wait for state]
ROLLBACK;
SET GTID_NEXT = AUTOMATIC;
[connection slave1]
[connection slave]
include/rpl/wait_for_applier_error.inc [errno=1205]
[connection slave1]
ROLLBACK;
[connection master]
XA COMMIT 'xa1';
[connection slave]
include/rpl/start_applier.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/diff.inc
[connection master]
include/rpl/sync_to_replica.inc
include/rpl/stop_applier.inc
include/rpl/start_applier.inc
[connection master]
DROP TABLE t1;
DROP TABLE t2;
include/rpl/deinit.inc
[connection slave]
SET GLOBAL replica_parallel_type = 'SAVED_REPLICA_PARALLEL_TYPE';
Warnings:
Warning	1287	'@@replica_parallel_type' is deprecated and will be removed in a future release.
SET GLOBAL replica_parallel_workers = SAVED_REPLICA_PARALLEL_WORKERS;
SET GLOBAL replica_preserve_commit_order = SAVED_REPLICA_PRESERVE_COMMIT_ORDER;
SET GLOBAL replica_transaction_retries = SAVED_REPLICA_TRANSACTION_RETRIES;
SET GLOBAL innodb_lock_wait_timeout = SAVED_INNODB_LOCK_WAIT_TIMEOUT;
SET GLOBAL read_only = SAVED_READ_ONLY;
