------ Run plugin ------------------------------------------------
INSTALL PLUGIN test_sql_stmt SONAME 'TEST_SQL_STMT';
------ Stop plugin -----------------------------------------------
UNINSTALL PLUGIN test_sql_stmt;
------ plugin log ------------------------------------------------
========================================================================
Test in a server thread
[srv_session_open]
CHANGE DATABASE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_INIT_DB: db_name[test]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CREATE TABLE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE TABLE t1 (a INT, b INT, c INT, UNIQUE (A), UNIQUE(B))]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
INSERT VALUES INTO THE TABLE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[INSERT INTO t1 VALUES(1, 12, 1111), (2, 11, 2222),(3, 10, 3333), (4, 9, 4444),(5, 8, 5555), (6, 7, 6666),(7, 6, 7777), (8, 5, -1111),(9, 4, -2222), (10, 3, -3333),(11, 2, -4444), (12, 1, -5555)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  12
	[end] last insert id: 0
	[end] message: Records: 12  Duplicates: 0  Warnings: 0
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE PROCEDURE proc_set_out_params(   OUT v_str_1 CHAR(32),    OUT v_dbl_1 DOUBLE(4, 2),    OUT v_dec_1 DECIMAL(6, 3),    OUT v_int_1 INT)BEGIN    SET v_str_1 = 'test_1';    SET v_dbl_1 = 12.34;    SET v_dec_1 = 567.891;    SET v_int_1 = 2345; END]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  1
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE PROCEDURE verify_user_variables_are_null(v_str_1 CHAR(32),    v_dbl_1 DOUBLE(4, 2),    v_dec_1 DECIMAL(6, 3),    v_int_1 INT)BEGIN DECLARE unexpected CONDITION FOR SQLSTATE '45000';  IF v_str_1 is not null THEN    SIGNAL unexpected;  ELSEIF v_dbl_1 is not null THEN    SIGNAL unexpected;  ELSEIF v_dec_1 is not null THEN    SIGNAL unexpected;  ELSEIF v_int_1 is not null THEN    SIGNAL unexpected;  END IF;END]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  1
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE PROCEDURE verify_user_variables_are_set(v_str_1 CHAR(32),    v_dbl_1 DOUBLE(4, 2),    v_dec_1 DECIMAL(6, 3),    v_int_1 INT)BEGIN DECLARE unexpected CONDITION FOR SQLSTATE '45000';  IF v_str_1 != 'test_1' THEN    SIGNAL unexpected;  ELSEIF v_dbl_1 != 12.34 THEN    SIGNAL unexpected;  ELSEIF v_dec_1 != 567.891 THEN    SIGNAL unexpected;  ELSEIF v_int_1 != 2345 THEN    SIGNAL unexpected;  END IF;END]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  1
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
##################################################################
test COM_STMT_EXECUTE and FETCH AFTER CLOSE
##################################################################
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[SELECT * from t1 where a > ? and b < ?]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITH PARAMETERS AND CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [1]
handle_start_column_metadata
handle_end_column_metadata
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][1]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[end] server status: 66
	[end] server status: AUTOCOMMIT CURSOR_EXISTS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITH WRONG NO OF PARAM
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [1]
handle_error
[1210][HY000][Incorrect arguments to COM_STMT_EXECUTE]
------------------------------------------------------------------
FETCH ONE ROW FROM THE CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_FETCH: stmt_id [1]
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][1]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t1.a][  1][6]
	[meta] current col: 1
		[data][t1.b][  1][7]
	[meta] current col: 2
		[data][t1.c][  4][6666]

	[end] server status: 66
	[end] server status: AUTOCOMMIT CURSOR_EXISTS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
FETCH TWO ROWS FROM THE CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_FETCH: stmt_id [1]
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][1]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t1.a][  1][6]
	[meta] current col: 1
		[data][t1.b][  1][7]
	[meta] current col: 2
		[data][t1.c][  4][6666]

	[meta] current col: 0
		[data][t1.a][  1][7]
	[meta] current col: 1
		[data][t1.b][  1][6]
	[meta] current col: 2
		[data][t1.c][  4][7777]

	[meta] current col: 0
		[data][t1.a][  1][8]
	[meta] current col: 1
		[data][t1.b][  1][5]
	[meta] current col: 2
		[data][t1.c][  5][-1111]

	[end] server status: 66
	[end] server status: AUTOCOMMIT CURSOR_EXISTS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CLOSE THE STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [1]
------------------------------------------------------------------
CLOSE NON-EXISTING STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [100001]
------------------------------------------------------------------
TRY TO FETCH ONE ROW FROM A DEALLOCATED(CLOSED) PS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_FETCH: stmt_id [1]
handle_error
[1243][HY000][Unknown prepared statement handler (1) given to mysql_stmt_precheck]
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE with cursor
##################################################################
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[SELECT * from t1 where a > ? and b < ?]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE THE PS FOR OPEN CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [2]
handle_start_column_metadata
handle_end_column_metadata
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][2]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[end] server status: 66
	[end] server status: AUTOCOMMIT CURSOR_EXISTS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
FETCH ONE ROW
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_FETCH: stmt_id [2]
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][2]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t1.a][  2][12]
	[meta] current col: 1
		[data][t1.b][  1][1]
	[meta] current col: 2
		[data][t1.c][  5][-5555]

	[end] server status: 66
	[end] server status: AUTOCOMMIT CURSOR_EXISTS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
RESET THE STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_RESET: stmt_id [2]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][2]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t1.a][  2][12]
	[meta] current col: 1
		[data][t1.b][  1][1]
	[meta] current col: 2
		[data][t1.c][  5][-5555]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
RESET NON-EXISTING STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_RESET: stmt_id [199999]
handle_error
[1243][HY000][Unknown prepared statement handler (199999) given to mysql_stmt_precheck]
------------------------------------------------------------------
TRY TO FETCH ONE ROW FROM THE PS WITH REMOVED CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_FETCH: stmt_id [2]
handle_error
[1421][HY000][The statement (2) has no open cursor.]
------------------------------------------------------------------
CLOSE THE STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [2]
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE without cursor
##################################################################
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[SELECT * from t1 where a > ? and b > ?]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE THE PS WITHOUT CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [3]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][3]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t1.a][  1][9]
	[meta] current col: 1
		[data][t1.b][  1][4]
	[meta] current col: 2
		[data][t1.c][  5][-2222]

	[meta] current col: 0
		[data][t1.a][  1][8]
	[meta] current col: 1
		[data][t1.b][  1][5]
	[meta] current col: 2
		[data][t1.c][  5][-1111]

	[meta] current col: 0
		[data][t1.a][  1][7]
	[meta] current col: 1
		[data][t1.b][  1][6]
	[meta] current col: 2
		[data][t1.c][  4][7777]

	[meta] current col: 0
		[data][t1.a][  1][6]
	[meta] current col: 1
		[data][t1.b][  1][7]
	[meta] current col: 2
		[data][t1.c][  4][6666]

	[meta] current col: 0
		[data][t1.a][  1][5]
	[meta] current col: 1
		[data][t1.b][  1][8]
	[meta] current col: 2
		[data][t1.c][  4][5555]

	[meta] current col: 0
		[data][t1.a][  1][4]
	[meta] current col: 1
		[data][t1.b][  1][9]
	[meta] current col: 2
		[data][t1.c][  4][4444]

	[meta] current col: 0
		[data][t1.a][  1][3]
	[meta] current col: 1
		[data][t1.b][  2][10]
	[meta] current col: 2
		[data][t1.c][  4][3333]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
TRY TO FETCH ONE ROW FROM A PS WITHOUT CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_FETCH: stmt_id [3]
handle_error
[1421][HY000][The statement (3) has no open cursor.]
------------------------------------------------------------------
TRY TO RESET THE CURSOR FROM A PS WITHOUT CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_RESET: stmt_id [3]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][3]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
TRY TO CLOSE THE CURSOR FROM A PS WITHOUT CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [3]
------------------------------------------------------------------
##################################################################
Test ps with different data-types
##################################################################
CREATE TABLE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE TABLE t2( c1  tinyint, c2  smallint, c3  mediumint, c4  int, c5  integer, c6  bigint, c7  float, c8  double, c9 date)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[INSERT INTO t2(c1, c2, c3, c4, c5, c6, c7, c8, c9) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?)]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITH PARAMETERS AND CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_error
[1210][HY000][Incorrect arguments to mysqld_stmt_execute]
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[SELECT * FROM t2]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c1
		[meta][field] org col name: c1
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: TINY (1)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c2
		[meta][field] org col name: c2
		[meta][field] length: 6
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: SHORT (2)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c3
		[meta][field] org col name: c3
		[meta][field] length: 9
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: INT24 (9)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c4
		[meta][field] org col name: c4
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c5
		[meta][field] org col name: c5
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c6
		[meta][field] org col name: c6
		[meta][field] length: 20
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c7
		[meta][field] org col name: c7
		[meta][field] length: 12
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: FLOAT (4)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c8
		[meta][field] org col name: c8
		[meta][field] length: 22
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c9
		[meta][field] org col name: c9
		[meta][field] length: 10
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: DATE (10)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t2.c1][  1][2]
	[meta] current col: 1
		[data][t2.c2][  1][2]
	[meta] current col: 2
		[data][t2.c3][  2][11]
	[meta] current col: 3
		[data][t2.c4][  2][11]
	[meta] current col: 4
		[data][t2.c5][  2][11]
	[meta] current col: 5
		[data][t2.c6][  2][21]
	[meta] current col: 6
		[data][t2.c7][  1][3]
	[meta] current col: 7
		[data][t2.c8][  8][6576.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][3]
	[meta] current col: 1
		[data][t2.c2][  1][3]
	[meta] current col: 2
		[data][t2.c3][  2][12]
	[meta] current col: 3
		[data][t2.c4][  2][12]
	[meta] current col: 4
		[data][t2.c5][  2][12]
	[meta] current col: 5
		[data][t2.c6][  2][22]
	[meta] current col: 6
		[data][t2.c7][  1][4]
	[meta] current col: 7
		[data][t2.c8][  8][6577.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][4]
	[meta] current col: 1
		[data][t2.c2][  1][4]
	[meta] current col: 2
		[data][t2.c3][  2][13]
	[meta] current col: 3
		[data][t2.c4][  2][13]
	[meta] current col: 4
		[data][t2.c5][  2][13]
	[meta] current col: 5
		[data][t2.c6][  2][23]
	[meta] current col: 6
		[data][t2.c7][  1][5]
	[meta] current col: 7
		[data][t2.c8][  8][6578.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][5]
	[meta] current col: 1
		[data][t2.c2][  1][5]
	[meta] current col: 2
		[data][t2.c3][  2][14]
	[meta] current col: 3
		[data][t2.c4][  2][14]
	[meta] current col: 4
		[data][t2.c5][  2][14]
	[meta] current col: 5
		[data][t2.c6][  2][24]
	[meta] current col: 6
		[data][t2.c7][  1][6]
	[meta] current col: 7
		[data][t2.c8][  8][6579.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][6]
	[meta] current col: 1
		[data][t2.c2][  1][6]
	[meta] current col: 2
		[data][t2.c3][  2][15]
	[meta] current col: 3
		[data][t2.c4][  2][15]
	[meta] current col: 4
		[data][t2.c5][  2][15]
	[meta] current col: 5
		[data][t2.c6][  2][25]
	[meta] current col: 6
		[data][t2.c7][  1][7]
	[meta] current col: 7
		[data][t2.c8][  8][6580.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][7]
	[meta] current col: 1
		[data][t2.c2][  1][7]
	[meta] current col: 2
		[data][t2.c3][  2][16]
	[meta] current col: 3
		[data][t2.c4][  2][16]
	[meta] current col: 4
		[data][t2.c5][  2][16]
	[meta] current col: 5
		[data][t2.c6][  2][26]
	[meta] current col: 6
		[data][t2.c7][  1][8]
	[meta] current col: 7
		[data][t2.c8][  8][6581.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][8]
	[meta] current col: 1
		[data][t2.c2][  1][8]
	[meta] current col: 2
		[data][t2.c3][  2][17]
	[meta] current col: 3
		[data][t2.c4][  2][17]
	[meta] current col: 4
		[data][t2.c5][  2][17]
	[meta] current col: 5
		[data][t2.c6][  2][27]
	[meta] current col: 6
		[data][t2.c7][  1][9]
	[meta] current col: 7
		[data][t2.c8][  8][6582.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][9]
	[meta] current col: 1
		[data][t2.c2][  1][9]
	[meta] current col: 2
		[data][t2.c3][  2][18]
	[meta] current col: 3
		[data][t2.c4][  2][18]
	[meta] current col: 4
		[data][t2.c5][  2][18]
	[meta] current col: 5
		[data][t2.c6][  2][28]
	[meta] current col: 6
		[data][t2.c7][  2][10]
	[meta] current col: 7
		[data][t2.c8][  8][6583.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[end] server status: 34
	[end] server status: AUTOCOMMIT QUERY_NO_INDEX_USED 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [4]
------------------------------------------------------------------
##################################################################
Test COM_STMT_SEND_LONG_DATA
##################################################################
CREATE TABLE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE TABLE test_long_data(col1 int, col2 long varchar)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[INSERT INTO test_long_data(col1, col2) VALUES(?, ?)]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
SEND PARAMETER AS COM_STMT_SEND_LONG_DATA
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_SEND_LONG_DATA: stmt_id [5]
------------------------------------------------------------------
APPEND TO THE SAME COLUMN
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_SEND_LONG_DATA: stmt_id [5]
------------------------------------------------------------------
EXECUTE PS WITH LONG DATA CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [5]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][5]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 4294967292
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: LONG_BLOB (251)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[SELECT * from test_long_data]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][5]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 4294967292
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: LONG_BLOB (251)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: test_long_data
		[meta][field] org table name: test_long_data
		[meta][field] col name: col1
		[meta][field] org col name: col1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: test_long_data
		[meta][field] org table name: test_long_data
		[meta][field] col name: col2
		[meta][field] org col name: col2
		[meta][field] length: 16777215
		[meta][field] charsetnr: 255
		[meta][field] flags: 16 (BLOB )
		[meta][field] decimals: 0
		[meta][field] type: BLOB (252)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][test_long_data.col1][  1][4]
	[meta] current col: 1
		[data][test_long_data.col2][ 16][Catalin Besleaga]

	[end] server status: 34
	[end] server status: AUTOCOMMIT QUERY_NO_INDEX_USED 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
APPEND TO A NON EXISTING STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_SEND_LONG_DATA: stmt_id [199999]
------------------------------------------------------------------
ERRORS ONLY SHOW AT FIRST EXECUTION OF COM_STMT_EXECUTE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [199999]
handle_error
[1243][HY000][Unknown prepared statement handler (199999) given to mysql_stmt_precheck]
------------------------------------------------------------------
APPEND DATA TO NON EXISTING PARAMETER
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_SEND_LONG_DATA: stmt_id [5]
------------------------------------------------------------------
ERRORS ONLY SHOW AT FIRST EXECUTION OF COM_STMT_EXECUTE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [5]
handle_error
[1210][HY000][Incorrect arguments to COM_STMT_EXECUTE]
------------------------------------------------------------------
TRY TO CLOSE THE CURSOR FROM A PS WITHOUT CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [5]
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE with SELECT nested in CALL
##################################################################
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE TABLE t3(a1 INT, a2 CHAR(32), a3 DOUBLE(4, 2), a4 DECIMAL(3, 1))]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  1
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE TABLE t4(b0 INT, b1 INT, b2 CHAR(32), b3 DOUBLE(4, 2), b4 DECIMAL(3, 1))]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  1
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[INSERT INTO t3 VALUES(1, '11', 12.34, 56.7), (2, '12', 56.78, 90.1), (3, '13', 23.45, 67.8)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  3
	[end] last insert id: 0
	[end] message: Records: 3  Duplicates: 0  Warnings: 0
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[INSERT INTO t4 VALUES(100, 10, '110', 70.70, 10.1), (200, 20, '120', 80.80, 20.2), (300, 30, '130', 90.90, 30.3)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  3
	[end] last insert id: 0
	[end] message: Records: 3  Duplicates: 0  Warnings: 0
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE PROCEDURE p1(   IN v0 INT,    OUT v_str_1 CHAR(32),    OUT v_dbl_1 DOUBLE(4, 2),    OUT v_dec_1 DECIMAL(6, 3),    OUT v_int_1 INT,    IN v1 INT,    INOUT v_str_2 CHAR(64),    INOUT v_dbl_2 DOUBLE(5, 3),    INOUT v_dec_2 DECIMAL(7, 4),    INOUT v_int_2 INT)BEGIN    SET v0 = -1;    SET v1 = -1;    SET v_str_1 = 'test_1';    SET v_dbl_1 = 12.34;    SET v_dec_1 = 567.891;    SET v_int_1 = 2345;    SET v_str_2 = 'test_2';    SET v_dbl_2 = 67.891;    SET v_dec_2 = 234.6789;    SET v_int_2 = 6789;    SELECT * FROM t3;    SELECT * FROM t4; END]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  2
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[CALL p1(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [6]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][6]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  2][10]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a1
		[meta][field] org col name: a1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a2
		[meta][field] org col name: a2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a3
		[meta][field] org col name: a3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a4
		[meta][field] org col name: a4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t3.a1][  1][1]
	[meta] current col: 1
		[data][t3.a2][  2][11]
	[meta] current col: 2
		[data][t3.a3][  5][12.34]
	[meta] current col: 3
		[data][t3.a4][  4][56.7]

	[meta] current col: 0
		[data][t3.a1][  1][2]
	[meta] current col: 1
		[data][t3.a2][  2][12]
	[meta] current col: 2
		[data][t3.a3][  5][56.78]
	[meta] current col: 3
		[data][t3.a4][  4][90.1]

	[meta] current col: 0
		[data][t3.a1][  1][3]
	[meta] current col: 1
		[data][t3.a2][  2][13]
	[meta] current col: 2
		[data][t3.a3][  5][23.45]
	[meta] current col: 3
		[data][t3.a4][  4][67.8]

	[end] server status: 42
	[end] server status: AUTOCOMMIT MORE_RESULTS_EXISTS QUERY_NO_INDEX_USED 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][6]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  2][10]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a1
		[meta][field] org col name: a1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a2
		[meta][field] org col name: a2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a3
		[meta][field] org col name: a3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a4
		[meta][field] org col name: a4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t3.a1][  1][1]
	[meta] current col: 1
		[data][t3.a2][  2][11]
	[meta] current col: 2
		[data][t3.a3][  5][12.34]
	[meta] current col: 3
		[data][t3.a4][  4][56.7]

	[meta] current col: 0
		[data][t3.a1][  1][2]
	[meta] current col: 1
		[data][t3.a2][  2][12]
	[meta] current col: 2
		[data][t3.a3][  5][56.78]
	[meta] current col: 3
		[data][t3.a4][  4][90.1]

	[meta] current col: 0
		[data][t3.a1][  1][3]
	[meta] current col: 1
		[data][t3.a2][  2][13]
	[meta] current col: 2
		[data][t3.a3][  5][23.45]
	[meta] current col: 3
		[data][t3.a4][  4][67.8]

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b0
		[meta][field] org col name: b0
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b1
		[meta][field] org col name: b1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b2
		[meta][field] org col name: b2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b3
		[meta][field] org col name: b3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b4
		[meta][field] org col name: b4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t4.b0][  3][100]
	[meta] current col: 1
		[data][t4.b1][  2][10]
	[meta] current col: 2
		[data][t4.b2][  3][110]
	[meta] current col: 3
		[data][t4.b3][  5][70.70]
	[meta] current col: 4
		[data][t4.b4][  4][10.1]

	[meta] current col: 0
		[data][t4.b0][  3][200]
	[meta] current col: 1
		[data][t4.b1][  2][20]
	[meta] current col: 2
		[data][t4.b2][  3][120]
	[meta] current col: 3
		[data][t4.b3][  5][80.80]
	[meta] current col: 4
		[data][t4.b4][  4][20.2]

	[meta] current col: 0
		[data][t4.b0][  3][300]
	[meta] current col: 1
		[data][t4.b1][  2][30]
	[meta] current col: 2
		[data][t4.b2][  3][130]
	[meta] current col: 3
		[data][t4.b3][  5][90.90]
	[meta] current col: 4
		[data][t4.b4][  4][30.3]

	[end] server status: 42
	[end] server status: AUTOCOMMIT MORE_RESULTS_EXISTS QUERY_NO_INDEX_USED 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][6]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  2][10]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a1
		[meta][field] org col name: a1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a2
		[meta][field] org col name: a2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a3
		[meta][field] org col name: a3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a4
		[meta][field] org col name: a4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t3.a1][  1][1]
	[meta] current col: 1
		[data][t3.a2][  2][11]
	[meta] current col: 2
		[data][t3.a3][  5][12.34]
	[meta] current col: 3
		[data][t3.a4][  4][56.7]

	[meta] current col: 0
		[data][t3.a1][  1][2]
	[meta] current col: 1
		[data][t3.a2][  2][12]
	[meta] current col: 2
		[data][t3.a3][  5][56.78]
	[meta] current col: 3
		[data][t3.a4][  4][90.1]

	[meta] current col: 0
		[data][t3.a1][  1][3]
	[meta] current col: 1
		[data][t3.a2][  2][13]
	[meta] current col: 2
		[data][t3.a3][  5][23.45]
	[meta] current col: 3
		[data][t3.a4][  4][67.8]

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b0
		[meta][field] org col name: b0
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b1
		[meta][field] org col name: b1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b2
		[meta][field] org col name: b2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b3
		[meta][field] org col name: b3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b4
		[meta][field] org col name: b4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t4.b0][  3][100]
	[meta] current col: 1
		[data][t4.b1][  2][10]
	[meta] current col: 2
		[data][t4.b2][  3][110]
	[meta] current col: 3
		[data][t4.b3][  5][70.70]
	[meta] current col: 4
		[data][t4.b4][  4][10.1]

	[meta] current col: 0
		[data][t4.b0][  3][200]
	[meta] current col: 1
		[data][t4.b1][  2][20]
	[meta] current col: 2
		[data][t4.b2][  3][120]
	[meta] current col: 3
		[data][t4.b3][  5][80.80]
	[meta] current col: 4
		[data][t4.b4][  4][20.2]

	[meta] current col: 0
		[data][t4.b0][  3][300]
	[meta] current col: 1
		[data][t4.b1][  2][30]
	[meta] current col: 2
		[data][t4.b2][  3][130]
	[meta] current col: 3
		[data][t4.b3][  5][90.90]
	[meta] current col: 4
		[data][t4.b4][  4][30.3]

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_str_1
		[meta][field] org col name: v_str_1
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dbl_1
		[meta][field] org col name: v_dbl_1
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dec_1
		[meta][field] org col name: v_dec_1
		[meta][field] length: 8
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 3
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_int_1
		[meta][field] org col name: v_int_1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_str_2
		[meta][field] org col name: v_str_2
		[meta][field] length: 256
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dbl_2
		[meta][field] org col name: v_dbl_2
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 3
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dec_2
		[meta][field] org col name: v_dec_2
		[meta][field] length: 9
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 4
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_int_2
		[meta][field] org col name: v_int_2
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][p1.v_str_1][  6][test_1]
	[meta] current col: 1
		[data][p1.v_dbl_1][  5][12.34]
	[meta] current col: 2
		[data][p1.v_dec_1][  7][567.891]
	[meta] current col: 3
		[data][p1.v_int_1][  4][2345]
	[meta] current col: 4
		[data][p1.v_str_2][  6][test_2]
	[meta] current col: 5
		[data][p1.v_dbl_2][  6][67.891]
	[meta] current col: 6
		[data][p1.v_dec_2][  8][234.6789]
	[meta] current col: 7
		[data][p1.v_int_2][  4][6789]

	[end] server status: 4138
	[end] server status: AUTOCOMMIT MORE_RESULTS_EXISTS QUERY_NO_INDEX_USED PS_OUT_PARAMS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][6]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  2][10]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a1
		[meta][field] org col name: a1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a2
		[meta][field] org col name: a2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a3
		[meta][field] org col name: a3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a4
		[meta][field] org col name: a4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t3.a1][  1][1]
	[meta] current col: 1
		[data][t3.a2][  2][11]
	[meta] current col: 2
		[data][t3.a3][  5][12.34]
	[meta] current col: 3
		[data][t3.a4][  4][56.7]

	[meta] current col: 0
		[data][t3.a1][  1][2]
	[meta] current col: 1
		[data][t3.a2][  2][12]
	[meta] current col: 2
		[data][t3.a3][  5][56.78]
	[meta] current col: 3
		[data][t3.a4][  4][90.1]

	[meta] current col: 0
		[data][t3.a1][  1][3]
	[meta] current col: 1
		[data][t3.a2][  2][13]
	[meta] current col: 2
		[data][t3.a3][  5][23.45]
	[meta] current col: 3
		[data][t3.a4][  4][67.8]

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b0
		[meta][field] org col name: b0
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b1
		[meta][field] org col name: b1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b2
		[meta][field] org col name: b2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b3
		[meta][field] org col name: b3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b4
		[meta][field] org col name: b4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t4.b0][  3][100]
	[meta] current col: 1
		[data][t4.b1][  2][10]
	[meta] current col: 2
		[data][t4.b2][  3][110]
	[meta] current col: 3
		[data][t4.b3][  5][70.70]
	[meta] current col: 4
		[data][t4.b4][  4][10.1]

	[meta] current col: 0
		[data][t4.b0][  3][200]
	[meta] current col: 1
		[data][t4.b1][  2][20]
	[meta] current col: 2
		[data][t4.b2][  3][120]
	[meta] current col: 3
		[data][t4.b3][  5][80.80]
	[meta] current col: 4
		[data][t4.b4][  4][20.2]

	[meta] current col: 0
		[data][t4.b0][  3][300]
	[meta] current col: 1
		[data][t4.b1][  2][30]
	[meta] current col: 2
		[data][t4.b2][  3][130]
	[meta] current col: 3
		[data][t4.b3][  5][90.90]
	[meta] current col: 4
		[data][t4.b4][  4][30.3]

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_str_1
		[meta][field] org col name: v_str_1
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dbl_1
		[meta][field] org col name: v_dbl_1
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dec_1
		[meta][field] org col name: v_dec_1
		[meta][field] length: 8
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 3
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_int_1
		[meta][field] org col name: v_int_1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_str_2
		[meta][field] org col name: v_str_2
		[meta][field] length: 256
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dbl_2
		[meta][field] org col name: v_dbl_2
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 3
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dec_2
		[meta][field] org col name: v_dec_2
		[meta][field] length: 9
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 4
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_int_2
		[meta][field] org col name: v_int_2
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][p1.v_str_1][  6][test_1]
	[meta] current col: 1
		[data][p1.v_dbl_1][  5][12.34]
	[meta] current col: 2
		[data][p1.v_dec_1][  7][567.891]
	[meta] current col: 3
		[data][p1.v_int_1][  4][2345]
	[meta] current col: 4
		[data][p1.v_str_2][  6][test_2]
	[meta] current col: 5
		[data][p1.v_dbl_2][  6][67.891]
	[meta] current col: 6
		[data][p1.v_dec_2][  8][234.6789]
	[meta] current col: 7
		[data][p1.v_int_2][  4][6789]

	[end] server status: 34
	[end] server status: AUTOCOMMIT QUERY_NO_INDEX_USED 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CLOSE PS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [6]
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE with wrong data type
##################################################################
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[SELECT CONCAT(9< ?)]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITH PARAMETERS AND CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [7]
handle_start_column_metadata
handle_end_column_metadata
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][7]
	[meta] current col: 1
		[data][.column_no][  1][1]
	[meta] current col: 2
		[data][.param_no][  1][1]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: CONCAT(9< ?)
		[meta][field] org col name: 
		[meta][field] length: 3
		[meta][field] charsetnr: 33
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: CONCAT(9< ?)
		[meta][field] org col name: CONCAT(9< ?)
		[meta][field] length: 3
		[meta][field] charsetnr: 33
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[end] server status: 66
	[end] server status: AUTOCOMMIT CURSOR_EXISTS 
	[end] warning count:  1
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CLOSE PS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [7]
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE with out-params as placeholders
##################################################################
RESET VARIABLES THAT ARE GOING TO BE USED FOR OUT-PARAMS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[SET @my_v1=null, @my_v2=null, @my_v3=null, @my_v4=null]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[CALL proc_set_out_params(?, ?, ?, ?)]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITH PARAMETERS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [8]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_str_1
		[meta][field] org col name: v_str_1
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_dbl_1
		[meta][field] org col name: v_dbl_1
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_dec_1
		[meta][field] org col name: v_dec_1
		[meta][field] length: 8
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 3
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_int_1
		[meta][field] org col name: v_int_1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][proc_set_out_params.v_str_1][  6][test_1]
	[meta] current col: 1
		[data][proc_set_out_params.v_dbl_1][  5][12.34]
	[meta] current col: 2
		[data][proc_set_out_params.v_dec_1][  7][567.891]
	[meta] current col: 3
		[data][proc_set_out_params.v_int_1][  4][2345]

	[end] server status: 4106
	[end] server status: AUTOCOMMIT MORE_RESULTS_EXISTS PS_OUT_PARAMS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_str_1
		[meta][field] org col name: v_str_1
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_dbl_1
		[meta][field] org col name: v_dbl_1
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_dec_1
		[meta][field] org col name: v_dec_1
		[meta][field] length: 8
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 3
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_int_1
		[meta][field] org col name: v_int_1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][proc_set_out_params.v_str_1][  6][test_1]
	[meta] current col: 1
		[data][proc_set_out_params.v_dbl_1][  5][12.34]
	[meta] current col: 2
		[data][proc_set_out_params.v_dec_1][  7][567.891]
	[meta] current col: 3
		[data][proc_set_out_params.v_int_1][  4][2345]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
VERIFY THAT VARIABLES ARE STILL NULL AND OUT PRAMETERS WERE TRANSFERED IN METADATA
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CALL verify_user_variables_are_null(@my_v1, @my_v2, @my_v3, @my_v4)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CLOSE PS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [8]
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE with out-params as variables
##################################################################
RESET VARIABLES THAT ARE GOING TO BE USED FOR OUT-PARAMS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[SET @my_v1=null, @my_v2=null, @my_v3=null, @my_v4=null]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[CALL proc_set_out_params(@my_v1, @my_v2, @my_v3, @my_v4)]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITHOUT PARAMETERS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [9]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
VERIFY THAT VARIABLES ARE SET AND OUT PRAMETERS WERE NOT TRANSFERED IN METADATA
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CALL verify_user_variables_are_set(@my_v1, @my_v2, @my_v3, @my_v4)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CLOSE PS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [9]
------------------------------------------------------------------
##################################################################
Test COM_QUERY with out-params as placeholders
##################################################################
RESET VARIABLES THAT ARE GOING TO BE USED FOR OUT-PARAMS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[SET @my_v1=null, @my_v2=null, @my_v3=null, @my_v4=null]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[PREPARE stmt FROM 'CALL proc_set_out_params(?, ?, ?, ?)']
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: Statement prepared
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITHOUT PARAMETERS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[EXECUTE stmt USING @my_v1, @my_v2, @my_v3, @my_v4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
VERIFY THAT VARIABLES ARE SET AND OUT PRAMETERS WERE NOT TRANSFERED IN METADATA
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CALL verify_user_variables_are_set(@my_v1, @my_v2, @my_v3, @my_v4)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CLOSE PS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DEALLOCATE PREPARE stmt;]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE with wrong parameters
##################################################################
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[SELECT * from t1 where a = ?]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE THE PS WITH INVALID PARAMETER TYPE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [11]
handle_error
[1210][HY000][Incorrect arguments to mysqld_stmt_execute]
------------------------------------------------------------------
EXECUTE THE PS WITH BOOL PARAMETER TYPE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [11]
handle_error
[1210][HY000][Incorrect arguments to mysqld_stmt_execute]
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP TABLE IF EXISTS t1]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP TABLE IF EXISTS t2]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP TABLE IF EXISTS test_long_data]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP TABLE IF EXISTS t3]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP TABLE IF EXISTS t4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP PROCEDURE IF EXISTS p1]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP PROCEDURE IF EXISTS proc_set_out_params]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP PROCEDURE IF EXISTS verify_user_variables_are_null]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP PROCEDURE IF EXISTS verify_user_variables_are_set]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
[srv_session_close]
Follows threaded run
========================================================================
init thread
[srv_session_open]
CHANGE DATABASE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_INIT_DB: db_name[test]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CREATE TABLE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE TABLE t1 (a INT, b INT, c INT, UNIQUE (A), UNIQUE(B))]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
INSERT VALUES INTO THE TABLE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[INSERT INTO t1 VALUES(1, 12, 1111), (2, 11, 2222),(3, 10, 3333), (4, 9, 4444),(5, 8, 5555), (6, 7, 6666),(7, 6, 7777), (8, 5, -1111),(9, 4, -2222), (10, 3, -3333),(11, 2, -4444), (12, 1, -5555)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  12
	[end] last insert id: 0
	[end] message: Records: 12  Duplicates: 0  Warnings: 0
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE PROCEDURE proc_set_out_params(   OUT v_str_1 CHAR(32),    OUT v_dbl_1 DOUBLE(4, 2),    OUT v_dec_1 DECIMAL(6, 3),    OUT v_int_1 INT)BEGIN    SET v_str_1 = 'test_1';    SET v_dbl_1 = 12.34;    SET v_dec_1 = 567.891;    SET v_int_1 = 2345; END]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  1
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE PROCEDURE verify_user_variables_are_null(v_str_1 CHAR(32),    v_dbl_1 DOUBLE(4, 2),    v_dec_1 DECIMAL(6, 3),    v_int_1 INT)BEGIN DECLARE unexpected CONDITION FOR SQLSTATE '45000';  IF v_str_1 is not null THEN    SIGNAL unexpected;  ELSEIF v_dbl_1 is not null THEN    SIGNAL unexpected;  ELSEIF v_dec_1 is not null THEN    SIGNAL unexpected;  ELSEIF v_int_1 is not null THEN    SIGNAL unexpected;  END IF;END]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  1
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE PROCEDURE verify_user_variables_are_set(v_str_1 CHAR(32),    v_dbl_1 DOUBLE(4, 2),    v_dec_1 DECIMAL(6, 3),    v_int_1 INT)BEGIN DECLARE unexpected CONDITION FOR SQLSTATE '45000';  IF v_str_1 != 'test_1' THEN    SIGNAL unexpected;  ELSEIF v_dbl_1 != 12.34 THEN    SIGNAL unexpected;  ELSEIF v_dec_1 != 567.891 THEN    SIGNAL unexpected;  ELSEIF v_int_1 != 2345 THEN    SIGNAL unexpected;  END IF;END]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  1
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
##################################################################
test COM_STMT_EXECUTE and FETCH AFTER CLOSE
##################################################################
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[SELECT * from t1 where a > ? and b < ?]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITH PARAMETERS AND CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [1]
handle_start_column_metadata
handle_end_column_metadata
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][1]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[end] server status: 66
	[end] server status: AUTOCOMMIT CURSOR_EXISTS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITH WRONG NO OF PARAM
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [1]
handle_error
[1210][HY000][Incorrect arguments to COM_STMT_EXECUTE]
------------------------------------------------------------------
FETCH ONE ROW FROM THE CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_FETCH: stmt_id [1]
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][1]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t1.a][  1][6]
	[meta] current col: 1
		[data][t1.b][  1][7]
	[meta] current col: 2
		[data][t1.c][  4][6666]

	[end] server status: 66
	[end] server status: AUTOCOMMIT CURSOR_EXISTS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
FETCH TWO ROWS FROM THE CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_FETCH: stmt_id [1]
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][1]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t1.a][  1][6]
	[meta] current col: 1
		[data][t1.b][  1][7]
	[meta] current col: 2
		[data][t1.c][  4][6666]

	[meta] current col: 0
		[data][t1.a][  1][7]
	[meta] current col: 1
		[data][t1.b][  1][6]
	[meta] current col: 2
		[data][t1.c][  4][7777]

	[meta] current col: 0
		[data][t1.a][  1][8]
	[meta] current col: 1
		[data][t1.b][  1][5]
	[meta] current col: 2
		[data][t1.c][  5][-1111]

	[end] server status: 66
	[end] server status: AUTOCOMMIT CURSOR_EXISTS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CLOSE THE STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [1]
------------------------------------------------------------------
CLOSE NON-EXISTING STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [100001]
------------------------------------------------------------------
TRY TO FETCH ONE ROW FROM A DEALLOCATED(CLOSED) PS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_FETCH: stmt_id [1]
handle_error
[1243][HY000][Unknown prepared statement handler (1) given to mysql_stmt_precheck]
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE with cursor
##################################################################
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[SELECT * from t1 where a > ? and b < ?]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE THE PS FOR OPEN CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [2]
handle_start_column_metadata
handle_end_column_metadata
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][2]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[end] server status: 66
	[end] server status: AUTOCOMMIT CURSOR_EXISTS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
FETCH ONE ROW
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_FETCH: stmt_id [2]
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][2]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t1.a][  2][12]
	[meta] current col: 1
		[data][t1.b][  1][1]
	[meta] current col: 2
		[data][t1.c][  5][-5555]

	[end] server status: 66
	[end] server status: AUTOCOMMIT CURSOR_EXISTS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
RESET THE STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_RESET: stmt_id [2]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][2]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t1.a][  2][12]
	[meta] current col: 1
		[data][t1.b][  1][1]
	[meta] current col: 2
		[data][t1.c][  5][-5555]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
RESET NON-EXISTING STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_RESET: stmt_id [199999]
handle_error
[1243][HY000][Unknown prepared statement handler (199999) given to mysql_stmt_precheck]
------------------------------------------------------------------
TRY TO FETCH ONE ROW FROM THE PS WITH REMOVED CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_FETCH: stmt_id [2]
handle_error
[1421][HY000][The statement (2) has no open cursor.]
------------------------------------------------------------------
CLOSE THE STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [2]
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE without cursor
##################################################################
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[SELECT * from t1 where a > ? and b > ?]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE THE PS WITHOUT CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [3]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][3]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t1.a][  1][9]
	[meta] current col: 1
		[data][t1.b][  1][4]
	[meta] current col: 2
		[data][t1.c][  5][-2222]

	[meta] current col: 0
		[data][t1.a][  1][8]
	[meta] current col: 1
		[data][t1.b][  1][5]
	[meta] current col: 2
		[data][t1.c][  5][-1111]

	[meta] current col: 0
		[data][t1.a][  1][7]
	[meta] current col: 1
		[data][t1.b][  1][6]
	[meta] current col: 2
		[data][t1.c][  4][7777]

	[meta] current col: 0
		[data][t1.a][  1][6]
	[meta] current col: 1
		[data][t1.b][  1][7]
	[meta] current col: 2
		[data][t1.c][  4][6666]

	[meta] current col: 0
		[data][t1.a][  1][5]
	[meta] current col: 1
		[data][t1.b][  1][8]
	[meta] current col: 2
		[data][t1.c][  4][5555]

	[meta] current col: 0
		[data][t1.a][  1][4]
	[meta] current col: 1
		[data][t1.b][  1][9]
	[meta] current col: 2
		[data][t1.c][  4][4444]

	[meta] current col: 0
		[data][t1.a][  1][3]
	[meta] current col: 1
		[data][t1.b][  2][10]
	[meta] current col: 2
		[data][t1.c][  4][3333]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
TRY TO FETCH ONE ROW FROM A PS WITHOUT CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_FETCH: stmt_id [3]
handle_error
[1421][HY000][The statement (3) has no open cursor.]
------------------------------------------------------------------
TRY TO RESET THE CURSOR FROM A PS WITHOUT CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_RESET: stmt_id [3]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][3]
	[meta] current col: 1
		[data][.column_no][  1][3]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: a
		[meta][field] org col name: a
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: b
		[meta][field] org col name: b
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 16388 (UNIQUE_KEY PART_KEY )
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t1
		[meta][field] org table name: t1
		[meta][field] col name: c
		[meta][field] org col name: c
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
TRY TO CLOSE THE CURSOR FROM A PS WITHOUT CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [3]
------------------------------------------------------------------
##################################################################
Test ps with different data-types
##################################################################
CREATE TABLE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE TABLE t2( c1  tinyint, c2  smallint, c3  mediumint, c4  int, c5  integer, c6  bigint, c7  float, c8  double, c9 date)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[INSERT INTO t2(c1, c2, c3, c4, c5, c6, c7, c8, c9) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?)]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITH PARAMETERS AND CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_error
[1210][HY000][Incorrect arguments to mysqld_stmt_execute]
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[SELECT * FROM t2]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][4]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][9]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c1
		[meta][field] org col name: c1
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: TINY (1)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c2
		[meta][field] org col name: c2
		[meta][field] length: 6
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: SHORT (2)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c3
		[meta][field] org col name: c3
		[meta][field] length: 9
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: INT24 (9)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c4
		[meta][field] org col name: c4
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c5
		[meta][field] org col name: c5
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c6
		[meta][field] org col name: c6
		[meta][field] length: 20
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c7
		[meta][field] org col name: c7
		[meta][field] length: 12
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: FLOAT (4)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c8
		[meta][field] org col name: c8
		[meta][field] length: 22
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t2
		[meta][field] org table name: t2
		[meta][field] col name: c9
		[meta][field] org col name: c9
		[meta][field] length: 10
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: DATE (10)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t2.c1][  1][2]
	[meta] current col: 1
		[data][t2.c2][  1][2]
	[meta] current col: 2
		[data][t2.c3][  2][11]
	[meta] current col: 3
		[data][t2.c4][  2][11]
	[meta] current col: 4
		[data][t2.c5][  2][11]
	[meta] current col: 5
		[data][t2.c6][  2][21]
	[meta] current col: 6
		[data][t2.c7][  1][3]
	[meta] current col: 7
		[data][t2.c8][  8][6576.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][3]
	[meta] current col: 1
		[data][t2.c2][  1][3]
	[meta] current col: 2
		[data][t2.c3][  2][12]
	[meta] current col: 3
		[data][t2.c4][  2][12]
	[meta] current col: 4
		[data][t2.c5][  2][12]
	[meta] current col: 5
		[data][t2.c6][  2][22]
	[meta] current col: 6
		[data][t2.c7][  1][4]
	[meta] current col: 7
		[data][t2.c8][  8][6577.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][4]
	[meta] current col: 1
		[data][t2.c2][  1][4]
	[meta] current col: 2
		[data][t2.c3][  2][13]
	[meta] current col: 3
		[data][t2.c4][  2][13]
	[meta] current col: 4
		[data][t2.c5][  2][13]
	[meta] current col: 5
		[data][t2.c6][  2][23]
	[meta] current col: 6
		[data][t2.c7][  1][5]
	[meta] current col: 7
		[data][t2.c8][  8][6578.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][5]
	[meta] current col: 1
		[data][t2.c2][  1][5]
	[meta] current col: 2
		[data][t2.c3][  2][14]
	[meta] current col: 3
		[data][t2.c4][  2][14]
	[meta] current col: 4
		[data][t2.c5][  2][14]
	[meta] current col: 5
		[data][t2.c6][  2][24]
	[meta] current col: 6
		[data][t2.c7][  1][6]
	[meta] current col: 7
		[data][t2.c8][  8][6579.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][6]
	[meta] current col: 1
		[data][t2.c2][  1][6]
	[meta] current col: 2
		[data][t2.c3][  2][15]
	[meta] current col: 3
		[data][t2.c4][  2][15]
	[meta] current col: 4
		[data][t2.c5][  2][15]
	[meta] current col: 5
		[data][t2.c6][  2][25]
	[meta] current col: 6
		[data][t2.c7][  1][7]
	[meta] current col: 7
		[data][t2.c8][  8][6580.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][7]
	[meta] current col: 1
		[data][t2.c2][  1][7]
	[meta] current col: 2
		[data][t2.c3][  2][16]
	[meta] current col: 3
		[data][t2.c4][  2][16]
	[meta] current col: 4
		[data][t2.c5][  2][16]
	[meta] current col: 5
		[data][t2.c6][  2][26]
	[meta] current col: 6
		[data][t2.c7][  1][8]
	[meta] current col: 7
		[data][t2.c8][  8][6581.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][8]
	[meta] current col: 1
		[data][t2.c2][  1][8]
	[meta] current col: 2
		[data][t2.c3][  2][17]
	[meta] current col: 3
		[data][t2.c4][  2][17]
	[meta] current col: 4
		[data][t2.c5][  2][17]
	[meta] current col: 5
		[data][t2.c6][  2][27]
	[meta] current col: 6
		[data][t2.c7][  1][9]
	[meta] current col: 7
		[data][t2.c8][  8][6582.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[meta] current col: 0
		[data][t2.c1][  1][9]
	[meta] current col: 1
		[data][t2.c2][  1][9]
	[meta] current col: 2
		[data][t2.c3][  2][18]
	[meta] current col: 3
		[data][t2.c4][  2][18]
	[meta] current col: 4
		[data][t2.c5][  2][18]
	[meta] current col: 5
		[data][t2.c6][  2][28]
	[meta] current col: 6
		[data][t2.c7][  2][10]
	[meta] current col: 7
		[data][t2.c8][  8][6583.001]
	[meta] current col: 8
		[data][t2.c9][ 10][1988-12-20]

	[end] server status: 34
	[end] server status: AUTOCOMMIT QUERY_NO_INDEX_USED 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [4]
------------------------------------------------------------------
##################################################################
Test COM_STMT_SEND_LONG_DATA
##################################################################
CREATE TABLE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE TABLE test_long_data(col1 int, col2 long varchar)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[INSERT INTO test_long_data(col1, col2) VALUES(?, ?)]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
SEND PARAMETER AS COM_STMT_SEND_LONG_DATA
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_SEND_LONG_DATA: stmt_id [5]
------------------------------------------------------------------
APPEND TO THE SAME COLUMN
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_SEND_LONG_DATA: stmt_id [5]
------------------------------------------------------------------
EXECUTE PS WITH LONG DATA CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [5]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][5]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 4294967292
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: LONG_BLOB (251)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  1
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[SELECT * from test_long_data]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][5]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  1][2]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 4294967292
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: LONG_BLOB (251)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: test_long_data
		[meta][field] org table name: test_long_data
		[meta][field] col name: col1
		[meta][field] org col name: col1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: test_long_data
		[meta][field] org table name: test_long_data
		[meta][field] col name: col2
		[meta][field] org col name: col2
		[meta][field] length: 16777215
		[meta][field] charsetnr: 255
		[meta][field] flags: 16 (BLOB )
		[meta][field] decimals: 0
		[meta][field] type: BLOB (252)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][test_long_data.col1][  1][4]
	[meta] current col: 1
		[data][test_long_data.col2][ 16][Catalin Besleaga]

	[end] server status: 34
	[end] server status: AUTOCOMMIT QUERY_NO_INDEX_USED 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
APPEND TO A NON EXISTING STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_SEND_LONG_DATA: stmt_id [199999]
------------------------------------------------------------------
ERRORS ONLY SHOW AT FIRST EXECUTION OF COM_STMT_EXECUTE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [199999]
handle_error
[1243][HY000][Unknown prepared statement handler (199999) given to mysql_stmt_precheck]
------------------------------------------------------------------
APPEND DATA TO NON EXISTING PARAMETER
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_SEND_LONG_DATA: stmt_id [5]
------------------------------------------------------------------
ERRORS ONLY SHOW AT FIRST EXECUTION OF COM_STMT_EXECUTE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [5]
handle_error
[1210][HY000][Incorrect arguments to COM_STMT_EXECUTE]
------------------------------------------------------------------
TRY TO CLOSE THE CURSOR FROM A PS WITHOUT CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [5]
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE with SELECT nested in CALL
##################################################################
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE TABLE t3(a1 INT, a2 CHAR(32), a3 DOUBLE(4, 2), a4 DECIMAL(3, 1))]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  1
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE TABLE t4(b0 INT, b1 INT, b2 CHAR(32), b3 DOUBLE(4, 2), b4 DECIMAL(3, 1))]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  1
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[INSERT INTO t3 VALUES(1, '11', 12.34, 56.7), (2, '12', 56.78, 90.1), (3, '13', 23.45, 67.8)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  3
	[end] last insert id: 0
	[end] message: Records: 3  Duplicates: 0  Warnings: 0
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[INSERT INTO t4 VALUES(100, 10, '110', 70.70, 10.1), (200, 20, '120', 80.80, 20.2), (300, 30, '130', 90.90, 30.3)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  3
	[end] last insert id: 0
	[end] message: Records: 3  Duplicates: 0  Warnings: 0
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CREATE PROCEDURE p1(   IN v0 INT,    OUT v_str_1 CHAR(32),    OUT v_dbl_1 DOUBLE(4, 2),    OUT v_dec_1 DECIMAL(6, 3),    OUT v_int_1 INT,    IN v1 INT,    INOUT v_str_2 CHAR(64),    INOUT v_dbl_2 DOUBLE(5, 3),    INOUT v_dec_2 DECIMAL(7, 4),    INOUT v_int_2 INT)BEGIN    SET v0 = -1;    SET v1 = -1;    SET v_str_1 = 'test_1';    SET v_dbl_1 = 12.34;    SET v_dec_1 = 567.891;    SET v_int_1 = 2345;    SET v_str_2 = 'test_2';    SET v_dbl_2 = 67.891;    SET v_dec_2 = 234.6789;    SET v_int_2 = 6789;    SELECT * FROM t3;    SELECT * FROM t4; END]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  2
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[CALL p1(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [6]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][6]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  2][10]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a1
		[meta][field] org col name: a1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a2
		[meta][field] org col name: a2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a3
		[meta][field] org col name: a3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a4
		[meta][field] org col name: a4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t3.a1][  1][1]
	[meta] current col: 1
		[data][t3.a2][  2][11]
	[meta] current col: 2
		[data][t3.a3][  5][12.34]
	[meta] current col: 3
		[data][t3.a4][  4][56.7]

	[meta] current col: 0
		[data][t3.a1][  1][2]
	[meta] current col: 1
		[data][t3.a2][  2][12]
	[meta] current col: 2
		[data][t3.a3][  5][56.78]
	[meta] current col: 3
		[data][t3.a4][  4][90.1]

	[meta] current col: 0
		[data][t3.a1][  1][3]
	[meta] current col: 1
		[data][t3.a2][  2][13]
	[meta] current col: 2
		[data][t3.a3][  5][23.45]
	[meta] current col: 3
		[data][t3.a4][  4][67.8]

	[end] server status: 42
	[end] server status: AUTOCOMMIT MORE_RESULTS_EXISTS QUERY_NO_INDEX_USED 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][6]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  2][10]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a1
		[meta][field] org col name: a1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a2
		[meta][field] org col name: a2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a3
		[meta][field] org col name: a3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a4
		[meta][field] org col name: a4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t3.a1][  1][1]
	[meta] current col: 1
		[data][t3.a2][  2][11]
	[meta] current col: 2
		[data][t3.a3][  5][12.34]
	[meta] current col: 3
		[data][t3.a4][  4][56.7]

	[meta] current col: 0
		[data][t3.a1][  1][2]
	[meta] current col: 1
		[data][t3.a2][  2][12]
	[meta] current col: 2
		[data][t3.a3][  5][56.78]
	[meta] current col: 3
		[data][t3.a4][  4][90.1]

	[meta] current col: 0
		[data][t3.a1][  1][3]
	[meta] current col: 1
		[data][t3.a2][  2][13]
	[meta] current col: 2
		[data][t3.a3][  5][23.45]
	[meta] current col: 3
		[data][t3.a4][  4][67.8]

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b0
		[meta][field] org col name: b0
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b1
		[meta][field] org col name: b1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b2
		[meta][field] org col name: b2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b3
		[meta][field] org col name: b3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b4
		[meta][field] org col name: b4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t4.b0][  3][100]
	[meta] current col: 1
		[data][t4.b1][  2][10]
	[meta] current col: 2
		[data][t4.b2][  3][110]
	[meta] current col: 3
		[data][t4.b3][  5][70.70]
	[meta] current col: 4
		[data][t4.b4][  4][10.1]

	[meta] current col: 0
		[data][t4.b0][  3][200]
	[meta] current col: 1
		[data][t4.b1][  2][20]
	[meta] current col: 2
		[data][t4.b2][  3][120]
	[meta] current col: 3
		[data][t4.b3][  5][80.80]
	[meta] current col: 4
		[data][t4.b4][  4][20.2]

	[meta] current col: 0
		[data][t4.b0][  3][300]
	[meta] current col: 1
		[data][t4.b1][  2][30]
	[meta] current col: 2
		[data][t4.b2][  3][130]
	[meta] current col: 3
		[data][t4.b3][  5][90.90]
	[meta] current col: 4
		[data][t4.b4][  4][30.3]

	[end] server status: 42
	[end] server status: AUTOCOMMIT MORE_RESULTS_EXISTS QUERY_NO_INDEX_USED 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][6]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  2][10]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a1
		[meta][field] org col name: a1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a2
		[meta][field] org col name: a2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a3
		[meta][field] org col name: a3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a4
		[meta][field] org col name: a4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t3.a1][  1][1]
	[meta] current col: 1
		[data][t3.a2][  2][11]
	[meta] current col: 2
		[data][t3.a3][  5][12.34]
	[meta] current col: 3
		[data][t3.a4][  4][56.7]

	[meta] current col: 0
		[data][t3.a1][  1][2]
	[meta] current col: 1
		[data][t3.a2][  2][12]
	[meta] current col: 2
		[data][t3.a3][  5][56.78]
	[meta] current col: 3
		[data][t3.a4][  4][90.1]

	[meta] current col: 0
		[data][t3.a1][  1][3]
	[meta] current col: 1
		[data][t3.a2][  2][13]
	[meta] current col: 2
		[data][t3.a3][  5][23.45]
	[meta] current col: 3
		[data][t3.a4][  4][67.8]

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b0
		[meta][field] org col name: b0
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b1
		[meta][field] org col name: b1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b2
		[meta][field] org col name: b2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b3
		[meta][field] org col name: b3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b4
		[meta][field] org col name: b4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t4.b0][  3][100]
	[meta] current col: 1
		[data][t4.b1][  2][10]
	[meta] current col: 2
		[data][t4.b2][  3][110]
	[meta] current col: 3
		[data][t4.b3][  5][70.70]
	[meta] current col: 4
		[data][t4.b4][  4][10.1]

	[meta] current col: 0
		[data][t4.b0][  3][200]
	[meta] current col: 1
		[data][t4.b1][  2][20]
	[meta] current col: 2
		[data][t4.b2][  3][120]
	[meta] current col: 3
		[data][t4.b3][  5][80.80]
	[meta] current col: 4
		[data][t4.b4][  4][20.2]

	[meta] current col: 0
		[data][t4.b0][  3][300]
	[meta] current col: 1
		[data][t4.b1][  2][30]
	[meta] current col: 2
		[data][t4.b2][  3][130]
	[meta] current col: 3
		[data][t4.b3][  5][90.90]
	[meta] current col: 4
		[data][t4.b4][  4][30.3]

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_str_1
		[meta][field] org col name: v_str_1
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dbl_1
		[meta][field] org col name: v_dbl_1
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dec_1
		[meta][field] org col name: v_dec_1
		[meta][field] length: 8
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 3
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_int_1
		[meta][field] org col name: v_int_1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_str_2
		[meta][field] org col name: v_str_2
		[meta][field] length: 256
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dbl_2
		[meta][field] org col name: v_dbl_2
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 3
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dec_2
		[meta][field] org col name: v_dec_2
		[meta][field] length: 9
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 4
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_int_2
		[meta][field] org col name: v_int_2
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][p1.v_str_1][  6][test_1]
	[meta] current col: 1
		[data][p1.v_dbl_1][  5][12.34]
	[meta] current col: 2
		[data][p1.v_dec_1][  7][567.891]
	[meta] current col: 3
		[data][p1.v_int_1][  4][2345]
	[meta] current col: 4
		[data][p1.v_str_2][  6][test_2]
	[meta] current col: 5
		[data][p1.v_dbl_2][  6][67.891]
	[meta] current col: 6
		[data][p1.v_dec_2][  8][234.6789]
	[meta] current col: 7
		[data][p1.v_int_2][  4][6789]

	[end] server status: 4138
	[end] server status: AUTOCOMMIT MORE_RESULTS_EXISTS QUERY_NO_INDEX_USED PS_OUT_PARAMS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][6]
	[meta] current col: 1
		[data][.column_no][  1][0]
	[meta] current col: 2
		[data][.param_no][  2][10]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 65532
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 23
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 31
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 67
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 30
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a1
		[meta][field] org col name: a1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a2
		[meta][field] org col name: a2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a3
		[meta][field] org col name: a3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t3
		[meta][field] org table name: t3
		[meta][field] col name: a4
		[meta][field] org col name: a4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t3.a1][  1][1]
	[meta] current col: 1
		[data][t3.a2][  2][11]
	[meta] current col: 2
		[data][t3.a3][  5][12.34]
	[meta] current col: 3
		[data][t3.a4][  4][56.7]

	[meta] current col: 0
		[data][t3.a1][  1][2]
	[meta] current col: 1
		[data][t3.a2][  2][12]
	[meta] current col: 2
		[data][t3.a3][  5][56.78]
	[meta] current col: 3
		[data][t3.a4][  4][90.1]

	[meta] current col: 0
		[data][t3.a1][  1][3]
	[meta] current col: 1
		[data][t3.a2][  2][13]
	[meta] current col: 2
		[data][t3.a3][  5][23.45]
	[meta] current col: 3
		[data][t3.a4][  4][67.8]

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b0
		[meta][field] org col name: b0
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b1
		[meta][field] org col name: b1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b2
		[meta][field] org col name: b2
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b3
		[meta][field] org col name: b3
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: t4
		[meta][field] org table name: t4
		[meta][field] col name: b4
		[meta][field] org col name: b4
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 1
		[meta][field] type: NEWDECIMAL (246)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][t4.b0][  3][100]
	[meta] current col: 1
		[data][t4.b1][  2][10]
	[meta] current col: 2
		[data][t4.b2][  3][110]
	[meta] current col: 3
		[data][t4.b3][  5][70.70]
	[meta] current col: 4
		[data][t4.b4][  4][10.1]

	[meta] current col: 0
		[data][t4.b0][  3][200]
	[meta] current col: 1
		[data][t4.b1][  2][20]
	[meta] current col: 2
		[data][t4.b2][  3][120]
	[meta] current col: 3
		[data][t4.b3][  5][80.80]
	[meta] current col: 4
		[data][t4.b4][  4][20.2]

	[meta] current col: 0
		[data][t4.b0][  3][300]
	[meta] current col: 1
		[data][t4.b1][  2][30]
	[meta] current col: 2
		[data][t4.b2][  3][130]
	[meta] current col: 3
		[data][t4.b3][  5][90.90]
	[meta] current col: 4
		[data][t4.b4][  4][30.3]

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_str_1
		[meta][field] org col name: v_str_1
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dbl_1
		[meta][field] org col name: v_dbl_1
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dec_1
		[meta][field] org col name: v_dec_1
		[meta][field] length: 8
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 3
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_int_1
		[meta][field] org col name: v_int_1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_str_2
		[meta][field] org col name: v_str_2
		[meta][field] length: 256
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dbl_2
		[meta][field] org col name: v_dbl_2
		[meta][field] length: 5
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 3
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_dec_2
		[meta][field] org col name: v_dec_2
		[meta][field] length: 9
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 4
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: test
		[meta][field] table name: p1
		[meta][field] org table name: p1
		[meta][field] col name: v_int_2
		[meta][field] org col name: v_int_2
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][p1.v_str_1][  6][test_1]
	[meta] current col: 1
		[data][p1.v_dbl_1][  5][12.34]
	[meta] current col: 2
		[data][p1.v_dec_1][  7][567.891]
	[meta] current col: 3
		[data][p1.v_int_1][  4][2345]
	[meta] current col: 4
		[data][p1.v_str_2][  6][test_2]
	[meta] current col: 5
		[data][p1.v_dbl_2][  6][67.891]
	[meta] current col: 6
		[data][p1.v_dec_2][  8][234.6789]
	[meta] current col: 7
		[data][p1.v_int_2][  4][6789]

	[end] server status: 34
	[end] server status: AUTOCOMMIT QUERY_NO_INDEX_USED 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CLOSE PS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [6]
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE with wrong data type
##################################################################
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[SELECT CONCAT(9< ?)]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITH PARAMETERS AND CURSOR
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [7]
handle_start_column_metadata
handle_end_column_metadata
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: stmt_id
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: column_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: param_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: warning_no
		[meta][field] org col name: 
		[meta][field] length: 96
		[meta][field] charsetnr: 33
		[meta][field] flags: 1 (NOT_NULL )
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][.stmt_id][  1][7]
	[meta] current col: 1
		[data][.column_no][  1][1]
	[meta] current col: 2
		[data][.param_no][  1][1]
	[meta] current col: 3
		[data][.warning_no][  1][0]

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: ?
		[meta][field] org col name: 
		[meta][field] length: 21
		[meta][field] charsetnr: 8
		[meta][field] flags: 128 (BINARY )
		[meta][field] decimals: 0
		[meta][field] type: LONGLONG (8)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: CONCAT(9< ?)
		[meta][field] org col name: 
		[meta][field] length: 3
		[meta][field] charsetnr: 33
		[meta][field] flags: 0
		[meta][field] decimals: 31
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

		[meta][field] db name: 
		[meta][field] table name: 
		[meta][field] org table name: 
		[meta][field] col name: CONCAT(9< ?)
		[meta][field] org col name: CONCAT(9< ?)
		[meta][field] length: 3
		[meta][field] charsetnr: 33
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: VARCHAR (15)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[end] server status: 66
	[end] server status: AUTOCOMMIT CURSOR_EXISTS 
	[end] warning count:  1
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CLOSE PS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [7]
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE with out-params as placeholders
##################################################################
RESET VARIABLES THAT ARE GOING TO BE USED FOR OUT-PARAMS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[SET @my_v1=null, @my_v2=null, @my_v3=null, @my_v4=null]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[CALL proc_set_out_params(?, ?, ?, ?)]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITH PARAMETERS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [8]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_str_1
		[meta][field] org col name: v_str_1
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_dbl_1
		[meta][field] org col name: v_dbl_1
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_dec_1
		[meta][field] org col name: v_dec_1
		[meta][field] length: 8
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 3
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_int_1
		[meta][field] org col name: v_int_1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][proc_set_out_params.v_str_1][  6][test_1]
	[meta] current col: 1
		[data][proc_set_out_params.v_dbl_1][  5][12.34]
	[meta] current col: 2
		[data][proc_set_out_params.v_dec_1][  7][567.891]
	[meta] current col: 3
		[data][proc_set_out_params.v_int_1][  4][2345]

	[end] server status: 4106
	[end] server status: AUTOCOMMIT MORE_RESULTS_EXISTS PS_OUT_PARAMS 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_str_1
		[meta][field] org col name: v_str_1
		[meta][field] length: 128
		[meta][field] charsetnr: 255
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: STRING (254)

		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_dbl_1
		[meta][field] org col name: v_dbl_1
		[meta][field] length: 4
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 2
		[meta][field] type: DOUBLE (5)

		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_dec_1
		[meta][field] org col name: v_dec_1
		[meta][field] length: 8
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 3
		[meta][field] type: NEWDECIMAL (246)

		[meta][field] db name: test
		[meta][field] table name: proc_set_out_params
		[meta][field] org table name: proc_set_out_params
		[meta][field] col name: v_int_1
		[meta][field] org col name: v_int_1
		[meta][field] length: 11
		[meta][field] charsetnr: 8
		[meta][field] flags: 0
		[meta][field] decimals: 0
		[meta][field] type: LONG (3)


	[meta][charset result] number: 33
	[meta][charset result] name: utf8mb3
	[meta][charset result] collation: utf8mb3_general_ci
	[meta][charset result] sort order: 

	[meta] current col: 0
		[data][proc_set_out_params.v_str_1][  6][test_1]
	[meta] current col: 1
		[data][proc_set_out_params.v_dbl_1][  5][12.34]
	[meta] current col: 2
		[data][proc_set_out_params.v_dec_1][  7][567.891]
	[meta] current col: 3
		[data][proc_set_out_params.v_int_1][  4][2345]

	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
VERIFY THAT VARIABLES ARE STILL NULL AND OUT PRAMETERS WERE TRANSFERED IN METADATA
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CALL verify_user_variables_are_null(@my_v1, @my_v2, @my_v3, @my_v4)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CLOSE PS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [8]
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE with out-params as variables
##################################################################
RESET VARIABLES THAT ARE GOING TO BE USED FOR OUT-PARAMS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[SET @my_v1=null, @my_v2=null, @my_v3=null, @my_v4=null]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[CALL proc_set_out_params(@my_v1, @my_v2, @my_v3, @my_v4)]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITHOUT PARAMETERS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [9]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
VERIFY THAT VARIABLES ARE SET AND OUT PRAMETERS WERE NOT TRANSFERED IN METADATA
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CALL verify_user_variables_are_set(@my_v1, @my_v2, @my_v3, @my_v4)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CLOSE PS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_CLOSE: stmt_id [9]
------------------------------------------------------------------
##################################################################
Test COM_QUERY with out-params as placeholders
##################################################################
RESET VARIABLES THAT ARE GOING TO BE USED FOR OUT-PARAMS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[SET @my_v1=null, @my_v2=null, @my_v3=null, @my_v4=null]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[PREPARE stmt FROM 'CALL proc_set_out_params(?, ?, ?, ?)']
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: Statement prepared
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
EXECUTE PREPARED STATEMENT WITHOUT PARAMETERS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[EXECUTE stmt USING @my_v1, @my_v2, @my_v3, @my_v4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
VERIFY THAT VARIABLES ARE SET AND OUT PRAMETERS WERE NOT TRANSFERED IN METADATA
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[CALL verify_user_variables_are_set(@my_v1, @my_v2, @my_v3, @my_v4)]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
CLOSE PS
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DEALLOCATE PREPARE stmt;]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
##################################################################
Test COM_STMT_EXECUTE with wrong parameters
##################################################################
CREATE PREPARED STATEMENT
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_PREPARE: query[SELECT * from t1 where a = ?]
handle_start_column_metadata
handle_end_column_metadata
handle_start_row
handle_end_row
handle_start_column_metadata
handle_end_column_metadata
handle_start_column_metadata
handle_end_column_metadata
------------------------------------------------------------------
EXECUTE THE PS WITH INVALID PARAMETER TYPE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [11]
handle_error
[1210][HY000][Incorrect arguments to mysqld_stmt_execute]
------------------------------------------------------------------
EXECUTE THE PS WITH BOOL PARAMETER TYPE
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_STMT_EXECUTE: stmt_id [11]
handle_error
[1210][HY000][Incorrect arguments to mysqld_stmt_execute]
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP TABLE IF EXISTS t1]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP TABLE IF EXISTS t2]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP TABLE IF EXISTS test_long_data]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP TABLE IF EXISTS t3]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP TABLE IF EXISTS t4]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP PROCEDURE IF EXISTS p1]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP PROCEDURE IF EXISTS proc_set_out_params]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP PROCEDURE IF EXISTS verify_user_variables_are_null]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
------------------------------------------------------------------
[CS_TEXT_REPRESENTATION]
COM_QUERY: query[DROP PROCEDURE IF EXISTS verify_user_variables_are_set]
handle_ok
<<<<<<<<<<<< Current context >>>>>>>>>>>>>>>
	[end] server status: 2
	[end] server status: AUTOCOMMIT 
	[end] warning count:  0
	[end] affected rows:  0
	[end] last insert id: 0
	[end] message: 
<<<<<<<<<<<<>>>>>>>>>>>>>>>
------------------------------------------------------------------
[srv_session_close]
deinit thread
------ cleanup ---------------------------------------------------
