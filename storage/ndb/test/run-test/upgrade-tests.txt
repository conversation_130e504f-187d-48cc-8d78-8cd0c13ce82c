# Copyright (c) 2008, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
#
cmd: testUpgrade
args: -n ShowVersions T1
max-time: 600
type: bench  # Always record result

cmd: testUpgrade
args: -n Upgrade_NR1 T1
max-time: 600

cmd: testUpgrade
args: -n Upgrade_NR2 T1
max-time: 600

cmd: testUpgrade
args: -n Upgrade_NR3 T1
max-time: 600

cmd: testUpgrade
args: -n Upgrade_FS T1
max-time: 600

cmd: testUpgrade
args: -n Upgrade_Traffic T1
max-time: 1200

cmd: testUpgrade
args: -n Upgrade_Traffic_FS T1
max-time: 1200

cmd: testUpgrade
args: -n Bug14702377 T1
max-time: 600

cmd: testUpgrade
args: -n Upgrade_Api_Only T1
max-time: 1200

cmd: testUpgrade
args: -n Upgrade_Api_Before_NR1 T2
max-time: 1200

cmd: testUpgrade
args: -n Upgrade_Api_NDBD_MGMD T1
max-time: 1200

cmd: testUpgrade
args: -n Upgrade_Mixed_MGMD_API_NDBD T2
max-time: 1200

cmd: testUpgrade
args: -n Upgrade_SR_ManyTablesMaxFrag T1
max-time: 6000

cmd: testUpgrade
args: -n Upgrade_NR3_LCP_InProgress T1
max-time: 1200

cmd: testUpgrade
args: -n Upgrade_Newer_LCP_FS_Fail T1
max-time: 1200

cmd: testUpgrade
args: -n ChangeHalfRestartChangeHalf T1
max-time: 1200

cmd: testUpgrade
args: -n ChangeMGMDChangeHalfRestartChangeHalf T1
max-time: 1200

