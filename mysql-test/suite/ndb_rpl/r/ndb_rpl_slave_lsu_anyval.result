include/rpl/init.inc [topology=none]
*** Configuring connections ***
include/rpl/connect.inc [creating srv_master]
include/rpl/connect.inc [creating srv_master1]
include/rpl/connect.inc [creating srv_master2]
include/rpl/connect.inc [creating srv_slave]
include/rpl/connect.inc [creating srv_slave1]
include/rpl/connect.inc [creating srv_slave2]
*** Waiting for each cluster to startup ***
*** Configuring replication via Slave ***
[connection srv_slave]
include/rpl/change_topology.inc [new topology=1->4]
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
include/rpl/start_replica.inc
*** Generating slave cluster originated binloggable changes ***
CREATE TABLE bug_45756_slave_logged_1 (a int) engine = NDB;
INSERT INTO bug_45756_slave_logged_1 VALUES (1);
DROP TABLE bug_45756_slave_logged_1;
CREATE TABLE bug_45756_slave_logged_2 (a int) engine = NDB;
INSERT INTO bug_45756_slave_logged_2 VALUES (1);
DROP TABLE bug_45756_slave_logged_2;
CREATE TABLE bug_45756_slave_logged_3 (a int) engine = NDB;
INSERT INTO bug_45756_slave_logged_3 VALUES (1);
DROP TABLE bug_45756_slave_logged_3;
***Generating slave cluster non-binloggable changes***
SET SQL_LOG_BIN= 0;
CREATE TABLE bug_45756_slave_not_logged_1 (a int) engine = NDB;
INSERT INTO bug_45756_slave_not_logged_1 VALUES (1);
DROP TABLE bug_45756_slave_not_logged_1;
SET SQL_LOG_BIN= 1;
SET SQL_LOG_BIN= 0;
CREATE TABLE bug_45756_slave_not_logged_2 (a int) engine = NDB;
INSERT INTO bug_45756_slave_not_logged_2 VALUES (1);
DROP TABLE bug_45756_slave_not_logged_2;
SET SQL_LOG_BIN= 1;
SET SQL_LOG_BIN= 0;
CREATE TABLE bug_45756_slave_not_logged_3 (a int) engine = NDB;
INSERT INTO bug_45756_slave_not_logged_3 VALUES (1);
DROP TABLE bug_45756_slave_not_logged_3;
SET SQL_LOG_BIN= 1;
*** Generating data to be replicated ***
CREATE TABLE bug45756_master_logged_1 (a int) engine = NDB;
INSERT INTO bug45756_master_logged_1 VALUES (1);
DROP TABLE bug45756_master_logged_1;
CREATE TABLE bug45756_master_logged_2 (a int) engine = NDB;
INSERT INTO bug45756_master_logged_2 VALUES (1);
DROP TABLE bug45756_master_logged_2;
CREATE TABLE bug45756_master_logged_3 (a int) engine = NDB;
INSERT INTO bug45756_master_logged_3 VALUES (1);
DROP TABLE bug45756_master_logged_3;
*** Generating changes not to be replicated ***
SET SQL_LOG_BIN= 0;
CREATE TABLE bug45756_master_not_logged_1 (a int) engine = NDB;
INSERT INTO bug45756_master_not_logged_1 VALUES (1);
DROP TABLE bug45756_master_not_logged_1;
SET SQL_LOG_BIN= 1;
SET SQL_LOG_BIN= 0;
CREATE TABLE bug45756_master_not_logged_2 (a int) engine = NDB;
INSERT INTO bug45756_master_not_logged_2 VALUES (1);
DROP TABLE bug45756_master_not_logged_2;
SET SQL_LOG_BIN= 1;
SET SQL_LOG_BIN= 0;
CREATE TABLE bug45756_master_not_logged_3 (a int) engine = NDB;
INSERT INTO bug45756_master_not_logged_3 VALUES (1);
DROP TABLE bug45756_master_not_logged_3;
SET SQL_LOG_BIN= 1;
*** Checking binlog contents on every server in both clusters ***



connection srv_master;
show variables like 'server_id';
Variable_name	Value
server_id	1
show variables like 'log_bin';
Variable_name	Value
log_bin	ON
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_1 (a int) engine = NDB
master-bin.000001	#	Query	1	#	BEGIN
master-bin.000001	#	Table_map	1	#	table_id: # (test.bug45756_master_logged_1)
master-bin.000001	#	Table_map	1	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	1	#	table_id: #
master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	1	#	COMMIT
master-bin.000001	#	Query	1	#	use `test`; DROP TABLE `bug45756_master_logged_1` /* generated by server */
master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_2 (a int) engine = NDB
master-bin.000001	#	Query	1	#	BEGIN
master-bin.000001	#	Table_map	1	#	table_id: # (test.bug45756_master_logged_2)
master-bin.000001	#	Table_map	1	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	1	#	table_id: #
master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	1	#	COMMIT
master-bin.000001	#	Query	1	#	use `test`; drop table `test`.`bug45756_master_logged_2`
master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_3 (a int) engine = NDB
master-bin.000001	#	Query	1	#	BEGIN
master-bin.000001	#	Table_map	1	#	table_id: # (test.bug45756_master_logged_3)
master-bin.000001	#	Table_map	1	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	1	#	table_id: #
master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	1	#	COMMIT
master-bin.000001	#	Query	1	#	use `test`; drop table `test`.`bug45756_master_logged_3`
select 'server id' and 'event type' from mysqlbinlog output
binlog_entries
server id 1    Query
server id 1    Query
server id 1    Table_map: `test`.`bug45756_master_logged_1` 
server id 1    Table_map: `mysql`.`ndb_apply_status` 
server id 1    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 1    Query
server id 1    Query
server id 2147483521    Query
server id 1    Query
server id 1    Table_map: `test`.`bug45756_master_logged_2` 
server id 1    Table_map: `mysql`.`ndb_apply_status` 
server id 1    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 1    Query
server id 2147483521    Query
server id 2147483521    Query
server id 1    Query
server id 1    Table_map: `test`.`bug45756_master_logged_3` 
server id 1    Table_map: `mysql`.`ndb_apply_status` 
server id 1    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 1    Query
server id 2147483521    Query



connection srv_master1;
show variables like 'server_id';
Variable_name	Value
server_id	2
show variables like 'log_bin';
Variable_name	Value
log_bin	OFF
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
show binlog events;
Log_name	Pos	Event_type	Server_id	End_log_pos	Info



connection srv_master2;
show variables like 'server_id';
Variable_name	Value
server_id	3
show variables like 'log_bin';
Variable_name	Value
log_bin	ON
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	3	#	use `test`; CREATE TABLE bug45756_master_logged_1 (a int) engine = NDB
master-bin.000001	#	Query	3	#	BEGIN
master-bin.000001	#	Table_map	3	#	table_id: # (test.bug45756_master_logged_1)
master-bin.000001	#	Table_map	3	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	3	#	table_id: #
master-bin.000001	#	Write_rows	3	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	3	#	COMMIT
master-bin.000001	#	Query	3	#	use `test`; drop table `test`.`bug45756_master_logged_1`
master-bin.000001	#	Query	3	#	use `test`; CREATE TABLE bug45756_master_logged_2 (a int) engine = NDB
master-bin.000001	#	Query	3	#	BEGIN
master-bin.000001	#	Table_map	3	#	table_id: # (test.bug45756_master_logged_2)
master-bin.000001	#	Table_map	3	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	3	#	table_id: #
master-bin.000001	#	Write_rows	3	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	3	#	COMMIT
master-bin.000001	#	Query	3	#	use `test`; drop table `test`.`bug45756_master_logged_2`
master-bin.000001	#	Query	3	#	use `test`; CREATE TABLE bug45756_master_logged_3 (a int) engine = NDB
master-bin.000001	#	Query	3	#	BEGIN
master-bin.000001	#	Table_map	3	#	table_id: # (test.bug45756_master_logged_3)
master-bin.000001	#	Table_map	3	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	3	#	table_id: #
master-bin.000001	#	Write_rows	3	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	3	#	COMMIT
master-bin.000001	#	Query	3	#	use `test`; DROP TABLE `bug45756_master_logged_3` /* generated by server */
select 'server id' and 'event type' from mysqlbinlog output
binlog_entries
server id 2147483523    Query
server id 3    Query
server id 3    Table_map: `test`.`bug45756_master_logged_1` 
server id 3    Table_map: `mysql`.`ndb_apply_status` 
server id 3    Write_rows: 
server id 2147483523    Write_rows:  flags: STMT_END_F
server id 3    Query
server id 2147483523    Query
server id 2147483523    Query
server id 3    Query
server id 3    Table_map: `test`.`bug45756_master_logged_2` 
server id 3    Table_map: `mysql`.`ndb_apply_status` 
server id 3    Write_rows: 
server id 2147483523    Write_rows:  flags: STMT_END_F
server id 3    Query
server id 2147483523    Query
server id 3    Query
server id 3    Query
server id 3    Table_map: `test`.`bug45756_master_logged_3` 
server id 3    Table_map: `mysql`.`ndb_apply_status` 
server id 3    Write_rows: 
server id 2147483523    Write_rows:  flags: STMT_END_F
server id 3    Query
server id 3    Query



connection srv_slave;
show variables like 'server_id';
Variable_name	Value
server_id	4
show variables like 'log_bin';
Variable_name	Value
log_bin	OFF
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
show binlog events;
Log_name	Pos	Event_type	Server_id	End_log_pos	Info



connection srv_slave1;
show variables like 'server_id';
Variable_name	Value
server_id	5
show variables like 'log_bin';
Variable_name	Value
log_bin	ON
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
slave-master-bin.000001	#	Query	5	#	use `test`; CREATE TABLE bug_45756_slave_logged_1 (a int) engine = NDB
slave-master-bin.000001	#	Query	5	#	BEGIN
slave-master-bin.000001	#	Table_map	5	#	table_id: # (test.bug_45756_slave_logged_1)
slave-master-bin.000001	#	Table_map	5	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	5	#	table_id: #
slave-master-bin.000001	#	Write_rows	5	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	5	#	COMMIT
slave-master-bin.000001	#	Query	5	#	use `test`; drop table `test`.`bug_45756_slave_logged_1`
slave-master-bin.000001	#	Query	5	#	use `test`; CREATE TABLE bug_45756_slave_logged_2 (a int) engine = NDB
slave-master-bin.000001	#	Query	5	#	BEGIN
slave-master-bin.000001	#	Table_map	5	#	table_id: # (test.bug_45756_slave_logged_2)
slave-master-bin.000001	#	Table_map	5	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	5	#	table_id: #
slave-master-bin.000001	#	Write_rows	5	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	5	#	COMMIT
slave-master-bin.000001	#	Query	5	#	use `test`; DROP TABLE `bug_45756_slave_logged_2` /* generated by server */
slave-master-bin.000001	#	Query	5	#	use `test`; CREATE TABLE bug_45756_slave_logged_3 (a int) engine = NDB
slave-master-bin.000001	#	Query	5	#	BEGIN
slave-master-bin.000001	#	Table_map	5	#	table_id: # (test.bug_45756_slave_logged_3)
slave-master-bin.000001	#	Table_map	5	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	5	#	table_id: #
slave-master-bin.000001	#	Write_rows	5	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	5	#	COMMIT
slave-master-bin.000001	#	Query	5	#	use `test`; drop table `test`.`bug_45756_slave_logged_3`
select 'server id' and 'event type' from mysqlbinlog output
binlog_entries
server id 2147483525    Query
server id 5    Query
server id 5    Table_map: `test`.`bug_45756_slave_logged_1` 
server id 5    Table_map: `mysql`.`ndb_apply_status` 
server id 5    Write_rows: 
server id 2147483525    Write_rows:  flags: STMT_END_F
server id 5    Query
server id 2147483525    Query
server id 5    Query
server id 5    Query
server id 5    Table_map: `test`.`bug_45756_slave_logged_2` 
server id 5    Table_map: `mysql`.`ndb_apply_status` 
server id 5    Write_rows: 
server id 2147483525    Write_rows:  flags: STMT_END_F
server id 5    Query
server id 5    Query
server id 2147483525    Query
server id 5    Query
server id 5    Table_map: `test`.`bug_45756_slave_logged_3` 
server id 5    Table_map: `mysql`.`ndb_apply_status` 
server id 5    Write_rows: 
server id 2147483525    Write_rows:  flags: STMT_END_F
server id 5    Query
server id 2147483525    Query



connection srv_slave2;
show variables like 'server_id';
Variable_name	Value
server_id	6
show variables like 'log_bin';
Variable_name	Value
log_bin	ON
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	ON
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
slave-master-bin.000001	#	Query	6	#	use `test`; CREATE TABLE bug_45756_slave_logged_1 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug_45756_slave_logged_1)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	6	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	6	#	use `test`; drop table `test`.`bug_45756_slave_logged_1`
slave-master-bin.000001	#	Query	6	#	use `test`; CREATE TABLE bug_45756_slave_logged_2 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug_45756_slave_logged_2)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	6	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	6	#	use `test`; drop table `test`.`bug_45756_slave_logged_2`
slave-master-bin.000001	#	Query	6	#	use `test`; CREATE TABLE bug_45756_slave_logged_3 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug_45756_slave_logged_3)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	6	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	6	#	use `test`; DROP TABLE `bug_45756_slave_logged_3` /* generated by server */
slave-master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_1 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug45756_master_logged_1)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	1	#	use `test`; drop table `test`.`bug45756_master_logged_1`
slave-master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_2 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug45756_master_logged_2)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	1	#	use `test`; drop table `test`.`bug45756_master_logged_2`
slave-master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_3 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug45756_master_logged_3)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	1	#	use `test`; drop table `test`.`bug45756_master_logged_3`
select 'server id' and 'event type' from mysqlbinlog output
binlog_entries
server id 2147483526    Query
server id 6    Query
server id 6    Table_map: `test`.`bug_45756_slave_logged_1` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483526    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 2147483526    Query
server id 2147483526    Query
server id 6    Query
server id 6    Table_map: `test`.`bug_45756_slave_logged_2` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483526    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 2147483526    Query
server id 6    Query
server id 6    Query
server id 6    Table_map: `test`.`bug_45756_slave_logged_3` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483526    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 6    Query
server id 2147483521    Query
server id 6    Query
server id 6    Table_map: `test`.`bug45756_master_logged_1` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 2147483521    Query
server id 2147483521    Query
server id 6    Query
server id 6    Table_map: `test`.`bug45756_master_logged_2` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 2147483521    Query
server id 2147483521    Query
server id 6    Query
server id 6    Table_map: `test`.`bug45756_master_logged_3` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 2147483521    Query
include/rpl/stop_replica.inc
*** Configuring replication via Slave1 ***
[connection srv_slave1]
include/rpl/change_topology.inc [new topology=1->4,1->5]
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
include/rpl/start_replica.inc
*** Generating slave cluster originated binloggable changes ***
CREATE TABLE bug_45756_slave_logged_1 (a int) engine = NDB;
INSERT INTO bug_45756_slave_logged_1 VALUES (1);
DROP TABLE bug_45756_slave_logged_1;
CREATE TABLE bug_45756_slave_logged_2 (a int) engine = NDB;
INSERT INTO bug_45756_slave_logged_2 VALUES (1);
DROP TABLE bug_45756_slave_logged_2;
CREATE TABLE bug_45756_slave_logged_3 (a int) engine = NDB;
INSERT INTO bug_45756_slave_logged_3 VALUES (1);
DROP TABLE bug_45756_slave_logged_3;
***Generating slave cluster non-binloggable changes***
SET SQL_LOG_BIN= 0;
CREATE TABLE bug_45756_slave_not_logged_1 (a int) engine = NDB;
INSERT INTO bug_45756_slave_not_logged_1 VALUES (1);
DROP TABLE bug_45756_slave_not_logged_1;
SET SQL_LOG_BIN= 1;
SET SQL_LOG_BIN= 0;
CREATE TABLE bug_45756_slave_not_logged_2 (a int) engine = NDB;
INSERT INTO bug_45756_slave_not_logged_2 VALUES (1);
DROP TABLE bug_45756_slave_not_logged_2;
SET SQL_LOG_BIN= 1;
SET SQL_LOG_BIN= 0;
CREATE TABLE bug_45756_slave_not_logged_3 (a int) engine = NDB;
INSERT INTO bug_45756_slave_not_logged_3 VALUES (1);
DROP TABLE bug_45756_slave_not_logged_3;
SET SQL_LOG_BIN= 1;
*** Generating data to be replicated ***
CREATE TABLE bug45756_master_logged_1 (a int) engine = NDB;
INSERT INTO bug45756_master_logged_1 VALUES (1);
DROP TABLE bug45756_master_logged_1;
CREATE TABLE bug45756_master_logged_2 (a int) engine = NDB;
INSERT INTO bug45756_master_logged_2 VALUES (1);
DROP TABLE bug45756_master_logged_2;
CREATE TABLE bug45756_master_logged_3 (a int) engine = NDB;
INSERT INTO bug45756_master_logged_3 VALUES (1);
DROP TABLE bug45756_master_logged_3;
*** Generating changes not to be replicated ***
SET SQL_LOG_BIN= 0;
CREATE TABLE bug45756_master_not_logged_1 (a int) engine = NDB;
INSERT INTO bug45756_master_not_logged_1 VALUES (1);
DROP TABLE bug45756_master_not_logged_1;
SET SQL_LOG_BIN= 1;
SET SQL_LOG_BIN= 0;
CREATE TABLE bug45756_master_not_logged_2 (a int) engine = NDB;
INSERT INTO bug45756_master_not_logged_2 VALUES (1);
DROP TABLE bug45756_master_not_logged_2;
SET SQL_LOG_BIN= 1;
SET SQL_LOG_BIN= 0;
CREATE TABLE bug45756_master_not_logged_3 (a int) engine = NDB;
INSERT INTO bug45756_master_not_logged_3 VALUES (1);
DROP TABLE bug45756_master_not_logged_3;
SET SQL_LOG_BIN= 1;
*** Checking binlog contents on every server in both clusters ***



connection srv_master;
show variables like 'server_id';
Variable_name	Value
server_id	1
show variables like 'log_bin';
Variable_name	Value
log_bin	ON
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_1 (a int) engine = NDB
master-bin.000001	#	Query	1	#	BEGIN
master-bin.000001	#	Table_map	1	#	table_id: # (test.bug45756_master_logged_1)
master-bin.000001	#	Table_map	1	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	1	#	table_id: #
master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	1	#	COMMIT
master-bin.000001	#	Query	1	#	use `test`; DROP TABLE `bug45756_master_logged_1` /* generated by server */
master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_2 (a int) engine = NDB
master-bin.000001	#	Query	1	#	BEGIN
master-bin.000001	#	Table_map	1	#	table_id: # (test.bug45756_master_logged_2)
master-bin.000001	#	Table_map	1	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	1	#	table_id: #
master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	1	#	COMMIT
master-bin.000001	#	Query	1	#	use `test`; drop table `test`.`bug45756_master_logged_2`
master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_3 (a int) engine = NDB
master-bin.000001	#	Query	1	#	BEGIN
master-bin.000001	#	Table_map	1	#	table_id: # (test.bug45756_master_logged_3)
master-bin.000001	#	Table_map	1	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	1	#	table_id: #
master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	1	#	COMMIT
master-bin.000001	#	Query	1	#	use `test`; drop table `test`.`bug45756_master_logged_3`
select 'server id' and 'event type' from mysqlbinlog output
binlog_entries
server id 1    Query
server id 1    Query
server id 1    Table_map: `test`.`bug45756_master_logged_1` 
server id 1    Table_map: `mysql`.`ndb_apply_status` 
server id 1    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 1    Query
server id 1    Query
server id 2147483521    Query
server id 1    Query
server id 1    Table_map: `test`.`bug45756_master_logged_2` 
server id 1    Table_map: `mysql`.`ndb_apply_status` 
server id 1    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 1    Query
server id 2147483521    Query
server id 2147483521    Query
server id 1    Query
server id 1    Table_map: `test`.`bug45756_master_logged_3` 
server id 1    Table_map: `mysql`.`ndb_apply_status` 
server id 1    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 1    Query
server id 2147483521    Query



connection srv_master1;
show variables like 'server_id';
Variable_name	Value
server_id	2
show variables like 'log_bin';
Variable_name	Value
log_bin	OFF
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
show binlog events;
Log_name	Pos	Event_type	Server_id	End_log_pos	Info



connection srv_master2;
show variables like 'server_id';
Variable_name	Value
server_id	3
show variables like 'log_bin';
Variable_name	Value
log_bin	ON
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	3	#	use `test`; CREATE TABLE bug45756_master_logged_1 (a int) engine = NDB
master-bin.000001	#	Query	3	#	BEGIN
master-bin.000001	#	Table_map	3	#	table_id: # (test.bug45756_master_logged_1)
master-bin.000001	#	Table_map	3	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	3	#	table_id: #
master-bin.000001	#	Write_rows	3	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	3	#	COMMIT
master-bin.000001	#	Query	3	#	use `test`; drop table `test`.`bug45756_master_logged_1`
master-bin.000001	#	Query	3	#	use `test`; CREATE TABLE bug45756_master_logged_2 (a int) engine = NDB
master-bin.000001	#	Query	3	#	BEGIN
master-bin.000001	#	Table_map	3	#	table_id: # (test.bug45756_master_logged_2)
master-bin.000001	#	Table_map	3	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	3	#	table_id: #
master-bin.000001	#	Write_rows	3	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	3	#	COMMIT
master-bin.000001	#	Query	3	#	use `test`; drop table `test`.`bug45756_master_logged_2`
master-bin.000001	#	Query	3	#	use `test`; CREATE TABLE bug45756_master_logged_3 (a int) engine = NDB
master-bin.000001	#	Query	3	#	BEGIN
master-bin.000001	#	Table_map	3	#	table_id: # (test.bug45756_master_logged_3)
master-bin.000001	#	Table_map	3	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	3	#	table_id: #
master-bin.000001	#	Write_rows	3	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	3	#	COMMIT
master-bin.000001	#	Query	3	#	use `test`; DROP TABLE `bug45756_master_logged_3` /* generated by server */
select 'server id' and 'event type' from mysqlbinlog output
binlog_entries
server id 2147483523    Query
server id 3    Query
server id 3    Table_map: `test`.`bug45756_master_logged_1` 
server id 3    Table_map: `mysql`.`ndb_apply_status` 
server id 3    Write_rows: 
server id 2147483523    Write_rows:  flags: STMT_END_F
server id 3    Query
server id 2147483523    Query
server id 2147483523    Query
server id 3    Query
server id 3    Table_map: `test`.`bug45756_master_logged_2` 
server id 3    Table_map: `mysql`.`ndb_apply_status` 
server id 3    Write_rows: 
server id 2147483523    Write_rows:  flags: STMT_END_F
server id 3    Query
server id 2147483523    Query
server id 3    Query
server id 3    Query
server id 3    Table_map: `test`.`bug45756_master_logged_3` 
server id 3    Table_map: `mysql`.`ndb_apply_status` 
server id 3    Write_rows: 
server id 2147483523    Write_rows:  flags: STMT_END_F
server id 3    Query
server id 3    Query



connection srv_slave;
show variables like 'server_id';
Variable_name	Value
server_id	4
show variables like 'log_bin';
Variable_name	Value
log_bin	OFF
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
show binlog events;
Log_name	Pos	Event_type	Server_id	End_log_pos	Info



connection srv_slave1;
show variables like 'server_id';
Variable_name	Value
server_id	5
show variables like 'log_bin';
Variable_name	Value
log_bin	ON
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
slave-master-bin.000001	#	Query	5	#	use `test`; CREATE TABLE bug_45756_slave_logged_1 (a int) engine = NDB
slave-master-bin.000001	#	Query	5	#	BEGIN
slave-master-bin.000001	#	Table_map	5	#	table_id: # (test.bug_45756_slave_logged_1)
slave-master-bin.000001	#	Table_map	5	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	5	#	table_id: #
slave-master-bin.000001	#	Write_rows	5	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	5	#	COMMIT
slave-master-bin.000001	#	Query	5	#	use `test`; drop table `test`.`bug_45756_slave_logged_1`
slave-master-bin.000001	#	Query	5	#	use `test`; CREATE TABLE bug_45756_slave_logged_2 (a int) engine = NDB
slave-master-bin.000001	#	Query	5	#	BEGIN
slave-master-bin.000001	#	Table_map	5	#	table_id: # (test.bug_45756_slave_logged_2)
slave-master-bin.000001	#	Table_map	5	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	5	#	table_id: #
slave-master-bin.000001	#	Write_rows	5	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	5	#	COMMIT
slave-master-bin.000001	#	Query	5	#	use `test`; DROP TABLE `bug_45756_slave_logged_2` /* generated by server */
slave-master-bin.000001	#	Query	5	#	use `test`; CREATE TABLE bug_45756_slave_logged_3 (a int) engine = NDB
slave-master-bin.000001	#	Query	5	#	BEGIN
slave-master-bin.000001	#	Table_map	5	#	table_id: # (test.bug_45756_slave_logged_3)
slave-master-bin.000001	#	Table_map	5	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	5	#	table_id: #
slave-master-bin.000001	#	Write_rows	5	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	5	#	COMMIT
slave-master-bin.000001	#	Query	5	#	use `test`; drop table `test`.`bug_45756_slave_logged_3`
select 'server id' and 'event type' from mysqlbinlog output
binlog_entries
server id 2147483525    Query
server id 5    Query
server id 5    Table_map: `test`.`bug_45756_slave_logged_1` 
server id 5    Table_map: `mysql`.`ndb_apply_status` 
server id 5    Write_rows: 
server id 2147483525    Write_rows:  flags: STMT_END_F
server id 5    Query
server id 2147483525    Query
server id 5    Query
server id 5    Query
server id 5    Table_map: `test`.`bug_45756_slave_logged_2` 
server id 5    Table_map: `mysql`.`ndb_apply_status` 
server id 5    Write_rows: 
server id 2147483525    Write_rows:  flags: STMT_END_F
server id 5    Query
server id 5    Query
server id 2147483525    Query
server id 5    Query
server id 5    Table_map: `test`.`bug_45756_slave_logged_3` 
server id 5    Table_map: `mysql`.`ndb_apply_status` 
server id 5    Write_rows: 
server id 2147483525    Write_rows:  flags: STMT_END_F
server id 5    Query
server id 2147483525    Query



connection srv_slave2;
show variables like 'server_id';
Variable_name	Value
server_id	6
show variables like 'log_bin';
Variable_name	Value
log_bin	ON
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	ON
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
slave-master-bin.000001	#	Query	6	#	use `test`; CREATE TABLE bug_45756_slave_logged_1 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug_45756_slave_logged_1)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	6	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	6	#	use `test`; drop table `test`.`bug_45756_slave_logged_1`
slave-master-bin.000001	#	Query	6	#	use `test`; CREATE TABLE bug_45756_slave_logged_2 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug_45756_slave_logged_2)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	6	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	6	#	use `test`; drop table `test`.`bug_45756_slave_logged_2`
slave-master-bin.000001	#	Query	6	#	use `test`; CREATE TABLE bug_45756_slave_logged_3 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug_45756_slave_logged_3)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	6	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	6	#	use `test`; DROP TABLE `bug_45756_slave_logged_3` /* generated by server */
slave-master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_1 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug45756_master_logged_1)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	1	#	use `test`; drop table `test`.`bug45756_master_logged_1`
slave-master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_2 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug45756_master_logged_2)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	1	#	use `test`; drop table `test`.`bug45756_master_logged_2`
slave-master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_3 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug45756_master_logged_3)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	1	#	use `test`; drop table `test`.`bug45756_master_logged_3`
select 'server id' and 'event type' from mysqlbinlog output
binlog_entries
server id 2147483526    Query
server id 6    Query
server id 6    Table_map: `test`.`bug_45756_slave_logged_1` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483526    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 2147483526    Query
server id 2147483526    Query
server id 6    Query
server id 6    Table_map: `test`.`bug_45756_slave_logged_2` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483526    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 2147483526    Query
server id 6    Query
server id 6    Query
server id 6    Table_map: `test`.`bug_45756_slave_logged_3` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483526    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 6    Query
server id 2147483521    Query
server id 6    Query
server id 6    Table_map: `test`.`bug45756_master_logged_1` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 2147483521    Query
server id 2147483521    Query
server id 6    Query
server id 6    Table_map: `test`.`bug45756_master_logged_2` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 2147483521    Query
server id 2147483521    Query
server id 6    Query
server id 6    Table_map: `test`.`bug45756_master_logged_3` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 2147483521    Query
include/rpl/stop_replica.inc
*** Configuring replication via Slave2 ***
[connection srv_slave2]
include/rpl/change_topology.inc [new topology=1->4,1->5,1->6]
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
include/rpl/start_replica.inc
*** Generating slave cluster originated binloggable changes ***
CREATE TABLE bug_45756_slave_logged_1 (a int) engine = NDB;
INSERT INTO bug_45756_slave_logged_1 VALUES (1);
DROP TABLE bug_45756_slave_logged_1;
CREATE TABLE bug_45756_slave_logged_2 (a int) engine = NDB;
INSERT INTO bug_45756_slave_logged_2 VALUES (1);
DROP TABLE bug_45756_slave_logged_2;
CREATE TABLE bug_45756_slave_logged_3 (a int) engine = NDB;
INSERT INTO bug_45756_slave_logged_3 VALUES (1);
DROP TABLE bug_45756_slave_logged_3;
***Generating slave cluster non-binloggable changes***
SET SQL_LOG_BIN= 0;
CREATE TABLE bug_45756_slave_not_logged_1 (a int) engine = NDB;
INSERT INTO bug_45756_slave_not_logged_1 VALUES (1);
DROP TABLE bug_45756_slave_not_logged_1;
SET SQL_LOG_BIN= 1;
SET SQL_LOG_BIN= 0;
CREATE TABLE bug_45756_slave_not_logged_2 (a int) engine = NDB;
INSERT INTO bug_45756_slave_not_logged_2 VALUES (1);
DROP TABLE bug_45756_slave_not_logged_2;
SET SQL_LOG_BIN= 1;
SET SQL_LOG_BIN= 0;
CREATE TABLE bug_45756_slave_not_logged_3 (a int) engine = NDB;
INSERT INTO bug_45756_slave_not_logged_3 VALUES (1);
DROP TABLE bug_45756_slave_not_logged_3;
SET SQL_LOG_BIN= 1;
*** Generating data to be replicated ***
CREATE TABLE bug45756_master_logged_1 (a int) engine = NDB;
INSERT INTO bug45756_master_logged_1 VALUES (1);
DROP TABLE bug45756_master_logged_1;
CREATE TABLE bug45756_master_logged_2 (a int) engine = NDB;
INSERT INTO bug45756_master_logged_2 VALUES (1);
DROP TABLE bug45756_master_logged_2;
CREATE TABLE bug45756_master_logged_3 (a int) engine = NDB;
INSERT INTO bug45756_master_logged_3 VALUES (1);
DROP TABLE bug45756_master_logged_3;
*** Generating changes not to be replicated ***
SET SQL_LOG_BIN= 0;
CREATE TABLE bug45756_master_not_logged_1 (a int) engine = NDB;
INSERT INTO bug45756_master_not_logged_1 VALUES (1);
DROP TABLE bug45756_master_not_logged_1;
SET SQL_LOG_BIN= 1;
SET SQL_LOG_BIN= 0;
CREATE TABLE bug45756_master_not_logged_2 (a int) engine = NDB;
INSERT INTO bug45756_master_not_logged_2 VALUES (1);
DROP TABLE bug45756_master_not_logged_2;
SET SQL_LOG_BIN= 1;
SET SQL_LOG_BIN= 0;
CREATE TABLE bug45756_master_not_logged_3 (a int) engine = NDB;
INSERT INTO bug45756_master_not_logged_3 VALUES (1);
DROP TABLE bug45756_master_not_logged_3;
SET SQL_LOG_BIN= 1;
*** Checking binlog contents on every server in both clusters ***



connection srv_master;
show variables like 'server_id';
Variable_name	Value
server_id	1
show variables like 'log_bin';
Variable_name	Value
log_bin	ON
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_1 (a int) engine = NDB
master-bin.000001	#	Query	1	#	BEGIN
master-bin.000001	#	Table_map	1	#	table_id: # (test.bug45756_master_logged_1)
master-bin.000001	#	Table_map	1	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	1	#	table_id: #
master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	1	#	COMMIT
master-bin.000001	#	Query	1	#	use `test`; DROP TABLE `bug45756_master_logged_1` /* generated by server */
master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_2 (a int) engine = NDB
master-bin.000001	#	Query	1	#	BEGIN
master-bin.000001	#	Table_map	1	#	table_id: # (test.bug45756_master_logged_2)
master-bin.000001	#	Table_map	1	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	1	#	table_id: #
master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	1	#	COMMIT
master-bin.000001	#	Query	1	#	use `test`; drop table `test`.`bug45756_master_logged_2`
master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_3 (a int) engine = NDB
master-bin.000001	#	Query	1	#	BEGIN
master-bin.000001	#	Table_map	1	#	table_id: # (test.bug45756_master_logged_3)
master-bin.000001	#	Table_map	1	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	1	#	table_id: #
master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	1	#	COMMIT
master-bin.000001	#	Query	1	#	use `test`; drop table `test`.`bug45756_master_logged_3`
select 'server id' and 'event type' from mysqlbinlog output
binlog_entries
server id 1    Query
server id 1    Query
server id 1    Table_map: `test`.`bug45756_master_logged_1` 
server id 1    Table_map: `mysql`.`ndb_apply_status` 
server id 1    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 1    Query
server id 1    Query
server id 2147483521    Query
server id 1    Query
server id 1    Table_map: `test`.`bug45756_master_logged_2` 
server id 1    Table_map: `mysql`.`ndb_apply_status` 
server id 1    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 1    Query
server id 2147483521    Query
server id 2147483521    Query
server id 1    Query
server id 1    Table_map: `test`.`bug45756_master_logged_3` 
server id 1    Table_map: `mysql`.`ndb_apply_status` 
server id 1    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 1    Query
server id 2147483521    Query



connection srv_master1;
show variables like 'server_id';
Variable_name	Value
server_id	2
show variables like 'log_bin';
Variable_name	Value
log_bin	OFF
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
show binlog events;
Log_name	Pos	Event_type	Server_id	End_log_pos	Info



connection srv_master2;
show variables like 'server_id';
Variable_name	Value
server_id	3
show variables like 'log_bin';
Variable_name	Value
log_bin	ON
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
master-bin.000001	#	Query	3	#	use `test`; CREATE TABLE bug45756_master_logged_1 (a int) engine = NDB
master-bin.000001	#	Query	3	#	BEGIN
master-bin.000001	#	Table_map	3	#	table_id: # (test.bug45756_master_logged_1)
master-bin.000001	#	Table_map	3	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	3	#	table_id: #
master-bin.000001	#	Write_rows	3	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	3	#	COMMIT
master-bin.000001	#	Query	3	#	use `test`; drop table `test`.`bug45756_master_logged_1`
master-bin.000001	#	Query	3	#	use `test`; CREATE TABLE bug45756_master_logged_2 (a int) engine = NDB
master-bin.000001	#	Query	3	#	BEGIN
master-bin.000001	#	Table_map	3	#	table_id: # (test.bug45756_master_logged_2)
master-bin.000001	#	Table_map	3	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	3	#	table_id: #
master-bin.000001	#	Write_rows	3	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	3	#	COMMIT
master-bin.000001	#	Query	3	#	use `test`; drop table `test`.`bug45756_master_logged_2`
master-bin.000001	#	Query	3	#	use `test`; CREATE TABLE bug45756_master_logged_3 (a int) engine = NDB
master-bin.000001	#	Query	3	#	BEGIN
master-bin.000001	#	Table_map	3	#	table_id: # (test.bug45756_master_logged_3)
master-bin.000001	#	Table_map	3	#	table_id: # (mysql.ndb_apply_status)
master-bin.000001	#	Write_rows	3	#	table_id: #
master-bin.000001	#	Write_rows	3	#	table_id: # flags: STMT_END_F
master-bin.000001	#	Query	3	#	COMMIT
master-bin.000001	#	Query	3	#	use `test`; DROP TABLE `bug45756_master_logged_3` /* generated by server */
select 'server id' and 'event type' from mysqlbinlog output
binlog_entries
server id 2147483523    Query
server id 3    Query
server id 3    Table_map: `test`.`bug45756_master_logged_1` 
server id 3    Table_map: `mysql`.`ndb_apply_status` 
server id 3    Write_rows: 
server id 2147483523    Write_rows:  flags: STMT_END_F
server id 3    Query
server id 2147483523    Query
server id 2147483523    Query
server id 3    Query
server id 3    Table_map: `test`.`bug45756_master_logged_2` 
server id 3    Table_map: `mysql`.`ndb_apply_status` 
server id 3    Write_rows: 
server id 2147483523    Write_rows:  flags: STMT_END_F
server id 3    Query
server id 2147483523    Query
server id 3    Query
server id 3    Query
server id 3    Table_map: `test`.`bug45756_master_logged_3` 
server id 3    Table_map: `mysql`.`ndb_apply_status` 
server id 3    Write_rows: 
server id 2147483523    Write_rows:  flags: STMT_END_F
server id 3    Query
server id 3    Query



connection srv_slave;
show variables like 'server_id';
Variable_name	Value
server_id	4
show variables like 'log_bin';
Variable_name	Value
log_bin	OFF
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
show binlog events;
Log_name	Pos	Event_type	Server_id	End_log_pos	Info



connection srv_slave1;
show variables like 'server_id';
Variable_name	Value
server_id	5
show variables like 'log_bin';
Variable_name	Value
log_bin	ON
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	OFF
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
slave-master-bin.000001	#	Query	5	#	use `test`; CREATE TABLE bug_45756_slave_logged_1 (a int) engine = NDB
slave-master-bin.000001	#	Query	5	#	BEGIN
slave-master-bin.000001	#	Table_map	5	#	table_id: # (test.bug_45756_slave_logged_1)
slave-master-bin.000001	#	Table_map	5	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	5	#	table_id: #
slave-master-bin.000001	#	Write_rows	5	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	5	#	COMMIT
slave-master-bin.000001	#	Query	5	#	use `test`; drop table `test`.`bug_45756_slave_logged_1`
slave-master-bin.000001	#	Query	5	#	use `test`; CREATE TABLE bug_45756_slave_logged_2 (a int) engine = NDB
slave-master-bin.000001	#	Query	5	#	BEGIN
slave-master-bin.000001	#	Table_map	5	#	table_id: # (test.bug_45756_slave_logged_2)
slave-master-bin.000001	#	Table_map	5	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	5	#	table_id: #
slave-master-bin.000001	#	Write_rows	5	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	5	#	COMMIT
slave-master-bin.000001	#	Query	5	#	use `test`; DROP TABLE `bug_45756_slave_logged_2` /* generated by server */
slave-master-bin.000001	#	Query	5	#	use `test`; CREATE TABLE bug_45756_slave_logged_3 (a int) engine = NDB
slave-master-bin.000001	#	Query	5	#	BEGIN
slave-master-bin.000001	#	Table_map	5	#	table_id: # (test.bug_45756_slave_logged_3)
slave-master-bin.000001	#	Table_map	5	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	5	#	table_id: #
slave-master-bin.000001	#	Write_rows	5	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	5	#	COMMIT
slave-master-bin.000001	#	Query	5	#	use `test`; drop table `test`.`bug_45756_slave_logged_3`
select 'server id' and 'event type' from mysqlbinlog output
binlog_entries
server id 2147483525    Query
server id 5    Query
server id 5    Table_map: `test`.`bug_45756_slave_logged_1` 
server id 5    Table_map: `mysql`.`ndb_apply_status` 
server id 5    Write_rows: 
server id 2147483525    Write_rows:  flags: STMT_END_F
server id 5    Query
server id 2147483525    Query
server id 5    Query
server id 5    Query
server id 5    Table_map: `test`.`bug_45756_slave_logged_2` 
server id 5    Table_map: `mysql`.`ndb_apply_status` 
server id 5    Write_rows: 
server id 2147483525    Write_rows:  flags: STMT_END_F
server id 5    Query
server id 5    Query
server id 2147483525    Query
server id 5    Query
server id 5    Table_map: `test`.`bug_45756_slave_logged_3` 
server id 5    Table_map: `mysql`.`ndb_apply_status` 
server id 5    Write_rows: 
server id 2147483525    Write_rows:  flags: STMT_END_F
server id 5    Query
server id 2147483525    Query



connection srv_slave2;
show variables like 'server_id';
Variable_name	Value
server_id	6
show variables like 'log_bin';
Variable_name	Value
log_bin	ON
show variables like 'log_replica_updates';
Variable_name	Value
log_replica_updates	ON
include/rpl/deprecated/show_binlog_events.inc
Log_name	Pos	Event_type	Server_id	End_log_pos	Info
slave-master-bin.000001	#	Query	6	#	use `test`; CREATE TABLE bug_45756_slave_logged_1 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug_45756_slave_logged_1)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	6	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	6	#	use `test`; drop table `test`.`bug_45756_slave_logged_1`
slave-master-bin.000001	#	Query	6	#	use `test`; CREATE TABLE bug_45756_slave_logged_2 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug_45756_slave_logged_2)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	6	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	6	#	use `test`; drop table `test`.`bug_45756_slave_logged_2`
slave-master-bin.000001	#	Query	6	#	use `test`; CREATE TABLE bug_45756_slave_logged_3 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug_45756_slave_logged_3)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	6	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	6	#	use `test`; DROP TABLE `bug_45756_slave_logged_3` /* generated by server */
slave-master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_1 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug45756_master_logged_1)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	1	#	use `test`; DROP TABLE `bug45756_master_logged_1` /* generated by server */
slave-master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_2 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug45756_master_logged_2)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	1	#	use `test`; DROP TABLE `bug45756_master_logged_2` /* generated by server */
slave-master-bin.000001	#	Query	1	#	use `test`; CREATE TABLE bug45756_master_logged_3 (a int) engine = NDB
slave-master-bin.000001	#	Query	6	#	BEGIN
slave-master-bin.000001	#	Table_map	6	#	table_id: # (test.bug45756_master_logged_3)
slave-master-bin.000001	#	Table_map	6	#	table_id: # (mysql.ndb_apply_status)
slave-master-bin.000001	#	Write_rows	6	#	table_id: #
slave-master-bin.000001	#	Write_rows	1	#	table_id: # flags: STMT_END_F
slave-master-bin.000001	#	Query	6	#	COMMIT
slave-master-bin.000001	#	Query	1	#	use `test`; DROP TABLE `bug45756_master_logged_3` /* generated by server */
select 'server id' and 'event type' from mysqlbinlog output
binlog_entries
server id 2147483526    Query
server id 6    Query
server id 6    Table_map: `test`.`bug_45756_slave_logged_1` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483526    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 2147483526    Query
server id 2147483526    Query
server id 6    Query
server id 6    Table_map: `test`.`bug_45756_slave_logged_2` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483526    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 2147483526    Query
server id 6    Query
server id 6    Query
server id 6    Table_map: `test`.`bug_45756_slave_logged_3` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483526    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 6    Query
server id 1    Query
server id 6    Query
server id 6    Table_map: `test`.`bug45756_master_logged_1` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 1    Query
server id 1    Query
server id 6    Query
server id 6    Table_map: `test`.`bug45756_master_logged_2` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 1    Query
server id 1    Query
server id 6    Query
server id 6    Table_map: `test`.`bug45756_master_logged_3` 
server id 6    Table_map: `mysql`.`ndb_apply_status` 
server id 6    Write_rows: 
server id 2147483521    Write_rows:  flags: STMT_END_F
server id 6    Query
server id 1    Query
include/rpl/stop_replica.inc
include/rpl/change_topology.inc [new topology=none]
include/rpl/deinit.inc
