set sql_mode='traditional';
create table t1 (d date);
insert into t1 (d) select date_sub('2000-01-01', INTERVAL 2001 YEAR);
ERROR 22008: Datetime function: datetime field overflow
insert into t1 (d) select date_add('2000-01-01',interval 8000 year);
ERROR 22008: Datetime function: datetime field overflow
insert into t1 values (date_add(NULL, INTERVAL 1 DAY));
insert into t1 values (date_add('2000-01-04', INTERVAL NULL DAY));
set sql_mode='';
insert into t1 (d) select date_sub('2000-01-01', INTERVAL 2001 YEAR);
Warnings:
Warning	1441	Datetime function: datetime field overflow
insert into t1 (d) select date_add('2000-01-01',interval 8000 year);
Warnings:
Warning	1441	Datetime function: datetime field overflow
insert into t1 values (date_add(NULL, INTERVAL 1 DAY));
insert into t1 values (date_add('2000-01-04', INTERVAL NULL DAY));
select * from t1;
d
NULL
NULL
NULL
NULL
NULL
NULL
drop table t1;
End of 4.1 tests
SELECT CAST('2006-09-26' AS DATE) + INTERVAL 1 DAY;
CAST('2006-09-26' AS DATE) + INTERVAL 1 DAY
2006-09-27
SELECT CAST('2006-09-26' AS DATE) + INTERVAL 1 MONTH;
CAST('2006-09-26' AS DATE) + INTERVAL 1 MONTH
2006-10-26
SELECT CAST('2006-09-26' AS DATE) + INTERVAL 1 YEAR;
CAST('2006-09-26' AS DATE) + INTERVAL 1 YEAR
2007-09-26
SELECT CAST('2006-09-26' AS DATE) + INTERVAL 1 WEEK;
CAST('2006-09-26' AS DATE) + INTERVAL 1 WEEK
2006-10-03
create table t1 (a int, b varchar(10));
insert into t1 values (1, '2001-01-01'),(2, '2002-02-02');
select '2007-01-01' + interval a day from t1;
'2007-01-01' + interval a day
2007-01-02
2007-01-03
select b + interval a day from t1;
b + interval a day
2001-01-02
2002-02-04
drop table t1;
End of 5.0 tests
#
# Bug #27004806: UBSAN: ITEM_DATE_ADD_INTERVAL - NEGATION OF XYZ AND SIGNED INTEGER OVERFLOW
#
SELECT ADDDATE('8112-06-20', REPEAT('1', 32));
ADDDATE('8112-06-20', REPEAT('1', 32))
8112-06-19
Warnings:
Warning	1292	Truncated incorrect INTEGER value: '11111111111111111111111111111111'
# Bug#32915973: Prepare DATE_ADD/DATE_SUB incorrectly returns DATETIME
SELECT ADDDATE(DATE'2021-01-01', INTERVAL 1 DAY);
ADDDATE(DATE'2021-01-01', INTERVAL 1 DAY)
2021-01-02
SELECT ADDDATE(DATE'2021-01-01', INTERVAL 1 HOUR);
ADDDATE(DATE'2021-01-01', INTERVAL 1 HOUR)
2021-01-01 01:00:00
SELECT ADDDATE(TIMESTAMP'2021-01-01 00:00:00', INTERVAL 1 DAY);
ADDDATE(TIMESTAMP'2021-01-01 00:00:00', INTERVAL 1 DAY)
2021-01-02 00:00:00
SELECT ADDDATE(TIMESTAMP'2021-01-01 00:00:00', INTERVAL 1 HOUR);
ADDDATE(TIMESTAMP'2021-01-01 00:00:00', INTERVAL 1 HOUR)
2021-01-01 01:00:00
SELECT DATE(ts) = CURRENT_DATE + INTERVAL '1' DAY AS is_tomorrow, TIME(ts)
FROM (SELECT ADDDATE(TIME'00:00:00', INTERVAL 1 DAY) AS ts) AS dt;
is_tomorrow	TIME(ts)
1	00:00:00
SELECT ADDDATE(TIME'00:00:00', INTERVAL 1 HOUR);
ADDDATE(TIME'00:00:00', INTERVAL 1 HOUR)
01:00:00
SELECT ADDDATE('2021-01-01', INTERVAL 1 DAY);
ADDDATE('2021-01-01', INTERVAL 1 DAY)
2021-01-02
SELECT ADDDATE('2021-01-01', INTERVAL 1 HOUR);
ADDDATE('2021-01-01', INTERVAL 1 HOUR)
2021-01-01 01:00:00
SELECT ADDDATE('2021-01-01 00:00:00', INTERVAL 1 DAY);
ADDDATE('2021-01-01 00:00:00', INTERVAL 1 DAY)
2021-01-02 00:00:00
SELECT ADDDATE('2021-01-01 00:00:00', INTERVAL 1 HOUR);
ADDDATE('2021-01-01 00:00:00', INTERVAL 1 HOUR)
2021-01-01 01:00:00
SELECT ADDDATE('00:00:00', INTERVAL 1 DAY);
ADDDATE('00:00:00', INTERVAL 1 DAY)
NULL
Warnings:
Warning	1292	Incorrect datetime value: '00:00:00'
SELECT ADDDATE('00:00:00', INTERVAL 1 HOUR);
ADDDATE('00:00:00', INTERVAL 1 HOUR)
NULL
Warnings:
Warning	1292	Incorrect datetime value: '00:00:00'
set @d='2021-01-01';
set @t='00:00:00';
set @ts='2021-01-01 00:00:00';
prepare sd from "SELECT ADDDATE(?, INTERVAL 1 DAY)";
execute sd using @d;
ADDDATE(?, INTERVAL 1 DAY)
2021-01-02
prepare st from "SELECT ADDDATE(?, INTERVAL 1 HOUR)";
execute st using @d;
ADDDATE(?, INTERVAL 1 HOUR)
2021-01-01 01:00:00.000000
prepare sd from "SELECT ADDDATE(?, INTERVAL 1 DAY)";
execute sd using @ts;
ADDDATE(?, INTERVAL 1 DAY)
2021-01-02 00:00:00.000000
prepare st from "SELECT ADDDATE(?, INTERVAL 1 HOUR)";
execute st using @ts;
ADDDATE(?, INTERVAL 1 HOUR)
2021-01-01 01:00:00.000000
prepare sd from "SELECT ADDDATE(?, INTERVAL 1 DAY)";
execute sd using @t;
ADDDATE(?, INTERVAL 1 DAY)
0000-00-00
prepare st from "SELECT ADDDATE(?, INTERVAL 1 HOUR)";
execute st using @t;
ADDDATE(?, INTERVAL 1 HOUR)
NULL
Warnings:
Warning	1441	Datetime function: datetime field overflow
SELECT ADDTIME(DATE'2021-01-01', '01:01:01');
ADDTIME(DATE'2021-01-01', '01:01:01')
2021-01-01 01:01:01
SELECT ADDTIME(TIMESTAMP'2021-01-01 00:00:00', TIME'01:01:01');
ADDTIME(TIMESTAMP'2021-01-01 00:00:00', TIME'01:01:01')
2021-01-01 01:01:01
SELECT ADDTIME(TIME'00:00:00', TIME'01:01:01');
ADDTIME(TIME'00:00:00', TIME'01:01:01')
01:01:01
SELECT ADDTIME('2021-01-01', '01:01:01');
ADDTIME('2021-01-01', '01:01:01')
01:21:22
Warnings:
Warning	1292	Truncated incorrect time value: '2021-01-01'
SELECT ADDTIME('2021-01-01 00:00:00', TIME'01:01:01');
ADDTIME('2021-01-01 00:00:00', TIME'01:01:01')
2021-01-01 01:01:01
SELECT ADDTIME('00:00:00', TIME'01:01:01');
ADDTIME('00:00:00', TIME'01:01:01')
01:01:01
prepare s from "SELECT ADDTIME(?, TIME'01:01:01')";
execute s using @d;
ADDTIME(?, TIME'01:01:01')
01:21:22.000000
Warnings:
Warning	1292	Truncated incorrect time value: '2021-01-01'
prepare s from "SELECT ADDTIME(?, TIME'01:01:01')";
execute s using @ts;
ADDTIME(?, TIME'01:01:01')
01:01:01.000000
prepare s from "SELECT ADDTIME(?, TIME'01:01:01')";
execute s using @t;
ADDTIME(?, TIME'01:01:01')
01:01:01.000000
