ARC FROM "mutex/archive/Archive_share::mutex" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/csv/tina" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/federated/federated" TO "rwlock/sql/THR_LOCK_servers" OP "R"
ARC FROM "mutex/group_rpl/GCS_Gcs_suspicions_manager::m_suspicions_mutex" TO "mutex/group_rpl/GCS_Gcs_suspicions_manager::m_suspicions_parameters_mutex"
ARC FROM "mutex/group_rpl/GCS_Gcs_xcom_interface::m_wait_for_ssl_init_mutex" TO "mutex/group_rpl/GCS_Gcs_xcom_engine::m_wait_for_notification_mutex"
ARC FROM "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_process" TO "cond/group_rpl/COND_group_action_coordinator_thread"
ARC FROM "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_process" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread"
ARC FROM "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_process" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread_end"
ARC FROM "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_process" TO "mutex/group_rpl/LOCK_primary_election_action_notification"
ARC FROM "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_process" TO "mutex/group_rpl/LOCK_primary_election_running_flag"
ARC FROM "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_process" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread_end"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/group_rpl/LOCK_applier_module_suspend"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/group_rpl/LOCK_certification_info"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/group_rpl/LOCK_certification_members"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/group_rpl/LOCK_stage_monitor_handler"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "rwlock/group_rpl/RWLOCK_certifier_stable_gtid_set" OP "W"
ARC FROM "mutex/group_rpl/LOCK_applier_module_run" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/group_rpl/LOCK_applier_module_suspend" TO "mutex/group_rpl/LOCK_stage_monitor_handler"
ARC FROM "mutex/group_rpl/LOCK_certification_info" TO "cond/group_rpl/COND_certifier_broadcast_run"
ARC FROM "mutex/group_rpl/LOCK_certification_info" TO "cond/group_rpl/COND_mysql_thread_handler_dispatcher_run"
ARC FROM "mutex/group_rpl/LOCK_certification_info" TO "mutex/group_rpl/LOCK_certifier_broadcast_run"
ARC FROM "mutex/group_rpl/LOCK_certification_info" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_certification_info" TO "mutex/group_rpl/LOCK_mysql_thread_handler_dispatcher_run"
ARC FROM "mutex/group_rpl/LOCK_certification_info" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_certification_info" TO "rwlock/group_rpl/RWLOCK_certifier_stable_gtid_set" OP "W"
ARC FROM "mutex/group_rpl/LOCK_certification_info" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "mutex/group_rpl/LOCK_certification_members" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "mutex/group_rpl/LOCK_certification_members" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_certification_members" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_certification_members" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "mutex/group_rpl/LOCK_certification_members" TO "rwlock/group_rpl/RWLOCK_certifier_stable_gtid_set" OP "W"
ARC FROM "mutex/group_rpl/LOCK_certifier_broadcast_run" TO "mutex/group_rpl/LOCK_certifier_broadcast_dispatcher_run"
ARC FROM "mutex/group_rpl/LOCK_certifier_broadcast_run" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/group_rpl/LOCK_certifier_broadcast_run" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/group_rpl/LOCK_clone_donor_list" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/group_rpl/LOCK_clone_query"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "rwlock/group_rpl/RWLOCK_group_event_observation_list" OP "W"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "R"
ARC FROM "mutex/group_rpl/LOCK_clone_handler_run" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "mutex/group_rpl/LOCK_clone_query" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "cond/group_rpl/COND_session_thread_method_exec"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "cond/sql/MDL_context::COND_wait_status"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/group_rpl/LOCK_primary_election_secondary_process_run"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/group_rpl/LOCK_session_thread_method_exec"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/LOCK_connection_count"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/LOG::LOCK_log"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/THD::LOCK_thd_protocol"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "mutex/sql/THD::LOCK_thd_sysvar"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "R"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "W"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "prlock/sql/MDL_lock::rwlock" OP "R"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "R"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "rwlock/session/LOCK_srv_session_collection" OP "W"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "rwlock/sql/LOGGER::LOCK_logger" OP "R"
ARC FROM "mutex/group_rpl/LOCK_clone_read_mode" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "mutex/group_rpl/LOCK_connection_map" TO "mutex/sql/LOCK_group_replication_connection_mutex"
ARC FROM "mutex/group_rpl/LOCK_connection_map" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/group_rpl/LOCK_connection_map" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/group_rpl/LOCK_connection_map" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/group_rpl/LOCK_delayed_init_run" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/group_rpl/LOCK_delayed_init_run" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/group_rpl/LOCK_delayed_init_run" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/group_rpl/LOCK_delayed_init_run" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/group_rpl/LOCK_delayed_init_run" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/group_rpl/LOCK_delayed_init_run" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "mutex/group_rpl/LOCK_delayed_init_run" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "mutex/group_rpl/LOCK_delayed_init_run" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/group_rpl/LOCK_delayed_init_run" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/group_rpl/LOCK_delayed_init_run" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/group_rpl/LOCK_delayed_init_run" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/group_rpl/LOCK_delayed_init_run" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "mutex/group_rpl/LOCK_group_member_info_manager_update_lock" TO "cond/group_rpl/COND_session_thread_method_exec"
ARC FROM "mutex/group_rpl/LOCK_group_member_info_manager_update_lock" TO "cond/group_rpl/COND_session_thread_run"
ARC FROM "mutex/group_rpl/LOCK_group_member_info_manager_update_lock" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_group_member_info_manager_update_lock" TO "mutex/group_rpl/LOCK_session_thread_method_exec"
ARC FROM "mutex/group_rpl/LOCK_group_member_info_manager_update_lock" TO "mutex/group_rpl/LOCK_session_thread_run"
ARC FROM "mutex/group_rpl/LOCK_group_member_info_manager_update_lock" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_group_member_info_update_lock" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_message_service_run" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_mysql_handler_thread_run" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_mysql_thread_dispatcher_run" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_mysql_thread_handler_dispatcher_run" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_mysql_thread_run" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "cond/group_rpl/COND_applier_module_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "cond/sql/Relay_log_info::stop_cond"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/group_rpl/LOCK_applier_module_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/group_rpl/LOCK_applier_module_suspend"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/group_rpl/LOCK_certification_info"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/group_rpl/LOCK_certification_members"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/sql/Source_info::run_lock"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "rwlock/group_rpl/RWLOCK_certifier_stable_gtid_set" OP "W"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "rwlock/group_rpl/RWLOCK_flow_control_module_info" OP "R"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "rwlock/sql/channel_lock" OP "W"
ARC FROM "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "cond/group_rpl/COND_applier_module_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "cond/group_rpl/COND_clone_handler_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "cond/group_rpl/COND_group_action_coordinator_thread"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "cond/group_rpl/COND_message_service_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "cond/group_rpl/COND_mysql_thread_handler_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "cond/group_rpl/COND_mysql_thread_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "cond/group_rpl/COND_recovery_module_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "cond/group_rpl/COND_view_modification_wait"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "cond/sql/Relay_log_info::stop_cond"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "cond/sql/Source_info::stop_cond"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "cond/sql/Source_IO_monitor::run_cond"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/GCS_Gcs_async_buffer::m_free_buffer_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/GCS_Gcs_xcom_engine::m_wait_for_notification_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/GCS_Gcs_xcom_view_change_control::m_joining_leaving_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_process"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread_end"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/key_GR_LOCK_group_part_handler_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_applier_module_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_applier_module_suspend"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_autorejoin_module"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_certification_members"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_clone_handler_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_clone_query"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_connection_map"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_message_service_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_mysql_handler_thread_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_mysql_thread_dispatcher_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_mysql_thread_handler_dispatcher_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_mysql_thread_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_plugin_online"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_recovery"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_recovery_module_run"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_view_modification_wait"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/group_rpl/LOCK_wait_ticket"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/mysqlx/broker_context_sync"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/key_mta_temp_table_LOCK"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/Replica_reporting_capability::err_lock"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/Source_info::data_lock"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/Source_info::run_lock"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/Source_IO_monitor::run_lock"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "W"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "rwlock/group_rpl/RWLOCK_gcs_operations_view_change_observers" OP "W"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "rwlock/sql/channel_lock" OP "W"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/group_rpl/LOCK_plugin_modules_termination" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/group_rpl/LOCK_primary_election_action_notification" TO "mutex/group_rpl/LOCK_primary_election_action_phase"
ARC FROM "mutex/group_rpl/LOCK_primary_election_action_phase" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_primary_election_primary_process_run" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "mutex/group_rpl/LOCK_primary_election_primary_process_run" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_primary_election_primary_process_run" TO "mutex/group_rpl/LOCK_pipeline_continuation"
ARC FROM "mutex/group_rpl/LOCK_primary_election_primary_process_run" TO "mutex/group_rpl/LOCK_primary_election_action_notification"
ARC FROM "mutex/group_rpl/LOCK_primary_election_primary_process_run" TO "mutex/group_rpl/LOCK_primary_election_action_phase"
ARC FROM "mutex/group_rpl/LOCK_primary_election_primary_process_run" TO "mutex/group_rpl/LOCK_stage_monitor_handler"
ARC FROM "mutex/group_rpl/LOCK_primary_election_primary_process_run" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_primary_election_primary_process_run" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "mutex/group_rpl/LOCK_primary_election_primary_process_run" TO "rwlock/group_rpl/RWLOCK_group_event_observation_list" OP "R"
ARC FROM "mutex/group_rpl/LOCK_primary_election_primary_process_run" TO "rwlock/group_rpl/RWLOCK_group_event_observation_list" OP "W"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "cond/sql/MDL_context::COND_wait_status"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/group_rpl/LOCK_primary_election_action_notification"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/group_rpl/LOCK_primary_election_action_phase"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/group_rpl/LOCK_stage_monitor_handler"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/LOCK_connection_count"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/LOG::LOCK_log"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/THD::LOCK_thd_protocol"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "mutex/sql/THD::LOCK_thd_sysvar"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "W"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "rwlock/group_rpl/RWLOCK_group_event_observation_list" OP "R"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "rwlock/group_rpl/RWLOCK_group_event_observation_list" OP "W"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "R"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "rwlock/session/LOCK_srv_session_collection" OP "W"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "rwlock/sql/LOGGER::LOCK_logger" OP "R"
ARC FROM "mutex/group_rpl/LOCK_primary_election_secondary_process_run" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "mutex/group_rpl/LOCK_primary_election_validation_notification" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_primary_election_validation_notification" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "mutex/group_rpl/LOCK_primary_election_validation_notification" TO "mutex/sql/Source_info::run_lock"
ARC FROM "mutex/group_rpl/LOCK_primary_election_validation_notification" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "mutex/group_rpl/LOCK_primary_promotion_policy" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "cond/sql/Relay_log_info::start_cond"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "cond/sql/Relay_log_info::stop_cond"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "cond/sql/Source_info::start_cond"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/key_mta_temp_table_LOCK"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/LOCK_sql_replica_skip_counter"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_index"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_xids"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/Relay_log_info::data_lock"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/Replica_reporting_capability::err_lock"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/Source_info::data_lock"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/Source_info::run_lock"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "rwlock/group_rpl/RWLOCK_channel_observation_list" OP "R"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "rwlock/group_rpl/RWLOCK_channel_observation_list" OP "W"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "rwlock/sql/Binlog_relay_IO_delegate::lock" OP "R"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "rwlock/sql/channel_lock" OP "W"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "rwlock/sql/rpl_filter_lock" OP "R"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/group_rpl/LOCK_recovery_donor_selection" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/group_rpl/LOCK_recovery_module_run" TO "mutex/group_rpl/LOCK_applier_module_suspend"
ARC FROM "mutex/group_rpl/LOCK_recovery_module_run" TO "mutex/group_rpl/LOCK_recovery"
ARC FROM "mutex/group_rpl/LOCK_recovery_module_run" TO "mutex/group_rpl/LOCK_stage_monitor_handler"
ARC FROM "mutex/group_rpl/LOCK_recovery_module_run" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/group_rpl/LOCK_recovery_module_run" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/group_rpl/LOCK_recovery_module_run" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/group_rpl/LOCK_recovery_module_run" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/group_rpl/LOCK_session_thread_run" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "R"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "mutex/group_rpl/LOCK_transaction_monitoring" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/group_rpl/LOCK_transaction_unblocking" TO "mutex/group_rpl/LOCK_wait_ticket"
ARC FROM "mutex/group_rpl/LOCK_wait_ticket" TO "mutex/group_rpl/LOCK_count_down_latch"
ARC FROM "mutex/group_rpl/LOCK_write_lock_protection" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/group_rpl/LOCK_write_lock_protection" TO "rwlock/group_rpl/RWLOCK_plugin_stop" OP "R"
ARC FROM "mutex/group_rpl/LOCK_write_lock_protection" TO "rwlock/group_rpl/RWLOCK_plugin_stop" OP "W"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/innodb/autoinc_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/autoinc_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/autoinc_mutex" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/innodb/autoinc_mutex" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/innodb/autoinc_persisted_mutex" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "mutex/innodb/autoinc_persisted_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/innodb/autoinc_persisted_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/autoinc_persisted_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/buf_pool_chunks_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/buf_pool_chunks_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/buf_pool_chunks_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/buf_pool_chunks_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/buf_pool_chunks_mutex" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "mutex/innodb/buf_pool_chunks_mutex" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "mutex/innodb/buf_pool_chunks_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/buf_pool_chunks_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/buf_pool_chunks_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/buf_pool_chunks_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/innodb/buf_pool_chunks_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/buf_pool_chunks_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/buf_pool_flush_state_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/buf_pool_flush_state_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/buf_pool_flush_state_mutex" TO "mutex/innodb/page_sys_arch_oper_mutex"
ARC FROM "mutex/innodb/buf_pool_flush_state_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/buf_pool_flush_state_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/innodb/buf_pool_flush_state_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/buf_pool_free_list_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/buf_pool_free_list_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/buf_pool_free_list_mutex" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "mutex/innodb/buf_pool_free_list_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/buf_pool_free_list_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/buf_pool_free_list_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/innodb/buf_pool_free_list_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "mutex/innodb/page_sys_arch_oper_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/buf_pool_LRU_list_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/buf_pool_zip_free_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_free_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_free_mutex" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_free_mutex" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_free_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_free_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_hash_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_hash_mutex" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_hash_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_hash_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_mutex" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_mutex" TO "mutex/innodb/page_sys_arch_oper_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/buf_pool_zip_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/clone_snapshot_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/clone_task_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/log_sn_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/log_sys_arch_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/log_writer_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/page_sys_arch_client_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/page_sys_arch_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/page_sys_arch_oper_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/innodb/clone_snapshot_mutex" TO "sxlock/innodb/log_sn_lock" OP "X"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/innodb/clone_snapshot_mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/innodb/clone_task_mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/innodb/log_sys_arch_mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/innodb/log_writer_mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/innodb/page_sys_arch_client_mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/innodb/page_sys_arch_mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/innodb/page_sys_arch_oper_mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/innodb/clone_sys_mutex" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "mutex/innodb/clone_task_mutex" TO "mutex/innodb/clone_snapshot_mutex"
ARC FROM "mutex/innodb/dblwr_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/dblwr_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/dblwr_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/dblwr_mutex" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "mutex/innodb/dblwr_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/dblwr_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/dblwr_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/dblwr_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/dblwr_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/dict_foreign_err_mutex" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/innodb/dict_persist_dirty_tables_mutex" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/clone_snapshot_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/clone_sys_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/dict_foreign_err_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/fts_delete_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/master_key_id_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/page_zip_stat_per_index_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/recalc_pool_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/innodb/zip_pad_mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "rwlock/keyring/LOCK_keyring" OP "W"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/dict_table_stats" OP "S"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/fts_cache_init_rw_lock" OP "X"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/index_tree_rw_lock" OP "X"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/innodb/dict_sys_mutex" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/innodb/dict_table_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/dict_table_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/dict_table_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/innodb/dict_table_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/dict_table_mutex" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/innodb/dict_table_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/dict_table_mutex" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/innodb/dict_table_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/dict_table_mutex" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/innodb/dict_table_mutex" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/innodb/fil_system_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/fil_system_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/fil_system_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/innodb/fil_system_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/flush_list_mutex" TO "mutex/innodb/page_sys_arch_oper_mutex"
ARC FROM "mutex/innodb/flush_list_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/ibuf_bitmap_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/ibuf_bitmap_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/ibuf_bitmap_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/ibuf_bitmap_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/ibuf_bitmap_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/ibuf_bitmap_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/ibuf_bitmap_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/ibuf_bitmap_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/ibuf_bitmap_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/ibuf_bitmap_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/ibuf_bitmap_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/ibuf_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/ibuf_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/ibuf_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/ibuf_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/ibuf_mutex" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "mutex/innodb/ibuf_mutex" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "mutex/innodb/ibuf_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/ibuf_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/ibuf_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/ibuf_mutex" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/innodb/ibuf_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/ibuf_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/ibuf_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/ibuf_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/ibuf_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/ibuf_mutex" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/innodb/ibuf_mutex" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "mutex/innodb/ibuf_mutex"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/innodb/ibuf_pessimistic_insert_mutex" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/innodb/lock_sys_page_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/lock_sys_table_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/lock_sys_table_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/lock_sys_table_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/lock_sys_table_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/innodb/lock_wait_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/lock_wait_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/lock_wait_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/innodb/lock_wait_mutex" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/innodb/log_checkpointer_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/log_checkpointer_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/log_checkpointer_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/log_checkpointer_mutex" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/innodb/log_checkpointer_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/innodb/log_checkpointer_mutex" TO "mutex/innodb/log_writer_mutex"
ARC FROM "mutex/innodb/log_checkpointer_mutex" TO "mutex/innodb/page_sys_arch_oper_mutex"
ARC FROM "mutex/innodb/log_checkpointer_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/log_checkpointer_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/log_checkpointer_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/log_files_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/log_files_mutex" TO "mutex/innodb/log_flusher_mutex"
ARC FROM "mutex/innodb/log_files_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/innodb/log_files_mutex" TO "mutex/innodb/master_key_id_mutex"
ARC FROM "mutex/innodb/log_files_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/log_files_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/log_files_mutex" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/innodb/log_files_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/log_files_mutex" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "mutex/innodb/log_files_mutex" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/innodb/log_files_mutex" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/innodb/log_files_mutex" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "mutex/innodb/log_files_mutex" TO "rwlock/keyring/LOCK_keyring" OP "W"
ARC FROM "mutex/innodb/log_flusher_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/innodb/log_sn_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/log_sn_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/log_sn_mutex" TO "mutex/innodb/log_checkpointer_mutex"
ARC FROM "mutex/innodb/log_sn_mutex" TO "mutex/innodb/log_writer_mutex"
ARC FROM "mutex/innodb/log_sn_mutex" TO "mutex/innodb/page_sys_arch_oper_mutex"
ARC FROM "mutex/innodb/log_sn_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/log_sn_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/log_sn_mutex" TO "sxlock/innodb/log_sn_lock" OP "X"
ARC FROM "mutex/innodb/log_sys_arch_mutex" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/innodb/log_sys_arch_mutex" TO "mutex/innodb/log_writer_mutex"
ARC FROM "mutex/innodb/log_sys_arch_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/log_sys_arch_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/log_writer_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/log_writer_mutex" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/innodb/log_writer_mutex" TO "mutex/innodb/log_flusher_mutex"
ARC FROM "mutex/innodb/log_writer_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/innodb/log_writer_mutex" TO "mutex/innodb/master_key_id_mutex"
ARC FROM "mutex/innodb/log_writer_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/log_writer_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/log_writer_mutex" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/innodb/log_writer_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/log_writer_mutex" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "mutex/innodb/log_writer_mutex" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/innodb/log_writer_mutex" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/innodb/log_writer_mutex" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "mutex/innodb/log_writer_mutex" TO "rwlock/keyring/LOCK_keyring" OP "W"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/innodb/log_writer_mutex"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "rwlock/keyring/LOCK_keyring" OP "W"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/master_key_id_mutex" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/innodb/page_sys_arch_client_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/page_sys_arch_client_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/page_sys_arch_client_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/innodb/page_sys_arch_client_mutex" TO "mutex/innodb/log_sn_mutex"
ARC FROM "mutex/innodb/page_sys_arch_client_mutex" TO "mutex/innodb/page_sys_arch_mutex"
ARC FROM "mutex/innodb/page_sys_arch_client_mutex" TO "mutex/innodb/page_sys_arch_oper_mutex"
ARC FROM "mutex/innodb/page_sys_arch_client_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/page_sys_arch_client_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/page_sys_arch_client_mutex" TO "sxlock/innodb/log_sn_lock" OP "X"
ARC FROM "mutex/innodb/page_sys_arch_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/page_sys_arch_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/page_sys_arch_mutex" TO "mutex/innodb/log_sn_mutex"
ARC FROM "mutex/innodb/page_sys_arch_mutex" TO "mutex/innodb/page_sys_arch_oper_mutex"
ARC FROM "mutex/innodb/page_sys_arch_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/page_sys_arch_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/page_sys_arch_mutex" TO "sxlock/innodb/log_sn_lock" OP "X"
ARC FROM "mutex/innodb/page_sys_arch_oper_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/parser_mutex" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/innodb/parser_mutex" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/innodb/parser_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/parser_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/purge_sys_pq_mutex" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/innodb/rtr_active_mutex" TO "mutex/innodb/rtr_path_mutex"
ARC FROM "mutex/innodb/rw_lock_list_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/dict_foreign_err_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/ibuf_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/innodb/srv_innodb_monitor_mutex" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/dict_foreign_err_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/ibuf_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/srv_innodb_monitor_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/innodb/srv_monitor_file_mutex" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/innodb/sync_array_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "mutex/innodb/clone_sys_mutex"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/innodb/temp_pool_manager_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/temp_space_rseg_mutex" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/innodb/trx_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/trx_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/trx_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/trx_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/trx_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/trx_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/trx_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/trx_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/trx_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/innodb/trx_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/trx_mutex" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/innodb/trx_mutex" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/innodb/trx_mutex" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/innodb/trx_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/trx_mutex" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/innodb/trx_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/trx_pool_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/innodb/dict_foreign_err_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/trx_sys_mutex" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/trx_sys_serialisation_mutex" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/innodb/trx_sys_shard_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/trx_sys_shard_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/trx_sys_shard_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/temp_space_rseg_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/trx_undo_mutex" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/srv_sys_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/temp_space_rseg_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/innodb/undo_space_rseg_mutex" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/myisam/MYISAM_SHARE::intern_lock" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/myisam/MYISAM_SHARE::intern_lock" TO "mutex/mysys/KEY_CACHE::cache_lock"
ARC FROM "mutex/myisam/MYISAM_SHARE::intern_lock" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/myisam/MYISAM_SHARE::intern_lock" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/myisam/MYISAM_SHARE::intern_lock" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/myisam/MYISAM_SHARE::intern_lock" TO "rwlock/mysys/SAFE_HASH::lock" OP "W"
ARC FROM "mutex/mysqlx/client_session_exit" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/mysqlx/client_session_exit" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/mysqlx/client_session_exit" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "mutex/mysqlx/notice_output_queue" TO "mutex/mysqlx/vio_shutdown"
ARC FROM "mutex/mysqlx/scheduler_dynamic_worker_pending" TO "mutex/mysqlx/lock_list_access"
ARC FROM "mutex/mysqlx/server_client_exit" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/mysqlx/client_session_exit"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/mysqlx/vio_shutdown"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/sql/LOCK_item_func_sleep"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/sql/LOG::LOCK_log"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/mysqlx/server_client_exit" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/mysqlx/server_client_exit" TO "rwlock/mysqlx/client_list_clients" OP "R"
ARC FROM "mutex/mysqlx/server_client_exit" TO "rwlock/mysqlx/client_list_clients" OP "W"
ARC FROM "mutex/mysqlx/server_client_exit" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "mutex/mysqlx/server_client_exit" TO "rwlock/sql/LOGGER::LOCK_logger" OP "R"
ARC FROM "mutex/mysqlx/server_client_exit" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/mysys/THR_LOCK_charset" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/mysys/THR_LOCK::mutex" TO "mutex/csv/TINA_SHARE::mutex"
ARC FROM "mutex/mysys/THR_LOCK::mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/mysys/THR_LOCK_myisam" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/mysys/THR_LOCK_myisam" TO "mutex/myisam/MYISAM_SHARE::intern_lock"
ARC FROM "mutex/mysys/THR_LOCK_myisam" TO "mutex/mysys/KEY_CACHE::cache_lock"
ARC FROM "mutex/mysys/THR_LOCK_myisam" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/mysys/THR_LOCK_myisam" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/mysys/THR_LOCK_myisam" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/mysys/THR_LOCK_myisam" TO "rwlock/mysys/SAFE_HASH::lock" OP "R"
ARC FROM "mutex/mysys/THR_LOCK_myisam" TO "rwlock/mysys/SAFE_HASH::lock" OP "W"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/parser_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/recalc_pool_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/myisam/MYISAM_SHARE::intern_lock"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/mysys/KEY_CACHE::cache_lock"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/mysys/THR_LOCK_charset"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/mysys/THR_LOCK::mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/pfs_example/X"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/pfs_example/Y"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/LOG::LOCK_log"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "rwlock/pfs_example/T" OP "W"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "rwlock/pfs_example/X" OP "R"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "rwlock/pfs_example/Y" OP "W"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "rwlock/pfs_example/Z" OP "R"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "rwlock/sql/LOCK_system_variables_hash" OP "W"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "rwlock/sql/LOGGER::LOCK_logger" OP "R"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "rwlock/sql/THR_LOCK_udf" OP "W"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "rwlock/validate_pwd/LOCK_dict_file" OP "W"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/p_dyn_loader/key_component_id_by_urn_mutex" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/pfs_example/X" TO "mutex/pfs_example/Y"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/LOCK_event_queue"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "R"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "mutex/sql/Event_scheduler::LOCK_scheduler_state" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/sql/Gtid_state" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "mutex/sql/Gtid_state" TO "mutex/sql/Gtid_state"
ARC FROM "mutex/sql/Gtid_state" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/key_mta_temp_table_LOCK" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/LOCK_audit_mask" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/LOCK_collect_instance_log" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_collect_instance_log" TO "mutex/innodb/log_checkpointer_mutex"
ARC FROM "mutex/sql/LOCK_collect_instance_log" TO "mutex/innodb/log_sn_mutex"
ARC FROM "mutex/sql/LOCK_collect_instance_log" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/LOCK_collect_instance_log" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_collect_instance_log" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/LOCK_collect_instance_log" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_log"
ARC FROM "mutex/sql/LOCK_collect_instance_log" TO "mutex/sql/Source_info::data_lock"
ARC FROM "mutex/sql/LOCK_collect_instance_log" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/LOCK_collect_instance_log" TO "sxlock/innodb/log_sn_lock" OP "X"
ARC FROM "mutex/sql/LOCK_error_messages" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "cond/semisync/Ack_receiver::m_cond"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "cond/sql/COND_flush_thread_cache"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/group_rpl/GCS_Gcs_suspicions_manager::m_suspicions_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/group_rpl/GCS_Gcs_suspicions_manager::m_suspicions_parameters_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/group_rpl/LOCK_autorejoin_module"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/dblwr_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/ibuf_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/lock_wait_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/log_checkpointer_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/log_sn_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/log_writer_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/page_cleaner_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/page_sys_arch_oper_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/page_zip_stat_per_index_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/srv_sys_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/mysqlx/lock_list_access"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/mysqlx/scheduler_dynamic_worker_pending"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/semisync/Ack_receiver::m_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/semisync/LOCK_binlog_"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/session/LOCK_srv_session_threads"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/Gtid_state"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/hash_filo::lock"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_acl_cache_flush"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_admin_tls_ctx_options"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_authentication_policy"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_compress_gtid_table"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_default_password_lifetime"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_global_conn_mem_limit"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_log_throttle_qni"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_mandatory_roles"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_password_history"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_password_reuse_interval"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_plugin_delete"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_prepared_stmt_count"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_replica_net_timeout"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_replica_trans_dep_tracker"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_sql_replica_skip_counter"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_thread_cache"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/LOCK_tls_ctx_options"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_index"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_log"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_xids"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "prlock/sql/MDL_lock::rwlock" OP "R"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/conn_delay/connection_event_delay_lock" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/group_rpl/RWLOCK_plugin_running" OP "TRY R"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/keyring/LOCK_keyring" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/mysqlx/client_list_clients" OP "R"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/Binlog_relay_IO_delegate::lock" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/Binlog_storage_delegate::lock" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/Binlog_transmit_delegate::lock" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/LOCK_sys_init_connect" OP "R"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/LOCK_sys_init_connect" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/LOCK_sys_init_replica" OP "R"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/LOCK_sys_init_replica" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/LOCK_system_variables_hash" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/LOGGER::LOCK_logger" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/Server_state_delegate::lock" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/sql/Trans_delegate::lock" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/validate/LOCK_dict_file" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "rwlock/validate_pwd/LOCK_dict_file" OP "W"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/index_tree_rw_lock" OP "X"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/log_sn_lock" OP "X"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/rsegs_lock" OP "X"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/trx_purge_latch" OP "X"
ARC FROM "mutex/sql/LOCK_global_system_variables" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/LOCK_mandatory_roles" TO "mutex/sql/LOCK_acl_cache_flush"
ARC FROM "mutex/sql/LOCK_mdl_context_backup_manager" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/LOCK_open" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/archive/Archive_share::mutex"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/blackhole/blackhole"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/csv/tina"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/federated/federated"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/innodb/rtr_active_mutex"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/innodb/srv_sys_mutex"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/myisam/MYISAM_SHARE::intern_lock"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/mysys/KEY_CACHE::cache_lock"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/mysys/THR_LOCK_heap"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/mysys/THR_LOCK_myisam"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/session/LOCK_srv_session_threads"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/sql/LOCK_plugin_delete"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/sql/TABLE_SHARE::LOCK_ha_data"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/LOCK_open" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/LOCK_open" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "R"
ARC FROM "mutex/sql/LOCK_open" TO "prlock/sql/MDL_lock::rwlock" OP "R"
ARC FROM "mutex/sql/LOCK_open" TO "rwlock/sql/LOCK_system_variables_hash" OP "W"
ARC FROM "mutex/sql/LOCK_open" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_admin_tls_ctx_options"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_authentication_policy"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_default_password_lifetime"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_global_conn_mem_limit"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_mandatory_roles"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_password_history"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_password_reuse_interval"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_prepared_stmt_count"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_replica_net_timeout"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_replica_trans_dep_tracker"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_sql_replica_skip_counter"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/LOCK_tls_ctx_options"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "mutex/sql/THD::LOCK_thd_sysvar"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "rwlock/sql/LOCK_sys_init_connect" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "rwlock/sql/LOCK_sys_init_replica" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_delete" TO "rwlock/sql/LOCK_system_variables_hash" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "cond/group_rpl/COND_applier_module_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "cond/group_rpl/COND_message_service_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "cond/group_rpl/COND_mysql_thread_handler_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "cond/group_rpl/COND_mysql_thread_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "cond/group_rpl/COND_view_modification_wait"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "cond/semisync/Ack_receiver::m_cond"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "cond/sql/Item_func_sleep::cond"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "cond/sql/MDL_context::COND_wait_status"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/csv/tina"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/csv/TINA_SHARE::mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/GCS_Gcs_async_buffer::m_free_buffer_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/GCS_Gcs_xcom_engine::m_wait_for_notification_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/GCS_Gcs_xcom_view_change_control::m_joining_leaving_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_process"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread_end"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/key_GR_LOCK_group_part_handler_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_applier_module_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_applier_module_suspend"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_autorejoin_module"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_certification_members"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_clone_handler_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_clone_query"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_message_service_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_mysql_handler_thread_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_mysql_thread_dispatcher_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_mysql_thread_handler_dispatcher_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_mysql_thread_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_plugin_modules_termination"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_plugin_online"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_primary_promotion_policy"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_recovery_module_run"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_view_modification_wait"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_wait_ticket"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/group_rpl/LOCK_write_lock_protection"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/clone_sys_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/ibuf_bitmap_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/parallel_read_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/parser_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/recalc_pool_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/temp_pool_manager_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/mysys/THR_LOCK::mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/pfs_example2/LOCK_ename_records_array"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/pfs_example2/LOCK_esalary_records_array"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/pfs_example2/LOCK_machine_records_array"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/semisync/Ack_receiver::m_mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/semisync/LOCK_binlog_"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/session/LOCK_srv_session_threads"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_acl_cache_flush"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_admin_tls_ctx_options"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_audit_mask"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_authentication_policy"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_connection_count"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_default_password_lifetime"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_event_queue"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_global_conn_mem_limit"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_item_func_sleep"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_mandatory_roles"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_password_history"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_password_reuse_interval"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_plugin_delete"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_prepared_stmt_count"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_replica_net_timeout"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_replica_trans_dep_tracker"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_sql_replica_skip_counter"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_tls_ctx_options"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOCK_user_conn"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/LOG::LOCK_log"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_commit"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_commit_queue"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_done"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_flush_queue"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_log"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync_queue"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_xids"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/Source_info::run_lock"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/THD::LOCK_thd_protocol"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/THD::LOCK_thd_security_ctx"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "mutex/sql/THD::LOCK_thd_sysvar"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "prlock/sql/MDL_lock::rwlock" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/conn_delay/connection_event_delay_lock" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/group_rpl/RWLOCK_channel_observation_list" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/group_rpl/RWLOCK_gcs_operations_view_change_observers" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/group_rpl/RWLOCK_group_event_observation_list" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/group_rpl/RWLOCK_plugin_running" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/group_rpl/RWLOCK_plugin_stop" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_prepared_transactions_on_my_applier" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/session/LOCK_srv_session_collection" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/Binlog_relay_IO_delegate::lock" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/Binlog_storage_delegate::lock" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/Binlog_transmit_delegate::lock" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/channel_lock" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/LOCK_sys_init_connect" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/LOCK_sys_init_replica" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/LOCK_system_variables_hash" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/LOGGER::LOCK_logger" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/Server_state_delegate::lock" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/THR_LOCK_udf" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/THR_LOCK_udf" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/sql/Trans_delegate::lock" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "rwlock/validate/LOCK_dict_file" OP "W"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/dict_operation_lock" OP "X"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/dict_table_stats" OP "S"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/index_tree_rw_lock" OP "X"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/LOCK_plugin_install" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/LOCK_plugin" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/LOCK_plugin" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/LOCK_plugin" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/sql/LOCK_plugin" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/sql/LOCK_plugin" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/LOCK_plugin" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/LOCK_plugin" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/LOCK_plugin" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/LOCK_plugin" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/LOCK_plugin" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/LOCK_plugin" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/LOCK_replica_net_timeout" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_replica_net_timeout" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "cond/sql/MDL_context::COND_wait_status"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/csv/tina"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/commit_cond_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/lock_wait_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "R"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "W"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "prlock/sql/MDL_lock::rwlock" OP "R"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/LOCK_reset_gtid_table" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_index"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_log"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_xids"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_index"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_xids"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "prlock/sql/MDL_lock::rwlock" OP "R"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "rwlock/keyring/LOCK_keyring" OP "W"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/LOCK_rotate_binlog_master_key" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "mutex/sql/LOCK_status" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/innodb/srv_innodb_monitor_mutex"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/mysqlx/client_session_exit"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/mysqlx/server_client_exit"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/semisync/LOCK_binlog_"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/sql/LOCK_global_conn_mem_limit"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/sql/LOCK_prepared_stmt_count"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "mutex/sql/LOCK_status" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/LOCK_status" TO "rwlock/mysqlx/client_list_clients" OP "R"
ARC FROM "mutex/sql/LOCK_status" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "mutex/sql/LOCK_status" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/LOCK_status" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/LOCK_status" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/LOCK_table_cache" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/archive/Archive_share::mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/blackhole/blackhole"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/csv/tina"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/federated/federated"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/innodb/rtr_active_mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/innodb/srv_sys_mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/myisam/MYISAM_SHARE::intern_lock"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/mysys/KEY_CACHE::cache_lock"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/mysys/THR_LOCK_heap"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/mysys/THR_LOCK_myisam"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/session/LOCK_srv_session_threads"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/sql/LOCK_plugin_delete"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/sql/TABLE_SHARE::LOCK_ha_data"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/LOCK_table_cache" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/LOCK_table_cache" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "R"
ARC FROM "mutex/sql/LOCK_table_cache" TO "prlock/sql/MDL_lock::rwlock" OP "R"
ARC FROM "mutex/sql/LOCK_table_cache" TO "rwlock/sql/LOCK_system_variables_hash" OP "W"
ARC FROM "mutex/sql/LOCK_table_cache" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/LOCK_table_cache" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/LOCK_thd_list" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/LOCK_thd_list" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/LOCK_thd_list" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/LOCK_thd_list" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_thd_list" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/LOCK_thd_list" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/LOCK_thd_list" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "mutex/sql/LOCK_thd_list" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/LOCK_thd_list" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/sql/LOCK_thd_list" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/LOCK_thd_list" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "mutex/sql/THD::LOCK_thd_protocol"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "mutex/sql/THD::LOCK_thd_security_ctx"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "mutex/sql/LOCK_thd_remove" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/LOCK_transaction_cache" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_transaction_cache" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_user_conn" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/LOCK_user_conn" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/LOCK_user_conn" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/LOCK_uuid_generator" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "mutex/sql/LOG::LOCK_log" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "cond/semisync/COND_binlog_send_"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "cond/sql/MYSQL_BIN_LOG::COND_done"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/fts_delete_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/log_flusher_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/log_writer_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/parser_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/srv_sys_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/temp_space_rseg_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/semisync/LOCK_binlog_"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/Gtid_state"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/LOCK_replica_trans_dep_tracker"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_commit_queue"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_done"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_xids"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "rwlock/sql/Binlog_storage_delegate::lock" OP "R"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "sxlock/innodb/fts_cache_rw_lock" OP "X"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_commit" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_done" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_done" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_done" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_flush_queue" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_flush_queue" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/Gtid_state"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/LOCK_compress_gtid_table"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/LOCK_replica_trans_dep_tracker"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/LOCK_reset_gtid_table"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_commit"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_commit_queue"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_done"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_flush_queue"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync_queue"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_index" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "cond/sql/MYSQL_BIN_LOG::prep_xids_cond"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/log_checkpointer_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/log_flusher_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/log_sn_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/log_writer_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/semisync/LOCK_binlog_"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/Gtid_state"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/LOCK_compress_gtid_table"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/LOCK_replica_trans_dep_tracker"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/LOCK_reset_gtid_table"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_commit"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_commit_queue"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_done"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_flush_queue"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_index"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync_queue"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_xids"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "prlock/sql/MDL_lock::rwlock" OP "R"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "rwlock/sql/Binlog_storage_delegate::lock" OP "R"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "sxlock/innodb/log_sn_lock" OP "X"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_log" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_sync" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_sync" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_sync" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_sync" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_sync" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_commit_queue"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_sync" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync_queue"
ARC FROM "mutex/sql/MYSQL_BIN_LOG::LOCK_sync" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_index" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_index" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_index" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_index" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_index" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_index" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_index" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_index" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_index" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_index" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_index" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_index" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos" TO "mutex/sql/Relay_log_info::data_lock"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_index"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_xids"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/Relay_log_info::data_lock"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/Replica_reporting_capability::err_lock"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/Source_info::data_lock"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/Source_info::rotate_lock"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "rwlock/sql/gtid_retrieved" OP "R"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/MYSQL_RELAY_LOG::LOCK_log" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/recalc_pool_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_log"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/Partition_share::auto_inc_mutex" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "cond/group_rpl/COND_mysql_thread_handler_dispatcher_run"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/group_rpl/LOCK_certification_info"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/group_rpl/LOCK_mysql_thread_handler_dispatcher_run"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/group_rpl/LOCK_recovery"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/lock_wait_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/log_limits_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/key_mta_temp_table_LOCK"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_index"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/Relay_log_info::info_thd_lock"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/Replica_reporting_capability::err_lock"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "mutex/sql/Worker_info::jobs_lock"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "prlock/sql/MDL_lock::rwlock" OP "R"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "rwlock/sql/rpl_filter_lock" OP "R"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/Relay_log_info::data_lock" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_index"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_xids"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/Relay_log_info::log_space_lock" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "cond/sql/Source_info::start_cond"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "cond/sql/Source_info::stop_cond"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "cond/sql/Source_IO_monitor::run_cond"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "cond/sql/Worker_info::jobs_cond"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/group_rpl/LOCK_primary_election_running_flag"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/lock_wait_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/key_mta_gaq_LOCK"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/key_mta_temp_table_LOCK"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/LOCK_sql_replica_skip_counter"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_index"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Relay_log_info::data_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Relay_log_info::info_thd_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Relay_log_info::sleep_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Replica_reporting_capability::err_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Source_info::data_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Source_info::run_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Source_info::sleep_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Source_IO_monitor::run_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "mutex/sql/Worker_info::jobs_lock"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "rwlock/group_rpl/RWLOCK_channel_observation_list" OP "R"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "R"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "rwlock/sql/Binlog_relay_IO_delegate::lock" OP "R"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "rwlock/sql/rpl_filter_lock" OP "W"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/Relay_log_info::run_lock" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/Replica_reporting_capability::err_lock" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/Replica_reporting_capability::err_lock" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/Replica_reporting_capability::err_lock" TO "mutex/sql/Replica_reporting_capability::err_lock"
ARC FROM "mutex/sql/Replica_reporting_capability::err_lock" TO "rwlock/sql/rpl_filter_lock" OP "R"
ARC FROM "mutex/sql/Source_info::data_lock" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/lock_wait_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/log_checkpointer_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/log_sn_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/key_mta_temp_table_LOCK"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_log"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_index"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/Relay_log_info::data_lock"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/Relay_log_info::info_thd_lock"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/Replica_reporting_capability::err_lock"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/Source_info::data_lock"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/Source_info::data_lock" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/sql/Source_info::data_lock" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/Source_info::data_lock" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/Source_info::data_lock" TO "rwlock/sql/gtid_retrieved" OP "R"
ARC FROM "mutex/sql/Source_info::data_lock" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "mutex/sql/Source_info::data_lock" TO "rwlock/sql/rpl_filter_lock" OP "R"
ARC FROM "mutex/sql/Source_info::data_lock" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/log_sn_lock" OP "X"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/Source_info::data_lock" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/Source_info::rotate_lock" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "cond/sql/Relay_log_info::start_cond"
ARC FROM "mutex/sql/Source_info::run_lock" TO "cond/sql/Relay_log_info::stop_cond"
ARC FROM "mutex/sql/Source_info::run_lock" TO "cond/sql/Source_IO_monitor::run_cond"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/lock_wait_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/key_mta_gaq_LOCK"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/key_mta_temp_table_LOCK"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/LOCK_replica_list"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/LOCK_sql_replica_skip_counter"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/LOCK_thd_list"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_index"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Relay_log_info::data_lock"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Relay_log_info::info_thd_lock"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Relay_log_info::sleep_lock"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Replica_reporting_capability::err_lock"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Source_info::data_lock"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Source_info::info_thd_lock"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Source_info::sleep_lock"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Source_IO_monitor::run_lock"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "mutex/sql/Source_info::run_lock" TO "mutex/sql/Worker_info::jobs_lock"
ARC FROM "mutex/sql/Source_info::run_lock" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/Source_info::run_lock" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "R"
ARC FROM "mutex/sql/Source_info::run_lock" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "mutex/sql/Source_info::run_lock" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/Source_info::run_lock" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "mutex/sql/Source_info::run_lock" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "mutex/sql/Source_info::run_lock" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/Source_info::run_lock" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/Source_IO_monitor::run_lock" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/Source_IO_monitor::run_lock" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/Source_IO_monitor::run_lock" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/Source_IO_monitor::run_lock" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/Source_IO_monitor::run_lock" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/Source_IO_monitor::run_lock" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "mutex/myisam/MYISAM_SHARE::intern_lock"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "mutex/mysys/KEY_CACHE::cache_lock"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "mutex/mysys/THR_LOCK_myisam"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "rwlock/mysys/SAFE_HASH::lock" OP "R"
ARC FROM "mutex/sql/TABLE_SHARE::LOCK_ha_data" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/mysys/THR_LOCK::mutex"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/Gtid_state"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/key_mta_gaq_LOCK"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/LOCK_event_queue"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/LOCK_group_replication_connection_mutex"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/LOCK_item_func_sleep"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/Relay_log_info::sleep_lock"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/Source_info::sleep_lock"
ARC FROM "mutex/sql/THD::LOCK_current_cond" TO "mutex/sql/Worker_info::jobs_lock"
ARC FROM "mutex/sql/THD::LOCK_query_plan" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/THD::LOCK_query_plan" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/group_rpl/LOCK_certifier_broadcast_dispatcher_run"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/innodb/flush_list_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/innodb/srv_innodb_monitor_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/mysqlx/client_session_exit"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/mysqlx/server_client_exit"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/mysys/THR_LOCK::mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/semisync/LOCK_binlog_"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/Gtid_state"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/key_mta_gaq_LOCK"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_admin_tls_ctx_options"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_authentication_policy"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_default_password_lifetime"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_event_queue"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_global_conn_mem_limit"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_group_replication_connection_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_item_func_sleep"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_mandatory_roles"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_password_history"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_password_reuse_interval"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_prepared_stmt_count"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_replica_net_timeout"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_replica_trans_dep_tracker"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_sql_replica_skip_counter"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/LOCK_tls_ctx_options"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/Relay_log_info::sleep_lock"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/Source_info::sleep_lock"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/THD::LOCK_thd_sysvar"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "mutex/sql/Worker_info::jobs_lock"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "rwlock/mysqlx/client_list_clients" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "rwlock/sql/LOCK_sys_init_connect" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "rwlock/sql/LOCK_sys_init_replica" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/THD::LOCK_thd_data" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "mutex/sql/THD::LOCK_thd_protocol" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_protocol" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_protocol" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/THD::LOCK_thd_protocol" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_protocol" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/csv/TINA_SHARE::mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/mysys/THR_LOCK::mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/hash_filo::lock"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/LOCK_default_password_lifetime"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/LOCK_prepared_stmt_count"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/LOCK_status"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/LOG::LOCK_log"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "mutex/sql/THD::LOCK_thd_protocol"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "rwlock/sha2_auth/key_m_cache_lock" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "rwlock/sha2_auth/key_m_cache_lock" OP "W"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "rwlock/sql/LOGGER::LOCK_logger" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/THD::LOCK_thd_security_ctx" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "cond/sql/Gtid_state"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "mutex/mysqlx/client_session_exit"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "mutex/mysqlx/scheduler_dynamic_worker_pending"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "mutex/sql/Gtid_state"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "rwlock/mysqlx/client_list_clients" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "mutex/sql/THD::LOCK_thd_sysvar" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "mutex/sql/tz_LOCK" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/autoinc_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/dict_table_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/fil_system_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/log_files_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/sync_array_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/trx_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/myisam/MYISAM_SHARE::intern_lock"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/mysys/KEY_CACHE::cache_lock"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/mysys/THR_LOCK_myisam"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/sql/LOCK_open"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/sql/LOCK_plugin"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/sql/LOCK_table_cache"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/sql/TABLE_SHARE::LOCK_ha_data"
ARC FROM "mutex/sql/tz_LOCK" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "mutex/sql/tz_LOCK" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "mutex/sql/tz_LOCK" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "mutex/sql/tz_LOCK" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "mutex/sql/tz_LOCK" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "mutex/sql/tz_LOCK" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "mutex/sql/tz_LOCK" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "mutex/sql/tz_LOCK" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "mutex/sql/Worker_info::jobs_lock" TO "mutex/sql/Relay_log_info::exit_count_lock"
ARC FROM "mutex/sql/Worker_info::jobs_lock" TO "mutex/sql/Relay_log_info::info_thd_lock"
ARC FROM "prlock/sql/MDL_context::LOCK_waiting_for" STATE "R" TO "mutex/sql/LOCK_open"
ARC FROM "prlock/sql/MDL_context::LOCK_waiting_for" STATE "R" TO "mutex/sql/LOCK_table_cache"
ARC FROM "prlock/sql/MDL_context::LOCK_waiting_for" STATE "R" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "prlock/sql/MDL_context::LOCK_waiting_for" STATE "R" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "R"
ARC FROM "prlock/sql/MDL_context::LOCK_waiting_for" STATE "R" TO "prlock/sql/MDL_context::LOCK_waiting_for" RECURSIVE OP "R"
ARC FROM "prlock/sql/MDL_context::LOCK_waiting_for" STATE "R" TO "prlock/sql/MDL_lock::rwlock" OP "R"
ARC FROM "prlock/sql/MDL_lock::rwlock" STATE "R" TO "mutex/sql/LOCK_open"
ARC FROM "prlock/sql/MDL_lock::rwlock" STATE "R" TO "mutex/sql/LOCK_table_cache"
ARC FROM "prlock/sql/MDL_lock::rwlock" STATE "R" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "prlock/sql/MDL_lock::rwlock" STATE "R" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "R"
ARC FROM "prlock/sql/MDL_lock::rwlock" STATE "R" TO "prlock/sql/MDL_lock::rwlock" OP "R"
ARC FROM "prlock/sql/MDL_lock::rwlock" STATE "R" TO "prlock/sql/MDL_lock::rwlock" RECURSIVE OP "R"
ARC FROM "prlock/sql/MDL_lock::rwlock" STATE "W" TO "mutex/mysys/THR_LOCK::mutex"
ARC FROM "prlock/sql/MDL_lock::rwlock" STATE "W" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "prlock/sql/MDL_lock::rwlock" STATE "W" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/group_rpl/RWLOCK_certifier_stable_gtid_set" STATE "W" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_channel_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_applier_module_suspend"
ARC FROM "rwlock/group_rpl/RWLOCK_channel_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_channel_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_channel_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_primary_election_running_flag"
ARC FROM "rwlock/group_rpl/RWLOCK_channel_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_recovery"
ARC FROM "rwlock/group_rpl/RWLOCK_channel_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "rwlock/group_rpl/RWLOCK_channel_observation_list" STATE "R" TO "mutex/sql/Relay_log_info::info_thd_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_channel_observation_list" STATE "R" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_channel_observation_list" STATE "R" TO "mutex/sql/Source_info::info_thd_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_channel_observation_list" STATE "R" TO "mutex/sql/Worker_info::jobs_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "R" TO "mutex/group_rpl/GCS_Gcs_xcom_view_change_control::m_current_view_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "R" TO "mutex/group_rpl/GCS_Gcs_xcom_view_change_control::m_joining_leaving_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "cond/group_rpl/COND_view_modification_wait"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "cond/group_rpl/GCS_Gcs_xcom_interface::m_wait_for_ssl_init_cond"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "mutex/group_rpl/GCS_Gcs_async_buffer::m_free_buffer_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "mutex/group_rpl/GCS_Gcs_suspicions_manager::m_suspicions_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "mutex/group_rpl/GCS_Gcs_suspicions_manager::m_suspicions_parameters_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "mutex/group_rpl/GCS_Gcs_xcom_engine::m_wait_for_notification_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "mutex/group_rpl/GCS_Gcs_xcom_group_management::m_nodes_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "mutex/group_rpl/GCS_Gcs_xcom_interface::m_wait_for_ssl_init_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "mutex/group_rpl/GCS_Gcs_xcom_view_change_control::m_joining_leaving_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "mutex/group_rpl/LOCK_connection_map"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "mutex/group_rpl/LOCK_view_modification_wait"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations" STATE "W" TO "rwlock/group_rpl/RWLOCK_gcs_operations_view_change_observers" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_gcs_operations_view_change_observers" STATE "R" TO "mutex/group_rpl/LOCK_view_modification_wait"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_process"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread_end"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_clone_donor_list"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_multi_primary_action_notification"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_pipeline_continuation"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_primary_election_action_notification"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_primary_election_action_phase"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_primary_election_primary_process_run"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_primary_election_secondary_process_run"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_primary_election_validation_notification"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_stage_monitor_handler"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_group_event_observation_list" STATE "R" TO "rwlock/group_rpl/RWLOCK_group_event_observation_list" RECURSIVE OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "cond/group_rpl/COND_view_modification_wait"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/group_rpl/GCS_Gcs_suspicions_manager::m_suspicions_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/group_rpl/GCS_Gcs_suspicions_manager::m_suspicions_parameters_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/group_rpl/GCS_Gcs_xcom_group_management::m_nodes_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/group_rpl/LOCK_applier_module_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/group_rpl/LOCK_autorejoin_module"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/group_rpl/LOCK_certification_info"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/group_rpl/LOCK_force_members_running"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/group_rpl/LOCK_view_modification_wait"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/autoinc_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/clone_sys_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/dict_table_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/fil_system_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/flush_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/log_files_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/log_limits_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/parallel_read_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/parser_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/recalc_pool_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/sync_array_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/temp_pool_manager_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/trx_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/mysys/THR_LOCK_charset"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/mysys/THR_LOCK::mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/Event_scheduler::LOCK_scheduler_state"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_admin_tls_ctx_options"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_authentication_policy"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_connection_count"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_default_password_lifetime"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_global_conn_mem_limit"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_mandatory_roles"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_open"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_password_history"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_password_reuse_interval"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_plugin_delete"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_prepared_stmt_count"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_replica_list"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_replica_net_timeout"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_replica_trans_dep_tracker"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_sql_replica_skip_counter"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_status"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_table_cache"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_thd_list"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_tls_ctx_options"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/LOG::LOCK_log"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/THD::LOCK_thd_protocol"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "mutex/sql/THD::LOCK_thd_sysvar"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/group_rpl/RWLOCK_certifier_stable_gtid_set" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/group_rpl/RWLOCK_flow_control_module_info" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/group_rpl/RWLOCK_gcs_operations_view_change_observers" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/group_rpl/RWLOCK_plugin_running" RECURSIVE OP "TRY R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_prepared_transactions_on_my_applier" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/session/LOCK_srv_session_collection" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/sql/LOCK_sys_init_connect" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/sql/LOCK_sys_init_replica" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/sql/LOGGER::LOCK_logger" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/sql/THR_LOCK_udf" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/sql/THR_LOCK_udf" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/dict_operation_lock" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/dict_table_stats" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/index_tree_rw_lock" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "R" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/group_rpl/COND_applier_module_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/group_rpl/COND_clone_handler_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/group_rpl/COND_group_action_coordinator_thread"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/group_rpl/COND_message_service_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/group_rpl/COND_mysql_thread_handler_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/group_rpl/COND_mysql_thread_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/group_rpl/COND_recovery_module_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/group_rpl/COND_session_thread_method_exec"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/group_rpl/COND_session_thread_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/group_rpl/COND_view_modification_wait"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/group_rpl/COND_write_lock_protection"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/group_rpl/GCS_Gcs_xcom_interface::m_wait_for_ssl_init_cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/sql/Relay_log_info::stop_cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/sql/Source_info::stop_cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "cond/sql/Source_IO_monitor::run_cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/GCS_Gcs_async_buffer::m_free_buffer_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/GCS_Gcs_suspicions_manager::m_suspicions_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/GCS_Gcs_suspicions_manager::m_suspicions_parameters_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/GCS_Gcs_xcom_engine::m_wait_for_notification_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/GCS_Gcs_xcom_interface::m_wait_for_ssl_init_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/GCS_Gcs_xcom_view_change_control::m_joining_leaving_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_process"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread_end"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/key_GR_LOCK_group_part_handler_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_applier_module_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_applier_module_suspend"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_autorejoin_module"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_certification_members"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_clone_handler_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_clone_query"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_connection_map"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_delayed_init_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_delayed_init_server_ready"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_message_service_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_mysql_handler_thread_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_mysql_thread_dispatcher_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_mysql_thread_handler_dispatcher_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_mysql_thread_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_plugin_modules_termination"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_plugin_online"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_primary_promotion_policy"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_recovery"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_recovery_module_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_session_thread_method_exec"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_session_thread_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_view_modification_wait"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_wait_ticket"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/group_rpl/LOCK_write_lock_protection"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/fil_system_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/flush_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/trx_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/mysqlx/broker_context_sync"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/session/LOCK_srv_session_threads"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/Event_scheduler::LOCK_scheduler_state"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/key_mta_temp_table_LOCK"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_admin_tls_ctx_options"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_connection_count"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_open"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_replica_list"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_status"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_table_cache"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_thd_list"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_tls_ctx_options"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/LOG::LOCK_log"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/Replica_reporting_capability::err_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/Source_info::data_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/Source_info::run_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/Source_IO_monitor::run_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/THD::LOCK_thd_protocol"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "mutex/sql/THD::LOCK_thd_sysvar"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/group_rpl/RWLOCK_gcs_operations_view_change_observers" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/group_rpl/RWLOCK_plugin_stop" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_prepared_transactions_on_my_applier" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/session/LOCK_srv_session_collection" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/sql/channel_lock" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/sql/LOGGER::LOCK_logger" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_running" STATE "W" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "cond/group_rpl/COND_count_down_latch"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/group_rpl/LOCK_count_down_latch"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/group_rpl/LOCK_pipeline_stats_transactions_waiting_apply"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/group_rpl/LOCK_wait_ticket"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/group_rpl/LOCK_write_lock_protection"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/autoinc_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/clone_sys_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/dict_table_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/fil_system_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/flush_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/log_files_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/parser_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/recalc_pool_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/sync_array_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/trx_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/Gtid_state"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_connection_count"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_open"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_replica_list"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_replica_trans_dep_tracker"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_status"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_table_cache"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_thd_list"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/LOG::LOCK_log"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_commit"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_commit_queue"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_done"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_flush_queue"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_log"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync_queue"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_xids"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/THD::LOCK_thd_protocol"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "mutex/sql/THD::LOCK_thd_sysvar"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/group_rpl/RWLOCK_plugin_running" OP "TRY R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/group_rpl/RWLOCK_plugin_stop" RECURSIVE OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_prepared_transactions_on_my_applier" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/session/LOCK_srv_session_collection" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/sql/LOGGER::LOCK_logger" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/sql/THR_LOCK_udf" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/dict_operation_lock" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/dict_table_stats" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/index_tree_rw_lock" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "R" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "cond/group_rpl/COND_applier_module_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "cond/group_rpl/COND_clone_handler_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "cond/group_rpl/COND_group_action_coordinator_thread"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "cond/group_rpl/COND_message_service_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "cond/group_rpl/COND_mysql_thread_handler_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "cond/group_rpl/COND_mysql_thread_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "cond/group_rpl/COND_recovery_module_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "cond/group_rpl/COND_view_modification_wait"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "cond/sql/Relay_log_info::stop_cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "cond/sql/Source_info::stop_cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "cond/sql/Source_IO_monitor::run_cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/GCS_Gcs_async_buffer::m_free_buffer_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/GCS_Gcs_xcom_engine::m_wait_for_notification_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/GCS_Gcs_xcom_view_change_control::m_joining_leaving_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_process"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread_end"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/key_GR_LOCK_group_part_handler_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_applier_module_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_applier_module_suspend"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_autorejoin_module"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_certification_members"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_clone_handler_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_clone_query"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_message_service_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_mysql_handler_thread_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_mysql_thread_dispatcher_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_mysql_thread_handler_dispatcher_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_mysql_thread_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_plugin_applier_module_initialize_terminate"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_plugin_modules_termination"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_plugin_online"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_recovery"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_recovery_module_run"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_transaction_unblocking"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_view_modification_wait"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_wait_ticket"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/group_rpl/LOCK_write_lock_protection"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/fil_system_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/flush_list_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/trx_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/mysqlx/broker_context_sync"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/key_mta_temp_table_LOCK"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/LOCK_open"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/LOCK_table_cache"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/LOCK_thd_list"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/Replica_reporting_capability::err_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/Source_info::data_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/Source_info::run_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/Source_IO_monitor::run_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "rwlock/group_rpl/RWLOCK_gcs_operations_view_change_observers" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "rwlock/sql/channel_lock" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_plugin_stop" STATE "W" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_info_members_that_must_prepare_the_transaction" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "R" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "R" TO "mutex/group_rpl/LOCK_count_down_latch"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "R" TO "mutex/group_rpl/LOCK_wait_ticket"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "R" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "R" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_info_members_that_must_prepare_the_transaction" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_info_members_that_must_prepare_the_transaction" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_prepared_transactions_on_my_applier" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "W" TO "mutex/group_rpl/LOCK_count_down_latch"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "W" TO "mutex/group_rpl/LOCK_wait_ticket"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "W" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_info_members_that_must_prepare_the_transaction" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" STATE "W" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_info_members_that_must_prepare_the_transaction" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_prepared_transactions_on_my_applier" STATE "W" TO "mutex/group_rpl/LOCK_count_down_latch"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_prepared_transactions_on_my_applier" STATE "W" TO "mutex/group_rpl/LOCK_wait_ticket"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "cond/group_rpl/COND_count_down_latch"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "cond/group_rpl/COND_primary_promotion_policy"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "cond/sql/Gtid_state"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_count_down_latch"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_primary_promotion_policy"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_server_ongoing_transaction_handler"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "mutex/group_rpl/LOCK_wait_ticket"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "mutex/sql/Gtid_state"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_prepared_transactions_on_my_applier" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_prepared_transactions_on_my_applier" OP "W"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "rwlock/group_rpl/RWLOCK_transaction_observation_list" STATE "R" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "rwlock/keyring/LOCK_keyring" STATE "W" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "rwlock/myisam/MYISAM_SHARE::key_root_lock" STATE "R" TO "mutex/mysys/KEY_CACHE::cache_lock"
ARC FROM "rwlock/myisam/MYISAM_SHARE::key_root_lock" STATE "R" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/myisam/MYISAM_SHARE::key_root_lock" STATE "W" TO "mutex/mysys/KEY_CACHE::cache_lock"
ARC FROM "rwlock/myisam/MYISAM_SHARE::key_root_lock" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/mysqlx/client_list_clients" STATE "W" TO "mutex/mysqlx/page_pool"
ARC FROM "rwlock/mysqlx/client_list_clients" STATE "W" TO "mutex/mysqlx/server_state_sync"
ARC FROM "rwlock/mysqlx/client_list_clients" STATE "W" TO "mutex/mysqlx/vio_shutdown"
ARC FROM "rwlock/pfs_example/X" STATE "R" TO "rwlock/pfs_example/T" OP "W"
ARC FROM "rwlock/pfs_example/X" STATE "R" TO "rwlock/pfs_example/Y" OP "W"
ARC FROM "rwlock/pfs_example/X" STATE "R" TO "rwlock/pfs_example/Z" OP "R"
ARC FROM "rwlock/pfs_example/Y" STATE "W" TO "rwlock/pfs_example/T" OP "W"
ARC FROM "rwlock/pfs_example/Y" STATE "W" TO "rwlock/pfs_example/Z" OP "R"
ARC FROM "rwlock/pfs_example/Z" STATE "R" TO "rwlock/pfs_example/T" OP "W"
ARC FROM "rwlock/rewriter/LOCK_plugin_rewriter_table_" STATE "W" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "rwlock/rewriter/LOCK_plugin_rewriter_table_" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/rewriter/LOCK_plugin_rewriter_table_" STATE "W" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "rwlock/rewriter/LOCK_plugin_rewriter_table_" STATE "W" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_applier_module_suspend"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_manager_update_lock"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_primary_election_running_flag"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_recovery"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_synchronized_queue"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/sql/Relay_log_info::info_thd_lock"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/sql/Source_info::info_thd_lock"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "mutex/sql/Worker_info::jobs_lock"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "R" TO "rwlock/group_rpl/RWLOCK_channel_observation_list" OP "R"
ARC FROM "rwlock/sql/Binlog_relay_IO_delegate::lock" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/Binlog_storage_delegate::lock" STATE "R" TO "cond/semisync/COND_binlog_send_"
ARC FROM "rwlock/sql/Binlog_storage_delegate::lock" STATE "R" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/sql/Binlog_storage_delegate::lock" STATE "R" TO "mutex/semisync/LOCK_binlog_"
ARC FROM "rwlock/sql/Binlog_storage_delegate::lock" STATE "R" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/sql/Binlog_storage_delegate::lock" STATE "R" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/Binlog_storage_delegate::lock" STATE "R" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "rwlock/sql/Binlog_storage_delegate::lock" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/Binlog_storage_delegate::lock" STATE "W" TO "rwlock/sql/Binlog_relay_IO_delegate::lock" OP "W"
ARC FROM "rwlock/sql/Binlog_storage_delegate::lock" STATE "W" TO "rwlock/sql/Binlog_transmit_delegate::lock" OP "W"
ARC FROM "rwlock/sql/Binlog_storage_delegate::lock" STATE "W" TO "rwlock/sql/Server_state_delegate::lock" OP "W"
ARC FROM "rwlock/sql/Binlog_transmit_delegate::lock" STATE "R" TO "cond/semisync/Ack_receiver::m_cond"
ARC FROM "rwlock/sql/Binlog_transmit_delegate::lock" STATE "R" TO "mutex/semisync/Ack_receiver::m_mutex"
ARC FROM "rwlock/sql/Binlog_transmit_delegate::lock" STATE "R" TO "mutex/semisync/LOCK_binlog_"
ARC FROM "rwlock/sql/Binlog_transmit_delegate::lock" STATE "R" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/Binlog_transmit_delegate::lock" STATE "R" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/sql/Binlog_transmit_delegate::lock" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/Binlog_transmit_delegate::lock" STATE "W" TO "rwlock/sql/Binlog_relay_IO_delegate::lock" OP "W"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "cond/sql/Relay_log_info::start_cond"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "cond/sql/Relay_log_info::stop_cond"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "cond/sql/Source_info::start_cond"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "cond/sql/Source_info::stop_cond"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "cond/sql/Source_IO_monitor::run_cond"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/autoinc_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/dict_table_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/fil_system_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/flush_list_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/lock_wait_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/log_files_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/sync_array_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/trx_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/key_mta_gaq_LOCK"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/key_mta_temp_table_LOCK"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/LOCK_open"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/LOCK_replica_list"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/LOCK_sql_replica_skip_counter"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/LOCK_status"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/LOCK_table_cache"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_index"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Relay_log_info::data_lock"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Relay_log_info::info_thd_lock"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Relay_log_info::sleep_lock"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Replica_reporting_capability::err_lock"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Source_info::data_lock"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Source_info::run_lock"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Source_info::sleep_lock"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Source_IO_monitor::run_lock"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "mutex/sql/Worker_info::jobs_lock"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "rwlock/group_rpl/RWLOCK_channel_observation_list" OP "R"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "rwlock/sql/Binlog_relay_IO_delegate::lock" OP "R"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "rwlock/sql/rpl_filter_lock" OP "R"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "rwlock/sql/Trans_delegate::lock" OP "R"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "rwlock/sql/channel_lock" STATE "W" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "R" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "R" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "R" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "R" TO "mutex/sql/Gtid_state"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "R" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "R" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "R" TO "rwlock/sql/gtid_commit_rollback" RECURSIVE OP "R"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/dict_table_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/fil_system_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/flush_list_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/lock_wait_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/log_checkpointer_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/log_sn_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/sync_array_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/trx_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/sql/Gtid_state"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/sql/LOCK_compress_gtid_table"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/sql/LOCK_open"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/sql/LOCK_reset_gtid_table"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/sql/LOCK_table_cache"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "sxlock/innodb/log_sn_lock" OP "X"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "rwlock/sql/gtid_commit_rollback" STATE "W" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "rwlock/sql/gtid_retrieved" STATE "R" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/sql/gtid_retrieved" STATE "R" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/sql/gtid_retrieved" STATE "R" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "rwlock/sql/gtid_retrieved" STATE "W" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "rwlock/sql/gtid_retrieved" STATE "W" TO "mutex/sql/Gtid_set::gtid_executed::free_intervals_mutex"
ARC FROM "rwlock/sql/gtid_retrieved" STATE "W" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "R" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "R" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "R" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "R" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "R" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "R" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "R" TO "rwlock/sql/LOCK_system_variables_hash" RECURSIVE OP "R"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "R" TO "rwlock/validate_pwd/LOCK_dict_file" OP "W"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/autoinc_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/dict_table_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/fil_system_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/flush_list_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/log_limits_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/trx_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/sql/LOCK_open"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/sql/LOCK_table_cache"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "rwlock/sql/LOCK_system_variables_hash" STATE "W" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "cond/sql/COND_thr_lock"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/csv/tina"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/csv/TINA_SHARE::mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/innodb/fil_system_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/innodb/log_files_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/innodb/sync_array_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/innodb/trx_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/myisam/MYISAM_SHARE::intern_lock"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/mysys/KEY_CACHE::cache_lock"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/mysys/THR_LOCK::mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/sql/LOCK_open"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/sql/LOCK_table_cache"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/sql/LOG::LOCK_log"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "R" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/csv/tina"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/csv/TINA_SHARE::mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/innodb/fil_system_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/innodb/log_files_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/innodb/sync_array_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/innodb/trx_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/myisam/MYISAM_SHARE::intern_lock"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/mysys/KEY_CACHE::cache_lock"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/mysys/THR_LOCK::mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/sql/LOCK_open"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/sql/LOCK_table_cache"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "rwlock/sql/LOGGER::LOCK_logger" STATE "W" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "rwlock/sql/Server_state_delegate::lock" STATE "R" TO "cond/group_rpl/COND_delayed_init_run"
ARC FROM "rwlock/sql/Server_state_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_delayed_init_run"
ARC FROM "rwlock/sql/Server_state_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_delayed_init_server_ready"
ARC FROM "rwlock/sql/Server_state_delegate::lock" STATE "R" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/Server_state_delegate::lock" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/Server_state_delegate::lock" STATE "W" TO "rwlock/sql/Binlog_relay_IO_delegate::lock" OP "W"
ARC FROM "rwlock/sql/Server_state_delegate::lock" STATE "W" TO "rwlock/sql/Binlog_transmit_delegate::lock" OP "W"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/autoinc_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/dict_table_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/fil_system_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/flush_list_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/lock_wait_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/log_files_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/sync_array_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/trx_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/sql/LOCK_open"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/sql/LOCK_table_cache"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "rwlock/sql/THR_LOCK_servers" STATE "W" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "cond/group_rpl/COND_count_down_latch"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "cond/group_rpl/COND_primary_promotion_policy"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "cond/sql/Gtid_state"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "cond/sql/Relay_log_info::start_cond"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "cond/sql/Relay_log_info::stop_cond"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "cond/sql/Source_info::stop_cond"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_count_down_latch"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_group_member_info_update_lock"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_pipeline_stats_transactions_waiting_apply"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_primary_promotion_policy"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_server_ongoing_transaction_handler"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_wait_ticket"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/group_rpl/LOCK_write_lock_protection"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/autoinc_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/clone_sys_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/dict_table_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/fil_system_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/flush_list_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/log_files_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/parser_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/recalc_pool_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/sync_array_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/trx_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/semisync/LOCK_binlog_"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/Cost_constant_cache::LOCK_cost_const"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/Gtid_state"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/key_mta_temp_table_LOCK"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_connection_count"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_open"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_replica_list"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_replica_trans_dep_tracker"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_sql_replica_skip_counter"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_status"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_table_cache"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_thd_list"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_thd_remove"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_thread_ids"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/LOG::LOCK_log"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_commit"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_commit_queue"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_done"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_flush_queue"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_log"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_sync_queue"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_xids"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_index"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/Relay_log_info::data_lock"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/Relay_log_info::info_thd_lock"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/Relay_log_info::log_space_lock"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/Relay_log_info::run_lock"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/Replica_reporting_capability::err_lock"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/Source_info::data_lock"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/Source_info::info_thd_lock"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/Source_info::rotate_lock"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/Source_info::run_lock"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/THD::LOCK_thd_protocol"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/THD::LOCK_thd_sysvar"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "mutex/sql/Worker_info::jobs_lock"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/group_rpl/RWLOCK_gcs_operations" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/group_rpl/RWLOCK_plugin_running" OP "TRY R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/group_rpl/RWLOCK_plugin_stop" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_info_members_that_must_prepare_the_transaction" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_map" OP "W"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_prepared_transactions_on_my_applier" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_consistency_manager_prepared_transactions_on_my_applier" OP "W"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/group_rpl/RWLOCK_transaction_observation_list" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/session/LOCK_srv_session_collection" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/session/LOCK_srv_session_collection" OP "W"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/sql/Binlog_relay_IO_delegate::lock" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/sql/channel_lock" OP "W"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/sql/gtid_commit_rollback" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/sql/gtid_commit_rollback" OP "W"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/sql/gtid_retrieved" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/sql/gtid_retrieved" OP "W"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/sql/LOGGER::LOCK_logger" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/sql/rpl_filter_lock" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/sql/THR_LOCK_udf" OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "rwlock/sql/Trans_delegate::lock" RECURSIVE OP "R"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/dict_operation_lock" OP "X"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/dict_table_stats" OP "S"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/index_tree_rw_lock" OP "X"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "R" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "W" TO "mutex/sql/LOCK_plugin"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "W" TO "rwlock/sql/Binlog_relay_IO_delegate::lock" OP "W"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "W" TO "rwlock/sql/Binlog_storage_delegate::lock" OP "W"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "W" TO "rwlock/sql/Binlog_transmit_delegate::lock" OP "W"
ARC FROM "rwlock/sql/Trans_delegate::lock" STATE "W" TO "rwlock/sql/Server_state_delegate::lock" OP "W"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "S" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "S" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "S" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "S" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "S" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "S" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "S" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "S" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "S" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "S" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "X" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "X" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "X" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/btr_search_latch" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/dict_table_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/parser_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/trx_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "S" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "cond/sql/MDL_context::COND_wait_status"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/autoinc_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/clone_snapshot_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/clone_sys_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/dict_foreign_err_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/dict_table_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/fts_delete_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/ibuf_bitmap_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/ibuf_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/lock_free_hash_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/lock_wait_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/log_files_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/log_flusher_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/log_limits_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/log_writer_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/master_key_id_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/page_zip_stat_per_index_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/parser_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/recalc_pool_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/srv_sys_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/trx_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/innodb/zip_pad_mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/sql/LOCK_open"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/sql/LOCK_plugin"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/sql/LOCK_table_cache"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "R"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "prlock/sql/MDL_context::LOCK_waiting_for" OP "W"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "prlock/sql/MDL_lock::rwlock" OP "R"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "prlock/sql/MDL_lock::rwlock" OP "W"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "rwlock/keyring/LOCK_keyring" OP "W"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "rwlock/sql/LOCK_system_variables_hash" OP "R"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/fts_cache_init_rw_lock" OP "X"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" OP "X"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "sxlock/innodb/dict_operation_lock" STATE "X" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/ibuf_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "sxlock/innodb/dict_table_stats" STATE "X" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/ibuf_bitmap_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/ibuf_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/ibuf_pessimistic_insert_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/lock_free_hash_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/master_key_id_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/page_zip_stat_per_index_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/rtr_active_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/rtr_path_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/rtr_ssn_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/trx_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/innodb/zip_pad_mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/sql/LOCK_plugin"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "mutex/sql/THD::LOCK_thd_protocol"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "rwlock/keyring/LOCK_keyring" OP "W"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "sxlock/innodb/fil_space_latch" RECURSIVE OP "X"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "sxlock/innodb/fil_space_latch" STATE "X" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "sxlock/innodb/fts_cache_init_rw_lock" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/autoinc_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/dict_sys_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/dict_table_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/fts_delete_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/fts_doc_id_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/innobase_share_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/log_files_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/parser_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/purge_sys_pq_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/trx_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/trx_pool_manager_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/trx_pool_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/trx_sys_serialisation_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/sql/LOCK_open"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/sql/LOCK_plugin"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/sql/LOCK_table_cache"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/dict_table_stats" OP "X"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/fts_cache_init_rw_lock" OP "X"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "sxlock/innodb/fts_cache_rw_lock" STATE "X" TO "sxlock/innodb/undo_spaces_lock" OP "S"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "S" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "S" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "S" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "X" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "X" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "X" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "X" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "X" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "X" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "X" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "X" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "X" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/hash_table_locks" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/dict_persist_dirty_tables_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/ibuf_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/ibuf_pessimistic_insert_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/log_limits_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/parallel_read_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/rtr_active_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/rtr_path_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/rtr_ssn_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/trx_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/innodb/zip_pad_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "mutex/sql/THD::LOCK_thd_protocol"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/index_tree_rw_lock" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/index_tree_rw_lock" RECURSIVE OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "S" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/autoinc_persisted_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/ibuf_bitmap_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/ibuf_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/page_zip_stat_per_index_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/rtr_active_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/rtr_path_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/rtr_ssn_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/temp_space_rseg_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/trx_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/trx_undo_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/innodb/zip_pad_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/mysys/THR_LOCK_open"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "mutex/sql/THD::LOCK_thd_protocol"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/index_tree_rw_lock" RECURSIVE OP "SX"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/index_tree_rw_lock" RECURSIVE OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "SX" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/ibuf_bitmap_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/log_limits_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/rtr_active_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/rtr_path_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/rtr_ssn_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/trx_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "mutex/innodb/zip_pad_mutex"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" OP "SX"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/index_tree_rw_lock" RECURSIVE OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "sxlock/innodb/index_tree_rw_lock" STATE "X" TO "sxlock/innodb/trx_purge_latch" OP "S"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/innodb/lock_sys_table_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/innodb/trx_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/sql/MDL_wait::LOCK_wait_status"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "mutex/sql/THD::LOCK_current_cond"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "S" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/innodb/trx_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/sql/LOCK_global_system_variables"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/sql/LOCK_plugin"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/sql/LOCK_replica_list"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/sql/LOCK_sql_rand"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/sql/LOCK_status"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/sql/LOCK_transaction_cache"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/sql/THD::LOCK_query_plan"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/sql/THD::LOCK_thd_data"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/lock_sys_global_rw_lock" STATE "X" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "sxlock/innodb/log_sn_lock" STATE "S" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "sxlock/innodb/log_sn_lock" STATE "S" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/log_sn_lock" STATE "S" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/log_sn_lock" STATE "S" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/log_sn_lock" STATE "S" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "sxlock/innodb/log_sn_lock" STATE "X" TO "cond/sql/DEBUG_SYNC::cond"
ARC FROM "sxlock/innodb/log_sn_lock" STATE "X" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/log_sn_lock" STATE "X" TO "mutex/innodb/log_checkpointer_mutex"
ARC FROM "sxlock/innodb/log_sn_lock" STATE "X" TO "mutex/innodb/log_writer_mutex"
ARC FROM "sxlock/innodb/log_sn_lock" STATE "X" TO "mutex/innodb/page_sys_arch_oper_mutex"
ARC FROM "sxlock/innodb/log_sn_lock" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/log_sn_lock" STATE "X" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/master_key_id_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/temp_space_rseg_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "mutex/sql/LOCK_plugin"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "S" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "mutex/innodb/log_limits_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "mutex/sql/LOCK_plugin"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/rsegs_lock" STATE "X" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "sxlock/innodb/trx_i_s_cache_lock" STATE "S" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/trx_i_s_cache_lock" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/trx_i_s_cache_lock" STATE "X" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/trx_i_s_cache_lock" STATE "X" TO "mutex/innodb/trx_mutex"
ARC FROM "sxlock/innodb/trx_i_s_cache_lock" STATE "X" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "sxlock/innodb/trx_i_s_cache_lock" STATE "X" TO "mutex/sql/THD::LOCK_thd_query"
ARC FROM "sxlock/innodb/trx_i_s_cache_lock" STATE "X" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "X"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/lock_sys_page_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/trx_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "sxlock/innodb/lock_sys_global_rw_lock" OP "S"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "S" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "X" TO "mutex/innodb/srv_sys_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "X" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "X" TO "mutex/innodb/trx_sys_mutex"
ARC FROM "sxlock/innodb/trx_purge_latch" STATE "X" TO "mutex/innodb/trx_sys_shard_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/buf_pool_chunks_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/buf_pool_flush_state_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/buf_pool_free_list_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/buf_pool_LRU_list_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/buf_pool_zip_free_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/buf_pool_zip_hash_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/buf_pool_zip_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/clone_snapshot_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/clone_sys_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/flush_list_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/master_key_id_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/srv_sys_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/sync_array_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/innodb/undo_space_rseg_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/sql/DEBUG_SYNC::mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/sql/LOCK_keyring_operations"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "mutex/sql/LOCK_plugin"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "rwlock/keyring/LOCK_keyring" OP "R"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "sxlock/innodb/btr_search_latch" OP "S"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "sxlock/innodb/btr_search_latch" OP "X"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "sxlock/innodb/fil_space_latch" OP "X"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "sxlock/innodb/hash_table_locks" OP "S"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "sxlock/innodb/hash_table_locks" OP "X"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "sxlock/innodb/log_sn_lock" OP "S"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "sxlock/innodb/rsegs_lock" OP "S"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "S" TO "sxlock/innodb/rsegs_lock" OP "X"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "X" TO "mutex/innodb/fil_system_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "X" TO "mutex/innodb/rw_lock_debug_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "X" TO "mutex/innodb/rw_lock_list_mutex"
ARC FROM "sxlock/innodb/undo_spaces_lock" STATE "X" TO "sxlock/innodb/rsegs_lock" OP "S"
BIND "cond/conn_delay/connection_delay_wait_condition" TO "mutex/conn_delay/connection_delay_mutex"
BIND "cond/group_rpl/COND_applier_module_run" TO "mutex/group_rpl/LOCK_applier_module_run"
BIND "cond/group_rpl/COND_applier_module_suspend" TO "mutex/group_rpl/LOCK_applier_module_suspend"
BIND "cond/group_rpl/COND_applier_module_wait" TO "mutex/group_rpl/LOCK_applier_module_suspend"
BIND "cond/group_rpl/COND_certifier_broadcast_dispatcher_run" TO "mutex/group_rpl/LOCK_certifier_broadcast_dispatcher_run"
BIND "cond/group_rpl/COND_certifier_broadcast_run" TO "mutex/group_rpl/LOCK_certifier_broadcast_run"
BIND "cond/group_rpl/COND_clone_handler_run" TO "mutex/group_rpl/LOCK_clone_handler_run"
BIND "cond/group_rpl/COND_count_down_latch" TO "mutex/group_rpl/LOCK_count_down_latch"
BIND "cond/group_rpl/COND_delayed_init_run" TO "mutex/group_rpl/LOCK_delayed_init_run"
BIND "cond/group_rpl/COND_delayed_init_server_ready" TO "mutex/group_rpl/LOCK_delayed_init_server_ready"
BIND "cond/group_rpl/COND_group_action_coordinator_process" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_process"
BIND "cond/group_rpl/COND_group_action_coordinator_thread_end" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread_end" FLAGS UNFAIR
BIND "cond/group_rpl/COND_group_action_coordinator_thread" TO "mutex/group_rpl/key_GR_LOCK_group_action_coordinator_thread"
BIND "cond/group_rpl/COND_message_service_run" TO "mutex/group_rpl/LOCK_message_service_run"
BIND "cond/group_rpl/COND_multi_primary_action_notification" TO "mutex/group_rpl/LOCK_multi_primary_action_notification"
BIND "cond/group_rpl/COND_mysql_thread_dispatcher_run" TO "mutex/group_rpl/LOCK_mysql_thread_dispatcher_run"
BIND "cond/group_rpl/COND_mysql_thread_handler_dispatcher_run" TO "mutex/group_rpl/LOCK_mysql_thread_handler_dispatcher_run"
BIND "cond/group_rpl/COND_mysql_thread_handler_run" TO "mutex/group_rpl/LOCK_mysql_handler_thread_run"
BIND "cond/group_rpl/COND_mysql_thread_run" TO "mutex/group_rpl/LOCK_mysql_thread_run"
BIND "cond/group_rpl/COND_pipeline_continuation" TO "mutex/group_rpl/LOCK_pipeline_continuation" FLAGS UNFAIR
BIND "cond/group_rpl/COND_primary_election_action_notification" TO "mutex/group_rpl/LOCK_primary_election_action_notification"
BIND "cond/group_rpl/COND_primary_election_primary_process_run" TO "mutex/group_rpl/LOCK_primary_election_primary_process_run"
BIND "cond/group_rpl/COND_primary_election_secondary_process_run" TO "mutex/group_rpl/LOCK_primary_election_secondary_process_run"
BIND "cond/group_rpl/COND_primary_election_validation_notification" TO "mutex/group_rpl/LOCK_primary_election_validation_notification"
BIND "cond/group_rpl/COND_primary_promotion_policy" TO "mutex/group_rpl/LOCK_primary_promotion_policy"
BIND "cond/group_rpl/COND_recovery_module_run" TO "mutex/group_rpl/LOCK_recovery_module_run"
BIND "cond/group_rpl/COND_recovery" TO "mutex/group_rpl/LOCK_recovery"
BIND "cond/group_rpl/COND_session_thread_method_exec" TO "mutex/group_rpl/LOCK_session_thread_method_exec"
BIND "cond/group_rpl/COND_session_thread_run" TO "mutex/group_rpl/LOCK_session_thread_run"
BIND "cond/group_rpl/COND_synchronized_queue" TO "mutex/group_rpl/LOCK_synchronized_queue"
BIND "cond/group_rpl/COND_transaction_monitoring_wait" TO "mutex/group_rpl/LOCK_transaction_monitoring"
BIND "cond/group_rpl/COND_view_modification_wait" TO "mutex/group_rpl/LOCK_view_modification_wait"
BIND "cond/group_rpl/COND_write_lock_protection" TO "mutex/group_rpl/LOCK_write_lock_protection"
BIND "cond/group_rpl/GCS_Gcs_async_buffer::m_wait_for_events_cond" TO "mutex/group_rpl/GCS_Gcs_async_buffer::m_free_buffer_mutex" FLAGS UNFAIR
BIND "cond/group_rpl/GCS_Gcs_suspicions_manager::m_suspicions_cond" TO "mutex/group_rpl/GCS_Gcs_suspicions_manager::m_suspicions_mutex"
BIND "cond/group_rpl/GCS_Gcs_xcom_engine::m_wait_for_notification_cond" TO "mutex/group_rpl/GCS_Gcs_xcom_engine::m_wait_for_notification_mutex"
BIND "cond/group_rpl/GCS_Gcs_xcom_interface::m_wait_for_ssl_init_cond" TO "mutex/group_rpl/GCS_Gcs_xcom_interface::m_wait_for_ssl_init_mutex"
BIND "cond/group_rpl/GCS_Gcs_xcom_proxy_impl::m_cond_xcom_comms_status" TO "mutex/group_rpl/GCS_Gcs_xcom_proxy_impl::m_lock_xcom_comms_status"
BIND "cond/group_rpl/GCS_Gcs_xcom_proxy_impl::m_cond_xcom_exit" TO "mutex/group_rpl/GCS_Gcs_xcom_proxy_impl::m_lock_xcom_exit"
BIND "cond/group_rpl/GCS_Gcs_xcom_proxy_impl::m_cond_xcom_ready" TO "mutex/group_rpl/GCS_Gcs_xcom_proxy_impl::m_lock_xcom_ready"
BIND "cond/innodb/commit_cond" TO "mutex/innodb/commit_cond_mutex"
BIND "cond/innodb/resume_encryption_cond" TO "mutex/innodb/resume_encryption_cond_mutex"
BIND "cond/myisam/keycache_thread_var::suspend" TO "mutex/mysys/KEY_CACHE::cache_lock"
BIND "cond/mysqlx/broker_context_sync" TO "mutex/mysqlx/broker_context_sync"
BIND "cond/mysqlx/listener_tcp_sync" TO "mutex/mysqlx/listener_tcp_sync"
BIND "cond/mysqlx/listener_unix_socket_sync" TO "mutex/mysqlx/listener_unix_socket_sync"
BIND "cond/mysqlx/scheduler_dynamic_thread_exit" TO "mutex/mysqlx/scheduler_dynamic_thread_exit"
BIND "cond/mysqlx/scheduler_dynamic_worker_pending" TO "mutex/mysqlx/scheduler_dynamic_worker_pending"
BIND "cond/mysqlx/server_state_sync" TO "mutex/mysqlx/server_state_sync"
BIND "cond/mysqlx/socket_acceptors_sync" TO "mutex/mysqlx/socket_acceptors_sync"
BIND "cond/mysys/THR_COND_threads" TO "mutex/mysys/THR_LOCK_threads"
BIND "cond/semisync/Ack_receiver::m_cond" TO "mutex/semisync/Ack_receiver::m_mutex"
BIND "cond/semisync/COND_binlog_send_" TO "mutex/semisync/LOCK_binlog_"
BIND "cond/sql/COND_compress_gtid_table" TO "mutex/sql/LOCK_compress_gtid_table"
BIND "cond/sql/COND_connection_count" TO "mutex/sql/LOCK_connection_count"
BIND "cond/sql/COND_flush_thread_cache" TO "mutex/sql/LOCK_thread_cache"
BIND "cond/sql/COND_manager" TO "mutex/sql/LOCK_manager"
BIND "cond/sql/COND_open" TO "mutex/sql/LOCK_open"
BIND "cond/sql/COND_queue_state" TO "mutex/sql/LOCK_event_queue"
BIND "cond/sql/COND_server_started" TO "mutex/sql/LOCK_server_started"
BIND "cond/sql/COND_socket_listener_active" TO "mutex/sql/LOCK_socket_listener_active"
BIND "cond/sql/COND_start_admin_thread" TO "mutex/sql/LOCK_start_admin_thread"
BIND "cond/sql/COND_start_signal_handler" TO "mutex/sql/LOCK_start_signal_handler"
BIND "cond/sql/COND_thd_list" TO "mutex/sql/LOCK_thd_list"
BIND "cond/sql/COND_thread_cache" TO "mutex/sql/LOCK_thread_cache"
BIND "cond/sql/COND_thr_lock" TO "mutex/mysys/THR_LOCK::mutex"
BIND "cond/sql/DEBUG_SYNC::cond" TO "mutex/sql/DEBUG_SYNC::mutex"
BIND "cond/sql/Event_scheduler::COND_state" TO "mutex/sql/Event_scheduler::LOCK_scheduler_state"
BIND "cond/sql/Gtid_state" TO "mutex/sql/Gtid_state"
BIND "cond/sql/Item_func_sleep::cond" TO "mutex/sql/LOCK_item_func_sleep"
BIND "cond/sql/MDL_context::COND_wait_status" TO "mutex/sql/MDL_wait::LOCK_wait_status"
BIND "cond/sql/MYSQL_BIN_LOG::COND_done" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_done"
BIND "cond/sql/MYSQL_BIN_LOG::COND_flush_queue" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_flush_queue"
BIND "cond/sql/MYSQL_BIN_LOG::prep_xids_cond" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_xids"
BIND "cond/sql/MYSQL_BIN_LOG::update_cond" TO "mutex/sql/MYSQL_BIN_LOG::LOCK_binlog_end_pos"
BIND "cond/sql/MYSQL_RELAY_LOG::update_cond" TO "mutex/sql/MYSQL_RELAY_LOG::LOCK_log_end_pos"
BIND "cond/sql/Relay_log_info::data_cond" TO "mutex/sql/Relay_log_info::data_lock"
BIND "cond/sql/Relay_log_info::log_space_cond" TO "mutex/sql/Relay_log_info::log_space_lock"
BIND "cond/sql/Relay_log_info::mta_gaq_cond" TO "mutex/sql/key_mta_gaq_LOCK"
BIND "cond/sql/Relay_log_info::pending_jobs_cond" TO "mutex/sql/Relay_log_info::pending_jobs_lock"
BIND "cond/sql/Relay_log_info::replica_worker_hash_cond" TO "mutex/sql/Relay_log_info::replica_worker_hash_lock"
BIND "cond/sql/Relay_log_info::sleep_cond" TO "mutex/sql/Relay_log_info::sleep_lock"
BIND "cond/sql/Relay_log_info::start_cond" TO "mutex/sql/Relay_log_info::run_lock"
BIND "cond/sql/Relay_log_info::stop_cond" TO "mutex/sql/Relay_log_info::run_lock"
BIND "cond/sql/Source_info::rotate_cond" TO "mutex/sql/Source_info::rotate_lock"
BIND "cond/sql/Source_info::sleep_cond" TO "mutex/sql/Source_info::sleep_lock"
BIND "cond/sql/Source_info::start_cond" TO "mutex/sql/Source_info::run_lock" FLAGS UNFAIR
BIND "cond/sql/Source_info::stop_cond" TO "mutex/sql/Source_info::run_lock"
BIND "cond/sql/Source_IO_monitor::run_cond" TO "mutex/sql/Source_IO_monitor::run_lock"
BIND "cond/sql/THD::COND_group_replication_connection_cond_var" TO "mutex/sql/LOCK_group_replication_connection_mutex"
BIND "cond/sql/Worker_info::jobs_cond" TO "mutex/sql/Worker_info::jobs_lock"
