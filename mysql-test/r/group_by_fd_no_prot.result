set optimizer_trace_max_mem_size=1048576;
set end_markers_in_json=on;
set optimizer_trace="enabled=on";
SET @old_sql_mode = @@sql_mode;
SET @@sql_mode='ONLY_FULL_GROUP_BY';
#
# Bug#16021396 ONLY_FULL_GROUP_BY REJECTS VALID QUERY USING VIEW
#
create table t1(a int, b int, c int) engine=InnoDB;
create algorithm=merge view v1 as select t1.a*2 as a, t1.b*2 as b, t1.c*2 as c from t1;
show create view v1;
View	Create View	character_set_client	collation_connection
v1	CREATE ALGORITHM=MERGE DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v1` AS select (`t1`.`a` * 2) AS `a`,(`t1`.`b` * 2) AS `b`,(`t1`.`c` * 2) AS `c` from `t1`	utf8mb4	utf8mb4_0900_ai_ci
select sin(b) as z from t1 group by sin(b);
z
select sin(b) as z from v1 group by sin(b);
z
select sin(b) as z from t1 group by b;
z
select sin(b) as z from v1 group by b;
z
select sin(b) as z from v1 group by z;
z
drop view v1;
create algorithm=temptable view v1 as select t1.a*2 as a, t1.b*2 as b, t1.c*2 as c from t1;
show create view v1;
View	Create View	character_set_client	collation_connection
v1	CREATE ALGORITHM=TEMPTABLE DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v1` AS select (`t1`.`a` * 2) AS `a`,(`t1`.`b` * 2) AS `b`,(`t1`.`c` * 2) AS `c` from `t1`	utf8mb4	utf8mb4_0900_ai_ci
select sin(b) as z from t1 group by sin(b);
z
select sin(b) as z from v1 group by sin(b);
z
select sin(b) as z from t1 group by b;
z
select sin(b) as z from v1 group by b;
z
select sin(b) as z from v1 group by z;
z
drop view v1;
drop table t1;
# From testcase of Bug#16903135:
CREATE TABLE group_by_test2 (
id int unsigned primary key,
cat int unsigned not null,
name varchar(10),
num int unsigned
);
INSERT INTO group_by_test2 (id,cat,name,num) VALUES
(1,10,'foo',2),
(2,11,'foo',1),
(3,22,'bar',3),
(4,23,'bar',7),
(5,34,'test',7);
SELECT
cat,
name,
SUM(num)
FROM
group_by_test2
GROUP BY
cat;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.group_by_test2.name' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
ALTER TABLE group_by_test2 ADD UNIQUE INDEX (cat);
SELECT
cat,
name,
SUM(num)
FROM
group_by_test2
GROUP BY
cat;
cat	name	SUM(num)
10	foo	2
11	foo	1
22	bar	3
23	bar	7
34	test	7
SELECT
cat,
name,
SUM(num)
FROM
group_by_test2
GROUP BY
cat WITH ROLLUP;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.group_by_test2.name' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
# Expressions of group columns are ok:
SELECT
cat,
length(cat),
SUM(num)
FROM
group_by_test2
GROUP BY
cat
WITH ROLLUP;
cat	length(cat)	SUM(num)
10	2	2
11	2	1
22	2	3
23	2	7
34	2	7
NULL	NULL	20
DROP TABLE group_by_test2;
# Test from Bug #18993257 SELECT AGGR + NON-AGGR FROM JOIN WITH VIEW IS NOT REJECTED BY ONLY_FULL_GROUP_BY
CREATE TABLE t1 (
col_int_key INT,
col_varchar_key VARCHAR(1)
);
CREATE TABLE t2 (
pk INTEGER,
col_int_key INTEGER
);
CREATE VIEW view_b AS SELECT * FROM t2;
SELECT MIN( alias2.col_int_key ),
alias2.col_int_key
FROM t1 AS alias1, t2 AS alias2
WHERE alias1.col_int_key IS NULL;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.alias2.col_int_key'; this is incompatible with sql_mode=only_full_group_by
# Same with view:
SELECT MIN( alias2.col_int_key ),
alias2.col_int_key
FROM t1 AS alias1, view_b AS alias2
WHERE alias1.col_int_key IS NULL;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.alias2.col_int_key'; this is incompatible with sql_mode=only_full_group_by
DROP TABLE t1, t2;
DROP VIEW view_b;
#
# WL#2489; Recognizing some functional dependencies
#
create table t1(
a int,
b int not null,
c int not null,
d int,
unique key(b,c),
unique key(b,d)
);
select sin(a) as z from t1 group by b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select sin(a) as z from t1 group by d,b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
# {b,c} UNIQUE NOT NULL => {c,b}->a
select sin(a) as z from t1 group by c,b;
z
select sin(a+b*c) as z from t1 group by c,b;
z
# In PS mode, we see nothing below, because only_full_group_by
# checks are done at PREPARE, whereas trace below is from EXECUTE.
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.t1.c",
                "test.t1.b"
              ] /* columns */
            } 
# With outer references:
select (select sin(a)) as z from t1 group by d,b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select (select sin(a)) as z from t1 group by c,b;
z
# If key columns are in function, functional dependency disappears
select sin(a) as z from t1 group by c*2,b*2;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
# FDs are recognized, like in SQL standard:
# b=2 => c->{b,c} => c->a as (b,c) is unique not null.
select sin(a) as z from t1 where b=2 group by c;
z
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.t1.c",
                "test.t1.b"
              ] /* columns */
            } 
# t2.a=t1.a => {t1.b,t1.c}->t2.a
select sin(t2.a) as z from t1, t1 as t2
where t2.a=t1.a group by t1.b,t1.c;
z
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.t1.b",
                "test.t1.c",
                "test.t2.a"
              ] /* columns */
            } 
# t2.b=t1.b and t2.c=t1.c => {t1.b,t1.c}->{all cols of t2}
select sin(t2.a) as z from t1, t1 as t2
where t2.b=t1.b and t2.c=t1.c group by t1.b,t1.c;
z
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0,
                1
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.t1.b",
                "test.t1.c",
                "test.t2.b",
                "test.t2.c"
              ] /* columns */
            } 
# t2.b=t1.b and t2.c=t1.c => {t1.b,t1.c}->{all cols of t2}
# Moreover, {t1.b,t1.c}->{t1.d}.
# So t3.b=t2.b and t3.c=t1.d => {t1.b,t1.c}->{all cols of t3}.
select t3.a from t1, t1 as t2, t1 as t3
where
t3.b=t2.b and t3.c=t1.d and
t2.b=t1.b and t2.c=t1.c
group by t1.b,t1.c;
a
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0,
                1,
                2
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.t1.b",
                "test.t1.c",
                "test.t3.c",
                "test.t2.b",
                "test.t2.c",
                "test.t3.b"
              ] /* columns */
            } 
# 3 tables:
# {t1.b,t1.c}->{t1.*}->{t2.b,t2.c}->{t2.*}->{t3.pk}->{t3.b}
create table t3(pk int primary key, b int);
select t3.b from  t1,t1 as t2,t3
where t3.pk=t2.d and t2.b=t1.b and t2.c=t1.a
group by t1.b,t1.c;
b
drop table t3;
# With subq
select (select t1.b from t1
where t2.b=t1.b
group by t1.a) from t1 as t2;
(select t1.b from t1
where t2.b=t1.b
group by t1.a)
# Outer join.
create table t2 like t1;
delete from t1;
insert into t1 (a,b) values(1,10),(2,20);
Warnings:
Warning	1364	Field 'c' doesn't have a default value
insert into t2 (a,b) values(1,-10);
Warnings:
Warning	1364	Field 'c' doesn't have a default value
# In result, t2.a is NULL for both rows, values of t1.a are 1 and 2
select t1.a,t2.a from t1 left join t2 on t2.a=t1.a and t2.b=t1.b;
a	a
1	NULL
2	NULL
# So this query would choose one arbitrary value of t1.a - wrong:
select t1.a from t1 left join t2 on t2.a=t1.a and t2.b=t1.b group by t2.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
# Also problem for FD with constant:
select t1.a,t2.a from t1 left join t2 on 42=t1.a and t2.b=t1.b;
a	a
1	NULL
2	NULL
select t1.a from t1 left join t2 on 42=t1.a and t2.b=t1.b group by t2.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select t1.a from t1 left join t2 on t2.b=t1.a group by t2.b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select t1.a from t1 left join t2 on 42=t1.a group by t2.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select t1.c from t1 left join t2 on t1.a=t1.c group by t1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select t1.b from t1 left join t2 on t1.c=t1.b and t1.a=t1.c group by t1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select t1.a,t2.c from t1 left join t2 on t1.a=t2.c
and cos(t2.c+t2.b)>0.5 and sin(t1.a+t2.d)<0.9 group by t1.a;
a	c
1	NULL
2	NULL
# with keys:
select t1.a,t2.d from t1 left join t2 on t1.a=t2.c and t1.d=t2.b
and cos(t2.c+t2.b)>0.5 and sin(t1.a+t2.d)<0.9 group by t1.a,t1.d;
a	d
1	NULL
2	NULL
# with non-determinism:
select t1.a,t2.c from t1 left join t2 on t1.a=t2.c
and cos(t2.c+rand())>0.5 group by t1.a;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t2.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select t1.a, ANY_VALUE(t2.c) from t1 left join t2 on t1.a=t2.c
and cos(t2.c+rand())>0.5 group by t1.a;
a	ANY_VALUE(t2.c)
1	NULL
2	NULL
# with parameter:
prepare s from 'select t1.a,t2.c from t1 left join t2 on t1.a=t2.c
and cos(t2.c+ ? )>0.5 group by t1.a';
execute s using @dummy;
a	c
1	NULL
2	NULL
# No OR
select t1.a,t2.c from t1 left join t2 on t1.a=t2.c
and cos(t2.c+t2.b)>0.5 OR sin(t2.d)<0.9 group by t1.a;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t2.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
# With subq
select t2.b from t1 left join t1 as t2 on t1.a=t2.b and t1.b group by t1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t2.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select t2.b from t1 left join t1 as t2 on t1.a=t2.b and (select t1.b) group by t1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t2.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
# Test ANY_VALUE:
select ANY_VALUE(t1.b) from t1 left join t2 on t1.c=t1.b and t1.a=t1.c group by t1.a;
ANY_VALUE(t1.b)
10
20
select 3+(5*t1.b) from t1 left join t2 on t1.c=t1.b and t1.a=t1.c group by t1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select 3+(5*ANY_VALUE(t1.b)) from t1 left join t2 on t1.c=t1.b and t1.a=t1.c group by t1.a;
3+(5*ANY_VALUE(t1.b))
53
103
delete from t1;
insert into t1 (a,b) values(1,10),(1,20),(2,30),(2,40);
Warnings:
Warning	1364	Field 'c' doesn't have a default value
select a, sum(b) from t1;
ERROR 42000: In aggregated query without GROUP BY, expression #1 of SELECT list contains nonaggregated column 'test.t1.a'; this is incompatible with sql_mode=only_full_group_by
select any_value(a), sum(b) from t1;
any_value(a)	sum(b)
1	100
# different order of input rows, different "any_value":
select any_value(a), sum(b) from (select * from t1 order by a desc) as d;
any_value(a)	sum(b)
1	100
select a,b,sum(c) from t1 group by a;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select a,any_value(b),sum(c) from t1 group by a;
a	any_value(b)	sum(c)
1	10	0
2	30	0
select a,any_value(b),sum(c)
from (select * from t1 order by a desc, b desc) as d
group by a;
a	any_value(b)	sum(c)
1	10	0
2	30	0
# With view.
set @optimizer_switch_saved=@@optimizer_switch;
# Merged view
create algorithm=merge view v1 as select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1;
show create view v1;
View	Create View	character_set_client	collation_connection
v1	CREATE ALGORITHM=MERGE DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v1` AS select (`t1`.`a` * 2) AS `a`,`t1`.`b` AS `b`,`t1`.`c` AS `c`,`t1`.`d` AS `d`,`t1`.`a` AS `e` from `t1`	utf8mb4	utf8mb4_0900_ai_ci
select sin(a) as z from v1 group by b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select sin(a) as z from v1 group by d,b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select sin(a) as z from v1 group by c,b;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
select (select sin(a)) as z from v1 group by d,b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select (select sin(a)) as z from v1 group by c,b;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
select sin(a) as z from t1 group by c*2,b*2;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select sin(a+b*c) as z from v1 group by c,b;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.v1.c",
                "test.v1.b"
              ] /* columns */
            } 
select sin(a) as z from v1 where b=2 group by c;
z
# {v1.b,v1.c}->v1.a->t2.a
select sin(t2.a) as z from v1, v1 as t2
where t2.a=v1.a group by v1.b,v1.c;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.v1.b",
                "test.v1.c",
                "test.t2.a"
              ] /* columns */
            } 
select sin(t2.a) as z from v1, v1 as t2
where t2.b=v1.b and t2.c=v1.c group by v1.b,v1.c;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
# With materialized view we get more dependencies than needed, due to implementation.
select t3.a from v1, v1 as t2, v1 as t3
where
t3.b=t2.b and t3.c=v1.d and
t2.b=v1.b and t2.c=v1.c
group by v1.b,v1.c;
a
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0,
                1,
                2
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.v1.b",
                "test.v1.c",
                "test.t3.c",
                "test.t2.b",
                "test.t2.c",
                "test.t3.b"
              ] /* columns */
            } 
# If we simply went to real_item(), we would have WHERE 2*a=b, or
# GROUP BY 2*t1.a, so we would not find FDs. The original item
# (direct_view_ref here) must also be considered!
select a from v1 where a=b group by b;
a
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.b",
                "test.v1.a"
              ] /* columns */
            } 
select b from v1 where a=b group by a;
b
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.a",
                "test.v1.b"
              ] /* columns */
            } 
select v1.c from v1 where v1.c=v1.a group by v1.a;
c
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.a",
                "test.v1.c"
              ] /* columns */
            } 
select v1.a from v1 group by v1.e;
a
2
4
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.e",
                "test.v1.a"
              ] /* columns */
            } 
select v1.c from v1 where v1.c=v1.a group by v1.e;
c
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.e",
                "test.v1.a",
                "test.v1.c"
              ] /* columns */
            } 
# View appears only in WHERE
select t2.d from v1, t1 as t2 where v1.a=t2.d and v1.e=t2.a group by t2.a;
d
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.t2.a",
                "test.v1.e",
                "test.v1.a",
                "test.t2.d"
              ] /* columns */
            } 
drop view if exists v1;
# FD due to view's WHERE:
create algorithm=merge view v1 as select t1.a*2 as a, t1.b as b from t1;
select a from v1 group by b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
drop view if exists v1;
# Merged view
create algorithm=merge view v1 as select t1.a*2 as a, t1.b as b from t1 where t1.a=t1.b;
select a from v1 group by b;
a
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.b",
                "test.t1.a",
                "test.v1.a"
              ] /* columns */
            } 
drop view if exists v1;
# Aggregates in view
# Aggregates => skipping Merged view
drop view if exists v1;
Warnings:
Note	1051	Unknown table 'test.v1'
# Aggregates + GROUP BY in view
# We group by b*5, to show that it works with GROUP expressions, not only fields.
# Aggregates => skipping Merged view
drop view if exists v1;
Warnings:
Note	1051	Unknown table 'test.v1'
set optimizer_switch=@optimizer_switch_saved;
set @optimizer_switch_saved=@@optimizer_switch;
# Materialized view
create algorithm=temptable view v1 as select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1;
show create view v1;
View	Create View	character_set_client	collation_connection
v1	CREATE ALGORITHM=TEMPTABLE DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v1` AS select (`t1`.`a` * 2) AS `a`,`t1`.`b` AS `b`,`t1`.`c` AS `c`,`t1`.`d` AS `d`,`t1`.`a` AS `e` from `t1`	utf8mb4	utf8mb4_0900_ai_ci
select sin(a) as z from v1 group by b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select sin(a) as z from v1 group by d,b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select sin(a) as z from v1 group by c,b;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
select (select sin(a)) as z from v1 group by d,b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select (select sin(a)) as z from v1 group by c,b;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
select sin(a) as z from t1 group by c*2,b*2;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select sin(a+b*c) as z from v1 group by c,b;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.c",
                "test.v1.b",
                "test.v1.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v1`",
                  "all_columns_of_table_map_bits": [
                    0
                  ] /* all_columns_of_table_map_bits */,
                  "columns": [
                    "test.t1.c",
                    "test.t1.b"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
select sin(a) as z from v1 where b=2 group by c;
z
# {v1.b,v1.c}->v1.a->t2.a
select sin(t2.a) as z from v1, v1 as t2
where t2.a=v1.a group by v1.b,v1.c;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.b",
                "test.v1.c",
                "test.v1.a",
                "test.t2.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v1`",
                  "all_columns_of_table_map_bits": [
                    0
                  ] /* all_columns_of_table_map_bits */,
                  "columns": [
                    "test.t1.b",
                    "test.t1.c"
                  ] /* columns */
                },
                {
                  "table": "`v1` `t2`"
                }
              ] /* searched_in_materialized_tables */
            } 
select sin(t2.a) as z from v1, v1 as t2
where t2.b=v1.b and t2.c=v1.c group by v1.b,v1.c;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
# With materialized view we get more dependencies than needed, due to implementation.
select t3.a from v1, v1 as t2, v1 as t3
where
t3.b=t2.b and t3.c=v1.d and
t2.b=v1.b and t2.c=v1.c
group by v1.b,v1.c;
a
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.b",
                "test.v1.c",
                "test.t2.b",
                "test.t2.c",
                "test.t3.b",
                "test.v1.d",
                "test.t3.c",
                "test.t3.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v1`",
                  "all_columns_of_table_map_bits": [
                    0
                  ] /* all_columns_of_table_map_bits */,
                  "columns": [
                    "test.t1.b",
                    "test.t1.c"
                  ] /* columns */
                },
                {
                  "table": "`v1` `t2`",
                  "columns": [
                    "test.t1.b",
                    "test.t1.c"
                  ] /* columns */
                },
                {
                  "table": "`v1` `t3`",
                  "all_columns_of_table_map_bits": [
                    0
                  ] /* all_columns_of_table_map_bits */,
                  "columns": [
                    "test.t1.b",
                    "test.t1.c"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
# If we simply went to real_item(), we would have WHERE 2*a=b, or
# GROUP BY 2*t1.a, so we would not find FDs. The original item
# (direct_view_ref here) must also be considered!
select a from v1 where a=b group by b;
a
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.b",
                "test.v1.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v1`",
                  "columns": [
                    "test.t1.b"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
select b from v1 where a=b group by a;
b
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.a",
                "test.v1.b"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v1`",
                  "columns": [
                    "test.t1.b"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
select v1.c from v1 where v1.c=v1.a group by v1.a;
c
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.a",
                "test.v1.c"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v1`",
                  "columns": [
                    "test.t1.c"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
select v1.a from v1 group by v1.e;
a
2
4
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.e",
                "test.v1.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v1`",
                  "columns": [
                    "test.t1.a"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
select v1.c from v1 where v1.c=v1.a group by v1.e;
c
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.e",
                "test.v1.a",
                "test.v1.c"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v1`",
                  "columns": [
                    "test.t1.a",
                    "test.t1.c"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
# View appears only in WHERE
select t2.d from v1, t1 as t2 where v1.a=t2.d and v1.e=t2.a group by t2.a;
d
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.t2.a",
                "test.v1.e",
                "test.v1.a",
                "test.t2.d"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v1`",
                  "columns": [
                    "test.t1.a"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
drop view if exists v1;
# FD due to view's WHERE:
create algorithm=temptable view v1 as select t1.a*2 as a, t1.b as b from t1;
select a from v1 group by b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
drop view if exists v1;
# Materialized view
create algorithm=temptable view v1 as select t1.a*2 as a, t1.b as b from t1 where t1.a=t1.b;
select a from v1 group by b;
a
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.b",
                "test.v1.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v1`",
                  "columns": [
                    "test.t1.b",
                    "test.t1.a"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
drop view if exists v1;
# Aggregates in view
# Materialized view
create algorithm=temptable view v1 as select sum(t1.a) as a, sum(t1.b) as b from t1;
select a from v1 group by b;
a
6
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.v1.b"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v1`"
                }
              ] /* searched_in_materialized_tables */
            } 
drop view if exists v1;
# Aggregates + GROUP BY in view
# We group by b*5, to show that it works with GROUP expressions, not only fields.
# Materialized view
create algorithm=temptable view v1 as select a, b*5 as b, sum(t1.c) as c, sum(t1.d) as d from t1 group by a,b*5;
select a from v1 group by b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select c from v1 group by b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select c from v1 group by b,d;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select a,c from v1 group by a;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select d,c from v1 group by b,a;
d	c
NULL	0
NULL	0
NULL	0
NULL	0
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.v1.b",
                "test.v1.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v1`",
                  "columns": [
                    "test.t1.a"
                  ] /* columns */,
                  "all_group_expressions": true
                }
              ] /* searched_in_materialized_tables */
            } 
drop view if exists v1;
set optimizer_switch=@optimizer_switch_saved;
set @optimizer_switch_saved=@@optimizer_switch;
# Materialized derived table
set optimizer_switch='derived_merge=off';
select sin(a) as z from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1 group by b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select sin(a) as z from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1 group by d,b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select sin(a) as z from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1 group by c,b;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
select (select sin(a)) as z from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1 group by d,b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select (select sin(a)) as z from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1 group by c,b;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
select sin(a) as z from t1 group by c*2,b*2;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select sin(a+b*c) as z from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1 group by c,b;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "v1.c",
                "v1.b",
                "v1.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `v1`",
                  "all_columns_of_table_map_bits": [
                    0
                  ] /* all_columns_of_table_map_bits */,
                  "columns": [
                    "test.t1.c",
                    "test.t1.b"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
select sin(a) as z from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1 where b=2 group by c;
z
# {v1.b,v1.c}->v1.a->t2.a
select sin(t2.a) as z from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1, (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as t2
where t2.a=v1.a group by v1.b,v1.c;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "v1.b",
                "v1.c",
                "v1.a",
                "t2.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `v1`",
                  "all_columns_of_table_map_bits": [
                    0
                  ] /* all_columns_of_table_map_bits */,
                  "columns": [
                    "test.t1.b",
                    "test.t1.c"
                  ] /* columns */
                },
                {
                  "table": " `t2`"
                }
              ] /* searched_in_materialized_tables */
            } 
select sin(t2.a) as z from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1, (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as t2
where t2.b=v1.b and t2.c=v1.c group by v1.b,v1.c;
z
0.909297426825682
0.909297426825682
-0.756802495307928
-0.756802495307928
# With materialized view we get more dependencies than needed, due to implementation.
select t3.a from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1, (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as t2, (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as t3
where
t3.b=t2.b and t3.c=v1.d and
t2.b=v1.b and t2.c=v1.c
group by v1.b,v1.c;
a
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "v1.b",
                "v1.c",
                "t2.b",
                "t2.c",
                "t3.b",
                "v1.d",
                "t3.c",
                "t3.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `v1`",
                  "all_columns_of_table_map_bits": [
                    0
                  ] /* all_columns_of_table_map_bits */,
                  "columns": [
                    "test.t1.b",
                    "test.t1.c"
                  ] /* columns */
                },
                {
                  "table": " `t2`",
                  "columns": [
                    "test.t1.b",
                    "test.t1.c"
                  ] /* columns */
                },
                {
                  "table": " `t3`",
                  "all_columns_of_table_map_bits": [
                    0
                  ] /* all_columns_of_table_map_bits */,
                  "columns": [
                    "test.t1.b",
                    "test.t1.c"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
# If we simply went to real_item(), we would have WHERE 2*a=b, or
# GROUP BY 2*t1.a, so we would not find FDs. The original item
# (direct_view_ref here) must also be considered!
select a from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1 where a=b group by b;
a
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "v1.b",
                "v1.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `v1`",
                  "columns": [
                    "test.t1.b"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
select b from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1 where a=b group by a;
b
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "v1.a",
                "v1.b"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `v1`",
                  "columns": [
                    "test.t1.b"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
select v1.c from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1 where v1.c=v1.a group by v1.a;
c
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "v1.a",
                "v1.c"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `v1`",
                  "columns": [
                    "test.t1.c"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
select v1.a from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1 group by v1.e;
a
2
4
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "v1.e",
                "v1.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `v1`",
                  "columns": [
                    "test.t1.a"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
select v1.c from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1 where v1.c=v1.a group by v1.e;
c
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "v1.e",
                "v1.a",
                "v1.c"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `v1`",
                  "columns": [
                    "test.t1.a",
                    "test.t1.c"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
# View appears only in WHERE
select t2.d from (select t1.a*2 as a, t1.b as b, t1.c as c, t1.d as d, t1.a as e from t1) as v1, t1 as t2 where v1.a=t2.d and v1.e=t2.a group by t2.a;
d
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.t2.a",
                "v1.e",
                "v1.a",
                "test.t2.d"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `v1`",
                  "columns": [
                    "test.t1.a"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
drop view if exists v1;
Warnings:
Note	1051	Unknown table 'test.v1'
# FD due to view's WHERE:
select a from (select t1.a*2 as a, t1.b as b from t1) as v1 group by b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
drop view if exists v1;
Warnings:
Note	1051	Unknown table 'test.v1'
# Derived table
select a from (select t1.a*2 as a, t1.b as b from t1 where t1.a=t1.b) as v1 group by b;
a
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "v1.b",
                "v1.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `v1`",
                  "columns": [
                    "test.t1.b",
                    "test.t1.a"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
drop view if exists v1;
Warnings:
Note	1051	Unknown table 'test.v1'
# Aggregates in view
# Derived table
select a from (select sum(t1.a) as a, sum(t1.b) as b from t1) as v1 group by b;
a
6
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "v1.b"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `v1`"
                }
              ] /* searched_in_materialized_tables */
            } 
drop view if exists v1;
Warnings:
Note	1051	Unknown table 'test.v1'
# Aggregates + GROUP BY in view
# We group by b*5, to show that it works with GROUP expressions, not only fields.
# Derived table
select a from (select a, b*5 as b, sum(t1.c) as c, sum(t1.d) as d from t1 group by a,b*5) as v1 group by b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'v1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select c from (select a, b*5 as b, sum(t1.c) as c, sum(t1.d) as d from t1 group by a,b*5) as v1 group by b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'v1.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select c from (select a, b*5 as b, sum(t1.c) as c, sum(t1.d) as d from t1 group by a,b*5) as v1 group by b,d;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'v1.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select a,c from (select a, b*5 as b, sum(t1.c) as c, sum(t1.d) as d from t1 group by a,b*5) as v1 group by a;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'v1.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select d,c from (select a, b*5 as b, sum(t1.c) as c, sum(t1.d) as d from t1 group by a,b*5) as v1 group by b,a;
d	c
NULL	0
NULL	0
NULL	0
NULL	0
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "v1.b",
                "v1.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `v1`",
                  "columns": [
                    "test.t1.a"
                  ] /* columns */,
                  "all_group_expressions": true
                }
              ] /* searched_in_materialized_tables */
            } 
drop view if exists v1;
Warnings:
Note	1051	Unknown table 'test.v1'
set optimizer_switch=@optimizer_switch_saved;
# Derived table in merged view
create algorithm=temptable view v2
as select a as a, 2*a as b from t1;
create algorithm=merge view v1
as select v2.a as a, 3*v2.b as b from v2;
select v1.b from v1 group by v1.a;
b
6
12
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.a",
                "test.v2.b",
                "test.v1.b"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v2`",
                  "columns": [
                    "test.t1.a"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
select t2.a from t1 as t2, v1 where t2.a=v1.b group by v1.a;
a
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.v1.a",
                "test.v2.b",
                "test.v1.b",
                "test.t2.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`v2`",
                  "columns": [
                    "test.t1.a"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
# FDs in a view are those of the underlying query expression.
# FDs in a query expression: expressions in the SELECT list must be
# deterministic.
drop view v1;
create algorithm=merge view v1
as select v2.a as a, rand()*v2.b as b from v2;
select v1.b from v1 group by v1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
drop view v1;
create algorithm=temptable view v1
as select v2.a as a, rand()*v2.b as b from v2;
select v1.b from v1 group by v1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
drop view v1,v2;
# Item_direct_view_ref pointing to Item_direct_view_ref pointing to
# Item_field (a rare case!)
create algorithm=merge view v2 as select 2*a as a, 2*b as b from t1;
create algorithm=merge view v1 as select a, 3*b as b from v2 where a=b;
select 1 from (select a,b+0 from v1 group by a) as d;
1
drop view v1,v2;
# Some fun cases with aggregates in derived table.
# Inner query is valid: t1.a -> t1.b (equality in WHERE). Outer query:
# d.b -> t1.b (underlying column of d.b) -> t1.a (equality)
# -> sum(1) (because t1.a is all group columns so determines
# sum()) -> d.s (because sum() is underlying of d.s)
select d.s from
(select b, sum(1) as s from t1 where a=b group by a) as d
group by d.b;
s
alter table t1 add column pk int primary key auto_increment;
# Inner query: t1.pk -> t1.* (pk). Outer query:
# d.b,d.c -> t1.b,t1.c (their underlying columns) -> t1.pk (because
# t1.b,t1.c is unique) -> sum(1) (because t1.pk is all group columns so
# determines sum()) -> d.s (because sum() is underlying of d.s)
select d.s from
(select b, c, sum(d) as s from t1 group by pk) as d
group by d.b,d.c;
s
NULL
NULL
NULL
NULL
# Outer query:
# d.c -> t1.b*3 (underlying column of d.c) -> sum(a) (because t1.b*3
# is all group expressions) -> d.s
select d.s from
(select b*3 as c, sum(a) as s from t1 group by b*3) as d
group by d.c;
s
1
1
2
2
drop table t1,t2;
# Testcase from Reviewers
create table customer1(pk int primary key, a int);
create table customer2(pk int primary key, b int);
CREATE algorithm=merge VIEW customer as SELECT pk,a,b
FROM customer1 JOIN customer2 USING (pk);
select customer.pk, customer.b
from customer
group by customer.pk;
pk	b
# View is merged. Show FDs. Note that in --ps-protocol, the trace
# is that of execution, so contains no group-by checks.
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0,
                1
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.customer.pk",
                "test.customer2.pk"
              ] /* columns */
            } 
drop view customer;
CREATE algorithm=temptable VIEW customer as SELECT pk,a,b
FROM customer1 JOIN customer2 USING (pk);
select customer.pk, customer.b
from customer
group by customer.pk;
pk	b
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "test.customer.pk",
                "test.customer.b"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": "`customer`",
                  "all_columns_of_table_map_bits": [
                    0,
                    1
                  ] /* all_columns_of_table_map_bits */,
                  "columns": [
                    "test.customer1.pk",
                    "test.customer2.pk"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
# Benefit from outer-join-to-inner conversion.
insert into customer1 values(0,10),(1,20);
insert into customer2 values(0,10),(1,20);
# 1) no conversion, no FD from customer2.b to customer1.a.
explain select customer1.a, count(*)
from customer1 left join customer2 on customer1.a=customer2.b
where customer1.pk in (7,9)
group by customer2.b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.customer1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
# 2) null-complemented row can't pass WHERE => conversion
# => FD from customer2.b to customer1.a.
explain select customer1.a, count(*)
from customer1 left join customer2 on customer1.a=customer2.b
where customer2.pk in (7,9)
group by customer2.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	customer1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using temporary
1	SIMPLE	customer2	NULL	range	PRIMARY	PRIMARY	4	NULL	2	50.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`customer1`.`a` AS `a`,count(0) AS `count(*)` from `test`.`customer1` join `test`.`customer2` where ((`test`.`customer2`.`b` = `test`.`customer1`.`a`) and (`test`.`customer2`.`pk` in (7,9))) group by `test`.`customer2`.`b`
drop table customer1,customer2;
drop view customer;
# FDs of JOIN...USING and NATURAL JOIN
create table t1(pk int primary key, a int);
create table t2(pk int primary key, b int);
select t1.pk, t1.a from t1 join t2 on t1.pk=t2.pk group by t1.pk;
pk	a
select t1.pk, t1.a from t1 join t2 using(pk) group by t1.pk;
pk	a
select t1.pk, t1.a from t1 natural join t2 group by t1.pk;
pk	a
select t1.pk, t1.a from t1 left join t2 using(pk) group by t1.pk;
pk	a
select t1.pk, t1.a from t1 natural left join t2 group by t1.pk;
pk	a
select t1.pk, t2.b from t1 join t2 on t1.pk=t2.pk group by t1.pk;
pk	b
select t1.pk, t2.b from t1 join t2 using(pk) group by t1.pk;
pk	b
select t1.pk, t2.b from t1 natural join t2 group by t1.pk;
pk	b
select t1.pk, t2.b from t1 left join t2 using(pk) group by t1.pk;
pk	b
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0,
                1
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.t1.pk",
                "test.t2.pk"
              ] /* columns */
            } 
select t1.pk, t2.b from t1 natural left join t2 group by t1.pk;
pk	b
# Equivalent queries, with RIGHT JOIN
select t1.pk, t2.b from t2 right join t1 using(pk) group by t1.pk;
pk	b
select t1.pk, t2.b from t2 natural right join t1 group by t1.pk;
pk	b
select t1.pk, t2.b from t1 left join t2 on t1.pk>t2.pk group by t1.pk;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t2.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
# Even in --ps-protocol we see the group-by checks in trace because
# there has been no execution (due to error).
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                0
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.t1.pk"
              ] /* columns */
            } 
select t1.pk, t2.b from t2 right join t1 on t1.pk>t2.pk group by t1.pk;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t2.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
drop table t1,t2;
# Cases where FDs from weak side do not propagate
create table t1(a int, b int);
insert into t1 values(null,0),(null,1);
select d.a,d.c
from t1 left join (select a, coalesce(a,3) as c from t1) as d
on t1.b>0;
a	c
NULL	3
NULL	3
NULL	NULL
# Now group it by d.a:
select d.a,d.c
from t1 left join (select a, coalesce(a,3) as c from t1) as d
on t1.b>0 group by d.a;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'd.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "d.a"
              ] /* columns */
            } 
select d.a,d.c
from t1 left join (select a, count(a) as c from t1 group by a) as d
on t1.b+d.c>0;
a	c
NULL	0
NULL	NULL
# Now group it by d.a:
select d.a,d.c
from t1 left join (select a, count(a) as c from t1 group by a) as d
on t1.b+d.c>0 group by d.a;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'd.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "d.a"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `d`",
                  "columns": [
                    "test.t1.a"
                  ] /* columns */,
                  "all_group_expressions": true
                }
              ] /* searched_in_materialized_tables */
            } 
select d.m,d.c
from t1 left join (select max(a) as m, count(a) as c from t1) as d
on t1.b+d.c>0;
m	c
NULL	0
NULL	NULL
# Now group it by d.m:
select d.m,d.c
from t1 left join (select max(a) as m, count(a) as c from t1) as d
on t1.b+d.c>0 group by d.m;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'd.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "d.m"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `d`"
                }
              ] /* searched_in_materialized_tables */
            } 
# Now group it by d.c which is non-nullable:
select d.m,d.c
from t1 left join (select max(a) as m, count(a) as c from t1) as d
on t1.b+d.c>0 group by d.c;
m	c
NULL	0
NULL	NULL
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                1
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "d.c"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `d`"
                }
              ] /* searched_in_materialized_tables */
            } 
drop table t1;
create table t1(pk int primary key, a int);
# Not valid per the standard, because:
# 1) In t3, t3.pk->t3.a holds.
# 2) In R1 the result of "(t2 left join t3 on 1)", t3.pk->t3.a
# holds, by application of: there is a functional dependency in the
# weak side t3, and t3.pk is not nullable in t3.
# 3) In R2 the result of "t1 left join (t2 left join t3 on 1) on 1",
# t3.pk->t3.a doesn't hold anymore, because: it's a dependency in the
# weak side (weak side is R1), and t3.pk is nullable _when
# seen as a column of R1_ (in R1 t3.pk can be NULL, if the row of t3
# is actually a null-complemented one).
# But for us it is valid, because we have refined the logic: the
# pk-based FD satisfies the requirement that a NULL value of t3.pk
# implies a NULL value of t3.a (indeed, the NULL value of t3.pk can
# only come from null-complementing of the row of t3 in R1, in which
# case t3.a is also NULL).
select t3.a
from t1 left join (t1 as t2 left join t1 as t3 on 1) on 1
group by t3.pk;
a
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                2
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.t3.pk"
              ] /* columns */
            } 
# Outer reference - why we use resolved_used_tables():
select (select t1.a from t1 as t2 limit 1) from t1 group by pk;
(select t1.a from t1 as t2 limit 1)
# We don't build the FD list if not needed
select t1.a*3 from t1 group by t1.a;
t1.a*3
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
NOT FOUND
drop table t1;
# Tricky cases with "ON col=literal" propagating.
create table t1(a int, b int);
insert into t1 values();
insert into t1 values(10,11);
create table t2(c int, d int);
insert into t2 values(2,3);
select t4.d
from t1 left join (t2 as t3 join t2 as t4 on t4.d=3) on t1.a=10;
d
3
NULL
# Equivalent to T1 LJ (T2, T3) ON T4.D=3 AND T1.A=10
# (this is what simplify_joins() does).
# For T4.D=3, DJS is {T1.A} which is not group column.
select t4.d
from t1 left join (t2 as t3 join t2 as t4 on t4.d=3) on t1.a=10 group by "";
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t4.d' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select t4.d
from t1 left join (t2 as t3 left join t2 as t4 on t4.d=3) on t1.a=10;
d
3
NULL
# For T4.D=3, DJS={}, not NULL-friendly, and embedding is on weak side
# so FD cannot propagate.
select t4.d
from t1 left join (t2 as t3 left join t2 as t4 on t4.d=3) on t1.a=10 group by "";
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t4.d' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
select t4.d
from t1 join (t2 as t3 left join t2 as t4 on t4.d=3) on t1.a=10;
d
3
# For T4.D=3, DJS={}, not NULL-friendly, but embedding is on weak side
# so FD can propagate.
select t4.d
from t1 join (t2 as t3 left join t2 as t4 on t4.d=3) on t1.a=10 group by "";
d
3
# With a view
create view v1 as select a as a, 2*a as b, coalesce(a,3) as c from t1;
select v1.b from t1 left join v1 on 1;
b
20
20
NULL
NULL
# If v1.a is NULL then v1.b is NULL: a->b is NULL-friendly
select v1.b from t1 left join v1 on 1 group by v1.a;
b
20
NULL
select v1.c from t1 left join v1 on 1;
c
10
10
3
3
# If v1.a is NULL then v1.c may not be NULL: a->c is not NULL-friendly
select v1.c from t1 left join v1 on 1 group by v1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.v1.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
drop view v1;
# Constant view item
create view v1 as select a as a, 2 as b from t1;
# Because of BUG#17023060, the result is wrong, should be
# [10,2 ; 10,2 ; NULL,NULL], which would show that {}->{v1.b} does not
# hold in the result, even though v1.b is constant (=2) in v1.
select t1.a, v1.b from t1 left join v1 on t1.a is not null;
a	b
10	2
10	2
NULL	NULL
# We correctly reject this:
select t1.a, v1.b from t1 left join v1 on t1.a is not null group by v1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
drop view v1;
drop table t1,t2;
create table emp(empno int, ename char(1), deptno int);
create table dept(deptno int primary key, dname char(1));
CREATE algorithm=merge VIEW empdept AS
SELECT emp.empno, emp.ename, dept.deptno, dept.dname
FROM emp LEFT OUTER JOIN dept ON (emp.deptno = dept.deptno);
EXPLAIN SELECT dname, COUNT(*)
FROM empdept
GROUP BY deptno;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	emp	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using temporary
1	SIMPLE	dept	NULL	eq_ref	PRIMARY	PRIMARY	4	test.emp.deptno	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`dept`.`dname` AS `dname`,count(0) AS `COUNT(*)` from `test`.`emp` left join `test`.`dept` on((`test`.`dept`.`deptno` = `test`.`emp`.`deptno`)) where true group by `test`.`dept`.`deptno`
# There is pk-based FD dept.Depno->dept.dname in dept
# and it propagates in the view-which-became-nest because it is
# NULL-friendly.
SELECT dname, COUNT(*)
FROM empdept
GROUP BY deptno;
dname	COUNT(*)
drop view empdept;
CREATE algorithm=temptable VIEW empdept AS
SELECT emp.empno, emp.ename, dept.deptno, dept.dname
FROM emp LEFT OUTER JOIN dept ON (emp.deptno = dept.deptno);
EXPLAIN SELECT dname, COUNT(*)
FROM empdept
GROUP BY deptno;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	<derived2>	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using temporary
2	DERIVED	emp	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
2	DERIVED	dept	NULL	eq_ref	PRIMARY	PRIMARY	4	test.emp.deptno	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`empdept`.`dname` AS `dname`,count(0) AS `COUNT(*)` from `test`.`empdept` group by `test`.`empdept`.`deptno`
# There is pk-based FD dept.Depno->dept.dname in dept
# and it propagates in the materialized view because it is
# NULL-friendly, and then in the top query because the view is not in
# the weak side of an outer join.
SELECT dname, COUNT(*)
FROM empdept
GROUP BY deptno;
dname	COUNT(*)
# More tests, for code coverage.
# UNION in derived table
select emp.ename
from
(select 1 as empno union select 2) deriv,
emp
where emp.empno=deriv.empno
group by emp.empno;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.emp.ename' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
drop view empdept;
# Make the key-searching loop meet view columns which don't wrap a
# column (CONCAT).
CREATE VIEW empdept AS
SELECT emp.empno, emp.ename, dept.deptno, concat("",dept.dname) as dname
FROM emp LEFT JOIN dept ON (emp.deptno = dept.deptno);
SELECT ename, COUNT(*) FROM empdept WHERE empno=dname and empno=deptno GROUP BY empno;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.empdept.ename' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
drop table emp,dept;
drop view empdept;
#
# Bug#19636980 ASSERT `TABLE->OUTER_JOIN' FAILED IN GROUP_CHECK::FIND_FD_IN_JOINED_TABLE
#
CREATE TABLE t1 (
c1 INT,
c2 INT,
c4 DATE,
c5 VARCHAR(1)
);
CREATE TABLE t2 (
c1 INT,
c2 INT,
c3 INT,
c5 VARCHAR(1)
);
# alias1.c5 is not FD, the error is detected at SELECT time
CREATE VIEW v1 AS
SELECT alias1.c4 AS field1
FROM t1 AS alias1
INNER JOIN t1 AS alias2
ON 1
GROUP BY field1
ORDER BY alias1.c5;
SELECT * FROM v1;
ERROR 42000: Expression #1 of ORDER BY clause is not in GROUP BY clause and contains nonaggregated column 'test.alias1.c5' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
DROP VIEW v1;
# alias1.c5 is FD (WHERE contains: alias1.c5='d')
CREATE VIEW v1 AS
SELECT alias1.c4 AS field1, alias1.c4 AS field2
FROM t1 AS alias1
INNER JOIN t1 AS alias2
ON (alias2.c1 = alias1.c2)
WHERE ( NOT EXISTS (
SELECT SQ1_alias1.c5 AS SQ1_field1
FROM t2 AS SQ1_alias1
WHERE SQ1_alias1.c3 < alias1.c1
))
AND (alias1.c5 = alias1.c5
AND alias1.c5 = 'd'
    )
GROUP BY field1, field2
ORDER BY alias1.c5, field1, field2
;
SELECT * FROM v1;
field1	field2
DROP VIEW v1;
DROP TABLE t1,t2;
#
# Bug#19636409 ASSERT `(MAP_OF_NEW_EQ_FDS...` IN GROUP_CHECK::IS_FD_ON_SOURCE ON SELECT
#
CREATE TABLE t1 (
pk int NOT NULL,
c1 datetime,
c2 varchar(1),
c3 date,
c4 date,
c5 varchar(1),
PRIMARY KEY (pk)
);
CREATE VIEW v1 AS
SELECT c3 AS subfield11,
pk AS subfield12,
c2 AS subfield13
FROM t1
GROUP BY subfield11, subfield12
;
# This query is valid. Indeed: field3 i.e. t1.c2 is part of GROUP BY,
# so, in a group:
# - all rows have the same value of t1.c2, noted val_c2,
# - so all rows, which necessarily match WHERE, matched with a row of
# v1 having subfield11=val_c2 and subfield12=val_c2 (due to IN
# predicate),
# - thus both grouping columns of v1's query expression are constant
# (to val_c2)
# - thus v1.subfield13 is constant too (grouping columns determine the
# SELECT list)
# - so, due to IN, this determines the value of t1.pk (t1 is the top
# query's table).
# - so t1.pk is constant, so all columns of t1 are constant.
# Which proves that it makes sense to search FDs in semijoin
# equalities.
# In other words: a semijoin is like a join except that it eliminates
# duplicates, but duplicates are irrelevant to the decision whether a
# column is functionally dependent on a set of columns.
SELECT c5 AS field1,
c4 AS field2,
c2 AS field3,
c1 AS field4
FROM t1
WHERE ( c2, c2, pk ) IN (
SELECT * FROM v1
)
GROUP BY field1, field3, field3;
field1	field2	field3	field4
#
# Bug#19687724 FUNCTIONAL DEPENDENCIES ARE NOT RECOGNIZED IN EQUALITIES BETWEEN ROWS
#
# Inspired by query above, but with a join:
SELECT c5 AS field1,
c4 AS field2,
c2 AS field3,
c1 AS field4
FROM t1, v1
WHERE ( c2, c2, pk ) = (subfield11, subfield12, subfield13)
GROUP BY field1, field3, field3;
field1	field2	field3	field4
# With constants:
SELECT c5 AS field1,
c4 AS field2,
c2 AS field3,
c1 AS field4
FROM t1
WHERE ( c2, c2, pk ) = (1, 2, 3)
GROUP BY field1, field3, field3;
field1	field2	field3	field4
DROP TABLE t1;
CREATE TABLE t1(a INT, b INT, c INT, d INT);
SELECT a,b,c,d FROM t1 WHERE a=c AND b=d GROUP by a,b;
a	b	c	d
SELECT a,b,c,d FROM t1 WHERE (a,b)=(c,d) GROUP BY a,b;
a	b	c	d
SELECT a,b,c,d FROM t1 WHERE (a,b)=(c,d+1) GROUP BY a,b;
ERROR 42000: Expression #4 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.d' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
DROP TABLE t1;
DROP VIEW v1;
#
# Bug #20031708 ASSERT ON GROUP_CHECK::IS_FD_ON_SOURCE
#
CREATE TABLE t1 (
col_varchar_10_utf8 VARCHAR(10) CHARACTER SET utf8mb3,
col_int_key INT,
pk INT PRIMARY KEY
);
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
CREATE TABLE t2 (
col_varchar_10_utf8 VARCHAR(10) CHARACTER SET utf8mb3 DEFAULT NULL,
col_int_key INT DEFAULT NULL,
pk INT PRIMARY KEY
);
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
CREATE VIEW v2 AS SELECT * FROM t2;
# The reporter's testcase:
SELECT COUNT(*), t1.col_int_key
FROM v2 LEFT OUTER JOIN t1
ON v2.col_varchar_10_utf8 = t1.col_varchar_10_utf8
WHERE v2.pk = 4;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.t1.col_int_key'; this is incompatible with sql_mode=only_full_group_by
DROP VIEW v2;
# A variant: one column is an expression:
CREATE VIEW v2 AS SELECT
CONCAT(col_varchar_10_utf8,' ') AS col_varchar_10_utf8,
col_int_key,
pk
FROM t2;
SELECT COUNT(*), t1.col_int_key
FROM v2 LEFT OUTER JOIN t1
ON v2.col_varchar_10_utf8 = t1.col_varchar_10_utf8
WHERE v2.pk = 4;
ERROR 42000: In aggregated query without GROUP BY, expression #2 of SELECT list contains nonaggregated column 'test.t1.col_int_key'; this is incompatible with sql_mode=only_full_group_by
DROP VIEW v2;
# Query used in the commit comment: view column involving two tables
CREATE VIEW v2 AS
SELECT t1.pk, t2.col_int_key+1 as c, t1.pk+t2.col_int_key as p
FROM t1, t2;
# FDs will be discovered in this order: {}->v2.pk, v2.pk->v2.c,
# v2.c->v2.p
SELECT COUNT(*), v2.p
FROM v2
WHERE v2.c=v2.p and v2.c=v2.pk AND v2.pk = 4;
COUNT(*)	p
0	NULL
DROP VIEW v2;
# If in the query specification defining a view, a base table's pk is
# determined,
# and the view's column is a function of this base table's columns,
# then the view's column is also determined.
# So, in this view's result, {v2.pk}->{v2.coa}:
CREATE ALGORITHM=MERGE VIEW v2 AS
SELECT t2.pk, COALESCE(t2.pk, 3) AS coa
FROM t1 LEFT JOIN t2 ON 0;
# And thus {pk}->{coa} holds in the result of this query using the view;
# if there is NULL-complementing in the LEFT JOIN below then (pk,coa)
# will be (NULL,NULL) and if there is not it will be (non-NULL,3):
# v2.coa is determined by v2.pk. The key fact is that v2.pk is not
# NULLable so this is a NFFD.
SELECT v2.pk, v2.coa
FROM t1 LEFT JOIN v2 AS v2 ON 0
GROUP BY v2.pk;
pk	coa
DROP VIEW v2;
DROP TABLE t1,t2;
#
# Bug#21807579 FUNCTIONAL DEPENDENCIES ARE NOT RECOGNIZED IN GENERATED COLUMNS
#
CREATE TABLE t ( a INT, c INT GENERATED ALWAYS AS (a+2), d INT GENERATED ALWAYS AS (c+2) );
# {a}->{c}, {c}->{d} and {a}->{d} hold.
SELECT c FROM t GROUP BY a;
c
SELECT d FROM t GROUP BY c;
d
SELECT d FROM t GROUP BY a;
d
SELECT 1+c FROM t GROUP BY a;
1+c
SELECT 1+d FROM t GROUP BY c;
1+d
SELECT 1+d FROM t GROUP BY a;
1+d
# {t2.a}->{t2.d}->{t1.c}->{t1.d}
SELECT t1.d FROM t as t1, t as t2 WHERE t2.d=t1.c GROUP BY t2.a;
d
SELECT t1.d FROM t as t1, t as t2 WHERE t2.d>t1.c GROUP BY t2.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.d' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
DROP TABLE t;
# A non-NULL-friendly dependency.
CREATE TABLE t ( a INT,
c INT GENERATED ALWAYS AS (COALESCE(a,3)) );
INSERT INTO t (a) VALUES(NULL);
CREATE TABLE u ( a INT );
INSERT INTO u VALUES(0),(1);
# Even though {a}->{c} holds in 't', it doesn't propagate to the left
# join's result
SELECT t.a,t.c FROM u LEFT JOIN t ON u.a>0;
a	c
NULL	3
NULL	NULL
SELECT t.a,t.c FROM u LEFT JOIN t ON u.a>0 GROUP BY t.a;
ERROR 42000: Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
DROP TABLE t,u;
#
# Bug #22924183 	GROUP BY ON DERIVED TABLE, EXPRESSION OF AGGREGATE AND GROUP COLUMN: ERROR
#
CREATE TABLE coll (
doc text
) ENGINE=InnoDB;
SELECT (je+1)+count(*) FROM (SELECT doc+1 AS je FROM coll) AS dt GROUP BY je;
(je+1)+count(*)
SELECT je+(1+count(*)) FROM (SELECT doc+1 AS je FROM coll) AS dt GROUP BY je;
je+(1+count(*))
DROP TABLE coll;
#
# Bug#27114719 FUNCTIONAL DEPENDENCY CHECK FAILS ON FIRST CALL, SUCCEEDS ON NEXT
#
CREATE TABLE t ( a INT, c INT GENERATED ALWAYS AS (a+2), d INT GENERATED
ALWAYS AS (c+2) );
SELECT t1.d FROM t as t1, t as t2 WHERE t2.d>t1.c GROUP BY t2.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.d' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT t1.d FROM t as t1, t as t2 WHERE t2.d>t1.c GROUP BY t2.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.d' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
FLUSH TABLES;
SELECT (SELECT t1.c FROM t as t1 GROUP BY -3) FROM t as t2;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT DISTINCT t1.a FROM t as t1 ORDER BY t1.d LIMIT 1;
ERROR HY000: Expression #1 of ORDER BY clause is not in SELECT list, references column 'test.t1.d' which is not in SELECT list; this is incompatible with DISTINCT
SELECT (SELECT DISTINCT t1.a FROM t as t1 ORDER BY t1.d LIMIT 1) FROM t as t2;
ERROR HY000: Expression #1 of ORDER BY clause is not in SELECT list, references column 'test.t1.d' which is not in SELECT list; this is incompatible with DISTINCT
DROP TABLE t;
#
# Bug#22279903 ISSUE WITH ONLY_FULL_GROUP_BY AND UNIQUE KEY WITH NULL VALUES
#
CREATE TABLE t(a INT NULL, b INT NOT NULL, c INT, UNIQUE(a,b));
INSERT INTO t VALUES (NULL, 1, 4);
INSERT INTO t VALUES (NULL, 1, 5);
SELECT a,b,c FROM t GROUP BY a,b;
ERROR 42000: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT a,b,c FROM t WHERE a IS NOT NULL GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE NOT (a IS NULL) GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE a > 3 GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE a = 3 GROUP BY b;
a	b	c
SELECT a,b,c FROM t WHERE a BETWEEN 3 AND 6 GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE a <> 3 GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE a IN (3,4) GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE a IN (SELECT b FROM t) GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE a IS TRUE GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE (a <> 3) IS TRUE GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE a IS FALSE GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE (a <> 3) IS FALSE GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE NOT(a IN (3,4)) GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE a NOT IN (3,4) GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE a LIKE "%abc%" GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE a NOT LIKE "%abc%" GROUP BY a,b;
a	b	c
SELECT a,b,c FROM t WHERE a IS TRUE GROUP BY b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT a,b,c FROM t WHERE a<=>NULL GROUP BY b;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT a,b,c FROM t WHERE a IS NOT TRUE GROUP BY a,b;
ERROR 42000: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT a,b,c FROM
(SELECT * FROM t WHERE a IS NOT NULL) AS dt GROUP BY a,b;
a	b	c
SELECT /*+ NO_MERGE() */ a,b,c FROM
(SELECT * FROM t WHERE a IS NOT NULL) AS dt GROUP BY a,b;
a	b	c
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "columns": [
                "dt.a",
                "dt.b",
                "dt.c"
              ] /* columns */,
              "searched_in_materialized_tables": [
                {
                  "table": " `dt`",
                  "all_columns_of_table_map_bits": [
                    0
                  ] /* all_columns_of_table_map_bits */,
                  "columns": [
                    "test.t.a",
                    "test.t.b"
                  ] /* columns */
                }
              ] /* searched_in_materialized_tables */
            } 
SELECT a,b,c FROM t WHERE a IS NULL GROUP BY a,b;
ERROR 42000: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT a,b,c FROM t WHERE NOT(a IS NOT NULL) GROUP BY a,b;
ERROR 42000: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT t.* FROM t LEFT JOIN t AS t1 ON t.a IS NOT NULL
GROUP BY t.a,t.b;
ERROR 42000: Expression #3 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT t.* FROM t RIGHT JOIN t AS t1 ON t.a IS NOT NULL
GROUP BY t.a,t.b;
a	b	c
NULL	NULL	NULL
DROP TABLE t;
CREATE TABLE t1 (a INT, b INT);
CREATE TABLE t2 (b INT);
CREATE TABLE t3 (
b INT NULL,
c INT NULL,
d INT NULL,
e INT NULL,
UNIQUE KEY (b,d,e));
SELECT * FROM t1, t2, t3
WHERE t2.b = t1.b AND t2.b = t3.b
AND t3.d = 1 AND t3.e = 1 AND t3.d IS NOT NULL AND t1.a = 2
GROUP BY t1.b;
a	b	b	b	c	d	e
SELECT * FROM t1, t2, t3
WHERE t2.b = t1.b AND t2.b = t3.b
AND t3.d = 1 AND t3.e = 1 AND t1.a = 2
GROUP BY t1.b;
a	b	b	b	c	d	e
SELECT show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
show_json_object('"functional_dependencies_of_GROUP_columns": {', TRACE)
"functional_dependencies_of_GROUP_columns": {
              "all_columns_of_table_map_bits": [
                2
              ] /* all_columns_of_table_map_bits */,
              "columns": [
                "test.t1.b",
                "test.t2.b",
                "test.t3.b",
                "test.t3.d",
                "test.t3.e",
                "test.t1.a"
              ] /* columns */
            } 
SELECT * FROM t1, t2, t3
WHERE t2.b = t1.b AND t2.b = t3.b
AND t3.e = 1 AND t3.d IS NOT NULL AND t1.a = 2
GROUP BY t1.b;
ERROR 42000: Expression #5 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t3.c' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
# Verify that for semijoin and antijoin we catch the FD issues.
explain select (select 2 from t2 where 1=(select t1.b from t3)) as col from t1 group by t1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
explain select (select 2 from t2 where exists(select t1.b from t3)) as col from t1 group by t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using temporary
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
2	DEPENDENT SUBQUERY	t3	NULL	index	NULL	b	15	NULL	1	100.00	Using index; FirstMatch(t2); Using join buffer (hash join)
Warnings:
Note	1276	Field or reference 'test.t1.b' of SELECT #3 was resolved in SELECT #1
Note	1003	/* select#1 */ select (/* select#2 */ select 2 from `test`.`t2` semi join (`test`.`t3`) where true) AS `col` from `test`.`t1` group by `test`.`t1`.`a`
explain select (select 2 from t2 where not exists(select t1.b from t3)) as col from t1 group by t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using temporary
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
2	DEPENDENT SUBQUERY	t3	NULL	index	NULL	b	15	NULL	1	100.00	Using where; Not exists; Using index; Using join buffer (hash join)
Warnings:
Note	1276	Field or reference 'test.t1.b' of SELECT #3 was resolved in SELECT #1
Note	1003	/* select#1 */ select (/* select#2 */ select 2 from `test`.`t2` anti join (`test`.`t3`) on(true) where true) AS `col` from `test`.`t1` group by `test`.`t1`.`a`
explain select (select 2 from t2 where exists(select 1 from t3 where t1.b)) as col from t1 group by t1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
explain select (select 2 from t2 where not exists(select 1 from t3 where t1.b)) as col from t1 group by t1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
explain select (select 2 from t2 where 1 in(select t1.b from t3)) as col from t1 group by t1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
alter table t1 modify b int not null;
explain select (select 2 from t2 where 1 not in(select t1.b from t3)) as col from t1 group by t1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
explain select (select 2 from t2 where 1 in(select 1 from t3 where t1.b)) as col from t1 group by t1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
explain select (select 2 from t2 where 1 not in(select 1 from t3 where t1.b)) as col from t1 group by t1.a;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.b' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
DROP TABLE t1, t2, t3;
DROP FUNCTION show_json_object;
#
# Bug#29006668 UNEXPECTED ERROR: "ORDER BY CLAUSE IS NOT IN GROUP BY CLAUSE" WITH ROLLUP
#
CREATE TABLE t(i INT);
INSERT INTO t VALUES (-1),(2),(1);
SELECT ABS(i) AS a FROM t GROUP BY abs(i)             ORDER BY a + 1;
a
1
2
SELECT ABS(i) AS a FROM t GROUP BY abs(i) WITH ROLLUP ORDER BY a + 1;
a
NULL
1
2
SELECT ABS(i) AS a FROM t GROUP BY a                  ORDER BY a + 1;
a
1
2
SELECT ABS(i) AS a FROM t GROUP BY a      WITH ROLLUP ORDER BY a + 1;
a
NULL
1
2
DROP TABLE t;
#
# Bug#30753524 DBUG_ASSERT((MAP_OF_NEW_FDS & PSEUDO_TABLE_BITS) == 0)
#
CREATE TABLE t (a INT,b INT);
SELECT 1 FROM t AS ot
WHERE 1 <=
(
SELECT it2.a
FROM
(
SELECT ot.b FROM t AS it1
) AS dt
JOIN t AS it2 ON dt.b=it2.b
GROUP BY it2.b
)
;
ERROR 42000: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.it2.a' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
SELECT 1 FROM t AS ot
WHERE 1 <=
(
SELECT dt.b
FROM
(
SELECT ot.b FROM t AS it1
) AS dt
JOIN t AS it2 ON dt.b=it2.b
GROUP BY it2.b
)
;
1
DROP TABLE t;
