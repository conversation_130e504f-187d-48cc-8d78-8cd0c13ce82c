include/group_replication.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection server1]
#################################################################
# Start group replication on server 1.
server1
include/start_and_bootstrap_group_replication.inc
CREATE TABLE test.t1 (a INT NOT NULL AUTO_INCREMENT PRIMARY KEY , b INT);

#####################################################################
# Start group replication on server2 and wait for the state to change.
server2
set session sql_log_bin=0;
call mtr.add_suppression("Transaction cannot be executed while Group Replication is recovering.*");
call mtr.add_suppression("Run function 'before_commit' in plugin 'group_replication' failed");
set session sql_log_bin=1;
SET @@GLOBAL.DEBUG='+d,recovery_thread_wait_before_finish';
include/start_group_replication.inc

###################################################
# On server 2 wait for recovery to create the table.

##########################################################
# On server 2 try to execute a query and see that it fails as the
# server is in super-read-only mode during recovery.
INSERT INTO test.t1 (b) VALUES (1);
ERROR HY000: The MySQL server is running with the --super-read-only option so it cannot execute this statement

###################################################################
# On server 2 wait for it to become online and re-execute the query
server2
SET DEBUG_SYNC= "now WAIT_FOR signal.recovery_thread_wait_before_finish_reached";
SET @@GLOBAL.DEBUG='-d,recovery_thread_wait_before_finish';
SET DEBUG_SYNC= "now SIGNAL signal.recovery_end";
include/gr_wait_for_member_state.inc
SET DEBUG_SYNC= 'RESET';
INSERT INTO test.t1 (b) VALUES (1);
include/rpl/sync.inc

#######################################################################
# Assert that server1 and server2 have the same data and GTID_EXECUTED.
server1
include/assert.inc ['There is a value 1 in table t1 on server1']
server2
include/assert.inc ['There is a value 1 in table t1 on server2']
server1
server2
include/assert.inc ['server1 and server2 GTID_EXECUTED must be equal']

#########
# Cleanup
DROP TABLE t1;
include/group_replication_end.inc
