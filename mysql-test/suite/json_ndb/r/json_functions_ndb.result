set default_storage_engine=ndb;
# Test of the JSON functions used in SQL statements
# ----------------------------------------------------------------------
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
CREATE TABLE t1 (pk int  NOT NULL PRIMARY KEY AUTO_INCREMENT, i INT, j JSON);
INSERT INTO t1(i, j) VALUES (0, NULL);
INSERT INTO t1(i, j) VALUES (1, '{"a": 2}');
INSERT INTO t1(i, j) VALUES (2, '[1,2]');
INSERT INTO t1(i, j) VALUES (3, '{"a":"b", "c":"d","ab":"abc", "bc": ["x", "y"]}');
INSERT INTO t1(i, j) VALUES (4, '["here", ["I", "am"], "!!!"]');
INSERT INTO t1(i, j) VALUES (5, '"scalar string"');
INSERT INTO t1(i, j) VALUES (6, 'true');
INSERT INTO t1(i, j) VALUES (7, 'false');
INSERT INTO t1(i, j) VALUES (8, 'null');
INSERT INTO t1(i, j) VALUES (9, '-1');
INSERT INTO t1(i, j) VALUES (10, CAST(CAST(1 AS UNSIGNED) AS JSON));
INSERT INTO t1(i, j) VALUES (11, '32767');
INSERT INTO t1(i, j) VALUES (12, '32768');
INSERT INTO t1(i, j) VALUES (13, '-32768');
INSERT INTO t1(i, j) VALUES (14, '-32769');
INSERT INTO t1(i, j) VALUES (15, '2147483647');
INSERT INTO t1(i, j) VALUES (16, '2147483648');
INSERT INTO t1(i, j) VALUES (17, '-2147483648');
INSERT INTO t1(i, j) VALUES (18, '-2147483649');
INSERT INTO t1(i, j) VALUES (19, '18446744073709551615');
INSERT INTO t1(i, j) VALUES (20, '18446744073709551616');
INSERT INTO t1(i, j) VALUES (21, '3.14');
INSERT INTO t1(i, j) VALUES (22, '{}');
INSERT INTO t1(i, j) VALUES (23, '[]');
INSERT INTO t1(i, j) VALUES (24, CAST(CAST('2015-01-15 23:24:25' AS DATETIME) AS JSON));
INSERT INTO t1(i, j) VALUES (25, CAST(CAST('23:24:25' AS TIME) AS JSON));
INSERT INTO t1(i, j) VALUES (26, CAST(CAST('2015-01-15' AS DATE) AS JSON));
INSERT INTO t1(i, j) VALUES (27, CAST(TIMESTAMP'2015-01-15 23:24:25' AS JSON));
INSERT INTO t1(i, j) VALUES (28, CAST(ST_GeomFromText('POINT(1 1)') AS JSON));
INSERT INTO t1(i, j) VALUES (29, CAST('[]' AS CHAR CHARACTER SET 'ascii'));
INSERT INTO t1(i, j) VALUES (30, CAST(x'cafe' AS JSON));
INSERT INTO t1(i, j) VALUES (31, CAST(x'cafebabe' AS JSON));
#
# Test of JSON comparator.
#
SELECT i,
(j = '"scalar string"') AS c1,
(j = 'scalar string') AS c2,
(j = CAST('"scalar string"' AS JSON)) AS c3,
(j = CAST(CAST(j AS CHAR CHARACTER SET 'utf8mb4') AS JSON)) AS c4,
(j = CAST(NULL AS JSON)) AS c5,
(j = NULL) AS c6,
(j <=> NULL) AS c7,
(j <=> CAST(NULL AS JSON)) AS c8,
(j IN (-1, 2, 32768, 3.14)) AS c9,
(j IN (CAST('[1, 2]' AS JSON), CAST('{}' AS JSON), CAST(3.14 AS JSON))) AS c10,
(j = (SELECT j FROM t1 WHERE j = CAST('null' AS JSON))) AS c11,
(j = (SELECT j FROM t1 WHERE j IS NULL)) AS c12,
(j = (SELECT j FROM t1 WHERE 1<>1)) AS c13,
(j = DATE'2015-01-15') AS c14,
(j = TIME'23:24:25') AS c15,
(j = TIMESTAMP'2015-01-15 23:24:25') AS c16,
(j = CURRENT_TIMESTAMP) AS c17,
(j = ST_GeomFromText('POINT(1 1)')) AS c18,
(JSON_EXTRACT(j, '$.a') = 2) AS c19
FROM t1
ORDER BY i;
i	c1	c2	c3	c4	c5	c6	c7	c8	c9	c10	c11	c12	c13	c14	c15	c16	c17	c18	c19
0	NULL	NULL	NULL	NULL	NULL	NULL	1	1	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
1	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	1
2	0	0	0	1	NULL	NULL	0	0	0	1	0	NULL	NULL	0	0	0	0	0	NULL
3	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	0
4	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
5	0	1	1	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
6	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
7	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
8	0	0	0	1	NULL	NULL	0	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL
9	0	0	0	1	NULL	NULL	0	0	1	0	0	NULL	NULL	0	0	0	0	0	NULL
10	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
11	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
12	0	0	0	1	NULL	NULL	0	0	1	0	0	NULL	NULL	0	0	0	0	0	NULL
13	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
14	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
15	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
16	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
17	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
18	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
19	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
20	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
21	0	0	0	1	NULL	NULL	0	0	1	1	0	NULL	NULL	0	0	0	0	0	NULL
22	0	0	0	1	NULL	NULL	0	0	0	1	0	NULL	NULL	0	0	0	0	0	NULL
23	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
24	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	1	0	0	NULL
25	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL	NULL	0	1	0	0	0	NULL
26	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL	NULL	1	0	0	0	0	NULL
27	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	1	0	0	NULL
28	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	1	NULL
29	0	0	0	1	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
30	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
31	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL	NULL	0	0	0	0	0	NULL
SELECT i FROM t1
WHERE j = CAST(CAST(j AS CHAR CHARACTER SET 'utf8mb3') AS JSON)
ORDER BY i;
i
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
28
29
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT CAST(NULL AS UNSIGNED) = CAST(NULL AS JSON);
CAST(NULL AS UNSIGNED) = CAST(NULL AS JSON)
NULL
SELECT CAST(NULL AS JSON) = CAST(NULL AS JSON);
CAST(NULL AS JSON) = CAST(NULL AS JSON)
NULL
SELECT CAST(NULL AS JSON) = NULL;
CAST(NULL AS JSON) = NULL
NULL
SELECT CAST(1 AS JSON) = NULL;
CAST(1 AS JSON) = NULL
NULL
SELECT CAST('true' AS JSON) = 1;
CAST('true' AS JSON) = 1
0
SELECT CAST('true' AS JSON) = true;
CAST('true' AS JSON) = true
1
SELECT a.i, b.i, a.j < b.j, a.j = b.j, a.j > b.j, a.j <> b.j, a.j <=> b.j
FROM t1 a, t1 b
ORDER BY a.i, b.i;
i	i	a.j < b.j	a.j = b.j	a.j > b.j	a.j <> b.j	a.j <=> b.j
0	0	NULL	NULL	NULL	NULL	1
0	1	NULL	NULL	NULL	NULL	0
0	2	NULL	NULL	NULL	NULL	0
0	3	NULL	NULL	NULL	NULL	0
0	4	NULL	NULL	NULL	NULL	0
0	5	NULL	NULL	NULL	NULL	0
0	6	NULL	NULL	NULL	NULL	0
0	7	NULL	NULL	NULL	NULL	0
0	8	NULL	NULL	NULL	NULL	0
0	9	NULL	NULL	NULL	NULL	0
0	10	NULL	NULL	NULL	NULL	0
0	11	NULL	NULL	NULL	NULL	0
0	12	NULL	NULL	NULL	NULL	0
0	13	NULL	NULL	NULL	NULL	0
0	14	NULL	NULL	NULL	NULL	0
0	15	NULL	NULL	NULL	NULL	0
0	16	NULL	NULL	NULL	NULL	0
0	17	NULL	NULL	NULL	NULL	0
0	18	NULL	NULL	NULL	NULL	0
0	19	NULL	NULL	NULL	NULL	0
0	20	NULL	NULL	NULL	NULL	0
0	21	NULL	NULL	NULL	NULL	0
0	22	NULL	NULL	NULL	NULL	0
0	23	NULL	NULL	NULL	NULL	0
0	24	NULL	NULL	NULL	NULL	0
0	25	NULL	NULL	NULL	NULL	0
0	26	NULL	NULL	NULL	NULL	0
0	27	NULL	NULL	NULL	NULL	0
0	28	NULL	NULL	NULL	NULL	0
0	29	NULL	NULL	NULL	NULL	0
0	30	NULL	NULL	NULL	NULL	0
0	31	NULL	NULL	NULL	NULL	0
1	0	NULL	NULL	NULL	NULL	0
1	1	0	1	0	0	1
1	2	1	0	0	1	0
1	3	1	0	0	1	0
1	4	1	0	0	1	0
1	5	0	0	1	1	0
1	6	1	0	0	1	0
1	7	1	0	0	1	0
1	8	0	0	1	1	0
1	9	0	0	1	1	0
1	10	0	0	1	1	0
1	11	0	0	1	1	0
1	12	0	0	1	1	0
1	13	0	0	1	1	0
1	14	0	0	1	1	0
1	15	0	0	1	1	0
1	16	0	0	1	1	0
1	17	0	0	1	1	0
1	18	0	0	1	1	0
1	19	0	0	1	1	0
1	20	0	0	1	1	0
1	21	0	0	1	1	0
1	22	0	0	1	1	0
1	23	1	0	0	1	0
1	24	1	0	0	1	0
1	25	1	0	0	1	0
1	26	1	0	0	1	0
1	27	1	0	0	1	0
1	28	1	0	0	1	0
1	29	1	0	0	1	0
1	30	1	0	0	1	0
1	31	1	0	0	1	0
2	0	NULL	NULL	NULL	NULL	0
2	1	0	0	1	1	0
2	2	0	1	0	0	1
2	3	0	0	1	1	0
2	4	1	0	0	1	0
2	5	0	0	1	1	0
2	6	1	0	0	1	0
2	7	1	0	0	1	0
2	8	0	0	1	1	0
2	9	0	0	1	1	0
2	10	0	0	1	1	0
2	11	0	0	1	1	0
2	12	0	0	1	1	0
2	13	0	0	1	1	0
2	14	0	0	1	1	0
2	15	0	0	1	1	0
2	16	0	0	1	1	0
2	17	0	0	1	1	0
2	18	0	0	1	1	0
2	19	0	0	1	1	0
2	20	0	0	1	1	0
2	21	0	0	1	1	0
2	22	0	0	1	1	0
2	23	0	0	1	1	0
2	24	1	0	0	1	0
2	25	1	0	0	1	0
2	26	1	0	0	1	0
2	27	1	0	0	1	0
2	28	0	0	1	1	0
2	29	0	0	1	1	0
2	30	1	0	0	1	0
2	31	1	0	0	1	0
3	0	NULL	NULL	NULL	NULL	0
3	1	0	0	1	1	0
3	2	1	0	0	1	0
3	3	0	1	0	0	1
3	4	1	0	0	1	0
3	5	0	0	1	1	0
3	6	1	0	0	1	0
3	7	1	0	0	1	0
3	8	0	0	1	1	0
3	9	0	0	1	1	0
3	10	0	0	1	1	0
3	11	0	0	1	1	0
3	12	0	0	1	1	0
3	13	0	0	1	1	0
3	14	0	0	1	1	0
3	15	0	0	1	1	0
3	16	0	0	1	1	0
3	17	0	0	1	1	0
3	18	0	0	1	1	0
3	19	0	0	1	1	0
3	20	0	0	1	1	0
3	21	0	0	1	1	0
3	22	0	0	1	1	0
3	23	1	0	0	1	0
3	24	1	0	0	1	0
3	25	1	0	0	1	0
3	26	1	0	0	1	0
3	27	1	0	0	1	0
3	28	0	0	1	1	0
3	29	1	0	0	1	0
3	30	1	0	0	1	0
3	31	1	0	0	1	0
4	0	NULL	NULL	NULL	NULL	0
4	1	0	0	1	1	0
4	2	0	0	1	1	0
4	3	0	0	1	1	0
4	4	0	1	0	0	1
4	5	0	0	1	1	0
4	6	1	0	0	1	0
4	7	1	0	0	1	0
4	8	0	0	1	1	0
4	9	0	0	1	1	0
4	10	0	0	1	1	0
4	11	0	0	1	1	0
4	12	0	0	1	1	0
4	13	0	0	1	1	0
4	14	0	0	1	1	0
4	15	0	0	1	1	0
4	16	0	0	1	1	0
4	17	0	0	1	1	0
4	18	0	0	1	1	0
4	19	0	0	1	1	0
4	20	0	0	1	1	0
4	21	0	0	1	1	0
4	22	0	0	1	1	0
4	23	0	0	1	1	0
4	24	1	0	0	1	0
4	25	1	0	0	1	0
4	26	1	0	0	1	0
4	27	1	0	0	1	0
4	28	0	0	1	1	0
4	29	0	0	1	1	0
4	30	1	0	0	1	0
4	31	1	0	0	1	0
5	0	NULL	NULL	NULL	NULL	0
5	1	1	0	0	1	0
5	2	1	0	0	1	0
5	3	1	0	0	1	0
5	4	1	0	0	1	0
5	5	0	1	0	0	1
5	6	1	0	0	1	0
5	7	1	0	0	1	0
5	8	0	0	1	1	0
5	9	0	0	1	1	0
5	10	0	0	1	1	0
5	11	0	0	1	1	0
5	12	0	0	1	1	0
5	13	0	0	1	1	0
5	14	0	0	1	1	0
5	15	0	0	1	1	0
5	16	0	0	1	1	0
5	17	0	0	1	1	0
5	18	0	0	1	1	0
5	19	0	0	1	1	0
5	20	0	0	1	1	0
5	21	0	0	1	1	0
5	22	1	0	0	1	0
5	23	1	0	0	1	0
5	24	1	0	0	1	0
5	25	1	0	0	1	0
5	26	1	0	0	1	0
5	27	1	0	0	1	0
5	28	1	0	0	1	0
5	29	1	0	0	1	0
5	30	1	0	0	1	0
5	31	1	0	0	1	0
6	0	NULL	NULL	NULL	NULL	0
6	1	0	0	1	1	0
6	2	0	0	1	1	0
6	3	0	0	1	1	0
6	4	0	0	1	1	0
6	5	0	0	1	1	0
6	6	0	1	0	0	1
6	7	0	0	1	1	0
6	8	0	0	1	1	0
6	9	0	0	1	1	0
6	10	0	0	1	1	0
6	11	0	0	1	1	0
6	12	0	0	1	1	0
6	13	0	0	1	1	0
6	14	0	0	1	1	0
6	15	0	0	1	1	0
6	16	0	0	1	1	0
6	17	0	0	1	1	0
6	18	0	0	1	1	0
6	19	0	0	1	1	0
6	20	0	0	1	1	0
6	21	0	0	1	1	0
6	22	0	0	1	1	0
6	23	0	0	1	1	0
6	24	1	0	0	1	0
6	25	1	0	0	1	0
6	26	1	0	0	1	0
6	27	1	0	0	1	0
6	28	0	0	1	1	0
6	29	0	0	1	1	0
6	30	1	0	0	1	0
6	31	1	0	0	1	0
7	0	NULL	NULL	NULL	NULL	0
7	1	0	0	1	1	0
7	2	0	0	1	1	0
7	3	0	0	1	1	0
7	4	0	0	1	1	0
7	5	0	0	1	1	0
7	6	1	0	0	1	0
7	7	0	1	0	0	1
7	8	0	0	1	1	0
7	9	0	0	1	1	0
7	10	0	0	1	1	0
7	11	0	0	1	1	0
7	12	0	0	1	1	0
7	13	0	0	1	1	0
7	14	0	0	1	1	0
7	15	0	0	1	1	0
7	16	0	0	1	1	0
7	17	0	0	1	1	0
7	18	0	0	1	1	0
7	19	0	0	1	1	0
7	20	0	0	1	1	0
7	21	0	0	1	1	0
7	22	0	0	1	1	0
7	23	0	0	1	1	0
7	24	1	0	0	1	0
7	25	1	0	0	1	0
7	26	1	0	0	1	0
7	27	1	0	0	1	0
7	28	0	0	1	1	0
7	29	0	0	1	1	0
7	30	1	0	0	1	0
7	31	1	0	0	1	0
8	0	NULL	NULL	NULL	NULL	0
8	1	1	0	0	1	0
8	2	1	0	0	1	0
8	3	1	0	0	1	0
8	4	1	0	0	1	0
8	5	1	0	0	1	0
8	6	1	0	0	1	0
8	7	1	0	0	1	0
8	8	0	1	0	0	1
8	9	1	0	0	1	0
8	10	1	0	0	1	0
8	11	1	0	0	1	0
8	12	1	0	0	1	0
8	13	1	0	0	1	0
8	14	1	0	0	1	0
8	15	1	0	0	1	0
8	16	1	0	0	1	0
8	17	1	0	0	1	0
8	18	1	0	0	1	0
8	19	1	0	0	1	0
8	20	1	0	0	1	0
8	21	1	0	0	1	0
8	22	1	0	0	1	0
8	23	1	0	0	1	0
8	24	1	0	0	1	0
8	25	1	0	0	1	0
8	26	1	0	0	1	0
8	27	1	0	0	1	0
8	28	1	0	0	1	0
8	29	1	0	0	1	0
8	30	1	0	0	1	0
8	31	1	0	0	1	0
9	0	NULL	NULL	NULL	NULL	0
9	1	1	0	0	1	0
9	2	1	0	0	1	0
9	3	1	0	0	1	0
9	4	1	0	0	1	0
9	5	1	0	0	1	0
9	6	1	0	0	1	0
9	7	1	0	0	1	0
9	8	0	0	1	1	0
9	9	0	1	0	0	1
9	10	1	0	0	1	0
9	11	1	0	0	1	0
9	12	1	0	0	1	0
9	13	0	0	1	1	0
9	14	0	0	1	1	0
9	15	1	0	0	1	0
9	16	1	0	0	1	0
9	17	0	0	1	1	0
9	18	0	0	1	1	0
9	19	1	0	0	1	0
9	20	1	0	0	1	0
9	21	1	0	0	1	0
9	22	1	0	0	1	0
9	23	1	0	0	1	0
9	24	1	0	0	1	0
9	25	1	0	0	1	0
9	26	1	0	0	1	0
9	27	1	0	0	1	0
9	28	1	0	0	1	0
9	29	1	0	0	1	0
9	30	1	0	0	1	0
9	31	1	0	0	1	0
10	0	NULL	NULL	NULL	NULL	0
10	1	1	0	0	1	0
10	2	1	0	0	1	0
10	3	1	0	0	1	0
10	4	1	0	0	1	0
10	5	1	0	0	1	0
10	6	1	0	0	1	0
10	7	1	0	0	1	0
10	8	0	0	1	1	0
10	9	0	0	1	1	0
10	10	0	1	0	0	1
10	11	1	0	0	1	0
10	12	1	0	0	1	0
10	13	0	0	1	1	0
10	14	0	0	1	1	0
10	15	1	0	0	1	0
10	16	1	0	0	1	0
10	17	0	0	1	1	0
10	18	0	0	1	1	0
10	19	1	0	0	1	0
10	20	1	0	0	1	0
10	21	1	0	0	1	0
10	22	1	0	0	1	0
10	23	1	0	0	1	0
10	24	1	0	0	1	0
10	25	1	0	0	1	0
10	26	1	0	0	1	0
10	27	1	0	0	1	0
10	28	1	0	0	1	0
10	29	1	0	0	1	0
10	30	1	0	0	1	0
10	31	1	0	0	1	0
11	0	NULL	NULL	NULL	NULL	0
11	1	1	0	0	1	0
11	2	1	0	0	1	0
11	3	1	0	0	1	0
11	4	1	0	0	1	0
11	5	1	0	0	1	0
11	6	1	0	0	1	0
11	7	1	0	0	1	0
11	8	0	0	1	1	0
11	9	0	0	1	1	0
11	10	0	0	1	1	0
11	11	0	1	0	0	1
11	12	1	0	0	1	0
11	13	0	0	1	1	0
11	14	0	0	1	1	0
11	15	1	0	0	1	0
11	16	1	0	0	1	0
11	17	0	0	1	1	0
11	18	0	0	1	1	0
11	19	1	0	0	1	0
11	20	1	0	0	1	0
11	21	0	0	1	1	0
11	22	1	0	0	1	0
11	23	1	0	0	1	0
11	24	1	0	0	1	0
11	25	1	0	0	1	0
11	26	1	0	0	1	0
11	27	1	0	0	1	0
11	28	1	0	0	1	0
11	29	1	0	0	1	0
11	30	1	0	0	1	0
11	31	1	0	0	1	0
12	0	NULL	NULL	NULL	NULL	0
12	1	1	0	0	1	0
12	2	1	0	0	1	0
12	3	1	0	0	1	0
12	4	1	0	0	1	0
12	5	1	0	0	1	0
12	6	1	0	0	1	0
12	7	1	0	0	1	0
12	8	0	0	1	1	0
12	9	0	0	1	1	0
12	10	0	0	1	1	0
12	11	0	0	1	1	0
12	12	0	1	0	0	1
12	13	0	0	1	1	0
12	14	0	0	1	1	0
12	15	1	0	0	1	0
12	16	1	0	0	1	0
12	17	0	0	1	1	0
12	18	0	0	1	1	0
12	19	1	0	0	1	0
12	20	1	0	0	1	0
12	21	0	0	1	1	0
12	22	1	0	0	1	0
12	23	1	0	0	1	0
12	24	1	0	0	1	0
12	25	1	0	0	1	0
12	26	1	0	0	1	0
12	27	1	0	0	1	0
12	28	1	0	0	1	0
12	29	1	0	0	1	0
12	30	1	0	0	1	0
12	31	1	0	0	1	0
13	0	NULL	NULL	NULL	NULL	0
13	1	1	0	0	1	0
13	2	1	0	0	1	0
13	3	1	0	0	1	0
13	4	1	0	0	1	0
13	5	1	0	0	1	0
13	6	1	0	0	1	0
13	7	1	0	0	1	0
13	8	0	0	1	1	0
13	9	1	0	0	1	0
13	10	1	0	0	1	0
13	11	1	0	0	1	0
13	12	1	0	0	1	0
13	13	0	1	0	0	1
13	14	0	0	1	1	0
13	15	1	0	0	1	0
13	16	1	0	0	1	0
13	17	0	0	1	1	0
13	18	0	0	1	1	0
13	19	1	0	0	1	0
13	20	1	0	0	1	0
13	21	1	0	0	1	0
13	22	1	0	0	1	0
13	23	1	0	0	1	0
13	24	1	0	0	1	0
13	25	1	0	0	1	0
13	26	1	0	0	1	0
13	27	1	0	0	1	0
13	28	1	0	0	1	0
13	29	1	0	0	1	0
13	30	1	0	0	1	0
13	31	1	0	0	1	0
14	0	NULL	NULL	NULL	NULL	0
14	1	1	0	0	1	0
14	2	1	0	0	1	0
14	3	1	0	0	1	0
14	4	1	0	0	1	0
14	5	1	0	0	1	0
14	6	1	0	0	1	0
14	7	1	0	0	1	0
14	8	0	0	1	1	0
14	9	1	0	0	1	0
14	10	1	0	0	1	0
14	11	1	0	0	1	0
14	12	1	0	0	1	0
14	13	1	0	0	1	0
14	14	0	1	0	0	1
14	15	1	0	0	1	0
14	16	1	0	0	1	0
14	17	0	0	1	1	0
14	18	0	0	1	1	0
14	19	1	0	0	1	0
14	20	1	0	0	1	0
14	21	1	0	0	1	0
14	22	1	0	0	1	0
14	23	1	0	0	1	0
14	24	1	0	0	1	0
14	25	1	0	0	1	0
14	26	1	0	0	1	0
14	27	1	0	0	1	0
14	28	1	0	0	1	0
14	29	1	0	0	1	0
14	30	1	0	0	1	0
14	31	1	0	0	1	0
15	0	NULL	NULL	NULL	NULL	0
15	1	1	0	0	1	0
15	2	1	0	0	1	0
15	3	1	0	0	1	0
15	4	1	0	0	1	0
15	5	1	0	0	1	0
15	6	1	0	0	1	0
15	7	1	0	0	1	0
15	8	0	0	1	1	0
15	9	0	0	1	1	0
15	10	0	0	1	1	0
15	11	0	0	1	1	0
15	12	0	0	1	1	0
15	13	0	0	1	1	0
15	14	0	0	1	1	0
15	15	0	1	0	0	1
15	16	1	0	0	1	0
15	17	0	0	1	1	0
15	18	0	0	1	1	0
15	19	1	0	0	1	0
15	20	1	0	0	1	0
15	21	0	0	1	1	0
15	22	1	0	0	1	0
15	23	1	0	0	1	0
15	24	1	0	0	1	0
15	25	1	0	0	1	0
15	26	1	0	0	1	0
15	27	1	0	0	1	0
15	28	1	0	0	1	0
15	29	1	0	0	1	0
15	30	1	0	0	1	0
15	31	1	0	0	1	0
16	0	NULL	NULL	NULL	NULL	0
16	1	1	0	0	1	0
16	2	1	0	0	1	0
16	3	1	0	0	1	0
16	4	1	0	0	1	0
16	5	1	0	0	1	0
16	6	1	0	0	1	0
16	7	1	0	0	1	0
16	8	0	0	1	1	0
16	9	0	0	1	1	0
16	10	0	0	1	1	0
16	11	0	0	1	1	0
16	12	0	0	1	1	0
16	13	0	0	1	1	0
16	14	0	0	1	1	0
16	15	0	0	1	1	0
16	16	0	1	0	0	1
16	17	0	0	1	1	0
16	18	0	0	1	1	0
16	19	1	0	0	1	0
16	20	1	0	0	1	0
16	21	0	0	1	1	0
16	22	1	0	0	1	0
16	23	1	0	0	1	0
16	24	1	0	0	1	0
16	25	1	0	0	1	0
16	26	1	0	0	1	0
16	27	1	0	0	1	0
16	28	1	0	0	1	0
16	29	1	0	0	1	0
16	30	1	0	0	1	0
16	31	1	0	0	1	0
17	0	NULL	NULL	NULL	NULL	0
17	1	1	0	0	1	0
17	2	1	0	0	1	0
17	3	1	0	0	1	0
17	4	1	0	0	1	0
17	5	1	0	0	1	0
17	6	1	0	0	1	0
17	7	1	0	0	1	0
17	8	0	0	1	1	0
17	9	1	0	0	1	0
17	10	1	0	0	1	0
17	11	1	0	0	1	0
17	12	1	0	0	1	0
17	13	1	0	0	1	0
17	14	1	0	0	1	0
17	15	1	0	0	1	0
17	16	1	0	0	1	0
17	17	0	1	0	0	1
17	18	0	0	1	1	0
17	19	1	0	0	1	0
17	20	1	0	0	1	0
17	21	1	0	0	1	0
17	22	1	0	0	1	0
17	23	1	0	0	1	0
17	24	1	0	0	1	0
17	25	1	0	0	1	0
17	26	1	0	0	1	0
17	27	1	0	0	1	0
17	28	1	0	0	1	0
17	29	1	0	0	1	0
17	30	1	0	0	1	0
17	31	1	0	0	1	0
18	0	NULL	NULL	NULL	NULL	0
18	1	1	0	0	1	0
18	2	1	0	0	1	0
18	3	1	0	0	1	0
18	4	1	0	0	1	0
18	5	1	0	0	1	0
18	6	1	0	0	1	0
18	7	1	0	0	1	0
18	8	0	0	1	1	0
18	9	1	0	0	1	0
18	10	1	0	0	1	0
18	11	1	0	0	1	0
18	12	1	0	0	1	0
18	13	1	0	0	1	0
18	14	1	0	0	1	0
18	15	1	0	0	1	0
18	16	1	0	0	1	0
18	17	1	0	0	1	0
18	18	0	1	0	0	1
18	19	1	0	0	1	0
18	20	1	0	0	1	0
18	21	1	0	0	1	0
18	22	1	0	0	1	0
18	23	1	0	0	1	0
18	24	1	0	0	1	0
18	25	1	0	0	1	0
18	26	1	0	0	1	0
18	27	1	0	0	1	0
18	28	1	0	0	1	0
18	29	1	0	0	1	0
18	30	1	0	0	1	0
18	31	1	0	0	1	0
19	0	NULL	NULL	NULL	NULL	0
19	1	1	0	0	1	0
19	2	1	0	0	1	0
19	3	1	0	0	1	0
19	4	1	0	0	1	0
19	5	1	0	0	1	0
19	6	1	0	0	1	0
19	7	1	0	0	1	0
19	8	0	0	1	1	0
19	9	0	0	1	1	0
19	10	0	0	1	1	0
19	11	0	0	1	1	0
19	12	0	0	1	1	0
19	13	0	0	1	1	0
19	14	0	0	1	1	0
19	15	0	0	1	1	0
19	16	0	0	1	1	0
19	17	0	0	1	1	0
19	18	0	0	1	1	0
19	19	0	1	0	0	1
19	20	1	0	0	1	0
19	21	0	0	1	1	0
19	22	1	0	0	1	0
19	23	1	0	0	1	0
19	24	1	0	0	1	0
19	25	1	0	0	1	0
19	26	1	0	0	1	0
19	27	1	0	0	1	0
19	28	1	0	0	1	0
19	29	1	0	0	1	0
19	30	1	0	0	1	0
19	31	1	0	0	1	0
20	0	NULL	NULL	NULL	NULL	0
20	1	1	0	0	1	0
20	2	1	0	0	1	0
20	3	1	0	0	1	0
20	4	1	0	0	1	0
20	5	1	0	0	1	0
20	6	1	0	0	1	0
20	7	1	0	0	1	0
20	8	0	0	1	1	0
20	9	0	0	1	1	0
20	10	0	0	1	1	0
20	11	0	0	1	1	0
20	12	0	0	1	1	0
20	13	0	0	1	1	0
20	14	0	0	1	1	0
20	15	0	0	1	1	0
20	16	0	0	1	1	0
20	17	0	0	1	1	0
20	18	0	0	1	1	0
20	19	0	0	1	1	0
20	20	0	1	0	0	1
20	21	0	0	1	1	0
20	22	1	0	0	1	0
20	23	1	0	0	1	0
20	24	1	0	0	1	0
20	25	1	0	0	1	0
20	26	1	0	0	1	0
20	27	1	0	0	1	0
20	28	1	0	0	1	0
20	29	1	0	0	1	0
20	30	1	0	0	1	0
20	31	1	0	0	1	0
21	0	NULL	NULL	NULL	NULL	0
21	1	1	0	0	1	0
21	2	1	0	0	1	0
21	3	1	0	0	1	0
21	4	1	0	0	1	0
21	5	1	0	0	1	0
21	6	1	0	0	1	0
21	7	1	0	0	1	0
21	8	0	0	1	1	0
21	9	0	0	1	1	0
21	10	0	0	1	1	0
21	11	1	0	0	1	0
21	12	1	0	0	1	0
21	13	0	0	1	1	0
21	14	0	0	1	1	0
21	15	1	0	0	1	0
21	16	1	0	0	1	0
21	17	0	0	1	1	0
21	18	0	0	1	1	0
21	19	1	0	0	1	0
21	20	1	0	0	1	0
21	21	0	1	0	0	1
21	22	1	0	0	1	0
21	23	1	0	0	1	0
21	24	1	0	0	1	0
21	25	1	0	0	1	0
21	26	1	0	0	1	0
21	27	1	0	0	1	0
21	28	1	0	0	1	0
21	29	1	0	0	1	0
21	30	1	0	0	1	0
21	31	1	0	0	1	0
22	0	NULL	NULL	NULL	NULL	0
22	1	1	0	0	1	0
22	2	1	0	0	1	0
22	3	1	0	0	1	0
22	4	1	0	0	1	0
22	5	0	0	1	1	0
22	6	1	0	0	1	0
22	7	1	0	0	1	0
22	8	0	0	1	1	0
22	9	0	0	1	1	0
22	10	0	0	1	1	0
22	11	0	0	1	1	0
22	12	0	0	1	1	0
22	13	0	0	1	1	0
22	14	0	0	1	1	0
22	15	0	0	1	1	0
22	16	0	0	1	1	0
22	17	0	0	1	1	0
22	18	0	0	1	1	0
22	19	0	0	1	1	0
22	20	0	0	1	1	0
22	21	0	0	1	1	0
22	22	0	1	0	0	1
22	23	1	0	0	1	0
22	24	1	0	0	1	0
22	25	1	0	0	1	0
22	26	1	0	0	1	0
22	27	1	0	0	1	0
22	28	1	0	0	1	0
22	29	1	0	0	1	0
22	30	1	0	0	1	0
22	31	1	0	0	1	0
23	0	NULL	NULL	NULL	NULL	0
23	1	0	0	1	1	0
23	2	1	0	0	1	0
23	3	0	0	1	1	0
23	4	1	0	0	1	0
23	5	0	0	1	1	0
23	6	1	0	0	1	0
23	7	1	0	0	1	0
23	8	0	0	1	1	0
23	9	0	0	1	1	0
23	10	0	0	1	1	0
23	11	0	0	1	1	0
23	12	0	0	1	1	0
23	13	0	0	1	1	0
23	14	0	0	1	1	0
23	15	0	0	1	1	0
23	16	0	0	1	1	0
23	17	0	0	1	1	0
23	18	0	0	1	1	0
23	19	0	0	1	1	0
23	20	0	0	1	1	0
23	21	0	0	1	1	0
23	22	0	0	1	1	0
23	23	0	1	0	0	1
23	24	1	0	0	1	0
23	25	1	0	0	1	0
23	26	1	0	0	1	0
23	27	1	0	0	1	0
23	28	0	0	1	1	0
23	29	0	1	0	0	1
23	30	1	0	0	1	0
23	31	1	0	0	1	0
24	0	NULL	NULL	NULL	NULL	0
24	1	0	0	1	1	0
24	2	0	0	1	1	0
24	3	0	0	1	1	0
24	4	0	0	1	1	0
24	5	0	0	1	1	0
24	6	0	0	1	1	0
24	7	0	0	1	1	0
24	8	0	0	1	1	0
24	9	0	0	1	1	0
24	10	0	0	1	1	0
24	11	0	0	1	1	0
24	12	0	0	1	1	0
24	13	0	0	1	1	0
24	14	0	0	1	1	0
24	15	0	0	1	1	0
24	16	0	0	1	1	0
24	17	0	0	1	1	0
24	18	0	0	1	1	0
24	19	0	0	1	1	0
24	20	0	0	1	1	0
24	21	0	0	1	1	0
24	22	0	0	1	1	0
24	23	0	0	1	1	0
24	24	0	1	0	0	1
24	25	0	0	1	1	0
24	26	0	0	1	1	0
24	27	0	1	0	0	1
24	28	0	0	1	1	0
24	29	0	0	1	1	0
24	30	1	0	0	1	0
24	31	1	0	0	1	0
25	0	NULL	NULL	NULL	NULL	0
25	1	0	0	1	1	0
25	2	0	0	1	1	0
25	3	0	0	1	1	0
25	4	0	0	1	1	0
25	5	0	0	1	1	0
25	6	0	0	1	1	0
25	7	0	0	1	1	0
25	8	0	0	1	1	0
25	9	0	0	1	1	0
25	10	0	0	1	1	0
25	11	0	0	1	1	0
25	12	0	0	1	1	0
25	13	0	0	1	1	0
25	14	0	0	1	1	0
25	15	0	0	1	1	0
25	16	0	0	1	1	0
25	17	0	0	1	1	0
25	18	0	0	1	1	0
25	19	0	0	1	1	0
25	20	0	0	1	1	0
25	21	0	0	1	1	0
25	22	0	0	1	1	0
25	23	0	0	1	1	0
25	24	1	0	0	1	0
25	25	0	1	0	0	1
25	26	0	0	1	1	0
25	27	1	0	0	1	0
25	28	0	0	1	1	0
25	29	0	0	1	1	0
25	30	1	0	0	1	0
25	31	1	0	0	1	0
26	0	NULL	NULL	NULL	NULL	0
26	1	0	0	1	1	0
26	2	0	0	1	1	0
26	3	0	0	1	1	0
26	4	0	0	1	1	0
26	5	0	0	1	1	0
26	6	0	0	1	1	0
26	7	0	0	1	1	0
26	8	0	0	1	1	0
26	9	0	0	1	1	0
26	10	0	0	1	1	0
26	11	0	0	1	1	0
26	12	0	0	1	1	0
26	13	0	0	1	1	0
26	14	0	0	1	1	0
26	15	0	0	1	1	0
26	16	0	0	1	1	0
26	17	0	0	1	1	0
26	18	0	0	1	1	0
26	19	0	0	1	1	0
26	20	0	0	1	1	0
26	21	0	0	1	1	0
26	22	0	0	1	1	0
26	23	0	0	1	1	0
26	24	1	0	0	1	0
26	25	1	0	0	1	0
26	26	0	1	0	0	1
26	27	1	0	0	1	0
26	28	0	0	1	1	0
26	29	0	0	1	1	0
26	30	1	0	0	1	0
26	31	1	0	0	1	0
27	0	NULL	NULL	NULL	NULL	0
27	1	0	0	1	1	0
27	2	0	0	1	1	0
27	3	0	0	1	1	0
27	4	0	0	1	1	0
27	5	0	0	1	1	0
27	6	0	0	1	1	0
27	7	0	0	1	1	0
27	8	0	0	1	1	0
27	9	0	0	1	1	0
27	10	0	0	1	1	0
27	11	0	0	1	1	0
27	12	0	0	1	1	0
27	13	0	0	1	1	0
27	14	0	0	1	1	0
27	15	0	0	1	1	0
27	16	0	0	1	1	0
27	17	0	0	1	1	0
27	18	0	0	1	1	0
27	19	0	0	1	1	0
27	20	0	0	1	1	0
27	21	0	0	1	1	0
27	22	0	0	1	1	0
27	23	0	0	1	1	0
27	24	0	1	0	0	1
27	25	0	0	1	1	0
27	26	0	0	1	1	0
27	27	0	1	0	0	1
27	28	0	0	1	1	0
27	29	0	0	1	1	0
27	30	1	0	0	1	0
27	31	1	0	0	1	0
28	0	NULL	NULL	NULL	NULL	0
28	1	0	0	1	1	0
28	2	1	0	0	1	0
28	3	1	0	0	1	0
28	4	1	0	0	1	0
28	5	0	0	1	1	0
28	6	1	0	0	1	0
28	7	1	0	0	1	0
28	8	0	0	1	1	0
28	9	0	0	1	1	0
28	10	0	0	1	1	0
28	11	0	0	1	1	0
28	12	0	0	1	1	0
28	13	0	0	1	1	0
28	14	0	0	1	1	0
28	15	0	0	1	1	0
28	16	0	0	1	1	0
28	17	0	0	1	1	0
28	18	0	0	1	1	0
28	19	0	0	1	1	0
28	20	0	0	1	1	0
28	21	0	0	1	1	0
28	22	0	0	1	1	0
28	23	1	0	0	1	0
28	24	1	0	0	1	0
28	25	1	0	0	1	0
28	26	1	0	0	1	0
28	27	1	0	0	1	0
28	28	0	1	0	0	1
28	29	1	0	0	1	0
28	30	1	0	0	1	0
28	31	1	0	0	1	0
29	0	NULL	NULL	NULL	NULL	0
29	1	0	0	1	1	0
29	2	1	0	0	1	0
29	3	0	0	1	1	0
29	4	1	0	0	1	0
29	5	0	0	1	1	0
29	6	1	0	0	1	0
29	7	1	0	0	1	0
29	8	0	0	1	1	0
29	9	0	0	1	1	0
29	10	0	0	1	1	0
29	11	0	0	1	1	0
29	12	0	0	1	1	0
29	13	0	0	1	1	0
29	14	0	0	1	1	0
29	15	0	0	1	1	0
29	16	0	0	1	1	0
29	17	0	0	1	1	0
29	18	0	0	1	1	0
29	19	0	0	1	1	0
29	20	0	0	1	1	0
29	21	0	0	1	1	0
29	22	0	0	1	1	0
29	23	0	1	0	0	1
29	24	1	0	0	1	0
29	25	1	0	0	1	0
29	26	1	0	0	1	0
29	27	1	0	0	1	0
29	28	0	0	1	1	0
29	29	0	1	0	0	1
29	30	1	0	0	1	0
29	31	1	0	0	1	0
30	0	NULL	NULL	NULL	NULL	0
30	1	0	0	1	1	0
30	2	0	0	1	1	0
30	3	0	0	1	1	0
30	4	0	0	1	1	0
30	5	0	0	1	1	0
30	6	0	0	1	1	0
30	7	0	0	1	1	0
30	8	0	0	1	1	0
30	9	0	0	1	1	0
30	10	0	0	1	1	0
30	11	0	0	1	1	0
30	12	0	0	1	1	0
30	13	0	0	1	1	0
30	14	0	0	1	1	0
30	15	0	0	1	1	0
30	16	0	0	1	1	0
30	17	0	0	1	1	0
30	18	0	0	1	1	0
30	19	0	0	1	1	0
30	20	0	0	1	1	0
30	21	0	0	1	1	0
30	22	0	0	1	1	0
30	23	0	0	1	1	0
30	24	0	0	1	1	0
30	25	0	0	1	1	0
30	26	0	0	1	1	0
30	27	0	0	1	1	0
30	28	0	0	1	1	0
30	29	0	0	1	1	0
30	30	0	1	0	0	1
30	31	1	0	0	1	0
31	0	NULL	NULL	NULL	NULL	0
31	1	0	0	1	1	0
31	2	0	0	1	1	0
31	3	0	0	1	1	0
31	4	0	0	1	1	0
31	5	0	0	1	1	0
31	6	0	0	1	1	0
31	7	0	0	1	1	0
31	8	0	0	1	1	0
31	9	0	0	1	1	0
31	10	0	0	1	1	0
31	11	0	0	1	1	0
31	12	0	0	1	1	0
31	13	0	0	1	1	0
31	14	0	0	1	1	0
31	15	0	0	1	1	0
31	16	0	0	1	1	0
31	17	0	0	1	1	0
31	18	0	0	1	1	0
31	19	0	0	1	1	0
31	20	0	0	1	1	0
31	21	0	0	1	1	0
31	22	0	0	1	1	0
31	23	0	0	1	1	0
31	24	0	0	1	1	0
31	25	0	0	1	1	0
31	26	0	0	1	1	0
31	27	0	0	1	1	0
31	28	0	0	1	1	0
31	29	0	0	1	1	0
31	30	0	0	1	1	0
31	31	0	1	0	0	1
# Verify that the index on the int column is not used when
# comparing the int column to a JSON column. The two columns
# should be compared using the JSON comparator.
CREATE TABLE t2(i int, j json);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
CREATE INDEX t2_i ON t2(i);
INSERT INTO t2 values (1, CAST(1 AS JSON));
INSERT INTO t2 values (1, CAST('"1"' AS JSON));
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
EXPLAIN SELECT * FROM t2 where i = j;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`i` AS `i`,`test`.`t2`.`j` AS `j` from `test`.`t2` where (`test`.`t2`.`i` = `test`.`t2`.`j`)
SELECT * FROM t2 where i = j;
i	j
1	1
DROP TABLE t2;
CREATE TABLE numbers(id INT NOT NULL AUTO_INCREMENT,
`rank` INT,
j JSON,
PRIMARY KEY(id));
INSERT INTO numbers(`rank`, j) VALUES
(1, '-1e100'),
(2, '-1e65'),
# smallest DECIMAL (negative with 65 digits)
(3, CAST(-99999999999999999999999999999999999999999999999999999999999999999 AS JSON)),
(4, CAST(-9223372036854776001 AS JSON)),
(5, CAST(-9223372036854776000 AS JSON)),
# closest DOUBLE approximation of the smallest SIGNED BIGINT
(5 /* same rank as previous */, '-9.223372036854776e18'),
(6, CAST(-9223372036854775999 AS JSON)),
(7, CAST(-9223372036854775809 AS JSON)),   # smallest SIGNED BIGINT - 1
(8, CAST(-9223372036854775808 AS JSON)),   # smallest SIGNED BIGINT
(9, CAST(-9223372036854775807 AS JSON)),   # smallest SIGNED BIGINT + 1
(10, '-1e-50'),                  # close to zero, fits in a DECIMAL
(11, '-1.2345678901234e-71'),    # has to be truncated to fit in a DECIMAL
(12, CAST(-0.000000000000000000000000000000000000000000000000000000000000000000000012 AS JSON)),
(12 /* same rank as previous */, '-1.2e-71'),
(13, '-1.0345678901234e-71'),    # has to be truncated to fit in a DECIMAL
(14, '-1e-100'),                 # too close to zero to fit in a DECIMAL
(15, '0'),
(15 /* same rank as previous */, '0.0'),
(15 /* same rank as previous */, '-0.0'),
(15 /* same rank as previous */, CAST(0.0 AS JSON)),
(15 /* same rank as previous */, CAST(CAST(-0.0e0 AS DECIMAL) AS JSON)),
(16, '1e-100'),                  # too close to zero to fit in a DECIMAL
(17, '1.0345678901234e-71'),     # has to be truncated to fit in a DECIMAL
(18, CAST(0.000000000000000000000000000000000000000000000000000000000000000000000012 AS JSON)),
(18 /* same rank as previous */, '1.2e-71'),
(19, '1.2345678901234e-71'),     # has to be truncated to fit in a DECIMAL
(20, '1e-50'),                   # close to zero, fits in a DECIMAL
(21, CAST(9223372036854775806 AS JSON)),    # largest SIGNED BIGINT - 1
(22, CAST(9223372036854775807 AS JSON)),    # largest SIGNED BIGINT
(23, CAST(9223372036854775808 AS JSON)),    # largest SIGNED BIGINT + 1
(24, CAST(9223372036854775999 AS JSON)),
# closest DOUBLE approximation of the largest SIGNED BIGINT
(25, '9.223372036854776e18'),
(25 /* same rank as previous */, CAST(9223372036854776000 AS JSON)),
(26, CAST(9223372036854776001 AS JSON)),
(27, CAST(18446744073709551614 AS JSON)),   # largest UNSIGNED BIGINT - 1
(28, CAST(18446744073709551615 AS JSON)),   # largest UNSIGNED BIGINT
(29, CAST(18446744073709551616 AS JSON)),   # largest UNSIGNED BIGINT + 1
# Gets converted to the closest DOUBLE approximation of UNSIGNED BIGINT + 1
# by the JSON parser
(30, '18446744073709551616'),
# biggest DECIMAL (65 digits)
(31, CAST(99999999999999999999999999999999999999999999999999999999999999999 AS JSON)),
(32, CAST('1e65' AS JSON)),
(33, CAST('1e100' AS JSON));
SELECT *, JSON_TYPE(j) FROM numbers ORDER BY id;
id	rank	j	JSON_TYPE(j)
1	1	-1e100	DOUBLE
2	2	-1e65	DOUBLE
3	3	-99999999999999999999999999999999999999999999999999999999999999999	DECIMAL
4	4	-9223372036854776001	DECIMAL
5	5	-9223372036854776000	DECIMAL
6	5	-9.223372036854776e18	DOUBLE
7	6	-9223372036854775999	DECIMAL
8	7	-9223372036854775809	DECIMAL
9	8	-9223372036854775808	INTEGER
10	9	-9223372036854775807	INTEGER
11	10	-9.999999999999999e-51	DOUBLE
12	11	-1.2345678901233999e-71	DOUBLE
13	12	-0.000000000000000000000000000000000000000000000000000000000000000000000012	DECIMAL
14	12	-1.2e-71	DOUBLE
15	13	-1.0345678901234e-71	DOUBLE
16	14	-1e-100	DOUBLE
17	15	0	INTEGER
18	15	0.0	DOUBLE
19	15	-0.0	DOUBLE
20	15	0.0	DECIMAL
21	15	0	DECIMAL
22	16	1e-100	DOUBLE
23	17	1.0345678901234e-71	DOUBLE
24	18	0.000000000000000000000000000000000000000000000000000000000000000000000012	DECIMAL
25	18	1.2e-71	DOUBLE
26	19	1.2345678901233999e-71	DOUBLE
27	20	9.999999999999999e-51	DOUBLE
28	21	9223372036854775806	INTEGER
29	22	9223372036854775807	INTEGER
30	23	9223372036854775808	UNSIGNED INTEGER
31	24	9223372036854775999	UNSIGNED INTEGER
32	25	9.223372036854776e18	DOUBLE
33	25	9223372036854776000	UNSIGNED INTEGER
34	26	9223372036854776001	UNSIGNED INTEGER
35	27	18446744073709551614	UNSIGNED INTEGER
36	28	18446744073709551615	UNSIGNED INTEGER
37	29	18446744073709551616	DECIMAL
38	30	1.8446744073709552e19	DOUBLE
39	31	99999999999999999999999999999999999999999999999999999999999999999	DECIMAL
40	32	1e65	DOUBLE
41	33	1e100	DOUBLE
SELECT a.j, b.j, a.j < b.j, a.j = b.j, a.j > b.j, a.j <=> b.j
FROM numbers a, numbers b
WHERE ((a.j < b.j) <> (a.`rank` < b.`rank`)) OR
((a.j = b.j) <> (a.`rank` = b.`rank`)) OR
((a.j > b.j) <> (a.`rank` > b.`rank`)) OR
((a.j <=> b.j) <> (a.`rank` <=> b.`rank`));
j	j	a.j < b.j	a.j = b.j	a.j > b.j	a.j <=> b.j
DROP TABLE numbers;
CREATE TABLE t(txt TEXT);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES ('');
SELECT COUNT(*) FROM t WHERE JSON_EXTRACT(txt, '$') = 5;
ERROR 22032: Invalid JSON text in argument 1 to function json_extract: "The document is empty." at position 0.
SELECT COUNT(*) FROM t WHERE 5 = JSON_EXTRACT(txt, '$');
ERROR 22032: Invalid JSON text in argument 1 to function json_extract: "The document is empty." at position 0.
DROP TABLE t;
#
# WL#8539 - Ordering of scalar JSON values
#
CREATE TABLE timestamps (ts TIMESTAMP(6));
INSERT INTO timestamps VALUES
('2000-01-01 00:00:00'),
('2000-01-01 00:00:00.01'),
('2000-01-01 00:00:00.001'),
('2000-01-01 00:00:00.002'),
('2000-01-01 00:00:00.02'),
('2000-01-01 23:59:59.999999'),
('2000-01-02 00:00:00'),
('2000-02-01 00:00:00'),
('2010-12-02 01:00:00'),
('2010-12-02 01:02:00'),
('2010-12-02 01:02:03'),
('2010-12-02 02:01:00'),
('1970-01-02 00:00:01'),
('1970-01-02 00:00:01.000001');
SELECT * FROM timestamps ORDER BY CAST(ts AS JSON);
ts
1970-01-02 00:00:01.000000
1970-01-02 00:00:01.000001
2000-01-01 00:00:00.000000
2000-01-01 00:00:00.001000
2000-01-01 00:00:00.002000
2000-01-01 00:00:00.010000
2000-01-01 00:00:00.020000
2000-01-01 23:59:59.999999
2000-01-02 00:00:00.000000
2000-02-01 00:00:00.000000
2010-12-02 01:00:00.000000
2010-12-02 01:02:00.000000
2010-12-02 01:02:03.000000
2010-12-02 02:01:00.000000
CREATE TABLE datetimes (dt DATETIME(6));
INSERT INTO datetimes SELECT ts FROM timestamps;
INSERT INTO datetimes VALUES
('1960-01-02 03:04:05'),
('1960-01-02 03:04:06'),
('1000-01-01 00:00:00'),
('9999-12-31 23:59:59.999999');
SELECT * FROM datetimes ORDER BY CAST(dt AS JSON);
dt
1000-01-01 00:00:00.000000
1960-01-02 03:04:05.000000
1960-01-02 03:04:06.000000
1970-01-02 00:00:01.000000
1970-01-02 00:00:01.000001
2000-01-01 00:00:00.000000
2000-01-01 00:00:00.001000
2000-01-01 00:00:00.002000
2000-01-01 00:00:00.010000
2000-01-01 00:00:00.020000
2000-01-01 23:59:59.999999
2000-01-02 00:00:00.000000
2000-02-01 00:00:00.000000
2010-12-02 01:00:00.000000
2010-12-02 01:02:00.000000
2010-12-02 01:02:03.000000
2010-12-02 02:01:00.000000
9999-12-31 23:59:59.999999
CREATE TABLE times (t TIME(6));
INSERT INTO times SELECT DISTINCT TIME(dt) FROM datetimes;
INSERT INTO times VALUES
('-838:59:59'),
('838:59:59'),
('-00:00:00.000001'),
('-00:00:00'),
('24:00:00'),
('-12:00:00'),
('-24:00:00');
SELECT * FROM times ORDER BY CAST(t AS JSON);
t
-838:59:59.000000
-24:00:00.000000
-12:00:00.000000
-00:00:00.000001
00:00:00.000000
00:00:00.000000
00:00:00.001000
00:00:00.002000
00:00:00.010000
00:00:00.020000
00:00:01.000000
00:00:01.000001
01:00:00.000000
01:02:00.000000
01:02:03.000000
02:01:00.000000
03:04:05.000000
03:04:06.000000
23:59:59.999999
24:00:00.000000
838:59:59.000000
CREATE TABLE dates(d DATE);
INSERT INTO dates SELECT DISTINCT DATE(dt) FROM datetimes;
CREATE TABLE signed_integers(i BIGINT);
INSERT INTO signed_integers VALUES
(0), (1), (2), (3), (4), (5), (10), (11), (12), (20), (21), (22),
(99), (100), (101), (999), (1000), (1001),
(9223372036854775806), (9223372036854775807);
INSERT INTO signed_integers SELECT -i FROM signed_integers;
INSERT INTO signed_integers VALUES (-9223372036854775808);
SELECT * FROM signed_integers ORDER BY CAST(i AS JSON);
i
-9223372036854775808
-9223372036854775807
-9223372036854775806
-1001
-1000
-999
-101
-100
-99
-22
-21
-20
-12
-11
-10
-5
-4
-3
-2
-1
0
0
1
2
3
4
5
10
11
12
20
21
22
99
100
101
999
1000
1001
9223372036854775806
9223372036854775807
CREATE TABLE unsigned_integers(i BIGINT UNSIGNED);
INSERT INTO unsigned_integers SELECT i FROM signed_integers where i >= 0;
INSERT INTO unsigned_integers VALUES
(9223372036854775808), (18446744073709551614), (18446744073709551615);
SELECT * FROM unsigned_integers ORDER BY CAST(i AS JSON);
i
0
0
1
2
3
4
5
10
11
12
20
21
22
99
100
101
999
1000
1001
9223372036854775806
9223372036854775807
9223372036854775808
18446744073709551614
18446744073709551615
CREATE TABLE decimals (d DECIMAL(25,3));
INSERT INTO decimals SELECT i FROM signed_integers;
INSERT INTO decimals SELECT i FROM unsigned_integers;
INSERT INTO decimals VALUES
(9223372036854776000), (-9223372036854776000),
(9223372036854776001), (-9223372036854776001),
(3.13), (3.14), (3.15), (-3.13), (-3.14), (-3.15),
(3.131), (3.141), (3.151), (-3.131), (-3.141), (-3.151),
(3.129), (3.139), (3.149), (-3.129), (-3.139), (-3.149),
(0.1), (0.01), (0.001), (-0.1), (-0.01), (-0.001);
SELECT * FROM decimals ORDER BY CAST(d AS JSON);
d
-9223372036854776001.000
-9223372036854776000.000
-9223372036854775808.000
-9223372036854775807.000
-9223372036854775806.000
-1001.000
-1000.000
-999.000
-101.000
-100.000
-99.000
-22.000
-21.000
-20.000
-12.000
-11.000
-10.000
-5.000
-4.000
-3.151
-3.150
-3.149
-3.141
-3.140
-3.139
-3.131
-3.130
-3.129
-3.000
-2.000
-1.000
-0.100
-0.010
-0.001
0.000
0.000
0.000
0.000
0.001
0.010
0.100
1.000
1.000
2.000
2.000
3.000
3.000
3.129
3.130
3.131
3.139
3.140
3.141
3.149
3.150
3.151
4.000
4.000
5.000
5.000
10.000
10.000
11.000
11.000
12.000
12.000
20.000
20.000
21.000
21.000
22.000
22.000
99.000
99.000
100.000
100.000
101.000
101.000
999.000
999.000
1000.000
1000.000
1001.000
1001.000
9223372036854775806.000
9223372036854775806.000
9223372036854775807.000
9223372036854775807.000
9223372036854775808.000
9223372036854776000.000
9223372036854776001.000
18446744073709551614.000
18446744073709551615.000
CREATE TABLE doubles (d DOUBLE);
INSERT INTO doubles SELECT d FROM decimals;
INSERT INTO doubles VALUES
(1.5E-200), (1.5E200), (-1.5E-200), (-1.5E200),
(-1E-323), (-1E-322), (-1E-321), (1E-323), (1E-322), (1E-321),
(-1E308), (-1E307), (-1E306), (1E308), (1E307), (1E306);
SELECT * FROM doubles ORDER BY CAST(d AS JSON);
d
-1e308
-1e307
-1e306
-1.5e200
-9.223372036854776e18
-9.223372036854776e18
-9.223372036854776e18
-9.223372036854776e18
-9.223372036854776e18
-1001
-1000
-999
-101
-100
-99
-22
-21
-20
-12
-11
-10
-5
-4
-3.151
-3.15
-3.149
-3.141
-3.14
-3.139
-3.131
-3.13
-3.129
-3
-2
-1
-0.1
-0.01
-0.001
-1.5e-200
-1e-321
-1e-322
-1e-323
0
0
0
0
1e-323
1e-322
1e-321
1.5e-200
0.001
0.01
0.1
1
1
2
2
3
3
3.129
3.13
3.131
3.139
3.14
3.141
3.149
3.15
3.151
4
4
5
5
10
10
11
11
12
12
20
20
21
21
22
22
99
99
100
100
101
101
999
999
1000
1000
1001
1001
9.223372036854776e18
9.223372036854776e18
9.223372036854776e18
9.223372036854776e18
9.223372036854776e18
9.223372036854776e18
9.223372036854776e18
1.8446744073709552e19
1.8446744073709552e19
1.5e200
1e306
1e307
1e308
CREATE TABLE t(id INT PRIMARY KEY AUTO_INCREMENT, j JSON);
INSERT INTO t(j) SELECT CAST(ts AS JSON) FROM timestamps ORDER BY ts;
INSERT INTO t(j) SELECT CAST(dt AS JSON) FROM datetimes ORDER BY dt;
INSERT INTO t(j) SELECT CAST(t AS JSON) FROM times ORDER BY t;
INSERT INTO t(j) SELECT CAST(d AS JSON) FROM dates ORDER BY d;
INSERT INTO t(j) SELECT CAST(i AS JSON) FROM signed_integers ORDER BY i;
INSERT INTO t(j) SELECT CAST(i AS JSON) FROM unsigned_integers ORDER BY i;
INSERT INTO t(j) SELECT CAST(d AS JSON) FROM decimals ORDER BY d;
INSERT INTO t(j) SELECT CAST(d AS JSON) FROM doubles ORDER BY d;
INSERT INTO t(j) VALUES
(NULL), (NULL), ('true'), ('false'), ('null'),
('"abc"'), ('""'), ('"abcd"'), ('"bc"'),
('"abc\\u0000\\u0000"'), ('"abc\\u0000"'),
('0.0'), ('-0.0'), ('9223372036854776000'),
('1.0e-1'), ('1.0e-2'),
(CAST(0.000000000000001 AS JSON)),
(CAST(0.00000000000000115 AS JSON)),
(CAST(0.0000000000000001 AS JSON)),
(CAST(0.000000000000000116 AS JSON)),
(CAST(0.0 AS JSON)),
(CAST(-999999999999999999999999999999999999999999999999999999999999999999999999999999999 AS JSON)),
(CAST(-999999999999999999999999999999999999999999999999999999999999999999999999999999998 AS JSON)),
(CAST(-999999999999999999999999999999999999999999999999999999999999999999999999999999997 AS JSON)),
(CAST(999999999999999999999999999999999999999999999999999999999999999999999999999999997 AS JSON)),
(CAST(999999999999999999999999999999999999999999999999999999999999999999999999999999998 AS JSON)),
(CAST(999999999999999999999999999999999999999999999999999999999999999999999999999999999 AS JSON)),
(CAST(-1E81 AS JSON)),
(CAST(-9.99E80 AS JSON)),
(CAST(9.99E80 AS JSON)),
(CAST(1E81 AS JSON)),
(JSON_ARRAY('an array')),
(JSON_ARRAY('another array')),
(JSON_OBJECT('an', 'object')),
(JSON_OBJECT('another', 'object')),
(CAST(ST_GeomFromText('POINT(0 0)') AS JSON)),
(CAST(ST_GeomFromText('POINT(0 1)') AS JSON)),
(CAST(CAST('1234abcd' AS BINARY) AS JSON));
SELECT j, JSON_TYPE(j) AS tp FROM t ORDER BY j, id;
j	tp
NULL	NULL
NULL	NULL
null	NULL
-1e308	DOUBLE
-1e307	DOUBLE
-1e306	DOUBLE
-1.5e200	DOUBLE
-1e81	DOUBLE
-999999999999999999999999999999999999999999999999999999999999999999999999999999999	DECIMAL
-999999999999999999999999999999999999999999999999999999999999999999999999999999998	DECIMAL
-999999999999999999999999999999999999999999999999999999999999999999999999999999997	DECIMAL
-9.99e80	DOUBLE
-9223372036854776001.000	DECIMAL
-9223372036854776000.000	DECIMAL
-9.223372036854776e18	DOUBLE
-9.223372036854776e18	DOUBLE
-9.223372036854776e18	DOUBLE
-9.223372036854776e18	DOUBLE
-9.223372036854776e18	DOUBLE
-9223372036854775808	INTEGER
-9223372036854775808.000	DECIMAL
-9223372036854775807	INTEGER
-9223372036854775807.000	DECIMAL
-9223372036854775806	INTEGER
-9223372036854775806.000	DECIMAL
-1001	INTEGER
-1001.000	DECIMAL
-1001.0	DOUBLE
-1000	INTEGER
-1000.000	DECIMAL
-1000.0	DOUBLE
-999	INTEGER
-999.000	DECIMAL
-999.0	DOUBLE
-101	INTEGER
-101.000	DECIMAL
-101.0	DOUBLE
-100	INTEGER
-100.000	DECIMAL
-100.0	DOUBLE
-99	INTEGER
-99.000	DECIMAL
-99.0	DOUBLE
-22	INTEGER
-22.000	DECIMAL
-22.0	DOUBLE
-21	INTEGER
-21.000	DECIMAL
-21.0	DOUBLE
-20	INTEGER
-20.000	DECIMAL
-20.0	DOUBLE
-12	INTEGER
-12.000	DECIMAL
-12.0	DOUBLE
-11	INTEGER
-11.000	DECIMAL
-11.0	DOUBLE
-10	INTEGER
-10.000	DECIMAL
-10.0	DOUBLE
-5	INTEGER
-5.000	DECIMAL
-5.0	DOUBLE
-4	INTEGER
-4.000	DECIMAL
-4.0	DOUBLE
-3.151	DECIMAL
-3.151	DOUBLE
-3.150	DECIMAL
-3.15	DOUBLE
-3.149	DECIMAL
-3.149	DOUBLE
-3.141	DECIMAL
-3.141	DOUBLE
-3.140	DECIMAL
-3.14	DOUBLE
-3.139	DECIMAL
-3.139	DOUBLE
-3.131	DECIMAL
-3.131	DOUBLE
-3.130	DECIMAL
-3.13	DOUBLE
-3.129	DECIMAL
-3.129	DOUBLE
-3	INTEGER
-3.000	DECIMAL
-3.0	DOUBLE
-2	INTEGER
-2.000	DECIMAL
-2.0	DOUBLE
-1	INTEGER
-1.000	DECIMAL
-1.0	DOUBLE
-0.100	DECIMAL
-0.1	DOUBLE
-0.010	DECIMAL
-0.01	DOUBLE
-0.001	DECIMAL
-0.001	DOUBLE
-1.5e-200	DOUBLE
-1e-321	DOUBLE
-1e-322	DOUBLE
-1e-323	DOUBLE
0	INTEGER
0	INTEGER
0	UNSIGNED INTEGER
0	UNSIGNED INTEGER
0.000	DECIMAL
0.000	DECIMAL
0.000	DECIMAL
0.000	DECIMAL
0.0	DOUBLE
0.0	DOUBLE
0.0	DOUBLE
0.0	DOUBLE
0.0	DOUBLE
-0.0	DOUBLE
0.0	DECIMAL
1e-323	DOUBLE
1e-322	DOUBLE
1e-321	DOUBLE
1.5e-200	DOUBLE
0.0000000000000001	DECIMAL
0.000000000000000116	DECIMAL
0.000000000000001	DECIMAL
0.00000000000000115	DECIMAL
0.001	DECIMAL
0.001	DOUBLE
0.010	DECIMAL
0.01	DOUBLE
0.01	DOUBLE
0.100	DECIMAL
0.1	DOUBLE
0.1	DOUBLE
1	INTEGER
1	UNSIGNED INTEGER
1.000	DECIMAL
1.000	DECIMAL
1.0	DOUBLE
1.0	DOUBLE
2	INTEGER
2	UNSIGNED INTEGER
2.000	DECIMAL
2.000	DECIMAL
2.0	DOUBLE
2.0	DOUBLE
3	INTEGER
3	UNSIGNED INTEGER
3.000	DECIMAL
3.000	DECIMAL
3.0	DOUBLE
3.0	DOUBLE
3.129	DECIMAL
3.129	DOUBLE
3.130	DECIMAL
3.13	DOUBLE
3.131	DECIMAL
3.131	DOUBLE
3.139	DECIMAL
3.139	DOUBLE
3.140	DECIMAL
3.14	DOUBLE
3.141	DECIMAL
3.141	DOUBLE
3.149	DECIMAL
3.149	DOUBLE
3.150	DECIMAL
3.15	DOUBLE
3.151	DECIMAL
3.151	DOUBLE
4	INTEGER
4	UNSIGNED INTEGER
4.000	DECIMAL
4.000	DECIMAL
4.0	DOUBLE
4.0	DOUBLE
5	INTEGER
5	UNSIGNED INTEGER
5.000	DECIMAL
5.000	DECIMAL
5.0	DOUBLE
5.0	DOUBLE
10	INTEGER
10	UNSIGNED INTEGER
10.000	DECIMAL
10.000	DECIMAL
10.0	DOUBLE
10.0	DOUBLE
11	INTEGER
11	UNSIGNED INTEGER
11.000	DECIMAL
11.000	DECIMAL
11.0	DOUBLE
11.0	DOUBLE
12	INTEGER
12	UNSIGNED INTEGER
12.000	DECIMAL
12.000	DECIMAL
12.0	DOUBLE
12.0	DOUBLE
20	INTEGER
20	UNSIGNED INTEGER
20.000	DECIMAL
20.000	DECIMAL
20.0	DOUBLE
20.0	DOUBLE
21	INTEGER
21	UNSIGNED INTEGER
21.000	DECIMAL
21.000	DECIMAL
21.0	DOUBLE
21.0	DOUBLE
22	INTEGER
22	UNSIGNED INTEGER
22.000	DECIMAL
22.000	DECIMAL
22.0	DOUBLE
22.0	DOUBLE
99	INTEGER
99	UNSIGNED INTEGER
99.000	DECIMAL
99.000	DECIMAL
99.0	DOUBLE
99.0	DOUBLE
100	INTEGER
100	UNSIGNED INTEGER
100.000	DECIMAL
100.000	DECIMAL
100.0	DOUBLE
100.0	DOUBLE
101	INTEGER
101	UNSIGNED INTEGER
101.000	DECIMAL
101.000	DECIMAL
101.0	DOUBLE
101.0	DOUBLE
999	INTEGER
999	UNSIGNED INTEGER
999.000	DECIMAL
999.000	DECIMAL
999.0	DOUBLE
999.0	DOUBLE
1000	INTEGER
1000	UNSIGNED INTEGER
1000.000	DECIMAL
1000.000	DECIMAL
1000.0	DOUBLE
1000.0	DOUBLE
1001	INTEGER
1001	UNSIGNED INTEGER
1001.000	DECIMAL
1001.000	DECIMAL
1001.0	DOUBLE
1001.0	DOUBLE
9223372036854775806	INTEGER
9223372036854775806	UNSIGNED INTEGER
9223372036854775806.000	DECIMAL
9223372036854775806.000	DECIMAL
9223372036854775807	INTEGER
9223372036854775807	UNSIGNED INTEGER
9223372036854775807.000	DECIMAL
9223372036854775807.000	DECIMAL
9223372036854775808	UNSIGNED INTEGER
9223372036854775808.000	DECIMAL
9223372036854776000.000	DECIMAL
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9223372036854776000	UNSIGNED INTEGER
9223372036854776001.000	DECIMAL
18446744073709551614	UNSIGNED INTEGER
18446744073709551614.000	DECIMAL
18446744073709551615	UNSIGNED INTEGER
18446744073709551615.000	DECIMAL
1.8446744073709552e19	DOUBLE
1.8446744073709552e19	DOUBLE
9.99e80	DOUBLE
999999999999999999999999999999999999999999999999999999999999999999999999999999997	DECIMAL
999999999999999999999999999999999999999999999999999999999999999999999999999999998	DECIMAL
999999999999999999999999999999999999999999999999999999999999999999999999999999999	DECIMAL
1e81	DOUBLE
1.5e200	DOUBLE
1e306	DOUBLE
1e307	DOUBLE
1e308	DOUBLE
""	STRING
"abc"	STRING
"abc\u0000"	STRING
"abc\u0000\u0000"	STRING
"abcd"	STRING
"bc"	STRING
{"an": "object"}	OBJECT
{"another": "object"}	OBJECT
{"type": "Point", "coordinates": [0.0, 0.0]}	OBJECT
{"type": "Point", "coordinates": [0.0, 1.0]}	OBJECT
["an array"]	ARRAY
["another array"]	ARRAY
false	BOOLEAN
true	BOOLEAN
"1000-01-01"	DATE
"1960-01-02"	DATE
"1970-01-02"	DATE
"2000-01-01"	DATE
"2000-01-02"	DATE
"2000-02-01"	DATE
"2010-12-02"	DATE
"9999-12-31"	DATE
"-630:59:59.000000"	TIME
"-24:00:00.000000"	TIME
"-12:00:00.000000"	TIME
"-00:00:00.000001"	TIME
"00:00:00.000000"	TIME
"00:00:00.000000"	TIME
"00:00:00.001000"	TIME
"00:00:00.002000"	TIME
"00:00:00.010000"	TIME
"00:00:00.020000"	TIME
"00:00:01.000000"	TIME
"00:00:01.000001"	TIME
"01:00:00.000000"	TIME
"01:02:00.000000"	TIME
"01:02:03.000000"	TIME
"02:01:00.000000"	TIME
"03:04:05.000000"	TIME
"03:04:06.000000"	TIME
"23:59:59.999999"	TIME
"24:00:00.000000"	TIME
"630:59:59.000000"	TIME
"1000-01-01 00:00:00.000000"	DATETIME
"1960-01-02 03:04:05.000000"	DATETIME
"1960-01-02 03:04:06.000000"	DATETIME
"1970-01-02 00:00:01.000000"	TIMESTAMP
"1970-01-02 00:00:01.000000"	DATETIME
"1970-01-02 00:00:01.000001"	TIMESTAMP
"1970-01-02 00:00:01.000001"	DATETIME
"2000-01-01 00:00:00.000000"	TIMESTAMP
"2000-01-01 00:00:00.000000"	DATETIME
"2000-01-01 00:00:00.001000"	TIMESTAMP
"2000-01-01 00:00:00.001000"	DATETIME
"2000-01-01 00:00:00.002000"	TIMESTAMP
"2000-01-01 00:00:00.002000"	DATETIME
"2000-01-01 00:00:00.010000"	TIMESTAMP
"2000-01-01 00:00:00.010000"	DATETIME
"2000-01-01 00:00:00.020000"	TIMESTAMP
"2000-01-01 00:00:00.020000"	DATETIME
"2000-01-01 23:59:59.999999"	TIMESTAMP
"2000-01-01 23:59:59.999999"	DATETIME
"2000-01-02 00:00:00.000000"	TIMESTAMP
"2000-01-02 00:00:00.000000"	DATETIME
"2000-02-01 00:00:00.000000"	TIMESTAMP
"2000-02-01 00:00:00.000000"	DATETIME
"2010-12-02 01:00:00.000000"	TIMESTAMP
"2010-12-02 01:00:00.000000"	DATETIME
"2010-12-02 01:02:00.000000"	TIMESTAMP
"2010-12-02 01:02:00.000000"	DATETIME
"2010-12-02 01:02:03.000000"	TIMESTAMP
"2010-12-02 01:02:03.000000"	DATETIME
"2010-12-02 02:01:00.000000"	TIMESTAMP
"2010-12-02 02:01:00.000000"	DATETIME
"9999-12-31 23:59:59.999999"	DATETIME
"base64:type15:MTIzNGFiY2Q="	BLOB
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
SELECT j, JSON_TYPE(j) AS tp FROM t ORDER BY j, id limit 2 offset 2;
j	tp
null	NULL
-1e308	DOUBLE
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
SELECT j, JSON_TYPE(j) AS tp FROM t ORDER BY j DESC, id;
j	tp
"base64:type15:MTIzNGFiY2Q="	BLOB
"9999-12-31 23:59:59.999999"	DATETIME
"2010-12-02 02:01:00.000000"	TIMESTAMP
"2010-12-02 02:01:00.000000"	DATETIME
"2010-12-02 01:02:03.000000"	TIMESTAMP
"2010-12-02 01:02:03.000000"	DATETIME
"2010-12-02 01:02:00.000000"	TIMESTAMP
"2010-12-02 01:02:00.000000"	DATETIME
"2010-12-02 01:00:00.000000"	TIMESTAMP
"2010-12-02 01:00:00.000000"	DATETIME
"2000-02-01 00:00:00.000000"	TIMESTAMP
"2000-02-01 00:00:00.000000"	DATETIME
"2000-01-02 00:00:00.000000"	TIMESTAMP
"2000-01-02 00:00:00.000000"	DATETIME
"2000-01-01 23:59:59.999999"	TIMESTAMP
"2000-01-01 23:59:59.999999"	DATETIME
"2000-01-01 00:00:00.020000"	TIMESTAMP
"2000-01-01 00:00:00.020000"	DATETIME
"2000-01-01 00:00:00.010000"	TIMESTAMP
"2000-01-01 00:00:00.010000"	DATETIME
"2000-01-01 00:00:00.002000"	TIMESTAMP
"2000-01-01 00:00:00.002000"	DATETIME
"2000-01-01 00:00:00.001000"	TIMESTAMP
"2000-01-01 00:00:00.001000"	DATETIME
"2000-01-01 00:00:00.000000"	TIMESTAMP
"2000-01-01 00:00:00.000000"	DATETIME
"1970-01-02 00:00:01.000001"	TIMESTAMP
"1970-01-02 00:00:01.000001"	DATETIME
"1970-01-02 00:00:01.000000"	TIMESTAMP
"1970-01-02 00:00:01.000000"	DATETIME
"1960-01-02 03:04:06.000000"	DATETIME
"1960-01-02 03:04:05.000000"	DATETIME
"1000-01-01 00:00:00.000000"	DATETIME
"630:59:59.000000"	TIME
"24:00:00.000000"	TIME
"23:59:59.999999"	TIME
"03:04:06.000000"	TIME
"03:04:05.000000"	TIME
"02:01:00.000000"	TIME
"01:02:03.000000"	TIME
"01:02:00.000000"	TIME
"01:00:00.000000"	TIME
"00:00:01.000001"	TIME
"00:00:01.000000"	TIME
"00:00:00.020000"	TIME
"00:00:00.010000"	TIME
"00:00:00.002000"	TIME
"00:00:00.001000"	TIME
"00:00:00.000000"	TIME
"00:00:00.000000"	TIME
"-00:00:00.000001"	TIME
"-12:00:00.000000"	TIME
"-24:00:00.000000"	TIME
"-630:59:59.000000"	TIME
"9999-12-31"	DATE
"2010-12-02"	DATE
"2000-02-01"	DATE
"2000-01-02"	DATE
"2000-01-01"	DATE
"1970-01-02"	DATE
"1960-01-02"	DATE
"1000-01-01"	DATE
true	BOOLEAN
false	BOOLEAN
["an array"]	ARRAY
["another array"]	ARRAY
{"type": "Point", "coordinates": [0.0, 0.0]}	OBJECT
{"type": "Point", "coordinates": [0.0, 1.0]}	OBJECT
{"an": "object"}	OBJECT
{"another": "object"}	OBJECT
"bc"	STRING
"abcd"	STRING
"abc\u0000\u0000"	STRING
"abc\u0000"	STRING
"abc"	STRING
""	STRING
1e308	DOUBLE
1e307	DOUBLE
1e306	DOUBLE
1.5e200	DOUBLE
1e81	DOUBLE
999999999999999999999999999999999999999999999999999999999999999999999999999999999	DECIMAL
999999999999999999999999999999999999999999999999999999999999999999999999999999998	DECIMAL
999999999999999999999999999999999999999999999999999999999999999999999999999999997	DECIMAL
9.99e80	DOUBLE
1.8446744073709552e19	DOUBLE
1.8446744073709552e19	DOUBLE
18446744073709551615	UNSIGNED INTEGER
18446744073709551615.000	DECIMAL
18446744073709551614	UNSIGNED INTEGER
18446744073709551614.000	DECIMAL
9223372036854776001.000	DECIMAL
9223372036854776000.000	DECIMAL
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9223372036854776000	UNSIGNED INTEGER
9223372036854775808	UNSIGNED INTEGER
9223372036854775808.000	DECIMAL
9223372036854775807	INTEGER
9223372036854775807	UNSIGNED INTEGER
9223372036854775807.000	DECIMAL
9223372036854775807.000	DECIMAL
9223372036854775806	INTEGER
9223372036854775806	UNSIGNED INTEGER
9223372036854775806.000	DECIMAL
9223372036854775806.000	DECIMAL
1001	INTEGER
1001	UNSIGNED INTEGER
1001.000	DECIMAL
1001.000	DECIMAL
1001.0	DOUBLE
1001.0	DOUBLE
1000	INTEGER
1000	UNSIGNED INTEGER
1000.000	DECIMAL
1000.000	DECIMAL
1000.0	DOUBLE
1000.0	DOUBLE
999	INTEGER
999	UNSIGNED INTEGER
999.000	DECIMAL
999.000	DECIMAL
999.0	DOUBLE
999.0	DOUBLE
101	INTEGER
101	UNSIGNED INTEGER
101.000	DECIMAL
101.000	DECIMAL
101.0	DOUBLE
101.0	DOUBLE
100	INTEGER
100	UNSIGNED INTEGER
100.000	DECIMAL
100.000	DECIMAL
100.0	DOUBLE
100.0	DOUBLE
99	INTEGER
99	UNSIGNED INTEGER
99.000	DECIMAL
99.000	DECIMAL
99.0	DOUBLE
99.0	DOUBLE
22	INTEGER
22	UNSIGNED INTEGER
22.000	DECIMAL
22.000	DECIMAL
22.0	DOUBLE
22.0	DOUBLE
21	INTEGER
21	UNSIGNED INTEGER
21.000	DECIMAL
21.000	DECIMAL
21.0	DOUBLE
21.0	DOUBLE
20	INTEGER
20	UNSIGNED INTEGER
20.000	DECIMAL
20.000	DECIMAL
20.0	DOUBLE
20.0	DOUBLE
12	INTEGER
12	UNSIGNED INTEGER
12.000	DECIMAL
12.000	DECIMAL
12.0	DOUBLE
12.0	DOUBLE
11	INTEGER
11	UNSIGNED INTEGER
11.000	DECIMAL
11.000	DECIMAL
11.0	DOUBLE
11.0	DOUBLE
10	INTEGER
10	UNSIGNED INTEGER
10.000	DECIMAL
10.000	DECIMAL
10.0	DOUBLE
10.0	DOUBLE
5	INTEGER
5	UNSIGNED INTEGER
5.000	DECIMAL
5.000	DECIMAL
5.0	DOUBLE
5.0	DOUBLE
4	INTEGER
4	UNSIGNED INTEGER
4.000	DECIMAL
4.000	DECIMAL
4.0	DOUBLE
4.0	DOUBLE
3.151	DECIMAL
3.151	DOUBLE
3.150	DECIMAL
3.15	DOUBLE
3.149	DECIMAL
3.149	DOUBLE
3.141	DECIMAL
3.141	DOUBLE
3.140	DECIMAL
3.14	DOUBLE
3.139	DECIMAL
3.139	DOUBLE
3.131	DECIMAL
3.131	DOUBLE
3.130	DECIMAL
3.13	DOUBLE
3.129	DECIMAL
3.129	DOUBLE
3	INTEGER
3	UNSIGNED INTEGER
3.000	DECIMAL
3.000	DECIMAL
3.0	DOUBLE
3.0	DOUBLE
2	INTEGER
2	UNSIGNED INTEGER
2.000	DECIMAL
2.000	DECIMAL
2.0	DOUBLE
2.0	DOUBLE
1	INTEGER
1	UNSIGNED INTEGER
1.000	DECIMAL
1.000	DECIMAL
1.0	DOUBLE
1.0	DOUBLE
0.100	DECIMAL
0.1	DOUBLE
0.1	DOUBLE
0.010	DECIMAL
0.01	DOUBLE
0.01	DOUBLE
0.001	DECIMAL
0.001	DOUBLE
0.00000000000000115	DECIMAL
0.000000000000001	DECIMAL
0.000000000000000116	DECIMAL
0.0000000000000001	DECIMAL
1.5e-200	DOUBLE
1e-321	DOUBLE
1e-322	DOUBLE
1e-323	DOUBLE
0	INTEGER
0	INTEGER
0	UNSIGNED INTEGER
0	UNSIGNED INTEGER
0.000	DECIMAL
0.000	DECIMAL
0.000	DECIMAL
0.000	DECIMAL
0.0	DOUBLE
0.0	DOUBLE
0.0	DOUBLE
0.0	DOUBLE
0.0	DOUBLE
-0.0	DOUBLE
0.0	DECIMAL
-1e-323	DOUBLE
-1e-322	DOUBLE
-1e-321	DOUBLE
-1.5e-200	DOUBLE
-0.001	DECIMAL
-0.001	DOUBLE
-0.010	DECIMAL
-0.01	DOUBLE
-0.100	DECIMAL
-0.1	DOUBLE
-1	INTEGER
-1.000	DECIMAL
-1.0	DOUBLE
-2	INTEGER
-2.000	DECIMAL
-2.0	DOUBLE
-3	INTEGER
-3.000	DECIMAL
-3.0	DOUBLE
-3.129	DECIMAL
-3.129	DOUBLE
-3.130	DECIMAL
-3.13	DOUBLE
-3.131	DECIMAL
-3.131	DOUBLE
-3.139	DECIMAL
-3.139	DOUBLE
-3.140	DECIMAL
-3.14	DOUBLE
-3.141	DECIMAL
-3.141	DOUBLE
-3.149	DECIMAL
-3.149	DOUBLE
-3.150	DECIMAL
-3.15	DOUBLE
-3.151	DECIMAL
-3.151	DOUBLE
-4	INTEGER
-4.000	DECIMAL
-4.0	DOUBLE
-5	INTEGER
-5.000	DECIMAL
-5.0	DOUBLE
-10	INTEGER
-10.000	DECIMAL
-10.0	DOUBLE
-11	INTEGER
-11.000	DECIMAL
-11.0	DOUBLE
-12	INTEGER
-12.000	DECIMAL
-12.0	DOUBLE
-20	INTEGER
-20.000	DECIMAL
-20.0	DOUBLE
-21	INTEGER
-21.000	DECIMAL
-21.0	DOUBLE
-22	INTEGER
-22.000	DECIMAL
-22.0	DOUBLE
-99	INTEGER
-99.000	DECIMAL
-99.0	DOUBLE
-100	INTEGER
-100.000	DECIMAL
-100.0	DOUBLE
-101	INTEGER
-101.000	DECIMAL
-101.0	DOUBLE
-999	INTEGER
-999.000	DECIMAL
-999.0	DOUBLE
-1000	INTEGER
-1000.000	DECIMAL
-1000.0	DOUBLE
-1001	INTEGER
-1001.000	DECIMAL
-1001.0	DOUBLE
-9223372036854775806	INTEGER
-9223372036854775806.000	DECIMAL
-9223372036854775807	INTEGER
-9223372036854775807.000	DECIMAL
-9223372036854775808	INTEGER
-9223372036854775808.000	DECIMAL
-9223372036854776000.000	DECIMAL
-9.223372036854776e18	DOUBLE
-9.223372036854776e18	DOUBLE
-9.223372036854776e18	DOUBLE
-9.223372036854776e18	DOUBLE
-9.223372036854776e18	DOUBLE
-9223372036854776001.000	DECIMAL
-9.99e80	DOUBLE
-999999999999999999999999999999999999999999999999999999999999999999999999999999997	DECIMAL
-999999999999999999999999999999999999999999999999999999999999999999999999999999998	DECIMAL
-999999999999999999999999999999999999999999999999999999999999999999999999999999999	DECIMAL
-1e81	DOUBLE
-1.5e200	DOUBLE
-1e306	DOUBLE
-1e307	DOUBLE
-1e308	DOUBLE
null	NULL
NULL	NULL
NULL	NULL
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
SELECT j, JSON_TYPE(j) AS tp FROM t ORDER BY j DESC, id limit 2 offset 2;
j	tp
"2010-12-02 02:01:00.000000"	TIMESTAMP
"2010-12-02 02:01:00.000000"	DATETIME
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
SELECT JSON_EXTRACT(j, '$') AS je, JSON_TYPE(j) AS tp FROM t ORDER BY je, id;
je	tp
NULL	NULL
NULL	NULL
null	NULL
-1e308	DOUBLE
-1e307	DOUBLE
-1e306	DOUBLE
-1.5e200	DOUBLE
-1e81	DOUBLE
-999999999999999999999999999999999999999999999999999999999999999999999999999999999	DECIMAL
-999999999999999999999999999999999999999999999999999999999999999999999999999999998	DECIMAL
-999999999999999999999999999999999999999999999999999999999999999999999999999999997	DECIMAL
-9.99e80	DOUBLE
-9223372036854776001.000	DECIMAL
-9223372036854776000.000	DECIMAL
-9.223372036854776e18	DOUBLE
-9.223372036854776e18	DOUBLE
-9.223372036854776e18	DOUBLE
-9.223372036854776e18	DOUBLE
-9.223372036854776e18	DOUBLE
-9223372036854775808	INTEGER
-9223372036854775808.000	DECIMAL
-9223372036854775807	INTEGER
-9223372036854775807.000	DECIMAL
-9223372036854775806	INTEGER
-9223372036854775806.000	DECIMAL
-1001	INTEGER
-1001.000	DECIMAL
-1001.0	DOUBLE
-1000	INTEGER
-1000.000	DECIMAL
-1000.0	DOUBLE
-999	INTEGER
-999.000	DECIMAL
-999.0	DOUBLE
-101	INTEGER
-101.000	DECIMAL
-101.0	DOUBLE
-100	INTEGER
-100.000	DECIMAL
-100.0	DOUBLE
-99	INTEGER
-99.000	DECIMAL
-99.0	DOUBLE
-22	INTEGER
-22.000	DECIMAL
-22.0	DOUBLE
-21	INTEGER
-21.000	DECIMAL
-21.0	DOUBLE
-20	INTEGER
-20.000	DECIMAL
-20.0	DOUBLE
-12	INTEGER
-12.000	DECIMAL
-12.0	DOUBLE
-11	INTEGER
-11.000	DECIMAL
-11.0	DOUBLE
-10	INTEGER
-10.000	DECIMAL
-10.0	DOUBLE
-5	INTEGER
-5.000	DECIMAL
-5.0	DOUBLE
-4	INTEGER
-4.000	DECIMAL
-4.0	DOUBLE
-3.151	DECIMAL
-3.151	DOUBLE
-3.150	DECIMAL
-3.15	DOUBLE
-3.149	DECIMAL
-3.149	DOUBLE
-3.141	DECIMAL
-3.141	DOUBLE
-3.140	DECIMAL
-3.14	DOUBLE
-3.139	DECIMAL
-3.139	DOUBLE
-3.131	DECIMAL
-3.131	DOUBLE
-3.130	DECIMAL
-3.13	DOUBLE
-3.129	DECIMAL
-3.129	DOUBLE
-3	INTEGER
-3.000	DECIMAL
-3.0	DOUBLE
-2	INTEGER
-2.000	DECIMAL
-2.0	DOUBLE
-1	INTEGER
-1.000	DECIMAL
-1.0	DOUBLE
-0.100	DECIMAL
-0.1	DOUBLE
-0.010	DECIMAL
-0.01	DOUBLE
-0.001	DECIMAL
-0.001	DOUBLE
-1.5e-200	DOUBLE
-1e-321	DOUBLE
-1e-322	DOUBLE
-1e-323	DOUBLE
0	INTEGER
0	INTEGER
0	UNSIGNED INTEGER
0	UNSIGNED INTEGER
0.000	DECIMAL
0.000	DECIMAL
0.000	DECIMAL
0.000	DECIMAL
0.0	DOUBLE
0.0	DOUBLE
0.0	DOUBLE
0.0	DOUBLE
0.0	DOUBLE
-0.0	DOUBLE
0.0	DECIMAL
1e-323	DOUBLE
1e-322	DOUBLE
1e-321	DOUBLE
1.5e-200	DOUBLE
0.0000000000000001	DECIMAL
0.000000000000000116	DECIMAL
0.000000000000001	DECIMAL
0.00000000000000115	DECIMAL
0.001	DECIMAL
0.001	DOUBLE
0.010	DECIMAL
0.01	DOUBLE
0.01	DOUBLE
0.100	DECIMAL
0.1	DOUBLE
0.1	DOUBLE
1	INTEGER
1	UNSIGNED INTEGER
1.000	DECIMAL
1.000	DECIMAL
1.0	DOUBLE
1.0	DOUBLE
2	INTEGER
2	UNSIGNED INTEGER
2.000	DECIMAL
2.000	DECIMAL
2.0	DOUBLE
2.0	DOUBLE
3	INTEGER
3	UNSIGNED INTEGER
3.000	DECIMAL
3.000	DECIMAL
3.0	DOUBLE
3.0	DOUBLE
3.129	DECIMAL
3.129	DOUBLE
3.130	DECIMAL
3.13	DOUBLE
3.131	DECIMAL
3.131	DOUBLE
3.139	DECIMAL
3.139	DOUBLE
3.140	DECIMAL
3.14	DOUBLE
3.141	DECIMAL
3.141	DOUBLE
3.149	DECIMAL
3.149	DOUBLE
3.150	DECIMAL
3.15	DOUBLE
3.151	DECIMAL
3.151	DOUBLE
4	INTEGER
4	UNSIGNED INTEGER
4.000	DECIMAL
4.000	DECIMAL
4.0	DOUBLE
4.0	DOUBLE
5	INTEGER
5	UNSIGNED INTEGER
5.000	DECIMAL
5.000	DECIMAL
5.0	DOUBLE
5.0	DOUBLE
10	INTEGER
10	UNSIGNED INTEGER
10.000	DECIMAL
10.000	DECIMAL
10.0	DOUBLE
10.0	DOUBLE
11	INTEGER
11	UNSIGNED INTEGER
11.000	DECIMAL
11.000	DECIMAL
11.0	DOUBLE
11.0	DOUBLE
12	INTEGER
12	UNSIGNED INTEGER
12.000	DECIMAL
12.000	DECIMAL
12.0	DOUBLE
12.0	DOUBLE
20	INTEGER
20	UNSIGNED INTEGER
20.000	DECIMAL
20.000	DECIMAL
20.0	DOUBLE
20.0	DOUBLE
21	INTEGER
21	UNSIGNED INTEGER
21.000	DECIMAL
21.000	DECIMAL
21.0	DOUBLE
21.0	DOUBLE
22	INTEGER
22	UNSIGNED INTEGER
22.000	DECIMAL
22.000	DECIMAL
22.0	DOUBLE
22.0	DOUBLE
99	INTEGER
99	UNSIGNED INTEGER
99.000	DECIMAL
99.000	DECIMAL
99.0	DOUBLE
99.0	DOUBLE
100	INTEGER
100	UNSIGNED INTEGER
100.000	DECIMAL
100.000	DECIMAL
100.0	DOUBLE
100.0	DOUBLE
101	INTEGER
101	UNSIGNED INTEGER
101.000	DECIMAL
101.000	DECIMAL
101.0	DOUBLE
101.0	DOUBLE
999	INTEGER
999	UNSIGNED INTEGER
999.000	DECIMAL
999.000	DECIMAL
999.0	DOUBLE
999.0	DOUBLE
1000	INTEGER
1000	UNSIGNED INTEGER
1000.000	DECIMAL
1000.000	DECIMAL
1000.0	DOUBLE
1000.0	DOUBLE
1001	INTEGER
1001	UNSIGNED INTEGER
1001.000	DECIMAL
1001.000	DECIMAL
1001.0	DOUBLE
1001.0	DOUBLE
9223372036854775806	INTEGER
9223372036854775806	UNSIGNED INTEGER
9223372036854775806.000	DECIMAL
9223372036854775806.000	DECIMAL
9223372036854775807	INTEGER
9223372036854775807	UNSIGNED INTEGER
9223372036854775807.000	DECIMAL
9223372036854775807.000	DECIMAL
9223372036854775808	UNSIGNED INTEGER
9223372036854775808.000	DECIMAL
9223372036854776000.000	DECIMAL
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9.223372036854776e18	DOUBLE
9223372036854776000	UNSIGNED INTEGER
9223372036854776001.000	DECIMAL
18446744073709551614	UNSIGNED INTEGER
18446744073709551614.000	DECIMAL
18446744073709551615	UNSIGNED INTEGER
18446744073709551615.000	DECIMAL
1.8446744073709552e19	DOUBLE
1.8446744073709552e19	DOUBLE
9.99e80	DOUBLE
999999999999999999999999999999999999999999999999999999999999999999999999999999997	DECIMAL
999999999999999999999999999999999999999999999999999999999999999999999999999999998	DECIMAL
999999999999999999999999999999999999999999999999999999999999999999999999999999999	DECIMAL
1e81	DOUBLE
1.5e200	DOUBLE
1e306	DOUBLE
1e307	DOUBLE
1e308	DOUBLE
""	STRING
"abc"	STRING
"abc\u0000"	STRING
"abc\u0000\u0000"	STRING
"abcd"	STRING
"bc"	STRING
{"an": "object"}	OBJECT
{"another": "object"}	OBJECT
{"type": "Point", "coordinates": [0.0, 0.0]}	OBJECT
{"type": "Point", "coordinates": [0.0, 1.0]}	OBJECT
["an array"]	ARRAY
["another array"]	ARRAY
false	BOOLEAN
true	BOOLEAN
"1000-01-01"	DATE
"1960-01-02"	DATE
"1970-01-02"	DATE
"2000-01-01"	DATE
"2000-01-02"	DATE
"2000-02-01"	DATE
"2010-12-02"	DATE
"9999-12-31"	DATE
"-630:59:59.000000"	TIME
"-24:00:00.000000"	TIME
"-12:00:00.000000"	TIME
"-00:00:00.000001"	TIME
"00:00:00.000000"	TIME
"00:00:00.000000"	TIME
"00:00:00.001000"	TIME
"00:00:00.002000"	TIME
"00:00:00.010000"	TIME
"00:00:00.020000"	TIME
"00:00:01.000000"	TIME
"00:00:01.000001"	TIME
"01:00:00.000000"	TIME
"01:02:00.000000"	TIME
"01:02:03.000000"	TIME
"02:01:00.000000"	TIME
"03:04:05.000000"	TIME
"03:04:06.000000"	TIME
"23:59:59.999999"	TIME
"24:00:00.000000"	TIME
"630:59:59.000000"	TIME
"1000-01-01 00:00:00.000000"	DATETIME
"1960-01-02 03:04:05.000000"	DATETIME
"1960-01-02 03:04:06.000000"	DATETIME
"1970-01-02 00:00:01.000000"	TIMESTAMP
"1970-01-02 00:00:01.000000"	DATETIME
"1970-01-02 00:00:01.000001"	TIMESTAMP
"1970-01-02 00:00:01.000001"	DATETIME
"2000-01-01 00:00:00.000000"	TIMESTAMP
"2000-01-01 00:00:00.000000"	DATETIME
"2000-01-01 00:00:00.001000"	TIMESTAMP
"2000-01-01 00:00:00.001000"	DATETIME
"2000-01-01 00:00:00.002000"	TIMESTAMP
"2000-01-01 00:00:00.002000"	DATETIME
"2000-01-01 00:00:00.010000"	TIMESTAMP
"2000-01-01 00:00:00.010000"	DATETIME
"2000-01-01 00:00:00.020000"	TIMESTAMP
"2000-01-01 00:00:00.020000"	DATETIME
"2000-01-01 23:59:59.999999"	TIMESTAMP
"2000-01-01 23:59:59.999999"	DATETIME
"2000-01-02 00:00:00.000000"	TIMESTAMP
"2000-01-02 00:00:00.000000"	DATETIME
"2000-02-01 00:00:00.000000"	TIMESTAMP
"2000-02-01 00:00:00.000000"	DATETIME
"2010-12-02 01:00:00.000000"	TIMESTAMP
"2010-12-02 01:00:00.000000"	DATETIME
"2010-12-02 01:02:00.000000"	TIMESTAMP
"2010-12-02 01:02:00.000000"	DATETIME
"2010-12-02 01:02:03.000000"	TIMESTAMP
"2010-12-02 01:02:03.000000"	DATETIME
"2010-12-02 02:01:00.000000"	TIMESTAMP
"2010-12-02 02:01:00.000000"	DATETIME
"9999-12-31 23:59:59.999999"	DATETIME
"base64:type15:MTIzNGFiY2Q="	BLOB
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN SELECT j, COUNT(*) FROM t GROUP BY j ORDER BY j;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	366	100.00	Using temporary; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`j` AS `j`,count(0) AS `COUNT(*)` from `test`.`t` group by `test`.`t`.`j` order by `test`.`t`.`j`
EXPLAIN SELECT j, COUNT(*) FROM t GROUP BY j WITH ROLLUP;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	366	100.00	Using filesort
Warnings:
Note	1003	/* select#1 */ select rollup_group_item(`test`.`t`.`j`,0) AS `j`,rollup_sum_switcher(count(0)) AS `COUNT(*)` from `test`.`t` group by `test`.`t`.`j` with rollup
SELECT j, COUNT(*) FROM t GROUP BY j ORDER BY j;
j	COUNT(*)
NULL	2
null	1
-1e308	1
-1e307	1
-1e306	1
-1.5e200	1
-1e81	1
-999999999999999999999999999999999999999999999999999999999999999999999999999999999	1
-999999999999999999999999999999999999999999999999999999999999999999999999999999998	1
-999999999999999999999999999999999999999999999999999999999999999999999999999999997	1
-9.99e80	1
-9223372036854776001	1
-9223372036854776000	6
-9223372036854775808	2
-9223372036854775807	2
-9223372036854775806	2
-1001	3
-1000	3
-999	3
-101	3
-100	3
-99	3
-22	3
-21	3
-20	3
-12	3
-11	3
-10	3
-5	3
-4	3
-3.151	2
-3.15	2
-3.149	2
-3.141	2
-3.14	2
-3.139	2
-3.131	2
-3.13	2
-3.129	2
-3	3
-2	3
-1	3
0.1	2
01	2
001	2
-1.5e-200	1
-1e-321	1
-1e-322	1
-1e-323	1
0	15
1e-323	1
1e-322	1
1e-321	1
1.5e-200	1
00000000000001	1
0000000000000116	1
0000000000001	1
000000000000115	1
001	2
01	3
0.1	3
1	6
2	6
3	6
3.129	2
3.13	2
3.131	2
3.139	2
3.14	2
3.141	2
3.149	2
3.15	2
3.151	2
4	6
5	6
10	6
11	6
12	6
20	6
21	6
22	6
99	6
100	6
101	6
999	6
1000	6
1001	6
9223372036854775806	4
9223372036854775807	4
9223372036854775808	2
9223372036854776000	9
9223372036854776001	1
18446744073709551614	2
18446744073709551615	2
1.8446744073709552e19	2
9.99e80	1
999999999999999999999999999999999999999999999999999999999999999999999999999999997	1
999999999999999999999999999999999999999999999999999999999999999999999999999999998	1
999999999999999999999999999999999999999999999999999999999999999999999999999999999	1
1e81	1
1.5e200	1
1e306	1
1e307	1
1e308	1
""	1
"abc"	1
"abc\u0000"	1
"abc\u0000\u0000"	1
"abcd"	1
"bc"	1
{"an": "object"}	1
{"another": "object"}	1
{"type": "Point", "coordinates": [0, 0]}	1
{"type": "Point", "coordinates": [0, 1]}	1
["an array"]	1
["another array"]	1
false	1
true	1
"10000101"	1
"19600102"	1
"19700102"	1
"20000101"	1
"20000102"	1
"20000201"	1
"2010-1202"	1
"9999-12-31"	1
"-630:59:59000"	1
"-24:00:00000"	1
"-12:00:00000"	1
"00:00:00001"	1
"00:00:00000"	2
"00:00:0001000"	1
"00:00:0002000"	1
"00:00:001000"	1
"00:00:0020000"	1
"00:00:01000"	1
"00:00:01001"	1
"01:00:00000"	1
"01:02:00000"	1
"01:02:03000"	1
"02:01:00000"	1
"03:04:05000"	1
"03:04:06000"	1
"23:59:59.999999"	1
"24:00:00000"	1
"630:59:59000"	1
"10000101 00:00:00000"	1
"19600102 03:04:05000"	1
"19600102 03:04:06000"	1
"19700102 00:00:01000"	2
"19700102 00:00:01001"	2
"20000101 00:00:00000"	2
"20000101 00:00:0001000"	2
"20000101 00:00:0002000"	2
"20000101 00:00:001000"	2
"20000101 00:00:0020000"	2
"20000101 23:59:59.999999"	2
"20000102 00:00:00000"	2
"20000201 00:00:00000"	2
"2010-1202 01:00:00000"	2
"2010-1202 01:02:00000"	2
"2010-1202 01:02:03000"	2
"2010-1202 02:01:00000"	2
"9999-12-31 23:59:59.999999"	1
"base64:type15:MTIzNGFiY2Q="	1
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
SELECT JSON_EXTRACT(j, '$') AS je, COUNT(*) FROM t GROUP BY je ORDER BY je;
je	COUNT(*)
NULL	2
null	1
-1e308	1
-1e307	1
-1e306	1
-1.5e200	1
-1e81	1
-999999999999999999999999999999999999999999999999999999999999999999999999999999999	1
-999999999999999999999999999999999999999999999999999999999999999999999999999999998	1
-999999999999999999999999999999999999999999999999999999999999999999999999999999997	1
-9.99e80	1
-9223372036854776001	1
-9223372036854776000	6
-9223372036854775808	2
-9223372036854775807	2
-9223372036854775806	2
-1001	3
-1000	3
-999	3
-101	3
-100	3
-99	3
-22	3
-21	3
-20	3
-12	3
-11	3
-10	3
-5	3
-4	3
-3.151	2
-3.15	2
-3.149	2
-3.141	2
-3.14	2
-3.139	2
-3.131	2
-3.13	2
-3.129	2
-3	3
-2	3
-1	3
0.1	2
01	2
001	2
-1.5e-200	1
-1e-321	1
-1e-322	1
-1e-323	1
0	15
1e-323	1
1e-322	1
1e-321	1
1.5e-200	1
00000000000001	1
0000000000000116	1
0000000000001	1
000000000000115	1
001	2
01	3
0.1	3
1	6
2	6
3	6
3.129	2
3.13	2
3.131	2
3.139	2
3.14	2
3.141	2
3.149	2
3.15	2
3.151	2
4	6
5	6
10	6
11	6
12	6
20	6
21	6
22	6
99	6
100	6
101	6
999	6
1000	6
1001	6
9223372036854775806	4
9223372036854775807	4
9223372036854775808	2
9223372036854776000	9
9223372036854776001	1
18446744073709551614	2
18446744073709551615	2
1.8446744073709552e19	2
9.99e80	1
999999999999999999999999999999999999999999999999999999999999999999999999999999997	1
999999999999999999999999999999999999999999999999999999999999999999999999999999998	1
999999999999999999999999999999999999999999999999999999999999999999999999999999999	1
1e81	1
1.5e200	1
1e306	1
1e307	1
1e308	1
""	1
"abc"	1
"abc\u0000"	1
"abc\u0000\u0000"	1
"abcd"	1
"bc"	1
{"an": "object"}	1
{"another": "object"}	1
{"type": "Point", "coordinates": [0, 0]}	1
{"type": "Point", "coordinates": [0, 1]}	1
["an array"]	1
["another array"]	1
false	1
true	1
"10000101"	1
"19600102"	1
"19700102"	1
"20000101"	1
"20000102"	1
"20000201"	1
"2010-1202"	1
"9999-12-31"	1
"-630:59:59000"	1
"-24:00:00000"	1
"-12:00:00000"	1
"00:00:00001"	1
"00:00:00000"	2
"00:00:0001000"	1
"00:00:0002000"	1
"00:00:001000"	1
"00:00:0020000"	1
"00:00:01000"	1
"00:00:01001"	1
"01:00:00000"	1
"01:02:00000"	1
"01:02:03000"	1
"02:01:00000"	1
"03:04:05000"	1
"03:04:06000"	1
"23:59:59.999999"	1
"24:00:00000"	1
"630:59:59000"	1
"10000101 00:00:00000"	1
"19600102 03:04:05000"	1
"19600102 03:04:06000"	1
"19700102 00:00:01000"	2
"19700102 00:00:01001"	2
"20000101 00:00:00000"	2
"20000101 00:00:0001000"	2
"20000101 00:00:0002000"	2
"20000101 00:00:001000"	2
"20000101 00:00:0020000"	2
"20000101 23:59:59.999999"	2
"20000102 00:00:00000"	2
"20000201 00:00:00000"	2
"2010-1202 01:00:00000"	2
"2010-1202 01:02:00000"	2
"2010-1202 01:02:03000"	2
"2010-1202 02:01:00000"	2
"9999-12-31 23:59:59.999999"	1
"base64:type15:MTIzNGFiY2Q="	1
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
SELECT j, COUNT(*) FROM t GROUP BY j WITH ROLLUP;
j	COUNT(*)
NULL	2
null	1
-1e308	1
-1e307	1
-1e306	1
-1.5e200	1
-1e81	1
-999999999999999999999999999999999999999999999999999999999999999999999999999999999	1
-999999999999999999999999999999999999999999999999999999999999999999999999999999998	1
-999999999999999999999999999999999999999999999999999999999999999999999999999999997	1
-9.99e80	1
-9223372036854776001	1
-9223372036854776000	6
-9223372036854775808	2
-9223372036854775807	2
-9223372036854775806	2
-1001	3
-1000	3
-999	3
-101	3
-100	3
-99	3
-22	3
-21	3
-20	3
-12	3
-11	3
-10	3
-5	3
-4	3
-3.151	2
-3.15	2
-3.149	2
-3.141	2
-3.14	2
-3.139	2
-3.131	2
-3.13	2
-3.129	2
-3	3
-2	3
-1	3
0.1	2
01	2
001	2
-1.5e-200	1
-1e-321	1
-1e-322	1
-1e-323	1
0	15
1e-323	1
1e-322	1
1e-321	1
1.5e-200	1
00000000000001	1
0000000000000116	1
0000000000001	1
000000000000115	1
001	2
01	3
0.1	3
1	6
2	6
3	6
3.129	2
3.13	2
3.131	2
3.139	2
3.14	2
3.141	2
3.149	2
3.15	2
3.151	2
4	6
5	6
10	6
11	6
12	6
20	6
21	6
22	6
99	6
100	6
101	6
999	6
1000	6
1001	6
9223372036854775806	4
9223372036854775807	4
9223372036854775808	2
9223372036854776000	9
9223372036854776001	1
18446744073709551614	2
18446744073709551615	2
1.8446744073709552e19	2
9.99e80	1
999999999999999999999999999999999999999999999999999999999999999999999999999999997	1
999999999999999999999999999999999999999999999999999999999999999999999999999999998	1
999999999999999999999999999999999999999999999999999999999999999999999999999999999	1
1e81	1
1.5e200	1
1e306	1
1e307	1
1e308	1
""	1
"abc"	1
"abc\u0000"	1
"abc\u0000\u0000"	1
"abcd"	1
"bc"	1
{"an": "object"}	1
{"another": "object"}	1
{"type": "Point", "coordinates": [0, 0]}	1
{"type": "Point", "coordinates": [0, 1]}	1
["an array"]	1
["another array"]	1
false	1
true	1
"10000101"	1
"19600102"	1
"19700102"	1
"20000101"	1
"20000102"	1
"20000201"	1
"2010-1202"	1
"9999-12-31"	1
"-630:59:59000"	1
"-24:00:00000"	1
"-12:00:00000"	1
"00:00:00001"	1
"00:00:00000"	2
"00:00:0001000"	1
"00:00:0002000"	1
"00:00:001000"	1
"00:00:0020000"	1
"00:00:01000"	1
"00:00:01001"	1
"01:00:00000"	1
"01:02:00000"	1
"01:02:03000"	1
"02:01:00000"	1
"03:04:05000"	1
"03:04:06000"	1
"23:59:59.999999"	1
"24:00:00000"	1
"630:59:59000"	1
"10000101 00:00:00000"	1
"19600102 03:04:05000"	1
"19600102 03:04:06000"	1
"19700102 00:00:01000"	2
"19700102 00:00:01001"	2
"20000101 00:00:00000"	2
"20000101 00:00:0001000"	2
"20000101 00:00:0002000"	2
"20000101 00:00:001000"	2
"20000101 00:00:0020000"	2
"20000101 23:59:59.999999"	2
"20000102 00:00:00000"	2
"20000201 00:00:00000"	2
"2010-1202 01:00:00000"	2
"2010-1202 01:02:00000"	2
"2010-1202 01:02:03000"	2
"2010-1202 02:01:00000"	2
"9999-12-31 23:59:59.999999"	1
"base64:type15:MTIzNGFiY2Q="	1
NULL	366
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
SELECT JSON_EXTRACT(j, '$') AS je, COUNT(*) FROM t GROUP BY je WITH ROLLUP;
je	COUNT(*)
NULL	2
null	1
-1e308	1
-1e307	1
-1e306	1
-1.5e200	1
-1e81	1
-999999999999999999999999999999999999999999999999999999999999999999999999999999999	1
-999999999999999999999999999999999999999999999999999999999999999999999999999999998	1
-999999999999999999999999999999999999999999999999999999999999999999999999999999997	1
-9.99e80	1
-9223372036854776001	1
-9223372036854776000	6
-9223372036854775808	2
-9223372036854775807	2
-9223372036854775806	2
-1001	3
-1000	3
-999	3
-101	3
-100	3
-99	3
-22	3
-21	3
-20	3
-12	3
-11	3
-10	3
-5	3
-4	3
-3.151	2
-3.15	2
-3.149	2
-3.141	2
-3.14	2
-3.139	2
-3.131	2
-3.13	2
-3.129	2
-3	3
-2	3
-1	3
0.1	2
01	2
001	2
-1.5e-200	1
-1e-321	1
-1e-322	1
-1e-323	1
0	15
1e-323	1
1e-322	1
1e-321	1
1.5e-200	1
00000000000001	1
0000000000000116	1
0000000000001	1
000000000000115	1
001	2
01	3
0.1	3
1	6
2	6
3	6
3.129	2
3.13	2
3.131	2
3.139	2
3.14	2
3.141	2
3.149	2
3.15	2
3.151	2
4	6
5	6
10	6
11	6
12	6
20	6
21	6
22	6
99	6
100	6
101	6
999	6
1000	6
1001	6
9223372036854775806	4
9223372036854775807	4
9223372036854775808	2
9223372036854776000	9
9223372036854776001	1
18446744073709551614	2
18446744073709551615	2
1.8446744073709552e19	2
9.99e80	1
999999999999999999999999999999999999999999999999999999999999999999999999999999997	1
999999999999999999999999999999999999999999999999999999999999999999999999999999998	1
999999999999999999999999999999999999999999999999999999999999999999999999999999999	1
1e81	1
1.5e200	1
1e306	1
1e307	1
1e308	1
""	1
"abc"	1
"abc\u0000"	1
"abc\u0000\u0000"	1
"abcd"	1
"bc"	1
{"an": "object"}	1
{"another": "object"}	1
{"type": "Point", "coordinates": [0, 0]}	1
{"type": "Point", "coordinates": [0, 1]}	1
["an array"]	1
["another array"]	1
false	1
true	1
"10000101"	1
"19600102"	1
"19700102"	1
"20000101"	1
"20000102"	1
"20000201"	1
"2010-1202"	1
"9999-12-31"	1
"-630:59:59000"	1
"-24:00:00000"	1
"-12:00:00000"	1
"00:00:00001"	1
"00:00:00000"	2
"00:00:0001000"	1
"00:00:0002000"	1
"00:00:001000"	1
"00:00:0020000"	1
"00:00:01000"	1
"00:00:01001"	1
"01:00:00000"	1
"01:02:00000"	1
"01:02:03000"	1
"02:01:00000"	1
"03:04:05000"	1
"03:04:06000"	1
"23:59:59.999999"	1
"24:00:00000"	1
"630:59:59000"	1
"10000101 00:00:00000"	1
"19600102 03:04:05000"	1
"19600102 03:04:06000"	1
"19700102 00:00:01000"	2
"19700102 00:00:01001"	2
"20000101 00:00:00000"	2
"20000101 00:00:0001000"	2
"20000101 00:00:0002000"	2
"20000101 00:00:001000"	2
"20000101 00:00:0020000"	2
"20000101 23:59:59.999999"	2
"20000102 00:00:00000"	2
"20000201 00:00:00000"	2
"2010-1202 01:00:00000"	2
"2010-1202 01:02:00000"	2
"2010-1202 01:02:03000"	2
"2010-1202 02:01:00000"	2
"9999-12-31 23:59:59.999999"	1
"base64:type15:MTIzNGFiY2Q="	1
NULL	366
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
DROP TABLE t, timestamps, datetimes, times, dates, signed_integers,
unsigned_integers, decimals, doubles;
CREATE TABLE t(j JSON NOT NULL);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES ('1'), ('2'), ('10'), ('"1"'), ('"2"'), ('"10"'),
('true'), ('false'), ('null');
SELECT j FROM t ORDER BY j;
j
null
1
2
10
"1"
"10"
"2"
false
true
SELECT j FROM t ORDER BY JSON_EXTRACT(j, '$');
j
null
1
2
10
"1"
"10"
"2"
false
true
SELECT JSON_EXTRACT(j, '$') FROM t ORDER BY 1;
JSON_EXTRACT(j, '$')
null
1
2
10
"1"
"10"
"2"
false
true
SELECT j FROM t ORDER BY j+1, JSON_TYPE(j);
j
false
null
true
1
"1"
2
"2"
10
"10"
Warnings:
Warning	3156	Invalid JSON value for CAST to DOUBLE from column j at row 1
DROP TABLE t;
CREATE TABLE t(vc varchar(10));
INSERT INTO t VALUES ('["abc"]'), ('[1');
SELECT * FROM t ORDER BY CAST(vc AS JSON);
ERROR 22032: Invalid JSON text in argument 1 to function cast_as_json: "Missing a comma or ']' after an array element." at position 2.
SELECT * FROM t ORDER BY JSON_EXTRACT(vc, '$[0]');
ERROR 22032: Invalid JSON text in argument 1 to function json_extract: "Missing a comma or ']' after an array element." at position 2.
SELECT CAST(vc AS JSON) AS j FROM t ORDER BY j;
ERROR 22032: Invalid JSON text in argument 1 to function cast_as_json: "Missing a comma or ']' after an array element." at position 2.
SELECT JSON_EXTRACT(vc, '$[0]') AS j FROM t ORDER BY j;
ERROR 22032: Invalid JSON text in argument 1 to function json_extract: "Missing a comma or ']' after an array element." at position 2.
SELECT CAST(vc AS JSON) FROM t ORDER BY 1;
ERROR 22032: Invalid JSON text in argument 1 to function cast_as_json: "Missing a comma or ']' after an array element." at position 2.
SELECT JSON_EXTRACT(vc, '$[0]') FROM t ORDER BY 1;
ERROR 22032: Invalid JSON text in argument 1 to function json_extract: "Missing a comma or ']' after an array element." at position 2.
DROP TABLE t;
#
# Internal ordering of arrays and objects. Ordered by cardinality.
#
CREATE TABLE t(i int, j json);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES
(1, '{}'), (2, '{"a":1}'), (3, '{"ab":2}'), (4, '{"a":1,"b":2}'),
(5, '{"c":3,"d":4}'), (6, '{"a":1,"b":2,"c":3,"d":4}');
INSERT INTO t VALUES
(1, '[]'), (2, '[1]'), (3, '[2]'), (4, '[1,2]'), (5, '[2,1]'), (6, '[1,2,3]'),
(7, '[1,2,3,4]'), (8, '[4,3,2,1]'), (9, '[1,2,3,4,5]');
INSERT INTO t SELECT i+100, j FROM t;
SELECT * FROM t ORDER BY j, i;
i	j
1	{}
101	{}
2	{"a": 1}
3	{"ab": 2}
102	{"a": 1}
103	{"ab": 2}
4	{"a": 1, "b": 2}
5	{"c": 3, "d": 4}
104	{"a": 1, "b": 2}
105	{"c": 3, "d": 4}
6	{"a": 1, "b": 2, "c": 3, "d": 4}
106	{"a": 1, "b": 2, "c": 3, "d": 4}
1	[]
101	[]
2	[1]
3	[2]
102	[1]
103	[2]
4	[1, 2]
5	[2, 1]
104	[1, 2]
105	[2, 1]
6	[1, 2, 3]
106	[1, 2, 3]
7	[1, 2, 3, 4]
8	[4, 3, 2, 1]
107	[1, 2, 3, 4]
108	[4, 3, 2, 1]
9	[1, 2, 3, 4, 5]
109	[1, 2, 3, 4, 5]
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
SELECT * FROM t ORDER BY j DESC, i;
i	j
9	[1, 2, 3, 4, 5]
109	[1, 2, 3, 4, 5]
7	[1, 2, 3, 4]
8	[4, 3, 2, 1]
107	[1, 2, 3, 4]
108	[4, 3, 2, 1]
6	[1, 2, 3]
106	[1, 2, 3]
4	[1, 2]
5	[2, 1]
104	[1, 2]
105	[2, 1]
2	[1]
3	[2]
102	[1]
103	[2]
1	[]
101	[]
6	{"a": 1, "b": 2, "c": 3, "d": 4}
106	{"a": 1, "b": 2, "c": 3, "d": 4}
4	{"a": 1, "b": 2}
5	{"c": 3, "d": 4}
104	{"a": 1, "b": 2}
105	{"c": 3, "d": 4}
2	{"a": 1}
3	{"ab": 2}
102	{"a": 1}
103	{"ab": 2}
1	{}
101	{}
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
SELECT j, COUNT(*) FROM t GROUP BY j WITH ROLLUP;
j	COUNT(*)
{}	2
{"a": 1}	2
{"ab": 2}	2
{"c": 3, "d": 4}	2
{"a": 1, "b": 2}	2
{"a": 1, "b": 2, "c": 3, "d": 4}	2
[]	2
[2]	2
[1]	2
[1, 2]	2
[2, 1]	2
[1, 2, 3]	2
[1, 2, 3, 4]	2
[4, 3, 2, 1]	2
[1, 2, 3, 4, 5]	2
NULL	30
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
DROP TABLE t;
# Test NULLs sorting.
CREATE TABLE t(i int, j json);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t(i) VALUES (1),(2),(3),(2),(1);
SELECT * FROM t ORDER BY j, i;
i	j
1	NULL
1	NULL
2	NULL
2	NULL
3	NULL
SELECT * FROM t ORDER BY j DESC, i;
i	j
1	NULL
1	NULL
2	NULL
2	NULL
3	NULL
SELECT i, JSON_EXTRACT(j, '$') AS je FROM t ORDER BY je, i;
i	je
1	NULL
1	NULL
2	NULL
2	NULL
3	NULL
SELECT i, JSON_EXTRACT(j, '$') AS je FROM t ORDER BY je DESC, i;
i	je
1	NULL
1	NULL
2	NULL
2	NULL
3	NULL
INSERT INTO t(i, j) VALUES (1, '1');
SELECT * FROM t ORDER BY j, i;
i	j
1	NULL
1	NULL
2	NULL
2	NULL
3	NULL
1	1
SELECT * FROM t ORDER BY j DESC, i;
i	j
1	1
1	NULL
1	NULL
2	NULL
2	NULL
3	NULL
SELECT i, JSON_EXTRACT(j, '$') AS je FROM t ORDER BY je, i;
i	je
1	NULL
1	NULL
2	NULL
2	NULL
3	NULL
1	1
SELECT i, JSON_EXTRACT(j, '$') AS je FROM t ORDER BY je DESC, i;
i	je
1	1
1	NULL
1	NULL
2	NULL
2	NULL
3	NULL
DROP TABLE t;
CREATE TABLE t(vc TEXT, j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t (vc) VALUES ('a'), ('b'), ('c');
INSERT INTO t SELECT * FROM t;
INSERT INTO t SELECT * FROM t;
INSERT INTO t SELECT * FROM t;
INSERT INTO t SELECT * FROM t;
INSERT INTO t SELECT * FROM t;
INSERT INTO t SELECT * FROM t;
INSERT INTO t SELECT * FROM t;
SELECT * FROM t ORDER BY vc, j;
vc	j
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
a	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
b	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
c	NULL
DROP TABLE t;
# ----------------------------------------------------------------------
# Test of JSON_VALID function.
# ----------------------------------------------------------------------

# Table - Json string column - utf-8, NULL
Note: 'utf8mb3' is a subset of internal 'utf8mb4'

create table utf8_t (c varchar(20)) CHARACTER SET 'utf8mb3';
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into utf8_t values (NULL);
# Expect NULL:
select JSON_VALID(c) from utf8_t;
JSON_VALID(c)
NULL
delete from utf8_t;

# Table - Json string column - utf-8, valid
insert into utf8_t values ('[123]');
select JSON_VALID(c) from utf8_t;
JSON_VALID(c)
1
delete from utf8_t;

# Table - Json string column - utf-8, non-utf8mb3
insert into utf8_t values ('[123');
expect 0 (false)
select JSON_VALID(c) from utf8_t;
JSON_VALID(c)
0
delete from utf8_t;

# Table - Try to extract JSON from TIMESTAMP column
ALTER TABLE utf8_t ADD d TIMESTAMP;
# Should give false; not string or JSON type
# and we do not convert automatically from TIMESTAMP to JSON
insert into utf8_t values (NULL, '2014-11-25 18:00');
select JSON_VALID(d) from utf8_t;
JSON_VALID(d)
0
# Explicit cast to a character data type
# allows MySQL to parse this is a JSON text
# The string isn't a legal JSON document, tho, so not valid.
select JSON_VALID(CAST(d as CHAR)) from utf8_t;
JSON_VALID(CAST(d as CHAR))
0
# Should give true
select JSON_VALID(CONCAT( CONCAT('"', CAST(d as CHAR)), '"')) from utf8_t;
JSON_VALID(CONCAT( CONCAT('"', CAST(d as CHAR)), '"'))
1
delete from utf8_t;
drop table utf8_t;

# Table - JSON type; should give true by definition
create table json_t(t json);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into json_t values ('[123]');
select json_VALID(t) from json_t;
json_VALID(t)
1

# Function result - JSON
select JSON_VALID( JSON_ARRAY(t, t) ) from json_t;
JSON_VALID( JSON_ARRAY(t, t) )
1
drop table json_t;
# ----------------------------------------------------------------------
# Test of JSON_LENGTH function.
# ----------------------------------------------------------------------
create table utf8_mj_length (a int, c varchar(20)) CHARACTER SET 'utf8mb3';
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into utf8_mj_length values( 1, null );
insert into utf8_mj_length values( 2, '1' );
insert into utf8_mj_length values( 3, 'abc' );
insert into utf8_mj_length values( 4, '"abc"' );
insert into utf8_mj_length values ( 5, 'true' );
insert into utf8_mj_length values ( 6, 'false' );
insert into utf8_mj_length values ( 7, 'null' );
select a, c, json_length( c ) from utf8_mj_length where a = 1;
a	c	json_length( c )
1	NULL	NULL
select a, c, json_length( c ) from utf8_mj_length where a = 2;
a	c	json_length( c )
2	1	1

# invalid json text
select a, c, json_length( c ) from utf8_mj_length where a = 3;
ERROR 22032: Invalid JSON text in argument 1 to function json_length: "Invalid value." at position 0.
select a, c, json_length( c ) from utf8_mj_length where a = 4;
a	c	json_length( c )
4	"abc"	1
select a, c, json_length( c ) from utf8_mj_length where a = 5;
a	c	json_length( c )
5	true	1
select a, c, json_length( c ) from utf8_mj_length where a = 6;
a	c	json_length( c )
6	false	1
select a, c, json_length( c ) from utf8_mj_length where a = 7;
a	c	json_length( c )
7	null	1
create table json_mj_length( a int, b json );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into json_mj_length values( 1, NULL );
select a, b, json_length( b ) from json_mj_length where a = 1;
a	b	json_length( b )
1	NULL	NULL
set names 'ascii';

# path auto-converted to a utf8mb3 string from ascii

select a, c, json_length( c, '$' ) from utf8_mj_length where a = 2;
a	c	json_length( c, '$' )
2	1	1
set names 'utf8mb3';
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
select a, c, json_length( c, '$' ) from utf8_mj_length where a = 1;
a	c	json_length( c, '$' )
1	NULL	NULL
select a, c, json_length( c, '$' ) from utf8_mj_length where a = 2;
a	c	json_length( c, '$' )
2	1	1

# invalid json text
select a, c, json_length( c, '$' ) from utf8_mj_length where a = 3;
ERROR 22032: Invalid JSON text in argument 1 to function json_extract: "Invalid value." at position 0.
select a, c, json_length( c, '$' ) from utf8_mj_length where a = 4;
a	c	json_length( c, '$' )
4	"abc"	1
select a, c, json_length( c, '$' ) from utf8_mj_length where a = 5;
a	c	json_length( c, '$' )
5	true	1
select a, c, json_length( c, '$' ) from utf8_mj_length where a = 6;
a	c	json_length( c, '$' )
6	false	1
select a, c, json_length( c, '$' ) from utf8_mj_length where a = 7;
a	c	json_length( c, '$' )
7	null	1
select a, b, json_length( b, '$' ) from json_mj_length where a = 1;
a	b	json_length( b, '$' )
1	NULL	NULL
drop table utf8_mj_length;
drop table json_mj_length;
CREATE TABLE json_remove_t(j JSON, p TEXT);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO json_remove_t(p) VALUES ('$.a'), ('$.b'), ('$.c');
UPDATE json_remove_t SET j = '{"a":1,"b":2,"c":3}';
SELECT j, p, json_remove(j, p) FROM json_remove_t ORDER BY p;
j	p	json_remove(j, p)
{"a": 1, "b": 2, "c": 3}	$.a	{"b": 2, "c": 3}
{"a": 1, "b": 2, "c": 3}	$.b	{"a": 1, "c": 3}
{"a": 1, "b": 2, "c": 3}	$.c	{"a": 1, "b": 2}
DROP TABLE json_remove_t;
CREATE TABLE json_merge_t(i INT, j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO json_merge_t VALUES
(0, NULL),
(1, 'true'),
(2, '5'),
(3, '[1,2]'),
(4, '{"a":["x", "y"]}'),
(5, '{"a":"b","c":"d"}');
SELECT t1.j, t2.j,
JSON_MERGE_PRESERVE(t1.j, t2.j) AS m1,
JSON_MERGE_PRESERVE(t2.j, t1.j) AS m2
FROM json_merge_t t1, json_merge_t t2 ORDER BY t1.i, t2.i;
j	j	m1	m2
NULL	NULL	NULL	NULL
NULL	true	NULL	NULL
NULL	5	NULL	NULL
NULL	[1, 2]	NULL	NULL
NULL	{"a": ["x", "y"]}	NULL	NULL
NULL	{"a": "b", "c": "d"}	NULL	NULL
true	NULL	NULL	NULL
true	true	[true, true]	[true, true]
true	5	[true, 5]	[5, true]
true	[1, 2]	[true, 1, 2]	[1, 2, true]
true	{"a": ["x", "y"]}	[true, {"a": ["x", "y"]}]	[{"a": ["x", "y"]}, true]
true	{"a": "b", "c": "d"}	[true, {"a": "b", "c": "d"}]	[{"a": "b", "c": "d"}, true]
5	NULL	NULL	NULL
5	true	[5, true]	[true, 5]
5	5	[5, 5]	[5, 5]
5	[1, 2]	[5, 1, 2]	[1, 2, 5]
5	{"a": ["x", "y"]}	[5, {"a": ["x", "y"]}]	[{"a": ["x", "y"]}, 5]
5	{"a": "b", "c": "d"}	[5, {"a": "b", "c": "d"}]	[{"a": "b", "c": "d"}, 5]
[1, 2]	NULL	NULL	NULL
[1, 2]	true	[1, 2, true]	[true, 1, 2]
[1, 2]	5	[1, 2, 5]	[5, 1, 2]
[1, 2]	[1, 2]	[1, 2, 1, 2]	[1, 2, 1, 2]
[1, 2]	{"a": ["x", "y"]}	[1, 2, {"a": ["x", "y"]}]	[{"a": ["x", "y"]}, 1, 2]
[1, 2]	{"a": "b", "c": "d"}	[1, 2, {"a": "b", "c": "d"}]	[{"a": "b", "c": "d"}, 1, 2]
{"a": ["x", "y"]}	NULL	NULL	NULL
{"a": ["x", "y"]}	true	[{"a": ["x", "y"]}, true]	[true, {"a": ["x", "y"]}]
{"a": ["x", "y"]}	5	[{"a": ["x", "y"]}, 5]	[5, {"a": ["x", "y"]}]
{"a": ["x", "y"]}	[1, 2]	[{"a": ["x", "y"]}, 1, 2]	[1, 2, {"a": ["x", "y"]}]
{"a": ["x", "y"]}	{"a": ["x", "y"]}	{"a": ["x", "y", "x", "y"]}	{"a": ["x", "y", "x", "y"]}
{"a": ["x", "y"]}	{"a": "b", "c": "d"}	{"a": ["x", "y", "b"], "c": "d"}	{"a": ["b", "x", "y"], "c": "d"}
{"a": "b", "c": "d"}	NULL	NULL	NULL
{"a": "b", "c": "d"}	true	[{"a": "b", "c": "d"}, true]	[true, {"a": "b", "c": "d"}]
{"a": "b", "c": "d"}	5	[{"a": "b", "c": "d"}, 5]	[5, {"a": "b", "c": "d"}]
{"a": "b", "c": "d"}	[1, 2]	[{"a": "b", "c": "d"}, 1, 2]	[1, 2, {"a": "b", "c": "d"}]
{"a": "b", "c": "d"}	{"a": ["x", "y"]}	{"a": ["b", "x", "y"], "c": "d"}	{"a": ["x", "y", "b"], "c": "d"}
{"a": "b", "c": "d"}	{"a": "b", "c": "d"}	{"a": ["b", "b"], "c": ["d", "d"]}	{"a": ["b", "b"], "c": ["d", "d"]}
DROP TABLE json_merge_t;
create table keys1(i int, j json);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into keys1 select i, j from t1;
DROP TABLE t1;
create table rawOrders( orderID int, doc json );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into rawOrders values ( 1, '100' ), ( 2, '{ "id": 2, "quantity": 200 }' );
create table orders( orderID int, quantity int unsigned );
INSERT INTO orders( orderID, quantity )
SELECT
r.orderID,
CASE( JSON_TYPE( r.doc ) )
WHEN "INTEGER" THEN CAST( r.doc AS UNSIGNED INT )
WHEN "OBJECT" THEN CAST( JSON_EXTRACT( r.doc, '$.quantity' ) AS UNSIGNED INT )
ELSE NULL
END
FROM rawOrders r;
select * from rawOrders order by orderID;
orderID	doc
1	100
2	{"id": 2, "quantity": 200}
select * from orders order by orderID;
orderID	quantity
1	100
2	200
drop table rawOrders;
drop table orders;
select charset(json_type('{}'));
charset(json_type('{}'))
utf8mb4
# ----------------------------------------------------------------------
# Test of CAST(<column> AS JSON)
# ----------------------------------------------------------------------
create table t1(dati datetime, da date,
tim time, ts timestamp,
y year,
--
ti tinyint,   tiu tinyint unsigned,
si smallint,  siu smallint unsigned,
mi mediumint, miu mediumint unsigned,
i  int,       iu  int unsigned,
bi bigint,    biu bigint unsigned,
boo boolean,
--
dc decimal(5,2),
n numeric(5,2),
--
f float, d double,
bitt bit(10),
blb blob,
bin binary(10),
en enum('a','b','c'),
se set('a','b','c'),
--
ge geometry,
po point,
ls linestring,
py polygon,
js json
);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t1 values('2014-11-25 18:00', '2014-11-25',
'18:00:59', '2014-11-25 18:00',
'1999',
--
127, 255,
32767, 65535,
8388607, 16777215, -- 3 bytes
2147483647, 4294967295, -- 4 bytes
9223372036854775807, 18446744073709551615,
true,
--
3.14,
3.14,
--
3.14, 3.14,
b'10101',
'10101abcde',
'10101abcde',
'b',
'a,c',
--
ST_GeomFromText('POINT(1 1)'),
ST_GeomFromText('POINT(1 1)'),
ST_GeomFromText('LINESTRING(0 0,1 1,2 2)'),
ST_GeomFromText('POLYGON((0 0,10 0,10 10,0 10,0 0),
                                            (5 5,7 5,7 7,5 7, 5 5))'),
'[123]'
                      );
select json_type(cast(dati as json)) from t1;
json_type(cast(dati as json))
DATETIME
select json_type(cast(da as json)) from t1;
json_type(cast(da as json))
DATE
select json_type(cast(tim as json)) from t1;
json_type(cast(tim as json))
TIME
select json_type(cast(ts as json)) from t1;
json_type(cast(ts as json))
TIMESTAMP
select json_type(cast(y as json)) from t1;
json_type(cast(y as json))
UNSIGNED INTEGER
select json_type(cast(ti as json)) from t1;
json_type(cast(ti as json))
INTEGER
select json_type(cast(tiu as json)) from t1;
json_type(cast(tiu as json))
UNSIGNED INTEGER
select json_type(cast(si as json)) from t1;
json_type(cast(si as json))
INTEGER
select json_type(cast(siu as json)) from t1;
json_type(cast(siu as json))
UNSIGNED INTEGER
select json_type(cast(mi as json)) from t1;
json_type(cast(mi as json))
INTEGER
select json_type(cast(miu as json)) from t1;
json_type(cast(miu as json))
UNSIGNED INTEGER
select json_type(cast(i as json)) from t1;
json_type(cast(i as json))
INTEGER
select json_type(cast(iu as json)) from t1;
json_type(cast(iu as json))
UNSIGNED INTEGER
select json_type(cast(bi as json)) from t1;
json_type(cast(bi as json))
INTEGER
select json_type(cast(biu as json)) from t1;
json_type(cast(biu as json))
UNSIGNED INTEGER
select json_type(cast(boo as json)) from t1;
json_type(cast(boo as json))
INTEGER
select json_type(cast(dc as json)) from t1;
json_type(cast(dc as json))
DECIMAL
select json_type(cast(f as json)) from t1;
json_type(cast(f as json))
DOUBLE
select json_type(cast(d as json)) from t1;
json_type(cast(d as json))
DOUBLE
select json_type(cast(bitt as json)) from t1;
json_type(cast(bitt as json))
BIT
select json_type(cast(blb as json)) from t1;
json_type(cast(blb as json))
BLOB
select json_type(cast(bin as json)) from t1;
json_type(cast(bin as json))
BLOB
select json_type(cast(en as json)) from t1;
json_type(cast(en as json))
STRING
select json_type(cast(se as json)) from t1;
json_type(cast(se as json))
STRING
select json_type(cast(ge as json)) from t1;
json_type(cast(ge as json))
OBJECT
select json_type(cast(po as json)) from t1;
json_type(cast(po as json))
OBJECT
select json_type(cast(ls as json)) from t1;
json_type(cast(ls as json))
OBJECT
select json_type(cast(py as json)) from t1;
json_type(cast(py as json))
OBJECT
select json_type(cast(js as json)) from t1;
json_type(cast(js as json))
ARRAY
select cast(dati as json) from t1;
cast(dati as json)
"2014-11-25 18:00:00.000000"
select cast(da as json) from t1;
cast(da as json)
"2014-11-25"
select cast(tim as json) from t1;
cast(tim as json)
"18:00:59.000000"
select cast(ts as json) from t1;
cast(ts as json)
"2014-11-25 18:00:00.000000"
select cast(y as json) from t1;
cast(y as json)
1999
select cast(ti as json) from t1;
cast(ti as json)
127
select cast(tiu as json) from t1;
cast(tiu as json)
255
select cast(si as json) from t1;
cast(si as json)
32767
select cast(siu as json) from t1;
cast(siu as json)
65535
select cast(mi as json) from t1;
cast(mi as json)
8388607
select cast(miu as json) from t1;
cast(miu as json)
16777215
select cast(i as json) from t1;
cast(i as json)
2147483647
select cast(iu as json) from t1;
cast(iu as json)
4294967295
select cast(bi as json) from t1;
cast(bi as json)
9223372036854775807
select cast(biu as json) from t1;
cast(biu as json)
18446744073709551615
select cast(boo as json) from t1;
cast(boo as json)
1
select cast(dc as json) from t1;
cast(dc as json)
3.14
select cast(f as json) from t1;
cast(f as json)
3.140000104904175
select cast(d as json) from t1;
cast(d as json)
3.14
select cast(bitt as json) from t1;
cast(bitt as json)
"base64:type16:ABU="
select cast(blb as json) from t1;
cast(blb as json)
"base64:type252:MTAxMDFhYmNkZQ=="
select cast(bin as json) from t1;
cast(bin as json)
"base64:type254:MTAxMDFhYmNkZQ=="
select cast(en as json) from t1;
cast(en as json)
"b"
select cast(se as json) from t1;
cast(se as json)
"a,c"
select cast(ge as json) from t1;
cast(ge as json)
{"type": "Point", "coordinates": [1.0, 1.0]}
select cast(po as json) from t1;
cast(po as json)
{"type": "Point", "coordinates": [1.0, 1.0]}
select cast(ls as json) from t1;
cast(ls as json)
{"type": "LineString", "coordinates": [[0.0, 0.0], [1.0, 1.0], [2.0, 2.0]]}
select cast(py as json) from t1;
cast(py as json)
{"type": "Polygon", "coordinates": [[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 10.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]]}
select cast(js as json) from t1;
cast(js as json)
[123]
#
# Bug#21442878 INCORRECT RETURN STATUS FROM
#              ITEM_JSON_TYPECAST::VAL_JSON() ON PARSE ERRORS
#
select json_extract(en, '$') from t1;
ERROR 22032: Invalid data type for JSON data in argument 1 to function json_extract; a JSON string or JSON type is required.
drop table t1;
create table t1 ( c1 varchar(200) character set 'latin1',
c2 varchar(200) character set 'utf8mb3' );
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values ('[1,2]',  # legal json, but not utf-8
'[1,2 ');
select cast(c1 as json) from t1;
cast(c1 as json)
[1, 2]
select cast(c2 as json) from t1;
ERROR 22032: Invalid JSON text in argument 1 to function cast_as_json: "Missing a comma or ']' after an array element." at position 5.
select cast(c2 as json) is null from t1;
ERROR 22032: Invalid JSON text in argument 1 to function cast_as_json: "Missing a comma or ']' after an array element." at position 5.
drop table t1;
create table t2(x int);
insert into t2 values (1), (2);
select x, cast(y as json) from (select x, cast(x as json) as y from t2) s order by x;
x	cast(y as json)
1	1
2	2
select x, cast(y as json) from (select x, cast(cast(x as json) as char charset utf8mb3) as y from t2) s order by x;
x	cast(y as json)
1	1
2	2
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
drop table t2;
# ----------------------------------------------------------------------
# Test of CAST(<select> AS JSON)
# ----------------------------------------------------------------------
select cast((select 1) as json);
cast((select 1) as json)
1
create table t(i int, j json, c char(10) character set 'utf8mb3');
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t values (5, '6', '{}');
select cast((select i from t) as json);
cast((select i from t) as json)
5
select cast((select j from t) as json);
cast((select j from t) as json)
6
select cast((select c from t) as json);
cast((select c from t) as json)
{}
select cast((select cast(i as json) from t) as json);
cast((select cast(i as json) from t) as json)
5
select cast((select cast(j as json) from t) as json);
cast((select cast(j as json) from t) as json)
6
select cast((select cast(c as json) from t) as json);
cast((select cast(c as json) from t) as json)
{}
insert into t values (7, '8', '[]');
select cast((select i from t) as json);
ERROR 21000: Subquery returns more than 1 row
delete from t;
insert into t values (null, null, null);
select cast((select i from t) as json);
cast((select i from t) as json)
NULL
select cast((select j from t) as json);
cast((select j from t) as json)
NULL
select cast((select cast(i as json) from t) as json);
cast((select cast(i as json) from t) as json)
NULL
select cast((select cast(j as json) from t) as json);
cast((select cast(j as json) from t) as json)
NULL
select cast((select cast(c as json) from t) as json);
cast((select cast(c as json) from t) as json)
NULL
select cast((select i,i from t) as json);
ERROR 21000: Operand should contain 1 column(s)
select cast((select * from t) as json);
ERROR 21000: Operand should contain 1 column(s)
drop table t;
# ----------------------------------------------------------------------
# Test of JSON_KEYS function.
# ----------------------------------------------------------------------
select i, json_keys(j) from keys1 order by i;
i	json_keys(j)
0	NULL
1	["a"]
2	NULL
3	["a", "c", "ab", "bc"]
4	NULL
5	NULL
6	NULL
7	NULL
8	NULL
9	NULL
10	NULL
11	NULL
12	NULL
13	NULL
14	NULL
15	NULL
16	NULL
17	NULL
18	NULL
19	NULL
20	NULL
21	NULL
22	[]
23	NULL
24	NULL
25	NULL
26	NULL
27	NULL
28	["type", "coordinates"]
29	NULL
30	NULL
31	NULL
delete from keys1;
insert into keys1 values (0, NULL),
(1, '{"a": 1, "b": {"e": "foo", "b": 3}}');
select i, json_keys(j), json_keys(j, '$.b') from keys1 order by i;
i	json_keys(j)	json_keys(j, '$.b')
0	NULL	NULL
1	["a", "b"]	["b", "e"]
select cast(j as char) from keys1 order by i;
cast(j as char)
NULL
{"a": 1, "b": {"b": 3, "e": "foo"}}
create table t(i int);
select cast(json_extract(j, '$.b.b') as char) from keys1 order by i;
cast(json_extract(j, '$.b.b') as char)
NULL
3
insert into t select cast(json_extract(j, '$.b.b') as char) from keys1;
select * from t order by i;
i
NULL
3
drop table t;
drop table keys1;
create table t(j json);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t values ('[ 1, 2, 3, {"a": [4,5,6]}]');
select json_array_append(j, '$[3].a', cast(7 as json)) from t;
json_array_append(j, '$[3].a', cast(7 as json))
[1, 2, 3, {"a": [4, 5, 6, 7]}]
select json_array_append(j, '$', 7) from t;
json_array_append(j, '$', 7)
[1, 2, 3, {"a": [4, 5, 6]}, 7]
select json_array_append(j, '$', cast(7 as json), '$[3].a', 3.14) from t;
json_array_append(j, '$', cast(7 as json), '$[3].a', 3.14)
[1, 2, 3, {"a": [4, 5, 6, 3.14]}, 7]
# second path's append ignored since it doesn't specify an array
# nor is it an existing scalar,  so no auto-wrapping either
select json_array_append(j, '$', 7, '$[3].b', cast(8 as json)) from t;
json_array_append(j, '$', 7, '$[3].b', cast(8 as json))
[1, 2, 3, {"a": [4, 5, 6]}, 7]
drop table t;
create table jdoc( id int, doc json );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into jdoc values
( 1, '[ [ true ], [ false ] ]' ),
( 2, '[ [ 0 ], [ 1 ] ]' ),
( 3, '[ [ "abc" ], [ "def" ] ]' );
select id, json_array_insert( doc, '$[0][1]', 'fred' )
from jdoc order by id;
id	json_array_insert( doc, '$[0][1]', 'fred' )
1	[[true, "fred"], [false]]
2	[[0, "fred"], [1]]
3	[["abc", "fred"], ["def"]]
select id, json_array_insert( doc, '$[1][0]', 'fred' )
from jdoc order by id;
id	json_array_insert( doc, '$[1][0]', 'fred' )
1	[[true], ["fred", false]]
2	[[0], ["fred", 1]]
3	[["abc"], ["fred", "def"]]
drop table jdoc;
create table t( id int, v varchar(10));
insert into t values (1, 'a'), (2, null), (3, 'a');
select id v, json_array_insert('[[1]]', '$[0][0]', v) from t order by id;
v	json_array_insert('[[1]]', '$[0][0]', v)
1	[["a", 1]]
2	[[null, 1]]
3	[["a", 1]]
drop table t;
#
# Bug #21304639: JSON_SET() WITH MULTI-LEG PATH RETURNS DIFFERENT
#                RESULTS ON FIRST ROW VS NEXT
#
create table t21304639(pk int);
insert into t21304639 values (2), (1), (3);
select json_set(
json_object('existing', pk),
'$.key_b.test',
json_object('new', 'apple')
) as field1 from t21304639 order by field1;
field1
{"existing": 2}
{"existing": 3}
{"existing": 1}
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select json_set(
json_object('existing', pk),
'$.key_b.test',
json_object('new', 'apple')
) as field1 from t21304639 order by field1;
field1
{"existing": 2}
{"existing": 3}
{"existing": 1}
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
drop table t21304639;
create table t (i int, j json, d double);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t values (3, '["a", "b"]', 3.14);
select json_array(i, j, d) from t;
json_array(i, j, d)
[3, ["a", "b"], 3.14]
drop table t;
CREATE TABLE t(j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES (JSON_ARRAY(-9223372036854775808, 18446744073709551614));
SELECT * FROM t;
j
[-9223372036854775808, 18446744073709551614]
DROP TABLE t;
create table department( id int, deptName varchar(50), isExempt boolean, blobColumn blob );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into department values ( 405, 'Accounting', true, '<a><b>ccc</b><d></d></a>' );
SELECT JSON_ARRAY( d.deptName, CAST( '{ "processed" : true }' AS JSON ) )
FROM department d
WHERE id = 405;
JSON_ARRAY( d.deptName, CAST( '{ "processed" : true }' AS JSON ) )
["Accounting", {"processed": true}]
create table jsn_table( json_column json );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO jsn_table( json_column )
SELECT JSON_ARRAY( d.deptName, d.id, d.blobColumn )
FROM department d
WHERE id = 405;
drop table jsn_table;
drop table department;
create table misc_dt
(
id int, py polygon
);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into misc_dt values
(
1, ST_GeomFromText('POLYGON((0 0,10 0,10 10,0 10,0 0),
    (5 5,7 5,7 7,5 7, 5 5))')
),
(
2, null
);
select id, json_array( true, py, false ) from misc_dt order by id;
id	json_array( true, py, false )
1	[true, {"type": "Polygon", "coordinates": [[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 10.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]]}, false]
2	[true, null, false]
drop table misc_dt;
create table jro
(
a int,
b varchar( 10 ),
c boolean
);
insert into jro( a, b, c ) values
( 0, 'zero', false ),
( 1, 'one', true ),
( null, null, null );
select a, json_object( 'a', a, 'b', b, 'c', c )
from jro
order by a;
a	json_object( 'a', a, 'b', b, 'c', c )
NULL	{"a": null, "b": null, "c": null}
0	{"a": 0, "b": "zero", "c": 0}
1	{"a": 1, "b": "one", "c": 1}
drop table jro;
create table jro2( a int, b varchar( 10 ), c json );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into jro2 ( a, b, c ) values
( 1, 'array', '[ 1, 2, 3 ]' ), ( 2, 'object', '{ "d": "foo", "e": true }' );
select a, json_object( 'type', b, 'value', c )
from jro2 order by a;
a	json_object( 'type', b, 'value', c )
1	{"type": "array", "value": [1, 2, 3]}
2	{"type": "object", "value": {"d": "foo", "e": true}}
drop table jro2;
create table department( id int, deptName varchar(50), isExempt boolean, blobColumn blob );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into department values ( 405, 'Accounting', true, '<a><b>ccc</b><d></d></a>' );
SELECT JSON_OBJECT
(
'deptName', d.deptName,
'id', d.id,
'isExempt', d.isExempt and true
)
FROM department d
WHERE id = 405;
JSON_OBJECT
(
'deptName', d.deptName,
'id', d.id,
'isExempt', d.isExempt and true
)
{"id": 405, "deptName": "Accounting", "isExempt": true}
drop table department;
create table misc_dt
(
py polygon
);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into misc_dt values
(
ST_GeomFromText('POLYGON((0 0,10 0,10 10,0 10,0 0),
    (5 5,7 5,7 7,5 7, 5 5))')
);
select json_object( py, 'def' ) from misc_dt;
ERROR 22032: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
drop table misc_dt;
create table json_search_table( id_col int, json_col json );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into json_search_table values
( 1, '{ "a": "foobar" }' ),
( 2, '{ "a": "foobar", "b": "focus", "c": [ "arm", "foot", "shoulder" ] }' );
select id_col, json_search( json_col, 'all', 'foo%' )
from json_search_table
order by id_col;
id_col	json_search( json_col, 'all', 'foo%' )
1	"$.a"
2	["$.a", "$.c[1]"]
select id_col, json_search( json_col, 'all', 'foot' )
from json_search_table
order by id_col;
id_col	json_search( json_col, 'all', 'foot' )
1	NULL
2	"$.c[1]"
select id_col, json_search( json_col, 'all', 'f__us' )
from json_search_table
order by id_col;
id_col	json_search( json_col, 'all', 'f__us' )
1	NULL
2	"$.b"
delete from json_search_table;
insert into json_search_table values
( 1, '{ "a": "foobar" }' ),
( 2, '{ "a": [ "foolish", "folly", "foolhardy"  ], "b" : "fool" }' );
select id_col, json_search( json_col, 'all', 'foo%', null, '$.a' )
from json_search_table
order by id_col;
id_col	json_search( json_col, 'all', 'foo%', null, '$.a' )
1	"$.a"
2	["$.a[0]", "$.a[2]"]
select id_col, json_search( json_col, 'all', 'foo%', null, '$.a', '$.b' )
from json_search_table
order by id_col;
id_col	json_search( json_col, 'all', 'foo%', null, '$.a', '$.b' )
1	"$.a"
2	["$.a[0]", "$.a[2]", "$.b"]
select id_col, json_search( json_col, 'one', 'foo%', null, '$.a', '$.b' )
from json_search_table
order by id_col;
id_col	json_search( json_col, 'one', 'foo%', null, '$.a', '$.b' )
1	"$.a"
2	"$.a[0]"
delete from json_search_table;
insert into json_search_table values
( 1, '{ "a": "foobar" }' ),
( 2, '[ { "a": { "b": { "c": "fool" } } }, { "b": { "c": "shoulder" } }, { "c": { "c": "food"} } ]' );
select id_col, json_search( json_col, 'all', 'foo%', null, '$.a', '$**.c' )
from json_search_table
order by id_col;
id_col	json_search( json_col, 'all', 'foo%', null, '$.a', '$**.c' )
1	"$.a"
2	["$[0].a.b.c", "$[2].c.c"]
select id_col, json_search( json_col, 'one', 'foo%', null, '$.a', '$**.c' )
from json_search_table
order by id_col;
id_col	json_search( json_col, 'one', 'foo%', null, '$.a', '$**.c' )
1	"$.a"
2	"$[0].a.b.c"
drop table json_search_table;
create table jep( key_col int primary key, doc json, path varchar( 50 ) );
insert into jep values
( 1, '{ "onepotato": "seven"  }', '$.onepotato' ),
( 2, '{ "one potato": "seven"  }', '$."one potato"' ),
( 3, '{ "one \\"potato": "seven"  }', '$."one \\"potato"' ),
( 4, '{ "one \\npotato": "seven"  }', '$."one \\npotato"' );
select key_col,
json_search( doc, 'all', 'seven' ) paths,
json_unquote( cast( json_search( doc, 'all', 'seven' ) as char ) ) unquoted,
path
from jep order by key_col;
key_col	paths	unquoted	path
1	"$.onepotato"	$.onepotato	$.onepotato
2	"$.\"one potato\""	$."one potato"	$."one potato"
3	"$.\"one \\\"potato\""	$."one \"potato"	$."one \"potato"
4	"$.\"one \\npotato\""	$."one \npotato"	$."one \npotato"
drop table jep;
# ----------------------------------------------------------------------
# Test of CASE and IF expressions returning JSON
# ----------------------------------------------------------------------
create table t(j json);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t values (null), ('[3,4,5]');
select json_type(case (j is null) when 1 then
cast('null' as json) else
cast('[1,2,3]' as json) end) from t order by j;
json_type(case (j is null) when 1 then
cast('null' as json) else
cast('[1,2,3]' as json) end)
NULL
ARRAY
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select json_type(case (j is null) when 1 then cast(1 as json) end) from t order by j;
json_type(case (j is null) when 1 then cast(1 as json) end)
INTEGER
NULL
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select json_type( if(j is null,
cast('{"a": 6}' as json),
cast('[1,2,3]' as json))) from t order by j;
json_type( if(j is null,
cast('{"a": 6}' as json),
cast('[1,2,3]' as json)))
OBJECT
ARRAY
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select json_type( if(j is null,
NULL,
cast('[1,2,3]' as json))    ) from t order by j;
json_type( if(j is null,
NULL,
cast('[1,2,3]' as json))    )
NULL
ARRAY
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
# ----------------------------------------------------------------------
# Test of CASE and IF expressions with mix of JSON and other types
# Common result type is VARCHAR
# ----------------------------------------------------------------------
select json_type(case (j is null) when 1 then
3.14 else
cast('[1,2,3]' as json) end) from t order by j;
json_type(case (j is null) when 1 then
3.14 else
cast('[1,2,3]' as json) end)
DOUBLE
ARRAY
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select case (j is null) when 1 then
3.14 else
cast('[1,2,3]' as json) end from t order by j;
case (j is null) when 1 then
3.14 else
cast('[1,2,3]' as json) end
3.14
[1, 2, 3]
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select case (j is null) when 1 then
'foobar' else
cast('[1,2,3]' as json) end from t order by j;
case (j is null) when 1 then
'foobar' else
cast('[1,2,3]' as json) end
foobar
[1, 2, 3]
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select json_type( if(j is null,
3.14,
cast('[1,2,3]' as json))) from t order by j;
json_type( if(j is null,
3.14,
cast('[1,2,3]' as json)))
DOUBLE
ARRAY
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select if(j is null,
3.14,
cast('[1,2,3]' as json)) from t order by j;
if(j is null,
3.14,
cast('[1,2,3]' as json))
3.14
[1, 2, 3]
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
# ----------------------------------------------------------------------
# Test of IFNULL
# ----------------------------------------------------------------------
select json_type(ifnull(j, cast(3 as json))) from t order by j;
json_type(ifnull(j, cast(3 as json)))
INTEGER
ARRAY
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select ifnull(j, cast(3 as json)) from t order by j;
ifnull(j, cast(3 as json))
3
[3, 4, 5]
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select json_type(ifnull(NULL, cast(3 as json)));
json_type(ifnull(NULL, cast(3 as json)))
INTEGER
select json_type(ifnull(cast(3 as json), NULL));
json_type(ifnull(cast(3 as json), NULL))
INTEGER
SELECT JSON_TYPE(IFNULL(JSON_EXTRACT(CONCAT(t1.j, 'abc'), '$'), t2.j))
FROM t t1, t t2;
ERROR 22032: Invalid JSON text in argument 1 to function json_extract: "The document root must not be followed by other values." at position 9.
SELECT JSON_TYPE(IFNULL(t1.j, JSON_EXTRACT(CONCAT(t2.j, 'abc'), '$')))
FROM t t1, t t2;
ERROR 22032: Invalid JSON text in argument 1 to function json_extract: "The document root must not be followed by other values." at position 9.
# ----------------------------------------------------------------------
# Json values used in text contexts
# ----------------------------------------------------------------------
delete from t;
insert into t values (NULL), (cast('"aBc"' as json));
select upper(j) from t order by j;
upper(j)
NULL
"ABC"
delete from t;
insert into t values (cast(1 as json)), (cast(10 as json)), (cast(2 as json));
select * from t order by j;
j
1
2
10
select max(j) from t;
max(j)
10
select json_type(max(j)) from t;
json_type(max(j))
INTEGER
select min(j) from t;
min(j)
1
select json_type(max(j)) from t;
json_type(max(j))
INTEGER
select max(cast(j as unsigned)) from t;
max(cast(j as unsigned))
10
select json_type(max(cast(j as unsigned))) from t;
ERROR 22032: Invalid data type for JSON data in argument 1 to function json_type; a JSON string or JSON type is required.
drop table t;
# ----------------------------------------------------------------------
# Test JSON arguments and return values of stored functions
# ----------------------------------------------------------------------
create function make_message
(
sender varchar(50),
receiver varchar(50),
subject text,
received datetime,
body text
)
returns json
language sql deterministic no sql
return json_object
(
'sender', sender,
'receiver', receiver,
'subject', subject,
'received', received,
'body', body
);
create function extract_date( message json )
returns datetime
language sql deterministic no sql
return json_extract( message, '$.received' );
create table messages
(
id int,
raw_message json
);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into messages(id, raw_message) values
(
1,
make_message
(
'fred',
'alice',
'lunch today?',
timestamp( '2015-05-11 09:30:05' ),
'How about lunch at 11:30?'
  )
),
(
2,
make_message
(
'alice',
'fred',
're: lunch today?',
timestamp( '2015-05-11 09:45:05' ),
'Sorry. I am in meetings all day long.'
  )
),
(
3,
json_object
(
'sender', 'fred',
'receiver', 'alice',
'subject', 're: lunch today?',
'received', timestamp( '2015-05-11 09:50:05' ),
'body', 'Oh, bummer.'
  )
)
;
select * from messages order by id;
id	raw_message
1	{"body": "How about lunch at 11:30?", "sender": "fred", "subject": "lunch today?", "received": "2015-05-11 09:30:05.000000", "receiver": "alice"}
2	{"body": "Sorry. I am in meetings all day long.", "sender": "alice", "subject": "re: lunch today?", "received": "2015-05-11 09:45:05.000000", "receiver": "fred"}
3	{"body": "Oh, bummer.", "sender": "fred", "subject": "re: lunch today?", "received": "2015-05-11 09:50:05.000000", "receiver": "alice"}
select json_type
(
json_extract
(
json_object
(
'sender', 'fred',
'receiver', 'alice',
'subject', 'lunch today?',
'received', timestamp( '2015-05-11 09:45:05' ),
'body', 'How about lunch at 11:30?'
    ),
'$.received'
  )
) received_type
;
received_type
DATETIME
select id, extract_date( raw_message ) extracted_date
from messages order by id;
id	extracted_date
1	2015-05-11 09:30:05
2	2015-05-11 09:45:05
3	2015-05-11 09:50:05
create function show_received_type( message json )
returns tinytext
language sql deterministic no sql
return json_type( json_extract( message, '$.received' ) );
select show_received_type
(
json_object
(
'sender', 'fred',
'receiver', 'alice',
'subject', 're: lunch today?',
'received', timestamp( '2015-05-11 09:50:05' ),
'body', 'Oh, bummer.'
  )
) received_type;
received_type
DATETIME
select show_received_type
(
make_message
(
'fred',
'alice',
'lunch today?',
timestamp( '2015-05-11 09:30:05' ),
'How about lunch at 11:30?'
     )
) received_type;
received_type
DATETIME
select id, show_received_type( raw_message ) received_type
from messages order by id;
id	received_type
1	DATETIME
2	DATETIME
3	DATETIME
drop function show_received_type;
drop function make_message;
drop function extract_date;
drop table messages;
# Test a function that fails.
CREATE FUNCTION func_that_fails() RETURNS JSON
LANGUAGE SQL DETERMINISTIC NO SQL
RETURN '[not valid json]';
SELECT JSON_EXTRACT(func_that_fails(), '$');
ERROR 22032: Invalid JSON text: "Invalid value." at position 2 in value for column '.func_that_fails()'.
DROP FUNCTION func_that_fails;
create function get_types( input_value json )
returns json
language sql deterministic contains sql
begin
declare array_length integer;
declare return_value json;
declare idx int;
declare path varchar(100);
set array_length = json_length( input_value );
set return_value = json_array();
set idx = 0;
while idx < array_length do
set path = concat( '$[', idx, ']' );
set return_value = json_array_append
(
return_value,
'$',
json_type( json_extract( input_value, path ) )
);
set idx = idx + 1;
end while;
return return_value;
end//
create table blob_table( blob_col blob );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into blob_table values( '10101abcde' );
select json_type( dt.a ), dt.a
from
( select get_types
(
json_array
(
cast( '{}' as json ),
cast( '[]' as json ),
'null',
true,
1,
2.3,
timestamp( '2015-05-11 09:30:05' ),
cast('23:24:25' as time),
cast('2015-01-15' as date),
b'10101',
blob_col
)
) a
from blob_table
) dt;
json_type( dt.a )	a
ARRAY	["OBJECT", "ARRAY", "STRING", "BOOLEAN", "INTEGER", "DECIMAL", "DATETIME", "TIME", "DATE", "BLOB", "BLOB"]
drop table blob_table;
drop function get_types;
create procedure merge_docs
(
inout inout_value json
)
begin
set inout_value = json_object();
end//
create procedure merge_doc_types()
begin
declare proc_inout json;
declare tmp_types varchar(100);
set proc_inout = null;
call merge_docs( proc_inout );
set tmp_types = json_type( proc_inout );
end//
call merge_doc_types();
drop procedure merge_doc_types;
drop procedure merge_docs;
create function get_types( input_value json )
returns json
language sql deterministic contains sql
begin
declare array_length integer;
declare return_value json;
declare idx int;
declare path varchar(100);
set array_length = json_length( input_value );
set return_value = json_array();
set idx = 0;
while idx < array_length do
set path = concat( '$[', idx, ']' );
set return_value = json_array_append
(
return_value,
'$',
json_type( json_extract( input_value, path ) )
);
set idx = idx + 1;
end while;
return return_value;
end//
create procedure merge_docs
(
in in_value json,
inout inout_value json,
out out_value json
)
language sql deterministic contains sql
begin
set out_value = json_merge_preserve(in_value, inout_value);
set inout_value = in_value;
end//
create procedure merge_doc_types
(
out in_types varchar(100),
out inout_types varchar(100),
out out_types varchar(100)
)
language sql deterministic contains sql
begin
declare proc_in json;
declare proc_inout json;
declare proc_out json;
set proc_in = json_array
(
cast( '{}' as json ),
cast( '[]' as json ),
'null',
true
);
set proc_inout = json_array
(
1,
2.3,
timestamp( '2015-05-11 09:30:05' ),
cast('23:24:25' as time),
cast('2015-01-15' as date),
b'10101'
  );
set proc_out = null;
call merge_docs( proc_in, proc_inout, proc_out );
set in_types = get_types( proc_in );
set inout_types = get_types( proc_inout );
set out_types = get_types( proc_out );
end//
call merge_doc_types( @in_types, @inout_types, @out_types );
select @in_types, @inout_types, @out_types;
@in_types	@inout_types	@out_types
["OBJECT", "ARRAY", "STRING", "BOOLEAN"]	["OBJECT", "ARRAY", "STRING", "BOOLEAN"]	["OBJECT", "ARRAY", "STRING", "BOOLEAN", "INTEGER", "DECIMAL", "DATETIME", "TIME", "DATE", "BLOB"]
drop procedure merge_doc_types;
drop procedure merge_docs;
drop function get_types;
#
# Bug#20898238: WRONG RESULT FOR MAX() OF JSON SCALARS RETURNED
#               WHEN NULL IS PRESENT
#
CREATE TABLE bug20898238(j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO bug20898238 VALUES ('{"id":1}'), (NULL), ('{"id":2}'), ('{"id":0}');
SELECT MIN(JSON_EXTRACT(j, '$.id')),
MAX(JSON_EXTRACT(j, '$.id')) FROM bug20898238;
MIN(JSON_EXTRACT(j, '$.id'))	MAX(JSON_EXTRACT(j, '$.id'))
0	2
DROP TABLE bug20898238;
# ----------------------------------------------------------------------
# Test of aggregate function SUM, AVG: in constrast to strings, we do not
# auto-convert to numeric (double) type:
# ----------------------------------------------------------------------
create table t(j json, c varchar(20));
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t values (cast('[1,2,3]' as json), '[a,b,c]');
insert into t values (cast(7 as json), '7'),  (cast(2 as json), '2');
select sum(j), sum(cast(j as unsigned)), sum(c) from t;
sum(j)	sum(cast(j as unsigned))	sum(c)
9	9	9
select avg(j), avg(cast(j as unsigned)), avg(c) from t;
avg(j)	avg(cast(j as unsigned))	avg(c)
3	3.0000	3
# ----------------------------------------------------------------------
# Test of aggregate function COUNT(DISTINCT) and unaggregated DISTINCT
# ----------------------------------------------------------------------
create table t_doc( bucket int, doc json);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t_doc values
( 1, cast( 1 as json ) ),
( 1, cast( 1.0 as json ) ),
( 1, cast( 1e0 as json ) ),
( 2, cast( cast( 1 as unsigned ) as json ) ),
( 2, cast( 2 as json ) ),
( 2, cast( 2.0 as json ) ),
( 3, cast( 2e0 as json ) ),
( 3, cast( cast( 7 as unsigned ) as json ) ),
( 3, cast( 7 as json ) ),
( 4, cast( 7.0 as json ) ),
( 4, cast( 7e0 as json ) ),
( 4, cast( cast( 7 as unsigned ) as json ) ),
( 5, cast( true as json ) ),
( 5, cast( true as json ) ),
( 5, cast( false as json ) ),
( 6, cast( false as json ) ),
( 6, cast( 'null' as json ) ),
( 6, cast( 'null' as json ) ),
( 7, cast( '"abc"' as json ) ),
( 7, cast( '"abc"' as json ) ),
( 7, cast( '"abcd"' as json ) ),
( 8, cast( '"abcd"' as json ) ),
( 8, cast( '{ "a": 1, "b": 2 }' as json ) ),
( 8, cast( '{ "a": 1, "b": 2 }' as json ) ),
( 9, cast( '{ "a": 1, "b": 3 }' as json ) ),
( 9, cast( '{ "a": 1, "b": 3 }' as json ) ),
( 9, cast( '[ true, false ]' as json ) ),
( 10, cast( '[ true, false ]' as json ) ),
( 10, cast( '[ true, true ]' as json ) );
select distinct( doc ) a from t_doc order by a;
a
null
1
2
7
"abc"
"abcd"
{"a": 1, "b": 2}
{"a": 1, "b": 3}
[true, false]
[true, true]
false
true
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select count( distinct doc ) from t_doc;
count( distinct doc )
12
select bucket, count( distinct doc ) from t_doc group by bucket;
bucket	count( distinct doc )
1	1
2	2
3	2
4	1
5	2
6	2
7	2
8	2
9	2
10	2
delete from t_doc;
create table dt(dati datetime, da date,
tim time, ts timestamp,
y year,
--
ti tinyint,   tiu tinyint unsigned,
si smallint,  siu smallint unsigned,
mi mediumint, miu mediumint unsigned,
i  int,       iu  int unsigned,
bi bigint,    biu bigint unsigned,
boo boolean,
--
dc decimal(5,2),
n numeric(5,2),
--
f float, d double,
bitt bit(10),
blb blob,
bin binary(10),
en enum('a','b','c'),
se set('a','b','c'),
--
ge geometry,
po point,
ls linestring,
py polygon,
jso json,
jsa json,
id int
);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into dt values('2014-11-25 18:00', '2014-11-25',
'18:00:59', '2014-11-25 17:00',
'1999',
--
127, 255,
32767, 65535,
8388607, 16777215, -- 3 bytes
2147483647, 4294967295, -- 4 bytes
9223372036854775807, 18446744073709551615,
true,
--
3.1,
3.2,
--
3.3, 3.4,
b'10101',
'10101abcde',
'10001abcde',
'b',
'a,c',
--
ST_GeomFromText('POINT(1 1)'),
ST_GeomFromText('POINT(1 2)'),
ST_GeomFromText('LINESTRING(0 0,1 1,2 2)'),
ST_GeomFromText('POLYGON((0 0,10 0,10 10,0 10,0 0),
                                            (5 5,7 5,7 7,5 7, 5 5))'),
'{"a": 1, "b": 2 }',
'[1, 2]',
1
),
('2013-11-25 18:00', '2013-11-25',
'17:00:59', '2013-11-25 17:00',
'1998',
--
126, 254,
32766, 65534,
8388606, 16777214, -- 3 bytes
2147483646, 4294967294, -- 4 bytes
9223372036854775806, 18446744073709551614,
false,
--
4.1,
4.2,
--
4.3, 4.4,
b'10111',
'10001abcdf',
'10101abcdf',
'a',
'a,b',
--
ST_GeomFromText('POINT(1 3)'),
ST_GeomFromText('POINT(1 4)'),
ST_GeomFromText('LINESTRING(0 0,1 1,2 3)'),
ST_GeomFromText('POLYGON((0 0,10 0,10 10,0 9,0 0),
                                            (5 5,7 5,7 7,5 7, 5 5))'),
'{"a": 1, "b": 3 }',
'[1, 3]',
2
);
insert into t_doc select id, cast(f as json) from dt;
insert into t_doc select id, cast(d as json) from dt;
insert into t_doc select * from t_doc;
select count( distinct doc ) from t_doc;
count( distinct doc )
4
select bucket, count( distinct doc ) from t_doc group by bucket;
bucket	count( distinct doc )
1	2
2	2
delete from t_doc;
insert into t_doc select id, cast(dati as json) from dt;
insert into t_doc select id, cast(da as json) from dt;
insert into t_doc select id, cast(tim as json) from dt;
insert into t_doc select id, cast(ts as json) from dt;
insert into t_doc select id, cast(y as json) from dt;
insert into t_doc select id, cast(ti as json) from dt;
insert into t_doc select id, cast(tiu as json) from dt;
insert into t_doc select id, cast(si as json) from dt;
insert into t_doc select id, cast(siu as json) from dt;
insert into t_doc select id, cast(mi as json) from dt;
insert into t_doc select id, cast(miu as json) from dt;
insert into t_doc select id, cast(i as json) from dt;
insert into t_doc select id, cast(iu as json) from dt;
insert into t_doc select id, cast(bi as json) from dt;
insert into t_doc select id, cast(biu as json) from dt;
insert into t_doc select id, cast(dc as json) from dt;
insert into t_doc select id, cast(n as json) from dt;
insert into t_doc select id, cast(bitt as json) from dt;
insert into t_doc select id, cast(blb as json) from dt;
insert into t_doc select id, cast(bin as json) from dt;
insert into t_doc select id, cast(en as json) from dt;
insert into t_doc select id, cast(se as json) from dt;
insert into t_doc select id, cast(ge as json) from dt;
insert into t_doc select id, cast(po as json) from dt;
insert into t_doc select id, cast(ls as json) from dt;
insert into t_doc select id, cast(py as json) from dt;
insert into t_doc select id, jso from dt;
insert into t_doc select id, jsa from dt;
insert into t_doc select * from t_doc;
select distinct( doc ) a from t_doc order by a;
a
3.10
3.20
4.10
4.20
126
127
254
255
1998
1999
32766
32767
65534
65535
8388606
8388607
16777214
16777215
2147483646
2147483647
4294967294
4294967295
9223372036854775806
9223372036854775807
18446744073709551614
18446744073709551615
"a"
"a,b"
"a,c"
"b"
{"a": 1, "b": 2}
{"a": 1, "b": 3}
{"type": "Polygon", "coordinates": [[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 10.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]]}
{"type": "Polygon", "coordinates": [[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 9.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]]}
{"type": "LineString", "coordinates": [[0.0, 0.0], [1.0, 1.0], [2.0, 2.0]]}
{"type": "LineString", "coordinates": [[0.0, 0.0], [1.0, 1.0], [2.0, 3.0]]}
{"type": "Point", "coordinates": [1.0, 1.0]}
{"type": "Point", "coordinates": [1.0, 4.0]}
{"type": "Point", "coordinates": [1.0, 2.0]}
{"type": "Point", "coordinates": [1.0, 3.0]}
[1, 2]
[1, 3]
"2013-11-25"
"2014-11-25"
"17:00:59.000000"
"18:00:59.000000"
"2013-11-25 17:00:00.000000"
"2013-11-25 18:00:00.000000"
"2014-11-25 17:00:00.000000"
"2014-11-25 18:00:00.000000"
"base64:type16:ABU="
"base64:type16:ABc="
"base64:type252:MTAwMDFhYmNkZg=="
"base64:type252:MTAxMDFhYmNkZQ=="
"base64:type254:MTAwMDFhYmNkZQ=="
"base64:type254:MTAxMDFhYmNkZg=="
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select count( distinct doc ) from t_doc;
count( distinct doc )
56
select bucket, count( distinct doc ) from t_doc group by bucket;
bucket	count( distinct doc )
1	28
2	28
delete from t_doc;
create table ndt(dati datetime,
ts timestamp,
--
ti tinyint,   tiu tinyint unsigned,
si smallint,  siu smallint unsigned,
mi mediumint, miu mediumint unsigned,
i  int,       iu  int unsigned,
bi bigint,    biu bigint unsigned,
--
dc decimal(5,2),
n numeric(5,2),
--
f float, d double,
id int
);
insert into ndt values('2014-11-25 18:00',
'2014-11-25 18:00',
--
1, 1,
1, 1,
1, 1,
1, 1,
1, 1,
--
1.0,
1.0,
--
1.0, 1.0,
1
),
('2013-11-25 18:00',
'2013-11-25 18:00',
--
2, 2,
2, 2,
2, 2,
2, 2,
2, 2,
--
2.0,
2.0,
--
2.0, 2.0,
2
);
insert into t_doc select id, cast(dati as json) from ndt;
insert into t_doc select id, cast(ts as json) from ndt;
insert into t_doc select id, cast(ti as json) from ndt;
insert into t_doc select id, cast(tiu as json) from ndt;
insert into t_doc select id, cast(si as json) from ndt;
insert into t_doc select id, cast(siu as json) from ndt;
insert into t_doc select id, cast(mi as json) from ndt;
insert into t_doc select id, cast(miu as json) from ndt;
insert into t_doc select id, cast(i as json) from ndt;
insert into t_doc select id, cast(iu as json) from ndt;
insert into t_doc select id, cast(bi as json) from ndt;
insert into t_doc select id, cast(biu as json) from ndt;
insert into t_doc select id, cast(dc as json) from ndt;
insert into t_doc select id, cast(n as json) from ndt;
insert into t_doc select id, cast(f as json) from ndt;
insert into t_doc select id, cast(d as json) from ndt;
insert into t_doc select * from t_doc;
select count( distinct doc ) from t_doc;
count( distinct doc )
4
select bucket, count( distinct doc ) from t_doc group by bucket;
bucket	count( distinct doc )
1	2
2	2
drop table t_doc;
drop table dt;
drop table ndt;
# ----------------------------------------------------------------------
# Special CASTing behavior of geometry types
# ----------------------------------------------------------------------
create table jtable( id int, descr varchar(20), doc json );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
create table misc_dt
(
ge geometry,
po point,
ls linestring,
py polygon
);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into misc_dt values
(
ST_GeomFromText('POINT(1 1)'),
ST_GeomFromText('POINT(1 1)'),
ST_GeomFromText('LINESTRING(0 0,1 1,2 2)'),
ST_GeomFromText('POLYGON((0 0,10 0,10 10,0 10,0 0),
    (5 5,7 5,7 7,5 7, 5 5))')
);
insert into jtable select 1, 'geometry', cast(ge as json) from misc_dt;
insert into jtable select 2, 'point', cast(po as json) from misc_dt;
insert into jtable select 3, 'linestring', cast(ls as json) from misc_dt;
insert into jtable select 4, 'polygon', cast(py as json) from misc_dt;
select id, descr, json_type( doc ), doc from jtable order by id;
id	descr	json_type( doc )	doc
1	geometry	OBJECT	{"type": "Point", "coordinates": [1.0, 1.0]}
2	point	OBJECT	{"type": "Point", "coordinates": [1.0, 1.0]}
3	linestring	OBJECT	{"type": "LineString", "coordinates": [[0.0, 0.0], [1.0, 1.0], [2.0, 2.0]]}
4	polygon	OBJECT	{"type": "Polygon", "coordinates": [[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 10.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]]}
select json_object
(
'geometry', ST_GeomFromText('POINT(1 1)'),
'point', ST_GeomFromText('POINT(1 1)'),
'linestring', ST_GeomFromText('LINESTRING(0 0,1 1,2 2)'),
'polygon', ST_GeomFromText('POLYGON((0 0,10 0,10 10,0 10,0 0),
    (5 5,7 5,7 7,5 7, 5 5))')
);
json_object
(
'geometry', ST_GeomFromText('POINT(1 1)'),
'point', ST_GeomFromText('POINT(1 1)'),
'linestring', ST_GeomFromText('LINESTRING(0 0,1 1,2 2)'),
'polygon', ST_GeomFromText('POLYGON((0 0,10 0,10 10,0 10,0 0),
    (5 5,7 5,7 7,5 7, 5 5))')
)
{"point": {"type": "Point", "coordinates": [1.0, 1.0]}, "polygon": {"type": "Polygon", "coordinates": [[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 10.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]]}, "geometry": {"type": "Point", "coordinates": [1.0, 1.0]}, "linestring": {"type": "LineString", "coordinates": [[0.0, 0.0], [1.0, 1.0], [2.0, 2.0]]}}
delete from misc_dt;
select * from misc_dt;
ge	po	ls	py
insert into misc_dt values
(
(select ST_GeomFromGeoJSON( cast( doc as char ) ) from jtable where id = 1),
(select ST_GeomFromGeoJSON( cast( doc as char ) ) from jtable where id = 2),
(select ST_GeomFromGeoJSON( cast( doc as char ) ) from jtable where id = 3),
(select ST_GeomFromGeoJSON( cast( doc as char ) ) from jtable where id = 4)
);
select ST_AsGeoJSON( ge ),
ST_AsGeoJSON( po ),
ST_AsGeoJSON( ls ),
ST_AsGeoJSON( py )
from misc_dt;
ST_AsGeoJSON( ge )	ST_AsGeoJSON( po )	ST_AsGeoJSON( ls )	ST_AsGeoJSON( py )
{"type": "Point", "coordinates": [1.0, 1.0]}	{"type": "Point", "coordinates": [1.0, 1.0]}	{"type": "LineString", "coordinates": [[0.0, 0.0], [1.0, 1.0], [2.0, 2.0]]}	{"type": "Polygon", "coordinates": [[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 10.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]]}
drop table misc_dt;
drop table jtable;
create table jtable( id int, descr varchar(20), doc json );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
create table misc_dt
(
ge geometrycollection,
po multipoint,
ls multilinestring,
py multipolygon
);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into misc_dt values
(
geometrycollection(point(1, 1), point(2, 2)),
multipoint(point(1, 1), point(2, 2)),
multilinestring
(
linestring(point(0, 0), point(1, 1), point(2, 2)),
linestring(point(0, 0), point(11, 11), point(12, 12))
),
multipolygon
(
polygon
(
linestring(point(0, 0), point(10, 0), point(10, 10), point(0, 10), point(0, 0)),
linestring(point(5, 5), point(7, 5), point(7, 7), point(5, 7), point(5, 5))
),
polygon
(
linestring(point(0, 0), point(10, 0), point(10, 10), point(0, 10), point(0, 0)),
linestring(point(5, 5), point(7, 5), point(7, 7), point(5, 7), point(5, 5))
)
)
);
insert into jtable select 1, 'geometrycollection', cast(ge as json) from misc_dt;
insert into jtable select 2, 'multipoint', cast(po as json) from misc_dt;
insert into jtable select 3, 'multilinestring', cast(ls as json) from misc_dt;
insert into jtable select 4, 'multipolygon', cast(py as json) from misc_dt;
select id, descr, json_type( doc ), doc from jtable order by id;
id	descr	json_type( doc )	doc
1	geometrycollection	OBJECT	{"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [1.0, 1.0]}, {"type": "Point", "coordinates": [2.0, 2.0]}]}
2	multipoint	OBJECT	{"type": "MultiPoint", "coordinates": [[1.0, 1.0], [2.0, 2.0]]}
3	multilinestring	OBJECT	{"type": "MultiLineString", "coordinates": [[[0.0, 0.0], [1.0, 1.0], [2.0, 2.0]], [[0.0, 0.0], [11.0, 11.0], [12.0, 12.0]]]}
4	multipolygon	OBJECT	{"type": "MultiPolygon", "coordinates": [[[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 10.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]], [[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 10.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]]]}
select ST_AsGeoJSON( ge ),
ST_AsGeoJSON( po ),
ST_AsGeoJSON( ls ),
ST_AsGeoJSON( py )
from misc_dt;
ST_AsGeoJSON( ge )	ST_AsGeoJSON( po )	ST_AsGeoJSON( ls )	ST_AsGeoJSON( py )
{"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [1.0, 1.0]}, {"type": "Point", "coordinates": [2.0, 2.0]}]}	{"type": "MultiPoint", "coordinates": [[1.0, 1.0], [2.0, 2.0]]}	{"type": "MultiLineString", "coordinates": [[[0.0, 0.0], [1.0, 1.0], [2.0, 2.0]], [[0.0, 0.0], [11.0, 11.0], [12.0, 12.0]]]}	{"type": "MultiPolygon", "coordinates": [[[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 10.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]], [[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 10.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]]]}
delete from misc_dt;
select * from misc_dt;
ge	po	ls	py
insert into misc_dt values
(
(select ST_GeomFromGeoJSON( cast( doc as char ) ) from jtable where id = 1),
(select ST_GeomFromGeoJSON( cast( doc as char ) ) from jtable where id = 2),
(select ST_GeomFromGeoJSON( cast( doc as char ) ) from jtable where id = 3),
(select ST_GeomFromGeoJSON( cast( doc as char ) ) from jtable where id = 4)
);
select ST_AsGeoJSON( ge ),
ST_AsGeoJSON( po ),
ST_AsGeoJSON( ls ),
ST_AsGeoJSON( py )
from misc_dt;
ST_AsGeoJSON( ge )	ST_AsGeoJSON( po )	ST_AsGeoJSON( ls )	ST_AsGeoJSON( py )
{"type": "GeometryCollection", "geometries": [{"type": "Point", "coordinates": [1.0, 1.0]}, {"type": "Point", "coordinates": [2.0, 2.0]}]}	{"type": "MultiPoint", "coordinates": [[1.0, 1.0], [2.0, 2.0]]}	{"type": "MultiLineString", "coordinates": [[[0.0, 0.0], [1.0, 1.0], [2.0, 2.0]], [[0.0, 0.0], [11.0, 11.0], [12.0, 12.0]]]}	{"type": "MultiPolygon", "coordinates": [[[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 10.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]], [[[0.0, 0.0], [10.0, 0.0], [10.0, 10.0], [0.0, 10.0], [0.0, 0.0]], [[5.0, 5.0], [7.0, 5.0], [7.0, 7.0], [5.0, 7.0], [5.0, 5.0]]]]}
drop table misc_dt;
drop table jtable;
# ----------------------------------------------------------------------
# Test of COALESCE
# ----------------------------------------------------------------------
select coalesce(cast(1 as json), cast(2 as json));
coalesce(cast(1 as json), cast(2 as json))
1
select j, coalesce(j, cast(3 as json)) from t;
j	coalesce(j, cast(3 as json))
2	2
7	7
[1, 2, 3]	[1, 2, 3]
select j, coalesce(j, 666) from t;
j	coalesce(j, 666)
2	2
7	7
[1, 2, 3]	[1, 2, 3]
select j, json_type(coalesce(j, '[1,2,3]')) from t;
j	json_type(coalesce(j, '[1,2,3]'))
2	INTEGER
7	INTEGER
[1, 2, 3]	ARRAY
select j, json_type(coalesce(j, 'abc')) from t;
j	json_type(coalesce(j, 'abc'))
2	INTEGER
7	INTEGER
[1, 2, 3]	ARRAY
select j, json_type(coalesce(j, cast('"arg2"' as json))) from t;
j	json_type(coalesce(j, cast('"arg2"' as json)))
2	INTEGER
7	INTEGER
[1, 2, 3]	ARRAY
select j, json_type(coalesce(j, j)) from t;
j	json_type(coalesce(j, j))
2	INTEGER
7	INTEGER
[1, 2, 3]	ARRAY
inconsistent result: error message depends on the order of evaluation of rows
--error ER_INVALID_JSON_TEXT_IN_PARAM
select json_type(coalesce(json_extract(concat(j, 'abc'), '$'), j)) from t;
--error ER_INVALID_JSON_TEXT_IN_PARAM
#select json_type(coalesce(t1.j, json_extract(concat(t2.j, 'abc'), '$')))
from t t1, t t2;
drop table t;
# ----------------------------------------------------------------------
# Auto-convert of non-utf8mb3 returning system function
# ----------------------------------------------------------------------
create table t(j json, id int);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t values ('{"user": "foo"}', 8), (NULL, 8);
update t set j=json_set(j, '$.user', current_user()) where id=8;
select j from t order by j;
j
NULL
{"user": "root@localhost"}
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
update t set j=json_set(j, '$.user', rtrim('foo    '))  where id=8;
select j from t order by j;
j
NULL
{"user": "foo"}
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
update t set j=json_set(j, '$.user', hex('abc'))  where id=8;
select j from t order by j;
j
NULL
{"user": "616263"}
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
update t set j=json_set(j, '$.user', sha2('bingle',0))  where id=8;
select j from t order by j;
j
NULL
{"user": "75f95daa72e02aeff5e4387ec2ab63a223ba46aed29a8d5f375b3fc02ce7bc78"}
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
update t set j=json_set(j, '$.user', database())  where id=8;
select j from t order by j;
j
NULL
{"user": "test"}
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
update t set j=json_set(j, '$.user', schema()) where id=8;
select j from t order by j;
j
NULL
{"user": "test"}
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
update t set j=json_set(j, '$.user',
cast(UNHEX('F0A09C8E') as char character set 'utf8mb4')) where id=8;
set names 'utf8mb4';
select j from t order by j;
j
NULL
{"user": "𠜎"}
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
select char_length(json_extract(j, '$.user')) from t order by j;
char_length(json_extract(j, '$.user'))
NULL
3
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
drop table t;
#
# Bug#21257946 JSON_TYPE(TEXT) OF TABLE COLUMN STICKS WITH NULL
#              AFTER FIRST ENCOUNTER OF NULL
#
CREATE TABLE T_WITH_NULLS(i INT, j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO T_WITH_NULLS VALUES
(0, NULL),
(1, '[1]'),
(2, NULL),
(3, '{"a":"b"}'),
(4, NULL),
(5, '"abc"');
SELECT
JSON_VALID(j),
JSON_TYPE(j),
JSON_KEYS(j),
JSON_EXTRACT(j, '$'),
JSON_REMOVE(j, '$.a.b.c'),
JSON_ARRAY_APPEND(j, '$', 2),
JSON_SET(j, '$[0]', 2),
JSON_INSERT(j, '$[0]', 2),
JSON_REPLACE(j, '$[0]', 2),
JSON_MERGE_PRESERVE(j, j),
JSON_SEARCH(j, 'one', 'abc'),
JSON_CONTAINS(j, '[1]'),
JSON_CONTAINS_PATH(j, 'all', '$.a'),
JSON_LENGTH(j),
JSON_DEPTH(j),
JSON_ARRAY(j, j),
JSON_OBJECT('k', j),
JSON_UNQUOTE(CAST(j AS CHAR)),
JSON_QUOTE(CAST(j AS CHAR)),
JSON_PRETTY(j),
JSON_STORAGE_FREE(j),
JSON_STORAGE_SIZE(j),
JSON_SCHEMA_VALID('{"type":"object"}', j)
FROM T_WITH_NULLS
ORDER BY i;
JSON_VALID(j)	JSON_TYPE(j)	JSON_KEYS(j)	JSON_EXTRACT(j, '$')	JSON_REMOVE(j, '$.a.b.c')	JSON_ARRAY_APPEND(j, '$', 2)	JSON_SET(j, '$[0]', 2)	JSON_INSERT(j, '$[0]', 2)	JSON_REPLACE(j, '$[0]', 2)	JSON_MERGE_PRESERVE(j, j)	JSON_SEARCH(j, 'one', 'abc')	JSON_CONTAINS(j, '[1]')	JSON_CONTAINS_PATH(j, 'all', '$.a')	JSON_LENGTH(j)	JSON_DEPTH(j)	JSON_ARRAY(j, j)	JSON_OBJECT('k', j)	JSON_UNQUOTE(CAST(j AS CHAR))	JSON_QUOTE(CAST(j AS CHAR))	JSON_PRETTY(j)	JSON_STORAGE_FREE(j)	JSON_STORAGE_SIZE(j)	JSON_SCHEMA_VALID('{"type":"object"}', j)
NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	[null, null]	{"k": null}	NULL	NULL	NULL	NULL	NULL	NULL
1	ARRAY	NULL	[1]	[1]	[1, 2]	[2]	[1]	[2]	[1, 1]	NULL	1	0	1	2	[[1], [1]]	{"k": [1]}	[1]	"[1]"	[
  1
]	0	8	0
NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	[null, null]	{"k": null}	NULL	NULL	NULL	NULL	NULL	NULL
1	OBJECT	["a"]	{"a": "b"}	{"a": "b"}	[{"a": "b"}, 2]	2	{"a": "b"}	2	{"a": ["b", "b"]}	NULL	0	1	1	2	[{"a": "b"}, {"a": "b"}]	{"k": {"a": "b"}}	{"a": "b"}	"{\"a\": \"b\"}"	{
  "a": "b"
}	0	15	1
NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	[null, null]	{"k": null}	NULL	NULL	NULL	NULL	NULL	NULL
1	STRING	NULL	"abc"	"abc"	["abc", 2]	2	"abc"	2	["abc", "abc"]	"$"	0	0	1	1	["abc", "abc"]	{"k": "abc"}	abc	"\"abc\""	"abc"	0	5	0
ALTER TABLE T_WITH_NULLS MODIFY COLUMN j TEXT;
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
SELECT
JSON_VALID(j),
JSON_TYPE(j),
JSON_KEYS(j),
JSON_EXTRACT(j, '$'),
JSON_REMOVE(j, '$.a.b.c'),
JSON_ARRAY_APPEND(j, '$', 2),
JSON_SET(j, '$[0]', 2),
JSON_INSERT(j, '$[0]', 2),
JSON_REPLACE(j, '$[0]', 2),
JSON_MERGE_PRESERVE(j, j),
JSON_SEARCH(j, 'one', 'abc'),
JSON_CONTAINS(j, '[1]'),
JSON_CONTAINS_PATH(j, 'all', '$.a'),
JSON_LENGTH(j),
JSON_DEPTH(j),
JSON_ARRAY(j, j),
JSON_OBJECT('k', j),
JSON_UNQUOTE(CAST(j AS CHAR)),
JSON_QUOTE(CAST(j AS CHAR)),
JSON_PRETTY(j),
JSON_STORAGE_FREE(j),
JSON_STORAGE_SIZE(j),
JSON_SCHEMA_VALID('{"type":"object"}', j)
FROM T_WITH_NULLS
ORDER BY i;
JSON_VALID(j)	JSON_TYPE(j)	JSON_KEYS(j)	JSON_EXTRACT(j, '$')	JSON_REMOVE(j, '$.a.b.c')	JSON_ARRAY_APPEND(j, '$', 2)	JSON_SET(j, '$[0]', 2)	JSON_INSERT(j, '$[0]', 2)	JSON_REPLACE(j, '$[0]', 2)	JSON_MERGE_PRESERVE(j, j)	JSON_SEARCH(j, 'one', 'abc')	JSON_CONTAINS(j, '[1]')	JSON_CONTAINS_PATH(j, 'all', '$.a')	JSON_LENGTH(j)	JSON_DEPTH(j)	JSON_ARRAY(j, j)	JSON_OBJECT('k', j)	JSON_UNQUOTE(CAST(j AS CHAR))	JSON_QUOTE(CAST(j AS CHAR))	JSON_PRETTY(j)	JSON_STORAGE_FREE(j)	JSON_STORAGE_SIZE(j)	JSON_SCHEMA_VALID('{"type":"object"}', j)
NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	[null, null]	{"k": null}	NULL	NULL	NULL	NULL	NULL	NULL
1	ARRAY	NULL	[1]	[1]	[1, 2]	[2]	[1]	[2]	[1, 1]	NULL	1	0	1	2	["[1]", "[1]"]	{"k": "[1]"}	[1]	"[1]"	[
  1
]	0	8	0
NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	[null, null]	{"k": null}	NULL	NULL	NULL	NULL	NULL	NULL
1	OBJECT	["a"]	{"a": "b"}	{"a": "b"}	[{"a": "b"}, 2]	2	{"a": "b"}	2	{"a": ["b", "b"]}	NULL	0	1	1	2	["{\"a\": \"b\"}", "{\"a\": \"b\"}"]	{"k": "{\"a\": \"b\"}"}	{"a": "b"}	"{\"a\": \"b\"}"	{
  "a": "b"
}	0	15	1
NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	[null, null]	{"k": null}	NULL	NULL	NULL	NULL	NULL	NULL
1	STRING	NULL	"abc"	"abc"	["abc", 2]	2	"abc"	2	["abc", "abc"]	"$"	0	0	1	1	["\"abc\"", "\"abc\""]	{"k": "\"abc\""}	abc	"\"abc\""	"abc"	0	5	0
DROP TABLE T_WITH_NULLS;
CREATE TABLE t_latin1(id INT PRIMARY KEY AUTO_INCREMENT,
json_text VARCHAR(20),
json_atom_text VARCHAR(20),
json_path VARCHAR(20))
CHARACTER SET 'latin1';
INSERT INTO t_latin1 (json_text, json_atom_text, json_path) VALUES
(CONVERT(X'5B22E6F8E5225D' USING latin1),             # ["\u00e6\u00f8\u00e5"]
CONVERT(X'E5F8E6' USING latin1),                     # \u00e5\u00f8\u00e6
'$[0]'),
(CONVERT(X'7B22E6F8E5223A22E6F8E5227D' USING latin1),
# {"\u00e6\u00f8\u00e5":"\u00e6\u00f8\u00e5"}
CONVERT(X'E5F8E6' USING latin1),                     # \u00e5\u00f8\u00e6
CONVERT(X'242E22E6F8E522' USING latin1));
SELECT * FROM t_latin1 ORDER BY id;
id	json_text	json_atom_text	json_path
1	["æøå"]	åøæ	$[0]
2	{"æøå":"æøå"}	åøæ	$."æøå"
SELECT CAST(json_text AS JSON) FROM t_latin1 ORDER BY id;
CAST(json_text AS JSON)
["æøå"]
{"æøå": "æøå"}
SELECT JSON_VALID(json_text) FROM t_latin1 ORDER BY id;
JSON_VALID(json_text)
1
1
SELECT JSON_VALID(json_atom_text) FROM t_latin1 ORDER BY id;
JSON_VALID(json_atom_text)
0
0
SELECT JSON_TYPE(json_text) FROM t_latin1 ORDER BY id;
JSON_TYPE(json_text)
ARRAY
OBJECT
SELECT JSON_EXTRACT(json_text, json_path) FROM t_latin1 ORDER BY id;
JSON_EXTRACT(json_text, json_path)
"æøå"
"æøå"
SELECT JSON_REMOVE(json_text, json_path) FROM t_latin1 ORDER BY id;
JSON_REMOVE(json_text, json_path)
[]
{}
SELECT JSON_ARRAY_APPEND(json_text, json_path, json_atom_text)
FROM t_latin1 ORDER BY id;
JSON_ARRAY_APPEND(json_text, json_path, json_atom_text)
[["æøå", "åøæ"]]
{"æøå": ["æøå", "åøæ"]}
SELECT JSON_SET(json_text, json_path, json_atom_text) FROM t_latin1 ORDER BY id;
JSON_SET(json_text, json_path, json_atom_text)
["åøæ"]
{"æøå": "åøæ"}
SELECT JSON_INSERT(json_text, json_path, json_atom_text)
FROM t_latin1 ORDER BY id;
JSON_INSERT(json_text, json_path, json_atom_text)
["æøå"]
{"æøå": "æøå"}
SELECT JSON_REPLACE(json_text, json_path, json_atom_text)
FROM t_latin1 ORDER BY id;
JSON_REPLACE(json_text, json_path, json_atom_text)
["åøæ"]
{"æøå": "åøæ"}
SELECT JSON_MERGE_PRESERVE(json_text, json_text) FROM t_latin1 ORDER BY id;
JSON_MERGE_PRESERVE(json_text, json_text)
["æøå", "æøå"]
{"æøå": ["æøå", "æøå"]}
SELECT JSON_SEARCH(json_text, CONVERT('one' USING latin1), json_atom_text,
CONVERT(X'F8' USING latin1), json_path)
FROM t_latin1 ORDER BY id;
JSON_SEARCH(json_text, CONVERT('one' USING latin1), json_atom_text,
CONVERT(X'F8' USING latin1), json_path)
NULL
NULL
SELECT JSON_CONTAINS(json_text, json_text, json_path) FROM t_latin1 ORDER BY id;
JSON_CONTAINS(json_text, json_text, json_path)
0
0
SELECT JSON_CONTAINS_PATH(json_text, CONVERT('one' USING latin1), json_path)
FROM t_latin1 ORDER BY id;
JSON_CONTAINS_PATH(json_text, CONVERT('one' USING latin1), json_path)
1
1
SELECT JSON_LENGTH(json_text, json_path) FROM t_latin1 ORDER BY id;
JSON_LENGTH(json_text, json_path)
1
1
SELECT JSON_DEPTH(json_text) FROM t_latin1 ORDER BY id;
JSON_DEPTH(json_text)
2
2
SELECT JSON_ARRAY(json_atom_text, json_atom_text) FROM t_latin1 ORDER BY id;
JSON_ARRAY(json_atom_text, json_atom_text)
["åøæ", "åøæ"]
["åøæ", "åøæ"]
SELECT JSON_OBJECT(json_atom_text, json_atom_text) FROM t_latin1 ORDER BY id;
JSON_OBJECT(json_atom_text, json_atom_text)
{"åøæ": "åøæ"}
{"åøæ": "åøæ"}
SELECT JSON_UNQUOTE(json_atom_text) FROM t_latin1 ORDER BY id;
JSON_UNQUOTE(json_atom_text)
åøæ
åøæ
SELECT JSON_UNQUOTE(CONVERT(CONCAT('"', json_atom_text, '"') USING latin1))
FROM t_latin1 ORDER BY id;
JSON_UNQUOTE(CONVERT(CONCAT('"', json_atom_text, '"') USING latin1))
åøæ
åøæ
SELECT JSON_QUOTE(json_atom_text) FROM t_latin1 ORDER BY id;
JSON_QUOTE(json_atom_text)
"åøæ"
"åøæ"
DROP TABLE t_latin1;
# ----------------------------------------------------------------------
# Test that boolean expressions are treated as boolean atom literals
# ----------------------------------------------------------------------
create table t_bool_literals( a int, b varchar(10) );
insert into t_bool_literals values ( 1, 'food' ), ( 2, 'fool' ), ( 3, 'water' );
select a, json_array( ((a < 3) and (a > 1)) ) from t_bool_literals order by a;
a	json_array( ((a < 3) and (a > 1)) )
1	[false]
2	[true]
3	[false]
select a, json_array_append( '[]', '$', ((a < 3) and (a > 1)) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', ((a < 3) and (a > 1)) )
1	[false]
2	[true]
3	[false]
select a, json_array( not ((a < 3) and (a > 1)) ) from t_bool_literals order by a;
a	json_array( not ((a < 3) and (a > 1)) )
1	[true]
2	[false]
3	[true]
select a, json_array_append( '[]', '$', not ((a < 3) and (a > 1)) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', not ((a < 3) and (a > 1)) )
1	[true]
2	[false]
3	[true]
select a, json_array( ((a < 3) or (a > 1)) ) from t_bool_literals order by a;
a	json_array( ((a < 3) or (a > 1)) )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', ((a < 3) or (a > 1)) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', ((a < 3) or (a > 1)) )
1	[true]
2	[true]
3	[true]
select a, json_array( not ((a < 3) or (a > 1)) ) from t_bool_literals order by a;
a	json_array( not ((a < 3) or (a > 1)) )
1	[false]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', not ((a < 3) or (a > 1)) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', not ((a < 3) or (a > 1)) )
1	[false]
2	[false]
3	[false]
select json_array( not true, not false );
json_array( not true, not false )
[false, true]
select json_array_append( '[]', '$', not true, '$', not false );
json_array_append( '[]', '$', not true, '$', not false )
[false, true]
select a, json_array( 1 and true ) from t_bool_literals order by a;
a	json_array( 1 and true )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', 1 and true ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', 1 and true )
1	[true]
2	[true]
3	[true]
select a, json_array( not 1 ) from t_bool_literals order by a;
a	json_array( not 1 )
1	[false]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', not 1 ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', not 1 )
1	[false]
2	[false]
3	[false]
select json_array( true, false );
json_array( true, false )
[true, false]
select json_array_append( '[]', '$', true, '$', false );
json_array_append( '[]', '$', true, '$', false )
[true, false]
select a, json_array( (a < 3) ) from t_bool_literals order by a;
a	json_array( (a < 3) )
1	[true]
2	[true]
3	[false]
select a, json_array_append( '[]', '$', (a < 3) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', (a < 3) )
1	[true]
2	[true]
3	[false]
select a, json_array( (a <= 3) ) from t_bool_literals order by a;
a	json_array( (a <= 3) )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', (a <= 3) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', (a <= 3) )
1	[true]
2	[true]
3	[true]
select a, json_array( (a > 3) ) from t_bool_literals order by a;
a	json_array( (a > 3) )
1	[false]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', (a > 3) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', (a > 3) )
1	[false]
2	[false]
3	[false]
select a, json_array( (a >= 3) ) from t_bool_literals order by a;
a	json_array( (a >= 3) )
1	[false]
2	[false]
3	[true]
select a, json_array_append( '[]', '$', (a >= 3) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', (a >= 3) )
1	[false]
2	[false]
3	[true]
select a, json_array( (a <> 3) ) from t_bool_literals order by a;
a	json_array( (a <> 3) )
1	[true]
2	[true]
3	[false]
select a, json_array_append( '[]', '$', (a <> 3) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', (a <> 3) )
1	[true]
2	[true]
3	[false]
select a, json_array( (a != 3) ) from t_bool_literals order by a;
a	json_array( (a != 3) )
1	[true]
2	[true]
3	[false]
select a, json_array_append( '[]', '$', (a != 3) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', (a != 3) )
1	[true]
2	[true]
3	[false]
select a, json_array( a is null ) from t_bool_literals order by a;
a	json_array( a is null )
1	[false]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', a is null ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', a is null )
1	[false]
2	[false]
3	[false]
select a, json_array( a is not null ) from t_bool_literals order by a;
a	json_array( a is not null )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', a is not null ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', a is not null )
1	[true]
2	[true]
3	[true]
select a, json_array( a is true ) from t_bool_literals order by a;
a	json_array( a is true )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', a is true ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', a is true )
1	[true]
2	[true]
3	[true]
select a, json_array( a is not true ) from t_bool_literals order by a;
a	json_array( a is not true )
1	[false]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', a is not true ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', a is not true )
1	[false]
2	[false]
3	[false]
select a, json_array(nullif(true, false)  ) from t_bool_literals order by a;
a	json_array(nullif(true, false)  )
1	[1]
2	[1]
3	[1]
select a, json_array_append
(
'[]',
'$',  nullif(true, false)
) from t_bool_literals order by a;
a	json_array_append
(
'[]',
'$',  nullif(true, false)
)
1	[1]
2	[1]
3	[1]
select a, json_array( (case when (a > 1) then true else false end) and true ) from t_bool_literals order by a;
a	json_array( (case when (a > 1) then true else false end) and true )
1	[false]
2	[true]
3	[true]
select a, json_array_append
(
'[]',
'$', (case when (a > 1) then true else false end) and true
) from t_bool_literals order by a;
a	json_array_append
(
'[]',
'$', (case when (a > 1) then true else false end) and true
)
1	[false]
2	[true]
3	[true]
select a, json_array( a between 2 and 4 ) from t_bool_literals order by a;
a	json_array( a between 2 and 4 )
1	[false]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', a between 2 and 4 ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', a between 2 and 4 )
1	[false]
2	[true]
3	[true]
select a, json_array( a in (1,3) ) from t_bool_literals order by a;
a	json_array( a in (1,3) )
1	[true]
2	[false]
3	[true]
select a, json_array_append( '[]', '$', a in (1,3) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', a in (1,3) )
1	[true]
2	[false]
3	[true]
select a, json_array( b like 'foo%' ) from t_bool_literals order by a;
a	json_array( b like 'foo%' )
1	[true]
2	[true]
3	[false]
select a, json_array_append( '[]', '$', b like 'foo%' ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b like 'foo%' )
1	[true]
2	[true]
3	[false]
select a, json_array( b REGEXP '^fo+d' ) from t_bool_literals order by a;
a	json_array( b REGEXP '^fo+d' )
1	[true]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', b REGEXP '^fo+d' ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b REGEXP '^fo+d' )
1	[true]
2	[false]
3	[false]
select a, json_array( b rlike '^fo+d' ) from t_bool_literals order by a;
a	json_array( b rlike '^fo+d' )
1	[true]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', b rlike '^fo+d' ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b rlike '^fo+d' )
1	[true]
2	[false]
3	[false]
select a, json_array( b not REGEXP '^fo+d' ) from t_bool_literals order by a;
a	json_array( b not REGEXP '^fo+d' )
1	[false]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', b not REGEXP '^fo+d' ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b not REGEXP '^fo+d' )
1	[false]
2	[true]
3	[true]
select a, json_array( b not rlike '^fo+d' ) from t_bool_literals order by a;
a	json_array( b not rlike '^fo+d' )
1	[false]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', b not rlike '^fo+d' ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b not rlike '^fo+d' )
1	[false]
2	[true]
3	[true]
select a, json_array( b = some( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b = some( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', b = some( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b = some( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[true]
select a, json_array( b = all( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b = all( select b from t_bool_literals ) )
1	[false]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', b = all( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b = all( select b from t_bool_literals ) )
1	[false]
2	[false]
3	[false]
select a, json_array( b = any( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b = any( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', b = any( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b = any( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[true]
select a, json_array( b > some( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b > some( select b from t_bool_literals ) )
1	[false]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', b > some( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b > some( select b from t_bool_literals ) )
1	[false]
2	[true]
3	[true]
select a, json_array( b > all( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b > all( select b from t_bool_literals ) )
1	[false]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', b > all( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b > all( select b from t_bool_literals ) )
1	[false]
2	[false]
3	[false]
select a, json_array( b > any( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b > any( select b from t_bool_literals ) )
1	[false]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', b > any( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b > any( select b from t_bool_literals ) )
1	[false]
2	[true]
3	[true]
select a, json_array( b < some( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b < some( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[false]
select a, json_array_append( '[]', '$', b < some( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b < some( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[false]
select a, json_array( b < all( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b < all( select b from t_bool_literals ) )
1	[false]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', b < all( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b < all( select b from t_bool_literals ) )
1	[false]
2	[false]
3	[false]
select a, json_array( b < any( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b < any( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[false]
select a, json_array_append( '[]', '$', b < any( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b < any( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[false]
select a, json_array( b <= some( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b <= some( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', b <= some( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b <= some( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[true]
select a, json_array( b <= all( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b <= all( select b from t_bool_literals ) )
1	[true]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', b <= all( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b <= all( select b from t_bool_literals ) )
1	[true]
2	[false]
3	[false]
select a, json_array( b <= any( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b <= any( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', b <= any( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b <= any( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[true]
select a, json_array( b >= some( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b >= some( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', b >= some( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b >= some( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[true]
select a, json_array( b >= all( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b >= all( select b from t_bool_literals ) )
1	[false]
2	[false]
3	[true]
select a, json_array_append( '[]', '$', b >= all( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b >= all( select b from t_bool_literals ) )
1	[false]
2	[false]
3	[true]
select a, json_array( b >= any( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array( b >= any( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', b >= any( select b from t_bool_literals ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', b >= any( select b from t_bool_literals ) )
1	[true]
2	[true]
3	[true]
select a, json_array( exists( select b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array( exists( select b from t_bool_literals where a = 1 ) )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', exists( select b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', exists( select b from t_bool_literals where a = 1 ) )
1	[true]
2	[true]
3	[true]
select a, json_array( not exists( select b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array( not exists( select b from t_bool_literals where a = 1 ) )
1	[false]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', not exists( select b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', not exists( select b from t_bool_literals where a = 1 ) )
1	[false]
2	[false]
3	[false]
select a, json_array( json_valid( b ) ) from t_bool_literals order by a;
a	json_array( json_valid( b ) )
1	[false]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', json_valid( b ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', json_valid( b ) )
1	[false]
2	[false]
3	[false]
select a, json_array( not json_valid( b ) ) from t_bool_literals order by a;
a	json_array( not json_valid( b ) )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', not json_valid( b ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$', not json_valid( b ) )
1	[true]
2	[true]
3	[true]
select json_array( json_contains_path( '{ "a" : { "b" : 100 } }', 'all', '$.a.b' ) );
json_array( json_contains_path( '{ "a" : { "b" : 100 } }', 'all', '$.a.b' ) )
[true]
select a, json_array( gtid_subset('3E11FA47-71CA-11E1-9E33-C80AA9429562:23', '3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57') )
from t_bool_literals order by a;
a	json_array( gtid_subset('3E11FA47-71CA-11E1-9E33-C80AA9429562:23', '3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57') )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$', gtid_subset('3E11FA47-71CA-11E1-9E33-C80AA9429562:23', '3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57') )
from t_bool_literals order by a;
a	json_array_append( '[]', '$', gtid_subset('3E11FA47-71CA-11E1-9E33-C80AA9429562:23', '3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57') )
1	[true]
2	[true]
3	[true]
select a, json_array( not gtid_subset('3E11FA47-71CA-11E1-9E33-C80AA9429562:23', '3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57') )
from t_bool_literals order by a;
a	json_array( not gtid_subset('3E11FA47-71CA-11E1-9E33-C80AA9429562:23', '3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57') )
1	[false]
2	[false]
3	[false]
select a, json_array_append( '[]', '$', not gtid_subset('3E11FA47-71CA-11E1-9E33-C80AA9429562:23', '3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57') )
from t_bool_literals order by a;
a	json_array_append( '[]', '$', not gtid_subset('3E11FA47-71CA-11E1-9E33-C80AA9429562:23', '3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57') )
1	[false]
2	[false]
3	[false]
select a, json_array( b = ( select distinct b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array( b = ( select distinct b from t_bool_literals where a = 1 ) )
1	[true]
2	[false]
3	[false]
select a, json_array_append( '[]', '$',  b = ( select distinct b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$',  b = ( select distinct b from t_bool_literals where a = 1 ) )
1	[true]
2	[false]
3	[false]
select a, json_array( b > ( select distinct b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array( b > ( select distinct b from t_bool_literals where a = 1 ) )
1	[false]
2	[true]
3	[true]
select a, json_array_append( '[]', '$',  b > ( select distinct b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$',  b > ( select distinct b from t_bool_literals where a = 1 ) )
1	[false]
2	[true]
3	[true]
select a, json_array( b >= ( select distinct b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array( b >= ( select distinct b from t_bool_literals where a = 1 ) )
1	[true]
2	[true]
3	[true]
select a, json_array_append( '[]', '$',  b >= ( select distinct b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$',  b >= ( select distinct b from t_bool_literals where a = 1 ) )
1	[true]
2	[true]
3	[true]
select a, json_array( b < ( select distinct b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array( b < ( select distinct b from t_bool_literals where a = 1 ) )
1	[false]
2	[false]
3	[false]
select a, json_array_append( '[]', '$',  b < ( select distinct b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$',  b < ( select distinct b from t_bool_literals where a = 1 ) )
1	[false]
2	[false]
3	[false]
select a, json_array( b <= ( select distinct b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array( b <= ( select distinct b from t_bool_literals where a = 1 ) )
1	[true]
2	[false]
3	[false]
select a, json_array_append( '[]', '$',  b <= ( select distinct b from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array_append( '[]', '$',  b <= ( select distinct b from t_bool_literals where a = 1 ) )
1	[true]
2	[false]
3	[false]
select a, json_array( ( select distinct a from t_bool_literals where a = 1 ) ) from t_bool_literals order by a;
a	json_array( ( select distinct a from t_bool_literals where a = 1 ) )
1	[1]
2	[1]
3	[1]
drop table t_bool_literals;
# ----------------------------------------------------------------------
# Verify that all of the string types behave similarly when used as ANY_JSON_ATOMS
# ----------------------------------------------------------------------
create table t_char( a int, b char(20) );
insert into t_char values ( 1, 'foo' );
create table t_varchar( a int, b varchar(20) );
insert into t_varchar values ( 1, 'foo' );
Create table t_tinytext( a int, b tinytext );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t_tinytext values ( 1, 'foo' );
create table t_text( a int, b text );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t_text values ( 1, 'foo' );
create table t_mediumtext( a int, b mediumtext );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t_mediumtext values ( 1, 'foo' );
create table t_longtext( a int, b longtext );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t_longtext values ( 1, 'foo' );
select json_array( b ) from t_char;
json_array( b )
["foo"]
select json_array( b ) from t_varchar;
json_array( b )
["foo"]
select json_array( b ) from t_tinytext;
json_array( b )
["foo"]
select json_array( b ) from t_text;
json_array( b )
["foo"]
select json_array( b ) from t_mediumtext;
json_array( b )
["foo"]
select json_array( b ) from t_longtext;
json_array( b )
["foo"]
select json_array( cast( b as char ) ) from t_char;
json_array( cast( b as char ) )
["foo"]
select json_array( cast( b as char ) ) from t_varchar;
json_array( cast( b as char ) )
["foo"]
select json_array( cast( b as char ) ) from t_tinytext;
json_array( cast( b as char ) )
["foo"]
select json_array( cast( b as char ) ) from t_text;
json_array( cast( b as char ) )
["foo"]
select json_array( cast( b as char ) ) from t_mediumtext;
json_array( cast( b as char ) )
["foo"]
select json_array( cast( b as char ) ) from t_longtext;
json_array( cast( b as char ) )
["foo"]
select json_array( UpdateXML('<a><b>ccc</b><d></d></a>', '/a/d', '<e>fff</e>') );
json_array( UpdateXML('<a><b>ccc</b><d></d></a>', '/a/d', '<e>fff</e>') )
["<a><b>ccc</b><e>fff</e></a>"]
select json_array( cast( UpdateXML('<a><b>ccc</b><d></d></a>', '/a/d', '<e>fff</e>') as char ) );
json_array( cast( UpdateXML('<a><b>ccc</b><d></d></a>', '/a/d', '<e>fff</e>') as char ) )
["<a><b>ccc</b><e>fff</e></a>"]
select json_array( ExtractValue('<r><n id="1">v1</n><n id="2">v2</n></r>','//n[@id=1]' ) );
json_array( ExtractValue('<r><n id="1">v1</n><n id="2">v2</n></r>','//n[@id=1]' ) )
["v1"]
select json_array( cast( ExtractValue('<r><n id="1">v1</n><n id="2">v2</n></r>','//n[@id=1]' ) as char ) );
json_array( cast( ExtractValue('<r><n id="1">v1</n><n id="2">v2</n></r>','//n[@id=1]' ) as char ) )
["v1"]
drop table t_char;
drop table t_varchar;
drop table t_tinytext;
drop table t_text;
drop table t_mediumtext;
drop table t_longtext;
# ----------------------------------------------------------------------
# Check that JSON values stemming from views and derived tables work
# ----------------------------------------------------------------------
create table t(x int);
insert into t values (NULL), (4);
select json_array(x) from (select x from t) tt order by x;
json_array(x)
[null]
[4]
create view v as select * from t;
select json_array(x) from v order by x;
json_array(x)
[null]
[4]
drop view v;
drop table t;
# ----------------------------------------------------------------------
# Ignore collation.collation when handing off val_str to a JSON field -
# bug found by John E.
# ----------------------------------------------------------------------
create table t3( col_json json );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t3(col_json) values ( json_quote( '1' ) );
select * from t3;
col_json
"1"
select json_type(col_json) from t3;
json_type(col_json)
STRING
drop table t3;
# ----------------------------------------------------------------------
# Correctly escape key names when pretty-printing JSON objects.
# Correct behavior means that the strings can be re-used for
# their original purposes as key names and paths.
# ----------------------------------------------------------------------
create table jep( key_col int primary key, doc json, path varchar( 50 ) );
insert into jep values
( 1, '{ "one \\"potato": "seven"  }', '$."one \\"potato"' ),
( 2, '{ "one \\npotato": "seven"  }', '$."one \\npotato"' ),
( 3, '{ "one \\tpotato": "seven"  }', '$."one \\tpotato"' ),
( 4, '{ "one \\bpotato": "seven"  }', '$."one \\bpotato"' ),
( 5, '{ "one \\fpotato": "seven"  }', '$."one \\fpotato"' ),
( 6, '{ "one \\rpotato": "seven"  }', '$."one \\rpotato"' ),
( 7, '{ "one \\\\potato": "seven"  }', '$."one \\\\potato"' );
insert into jep select key_col + 100, cast( doc as char ), path from jep;
select key_col, doc, json_keys( doc ) from jep order by key_col;
key_col	doc	json_keys( doc )
1	{"one \"potato": "seven"}	["one \"potato"]
2	{"one \npotato": "seven"}	["one \npotato"]
3	{"one \tpotato": "seven"}	["one \tpotato"]
4	{"one \bpotato": "seven"}	["one \bpotato"]
5	{"one \fpotato": "seven"}	["one \fpotato"]
6	{"one \rpotato": "seven"}	["one \rpotato"]
7	{"one \\potato": "seven"}	["one \\potato"]
101	{"one \"potato": "seven"}	["one \"potato"]
102	{"one \npotato": "seven"}	["one \npotato"]
103	{"one \tpotato": "seven"}	["one \tpotato"]
104	{"one \bpotato": "seven"}	["one \bpotato"]
105	{"one \fpotato": "seven"}	["one \fpotato"]
106	{"one \rpotato": "seven"}	["one \rpotato"]
107	{"one \\potato": "seven"}	["one \\potato"]
select key_col, doc, json_extract( doc, cast(path as char) ) from jep order by key_col;
key_col	doc	json_extract( doc, cast(path as char) )
1	{"one \"potato": "seven"}	"seven"
2	{"one \npotato": "seven"}	"seven"
3	{"one \tpotato": "seven"}	"seven"
4	{"one \bpotato": "seven"}	"seven"
5	{"one \fpotato": "seven"}	"seven"
6	{"one \rpotato": "seven"}	"seven"
7	{"one \\potato": "seven"}	"seven"
101	{"one \"potato": "seven"}	"seven"
102	{"one \npotato": "seven"}	"seven"
103	{"one \tpotato": "seven"}	"seven"
104	{"one \bpotato": "seven"}	"seven"
105	{"one \fpotato": "seven"}	"seven"
106	{"one \rpotato": "seven"}	"seven"
107	{"one \\potato": "seven"}	"seven"
select * from jep order by key_col;
key_col	doc	path
1	{"one \"potato": "seven"}	$."one \"potato"
2	{"one \npotato": "seven"}	$."one \npotato"
3	{"one \tpotato": "seven"}	$."one \tpotato"
4	{"one \bpotato": "seven"}	$."one \bpotato"
5	{"one \fpotato": "seven"}	$."one \fpotato"
6	{"one \rpotato": "seven"}	$."one \rpotato"
7	{"one \\potato": "seven"}	$."one \\potato"
101	{"one \"potato": "seven"}	$."one \"potato"
102	{"one \npotato": "seven"}	$."one \npotato"
103	{"one \tpotato": "seven"}	$."one \tpotato"
104	{"one \bpotato": "seven"}	$."one \bpotato"
105	{"one \fpotato": "seven"}	$."one \fpotato"
106	{"one \rpotato": "seven"}	$."one \rpotato"
107	{"one \\potato": "seven"}	$."one \\potato"
drop table jep;
# ----------------------------------------------------------------------
# Test that cached, constant path objects are restored
# after the leg popping which happens inside json_insert()
# and json_replace().
# ----------------------------------------------------------------------
create table t_cache( id int, doc json );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t_cache values
( 1, '{ "a": { "b": 1 } }' ),
( 2, '{ "a": { "c": 1 } }' ),
( 3, '{ "a": { "d": 1 } }' );
select id, doc, json_insert( doc, '$.a.c', 2 ) from t_cache order by id;
id	doc	json_insert( doc, '$.a.c', 2 )
1	{"a": {"b": 1}}	{"a": {"b": 1, "c": 2}}
2	{"a": {"c": 1}}	{"a": {"c": 1}}
3	{"a": {"d": 1}}	{"a": {"c": 2, "d": 1}}
select id, doc, json_insert( doc, '$.a.c', 2, '$.a.d', 3 ) from t_cache order by id;
id	doc	json_insert( doc, '$.a.c', 2, '$.a.d', 3 )
1	{"a": {"b": 1}}	{"a": {"b": 1, "c": 2, "d": 3}}
2	{"a": {"c": 1}}	{"a": {"c": 1, "d": 3}}
3	{"a": {"d": 1}}	{"a": {"c": 2, "d": 1}}
delete from t_cache;
insert into t_cache values
( 1, '{ "a": { "b": 1, "c": 2, "d": 3 } }' ),
( 2, '{ "a": { "c": 2, "d": 3 } }' ),
( 3, '{ "a": { "b": 1, "d": 3 } }' ),
( 4, '{ "a": { "b": 1, "c": 2 } }' ),
( 5, '{ "a": { "b": 1 } }' ),
( 6, '{ "a": { "c": 2 } }' ),
( 7, '{ "a": { "d": 3 } }' ),
( 8, '{ "a": {} }' );
select id, doc, json_replace( doc, '$.a.c', 20 ) from t_cache order by id;
id	doc	json_replace( doc, '$.a.c', 20 )
1	{"a": {"b": 1, "c": 2, "d": 3}}	{"a": {"b": 1, "c": 20, "d": 3}}
2	{"a": {"c": 2, "d": 3}}	{"a": {"c": 20, "d": 3}}
3	{"a": {"b": 1, "d": 3}}	{"a": {"b": 1, "d": 3}}
4	{"a": {"b": 1, "c": 2}}	{"a": {"b": 1, "c": 20}}
5	{"a": {"b": 1}}	{"a": {"b": 1}}
6	{"a": {"c": 2}}	{"a": {"c": 20}}
7	{"a": {"d": 3}}	{"a": {"d": 3}}
8	{"a": {}}	{"a": {}}
select id, doc, json_replace( doc, '$.a.c', 20, '$.a.d', 30 ) from t_cache order by id;
id	doc	json_replace( doc, '$.a.c', 20, '$.a.d', 30 )
1	{"a": {"b": 1, "c": 2, "d": 3}}	{"a": {"b": 1, "c": 20, "d": 30}}
2	{"a": {"c": 2, "d": 3}}	{"a": {"c": 20, "d": 30}}
3	{"a": {"b": 1, "d": 3}}	{"a": {"b": 1, "d": 30}}
4	{"a": {"b": 1, "c": 2}}	{"a": {"b": 1, "c": 20}}
5	{"a": {"b": 1}}	{"a": {"b": 1}}
6	{"a": {"c": 2}}	{"a": {"c": 20}}
7	{"a": {"d": 3}}	{"a": {"d": 30}}
8	{"a": {}}	{"a": {}}
drop table t_cache;
# ----------------------------------------------------------------------
# Test that one_or_all arguments are cached correctly.
# ----------------------------------------------------------------------
create table t_ooa( id int, doc json, one_or_all varchar(10) );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t_ooa values
( 1, '{ "a": 1, "b": 2, "c": 3 }', 'one' ),
( 2, '{ "d": 4 }', 'one' ),
( 3, '{ "a": 1, "b": 2, "d": 4 }', 'all' ),
( 4, '{ "a": 1, "c": 3 }', 'all' ),
( 5, '{ "d": 4 }', 'all' ),
( 6, '{ "a": 1, "b": 2, "c": 3 }', null );
select id, doc, one_or_all, json_contains_path( doc, one_or_all, '$.a', '$.b' ) from t_ooa order by id;
id	doc	one_or_all	json_contains_path( doc, one_or_all, '$.a', '$.b' )
1	{"a": 1, "b": 2, "c": 3}	one	1
2	{"d": 4}	one	0
3	{"a": 1, "b": 2, "d": 4}	all	1
4	{"a": 1, "c": 3}	all	0
5	{"d": 4}	all	0
6	{"a": 1, "b": 2, "c": 3}	NULL	NULL
select id, doc, json_contains_path( doc, 'one', '$.a', '$.b' ) from t_ooa order by id;
id	doc	json_contains_path( doc, 'one', '$.a', '$.b' )
1	{"a": 1, "b": 2, "c": 3}	1
2	{"d": 4}	0
3	{"a": 1, "b": 2, "d": 4}	1
4	{"a": 1, "c": 3}	1
5	{"d": 4}	0
6	{"a": 1, "b": 2, "c": 3}	1
select id, doc, json_contains_path( doc, 'all', '$.a', '$.b' ) from t_ooa order by id;
id	doc	json_contains_path( doc, 'all', '$.a', '$.b' )
1	{"a": 1, "b": 2, "c": 3}	1
2	{"d": 4}	0
3	{"a": 1, "b": 2, "d": 4}	1
4	{"a": 1, "c": 3}	0
5	{"d": 4}	0
6	{"a": 1, "b": 2, "c": 3}	1
select id, doc, json_contains_path( doc, null, '$.a', '$.b' ) from t_ooa order by id;
id	doc	json_contains_path( doc, null, '$.a', '$.b' )
1	{"a": 1, "b": 2, "c": 3}	NULL
2	{"d": 4}	NULL
3	{"a": 1, "b": 2, "d": 4}	NULL
4	{"a": 1, "c": 3}	NULL
5	{"d": 4}	NULL
6	{"a": 1, "b": 2, "c": 3}	NULL
delete from t_ooa;
insert into t_ooa values
( 1, '{ "a": "foot", "b": "fool", "c": "food" }', 'one' ),
( 1, '{ "a": "foot", "b": "fool", "c": "food" }', 'all' ),
( 1, '{ "a": "foot", "b": "fool", "c": "food" }', null );
select id, doc, one_or_all, json_search( doc, one_or_all, 'foo%' ) from t_ooa order by id;
id	doc	one_or_all	json_search( doc, one_or_all, 'foo%' )
1	{"a": "foot", "b": "fool", "c": "food"}	NULL	NULL
1	{"a": "foot", "b": "fool", "c": "food"}	all	["$.a", "$.b", "$.c"]
1	{"a": "foot", "b": "fool", "c": "food"}	one	"$.a"
select id, doc, json_search( doc, 'one', 'foo%' ) from t_ooa order by id;
id	doc	json_search( doc, 'one', 'foo%' )
1	{"a": "foot", "b": "fool", "c": "food"}	"$.a"
1	{"a": "foot", "b": "fool", "c": "food"}	"$.a"
1	{"a": "foot", "b": "fool", "c": "food"}	"$.a"
select id, doc, json_search( doc, 'all', 'foo%' ) from t_ooa order by id;
id	doc	json_search( doc, 'all', 'foo%' )
1	{"a": "foot", "b": "fool", "c": "food"}	["$.a", "$.b", "$.c"]
1	{"a": "foot", "b": "fool", "c": "food"}	["$.a", "$.b", "$.c"]
1	{"a": "foot", "b": "fool", "c": "food"}	["$.a", "$.b", "$.c"]
select id, doc, json_search( doc, null, 'foo%' ) from t_ooa order by id;
id	doc	json_search( doc, null, 'foo%' )
1	{"a": "foot", "b": "fool", "c": "food"}	NULL
1	{"a": "foot", "b": "fool", "c": "food"}	NULL
1	{"a": "foot", "b": "fool", "c": "food"}	NULL
drop table t_ooa;
CREATE TABLE t1(j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
CREATE TABLE t2(j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t1 VALUES ('[1]'), ('[2]'), ('[3]'), ('[4]');
INSERT INTO t2 VALUES ('[1]');
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN UPDATE t1, t2 SET t1.j = JSON_INSERT(t2.j, '$[1]', t2.j) WHERE t1.j=t2.j;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
1	UPDATE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	4	25.00	Using where
Warnings:
Note	1003	Table 't2' is not pushable: select list can't contain BLOB columns
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`j` = json_insert(`test`.`t2`.`j`,'$[1]',`test`.`t2`.`j`) where (`test`.`t1`.`j` = `test`.`t2`.`j`)
UPDATE t1, t2 SET t1.j = JSON_INSERT(t2.j, '$[1]', t2.j) WHERE t1.j=t2.j;
SELECT * FROM t1 ORDER BY (CAST(j AS CHAR));
j
[1, [1]]
[2]
[3]
[4]
DROP TABLE t1, t2;
#
# Bug#20888919: ASSERT `!THD->IS_ERROR()' FAILED IN HANDLE_QUERY()
#               ON EXPLAIN SELECT JSON
#
create table t (pk int primary key, col_json json);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
explain SELECT col_json FROM t WHERE pk = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t	p0,p1,p2,p3,p4,p5,p6,p7	eq_ref	PRIMARY	PRIMARY	4	const	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t`.`col_json` AS `col_json` from `test`.`t` where (`test`.`t`.`pk` = 1)
drop table t;
# ----------------------------------------------------------------------
# Bug#20889248 Used to crash the server
# ----------------------------------------------------------------------
create table tt(i int, j json, si int);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
select count(*) , json_keys('{"key17": {"a": {"b": "c"}}, "key88": "value94"}');
count(*)	json_keys('{"key17": {"a": {"b": "c"}}, "key88": "value94"}')
1	["key17", "key88"]
insert into tt(i, j)
select count(*), json_extract('{"key17": {"a": {"b": "c"}}, "key88": 100}',
'$.key88');
insert into tt(i, si)
select count(*), json_extract('{"key17": {"a": {"b": "c"}}, "key88": 100}',
'$.key88');
select * from tt order by i;
i	j	si
1	100	NULL
1	NULL	100
delete from tt;
insert into tt(j) values (cast(1 as json)), (null);
select sum( distinct j ) from tt group by j having j in ( avg( 1 ), 1 + j);
sum( distinct j )
1
SELECT JSON_ARRAY(j), COUNT(*) FROM tt GROUP BY j, i WITH ROLLUP;
JSON_ARRAY(j)	COUNT(*)
[null]	1
[null]	1
[1]	1
[1]	1
[null]	2
SELECT REPEAT('abc', j), COUNT(*) FROM tt GROUP BY j, i WITH ROLLUP;
REPEAT('abc', j)	COUNT(*)
NULL	1
NULL	1
abc	1
abc	1
NULL	2
SELECT REPEAT(j, 2), COUNT(*) FROM tt GROUP BY j, i WITH ROLLUP;
REPEAT(j, 2)	COUNT(*)
NULL	1
NULL	1
11	1
11	1
NULL	2
SELECT CAST(j AS DECIMAL(5,2)), COUNT(*) FROM tt GROUP BY j, i WITH ROLLUP;
CAST(j AS DECIMAL(5,2))	COUNT(*)
NULL	1
NULL	1
1.00	1
1.00	1
NULL	2
UPDATE tt SET j = CAST(CAST('12:13:14' AS TIME) AS JSON) WHERE j IS NOT NULL;
SELECT CAST(j AS TIME), COUNT(*) FROM tt GROUP BY j, i WITH ROLLUP;
CAST(j AS TIME)	COUNT(*)
NULL	1
NULL	1
12:13:14	1
12:13:14	1
NULL	2
SELECT CAST(j AS DATE) = CURRENT_DATE, COUNT(*) FROM tt
GROUP BY j, i WITH ROLLUP;
CAST(j AS DATE) = CURRENT_DATE	COUNT(*)
NULL	1
NULL	1
NULL	1
NULL	1
NULL	2
Warnings:
Warning	3156	Invalid JSON value for CAST to DATE/TIME/DATETIME/TIMESTAMP from column j at row 3
Warning	3156	Invalid JSON value for CAST to DATE/TIME/DATETIME/TIMESTAMP from column j at row 4
UPDATE tt SET j = CAST(CAST('2015-06-19' AS DATE) AS JSON) WHERE j IS NOT NULL;
SELECT CAST(j AS DATE), COUNT(*) FROM tt GROUP BY j, i WITH ROLLUP;
CAST(j AS DATE)	COUNT(*)
NULL	1
NULL	1
2015-06-19	1
2015-06-19	1
NULL	2
DELETE FROM tt;
INSERT INTO tt(j) VALUES (JSON_ARRAY(REPEAT('abc', 100)));
UPDATE tt SET j = JSON_ARRAY(j,j,j,j);
SET GLOBAL net_buffer_length = 1024;
SET GLOBAL max_allowed_packet = 1024;
SELECT REPEAT(j, 2), COUNT(*) FROM tt GROUP BY j, i WITH ROLLUP;
REPEAT(j, 2)	COUNT(*)
	1
	1
NULL	1
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
Warning	1301	Result of j() was larger than max_allowed_packet (1024) - truncated
Warning	1301	Result of j() was larger than max_allowed_packet (1024) - truncated
SELECT REPEAT(j, 2), COUNT(*) FROM tt GROUP BY j, i;
REPEAT(j, 2)	COUNT(*)
	1
SET GLOBAL max_allowed_packet = default;
SET GLOBAL net_buffer_length = default;
DROP TABLE tt;
# ----------------------------------------------------------------------
# Bug#20914054 Used to crash the server
# ----------------------------------------------------------------------
CREATE TABLE t1 (
pk INT NOT NULL,
col_int_key INT,
col_json json,
PRIMARY KEY (pk),
KEY col_int_key (col_int_key)
);
INSERT INTO t1 VALUES (8, 4, '{}');
CREATE TABLE t2 (
pk INT NOT NULL,
PRIMARY KEY (pk)
);
INSERT INTO t2 VALUES (20);
SELECT MIN(JSON_KEYS( t1.col_json )) AS field1
FROM t1 JOIN t2
HAVING field1 = 7;
field1
drop table t1;
drop table t2;
# ----------------------------------------------------------------------
# Bug#20920788 Used to give SQL state 22032: Cannot create a JSON value
# from a string with CHARACTER SET 'binary'.
#----------------------------------------------------------------------
CREATE TABLE t (
col_json JSON,
col_varchar VARCHAR(1),
col_varchar_key VARCHAR(1),
KEY col_varchar_key (col_varchar_key)
);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES ('{}', 'a', 'a');
# This always succeeded, group by column is indexed, optimizer does not
# use filesort:
SELECT MAX(col_json) AS field1, col_varchar_key AS field2 FROM t GROUP BY field2;
field1	field2
{}	a
# This used to fail, group by column is not indexed, EXPLAIN says
# filesort is used:
SELECT MAX(col_json) AS field1, col_varchar AS field2 FROM t GROUP BY field2;
field1	field2
{}	a
drop table t;
# ----------------------------------------------------------------------
# Bug#20962317 WARNING 3150 'INVALID JSON VALUE FOR CAST TO INTEGER' ON
#              SUBQUERY IN JSON_VALID
#----------------------------------------------------------------------
create table myt(col_json json);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into myt values ('{}');
# This statement used to give two wrong warnings
select json_valid((select col_json from myt));
json_valid((select col_json from myt))
1
drop table myt;
# ----------------------------------------------------------------------
# Bug#20954309 JSON_SEARCH() IN VIEWS DOES NOT WORK, ALWAYS RETURNS NULL
#----------------------------------------------------------------------
CREATE TABLE t_20954309 (id int, col_json JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t_20954309 VALUES
(2, '{"keyA": "eleven"}');
CREATE VIEW v1_20954309 AS SELECT id, JSON_SEARCH(col_json, 'one', 'ele%' ) FROM t_20954309;
CREATE VIEW v2_20954309 AS SELECT id, col_json FROM t_20954309;
SELECT id, JSON_SEARCH(col_json, 'one', 'ele%' ) from t_20954309 order by id;
id	JSON_SEARCH(col_json, 'one', 'ele%' )
2	"$.keyA"
SELECT id, JSON_SEARCH(col_json, 'one', 'eleven' ) from v2_20954309 order by id;
id	JSON_SEARCH(col_json, 'one', 'eleven' )
2	"$.keyA"
SELECT * FROM v1_20954309 order by id;
id	JSON_SEARCH(col_json, 'one', 'ele%' )
2	"$.keyA"
drop view v1_20954309;
drop view v2_20954309;
drop table t_20954309;
create table t_20954309 (id int, doc JSON, search_string varchar(20), escape_char varchar(10) );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t_20954309 values
(1, '{"match11": "eleven", "match12": "element", "notMatch": "elven" }', 'ele%', null ),
(2, '{"match21": "eleven", "match22": "element", "notMatch": "elven" }', 'ele%', 'z' ),
(3, '{"match31": "tw%elve", "match32": "tw%ilight", "notMatch": "twitter" }', 'tw|%%', '|' );
select id, json_search( doc, 'all', search_string, '|' ) from t_20954309 order by id;
id	json_search( doc, 'all', search_string, '|' )
1	["$.match11", "$.match12"]
2	["$.match21", "$.match22"]
3	["$.match31", "$.match32"]
create view v_20954309 as select id, json_search( doc, 'all', search_string, '|' ) from t_20954309 order by id;
select * from v_20954309;
id	json_search( doc, 'all', search_string, '|' )
1	["$.match11", "$.match12"]
2	["$.match21", "$.match22"]
3	["$.match31", "$.match32"]
select id, json_search( doc, 'all', search_string, null ) from t_20954309 where id < 3 order by id;
id	json_search( doc, 'all', search_string, null )
1	["$.match11", "$.match12"]
2	["$.match21", "$.match22"]
create view v2_20954309 as select id, json_search( doc, 'all', search_string, null ) result from t_20954309 where id < 3 order by id;
select * from v2_20954309;
id	result
1	["$.match11", "$.match12"]
2	["$.match21", "$.match22"]
drop view v_20954309;
drop view v2_20954309;
drop table t_20954309;
create table t_doc (id int, doc JSON );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t_doc values
(1, '{"match11": "eleven", "match12": "element", "notMatch": "elven" }' ),
(2, '{"match21": "eleven", "match22": "element", "notMatch": "elven" }' ),
(3, '{"match31": "tw%elve", "match32": "tw%ilight", "notMatch": "twitter" }' );
create table t_search_string (id int, search_string varchar(20) );
insert into t_search_string values
(1, 'ele%' ),
(2, 'ele%' ),
(3, 'tw|%%' );
select t.id, json_search( doc, 'all', (select search_string from t_search_string s where s.id = t.id), '|' )
from t_doc t order by id;
id	json_search( doc, 'all', (select search_string from t_search_string s where s.id = t.id), '|' )
1	["$.match11", "$.match12"]
2	["$.match21", "$.match22"]
3	["$.match31", "$.match32"]
create view v_doc as
select t.id, json_search( doc, 'all', (select search_string from t_search_string s where s.id = t.id), '|' )
from t_doc t order by id;
select * from v_doc;
id	Name_exp_2
1	["$.match11", "$.match12"]
2	["$.match21", "$.match22"]
3	["$.match31", "$.match32"]
drop view v_doc;
drop table t_doc;
drop table t_search_string;
set names default;
#
# Bug#20972793 ASSERT FIELD_TYPE() == MYSQL_TYPE_JSON...
#              IN ARG_COMPARATOR::COMPARE_JSON
#
CREATE TABLE t1 (
pk INT NOT NULL,
col_int_key INT,
col_int INT,
col_json JSON,
PRIMARY KEY (pk),
KEY col_int_key (col_int_key)
);
INSERT INTO t1 VALUES (2,4,2,NULL);
CREATE TABLE t2 (
pk INT NOT NULL,
col_int_key INT,
PRIMARY KEY (pk),
KEY col_int_key (col_int_key)
);
SELECT
(SELECT MAX(sq1_alias1.pk) AS sq1_field1
FROM (t1 AS sq1_alias1
INNER JOIN t2 AS sq1_alias2
ON (sq1_alias2.col_int_key = sq1_alias1.col_int_key)
)
WHERE sq1_alias2.pk <= alias1.col_int
) AS field1,
MAX(alias1.col_json) AS field2
FROM (
SELECT sq2_alias1.*
FROM t1 AS sq2_alias1
) AS alias1
GROUP BY field1
HAVING field2 > 1;
field1	field2
DROP TABLE t1, t2;
# ----------------------------------------------------------------------
# Bug#20987329 VALUE OF PREPARED STATEMENT PLACEHOLDER FOR PARAMETER
#              IN JSON_EXTRACT IS STICKY
#----------------------------------------------------------------------
CREATE TABLE t_reuse (pk INT, col_json JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t_reuse VALUES (1, '{"keyA": 1}'), (2, '{"keyA": 2, "keyB": 22}');
PREPARE getjson FROM 'SELECT JSON_EXTRACT(col_json, ?) FROM t_reuse order by pk';
SET @mypath = '$.keyA';
EXECUTE getjson USING @mypath;
JSON_EXTRACT(col_json, ?)
1
2
SET @mypath = '$.keyB';
EXECUTE getjson USING @mypath;
JSON_EXTRACT(col_json, ?)
NULL
22
drop table t_reuse;
#
# Test that max_allowed_packet is respected.
#
SET GLOBAL net_buffer_length = 1024;
SET GLOBAL max_allowed_packet = 1024;
CREATE TABLE t1(j JSON);
INSERT INTO t1 VALUES (JSON_ARRAY(REPEAT('abc', 100)));
SELECT JSON_ARRAY(j, j, j, j) FROM t1;
JSON_ARRAY(j, j, j, j)
NULL
Warnings:
Warning	1301	Result of json_array() was larger than max_allowed_packet (1024) - truncated
UPDATE t1 SET j = JSON_ARRAY(j, j, j, j);
ERROR HY000: Result of json_binary::serialize() was larger than max_allowed_packet (1024) - truncated
CREATE TABLE t2(s TEXT);
INSERT INTO t2 SELECT JSON_ARRAY(j, j, j, j) FROM t1;
ERROR HY000: Result of json_array() was larger than max_allowed_packet (1024) - truncated
SELECT * FROM t2;
s
INSERT INTO t2 SELECT * FROM t1;
UPDATE t2 SET s = JSON_ARRAY(s, s, s, s);
ERROR HY000: Result of json_array() was larger than max_allowed_packet (1024) - truncated
DROP TABLE t1, t2;
SET GLOBAL max_allowed_packet = default;
SET GLOBAL net_buffer_length = default;
#
# Test that very deep documents are rejected.
#
CREATE TABLE t(jarray JSON, jobject JSON, jmix JSON) ROW_FORMAT=DYNAMIC;
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES ('1', '1', '1');
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(0, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(1, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(2, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(3, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(4, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(5, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(6, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(7, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(8, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(9, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(10, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(11, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(12, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(13, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(14, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(15, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(16, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(17, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(18, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(19, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(20, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(21, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(22, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(23, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(24, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(25, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(26, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(27, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(28, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(29, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(30, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(31, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(32, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(33, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(34, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(35, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(36, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(37, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(38, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(39, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(40, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(41, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(42, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(43, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(44, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(45, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(46, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(47, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(48, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(49, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(50, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(51, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(52, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(53, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(54, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(55, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(56, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(57, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(58, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(59, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(60, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(61, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(62, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(63, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(64, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(65, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(66, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(67, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(68, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(69, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(70, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(71, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(72, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(73, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(74, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(75, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(76, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(77, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(78, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(79, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(80, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(81, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(82, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(83, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(84, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(85, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(86, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(87, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(88, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(89, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(90, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(91, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(92, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(93, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(94, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(95, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(96, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(97, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(98, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
UPDATE t SET jarray  = JSON_ARRAY(jarray),
jobject = JSON_OBJECT('a', jobject),
jmix    = CASE WHEN MOD(99, 2) = 0
THEN JSON_ARRAY(jmix)
ELSE JSON_OBJECT('a', jmix)
END;
SELECT JSON_DEPTH(jarray), JSON_DEPTH(jobject), JSON_DEPTH(jmix) FROM t;
JSON_DEPTH(jarray)	JSON_DEPTH(jobject)	JSON_DEPTH(jmix)
101	101	101
SELECT JSON_DEPTH(CAST(jarray AS CHAR)),
JSON_DEPTH(CAST(jobject AS CHAR)),
JSON_DEPTH(CAST(jmix AS CHAR)) FROM t;
JSON_DEPTH(CAST(jarray AS CHAR))	JSON_DEPTH(CAST(jobject AS CHAR))	JSON_DEPTH(CAST(jmix AS CHAR))
101	101	101
SELECT JSON_VALID(jarray), JSON_VALID(jobject), JSON_VALID(jmix) FROM t;
JSON_VALID(jarray)	JSON_VALID(jobject)	JSON_VALID(jmix)
1	1	1
SELECT JSON_VALID(CAST(jarray AS CHAR)),
JSON_VALID(CAST(jobject AS CHAR)),
JSON_VALID(CAST(jmix AS CHAR)) FROM t;
JSON_VALID(CAST(jarray AS CHAR))	JSON_VALID(CAST(jobject AS CHAR))	JSON_VALID(CAST(jmix AS CHAR))
1	1	1
INSERT INTO t(jarray) SELECT JSON_ARRAY(jarray) FROM t;
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t(jobject) SELECT JSON_OBJECT('a', jobject) FROM t;
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t(jmix) SELECT JSON_ARRAY(jmix) FROM t;
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t(jmix) SELECT JSON_OBJECT('a', jmix) FROM t;
ERROR 22032: The JSON document exceeds the maximum depth.
CREATE TABLE too_deep_docs(id INT PRIMARY KEY AUTO_INCREMENT, x text);
INSERT INTO too_deep_docs(x) SELECT CONCAT('[', jarray, ']') FROM t;
INSERT INTO too_deep_docs(x) SELECT CONCAT('[', jobject, ']') FROM t;
INSERT INTO too_deep_docs(x) SELECT CONCAT('[', jmix, ']') FROM t;
INSERT INTO too_deep_docs(x) SELECT CONCAT('{"a":', jarray, '}') FROM t;
INSERT INTO too_deep_docs(x) SELECT CONCAT('{"a":', jobject, '}') FROM t;
INSERT INTO too_deep_docs(x) SELECT CONCAT('{"a":', jmix, '}') FROM t;
INSERT INTO too_deep_docs(x) SELECT CONCAT('["abc", [', jarray, ']]') FROM t;
INSERT INTO too_deep_docs(x)
SELECT CONCAT('{"a":2,"b":{"c":', jobject, '}}') FROM t;
SELECT CAST(x AS JSON) FROM too_deep_docs WHERE id=8;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_DEPTH(x) FROM too_deep_docs WHERE id=8;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_VALID(x) FROM too_deep_docs WHERE id=8;
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t(jarray) SELECT x FROM too_deep_docs WHERE id=8;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT CAST(x AS JSON) FROM too_deep_docs WHERE id=7;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_DEPTH(x) FROM too_deep_docs WHERE id=7;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_VALID(x) FROM too_deep_docs WHERE id=7;
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t(jarray) SELECT x FROM too_deep_docs WHERE id=7;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT CAST(x AS JSON) FROM too_deep_docs WHERE id=6;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_DEPTH(x) FROM too_deep_docs WHERE id=6;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_VALID(x) FROM too_deep_docs WHERE id=6;
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t(jarray) SELECT x FROM too_deep_docs WHERE id=6;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT CAST(x AS JSON) FROM too_deep_docs WHERE id=5;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_DEPTH(x) FROM too_deep_docs WHERE id=5;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_VALID(x) FROM too_deep_docs WHERE id=5;
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t(jarray) SELECT x FROM too_deep_docs WHERE id=5;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT CAST(x AS JSON) FROM too_deep_docs WHERE id=4;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_DEPTH(x) FROM too_deep_docs WHERE id=4;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_VALID(x) FROM too_deep_docs WHERE id=4;
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t(jarray) SELECT x FROM too_deep_docs WHERE id=4;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT CAST(x AS JSON) FROM too_deep_docs WHERE id=3;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_DEPTH(x) FROM too_deep_docs WHERE id=3;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_VALID(x) FROM too_deep_docs WHERE id=3;
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t(jarray) SELECT x FROM too_deep_docs WHERE id=3;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT CAST(x AS JSON) FROM too_deep_docs WHERE id=2;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_DEPTH(x) FROM too_deep_docs WHERE id=2;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_VALID(x) FROM too_deep_docs WHERE id=2;
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t(jarray) SELECT x FROM too_deep_docs WHERE id=2;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT CAST(x AS JSON) FROM too_deep_docs WHERE id=1;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_DEPTH(x) FROM too_deep_docs WHERE id=1;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT JSON_VALID(x) FROM too_deep_docs WHERE id=1;
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t(jarray) SELECT x FROM too_deep_docs WHERE id=1;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT CAST(JSON_ARRAY(jarray) AS CHAR) FROM t;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT CAST(JSON_OBJECT('a', jobject) AS CHAR) FROM t;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT CAST(JSON_ARRAY(jmix) AS CHAR) FROM t;
ERROR 22032: The JSON document exceeds the maximum depth.
SELECT CAST(JSON_OBJECT('a', jmix) AS CHAR) FROM t;
ERROR 22032: The JSON document exceeds the maximum depth.
DROP TABLE t, too_deep_docs;
#
# Bug#21054252 QUERY HAVING SQL_BIG_RESULT ON JSON DATA GIVES INVALID
#              DATA ERROR
#
CREATE TABLE t1 (i1 INT, KEY(i1));
CREATE TABLE t2 (i2 INT, j2 JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t2 (i2, j2) VALUES
(1, '["a"]'),
(2, '["ab"]');
SELECT SQL_BIG_RESULT i1, j2
FROM t2 LEFT JOIN t1 ON i2 < i1 GROUP BY j2 ORDER BY i2;
i1	j2
NULL	["a"]
NULL	["ab"]
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'sorting of non-scalar JSON values'
DROP TABLE t1, t2;
#
# Bug#21104470 WL8132:ASSERTION `! IS_SET()' FAILED.
#
CREATE TABLE t_21104470(j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t_21104470 VALUES (NULL), (NULL);
SELECT j FROM t_21104470 GROUP BY j;
j
NULL
SELECT DISTINCT j FROM t_21104470;
j
NULL
DROP TABLE t_21104470;
CREATE TABLE t(j JSON NOT NULL);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t(j) VALUES (DEFAULT);
ERROR HY000: Field 'j' doesn't have a default value
SET sql_mode = '';
INSERT INTO t(j) VALUES (DEFAULT);
Warnings:
Warning	1364	Field 'j' doesn't have a default value
SELECT * FROM t;
j
null
SET sql_mode = default;
DROP TABLE t;
#
# Bug#21072360 ASSERTION `(*A)->FIELD_TYPE() == MYSQL_TYPE_JSON ||
#              (*B)->FIELD_TYPE() == FAILED
#
CREATE TABLE t (j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES ('true'), ('"abc"');
SELECT j FROM t WHERE j <= 'xyz' AND j = 'abc';
j
"abc"
DROP TABLE t;
#
# Bug#21094905 VALGRIND ERRORS WITH LATEST BUILDS OF WL7909
#
CREATE TABLE F (i1 INT, j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO F VALUES (1, '1'), (2, '2');
CREATE TABLE H (i2 INT);
SELECT SUM(DISTINCT i2), j FROM F LEFT JOIN H ON i1 = i2 GROUP BY j ORDER BY j;
SUM(DISTINCT i2)	j
NULL	1
NULL	2
DROP TABLE F, H;
#
# Bug#21110783 WL8132:DEBUG CRASH AT WRAPPER_TO_STRING | SQL/JSON_DOM.CC
#
CREATE TABLE t1 (i1 INT) ENGINE=MyISAM;
INSERT INTO t1 VALUES (1);
CREATE TABLE t2 (i2 INT, j JSON) ENGINE=MyISAM;
CREATE TABLE t3(v VARCHAR(100), j JSON) ENGINE=MyISAM;
INSERT INTO t3(v) SELECT j FROM t1 LEFT JOIN t2 ON i1 = i2 GROUP BY j;
INSERT INTO t3(j) SELECT j FROM t1 LEFT JOIN t2 ON i1 = i2 GROUP BY j;
SELECT * FROM t3;
v	j
NULL	NULL
NULL	NULL
DROP TABLE t1, t2, t3;
#
# Bug#21119971 WL8132:DEBUG CRASH AT ITEM_CACHE_JSON::CACHE_VALUE
#
CREATE TABLE t1(j1 JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
CREATE TABLE t2(j2 JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
CREATE TABLE t3(j3 JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t1 VALUES ('1');
INSERT INTO t2 VALUES ('1');
INSERT INTO t3 VALUES ('1');
SELECT * FROM t1 WHERE j1 >= ALL (SELECT j3 FROM t2 LEFT JOIN t3 ON (j2 > j3));
j1
DROP TABLE t1, t2, t3;
#
# Bug#21145759 ER_INVALID_CAST_TO_JSON ON CALL TO JSON_REMOVE
#              WITH EMPTY ARG + ORDER BY
#
CREATE TABLE t (pk INT PRIMARY KEY, col_json JSON);
INSERT INTO t VALUES (1, JSON_OBJECT());
SELECT JSON_REMOVE((SELECT col_json FROM t WHERE pk = 3),
'$.testpath1') AS field1
FROM t HAVING field1 >= 'c' ORDER BY field1;
field1
SELECT JSON_REMOVE((SELECT col_json FROM t WHERE pk = 3),
'$.testpath1') AS field1
FROM t HAVING field1 >= 'c';
field1
SELECT JSON_REMOVE((SELECT col_json FROM t WHERE pk = 3),
'$.testpath1') AS field1
FROM t ORDER BY field1;
field1
NULL
SELECT JSON_REMOVE((SELECT col_json FROM t WHERE pk = 3),
'$.testpath1') AS field1
FROM t;
field1
NULL
DROP TABLE t;
#
# Bug#21135312 BUG IN JSON COMPARE OPARATOR WHEN USING JSON_QUOTE
#
CREATE TABLE t1(c VARCHAR(10) CHARACTER SET latin1);
INSERT INTO t1 VALUES ('abc'), ('"abc"');
CREATE TABLE t2(j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t2 VALUES ('"abc"');
SELECT c,
c = CAST('"abc"' AS JSON) AS eq1,
c = JSON_EXTRACT('"abc"', '$') AS eq2,
c = j AS eq3
FROM t1, t2 ORDER BY c;
c	eq1	eq2	eq3
"abc"	0	0	0
abc	1	1	1
DROP TABLE t1, t2;
#
# Bug#21147943 JSON_CONTAINS() RETURNS INCORRECT RESULT
#
SELECT JSON_CONTAINS('[1, 2, [4] ]', '{ "b" : 2 }');
JSON_CONTAINS('[1, 2, [4] ]', '{ "b" : 2 }')
0
SELECT JSON_CONTAINS('[1, 2, [4,5] ]', '[1,2,3,4,5,6,7,8,9]');
JSON_CONTAINS('[1, 2, [4,5] ]', '[1,2,3,4,5,6,7,8,9]')
0
SELECT JSON_CONTAINS('[1, 2, [4,5] ]', '[111111111111111111]');
JSON_CONTAINS('[1, 2, [4,5] ]', '[111111111111111111]')
0
#
# Bug#21169109 WL8249:WRONG RESULTS WHILE COMPARING A JSON COLUMN
#              WITH AN INDEXED INT COLUMN
#
CREATE TABLE t1(j JSON, i INT);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t1 VALUES
('0', 0), ('1', 1), ('1.0', 2), ('[1,2,3]', 3), ('{}', 4), ('"abc"', 5);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT * FROM t1 WHERE j > i ORDER BY i;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`j` AS `j`,`test`.`t1`.`i` AS `i` from `test`.`t1` where (`test`.`t1`.`j` > `test`.`t1`.`i`) order by `test`.`t1`.`i`
EXPLAIN SELECT * FROM t1 AS a, t1 AS b WHERE a.j > b.i ORDER BY b.i, a.i;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	6	100.00	Using temporary; Using filesort
1	SIMPLE	b	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where; Using join buffer (hash join)
Warnings:
Note	1003	Table 'a' is not pushable: select list can't contain BLOB columns
Note	1003	/* select#1 */ select `test`.`a`.`j` AS `j`,`test`.`a`.`i` AS `i`,`test`.`b`.`j` AS `j`,`test`.`b`.`i` AS `i` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`a`.`j` > `test`.`b`.`i`) order by `test`.`b`.`i`,`test`.`a`.`i`
SELECT * FROM t1 WHERE j > i ORDER BY i;
j	i
[1, 2, 3]	3
{}	4
"abc"	5
SELECT * FROM t1 AS a, t1 AS b WHERE a.j > b.i ORDER BY b.i, a.i;
j	i	j	i
1	1	0	0
1.0	2	0	0
[1, 2, 3]	3	0	0
{}	4	0	0
"abc"	5	0	0
[1, 2, 3]	3	1	1
{}	4	1	1
"abc"	5	1	1
[1, 2, 3]	3	1.0	2
{}	4	1.0	2
"abc"	5	1.0	2
[1, 2, 3]	3	[1, 2, 3]	3
{}	4	[1, 2, 3]	3
"abc"	5	[1, 2, 3]	3
[1, 2, 3]	3	{}	4
{}	4	{}	4
"abc"	5	{}	4
[1, 2, 3]	3	"abc"	5
{}	4	"abc"	5
"abc"	5	"abc"	5
# Create an index on the INT column and verify that it is not used.
CREATE INDEX t1_idx ON t1(i);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN SELECT * FROM t1 WHERE j > i ORDER BY i;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	6	33.33	Using where; Using filesort
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`j` AS `j`,`test`.`t1`.`i` AS `i` from `test`.`t1` where (`test`.`t1`.`j` > `test`.`t1`.`i`) order by `test`.`t1`.`i`
EXPLAIN SELECT * FROM t1 AS a, t1 AS b WHERE a.j > b.i ORDER BY b.i, a.i;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	6	100.00	Using temporary; Using filesort
1	SIMPLE	b	p0,p1,p2,p3,p4,p5,p6,p7	ALL	t1_idx	NULL	NULL	NULL	6	33.33	Using where; Using join buffer (hash join)
Warnings:
Warning	1739	Cannot use range access on index 't1_idx' due to type or collation conversion on field 'i'
Note	1003	Table 'a' is not pushable: select list can't contain BLOB columns
Note	1003	/* select#1 */ select `test`.`a`.`j` AS `j`,`test`.`a`.`i` AS `i`,`test`.`b`.`j` AS `j`,`test`.`b`.`i` AS `i` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`a`.`j` > `test`.`b`.`i`) order by `test`.`b`.`i`,`test`.`a`.`i`
SELECT * FROM t1 WHERE j > i ORDER BY i;
j	i
[1, 2, 3]	3
{}	4
"abc"	5
SELECT * FROM t1 AS a, t1 AS b WHERE a.j > b.i ORDER BY b.i, a.i;
j	i	j	i
1	1	0	0
1.0	2	0	0
[1, 2, 3]	3	0	0
{}	4	0	0
"abc"	5	0	0
[1, 2, 3]	3	1	1
{}	4	1	1
"abc"	5	1	1
[1, 2, 3]	3	1.0	2
{}	4	1.0	2
"abc"	5	1.0	2
[1, 2, 3]	3	[1, 2, 3]	3
{}	4	[1, 2, 3]	3
"abc"	5	[1, 2, 3]	3
[1, 2, 3]	3	{}	4
{}	4	{}	4
"abc"	5	{}	4
[1, 2, 3]	3	"abc"	5
{}	4	"abc"	5
"abc"	5	"abc"	5
CREATE TABLE t2(i INT, vc VARCHAR(10))
ENGINE=InnoDB CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_bin';
INSERT INTO t2 VALUES (1, 'abc'), (2, '"abc"');
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
EXPLAIN SELECT i FROM t2 WHERE vc = CAST('"abc"' AS JSON);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`i` AS `i` from `test`.`t2` where (`test`.`t2`.`vc` = <cache>(cast('"abc"' as json)))
SELECT i FROM t2 WHERE vc = CAST('"abc"' AS JSON);
i
1
# Create an index on the VARCHAR column and verify that it is not used.
# Used to return 2 instead of 1.
CREATE INDEX t2_idx ON t2(vc);
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
EXPLAIN SELECT i FROM t2 WHERE vc = CAST('"abc"' AS JSON);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	t2_idx	NULL	NULL	NULL	2	50.00	Using where
Warnings:
Warning	1739	Cannot use ref access on index 't2_idx' due to type or collation conversion on field 'vc'
Warning	1739	Cannot use range access on index 't2_idx' due to type or collation conversion on field 'vc'
Note	1003	/* select#1 */ select `test`.`t2`.`i` AS `i` from `test`.`t2` where (`test`.`t2`.`vc` = <cache>(cast('"abc"' as json)))
SELECT i FROM t2 WHERE vc = CAST('"abc"' AS JSON);
i
1
DROP INDEX t2_idx ON t2;
# Create a unique index on the VARCHAR column and verify that it is not
# used. Used to return an empty result.
CREATE UNIQUE INDEX t2_idx ON t2(vc);
ANALYZE TABLE t2;
Table	Op	Msg_type	Msg_text
test.t2	analyze	status	OK
EXPLAIN SELECT i FROM t2 WHERE vc = CAST('"abc"' AS JSON);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	t2_idx	NULL	NULL	NULL	2	50.00	Using where
Warnings:
Warning	1739	Cannot use ref access on index 't2_idx' due to type or collation conversion on field 'vc'
Warning	1739	Cannot use range access on index 't2_idx' due to type or collation conversion on field 'vc'
Note	1003	/* select#1 */ select `test`.`t2`.`i` AS `i` from `test`.`t2` where (`test`.`t2`.`vc` = <cache>(cast('"abc"' as json)))
SELECT i FROM t2 WHERE vc = CAST('"abc"' AS JSON);
i
1
DROP TABLE t1, t2;
create table t_varchar( a varchar(3) );
insert into t_varchar values ( json_unquote( '"\\u0000\\u0001\\u0002"' ) );
select length(a) l from t_varchar;
l
3
select length( json_quote( a ) ) l, json_quote( a ) v from t_varchar;
l	v
20	"\u0000\u0001\u0002"
select * from
(
select length( json_quote( a ) ) as field0,
json_quote( a ) as field1
from t_varchar
) as derived_table;
field0	field1
20	"\u0000\u0001\u0002"
drop table t_varchar;
#
# Bug#21193273 CREATE TABLE SELECT JSN_QUOTE() RESULTS
# IN TRUNCATED DATA
#
set names latin1;
SELECT JSON_QUOTE('table') AS field1;
field1
"table"
CREATE TABLE t SELECT JSON_QUOTE('table') AS field1;
SELECT * FROM t;
field1
"table"
DESCRIBE t;
Field	Type	Null	Key	Default	Extra
field1	varchar(32)	YES		NULL	
DROP TABLE t;
SELECT JSON_UNQUOTE( '"table"' ) AS field1;
field1
table
CREATE TABLE t1 SELECT JSON_unquote( '"table"' ) AS field1;
SELECT * FROM t1;
field1
table
DESCRIBE t1;
Field	Type	Null	Key	Default	Extra
field1	varchar(7)	YES		NULL	
DROP TABLE t1;
set names default;
#
# Bug#25455065 JSON_QUOTE AND JSON_UNQUOTE
#              ARE WRONG FOR MULTIBYTE CHARSETS
#
set names utf8mb4;
SELECT JSON_QUOTE('table') AS field1;
field1
"table"
CREATE TABLE t SELECT JSON_QUOTE('table') AS field1;
SELECT * FROM t;
field1
"table"
DESCRIBE t;
Field	Type	Null	Key	Default	Extra
field1	varchar(32)	YES		NULL	
DROP TABLE t;
SELECT JSON_UNQUOTE( '"table"' ) AS field1;
field1
table
CREATE TABLE t1 SELECT JSON_unquote( '"table"' ) AS field1;
SELECT * FROM t1;
field1
table
DESCRIBE t1;
Field	Type	Null	Key	Default	Extra
field1	varchar(7)	YES		NULL	
DROP TABLE t1;
set names default;
#
# Bug#21230644 JSON_MERGE MEMORY USAGE
#
CREATE TABLE t (doc json);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES('{"array":[1,2,3,4]}');
UPDATE t SET doc=JSON_MERGE_PRESERVE(doc, doc);
UPDATE t SET doc=JSON_MERGE_PRESERVE(doc, doc);
drop table t;
#
# Bug#21224267 DEEPLY NESTED JSON OBJECTS MAY CAUSE CRASH
#
CREATE TABLE t (j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES (CONCAT(REPEAT('{"v":', 5000), '1', REPEAT('}', 5000)));
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t VALUES (CONCAT(REPEAT('{"v":[', 5000), '1', REPEAT(']}', 5000)));
ERROR 22032: The JSON document exceeds the maximum depth.
INSERT INTO t VALUES (CONCAT(REPEAT('[{"v":', 5000), '1', REPEAT('}]', 5000)));
ERROR 22032: The JSON document exceeds the maximum depth.
DROP TABLE t;
#
# JSON should work with INSERT .. ON DUPLICATE KEY UPDATE
#
CREATE TABLE t(id INT PRIMARY KEY, j JSON);
INSERT INTO t VALUES (1, '[1]')
ON DUPLICATE KEY UPDATE j = JSON_OBJECT("a", VALUES(j));
Warnings:
Warning	1287	'VALUES function' is deprecated and will be removed in a future release. Please use an alias (INSERT INTO ... VALUES (...) AS alias) and replace VALUES(col) in the ON DUPLICATE KEY UPDATE clause with alias.col instead
SELECT * FROM t;
id	j
1	[1]
INSERT INTO t VALUES (1, '[1,2]')
ON DUPLICATE KEY UPDATE j = JSON_OBJECT("ab", VALUES(j));
Warnings:
Warning	1287	'VALUES function' is deprecated and will be removed in a future release. Please use an alias (INSERT INTO ... VALUES (...) AS alias) and replace VALUES(col) in the ON DUPLICATE KEY UPDATE clause with alias.col instead
SELECT * FROM t;
id	j
1	{"ab": [1, 2]}
INSERT INTO t VALUES (1, '[1,2,3]')
ON DUPLICATE KEY UPDATE j = JSON_OBJECT("abc", VALUES(j));
Warnings:
Warning	1287	'VALUES function' is deprecated and will be removed in a future release. Please use an alias (INSERT INTO ... VALUES (...) AS alias) and replace VALUES(col) in the ON DUPLICATE KEY UPDATE clause with alias.col instead
SELECT * FROM t;
id	j
1	{"abc": [1, 2, 3]}
DROP TABLE t;
#
# Bug#21278178 JSON_QUOTE(LITERAL) GETS APPLIED DOUBLE DURING SELECT
#              FROM TABLE WITH MTR
#
CREATE TABLE t(x INT);
INSERT INTO t VALUES (1), (2), (3);
SET NAMES latin1;
SELECT JSON_QUOTE('abc') FROM t;
JSON_QUOTE('abc')
"abc"
"abc"
"abc"
SET NAMES utf8mb4;
SELECT JSON_QUOTE('abc') FROM t;
JSON_QUOTE('abc')
"abc"
"abc"
"abc"
SET NAMES default;
DROP TABLE t;
#
# Bug#21291993 ASSERT `0' FAILED AT THD::SEND_STATEMENT_STATUS()
#              ON JSON_SEARCH(NULL, ...)+JOIN
#
CREATE TABLE t (pk INT NOT NULL PRIMARY KEY, col_varchar VARCHAR(1));
SELECT COUNT(*), JSON_SEARCH(NULL, 'one', '5%')
FROM t t1, t t2 WHERE t1.pk = t2.pk;
COUNT(*)	JSON_SEARCH(NULL, 'one', '5%')
0	NULL
DROP TABLE t;
#
# Bug#21376088 JSON: CRASH IN VAL_JSON_FUNC_FIELD_SUBSELECT
#
create table t(a json);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into t values('{}');
select a from t where (select a from t where 1) in (select 1 from t);
a
select a from t where (select a from t where 1) in (select cast('{}' as json) from t);
a
{}
drop table t;
#
# Bug#21437989: ASSERTION FAILED:
#               JSON_BINARY::PARSE_BINARY(PTR, LENGTH).IS_VALID()
#
CREATE TABLE t(j JSON NOT NULL);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES ();
ERROR HY000: Field 'j' doesn't have a default value
INSERT INTO t VALUES (NULL);
ERROR 23000: Column 'j' cannot be null
INSERT IGNORE INTO t VALUES ();
Warnings:
Warning	1364	Field 'j' doesn't have a default value
INSERT IGNORE INTO t VALUES (NULL);
Warnings:
Warning	1048	Column 'j' cannot be null
SELECT * FROM t;
j
null
null
INSERT INTO t SELECT j FROM t;
REPLACE INTO t SELECT j FROM t;
SELECT * FROM t;
j
null
null
null
null
null
null
null
null
DROP TABLE t;
#
# Bug#21448719: WRONG RESULT FOR JSON VALUE IN OUTER JOIN WITH VIEW
#
CREATE TABLE t1(j1 JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
CREATE TABLE t2(j2 JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
CREATE VIEW v AS SELECT CAST('1' AS JSON) AS jv, j2 FROM t2;
INSERT INTO t1 VALUES ('1');
SELECT j1, jv, j2, JSON_ARRAY(j1, jv, j2) FROM t1 LEFT JOIN v ON j1 = jv;
j1	jv	j2	JSON_ARRAY(j1, jv, j2)
1	NULL	NULL	[1, null, null]
INSERT INTO t2 VALUES ('1');
SELECT j1, jv, j2, JSON_ARRAY(j1, jv, j2) FROM t1 LEFT JOIN v ON j1 = jv;
j1	jv	j2	JSON_ARRAY(j1, jv, j2)
1	1	1	[1, 1, 1]
DROP TABLE t1, t2;
DROP VIEW v;
#
# Bug#21472872 WRONG RESULTS CAUSED BY PATH LEG POPPING
# IN JSON FUNCTIONS
#
create table tdoc( id int, doc json );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into tdoc values
( 1, '[]' ),
( 2, '{ "a": { "b": true } }' );
select id, json_insert( doc, '$.a.c', false ) from tdoc where id = 2;
id	json_insert( doc, '$.a.c', false )
2	{"a": {"b": true, "c": false}}
select id, json_insert( doc, '$.a.c', false ) from tdoc order by id;
id	json_insert( doc, '$.a.c', false )
1	[]
2	{"a": {"b": true, "c": false}}
drop table tdoc;
create table tdoc( id int, doc json, new_value varchar( 10 ) );
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
insert into tdoc values
( 1, '{ "a": { "b": true } }', null ),
( 2, '{ "a": { "b": true } }', 'abc' );
select id, json_insert( doc, '$.a.c', new_value ) from tdoc where id = 2;
id	json_insert( doc, '$.a.c', new_value )
2	{"a": {"b": true, "c": "abc"}}
select id, json_insert( doc, '$.a.c', new_value ) from tdoc order by id;
id	json_insert( doc, '$.a.c', new_value )
1	{"a": {"b": true, "c": null}}
2	{"a": {"b": true, "c": "abc"}}
drop table tdoc;
#
# Bug#21487833: DBUG_ABORT() IN JSON_WRAPPER::MAKE_HASH_KEY
#               WITH ORDERED JSON
#
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
CREATE TABLE t (a BLOB, b JSON NOT NULL);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES ('', NULL), ('', NULL);
Warnings:
Warning	1048	Column 'b' cannot be null
UPDATE t SET a = 1 ORDER BY b;
SELECT COUNT(a) FROM t GROUP BY b;
COUNT(a)
2
SELECT DISTINCT B FROM t;
B
null
SELECT b FROM t UNION DISTINCT SELECT b FROM t;
b
null
SELECT * FROM t ORDER BY b;
a	b
1	null
1	null
DROP TABLE t;
#
# Bug#21541481: MEMORY LEAK OF ALLOCATIONS MADE IN
#               VAL_JSON_FUNC_FIELD_SUBSELECT
#
# This comparison gave valgrind leakage before the fix
create table t(a json not null) engine=innodb;
insert into t values('{}');
select row(uuid(), a) < row(a, str_to_date(1,1)) from t;
row(uuid(), a) < row(a, str_to_date(1,1))
1
drop table t;
# Bug#21547877: UPDATE/INSERT JSON COLUMN CRASHES IF EXPRESSION
#               REFERS TO SELF
#
SET NAMES latin1;
CREATE TABLE t (j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES ('{}');
UPDATE t SET j='1', j='1111-11-11', j=('1' NOT BETWEEN j AND '1');
ERROR 22032: Invalid JSON text: "The document root must not be followed by other values." at position 4 in value for column 't.j'.
SELECT * FROM t;
j
{}
DROP TABLE t;
SET NAMES DEFAULT;
#
# Bug#21602361: ASSERTION FAILED: (VECTOR_IDX == -1) || (VECTOR_IDX >= 0)
#
create table t(b int, key(b));
insert into t values(1),(2);
select json_length('{}',@uninitialized_21602361)
from t group by b into @uninitialized_21602361;
ERROR 42000: Result consisted of more than one row
set @initialized_21602361 = '$';
select json_length('{}',@initialized_21602361)
from t group by b into @initialized_21602361;
ERROR 42000: Result consisted of more than one row
set @error_value_21602361 = '$[';
select json_length('{}',@error_value_21602361)
from t group by b into @error_value_21602361;
ERROR 42000: Invalid JSON path expression. The error is around character position 2.
set @valid_path_21602361 = '$';
select concat( '$[', json_length('{}',@valid_path_21602361), ']' )
from t group by b into @valid_path_21602361;
ERROR 42000: Result consisted of more than one row
set @null_value_21602361 = null;
select json_length('{}',@null_value_21602361)
from t group by b into @null_value_21602361;
ERROR 42000: Result consisted of more than one row
drop table t;
#
# Bug#21649073: JSON_TYPE RETURNS OPAQUE FOR SOME BINARY VALUES
#
SELECT JSON_TYPE(CAST(CAST('abcd' AS BINARY) AS JSON));
JSON_TYPE(CAST(CAST('abcd' AS BINARY) AS JSON))
BLOB
CREATE TABLE t (bn BINARY(5), vb VARBINARY(5),
tb TINYBLOB, mb MEDIUMBLOB, bb BLOB, lb LONGBLOB);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t (bn) VALUES (x'cafe');
UPDATE t SET vb = bn, tb = bn, mb = bn, bb = bn, lb = bn;
SELECT JSON_TYPE(CAST(bn AS JSON)) AS bn, JSON_TYPE(CAST(vb AS JSON)) AS vb,
JSON_TYPE(CAST(tb AS JSON)) AS tb, JSON_TYPE(CAST(mb AS JSON)) AS mb,
JSON_TYPE(CAST(bb AS JSON)) AS bb, JSON_TYPE(CAST(lb AS JSON)) AS lb
FROM t;
bn	vb	tb	mb	bb	lb
BLOB	BLOB	BLOB	BLOB	BLOB	BLOB
DROP TABLE t;
#
# Basic tests for inlined JSON path
#
CREATE TABLE t1(autopk int primary key auto_increment, f1 JSON);
INSERT INTO t1(f1) VALUES
('{"a":1}'),
('{"a":3}'),
('{"a":2}'),
('{"a":11, "b":3}'),
('{"a":33, "b":1}'),
('{"a":22,"b":2}');
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
SELECT f1->"$.a" FROM t1 order by autopk;
f1->"$.a"
1
3
2
11
33
22
EXPLAIN SELECT f1->"$.a" FROM t1 order by autopk;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	4	NULL	6	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select json_extract(`test`.`t1`.`f1`,'$.a') AS `f1->"$.a"` from `test`.`t1` order by `test`.`t1`.`autopk`
SELECT f1->"$.a" FROM t1 WHERE f1->"$.b" > 1 order by autopk;
f1->"$.a"
11
22
EXPLAIN SELECT f1->"$.a" FROM t1 WHERE f1->"$.b" > 1 order by autopk;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	4	NULL	6	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select json_extract(`test`.`t1`.`f1`,'$.a') AS `f1->"$.a"` from `test`.`t1` where (json_extract(`test`.`t1`.`f1`,'$.b') > 1) order by `test`.`t1`.`autopk`
SELECT f1->"$.a", f1->"$.b" FROM t1 ORDER BY autopk;
f1->"$.a"	f1->"$.b"
1	NULL
3	NULL
2	NULL
11	3
33	1
22	2
EXPLAIN SELECT f1->"$.a", f1->"$.b" FROM t1 ORDER BY autopk;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	4	NULL	6	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select json_extract(`test`.`t1`.`f1`,'$.a') AS `f1->"$.a"`,json_extract(`test`.`t1`.`f1`,'$.b') AS `f1->"$.b"` from `test`.`t1` order by `test`.`t1`.`autopk`
SELECT MAX(f1->"$.a"), f1->"$.b" FROM t1 GROUP BY f1->"$.b";
MAX(f1->"$.a")	f1->"$.b"
11	3
22	2
3	NULL
33	1
EXPLAIN SELECT MAX(f1->"$.a"), f1->"$.b" FROM t1 GROUP BY f1->"$.b";
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	6	100.00	Using temporary
Warnings:
Note	1003	/* select#1 */ select max(json_extract(`test`.`t1`.`f1`,'$.a')) AS `MAX(f1->"$.a")`,json_extract(`test`.`t1`.`f1`,'$.b') AS `f1->"$.b"` from `test`.`t1` group by json_extract(`test`.`t1`.`f1`,'$.b')
SELECT JSON_OBJECT("c",f1->"$.b") AS f2
FROM t1 HAVING JSON_TYPE(f2->"$.c") <> 'NULL' ORDER BY autopk;
f2
{"c": 3}
{"c": 1}
{"c": 2}
EXPLAIN
SELECT JSON_OBJECT("c",f1->"$.b") AS f2 FROM t1 HAVING JSON_TYPE(f2->"$.c") <> 'NULL' order by autopk;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	index	NULL	PRIMARY	4	NULL	6	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select json_object('c',json_extract(`test`.`t1`.`f1`,'$.b')) AS `f2` from `test`.`t1` having (json_type(json_extract(`f2`,'$.c')) <> 'NULL') order by `test`.`t1`.`autopk`
Test unquoting operator
INSERT INTO t1(f1) VALUES
('{"t":"a"}'),
('{"t":"b"}'),
('{"t":"c"}');
Returned values should be quoted
SELECT f1->"$.t" FROM t1 WHERE f1->"$.t" <> 'NULL' order by autopk;
f1->"$.t"
"a"
"b"
"c"
Returned values should be unquoted
SELECT f1->>"$.t" FROM t1 WHERE f1->>"$.t" <> 'NULL' order by autopk;
f1->>"$.t"
a
b
c
EXPLAIN SELECT f1->>"$.t" FROM t1 WHERE f1->>"$.t" <> 'NULL';
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	p0,p1,p2,p3,p4,p5,p6,p7	ALL	NULL	NULL	NULL	NULL	9	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select json_unquote(json_extract(`test`.`t1`.`f1`,'$.t')) AS `f1->>"$.t"` from `test`.`t1` where (json_unquote(json_extract(`test`.`t1`.`f1`,'$.t')) <> 'NULL')
SELECT f1->>NULL FROM t1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'NULL FROM t1' at line 1
SELECT f1 ->> "NULL" FROM t1;
ERROR 42000: Invalid JSON path expression. The error is around character position 1.
SELECT f1->>"NULL" FROM t1;
ERROR 42000: Invalid JSON path expression. The error is around character position 1.
SELECT f1->>"!@#" FROM t1;
ERROR 42000: Invalid JSON path expression. The error is around character position 1.
SELECT COUNT(*)->>"$.t" FROM t1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '->>"$.t" FROM t1' at line 1
INSERT INTO t1(f1) VALUES
('[ { "a": 1 }, { "a": 2 } ]'),
('{ "a" : "foo", "b" : [ true, { "c" : 123, "c" : 456 } ] }'),
('{ "a" : "foo", "b" : [ true, { "c" : "123" } ] }'),
('{ "a" : "foo", "b" : [ true, { "c" : 123 } ] }');
SELECT
f1->>"$**.b",
cast(json_unquote(json_extract(f1,"$**.b")) as char),
cast(f1->>"$**.b" as char) <=> cast(json_unquote(json_extract(f1,"$**.b")) as char)
FROM t1 order by autopk;
f1->>"$**.b"	cast(json_unquote(json_extract(f1,"$**.b")) as char)	cast(f1->>"$**.b" as char) <=> cast(json_unquote(json_extract(f1,"$**.b")) as char)
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
[3]	[3]	1
[1]	[1]	1
[2]	[2]	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
[[true, {"c": 456}]]	[[true, {"c": 456}]]	1
[[true, {"c": "123"}]]	[[true, {"c": "123"}]]	1
[[true, {"c": 123}]]	[[true, {"c": 123}]]	1
SELECT
f1->>"$.c",
cast(json_unquote(json_extract(f1,"$.c")) as char),
cast(f1->>"$.c"  as char) <=> cast(json_unquote(json_extract(f1,"$.c")) as char)
FROM t1 order by autopk;
f1->>"$.c"	cast(json_unquote(json_extract(f1,"$.c")) as char)	cast(f1->>"$.c"  as char) <=> cast(json_unquote(json_extract(f1,"$.c")) as char)
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
SELECT
f1->>'$.b[1].c',
cast(json_unquote(json_extract(f1,'$.b[1].c')) as char),
cast(f1->>'$.b[1].c' as
char)<=>cast(json_unquote(json_extract(f1,'$.b[1].c')) as char)
FROM t1 order by autopk;
f1->>'$.b[1].c'	cast(json_unquote(json_extract(f1,'$.b[1].c')) as char)	cast(f1->>'$.b[1].c' as
char)<=>cast(json_unquote(json_extract(f1,'$.b[1].c')) as char)
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
456	456	1
123	123	1
123	123	1
SELECT
f1->'$.b[1].c',
cast(json_extract(f1,'$.b[1].c') as char),
cast(f1->'$.b[1].c' as char)
<=>cast(json_extract(f1,'$.b[1].c') as char)
FROM t1 order by autopk;
f1->'$.b[1].c'	cast(json_extract(f1,'$.b[1].c') as char)	cast(f1->'$.b[1].c' as char)
<=>cast(json_extract(f1,'$.b[1].c') as char)
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
456	456	1
"123"	"123"	1
123	123	1
SELECT
f1->>'$.b[1]',
cast(json_unquote(json_extract(f1,'$.b[1]')) as char),
cast(f1->>'$.b[1]' as char) <=>
cast(json_unquote(json_extract(f1,'$.b[1]')) as char)
FROM t1 order by autopk;
f1->>'$.b[1]'	cast(json_unquote(json_extract(f1,'$.b[1]')) as char)	cast(f1->>'$.b[1]' as char) <=>
cast(json_unquote(json_extract(f1,'$.b[1]')) as char)
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
{"c": 456}	{"c": 456}	1
{"c": "123"}	{"c": "123"}	1
{"c": 123}	{"c": 123}	1
SELECT
f1->>'$[0][0]',
cast(json_unquote(json_extract(f1,'$[0][0]')) as char),
cast(f1->>'$[0][0]' as char) <=>
cast(json_unquote(json_extract(f1,'$[0][0]')) as char)
FROM t1 order by autopk;
f1->>'$[0][0]'	cast(json_unquote(json_extract(f1,'$[0][0]')) as char)	cast(f1->>'$[0][0]' as char) <=>
cast(json_unquote(json_extract(f1,'$[0][0]')) as char)
{"a": 1}	{"a": 1}	1
{"a": 3}	{"a": 3}	1
{"a": 2}	{"a": 2}	1
{"a": 11, "b": 3}	{"a": 11, "b": 3}	1
{"a": 33, "b": 1}	{"a": 33, "b": 1}	1
{"a": 22, "b": 2}	{"a": 22, "b": 2}	1
{"t": "a"}	{"t": "a"}	1
{"t": "b"}	{"t": "b"}	1
{"t": "c"}	{"t": "c"}	1
{"a": 1}	{"a": 1}	1
{"a": "foo", "b": [true, {"c": 456}]}	{"a": "foo", "b": [true, {"c": 456}]}	1
{"a": "foo", "b": [true, {"c": "123"}]}	{"a": "foo", "b": [true, {"c": "123"}]}	1
{"a": "foo", "b": [true, {"c": 123}]}	{"a": "foo", "b": [true, {"c": 123}]}	1
SELECT
f1->>'$**[0]',
cast(json_unquote(json_extract(f1,'$**[0]')) as char),
cast(f1->>'$**[0]' as char) <=>
cast(json_unquote(json_extract(f1,'$**[0]')) as char)
FROM t1 order by autopk;
f1->>'$**[0]'	cast(json_unquote(json_extract(f1,'$**[0]')) as char)	cast(f1->>'$**[0]' as char) <=>
cast(json_unquote(json_extract(f1,'$**[0]')) as char)
[{"a": 1}, 1]	[{"a": 1}, 1]	1
[{"a": 3}, 3]	[{"a": 3}, 3]	1
[{"a": 2}, 2]	[{"a": 2}, 2]	1
[{"a": 11, "b": 3}, 11, 3]	[{"a": 11, "b": 3}, 11, 3]	1
[{"a": 33, "b": 1}, 33, 1]	[{"a": 33, "b": 1}, 33, 1]	1
[{"a": 22, "b": 2}, 22, 2]	[{"a": 22, "b": 2}, 22, 2]	1
[{"t": "a"}, "a"]	[{"t": "a"}, "a"]	1
[{"t": "b"}, "b"]	[{"t": "b"}, "b"]	1
[{"t": "c"}, "c"]	[{"t": "c"}, "c"]	1
[{"a": 1}, 1, {"a": 2}, 2]	[{"a": 1}, 1, {"a": 2}, 2]	1
[{"a": "foo", "b": [true, {"c": 456}]}, "foo", true, {"c": 456}, 456]	[{"a": "foo", "b": [true, {"c": 456}]}, "foo", true, {"c": 456}, 456]	1
[{"a": "foo", "b": [true, {"c": "123"}]}, "foo", true, {"c": "123"}, "123"]	[{"a": "foo", "b": [true, {"c": "123"}]}, "foo", true, {"c": "123"}, "123"]	1
[{"a": "foo", "b": [true, {"c": 123}]}, "foo", true, {"c": 123}, 123]	[{"a": "foo", "b": [true, {"c": 123}]}, "foo", true, {"c": 123}, 123]	1
SELECT
f1->> '$.a[0]',
cast(json_unquote(json_extract(f1, '$.a[0]')) as char),
cast(f1->> '$.a[0]' as char) <=> cast(json_unquote(json_extract(f1,
'$.a[0]')) as char)
FROM t1 order by autopk;
f1->> '$.a[0]'	cast(json_unquote(json_extract(f1, '$.a[0]')) as char)	cast(f1->> '$.a[0]' as char) <=> cast(json_unquote(json_extract(f1,
'$.a[0]')) as char)
1	1	1
3	3	1
2	2	1
11	11	1
33	33	1
22	22	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
foo	foo	1
foo	foo	1
foo	foo	1
SELECT
f1->>'$[0].a[0]',
cast(json_unquote(json_extract(f1,'$[0].a[0]')) as char),
cast(f1->>'$[0].a[0]' as char) <=>
cast(json_unquote(json_extract(f1,'$[0].a[0]')) as char)
FROM t1 order by autopk;
f1->>'$[0].a[0]'	cast(json_unquote(json_extract(f1,'$[0].a[0]')) as char)	cast(f1->>'$[0].a[0]' as char) <=>
cast(json_unquote(json_extract(f1,'$[0].a[0]')) as char)
1	1	1
3	3	1
2	2	1
11	11	1
33	33	1
22	22	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
1	1	1
foo	foo	1
foo	foo	1
foo	foo	1
SELECT
f1->>'$**.a',
cast(json_unquote(json_extract(f1,'$**.a')) as char),
cast(f1->>'$**.a' as char) <=> cast(json_unquote(json_extract(f1,'$**.a')) as char)
FROM t1 order by autopk;
f1->>'$**.a'	cast(json_unquote(json_extract(f1,'$**.a')) as char)	cast(f1->>'$**.a' as char) <=> cast(json_unquote(json_extract(f1,'$**.a')) as char)
[1]	[1]	1
[3]	[3]	1
[2]	[2]	1
[11]	[11]	1
[33]	[33]	1
[22]	[22]	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
[1, 2]	[1, 2]	1
["foo"]	["foo"]	1
["foo"]	["foo"]	1
["foo"]	["foo"]	1
SELECT
f1->>'$[0][0][0].a',
cast(json_unquote(json_extract(f1,'$[0][0][0].a')) as char),
cast(f1->>'$[0][0][0].a' as char) <=>
cast(json_unquote(json_extract(f1,'$[0][0][0].a')) as char)
FROM t1 order by autopk;
f1->>'$[0][0][0].a'	cast(json_unquote(json_extract(f1,'$[0][0][0].a')) as char)	cast(f1->>'$[0][0][0].a' as char) <=>
cast(json_unquote(json_extract(f1,'$[0][0][0].a')) as char)
1	1	1
3	3	1
2	2	1
11	11	1
33	33	1
22	22	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
1	1	1
foo	foo	1
foo	foo	1
foo	foo	1
SELECT
f1->>'$[*].b',
cast(json_unquote(json_extract(f1,'$[*].b')) as char),
cast(f1->>'$[*].b' as char) <=>
cast(json_unquote(json_extract(f1,'$[*].b')) as char)
FROM t1 order by autopk;
f1->>'$[*].b'	cast(json_unquote(json_extract(f1,'$[*].b')) as char)	cast(f1->>'$[*].b' as char) <=>
cast(json_unquote(json_extract(f1,'$[*].b')) as char)
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
SELECT
f1->>'$[*].a',
cast(json_unquote(json_extract(f1,'$[*].a')) as char),
cast(f1->>'$[*].a' as char) <=>
cast(json_unquote(json_extract(f1,'$[*].a')) as char)
FROM t1 order by autopk;
f1->>'$[*].a'	cast(json_unquote(json_extract(f1,'$[*].a')) as char)	cast(f1->>'$[*].a' as char) <=>
cast(json_unquote(json_extract(f1,'$[*].a')) as char)
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
[1, 2]	[1, 2]	1
NULL	NULL	1
NULL	NULL	1
NULL	NULL	1
DROP TABLE t1;
#
# Bug#21072360 ASSERTION `(*A)->FIELD_TYPE() == MYSQL_TYPE_JSON ||
#              (*B)->FIELD_TYPE() == FAILED
#
#
# Bug#21144949 WL8132:DELETE QUERY HAVING A SUB-QUERY DOES NOT
#              DELETE ROWS IN THE TABLE
#
CREATE TABLE t(j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES (JSON_OBJECT('a', 'b')), (JSON_OBJECT('a', 'b'));
DELETE FROM t WHERE j IN (SELECT JSON_OBJECT('a', 'b') FROM DUAL WHERE 1);
SELECT * FROM t;
j
INSERT INTO t VALUES (JSON_OBJECT('a', 'b')), (JSON_OBJECT('a', 'b'));
DELETE FROM t WHERE j IN (SELECT JSON_OBJECT('a', 'b') FROM DUAL);
SELECT * FROM t;
j
INSERT INTO t VALUES (JSON_OBJECT('a', 'b')), (JSON_OBJECT('a', 'b'));
DELETE FROM t WHERE j IN (SELECT CAST(NULL AS JSON) FROM DUAL);
DELETE FROM t WHERE j IN (SELECT CAST(NULL AS JSON) FROM DUAL WHERE 1);
DELETE FROM t WHERE j IN (SELECT CAST('not json' AS JSON) FROM DUAL);
ERROR 22032: Invalid JSON text in argument 1 to function cast_as_json: "Invalid value." at position 1.
DELETE FROM t WHERE j IN (SELECT CAST('not json' AS JSON) FROM DUAL WHERE 1);
ERROR 22032: Invalid JSON text in argument 1 to function cast_as_json: "Invalid value." at position 1.
SELECT * FROM t;
j
{"a": "b"}
{"a": "b"}
DROP TABLE t;
#
# Bug#22366102: EMPTY STRINGS AS KEYS PROBABLY NOT HANDLED CORRECTLY
#               BY JSON_EXTRACT()
#
CREATE TABLE t(j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES ('{"":"a"}'), ('{"":{"":"a"}}'), ('{"a":{"":{"b":"a"}}}');
SELECT JSON_UNQUOTE(JSON_SEARCH(j, 'one', 'a')) AS path FROM t;
path
$.""
$."".""
$.a."".b
SELECT j->'$.""' AS c1, j->'$."".""' AS c2, j->'$.a."".b' AS c3 FROM t;
c1	c2	c3
"a"	NULL	NULL
NULL	NULL	"a"
{"": "a"}	"a"	NULL
DROP TABLE t;
SELECT JSON_EXTRACT('[1]', '$.');
ERROR 42000: Invalid JSON path expression. The error is around character position 2.
SELECT JSON_EXTRACT('[1]', '$..');
ERROR 42000: Invalid JSON path expression. The error is around character position 2.
SELECT JSON_EXTRACT('[1]', '$.a..b');
ERROR 42000: Invalid JSON path expression. The error is around character position 4.
#
# Bug#22477046: HANDLE_FATAL_SIGNAL (SIG=6) IN ITEM_FUNC_SP::VAL_JSON
#
CREATE TABLE t1(j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
CREATE TABLE t2(vc VARCHAR(10));
CREATE FUNCTION f1(i INT) RETURNS INT RETURN i;
INSERT INTO t1 SELECT f1(3);
ERROR 22032: Invalid JSON text: "not a JSON text, may need CAST" at position 0 in value for column 't1.j'.
INSERT INTO t2 SELECT f1(3);
CREATE FUNCTION f2(vc VARCHAR(10)) RETURNS VARCHAR(10) RETURN vc;
INSERT INTO t1 SELECT f2('[1,2,3]');
INSERT INTO t2 SELECT f2('[1,2,3]');
CREATE FUNCTION f3(j JSON) RETURNS JSON RETURN j;
INSERT INTO t1 SELECT f3('[4,5,6]');
INSERT INTO t2 SELECT f3('[4,5,6]');
SELECT * FROM t1;
j
[1, 2, 3]
[4, 5, 6]
SELECT * FROM t2;
vc
3
[1,2,3]
[4, 5, 6]
DROP TABLE t1, t2;
DROP FUNCTION f1;
DROP FUNCTION f2;
DROP FUNCTION f3;
# Bug#22522073: Assertion failed: !thd->is_error() in optimize_cond()
CREATE TABLE s(a INTEGER, b CHAR(1));
INSERT INTO s VALUES (1,0), (1,0), (1,0);
CREATE TABLE t(c JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES (), (), ();
SELECT 1
FROM s WHERE a NOT IN (SELECT 1
FROM s WHERE (SELECT c FROM t) = b);
ERROR 21000: Subquery returns more than 1 row
DROP TABLE s, t;
#
# Bug#22546013: REMOVE DEPENDENCY ON BOOST IN ITEM_JSON_FUNC.CC
#
CREATE TABLE t(j JSON, vc VARCHAR(99));
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES ('"abc"', REPEAT('abc', 33)), ('"abc"', 'abc');
INSERT INTO t SELECT * FROM t;
SELECT * FROM t WHERE j = vc;
j	vc
"abc"	abc
"abc"	abc
DROP TABLE t;
#
# Bug#22816576: ASSERTION FAILED: !THD->IS_ERROR() IN OPTIMIZE_COND()
#
CREATE TABLE t(a DATE);
INSERT INTO t VALUES ('1970-01-01');
SELECT 1 FROM t WHERE 1 <>
(
SELECT 1 FROM
(
SELECT 1 FROM t e
JOIN t ON t.a <=> JSON_REPLACE(JSON_OBJECT('', ''), '', '')
WHERE e.a
) w
);
ERROR 42000: Invalid JSON path expression. The error is around character position 0.
DROP TABLE t;
#
# Bug#23209914: ASSERTION FAILED: THIS_TYPE != ENUM_JSON_TYPE::J_ERROR
#
CREATE PROCEDURE p(a JSON) SELECT 1 WHERE a = 1;
CALL p(NULL);
1
DROP PROCEDURE p;
CREATE TABLE t1 (pk INT AUTO_INCREMENT KEY, f1 INT, fj JSON);
INSERT INTO t1 VALUES(1, NULL, JSON_OBJECT('aaa','bbb'));
INSERT INTO t1 VALUES(2, NULL, JSON_OBJECT('bbb','aaa'));
SELECT * FROM t1 ORDER BY f1, pk, fj;
pk	f1	fj
1	NULL	{"aaa": "bbb"}
2	NULL	{"bbb": "aaa"}
DROP TABLE t1;
#
# Bug#25461627: VALGRIND WARNING WHEN UPDATING A JSON COLUMN
#
CREATE TABLE t (j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES (NULL);
UPDATE t SET j = JSON_ARRAY(REPEAT('a', 32)),
j = JSON_EXTRACT(j, '$[0]');
SELECT * FROM t;
j
"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
UPDATE t SET j = JSON_ARRAY(CAST(REPEAT('b', 32) AS BINARY)),
j = JSON_EXTRACT(j, '$[0]');
SELECT * FROM t;
j
"base64:type15:YmJiYmJiYmJiYmJiYmJiYmJiYmJiYmJiYmJiYmJiYmI="
UPDATE t SET j = JSON_ARRAY(JSON_ARRAY(REPEAT('c', 32))),
j = JSON_EXTRACT(j, '$[0]');
SELECT * FROM t;
j
["cccccccccccccccccccccccccccccccc"]
UPDATE t SET j = JSON_ARRAY(JSON_OBJECT(REPEAT('d', 16), REPEAT('e', 16))),
j = JSON_EXTRACT(j, '$[0]');
SELECT * FROM t;
j
{"dddddddddddddddd": "eeeeeeeeeeeeeeee"}
DROP TABLE t;
#
# WL#9191: JSON_PRETTY function
#
CREATE TABLE t(id INT PRIMARY KEY AUTO_INCREMENT,
uc VARCHAR(128) CHARACTER SET utf8mb4,
lc VARCHAR(128) CHARACTER SET latin1,
j JSON);
INSERT INTO t(uc) VALUES
(NULL),
('{}'),
('[]'),
('[1,2,3]'),
('{"a":1,"b":2}'),
('{"a":[1,{}],"b":[]}'),
('{"a":{"b":{"c":{"d":{"e":{"f":null}}}}}}'),
('true'),
('false'),
('null'),
('1'),
('1.1'),
('"hello"'),
(JSON_ARRAY('abc
def', '"abc"'));
UPDATE t SET lc = uc, j = uc;
SELECT JSON_PRETTY(uc) AS c1 FROM t ORDER BY id;
c1
NULL
{}
[]
[
  1,
  2,
  3
]
{
  "a": 1,
  "b": 2
}
{
  "a": [
    1,
    {}
  ],
  "b": []
}
{
  "a": {
    "b": {
      "c": {
        "d": {
          "e": {
            "f": null
          }
        }
      }
    }
  }
}
true
false
null
1
1.1
"hello"
[
  "abc\ndef",
  "\"abc\""
]
SELECT JSON_PRETTY(lc) AS c1 FROM t ORDER BY id;
c1
NULL
{}
[]
[
  1,
  2,
  3
]
{
  "a": 1,
  "b": 2
}
{
  "a": [
    1,
    {}
  ],
  "b": []
}
{
  "a": {
    "b": {
      "c": {
        "d": {
          "e": {
            "f": null
          }
        }
      }
    }
  }
}
true
false
null
1
1.1
"hello"
[
  "abc\ndef",
  "\"abc\""
]
SELECT JSON_PRETTY(j) AS c1 FROM t ORDER BY id;
c1
NULL
{}
[]
[
  1,
  2,
  3
]
{
  "a": 1,
  "b": 2
}
{
  "a": [
    1,
    {}
  ],
  "b": []
}
{
  "a": {
    "b": {
      "c": {
        "d": {
          "e": {
            "f": null
          }
        }
      }
    }
  }
}
true
false
null
1
1.1
"hello"
[
  "abc\ndef",
  "\"abc\""
]
SELECT DISTINCT CHARSET(JSON_PRETTY(uc)) AS c1,
CHARSET(JSON_PRETTY(lc)) AS c2,
CHARSET(JSON_PRETTY(j)) AS c3 FROM t;
c1	c2	c3
utf8mb4	utf8mb4	utf8mb4
DROP TABLE t;
SELECT JSON_PRETTY('this is not JSON') AS j;
ERROR 22032: Invalid JSON text in argument 1 to function json_pretty: "Invalid value." at position 1.
SELECT JSON_PRETTY();
ERROR 42000: Incorrect parameter count in the call to native function 'JSON_PRETTY'
SELECT JSON_PRETTY('{}', 2);
ERROR 42000: Incorrect parameter count in the call to native function 'JSON_PRETTY'
#
# WL#9192: Add JSON_STORAGE_SIZE / JSON_STORAGE_FREE functions
#
SELECT JSON_STORAGE_SIZE();
ERROR 42000: Incorrect parameter count in the call to native function 'JSON_STORAGE_SIZE'
SELECT JSON_STORAGE_FREE();
ERROR 42000: Incorrect parameter count in the call to native function 'JSON_STORAGE_FREE'
SELECT JSON_STORAGE_SIZE('{}', '[]');
ERROR 42000: Incorrect parameter count in the call to native function 'JSON_STORAGE_SIZE'
SELECT JSON_STORAGE_FREE('{}', '[]');
ERROR 42000: Incorrect parameter count in the call to native function 'JSON_STORAGE_FREE'
SELECT JSON_STORAGE_SIZE('this is not JSON');
ERROR 22032: Invalid JSON text in argument 1 to function json_storage_size: "Invalid value." at position 1.
SELECT JSON_STORAGE_FREE('this is not JSON');
ERROR 22032: Invalid JSON text in argument 1 to function json_storage_free: "Invalid value." at position 1.
SELECT JSON_STORAGE_SIZE(NULL);
JSON_STORAGE_SIZE(NULL)
NULL
SELECT JSON_STORAGE_FREE(NULL);
JSON_STORAGE_FREE(NULL)
NULL
SELECT JSON_STORAGE_SIZE(JSON_ARRAY(1,2,3));
JSON_STORAGE_SIZE(JSON_ARRAY(1,2,3))
14
SELECT JSON_STORAGE_FREE(JSON_ARRAY(1,2,3));
JSON_STORAGE_FREE(JSON_ARRAY(1,2,3))
0
CREATE TABLE t(id INT PRIMARY KEY, j JSON, v VARCHAR(100));
INSERT INTO t(id, j) VALUES (0, NULL), (1, '[["abc", "def", "ghi"], "jkl"]');
UPDATE t SET v = j;
SELECT *,
JSON_STORAGE_SIZE(j), JSON_STORAGE_SIZE(v),
JSON_STORAGE_SIZE(j->'$[0]'), JSON_STORAGE_SIZE(v->'$[0]'),
JSON_STORAGE_FREE(j), JSON_STORAGE_FREE(v),
JSON_STORAGE_FREE(j->'$[0]'), JSON_STORAGE_FREE(v->'$[0]')
FROM t ORDER BY id;
id	j	v	JSON_STORAGE_SIZE(j)	JSON_STORAGE_SIZE(v)	JSON_STORAGE_SIZE(j->'$[0]')	JSON_STORAGE_SIZE(v->'$[0]')	JSON_STORAGE_FREE(j)	JSON_STORAGE_FREE(v)	JSON_STORAGE_FREE(j->'$[0]')	JSON_STORAGE_FREE(v->'$[0]')
0	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
1	[["abc", "def", "ghi"], "jkl"]	[["abc", "def", "ghi"], "jkl"]	40	40	26	26	0	0	0	0
UPDATE t SET j = JSON_SET(j, '$[0][1]', NULL, '$[1]', 'X'),
v = JSON_SET(v, '$[0][1]', NULL, '$[1]', 'X');
SELECT *,
JSON_STORAGE_SIZE(j), JSON_STORAGE_SIZE(v),
JSON_STORAGE_SIZE(j->'$[0]'), JSON_STORAGE_SIZE(v->'$[0]'),
JSON_STORAGE_FREE(j), JSON_STORAGE_FREE(v),
JSON_STORAGE_FREE(j->'$[0]'), JSON_STORAGE_FREE(v->'$[0]')
FROM t ORDER BY id;
id	j	v	JSON_STORAGE_SIZE(j)	JSON_STORAGE_SIZE(v)	JSON_STORAGE_SIZE(j->'$[0]')	JSON_STORAGE_SIZE(v->'$[0]')	JSON_STORAGE_FREE(j)	JSON_STORAGE_FREE(v)	JSON_STORAGE_FREE(j->'$[0]')	JSON_STORAGE_FREE(v->'$[0]')
0	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
1	[["abc", null, "ghi"], "X"]	[["abc", null, "ghi"], "X"]	34	34	22	22	0	0	0	0
DROP TABLE t;
CREATE TABLE t(id INT PRIMARY KEY, j JSON);
INSERT INTO t VALUES
(1, '{"a":"a"}'), (2, '{"a":"b", "b":"b"}'), (3, '{"a":"c", "c":"c"}');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "a"}	15	0
2	{"a": "b", "b": "b"}	25	0
3	{"a": "c", "c": "c"}	25	0
UPDATE t SET j = JSON_SET(j, '$.a', 'x');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "x"}	15	0
2	{"a": "x", "b": "b"}	25	0
3	{"a": "x", "c": "c"}	25	0
UPDATE t SET j = JSON_SET(j, '$.a', 'y', '$.b', j->'$.a');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "y", "b": "x"}	25	0
2	{"a": "y", "b": "x"}	25	0
3	{"a": "y", "b": "x", "c": "c"}	35	0
UPDATE t SET j = JSON_SET(j, '$.a', 'z'), j = JSON_SET(j, '$.b', j->'$.a');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "z", "b": "z"}	25	0
2	{"a": "z", "b": "z"}	25	0
3	{"a": "z", "b": "z", "c": "c"}	35	0
UPDATE t SET j = JSON_SET(j, '$.a', 'w'),
j = JSON_SET(j, '$.a', 'v', '$.b', j->'$.a');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "v", "b": "w"}	25	0
2	{"a": "v", "b": "w"}	25	0
3	{"a": "v", "b": "w", "c": "c"}	35	0
UPDATE t SET j = JSON_REPLACE(j, '$.a', '1', '$.c', '2', '$.a', '3');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "3", "b": "w"}	25	0
2	{"a": "3", "b": "w"}	25	0
3	{"a": "3", "b": "w", "c": "2"}	35	0
UPDATE t SET j = JSON_REPLACE(j, '$.a', '4'), j = JSON_REPLACE(j, '$.c', '5'),
j = JSON_REPLACE(j, '$.a', '6');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "6", "b": "w"}	25	0
2	{"a": "6", "b": "w"}	25	0
3	{"a": "6", "b": "w", "c": "5"}	35	0
UPDATE t SET j = JSON_REPLACE(j, '$.a', '');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "", "b": "w"}	24	0
2	{"a": "", "b": "w"}	24	0
3	{"a": "", "b": "w", "c": "5"}	34	0
UPDATE t SET j = JSON_REPLACE(j, '$.a', 'a');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "a", "b": "w"}	25	0
2	{"a": "a", "b": "w"}	25	0
3	{"a": "a", "b": "w", "c": "5"}	35	0
UPDATE t SET j = JSON_REPLACE(j, '$.a', 'ab');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "ab", "b": "w"}	26	0
2	{"a": "ab", "b": "w"}	26	0
3	{"a": "ab", "b": "w", "c": "5"}	36	0
UPDATE t SET j = JSON_REPLACE(JSON_REPLACE(j, '$.a', 'A'), '$.b', true);
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "A", "b": true}	23	0
2	{"a": "A", "b": true}	23	0
3	{"a": "A", "b": true, "c": "5"}	33	0
UPDATE t SET j = JSON_REPLACE(JSON_REPLACE(j, '$.a', 'X'), '$.unknown', 'Y');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "X", "b": true}	23	0
2	{"a": "X", "b": true}	23	0
3	{"a": "X", "b": true, "c": "5"}	33	0
UPDATE t SET j = JSON_REPLACE(JSON_REPLACE(j, '$.unknown', 'Z'), '$.a', 'W');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "W", "b": true}	23	0
2	{"a": "W", "b": true}	23	0
3	{"a": "W", "b": true, "c": "5"}	33	0
UPDATE t SET j = JSON_REPLACE(JSON_REPLACE(j, '$.unknown1', 1),
'$.unknown2', 2);
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": "W", "b": true}	23	0
2	{"a": "W", "b": true}	23	0
3	{"a": "W", "b": true, "c": "5"}	33	0
UPDATE t SET j = JSON_SET(JSON_REPLACE(j, '$.a', 99), '$.b', 88);
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": 99, "b": 88}	21	0
2	{"a": 99, "b": 88}	21	0
3	{"a": 99, "b": 88, "c": "5"}	31	0
UPDATE t SET j = JSON_REPLACE(JSON_SET(j, '$.a', 77), '$.b', 66);
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": 77, "b": 66}	21	0
2	{"a": 77, "b": 66}	21	0
3	{"a": 77, "b": 66, "c": "5"}	31	0
UPDATE t SET j = JSON_REPLACE(JSON_SET(j, '$.c', 0), '$.a', 1);
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": 1, "b": 66, "c": 0}	29	0
2	{"a": 1, "b": 66, "c": 0}	29	0
3	{"a": 1, "b": 66, "c": 0}	29	0
UPDATE t SET j = JSON_SET(JSON_REPLACE(JSON_SET(j, '$.a', 11),
'$.unknown', 12),
'$.b', 13);
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	{"a": 11, "b": 13, "c": 0}	29	0
2	{"a": 11, "b": 13, "c": 0}	29	0
3	{"a": 11, "b": 13, "c": 0}	29	0
DROP TABLE t;
CREATE TABLE t(id INT PRIMARY KEY, j1 JSON, j2 JSON);
INSERT INTO t VALUES
(1, '{"a":"a","b":"b"}', '{"a":"a","b":"b"}'),
(2, NULL, NULL),
(3, '{"a":"aa","b":"bb"}', '{"a":"aa","b":"bb"}');
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
SELECT *, JSON_STORAGE_SIZE(j1) ss1,
JSON_STORAGE_FREE(j1) sf1,
JSON_STORAGE_SIZE(j2) ss2,
JSON_STORAGE_FREE(j2) sf2 FROM t ORDER BY id;
id	j1	j2	ss1	sf1	ss2	sf2
1	{"a": "a", "b": "b"}	{"a": "a", "b": "b"}	25	0	25	0
2	NULL	NULL	NULL	NULL	NULL	NULL
3	{"a": "aa", "b": "bb"}	{"a": "aa", "b": "bb"}	27	0	27	0
EXPLAIN FORMAT=JSON UPDATE t SET j1 = JSON_SET(j1, '$.a', 'x'),
j2 = JSON_SET(j2, '$.a', 'y'),
j1 = JSON_SET(j1, '$.b', 'z');
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t",
      "partitions": [
        "p0",
        "p1",
        "p2",
        "p3",
        "p4",
        "p5",
        "p6",
        "p7"
      ],
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00"
    }
  }
}
Warnings:
Note	1003	update `test`.`t` set `test`.`t`.`j1` = json_set(`test`.`t`.`j1`,'$.a','x'),`test`.`t`.`j2` = json_set(`test`.`t`.`j2`,'$.a','y'),`test`.`t`.`j1` = json_set(`test`.`t`.`j1`,'$.b','z')
SET optimizer_trace="enabled=on";
UPDATE t SET j1 = JSON_SET(j1, '$.a', 'x'),
j2 = JSON_SET(j2, '$.a', 'y'),
j1 = JSON_SET(j1, '$.b', 'z');
SET optimizer_trace="enabled=off";
SELECT JSON_PRETTY(JSON_OBJECT(
'json_partial_update', trace->'$**.json_partial_update',
'rejected_columns', trace->'$**.rejected_columns'))
AS json_trace
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
json_trace
{
  "rejected_columns": [
    [
      {
        "cause": "Storage engine does not support partial update",
        "table": "`t`",
        "column": "j1"
      },
      {
        "cause": "Storage engine does not support partial update",
        "table": "`t`",
        "column": "j2"
      }
    ]
  ],
  "json_partial_update": null
}
SELECT *, JSON_STORAGE_SIZE(j1) ss1,
JSON_STORAGE_FREE(j1) sf1,
JSON_STORAGE_SIZE(j2) ss2,
JSON_STORAGE_FREE(j2) sf2 FROM t ORDER BY id;
id	j1	j2	ss1	sf1	ss2	sf2
1	{"a": "x", "b": "z"}	{"a": "y", "b": "b"}	25	0	25	0
2	NULL	NULL	NULL	NULL	NULL	NULL
3	{"a": "x", "b": "z"}	{"a": "y", "b": "bb"}	25	0	26	0
EXPLAIN FORMAT=JSON UPDATE t SET j1 = JSON_SET(j1, '$.a', 's'),
j2 = JSON_SET(j2, '$.a', 'abcdefghi'),
j1 = JSON_SET(j1, '$.b', 't');
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t",
      "partitions": [
        "p0",
        "p1",
        "p2",
        "p3",
        "p4",
        "p5",
        "p6",
        "p7"
      ],
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00"
    }
  }
}
Warnings:
Note	1003	update `test`.`t` set `test`.`t`.`j1` = json_set(`test`.`t`.`j1`,'$.a','s'),`test`.`t`.`j2` = json_set(`test`.`t`.`j2`,'$.a','abcdefghi'),`test`.`t`.`j1` = json_set(`test`.`t`.`j1`,'$.b','t')
SET optimizer_trace="enabled=on";
UPDATE t SET j1 = JSON_SET(j1, '$.a', 's'),
j2 = JSON_SET(j2, '$.a', 'abcdefghi'),
j1 = JSON_SET(j1, '$.b', 't');
SET optimizer_trace="enabled=off";
SELECT JSON_PRETTY(JSON_OBJECT(
'json_partial_update', trace->'$**.json_partial_update',
'rejected_columns', trace->'$**.rejected_columns'))
AS json_trace
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
json_trace
{
  "rejected_columns": [
    [
      {
        "cause": "Storage engine does not support partial update",
        "table": "`t`",
        "column": "j1"
      },
      {
        "cause": "Storage engine does not support partial update",
        "table": "`t`",
        "column": "j2"
      }
    ]
  ],
  "json_partial_update": null
}
SELECT *, JSON_STORAGE_SIZE(j1) ss1,
JSON_STORAGE_FREE(j1) sf1,
JSON_STORAGE_SIZE(j2) ss2,
JSON_STORAGE_FREE(j2) sf2 FROM t ORDER BY id;
id	j1	j2	ss1	sf1	ss2	sf2
1	{"a": "s", "b": "t"}	{"a": "abcdefghi", "b": "b"}	25	0	33	0
2	NULL	NULL	NULL	NULL	NULL	NULL
3	{"a": "s", "b": "t"}	{"a": "abcdefghi", "b": "bb"}	25	0	34	0
EXPLAIN FORMAT=JSON UPDATE t SET j1 = JSON_SET(j1, '$.x.y.z', 'abc', '$.b', 'X');
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t",
      "partitions": [
        "p0",
        "p1",
        "p2",
        "p3",
        "p4",
        "p5",
        "p6",
        "p7"
      ],
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00"
    }
  }
}
Warnings:
Note	1003	update `test`.`t` set `test`.`t`.`j1` = json_set(`test`.`t`.`j1`,'$.x.y.z','abc','$.b','X')
SET optimizer_trace="enabled=on";
UPDATE t SET j1 = JSON_SET(j1, '$.x.y.z', 'abc', '$.b', 'X');
SET optimizer_trace="enabled=off";
SELECT JSON_PRETTY(JSON_OBJECT(
'json_partial_update', trace->'$**.json_partial_update',
'rejected_columns', trace->'$**.rejected_columns'))
AS json_trace
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
json_trace
{
  "rejected_columns": [
    [
      {
        "cause": "Storage engine does not support partial update",
        "table": "`t`",
        "column": "j1"
      }
    ]
  ],
  "json_partial_update": null
}
SELECT *, JSON_STORAGE_SIZE(j1) ss1,
JSON_STORAGE_FREE(j1) sf1,
JSON_STORAGE_SIZE(j2) ss2,
JSON_STORAGE_FREE(j2) sf2 FROM t ORDER BY id;
id	j1	j2	ss1	sf1	ss2	sf2
1	{"a": "s", "b": "X"}	{"a": "abcdefghi", "b": "b"}	25	0	33	0
2	NULL	NULL	NULL	NULL	NULL	NULL
3	{"a": "s", "b": "X"}	{"a": "abcdefghi", "b": "bb"}	25	0	34	0
EXPLAIN FORMAT=JSON UPDATE t SET j1 = JSON_REPLACE(j1, '$.x', 'abc', '$.b', 'Y');
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t",
      "partitions": [
        "p0",
        "p1",
        "p2",
        "p3",
        "p4",
        "p5",
        "p6",
        "p7"
      ],
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00"
    }
  }
}
Warnings:
Note	1003	update `test`.`t` set `test`.`t`.`j1` = json_replace(`test`.`t`.`j1`,'$.x','abc','$.b','Y')
SET optimizer_trace="enabled=on";
UPDATE t SET j1 = JSON_REPLACE(j1, '$.x', 'abc', '$.b', 'Y');
SET optimizer_trace="enabled=off";
SELECT JSON_PRETTY(JSON_OBJECT(
'json_partial_update', trace->'$**.json_partial_update',
'rejected_columns', trace->'$**.rejected_columns'))
AS json_trace
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
json_trace
{
  "rejected_columns": [
    [
      {
        "cause": "Storage engine does not support partial update",
        "table": "`t`",
        "column": "j1"
      }
    ]
  ],
  "json_partial_update": null
}
SELECT *, JSON_STORAGE_SIZE(j1) ss1,
JSON_STORAGE_FREE(j1) sf1,
JSON_STORAGE_SIZE(j2) ss2,
JSON_STORAGE_FREE(j2) sf2 FROM t ORDER BY id;
id	j1	j2	ss1	sf1	ss2	sf2
1	{"a": "s", "b": "Y"}	{"a": "abcdefghi", "b": "b"}	25	0	33	0
2	NULL	NULL	NULL	NULL	NULL	NULL
3	{"a": "s", "b": "Y"}	{"a": "abcdefghi", "b": "bb"}	25	0	34	0
EXPLAIN FORMAT=JSON UPDATE t SET j1 = JSON_SET(j1, '$.x', 'abc', '$.b', 'Z');
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t",
      "partitions": [
        "p0",
        "p1",
        "p2",
        "p3",
        "p4",
        "p5",
        "p6",
        "p7"
      ],
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00"
    }
  }
}
Warnings:
Note	1003	update `test`.`t` set `test`.`t`.`j1` = json_set(`test`.`t`.`j1`,'$.x','abc','$.b','Z')
SET optimizer_trace="enabled=on";
UPDATE t SET j1 = JSON_SET(j1, '$.x', 'abc', '$.b', 'Z');
SET optimizer_trace="enabled=off";
SELECT JSON_PRETTY(JSON_OBJECT(
'json_partial_update', trace->'$**.json_partial_update',
'rejected_columns', trace->'$**.rejected_columns'))
AS json_trace
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
json_trace
{
  "rejected_columns": [
    [
      {
        "cause": "Storage engine does not support partial update",
        "table": "`t`",
        "column": "j1"
      }
    ]
  ],
  "json_partial_update": null
}
SELECT *, JSON_STORAGE_SIZE(j1) ss1,
JSON_STORAGE_FREE(j1) sf1,
JSON_STORAGE_SIZE(j2) ss2,
JSON_STORAGE_FREE(j2) sf2 FROM t ORDER BY id;
id	j1	j2	ss1	sf1	ss2	sf2
1	{"a": "s", "b": "Z", "x": "abc"}	{"a": "abcdefghi", "b": "b"}	37	0	33	0
2	NULL	NULL	NULL	NULL	NULL	NULL
3	{"a": "s", "b": "Z", "x": "abc"}	{"a": "abcdefghi", "b": "bb"}	37	0	34	0
EXPLAIN FORMAT=JSON UPDATE t SET j1 = JSON_SET(j2, '$.a', 'ABC');
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t",
      "partitions": [
        "p0",
        "p1",
        "p2",
        "p3",
        "p4",
        "p5",
        "p6",
        "p7"
      ],
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00"
    }
  }
}
Warnings:
Note	1003	update `test`.`t` set `test`.`t`.`j1` = json_set(`test`.`t`.`j2`,'$.a','ABC')
SET optimizer_trace="enabled=on";
UPDATE t SET j1 = JSON_SET(j2, '$.a', 'ABC');
SET optimizer_trace="enabled=off";
SELECT JSON_PRETTY(JSON_OBJECT(
'json_partial_update', trace->'$**.json_partial_update',
'rejected_columns', trace->'$**.rejected_columns'))
AS json_trace
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
json_trace
{
  "rejected_columns": [
    [
      {
        "cause": "Storage engine does not support partial update",
        "table": "`t`",
        "column": "j1"
      }
    ]
  ],
  "json_partial_update": null
}
SELECT *, JSON_STORAGE_SIZE(j1) ss1,
JSON_STORAGE_FREE(j1) sf1,
JSON_STORAGE_SIZE(j2) ss2,
JSON_STORAGE_FREE(j2) sf2 FROM t ORDER BY id;
id	j1	j2	ss1	sf1	ss2	sf2
1	{"a": "ABC", "b": "b"}	{"a": "abcdefghi", "b": "b"}	27	0	33	0
2	NULL	NULL	NULL	NULL	NULL	NULL
3	{"a": "ABC", "b": "bb"}	{"a": "abcdefghi", "b": "bb"}	28	0	34	0
EXPLAIN FORMAT=JSON UPDATE t SET j1 = JSON_ARRAY(j1, j1);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t",
      "partitions": [
        "p0",
        "p1",
        "p2",
        "p3",
        "p4",
        "p5",
        "p6",
        "p7"
      ],
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00"
    }
  }
}
Warnings:
Note	1003	update `test`.`t` set `test`.`t`.`j1` = json_array(`test`.`t`.`j1`,`test`.`t`.`j1`)
SET optimizer_trace="enabled=on";
UPDATE t SET j1 = JSON_ARRAY(j1, j1);
SET optimizer_trace="enabled=off";
SELECT JSON_PRETTY(JSON_OBJECT(
'json_partial_update', trace->'$**.json_partial_update',
'rejected_columns', trace->'$**.rejected_columns'))
AS json_trace
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
json_trace
{
  "rejected_columns": [
    [
      {
        "cause": "Storage engine does not support partial update",
        "table": "`t`",
        "column": "j1"
      }
    ]
  ],
  "json_partial_update": null
}
SELECT *, JSON_STORAGE_SIZE(j1) ss1,
JSON_STORAGE_FREE(j1) sf1,
JSON_STORAGE_SIZE(j2) ss2,
JSON_STORAGE_FREE(j2) sf2 FROM t ORDER BY id;
id	j1	j2	ss1	sf1	ss2	sf2
1	[{"a": "ABC", "b": "b"}, {"a": "ABC", "b": "b"}]	{"a": "abcdefghi", "b": "b"}	63	0	33	0
2	[null, null]	NULL	11	0	NULL	NULL
3	[{"a": "ABC", "b": "bb"}, {"a": "ABC", "b": "bb"}]	{"a": "abcdefghi", "b": "bb"}	65	0	34	0
EXPLAIN FORMAT=JSON UPDATE t SET j1 = JSON_SET(j2, '$.a', 1),
j2 = JSON_SET(j2, '$.a', 1);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t",
      "partitions": [
        "p0",
        "p1",
        "p2",
        "p3",
        "p4",
        "p5",
        "p6",
        "p7"
      ],
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00"
    }
  }
}
Warnings:
Note	1003	update `test`.`t` set `test`.`t`.`j1` = json_set(`test`.`t`.`j2`,'$.a',1),`test`.`t`.`j2` = json_set(`test`.`t`.`j2`,'$.a',1)
SET optimizer_trace="enabled=on";
UPDATE t SET j1 = JSON_SET(j2, '$.a', 1),
j2 = JSON_SET(j2, '$.a', 1);
SET optimizer_trace="enabled=off";
SELECT JSON_PRETTY(JSON_OBJECT(
'json_partial_update', trace->'$**.json_partial_update',
'rejected_columns', trace->'$**.rejected_columns'))
AS json_trace
FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
json_trace
{
  "rejected_columns": [
    [
      {
        "cause": "Storage engine does not support partial update",
        "table": "`t`",
        "column": "j1"
      },
      {
        "cause": "Storage engine does not support partial update",
        "table": "`t`",
        "column": "j2"
      }
    ]
  ],
  "json_partial_update": null
}
SELECT *, JSON_STORAGE_SIZE(j1) ss1,
JSON_STORAGE_FREE(j1) sf1,
JSON_STORAGE_SIZE(j2) ss2,
JSON_STORAGE_FREE(j2) sf2 FROM t ORDER BY id;
id	j1	j2	ss1	sf1	ss2	sf2
1	{"a": 1, "b": "b"}	{"a": 1, "b": "b"}	23	0	23	0
2	NULL	NULL	NULL	NULL	NULL	NULL
3	{"a": 1, "b": "bb"}	{"a": 1, "b": "bb"}	24	0	24	0
DROP TABLE t;
CREATE TABLE t(j JSON,
i INT DEFAULT 42,
db DOUBLE DEFAULT 2.34e-10,
dc DECIMAL(5, 3) DEFAULT 98.765,
ts TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
gc JSON GENERATED ALWAYS AS (JSON_ARRAY(i, db, dc, ts)));
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t(j, ts) VALUES
('[null, "abc", true, "def", false]', '2017-01-02 14:15:16');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[null, "abc", true, "def", false]	ARRAY	28	0
UPDATE t SET j = JSON_SET(j, '$[1]', 'xy', '$[3]', 'xyzw');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[null, "xy", true, "xyzw", false]	ARRAY	28	0
UPDATE t SET j = JSON_SET(j, '$[1]', 'a');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[null, "a", true, "xyzw", false]	ARRAY	27	0
UPDATE t SET j = JSON_SET(j, '$[3]', 'abcde');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[null, "a", true, "abcde", false]	ARRAY	28	0
UPDATE t SET j = '[[[[[1]]]]]';
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[[[[[1]]]]]	ARRAY	36	0
UPDATE t SET j = JSON_SET(JSON_SET(j, '$[0][0][0]', 'ab'), '$[0][0]', 'cdefgh');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[["cdefgh"]]	ARRAY	22	0
UPDATE t SET j = JSON_REPLACE(j, '$', JSON_ARRAY(123));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[123]	ARRAY	8	0
UPDATE t SET j = JSON_SET(j, '$', 456);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
456	INTEGER	3	0
UPDATE t SET j = JSON_ARRAY(1,2,3);
UPDATE t SET j = JSON_REPLACE(j, '$.a', 4);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[1, 2, 3]	ARRAY	14	0
UPDATE t SET j = JSON_REPLACE(j, '$[1000]', 1000);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[1, 2, 3]	ARRAY	14	0
UPDATE t SET j = JSON_SET(j, '$[1000]', 1000);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[1, 2, 3, 1000]	ARRAY	17	0
UPDATE t SET j = JSON_OBJECT('a',1,'b',2,'c',3);
UPDATE t SET j = JSON_REPLACE(j, '$[1]', 4);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
{"a": 1, "b": 2, "c": 3}	OBJECT	29	0
UPDATE t SET j = JSON_OBJECT('a',1,'b',2,'c',3);
UPDATE t SET j = JSON_REPLACE(j, '$[1]', 4);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
{"a": 1, "b": 2, "c": 3}	OBJECT	29	0
UPDATE t SET j = JSON_REPLACE(j, '$.a.b', 5);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
{"a": 1, "b": 2, "c": 3}	OBJECT	29	0
UPDATE t SET j = JSON_REPLACE(j, '$.a[2]', 6);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
{"a": 1, "b": 2, "c": 3}	OBJECT	29	0
UPDATE t SET j = JSON_ARRAY('abcdefghijklmnopqrstuvwxyz');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
["abcdefghijklmnopqrstuvwxyz"]	ARRAY	35	0
UPDATE t SET j = JSON_SET(j, '$[0]', 3.14e0);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[3.14]	ARRAY	16	0
UPDATE t SET j = JSON_SET(j, '$[0]', CAST(10000000000 AS UNSIGNED));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[10000000000]	ARRAY	16	0
UPDATE t SET j = JSON_SET(j, '$[0]', CAST(4000000000 AS SIGNED));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[4000000000]	ARRAY	16	0
UPDATE t SET j = JSON_SET(j, '$[0]', CAST(123 AS UNSIGNED));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[123]	ARRAY	8	0
UPDATE t SET j = JSON_SET(j, '$[0]', CAST(456 AS SIGNED));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[456]	ARRAY	8	0
UPDATE t SET j = JSON_SET(j, '$[0]', -123);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[-123]	ARRAY	8	0
UPDATE t SET j = JSON_SET(j, '$[0]', -4000000000);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[-4000000000]	ARRAY	16	0
UPDATE t SET j = JSON_SET(j, '$[0]', CAST(76543 AS SIGNED));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[76543]	ARRAY	12	0
UPDATE t SET j = JSON_SET(j, '$[0]', CAST(87654 AS UNSIGNED));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[87654]	ARRAY	12	0
UPDATE t SET j = JSON_SET(j, '$[0]', -100000);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[-100000]	ARRAY	12	0
UPDATE t SET j = JSON_SET(j, '$[0]', TRUE);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[true]	ARRAY	8	0
UPDATE t SET j = JSON_SET(j, '$[0]', NULL);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[null]	ARRAY	8	0
UPDATE t SET j = JSON_SET(j, '$[0]', CAST('abc' AS BINARY));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
["base64:type15:YWJj"]	ARRAY	13	0
UPDATE t SET j = JSON_SET(j, '$[0]', CAST('2016-12-23 13:16:40' AS DATETIME));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
["2016-12-23 13:16:40.000000"]	ARRAY	18	0
UPDATE t SET j = JSON_SET(j, '$[0]', CAST('2016-12-24' AS DATE));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
["2016-12-24"]	ARRAY	18	0
UPDATE t SET j = JSON_SET(j, '$[0]', CAST('14:17:41' AS TIME));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
["14:17:41.000000"]	ARRAY	18	0
UPDATE t SET j = JSON_SET(j, '$[0]', 1.23);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[1.23]	ARRAY	14	0
UPDATE t SET j = JSON_SET(j, '$[0]', i);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[42]	ARRAY	8	0
UPDATE t SET j = JSON_SET(j, '$[0]', db);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[0.000000000234]	ARRAY	16	0
UPDATE t SET j = JSON_SET(j, '$[0]', dc);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[98.765]	ARRAY	15	0
UPDATE t SET j = JSON_SET(j, '$[0]', ts);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
["2017-01-02 14:15:16.000000"]	ARRAY	18	0
UPDATE t SET j = JSON_SET(j, '$[0]', JSON_ARRAY());
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[[]]	ARRAY	12	0
UPDATE t SET j = JSON_SET(j, '$[0]', JSON_ARRAY('abc'));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[["abc"]]	ARRAY	19	0
UPDATE t SET j = JSON_SET(j, '$[0]', JSON_OBJECT());
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[{}]	ARRAY	12	0
UPDATE t SET j = JSON_SET(j, '$[0]', JSON_OBJECT('abc', 'def'));
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[{"abc": "def"}]	ARRAY	26	0
UPDATE t SET j = JSON_SET(j, '$[0]', gc->'$[0]');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[42]	ARRAY	8	0
UPDATE t SET j = JSON_SET(j, '$[0]', gc->'$[1]');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[0.000000000234]	ARRAY	16	0
UPDATE t SET j = JSON_SET(j, '$[0]', gc->'$[2]');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[98.765]	ARRAY	15	0
UPDATE t SET j = JSON_SET(j, '$[0]', gc->'$[3]');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
["2017-01-02 14:15:16.000000"]	ARRAY	18	0
UPDATE t SET j = JSON_SET(j, '$[0]', gc->'$[4]');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[null]	ARRAY	8	0
# Test update with indexes relative to the end of the array.
UPDATE t SET j = JSON_ARRAY('a','b','c');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
["a", "b", "c"]	ARRAY	20	0
UPDATE t SET j = JSON_SET(j, '$[last]', 4);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
["a", "b", 4]	ARRAY	18	0
UPDATE t SET j = JSON_SET(j, '$[last-1]', 5);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
["a", 5, 4]	ARRAY	16	0
UPDATE t SET j = JSON_SET(j, '$[last-2]', 6);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[6, 5, 4]	ARRAY	14	0
UPDATE t SET j = JSON_SET(j, '$[last-3]', 7);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[7, 6, 5, 4]	ARRAY	17	0
UPDATE t SET j = JSON_ARRAY('a','b');
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
["a", "b"]	ARRAY	15	0
UPDATE t SET j = JSON_REPLACE(j, '$[last]', 3);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
["a", 3]	ARRAY	13	0
UPDATE t SET j = JSON_REPLACE(j, '$[last-1]', 4);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[4, 3]	ARRAY	11	0
UPDATE t SET j = JSON_REPLACE(j, '$[last-2]', 5);
SELECT j, JSON_TYPE(j) jt, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t;
j	jt	ss	sf
[4, 3]	ARRAY	11	0
DROP TABLE t;
CREATE TABLE t1(id INT PRIMARY KEY, j JSON, x INT);
INSERT INTO t1 VALUES (1, '{"a":"xy"}', 1), (2, '{"a":"zw"}', 2);
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t1 ORDER BY id;
id	j	x	ss	sf
1	{"a": "xy"}	1	16	0
2	{"a": "zw"}	2	16	0
CREATE TABLE t2(id INT PRIMARY KEY, j JSON, x INT);
INSERT INTO t2 VALUES
(1, '{"b":"X"}', 3),
(2, '{"b":"Y"}', 4),
(3, '{"b":"Z"}', 9);
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t2 ORDER BY id;
id	j	x	ss	sf
1	{"b": "X"}	3	15	0
2	{"b": "Y"}	4	15	0
3	{"b": "Z"}	9	15	0
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
EXPLAIN FORMAT=JSON UPDATE t1, t2
SET t1.j = JSON_SET(t1.j, '$.a', t2.j->'$.b')
WHERE t1.id = t2.id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "500.90"
    },
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t1",
          "partitions": [
            "p0",
            "p1",
            "p2",
            "p3",
            "p4",
            "p5",
            "p6",
            "p7"
          ],
          "access_type": "ALL",
          "possible_keys": [
            "PRIMARY"
          ],
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "500.00",
            "eval_cost": "0.20",
            "prefix_cost": "500.20",
            "data_read_per_join": "48"
          },
          "used_columns": "filtered due to bug#22472365"
        }
      },
      {
        "table": {
          "table_name": "t2",
          "partitions": [
            "p0",
            "p1",
            "p2",
            "p3",
            "p4",
            "p5",
            "p6",
            "p7"
          ],
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ],
          "key": "PRIMARY",
          "used_key_parts": [
            "id"
          ],
          "key_length": "4",
          "ref": [
            "test.t1.id"
          ],
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.20",
            "prefix_cost": "500.90",
            "data_read_per_join": "48"
          },
          "used_columns": "filtered due to bug#22472365"
        }
      }
    ]
  }
}
Warnings:
Note	1003	Table 't1' is not pushable: select list can't contain BLOB columns
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`j` = json_set(`test`.`t1`.`j`,'$.a',json_extract(`test`.`t2`.`j`,'$.b')) where (`test`.`t2`.`id` = `test`.`t1`.`id`)
SET optimizer_trace="enabled=on";
UPDATE t1, t2
SET t1.j = JSON_SET(t1.j, '$.a', t2.j->'$.b')
WHERE t1.id = t2.id;
SET optimizer_trace="enabled=off";
SELECT trace->'$**.json_partial_update'
       FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
trace->'$**.json_partial_update'
NULL
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t1 ORDER BY id;
id	j	x	ss	sf
1	{"a": "X"}	1	15	0
2	{"a": "Y"}	2	15	0
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t2 ORDER BY id;
id	j	x	ss	sf
1	{"b": "X"}	3	15	0
2	{"b": "Y"}	4	15	0
3	{"b": "Z"}	9	15	0
EXPLAIN FORMAT=JSON UPDATE t1, t2 SET t1.j = JSON_SET(t1.j, '$.a', t2.x),
t2.j = JSON_SET(t2.j, '$.b', t1.x)
WHERE t1.id = t2.id;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "500.90"
    },
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t1",
          "partitions": [
            "p0",
            "p1",
            "p2",
            "p3",
            "p4",
            "p5",
            "p6",
            "p7"
          ],
          "access_type": "ALL",
          "possible_keys": [
            "PRIMARY"
          ],
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "500.00",
            "eval_cost": "0.20",
            "prefix_cost": "500.20",
            "data_read_per_join": "48"
          },
          "used_columns": [
            "id",
            "j",
            "x"
          ]
        }
      },
      {
        "table": {
          "update": true,
          "table_name": "t2",
          "partitions": [
            "p0",
            "p1",
            "p2",
            "p3",
            "p4",
            "p5",
            "p6",
            "p7"
          ],
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ],
          "key": "PRIMARY",
          "used_key_parts": [
            "id"
          ],
          "key_length": "4",
          "ref": [
            "test.t1.id"
          ],
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.20",
            "prefix_cost": "500.90",
            "data_read_per_join": "48"
          },
          "used_columns": [
            "id",
            "j",
            "x"
          ]
        }
      }
    ]
  }
}
Warnings:
Note	1003	Table 't1' is not pushable: select list can't contain BLOB columns
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`j` = json_set(`test`.`t1`.`j`,'$.a',`test`.`t2`.`x`),`test`.`t2`.`j` = json_set(`test`.`t2`.`j`,'$.b',`test`.`t1`.`x`) where (`test`.`t2`.`id` = `test`.`t1`.`id`)
SET optimizer_trace="enabled=on";
UPDATE t1, t2 SET t1.j = JSON_SET(t1.j, '$.a', t2.x),
t2.j = JSON_SET(t2.j, '$.b', t1.x)
WHERE t1.id = t2.id;
SET optimizer_trace="enabled=off";
SELECT trace->'$**.json_partial_update'
       FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
trace->'$**.json_partial_update'
NULL
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t1 ORDER BY id;
id	j	x	ss	sf
1	{"a": 3}	1	13	0
2	{"a": 4}	2	13	0
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t2 ORDER BY id;
id	j	x	ss	sf
1	{"b": 1}	3	13	0
2	{"b": 2}	4	13	0
3	{"b": "Z"}	9	15	0
EXPLAIN FORMAT=JSON UPDATE t1, t2 SET t1.j = '[123]', t2.j = JSON_SET(t2.j, '$.b', 99);
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1250.80"
    },
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t1",
          "partitions": [
            "p0",
            "p1",
            "p2",
            "p3",
            "p4",
            "p5",
            "p6",
            "p7"
          ],
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "500.00",
            "eval_cost": "0.20",
            "prefix_cost": "500.20",
            "data_read_per_join": "48"
          },
          "used_columns": "filtered due to bug#22472365"
        }
      },
      {
        "table": {
          "update": true,
          "table_name": "t2",
          "partitions": [
            "p0",
            "p1",
            "p2",
            "p3",
            "p4",
            "p5",
            "p6",
            "p7"
          ],
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 6,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "750.00",
            "eval_cost": "0.60",
            "prefix_cost": "1250.80",
            "data_read_per_join": "144"
          },
          "used_columns": "filtered due to bug#22472365"
        }
      }
    ]
  }
}
Warnings:
Note	1003	Table 't1' is not pushable: lock modes other than 'read committed' not implemented
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`j` = '[123]',`test`.`t2`.`j` = json_set(`test`.`t2`.`j`,'$.b',99)
SET optimizer_trace="enabled=on";
UPDATE t1, t2 SET t1.j = '[123]', t2.j = JSON_SET(t2.j, '$.b', 99);
SET optimizer_trace="enabled=off";
SELECT trace->'$**.json_partial_update'
       FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
trace->'$**.json_partial_update'
NULL
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t1 ORDER BY id;
id	j	x	ss	sf
1	[123]	1	8	0
2	[123]	2	8	0
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t2 ORDER BY id;
id	j	x	ss	sf
1	{"b": 99}	3	13	0
2	{"b": 99}	4	13	0
3	{"b": 99}	9	13	0
DROP TABLE t1, t2;
CREATE TABLE t(j JSON) ENGINE=MyISAM;
INSERT INTO t VALUES ('["abcdef"]');
SELECT j, JSON_STORAGE_SIZE(j), JSON_STORAGE_FREE(j) FROM t;
j	JSON_STORAGE_SIZE(j)	JSON_STORAGE_FREE(j)
["abcdef"]	15	0
SET optimizer_trace="enabled=on";
UPDATE t SET j = JSON_SET(j, '$[0]', 1);
SET optimizer_trace="enabled=off";
SELECT trace->'$**.json_partial_update'
       FROM INFORMATION_SCHEMA.OPTIMIZER_TRACE;
trace->'$**.json_partial_update'
NULL
SELECT j, JSON_STORAGE_SIZE(j), JSON_STORAGE_FREE(j) FROM t;
j	JSON_STORAGE_SIZE(j)	JSON_STORAGE_FREE(j)
[1]	8	0
DROP TABLE t;
# Partial update on a DEFAULT NULL value in a NOT NULL column.
CREATE TABLE t(j JSON NOT NULL);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT IGNORE INTO t VALUES ();
Warnings:
Warning	1364	Field 'j' doesn't have a default value
UPDATE t SET j = JSON_SET(j, '$[0]', 'abc');
SELECT j, JSON_STORAGE_SIZE(j), JSON_STORAGE_FREE(j) FROM t;
j	JSON_STORAGE_SIZE(j)	JSON_STORAGE_FREE(j)
"abc"	5	0
UPDATE IGNORE t SET j = DEFAULT;
Warnings:
Warning	1364	Field 'j' doesn't have a default value
UPDATE t SET j = JSON_REMOVE(j, '$[0]');
SELECT j, JSON_STORAGE_SIZE(j), JSON_STORAGE_FREE(j) FROM t;
j	JSON_STORAGE_SIZE(j)	JSON_STORAGE_FREE(j)
null	0	0
DROP TABLE t;
#
# Bug#25893720: TABLE::SETUP_PARTIAL_UPDATE():
#               ASSERTION `M_PARTIAL_UPDATE_INFO == NULLPTR' FAIL
#
CREATE TEMPORARY TABLE t(j JSON);
INSERT INTO t VALUES ('["abc"]');
UPDATE t SET j = JSON_SET(j, '$[0]', 'de');
UPDATE t SET j = JSON_SET(j, '$[0]', 'f');
SELECT j, JSON_STORAGE_SIZE(j), JSON_STORAGE_FREE(j) FROM t;
j	JSON_STORAGE_SIZE(j)	JSON_STORAGE_FREE(j)
["f"]	12	2
DROP TABLE t;
# Partial update in JSON_REMOVE.
CREATE TABLE t(id INT PRIMARY KEY AUTO_INCREMENT, j JSON);
INSERT INTO t(j) VALUES
(JSON_ARRAY('x', JSON_ARRAY(1, 2, 3, 'abc', 4, 5, 6))),
(JSON_OBJECT('x', JSON_OBJECT('a', 'one',
'b', 'two',
'c', 'ABC',
'defghijkl', 'm'),
'y', 'z')),
('null'),
(NULL);
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	["x", [1, 2, 3, "abc", 4, 5, 6]]	42	0
2	{"x": {"a": "one", "b": "two", "c": "ABC", "defghijkl": "m"}, "y": "z"}	81	0
3	null	2	0
4	NULL	NULL	NULL
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
EXPLAIN FORMAT=JSON UPDATE t SET j = JSON_REMOVE(j, '$.x.b', '$[1][2]');
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t",
      "partitions": [
        "p0",
        "p1",
        "p2",
        "p3",
        "p4",
        "p5",
        "p6",
        "p7"
      ],
      "access_type": "ALL",
      "rows_examined_per_scan": 4,
      "filtered": "100.00"
    }
  }
}
Warnings:
Note	1003	update `test`.`t` set `test`.`t`.`j` = json_remove(`test`.`t`.`j`,'$.x.b','$[1][2]')
UPDATE t SET j = JSON_REMOVE(j, '$.x.b', '$[1][2]');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	["x", [1, 2, "abc", 4, 5, 6]]	39	0
2	{"x": {"a": "one", "c": "ABC", "defghijkl": "m"}, "y": "z"}	69	0
3	null	2	0
4	NULL	NULL	NULL
# See that the free space can be reused by increasing
# the size of some values.
UPDATE t SET j = JSON_REPLACE(j, '$[1][2]', 'xyzw', '$.x.c', 'XYZW');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	["x", [1, 2, "xyzw", 4, 5, 6]]	40	0
2	{"x": {"a": "one", "c": "XYZW", "defghijkl": "m"}, "y": "z"}	70	0
3	null	2	0
4	NULL	NULL	NULL
# Nested calls
UPDATE t SET j = JSON_REMOVE(JSON_REMOVE(j, '$[1][2]'), '$.x.c');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	["x", [1, 2, 4, 5, 6]]	32	0
2	{"x": {"a": "one", "defghijkl": "m"}, "y": "z"}	57	0
3	null	2	0
4	NULL	NULL	NULL
UPDATE t SET j = JSON_REMOVE(JSON_SET(j, '$.x.a', 'xyz'), '$.x.a');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	["x", [1, 2, 4, 5, 6]]	32	0
2	{"x": {"defghijkl": "m"}, "y": "z"}	45	0
3	null	2	0
4	NULL	NULL	NULL
UPDATE t SET j = JSON_REMOVE(JSON_SET(j, '$.x.a', 'zyx'), '$.x.a');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	["x", [1, 2, 4, 5, 6]]	32	0
2	{"x": {"defghijkl": "m"}, "y": "z"}	45	0
3	null	2	0
4	NULL	NULL	NULL
UPDATE t SET j = JSON_SET(JSON_REMOVE(j, '$.x.a'), '$.x.a', 'abcd', '$.x.b', 9);
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	["x", [1, 2, 4, 5, 6]]	32	0
2	{"x": {"a": "abcd", "b": 9, "defghijkl": "m"}, "y": "z"}	66	0
3	null	2	0
4	NULL	NULL	NULL
UPDATE t SET j = JSON_SET(JSON_REMOVE(j, '$.x.b'), '$.x.a', 'efg');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	["x", [1, 2, 4, 5, 6]]	32	0
2	{"x": {"a": "efg", "defghijkl": "m"}, "y": "z"}	57	0
3	null	2	0
4	NULL	NULL	NULL
UPDATE t SET j = JSON_REMOVE(JSON_REMOVE(j, '$[0]', '$[0]', '$.x'),
'$.b', '$.y', '$.z');
SELECT *, JSON_STORAGE_SIZE(j) ss,
JSON_STORAGE_FREE(j) sf FROM t ORDER BY id;
id	j	ss	sf
1	[]	5	0
2	{}	5	0
3	null	2	0
4	NULL	NULL	NULL
DROP TABLE t;
#
# WL#9692: Add JSON_MERGE_PATCH
#
CREATE TABLE merge_t(
id INT PRIMARY KEY AUTO_INCREMENT,
target VARCHAR(100), patch VARCHAR(100)
);
INSERT INTO merge_t(target, patch) VALUES
('{"a":"b"}', '{"a":"c"}'),
('{"a":"b"}', '{"b":"c"}'),
('{"a":"b"}', '{"a":null}'),
('{"a":"b", "b":"c"}', '{"a":null}'),
('{"a":["b"]}', '{"a":"c"}'),
('{"a":"c"}', '{"a":["b"]}'),
('{"a": {"b":"c"}}', '{"a": {"b":"d", "c":null}}'),
('{"a":[{"b":"c"}]}', '{"a": [1]}'),
('["a","b"]', '["c","d"]'),
('{"a":"b"}', '["c"]'),
('{"a":"foo"}', 'null'),
('{"a":"foo"}', '"bar"'),
('{"e":null}', '{"a":1}'),
('[1,2]', '{"a":"b", "c":null}'),
('{}', '{"a":{"bb":{"ccc":null}}}'),
(NULL, '{}'),
('{}', NULL);
SELECT id, target, patch,
JSON_MERGE_PATCH(target, patch) AS merged,
JSON_EXTRACT(JSON_MERGE_PATCH(target, patch), '$.a') AS a
FROM merge_t ORDER BY id;
id	target	patch	merged	a
1	{"a":"b"}	{"a":"c"}	{"a": "c"}	"c"
2	{"a":"b"}	{"b":"c"}	{"a": "b", "b": "c"}	"b"
3	{"a":"b"}	{"a":null}	{}	NULL
4	{"a":"b", "b":"c"}	{"a":null}	{"b": "c"}	NULL
5	{"a":["b"]}	{"a":"c"}	{"a": "c"}	"c"
6	{"a":"c"}	{"a":["b"]}	{"a": ["b"]}	["b"]
7	{"a": {"b":"c"}}	{"a": {"b":"d", "c":null}}	{"a": {"b": "d"}}	{"b": "d"}
8	{"a":[{"b":"c"}]}	{"a": [1]}	{"a": [1]}	[1]
9	["a","b"]	["c","d"]	["c", "d"]	NULL
10	{"a":"b"}	["c"]	["c"]	NULL
11	{"a":"foo"}	null	null	NULL
12	{"a":"foo"}	"bar"	"bar"	NULL
13	{"e":null}	{"a":1}	{"a": 1, "e": null}	1
14	[1,2]	{"a":"b", "c":null}	{"a": "b"}	"b"
15	{}	{"a":{"bb":{"ccc":null}}}	{"a": {"bb": {}}}	{"bb": {}}
16	NULL	{}	NULL	NULL
17	{}	NULL	NULL	NULL
DROP TABLE merge_t;
SELECT JSON_MERGE_PATCH('{"a":"b"}', NULL, '{"c":"d"}');
JSON_MERGE_PATCH('{"a":"b"}', NULL, '{"c":"d"}')
NULL
SELECT JSON_MERGE_PATCH(NULL, '[1,2,3]');
JSON_MERGE_PATCH(NULL, '[1,2,3]')
[1, 2, 3]
SELECT JSON_MERGE_PATCH('{"a":"b"}', NULL, '[1,2,3]', '{"c":null,"d":"e"}');
JSON_MERGE_PATCH('{"a":"b"}', NULL, '[1,2,3]', '{"c":null,"d":"e"}')
{"d": "e"}
SELECT JSON_MERGE_PATCH();
ERROR 42000: Incorrect parameter count in the call to native function 'JSON_MERGE_PATCH'
SELECT JSON_MERGE_PATCH('{}');
ERROR 42000: Incorrect parameter count in the call to native function 'JSON_MERGE_PATCH'
SELECT JSON_MERGE_PATCH('{', '[1,2,3]');
ERROR 22032: Invalid JSON text in argument 1 to function json_merge_patch: "Missing a name for object member." at position 1.
SELECT JSON_MERGE_PATCH('{"a":"b"}', '[1,');
ERROR 22032: Invalid JSON text in argument 2 to function json_merge_patch: "Invalid value." at position 3.
#
# Bug#28947381: MAX TREATING NUMBERS AS STRINGS WHEN USED
#               IN GROUP BY AND JSON_UNQUOTE
#
CREATE TABLE t(j JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t VALUES
('{"group": 1, "value": 20}'),
('{"group": 1, "value": 100}'),
(NULL),
('{"group": 2, "value": "abc"}'),
('{"group": 3, "value": "xyz"}'),
('{"group": 2, "value": "def"}'),
('{"group": 3, "value": "xyzw"}'),
('{"group": 1, "value": 3}'),
('{"group": 1}');
SELECT j->>'$.group' AS group_name,
MIN(j->'$.value') AS min_json,
MAX(j->'$.value') AS max_json,
MIN(j->>'$.value') AS min_string,
MAX(j->>'$.value') AS max_string
FROM t GROUP BY j->>'$.group';
group_name	min_json	max_json	min_string	max_string
1	3	100	100	3
2	"abc"	"def"	abc	def
3	"xyz"	"xyzw"	xyz	xyzw
NULL	NULL	NULL	NULL	NULL
DROP TABLE t;
#
# WL#8199: Add JSON support to IN/GREATEST/LEAST 
#
SELECT CAST(1 AS JSON) IN(CAST(1 AS JSON), CAST(2 AS JSON), CAST(3 AS JSON));
CAST(1 AS JSON) IN(CAST(1 AS JSON), CAST(2 AS JSON), CAST(3 AS JSON))
1
SELECT CAST(4 AS JSON) IN(CAST(1 AS JSON), CAST(2 AS JSON), CAST(3 AS JSON));
CAST(4 AS JSON) IN(CAST(1 AS JSON), CAST(2 AS JSON), CAST(3 AS JSON))
0
SELECT CAST(1 AS JSON) IN(CAST(1 AS JSON), CAST(2 AS JSON), NULL);
CAST(1 AS JSON) IN(CAST(1 AS JSON), CAST(2 AS JSON), NULL)
1
SELECT CAST(4 AS JSON) IN(CAST(1 AS JSON), CAST(2 AS JSON), NULL);
CAST(4 AS JSON) IN(CAST(1 AS JSON), CAST(2 AS JSON), NULL)
NULL
SELECT CAST('{"a":1, "b":2}' AS JSON) IN(
CAST(1 AS JSON),
CAST('{"a":1, "b":2}' AS JSON),
CAST('[1,2,3]' AS JSON));
CAST('{"a":1, "b":2}' AS JSON) IN(
CAST(1 AS JSON),
CAST('{"a":1, "b":2}' AS JSON),
CAST('[1,2,3]' AS JSON))
1
SELECT CAST('[1,2,3]' AS JSON) IN(
CAST(1 AS JSON),
CAST('{"a":1, "b":2}' AS JSON),
CAST('[1,2,3]' AS JSON));
CAST('[1,2,3]' AS JSON) IN(
CAST(1 AS JSON),
CAST('{"a":1, "b":2}' AS JSON),
CAST('[1,2,3]' AS JSON))
1
SELECT CAST('{"a":1, "b":2}' AS JSON) IN(
NULL,
CAST('{"a":1, "b":2}' AS JSON),
CAST('[1,2,3]' AS JSON));
CAST('{"a":1, "b":2}' AS JSON) IN(
NULL,
CAST('{"a":1, "b":2}' AS JSON),
CAST('[1,2,3]' AS JSON))
1
SELECT CAST('{"a":1, "b":2}' AS JSON) IN(
CAST(1 AS JSON),
CAST('{"a":1, "b":20}' AS JSON),
CAST('[1,2,3]' AS JSON));
CAST('{"a":1, "b":2}' AS JSON) IN(
CAST(1 AS JSON),
CAST('{"a":1, "b":20}' AS JSON),
CAST('[1,2,3]' AS JSON))
0
SELECT CAST('{"a":1, "b":2}' AS JSON) IN(
NULL,
CAST('{"a":1, "b":20}' AS JSON),
CAST('[1,2,3]' AS JSON));
CAST('{"a":1, "b":2}' AS JSON) IN(
NULL,
CAST('{"a":1, "b":20}' AS JSON),
CAST('[1,2,3]' AS JSON))
NULL
SELECT CAST('{"b":2, "a":2}' AS JSON) IN(CAST(@a_var AS JSON), CAST(NULL AS JSON));
CAST('{"b":2, "a":2}' AS JSON) IN(CAST(@a_var AS JSON), CAST(NULL AS JSON))
NULL
#
# BUG#28179109: JSON COLUMN WITH STRING VALUE GOT INCORRECT RESULT
# BUG#28182471: JSON COLUMN WITH INTEGER VALUE GOT INCORRECT RESULT
#
CREATE TABLE t1 (id INT, jdoc JSON);
Warnings:
Warning	1478	Table storage engine 'ndbcluster' does not support the create option 'Binlog of table with BLOB attribute and no PK'
INSERT INTO t1 VALUES(1, '{"key1": "111", "key2": "222"}');
INSERT INTO t1 VALUES(2, '{"key1": "333", "key2": "444"}');
SELECT *, JSON_EXTRACT(jdoc,'$.key1') from t1 ORDER BY id;
id	jdoc	JSON_EXTRACT(jdoc,'$.key1')
1	{"key1": "111", "key2": "222"}	"111"
2	{"key1": "333", "key2": "444"}	"333"
SELECT * FROM t1 WHERE JSON_EXTRACT(jdoc,'$.key1') IN ('"111"');
id	jdoc
SELECT * FROM t1 WHERE JSON_EXTRACT(jdoc,'$.key1') = ('"111"');
id	jdoc
SELECT * FROM t1 WHERE JSON_EXTRACT(jdoc,'$.key1') IN ('"111"', '"333"')
ORDER BY id;
id	jdoc
SELECT * FROM t1 WHERE JSON_EXTRACT(jdoc,'$.key1') IN ('111');
id	jdoc
1	{"key1": "111", "key2": "222"}
SELECT * FROM t1 WHERE JSON_EXTRACT(jdoc,'$.key1') = ('111');
id	jdoc
1	{"key1": "111", "key2": "222"}
SELECT * FROM t1 WHERE JSON_EXTRACT(jdoc,'$.key1') IN ('111', '333')
ORDER BY id;
id	jdoc
1	{"key1": "111", "key2": "222"}
2	{"key1": "333", "key2": "444"}
SELECT * FROM t1 WHERE '111' IN (JSON_EXTRACT(jdoc,'$.key1'), '333');
id	jdoc
1	{"key1": "111", "key2": "222"}
INSERT INTO t1 VALUES(3, '{"key1": 1, "key2": 11}');
INSERT INTO t1 VALUES(4, '{"key1": 2, "key2": 22}');
SELECT * FROM t1 WHERE JSON_EXTRACT(jdoc,'$.key1') IN ('1');
id	jdoc
SELECT * FROM t1 WHERE JSON_EXTRACT(jdoc,'$.key1') = ('1');
id	jdoc
SELECT * FROM t1 WHERE JSON_EXTRACT(jdoc,'$.key1') IN ('1', '2')
ORDER BY id;
id	jdoc
SELECT * FROM t1 WHERE JSON_EXTRACT(jdoc,'$.key1') IN (1);
id	jdoc
3	{"key1": 1, "key2": 11}
SELECT * FROM t1 WHERE JSON_EXTRACT(jdoc,'$.key1') = (1);
id	jdoc
3	{"key1": 1, "key2": 11}
SELECT * FROM t1 WHERE JSON_EXTRACT(jdoc,'$.key1') IN (1, 2)
ORDER BY id;
id	jdoc
3	{"key1": 1, "key2": 11}
4	{"key1": 2, "key2": 22}
SELECT * FROM t1 WHERE 1 IN (JSON_EXTRACT(jdoc,'$.key1'), 2);
id	jdoc
3	{"key1": 1, "key2": 11}
SELECT (5, CAST(1 AS JSON)) IN ((1, CAST(5 AS JSON)),(5, CAST(1 AS JSON)));
(5, CAST(1 AS JSON)) IN ((1, CAST(5 AS JSON)),(5, CAST(1 AS JSON)))
1
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'comparison of JSON within a ROW in the IN operator'
SELECT (5, 1) IN ((1, CAST(5 AS JSON)),(5, CAST(1 AS JSON)));
(5, 1) IN ((1, CAST(5 AS JSON)),(5, CAST(1 AS JSON)))
1
Warnings:
Warning	1235	This version of MySQL doesn't yet support 'comparison of JSON within a ROW in the IN operator'
DROP TABLE t1;
#
# Bug#30753642: ASSERTION `FALSE' FAILED. IN ITEM_JSON::VAL_STR
#
CREATE TABLE t(a DATETIME);
INSERT INTO t VALUES ('2019-01-13 00:00:00');
SELECT 1 IN (SELECT 1 FROM t WHERE JSON_OBJECT('a', 'b') = a);
1 IN (SELECT 1 FROM t WHERE JSON_OBJECT('a', 'b') = a)
0
Warnings:
Warning	3156	Invalid JSON value for CAST to DATE/TIME/DATETIME/TIMESTAMP from column (null) at row 1
DROP TABLE t;
#
# Bug#25840784: PARTIAL UPDATE ON VIEWS DOES NOT WORK AS EXPECTED ON BASE TABLES
#
CREATE TABLE t1(id INT PRIMARY KEY, j JSON);
CREATE VIEW v1 AS SELECT id, j, JSON_REMOVE(j, '$.b') jj FROM t1;
INSERT INTO t1 VALUES
(1, '{"a": "abcdefghijklmnopqrstuvwxyz", "b":"abcdef"}'),
(2, '{"a": "abcdefghijklmnopqrstuvwxyz"}'),
(3, '{"b":"abcdef"}');
UPDATE v1 SET j = JSON_SET(j, '$.a', 'abcdefghijklm');
SELECT j, JSON_STORAGE_SIZE(j), JSON_STORAGE_FREE(j) FROM v1 ORDER BY id;
j	JSON_STORAGE_SIZE(j)	JSON_STORAGE_FREE(j)
{"a": "abcdefghijklm", "b": "abcdef"}	42	0
{"a": "abcdefghijklm"}	27	0
{"a": "abcdefghijklm", "b": "abcdef"}	42	0
UPDATE v1 SET j = JSON_SET(jj, '$.a', 'abcdefg');
SELECT j, JSON_STORAGE_SIZE(j), JSON_STORAGE_FREE(j) FROM v1 ORDER BY id;
j	JSON_STORAGE_SIZE(j)	JSON_STORAGE_FREE(j)
{"a": "abcdefg"}	21	0
{"a": "abcdefg"}	21	0
{"a": "abcdefg"}	21	0
UPDATE v1 SET j = JSON_REMOVE(j, '$.a');
SELECT j, JSON_STORAGE_SIZE(j), JSON_STORAGE_FREE(j) FROM v1 ORDER BY id;
j	JSON_STORAGE_SIZE(j)	JSON_STORAGE_FREE(j)
{}	5	0
{}	5	0
{}	5	0
DROP TABLE t1;
DROP VIEW v1;
#
# Bug#32248705: PUTTING A MISSING ROW INTO JSON_OBJECT CAUSES
#               JSON_EXTRACT TO FAIL
#
CREATE TABLE t1(x VARCHAR(1));
CREATE TABLE t2(y VARCHAR(1));
INSERT INTO t1 VALUES ('a');
SELECT * FROM t1 LEFT JOIN t2 ON x=y WHERE JSON_ARRAY(y) <> 3;
x	y
a	NULL
SELECT * FROM t1 LEFT JOIN t2 ON x=y WHERE JSON_OBJECT('y', y) <> 3;
x	y
a	NULL
SELECT * FROM t1 LEFT JOIN t2 ON x=y WHERE JSON_ARRAY_APPEND('[]', '$', y) <> 3;
x	y
a	NULL
SELECT * FROM t1 LEFT JOIN t2 ON x=y WHERE JSON_INSERT('{}', '$.a', y) <> 3;
x	y
a	NULL
SELECT * FROM t1 LEFT JOIN t2 ON x=y WHERE JSON_ARRAY_INSERT('[]', '$[0]', y) <> 3;
x	y
a	NULL
SELECT * FROM t1 LEFT JOIN t2 ON x=y WHERE JSON_SET('{}', '$.a', y) <> 3;
x	y
a	NULL
SELECT * FROM t1 LEFT JOIN t2 ON x=y WHERE JSON_REPLACE('{}', '$.a', y) <> 3;
x	y
a	NULL
DROP TABLE t1, t2;
