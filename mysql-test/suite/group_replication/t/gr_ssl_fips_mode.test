################################################################################
# Test to verify the SSL Fips mode on the Group replication plugin
#
# Test:
# 0. The test requires two servers: M1 and M2.
# 1. Setup the first member (M1) with a recovery user that requires SSL with
#    ssl_fips_mode= 'ON'.
# 2. Start and bootstrap M1.
# 3. Insert some data and verify that it gives warning for MD5().
# 4. Configure joining member (M2) to use SSL and Fips mode options on recovery.
#    Start GR on M2. Check the data is there.
# 5. Restart M2 with weak cipher with Fips mode ON and verify it does not start.
# 6. Clean up.
################################################################################

--source include/big_test.inc
--source include/have_group_replication_xcom_communication_stack.inc
--source include/have_fips.inc
--source include/have_group_replication_plugin.inc
--let $rpl_skip_group_replication_start= 1
--source include/group_replication.inc


--echo #
--echo # 1. Setup the first member (M1) with a recovery user that requires SSL
--echo #    with ssl_fips_mode= 'ON'.
--echo #
--let $rpl_connection_name= server1
--source include/connection.inc

--let $allow_rpl_inited= 1
--let $_group_replication_local_address= `SELECT @@GLOBAL.group_replication_local_address`
--let $_group_replication_group_seeds= `SELECT @@GLOBAL.group_replication_group_seeds`
--let $restart_parameters=restart:--group_replication_local_address=$_group_replication_local_address --group_replication_group_seeds=$_group_replication_group_seeds --ssl-fips-mode=ON
--replace_result $_group_replication_local_address GROUP_REPLICATION_LOCAL_ADDRESS $_group_replication_group_seeds GROUP_REPLICATION_GROUP_SEEDS
--source include/restart_mysqld.inc

# Needed as we are not using rpl/restart_server.inc
--let $rpl_server_number= 1
--source include/rpl/reconnect.inc

# create a user for replication that requires ssl encryption
SET SESSION sql_log_bin= 0;
CREATE USER 'rec_ssl_user'@'%' REQUIRE SSL;
GRANT replication slave ON *.* TO 'rec_ssl_user'@'%';
SET SESSION sql_log_bin= 1;

--echo #
--echo # 2. Start and bootstrap M1.
--echo #
--source include/start_and_bootstrap_group_replication.inc


--echo #
--echo # 3. Insert some data and verify that it gives warning for MD5().
--echo #
CREATE TABLE t1 (c1 INT NOT NULL PRIMARY KEY, hash CHAR(32)) ENGINE=InnoDB;

INSERT INTO t1 VALUES (1, 'test');
INSERT INTO t1 VALUES (2, MD5(8));
SHOW WARNINGS;


--echo #
--echo # 4. Configure joining member (M2) to use SSL and Fips mode options on recovery.
--echo #    Start GR on M2. Check the data is there.
--echo #
--let $rpl_connection_name= server2
--source include/connection.inc

--let $allow_rpl_inited= 1
--let $_group_replication_local_address= `SELECT @@GLOBAL.group_replication_local_address`
--let $_group_replication_group_seeds= `SELECT @@GLOBAL.group_replication_group_seeds`
--let $restart_parameters=restart:--group_replication_local_address=$_group_replication_local_address --group_replication_group_seeds=$_group_replication_group_seeds --ssl-fips-mode=ON
--replace_result $_group_replication_local_address GROUP_REPLICATION_LOCAL_ADDRESS $_group_replication_group_seeds GROUP_REPLICATION_GROUP_SEEDS
--source include/restart_mysqld.inc

# Needed as we are not using rpl/restart_server.inc
--let $rpl_server_number= 2
--source include/rpl/reconnect.inc

SET @group_replication_recovery_use_ssl_save= @@GLOBAL.group_replication_recovery_use_ssl;
SET @group_replication_recovery_ssl_ca_save= @@GLOBAL.group_replication_recovery_ssl_ca;
SET @group_replication_recovery_ssl_cert_save= @@GLOBAL.group_replication_recovery_ssl_cert;
SET @group_replication_recovery_ssl_key_save= @@GLOBAL.group_replication_recovery_ssl_key;
SET @group_replication_recovery_ssl_cipher_save= @@GLOBAL.group_replication_recovery_ssl_cipher;
SET @group_replication_recovery_retry_count_save= @@GLOBAL.group_replication_recovery_retry_count;

--disable_warnings
CHANGE REPLICATION SOURCE TO SOURCE_USER="rec_ssl_user" FOR CHANNEL "group_replication_recovery";
--enable_warnings

SET GLOBAL group_replication_recovery_use_ssl= 1;
--replace_result $MYSQL_TEST_DIR MYSQL_TEST_DIR
--eval SET GLOBAL group_replication_recovery_ssl_ca= '$MYSQL_TEST_DIR/std_data/cacert.pem'
--replace_result $MYSQL_TEST_DIR MYSQL_TEST_DIR
--eval SET GLOBAL group_replication_recovery_ssl_cert= '$MYSQL_TEST_DIR/std_data/client-cert.pem'
--replace_result $MYSQL_TEST_DIR MYSQL_TEST_DIR
--eval SET GLOBAL group_replication_recovery_ssl_key= '$MYSQL_TEST_DIR/std_data/client-key.pem'

--source include/start_group_replication.inc

--echo #
--echo # Check the data is there
--echo #
--let $assert_text= On the recovered member, the table should exist and have 1 elements;
--let $assert_cond= [select count(*) from t1] = 2;
--source include/assert.inc


--echo #
--echo # 5. Restart M2 with weak cipher with Fips mode ON and verify it
--echo #    does not start.
--echo #
--source include/stop_group_replication.inc

# Commit one transaction on server1 that will need to be replicated through
# `group_replication_recovery` channel.
--let $rpl_connection_name= server1
--source include/connection.inc
INSERT INTO t1 VALUES (3, 'three');
--let $rpl_connection_name= server2
--source include/connection.inc

SET SESSION sql_log_bin= 0;
call mtr.add_suppression("There was an error when connecting to the donor server. Please check that group_replication_recovery channel credentials and all MEMBER_HOST column values of performance_schema.replication_group_members table are correct and DNS resolvable.");
call mtr.add_suppression("For details please check performance_schema.replication_connection_status table and error log messages of Replica I/O for channel group_replication_recovery.");
call mtr.add_suppression("Maximum number of retries when trying to connect to a donor reached. Aborting group replication incremental recovery.");
call mtr.add_suppression("Fatal error during the incremental recovery process of Group Replication. The server will leave the group.");
call mtr.add_suppression("Skipping leave operation: concurrent attempt to leave the group is on-going.");
call mtr.add_suppression("The server was automatically set into read only mode after an error was detected.");
call mtr.add_suppression("The server was automatically set into offline mode after an error was detected.");
SET SESSION sql_log_bin= 1;

SET GLOBAL group_replication_recovery_ssl_cipher= 'CAMELLIA256-SHA';
SET GLOBAL group_replication_recovery_retry_count= 1;

--let $group_replication_start_member_state= ERROR
--source include/start_group_replication.inc

--source include/stop_group_replication.inc


--echo #
--echo # Clean up
--echo #
SET @@GLOBAL.group_replication_recovery_use_ssl= @group_replication_recovery_use_ssl_save;
SET @@GLOBAL.group_replication_recovery_ssl_ca= @group_replication_recovery_ssl_ca_save;
SET @@GLOBAL.group_replication_recovery_ssl_cert= @group_replication_recovery_ssl_cert_save;
SET @@GLOBAL.group_replication_recovery_ssl_key= @group_replication_recovery_ssl_key_save;
SET @@GLOBAL.group_replication_recovery_ssl_cipher= @group_replication_recovery_ssl_cipher_save;
SET @@GLOBAL.group_replication_recovery_retry_count= @group_replication_recovery_retry_count_save;

--let $rpl_connection_name= server1
--source include/connection.inc
SET SESSION sql_log_bin= 0;
DROP USER 'rec_ssl_user';
SET SESSION sql_log_bin= 1;

DROP TABLE t1;

--source include/group_replication_end.inc
--source include/force_restart.inc
