# Copyright (c) 2015, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
#
# GENERATED FILE
#
cmd: testDict
args: -n CreateInvalidTables T1
max-time: 180

cmd: testBlobs
args: -skip hp -bug 28116
max-time: 180

cmd: test_event
args: -n bug37672 T1
max-time: 180

cmd: testDict
args: -l 1 -n FailCreateHashmap T1
max-time: 180

cmd: testBasic
args: -n LeakApiConnectObjects T1
max-time: 180

cmd: testBlobs
args: -bug 45768 -skip p
max-time: 180

cmd: test_event
args: -n NextEventRemoveInconsisEvent T1
max-time: 180

cmd: testNdbApi
args: -n NdbClusterConnectionConnect T1
max-time: 180

cmd: testBasic
args: -n RefreshTuple T6 D1
max-time: 180

cmd: testDict
args: -n FK_TRANS1 T1
max-time: 180

cmd: testNdbApi
args: -n ReadColumnDuplicates
max-time: 180

cmd: testNdbApi
args: -n NdbClusterConnect T1
max-time: 180

cmd: testNdbApi
args: -n NdbClusterConnectNR_non_master T1
max-time: 180

cmd: test_event_merge
args: --no-implicit-nulls --separate-events --blob-version 1
max-time: 180

cmd: testNdbApi
args: -n Bug28443
max-time: 360

cmd: testBackup
args: -n BackupDDL T1
max-time: 360

cmd: testNodeRestart
args: -n Bug21271 T6
max-time: 180

cmd: testBasic
args: -r 5000 -n 899 T15 D1 D2
max-time: 360

cmd: testBasic
args: -n RefreshLocking D1
max-time: 360

cmd: testSpj
args: -n MixedJoin
max-time: 540

cmd: testBasic
args: -n PkDelete
max-time: 540

cmd: testBlobs
args: -version 1 -rows 25
max-time: 540

cmd: testNodeRestart
args: -n Bug26457 T1
max-time: 720

cmd: testNodeRestart
args: -n Bug24717 T1
max-time: 720

cmd: testSystemRestart
args: -n SR_DD_3 D2
max-time: 900

cmd: testNodeRestart
args: -n RestartAllNodesError9999 T6 T13
max-time: 1260

cmd: testNodeRestart
args: -n RestartAllNodes T6 T13
max-time: 1440

cmd: testNodeRestart
args: -n RestartRandomNode T6 T13
max-time: 1620

cmd: testSystemRestart
args: -n Bug24664
max-time: 2520

cmd: testIndex
args: -l 2 -n SR1 T6 T13
max-time: 4140

