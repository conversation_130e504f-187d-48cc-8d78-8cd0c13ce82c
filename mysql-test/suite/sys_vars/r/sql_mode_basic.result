SET @global_start_value = @@global.sql_mode;
SELECT @global_start_value;
@global_start_value
ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
SET @session_start_value = @@session.sql_mode;
SELECT @session_start_value;
@session_start_value
ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
'#--------------------FN_DYNVARS_152_01------------------------#'
SET @@global.sql_mode = ANSI;
SET @@global.sql_mode = DEFAULT;
SELECT @@global.sql_mode;
@@global.sql_mode
ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
SET @@session.sql_mode = ANSI;
SET @@session.sql_mode = DEFAULT;
SELECT @@session.sql_mode;
@@session.sql_mode
ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
'#---------------------FN_DYNVARS_152_02-------------------------#'
SET @@global.sql_mode = NULL;
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'NULL'
SET @@global.sql_mode = '';
SELECT @@global.sql_mode;
@@global.sql_mode

SET @@global.sql_mode = ' ';
SELECT @@global.sql_mode;
@@global.sql_mode

SET @@session.sql_mode = NULL;
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'NULL'
SET @@session.sql_mode = '';
SELECT @@session.sql_mode;
@@session.sql_mode

SET @@session.sql_mode = ' ';
SELECT @@session.sql_mode;
@@session.sql_mode

'#--------------------FN_DYNVARS_152_03------------------------#'
SET @@global.sql_mode = ANSI;
SELECT @@global.sql_mode;
@@global.sql_mode
REAL_AS_FLOAT,PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE,ONLY_FULL_GROUP_BY,ANSI
SET @@global.sql_mode = STRICT_TRANS_TABLES;
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@global.sql_mode;
@@global.sql_mode
STRICT_TRANS_TABLES
SET @@global.sql_mode = TRADITIONAL;
SELECT @@global.sql_mode;
@@global.sql_mode
STRICT_TRANS_TABLES,STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,TRADITIONAL,NO_ENGINE_SUBSTITUTION
SET @@global.sql_mode = ALLOW_INVALID_DATES;
SELECT @@global.sql_mode;
@@global.sql_mode
ALLOW_INVALID_DATES
SET @@global.sql_mode = ANSI_QUOTES;
SELECT @@global.sql_mode;
@@global.sql_mode
ANSI_QUOTES
SET @@global.sql_mode = ERROR_FOR_DIVISION_BY_ZERO;
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@global.sql_mode;
@@global.sql_mode
ERROR_FOR_DIVISION_BY_ZERO
SET @@global.sql_mode = HIGH_NOT_PRECEDENCE;
SELECT @@global.sql_mode;
@@global.sql_mode
HIGH_NOT_PRECEDENCE
SET @@global.sql_mode = IGNORE_SPACE;
SELECT @@global.sql_mode;
@@global.sql_mode
IGNORE_SPACE
SET @@global.sql_mode = NO_AUTO_VALUE_ON_ZERO;
SELECT @@global.sql_mode;
@@global.sql_mode
NO_AUTO_VALUE_ON_ZERO
SET @@global.sql_mode = NO_BACKSLASH_ESCAPES;
SELECT @@global.sql_mode;
@@global.sql_mode
NO_BACKSLASH_ESCAPES
SET @@global.sql_mode = NO_DIR_IN_CREATE;
SELECT @@global.sql_mode;
@@global.sql_mode
NO_DIR_IN_CREATE
SET @@global.sql_mode = NO_ENGINE_SUBSTITUTION;
SELECT @@global.sql_mode;
@@global.sql_mode
NO_ENGINE_SUBSTITUTION
SET @@global.sql_mode = NO_UNSIGNED_SUBTRACTION;
SELECT @@global.sql_mode;
@@global.sql_mode
NO_UNSIGNED_SUBTRACTION
SET @@global.sql_mode = NO_ZERO_DATE;
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_ZERO_DATE
SET @@global.sql_mode = NO_ZERO_IN_DATE;
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_ZERO_IN_DATE
SET @@global.sql_mode = ONLY_FULL_GROUP_BY;
SELECT @@global.sql_mode;
@@global.sql_mode
ONLY_FULL_GROUP_BY
SET @@global.sql_mode = PIPES_AS_CONCAT;
SELECT @@global.sql_mode;
@@global.sql_mode
PIPES_AS_CONCAT
SET @@global.sql_mode = REAL_AS_FLOAT;
SELECT @@global.sql_mode;
@@global.sql_mode
REAL_AS_FLOAT
SET @@global.sql_mode = STRICT_ALL_TABLES;
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@global.sql_mode;
@@global.sql_mode
STRICT_ALL_TABLES
SET @@global.sql_mode = STRICT_TRANS_TABLES;
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@global.sql_mode;
@@global.sql_mode
STRICT_TRANS_TABLES
SET @@global.sql_mode = TRADITIONAL;
SELECT @@global.sql_mode;
@@global.sql_mode
STRICT_TRANS_TABLES,STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,TRADITIONAL,NO_ENGINE_SUBSTITUTION
SET @@global.sql_mode = OFF;
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'OFF'
SET @@session.sql_mode = ANSI;
SELECT @@session.sql_mode;
@@session.sql_mode
REAL_AS_FLOAT,PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE,ONLY_FULL_GROUP_BY,ANSI
SET @@session.sql_mode = STRICT_TRANS_TABLES;
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@session.sql_mode;
@@session.sql_mode
STRICT_TRANS_TABLES
SET @@session.sql_mode = TRADITIONAL;
SELECT @@session.sql_mode;
@@session.sql_mode
STRICT_TRANS_TABLES,STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,TRADITIONAL,NO_ENGINE_SUBSTITUTION
SET @@session.sql_mode = ALLOW_INVALID_DATES;
SELECT @@session.sql_mode;
@@session.sql_mode
ALLOW_INVALID_DATES
SET @@session.sql_mode = ANSI_QUOTES;
SELECT @@session.sql_mode;
@@session.sql_mode
ANSI_QUOTES
SET @@session.sql_mode = ERROR_FOR_DIVISION_BY_ZERO;
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@session.sql_mode;
@@session.sql_mode
ERROR_FOR_DIVISION_BY_ZERO
SET @@session.sql_mode = HIGH_NOT_PRECEDENCE;
SELECT @@session.sql_mode;
@@session.sql_mode
HIGH_NOT_PRECEDENCE
SET @@session.sql_mode = IGNORE_SPACE;
SELECT @@session.sql_mode;
@@session.sql_mode
IGNORE_SPACE
SET @@session.sql_mode = NO_AUTO_VALUE_ON_ZERO;
SELECT @@session.sql_mode;
@@session.sql_mode
NO_AUTO_VALUE_ON_ZERO
SET @@session.sql_mode = NO_BACKSLASH_ESCAPES;
SELECT @@session.sql_mode;
@@session.sql_mode
NO_BACKSLASH_ESCAPES
SET @@session.sql_mode = NO_DIR_IN_CREATE;
SELECT @@session.sql_mode;
@@session.sql_mode
NO_DIR_IN_CREATE
SET @@session.sql_mode = NO_ENGINE_SUBSTITUTION;
SELECT @@session.sql_mode;
@@session.sql_mode
NO_ENGINE_SUBSTITUTION
SET @@session.sql_mode = NO_UNSIGNED_SUBTRACTION;
SELECT @@session.sql_mode;
@@session.sql_mode
NO_UNSIGNED_SUBTRACTION
SET @@session.sql_mode = NO_ZERO_DATE;
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_ZERO_DATE
SET @@session.sql_mode = NO_ZERO_IN_DATE;
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_ZERO_IN_DATE
SET @@session.sql_mode = ONLY_FULL_GROUP_BY;
SELECT @@session.sql_mode;
@@session.sql_mode
ONLY_FULL_GROUP_BY
SET @@session.sql_mode = PIPES_AS_CONCAT;
SELECT @@session.sql_mode;
@@session.sql_mode
PIPES_AS_CONCAT
SET @@session.sql_mode = REAL_AS_FLOAT;
SELECT @@session.sql_mode;
@@session.sql_mode
REAL_AS_FLOAT
SET @@session.sql_mode = STRICT_ALL_TABLES;
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@session.sql_mode;
@@session.sql_mode
STRICT_ALL_TABLES
SET @@session.sql_mode = STRICT_TRANS_TABLES;
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@session.sql_mode;
@@session.sql_mode
STRICT_TRANS_TABLES
SET @@session.sql_mode = TRADITIONAL;
SELECT @@session.sql_mode;
@@session.sql_mode
STRICT_TRANS_TABLES,STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,TRADITIONAL,NO_ENGINE_SUBSTITUTION
SET @@session.sql_mode = OFF;
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'OFF'
SET @@global.sql_mode = '?';
ERROR 42000: Variable 'sql_mode' can't be set to the value of '?'
SELECT @@global.sql_mode;
@@global.sql_mode
STRICT_TRANS_TABLES,STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,TRADITIONAL,NO_ENGINE_SUBSTITUTION
'#--------------------FN_DYNVARS_152_04-------------------------#'
SET @@global.sql_mode = -1;
ERROR 42000: Variable 'sql_mode' can't be set to the value of '-1'
SET @@global.sql_mode = ASCII;
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'ASCII'
SET @@global.sql_mode = NON_TRADITIONAL;
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'NON_TRADITIONAL'
SET @@global.sql_mode = 'OF';
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'OF'
SET @@global.sql_mode = NONE;
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'NONE'
SET @@session.sql_mode = -1;
ERROR 42000: Variable 'sql_mode' can't be set to the value of '-1'
SET @@session.sql_mode = ANSI_SINGLE_QUOTES;
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'ANSI_SINGLE_QUOTES'
SET @@session.sql_mode = 'ON';
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'ON'
SET @@session.sql_mode = 'OF';
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'OF'
SET @@session.sql_mode = DISABLE;
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'DISABLE'
'#-------------------FN_DYNVARS_152_05----------------------------#'
SELECT @@session.sql_mode = VARIABLE_VALUE 
FROM performance_schema.session_variables 
WHERE VARIABLE_NAME='sql_mode';
@@session.sql_mode = VARIABLE_VALUE
1
'#----------------------FN_DYNVARS_152_06------------------------#'
SELECT @@global.sql_mode = VARIABLE_VALUE 
FROM performance_schema.global_variables 
WHERE VARIABLE_NAME='sql_mode';
@@global.sql_mode = VARIABLE_VALUE
1
'#---------------------FN_DYNVARS_152_07-------------------------#'
SET @@global.sql_mode = 0;
SELECT @@global.sql_mode;
@@global.sql_mode

SET @@global.sql_mode = 1;
SELECT @@global.sql_mode;
@@global.sql_mode
REAL_AS_FLOAT
SET @@global.sql_mode = 2;
SELECT @@global.sql_mode;
@@global.sql_mode
PIPES_AS_CONCAT
SET @@global.sql_mode = 3;
SELECT @@global.sql_mode;
@@global.sql_mode
REAL_AS_FLOAT,PIPES_AS_CONCAT
SET @@global.sql_mode = 8589934592;
ERROR 42000: Variable 'sql_mode' can't be set to the value of '8589934592'
SET @@global.sql_mode = 0.4;
ERROR 42000: Incorrect argument type to variable 'sql_mode'
'#---------------------FN_DYNVARS_152_08----------------------#'
SET @@global.sql_mode = TRUE;
SELECT @@global.sql_mode;
@@global.sql_mode
REAL_AS_FLOAT
SET @@global.sql_mode = FALSE;
SELECT @@global.sql_mode;
@@global.sql_mode

'#---------------------FN_DYNVARS_152_09----------------------#'
SET sql_mode = 'ANSI';
SET session.sql_mode = 'ANSI';
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'session.sql_mode = 'ANSI'' at line 1
SET global.sql_mode = 'ANSI';
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'global.sql_mode = 'ANSI'' at line 1
SET session sql_mode = 1;
SELECT @@sql_mode;
@@sql_mode
REAL_AS_FLOAT
SET global sql_mode = 0;
SELECT @@global.sql_mode;
@@global.sql_mode

'#---------------------FN_DYNVARS_152_10----------------------#'
SET @@session.sql_mode = 'TRADITIONAL,ALLOW_INVALID_DATES,ANSI_QUOTES';
SELECT @@session.sql_mode;
@@session.sql_mode
ANSI_QUOTES,STRICT_TRANS_TABLES,STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ALLOW_INVALID_DATES,ERROR_FOR_DIVISION_BY_ZERO,TRADITIONAL,NO_ENGINE_SUBSTITUTION
SET @@global.sql_mode = 'ONLY_FULL_GROUP_BY,PIPES_AS_CONCAT,REAL_AS_FLOAT';
SELECT @@global.sql_mode;
@@global.sql_mode
REAL_AS_FLOAT,PIPES_AS_CONCAT,ONLY_FULL_GROUP_BY
SET @@session.sql_mode = 'ERROR_FOR_DIVISION_BY_ZERO,FOOBAR,IGNORE_SPACE';
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'FOOBAR'
SET @@sql_mode=',';
SELECT @@sql_mode;
@@sql_mode

SET @@sql_mode=',,,,ANSI_QUOTES,,,';
SELECT @@sql_mode;
@@sql_mode
ANSI_QUOTES
SET @@sql_mode=',,,,FOOBAR,,,,,';
ERROR 42000: Variable 'sql_mode' can't be set to the value of 'FOOBAR'
SELECT @@sql_mode;
@@sql_mode
ANSI_QUOTES
'#---------------------FN_DYNVARS_152_11----------------------#'
SET @@session.sql_mode= cast(pow(2,0) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
REAL_AS_FLOAT
SET @@global.sql_mode= cast(pow(2,0) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
REAL_AS_FLOAT
SET @@session.sql_mode= cast(pow(2,1) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
PIPES_AS_CONCAT
SET @@global.sql_mode= cast(pow(2,1) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
PIPES_AS_CONCAT
SET @@session.sql_mode= cast(pow(2,2) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
ANSI_QUOTES
SET @@global.sql_mode= cast(pow(2,2) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
ANSI_QUOTES
SET @@session.sql_mode= cast(pow(2,3) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
IGNORE_SPACE
SET @@global.sql_mode= cast(pow(2,3) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
IGNORE_SPACE
SET @@session.sql_mode= cast(pow(2,4) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
NOT_USED
SET @@global.sql_mode= cast(pow(2,4) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
NOT_USED
SET @@session.sql_mode= cast(pow(2,5) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
ONLY_FULL_GROUP_BY
SET @@global.sql_mode= cast(pow(2,5) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
ONLY_FULL_GROUP_BY
SET @@session.sql_mode= cast(pow(2,6) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
NO_UNSIGNED_SUBTRACTION
SET @@global.sql_mode= cast(pow(2,6) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
NO_UNSIGNED_SUBTRACTION
SET @@session.sql_mode= cast(pow(2,7) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
NO_DIR_IN_CREATE
SET @@global.sql_mode= cast(pow(2,7) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
NO_DIR_IN_CREATE
SET @@session.sql_mode= cast(pow(2,8) as unsigned integer);
ERROR HY000: sql_mode=0x00000100 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_DIR_IN_CREATE
SET @@global.sql_mode= cast(pow(2,8) as unsigned integer);
ERROR HY000: sql_mode=0x00000100 is not supported.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_DIR_IN_CREATE
SET @@session.sql_mode= cast(pow(2,9) as unsigned integer);
ERROR HY000: sql_mode=0x00000200 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_DIR_IN_CREATE
SET @@global.sql_mode= cast(pow(2,9) as unsigned integer);
ERROR HY000: sql_mode=0x00000200 is not supported.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_DIR_IN_CREATE
SET @@session.sql_mode= cast(pow(2,10) as unsigned integer);
ERROR HY000: sql_mode=0x00000400 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_DIR_IN_CREATE
SET @@global.sql_mode= cast(pow(2,10) as unsigned integer);
ERROR HY000: sql_mode=0x00000400 is not supported.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_DIR_IN_CREATE
SET @@session.sql_mode= cast(pow(2,11) as unsigned integer);
ERROR HY000: sql_mode=0x00000800 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_DIR_IN_CREATE
SET @@global.sql_mode= cast(pow(2,11) as unsigned integer);
ERROR HY000: sql_mode=0x00000800 is not supported.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_DIR_IN_CREATE
SET @@session.sql_mode= cast(pow(2,12) as unsigned integer);
ERROR HY000: sql_mode=0x00001000 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_DIR_IN_CREATE
SET @@global.sql_mode= cast(pow(2,12) as unsigned integer);
ERROR HY000: sql_mode=0x00001000 is not supported.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_DIR_IN_CREATE
SET @@session.sql_mode= cast(pow(2,13) as unsigned integer);
ERROR HY000: sql_mode=0x00002000 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_DIR_IN_CREATE
SET @@global.sql_mode= cast(pow(2,13) as unsigned integer);
ERROR HY000: sql_mode=0x00002000 is not supported.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_DIR_IN_CREATE
SET @@session.sql_mode= cast(pow(2,14) as unsigned integer);
ERROR HY000: sql_mode=0x00004000 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_DIR_IN_CREATE
SET @@global.sql_mode= cast(pow(2,14) as unsigned integer);
ERROR HY000: sql_mode=0x00004000 is not supported.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_DIR_IN_CREATE
SET @@session.sql_mode= cast(pow(2,15) as unsigned integer);
ERROR HY000: sql_mode=0x00008000 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_DIR_IN_CREATE
SET @@global.sql_mode= cast(pow(2,15) as unsigned integer);
ERROR HY000: sql_mode=0x00008000 is not supported.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_DIR_IN_CREATE
SET @@session.sql_mode= cast(pow(2,16) as unsigned integer);
ERROR HY000: sql_mode=0x00010000 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_DIR_IN_CREATE
SET @@global.sql_mode= cast(pow(2,16) as unsigned integer);
ERROR HY000: sql_mode=0x00010000 is not supported.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_DIR_IN_CREATE
SET @@session.sql_mode= cast(pow(2,17) as unsigned integer);
ERROR HY000: sql_mode=0x00020000 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_DIR_IN_CREATE
SET @@global.sql_mode= cast(pow(2,17) as unsigned integer);
ERROR HY000: sql_mode=0x00020000 is not supported.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_DIR_IN_CREATE
SET @@session.sql_mode= cast(pow(2,18) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
REAL_AS_FLOAT,PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE,ONLY_FULL_GROUP_BY,ANSI
SET @@global.sql_mode= cast(pow(2,18) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
REAL_AS_FLOAT,PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE,ONLY_FULL_GROUP_BY,ANSI
SET @@session.sql_mode= cast(pow(2,19) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
NO_AUTO_VALUE_ON_ZERO
SET @@global.sql_mode= cast(pow(2,19) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
NO_AUTO_VALUE_ON_ZERO
SET @@session.sql_mode= cast(pow(2,20) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
NO_BACKSLASH_ESCAPES
SET @@global.sql_mode= cast(pow(2,20) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
NO_BACKSLASH_ESCAPES
SET @@session.sql_mode= cast(pow(2,21) as unsigned integer);
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@session.sql_mode;
@@session.sql_mode
STRICT_TRANS_TABLES
SET @@global.sql_mode= cast(pow(2,21) as unsigned integer);
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@global.sql_mode;
@@global.sql_mode
STRICT_TRANS_TABLES
SET @@session.sql_mode= cast(pow(2,22) as unsigned integer);
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@session.sql_mode;
@@session.sql_mode
STRICT_ALL_TABLES
SET @@global.sql_mode= cast(pow(2,22) as unsigned integer);
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@global.sql_mode;
@@global.sql_mode
STRICT_ALL_TABLES
SET @@session.sql_mode= cast(pow(2,23) as unsigned integer);
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_ZERO_IN_DATE
SET @@global.sql_mode= cast(pow(2,23) as unsigned integer);
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_ZERO_IN_DATE
SET @@session.sql_mode= cast(pow(2,24) as unsigned integer);
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@session.sql_mode;
@@session.sql_mode
NO_ZERO_DATE
SET @@global.sql_mode= cast(pow(2,24) as unsigned integer);
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@global.sql_mode;
@@global.sql_mode
NO_ZERO_DATE
SET @@session.sql_mode= cast(pow(2,25) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
ALLOW_INVALID_DATES
SET @@global.sql_mode= cast(pow(2,25) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
ALLOW_INVALID_DATES
SET @@session.sql_mode= cast(pow(2,26) as unsigned integer);
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@session.sql_mode;
@@session.sql_mode
ERROR_FOR_DIVISION_BY_ZERO
SET @@global.sql_mode= cast(pow(2,26) as unsigned integer);
Warnings:
Warning	3135	'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
SELECT @@global.sql_mode;
@@global.sql_mode
ERROR_FOR_DIVISION_BY_ZERO
SET @@session.sql_mode= cast(pow(2,27) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
STRICT_TRANS_TABLES,STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,TRADITIONAL,NO_ENGINE_SUBSTITUTION
SET @@global.sql_mode= cast(pow(2,27) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
STRICT_TRANS_TABLES,STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,TRADITIONAL,NO_ENGINE_SUBSTITUTION
SET @@session.sql_mode= cast(pow(2,28) as unsigned integer);
ERROR HY000: sql_mode=0x10000000 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode
STRICT_TRANS_TABLES,STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,TRADITIONAL,NO_ENGINE_SUBSTITUTION
SET @@global.sql_mode= cast(pow(2,28) as unsigned integer);
ERROR HY000: sql_mode=0x10000000 is not supported.
SELECT @@global.sql_mode;
@@global.sql_mode
STRICT_TRANS_TABLES,STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,TRADITIONAL,NO_ENGINE_SUBSTITUTION
SET @@session.sql_mode= cast(pow(2,29) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
HIGH_NOT_PRECEDENCE
SET @@global.sql_mode= cast(pow(2,29) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
HIGH_NOT_PRECEDENCE
SET @@session.sql_mode= cast(pow(2,30) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
NO_ENGINE_SUBSTITUTION
SET @@global.sql_mode= cast(pow(2,30) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
NO_ENGINE_SUBSTITUTION
SET @@session.sql_mode= cast(pow(2,31) as unsigned integer);
Warnings:
Warning	3090	Changing sql mode 'PAD_CHAR_TO_FULL_LENGTH' is deprecated. It will be removed in a future release.
SELECT @@session.sql_mode;
@@session.sql_mode
PAD_CHAR_TO_FULL_LENGTH
SET @@global.sql_mode= cast(pow(2,31) as unsigned integer);
Warnings:
Warning	3090	Changing sql mode 'PAD_CHAR_TO_FULL_LENGTH' is deprecated. It will be removed in a future release.
SELECT @@global.sql_mode;
@@global.sql_mode
PAD_CHAR_TO_FULL_LENGTH
SET @@session.sql_mode= cast(pow(2,32) as unsigned integer);
SELECT @@session.sql_mode;
@@session.sql_mode
TIME_TRUNCATE_FRACTIONAL
SET @@global.sql_mode= cast(pow(2,32) as unsigned integer);
SELECT @@global.sql_mode;
@@global.sql_mode
TIME_TRUNCATE_FRACTIONAL
#
# Bug#27828236: ERROR 1231 WHEN 8.0 CLIENT TRIES TO READ THE OUTPUT OF
#               MYSQLBINLOG 5.7
#
SET @@session.sql_mode = 0;
CREATE TABLE t1 (i INT);
CREATE TRIGGER trg1 BEFORE INSERT ON t1 FOR EACH ROW SET @@session.sql_mode=1436549152;
CREATE PROCEDURE p1() SET @@session.sql_mode=1436549152;
# pseudo_replica_mode is "off", assignment should fail as before:
SET @@session.sql_mode=1436549152;
ERROR HY000: sql_mode=0x10000000 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode

INSERT INTO t1 VALUES (1);
ERROR HY000: sql_mode=0x10000000 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode

CALL p1;
ERROR HY000: sql_mode=0x10000000 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode

#
SET @session_pseudo_replica_mode = @@session.pseudo_replica_mode;
SET @@session.pseudo_replica_mode = TRUE;
# pseudo_replica_mode is "on", assignment should succeed:
SET @@session.sql_mode=1436549152;
Warnings:
Warning	13249	sql_mode=0x10000000 has been removed and will be ignored
SELECT @@session.sql_mode;
@@session.sql_mode
ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
# ... but the assignment in SBR-invoked triggers should continue to fail:
SET @@session.sql_mode = 0;
INSERT INTO t1 VALUES (1);
ERROR HY000: sql_mode=0x10000000 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode

# ... and in procedure calls:
CALL p1;
ERROR HY000: sql_mode=0x10000000 is not supported.
SELECT @@session.sql_mode;
@@session.sql_mode

DROP PROCEDURE p1;
DROP TABLE t1;
SET @@session.pseudo_replica_mode = @session_pseudo_replica_mode;
Warnings:
Warning	1231	Replica applier execution mode not active, statement ineffective.
SET @@session.sql_mode = DEFAULT;
SET @@global.sql_mode = @global_start_value;
SELECT @@global.sql_mode;
@@global.sql_mode
ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
SET @@session.sql_mode = @session_start_value;
SELECT @@session.sql_mode;
@@session.sql_mode
ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
