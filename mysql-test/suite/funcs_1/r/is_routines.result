SHOW TABLES FROM information_schema LIKE 'ROUTINES';
Tables_in_information_schema (ROUTINES)
ROUTINES
#######################################################################
# Testcase 3.2.1.1: INFORMATION_SCHEMA tables can be queried via SELECT
#######################################################################
DROP VIEW      IF EXISTS test.v1;
DROP PROCEDURE IF EXISTS test.p1;
DROP FUNCTION  IF EXISTS test.f1;
CREATE VIEW test.v1 AS     SELECT * FROM information_schema.ROUTINES;
CREATE PROCEDURE test.p1() SELECT * FROM information_schema.ROUTINES;
CREATE FUNCTION test.f1() returns BIGINT
BEGIN
DECLARE counter BIGINT DEFAULT NULL;
SELECT COUNT(*) INTO counter FROM information_schema.ROUTINES;
RETURN counter;
END//
# Attention: The printing of the next result sets is disabled.
SELECT * FROM information_schema.ROUTINES;
SELECT * FROM test.v1;
CALL test.p1;
SELECT test.f1();
DROP VIEW test.v1;
DROP PROCEDURE test.p1;
DROP FUNCTION test.f1;
#########################################################################
# Testcase 3.2.8.1: INFORMATION_SCHEMA.ROUTINES layout
#########################################################################
DESCRIBE          information_schema.ROUTINES;
Field	Type	Null	Key	Default	Extra
SPECIFIC_NAME	varchar(64)	NO		NULL	
ROUTINE_CATALOG	varchar(64)	NO		NULL	
ROUTINE_SCHEMA	varchar(64)	NO		NULL	
ROUTINE_NAME	varchar(64)	NO		NULL	
ROUTINE_TYPE	enum('FUNCTION','PROCEDURE','LIBRARY')	NO		NULL	
DATA_TYPE	longtext	YES		NULL	
CHARACTER_MAXIMUM_LENGTH	bigint	YES		NULL	
CHARACTER_OCTET_LENGTH	bigint	YES		NULL	
NUMERIC_PRECISION	int unsigned	YES		NULL	
NUMERIC_SCALE	int unsigned	YES		NULL	
DATETIME_PRECISION	int unsigned	YES		NULL	
CHARACTER_SET_NAME	varchar(64)	YES		NULL	
COLLATION_NAME	varchar(64)	YES		NULL	
DTD_IDENTIFIER	longtext	YES		NULL	
ROUTINE_BODY	varchar(8)	NO			
ROUTINE_DEFINITION	longtext	YES		NULL	
EXTERNAL_NAME	varbinary(0)	YES		NULL	
EXTERNAL_LANGUAGE	varchar(64)	NO		SQL	
PARAMETER_STYLE	varchar(3)	NO			
IS_DETERMINISTIC	varchar(3)	NO			
SQL_DATA_ACCESS	enum('CONTAINS SQL','NO SQL','READS SQL DATA','MODIFIES SQL DATA')	NO		NULL	
SQL_PATH	varbinary(0)	YES		NULL	
SECURITY_TYPE	enum('DEFAULT','INVOKER','DEFINER')	NO		NULL	
CREATED	timestamp	NO		NULL	
LAST_ALTERED	timestamp	NO		NULL	
SQL_MODE	set('REAL_AS_FLOAT','PIPES_AS_CONCAT','ANSI_QUOTES','IGNORE_SPACE','NOT_USED','ONLY_FULL_GROUP_BY','NO_UNSIGNED_SUBTRACTION','NO_DIR_IN_CREATE','NOT_USED_9','NOT_USED_10','NOT_USED_11','NOT_USED_12','NOT_USED_13','NOT_USED_14','NOT_USED_15','NOT_USED_16','NOT_USED_17','NOT_USED_18','ANSI','NO_AUTO_VALUE_ON_ZERO','NO_BACKSLASH_ESCAPES','STRICT_TRANS_TABLES','STRICT_ALL_TABLES','NO_ZERO_IN_DATE','NO_ZERO_DATE','ALLOW_INVALID_DATES','ERROR_FOR_DIVISION_BY_ZERO','TRADITIONAL','NOT_USED_29','HIGH_NOT_PRECEDENCE','NO_ENGINE_SUBSTITUTION','PAD_CHAR_TO_FULL_LENGTH','TIME_TRUNCATE_FRACTIONAL')	NO		NULL	
ROUTINE_COMMENT	text	NO		NULL	
DEFINER	varchar(288)	NO		NULL	
CHARACTER_SET_CLIENT	varchar(64)	NO		NULL	
COLLATION_CONNECTION	varchar(64)	NO		NULL	
DATABASE_COLLATION	varchar(64)	NO		NULL	
SHOW CREATE TABLE information_schema.ROUTINES;
View	Create View	character_set_client	collation_connection
ROUTINES	CREATE ALGORITHM=UNDEFINED DEFINER=`mysql.infoschema`@`localhost` SQL SECURITY DEFINER VIEW `information_schema`.`ROUTINES` AS select `rtn`.`name` AS `SPECIFIC_NAME`,`cat`.`name` AS `ROUTINE_CATALOG`,`sch`.`name` AS `ROUTINE_SCHEMA`,`rtn`.`name` AS `ROUTINE_NAME`,`rtn`.`type` AS `ROUTINE_TYPE`,if((`rtn`.`type` = 'PROCEDURE'),'',substring_index(substring_index(`rtn`.`result_data_type_utf8`,'(',1),' ',1)) AS `DATA_TYPE`,internal_dd_char_length(`rtn`.`result_data_type`,`rtn`.`result_char_length`,`coll_result`.`name`,0) AS `CHARACTER_MAXIMUM_LENGTH`,internal_dd_char_length(`rtn`.`result_data_type`,`rtn`.`result_char_length`,`coll_result`.`name`,1) AS `CHARACTER_OCTET_LENGTH`,`rtn`.`result_numeric_precision` AS `NUMERIC_PRECISION`,`rtn`.`result_numeric_scale` AS `NUMERIC_SCALE`,`rtn`.`result_datetime_precision` AS `DATETIME_PRECISION`,(case `rtn`.`result_data_type` when 'MYSQL_TYPE_STRING' then if((`cs_result`.`name` = 'binary'),NULL,`cs_result`.`name`) when 'MYSQL_TYPE_VAR_STRING' then if((`cs_result`.`name` = 'binary'),NULL,`cs_result`.`name`) when 'MYSQL_TYPE_VARCHAR' then if((`cs_result`.`name` = 'binary'),NULL,`cs_result`.`name`) when 'MYSQL_TYPE_TINY_BLOB' then if((`cs_result`.`name` = 'binary'),NULL,`cs_result`.`name`) when 'MYSQL_TYPE_MEDIUM_BLOB' then if((`cs_result`.`name` = 'binary'),NULL,`cs_result`.`name`) when 'MYSQL_TYPE_BLOB' then if((`cs_result`.`name` = 'binary'),NULL,`cs_result`.`name`) when 'MYSQL_TYPE_VECTOR' then if((`cs_result`.`name` = 'binary'),NULL,`cs_result`.`name`) when 'MYSQL_TYPE_LONG_BLOB' then if((`cs_result`.`name` = 'binary'),NULL,`cs_result`.`name`) when 'MYSQL_TYPE_ENUM' then if((`cs_result`.`name` = 'binary'),NULL,`cs_result`.`name`) when 'MYSQL_TYPE_SET' then if((`cs_result`.`name` = 'binary'),NULL,`cs_result`.`name`) else NULL end) AS `CHARACTER_SET_NAME`,(case `rtn`.`result_data_type` when 'MYSQL_TYPE_STRING' then if((`cs_result`.`name` = 'binary'),NULL,`coll_result`.`name`) when 'MYSQL_TYPE_VAR_STRING' then if((`cs_result`.`name` = 'binary'),NULL,`coll_result`.`name`) when 'MYSQL_TYPE_VARCHAR' then if((`cs_result`.`name` = 'binary'),NULL,`coll_result`.`name`) when 'MYSQL_TYPE_TINY_BLOB' then if((`cs_result`.`name` = 'binary'),NULL,`coll_result`.`name`) when 'MYSQL_TYPE_MEDIUM_BLOB' then if((`cs_result`.`name` = 'binary'),NULL,`coll_result`.`name`) when 'MYSQL_TYPE_BLOB' then if((`cs_result`.`name` = 'binary'),NULL,`coll_result`.`name`) when 'MYSQL_TYPE_VECTOR' then if((`cs_result`.`name` = 'binary'),NULL,`coll_result`.`name`) when 'MYSQL_TYPE_LONG_BLOB' then if((`cs_result`.`name` = 'binary'),NULL,`coll_result`.`name`) when 'MYSQL_TYPE_ENUM' then if((`cs_result`.`name` = 'binary'),NULL,`coll_result`.`name`) when 'MYSQL_TYPE_SET' then if((`cs_result`.`name` = 'binary'),NULL,`coll_result`.`name`) else NULL end) AS `COLLATION_NAME`,if((`rtn`.`type` = 'PROCEDURE'),NULL,`rtn`.`result_data_type_utf8`) AS `DTD_IDENTIFIER`,if((`rtn`.`external_language` = 'SQL'),'SQL','EXTERNAL') AS `ROUTINE_BODY`,if(can_access_routine(`sch`.`name`,`rtn`.`name`,`rtn`.`type`,`rtn`.`definer`,true),`rtn`.`definition_utf8`,NULL) AS `ROUTINE_DEFINITION`,NULL AS `EXTERNAL_NAME`,`rtn`.`external_language` AS `EXTERNAL_LANGUAGE`,'SQL' AS `PARAMETER_STYLE`,if((`rtn`.`is_deterministic` = 0),'NO','YES') AS `IS_DETERMINISTIC`,`rtn`.`sql_data_access` AS `SQL_DATA_ACCESS`,NULL AS `SQL_PATH`,`rtn`.`security_type` AS `SECURITY_TYPE`,`rtn`.`created` AS `CREATED`,`rtn`.`last_altered` AS `LAST_ALTERED`,`rtn`.`sql_mode` AS `SQL_MODE`,`rtn`.`comment` AS `ROUTINE_COMMENT`,`rtn`.`definer` AS `DEFINER`,`cs_client`.`name` AS `CHARACTER_SET_CLIENT`,`coll_conn`.`name` AS `COLLATION_CONNECTION`,`coll_db`.`name` AS `DATABASE_COLLATION` from ((((((((`mysql`.`routines` `rtn` join `mysql`.`schemata` `sch` on((`rtn`.`schema_id` = `sch`.`id`))) join `mysql`.`catalogs` `cat` on((`cat`.`id` = `sch`.`catalog_id`))) join `mysql`.`collations` `coll_client` on((`coll_client`.`id` = `rtn`.`client_collation_id`))) join `mysql`.`character_sets` `cs_client` on((`cs_client`.`id` = `coll_client`.`character_set_id`))) join `mysql`.`collations` `coll_conn` on((`coll_conn`.`id` = `rtn`.`connection_collation_id`))) join `mysql`.`collations` `coll_db` on((`coll_db`.`id` = `rtn`.`schema_collation_id`))) left join `mysql`.`collations` `coll_result` on((`coll_result`.`id` = `rtn`.`result_collation_id`))) left join `mysql`.`character_sets` `cs_result` on((`cs_result`.`id` = `coll_result`.`character_set_id`))) where ((0 <> can_access_routine(`sch`.`name`,`rtn`.`name`,`rtn`.`type`,`rtn`.`definer`,false)) and (`rtn`.`type` in ('FUNCTION','PROCEDURE')))	utf8mb3	utf8mb3_general_ci
SHOW COLUMNS FROM information_schema.ROUTINES;
Field	Type	Null	Key	Default	Extra
SPECIFIC_NAME	varchar(64)	NO		NULL	
ROUTINE_CATALOG	varchar(64)	NO		NULL	
ROUTINE_SCHEMA	varchar(64)	NO		NULL	
ROUTINE_NAME	varchar(64)	NO		NULL	
ROUTINE_TYPE	enum('FUNCTION','PROCEDURE','LIBRARY')	NO		NULL	
DATA_TYPE	longtext	YES		NULL	
CHARACTER_MAXIMUM_LENGTH	bigint	YES		NULL	
CHARACTER_OCTET_LENGTH	bigint	YES		NULL	
NUMERIC_PRECISION	int unsigned	YES		NULL	
NUMERIC_SCALE	int unsigned	YES		NULL	
DATETIME_PRECISION	int unsigned	YES		NULL	
CHARACTER_SET_NAME	varchar(64)	YES		NULL	
COLLATION_NAME	varchar(64)	YES		NULL	
DTD_IDENTIFIER	longtext	YES		NULL	
ROUTINE_BODY	varchar(8)	NO			
ROUTINE_DEFINITION	longtext	YES		NULL	
EXTERNAL_NAME	varbinary(0)	YES		NULL	
EXTERNAL_LANGUAGE	varchar(64)	NO		SQL	
PARAMETER_STYLE	varchar(3)	NO			
IS_DETERMINISTIC	varchar(3)	NO			
SQL_DATA_ACCESS	enum('CONTAINS SQL','NO SQL','READS SQL DATA','MODIFIES SQL DATA')	NO		NULL	
SQL_PATH	varbinary(0)	YES		NULL	
SECURITY_TYPE	enum('DEFAULT','INVOKER','DEFINER')	NO		NULL	
CREATED	timestamp	NO		NULL	
LAST_ALTERED	timestamp	NO		NULL	
SQL_MODE	set('REAL_AS_FLOAT','PIPES_AS_CONCAT','ANSI_QUOTES','IGNORE_SPACE','NOT_USED','ONLY_FULL_GROUP_BY','NO_UNSIGNED_SUBTRACTION','NO_DIR_IN_CREATE','NOT_USED_9','NOT_USED_10','NOT_USED_11','NOT_USED_12','NOT_USED_13','NOT_USED_14','NOT_USED_15','NOT_USED_16','NOT_USED_17','NOT_USED_18','ANSI','NO_AUTO_VALUE_ON_ZERO','NO_BACKSLASH_ESCAPES','STRICT_TRANS_TABLES','STRICT_ALL_TABLES','NO_ZERO_IN_DATE','NO_ZERO_DATE','ALLOW_INVALID_DATES','ERROR_FOR_DIVISION_BY_ZERO','TRADITIONAL','NOT_USED_29','HIGH_NOT_PRECEDENCE','NO_ENGINE_SUBSTITUTION','PAD_CHAR_TO_FULL_LENGTH','TIME_TRUNCATE_FRACTIONAL')	NO		NULL	
ROUTINE_COMMENT	text	NO		NULL	
DEFINER	varchar(288)	NO		NULL	
CHARACTER_SET_CLIENT	varchar(64)	NO		NULL	
COLLATION_CONNECTION	varchar(64)	NO		NULL	
DATABASE_COLLATION	varchar(64)	NO		NULL	
USE test;
DROP PROCEDURE IF EXISTS sp_for_routines;
DROP FUNCTION  IF EXISTS function_for_routines;
CREATE PROCEDURE sp_for_routines()      SELECT 'db_datadict';
CREATE FUNCTION function_for_routines() RETURNS INT RETURN 0;
SELECT specific_name,routine_catalog,routine_schema,routine_name,routine_type,
routine_body,external_name,external_language,parameter_style,sql_path
FROM information_schema.routines
WHERE routine_schema = 'test' AND
(routine_catalog   IS NOT NULL OR external_name   IS NOT NULL
OR external_language IS NOT NULL OR sql_path        IS NOT NULL
OR routine_body      <> 'SQL'    OR parameter_style <> 'SQL'
   OR specific_name     <> routine_name);
SPECIFIC_NAME	ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	SQL_PATH
function_for_routines	def	test	function_for_routines	FUNCTION	SQL	NULL	SQL	SQL	NULL
sp_for_routines	def	test	sp_for_routines	PROCEDURE	SQL	NULL	SQL	SQL	NULL
DROP PROCEDURE sp_for_routines;
DROP FUNCTION  function_for_routines;
################################################################################
# Testcase 3.2.8.2 + 3.2.8.3: INFORMATION_SCHEMA.ROUTINES accessible information
################################################################################
DROP DATABASE IF EXISTS db_datadict;
DROP DATABASE IF EXISTS db_datadict_2;
CREATE DATABASE db_datadict charset latin1;
USE db_datadict;
CREATE TABLE res_6_408002_1(f1 CHAR(3), f2 TEXT(25), f3 DATE, f4 INT)
ENGINE = <other_engine_type>;
INSERT INTO res_6_408002_1(f1, f2, f3, f4)
VALUES('abc', 'xyz', '1989-11-09', 0815);
DROP PROCEDURE IF EXISTS sp_6_408002_1;
CREATE PROCEDURE sp_6_408002_1()
BEGIN
SELECT * FROM db_datadict.res_6_408002_1;
END//
CREATE DATABASE db_datadict_2 charset latin1;
USE db_datadict_2;
CREATE TABLE res_6_408002_2(f1 CHAR(3), f2 TEXT(25), f3 DATE, f4 INT)
ENGINE = <other_engine_type>;
INSERT INTO res_6_408002_2(f1, f2, f3, f4)
VALUES('abc', 'xyz', '1990-10-03', 4711);
DROP PROCEDURE IF EXISTS sp_6_408002_2;
CREATE PROCEDURE sp_6_408002_2()
BEGIN
SELECT * FROM db_datadict_2.res_6_408002_2;
END//
DROP   USER 'testuser1'@'localhost';
CREATE USER 'testuser1'@'localhost';
DROP   USER 'testuser2'@'localhost';
CREATE USER 'testuser2'@'localhost';
DROP   USER 'testuser3'@'localhost';
CREATE USER 'testuser3'@'localhost';
GRANT SELECT  ON db_datadict_2.* TO 'testuser1'@'localhost';
GRANT EXECUTE ON db_datadict_2.* TO 'testuser1'@'localhost';
GRANT EXECUTE ON db_datadict.*   TO 'testuser1'@'localhost';
GRANT SELECT  ON db_datadict.*   TO 'testuser2'@'localhost';
GRANT EXECUTE ON PROCEDURE db_datadict_2.sp_6_408002_2
TO 'testuser2'@'localhost';
GRANT EXECUTE ON db_datadict_2.* TO 'testuser2'@'localhost';
FLUSH PRIVILEGES;
Warnings:
Warning	1681	'FLUSH PRIVILEGES' is deprecated and will be removed in a future release.
# Establish connection testuser1 (user=testuser1)
SELECT * FROM information_schema.routines where routine_schema like 'db_datadict%';
SPECIFIC_NAME	ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	CHARACTER_MAXIMUM_LENGTH	CHARACTER_OCTET_LENGTH	NUMERIC_PRECISION	NUMERIC_SCALE	DATETIME_PRECISION	CHARACTER_SET_NAME	COLLATION_NAME	DTD_IDENTIFIER	ROUTINE_BODY	ROUTINE_DEFINITION	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	IS_DETERMINISTIC	SQL_DATA_ACCESS	SQL_PATH	SECURITY_TYPE	CREATED	LAST_ALTERED	SQL_MODE	ROUTINE_COMMENT	DEFINER	CHARACTER_SET_CLIENT	COLLATION_CONNECTION	DATABASE_COLLATION
sp_6_408002_1	def	db_datadict	sp_6_408002_1	PROCEDURE		NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	SQL	NULL	NULL	SQL	SQL	NO	CONTAINS SQL	NULL	DEFINER	YYYY-MM-DD hh:mm:ss	YYYY-MM-DD hh:mm:ss	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION		root@localhost	utf8mb4	utf8mb4_0900_ai_ci	latin1_swedish_ci
sp_6_408002_2	def	db_datadict_2	sp_6_408002_2	PROCEDURE		NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	SQL	NULL	NULL	SQL	SQL	NO	CONTAINS SQL	NULL	DEFINER	YYYY-MM-DD hh:mm:ss	YYYY-MM-DD hh:mm:ss	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION		root@localhost	utf8mb4	utf8mb4_0900_ai_ci	latin1_swedish_ci
# Establish connection testuser2 (user=testuser2)
SELECT * FROM information_schema.routines where routine_schema like 'db_datadict%';
SPECIFIC_NAME	ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	CHARACTER_MAXIMUM_LENGTH	CHARACTER_OCTET_LENGTH	NUMERIC_PRECISION	NUMERIC_SCALE	DATETIME_PRECISION	CHARACTER_SET_NAME	COLLATION_NAME	DTD_IDENTIFIER	ROUTINE_BODY	ROUTINE_DEFINITION	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	IS_DETERMINISTIC	SQL_DATA_ACCESS	SQL_PATH	SECURITY_TYPE	CREATED	LAST_ALTERED	SQL_MODE	ROUTINE_COMMENT	DEFINER	CHARACTER_SET_CLIENT	COLLATION_CONNECTION	DATABASE_COLLATION
sp_6_408002_2	def	db_datadict_2	sp_6_408002_2	PROCEDURE		NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	SQL	NULL	NULL	SQL	SQL	NO	CONTAINS SQL	NULL	DEFINER	YYYY-MM-DD hh:mm:ss	YYYY-MM-DD hh:mm:ss	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION		root@localhost	utf8mb4	utf8mb4_0900_ai_ci	latin1_swedish_ci
# Establish connection testuser3 (user=testuser3)
SELECT * FROM information_schema.routines where routine_schema like 'db_datadict%';
SPECIFIC_NAME	ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	CHARACTER_MAXIMUM_LENGTH	CHARACTER_OCTET_LENGTH	NUMERIC_PRECISION	NUMERIC_SCALE	DATETIME_PRECISION	CHARACTER_SET_NAME	COLLATION_NAME	DTD_IDENTIFIER	ROUTINE_BODY	ROUTINE_DEFINITION	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	IS_DETERMINISTIC	SQL_DATA_ACCESS	SQL_PATH	SECURITY_TYPE	CREATED	LAST_ALTERED	SQL_MODE	ROUTINE_COMMENT	DEFINER	CHARACTER_SET_CLIENT	COLLATION_CONNECTION	DATABASE_COLLATION
# Switch to connection default and close connections testuser1,testuser2,testuser3
DROP USER 'testuser1'@'localhost';
DROP USER 'testuser2'@'localhost';
DROP USER 'testuser3'@'localhost';
USE test;
DROP DATABASE db_datadict;
DROP DATABASE db_datadict_2;
#########################################################################
# ********+********+********: INFORMATION_SCHEMA.ROUTINES modifications
#########################################################################
DROP DATABASE IF EXISTS db_datadict;
CREATE DATABASE db_datadict charset latin1;
SELECT * FROM information_schema.routines WHERE routine_schema = 'db_datadict';
SPECIFIC_NAME	ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	CHARACTER_MAXIMUM_LENGTH	CHARACTER_OCTET_LENGTH	NUMERIC_PRECISION	NUMERIC_SCALE	DATETIME_PRECISION	CHARACTER_SET_NAME	COLLATION_NAME	DTD_IDENTIFIER	ROUTINE_BODY	ROUTINE_DEFINITION	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	IS_DETERMINISTIC	SQL_DATA_ACCESS	SQL_PATH	SECURITY_TYPE	CREATED	LAST_ALTERED	SQL_MODE	ROUTINE_COMMENT	DEFINER	CHARACTER_SET_CLIENT	COLLATION_CONNECTION	DATABASE_COLLATION
USE db_datadict;
CREATE PROCEDURE sp_for_routines()      SELECT 'db_datadict';
CREATE FUNCTION function_for_routines() RETURNS INT RETURN 0;
SELECT * FROM information_schema.routines WHERE routine_schema = 'db_datadict'
ORDER BY routine_name;
SPECIFIC_NAME	function_for_routines
ROUTINE_CATALOG	def
ROUTINE_SCHEMA	db_datadict
ROUTINE_NAME	function_for_routines
ROUTINE_TYPE	FUNCTION
DATA_TYPE	int
CHARACTER_MAXIMUM_LENGTH	NULL
CHARACTER_OCTET_LENGTH	NULL
NUMERIC_PRECISION	10
NUMERIC_SCALE	0
DATETIME_PRECISION	NULL
CHARACTER_SET_NAME	NULL
COLLATION_NAME	NULL
DTD_IDENTIFIER	int
ROUTINE_BODY	SQL
ROUTINE_DEFINITION	RETURN 0
EXTERNAL_NAME	NULL
EXTERNAL_LANGUAGE	SQL
PARAMETER_STYLE	SQL
IS_DETERMINISTIC	NO
SQL_DATA_ACCESS	CONTAINS SQL
SQL_PATH	NULL
SECURITY_TYPE	DEFINER
CREATED	<created>
LAST_ALTERED	<modified>
SQL_MODE	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
ROUTINE_COMMENT	
DEFINER	root@localhost
CHARACTER_SET_CLIENT	utf8mb4
COLLATION_CONNECTION	utf8mb4_0900_ai_ci
DATABASE_COLLATION	latin1_swedish_ci
SPECIFIC_NAME	sp_for_routines
ROUTINE_CATALOG	def
ROUTINE_SCHEMA	db_datadict
ROUTINE_NAME	sp_for_routines
ROUTINE_TYPE	PROCEDURE
DATA_TYPE	
CHARACTER_MAXIMUM_LENGTH	NULL
CHARACTER_OCTET_LENGTH	NULL
NUMERIC_PRECISION	NULL
NUMERIC_SCALE	NULL
DATETIME_PRECISION	NULL
CHARACTER_SET_NAME	NULL
COLLATION_NAME	NULL
DTD_IDENTIFIER	NULL
ROUTINE_BODY	SQL
ROUTINE_DEFINITION	SELECT 'db_datadict'
EXTERNAL_NAME	NULL
EXTERNAL_LANGUAGE	SQL
PARAMETER_STYLE	SQL
IS_DETERMINISTIC	NO
SQL_DATA_ACCESS	CONTAINS SQL
SQL_PATH	NULL
SECURITY_TYPE	DEFINER
CREATED	<created>
LAST_ALTERED	<modified>
SQL_MODE	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
ROUTINE_COMMENT	
DEFINER	root@localhost
CHARACTER_SET_CLIENT	utf8mb4
COLLATION_CONNECTION	utf8mb4_0900_ai_ci
DATABASE_COLLATION	latin1_swedish_ci
ALTER PROCEDURE sp_for_routines SQL SECURITY INVOKER;
ALTER FUNCTION function_for_routines COMMENT 'updated comments';
SELECT * FROM information_schema.routines WHERE routine_schema = 'db_datadict'
ORDER BY routine_name;
SPECIFIC_NAME	function_for_routines
ROUTINE_CATALOG	def
ROUTINE_SCHEMA	db_datadict
ROUTINE_NAME	function_for_routines
ROUTINE_TYPE	FUNCTION
DATA_TYPE	int
CHARACTER_MAXIMUM_LENGTH	NULL
CHARACTER_OCTET_LENGTH	NULL
NUMERIC_PRECISION	10
NUMERIC_SCALE	0
DATETIME_PRECISION	NULL
CHARACTER_SET_NAME	NULL
COLLATION_NAME	NULL
DTD_IDENTIFIER	int
ROUTINE_BODY	SQL
ROUTINE_DEFINITION	RETURN 0
EXTERNAL_NAME	NULL
EXTERNAL_LANGUAGE	SQL
PARAMETER_STYLE	SQL
IS_DETERMINISTIC	NO
SQL_DATA_ACCESS	CONTAINS SQL
SQL_PATH	NULL
SECURITY_TYPE	DEFINER
CREATED	<created>
LAST_ALTERED	<modified>
SQL_MODE	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
ROUTINE_COMMENT	updated comments
DEFINER	root@localhost
CHARACTER_SET_CLIENT	utf8mb4
COLLATION_CONNECTION	utf8mb4_0900_ai_ci
DATABASE_COLLATION	latin1_swedish_ci
SPECIFIC_NAME	sp_for_routines
ROUTINE_CATALOG	def
ROUTINE_SCHEMA	db_datadict
ROUTINE_NAME	sp_for_routines
ROUTINE_TYPE	PROCEDURE
DATA_TYPE	
CHARACTER_MAXIMUM_LENGTH	NULL
CHARACTER_OCTET_LENGTH	NULL
NUMERIC_PRECISION	NULL
NUMERIC_SCALE	NULL
DATETIME_PRECISION	NULL
CHARACTER_SET_NAME	NULL
COLLATION_NAME	NULL
DTD_IDENTIFIER	NULL
ROUTINE_BODY	SQL
ROUTINE_DEFINITION	SELECT 'db_datadict'
EXTERNAL_NAME	NULL
EXTERNAL_LANGUAGE	SQL
PARAMETER_STYLE	SQL
IS_DETERMINISTIC	NO
SQL_DATA_ACCESS	CONTAINS SQL
SQL_PATH	NULL
SECURITY_TYPE	INVOKER
CREATED	<created>
LAST_ALTERED	<modified>
SQL_MODE	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
ROUTINE_COMMENT	
DEFINER	root@localhost
CHARACTER_SET_CLIENT	utf8mb4
COLLATION_CONNECTION	utf8mb4_0900_ai_ci
DATABASE_COLLATION	latin1_swedish_ci
DROP PROCEDURE sp_for_routines;
DROP FUNCTION function_for_routines;
SELECT * FROM information_schema.routines WHERE routine_schema = 'db_datadict';
SPECIFIC_NAME	ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	CHARACTER_MAXIMUM_LENGTH	CHARACTER_OCTET_LENGTH	NUMERIC_PRECISION	NUMERIC_SCALE	DATETIME_PRECISION	CHARACTER_SET_NAME	COLLATION_NAME	DTD_IDENTIFIER	ROUTINE_BODY	ROUTINE_DEFINITION	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	IS_DETERMINISTIC	SQL_DATA_ACCESS	SQL_PATH	SECURITY_TYPE	CREATED	LAST_ALTERED	SQL_MODE	ROUTINE_COMMENT	DEFINER	CHARACTER_SET_CLIENT	COLLATION_CONNECTION	DATABASE_COLLATION
CREATE PROCEDURE sp_for_routines()      SELECT 'db_datadict';
CREATE FUNCTION function_for_routines() RETURNS INT RETURN 0;
SELECT * FROM information_schema.routines WHERE routine_schema = 'db_datadict'
ORDER BY routine_name;
SPECIFIC_NAME	function_for_routines
ROUTINE_CATALOG	def
ROUTINE_SCHEMA	db_datadict
ROUTINE_NAME	function_for_routines
ROUTINE_TYPE	FUNCTION
DATA_TYPE	int
CHARACTER_MAXIMUM_LENGTH	NULL
CHARACTER_OCTET_LENGTH	NULL
NUMERIC_PRECISION	10
NUMERIC_SCALE	0
DATETIME_PRECISION	NULL
CHARACTER_SET_NAME	NULL
COLLATION_NAME	NULL
DTD_IDENTIFIER	int
ROUTINE_BODY	SQL
ROUTINE_DEFINITION	RETURN 0
EXTERNAL_NAME	NULL
EXTERNAL_LANGUAGE	SQL
PARAMETER_STYLE	SQL
IS_DETERMINISTIC	NO
SQL_DATA_ACCESS	CONTAINS SQL
SQL_PATH	NULL
SECURITY_TYPE	DEFINER
CREATED	<created>
LAST_ALTERED	<modified>
SQL_MODE	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
ROUTINE_COMMENT	
DEFINER	root@localhost
CHARACTER_SET_CLIENT	utf8mb4
COLLATION_CONNECTION	utf8mb4_0900_ai_ci
DATABASE_COLLATION	latin1_swedish_ci
SPECIFIC_NAME	sp_for_routines
ROUTINE_CATALOG	def
ROUTINE_SCHEMA	db_datadict
ROUTINE_NAME	sp_for_routines
ROUTINE_TYPE	PROCEDURE
DATA_TYPE	
CHARACTER_MAXIMUM_LENGTH	NULL
CHARACTER_OCTET_LENGTH	NULL
NUMERIC_PRECISION	NULL
NUMERIC_SCALE	NULL
DATETIME_PRECISION	NULL
CHARACTER_SET_NAME	NULL
COLLATION_NAME	NULL
DTD_IDENTIFIER	NULL
ROUTINE_BODY	SQL
ROUTINE_DEFINITION	SELECT 'db_datadict'
EXTERNAL_NAME	NULL
EXTERNAL_LANGUAGE	SQL
PARAMETER_STYLE	SQL
IS_DETERMINISTIC	NO
SQL_DATA_ACCESS	CONTAINS SQL
SQL_PATH	NULL
SECURITY_TYPE	DEFINER
CREATED	<created>
LAST_ALTERED	<modified>
SQL_MODE	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
ROUTINE_COMMENT	
DEFINER	root@localhost
CHARACTER_SET_CLIENT	utf8mb4
COLLATION_CONNECTION	utf8mb4_0900_ai_ci
DATABASE_COLLATION	latin1_swedish_ci
use test;
DROP DATABASE db_datadict;
SELECT * FROM information_schema.routines WHERE routine_schema = 'db_datadict';
SPECIFIC_NAME	ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	CHARACTER_MAXIMUM_LENGTH	CHARACTER_OCTET_LENGTH	NUMERIC_PRECISION	NUMERIC_SCALE	DATETIME_PRECISION	CHARACTER_SET_NAME	COLLATION_NAME	DTD_IDENTIFIER	ROUTINE_BODY	ROUTINE_DEFINITION	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	IS_DETERMINISTIC	SQL_DATA_ACCESS	SQL_PATH	SECURITY_TYPE	CREATED	LAST_ALTERED	SQL_MODE	ROUTINE_COMMENT	DEFINER	CHARACTER_SET_CLIENT	COLLATION_CONNECTION	DATABASE_COLLATION
#########################################################################
# *******: INFORMATION_SCHEMA.ROUTINES routine body too big for
#          ROUTINE_DEFINITION column
#########################################################################
DROP DATABASE IF EXISTS db_datadict;
CREATE DATABASE db_datadict charset latin1;
USE db_datadict;
CREATE TABLE db_datadict.res_6_408004_1
(f1 LONGTEXT , f2 MEDIUMINT , f3 LONGBLOB , f4 REAL , f5 YEAR)
ENGINE = <other_engine_type>;
INSERT INTO db_datadict.res_6_408004_1
VALUES ('abc', 98765 , 99999999 , 98765, 10);
CREATE TABLE db_datadict.res_6_408004_2
(f1 LONGTEXT , f2 MEDIUMINT , f3 LONGBLOB , f4 REAL , f5 YEAR)
ENGINE = <other_engine_type>;
INSERT INTO db_datadict.res_6_408004_2
VALUES ('abc', 98765 , 99999999 , 98765, 10);
# Checking the max. possible length of (currently) 4 GByte is not
# in this environment here.
CREATE PROCEDURE sp_6_408004 ()
BEGIN
DECLARE done INTEGER DEFAULt 0;
DECLARE variable_number_1 LONGTEXT;
DECLARE variable_number_2 MEDIUMINT;
DECLARE variable_number_3 LONGBLOB;
DECLARE variable_number_4 REAL;
DECLARE variable_number_5 YEAR;
DECLARE cursor_number_1 CURSOR FOR SELECT * FROM res_6_408004_1 LIMIT 0, 10;
DECLARE cursor_number_2 CURSOR FOR SELECT * FROM res_6_408004_1 LIMIT 0, 10;
DECLARE cursor_number_3 CURSOR FOR SELECT * FROM res_6_408004_1 LIMIT 0, 10;
DECLARE cursor_number_4 CURSOR FOR SELECT * FROM res_6_408004_1 LIMIT 0, 10;
DECLARE cursor_number_5 CURSOR FOR SELECT * FROM res_6_408004_1 LIMIT 0, 10;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
BEGIN
OPEN cursor_number_1;
WHILE done <> 1 DO
FETCH cursor_number_1
INTO variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5;
IF done <> 0 THEN
INSERT INTO res_6_408004_2
VALUES (variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5);
END IF;
END WHILE;
BEGIN
BEGIN
SET done = 0;
OPEN cursor_number_2;
WHILE done <> 1 DO
FETCH cursor_number_2
INTO variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5;
IF done <> 0 THEN
INSERT INTO res_6_408004_2
VALUES(variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5);
END IF;
END WHILE;
END;
SET done = 0;
OPEN cursor_number_3;
WHILE done <> 1 DO
FETCH cursor_number_3
INTO variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5;
IF done <> 0 THEN
INSERT INTO res_6_408004_2
VALUES(variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5);
END IF;
END WHILE;
END;
END;
BEGIN
SET done = 0;
OPEN cursor_number_4;
WHILE done <> 1 DO
FETCH cursor_number_4
INTO variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5;
IF done <> 0 THEN
INSERT INTO res_6_408004_2
VALUES (variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5);
END IF;
END WHILE;
END;
BEGIN
SET @a='test row';
SELECT @a;
SELECT @a;
SELECT @a;
END;
BEGIN
SET done = 0;
OPEN cursor_number_5;
WHILE done <> 1 DO
FETCH cursor_number_5
INTO variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5;
IF done <> 0 THEN
INSERT INTO res_6_408004_2
VALUES (variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5);
END IF;
END WHILE;
END;
BEGIN
SET @a='test row';
SELECT @a;
SELECT @a;
SELECT @a;
END;
END//
CALL db_datadict.sp_6_408004 ();
@a
test row
@a
test row
@a
test row
@a
test row
@a
test row
@a
test row
SELECT * FROM db_datadict.res_6_408004_2;
f1	f2	f3	f4	f5
abc	98765	99999999	98765	2010
abc	98765	99999999	98765	2010
abc	98765	99999999	98765	2010
abc	98765	99999999	98765	2010
abc	98765	99999999	98765	2010
abc	98765	99999999	98765	2010
SELECT *, LENGTH(routine_definition) FROM information_schema.routines
WHERE routine_schema = 'db_datadict';
SPECIFIC_NAME	sp_6_408004
ROUTINE_CATALOG	def
ROUTINE_SCHEMA	db_datadict
ROUTINE_NAME	sp_6_408004
ROUTINE_TYPE	PROCEDURE
DATA_TYPE	
CHARACTER_MAXIMUM_LENGTH	NULL
CHARACTER_OCTET_LENGTH	NULL
NUMERIC_PRECISION	NULL
NUMERIC_SCALE	NULL
DATETIME_PRECISION	NULL
CHARACTER_SET_NAME	NULL
COLLATION_NAME	NULL
DTD_IDENTIFIER	NULL
ROUTINE_BODY	SQL
ROUTINE_DEFINITION	BEGIN
DECLARE done INTEGER DEFAULt 0;
DECLARE variable_number_1 LONGTEXT;
DECLARE variable_number_2 MEDIUMINT;
DECLARE variable_number_3 LONGBLOB;
DECLARE variable_number_4 REAL;
DECLARE variable_number_5 YEAR;
DECLARE cursor_number_1 CURSOR FOR SELECT * FROM res_6_408004_1 LIMIT 0, 10;
DECLARE cursor_number_2 CURSOR FOR SELECT * FROM res_6_408004_1 LIMIT 0, 10;
DECLARE cursor_number_3 CURSOR FOR SELECT * FROM res_6_408004_1 LIMIT 0, 10;
DECLARE cursor_number_4 CURSOR FOR SELECT * FROM res_6_408004_1 LIMIT 0, 10;
DECLARE cursor_number_5 CURSOR FOR SELECT * FROM res_6_408004_1 LIMIT 0, 10;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
BEGIN
OPEN cursor_number_1;
WHILE done <> 1 DO
FETCH cursor_number_1
INTO variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5;
IF done <> 0 THEN
INSERT INTO res_6_408004_2
VALUES (variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5);
END IF;
END WHILE;
BEGIN
BEGIN
SET done = 0;
OPEN cursor_number_2;
WHILE done <> 1 DO
FETCH cursor_number_2
INTO variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5;
IF done <> 0 THEN
INSERT INTO res_6_408004_2
VALUES(variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5);
END IF;
END WHILE;
END;
SET done = 0;
OPEN cursor_number_3;
WHILE done <> 1 DO
FETCH cursor_number_3
INTO variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5;
IF done <> 0 THEN
INSERT INTO res_6_408004_2
VALUES(variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5);
END IF;
END WHILE;
END;
END;
BEGIN
SET done = 0;
OPEN cursor_number_4;
WHILE done <> 1 DO
FETCH cursor_number_4
INTO variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5;
IF done <> 0 THEN
INSERT INTO res_6_408004_2
VALUES (variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5);
END IF;
END WHILE;
END;
BEGIN
SET @a='test row';
SELECT @a;
SELECT @a;
SELECT @a;
END;
BEGIN
SET done = 0;
OPEN cursor_number_5;
WHILE done <> 1 DO
FETCH cursor_number_5
INTO variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5;
IF done <> 0 THEN
INSERT INTO res_6_408004_2
VALUES (variable_number_1, variable_number_2, variable_number_3,
variable_number_4, variable_number_5);
END IF;
END WHILE;
END;
BEGIN
SET @a='test row';
SELECT @a;
SELECT @a;
SELECT @a;
END;
END
EXTERNAL_NAME	NULL
EXTERNAL_LANGUAGE	SQL
PARAMETER_STYLE	SQL
IS_DETERMINISTIC	NO
SQL_DATA_ACCESS	CONTAINS SQL
SQL_PATH	NULL
SECURITY_TYPE	DEFINER
CREATED	YYYY-MM-DD hh:mm:ss
LAST_ALTERED	YYYY-MM-DD hh:mm:ss
SQL_MODE	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
ROUTINE_COMMENT	
DEFINER	root@localhost
CHARACTER_SET_CLIENT	utf8mb4
COLLATION_CONNECTION	utf8mb4_0900_ai_ci
DATABASE_COLLATION	latin1_swedish_ci
LENGTH(routine_definition)	2549
DROP DATABASE db_datadict;
########################################################################
# Testcases *******-******* + *******-********: INSERT/UPDATE/DELETE and
#           DDL on INFORMATION_SCHEMA table are not supported
########################################################################
DROP DATABASE IF EXISTS db_datadict;
CREATE DATABASE db_datadict charset latin1;
USE db_datadict;
CREATE PROCEDURE sp_for_routines() SELECT 'db_datadict';
USE test;
INSERT INTO information_schema.routines (routine_name, routine_type )
VALUES ('p2', 'procedure');
ERROR 42000: Access denied for user 'root'@'localhost' to database 'information_schema'
UPDATE information_schema.routines SET routine_name = 'p2'
WHERE routine_body = 'sql';
ERROR 42000: Access denied for user 'root'@'localhost' to database 'information_schema'
DELETE FROM information_schema.routines ;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'information_schema'
TRUNCATE information_schema.routines ;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'information_schema'
CREATE INDEX i7 ON information_schema.routines (routine_name);
ERROR 42000: Access denied for user 'root'@'localhost' to database 'information_schema'
ALTER TABLE information_schema.routines  ADD f1 INT;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'information_schema'
ALTER TABLE information_schema.routines  DISCARD TABLESPACE;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'information_schema'
DROP TABLE information_schema.routines ;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'information_schema'
ALTER TABLE information_schema.routines RENAME db_datadict.routines;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'information_schema'
ALTER TABLE information_schema.routines RENAME information_schema.xroutines;
ERROR 42000: Access denied for user 'root'@'localhost' to database 'information_schema'
DROP DATABASE db_datadict;
