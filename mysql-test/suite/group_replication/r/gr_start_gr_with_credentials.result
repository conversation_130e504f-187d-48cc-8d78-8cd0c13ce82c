include/group_replication.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection server1]

# 1. Bootstrap M1. And create new users.
[connection server1]
include/start_and_bootstrap_group_replication.inc
CREATE TABLE t1 (c1 INT NOT NULL PRIMARY KEY);
SET SQL_LOG_BIN=0;
CREATE USER 'regular_user_p' IDENTIFIED BY 'password';
CREATE USER 'regular_user_wp' IDENTIFIED BY '';
GRANT REPLICATION SLAVE ON *.* TO "regular_user_p";
GRANT REPLICATION SLAVE ON *.* TO "regular_user_wp";
SET SQL_LOG_BIN=1;

# 2. Reset recovery interval to 1 and count to 2 for M2.
[connection server2]
SET @saved_group_replication_recovery_reconnect_interval = @@GLOBAL.group_replication_recovery_reconnect_interval;
SET @saved_gr_recovery_retry_count = @@GLOBAL.group_replication_recovery_retry_count;
SET GLOBAL group_replication_recovery_reconnect_interval= 1;
SET GLOBAL group_replication_recovery_retry_count= 2;
SET GLOBAL group_replication_group_name= "GROUP_REPLICATION_GROUP_NAME";

# 3. Test START GR works without any option.
[connection server1]
INSERT INTO t1 VALUES (1);
[connection server2]
include/start_group_replication.inc
include/stop_group_replication.inc

# 4. Delete recovery channel and test START GR, it should fail.
[connection server1]
INSERT INTO t1 VALUES (2);
[connection server2]
RESET REPLICA ALL FOR CHANNEL 'group_replication_recovery';
START GROUP_REPLICATION;
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc

# 5. Pass USER as root and test START GR, member should come ONLINE.
#    START GR without parameter fails, credentials are removed on STOP.
[connection server1]
INSERT INTO t1 VALUES (3);
[connection server2]
START GROUP_REPLICATION USER='root';
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc
[connection server1]
INSERT INTO t1 VALUES (4);
[connection server2]
START GROUP_REPLICATION;
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc

# 6. Test wrong credentials do not connect with master.
[connection server1]
INSERT INTO t1 VALUES (5);
[connection server2]
START GROUP_REPLICATION USER='regular_user_p',PASSWORD='wrong';
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc
[connection server1]
INSERT INTO t1 VALUES (6);
[connection server2]
START GROUP_REPLICATION USER='regular_user_p';
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc
[connection server1]
INSERT INTO t1 VALUES (7);
[connection server2]
START GROUP_REPLICATION USER='regular_user_wp' ,  PASSWORD='password';
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc

# 7. Test right credentials in START GR connect with master.
[connection server1]
INSERT INTO t1 VALUES (8);
[connection server2]
START GROUP_REPLICATION USER='regular_user_p' , PASSWORD='password';
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc
[connection server1]
INSERT INTO t1 VALUES (9);
[connection server2]
START GROUP_REPLICATION USER='regular_user_p' , PASSWORD='password' , DEFAULT_AUTH='wrong_auth';
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc
[connection server1]
INSERT INTO t1 VALUES (10);
[connection server2]
START GROUP_REPLICATION USER='regular_user_p' , PASSWORD='password' , DEFAULT_AUTH= 'auth_test_plugin';
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc
[connection server1]
INSERT INTO t1 VALUES (11);
[connection server2]
START GROUP_REPLICATION PASSWORD='password' , USER='regular_user_p', DEFAULT_AUTH='auth_test_plugin';
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc
[connection server1]
INSERT INTO t1 VALUES (12);
[connection server2]
START GROUP_REPLICATION USER='regular_user_p' , DEFAULT_AUTH= '' , PASSWORD='password';
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc
[connection server1]
INSERT INTO t1 VALUES (13);
[connection server2]
START GROUP_REPLICATION USER='regular_user_wp';
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc
[connection server1]
INSERT INTO t1 VALUES (14);
[connection server2]
START GROUP_REPLICATION USER='regular_user_wp' , PASSWORD='';
include/gr_wait_for_member_state.inc
include/stop_group_replication.inc

# 8. Cleanup.
CHANGE REPLICATION SOURCE TO SOURCE_USER='root' , SOURCE_PASSWORD='' FOR CHANNEL 'group_replication_recovery';
Warnings:
Note	1759	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	1760	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
SET SESSION sql_log_bin = 0;
call mtr.add_suppression("Replica I/O for channel 'group_replication_recovery': Fatal error: Invalid .* username when attempting to connect to the source server.*");
call mtr.add_suppression("There was an error when connecting to the donor server. Please check that group_replication_recovery channel credentials and all MEMBER_HOST column values of performance_schema.replication_group_members table are correct and DNS resolvable.");
call mtr.add_suppression("For details please check performance_schema.replication_connection_status table and error log messages of Replica I/O for channel group_replication_recovery.");
call mtr.add_suppression("Maximum number of retries when trying to connect to a donor reached. Aborting group replication incremental recovery.");
call mtr.add_suppression("Fatal error during the incremental recovery process of Group Replication. The server will leave the group.");
call mtr.add_suppression("The server was automatically set into read only mode after an error was detected.");
call mtr.add_suppression("The server was automatically set into offline mode after an error was detected.");
call mtr.add_suppression("Skipping leave operation: concurrent attempt to leave the group is on-going.");
SET SESSION sql_log_bin = 1;
SET @@GLOBAL.group_replication_recovery_reconnect_interval = @saved_group_replication_recovery_reconnect_interval;
SET @@GLOBAL.group_replication_recovery_retry_count = @saved_gr_recovery_retry_count;
DROP TABLE t1;
[connection server1]
DROP TABLE t1;
SET SQL_LOG_BIN=0;
DROP USER 'regular_user_p';
DROP USER 'regular_user_wp';
SET SQL_LOG_BIN=1;
include/group_replication_end.inc
