use test;
Test read-locality of READ-BACKUP
create table test.t1 (a int primary key,
b int,
c int,
d int,
unique(b) using hash,
key(c),
key(a,c))
comment="NDB_TABLE=READ_BACKUP=1"
   engine=ndb;
Warnings:
Warning	1121	Ndb does not support unique index on NULL valued attributes, index access with NULL value will become full table scan
create table test.t2 (a int primary key,
b int,
c int,
d text,
unique(b) using hash,
key(c))
comment="NDB_TABLE=READ_BACKUP=1"
   engine=ndb;
Warnings:
Warning	1121	Ndb does not support unique index on NULL valued attributes, index access with NULL value will become full table scan
select id into @t1_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t1';
select id into @t1ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t1_id and type = 3;
select id into @t2_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t2';
select id into @t2ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and type = 3;
select id into @t2bt_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and fq_name like 'test/def/NDB$BLOB%';
Put some data in.
Make it more interesting for joining
update test.t1 set c=c+1, d=d+1;
select count(distinct node_id) into @nodes
from ndbinfo.ndb$membership where group_id < 0xFFFFFF00;
Check replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1'
   and fragment_num=0;
count(distinct node_id)
2
Check nodes with table data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1';
count(distinct node_id)
2
Check UI replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with UI data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id;
count(distinct node_id)
2
Put some data in.
insert into test.t2 select a,b,c,repeat('BJC',300) from test.t1;
Check blob table replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with blob data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id;
count(distinct node_id)
2
set @partitions = 'PARTITION p2 VALUES IN (2)';
set @partitions = concat(@partitions,',PARTITION p1 VALUES IN (1)');
create table hints (i int primary key) engine=ndb partition by list(i) (PARTITION p2 VALUES IN (2),PARTITION p1 VALUES IN (1));
Warnings:
Warning	6035	'PARTITION BY LIST' for 'ndbcluster' storage engine is deprecated and will be removed in a future release.
insert into hints values (2);
Hint node by 2 of 2
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (1);
Hint node by 1 of 2
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


drop table hints;
delete from t1;
delete from t2;
drop table test.t1;
drop table test.t2;
Test read-locality after ALTER TABLE into READ-BACKUP
create table test.t1 (a int primary key,
b int,
c int,
d int,
unique(b) using hash,
key(c),
key(a,c))
engine=ndb;
Warnings:
Warning	1121	Ndb does not support unique index on NULL valued attributes, index access with NULL value will become full table scan
create table test.t2 (a int primary key,
b int,
c int,
d text,
unique(b) using hash,
key(c))
engine=ndb;
Warnings:
Warning	1121	Ndb does not support unique index on NULL valued attributes, index access with NULL value will become full table scan
alter table t1 algorithm=inplace, comment="NDB_TABLE=READ_BACKUP=1";
alter table t2 algorithm=inplace, comment="NDB_TABLE=READ_BACKUP=1";
-- t2 --
Version: Any
Fragment type: HashMapPartition
K Value: 6
Min load factor: 78
Max load factor: 80
Temporary table: no
Number of attributes: 4
Number of primary keys: 1
Length of frm data: XXX
Max Rows: 0
Row Checksum: 1
Row GCI: 1
SingleUserMode: 0
ForceVarPart: 1
PartitionCount: 8
FragmentCount: 8
PartitionBalance: FOR_RP_BY_LDM
ExtraRowGciBits: 0
ExtraRowAuthorBits: 0
TableStatus: Retrieved
Table options: readbackup
HashMap: DEFAULT-HASHMAP-3840-8
-- Attributes --
a Int PRIMARY KEY DISTRIBUTION KEY AT=FIXED ST=MEMORY
b Int NULL AT=FIXED ST=MEMORY
c Int NULL AT=FIXED ST=MEMORY
d Text(256,2000,0;utf8mb4_0900_ai_ci) NULL AT=MEDIUM_VAR ST=MEMORY BV=2 BT=NDB$BLOB_XX_3
-- Indexes -- 
PRIMARY KEY(a) - UniqueHashIndex
PRIMARY(a) - OrderedIndex
b$unique(b) - UniqueHashIndex
c(c) - OrderedIndex
-- t2/PRIMARY --
Version: Any
Base table: t2
Number of attributes: 1
Logging: 0
Index type: OrderedIndex
Index status: Retrieved
-- Attributes --
a Int NOT NULL AT=FIXED ST=MEMORY
-- IndexTable XX/PRIMARY --
Version: Any
Fragment type: FragUndefined
K Value: 6
Min load factor: 78
Max load factor: 80
Temporary table: yes
Number of attributes: 2
Number of primary keys: 1
Length of frm data: XXX
Max Rows: 0
Row Checksum: 1
Row GCI: 1
SingleUserMode: 2
ForceVarPart: 0
PartitionCount: 8
FragmentCount: 8
PartitionBalance: FOR_RP_BY_LDM
ExtraRowGciBits: 0
ExtraRowAuthorBits: 0
TableStatus: Retrieved
Table options: readbackup
-- Attributes --
a Int NOT NULL DISTRIBUTION KEY AT=FIXED ST=MEMORY
NDB$TNODE Unsigned [64] PRIMARY KEY DISTRIBUTION KEY AT=FIXED ST=MEMORY
-- Indexes -- 
PRIMARY KEY(NDB$TNODE) - UniqueHashIndex

-- t2/b$unique --
Version: Any
Base table: t2
Number of attributes: 1
Logging: 1
Index type: UniqueHashIndex
Index status: Retrieved
-- Attributes --
b Int PRIMARY KEY DISTRIBUTION KEY AT=FIXED ST=MEMORY
-- IndexTable XX/b$unique --
Version: Any
Fragment type: HashMapPartition
K Value: 6
Min load factor: 78
Max load factor: 80
Temporary table: no
Number of attributes: 2
Number of primary keys: 1
Length of frm data: XXX
Max Rows: 0
Row Checksum: 1
Row GCI: 1
SingleUserMode: 2
ForceVarPart: 0
PartitionCount: 8
FragmentCount: 8
PartitionBalance: FOR_RP_BY_LDM
ExtraRowGciBits: 0
ExtraRowAuthorBits: 0
TableStatus: Retrieved
Table options: readbackup
HashMap: DEFAULT-HASHMAP-3840-8
-- Attributes --
b Int PRIMARY KEY DISTRIBUTION KEY AT=FIXED ST=MEMORY
NDB$PK Unsigned [2] NOT NULL AT=FIXED ST=MEMORY
-- Indexes -- 
PRIMARY KEY(b) - UniqueHashIndex

-- t2/c --
Version: Any
Base table: t2
Number of attributes: 1
Logging: 0
Index type: OrderedIndex
Index status: Retrieved
-- Attributes --
c Int NULL AT=FIXED ST=MEMORY
-- IndexTable XX/c --
Version: Any
Fragment type: FragUndefined
K Value: 6
Min load factor: 78
Max load factor: 80
Temporary table: yes
Number of attributes: 2
Number of primary keys: 1
Length of frm data: XXX
Max Rows: 0
Row Checksum: 1
Row GCI: 1
SingleUserMode: 2
ForceVarPart: 0
PartitionCount: 8
FragmentCount: 8
PartitionBalance: FOR_RP_BY_LDM
ExtraRowGciBits: 0
ExtraRowAuthorBits: 0
TableStatus: Retrieved
Table options: readbackup
-- Attributes --
c Int NULL AT=FIXED ST=MEMORY
NDB$TNODE Unsigned [64] PRIMARY KEY DISTRIBUTION KEY AT=FIXED ST=MEMORY
-- Indexes -- 
PRIMARY KEY(NDB$TNODE) - UniqueHashIndex

-- NDB$BLOB_XX_3 --
Version: Any
Fragment type: HashMapPartition
K Value: 6
Min load factor: 78
Max load factor: 80
Temporary table: no
Number of attributes: 4
Number of primary keys: 2
Length of frm data: XXX
Max Rows: 0
Row Checksum: 1
Row GCI: 1
SingleUserMode: 0
ForceVarPart: 0
PartitionCount: 8
FragmentCount: 8
PartitionBalance: FOR_RP_BY_LDM
ExtraRowGciBits: 0
ExtraRowAuthorBits: 0
TableStatus: Retrieved
Table options: readbackup
HashMap: DEFAULT-HASHMAP-3840-8
-- Attributes --
a Int PRIMARY KEY DISTRIBUTION KEY AT=FIXED ST=MEMORY
NDB$PART Unsigned PRIMARY KEY AT=FIXED ST=MEMORY
NDB$PKID Unsigned NOT NULL AT=FIXED ST=MEMORY
NDB$DATA Longvarchar(2000;binary) NOT NULL AT=MEDIUM_VAR ST=MEMORY
-- Indexes -- 
PRIMARY KEY(a, NDB$PART) - UniqueHashIndex

select id into @t1_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t1';
select id into @t1ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t1_id and type = 3;
select id into @t2_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t2';
select id into @t2ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and type = 3;
select id into @t2bt_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and fq_name like 'test/def/NDB$BLOB%';
Put some data in.
Make it more interesting for joining
update test.t1 set c=c+1, d=d+1;
select count(distinct node_id) into @nodes
from ndbinfo.ndb$membership where group_id < 0xFFFFFF00;
Check replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1'
   and fragment_num=0;
count(distinct node_id)
2
Check nodes with table data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1';
count(distinct node_id)
2
Check UI replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with UI data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id;
count(distinct node_id)
2
Put some data in.
insert into test.t2 select a,b,c,repeat('BJC',300) from test.t1;
Check blob table replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with blob data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id;
count(distinct node_id)
2
set @partitions = 'PARTITION p2 VALUES IN (2)';
set @partitions = concat(@partitions,',PARTITION p1 VALUES IN (1)');
create table hints (i int primary key) engine=ndb partition by list(i) (PARTITION p2 VALUES IN (2),PARTITION p1 VALUES IN (1));
Warnings:
Warning	6035	'PARTITION BY LIST' for 'ndbcluster' storage engine is deprecated and will be removed in a future release.
insert into hints values (2);
Hint node by 2 of 2
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (1);
Hint node by 1 of 2
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


drop table hints;
delete from t1;
delete from t2;
Executing RESTART on all nodes.
Starting shutdown. This may take a while. Please wait...
All DB nodes are being restarted.

-- t2 --
Version: Any
Fragment type: HashMapPartition
K Value: 6
Min load factor: 78
Max load factor: 80
Temporary table: no
Number of attributes: 4
Number of primary keys: 1
Length of frm data: XXX
Max Rows: 0
Row Checksum: 1
Row GCI: 1
SingleUserMode: 0
ForceVarPart: 1
PartitionCount: 8
FragmentCount: 8
PartitionBalance: FOR_RP_BY_LDM
ExtraRowGciBits: 0
ExtraRowAuthorBits: 0
TableStatus: Retrieved
Table options: readbackup
HashMap: DEFAULT-HASHMAP-3840-8
-- Attributes --
a Int PRIMARY KEY DISTRIBUTION KEY AT=FIXED ST=MEMORY
b Int NULL AT=FIXED ST=MEMORY
c Int NULL AT=FIXED ST=MEMORY
d Text(256,2000,0;utf8mb4_0900_ai_ci) NULL AT=MEDIUM_VAR ST=MEMORY BV=2 BT=NDB$BLOB_XX_3
-- Indexes -- 
PRIMARY KEY(a) - UniqueHashIndex
PRIMARY(a) - OrderedIndex
b$unique(b) - UniqueHashIndex
c(c) - OrderedIndex
-- t2/PRIMARY --
Version: Any
Base table: t2
Number of attributes: 1
Logging: 0
Index type: OrderedIndex
Index status: Retrieved
-- Attributes --
a Int NOT NULL AT=FIXED ST=MEMORY
-- IndexTable XX/PRIMARY --
Version: Any
Fragment type: FragUndefined
K Value: 6
Min load factor: 78
Max load factor: 80
Temporary table: yes
Number of attributes: 2
Number of primary keys: 1
Length of frm data: XXX
Max Rows: 0
Row Checksum: 1
Row GCI: 1
SingleUserMode: 2
ForceVarPart: 0
PartitionCount: 8
FragmentCount: 8
PartitionBalance: FOR_RP_BY_LDM
ExtraRowGciBits: 0
ExtraRowAuthorBits: 0
TableStatus: Retrieved
Table options: readbackup
-- Attributes --
a Int NOT NULL DISTRIBUTION KEY AT=FIXED ST=MEMORY
NDB$TNODE Unsigned [64] PRIMARY KEY DISTRIBUTION KEY AT=FIXED ST=MEMORY
-- Indexes -- 
PRIMARY KEY(NDB$TNODE) - UniqueHashIndex

-- t2/b$unique --
Version: Any
Base table: t2
Number of attributes: 1
Logging: 1
Index type: UniqueHashIndex
Index status: Retrieved
-- Attributes --
b Int PRIMARY KEY DISTRIBUTION KEY AT=FIXED ST=MEMORY
-- IndexTable XX/b$unique --
Version: Any
Fragment type: HashMapPartition
K Value: 6
Min load factor: 78
Max load factor: 80
Temporary table: no
Number of attributes: 2
Number of primary keys: 1
Length of frm data: XXX
Max Rows: 0
Row Checksum: 1
Row GCI: 1
SingleUserMode: 2
ForceVarPart: 0
PartitionCount: 8
FragmentCount: 8
PartitionBalance: FOR_RP_BY_LDM
ExtraRowGciBits: 0
ExtraRowAuthorBits: 0
TableStatus: Retrieved
Table options: readbackup
HashMap: DEFAULT-HASHMAP-3840-8
-- Attributes --
b Int PRIMARY KEY DISTRIBUTION KEY AT=FIXED ST=MEMORY
NDB$PK Unsigned [2] NOT NULL AT=FIXED ST=MEMORY
-- Indexes -- 
PRIMARY KEY(b) - UniqueHashIndex

-- t2/c --
Version: Any
Base table: t2
Number of attributes: 1
Logging: 0
Index type: OrderedIndex
Index status: Retrieved
-- Attributes --
c Int NULL AT=FIXED ST=MEMORY
-- IndexTable XX/c --
Version: Any
Fragment type: FragUndefined
K Value: 6
Min load factor: 78
Max load factor: 80
Temporary table: yes
Number of attributes: 2
Number of primary keys: 1
Length of frm data: XXX
Max Rows: 0
Row Checksum: 1
Row GCI: 1
SingleUserMode: 2
ForceVarPart: 0
PartitionCount: 8
FragmentCount: 8
PartitionBalance: FOR_RP_BY_LDM
ExtraRowGciBits: 0
ExtraRowAuthorBits: 0
TableStatus: Retrieved
Table options: readbackup
-- Attributes --
c Int NULL AT=FIXED ST=MEMORY
NDB$TNODE Unsigned [64] PRIMARY KEY DISTRIBUTION KEY AT=FIXED ST=MEMORY
-- Indexes -- 
PRIMARY KEY(NDB$TNODE) - UniqueHashIndex

-- NDB$BLOB_XX_3 --
Version: Any
Fragment type: HashMapPartition
K Value: 6
Min load factor: 78
Max load factor: 80
Temporary table: no
Number of attributes: 4
Number of primary keys: 2
Length of frm data: XXX
Max Rows: 0
Row Checksum: 1
Row GCI: 1
SingleUserMode: 0
ForceVarPart: 0
PartitionCount: 8
FragmentCount: 8
PartitionBalance: FOR_RP_BY_LDM
ExtraRowGciBits: 0
ExtraRowAuthorBits: 0
TableStatus: Retrieved
Table options: readbackup
HashMap: DEFAULT-HASHMAP-3840-8
-- Attributes --
a Int PRIMARY KEY DISTRIBUTION KEY AT=FIXED ST=MEMORY
NDB$PART Unsigned PRIMARY KEY AT=FIXED ST=MEMORY
NDB$PKID Unsigned NOT NULL AT=FIXED ST=MEMORY
NDB$DATA Longvarchar(2000;binary) NOT NULL AT=MEDIUM_VAR ST=MEMORY
-- Indexes -- 
PRIMARY KEY(a, NDB$PART) - UniqueHashIndex

select id into @t1_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t1';
select id into @t1ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t1_id and type = 3;
select id into @t2_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t2';
select id into @t2ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and type = 3;
select id into @t2bt_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and fq_name like 'test/def/NDB$BLOB%';
Put some data in.
Make it more interesting for joining
update test.t1 set c=c+1, d=d+1;
select count(distinct node_id) into @nodes
from ndbinfo.ndb$membership where group_id < 0xFFFFFF00;
Check replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1'
   and fragment_num=0;
count(distinct node_id)
2
Check nodes with table data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1';
count(distinct node_id)
2
Check UI replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with UI data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id;
count(distinct node_id)
2
Put some data in.
insert into test.t2 select a,b,c,repeat('BJC',300) from test.t1;
Check blob table replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with blob data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id;
count(distinct node_id)
2
set @partitions = 'PARTITION p2 VALUES IN (2)';
set @partitions = concat(@partitions,',PARTITION p1 VALUES IN (1)');
create table hints (i int primary key) engine=ndb partition by list(i) (PARTITION p2 VALUES IN (2),PARTITION p1 VALUES IN (1));
Warnings:
Warning	6035	'PARTITION BY LIST' for 'ndbcluster' storage engine is deprecated and will be removed in a future release.
insert into hints values (2);
Hint node by 2 of 2
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (1);
Hint node by 1 of 2
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


drop table hints;
delete from t1;
delete from t2;
drop table test.t1;
drop table test.t2;
Test read-locality of FULLY-REPLICATED
create table test.t1 (a int primary key,
b int,
c int,
d int,
unique(b) using hash,
key(c),
key(a,c))
comment="NDB_TABLE=FULLY_REPLICATED=1"
   engine=ndb;
Warnings:
Warning	1121	Ndb does not support unique index on NULL valued attributes, index access with NULL value will become full table scan
create table test.t2 (a int primary key,
b int,
c int,
d text,
unique(b) using hash,
key(c))
comment="NDB_TABLE=FULLY_REPLICATED=1"
   engine=ndb;
Warnings:
Warning	1121	Ndb does not support unique index on NULL valued attributes, index access with NULL value will become full table scan
select id into @t1_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t1';
select id into @t1ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t1_id and type = 3;
select id into @t2_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t2';
select id into @t2ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and type = 3;
select id into @t2bt_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and fq_name like 'test/def/NDB$BLOB%';
Put some data in.
Make it more interesting for joining
update test.t1 set c=c+1, d=d+1;
select count(distinct node_id) into @nodes
from ndbinfo.ndb$membership where group_id < 0xFFFFFF00;
Check replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1'
   and fragment_num=0;
count(distinct node_id)
2
Check nodes with table data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1';
count(distinct node_id)
2
Check UI replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with UI data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id;
count(distinct node_id)
2
Put some data in.
insert into test.t2 select a,b,c,repeat('BJC',300) from test.t1;
Check blob table replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with blob data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id;
count(distinct node_id)
2
set @partitions = 'PARTITION p2 VALUES IN (2)';
set @partitions = concat(@partitions,',PARTITION p1 VALUES IN (1)');
create table hints (i int primary key) engine=ndb partition by list(i) (PARTITION p2 VALUES IN (2),PARTITION p1 VALUES IN (1));
Warnings:
Warning	6035	'PARTITION BY LIST' for 'ndbcluster' storage engine is deprecated and will be removed in a future release.
insert into hints values (2);
Hint node by 2 of 2
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (1);
Hint node by 1 of 2
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


drop table hints;
delete from t1;
delete from t2;
Nodegroup 1 created
alter table t1 algorithm=inplace, reorganize partition;
alter table t2 algorithm=inplace, reorganize partition;
select id into @t1_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t1';
select id into @t1ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t1_id and type = 3;
select id into @t2_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t2';
select id into @t2ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and type = 3;
select id into @t2bt_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and fq_name like 'test/def/NDB$BLOB%';
Put some data in.
Make it more interesting for joining
update test.t1 set c=c+1, d=d+1;
select count(distinct node_id) into @nodes
from ndbinfo.ndb$membership where group_id < 0xFFFFFF00;
Check replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1'
   and fragment_num=0;
count(distinct node_id)
2
Check nodes with table data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1';
count(distinct node_id)
4
Check UI replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with UI data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id;
count(distinct node_id)
4
Put some data in.
insert into test.t2 select a,b,c,repeat('BJC',300) from test.t1;
Check blob table replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with blob data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id;
count(distinct node_id)
4
set @partitions = 'PARTITION p4 VALUES IN (4)';
set @partitions = concat(@partitions,',PARTITION p3 VALUES IN (3)');
set @partitions = concat(@partitions,',PARTITION p2 VALUES IN (2)');
set @partitions = concat(@partitions,',PARTITION p1 VALUES IN (1)');
create table hints (i int primary key) engine=ndb partition by list(i) (PARTITION p4 VALUES IN (4),PARTITION p3 VALUES IN (3),PARTITION p2 VALUES IN (2),PARTITION p1 VALUES IN (1));
Warnings:
Warning	6035	'PARTITION BY LIST' for 'ndbcluster' storage engine is deprecated and will be removed in a future release.
insert into hints values (4);
Hint node by 4 of 4
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 4;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 4;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 4;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (3);
Hint node by 3 of 4
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 3;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 3;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 3;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (2);
Hint node by 2 of 4
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (1);
Hint node by 1 of 4
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


drop table hints;
delete from t1;
delete from t2;
Nodegroup 2 created
alter table t1 algorithm=inplace, reorganize partition;
alter table t2 algorithm=inplace, reorganize partition;
select id into @t1_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t1';
select id into @t1ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t1_id and type = 3;
select id into @t2_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t2';
select id into @t2ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and type = 3;
select id into @t2bt_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and fq_name like 'test/def/NDB$BLOB%';
Put some data in.
Make it more interesting for joining
update test.t1 set c=c+1, d=d+1;
select count(distinct node_id) into @nodes
from ndbinfo.ndb$membership where group_id < 0xFFFFFF00;
Check replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1'
   and fragment_num=0;
count(distinct node_id)
2
Check nodes with table data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1';
count(distinct node_id)
6
Check UI replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with UI data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id;
count(distinct node_id)
6
Put some data in.
insert into test.t2 select a,b,c,repeat('BJC',300) from test.t1;
Check blob table replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with blob data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id;
count(distinct node_id)
6
set @partitions = 'PARTITION p6 VALUES IN (6)';
set @partitions = concat(@partitions,',PARTITION p5 VALUES IN (5)');
set @partitions = concat(@partitions,',PARTITION p4 VALUES IN (4)');
set @partitions = concat(@partitions,',PARTITION p3 VALUES IN (3)');
set @partitions = concat(@partitions,',PARTITION p2 VALUES IN (2)');
set @partitions = concat(@partitions,',PARTITION p1 VALUES IN (1)');
create table hints (i int primary key) engine=ndb partition by list(i) (PARTITION p6 VALUES IN (6),PARTITION p5 VALUES IN (5),PARTITION p4 VALUES IN (4),PARTITION p3 VALUES IN (3),PARTITION p2 VALUES IN (2),PARTITION p1 VALUES IN (1));
Warnings:
Warning	6035	'PARTITION BY LIST' for 'ndbcluster' storage engine is deprecated and will be removed in a future release.
insert into hints values (6);
Hint node by 6 of 6
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 6;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 6;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 6;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (5);
Hint node by 5 of 6
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 5;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 5;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 5;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (4);
Hint node by 4 of 6
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 4;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 4;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 4;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (3);
Hint node by 3 of 6
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 3;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 3;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 3;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (2);
Hint node by 2 of 6
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (1);
Hint node by 1 of 6
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


drop table hints;
delete from t1;
delete from t2;
drop table test.t1;
drop table test.t2;
Drop Node Group 2 done
Drop Node Group 1 done
Test read-locality of FULLY-REPLICATED with FOR_RA_BY_NODE
create table test.t1 (a int primary key,
b int,
c int,
d int,
unique(b) using hash,
key(c),
key(a,c))
comment="NDB_TABLE=FULLY_REPLICATED=1,PARTITION_BALANCE=FOR_RA_BY_NODE"
   engine=ndb;
Warnings:
Warning	1121	Ndb does not support unique index on NULL valued attributes, index access with NULL value will become full table scan
create table test.t2 (a int primary key,
b int,
c int,
d text,
unique(b) using hash,
key(c))
comment="NDB_TABLE=FULLY_REPLICATED=1,PARTITION_BALANCE=FOR_RA_BY_NODE"
   engine=ndb;
Warnings:
Warning	1121	Ndb does not support unique index on NULL valued attributes, index access with NULL value will become full table scan
select id into @t1_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t1';
select id into @t1ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t1_id and type = 3;
select id into @t2_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t2';
select id into @t2ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and type = 3;
select id into @t2bt_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and fq_name like 'test/def/NDB$BLOB%';
Put some data in.
Make it more interesting for joining
update test.t1 set c=c+1, d=d+1;
select count(distinct node_id) into @nodes
from ndbinfo.ndb$membership where group_id < 0xFFFFFF00;
Check replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1'
   and fragment_num=0;
count(distinct node_id)
2
Check nodes with table data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1';
count(distinct node_id)
2
Check UI replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with UI data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id;
count(distinct node_id)
2
Put some data in.
insert into test.t2 select a,b,c,repeat('BJC',300) from test.t1;
Check blob table replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with blob data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id;
count(distinct node_id)
2
set @partitions = 'PARTITION p2 VALUES IN (2)';
set @partitions = concat(@partitions,',PARTITION p1 VALUES IN (1)');
create table hints (i int primary key) engine=ndb partition by list(i) (PARTITION p2 VALUES IN (2),PARTITION p1 VALUES IN (1));
Warnings:
Warning	6035	'PARTITION BY LIST' for 'ndbcluster' storage engine is deprecated and will be removed in a future release.
insert into hints values (2);
Hint node by 2 of 2
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (1);
Hint node by 1 of 2
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


drop table hints;
delete from t1;
delete from t2;
Nodegroup 1 created
alter table t1 algorithm=inplace, reorganize partition;
alter table t2 algorithm=inplace, reorganize partition;
select id into @t1_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t1';
select id into @t1ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t1_id and type = 3;
select id into @t2_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t2';
select id into @t2ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and type = 3;
select id into @t2bt_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and fq_name like 'test/def/NDB$BLOB%';
Put some data in.
Make it more interesting for joining
update test.t1 set c=c+1, d=d+1;
select count(distinct node_id) into @nodes
from ndbinfo.ndb$membership where group_id < 0xFFFFFF00;
Check replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1'
   and fragment_num=0;
count(distinct node_id)
2
Check nodes with table data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1';
count(distinct node_id)
4
Check UI replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with UI data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id;
count(distinct node_id)
4
Put some data in.
insert into test.t2 select a,b,c,repeat('BJC',300) from test.t1;
Check blob table replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with blob data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id;
count(distinct node_id)
4
set @partitions = 'PARTITION p4 VALUES IN (4)';
set @partitions = concat(@partitions,',PARTITION p3 VALUES IN (3)');
set @partitions = concat(@partitions,',PARTITION p2 VALUES IN (2)');
set @partitions = concat(@partitions,',PARTITION p1 VALUES IN (1)');
create table hints (i int primary key) engine=ndb partition by list(i) (PARTITION p4 VALUES IN (4),PARTITION p3 VALUES IN (3),PARTITION p2 VALUES IN (2),PARTITION p1 VALUES IN (1));
Warnings:
Warning	6035	'PARTITION BY LIST' for 'ndbcluster' storage engine is deprecated and will be removed in a future release.
insert into hints values (4);
Hint node by 4 of 4
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 4;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 4;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 4;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (3);
Hint node by 3 of 4
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 3;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 3;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 3;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (2);
Hint node by 2 of 4
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (1);
Hint node by 1 of 4
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


drop table hints;
delete from t1;
delete from t2;
Nodegroup 2 created
alter table t1 algorithm=inplace, reorganize partition;
alter table t2 algorithm=inplace, reorganize partition;
select id into @t1_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t1';
select id into @t1ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t1_id and type = 3;
select id into @t2_id from ndbinfo.ndb$dict_obj_info where fq_name = 'test/def/t2';
select id into @t2ui_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and type = 3;
select id into @t2bt_id from ndbinfo.ndb$dict_obj_info where parent_obj_id = @t2_id and fq_name like 'test/def/NDB$BLOB%';
Put some data in.
Make it more interesting for joining
update test.t1 set c=c+1, d=d+1;
select count(distinct node_id) into @nodes
from ndbinfo.ndb$membership where group_id < 0xFFFFFF00;
Check replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1'
   and fragment_num=0;
count(distinct node_id)
2
Check nodes with table data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where fq_name='test/def/t1';
count(distinct node_id)
6
Check UI replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with UI data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t1ui_id;
count(distinct node_id)
6
Put some data in.
insert into test.t2 select a,b,c,repeat('BJC',300) from test.t1;
Check blob table replicas
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id
and fragment_num=0;
count(distinct node_id)
2
Check nodes with blob data
select count(distinct node_id)
from ndbinfo.operations_per_fragment
where table_id = @t2bt_id;
count(distinct node_id)
6
set @partitions = 'PARTITION p6 VALUES IN (6)';
set @partitions = concat(@partitions,',PARTITION p5 VALUES IN (5)');
set @partitions = concat(@partitions,',PARTITION p4 VALUES IN (4)');
set @partitions = concat(@partitions,',PARTITION p3 VALUES IN (3)');
set @partitions = concat(@partitions,',PARTITION p2 VALUES IN (2)');
set @partitions = concat(@partitions,',PARTITION p1 VALUES IN (1)');
create table hints (i int primary key) engine=ndb partition by list(i) (PARTITION p6 VALUES IN (6),PARTITION p5 VALUES IN (5),PARTITION p4 VALUES IN (4),PARTITION p3 VALUES IN (3),PARTITION p2 VALUES IN (2),PARTITION p1 VALUES IN (1));
Warnings:
Warning	6035	'PARTITION BY LIST' for 'ndbcluster' storage engine is deprecated and will be removed in a future release.
insert into hints values (6);
Hint node by 6 of 6
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 6;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 6;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 6;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (5);
Hint node by 5 of 6
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 5;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 5;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 5;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (4);
Hint node by 4 of 6
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 4;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 4;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 4;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (3);
Hint node by 3 of 6
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 3;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 3;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 3;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (2);
Hint node by 2 of 6
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 2;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


insert into hints values (1);
Hint node by 1 of 6
Tests on T1 (!Blobs)
Test some basic NdbApi
PK lookups
explain select * from test.t1 where t1.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t1 where t1.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t1 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t1`.`c` > 6); Using MRR
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1` where (`test`.`t1`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c`,`test`.`t1`.`d` AS `d` from `test`.`t1`
Error insert to ensure that TC requests are purely local


Test pushed queries (SPJ)
Pushed pk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed uk->pk
Lookup to get a.d, feeding pk lookup of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a
where a.b = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	b	b	5	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`a` = `test`.`a`.`d`) and (`test`.`a`.`b` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed pk->uk
Lookup to get a.d, feeding uk lookup of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b
where a.a = 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	eq_ref	PRIMARY,a	PRIMARY	4	const	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`b` = `test`.`a`.`d`) and (`test`.`a`.`a` = 6))
Error insert to ensure that SPJ requests are purely local


Pushed scan originating sub-lookups
Table scan to get a.d, feeding pk lookups of b.a
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	PRIMARY,a	PRIMARY	4	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`a` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.a;
commit;

Pushed scan originating sub-lookups
Table scan to get a.d, feeding uk lookups of b.b
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	ALL	NULL	NULL	NULL	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition (`test`.`a`.`d` is not null)
1	SIMPLE	b	#	eq_ref	b	b	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where (`test`.`b`.`b` = `test`.`a`.`d`)
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
a.d = b.b;
commit;

Pushed scan originating sub-scans
Range scan on a.a > 10 getting a.d, feeding range scans on b.c = a.d
explain select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	a	#	range	PRIMARY,a	PRIMARY	4	NULL	#	#	Parent of 2 pushed join@1; Using pushed condition ((`test`.`a`.`a` > 10) and (`test`.`a`.`d` is not null)); Using MRR
1	SIMPLE	b	#	ref	c	c	5	test.a.d	#	#	Child of 'a' in pushed join@1
Warnings:
Note	1003	/* select#1 */ select `test`.`a`.`a` AS `a`,`test`.`a`.`d` AS `d`,`test`.`b`.`d` AS `d` from `test`.`t1` `a` join `test`.`t1` `b` where ((`test`.`b`.`c` = `test`.`a`.`d`) and (`test`.`a`.`a` > 10))
Error insert to ensure that SPJ requests are purely local

begin;
select * from hints where i = 1;
select a.a, a.d, b.d
from
test.t1 a
join
test.t1 b
on
b.c=a.d
where
a.a>10;
commit;

Tests on T2 (Blobs)
Test some basic NdbApi
explain select * from test.t2 where t2.a=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	PRIMARY	PRIMARY	4	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`a` = 6)
Error insert to ensure that TC requests are purely local


UI lookups
explain select * from test.t2 where t2.b=6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	eq_ref	b	b	5	const	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 6)
Error insert to ensure that TC requests are purely local


OI scan
explain select * from test.t2 where c > 6;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	range	c	c	5	NULL	#	#	Using pushed condition (`test`.`t2`.`c` > 6)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`c` > 6)
Error insert to ensure that TC requests are purely local


Table scan
explain select * from test.t2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	#	ALL	NULL	NULL	NULL	NULL	#	#	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2`
Error insert to ensure that TC requests are purely local


drop table hints;
delete from t1;
delete from t2;
drop table test.t1;
drop table test.t2;
Drop Node Group 1 done
Drop Node Group 2 done
