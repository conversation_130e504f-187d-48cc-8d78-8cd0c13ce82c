######################################################################################################
# BUG#18900932 : ERROR MESSAGE ISN'T SENT TO CLIENT WHEN SERVER REPORTS ONE
######################################################################################################
SELECT ST_AREA(ST_GEOMFROMTEXT('POLYGON((0 0,5 5, 0 0))'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
######################################################################################################
# BUG#18911154 : CENTROID() RETURNING ERROR MESSAGE FOR VALID GEOMETRY-COLLECTION GEOMETRY
######################################################################################################
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('MULTIPOINT(0 0,10 10)')));
ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('MULTIPOINT(0 0,10 10)')))
POINT(5 5)
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))')));
ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))')))
POINT(5 5)
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(0 0,10 10))')));
ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(0 0,10 10))')))
POINT(5 5)
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))')));
ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))')))
POINT(5 5)
######################################################################################################
# BUG#18919820 : CONVEXHULL() FUNCTION PRODUCES INVALID POLYGON AS THE RESULT
######################################################################################################
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('MULTIPOINT(5 0,25 0,15 10,15 25)')));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('MULTIPOINT(5 0,25 0,15 10,15 25)')))
POLYGON((5 0,25 0,15 25,5 0))
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('POLYGON((5 0,15 25,25 0,15 5,5 0))')));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('POLYGON((5 0,15 25,25 0,15 5,5 0))')))
POLYGON((5 0,25 0,15 25,5 0))
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((4 4,4 6,6 6,6 4,4 4,4 4)))')));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((4 4,4 6,6 6,6 4,4 4,4 4)))')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
######################################################################################################
# BUG#18935403 : SPATIAL ANALYSIS FUNCTIONS NOT RETURNING CONSISTENT RESULTS
######################################################################################################
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()')));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()')))
NULL
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()')));
ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()')))
GEOMETRYCOLLECTION EMPTY
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()')));
ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()')))
NULL
SELECT ST_DISTANCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'));
ST_DISTANCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))
NULL
SELECT ST_DISTANCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'),ST_GEOMFROMTEXT('POINT(10 10)'));
ST_DISTANCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'),ST_GEOMFROMTEXT('POINT(10 10)'))
NULL
SELECT ST_DISTANCE(ST_GEOMFROMTEXT('POINT(10 10)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'));
ST_DISTANCE(ST_GEOMFROMTEXT('POINT(10 10)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))
NULL
######################################################################################################
# BUG#18957010 : CONVEXHULL() FUNCTION RETURNS WRONG RESULT
######################################################################################################
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('MULTIPOINT(5 -3,0 2,5 7,10 2,10 0,10 -2)')));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('MULTIPOINT(5 -3,0 2,5 7,10 2,10 0,10 -2)')))
POLYGON((0 2,5 -3,10 -2,10 2,5 7,0 2))
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('MULTIPOINT(5 0,0 5,5 10,10 5,10 -5)')));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('MULTIPOINT(5 0,0 5,5 10,10 5,10 -5)')))
POLYGON((0 5,10 -5,10 5,5 10,0 5))
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('MULTIPOINT(2 -5,2 5,8 5,8 2,8 0)')));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('MULTIPOINT(2 -5,2 5,8 5,8 2,8 0)')))
POLYGON((2 -5,8 0,8 5,2 5,2 -5))
######################################################################################################
# BUG#18972363 : CENTROID() FUNCTION RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10,10 0),LINESTRING(0 0,10 0,10 -10,0 -10))')));
ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10,10 0),LINESTRING(0 0,10 0,10 -10,0 -10))')))
POINT(5.833333333333333 0.8333333333333334)
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,10 0,10 10,0 10),LINESTRING(0 0,10 0,10 -10,0 -10))')));
ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,10 0,10 10,0 10),LINESTRING(0 0,10 0,10 -10,0 -10))')))
POINT(6.666666666666667 0)
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),POLYGON((0 0,10 0,10 -10,0 -10,0 0)))')));
ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),POLYGON((0 0,10 0,10 -10,0 -10,0 0)))')))
POINT(5 0)
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10,10 0,0 0),LINESTRING(0 0,10 0,10 -10,0 -10,0 0))')));
ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10,10 0,0 0),LINESTRING(0 0,10 0,10 -10,0 -10,0 0))')))
POINT(5 0)
######################################################################################################
# BUG#18991528 : LONGFROMGEOHASH() DECODING FUNCTION ACCEPTS NON-STRING VALUE AS THE ARGUMENT
######################################################################################################
SELECT ST_LONGFROMGEOHASH(0123456789);
ERROR HY000: Incorrect type for argument geohash in function ST_LONGFROMGEOHASH.
SELECT ST_LONGFROMGEOHASH(9876543210);
ERROR HY000: Incorrect type for argument geohash in function ST_LONGFROMGEOHASH.
SELECT ST_LONGFROMGEOHASH(1);
ERROR HY000: Incorrect type for argument geohash in function ST_LONGFROMGEOHASH.
######################################################################################################
# BUG#19019796 : LONGFROMGEOHASH() AND LATFROMGEOHASH() FUNCTIONS RETURN -0 AS THE RESULT
######################################################################################################
SELECT ST_LONGFROMGEOHASH("GZZZZZZZZZZZZZZZZZZZ");
ST_LONGFROMGEOHASH("GZZZZZZZZZZZZZZZZZZZ")
0
SELECT ST_LONGFROMGEOHASH("7ZZZZZZZZZZZZZZZZZZZ");
ST_LONGFROMGEOHASH("7ZZZZZZZZZZZZZZZZZZZ")
0
SELECT ST_LATFROMGEOHASH("7ZZZZZZZZZZZZZZZZZZZ");
ST_LATFROMGEOHASH("7ZZZZZZZZZZZZZZZZZZZ")
0
SELECT ST_LATFROMGEOHASH("RZZZZZZZZZZZZZZZZZZZ");
ST_LATFROMGEOHASH("RZZZZZZZZZZZZZZZZZZZ")
0
######################################################################################################
# BUG#19076184 : ST_GEOHASH() FUNCTION ACCEPTS NON-INTEGER VALUES FOR MAXLENGTH PARAMETER
######################################################################################################
SELECT ST_GEOHASH(180,90,10.1);
ERROR HY000: Incorrect type for argument geohash max length in function st_geohash.
SELECT ST_GEOHASH(ST_GEOMFROMTEXT('POINT(180 90)'),20.0001);
ERROR HY000: Incorrect type for argument geohash max length in function st_geohash.
SELECT ST_GEOHASH(ST_GEOMFROMTEXT('POINT(180 90)'),(CAST(10 AS BINARY)));
ERROR HY000: Incorrect type for argument geohash max length in function st_geohash.
######################################################################################################
# BUG#19133043 : REPRESENTATION OF THE SAME POINT GEOMETRY ISSUE WITH SPATIAL ANALYSIS FUNCTIONS
######################################################################################################
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('POINT(-0 0)')));
ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('POINT(-0 0)')))
POINT(-0 0)
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('POINT(0 -0)')));
ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('POINT(0 -0)')))
POINT(0 -0)
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('POINT(0 -0)')));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('POINT(0 -0)')))
POINT(0 -0)
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('POINT(-0 0)')));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('POINT(-0 0)')))
POINT(-0 0)
######################################################################################################
# BUG#19142227 : ENVELOPE() SPATIAL ANALYSIS FUNCTION ACCEPTS INVALID POLYGONS AS THE ARGUMENT
######################################################################################################
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('POLYGON((0 0))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('POLYGON((0 0,5 5))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('POLYGON((0 0,5 5,10 10, 0 0))')));
ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('POLYGON((0 0,5 5,10 10, 0 0))')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,5 5)),((1 1,1 1,1 1,1 1)))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
######################################################################################################
# BUG#19154955 : ST_GEOHASH() FUNCTION RETURNS BINARY DATA INSTEAD OF TEXT WITH A CHARSET
######################################################################################################
SELECT ST_LONGFROMGEOHASH(ST_GEOHASH(ST_GEOMFROMTEXT('POINT(10 10)'),10));
ST_LONGFROMGEOHASH(ST_GEOHASH(ST_GEOMFROMTEXT('POINT(10 10)'),10))
10
SELECT ST_LATFROMGEOHASH(ST_GEOHASH(ST_GEOMFROMTEXT('POINT(10 10)'),10));
ST_LATFROMGEOHASH(ST_GEOHASH(ST_GEOMFROMTEXT('POINT(10 10)'),10))
10
SELECT ST_ASTEXT(ST_POINTFROMGEOHASH(ST_GEOHASH(ST_GEOMFROMTEXT('POINT(10 10)'),10),0));
ST_ASTEXT(ST_POINTFROMGEOHASH(ST_GEOHASH(ST_GEOMFROMTEXT('POINT(10 10)'),10),0))
POINT(10 10)
######################################################################################################
# BUG#19204199 : DISTANCE() AND ENVELOPE() FUNCTIONS RETURN WRONG RESULT
######################################################################################################
SELECT ST_DISTANCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20))'),ST_GEOMFROMTEXT('LINESTRING(5 0,10 0)'));
ST_DISTANCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20))'),ST_GEOMFROMTEXT('LINESTRING(5 0,10 0)'))
3.5355339059327378
SELECT ST_DISTANCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20),GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('LINESTRING(5 0,10 0)'));
ST_DISTANCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20),GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('LINESTRING(5 0,10 0)'))
3.5355339059327378
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10))')));
ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10))')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10),GEOMETRYCOLLECTION())')));
ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10),GEOMETRYCOLLECTION())')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
######################################################################################################
# BUG#19223763 : ST_ASGEOJSON() GIVES SAME MAX DECIMAL DIGIT VALUE IN THE ERROR MESSAGE
######################################################################################################
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT('POINT(10.12345678901234567890 10)'),-1);
ERROR HY000: Incorrect max decimal digits value: '-1' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT('POINT(10.12345678901234567890 10)'),-1000);
ERROR HY000: Incorrect max decimal digits value: '-1000' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT('POINT(10.12345678901234567890 10)'),2147483648);
ERROR HY000: Incorrect max decimal digits value: '2147483648' for function st_asgeojson
SELECT ST_ASGEOJSON(ST_GEOMFROMTEXT('POINT(10.12345678901234567890 10)'),1000000000000000000000000);
ERROR HY000: Incorrect type for argument max decimal digits in function st_asgeojson.
######################################################################################################
# BUG#19304320 : CENTROID() AND CONVEXHULL() FUNCTIONS RETURN NULL WITH VALID GEOMETRY INPUT
######################################################################################################
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))')));
ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))')))
POINT(5 5)
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))')));
ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))')))
POINT(5 5)
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))')));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))')));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
######################################################################################################
# BUG#19331370 : ST_GEOMFROMGEOJSON() FUNCTION RETURNS BINARY DATA INSTEAD GEOMETRY DATA
######################################################################################################
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [10, 15] }"))
{"type": "Point", "coordinates": [10.0, 15.0]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"LineString\", \"coordinates\": [ [102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0] ]}"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"LineString\", \"coordinates\": [ [102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0] ]}"))
{"type": "LineString", "coordinates": [[102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0]]}
SELECT ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"MultiPoint\", \"coordinates\": [ [-105.01, 39.57],[-80.66, 35.0] ] }"));
ST_ASGEOJSON(ST_GEOMFROMGEOJSON(
"{ \"type\": \"MultiPoint\", \"coordinates\": [ [-105.01, 39.57],[-80.66, 35.0] ] }"))
{"type": "MultiPoint", "coordinates": [[-105.01, 39.57], [-80.66, 35.0]]}
SELECT CHARSET(ST_GEOMFROMGEOJSON(
"{ \"type\": \"MultiPoint\", \"coordinates\": [ [-105.01, 39.57],[-80.66, 35.0] ] }"));
CHARSET(ST_GEOMFROMGEOJSON(
"{ \"type\": \"MultiPoint\", \"coordinates\": [ [-105.01, 39.57],[-80.66, 35.0] ] }"))
binary
SELECT CHARSET(ST_GEOMFROMGEOJSON(
"{ \"type\": \"LineString\", \"coordinates\": [ [102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0] ]}"));
CHARSET(ST_GEOMFROMGEOJSON(
"{ \"type\": \"LineString\", \"coordinates\": [ [102.0, 0.0], [103.0, 1.0], [104.0, 0.0], [105.0, 1.0] ]}"))
binary
######################################################################################################
# BUG#19341961 : ASSERTION `COORDINATES->ISARRAY()' FAILED
######################################################################################################
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Polygon\", \"coordinates\": [[1, 1], [1, 10], [10, 10], [1, 1]] }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
######################################################################################################
# BUG#19342661 : ASSERTION `OBJECT->ISOBJECT()' FAILED
######################################################################################################
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"GeometryCollection\", \"geometries\": [[]] }"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Member 'geometries' must be of type 'object array'
######################################################################################################
# BUG#19351967 : CENTROID() AND COVEXHULL() ACCEPTS GEOMETRYCOLLECTION CONTAINING INVALID POLYGON
######################################################################################################
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0)))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,5 5,10 10)))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
######################################################################################################
# BUG#19498377 : ST_GEOMFROMGEOJSON() FUNCTION ACCEPTS INVALID GEOJSON VALUE
######################################################################################################
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type1234567890\": \"Point\", \"coordinates\": [102, 11]}"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'type'
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"typeabcdefghijkl\": \"Point\", \"coordinates\": [102, 11]}"));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson: Missing required member 'type'
######################################################################################################
# BUG#19499584 : RAPIDJSON DOES NOT PARSE ALL NUMBERS WITH TRAILING ZEROS PROPERLY
######################################################################################################
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [102.0000, 11]}"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [102.0000, 11]}"))
POINT(11 102)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [102.00000, 11]}"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [102.00000, 11]}"))
POINT(11 102)
SELECT ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [102.000000, 11]}"));
ST_ASTEXT(ST_GEOMFROMGEOJSON(
"{ \"type\": \"Point\", \"coordinates\": [102.000000, 11]}"))
POINT(11 102)
######################################################################################################
# BUG#19549099 : VIRTUAL CONST CHAR* ITEM_FUNC_SPATIAL_MBR_REL::FUNC_NAME() CONST: ASSERTION `0'
######################################################################################################
SELECT MBRCOVERS(ST_GEOMFROMTEXT('POINT(0 0)'), ST_GEOMFROMTEXT('POINT(1 1)',4326));
ERROR HY000: Binary geometry function mbrcovers given two geometries of different srids: 0 and 4326, which should have been identical.
SELECT MBRCOVERS(ST_GEOMFROMTEXT('POINT(0 0)'), ST_GEOMFROMTEXT('POINT(1 1)',4326));
ERROR HY000: Binary geometry function mbrcovers given two geometries of different srids: 0 and 4326, which should have been identical.
######################################################################################################
# BUG#19552241 : MBRTOUCHES() FUNCTION RETURNS INCORRECT RESULT
######################################################################################################
SELECT MBRTOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(5 0,5 10),GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('LINESTRING(5 0,5 10)'));
MBRTOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(5 0,5 10),GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('LINESTRING(5 0,5 10)'))
0
SELECT MBRTOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(5 0,5 10),GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'));
MBRTOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(5 0,5 10),GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'))
0
######################################################################################################
# BUG#19784515 : ASSERTION `DIM1 >= 0 && DIM1 <= 2 && DIM2 >= 0 && DIM2 <= 2' FAILED
######################################################################################################
# Invalid geometry in input
DO MBRWITHIN(
ST_CENTROID(
ST_UNION(
ST_UNION(
ST_GEOMFROMTEXT(
'MULTILINESTRING((-556 966,-721 -210),'
                                    '(-202 390,-954 804,682 504,-394 -254,832 371,907 -369,827 126,-567 -337,-304 -555,-957 -483,-660 792),(-965 -940,814 -804,-477 -909,-128 57,-819 880,761 497,-559 40,-431 427,179 -291,-707 315,137 -781,-416 -371,-5 -156),'
                                    '(-600 -570,-481 -191,991 -361,768 888,-647 566,795 -861,-82 -575,-593 539))'),
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((805 69,249 708,147 455,546 -672,-218 843,458 24,-630 -420,-83 -69, 805 69)),'
                                 '((196 -219,-201 663,-867 521,-910 -315,-749 801,-402 820,-167 -817,-526 -163,744 -988,-588 -370,573 695,-597 513,-246 439, 196 -219)),'
                                 '((32 -903,189 -871,-778 -741,784 340,403 -555,607 -540,-513 -982,700 -124,344 732,714 151,-812 -252,-440 -895,-426 231,-819 -357, 32 -903)),'
                                 '((-395 830,454 -143,788 -279,618 -843,-490 -507,-224 17, -395 830)))')),
ST_INTERSECTION(
ST_UNION(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                            'POINT(-169 -570),'
                            'MULTIPOINT(384 290,-601 123,408 86,-616 -300,160 -474,-979 -4,-63 -824,-689 -765,-219 802,-54 -93,191 -982,-723 -449),'
                            'MULTILINESTRING((683 4,864 -634,548 -891,727 -691,-570 32,-334 -438,127 -317,241 -12,-807 947,-987 693,-345 -867,854 -106)),'
                            'MULTIPOINT(384 290,-601 123,408 86,-616 -300,160 -474,-979 -4,-63 -824,-689 -765,-219 802,-54 -93,191 -982,-723 -449),'
                            'MULTIPOLYGON(((266 51,851 523,-781 366,-607 -581, 266 51)),'
                                         '((416 -450,-973 880,103 226,-896 -857,-369 761, 416 -450)),'
                                         '((168 171,26 -99,-606 -490,-174 -138,-325 -218,-833 -652,-255 -445,-882 -762,-202 -560, 168 171)),'
                                         '((-423 -216,-531 -190,-147 821,362 441,645 -128,-997 708,134 -426,714 -9,147 842,-887 -870,688 -330,689 17,-314 -262,401 -112,-606 761, -423 -216)),'
                                         '((-582 -373,-360 -84,-727 -171,412 -660,750 -846,-464 718,163 -11,489 -659,586 -324,-741 -198,144 -165,644 -80,930 -487,-504 -205, -582 -373))),'
                            'MULTIPOLYGON(((266 51,851 523,-781 366,-607 -581, 266 51)),'
                                         '((416 -450,-973 880,103 226,-896 -857,-369 761, 416 -450)),'
                                         '((168 171,26 -99,-606 -490,-174 -138,-325 -218,-833 -652,-255 -445,-882 -762,-202 -560, 168 171)),'
                                         '((-423 -216,-531 -190,-147 821,362 441,645 -128,-997 708,134 -426,714 -9,147 842,-887 -870,688 -330,689 17,-314 -262,401 -112,-606 761, -423 -216)),'
                                         '((-582 -373,-360 -84,-727 -171,412 -660,750 -846,-464 718,163 -11,489 -659,586 -324,-741 -198,144 -165,644 -80,930 -487,-504 -205, -582 -373))),'
                            'GEOMETRYCOLLECTION(),'
                            'MULTIPOINT(384 290,-601 123,408 86,-616 -300,160 -474,-979 -4,-63 -824,-689 -765,-219 802,-54 -93,191 -982,-723 -449),'
                            'MULTILINESTRING((683 4,864 -634,548 -891,727 -691,-570 32,-334 -438,127 -317,241 -12,-807 947,-987 693,-345 -867,854 -106)))'),
ST_GEOMFROMTEXT(
'MULTIPOINT(157 69,-725 -189,-176 -41,676 375,33 -672,-76 47)')),
ST_UNION(
ST_ENVELOPE(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                                'LINESTRING(-896 100,-793 810,243 -525,650 -373,599 170,-554 -890),'
                                'POINT(945 -828),'
                                'POINT(945 -828),'
                                'LINESTRING(-896 100,-793 810,243 -525,650 -373,599 170,-554 -890),'
                                'POINT(945 -828),'
                                'MULTIPOINT(-47 307,-768 -425,-3 167,-170 30,-784 721,951 146,407 790,37 850,-466 738),'
                                'GEOMETRYCOLLECTION(),'
                                'MULTIPOINT(-47 307,-768 -425,-3 167,-170 30,-784 721,951 146,407 790,37 850,-466 738),'
                                'MULTIPOLYGON(((104 113,688 423,-859 602,272 978, 104 113)),'
                                             '((981 -394,189 -400,649 -325,-977 371,30 859,590 318,329 -894,-51 262,197 952,-846 -139,-920 399, 981 -394)),'
                                             '((-236 -759,834 757,857 747,437 -146,194 913,316 862,976 -491,-745 933,610 687,-149 -164,-803 -565,451 -275, -236 -759)),'
                                             '((572 96,-160 -607,529 930,-544 -132,458 294, 572 96))))')),
ST_CENTROID(ST_GEOMFROMTEXT('POINT(-939 -921)')))))),
ST_INTERSECTION(
ST_GEOMFROMTEXT(
'MULTILINESTRING((541 -927,-414 316,-429 -444,212 260,-125 104,445 563,-713 -975,-976 514),'
                            '(-830 882,-377 914,-915 919,-535 -23,-508 979),'
                            '(806 347,-87 220,226 -22,-12 468,707 598,83 951,-592 701,833 964,270 -932,743 -514,231 469,-575 -122,-99 -245,416 465,801 -587))'),
ST_GEOMFROMTEXT(
'LINESTRING(-96 -182,-373 75,697 687,-881 -463,-557 -959,-493 810)')));
######################################################################################################
# BUG#20085563 : ASSERTION `!NULL_VALUE && OPDONE && STR_VALUE_ARG->LENGTH() > 0' FAILED
######################################################################################################
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), 
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))')));
ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), 
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))')))
GEOMETRYCOLLECTION EMPTY
######################################################################################################
# BUG#20111542 : SET OPERATIONS RETURN GEOMETRYCOLLECTION CONTAINING SINGLE GEOMETRY COMPONENT
######################################################################################################
SELECT ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('MULTIPOINT(0 0,100 100)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),GEOMETRYCOLLECTION())')));
ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('MULTIPOINT(0 0,100 100)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),GEOMETRYCOLLECTION())')))
MULTIPOINT((0 0),(100 100))
# Invalid geometry in input
DO ST_ASTEXT(ST_DIFFERENCE(ST_GEOMFROMTEXT('MULTIPOLYGON(((4 4,4 6,6 6,6 4,4 4, 4 4)),((0 0,0 10,10 10,10 0,0 0,0 0, 0 0)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION()))')));
######################################################################################################
# BUG#20202913 : ASSERTION `DEPTH == GC_DEPTH' FAILED
######################################################################################################
# Invalid geometry in input
DO ST_EQUALS(ST_CONVEXHULL(
ST_UNION(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                                 'MULTIPOLYGON(((3 -7,9 8,7 7,-10 -8,0 7,10 1,1 -2,3 -8, 3 -7)),'
                                              '((-3 -4,-1 -4,-10 -3,-2 -8,0 3,9 -5,-7 6,-3 -9,-3 -7,6 -1,-3 4,0 -1,-5 0, -3 -4)),'
                                              '((-5 -7,8 -7,-6 -4,-10 10,3 -1, -5 -7))),'
                                  'MULTIPOINT(10 -3,-9 -6,-8 -4,3 -9,-6 10,-6 -10,-1 -5,9 -7,5 0,5 0),'
                                  'GEOMETRYCOLLECTION(),'
                                  'LINESTRING(-4 5,7 10,2 3,7 5,6 -8,1 9,4 -3,-3 0,-10 4,7 -3,-5 3,8 -9,-5 8,-2 2),'
                                  'POINT(4 9),MULTIPOINT(1 8,-8 5,-5 -3,1 -5,6 2,-1 -10))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))),ST_GEOMFROMTEXT('POLYGON((-10 -8,-6 -10,-1 -10,8 -9,9 -7,10 -3,10 1,9 8,7 10,-10 10,-10 -8))'));
# Invalid geometry in input
DO ST_ASTEXT(ST_UNION(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION()'),
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                             'LINESTRING(-10 -1,-2 -9,0 6,2 2,-1 -10,9 -4,6 3),'
                             'MULTIPOINT(10 8,-7 -1,-9 6),'
                             'GEOMETRYCOLLECTION('
                                 'MULTILINESTRING((3 0,8 -5,-6 5,-8 -2,5 1,-1 3),'
                                                 '(4 -1,7 -9,2 -10,10 0,-4 -6,-2 0,9 -5),'
                                                 '(-10 7,5 -5,2 8)),'
                                 'POINT(6 6),'
                                 'MULTIPOLYGON(((-7 -8,6 1,-10 -2,9 10,-6 4,-5 -7,8 2,-8 -9,-6 5,9 -2,9 -7,0 10,-8 -5,3 6,-2 7, -7 -8)),'
                                              '((-2 -5,3 -2,8 -9, -2 -5)),'
                                              '((10 -8,6 -4,0 -10,-10 -7,-5 0,-1 -1,5 -3,-5 9,5 -1,-3 -5, 10 -8)),'
                                              '((-1 10,2 7,-4 -1, -1 10))),'
                                 'GEOMETRYCOLLECTION(),'
                                 'MULTILINESTRING((3 0,8 -5,-6 5,-8 -2,5 1,-1 3),'
                                                 '(4 -1,7 -9,2 -10,10 0,-4 -6,-2 0,9 -5),'
                                                 '(-10 7,5 -5,2 8)),'
                                 'MULTILINESTRING((3 0,8 -5,-6 5,-8 -2,5 1,-1 3),'
                                                 '(4 -1,7 -9,2 -10,10 0,-4 -6,-2 0,9 -5),'
                                                 '(-10 7,5 -5,2 8))),'
                             'GEOMETRYCOLLECTION('
                                 'MULTIPOINT(4 -10,-3 -8,9 10,-2 5,0 -7,-8 -4,-2 8,-8 1,5 -2,6 3,4 -9,5 -8,5 3,-7 -7),'
                                 'POINT(-7 1),'
                                 'LINESTRING(4 1,4 -2,2 0)),POINT(-5 -4))')));
######################################################################################################
# BUG#20211491 : ASSERTION `RTREE_RESULT.SIZE() != 0' FAILED
######################################################################################################
SELECT ST_ASTEXT(ST_UNION(
ST_ENVELOPE(
ST_GEOMFROMTEXT(
'LINESTRING(5 9,-1 10,-2 -6,2 9,2 0,3 6,-3 3,9 -2,-3 -10,-7 -4,1 4)')),
ST_UNION(
ST_GEOMFROMTEXT(
'MULTILINESTRING((6 -8,10 -8,3 0,-6 1,0 8,-1 8,-3 -3,6 -6,0 6,1 -6,-1 7,8 3),'
                                                   '(-9 -10,-4 0,0 1,-9 1,6 9,-8 7,-2 -6,2 10,-1 -5,3 -5,-1 -10))'),
ST_GEOMFROMTEXT(
'MULTILINESTRING((8 7,2 6,-6 -8,-2 10,4 1,9 7,5 9,4 1,8 2,-2 10,8 -5))'))));
ST_ASTEXT(ST_UNION(
ST_ENVELOPE(
ST_GEOMFROMTEXT(
'LINESTRING(5 9,-1 10,-2 -6,2 9,2 0,3 6,-3 3,9 -2,-3 -10,-7 -4,1 4)')),
ST_UNION(
ST_GEOMFROMTEXT(
'MULTILINESTRING((6 -8,10 -8,3 0,-6 1,0 8,-1 8,-3 -3,6 -6,0 6,1 -6,-1 7,8 3),'
                           
GEOMETRYCOLLECTION(POLYGON((-7 -10,9 -10,9 10,-7 10,-7 -10)),LINESTRING(9 -8,10 -8,9 -6.857142857142858),LINESTRING(-9 -10,-7 -6),LINESTRING(-7 1,-9 1,-7 2.0666666666666664),LINESTRING(-7 7.142857142857142,-8 7,-7 4.833333333333334))
# Invalid geometry in input
DO ST_ASTEXT(ST_UNION(
ST_INTERSECTION(
ST_GEOMFROMTEXT(
'MULTIPOINT(7 2,-4 -5,6 -9,-7 3,-10 5,8 -6,0 -10,10 -4,8 -10,2 6,8 -4,-8 2,-4 2)'),
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((6 2,1 1,-4 5,1 4,-3 -4,-7 9,-10 2,-6 1,10 -7,0 1,9 4, 6 2)))')),
ST_UNION(
ST_GEOMFROMTEXT(
'LINESTRING(-1 -5,0 -6,4 6,3 3,2 8,-2 6,-4 5,6 -7,-1 -1,-8 6,4 -2)'),
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((5 -4,-5 -9,-1 -6,-3 0,5 -2, 5 -4)),'
                                                '((-5 -10,-8 -2,-3 7,1 5,5 -10,1 -5,0 10,3 2,1 1, -5 -10)),'
                                                '((4 -2,6 3,7 5,1 2,8 -9,-10 -5,7 -10,-2 -9,-2 0,2 -8,-8 3,5 0, 4 -2)),'
                                                '((6 -4,0 4,-8 -2,10 -10,-6 5, 6 -4)))'))));
SELECT ST_CONTAINS(ST_UNION(
ST_INTERSECTION(
ST_GEOMFROMTEXT(
'POINT(-3 3)'),
ST_GEOMFROMTEXT(
'POLYGON((8 3,-2 9,-10 2,-10 -9,7 -1,4 1,7 6,5 -10,5 3,2 1,-10 0, 8 3))')),
ST_CONVEXHULL(
ST_GEOMFROMTEXT(
'MULTIPOINT(8 -8,-7 5)'))),
ST_UNION(
ST_GEOMFROMTEXT('POINT(4 1)'),
ST_GEOMFROMTEXT('MULTIPOINT(-10 -10,5 -2,-6 -7,1 5,-3 0)')));
ST_CONTAINS(ST_UNION(
ST_INTERSECTION(
ST_GEOMFROMTEXT(
'POINT(-3 3)'),
ST_GEOMFROMTEXT(
'POLYGON((8 3,-2 9,-10 2,-10 -9,7 -1,4 1,7 6,5 -10,5 3,2 1,-10 0, 8 3))')),
ST_CONVEXHULL(
ST_GEOMFROMTEXT(
'MULTIPOINT(8 -8,-7 5)'))),
ST_UNION(
ST_GEOMFROMTEXT('POI
0
######################################################################################################
# BUG#20211639 : ASSERTION `LHS.DENOMINATOR() != 0' FAILED
######################################################################################################
# Invalid geometry in input
DO ST_ISEMPTY(ST_INTERSECTION(
ST_UNION(
ST_GEOMFROMTEXT('MULTIPOINT(-9 4,10 -4,3 -10,-6 5,2 -4,-4 -8,1 10,4 10,6 6,9 2,9 4,-10 5,-6 7)'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((3 5,-7 -2,-5 -1,1 -4,2 9,-9 -6,-6 10,-7 2,9 -2,-2 1,-1 4,-3 10, 3 5)),'
                                                       '((-8 4,-8 -1,0 8, -8 4)),'
                                                       '((0 7,-6 -1,6 -10,5 -2,7 2,9 4,-9 -4,2 10,-3 -7,2 -9,-7 -1,-8 9, 0 7)),'
                                                       '((-5 -5,-1 -9,1 3,5 -5,9 8,2 9,9 -1,6 -2,-2 6,1 -5,9 -8,-4 2,-6 0, -5 -5)))')),
ST_INTERSECTION(
ST_INTERSECTION(
ST_GEOMFROMTEXT('MULTIPOINT(0 -8,-5 1,8 5,9 -4,0 5,8 4,-6 7,6 9,10 0)'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((2 0,-3 5,1 -4,0 10,3 9,-2 -9,9 -9,3 6,-9 -5,8 7,-3 1, 2 0)))')),
ST_UNION(
ST_GEOMFROMTEXT('MULTIPOLYGON(((-1 -9,7 -7,-6 6,9 6,5 -8,9 -9,5 -8,5 3,-8 9,10 8,10 -8, -1 -9)),'
                                                       '((8 -3,-5 0,2 -4,-8 -5,4 10,10 -8,5 3,-8 -9,-7 -2,8 9,-3 -3,-10 7,-10 -8,-7 -10,-5 -9, 8 -3)),'
                                                       '((4 9,8 -7,1 -10,9 6,-4 8,-7 1,3 -3,8 2,-10 3,-8 8,-3 1,2 0, 4 9)))'),
ST_GEOMFROMTEXT('LINESTRING(0 -3,8 9,10 -3,-8 7,4 4,8 -8,10 6,5 7)')))));
# Invalid geometry in input
DO ST_ISEMPTY(ST_INTERSECTION(
ST_CONVEXHULL(
ST_GEOMFROMTEXT('POLYGON((0 10,-4 7,10 5,-8 -6,9 1,4 7,5 0,-3 -1,9 10,-7 -5, 0 10))')),
ST_UNION(
ST_GEOMFROMTEXT('MULTILINESTRING((1 3,10 -8,-5 -8,-10 8,-9 -10,-5 8,8 6,6 -6,5 -3,-3 -8,-6 1,7 8,-2 -2),'
                                                          '(5 -6,-6 -5,7 -5,8 6,0 7,-1 7,0 2,10 -7),'
                                                          '(-5 1,-10 -6,5 -3,1 -3,-3 -1,7 -1,-8 8,-4 -9,2 -3,4 -8,-3 5,-9 7,-10 9,-9 -9,5 -4))'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((-10 0,-9 -8,4 -6,-8 6,0 -1,1 0,5 2,8 -5, -10 0)),'
                                                       '((-9 10,4 -8,-9 5,7 -9,-5 9,3 7,-5 -7,-4 -7,1 -7,10 2, -9 10)),'
                                                       '((-2 6,-2 -6,-7 -5,1 4,7 -4,5 10,7 4,-6 -4,0 4,-3 0,0 2, -2 6)))'))));
SELECT ST_ISEMPTY(ST_UNION(
ST_INTERSECTION(
ST_UNION(
ST_GEOMFROMTEXT('LINESTRING(7 -3,-1 6,1 3,4 2,-1 7,3 -4,1 10,-2 3,4 9,6 -2)'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((-4 3,1 -6,5 -4,-3 1,-7 -6,4 4,4 -2,0 4,-7 -1,1 -2, -4 3)))')),
ST_UNION(
ST_GEOMFROMTEXT('MULTIPOINT(8 9,9 -1,0 3,10 6,-3 0,9 7,-10 -10,-8 -8,1 -3,-2 3,0 3,7 9,-4 7,-6 -6,6 6)'),
ST_GEOMFROMTEXT('MULTILINESTRING((-8 6,-4 0,-3 -8,-9 -4,-4 -10,-2 6,-4 6,4 10,0 1,2 3,-8 6,7 5,-1 7),'
                                                              '(2 7,4 7,6 -1,7 4,-9 3,3 7,-9 -7))'))),
ST_UNION(
ST_GEOMFROMTEXT('LINESTRING(-6 -6,-7 1,5 5,-1 0,5 5,-8 -6,10 -9,7 9,-2 -7,10 -1,-3 10)'),
ST_GEOMFROMTEXT('MULTILINESTRING((10 10,2 -9,-8 2,9 -2,-10 -4,10 7,-10 8,-4 7,-9 3,8 8))'))));
ST_ISEMPTY(ST_UNION(
ST_INTERSECTION(
ST_UNION(
ST_GEOMFROMTEXT('LINESTRING(7 -3,-1 6,1 3,4 2,-1 7,3 -4,1 10,-2 3,4 9,6 -2)'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((-4 3,1 -6,5 -4,-3 1,-7 -6,4 4,4 -2,0 4,-7 -1,1 -2, -4 3)))')),
ST_UNION(
ST_GEOMFROMTEXT('MULTIP
0
# Invalid geometry in input
DO ST_SRID(
ST_INTERSECTION(
ST_UNION(
ST_UNION(
ST_GEOMFROMTEXT(
'MULTILINESTRING((3 7,-8 8,5 10,-7 0,-6 -2,7 4,-9 9,-10 -6,9 -8),'
                                    '(9 7,-6 1,-8 8,-8 -1,10 -7,-7 -6,-1 -8,0 8,-8 10,5 -2,-3 7,-5 -5))'),
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((-5 2,-1 -4,7 -1,-4 1,-7 4,-8 2,-4 -5,5 -3,-10 -6,-3 6,-2 8,5 -7,-10 -9,8 -1,2 -5, -5 2)),'
                                 '((-3 -8,-3 -3,-8 -1,-5 -8,-5 -9,8 -3,-1 -2,8 -4, -3 -8)),'
                                 '((3 -4,7 -1,-6 -8,4 -5,4 8,9 -6, 3 -4)))')),
ST_CONVEXHULL(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                        'POLYGON((0 -1,10 5,-1 2,6 -1, 0 -1)),'
                        'POINT(-2 10),'
                        'POINT(-6 -1),'
                        'GEOMETRYCOLLECTION('
                            'MULTIPOLYGON(((3 -3,5 2,5 -1,-8 4,3 1,-9 0,9 -3,6 4,-3 1,-10 2,-10 -2,-2 5,0 -2, 3 -3)),'
                                         '((-6 8,2 4,-10 4,9 -10,4 5,-5 7,0 -5,8 -2,-8 -3,-7 1, -6 8)),'
                                         '((5 10,3 2,-2 7,-3 6,7 0,-1 9,-5 6,8 -2, 5 10))),'
                            'MULTILINESTRING((4 5,9 -10,10 10,2 -3,2 4,7 -8,6 6,-8 1,-6 -7,-7 -6,8 -4,-5 -5,0 9,10 -8)),'
                            'LINESTRING(7 9,8 3,-4 -2,-7 6,-9 -7,8 9,3 9,-4 -10,-10 9)))'))),
ST_UNION(
ST_CONVEXHULL(
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((6 -4,4 9,10 -4,3 -3,7 6,0 -10,-7 1,-2 -2,9 9,9 -8,6 -3,-3 3,-1 10,-3 4, 6 -4)),'
                    '((-6 2,2 1,-2 1,3 -7,0 -1,7 1,-4 -1,8 2,-9 8,-10 -10,-4 -8,9 6,1 -10,9 -8, -6 2)),'
                    '((-4 3,7 1,10 6,7 -5,-3 2,-2 6,-3 9,-7 -7,1 7,-3 10,9 5,-2 4,8 -3,-7 -9, -4 3)))')),
ST_ENVELOPE(
ST_UNION(
ST_CONVEXHULL(
ST_ENVELOPE(
ST_UNION(
ST_ENVELOPE(
ST_GEOMFROMTEXT(
'MULTIPOINT(-9 -10,2 -1,1 -2,-8 -1,-1 -2,-9 4,-6 -10,-6 9,4 0,-7 -8,8 -9,-2 -10)')),
ST_INTERSECTION(
ST_UNION(
ST_GEOMFROMTEXT(
'POINT(-10 3)'),
ST_GEOMFROMTEXT(
'POLYGON((-1 -2,7 -1,3 0,-7 9,0 10,4 -1,-5 1,4 9,-9 -2,-3 -5,4 -6,7 5,9 4,3 0, -1 -2))')),
ST_ENVELOPE(
ST_CONVEXHULL(
ST_GEOMFROMTEXT(
'MULTILINESTRING((-3 2,1 10,1 9,-10 -2,2 7,-6 -8,-5 -1,10 0,6 -3),'
                                                '(-2 8,-4 6,-2 9,-4 8,-6 -7,8 9,6 6,5 -3,5 0,-8 2,-7 8,-10 -9),'
                                                '(-7 -9,0 3,10 1,7 3,-6 -5,-5 -3,6 -5,5 -4,-2 3,7 9,0 -1,-3 -1,-4 0))'))))))),
ST_UNION(
ST_INTERSECTION(
ST_GEOMFROMTEXT(
'POLYGON((0 3,9 9,-2 1,-9 -8,7 8,3 -2,-2 4,2 1,3 -9,6 -6, 0 3))'),
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                                    'MULTIPOLYGON(((-7 -8,-5 -10,-10 4,2 8,-7 -6,-4 -4,1 -10,-9 3,5 -9, -7 -8)),'
                                                 '((2 10,2 4,-7 -4,-5 1,-10 10,9 6,-4 -5, 2 10)),'
                                                 '((2 6,-5 4,-5 -2,-2 -8,-1 -3,10 4,-2 5,3 -5,-9 -7, 2 6)),'
                                                 '((-10 9,3 10,-5 -9, -10 9)),'
                                                 '((7 -2,3 -10,-3 2,5 3, 7 -2))),'
                                    'POLYGON((9 3,5 2,-8 1,1 -10,-1 -6,-8 3,-4 5,-4 10,6 3,4 -4,9 -5,-5 -4,1 -6, 9 3)),'
                                    'LINESTRING(-9 -8,2 10,-3 6,-10 5,6 -10,2 -10),'
                                    'MULTIPOLYGON(((-9 5,8 0,3 -6,3 0,8 -4,1 -2,-4 7,-2 -1,5 -1,-2 -8, -9 5))),'
                                    'GEOMETRYCOLLECTION('
                                        'MULTIPOLYGON(((10 -8,-8 -5,8 -7,9 9, 10 -8)),'
                                                     '((-10 -6,-3 0,8 -9,5 -8,-9 -3,3 3,-4 -1, -10 -6)),'
                                                     '((4 7,8 0,7 0,-2 -3,8 -8,-3 -6,9 -10,-8 -8,-10 -10,-4 0,2 2,3 4,1 -4, 4 7)))),'
                                    'POINT(-6 3),'
                                    'GEOMETRYCOLLECTION('
                                        'POINT(5 -8),'
                                        'GEOMETRYCOLLECTION(),'
                                        'MULTILINESTRING((4 10,1 5,-6 -6,3 -9,1 0,4 -9),'
                                                        '(10 1,-4 -1,-3 -7,-1 -10,-6 -8,5 -9,2 4,0 -3,2 -1,9 -6,-3 5),'
                                                        '(-8 0,5 -3,0 4,-2 -1,-4 2,-7 1,-5 -10,0 5,-3 -9,3 0)),'
                                        'POINT(5 -8)),'
                                    'MULTIPOINT(-2 -9,10 -2,-5 2,8 -10,3 -1,-8 9,-6 8,5 -7,-4 1,10 -4,6 -1,-9 4,-6 -5),'
                                    'GEOMETRYCOLLECTION())')),
ST_INTERSECTION(
ST_GEOMFROMTEXT(
'LINESTRING(4 -3,2 -10,5 -5,8 5,8 -3,-4 -9,2 -2,-3 4,-7 5,-9 -10)'),
ST_GEOMFROMTEXT('MULTIPOINT(-7 10,-9 8,3 3,5 5,10 -8,10 -9)'))))))));
######################################################################################################
# BUG#20218936 : ASSERTION `(M_PREVIOUS_OPERATION != OVERLAY::OPERATION_CONTINUE)' FAILED
######################################################################################################
SELECT ST_CONTAINS(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
            'GEOMETRYCOLLECTION('
                'GEOMETRYCOLLECTION('
                    'MULTILINESTRING((8 1,5 6)),'
                    'GEOMETRYCOLLECTION('
                        'LINESTRING(6 0,5 5,0 7,2 9,2 1,8 7,5 0,4 0,4 0),'
                        'POLYGON((1 4,2 9,7 1,1 4)),'
                        'GEOMETRYCOLLECTION('
                            'GEOMETRYCOLLECTION('
                                'MULTILINESTRING((6 2,2 6),(7 1,4 6)),'
                                'MULTIPOLYGON(((0 0,5 8,6 1,6 3,8 1,5 4,9 6,2 5,7 4,1 7,0 0))),'
                                'LINESTRING(6 2,3 1,0 4,4 6,5 2,0 5,9 0,1 7,4 2)))),'
                    'POLYGON((2 5,6 6,5 1,2 2,4 0,3 3,1 4,9 1,4 5,0 7,2 5))),'
                'MULTIPOLYGON(((9 0,5 2,5 6,9 3,1 8,7 6,8 2,4 8,3 3,9 7,9 0))),'
                'POLYGON((8 7,5 3,9 4,0 9,8 1,5 3,7 5,1 0,1 4,3 2,8 7)),'
                'POLYGON((6 4,3 9,8 0,6 4)),'
                'MULTIPOINT(6 0,9 1,4 1,2 2,6 9,4 8,9 6,9 1,5 0),'
                'LINESTRING(2 9,3 7,2 2,0 1,0 7,9 8,6 8,9 5,7 6),'
                'MULTIPOINT(9 9,1 6,6 8,5 0,6 7,3 7,8 4,6 7,9 9),'
                'MULTIPOINT(5 0,2 7,1 1,5 0,4 5,9 9,8 9,8 9,1 4),'
                'POINT(2 1)))'),
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
            'GEOMETRYCOLLECTION('
                'GEOMETRYCOLLECTION('
                    'GEOMETRYCOLLECTION('
                        'GEOMETRYCOLLECTION('
                            'LINESTRING(6 3,9 0),'
                            'MULTILINESTRING((5 8,2 1),(2 6,9 7)),'
                            'LINESTRING(1 2,1 7,1 3,0 5,9 0,6 3,4 2,0 5,7 2)))),'
                'GEOMETRYCOLLECTION('
                    'GEOMETRYCOLLECTION('
                        'POINT(3 0),'
                        'MULTIPOINT(3 6,9 3),'
                        'MULTIPOINT(7 0,3 7,6 6,9 8,1 6,9 6,2 5,0 0,3 4))),'
                'LINESTRING(7 0,3 6,5 0,3 3,1 6,5 6,8 0,9 3,2 9),'
                'POINT(8 2),'
                'LINESTRING(8 1,1 1),'
                'LINESTRING(5 7,3 9),'
                'POINT(1 6),'
                'MULTIPOINT(0 1,0 1),'
                'MULTILINESTRING((3 8,5 3,1 0,8 4,2 7,8 2,2 6,9 1,9 1))))'));
ST_CONTAINS(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
            'GEOMETRYCOLLECTION('
                'GEOMETRYCOLLECTION('
                    'MULTILINESTRING((8 1,5 6)),'
                    'GEOMETRYCOLLECTION('
                        'LINESTRING(6 0
0
######################################################################################################
# BUG#16174580 : ST_OVERLAPS AND ST_INTERSECTS GIVE ERRONEOUS RESULTS
######################################################################################################
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 -10,10 -10,10 0,0 0))'));
ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 -10,10 -10,10 0,0 0))'))
0
SELECT ST_INTERSECTS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 -10,10 -10,10 0,0 0))'));
ST_INTERSECTS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 -10,10 -10,10 0,0 0))'))
1
######################################################################################################
# BUG#18293562 : GIS SPATIAL RELATION CHECK FUNCTIONS GIVES INCORRECT RESULTS
######################################################################################################
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(0 0,3 3)'), ST_GEOMFROMTEXT('LINESTRING(1 1,10 10)'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(0 0,3 3)'), ST_GEOMFROMTEXT('LINESTRING(1 1,10 10)'))
1
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(1 0,15 0,10 10)'), ST_GEOMFROMTEXT('MULTILINESTRING((15 0,20 0,20 20),(10 10,20 20,15 0))'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(1 0,15 0,10 10)'), ST_GEOMFROMTEXT('MULTILINESTRING((15 0,20 0,20 20),(10 10,20 20,15 0))'))
1
SELECT ST_INTERSECTS(ST_GEOMFROMTEXT('LINESTRING(15 10,10 0)'), ST_GEOMFROMTEXT('POINT(15 10)'));
ST_INTERSECTS(ST_GEOMFROMTEXT('LINESTRING(15 10,10 0)'), ST_GEOMFROMTEXT('POINT(15 10)'))
1
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('LINESTRING(15 5,15 25)'), ST_GEOMFROMTEXT('LINESTRING(15 5,15 25)'));
ST_TOUCHES(ST_GEOMFROMTEXT('LINESTRING(15 5,15 25)'), ST_GEOMFROMTEXT('LINESTRING(15 5,15 25)'))
0
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,5 0,5 5,0 5,0 0),(1 1,3 1,3 3,1 3,1 1))'), ST_GEOMFROMTEXT('LINESTRING(3 3,10 10)'));
ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,5 0,5 5,0 5,0 0),(1 1,3 1,3 3,1 3,1 1))'), ST_GEOMFROMTEXT('LINESTRING(3 3,10 10)'))
0
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('POLYGON((0 0,5 0,5 5,0 5,0 0))'), ST_GEOMFROMTEXT('LINESTRING(1 2,5 5)'));
ST_CONTAINS(ST_GEOMFROMTEXT('POLYGON((0 0,5 0,5 5,0 5,0 0))'), ST_GEOMFROMTEXT('LINESTRING(1 2,5 5)'))
1
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(1 0,15 0,10 10)'), ST_GEOMFROMTEXT('LINESTRING(15 0,20 0,10 10,20 20)'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(1 0,15 0,10 10)'), ST_GEOMFROMTEXT('LINESTRING(15 0,20 0,10 10,20 20)'))
1
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(1 0,15 0,10 10)'), ST_GEOMFROMTEXT('MULTILINESTRING((15 0,20 0,20 20),(10 10,20 20,15 0))'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(1 0,15 0,10 10)'), ST_GEOMFROMTEXT('MULTILINESTRING((15 0,20 0,20 20),(10 10,20 20,15 0))'))
1
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(1 0,15 0,10 10)'), ST_GEOMFROMTEXT('MULTILINESTRING((15 0,20 0,20 20,15 0))'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(1 0,15 0,10 10)'), ST_GEOMFROMTEXT('MULTILINESTRING((15 0,20 0,20 20,15 0))'))
1
######################################################################################################
# BUG#20170591 : ST_CROSSES(MULTIPOINT, POLYGON) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 10)'), ST_GEOMFROMTEXT('POLYGON((0 0,5 0,5 5,0 5, 0 0))'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 10)'), ST_GEOMFROMTEXT('POLYGON((0 0,5 0,5 5,0 5, 0 0))'))
0
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 10,20 20)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 10,20 20)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'))
0
######################################################################################################
# BUG#20179082 : ST_TOUCHES() RETURNS INCORRECT RESULT WITH GEOMRTRYCOLLECTION GEOMETRY
######################################################################################################
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('POINT(0 0)'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'));
ST_TOUCHES(ST_GEOMFROMTEXT('POINT(0 0)'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'))
NULL
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLlECTION(POINT(0 0))'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLlECTION(POINT(0 0))'), ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLlECTION(POINT(0 0))'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'));
ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLlECTION(POINT(0 0))'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'))
NULL
######################################################################################################
# BUG#20187460 : OVERLAPPING GEOMETRY COMPONENTS OF SAME TYPE ARE NOT GETTING MERGED PROPERLY
######################################################################################################
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((4 4,4 6,6 6,6 4,4 4,4 4)),POLYGON((5 5,5 7,7 7,7 5,5 5)))')));
ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((4 4,4 6,6 6,6 4,4 4,4 4)),POLYGON((5 5,5 7,7 7,7 5,5 5)))')))
POLYGON((5 6,4 6,4 4,6 4,6 5,7 5,7 7,5 7,5 6))
######################################################################################################
# BUG#20188574 : ST_WITHIN(MULTIPOINT, POLYGON) RETURNS WRONG RESULT
######################################################################################################
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 5,10 10)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 5,10 10)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,3 3)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,3 3)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'))
1
######################################################################################################
# BUG#20234376 : ST_CONTAINS(POLYGON, MULTIPOINT) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(3 3,13 13)'));
ST_CONTAINS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(3 3,13 13)'))
0
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((5 0,0 5,10 5,5 0)),((5 0,0 -5,10 -5,5 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(5 0)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((5 0,0 5,10 5,5 0)),((5 0,0 -5,10 -5,5 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(5 0)'))
0
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(5 2,15 14)'));
ST_CONTAINS(ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(5 2,15 14)'))
0
######################################################################################################
# BUG#20235165 : ST_EQUALS() FUNCTION RETURNS WRONG RESULT
######################################################################################################
SELECT ST_EQUALS(ST_GEOMFROMTEXT('MULTIPOINT(2 2,3 3)'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(2 2),POINT(3 3))'));
ST_EQUALS(ST_GEOMFROMTEXT('MULTIPOINT(2 2,3 3)'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(2 2),POINT(3 3))'))
1
######################################################################################################
# BUG#20240519 : ST_TOUCHES(POLYGON, MULTIPOINT) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(0 0)'));
ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(0 0)'))
1
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 10)'));
ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 10)'))
1
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 10,10 10)'));
ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 10,10 10)'))
1
######################################################################################################
# BUG#20240934 : ST_OVERLAPS() RETURNS INCORRECT RESULT WITH GEOMETRYCOLLECTION GEOMETRY
######################################################################################################
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 -10,10 10,0 0))'));
ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 -10,10 10,0 0))'))
1
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 -10,10 10,0 0))'));
ST_OVERLAPS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 -10,10 10,0 0))'))
1
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 -10,10 10,0 0)))'));
ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 -10,10 10,0 0)))'))
1
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 -10,10 10,0 0)))'));
ST_OVERLAPS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 -10,10 10,0 0)))'))
1
######################################################################################################
# BUG#20253600 : ST_CROSSES() RETURNS INCORRECT RESULT WITH GEOMETRYCOLLECTION GEOMETRY
######################################################################################################
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(2 2,14 14)'), ST_GEOMFROMTEXT('LINESTRING(0 0,4 4)'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(2 2,14 14)'), ST_GEOMFROMTEXT('LINESTRING(0 0,4 4)'))
1
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(2 2,14 14)'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,4 4))'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(2 2,14 14)'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,4 4))'))
1
SELECT ST_CROSSES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(2 2,14 14))'), ST_GEOMFROMTEXT('LINESTRING(0 0,4 4)'));
ST_CROSSES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(2 2,14 14))'), ST_GEOMFROMTEXT('LINESTRING(0 0,4 4)'))
1
SELECT ST_CROSSES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(2 2,14 14))'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,4 4))'));
ST_CROSSES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(2 2,14 14))'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,4 4))'))
1
######################################################################################################
# BUG#20303050 : ST_TOUCHES() RETURNS WRONG RESULT WITH GEOMRTRYCOLLECTION GEOMETRY
######################################################################################################
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('LINESTRING(0 0,2 2)'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_TOUCHES(ST_GEOMFROMTEXT('LINESTRING(0 0,2 2)'), ST_GEOMFROMTEXT('POINT(0 0)'))
1
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,2 2))'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,2 2))'), ST_GEOMFROMTEXT('POINT(0 0)'))
1
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('LINESTRING(0 0,2 2)'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'));
ST_TOUCHES(ST_GEOMFROMTEXT('LINESTRING(0 0,2 2)'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'))
1
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,2 2))'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'));
ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,2 2))'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'))
1
######################################################################################################
# BUG#20308837 : SPATIAL RELATION CHECK FUNCTIONS RETURN NULL VALUE
######################################################################################################
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'), ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
SELECT ST_DISJOINT(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_DISJOINT(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
SELECT ST_INTERSECTS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_INTERSECTS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
SELECT ST_EQUALS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_EQUALS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_WITHIN(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
SELECT ST_CROSSES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_CROSSES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_OVERLAPS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),GEOMETRYCOLLECTION())'), ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION()))'), ST_GEOMFROMTEXT('POINT(0 0)'));
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION()))'), ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
######################################################################################################
# BUG#20309003 : ST_CONTAINS(GEOMETRYCOLLECTION(POLY,POLY), MULTIPOINT) RETURNS WRONG RESULT
######################################################################################################
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((5 0,0 10,10 10,5 0,5 0,5 0,5 0)),((5 0,0 -10,10 -10,5 0,5 0,5 0,5 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(5 2,5 -2)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((5 0,0 10,10 10,5 0,5 0,5 0,5 0)),((5 0,0 -10,10 -10,5 0,5 0,5 0,5 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(5 2,5 -2)'))
1
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)),((0 0,0 -5,-5 -5,-5 0,0 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(4 2,-4 -2)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)),((0 0,0 -5,-5 -5,-5 0,0 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(4 2,-4 -2)'))
1
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 5,5 5,5 0,0 0)),POLYGON((0 0,0 -5,-5 -5,-5 0,0 0)))'),
ST_GEOMFROMTEXT('MULTIPOINT(4 2,-4 -2)'));
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 5,5 5,5 0,0 0)),POLYGON((0 0,0 -5,-5 -5,-5 0,0 0)))'),
ST_GEOMFROMTEXT('MULTIPOINT(4 2,-4 -2)'))
1
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((5 0,0 10,10 10,5 0,5 0,5 0,5 0)),POLYGON((5 0,0 -10,10 -10,5 0,5 0,5 0,5 0)))'),
ST_GEOMFROMTEXT('MULTIPOINT(5 2,5 -2)'));
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((5 0,0 10,10 10,5 0,5 0,5 0,5 0)),POLYGON((5 0,0 -10,10 -10,5 0,5 0,5 0,5 0)))'),
ST_GEOMFROMTEXT('MULTIPOINT(5 2,5 -2)'))
1
######################################################################################################
# BUG#20315283 : ST_TOUCHES(LINESTRING, MULTIPOINT) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('LINESTRING(0 0,5 0,10 0)'), ST_GEOMFROMTEXT('MULTIPOINT(10 0)'));
ST_TOUCHES(ST_GEOMFROMTEXT('LINESTRING(0 0,5 0,10 0)'), ST_GEOMFROMTEXT('MULTIPOINT(10 0)'))
1
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('LINESTRING(0 0,5 0,10 0)'), ST_GEOMFROMTEXT('MULTIPOINT(0 0,10 0)'));
ST_TOUCHES(ST_GEOMFROMTEXT('LINESTRING(0 0,5 0,10 0)'), ST_GEOMFROMTEXT('MULTIPOINT(0 0,10 0)'))
1
######################################################################################################
# BUG#20316115 : ST_OVERLAPS(POLYGON, POLYGON) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'));
ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'))
1
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'));
ST_OVERLAPS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'))
1
######################################################################################################
# BUG#20328959 : ST_EQUALS(POLYGON, GEOMETRYCOLLECTION(POLYGON, POLYGON)) RETURNS WRONG RESULT
######################################################################################################
SELECT ST_EQUALS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4)),POLYGON((4 4,4 6,6 6,4 4)))'));
ST_EQUALS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4)),POLYGON((4 4,4 6,6 6,4 4)))'))
0
SELECT ST_EQUALS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4)),POLYGON((4 4,6 4,6 6,4 4)))'));
ST_EQUALS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4)),POLYGON((4 4,6 4,6 6,4 4)))'))
0
######################################################################################################
# BUG#20329124 : ST_WITHIN(MULTIPOINT, GEOMETRYCOLLECTION(POLYGON,POLYGON)) RETURNS WRONG RESULT
######################################################################################################
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(4 4,5 5)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4)),POLYGON((4 4,4 6,6 6,6 4,4 4,4 4)))'));
ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(4 4,5 5)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4)),POLYGON((4 4,4 6,6 6,6 4,4 4,4 4)))'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(4 4,3 3)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4)),POLYGON((4 4,4 6,6 6,6 4,4 4,4 4)))'));
ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(4 4,3 3)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4)),POLYGON((4 4,4 6,6 6,6 4,4 4,4 4)))'))
1
######################################################################################################
# BUG#20303208 : ST_WITHIN(POLYGON, POLYGON) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))'));
ST_WITHIN(ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2),(6 6,6 8,8 8,8 6,6 6))'))
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2))'));
ST_WITHIN(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2))'))
0
######################################################################################################
# BUG#20234206 : ST_CONTAINS(MULTIPOLYGON, LINESTRING) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((5 0,0 5,10 5,5 0)),((5 0,0 -5,10 -5,5 0)))'), ST_GEOMFROMTEXT('LINESTRING(5 -2,5 2)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((5 0,0 5,10 5,5 0)),((5 0,0 -5,10 -5,5 0)))'), ST_GEOMFROMTEXT('LINESTRING(5 -2,5 2)'))
1
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((5 0,0 5,10 5,5 0)),((5 0,0 -5,10 -5,5 0)))'), ST_GEOMFROMTEXT('LINESTRING(5 -2,5 0)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((5 0,0 5,10 5,5 0)),((5 0,0 -5,10 -5,5 0)))'), ST_GEOMFROMTEXT('LINESTRING(5 -2,5 0)'))
1
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((5 0,0 5,10 5,5 0)),((5 0,0 -5,10 -5,5 0)))'), ST_GEOMFROMTEXT('LINESTRING(5 -2,5 5)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((5 0,0 5,10 5,5 0)),((5 0,0 -5,10 -5,5 0)))'), ST_GEOMFROMTEXT('LINESTRING(5 -2,5 5)'))
1
######################################################################################################
# BUG#20356527 : ST_CONTAINS(MULTIPOLYGON, POLYGON) GIVES INCORRECT RESULT
######################################################################################################
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'))
1
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'))
1
######################################################################################################
# BUG#20356548 : ST_OVERLAPS(MULTIPOLYGON, POLYGON) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'));
ST_OVERLAPS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'))
0
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'));
ST_OVERLAPS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'))
0
######################################################################################################
# BUG#20384407 : ST_INTERSECTS(POLYGON, MULTILINESTRING) RETURNS WRONG RESULT
######################################################################################################
SELECT ST_INTERSECTS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2))'), ST_GEOMFROMTEXT('MULTILINESTRING((11 11,20 20),(5 7, 4 1))'));
ST_INTERSECTS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(2 2,2 4,4 4,4 2,2 2))'), ST_GEOMFROMTEXT('MULTILINESTRING((11 11,20 20),(5 7, 4 1))'))
1
SELECT ST_INTERSECTS(ST_GEOMFROMTEXT('POLYGON((5 0,0 5,-5 0,0 -5,5 0))'), ST_GEOMFROMTEXT('MULTILINESTRING((10 0, 18 12),(2 2, 2 1))'));
ST_INTERSECTS(ST_GEOMFROMTEXT('POLYGON((5 0,0 5,-5 0,0 -5,5 0))'), ST_GEOMFROMTEXT('MULTILINESTRING((10 0, 18 12),(2 2, 2 1))'))
1
######################################################################################################
# BUG#20390414 : ST_CROSSES(MULTILINESTRING, POLYGON) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTILINESTRING((6 6,15 15),(0 0,7 7))'), ST_GEOMFROMTEXT('POLYGON((5 5,5 15,15 15,15 5,5 5))'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTILINESTRING((6 6,15 15),(0 0,7 7))'), ST_GEOMFROMTEXT('POLYGON((5 5,5 15,15 15,15 5,5 5))'))
1
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTILINESTRING((15 15,6 6),(0 0,7 7))'), ST_GEOMFROMTEXT('POLYGON((5 5,5 15,15 15,15 5,5 5))'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTILINESTRING((15 15,6 6),(0 0,7 7))'), ST_GEOMFROMTEXT('POLYGON((5 5,5 15,15 15,15 5,5 5))'))
1
######################################################################################################
# BUG#20393099 : ST_CONTAINS(POLYGON, LINESTRING) RETURNS WRONG RESULT
######################################################################################################
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,5 5,0 0))'), ST_GEOMFROMTEXT('LINESTRING(3 7, 9 1)'));
ST_CONTAINS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,5 5,0 0))'), ST_GEOMFROMTEXT('LINESTRING(3 7, 9 1)'))
0
######################################################################################################
# BUG#20393159 : ST_CONTAINS(MULTIPOLYGON, MULTIPOINT) GIVES WRONG RESULT
######################################################################################################
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,5 5,0 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(2 9, 1 0)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,5 5,0 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(2 9, 1 0)'))
1
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,5 5,0 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(7 4, 8 0)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,5 5,0 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(7 4, 8 0)'))
1
######################################################################################################
# BUG#20398307 : ST_DISJOINT(LINESTRING, LINESTRING) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_DISJOINT(ST_GEOMFROMTEXT('LINESTRING(5 5,10 10)'), ST_GEOMFROMTEXT('LINESTRING(6 6,3 3)'));
ST_DISJOINT(ST_GEOMFROMTEXT('LINESTRING(5 5,10 10)'), ST_GEOMFROMTEXT('LINESTRING(6 6,3 3)'))
0
SELECT ST_DISJOINT(ST_GEOMFROMTEXT('LINESTRING(5 5,2 8)'), ST_GEOMFROMTEXT('LINESTRING(4 6,7 3)'));
ST_DISJOINT(ST_GEOMFROMTEXT('LINESTRING(5 5,2 8)'), ST_GEOMFROMTEXT('LINESTRING(4 6,7 3)'))
0
######################################################################################################
# BUG#20400252 : ST_TOUCHES(MULTIPOLYGON, LINESTRING) GIVES INCORRECT RESULT
######################################################################################################
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('LINESTRING(5 5,0 0,10 0)'));
ST_TOUCHES(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('LINESTRING(5 5,0 0,10 0)'))
0
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('LINESTRING(5 5,0 0,0 10)'));
ST_TOUCHES(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('LINESTRING(5 5,0 0,0 10)'))
0
######################################################################################################
# BUG#20400420 : ST_CONTAINS(MULTIPOLYGON, LINESTRING) GIVES INCORRECT RESULT
######################################################################################################
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('LINESTRING(5 5,0 0,10 0)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('LINESTRING(5 5,0 0,10 0)'))
1
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('LINESTRING(5 5,0 0,0 10)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((0 0,0 -10,-10 -10,-10 0,0 0)))'),
ST_GEOMFROMTEXT('LINESTRING(5 5,0 0,0 10)'))
1
######################################################################################################
# BUG#20409558 : ST_WITHIN(MULTILINESTRING, POLYGON) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTILINESTRING((4 10, 3 10, 10 6),(5 0, 7 5, 9 10))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,5 5,0 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('MULTILINESTRING((4 10, 3 10, 10 6),(5 0, 7 5, 9 10))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,5 5,0 0))'))
1
######################################################################################################
# BUG#20410030 : ST_CROSSES(LINESTRING, POLYGON) RETURNS WRONG RESULT
######################################################################################################
SELECT ST_CROSSES(ST_GEOMFROMTEXT('LINESTRING(3 10, 1 5, 1 10, 3 4, 7 8, 6 10, 10 2)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'));
ST_CROSSES(ST_GEOMFROMTEXT('LINESTRING(3 10, 1 5, 1 10, 3 4, 7 8, 6 10, 10 2)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'))
0
######################################################################################################
# BUG#20414100 : ST_WITHIN(LINESTRING, POLYGON) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_WITHIN(ST_GEOMFROMTEXT('LINESTRING(2 9, 1 1, 10 1, 10 10, 1 10, 0 6, 5 6)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4))'));
ST_WITHIN(ST_GEOMFROMTEXT('LINESTRING(2 9, 1 1, 10 1, 10 10, 1 10, 0 6, 5 6)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4))'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('LINESTRING(2 3, 4 5, 0 6, 5 6)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4))'));
ST_WITHIN(ST_GEOMFROMTEXT('LINESTRING(2 3, 4 5, 0 6, 5 6)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4))'))
1
######################################################################################################
# BUG#20414126 : ST_WITHIN(MULTILINESTRING, LINESTRING) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTILINESTRING((2 5, 7 5, 8 3, 6 3, 4 0),(0 0,10 10))'), ST_GEOMFROMTEXT('LINESTRING(1 1, 5 5, 4 4)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTILINESTRING((2 5, 7 5, 8 3, 6 3, 4 0),(0 0,10 10))'), ST_GEOMFROMTEXT('LINESTRING(1 1, 5 5, 4 4)'))
1
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTILINESTRING((2 5, 7 5, 8 3, 6 3, 4 0),(4 0, 4 8, 0 4))'), ST_GEOMFROMTEXT('LINESTRING(4 1, 4 5, 4 4)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTILINESTRING((2 5, 7 5, 8 3, 6 3, 4 0),(4 0, 4 8, 0 4))'), ST_GEOMFROMTEXT('LINESTRING(4 1, 4 5, 4 4)'))
1
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTILINESTRING((5 0,5 5,5 10),(0 0,10 10))'), ST_GEOMFROMTEXT('LINESTRING(1 1,5 5,4 4)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTILINESTRING((5 0,5 5,5 10),(0 0,10 10))'), ST_GEOMFROMTEXT('LINESTRING(1 1,5 5,4 4)'))
1
######################################################################################################
# More GIS crashes/assertion failure test cases
######################################################################################################
SELECT ST_INTERSECTS(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT(
'MULTIPOINT(1 0,8.5 0,5 1,8.3 4,7 -5.0)'),
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((3 8,6 1,9.5 4,5 2,9 2,-1032.34324 -1032.34324,4 -4.6,4 2,3 8),'
                                           '(5 9,2 4,6 -.8,4 +.0,-0 1,8 9.6,5 9),'
                                           '(8 5,3 4,3 1,0 1,6 -.5,8 -7.0,8 5),'
                                           '(8 6,0 1,9 5,7 5,5 3,2.232432 0,8 6)),'
                                          '((5 1,7 7,-1032.34324 7,8 -1,5 0,5 1),'
                                           '(2 9,2.232432 +.6,2 8,4 -4.4,-1032.34324 9,9 +.6,8 1,-.0 1,1 2,7 2,6 -5,2 1,-2 9,2 9),'
                                           '(7 8,0 4,7 8)),'
                                          '((5 0,7 1,5 0)),'
                                          '((3 9,8 5,0 -1032.34324,7 +.9,3 9),'
                                           '(9 9,7 2,0.4 7,9 9),'
                                           '(1 4,4 0,0 2,6.9 6,2.232432 -.8,7 3,1 4)))')),
ST_UNION(ST_GEOMFROMTEXT('MULTILINESTRING((-.5 9,1 1,1 6,6 6),(6 -.5,6 -.2,6 9,2 4),(4 7),(8 7,5 5,9 3,8 -.3))'),
ST_GEOMFROMTEXT('POINT()')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_TOUCHES(ST_INTERSECTION(
ST_GEOMFROMTEXT(
'POLYGON((6 9,0 0,10.0002 -6,6 9),'
                                  '(5 7,2 7,9 6,9 9,2 0,3 7,+.7 1,2.232432 9,8 +.3,5 7),'
                                  '(0 6,5 9,-.0 0,7 4,2 -3,2 7,9 2,0 6))'),
ST_GEOMFROMTEXT('POINT(7 8)',0)),
ST_DIFFERENCE(
ST_GEOMFROMWKB(
ST_ASWKB(MULTIPOINT(POINT(-3.4,2),POINT(9,5),POINT(8,9),POINT(-.0,-.7),POINT(8,1.8),POINT(-5.0,9),POINT(4,3),
POINT(8,-9.9),POINT(1,9),POINT(+.7,3),POINT(1,1),POINT(1,0),POINT(+.3,.7),POINT(4,7),POINT(2,-.0)))),
ST_GEOMFROMWKB(ST_ASWKB(POINT(0,2)))));
ST_TOUCHES(ST_INTERSECTION(
ST_GEOMFROMTEXT(
'POLYGON((6 9,0 0,10.0002 -6,6 9),'
                                  '(5 7,2 7,9 6,9 9,2 0,3 7,+.7 1,2.232432 9,8 +.3,5 7),'
                                  '(0 6,5 9,-.0 0,7 4,2 -3,2 7,9 2,0 6))'),
ST_GEOMF
NULL
SELECT ST_CONTAINS(ST_UNION(
ST_GEOMFROMWKB(ST_ASWKB(
GEOMETRYCOLLECTION(
POINT(1,6),
MULTIPOLYGON(
POLYGON(LINESTRING(POINT(6,3),POINT(3,0),POINT(2.7,2),POINT(2,8),POINT(1,5.8),POINT(6,3))),
POLYGON(LINESTRING(POINT(2,4),POINT(0,9),POINT(2,4)),
LINESTRING(POINT(5,4),POINT(5,1),POINT(5,4)))),
MULTIPOLYGON(
POLYGON(LINESTRING(POINT(1,9),POINT(-.9,.3),POINT(1,9)),
LINESTRING(POINT(1,9),POINT(1,1.6),POINT(8,5),POINT(7,2),POINT(6,9.9),POINT(1,2),
POINT(0,0),POINT(7,3),POINT(2,6),POINT(8,3),POINT(4,-.8),POINT(1,9)),
LINESTRING(POINT(1,8),POINT(3,3),POINT(+.5,9),POINT(-9.3,0),POINT(8,5),POINT(+.4,+.2),
POINT(7,+.6),POINT(7,7),POINT(2,4),POINT(2,2),POINT(6,0),POINT(1,8)),
LINESTRING(POINT(6,0),POINT(8,2),POINT(0,1),POINT(-.9,.9),POINT(6,0)),
LINESTRING(POINT(0,8),POINT(1,9),POINT(2,3),POINT(3,2),POINT(4,-3.2),POINT(7,6),POINT(4,9),
POINT(3,5),POINT(7,-.0),POINT(1,2),POINT(4,-.8),POINT(-2.3,7),POINT(9,4),POINT(0,8)),
LINESTRING(POINT(3,6),POINT(5,7),POINT(2,2),POINT(3,2),POINT(7,1),POINT(2,2),
POINT(3,9),POINT(5,7),POINT(8,2),POINT(5,6),POINT(3,6))),
POLYGON(LINESTRING(POINT(0,3),POINT(3,-.4),POINT(-.2,3),POINT(5,4.6),POINT(-2.7,8),POINT(0,3))))))),
ST_GEOMFROMTEXT('POINT(-1032.34324 -.9)')),
ST_UNION(
ST_GEOMFROMWKB(
ST_ASWKB(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                                       'MULTILINESTRING((5 5,6 3,+.8 -8,8 3),(2 2,1 3,8 4)),'
                                       'MULTIPOINT(2 3,9 3,9 7,+.2 0,1 2),'
                                       'MULTIPOLYGON(((2 1,2.232432 7.2,-0.7654 2,3 9,9 9,1 0,-0.7654 5,2 1),'
                                                     '(8 2,2 1,8 2),'
                                                     '(3 2,8.5 3,+.7 2,7 5,2 1,1 6,1 7,-9.4 6,3 2),'
                                                     '(5 9,7 +.5,5 9),'
                                                     '(6 8,-0.7654 1,6 8),'
                                                     '(0 9,9 0,5 4,6 -0.7654,-5 1,0 9))))'))),
ST_GEOMFROMTEXT('MULTIPOLYGON(((5 2,1 -0.7654,-6.3 9,6 3,2 6,7 6,4 -0.7654,2 +.2,-0.7654 9,5 2)))')));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT MBROVERLAPS(ST_INTERSECTION(
ST_GEOMFROMWKB(ST_ASWKB(
GEOMETRYCOLLECTION(
MULTILINESTRING(
LINESTRING(POINT(3,3)),
LINESTRING(POINT(3,5)),
LINESTRING(POINT(8,0),POINT(6,0),POINT(9,7),POINT(5,6),POINT(7,5),POINT(-5.6,4)),
LINESTRING(POINT(7,1),POINT(5,9),POINT(5,0),POINT(2,1),POINT(1,5),POINT(5,+.8),POINT(-.8,9),POINT(0,3),POINT(2.0,3)),
LINESTRING(POINT(2,8),POINT(0,2),POINT(.5,-.2),POINT(2,1)),
LINESTRING(POINT(9,8.8),POINT(-.3,.1),POINT(2,2),POINT(2,8))),
POLYGON(
LINESTRING(POINT(6,8),POINT(4,1),POINT(6,8),POINT(-.2,2),POINT(6,6.3),POINT(3,7),POINT(4,7.1),POINT(6,8))),
MULTILINESTRING(
LINESTRING(POINT(9,9)))))),
ST_GEOMFROMWKB(
ST_ASWKB(
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((6 4,3 3,-8.4 6,-.0 4,6 4)))')))),
ST_DIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(3.2 2)'),
ST_GEOMFROMTEXT('LINESTRING(7 3,-2 10.0002,1 6,3 7)',0)));
ERROR 22023: Invalid GIS data provided to function linestring.
# Invalid geometry in input
DO ST_ASTEXT(ST_INTERSECTION(
ST_GEOMFROMTEXT('POLYGON((6 8, 4 1, 6 8, -0.2 2,6 6.3, 3 7, 4 7.1, 6 8))'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((6 4,3 3,-8.4 6,-.0 4,6 4)))')));
SELECT ST_DISJOINT(ST_GEOMFROMTEXT(
'MULTIPOINT(189.7654 2)'),
ST_GEOMFROMWKB(ST_ASWKB(
GEOMETRYCOLLECTION(
MULTIPOINT(POINT(7,5),POINT(5,-.5),POINT(0,2),POINT(6.9,0)),
POLYGON(
LINESTRING(POINT(4,0),POINT(5,8),POINT(6.3,7),POINT(5,-.4),POINT(4,0))),
POLYGON(
LINESTRING(POINT(7,6),POINT(2,8),POINT(7,3),POINT(1,3),POINT(0,4),POINT(7,+.0),POINT(4,5),POINT(3,6),POINT(7,6)),
LINESTRING(POINT(2,5),POINT(4,7),POINT(6,5),POINT(4,-1.0),POINT(9,0),POINT(-1.9,-.6),POINT(9,0),POINT(2,5))),
MULTIPOINT(POINT(9,5),POINT(2,-0.7),POINT(6,1),POINT(1,1)),
LINESTRING(POINT(-.3,4),POINT(4,9),POINT(0,+.9),POINT(6.8,3),POINT(4,3),POINT(1,0.3)),
MULTIPOLYGON(
POLYGON(
LINESTRING(POINT(7,2),POINT(0,5),POINT(7,2))),
POLYGON(
LINESTRING(POINT(9,5),POINT(2,0),POINT(9,5)),
LINESTRING(POINT(8,9),POINT(0,-6.9),POINT(5,3),POINT(0,1),POINT(7,0.9),POINT(3,0),POINT(+.4,0),POINT(3,6),POINT(8,9))),
POLYGON(
LINESTRING(POINT(0,2),POINT(7,3),POINT(5,0),POINT(0,1),POINT(1,0),POINT(7,3),POINT(6,3),POINT(2,5),POINT(0,2))),
POLYGON(
LINESTRING(POINT(8,9),POINT(8,1),POINT(-.9,-.8),POINT(4,6),POINT(8,9)),
LINESTRING(POINT(2,8),POINT(6,7),POINT(8,+.2),POINT(5,-6.1),POINT(4,5),POINT(4,-.2),
POINT(1,5),POINT(-3.6,5),POINT(6,3),POINT(2,8),POINT(7,1.2),POINT(2,8))))))));
ERROR 22023: Invalid GIS data provided to function polygon.
# Invalid geometry in input
DO ST_ASTEXT(ST_INTERSECTION(
ST_GEOMFROMWKB(ST_ASWKB(
GEOMETRYCOLLECTION(
POINT(7,7),
POLYGON(
LINESTRING(POINT(4,9),POINT(5,6),POINT(9,-.9),POINT(8,8),POINT(1,1),POINT(5,5),
POINT(8,-0.8),POINT(7,4),POINT(0,+.8),POINT(0,8),POINT(5,8),POINT(4,9))),
POLYGON(
LINESTRING(POINT(2,2),POINT(3,9.5),POINT(2.2,1),POINT(-6.1,0),POINT(1,9),POINT(2,2)),
LINESTRING(POINT(0,8),POINT(0,0),POINT(3,-8.6),POINT(0,7),POINT(2,-.9),POINT(0,9),POINT(8,0),POINT(7,0),POINT(0,8))),
POLYGON(
LINESTRING(POINT(3,2),POINT(8,8),POINT(0,9),POINT(4,7.9),POINT(3,4),POINT(2,0),POINT(0,0),POINT(4,6.0),POINT(3,2))),
LINESTRING(POINT(5,2),POINT(9,2),POINT(7,5),POINT(5,1)),
MULTIPOINT(POINT(1,8),POINT(.6,8),POINT(.8,3))))),
ST_GEOMFROMTEXT(
'POLYGON((3 6,2 3,2 8,3 -1.6,4 -0.7654,3 6))')));
SELECT MBREQUALS(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT(
'POINT(5 0)'),
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((8 1,5 8,5 -5.7,9 8,5 9,8 1),'
                                        '(7 5,5 7.6,0 2,3 7,0 5,2 9,7 5)),'
                                       '((2 4,6 0,+.8 7,3 2,-4 8,2 -0,+.2 1,2 4)))')),
ST_UNION(ST_GEOMFROMWKB(
ST_ASWKB(
ST_GEOMFROMTEXT(
'POLYGON((7 3,2 7,7 3),'
                                              '(2 4,6 2,1 7,-2.7 5,0 4,-3.4 3,3 8,2 4),'
                                              '(8 0,189.7654 10.0002,9 1.1,1 0,6 -2.6,4 10.0002,1 8,8 0))'))),
ST_GEOMFROMTEXT('LINESTRING(6 1)')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_INTERSECTS(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT(
'MULTIPOINT(1 0,8.5 0,5 1,8.3 4,7 -5.0)'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((3 8,6 1,9.5 4,5 2,9 2,-1032.34324 -1032.34324,4 -4.6,4 2,3 8),'
                                                       '(5 9,2 4,6 -.8,4 +.0,-0 1,8 9.6,5 9),'
                                                       '(8 5,3 4,3 1,0 1,6 -.5,8 -7.0,8 5),'
                                                       '(8 6,0 1,9 5,7 5,5 3,2.232432 0,8 6)),'
                                                      '((5 1,7 7,-1032.34324 7,8 -1,5 0,5 1),'
                                                       '(2 9,2.232432 +.6,2 8,4 -4.4,-1032.34324 9,9 +.6,8 1,-.0 1,1 2,7 2,6 -5,2 1,-2 9,2 9),'
                                                       '(7 8,0 4,7 8)),'
                                                      '((5 0,7 1,5 0)),'
                                                      '((3 9,8 5,0 -1032.34324,7 +.9,3 9),(9 9,7 2,0.4 7,9 9),(1 4,4 0,0 2,6.9 6,2.232432 -.8,7 3,1 4)))')),
ST_UNION(
ST_GEOMFROMTEXT(
'MULTILINESTRING((-.5 9,1 1,1 6,6 6),(6 -.5,6 -.2,6 9,2 4),(4 7),(8 7,5 5,9 3,8 -.3))'),
ST_GEOMFROMTEXT('POINT()')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_INTERSECTS(ST_GEOMFROMWKB(ST_ASWKB(ST_GEOMFROMTEXT('MULTIPOLYGON(((4 4,9 10.0002,4 4),(5 0,2 6,5 0)))'))),
ST_GEOMFROMWKB(ST_ASWKB(MULTIPOINT(POINT(8,9)))));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('POINT(10 10)')));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT('POINT(10 10)')))
POINT(10 10)
SET @star_top= 'POINT(15 25)';
SET @star_of_elems='MULTIPOLYGON(((5 0,15 25,25 0,15 5,5 0)),((25 0,0 15,30 15,22 10,25 0)))';
SELECT ST_ASTEXT(ST_GEOMFROMTEXT(@star_of_elems));
ST_ASTEXT(ST_GEOMFROMTEXT(@star_of_elems))
MULTIPOLYGON(((5 0,15 25,25 0,15 5,5 0)),((25 0,0 15,30 15,22 10,25 0)))
SELECT ST_AREA(ST_GEOMFROMTEXT(@star_of_elems));
ST_AREA(ST_GEOMFROMTEXT(@star_of_elems))
377.5
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT(@star_of_elems)));
ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT(@star_of_elems)))
POLYGON((0 0,30 0,30 25,0 25,0 0))
SELECT ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT(@star_of_elems)));
ST_ASTEXT(ST_CENTROID(ST_GEOMFROMTEXT(@star_of_elems)))
POINT(15.64459161147903 10.20971302428256)
SELECT ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT(@star_of_elems)));
ST_ASTEXT(ST_CONVEXHULL(ST_GEOMFROMTEXT(@star_of_elems)))
POLYGON((0 15,5 0,25 0,30 15,15 25,0 15))
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT(@star_of_elems), 1));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT(@star_of_elems), 1))
POLYGON((15 3.882,24.5528 -0.8944,24.6028 -0.9139,24.6518 -0.9374,24.6896 -0.9476,24.7257 -0.9616,24.778 -0.9714,24.8301 -0.9855,24.8695 -0.9885,24.908 -0.9958,24.9608 -0.9957,25.014 -0.9999,25.0538 -0.9956,25.0935 -0.9956,25.1449 -0.9859,25.1976 -0.9803,25.2364 -0.9687,25.2758 -0.9612,25.3241 -0.9424,25.3743 -0.9273,25.4108 -0.9085,25.4486 -0.8937,25.4922 -0.8665,25.5384 -0.8427,25.5711 -0.8173,25.6059 -0.7955,25.6434 -0.761,25.6841 -0.7294,25.7119 -0.698,25.7424 -0.6699,25.7726 -0.6295,25.8065 -0.5913,25.8283 -0.5549,25.8534 -0.5213,25.8754 -0.4764,25.9014 -0.433,25.9163 -0.3928,25.935 -0.3547,25.9482 -0.3069,25.9656 -0.26,25.973 -0.2173,25.9844 -0.1759,25.9885 -0.127,25.9969 -0.0781,25.9963 -0.0343,26 0.009,25.995 0.0573,25.9943 0.1064,25.9856 0.1499,25.9811 0.1935,25.9674 0.2397,25.9578 0.2873,23.1771 9.5564,30.53 14.152,30.6766 14.2636,30.8002 14.4002,30.8966 14.5572,30.9626 14.7292,30.996 14.9104,30.9955 15.0947,30.9612 15.2757,30.8944 15.4473,30.7971 15.6038,30.6728 15.7398,30.5257 15.8507,30.3607 15.9327,30.1835 15.983,30 16,19.677 16,15.9285 25.3714,15.8453 25.5343,15.7339 25.6793,15.5979 25.8015,15.442 25.897,15.2712 25.9625,15.0914 25.9958,14.9086 25.9958,14.7288 25.9625,14.558 25.897,14.4021 25.8015,14.2661 25.6793,14.1547 25.5343,14.0715 25.3714,10.323 16,0 16,-0.1847 15.9828,-0.3631 15.9317,-0.529 15.8486,-0.6766 15.7363,-0.801 15.5986,-0.8978 15.4404,-0.9637 15.2669,-0.9964 15.0843,-0.9949 14.8988,-0.959 14.7167,-0.8902 14.5445,-0.7907 14.3878,-0.664 14.2523,-0.5145 14.1425,7.6262 9.2581,4.0715 0.3714,4.0189 0.1935,4 0.009,4.0156 -0.1759,4.065 -0.3547,4.1466 -0.5213,4.2576 -0.6699,4.3941 -0.7955,4.5514 -0.8937,4.7242 -0.9612,4.9065 -0.9956,5.092 -0.9958,5.2743 -0.9616,5.4472 -0.8944,15 3.882))
SET @buf = ST_BUFFER(ST_GEOMFROMTEXT(@star_of_elems), 1);
SELECT st_area(@buf);
st_area(@buf)
373.469403743631
SELECT ST_ASTEXT(ST_BUFFER(@buf, 1));
ST_ASTEXT(ST_BUFFER(@buf, 1))
POLYGON((15 2.7639,24.1056 -1.7889,24.1905 -1.8265,24.2045 -1.8319,24.2187 -1.8387,24.3274 -1.8791,24.3272 -1.8797,24.3634 -1.8937,24.3774 -1.8977,24.3917 -1.903,24.4294 -1.9132,24.4296 -1.9126,24.5417 -1.9446,24.5556 -1.9472,24.5699 -1.951,24.6856 -1.971,24.6855 -1.9715,24.7241 -1.9787,24.7377 -1.98,24.7518 -1.9824,24.7912 -1.9855,24.7912 -1.9849,24.9088 -1.9958,24.922 -1.9957,24.9358 -1.9968,25.0546 -1.9952,25.0546 -1.9956,25.0943 -1.9956,25.1069 -1.9944,25.1203 -1.9942,25.1601 -1.99,25.16 -1.9894,25.279 -1.9783,25.291 -1.976,25.3038 -1.9746,25.4217 -1.9509,25.4218 -1.9513,25.4612 -1.9439,25.4726 -1.9406,25.4847 -1.9382,25.5235 -1.9265,25.5234 -1.926,25.6395 -1.8927,25.6501 -1.8886,25.6615 -1.8852,25.7744 -1.8396,25.7745 -1.84,25.8123 -1.8252,25.822 -1.8204,25.8327 -1.8161,25.8691 -1.7973,25.8689 -1.7969,25.9781 -1.742,25.9869 -1.7365,25.9967 -1.7315,26.1004 -1.6652,26.1006 -1.6655,26.1355 -1.6438,26.1434 -1.6378,26.1522 -1.6321,26.1849 -1.6067,26.1847 -1.6063,26.283 -1.5314,26.29 -1.525,26.2979 -1.5188,26.3887 -1.4337,26.389 -1.4339,26.4196 -1.4058,26.4256 -1.3991,26.4325 -1.3926,26.4603 -1.3612,26.46 -1.3609,26.5438 -1.2681,26.5489 -1.2613,26.5549 -1.2545,26.6294 -1.1529,26.6297 -1.1531,26.6548 -1.1194,26.659 -1.1126,26.6641 -1.1056,26.6859 -1.0693,26.6855 -1.0691,26.7515 -0.9611,26.7548 -0.9543,26.759 -0.9473,26.8141 -0.8325,26.8144 -0.8327,26.8331 -0.7945,26.8356 -0.7879,26.8389 -0.781,26.8538 -0.7408,26.8535 -0.7407,26.8989 -0.6211,26.9006 -0.6147,26.9031 -0.608,26.9366 -0.4836,26.9368 -0.4837,26.9483 -0.4423,26.9494 -0.4362,26.9511 -0.4298,26.9585 -0.387,26.9582 -0.387,26.9809 -0.2597,26.9814 -0.254,26.9824 -0.2479,26.9926 -0.1181,26.9928 -0.1181,26.9964 -0.0748,26.9964 -0.0696,26.9968 -0.0639,26.9962 -0.0201,26.9959 -0.0201,26.9948 0.1107,26.9943 0.1154,26.9942 0.1207,26.9802 0.2515,26.9804 0.2515,26.9759 0.2952,26.9751 0.2994,26.9746 0.3042,26.9658 0.3476,26.9656 0.3476,26.94 0.4772,26.9389 0.4808,26.9381 0.4851,26.9157 0.5747,24.3542 9.1129,31.06 13.304,31.1359 13.3564,31.2824 13.4681,31.4181 13.5927,31.5417 13.7293,31.6522 13.8767,31.7487 14.0337,31.8302 14.1989,31.8962 14.3709,31.9461 14.5483,31.9795 14.7295,31.996 14.913,31.9955 15.0972,31.9781 15.2806,31.9438 15.4616,31.893 15.6387,31.8261 15.8104,31.7437 15.9752,31.6465 16.1317,31.5352 16.2785,31.4109 16.4145,31.2746 16.5385,31.1275 16.6493,30.9707 16.7462,30.8057 16.8282,30.6339 16.8946,30.4567 16.945,30.2756 16.9788,30.0921 16.9957,30 17,20.3541 17,16.857 25.7428,16.8191 25.8261,16.736 25.989,16.6383 26.1436,16.5268 26.2886,16.4026 26.4228,16.2667 26.5451,16.1201 26.6544,15.9641 26.7499,15.8001 26.8307,15.6294 26.8962,15.4533 26.9458,15.2735 26.9791,15.0914 26.9958,14.9086 26.9958,14.7265 26.9791,14.5467 26.9458,14.3706 26.8962,14.1999 26.8307,14.0359 26.7499,13.8799 26.6544,13.7333 26.5451,13.5974 26.4228,13.4732 26.2886,13.3617 26.1436,13.264 25.989,13.1809 25.8261,13.143 25.7428,9.6459 17,0 17,-0.0928 16.9957,-0.2775 16.9785,-0.4598 16.9442,-0.6382 16.8932,-0.8111 16.8258,-0.9769 16.7427,-1.1344 16.6445,-1.2821 16.5322,-1.4187 16.4067,-1.543 16.269,-1.6541 16.1204,-1.7509 15.9621,-1.8326 15.7955,-1.8985 15.6221,-1.948 15.4433,-1.9808 15.2607,-1.9964 15.0758,-1.9948 14.8903,-1.9761 14.7057,-1.9402 14.5237,-1.8877 14.3457,-1.8188 14.1735,-1.7343 14.0083,-1.6349 13.8517,-1.5213 13.705,-1.3946 13.5694,-1.2559 13.4462,-1.1063 13.3364,-1.029 13.285,6.3814 8.8388,3.143 0.7428,3.1126 0.6551,3.06 0.4772,3.0241 0.2952,3.0052 0.1107,3.0036 -0.0748,3.0191 -0.2597,3.0517 -0.4423,3.1011 -0.6211,3.1669 -0.7945,3.2485 -0.9611,3.3452 -1.1194,3.4562 -1.2681,3.5804 -1.4058,3.717 -1.5314,3.8645 -1.6438,4.0219 -1.742,4.1877 -1.8252,4.3605 -1.8927,4.5388 -1.9439,4.721 -1.9783,4.9057 -1.9956,5.0912 -1.9958,5.2759 -1.9787,5.4583 -1.9446,5.6366 -1.8937,5.8095 -1.8265,5.8944 -1.7889,15 2.7639))
SELECT ST_ASTEXT(ST_CENTROID(@buf));
ST_ASTEXT(ST_CENTROID(@buf))
POINT(15.1182 10.7393)
SELECT ST_ASTEXT(ST_CONVEXHULL(@buf));
ST_ASTEXT(ST_CONVEXHULL(@buf))
POLYGON((-0.9964 15.0843,-0.9949 14.8988,-0.959 14.7167,4.065 -0.3547,4.1466 -0.5213,4.2576 -0.6699,4.3941 -0.7955,4.5514 -0.8937,4.7242 -0.9612,4.9065 -0.9956,5.092 -0.9958,25.014 -0.9999,25.0935 -0.9956,25.1976 -0.9803,25.2758 -0.9612,25.3743 -0.9273,25.4486 -0.8937,25.5384 -0.8427,25.6059 -0.7955,25.6841 -0.7294,25.7424 -0.6699,25.8065 -0.5913,25.8534 -0.5213,25.9014 -0.433,25.935 -0.3547,30.9626 14.7292,30.996 14.9104,30.9955 15.0947,30.9612 15.2757,30.8944 15.4473,30.7971 15.6038,30.6728 15.7398,30.5257 15.8507,15.5979 25.8015,15.442 25.897,15.2712 25.9625,15.0914 25.9958,14.9086 25.9958,14.7288 25.9625,14.558 25.897,14.4021 25.8015,-0.529 15.8486,-0.6766 15.7363,-0.801 15.5986,-0.8978 15.4404,-0.9637 15.2669,-0.9964 15.0843))
SELECT ST_ASTEXT(ST_ENVELOPE(@buf));
ST_ASTEXT(ST_ENVELOPE(@buf))
POLYGON((-0.9964394501011175 -0.9999013929047789,30.995979706343796 -0.9999013929047789,30.995979706343796 25.995811240678275,-0.9964394501011175 25.995811240678275,-0.9964394501011175 -0.9999013929047789))
SELECT ST_DISTANCE(ST_GEOMFROMTEXT(@star_of_elems),ST_GEOMFROMTEXT(@star_top)) as result;
result
0
SELECT ST_DISTANCE(ST_GEOMFROMTEXT(@star_top),ST_GEOMFROMTEXT(@star_of_elems)) as result;
result
0
######################################################################################################
# BUG#19584838 : ST_SYMDIFFERENCE: ASSERTION FAILED: T1->RESULT_RANGE
######################################################################################################
DO ST_SYMDIFFERENCE(
MULTIPOLYGON(
POLYGON(
LINESTRING(POINT(61, 58), POINT(53, -3), POINT(-91, -19), POINT(61, 58)),
LINESTRING(POINT(-70, -6), POINT(-70, -6)))),
LINESTRING(POINT(27, 6), POINT(19, 15), POINT(78, -36)));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT ST_SYMDIFFERENCE(
MULTIPOLYGON(
POLYGON(
LINESTRING(POINT(61, 58), POINT(53, -3), POINT(-91, -19), POINT(61, 58)),
LINESTRING(POINT(-70, -6), POINT(-70, -6)))),
LINESTRING(POINT(27, 6), POINT(19, 15), POINT(78, -36)));
ERROR 22023: Invalid GIS data provided to function polygon.
######################################################################################################
# BUG#18307923 : CRASH IN GCALC_OPERATION_REDUCER::GET_RESULT_THREAD
######################################################################################################
SELECT ST_ASTEXT(ST_SYMDIFFERENCE(
LINESTRING(POINT(0,0), POINT(POW(2,32),POW(2,32)), POINT(POW(2,32),70)),
ST_ENVELOPE(
LINESTRING(POINT(POW(2,64),POWER(2,64)), POINT(4294967211,0)))));
ST_ASTEXT(ST_SYMDIFFERENCE(
LINESTRING(POINT(0,0), POINT(POW(2,32),POW(2,32)), POINT(POW(2,32),70)),
ST_ENVELOPE(
LINESTRING(POINT(POW(2,64),POWER(2,64)), POINT(4294967211,0)))))
GEOMETRYCOLLECTION(POLYGON((4294967211 0,1.8446744073709552e19 0,1.8446744073709552e19 1.8446744073709552e19,4294967211 1.8446744073709552e19,4294967211 0)),LINESTRING(0 0,4294967211 4294967211))
######################################################################################################
# BUG#17535294 : ISSIMPLE() PRODUCING WRONG RESULTS W.R.T. SINGLE POINT POLYGON
######################################################################################################
# Invalid polygon in input
DO ST_ISSIMPLE(ST_POLYGONFROMTEXT('POLYGON((1 1, 1 1, 1 1, 1 1, 1 1, 1 1, 1 1, 1 1))'));
# Invalid polygon in input
DO ST_ISSIMPLE(ST_POLYGONFROMTEXT('POLYGON((1 1, 1 1, 1 1, 1 1, 1 1, 1 1, 1 1, 1 1, 1 1))'));
######################################################################################################
# BUG#17507827 : ISSIMPLE() FUNCTION PRODUCING WRONG RESULTS
######################################################################################################
SELECT ST_ISSIMPLE(ST_LINESTRINGFROMTEXT('LINESTRING(0 0,1 0,2 0,2 0, 2 1, 1.5 0)'));
ST_ISSIMPLE(ST_LINESTRINGFROMTEXT('LINESTRING(0 0,1 0,2 0,2 0, 2 1, 1.5 0)'))
0
SELECT ST_ISSIMPLE(ST_LINESTRINGFROMTEXT('LINESTRING(0 0,1 0,2 0,2 0,2 0, 2 1, 1.5 0)'));
ST_ISSIMPLE(ST_LINESTRINGFROMTEXT('LINESTRING(0 0,1 0,2 0,2 0,2 0, 2 1, 1.5 0)'))
0
######################################################################################################
# BUG#17376038 : MULTIPOINT'S PROPERTY OF BEING SIMPLE GEOMETRY, BREACHED
######################################################################################################
SELECT ST_ISSIMPLE(ST_MPOINTFROMWKB(ST_ASWKB(ST_MULTIPOINTFROMTEXT('MULTIPOINT(0 0, 0 0, 1 1, 2 2, 1 1)'))));
ST_ISSIMPLE(ST_MPOINTFROMWKB(ST_ASWKB(ST_MULTIPOINTFROMTEXT('MULTIPOINT(0 0, 0 0, 1 1, 2 2, 1 1)'))))
0
SELECT ST_ISSIMPLE(ST_MPOINTFROMWKB(ST_ASWKB(ST_MULTIPOINTFROMTEXT('MULTIPOINT(0 0, 0 0, 1 1, 2 2, 1 1)'))));
ST_ISSIMPLE(ST_MPOINTFROMWKB(ST_ASWKB(ST_MULTIPOINTFROMTEXT('MULTIPOINT(0 0, 0 0, 1 1, 2 2, 1 1)'))))
0
######################################################################################################
# BUG#20402476 : ASSERTION FAILURE IN ITEM_FUNC_SPATIAL_OPERATION::VAL_STR FUNCTION
######################################################################################################
SELECT ST_EQUALS(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(-10 -9,-4 -4)'),
ST_GEOMFROMTEXT('MULTIPOINT(2 6,2 6,8 0,6 4,5 9,0 -9,0 -10,9 1,10 8,5 -9)')),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(-10 -9,-4 -4),POINT(0 -10),POINT(0 -9),POINT(2 6),POINT(5 -9),POINT(5 9),POINT(6 4),POINT(8 0),POINT(9 1),POINT(10 8))'));
ST_EQUALS(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(-10 -9,-4 -4)'),
ST_GEOMFROMTEXT('MULTIPOINT(2 6,2 6,8 0,6 4,5 9,0 -9,0 -10,9 1,10 8,5 -9)')),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(-10 -9,-4 -4),POINT(0 -10),POINT(0 -9),POINT(2 6),POINT(5
1
SELECT ST_EQUALS(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('MULTIPOINT(2 6,2 6,8 0,6 4,5 9,0 -9,0 -10,9 1,10 8,5 -9)'),
ST_GEOMFROMTEXT('LINESTRING(-10 -9,-4 -4)')),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(-10 -9,-4 -4),POINT(0 -10),POINT(0 -9),POINT(2 6),POINT(5 -9),POINT(5 9),POINT(6 4),POINT(8 0),POINT(9 1),POINT(10 8))'));
ST_EQUALS(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('MULTIPOINT(2 6,2 6,8 0,6 4,5 9,0 -9,0 -10,9 1,10 8,5 -9)'),
ST_GEOMFROMTEXT('LINESTRING(-10 -9,-4 -4)')),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(-10 -9,-4 -4),POINT(0 -10),POINT(0 -9),POINT(2 6),POINT(5
1
######################################################################################################
# BUG#20133571 : ASSERTION `GRES->HAS_GEOM_HEADER_SPACE()' FAILED
######################################################################################################
######################################################################################################
# BUG#20120173 : ASSERTION FAILURE WITH GEOMETRY SET OPERATIONS IN ITEM_GEOFUNC.CC FILE
######################################################################################################
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((0 0,0 4,0 8,0 0))'), ST_GEOMFROMTEXT('POINT(0 0)')));
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POINT(0 0)'), ST_GEOMFROMTEXT('POLYGON((0 0,0 4,0 8,0 0))')));
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((0 0,0 4,0 8,0 0))'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))')));
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'), ST_GEOMFROMTEXT('POLYGON((0 0,0 4,0 8,0 0))')));
SELECT ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((0 0))'), ST_GEOMFROMTEXT('POINT(0 0)')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POINT(0 0)'), ST_GEOMFROMTEXT('POLYGON((0 0))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((0 0))'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'), ST_GEOMFROMTEXT('POLYGON((0 0))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 4,0 8,0 0)))'), ST_GEOMFROMTEXT('POINT(0 0)')));
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POINT(0 0)'), ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 4,0 8,0 0)))')));
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 4,0 8,0 0)))'), ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))')));
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'), ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 4,0 8,0 0)))')));
######################################################################################################
# BUG#20142402 : ASSERTION FAILURE IN GIS_GEOMETRY_COLLECTION::APPEND_GEOMETRY FUNCTION
######################################################################################################
# Invalid geometry in input
DO ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(6 -6,4 -2,5 -1,1 1,-10 10,-1 -10,-8 8,-4 -9,-4 -3,1 -9,5 -2,-4 0,3 5,0 -5)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                            'MULTIPOLYGON(((0 2,-5 -9,7 1,-10 2,-7 1,6 0,0 -4, 0 2)),'
                                         '((-6 1,-5 8,-1 -3,-7 -6,-2 -8, -6 1))),'
                            'GEOMETRYCOLLECTION('
                                  'MULTIPOLYGON(((3 8,-6 -2,-9 8,2 4,-10 -9,1 -1,8 7,-8 3,1 -1,-8 10,-2 -7,-1 -10,-8 -8,-7 3, 3 8)),'
                                               '((-3 -2,3 -1,-8 -7,-3 5,-10 5,-6 3,8 3,4 -5,-5 10,0 9,-3 2,9 -1,-1 -6,7 -2, -3 -2)),'
                                               '((3 -9,4 1,-5 5,-9 6,-8 4, 3 -9)),((-10 10,-10 1,-3 -2, -10 10)),'
                                               '((3 -7,-1 2,-8 -2,9 -6,0 5,-1 -2,-4 -8,2 -6,-5 -10,-1 4, 3 -7))),'
                                  'MULTILINESTRING((4 -7,10 -4,3 4,-9 -9,3 -2,-6 -4,4 10,-3 -3,-1 6,-5 -2,1 0,-1 -9,-8 9,-4 9,-3 -2),'
                                                  '(-5 -7,-3 9,-6 -8,-3 0,-4 0,8 7,-4 9,-4 -6,-7 -9,9 6,10 -1)),'
                                  'GEOMETRYCOLLECTION(),'
                                  'POLYGON((2 -7,-2 -7,-2 2,-5 -6, 2 -7)),'
                                  'LINESTRING(-3 -7,-8 -3),'
                                  'GEOMETRYCOLLECTION(),'
                                  'LINESTRING(-3 -7,-8 -3)),'
                            'GEOMETRYCOLLECTION('
                                  'MULTIPOLYGON(((-10 8,10 -3,-3 5,-5 -10,10 -9,-8 4,5 -10,1 -8,3 -1,-10 10,-4 -6,-9 -8,-9 -5,2 2, -10 8)),'
                                               '((8 10,4 8,1 5,0 -7,6 8,-3 -1,-2 7, 8 10)),'
                                               '((-1 -8,10 -10,4 4,7 0,2 7,-2 0,6 -6,-8 5,-1 -5,2 6,10 -8,5 -2,8 5,-4 4,0 -2, -1 -8)),'
                                               '((6 -4,7 10,-8 0,-6 -2,7 -3,7 -5,8 -9,-9 9, 6 -4)),'
                                               '((-4 -2,1 -8,-5 -2,1 7,-10 -5,-8 4,-2 8,-8 1, -4 -2))),'
                                  'MULTILINESTRING((0 -8,-5 4,-3 -6),'
                                                  '(-1 6,7 -10,8 4,10 9),'
                                                  '(-4 9,0 8,-5 6,7 7,-2 0),'
                                                  '(-10 4,-7 -8,-4 9,-6 5,-9 7,10 -2,7 10,0 3,2 -10,-3 9,-6 4)),'
                                  'POINT(10 -10),'
                                  'POINT(10 -10),'
                                  'MULTIPOINT(3 9,1 -3,-7 -1,8 -3),'
                                  'MULTILINESTRING((0 -8,-5 4,-3 -6),'
                                                  '(-1 6,7 -10,8 4,10 9),'
                                                  '(-4 9,0 8,-5 6,7 7,-2 0),'
                                                  '(-10 4,-7 -8,-4 9,-6 5,-9 7,10 -2,7 10,0 3,2 -10,-3 9,-6 4))),'
                            'MULTILINESTRING((-3 -1,-8 -5,4 10),'
                                            '(-5 10,-5 -9,4 0,8 3,5 9)),'
                            'MULTIPOINT(0 -2,9 2,-6 0,8 -5,1 -2,8 0,9 6,-9 1,-5 -6,1 9,-6 -5,4 3,-2 -6))')));
######################################################################################################
# BUG#20415950 : WL#7225 : ASSERTION FAILURE IN ITEM_GEOFUNC_SETOPS.CC FILE
######################################################################################################
# Invalid geometry in input
DO ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('MULTIPOLYGON(((2 -6,10 -4,1 8, 2 -6)),'
                                                              '((-3 1,-7 2,3 -6,-1 2,3 8,-5 9,-10 9,9 9,4 4,-2 2,-5 8, -3 1)))'),
ST_GEOMFROMTEXT('LINESTRING(-9 -6,7 -3,0 -5,-4 -9,-5 7,-7 7,8 -4,0 0,-5 2,2 8,1 -8,-5 3,-3 -9,-8 -2)')));
# Invalid geometry in input
DO ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('LINESTRING(-9 -6,7 -3,0 -5,-4 -9,-5 7,-7 7,8 -4,0 0,-5 2,2 8,1 -8,-5 3,-3 -9,-8 -2)'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((2 -6,10 -4,1 8, 2 -6)),'
                                                 '((-3 1,-7 2,3 -6,-1 2,3 8,-5 9,-10 9,9 9,4 4,-2 2,-5 8, -3 1)))')));
# Invalid geometry in input
DO ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('MULTIPOLYGON(((2 -6,10 -4,1 8, 2 -6)),'
                                                              '((-3 1,-7 2,3 -6,-1 2,3 8,-5 9,-10 9,9 9,4 4,-2 2,-5 8, -3 1)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                                                       'LINESTRING(-9 -6,7 -3,0 -5,-4 -9,-5 7,-7 7,8 -4,0 0,-5 2,2 8,1 -8,-5 3,-3 -9,-8 -2))')));
# Invalid geometry in input
DO ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                                                       'LINESTRING(-9 -6,7 -3,0 -5,-4 -9,-5 7,-7 7,8 -4,0 0,-5 2,2 8,1 -8,-5 3,-3 -9,-8 -2))'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((2 -6,10 -4,1 8, 2 -6)),'
                                                              '((-3 1,-7 2,3 -6,-1 2,3 8,-5 9,-10 9,9 9,4 4,-2 2,-5 8, -3 1)))')));
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMWKB(ST_ASWKB(ST_GEOMFROMTEXT('MULTIPOLYGON(((7 0,-0.7654 0,-.3 -0.7654,-3 -2,0 7,8 3,7 0)))'))),
ST_GEOMFROMWKB(ST_ASWKB(POLYGON(LINESTRING(POINT(4,9), POINT(0,2), POINT(-9.9,5), POINT(2.3,6), POINT(5,4), POINT(4,9)),
LINESTRING(POINT(3,2), POINT(5,-.3), POINT(3,2)),
LINESTRING(POINT(9,2), POINT(2.0,6), POINT(9,2)))))));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMWKB(ST_ASWKB(POLYGON(LINESTRING(POINT(4,9), POINT(0,2), POINT(-9.9,5), POINT(2.3,6), POINT(5,4), POINT(4,9)),
LINESTRING(POINT(3,2), POINT(5,-.3), POINT(3,2)),
LINESTRING(POINT(9,2), POINT(2.0,6), POINT(9,2))))),
ST_GEOMFROMWKB(ST_ASWKB(ST_GEOMFROMTEXT('MULTIPOLYGON(((7 0,-0.7654 0,-.3 -0.7654,-3 -2,0 7,8 3,7 0)))')))));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMWKB(ST_ASWKB(ST_GEOMFROMTEXT('MULTIPOLYGON(((7 0,-0.7654 0,-.3 -0.7654,-3 -2,0 7,8 3,7 0)))'))),
ST_GEOMFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(
POLYGON(LINESTRING(POINT(4,9), POINT(0,2), POINT(-9.9,5), POINT(2.3,6), POINT(5,4), POINT(4,9)),
LINESTRING(POINT(3,2), POINT(5,-.3), POINT(3,2)),
LINESTRING(POINT(9,2), POINT(2.0,6), POINT(9,2))))))));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(
POLYGON(LINESTRING(POINT(4,9), POINT(0,2), POINT(-9.9,5), POINT(2.3,6), POINT(5,4), POINT(4,9)),
LINESTRING(POINT(3,2), POINT(5,-.3), POINT(3,2)),
LINESTRING(POINT(9,2), POINT(2.0,6), POINT(9,2)))))),
ST_GEOMFROMWKB(ST_ASWKB(ST_GEOMFROMTEXT('MULTIPOLYGON(((7 0,-0.7654 0,-.3 -0.7654,-3 -2,0 7,8 3,7 0))))')))));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMWKB(ST_ASWKB(MULTIPOLYGON(
POLYGON(LINESTRING(POINT(5,3), POINT(+.5,7), POINT(5,3))),
POLYGON(LINESTRING(POINT(2,4), POINT(-.0,0), POINT(1,2), POINT(2,+.2),POINT(1,4),POINT(2,4)))),0)),
ST_GEOMFROMWKB(ST_ASWKB(POINT(5,8),0))));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMWKB(ST_ASWKB(POINT(5,8)),0),
ST_GEOMFROMWKB(ST_ASWKB(MULTIPOLYGON(
POLYGON(LINESTRING(POINT(5,3), POINT(+.5,7), POINT(5,3))),
POLYGON(LINESTRING(POINT(2,4),POINT(-.0,0), POINT(1,2), POINT(2,+.2), POINT(1,4), POINT(2,4)))),0))));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMWKB(ST_ASWKB(MULTIPOLYGON(
POLYGON(LINESTRING(POINT(5,3), POINT(+.5,7), POINT(5,3))),
POLYGON(LINESTRING(POINT(2,4), POINT(-.0,0), POINT(1,2), POINT(2,+.2),POINT(1,4),POINT(2,4)))),0)),
ST_GEOMFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(POINT(5,8),0)))));
ERROR 22007: Illegal non geometric '0' value found during parsing
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(POINT(5,8),0))),
ST_GEOMFROMWKB(ST_ASWKB(MULTIPOLYGON(
POLYGON(LINESTRING(POINT(5,3), POINT(+.5,7), POINT(5,3))),
POLYGON(LINESTRING(POINT(2,4), POINT(-.0,0), POINT(1,2), POINT(2,+.2),POINT(1,4),POINT(2,4)))),0))));
ERROR 22007: Illegal non geometric '0' value found during parsing
SELECT ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('MULTILINESTRING((9 2,8 -7,1 -7))'), ST_GEOMFROMTEXT('POINT(-6 8)')));
ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('MULTILINESTRING((9 2,8 -7,1 -7))'), ST_GEOMFROMTEXT('POINT(-6 8)')))
GEOMETRYCOLLECTION(MULTILINESTRING((9 2,8 -7,1 -7)),POINT(-6 8))
SELECT ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('POINT(-6 8)'), ST_GEOMFROMTEXT('MULTILINESTRING((9 2,8 -7,1 -7))')));
ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('POINT(-6 8)'), ST_GEOMFROMTEXT('MULTILINESTRING((9 2,8 -7,1 -7))')))
GEOMETRYCOLLECTION(MULTILINESTRING((9 2,8 -7,1 -7)),POINT(-6 8))
SELECT ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('POINT(7 10)'), ST_GEOMFROMTEXT('MULTILINESTRING((7 6,3 -4))')));
ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('POINT(7 10)'), ST_GEOMFROMTEXT('MULTILINESTRING((7 6,3 -4))')))
GEOMETRYCOLLECTION(MULTILINESTRING((7 6,3 -4)),POINT(7 10))
SELECT ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('MULTILINESTRING((7 6,3 -4))'), ST_GEOMFROMTEXT('POINT(7 10)')));
ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('MULTILINESTRING((7 6,3 -4))'), ST_GEOMFROMTEXT('POINT(7 10)')))
GEOMETRYCOLLECTION(MULTILINESTRING((7 6,3 -4)),POINT(7 10))
DO ST_ASTEXT(ST_UNION(
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((-8 -1,-6 -4,-5 10,-7 2,-4 -9,10 4,-2 -7,-4 -10, -8 -1, -8 -1)),'
                        '((-9 -8,3 -4,3 -3,-4 10,7 1,2 -10,-1 -1,-2 -9,-1 8,2 -7,-10 10,-10 4,-10 8,-6 -3,-3 4,-9 -8, -9 -8)))'),
ST_GEOMFROMTEXT(
'MULTIPOINT(1 -3,7 0,-2 0,-3 -6,-6 0,3 -7,-9 0)')));
DO ST_ASTEXT(ST_UNION(
ST_GEOMFROMTEXT(
'MULTIPOINT(1 -3,7 0,-2 0,-3 -6,-6 0,3 -7,-9 0)'),
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((-8 -1,-6 -4,-5 10,-7 2,-4 -9,10 4,-2 -7,-4 -10, -8 -1, -8 -1)),'
                         '((-9 -8,3 -4,3 -3,-4 10,7 1,2 -10,-1 -1,-2 -9,-1 8,2 -7,-10 10,-10 4,-10 8,-6 -3,-3 4,-9 -8, -9 -8)))')));
DO ST_ASTEXT(ST_UNION(
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((-8 -1,-6 -4,-5 10,-7 2,-4 -9,10 4,-2 -7,-4 -10, -8 -1, -8 -1)),'
                         '((-9 -8,3 -4,3 -3,-4 10,7 1,2 -10,-1 -1,-2 -9,-1 8,2 -7,-10 10,-10 4,-10 8,-6 -3,-3 4,-9 -8, -9 -8)))'),
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(MULTIPOINT(1 -3,7 0,-2 0,-3 -6,-6 0,3 -7,-9 0))')));
DO ST_ASTEXT(ST_UNION(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(MULTIPOINT(1 -3,7 0,-2 0,-3 -6,-6 0,3 -7,-9 0))'),
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((-8 -1,-6 -4,-5 10,-7 2,-4 -9,10 4,-2 -7,-4 -10, -8 -1)),'
                         '((-9 -8,3 -4,3 -3,-4 10,7 1,2 -10,-1 -1,-2 -9,-1 8,2 -7,-10 10,-10 4,-10 8,-6 -3,-3 4,-9 -8,-9 -8)))')));
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('MULTIPOLYGON(((1 5,-2 2,0 4,-9 10,5 2,-3 3,1 5,1 5)))'), ST_GEOMFROMTEXT('POLYGON((-6 -4,4 4,5 -5,-7 5,-8 2,6 -5, -6 -4))')));
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((-6 -4,4 4,5 -5,-7 5,-8 2,6 -5, -6 -4))'), ST_GEOMFROMTEXT('MULTIPOLYGON(((1 5,-2 2,0 4,-9 10,5 2,-3 3,1 5,1 5)))')));
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('MULTIPOLYGON(((1 5,-2 2,0 4,-9 10,5 2,-3 3,1 5,1 5)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((-6 -4,4 4,5 -5,-7 5,-8 2,6 -5, -6 -4)))')));
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((-6 -4,4 4,5 -5,-7 5,-8 2,6 -5, -6 -4)))'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((1 5,-2 2,0 4,-9 10,5 2,-3 3,1 5,1 5)))')));
######################################################################################################
# BUG#20420721 : WL#7225 : ASSERTION FAILURE WITH SET OPERATIONS IN ITEM_GEOFUNC_SETOPS.CC FILE
######################################################################################################
SELECT ST_ASTEXT(
ST_INTERSECTION(
ST_GEOMFROMWKB(ST_ASWKB(
POLYGON(LINESTRING(POINT(3,4),POINT(1,9),POINT(3,4)),
LINESTRING(POINT(5,1),POINT(1,6),POINT(2,3),POINT(2,3),POINT(2,9),POINT(0,1),POINT(4,4),POINT(5,1)),
LINESTRING(POINT(7,3),POINT(8,3),POINT(4,4),POINT(3,8),POINT(4.2,9),POINT(6,4),POINT(8,8),POINT(-.7,1),POINT(7,3))))),
ST_GEOMFROMWKB(ST_ASWKB(POINT(5,+.1)))));
ERROR 22023: Invalid GIS data provided to function polygon.
#invalid geometry as input
DO ST_ASTEXT(
ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                        'LINESTRING(8 -2,-2 -10,8 5),'
                        'MULTIPOINT(7 -6,9 1,-4 2,1 -8,-7 -4,1 0,5 0,-3 -8,-6 0,-6 2,5 -6,1 2,4 8,-5 -7),'
                        'POINT(5 2))'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((5 -1,-2 7,-9 6,-4 2,4 -7,-2 1,9 2,-7 9,6 0,8 0,-5 -9,-8 2,-6 4,10 2,5 -1)))')));
SELECT ST_ASTEXT(
ST_INTERSECTION(
ST_GEOMFROMWKB(ST_ASWKB(
MULTIPOLYGON(
POLYGON(LINESTRING(POINT(2,2),POINT(6,0),POINT(2,2)),
LINESTRING(POINT(4,8),POINT(9,4),POINT(7,-.7),POINT(4,7),POINT(4,8)))))),
ST_GEOMFROMWKB(
ST_ASWKB(
ST_GEOMFROMTEXT('MULTIPOINT(1 189.7654,41,-1032.34324 9,6.4 1,4 9)')))));
ERROR 22023: Invalid GIS data provided to function polygon.
#invalid geometry as input
DO ST_ASTEXT(
ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                        'GEOMETRYCOLLECTION(),'
                        'MULTIPOINT(-5 -2,7 -4,5 -10,4 1,6 -3,-2 4,6 9),'
                        'LINESTRING(8 4,0 -10,-10 10,-2 -9),'
                        'MULTILINESTRING((-3 1,-8 -7,-6 1,-7 0,-9 10,-3 -9,4 6,8 5),'
                                        '(7 -10,-10 10,-9 7,-8 9,-5 -8,-3 3,10 -1,8 1,-3 -7),'
                                        '(10 10,10 -2,-1 -10,-2 -2,-1 -1,-4 8,-9 9,-6 -9,10 -7,-4 5,8 9)))'),
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((8 2,8 10,1 10, 8 2)),'
                               '((-10 9,1 -5,7 10,8 -6,7 -9,5 5,-8 -3,10 -6,6 -2, -10 9)))')));
SELECT ST_ASTEXT(
ST_INTERSECTION(
ST_GEOMFROMTEXT('MULTIPOLYGON(((2 9,-2 9,-3 -5)),((-10 6,1 6,3 -4,-1 6)))'),
ST_GEOMFROMTEXT('MULTIPOINT(8 -2,-2 -5,9 -9,-4 -10,-9 -4,10 -3,9 10,-1 -5,-3 3)')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
# Invalid geometry in input.
DO ST_ASTEXT(
ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('MULTIPOLYGON(((-2 3,3 2,-7 4,-8 -4,-8 8,4 3,-10 -4,0 6,-4 7,-9 3,-6 0,-1 -8,-5 -3,2 -6,-7 -7, -2 3)),'
                                         '((-8 -9,-4 7,-5 5,6 -9,-6 -4,-1 6,-7 8,-2 0,1 -4,-9 -5,2 -3,0 6,8 -6, -8 -9)),'
                                         '((-4 -5,-10 8,-1 -1,-5 -7,-5 5,0 6,10 1,6 -9,8 -4,6 7,-10 2,10 -8,0 -7,-4 -5)),'
                                         '((4 -9,7 -5,3 -6, 4 -9)),'
                                         '((-8 -4,5 4,-3 8,-10 -2,-3 -3,5 -8,9 -8,3 7,8 -1,8 4,-8 -4)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                                  'LINESTRING(0 1,8 -9,-1 0,-9 -3,-9 10,-9 2,1 -4,-6 1,1 9,-9 -6,5 -7,3 10,2 -1,8 -4),'
                                  'POINT(4 8))')));
SELECT ST_ASTEXT(
ST_INTERSECTION(
ST_GEOMFROMTEXT('MULTILINESTRING((3 -.7),(4 -1032.34324),(2 1,8 7),(7 5,-4 8,2.2 8,2 8,6 7))'),
ST_GEOMFROMTEXT('POLYGON((5 2,+.1 0,5 2))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(
ST_INTERSECTION(
ST_GEOMFROMTEXT('POLYGON((5 3,6 7,-2.0 4,0 1,7 -0,5 3),(5 6,9 5,5 6))',0),ST_GEOMFROMTEXT('POINT(8 9)',0)));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(
ST_UNION(
ST_GEOMFROMWKB(ST_ASWKB(POLYGON(LINESTRING(POINT(9,0),POINT(7,1),POINT(9,0))))),
ST_GEOMFROMWKB(ST_ASWKB(ST_GEOMFROMTEXT('MULTIPOINT(8 9,-9.9 6,7 8,7 5,1 -4)')))));
ERROR 22023: Invalid GIS data provided to function polygon.
DO (SELECT ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                                                            'POINT(-6 -5),'
                                                            'LINESTRING(-6 -5,-6 -3,4 0,2 -7,-10 -3,0 -5,-10 -3,0 8,-6 -2,6 -9))'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((9 -3,-7 9,5 -7,9 -3)),((-7 9,8 -5,3 7,-3 -9,-4 -10,-1 9,-3 4,-4 10,1 5,0 3,-7 9)))'))));
SELECT ST_ASTEXT(ST_INTERSECTION(
ST_GEOMFROMWKB(ST_ASWKB(
MULTIPOLYGON(
POLYGON(
LINESTRING(POINT(1,5),POINT(-.1,6),POINT(6.9,2),POINT(1,5),POINT(9,-8.8),POINT(4,9),POINT(7,8),POINT(1,5)),
LINESTRING(POINT(2,2),POINT(0,-.3),POINT(2,2)),
LINESTRING(POINT(6,1),POINT(4,0.4),POINT(4,9),POINT(2,8),POINT(8,4),POINT(-9.8,-2.4),POINT(9.9,6),
POINT(1,7),POINT(7,6),POINT(6,0),POINT(6,1))))),0),
ST_GEOMFROMWKB(ST_ASWKB(
MULTILINESTRING(
LINESTRING(POINT(1,2),POINT(3,8),POINT(2,9),POINT(7,+.5)),
LINESTRING(POINT(2,0),POINT(9,6),POINT(7,4),POINT(.1,7),POINT(3,2),POINT(2.4,2),POINT(-1.1,5)),
LINESTRING(POINT(8,5.1),POINT(-7.2,5),POINT(-.6,-.7),POINT(5,8),POINT(-4.0,4),POINT(9,4),POINT(2,7),POINT(7,7),POINT(3,8),POINT(2,3),
POINT(8.8,7),POINT(.9,8),POINT(3,1),POINT(1,9),POINT(5,4),POINT(4,0),POINT(4,9),POINT(2,0),POINT(6,6)),
LINESTRING(POINT(8,8),POINT(+.8,+.1),POINT(+.6,-3.1),POINT(8,0),POINT(5,8),POINT(4,9),POINT(9,6)),
LINESTRING(POINT(1,7),POINT(7,7),POINT(0,4),POINT(5,8)),
LINESTRING(POINT(-.6,6)),
LINESTRING(POINT(7,3),POINT(3,0),POINT(2,8),POINT(7,3),POINT(7,2),POINT(0,5),POINT(9,0)))),0)));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT ST_ASTEXT(ST_INTERSECTION(
ST_GEOMFROMWKB(ST_ASWKB(
POLYGON(
LINESTRING(POINT(6,3),POINT(6,8),POINT(6,3)),
LINESTRING(POINT(9,3),POINT(0,-.1),POINT(8,2),POINT(2.4,1),POINT(9,2),POINT(8,1.9),POINT(1,0),
POINT(5,9),POINT(8.1,-.5),POINT(0,-4.3),POINT(8,3),POINT(9,3))))),
ST_GEOMFROMTEXT('POINT(0 +.3)')));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMWKB(ST_ASWKB(POLYGON(LINESTRING(POINT(2,8),POINT(2,5),POINT(2,8))),0)),
ST_GEOMFROMTEXT('LINESTRING(6 6,-8 9,-4 4,1 2)',0)));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT ST_ASTEXT(ST_INTERSECTION(
ST_GEOMFROMWKB(ST_ASWKB(
POLYGON(
LINESTRING(POINT(6,7),POINT(+.3,1),POINT(6,7))))),
ST_GEOMFROMWKB(ST_ASWKB(
LINESTRING(POINT(3,5),POINT(3,9),POINT(-2.5,-3.9),POINT(9,8),POINT(5,0),POINT(3,1),POINT(+.3,1.9),POINT(-.3,9),POINT(0,-3.3))))));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMWKB(ST_ASWKB(ST_GEOMFROMTEXT('MULTILINESTRING((5 7,0.8 2,9 3,5 7),(5 3,-3 1))'))),
ST_GEOMFROMWKB(ST_ASWKB(ST_GEOMFROMTEXT('POLYGON((1 5,+.8 -0.7654,1 5))')))));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
# Invalid geometry in input
DO ST_ASTEXT(
ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('POLYGON((0 4,-8 -1,1 9,0 4))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                            'GEOMETRYCOLLECTION(),'
                            'GEOMETRYCOLLECTION(),'
                            'MULTIPOINT(-7 -6,-6 -3,5 -1,-8 7,-5 -4,-3 7,7 -7,7 -8,5 -1),'
                            'MULTIPOLYGON(((10 -1,-10 -8,-5 9,10 -1))),'
                            'MULTILINESTRING((1 -8,1 -4,-10 -8,-4 5,-4 0,-10 5,6 0,6 3,5 7,10 -8,-4 -3,-8 4,-7 6,-5 -7),'
                                            '(10 9,4 4,-2 6,8 4,-4 1,2 3,-7 8,-6 -3,3 8,-7 -10,-10 -1,-5 -7,2 1,-10 5)),'
                            'POLYGON((0 7,-3 4,-7 6,-6 9,2 -10,0 7)),'
                            'GEOMETRYCOLLECTION('
                                'MULTILINESTRING((3 10,-1 -2,8 4,-1 5,-6 0,9 0,-6 -2,8 9),'
                                                '(-4 -8,-4 -7,-2 7,8 -6,-2 -9,-6 -8,-7 -2,-7 6),'
                                                '(-4 0,1 10,4 3,-4 0,7 -1,-1 5,5 -8,5 1,5 -6),'
                                                '(-9 10,-4 1,-5 3,-6 6,-5 9,4 9,10 5,5 0),'
                                                '(4 10,3 -5,-1 5,-8 -3,0 -5,-7 5,3 0,3 -10,-7 0,5 1,1 5,3 -4,-10 7,-1 10,-8 0))),'
                            'MULTILINESTRING((-6 2,5 4,9 1,9 9)),'
                            'MULTILINESTRING((6 -5,-3 3,-6 8,9 -7,3 10,-9 -9,1 2,8 -9),'
                                            '(5 -10,6 -10,9 10,-1 -5,-8 4,-7 1,8 1,-2 9,3 5,-10 3,6 5,-9 7,-7 -7,-2 2,1 5),'
                                            '(-6 0,10 -1,2 4,9 3,1 -5),(6 8,-10 1,-3 3,10 6)),POINT(-3 0))')));
SELECT ST_ASTEXT(
ST_INTERSECTION(
ST_GEOMFROMWKB(ST_ASWKB(ST_GEOMFROMTEXT('POLYGON((1 3,+.5 -1032.34324,1 3))')),0),
ST_GEOMFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(POINT(2,.1),
LINESTRING(POINT(4,7),POINT(3,5),POINT(-5.1,7),POINT(5,-3.6)),
LINESTRING(POINT(6,3),POINT(5,3),POINT(0,1),POINT(8,7))),0))));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(
ST_INTERSECTION(
ST_GEOMFROMWKB(
POLYGON(
LINESTRING(POINT(4,6),POINT(9,9),POINT(2,0),POINT(4,4),POINT(4,6)),
LINESTRING(POINT(7,5),POINT(4,2),POINT(4,5),POINT(-.8,5),POINT(7,5)),
LINESTRING(POINT(4,1),POINT(6,0),POINT(3,3),POINT(-1.8,4),POINT(4,1)),
LINESTRING(POINT(6,7),POINT(2,7),POINT(2,-.0),POINT(.1,-.4),POINT(4,5),POINT(6,7)),
LINESTRING(POINT(2,5),POINT(2,.6),POINT(4,3),POINT(3,8),POINT(2,5))),0),
ST_GEOMFROMTEXT('LINESTRING(-.7 -.3,0 5,8 0,9 7)',0)));
ERROR 22023: Invalid GIS data provided to function st_geomfromwkb.
SELECT ST_ASTEXT(
ST_INTERSECTION(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                        'POLYGON((3 1,5 2,3 1),(8 5,4 9,-4.6 6,2.0 0,4 -1032.34324,8 5)),'
                        'LINESTRING(0 +.6),'
                        'LINESTRING(4 189.7654,9 5,0 +.8,3 9))',0),
ST_GEOMFROMWKB(
MULTIPOLYGON(
POLYGON(
LINESTRING(POINT(6,3),POINT(4,-.3),POINT(9,2),POINT(5,9),POINT(0,8),POINT(6,3))),
POLYGON(
LINESTRING(POINT(1,6),POINT(3,8),POINT(9,1),POINT(7,6),POINT(6,1),POINT(0,-.4),POINT(6,2),
POINT(0,4),POINT(0,9),POINT(3,4),POINT(4,+.0),POINT(4,2),POINT(4,7),POINT(1,6))),
POLYGON(
LINESTRING(POINT(3,1),POINT(2,3),POINT(3,1)),
LINESTRING(POINT(6,7),POINT(2,4),POINT(6,7))),
POLYGON(
LINESTRING(POINT(1,0),POINT(8,3),POINT(3,7),POINT(4,3),POINT(9,5),POINT(1,0))),
POLYGON(
LINESTRING(POINT(6,4),POINT(-3.0,1),POINT(5,8),POINT(8,4),POINT(3,7),POINT(8,9),POINT(5,6),POINT(6,4)),
LINESTRING(POINT(5,5),POINT(2,0),POINT(1.3,9),POINT(+.2,9),POINT(6,6),POINT(.5,9),POINT(7,0),
POINT(9,-.9),POINT(9,4),POINT(3,1),POINT(4,7),POINT(9,0),POINT(-.5,4),POINT(3,1),POINT(5,5)))),0)));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
######################################################################################################
# BUG#20445979 : ASSERTION `RESULT->LENGTH() == 0' FAILED
######################################################################################################
SELECT ST_ASTEXT(
ST_UNION(
ST_GEOMFROMWKB(
ST_ASWKB(
ST_GEOMFROMTEXT('MULTILINESTRING((3 189.7654, 0 0, 10.0002 1, -1.2 0),'
                                                        '(3 4, 10.0002 6,2.7 2.232432, 8 6, 0 3),'
                                                        '(7.1 9, -4 -.8, -0.7654 7, 1 5, -0.7654 5,-6.7 -0.7654, 6 2.232432, 0 2.232432),'
                                                        '(8 3.9, 2 3, 3 8, 0 7, 1 6),'
                                                        '(8 3, 5 3, 9 4.4, 4 2.4),'
                                                        '(9 2.232432, -1032.34324 2.232432, -1.9 9),'
                                                        '(2.232432 6, 9 189.7654, 8 7, 5 6, 10.0002 8),'
                                                        '(2.232432 -6.3, 189.7654 9,3 3, 10.0002 -8.7, 3 -0.7654),'
                                                        '(5 -.3, 5 1, 6 2, 2.232432 5, 1 1, 9 10.0002,9 3, -1032.34324 1),'
                                                        '(1 7, 4 -0.7654, 3 7, -0.7654 8))'))),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                                              'MULTIPOINT(0.8 2.232432, 6 -2, 2 -5, -9.06),'
                                              'MULTIPOINT(5 8, 9.3 7),'
                                              'POLYGON((4 1, -3.9 2, 1 0.6, 1 1, 4 1)),'
                                              'POINT(9 +.7),'
                                              'LINESTRING(189.7654 2, 4 -1032.34324, 9 0, 9 8),'
                                              'POLYGON((1 1, 0 +.0, 2 4, 7 9,9 -3, 3 -0.7654, 10.0002 7, 8 -1032.34324, 1 1)))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
######################################################################################################
# BUG#20445765 : ASSERTION `MLS != __NULL' FAILED
######################################################################################################
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(LINESTRING(9 -0.7654,3 -1032.34324,6 0))'),
ST_GEOMFROMTEXT(
'LINESTRING(0 1,-.7 6,9 6,-8.0 -.0,4 7)')));
ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(LINESTRING(9 -0.7654,3 -1032.34324,6 0))'),
ST_GEOMFROMTEXT(
'LINESTRING(0 1,-.7 6,9 6,-8.0 -.0,4 7)')))
GEOMETRYCOLLECTION EMPTY
######################################################################################################
# BUG#20462935 : WL#7225 : MYSQLD GOT SIGNAL 11
######################################################################################################
DO ST_ASTEXT(ST_UNION(
ST_GEOMFROMTEXT('POINT(8 0.8)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'POLYGON((7 2, 1 4, 5 6, +.3 -1032.34324, 7 2),'
                                '(7 4, 2 0, +.6 -1.4, -9 9, 8 7, 7 4),'
                                '(2 0, -.9 5, 2.232432 9, 2 4, 2.232432 1, 0 -1032.34324, 2 0)),'
                        'MULTIPOINT(-0.7654 2.232432,+.9 +.1, 8 3, 189.7654 -6, 4 -.5),'
                        'MULTIPOLYGON(((7 0, 7 1, 6 -9, 7 0))),'
                        'POLYGON((2 9, 9 2.232432, 1 189.7654, 2.232432 6, +.4 9, 189.7654 3, 6 2, 2 9),'
                                '(1 9, 3 -1, 189.7654 -0.7654, 1 9)),'
                        'MULTIPOINT(1 10.0002, 0 6, 8 3, 5 -0.7654),'
                        'POLYGON((6 3, -6.5 189.7654, -8 -1032.34324, +.4 3,-.2 1, -0.7654 -.0, 6 3)))')));
######################################################################################################
# BUG#20462858 : ASSERTION `GEOTYPE == GTYPE || GEOTYPE == 0' FAILED
######################################################################################################
DO ST_ASTEXT(ST_UNION(
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'MULTIPOINT(8 1, 5 9, 2.232432 2.232432, -1032.34324 7, 10.0002 6, 2 0.6))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'MULTIPOINT(2.232432 -1032.34324, 2.232432 2.232432, 0.6 1, -0.7654 -.5),'
                        'POLYGON((6 5, 4 -1032.34324, 189.7654 -.5, 6 5)),'
                        'POLYGON((4 6, 7 1, -0.7654 10.0002, 0 10.0002, -3.5 -7, 5 6, 3 -7.7,2.232432 9, 4 6)))')));
######################################################################################################
# BUG#20379857 : ASSERTION `ENTER_COUNT == 0' FAILED
######################################################################################################
# Invalid geometry in input.
DO ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('POLYGON((-4 0,7 -8,-5 1,1 9,-9 -7,9 6,-6 -9,-4 0))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'MULTILINESTRING((5 8,-5 -8,3 -7,-2 -7,6 2,6 3,6 10,7 7,1 0,-6 -1,8 -4,-2 1,9 -10,4 -2)),'
                        'LINESTRING(-1 -2,10 -7,8 -5))')));
# Invalid geometry in input.
DO ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('POLYGON((-4 0,7 -8,-5 1,1 9,-9 -7,9 6,-6 -9,-4 0))'),
ST_GEOMFROMTEXT('MULTILINESTRING((5 8,-5 -8,3 -7,-2 -7,6 2,6 3,6 10,7 7,1 0,-6 -1,8 -4,-2 1,9 -10,4 -2),(-1 -2,10 -7,8 -5))')));
# Invalid geometry in input.
DO ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('POLYGON((-4 0,7 -8,-5 1,1 9,-9 -7,9 6,-6 -9,-4 0))'), ST_GEOMFROMTEXT('LINESTRING(-1 -2,10 -7,8 -5)')));
######################################################################################################
# BUG#20422341 : ST_ISSIMPLE(MULTILINESTRING) RETURNS INCORRECT RESULT
######################################################################################################
SELECT ST_ISSIMPLE(ST_GEOMFROMTEXT('MULTILINESTRING((0 0,10 0,10 10,0 10,0 0))'));
ST_ISSIMPLE(ST_GEOMFROMTEXT('MULTILINESTRING((0 0,10 0,10 10,0 10,0 0))'))
1
SELECT ST_ISSIMPLE(ST_GEOMFROMTEXT('MULTILINESTRING((0 0,0 5,5 5,5 0,0 0))'));
ST_ISSIMPLE(ST_GEOMFROMTEXT('MULTILINESTRING((0 0,0 5,5 5,5 0,0 0))'))
1
######################################################################################################
# BUG#20430837 : WL#7225: BOOST_ASSERT( ENTER_COUNT == 0 )
######################################################################################################
#invalid geometry as input
DO ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('MULTIPOLYGON(((7 -6,-2 8,-5 -4,2 7,4 9,0 -9,8 2,5 -2,9 0,-6 1,10 -10,7 -6)),'
                                 '((-7 -4,9 3,-9 8,0 -4,5 -9,-7 -4)),'
                                 '((-10 1,-5 -3,1 -1,-9 -5,10 -9,9 -7,-4 9,8 0,2 0,-10 1)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'POINT(-4 -1),'
                        'MULTIPOINT(3 1,-5 10,-5 -6,7 -10,5 -5,-10 -7,-8 7,9 0,6 -4,5 7),'
                        'LINESTRING(5 -8,-7 -6,-3 6,-3 1,-5 4,-1 0,8 5,5 1,-2 3,1 10,8 5,6 2,7 4),'
                        'MULTIPOINT(0 1,-7 -7,-3 -2,6 -1,-9 -9,2 6,-9 -6,-4 0,0 10))')));
SELECT ST_ASTEXT(ST_DIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(5 -8,-7 -6,-3 6,-3 1,-5 4,-1 0,8 5,5 1,-2 3, 1 10,8 5,6 2,7 4)'),
ST_GEOMFROMTEXT('MULTILINESTRING((1.9375 1.875,1.7441860465116283 1.9302325581395348,-0.7692307692307692 2.6483516483516487,'
                                     '-2 3,-1.0071942446043165 5.316546762589928))')));
ST_ASTEXT(ST_DIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(5 -8,-7 -6,-3 6,-3 1,-5 4,-1 0,8 5,5 1,-2 3, 1 10,8 5,6 2,7 4)'),
ST_GEOMFROMTEXT('MULTILINESTRING((1.9375 1.875,1.7441860465116283 1.9302325581395348,-0.7692307692307692 2.6483516483516487,'
          
LINESTRING(5 -8,-7 -6,-3 6,-3 1,-5 4,-1 0,8 5,5 1,1.9375 1.875)
######################################################################################################
# BUG#20434940 : ASSERTION `IS_CLOSING_POINT_OF(TURN.POINT, LS1)' FAILED
######################################################################################################
SELECT ST_ISSIMPLE(ST_GEOMFROMTEXT('MULTILINESTRING((4 1,10 8,4 6,4 1,10 5,10 3))'));
ST_ISSIMPLE(ST_GEOMFROMTEXT('MULTILINESTRING((4 1,10 8,4 6,4 1,10 5,10 3))'))
0
######################################################################################################
# BUG#20455380 : ASSERTION `ENTER_COUNT == 0' FAILURE WITH LINEAR GEOMETRIES
######################################################################################################
SELECT ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('MULTILINESTRING((2 5,-0.7654 2),'
                                    '(-1032.34324 4,1 5,9 7,3 9,0.2 5,1 -0.3),'
                                    '(1 0.9,1 6,1 -0.6,2.232432 -0.7654,0.9 3,1 5,-0.7654 9,3 0.1,9 0,-6 8,-0.7 8,0 1,-1032.34324 0))'),
ST_GEOMFROMTEXT('LINESTRING(1 5,4 2,1 -1.1,1 6,1 189.7654,2 5,-0.7654 3)')));
ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('MULTILINESTRING((2 5,-0.7654 2),'
                                    '(-1032.34324 4,1 5,9 7,3 9,0.2 5,1 -0.3),'
                                    '(1 0.9,1 6,1 -0.6,2.232432 -0.7654,0.9 3,1 5,-0.7654 9,3 0.
MULTILINESTRING((1 5,4 2,1 -1.1,1 -0.6),(1 6,1 189.7654,2 5,-0.7654 3),(2 5,-0.7654 2),(-1032.34324 4,1 5,9 7,3 9,0.2 5,1 -0.3),(1 -0.6,2.232432 -0.7654,0.9 3,1 5,-0.7654 9,3 0.1,9 0,-6 8,-0.7 8,0 1,-1032.34324 0))
SELECT ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(0 -3,5 4,6 6,-3 2,-3 0,-3 -10,9 -2,9 5,5 -5,-4 -8,9 0)'),
ST_GEOMFROMTEXT('LINESTRING(-3 6,-3 0,-3 5,2 -3,-6 10,5 0,2 8,-6 1,10 -6)')));
ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(0 -3,5 4,6 6,-3 2,-3 0,-3 -10,9 -2,9 5,5 -5,-4 -8,9 0)'),
ST_GEOMFROMTEXT('LINESTRING(-3 6,-3 0,-3 5,2 -3,-6 10,5 0,2 8,-6 1,10 -6)')))
MULTILINESTRING((0 -3,5 4,6 6,-3 2),(-3 0,-3 -10,9 -2,9 5,5 -5,-4 -8,9 0),(-3 6,-3 2),(-3 2,-3 5,2 -3,-6 10,5 0,2 8,-6 1,10 -6))
######################################################################################################
# BUG#20463609 : ST_SYMDIFFERENCE : ASSERTION `ENTER_COUNT == 0' FAILED
######################################################################################################
SELECT ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(-2 -2,-4 0,1 -8,-2 6,8 5,-7 -8,3 0,4 -1,-7 10,-4 10)'),
ST_GEOMFROMTEXT('LINESTRING(-5 -4,3 0,4 -1,7 -4,2 -1,-4 -1,-2 6)')));
ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(-2 -2,-4 0,1 -8,-2 6,8 5,-7 -8,3 0,4 -1,-7 10,-4 10)'),
ST_GEOMFROMTEXT('LINESTRING(-5 -4,3 0,4 -1,7 -4,2 -1,-4 -1,-2 6)')))
MULTILINESTRING((-2 -2,-4 0,1 -8,-2 6,8 5,-7 -8,3 0),(3 0,-7 10,-4 10),(-5 -4,3 0),(4 -1,7 -4,2 -1,-4 -1,-2 6))
# Invalid geometry in input.
DO ST_ASTEXT(ST_INTERSECTION(
ST_GEOMFROMWKB(
ST_ASWKB(
ST_GEOMFROMTEXT('MULTILINESTRING((1 5, -4.3 -.1),'
                                            '(0 6,8.6 6,189.7654 5,1 3,6 3,3 5,6 2.232432,0 4),'
                                            '(-6 5,1 2.232432),'
                                            '(3 -1032.34324,9 0,189.7654 1,-1.4 3,3 189.7654,+.3 10.0002,1 5,6 3,5 1,9 1,10.0002 -1032.34324,-0.7654 0,5 3, 3 4),'
                                            '(2.232432 2.232432,8.6 +.4,0.0 2.232432,4 0,-8.8 10.0002),'
                                            '(1 0,6 6,7 2,-0 8.4),'
                                            '(-0.7654 3,+.6 8,4 -1032.34324,1 6,0 4),'
                                            '(0 7,2 1,8 -7,7 -.7, -1032.34324 9), (5 0, 10.0002 4, 8 7, 3 3, -8.1 5))'))),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'MULTIPOLYGON(((9 6,189.7654 10.0002,5.1 7,7 -0.7654,8 -.7,9 6),'
                                      '(3 7,10.0002 -1032.34324,5 5,2.232432 1,-.8 +.0,3 8,0 2.232432,3 7)),'
                                     '((7 5,1 3,1 0.8,2 -.5,3 6,6.6 3.7,2.232432 -1032.34324,+.2 10.0002,7 5),'
                                      '(0 8,10.0002 9,5 2,6 5,8 8,0 8)),'
                                     '((2 0,9 2.232432,9 -3,2 8,7 9,3 0,-1 -8.2,0 8,2 0),'
                                      '(5 2,2 3,9 5,6 1,10.0002 2.232432,-6.2 9,8 10.0002,9 3.0,5 -8.7,5 2))),'
                        'MULTILINESTRING((5 10.0002,2 7,-0.7654 0,5 3),'
                                        '(0 -0.7654,4 10.0002,4 +.1,-.8 3,-.1 8,10.0002 2,+.9 -1032.34324)))')));
# Invalid geometry in input.
DO ST_ASTEXT(ST_INTERSECTION(
ST_GEOMFROMWKB(
ST_ASWKB(
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                                'MULTILINESTRING((1 4, 6 5, 2 -1032.34324, 6.9 6),'
                                                '(6 3, 4 2, 7 0.8, 2 5),'
                                                '(0 9, 9 4, -0.1 10.0002, 0 +.5, 4 8, -3 7),'
                                                '(2 7, 5 -6),'
                                               '(9 -1032.34324, 2 0, 6 3, 2 2, 4 -1032.34324, 6 2.232432)),'
                                'MULTILINESTRING((-0.7 2.232432, +.3 -.5),'
                                                '(0 2, 7 -.3, 2.4 5, +.8 2)),'
                                'POLYGON((8 9, 9 0, 189.7654 8, 8 9),'
                                        '(9 5, 5 2, 2.232432 -.3, 4 3, 2 2, 10.0002 1.6, 1 6, 8 8, 3 3, 9 5),'
                                        '(4 5, 7 4, 2.232432 2.232432, 0 -.9, 0 189.7654, 2 2, 4 5),'
                                        '(7 9, 1 -3.6, 2.232432 2.232432, 6 1, 2.232432 6, 0 2.232432, 7 9)))'))),
ST_GEOMFROMWKB(
ST_ASWKB(
ST_GEOMFROMTEXT('LINESTRING(0 -5, 5 10.0002)')))));
# Invalid geometry in input
DO ST_ASTEXT(ST_UNION(
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'MULTIPOINT(1 1, 5 -3, 10.0002 1, -1032.34324 5, 9 1.1, 4.5 4, 5 0, -5.1 3),'
                        'MULTIPOINT(-1032.34324 5, 8 -1032.34324, -0.7654 3, -1032.34324 4, 1 1, 8 2),'
                        'POLYGON((7 2, 6.9 +.6, -0.7654 -.5, 2.4 7, 7 3, 7 2)))'),
ST_GEOMFROMWKB(ST_ASWKB(POLYGON(LINESTRING(POINT(7,9), POINT(7,3), POINT(7,6), POINT(0,6), POINT(7,9)))))));
# Invalid geometry in input
DO
ST_ASTEXT(ST_INTERSECTION(
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'MULTIPOINT(4 2, 2 4, 4 1, 5 -3.9, 10.0002 3, 2.232432 2, 189.7654 -.9, 7 7),'
                        'POLYGON((8 3, -0.7654 -.0, 10.0002 8, 1 6, 189.7654 4, -1032.34324 4, 8 3),'
                                '(2 7, 2.232432 0.5, 9 2, 2.232432 1, 9 5, 6 8, 8 -1032.34324, -9.7 -.1, 9.9 2, 1 5, 1 -.6, 10.0002 -.7, 1 4, 7.0 9, 2 7)),'
                        'MULTILINESTRING((1 8, 9 -0.7654, 5 7, +.0 -0.7654, 8 7),'
                                        '(8 1.1, 6 4, -.4 10.0002, -.3 -1, +.3 8)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'POINT(9 6.7),'
                        'MULTILINESTRING((5 0, 2.232432 -0.7654, 0 0, 10.0002 1),'
                                        '(9 0.6, -2.8 0, 2.232432 2.4),'
                                        '(1 -1032.34324, -1032.34324 -7.0, 1 0, 0 9, +.2 189.7654, 5 1),'
                                        '(-9.3 189.7654, 7 0, 10.0002 -1032.34324, 3 9.6, 9 10.0002),'
                                        '(1 8, 9 -0.7654, -0.7654 3, 8 5, 3 3)),'
                                        'POINT(-1032.34324 1))')));
SELECT ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('MULTILINESTRING((-.4 2, 2.232432 3, 6 9, 8 189.7654, -1032.34324 5.4, 2.232432 9),'
                                    '(-1032.34324 3, 8 -1.6),'
                                    '(0 -.2, 8 1, -.5 7, 6 +.2))'),
ST_GEOMFROMWKB(ST_ASWKB(MULTILINESTRING(
LINESTRING(POINT(-8,1), POINT(4.8,6), POINT(2,+.5)),
LINESTRING(POINT(10.0002,2), POINT(9,-1032.34324), POINT(.3,8), POINT(0,5), POINT(8,1), POINT(4,.4), POINT(2,8)),
LINESTRING(POINT(6,7), POINT(+.1,7), POINT(0,-.5)))))));
ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('MULTILINESTRING((-.4 2, 2.232432 3, 6 9, 8 189.7654, -1032.34324 5.4, 2.232432 9),'
                                    '(-1032.34324 3, 8 -1.6),'
                                    '(0 -.2, 8 1, -.5 7, 6 +.2)
MULTILINESTRING((-0.4 2,2.232432 3,6 9,8 189.7654,-1032.34324 5.4,2.232432 9),(-1032.34324 3,8 -1.6),(0 -0.2,4 0.4),(8 1,-0.5 7,6 0.2),(-8 1,4.8 6,2 0.5),(10.0002 2,9 -1032.34324,0.3 8,0 5,8 1),(4 0.4,2 8),(6 7,0.1 7,0 -0.5))
# Invalid geometry in input
DO ST_ASTEXT(ST_INTERSECTION(
ST_GEOMFROMWKB(
ST_ASWKB(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                    'MULTILINESTRING((4 -0.7654, -1032.34324 -1032.34324, 8 8, 1 0, 6 7, -1032.34324 -.0, -1032.34324 189.7654, 9 2, -9.6 -1032.34324, 5 6, 0 9.0)),'
                    'POINT(7 4),'
                    'LINESTRING(1 -.0, 6 2, 0 1, -2 -8.4, 0 7, -1032.34324 2.3, 6 3, -1032.34324 -1032.34324),'
                    'MULTILINESTRING((1 4, 6 5, 2 -1032.34324, 6.9 6),'
                                    '(6 3, 4 2, 7 0.8, 2 5),'
                                    '(0 9, 9 4, -0.1 10.0002, 0 +.5, 4 8, -3 7),'
                                    '(2 7, 5 -6),'
                                    '(9 -1032.34324, 2 0, 6 3, 2 2, 4 -1032.34324, 6 2.232432)),'
                    'MULTILINESTRING((-9.0 2, -5 0.8),'
                                    '(9 189.7654, 9 4, 3 -9, 9 2),'
                                    '(189.7654 9, 7 3, 5 189.7654, -1032.34324 4, -5.9 9)),'
                                    'POINT(1 -.2),'
                                    'LINESTRING(7 8, 6 1),'
                                    'POINT(2 2),'
                                    'LINESTRING(1 8, 0 189.7654, 1 -1032.34324, 8 2),'
                                    'MULTIPOLYGON(((2 8, 4 0, 3 9, 5 -3, 0 6, 6 1, 2 8))),'
                                    'LINESTRING(-0.7654 -.8, 8 1),'
                                    'POINT(7 9),'
                                    'MULTILINESTRING((-0.7 2.232432, +.3 -.5),'
                                                    '(0 2, 7 -.3, 2.4 5, +.8 2)),'
                                    'LINESTRING(10.0002 6.4, 189.7654 4, 189.7654 8, 3 5, 9 -2, 2 -1032.34324),'
                                    'POLYGON((8 9, 9 0, 189.7654 8, 8 9),'
                                            '(9 5, 5 2, 2.232432 -.3, 4 3, 2 2, 10.0002 1.6, 1 6, 8 8, 3 3, 9 5),'
                                            '(4 5, 7 4, 2.232432 2.232432, 0 -.9, 0 189.7654, 2 2, 4 5),'
                                            '(7 9, 1 -3.6, 2.232432 2.232432, 6 1, 2.232432 6, 0 2.232432, 7 9)),'
                                    'LINESTRING(3 1.2, -9 +.2),'
                                    'POINT(-.0 0),'
                                    'MULTIPOINT(-0.7654 -1032.34324, 3 189.7654, 4 6, -2.7 +.2, -1032.34324 6.5, 10.0002 -0.7654, 7 9))'))),
ST_GEOMFROMWKB(
ST_ASWKB(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                    'POINT(5 -3),'
                    'POINT(189.7654 7),'
                    'LINESTRING(0 -5, 5 10.0002))')))));
DO ST_ASTEXT(ST_SYMDIFFERENCE(
ST_UNION(
ST_GEOMFROMTEXT('MULTIPOLYGON(((10 0,-10 -3,-10 6,-10 7,4 6,6 1,-9 -8,10 0)))'),
ST_GEOMFROMTEXT('MULTILINESTRING((7 6,7 3,1 5,6 1,1 9,-6 -8,-7 -3,0 -3,7 -5,7 -2,-1 -9,-1 -6,7 7,-7 -10),'
                                        '(10 4,8 10,-3 8,2 3,9 2,9 7,-7 -4,-10 -7),'
                                        '(5 -3,-1 -3),'
                                        '(9 -9,-3 2,-1 -2,7 -1,9 9,-5 3,-8 5,-10 8,-2 -8,9 1))')),
ST_UNION(
ST_GEOMFROMTEXT('LINESTRING(-1 -6,-2 9,-9 -5,0 -6,10 -1,-2 3,4 -10)'),
ST_GEOMFROMTEXT('POINT(2 -1)'))));
DO ST_ASTEXT(ST_SYMDIFFERENCE(
ST_UNION(ST_GEOMFROMTEXT('POINT(7 -8)'),
ST_GEOMFROMTEXT('MULTILINESTRING((-7 2,3 7,-5 9,-8 -10,-3 10,-8 6,-5 -10,3 -1),(5 6,3 2,9 -1,-7 -7,-10 -7,7 9,-9 2,7 10,-6 6))')),
ST_UNION(ST_GEOMFROMTEXT('POINT(-9 5)'),
ST_GEOMFROMTEXT('POLYGON((-7 -4,6 7,10 2,8 6,-2 8,-3 -7,-5 4,-1 10,7 -8,8 9,1 -8,-7 -4))'))));
DO ST_ASTEXT(ST_SYMDIFFERENCE(
ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(3 8,-10 -5,3 5,-2 -9,6 2,-1 6,4 5,-3 3,-1 8,-2 -7,6 10,-1 -2,-1 10)'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((-5 3,-10 -4,-6 9,7 1,8 -7,-4 -8,-10 3,-5 3)),'
                                     '((8 -3,-5 -6,-9 9,9 10,-7 -7,6 -10,-1 -3,3 -5,6 2,-8 10,7 -9, 8 -3)),'
                                     '((6 10,-1 0,6 -8,-7 -1,6 -4,-2 7,8 -5,-6 10,9 -3,-1 -5,4 7,8 -3,6 10)))')),
ST_UNION(
ST_GEOMFROMTEXT('LINESTRING(5 -6,-6 -4,2 -2,4 -10,10 4,-7 4,7 3,1 -6,1 -2,5 -2,-5 8,-7 -2,2 8,3 -3,-7 -4)'),
ST_GEOMFROMTEXT('MULTIPOINT(4 9,-7 -1,-9 1,-5 -3,8 -6,8 -7,10 5,9 8,-2 -8,3 -2,1 9)'))));
# Invalid geometry in input.
DO ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('MULTILINESTRING((-3 9,3 -4,7 9,4 5,2 -6,-2 -1,-6 2))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((-3 -8,0 -8,4 -8,0 -3,3 6,6 9,2 1,9 6,1 6,-8 -2,2 5,-1 5,-6 9,-3 -8)))')));
# Invalid geometry in input.
DO ST_ASTEXT(ST_SYMDIFFERENCE(
ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('MULTIPOINT(10 -8,-2 6,-6 3,-3 7,7 -3,-8 -9,-3 9,-1 -7)'),
ST_GEOMFROMTEXT('LINESTRING(-10 0,-4 4,9 5,-7 -8,3 -5,-9 5,0 -6,-9 -4,9 -1,0 -2,-9 4,-7 -8,4 0)')),
ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('MULTILINESTRING((6 -2,4 -9,3 3,0 -10,-10 -1,-4 0,0 1,-2 8,-3 -9),'
                                        '(7 0,10 -7,10 2,-2 9,-2 10,-7 -10,6 5,-3 10,7 6,2 -6,8 9,9 -4,3 -2,3 1))'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((-1 -1,7 4,1 10,-1 -1)),'
                                     '((-3 -9,2 8,5 6,3 -5,-5 4,-5 -7,-1 -7,3 5,-4 6,9 -4,-5 2,-5 9,-3 -9)))'))));
######################################################################################################
# BUG#20462962 : ASSERTION `GEOMETRY::IS_VALID_GEOTYPE(GT)' FAILED
######################################################################################################
SELECT ST_ISVALID(
ST_GEOMFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(
MULTIPOINT(POINT(8,-6), POINT(9,2), POINT(9,1), POINT(-0,6), POINT(8,6), POINT(4,3), POINT(.7,6)),
MULTIPOINT(POINT(8,-0.7654), POINT(2,7), POINT(8,-0.7654), POINT(-.0,7), POINT(.0,6), POINT(.9,+.4), POINT(6,2.232432)),
POLYGON(
LINESTRING(POINT(8,6), POINT(-.4,3), POINT(-9.8,2.232432), POINT(-1032.34324,8), POINT(2.232432,.8), POINT(8,6))),
POLYGON(
LINESTRING(POINT(0,9), POINT(3,1), POINT(3,0), POINT(3,2), POINT(0,9)))))));
ST_ISVALID(
ST_GEOMFROMWKB(ST_ASWKB(GEOMETRYCOLLECTION(
MULTIPOINT(POINT(8,-6), POINT(9,2), POINT(9,1), POINT(-0,6), POINT(8,6), POINT(4,3), POINT(.7,6)),
MULTIPOINT(POINT(8,-0.7654), POINT(2,7), POINT(8,-0.7654), POINT(-.0,7), POINT(.0,6), POINT(.9,+.4
0
######################################################################################################
# BUG#20408240 : ST_DIFFERENCE : ASSERTION `T1->RESULT_RANGE' FAILED
######################################################################################################
SELECT ST_ASTEXT(ST_DIFFERENCE(
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'MULTIPOINT(-8 1,-8 1,-10 -3,-10 -1,-9 3),'
                        'POLYGON((8 -7,1 -7,-9 0,9 8,-9 7,-10 -2,5 3,8 -8,-10 0,-3 -8,2 9,-7 4,-2 -8,8 -7)))'),
ST_GEOMFROMTEXT('LINESTRING(-7 4,-7 4,9 3,1 -2,-10 2)')));
ST_ASTEXT(ST_DIFFERENCE(
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'MULTIPOINT(-8 1,-8 1,-10 -3,-10 -1,-9 3),'
                        'POLYGON((8 -7,1 -7,-9 0,9 8,-9 7,-10 -2,5 3,8 -8,-10 0,-3 -8,2 9,-7 4,-2 -8,8 -7)))'),
ST_GEOMFROMT
GEOMETRYCOLLECTION(POLYGON((8 -7,1 -7,-9 0,9 8,-9 7,-10 -2,5 3,8 -8,-10 0,-3 -8,2 9,-7 4,-2 -8,8 -7)),POINT(-10 -3),POINT(-10 -1))
#invalid geometry as input
DO ST_ASTEXT(ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('POLYGON((-10 -4,7 -3,7 4,-5 8,1 -7,-10 -4))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'MULTIPOINT(6 6,3 -7,6 -10,2 8),'
                        'LINESTRING(0 -4,-2 2,-3 6,2 -3,-10 -4,5 -7,10 -1,-6 -1,7 4,-1 -4,6 0),'
                        'GEOMETRYCOLLECTION('
                            'MULTILINESTRING((8 8,4 8,2 -2,4 0,7 -7,-5 10,-6 -2,-7 -1,-1 5,-3 -1),'
                                            '(-7 10,1 0,-1 7,9 2,-2 6,-1 -1,-1 -1))))')));
######################################################################################################
# BUG#20141918 : ASSERTION `CUR_SHAPE != GCALC_FUNCTION::SHAPE_POINT' FAILED
######################################################################################################
DO ST_ASTEXT(ST_DIFFERENCE(
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'POINT(-5 -4),'
                        'POLYGON((-5 1,7 -7,10 0,-10 8,-7 9,-5 1)))'),
ST_GEOMFROMTEXT('LINESTRING(5 3,7 -3,5 0,-6 -9,-4 9,-3 -8,10 1,10 -9,5 -3,4 -2,1 -3,-8 -5,4 -7,-2 -8)')));
# Invalid geometry in input
DO ST_ASTEXT(ST_DIFFERENCE(
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                        'POINT(1 6),'
                        'GEOMETRYCOLLECTION('
                            'LINESTRING(-9 5,1 -4,-5 4,2 2,-3 7)),'
                        'LINESTRING(9 -4,-4 -10,-8 7,-6 8,-4 4,7 -5,-3 7,5 4,2 -1,8 6,9 5),'
                        'MULTIPOLYGON(((-3 -5,0 -10,6 8,-3 -5)),'
                                     '((1 -2,-6 1,-8 8,9 6,-10 9,7 9,1 -2))))'),
ST_GEOMFROMTEXT('MULTILINESTRING((5 -6,0 1,5 -6,5 -8,1 -1,4 -2),'
                                    '(-7 8,1 2,-8 8,10 -9,-2 4,-8 7,-8 -2,7 2,10 4,9 0,0 -2,8 2))')));
######################################################################################################
# BUG#20106767 : CRASH WITH ST_ISVALID() FUNCTION
######################################################################################################
SELECT ST_ISVALID(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                  'MULTILINESTRING((-7 -2,-9 3,-2 -8),(3 7,5 6,-7 -9,7 -1,-2 -8,2 9,4 6,-5 -5)),'
                  'MULTILINESTRING((2 -2,2 -3,2 -1,-10 7,1 -2,-2 0,-9 -2,10 5,-7 -8,-9 -1,1 -1,-2 3,5 -9,-8 -9,-10 -9)),'
                  'MULTIPOINT(-7 -5,6 9,7 4))'));
ST_ISVALID(
ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION('
                  'MULTILINESTRING((-7 -2,-9 3,-2 -8),(3 7,5 6,-7 -9,7 -1,-2 -8,2 9,4 6,-5 -5)),'
                  'MULTILINESTRING((2 -2,2 -3,2 -1,-10 7,1 -2,-2 0,-9 -2,10 5,-7 -8,-9 -1,1 -1,-2 3,5 -9,-
1
######################################################################################################
# BUG#20112290 : HANG/INFINITE LOOP IN ST_ISVALID() FUNCTION WITH NESTED QUERIES
######################################################################################################
SELECT ST_ISVALID(
ST_CONVEXHULL(
ST_UNION(
ST_GEOMFROMTEXT('MULTILINESTRING((1 -4,-4 9,-8 1,2 2,5 1,8 4,-8 3,3 10,-3 -2,0 9),'
                                                  '(-9 10,-10 -8,4 2,-3 -3,-2 3,10 4,2 -7,-1 0,5 6,-9 -10,-7 -6,8 -8,-10 1,8 5,-7 -6),'
                                                  '(2 -4,-5 10,3 -1,6 0))'),
ST_GEOMFROMTEXT('POINT(-8 -10)'))));
ST_ISVALID(
ST_CONVEXHULL(
ST_UNION(
ST_GEOMFROMTEXT('MULTILINESTRING((1 -4,-4 9,-8 1,2 2,5 1,8 4,-8 3,3 10,-3 -2,0 9),'
                                                  '(-9 10,-10 -8,4 2,-3 -3,-2 3,10 4,2 -7,-1 0,5 6,-9 -10,-7 -6,8 -8,-10 1,8 5,-7 -6),
1
SELECT ST_ASTEXT(
ST_UNION(
ST_GEOMFROMTEXT('MULTILINESTRING((1 -4,-4 9,-8 1,2 2,5 1,8 4,-8 3,3 10,-3 -2,0 9),'
                                            '(-9 10,-10 -8,4 2,-3 -3,-2 3,10 4,2 -7,-1 0,5 6,-9 -10,-7 -6,8 -8,-10 1,8 5,-7 -6),'
                                            '(2 -4,-5 10,3 -1,6 0))'),
ST_GEOMFROMTEXT('POINT(-8 -10)')));
ST_ASTEXT(
ST_UNION(
ST_GEOMFROMTEXT('MULTILINESTRING((1 -4,-4 9,-8 1,2 2,5 1,8 4,-8 3,3 10,-3 -2,0 9),'
                                            '(-9 10,-10 -8,4 2,-3 -3,-2 3,10 4,2 -7,-1 0,5 6,-9 -10,-7 -6,8 -8,-10 1,8 5,-7 -6),'
                    
GEOMETRYCOLLECTION(LINESTRING(1 -4,-4 9,-8 1,2 2,5 1,8 4,-8 3,3 10,-3 -2,0 9),LINESTRING(-9 10,-10 -8,4 2,-3 -3,-2 3,10 4,2 -7,-1 0,5 6,-9 -10,-7 -6,8 -8,-10 1,8 5,-7 -6),LINESTRING(2 -4,-5 10,3 -1,6 0),POINT(-8 -10))
######################################################################################################
# BUG#20112707 : ST_VALIDATE() DOES NOT RETURN ANY VALUE WITH VALID GEOMETRY INPUT
######################################################################################################
SELECT ST_ASTEXT(ST_VALIDATE(ST_GEOMFROMTEXT('POINT(0 0)')));
ST_ASTEXT(ST_VALIDATE(ST_GEOMFROMTEXT('POINT(0 0)')))
POINT(0 0)
SELECT ST_ASTEXT(ST_VALIDATE(ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)')));
ST_ASTEXT(ST_VALIDATE(ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)')))
LINESTRING(0 0,10 10)
######################################################################################################
# BUG#20112849 : ST_ISVALID() FUNCTION RETURNS 'FALSE' WITH VALID POLYGON/MULTIPOLYGON INPUT
######################################################################################################
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'));
ST_ISVALID(ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4))'));
ST_ISVALID(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)))'));
ST_ISVALID(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((14 14,14 16,16 16,16 14,14 14)))'));
ST_ISVALID(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0,0 0,0 0)),((14 14,14 16,16 16,16 14,14 14)))'))
1
######################################################################################################
# BUG#20119431 : ST_ISVALID() FUNCTION RETURNS 'FALSE' WITH VALID GEOMETRYCOLLECTION INPUT
######################################################################################################
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0, 0 0))'));
ST_ISVALID(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0, 0 0))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0, 0 0)))'));
ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0,0 0, 0 0)))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10))'));
ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'));
ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'))
1
######################################################################################################
# BUG#20135389 : ST_MAKEENVELOPE() RETURNS SAME SET OF SRID VALUES IN THE ERROR MESSAGE
######################################################################################################
SELECT ST_ASTEXT(ST_MAKEENVELOPE(ST_GEOMFROMTEXT('POINT(0 1)', 4236), ST_GEOMFROMTEXT('POINT(0 0)', 0)));
ERROR HY000: Binary geometry function st_makeenvelope given two geometries of different srids: 4236 and 0, which should have been identical.
######################################################################################################
# BUG#20406722 : ST_ISVALID() DOES NOT PRINT THE FUNCTION NAME IN THE ERROR MESSAGE
######################################################################################################
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POINT(0 0)', -1));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POINT(0 0)', 1));
ERROR SR001: There's no spatial reference system with SRID 1.
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POINT(0 0)', 1000));
ERROR SR001: There's no spatial reference system with SRID 1000.
######################################################################################################
# BUG#20406850 : WL#8034 : CRASH WITH ST_ISVAID() IN ITEM_FUNC_ISVALID::VAL_INT FUNCTION
######################################################################################################
SET @star_center= 'POINT(15 10)';
SET @star_all_points= 'MULTIPOINT(5 0,25 0,15 10,15 25)';
SELECT ST_ISVALID(ST_GEOMFROMTEXT(@star_center,-1024));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_ISVALID(ST_GEOMFROMTEXT(@star_all_points,-1));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
######################################################################################################
# BUG#20414966 : WL#8034 : CRASH WITH ST_VAIDATE() IN ITEM_FUNC_VALIDATE::VAL_STR
######################################################################################################
SET @star_center= 'POINT(15 10)';
SET @star_all_points= 'MULTIPOINT(5 0,25 0,15 10,15 25)';
SELECT ST_VALIDATE(ST_GEOMFROMTEXT(@star_center,-1024));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_VALIDATE(ST_GEOMFROMTEXT(@star_all_points,-1));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
######################################################################################################
# BUG#20430521 : WL#8034 : CRASH WITH ST_SIMPLIFY() IN ITEM_FUNC_SIMPLIFY::VAL_STR
######################################################################################################
SELECT ST_ASTEXT(ST_SIMPLIFY(0x000000000200000000000000000000000000000000, 1));
ERROR 22023: Invalid GIS data provided to function st_simplify.
######################################################################################################
# BUG#20452274 : WL#8034 : SERVER IS HANGING WITH ST_VALIDATE() FUNCTION
######################################################################################################>>>>
SELECT ST_ASTEXT(
ST_VALIDATE(
ST_UNION(
ST_GEOMFROMTEXT('MULTIPOLYGON(((-7 -9,-3 7,0 -10,-6 5,10 10,-3 -4,7 9,2 -9,-7 -9)),((1 -10,-3 10,-2 5,1 -10)))'),
ST_GEOMFROMTEXT('POLYGON((6 10,-7 10,-1 -6,0 5,5 4,1 -9,1 3,-10 -7,-10 8,6 10))'))));
ERROR 22023: Invalid GIS data provided to function st_union.
######################################################################################################
# BUG#20454073 : ASSERTION `M_GEO_VECT && GET_GEOTYPE() != GEOMETRY::WKB_POLYGON' FAILED
######################################################################################################
SELECT ST_ASTEXT(ST_SIMPLIFY(ST_GEOMFROMTEXT('MULTIPOINT(19 -4,-2 -6,-8 2)'), 1));
ST_ASTEXT(ST_SIMPLIFY(ST_GEOMFROMTEXT('MULTIPOINT(19 -4,-2 -6,-8 2)'), 1))
MULTIPOINT((19 -4),(-2 -6),(-8 2))
######################################################################################################
# BUG#20534576 : WL#7929 : ST_BUFFER() : ASSERTION FAILURE IN SPATIAL.H FILE 
######################################################################################################
SELECT ST_ASTEXT(
ST_BUFFER(
ST_GEOMFROMTEXT('MULTILINESTRING((15 0,1 -18,-17 -13),(-10 -1,-5 -4,-15 4,1 -9,-5 1))'),
61555,
ST_BUFFER_STRATEGY('join_miter', 195)));
ST_ASTEXT(
ST_BUFFER(
ST_GEOMFROMTEXT('MULTILINESTRING((15 0,1 -18,-17 -13),(-10 -1,-5 -4,-15 4,1 -9,-5 1))'),
61555,
ST_BUFFER_STRATEGY('join_miter', 195)))
POLYGON((23555 -57555,23555 -57555,23555 -57555,30555 -53555,33555 -51555,33555 -51555,34555 -51555,40555 -46555,43555 -44555,43555 -44555,43555 -44555,49555 -37555,51555 -35555,51555 -34555,51555 -34555,55555 -26555,56555 -24555,56555 -24555,57555 -24555,59555 -15555,60555 -12555,60555 -12555,60555 -12555,61555 -2955,61555 -755,61555 -695,61555 -425,61555 9155,60555 11555,60555 11555,60555 11555,58555 20555,57555 23555,57555 23555,57555 23555,53555 31555,51555 33555,51555 33555,51555 34555,46555 40555,44555 43555,44555 43555,44555 43555,37555 48555,35555 51555,34555 51555,34555 51555,28555 54555,24555 56555,24555 56555,24555 57555,17555 59555,12555 60555,12555 60555,12555 60555,6455 61555,755 61555,585 61555,435 61555,-4955 61555,-11555 60555,-11555 60555,-11555 60555,-16555 59555,-23555 57555,-23555 57555,-23555 57555,-23555 57555,-23555 57555,-27555 55555,-33555 51555,-33555 51555,-34555 51555,-37555 48555,-43555 44555,-43555 44555,-43555 44555,-47555 39555,-51555 35555,-51555 34555,-51555 34555,-54555 29555,-56555 24555,-57555 24555,-57555 24555,-59555 17555,-60555 12555,-60555 12555,-60555 12555,-61555 5555,-61555 755,-61555 615,-61555 405,-61555 -6955,-60555 -11555,-60555 -11555,-60555 -11555,-58555 -19555,-57555 -23555,-57555 -23555,-57555 -23555,-53555 -30555,-51555 -33555,-51555 -33555,-51555 -34555,-46555 -40555,-44555 -43555,-44555 -43555,-44555 -43555,-37555 -48555,-35555 -51555,-34555 -51555,-34555 -51555,-27555 -55555,-24555 -56555,-24555 -56555,-24555 -57555,-16555 -59555,-12555 -60555,-12555 -60555,-12555 -60555,-4255 -61555,-765 -61555,-665 -61555,-435 -61555,7555 -61555,11555 -60555,11555 -60555,11555 -60555,19555 -58555,23555 -57555,23555 -57555,23555 -57555))
SELECT ST_ISVALID(
ST_BUFFER(
ST_GEOMFROMTEXT('MULTILINESTRING((15 0,1 -18,-17 -13),(-10 -1,-5 -4,-15 4,1 -9,-5 1))'),
61958,
ST_BUFFER_STRATEGY('join_miter', 199)));
ST_ISVALID(
ST_BUFFER(
ST_GEOMFROMTEXT('MULTILINESTRING((15 0,1 -18,-17 -13),(-10 -1,-5 -4,-15 4,1 -9,-5 1))'),
61958,
ST_BUFFER_STRATEGY('join_miter', 199)))
1
SELECT ST_ISVALID(
ST_BUFFER(
ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(-2 0,-17 -11,12 1,-19 -12,-1 0)'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,-7 -6,11 0,18 13,-14 11,0 0),(6 9,4 -2,-1 3,6 9)))')),
41751));
ST_ISVALID(
ST_BUFFER(
ST_SYMDIFFERENCE(
ST_GEOMFROMTEXT('LINESTRING(-2 0,-17 -11,12 1,-19 -12,-1 0)'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,-7 -6,11 0,18 13,-14 11,0 0),(6 9,4 -2,-1 3,6 9)))')),
41751))
1
######################################################################################################
# BUG#20518038 : WL#7929 : ST_BUFFER() IS PRODUCING NON-CLOSED POLYGONS
######################################################################################################
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('LINESTRING(0 0,0 1)'), 1));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('LINESTRING(0 0,0 1)'), 1))
POLYGON((-1 0,-0.9808 -0.1951,-0.9239 -0.3827,-0.8315 -0.5556,-0.7071 -0.7071,-0.5556 -0.8315,-0.3827 -0.9239,-0.1951 -0.9808,0 -1,0.1951 -0.9808,0.3827 -0.9239,0.5556 -0.8315,0.7071 -0.7071,0.8315 -0.5556,0.9239 -0.3827,0.9808 -0.1951,1 0,1 1,1 1,0.9808 1.1951,0.9239 1.3827,0.8315 1.5556,0.7071 1.7071,0.5556 1.8315,0.3827 1.9239,0.1951 1.9808,0 2,-0.1951 1.9808,-0.3827 1.9239,-0.5556 1.8315,-0.7071 1.7071,-0.8315 1.5556,-0.9239 1.3827,-0.9808 1.1951,-1 1,-1 0))
######################################################################################################
# BUG#19894465 : WL#7929 : BOOST GEOMETRY BUFFER BUGS
######################################################################################################
# Scenario 1
SELECT ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('LINESTRING(0 0,0 1)'), 1)) > 0;
ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('LINESTRING(0 0,0 1)'), 1)) > 0
1
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('LINESTRING(0 0,0 1)'), 1));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('LINESTRING(0 0,0 1)'), 1))
POLYGON((-1 0,-0.9808 -0.1951,-0.9239 -0.3827,-0.8315 -0.5556,-0.7071 -0.7071,-0.5556 -0.8315,-0.3827 -0.9239,-0.1951 -0.9808,0 -1,0.1951 -0.9808,0.3827 -0.9239,0.5556 -0.8315,0.7071 -0.7071,0.8315 -0.5556,0.9239 -0.3827,0.9808 -0.1951,1 0,1 1,1 1,0.9808 1.1951,0.9239 1.3827,0.8315 1.5556,0.7071 1.7071,0.5556 1.8315,0.3827 1.9239,0.1951 1.9808,0 2,-0.1951 1.9808,-0.3827 1.9239,-0.5556 1.8315,-0.7071 1.7071,-0.8315 1.5556,-0.9239 1.3827,-0.9808 1.1951,-1 1,-1 0))
# Scenario 2
SELECT ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('LINESTRING(1 1,1 1)'), 1)) > 0;
ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('LINESTRING(1 1,1 1)'), 1)) > 0
1
SELECT ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON(1 1,1 1,1 1,1 1)'), 1)) > 0;
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('LINESTRING(1 1,1 1)'), 1));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('LINESTRING(1 1,1 1)'), 1))
POLYGON((2 1,1.9808 1.1951,1.9239 1.3827,1.8315 1.5556,1.7071 1.7071,1.5556 1.8315,1.3827 1.9239,1.1951 1.9808,1 2,0.8049 1.9808,0.6173 1.9239,0.4444 1.8315,0.2929 1.7071,0.1685 1.5556,0.0761 1.3827,0.0192 1.1951,0 1,0.0192 0.8049,0.0761 0.6173,0.1685 0.4444,0.2929 0.2929,0.4444 0.1685,0.6173 0.0761,0.8049 0.0192,1 0,1.1951 0.0192,1.3827 0.0761,1.5556 0.1685,1.7071 0.2929,1.8315 0.4444,1.9239 0.6173,1.9808 0.8049,2 1))
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON(1 1,1 1,1 1,1 1)'), 1));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
# Scenario 3
SELECT ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0, 0 8, 8 8, 8 10, -10 10, -10 0, 0 0))'), 1)) > 0;
ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0, 0 8, 8 8, 8 10, -10 10, -10 0, 0 0))'), 1)) > 0
1
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0, 0 8, 8 8, 8 10, -10 10, -10 0, 0 0))'), 1));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0, 0 8, 8 8, 8 10, -10 10, -10 0, 0 0))'), 1))
POLYGON((1 7,8 7,8.1951 7.0192,8.3827 7.0761,8.5556 7.1685,8.7071 7.2929,8.8315 7.4444,8.9239 7.6173,8.9808 7.8049,9 8,9 10,8.9808 10.1951,8.9239 10.3827,8.8315 10.5556,8.7071 10.7071,8.5556 10.8315,8.3827 10.9239,8.1951 10.9808,8 11,-10 11,-10.1951 10.9808,-10.3827 10.9239,-10.5556 10.8315,-10.7071 10.7071,-10.8315 10.5556,-10.9239 10.3827,-10.9808 10.1951,-11 10,-11 0,-10.9808 -0.1951,-10.9239 -0.3827,-10.8315 -0.5556,-10.7071 -0.7071,-10.5556 -0.8315,-10.3827 -0.9239,-10.1951 -0.9808,-10 -1,0 -1,0.1951 -0.9808,0.3827 -0.9239,0.5556 -0.8315,0.7071 -0.7071,0.8315 -0.5556,0.9239 -0.3827,0.9808 -0.1951,1 0,1 7))
# Scenario 4
SELECT ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 10,0 8,0 0))'), 1)) > 0;
ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 10,0 8,0 0))'), 1)) > 0
1
SELECT ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((1 1,10 10,0 8,1 1))'), 1)) > 0;
ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((1 1,10 10,0 8,1 1))'), 1)) > 0
1
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 10,0 8,0 0))'), 1));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 10,0 8,0 0))'), 1))
POLYGON((-1 0,-0.9808 -0.1951,-0.9239 -0.3827,-0.8315 -0.5556,-0.7071 -0.7071,-0.5556 -0.8315,-0.3827 -0.9239,-0.1951 -0.9808,0 -1,0.1951 -0.9808,0.3827 -0.9239,0.5556 -0.8315,0.7071 -0.7071,10.7071 9.2929,10.8236 9.4329,10.9128 9.5917,10.9718 9.764,10.9984 9.9442,10.992 10.1263,10.9526 10.3041,10.8817 10.4719,10.7815 10.624,10.6553 10.7554,10.5074 10.8617,10.3427 10.9394,10.1666 10.986,9.985 10.9999,9.8039 10.9806,-0.1961 8.9806,-0.3835 8.9235,-0.5562 8.8311,-0.7075 8.7067,-0.8317 8.5552,-0.924 8.3824,-0.9808 8.1949,-1 8,-1 0))
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((1 1,10 10,0 8,1 1))'), 1));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((1 1,10 10,0 8,1 1))'), 1))
POLYGON((0.0101 0.8586,0.0528 0.6793,0.1277 0.511,0.2322 0.3593,0.3628 0.2293,0.5151 0.1254,0.6838 0.0513,0.8632 0.0094,1.0473 0.0011,1.2298 0.0268,1.4044 0.0854,1.5654 0.1752,1.7071 0.2929,10.7071 9.2929,10.8236 9.4329,10.9128 9.5917,10.9718 9.764,10.9984 9.9442,10.992 10.1263,10.9526 10.3041,10.8817 10.4719,10.7815 10.624,10.6553 10.7554,10.5074 10.8617,10.3427 10.9394,10.1666 10.986,9.985 10.9999,9.8039 10.9806,-0.1961 8.9806,-0.3772 8.9261,-0.5449 8.8385,-0.693 8.7209,-0.8163 8.5776,-0.9105 8.4135,-0.9721 8.2347,-0.9989 8.0475,-0.9899 7.8586,0.0101 0.8586))
# Scenario 5
SELECT ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((2 2,2 8,8 8,8 2,2 2))'), 1)) > 0;
ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((2 2,2 8,8 8,8 2,2 2))'), 1)) > 0
1
SELECT ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((2 2,8 2,8 8,2 8,2 2))'), 1)) > 0;
ST_AREA(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((2 2,8 2,8 8,2 8,2 2))'), 1)) > 0
1
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((2 2,2 8,8 8,8 2,2 2))'), 1));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((2 2,2 8,8 8,8 2,2 2))'), 1))
POLYGON((1 2,1.0192 1.8049,1.0761 1.6173,1.1685 1.4444,1.2929 1.2929,1.4444 1.1685,1.6173 1.0761,1.8049 1.0192,2 1,8 1,8.1951 1.0192,8.3827 1.0761,8.5556 1.1685,8.7071 1.2929,8.8315 1.4444,8.9239 1.6173,8.9808 1.8049,9 2,9 8,8.9808 8.1951,8.9239 8.3827,8.8315 8.5556,8.7071 8.7071,8.5556 8.8315,8.3827 8.9239,8.1951 8.9808,8 9,2 9,1.8049 8.9808,1.6173 8.9239,1.4444 8.8315,1.2929 8.7071,1.1685 8.5556,1.0761 8.3827,1.0192 8.1951,1 8,1 2))
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((2 2,8 2,8 8,2 8,2 2))'), 1));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((2 2,8 2,8 8,2 8,2 2))'), 1))
POLYGON((1 2,1.0192 1.8049,1.0761 1.6173,1.1685 1.4444,1.2929 1.2929,1.4444 1.1685,1.6173 1.0761,1.8049 1.0192,2 1,8 1,8.1951 1.0192,8.3827 1.0761,8.5556 1.1685,8.7071 1.2929,8.8315 1.4444,8.9239 1.6173,8.9808 1.8049,9 2,9 8,8.9808 8.1951,8.9239 8.3827,8.8315 8.5556,8.7071 8.7071,8.5556 8.8315,8.3827 8.9239,8.1951 8.9808,8 9,2 9,1.8049 8.9808,1.6173 8.9239,1.4444 8.8315,1.2929 8.7071,1.1685 8.5556,1.0761 8.3827,1.0192 8.1951,1 8,1 2))
######################################################################################################
# BUG#20510010 : ST_BUFFER() : MYSQLD GOT SIGNAL 11
######################################################################################################
SELECT ST_ASTEXT(
ST_BUFFER(
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                            'MULTILINESTRING((12 -12,-15 19),(2 -9,-4 -8,18 3,-9 -8),(13 11,-15 9,-16 6,-17 5)),'
                            'LINESTRING(14 -16,-3 18,-13 -7,-10 1))'), 6561));
ST_ASTEXT(
ST_BUFFER(
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION('
                            'MULTILINESTRING((12 -12,-15 19),(2 -9,-4 -8,18 3,-9 -8),(13 11,-15 9,-16 6,-17 5)),'
                            'LINESTRING(14 -16,-3 18,-13 -7,-10 1))'), 6561))
POLYGON((-1156 6456,-1274 6445,-1304 6439,-1348 6425,-1518 6402,-1773 6315,-2103 6239,-2405 6103,-2534 6064,-2580 6040,-2705 5997,-2933 5866,-3276 5712,-3537 5527,-3668 5456,-3713 5420,-3793 5373,-3987 5206,-4324 4967,-4534 4744,-4661 4639,-4702 4590,-4743 4554,-4796 4488,-4804 4480,-4888 4370,-4898 4359,-5207 4031,-5359 3787,-5476 3644,-5508 3584,-5520 3569,-5558 3495,-5586 3459,-5662 3304,-5889 2941,-5985 2690,-6081 2509,-6127 2359,-6153 2305,-6156 2297,-6208 2103,-6347 1739,-6388 1495,-6453 1278,-6467 1133,-6486 1063,-6503 812,-6560 470,-6554 246,-6578 -2,-6565 -129,-6571 -212,-6532 -515,-6523 -815,-6477 -1013,-6451 -1282,-6419 -1386,-6407 -1478,-6354 -1636,-6354 -1637,-6352 -1643,-6292 -1822,-6235 -2069,-6160 -2235,-6102 -2428,-6085 -2483,-6084 -2485,-6076 -2512,-6034 -2591,-6001 -2689,-5919 -2833,-5914 -2846,-5871 -2916,-5854 -2950,-5827 -2994,-5793 -3053,-5708 -3242,-5620 -3366,-5485 -3621,-5474 -3635,-5468 -3646,-5423 -3701,-5367 -3799,-5262 -3920,-5247 -3946,-5186 -4012,-5169 -4039,-5058 -4156,-5058 -4156,-4963 -4290,-4936 -4321,-4860 -4392,-4675 -4620,-4607 -4677,-4531 -4764,-4410 -4859,-4378 -4894,-4317 -4939,-4285 -4973,-4124 -5086,-4000 -5204,-3890 -5272,-3686 -5442,-3608 -5484,-3524 -5550,-3396 -5614,-3341 -5655,-3285 -5680,-3235 -5716,-3050 -5799,-2910 -5886,-2762 -5943,-2555 -6055,-2470 -6081,-2383 -6125,-2257 -6160,-2175 -6198,-2131 -6209,-2061 -6240,-1858 -6286,-1708 -6344,-1521 -6375,-1326 -6435,-1237 -6445,-1153 -6468,-1035 -6477,-926 -6504,-895 -6505,-807 -6525,-595 -6531,-439 -6557,-219 -6551,-47 -6569,42 -6561,120 -6567,224 -6554,359 -6560,376 -6558,479 -6560,691 -6524,846 -6520,1092 -6463,1233 -6450,1320 -6425,1389 -6416,1463 -6392,1582 -6372,1630 -6365,1635 -6363,1747 -6344,1950 -6266,2100 -6232,2356 -6117,2466 -6084,2493 -6073,2488 -6061,2499 -6058,2530 -6048,2533 -6046,2604 -6023,2647 -6000,2948 -5884,3133 -5768,3273 -5705,3583 -5485,3593 -5478,3664 -5440,3689 -5420,3720 -5402,3739 -5387,4037 -5199,4195 -5050,4321 -4960,4572 -4694,4657 -4623,4701 -4570,4854 -4425,4885 -4397,4888 -4393,4971 -4315,5095 -4138,5204 -4024,5384 -3736,5472 -3628,5514 -3551,5637 -3385,5652 -3353,5714 -3265,5801 -3070,5886 -2934,5996 -2645,6077 -2493,6101 -2415,6181 -2249,6198 -2181,6238 -2091,6285 -1886,6344 -1732,6389 -1461,6449 -1262,6457 -1185,6497 -1029,6504 -923,6523 -837,6529 -632,6557 -463,6550 -224,6574 18,6567 92,6575 228,6556 371,6558 449,6525 646,6520 822,6474 1021,6447 1298,6426 1366,6411 1478,6353 1650,6342 1717,6273 1898,6232 2076,6163 2230,6072 2528,6039 2589,6011 2672,5903 2863,5882 2918,5865 2952,5770 3104,5705 3249,5626 3360,5464 3662,5422 3713,5391 3769,5254 3929,5200 4015,5033 4194,4960 4297,4933 4328,4861 4396,4647 4655,4598 4696,4572 4726,4435 4835,4344 4932,4117 5098,3997 5211,3880 5284,3652 5470,3599 5499,3585 5510,3461 5574,3330 5670,3072 5790,2907 5893,2737 5958,2517 6075,2359 6123,2193 6200,1918 6269,1705 6351,1482 6388,1286 6447,1160 6460,977 6505,696 6521,436 6564,165 6556,6 6572,-85 6563,-276 6573,-549 6536,-849 6527,-1156 6456))
######################################################################################################
# BUG#20510359 : ST_BUFFER() RETURNS NULL INSTEAD OF AN EMPTY GEOMETRY
######################################################################################################
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'), -10));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'), -10))
GEOMETRYCOLLECTION EMPTY
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'), -8));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'), -8))
GEOMETRYCOLLECTION EMPTY
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'), -6));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'), -6))
GEOMETRYCOLLECTION EMPTY
######################################################################################################
# BUG#20517621 : ASSERTION `GEOMETRY::IS_VALID_GEOTYPE(GT)' FAILED
######################################################################################################
SELECT ST_ASTEXT(ST_BUFFER(0x000000000200000000000000000000000000000000, 1));
ERROR 22023: Invalid GIS data provided to function st_buffer.
######################################################################################################
# BUG#20558289 : ST_BUFFER() RETURNS EMPTY GEOMETRY WITH [MULTI]LINESTRING GEOMETRY
######################################################################################################
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('LINESTRING(0 0,10 10,10 0,0 10)'), 20, ST_BUFFER_STRATEGY('end_flat')));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('LINESTRING(0 0,10 10,10 0,0 10)'), 20, ST_BUFFER_STRATEGY('end_flat')))
POLYGON((-10 0,-14.1421 -4.1421,-4.1421 -14.1421,-1.1114 -16.6294,2.3463 -18.4776,6.0982 -19.6157,10 -20,13.9018 -19.6157,17.6537 -18.4776,21.1114 -16.6294,24.1421 -14.1421,26.6294 -11.1114,28.4776 -7.6537,29.6157 -3.9018,30 0,30 10,29.6157 13.9018,28.4776 17.6537,26.6294 21.1114,24.1421 24.1421,21.1114 26.6294,17.6537 28.4776,13.9018 29.6157,10 30,6.0982 29.6157,2.3463 28.4776,-1.1114 26.6294,-4.1421 24.1421,-14.1421 14.1421,-10 10,-10 0))
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('MULTILINESTRING((0 0,10 10),(10 0,0 10))'), 50, ST_BUFFER_STRATEGY('end_flat')));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('MULTILINESTRING((0 0,10 10),(10 0,0 10))'), 50, ST_BUFFER_STRATEGY('end_flat')))
POLYGON((5 15,-25.3553 45.3553,-35.3553 35.3553,-5 5,-35.3553 -25.3553,-25.3553 -35.3553,5 -5,35.3553 -35.3553,45.3553 -25.3553,15 5,45.3553 35.3553,35.3553 45.3553,5 15))
######################################################################################################
# BUG#20558350 : ST_BUFFER RETURNS INCORRECT RESULT WITH NEGATIVE DISTANCE AND POLYGON WITH HOLE
######################################################################################################
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4))'), -5));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4))'), -5))
GEOMETRYCOLLECTION EMPTY
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4))'), -10));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0),(4 4,4 6,6 6,6 4,4 4))'), -10))
GEOMETRYCOLLECTION EMPTY
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), -10));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0,0 0,0 0))'), -10))
GEOMETRYCOLLECTION EMPTY
######################################################################################################
# BUG#20594425 : WL#7929 : HANG/INFINITE LOOP WITH ST_BUFFER() FUNCTION
######################################################################################################
######################################################################################################
# BUG#20558379 : ST_BUFFER() RETURNS AN ERROR WITH VALID GEOMETRYCOLLECTION INPUT
######################################################################################################
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0)), POLYGON((10 10,10 20,20 20,20 10,10 10)))'), -1));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0)), POLYGON((10 10,10 20,20 20,20 10,10 10)))'), -1))
MULTIPOLYGON(((1 9,1 1,9 1,9 9,1 9)),((19 11,19 19,11 19,11 11,19 11)))
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 5,5 5,5 0,0 0)), POLYGON((10 10,10 20,20 20,20 10,10 10)))'), -1));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 5,5 5,5 0,0 0)), POLYGON((10 10,10 20,20 20,20 10,10 10)))'), -1))
MULTIPOLYGON(((19 11,19 19,11 19,11 11,19 11)),((4 1,4 4,1 4,1 1,4 1)))
######################################################################################################
# BUG#20607166 : SERVER IS HANGING WITH MULTIPOINT GEOMETRY AND '1' AS POINT_PER_CIRCLE' VALUE
######################################################################################################
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('MULTIPOINT(5 13,9 -10,12 -7,-1 -16,-18 15,-13 -10,-1 5,12 -7)'), 1, ST_BUFFER_STRATEGY('point_circle',1)));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('MULTIPOINT(5 13,9 -10,12 -7,-1 -16,-18 15,-13 -10,-1 5,12 -7)'), 1, ST_BUFFER_STRATEGY('point_circle',1)))
MULTIPOLYGON(((6 13,4.5 13.866,4.5 12.134,6 13)),((10 -10,8.5 -9.134,8.5 -10.866,10 -10)),((0 -16,-1.5 -15.134,-1.5 -16.866,0 -16)),((-17 15,-18.5 15.866,-18.5 14.134,-17 15)),((-12 -10,-13.5 -9.134,-13.5 -10.866,-12 -10)),((0 5,-1.5 5.866,-1.5 4.134,0 5)),((11.5 -7.866,13 -7,11.5 -6.134,11.5 -7.866)))
######################################################################################################
# BUG#20451555 : ASSERTION FAILED: INDEX >= 0 && UNSIGNED(INDEX) < INFO.COUNT
######################################################################################################
SELECT ST_DISJOINT(
ST_GEOMFROMTEXT('LINESTRING(-2305843009213693956 4611686018427387906, -33 -92, 78 83)'),
ST_GEOMFROMTEXT('MULTILINESTRING((20 100, 31 -97, -46 57, -20 -4),(-71,-4))'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_DISJOINT(
ST_GEOMFROMTEXT('LINESTRING(-2305843009213693956 4611686018427387906, -33 -92, 78 83)'),
ST_GEOMFROMTEXT('LINESTRING(20 100, 31 -97, -46 57, -20 -4)'));
ST_DISJOINT(
ST_GEOMFROMTEXT('LINESTRING(-2305843009213693956 4611686018427387906, -33 -92, 78 83)'),
ST_GEOMFROMTEXT('LINESTRING(20 100, 31 -97, -46 57, -20 -4)'))
0
######################################################################################################
# BUG#20841874 : ASSERT:GEOMETRY::EQUALS(CURRENT_ROBUST_RING.FRONT(), CURRENT_ROBUST_RING.BACK())
######################################################################################################
DO(SELECT ST_BUFFER(
ST_GEOMFROMTEXT("MULTILINESTRING((-58 19, 61 88),(1.922421e+307 1.520384e+308, 15 42, 89 -93, -89 -22),(-63 -5, -262141 -536870908, -3 87, 77 -69))")
, 0x98));
######################################################################################################
# Bug#20911624 THE SERVER CRASH WHEN TEST ST_INTERSECTS WITH ST_BUFFER
######################################################################################################
set @geom2 = 'LINESTRING(13115313.13 3600370.58,13115298.74 3600352.19,13115286.56 3600333.86)';
set @geom1 = 'MULTIPOLYGON(((13050407.79 3563020.68,
13050868.54 3563726.04,
13051606.22 3564382.08,
13052406.14 3565013.08,
13052935.88 3565405.58,
13053068.6 3565492.3,
13053090.69 3565506.74,
13053275.46 3565608.74,
13053431.71 3565694.76,
13053550.03 3565767.11,
13053638.67 3565839.82,
13053668.93 3565856.61,
13053779.26 3565918.73,
13053956.52 3566005.25,
13054060.42 3566063.3,
13054194.71 3566142.09,
13054357.51 3566256.82,
13054505.03 3566350.49,
13054661.07 3566436.45,
13054802.25 3566522.64,
13054928.46 3566594.29,
13055098.17 3566686.63,
13055270.14 3566763.83,
13055417.69 3566879.26,
13055573.76 3566965.05,
13055707.21 3567080.68,
13055795.86 3567123.77,
13055899.89 3567224.75,
13055996.66 3567312.52,
13056070.88 3567354.8,
13056144.97 3567405.48,
13056256.27 3567469.49,
13056374.84 3567526.09,
13056500.97 3567606.3,
13056619.82 3567685.43,
13056760.4 3567801.24,
13056886.92 3567880.44,
13056967.85 3567946.66,
13057018.54 3567975.9,
13057078.93 3568010.81,
13057153.02 3568077.23,
13057256.9 3568163.9,
13057419.91 3568259.49,
13057466.67 3568275.3,
13057568.41 3568309.87,
13057716.45 3568448.89,
13057901.41 3568536.73,
13058065.16 3568557.28,
13058309.51 3568594.52,
13058560.65 3568608.66,
13058827.7 3568587.53,
13059035.66 3568552.06,
13059168.15 3568501.54,
13059257.56 3568472.94,
13059323.84 3568437.31,
13059376.48 3568356.18,
13059455.78 3568271.67,
13059472.69 3568253.78,
13059487.18 3568195.57,
13059598.31 3568167.06,
13059768.14 3568168.75,
13059879.59 3568207.34,
13059967.59 3568253.33,
13059994.66 3568277.23,
13060085.61 3568358.24,
13060129.6 3568440.33,
13060151.5 3568530.12,
13060158.64 3568625.49,
13060158.58 3568699.78,
13060121.7 3568757.21,
13060114.09 3568831.4,
13060040.38 3568852.7,
13059981.07 3568910.01,
13059906.88 3568953.27,
13059840.02 3568981.97,
13059781.47 3569018.28,
13059736.12 3569083.98,
13059698.75 3569156.86,
13059662.26 3569244.96,
13059618.22 3569326.04,
13059558.43 3569362.58,
13059580.39 3569436.05,
13059639.24 3569473.68,
13059772.71 3569534.27,
13059926.78 3569603.13,
13060030.54 3569671.9,
13060178.22 3569740.09,
13060311.57 3569772.17,
13060465.64 3569797.88,
13060584.5 3569858.7,
13060687.15 3569934.9,
13060886.35 3569997.74,
13061077.87 3570054.45,
13061306.57 3570140.53,
13061410.15 3570209.5,
13061564.36 3570279.16,
13061689.87 3570327.02,
13061807.29 3570366.61,
13061910.86 3570413.9,
13062028.72 3570468.85,
13062131.84 3570515.36,
13062334.67 3570614.59,
13062541.39 3570738.44,
13062761.55 3570854.33,
13062975 3570956.13,
13063211.89 3571042.69,
13063417.8 3571106.53,
13063595.16 3571162.84,
13063779.22 3571239.88,
13064045.15 3571296.81,
13064267.57 3571359.17,
13064422.2 3571398.62,
13064636.24 3571460.8,
13064814.01 3571498.93,
13065035.91 3571567.92,
13065302.44 3571621.37,
13065620.71 3571696.24,
13065894.84 3571732.63,
13066073.01 3571805.78,
13066325.42 3571819.7,
13066525.84 3571855.57,
13066704.35 3571891.39,
13066815.45 3571919.79,
13066981.65 3571953.33,
13067023.43 3571961.58,
13067179.84 3571975.47,
13067467.6 3571939.34,
13067610.02 3571942.64,
13067660.78 3571943.75,
13067883.84 3571940.32,
13068054.5 3571966.38,
13068218.68 3571955.78,
13068389.36 3571937.78,
13068590.36 3571897.2,
13068858.36 3571883.66,
13069051.65 3571880.24,
13069252.17 3571817.48,
13069467.55 3571805.55,
13069646.67 3571787.65,
13069779.69 3571748.57,
13069935.84 3571709.31,
13070062.01 3571677.64,
13070226.02 3571638.37,
13070359.06 3571576.69,
13070552.37 3571522.17,
13070700.63 3571476.72,
13070834.35 3571438.44,
13070922.84 3571408.25,
13071019.9 3571369.67,
13071116.06 3571317.39,
13071219.89 3571249.95,
13071286.24 3571197.8,
13071382.34 3571152.6,
13071493.81 3571115.52,
13071671.42 3571078.18,
13071834.61 3571003.7,
13072011.76 3570929.49,
13072195.97 3570848.78,
13072336.94 3570782.3,
13072447.28 3570753.22,
13072580.39 3570695.03,
13072735.24 3570658.29,
13072890.38 3570629.38,
13073059.91 3570572.37,
13073236.92 3570544.17,
13073472.82 3570473.75,
13073708.19 3570396.02,
13073899.42 3570362,
13074076.75 3570319.84,
13074267 3570301.42,
13074465.75 3570275.1,
13074598.08 3570240.78,
13074759.22 3570185.08,
13074949.83 3570106.51,
13075112.6 3570057.93,
13075288.77 3570024.61,
13075427.47 3570012.91,
13075603.64 3569979.58,
13075780.36 3569968,
13075948.54 3570000.59,
13076162.45 3569930.61,
13076324.38 3569925.53,
13076478.35 3569935.63,
13076632.67 3569960.69,
13076808.65 3569985.96,
13076948.28 3570031.47,
13077250.49 3570137.94,
13077463.47 3570273.88,
13077683.63 3570452.76,
13077970.16 3570565.5,
13078205.48 3570729.01,
13078528.38 3570944.17,
13078786.96 3571083.82,
13079029.2 3571267.3,
13079235.8 3571437.02,
13079397.79 3571590.56,
13079566.66 3571818.25,
13079706.71 3572015.46,
13079794.9 3572169.78,
13079853.75 3572301.19,
13080034.3 3572447.83,
13080086.27 3572587.07,
13080145.93 3572667.44,
13080226.48 3572755.22,
13080307.95 3572841.53,
13080322.5 3572915.66,
13080367.69 3573033.26,
13080404.39 3573128.04,
13080456.07 3573238.31,
13080515.78 3573325.23,
13080589.57 3573382.65,
13080670.18 3573441.2,
13080737.48 3573476.76,
13080847.94 3573525.3,
13080899.6 3573539.69,
13081019.02 3573572.89,
13081107.37 3573608.82,
13081188.8 3573650.53,
13081263.07 3573686.04,
13081351.45 3573727.57,
13081455.16 3573777.26,
13081492.66 3573828.29,
13081508.89 3573846,
13081551.21 3573892.93,
13081618.71 3573972.2,
13081677.61 3574060.4,
13081781.37 3574138.76,
13081855.22 3574180.5,
13081966.68 3574229.55,
13082218.41 3574290.19,
13082314.55 3574354.66,
13082500.15 3574408.39,
13082610.68 3574442.58,
13082692.23 3574470.44,
13082744.28 3574521.07,
13082825.86 3574600.77,
13082930.05 3574701.32,
13083121.97 3574778.62,
13083203.5 3574827.65,
13083292.25 3574958.82,
13083381.42 3575068.75,
13083425.88 3575170.85,
13083566.26 3575352.47,
13083669.96 3575512.74,
13083781.37 3575688.44,
13083899.47 3575760.25,
13083959.16 3575840.4,
13084070.27 3575875.08,
13084150.81 3575947.85,
13084240.25 3575998.92,
13084343.73 3576056.24,
13084439.6 3576098.51,
13084521.24 3576098.58,
13084668.9 3576111.51,
13084727.98 3576103.51,
13084801.98 3576087.08,
13084941.91 3576167.98,
13085075 3576248.18,
13085185.12 3576336.34,
13085303.64 3576394.58,
13085421.36 3576460.18,
13085613.72 3576541.66,
13085760.95 3576622.17,
13085930.38 3576726.48,
13086011.34 3576756.48,
13086085.16 3576785.59,
13086173.75 3576808.81,
13086254.32 3576831.13,
13086335.32 3576853.13,
13086416.67 3576921.2,
13086578.09 3577024.78,
13086666.7 3577048.78,
13086762.44 3577130.42,
13086784.39 3577093.59,
13086979.74 3577356.56,
13087237.08 3577550.45,
13087370.43 3577669.36,
13087503.2 3577774.32,
13087529.48 3577788.97,
13087601 3577827.67,
13087618.3 3577845.27,
13087726.19 3577956.23,
13087777.31 3578000.78,
13087806.92 3578053.44,
13087844.17 3578113.2,
13087880.55 3578195.25,
13087939.75 3578277.38,
13088005.37 3578352.44,
13088064.56 3578412.68,
13088189.68 3578533.2,
13088248.41 3578622.34,
13088345.26 3578721.44,
13088359.11 3578735.39,
13088476.22 3578834.04,
13088593.77 3578961.06,
13088684.61 3579050.53,
13088769.92 3579134.6,
13088880.69 3579239.96,
13088953.8 3579344.38,
13089050.07 3579435.62,
13089182 3579571.15,
13089270.3 3579638.85,
13089410.56 3579729.89,
13089520.02 3579828.24,
13089630.83 3579904.15,
13089704.41 3579971.43,
13089932.28 3580115.89,
13090080.29 3580170.03,
13090234.21 3580208.93,
13090381.94 3580284.7,
13090775.99 3580400.49,
13091085.24 3580492.57,
13091402.66 3580621.21,
13091727.58 3580674.53,
13092000.94 3580755.93,
13092311.79 3580844.73,
13092645.04 3580925.08,
13093015.9 3581010.72,
13093319.82 3581096.86,
13093608.51 3581180.93,
13094002.66 3581324.48,
13094336.91 3581421.46,
13094604.63 3581505.71,
13094819.63 3581569.15,
13094910.09 3581595.89,
13095199.26 3581701.56,
13095407.02 3581790.19,
13095430.5 3581800.13,
13095713.29 3581912.46,
13096010.59 3582017.78,
13096308.62 3582123.6,
13096583.21 3582222.48,
13096783.88 3582286.78,
13096940.25 3582342.86,
13097163.14 3582422.11,
13097289.13 3582516.4,
13097497.29 3582558.39,
13097624.13 3582616.01,
13097758.2 3582666.48,
13097884.45 3582679.81,
13097980.69 3582686.92,
13098047.01 3582701.14,
13098144.3 3582744.49,
13098322.02 3582781.25,
13098433.75 3582795.68,
13098566.71 3582825.15,
13098797.24 3582869.26,
13098945.02 3582876.04,
13099078.69 3582928.24,
13099174.51 3582987.66,
13099300.46 3583033.16,
13099375.02 3583063.52,
13099471.25 3583101.17,
13099633.99 3583154.31,
13099781.81 3583185.24,
13099944.92 3583208.68,
13100114.73 3583248.42,
13100307.24 3583287.43,
13100388.53 3583333.88,
13100602.13 3583380.59,
13100735.35 3583465.1,
13100890.09 3583519.43,
13101134.46 3583582.71,
13101274.74 3583615,
13101422.29 3583677.94,
13101584.9 3583739.98,
13101739.52 3583787.85,
13101894.59 3583828.29,
13101998.15 3583866.87,
13102138.36 3583921.71,
13102322.22 3583977.34,
13102477.28 3584032.46,
13102677.09 3584125.45,
13102876.17 3584202.97,
13103030.83 3584280.13,
13103186.85 3584349.98,
13103319.96 3584418.92,
13103423.01 3584671.48,
13103497.38 3584916.8,
13103563.55 3585177.18,
13103667.58 3585436.58,
13103800.83 3585668.5,
13103933.58 3585906.08,
13104052.5 3586137.57,
13104178.68 3586352.65,
13104356.11 3586599.21,
13104430.74 3586820.98,
13104520.18 3587051.74,
13104565.09 3587191.41,
13104646.45 3587340.63,
13104727.97 3587532.87,
13104809.84 3587733.44,
13104928.67 3587955.8,
13105025.49 3588111.77,
13105099.85 3588221.92,
13105144.85 3588333.89,
13105240.83 3588452.12,
13105330.05 3588592.7,
13105359.82 3588680.96,
13105397.74 3588784.57,
13105471.6 3588865.68,
13105553.29 3588968.92,
13105657.84 3589079.91,
13105709.86 3589235.62,
13105799.65 3589382.63,
13105947.65 3589528.88,
13105956.58 3589541.35,
13106052.33 3589676.89,
13106119.23 3589816.82,
13106129.51 3589838.42,
13106194.29 3589970.84,
13106268.53 3590185.59,
13106269.44 3590340.05,
13106314.72 3590451.28,
13106366.74 3590620.75,
13106404.39 3590812.15,
13106419.85 3591019.39,
13106471.89 3591196.69,
13106517.74 3591447.6,
13106555.24 3591587.73,
13106577.58 3591742.35,
13106615.11 3591920.15,
13106674.87 3592126.19,
13106712.56 3592392.23,
13106735.66 3592539.71,
13106737.78 3592545.49,
13106739.46 3592549.73,
13106741.25 3592554.1,
13106773.23 3592634.35,
13106840.51 3592752.32,
13106877.21 3592870.3,
13106908.45 3593039.84,
13106982.7 3593231.46,
13107020.24 3593541.69,
13107006.67 3593726.05,
13107006.7 3593829.43,
13106976.93 3594036.61,
13107006.78 3594288.61,
13107051.14 3594561.29,
13107051.12 3594857.08,
13107103.03 3595100.72,
13107117.49 3595345.05,
13107177.05 3595639.51,
13107243.83 3595993.45,
13107303.3 3596362.06,
13107347.49 3596636.07,
13107451.31 3596959.73,
13107532.94 3597217.28,
13107621.46 3597504.09,
13107658.69 3597770.28,
13107718.58 3598071.4,
13107718.39 3598287.05,
13107710.39 3598611.79,
13107740 3598893.23,
13107773.1 3599149.57,
13107784.12 3599202.14,
13107832.58 3599429.01,
13107862.33 3599562.02,
13107803.35 3599778.21,
13107788.68 3599955.47,
13107788.57 3600125.47,
13107784.22 3600279.56,
13107781.59 3600362.35,
13107788.29 3600570.06,
13107789.17 3600599.61,
13107782.23 3600814.04,
13107753.04 3601080.56,
13107730.81 3601354.82,
13107769.1 3601554.35,
13107746.92 3601709.84,
13107761.31 3601894.73,
13107806.58 3602086,
13107807.35 3602226.87,
13107807.36 3602463.32,
13107800.56 3602701.59,
13107771.66 3603020.38,
13107749.57 3603161.07,
13107743.18 3603457.1,
13107758.27 3603694.61,
13107752.03 3603953.33,
13107714.61 3604213.18,
13107707.94 3604450.23,
13107686.77 3604710.56,
13107628.2 3605022.37,
13107620.68 3605267.08,
13107569.71 3605542.32,
13107569.92 3605786.13,
13107548.88 3606023.49,
13107519.22 3606231.67,
13107512.63 3606499,
13107506.05 3606772.81,
13107483.94 3606935.89,
13107477.3 3607151.55,
13107469.81 3607291.62,
13107455.41 3607455.22,
13107456.32 3607632.75,
13107456.36 3607691.81,
13107307.16 3607687.52,
13107188.72 3607689.85,
13106883.21 3607672.71,
13106666.8 3607624.6,
13106436.42 3607582.71,
13106220.2 3607547.22,
13106012.24 3607534.63,
13105707.84 3607462.15,
13105544.21 3607440.83,
13105384.56 3607465.63,
13104879.55 3607580.63,
13104878.66 3607575.72,
13104876.54 3607574.03,
13104870.65 3607573.35,
13104863.87 3607574.61,
13104757.05 3607606.56,
13104614.15 3607647.56,
13104581.93 3607653.64,
13104407.31 3607697.72,
13104074.02 3607776.22,
13103947.07 3607808.42,
13103752.8 3607851.01,
13103566.26 3607895.38,
13103190.21 3607979.14,
13103029.18 3608019.9,
13102914.15 3608041.55,
13102549.87 3608125.46,
13102447.18 3608143.06,
13102278.6 3608186.5,
13102192.86 3608204.95,
13102155.65 3608211.39,
13102094.39 3608216.66,
13102038.23 3608225.91,
13101967.23 3608239.5,
13101919.49 3608251.65,
13101864.55 3608259,
13101815.04 3608267.89,
13101799.41 3608266.52,
13101783.35 3608259.59,
13101754.2 3608239.97,
13101724.61 3608216.6,
13101658.21 3608158.72,
13101654.89 3608155.93,
13101618.86 3608122.74,
13101519.97 3608018.45,
13101509.33 3608005.69,
13101506.44 3607996.72,
13101506.77 3607987.55,
13101521.13 3607922.85,
13101533.38 3607857.72,
13101540.89 3607817.43,
13101564.52 3607697.62,
13101572.55 3607626.46,
13101572.88 3607616.91,
13101574.61 3607563.45,
13101561.81 3607480.61,
13101558.01 3607430,
13101573.07 3607288.31,
13101574.7 3607238.47,
13101561.59 3607195.16,
13101514.19 3607076.84,
13101452.81 3606943.12,
13101419.3 3606895.14,
13101405.43 3606866.17,
13101400.32 3606846.68,
13101399.86 3606827.29,
13101410.02 3606788.73,
13101417.99 3606775.46,
13101504.56 3606696.75,
13101539.21 3606657.29,
13101552.71 3606640.51,
13101638.35 3606511.94,
13101648.09 3606496.38,
13101660.36 3606466.02,
13101664.55 3606448.66,
13101670.82 3606396.21,
13101668.69 3606363.34,
13101654.66 3606286.16,
13101639 3606234.92,
13101622.45 3606187.93,
13101604.69 3606145.17,
13101495.91 3605917.04,
13101480.15 3605884.93,
13101462.4 3605858.59,
13101445.09 3605826.32,
13101418.8 3605798.52,
13101403.61 3605787.09,
13101388.76 3605780.84,
13101372.35 3605769.91,
13101288.56 3605743.66,
13101274.25 3605734.85,
13101260.28 3605722.04,
13101239.54 3605698.62,
13101225.11 3605669.39,
13101204.8 3605644.7,
13101184.94 3605616.01,
13101137.13 3605559.26,
13101115.95 3605542.69,
13101101.97 3605534.93,
13101071.15 3605522.83,
13101033.46 3605512.55,
13101013.61 3605503.65,
13100996.65 3605502.29,
13100968.4 3605516.88,
13100949.77 3605513.94,
13100932.8 3605500.84,
13100912.06 3605491.93,
13100808.83 3605465.37,
13100788.54 3605464.62,
13100774.57 3605469.02,
13100762.83 3605477.09,
13100636.39 3605574.78,
13100516.32 3605620.99,
13100496.36 3605625.32,
13100477.83 3605622.45,
13100459.96 3605604.72,
13100434.52 3605563.89,
13100423.52 3605537.49,
13100411.27 3605486.39,
13100402.69 3605440.91,
13100395.9 3605417.81,
13100374.3 3605321.49,
13100280.02 3604943.62,
13100239.72 3604761.92,
13100230.71 3604733.24,
13100218.04 3604707.75,
13100210.81 3604688.27,
13100147.26 3604595.42,
13100025.16 3604427.33,
13099922.15 3604276.9,
13099879.81 3604222.55,
13099816.57 3604137.12,
13099786.88 3604082.3,
13099772.09 3604047.66,
13099749.63 3604005.71,
13099634.64 3603759.84,
13099611.29 3603716.62,
13099600.72 3603702.71,
13099567.13 3603631.77,
13099554.45 3603597.56,
13099521.72 3603465.94,
13099495.47 3603393.15,
13099478.44 3603331.54,
13099473.76 3603303.6,
13099473.75 3603275.71,
13099249.85 3603211.52,
13099005.06 3603217.38,
13098923.53 3603284.22,
13098841.66 3603357.71,
13098701.57 3603490.68,
13098604.42 3603602.1,
13098538.22 3603706.31,
13098472.04 3603883.81,
13098404.51 3604039.41,
13098330.62 3604180.99,
13098234.66 3604328.56,
13098160.43 3604492.63,
13098056.79 3604692.99,
13097975.14 3604804.54,
13097900.84 3604909.38,
13097871.17 3605049.54,
13097856.77 3605168.65,
13097805 3605287.07,
13097715.75 3605421.24,
13097626.85 3605577.58,
13097500.99 3605742.56,
13097367.44 3605958.16,
13097285.36 3606063.37,
13097144.13 3606243.14,
13097010.01 3606407.83,
13096898.52 3606542.68,
13096749.8 3606685.75,
13096660.35 3606776.46,
13096549.55 3606874.33,
13096392.31 3606937.73,
13096206.47 3607066.94,
13095997.87 3607167.41,
13095833.73 3607229.59,
13095624.64 3607337.89,
13095475.8 3607407.73,
13095408.83 3607445.6,
13095244.57 3607538.79,
13095051.36 3607661.25,
13094827.82 3607740.48,
13094819.77 3607740.65,
13094686.24 3607742.73,
13094506.69 3607813.63,
13094320.75 3607809.96,
13094164.67 3607776.39,
13094015.01 3607771.53,
13093895.66 3607772.89,
13093755.03 3607813.25,
13093591.44 3607888.57,
13093398.07 3607952.06,
13093212.49 3608035.45,
13093085.66 3608110.72,
13092937.55 3608193.67,
13092810.46 3608284.02,
13092677.38 3608366.24,
13092446.91 3608397.7,
13092306.66 3608428.04,
13092024.71 3608443.93,
13091794.56 3608436.67,
13091646.83 3608435.95,
13091520.52 3608465.24,
13091349.98 3608463.84,
13091261.86 3608486.21,
13091180.85 3608485.95,
13091068.95 3608439.99,
13090958.74 3608373.12,
13090869.75 3608305.1,
13090729.14 3608207.56,
13090596.08 3608065.51,
13090477.8 3607916.3,
13090366.87 3607788.15,
13090292.35 3607661.4,
13090210.96 3607541.86,
13090129.74 3607488.83,
13090042.1 3607436.42,
13089886.38 3607366.97,
13089650.89 3607296.51,
13089465.7 3607292.85,
13089311.34 3607282.46,
13089157.11 3607272.01,
13089001.56 3607270.01,
13088862.52 3607252.56,
13088706.97 3607234.1,
13088582.34 3607225.21,
13088456.42 3607266.57,
13088361.4 3607309.51,
13088272.72 3607389.82,
13088199.7 3607522.02,
13088103.38 3607622.88,
13088007.95 3607732.69,
13087927.22 3607804.97,
13087846.15 3607878.56,
13087721.49 3607920.19,
13087566.33 3607947.49,
13087389.95 3607952.45,
13087235.15 3607958.09,
13087071.75 3607933.5,
13086984.53 3607888.12,
13086880.85 3607828.08,
13086807.11 3607744.63,
13086718.07 3607559.07,
13086688.79 3607441.11,
13086680.61 3607328.97,
13086562.81 3607180.26,
13086489.03 3607120.64,
13086370.82 3607067.48,
13086171.56 3606992.84,
13086046.53 3606991.93,
13085869.23 3607006.27,
13085721.55 3607028.64,
13085551.92 3607065.66,
13085395.71 3607118.09,
13085181.21 3607185.33,
13085077.99 3607223.7,
13084915.28 3607312.95,
13084767.45 3607403.54,
13084678.97 3607464.19,
13084633.99 3607545.66,
13084560.92 3607657.61,
13084568.24 3607769.33,
13084560.65 3607917.22,
13084605.44 3608034.59,
13084642.1 3608175.3,
13084664.3 3608397.66,
13084664.86 3608634.26,
13084716.34 3608818.83,
13084761.55 3608981.63,
13084827.47 3609114.99,
13084887.45 3609187.31,
13084975.85 3609320.66,
13085027.79 3609416.48,
13085049.86 3609497.81,
13085139.11 3609660.64,
13085153.6 3609770.96,
13085191.03 3609874.39,
13085205.84 3609985.1,
13085198.27 3610066.65,
13085153.16 3610133.42,
13085079.64 3610171.72,
13084968.34 3610208.94,
13084820.79 3610269.47,
13084575.84 3610330.93,
13084383.54 3610363.48,
13084153.99 3610410.62,
13083901.9 3610482.41,
13083709.35 3610523.03,
13083486.9 3610571.25,
13083338.03 3610655.57,
13083241.23 3610717.37,
13083152.56 3610786.04,
13083025.86 3610929.65,
13082943.09 3611007.86,
13082930.68 3611019.6,
13082452.36 3611306.96,
13082395.19 3611341.37,
13082232.5 3611456.83,
13082076.28 3611579.42,
13081808.32 3611682.81,
13081667.93 3611700.9,
13081514.72 3611645.18,
13081363.77 3611590.52,
13081170.58 3611491.14,
13080930.04 3611338.37,
13080902.77 3611321.17,
13080844.32 3611284.13,
13080599.75 3611074.43,
13080586.46 3611063.15,
13080508.93 3610997.36,
13080479.12 3610945.64,
13080427.18 3610887.67,
13080360.29 3610814.41,
13080242.24 3610757.62,
13080063.81 3610686.79,
13079730.88 3610602.66,
13079553.4 3610560.92,
13079442.33 3610517.11,
13079331.39 3610496.83,
13079145.58 3610453.94,
13079034.61 3610380.36,
13078971.34 3610335.86,
13078909.3 3610292.1,
13078768.65 3610182.5,
13078568.61 3609990.6,
13078296.13 3609827.26,
13078185.47 3609790.34,
13078037.38 3609782.67,
13077911.45 3609804.8,
13077800.89 3609840.99,
13077705.14 3609914.04,
13077646.16 3609966.71,
13077564.86 3610031.26,
13077491.57 3610039.36,
13077365.34 3610074.95,
13077262.45 3610036.81,
13077100.32 3609975.12,
13076915.38 3609861.96,
13076695.45 3609807.02,
13076569.38 3609753.24,
13076407.41 3609669.75,
13076253 3609571.03,
13076127.43 3609508.84,
13075994.79 3609499.82,
13075818.49 3609407.52,
13075671.02 3609344.83,
13075590.26 3609358.88,
13075479.57 3609289.18,
13075369.22 3609213.15,
13075273.7 3609122.71,
13075200.11 3609018.27,
13075178.14 3608920.89,
13075170.45 3608817.22,
13075119.22 3608683.41,
13075023.68 3608584.97,
13074868.51 3608462.81,
13074699.37 3608327.11,
13074522.26 3608263.82,
13074316.32 3608230.86,
13074183.5 3608191.55,
13074028.27 3608173.65,
13073887.89 3608171.64,
13073703.89 3608190.67,
13073526.04 3608196.32,
13073268.2 3608192.96,
13073075.89 3608198.96,
13072868.73 3608227,
13072757.75 3608269.8,
13072661.86 3608328.97,
13072536.03 3608358.08,
13072402.81 3608356.83,
13072269.55 3608370.87,
13072032.65 3608416.15,
13071839.8 3608400.54,
13071705.57 3608379.54,
13071609.98 3608372.26,
13071336.08 3608285.98,
13071272.74 3608266.14,
13071251.44 3608259.7,
13070942.4 3608163.47,
13070551.83 3607991.75,
13070465.36 3607931.17,
13070323.8 3607873.04,
13070205.33 3607793.73,
13070085.94 3607714.54,
13070003.96 3607626.51,
13069907.55 3607531.28,
13069751.42 3607430.87,
13069601.63 3607322.07,
13069430.12 3607207.45,
13069243.94 3607099.44,
13069072.86 3607029.07,
13068885.9 3606972.53,
13068744.58 3606931.86,
13068625.11 3606926.78,
13068498.8 3606907.11,
13068416.91 3606900.96,
13068296.95 3606844.7,
13068200.49 3606794.12,
13068132.96 3606692.02,
13068058.16 3606588.76,
13068035.93 3606485.32,
13067983.67 3606324.73,
13067983.55 3606183.37,
13067991.19 3606094.58,
13067998.81 3605990.54,
13067998.73 3605902.16,
13067998.64 3605790.14,
13067991.73 3605686.78,
13067991.65 3605590.78,
13067991.55 3605479.15,
13068036.66 3605389.47,
13068044.27 3605256.23,
13068006.64 3605190.59,
13067947.77 3605139.19,
13067827.84 3605075.56,
13067701.56 3605025.82,
13067613.67 3605011.73,
13067574.42 3605005.49,
13067432.45 3604999.94,
13067321.91 3604997.23,
13067253.74 3604995.42,
13067030.38 3604991.15,
13066982.68 3604992.91,
13066635.06 3605003.26,
13066464.43 3605056.81,
13066345.18 3605102.5,
13066233.34 3605177.08,
13066158.59 3605295.68,
13066084.2 3605415.52,
13066046.8 3605496.42,
13065979.85 3605704.52,
13065935.69 3605822.77,
13065905.12 3605919.47,
13065898.42 3606038.52,
13065927.48 3606118.84,
13066001.44 3606178.83,
13066121.02 3606222.01,
13066224.93 3606310.82,
13066265.69 3606615.14,
13066204.48 3606781.86,
13065551.37 3607867.09,
13065071.7 3608924.9,
13065056.04 3609006.01,
13064883.49 3609880.88,
13064787.63 3609931.72,
13064690.47 3609982.73,
13064519.64 3610033.21,
13064430.64 3610068.27,
13064319.22 3610119.2,
13064208.15 3610177.12,
13064170.5 3610228.4,
13064074.79 3610278.48,
13063948.13 3610306.51,
13063844.8 3610312.11,
13063741.16 3610302.38,
13063629.77 3610249.42,
13063518.96 3610173.51,
13063370.47 3610103.6,
13063200.82 3610055.01,
13063052.39 3609962.47,
13062934.45 3609863.69,
13062838.02 3609773.03,
13062742.05 3609711.94,
13062645.31 3609709.07,
13062542.16 3609722.06,
13062387.05 3609739.75,
13062260.65 3609788.75,
13062171.92 3609838.03,
13062016.83 3609901.51,
13061882.9 3609905.48,
13061735 3609901.4,
13061631.86 3609869.88,
13061491.16 3609858.77,
13061364.74 3609877.47,
13061283.19 3609912.89,
13061194.43 3609977.9,
13061075.63 3610064.2,
13060949.16 3610208.68,
13060808.36 3610398.59,
13060689.47 3610595.59,
13060563.02 3610777.8,
13060466.5 3610895.47,
13060386.93 3611007.92,
13060378.83 3611019.68,
13060389.01 3611073.72,
13060240.86 3611204.57,
13059987.24 3611279.29,
13059930.07 3611296.29,
13059632.24 3611296.42,
13059599.58 3611293.79,
13059477.6 3611273.99,
13059470.38 3611267.09,
13059373.72 3611214.41,
13059209.73 3611004.69,
13059205.84 3610992.78,
13059198.29 3610967.54,
13059131.72 3610860.99,
13059035.45 3610719.6,
13058939.16 3610555.95,
13058865.31 3610414.8,
13058791.43 3610288.81,
13058746.93 3610154.76,
13058681.15 3610006.77,
13058651.43 3609858.55,
13058622.47 3609666.28,
13058585.93 3609532.3,
13058540.49 3609443.96,
13058422.31 3609362.62,
13058295.31 3609317.39,
13058169.51 3609310.03,
13058013.45 3609273.69,
13057901.5 3609244.22,
13057768.01 3609230.56,
13057648.41 3609201.03,
13057500.22 3609158.51,
13057358.45 3609115.18,
13057216.64 3609049.28,
13057104.95 3608984.79,
13056985.21 3608971.03,
13056859.09 3608994.43,
13056747.47 3609011.37,
13056619.98 3609079.18,
13056478.5 3609163.23,
13056322.01 3609224.93,
13056112.5 3609325,
13056000.8 3609430.09,
13055918.06 3609528.69,
13055851.53 3609595.18,
13055746.51 3609649.94,
13055656.59 3609747.71,
13055596.86 3609844.58,
13055521.7 3610016.48,
13055453.91 3610143.78,
13055394.17 3610203.45,
13055297.29 3610294.29,
13055147.83 3610393.34,
13054990.88 3610492.67,
13054856.41 3610636.85,
13054743.29 3610785.97,
13054661.4 3610898.77,
13054578.6 3611011.57,
13054436.46 3611049.15,
13054389.16 3611061.57,
13054102.88 3611278.19,
13054074.35 3611340.85,
13054013.26 3611472.44,
13053804.27 3611727.03,
13053672.44 3611907.82,
13053640.47 3611951.73,
13053379.64 3612206.49,
13053133.35 3612417.8,
13053068.13 3612477.69,
13052768.73 3612753.26,
13052497.68 3612912.72,
13052478.61 3612924.97,
13052277.8 3613081.66,
13051735.72 3613347.75,
13051468.18 3613509.88,
13051186.84 3613745.65,
13051008.73 3613876.35,
13050874.7 3614083.35,
13050733.78 3614348,
13050615.45 3614598.96,
13050569.34 3615065.05,
13050530.99 3615286.07,
13050523.18 3615657.16,
13050440.87 3615944.28,
13050248.07 3616289.54,
13050143.95 3616458.78,
13049995.69 3616671.43,
13049625.61 3617087.19,
13049425.49 3617520.15,
13049240.32 3617945.28,
13049129.22 3618225.77,
13048980.48 3618444.54,
13048840.3 3618708.6,
13048478.68 3618908.8,
13048154.57 3619012.74,
13047969.49 3619134.23,
13047703.64 3619351.88,
13047690.13 3619362.09,
13047474.63 3619525.76,
13047305.51 3619626.08,
13046426.85 3620835.83,
13045691.1 3621528.12,
13045542.29 3621683.92,
13045433.66 3621776.48,
13045394.29 3621810.33,
13045231.33 3621950.8,
13045093.34 3622044.4,
13045045.8 3622076.85,
13044867.77 3622204.11,
13044526.42 3622302.13,
13044325.98 3622296.33,
13043880.9 3622053.85,
13043873.64 3622049.67,
13043732.88 3621859.48,
13043679.99 3621792.45,
13043599.36 3621690.41,
13043458.66 3621514.21,
13043332.7 3621361.69,
13043139.77 3621074.88,
13042999.32 3620899.45,
13042754.72 3620689.03,
13042665.11 3620639.85,
13042389.17 3620755.98,
13042091.79 3620828.34,
13041838.74 3620812.77,
13041429.42 3620642.33,
13041229.31 3620617.17,
13041013.35 3620591.16,
13040655.67 3620664.26,
13040380.75 3620779.72,
13040112.47 3620954.74,
13039873.22 3621116.72,
13039799.59 3621165.97,
13039769.82 3621191.28,
13039605.68 3621330.91,
13039405.31 3621428.81,
13039150.67 3621565.98,
13039085.61 3621600.86,
13038914.89 3621712.43,
13038750.94 3621838.19,
13038691.51 3621878.76,
13038587.14 3621949.97,
13038424.69 3622134.96,
13038327.99 3622245.37,
13038277.97 3622301.12,
13038306.13 3622447.02,
13038335.06 3622594.21,
13038364.11 3622740.35,
13038392.15 3622886.49,
13038421.88 3622915.53,
13038420.47 3623266.94,
13038506.5 3623618.47,
13038564.28 3623822.54,
13038651.83 3623911.29,
13038680.84 3624027.72,
13038767.55 3624174.72,
13038826.19 3624320.42,
13038913.02 3624467.3,
13038882.49 3624788.53,
13038881.64 3624934.22,
13038851.93 3625080.37,
13038851.95 3625226.58,
13038821.45 3625372.34,
13038791.71 3625518.48,
13038761.87 3625664.73,
13038703.29 3625810.89,
13038673.54 3625956.6,
13038614.07 3626103.09,
13038525.76 3626248.88,
13038408.54 3626393.76,
13038349.41 3626539.7,
13038319.32 3626685.43,
13038289.9 3626831.81,
13038260.13 3626977.39,
13038259.28 3627123.2,
13038288.01 3627270.4,
13038287.09 3627328.87,
13038287.02 3627445.7,
13038315.93 3627475.03,
13038403.29 3627621.04,
13038519.49 3627767.92,
13038664.7 3627856.16,
13038810.25 3628003.06,
13038809.7 3628148.74,
13038750.11 3628295.31,
13038661.7 3628441.09,
13038574.08 3628586.28,
13038543.35 3628732.34,
13038543.22 3628878.16,
13038542.32 3629025.27,
13038541.3 3629171.34,
13038598.95 3629317.05,
13038657.38 3629464.14,
13038773.64 3629580.45,
13038918.89 3629697.96,
13039006.34 3629844.76,
13039005.37 3629932.23,
13038975.45 3630136.41,
13038974.55 3630282.5,
13038945.58 3630282.87,
13038886.93 3630340.38,
13038857.08 3630486.3,
13038797.48 3630632.54,
13038680.11 3630778.81,
13038561.88 3630924.95,
13038414.87 3631069.99,
13038326.54 3631215.91,
13038296.74 3631361.93,
13038266.95 3631507.96,
13038237.17 3631653.72,
13038177.7 3631799.87,
13038089.77 3631945.34,
13038031.23 3632089.86,
13037942.9 3632235.85,
13037853.82 3632380.99,
13037795.32 3632526.69,
13037766.84 3632664.71,
13037765.61 3632672.6,
13037735.92 3632818.63,
13037638.8 3632913.57,
13037589.26 3632962.16,
13037471.4 3633107.11,
13037412.99 3633251.33,
13037412.1 3633398.52,
13037411.33 3633544.69,
13037381.7 3633690.52,
13037352.08 3633835.96,
13037322.46 3633981.92,
13037287.29 3634040.02,
13037278.41 3634054.15,
13037234.36 3634126.62,
13037116.76 3634270.34,
13036999.97 3634414.88,
13036911.5 3634558.46,
13036852.32 3634704.13,
13036822.78 3634849.48,
13036793.24 3634994.96,
13036734.1 3635140.21,
13036704.57 3635285.54,
13036733.5 3635432.63,
13036761.44 3635579.96,
13036703.2 3635725.35,
13036556.92 3635838.37,
13036410.67 3635951.66,
13036322.88 3636096,
13036206.28 3636240.39,
13036117.63 3636383.59,
13036029.43 3636527.69,
13035912.88 3636671.56,
13035853.84 3636816.72,
13035722.96 3637039,
13034344.93 3639396.18,
13034166.64 3639718.99,
13034031.87 3640065.79,
13033993.06 3640457.76,
13034029.92 3640792.92,
13033954.65 3640999.77,
13033797.29 3641323.29,
13033685.12 3641594.83,
13033431.56 3641797.69,
13033178.81 3641889.91,
13032986.03 3641916.81,
13032555.05 3641836.59,
13032197.87 3641759.89,
13031967.29 3641751,
13031714.65 3641727.15,
13031364.01 3641711.7,
13031102.3 3641830.89,
13030899.74 3642113.97,
13030576.8 3642479.47,
13030432.76 3642710.02,
13030206.54 3643298.36,
13030173.64 3643381.26,
13030130.55 3643492.32,
13030080.94 3643637.43,
13030001.09 3643872.97,
13029760.42 3644187.57,
13029535.46 3644348.93,
13029298.66 3644425.75,
13029048.21 3644459.9,
13028829.73 3644482.88,
13028566.68 3644679.56,
13028341.19 3644905.75,
13028122.61 3645049.74,
13027965.02 3645170.62,
13027738.75 3645343.84,
13027528.73 3645509.56,
13027279.97 3645691.05,
13027069.76 3645842.55,
13026762.33 3645993.48,
13026512.72 3646072.54,
13026477.32 3646083.84,
13026192.56 3646062.49,
13025903.38 3645925.37,
13025825.63 3645848.76,
13025799.79 3645822.87,
13025716.52 3645705.01,
13025655.78 3645588.03,
13025595.14 3645484.49,
13025534.08 3645366.4,
13025442.76 3645218.94,
13025396.64 3645107.73,
13025358.42 3645011.91,
13025289.7 3644901.85,
13025245.24 3644827.76,
13025235.47 3644793.17,
13025222.38 3644746.02,
13025176.15 3644656.74,
13025138.8 3644598.41,
13025108.33 3644568.71,
13025055.29 3644517.19,
13025003.15 3644472.65,
13024905.94 3644443.74,
13024823.22 3644406.9,
13024726.18 3644362.92,
13024657.84 3644310.86,
13024590.96 3644258.64,
13024477.82 3644125.45,
13024357.59 3643985.09,
13024266.58 3643888.14,
13024170.03 3643783.68,
13024086.57 3643709.98,
13024080.69 3643705.64,
13023997.25 3643642.11,
13023922.34 3643598.06,
13023802.63 3643537.18,
13023682.82 3643478.13,
13023585.99 3643440.17,
13023537.75 3643421.3,
13023467.11 3643394.55,
13023332.92 3643394.04,
13023214.92 3643406.78,
13023163.36 3643414.16,
13023111.25 3643421.13,
13023022.12 3643420.14,
13023014.48 3643410.8,
13022961.65 3643345.56,
13022915.21 3643181.83,
13022832.95 3643047.85,
13022801.67 3642905.52,
13022732.96 3642712.43,
13022694.9 3642585.24,
13022656.08 3642444.03,
13022610.03 3642317.96,
13022556.9 3642183.44,
13022518.49 3642086.79,
13022487.18 3641930.3,
13022420.07 3641839.9,
13022351.63 3641720.19,
13022298.8 3641631.12,
13022253.18 3641526.13,
13022170.77 3641405.71,
13022118.38 3641339.43,
13022020.8 3641249.37,
13021923.21 3641158.02,
13021841.23 3641075.16,
13021788.39 3641015.73,
13021690.79 3640872.56,
13021637.5 3640775.56,
13021607.03 3640679.04,
13021561.81 3640574.96,
13021522.9 3640440.7,
13021484.88 3640344.03,
13021475.99 3640239.82,
13021466.62 3640053.72,
13021471.64 3639845.63,
13021469.79 3639661.11,
13021452.86 3639489.75,
13021444.26 3639356.52,
13021428.24 3639243.74,
13021412.91 3639163.09,
13021374.41 3639079.9,
13021314.3 3638982.79,
13021238.68 3638893.27,
13021164.14 3638788.63,
13021102.79 3638682.99,
13021057.93 3638557.21,
13021041.78 3638445.48,
13021069.5 3638320.24,
13021105.9 3638253.08,
13021171.32 3638164.48,
13021222 3638076.9,
13021228.66 3637957.72,
13021190.45 3637839.06,
13021114.66 3637719.26,
13021031.79 3637614.11,
13020919.64 3637486.8,
13020828.65 3637366.41,
13020730.89 3637247.63,
13020648.64 3637135.13,
13020587.23 3637023.68,
13020534.7 3636918.37,
13020473.24 3636763.18,
13020390.22 3636554.56,
13020335.4 3636383.09,
13020311.65 3636234.15,
13020273.38 3636101.18,
13020242.91 3635996.96,
13020204.33 3635885,
13020150.89 3635795.23,
13020060.71 3635676.74,
13019947.51 3635527.71,
13019782.64 3635326.76,
13019639.85 3635156.19,
13019504.13 3635000.37,
13019384.07 3634865.7,
13019293.77 3634732.93,
13019196.12 3634615.35,
13019127.01 3634459.16,
13019087.92 3634288.41,
13019071.74 3634110.55,
13019062.35 3633924.69,
13019030.51 3633755.31,
13018999.02 3633613.27,
13018997.34 3633450.91,
13019017.68 3633235.04,
13019038.04 3633086.64,
13019058.4 3632945.5,
13019093.66 3632828.52,
13019108.91 3632722.83,
13019122.51 3632581.42,
13019118.74 3632534.67,
13019113.2 3632463.44,
13019082.76 3632352.35,
13019044.22 3632225.83,
13019006 3632138.02,
13018975.55 3632063.21,
13018953.44 3632019.64,
13018900.41 3631959.91,
13018840.25 3631908.35,
13018758.7 3631879.56,
13018624.17 3631842.9,
13018401.16 3631846.02,
13018237.63 3631869.48,
13017709.9 3631922.96,
13017568.56 3631940.68,
13017448.93 3631957.69,
13017241.21 3631977.58,
13017091.7 3631980.19,
13016973.21 3631975.9,
13016808.33 3631972.19,
13016666.89 3631946.13,
13016584.54 3631919.56,
13016531.71 3631846.69,
13016500.23 3631751.47,
13016492.58 3631647.54,
13016484.13 3631558.91,
13016452.69 3631418.27,
13016382.89 3631132.22,
13016344.18 3631013.74,
13016291.39 3630896.31,
13016207.84 3630781.29,
13016125.19 3630664.73,
13016034.47 3630563.5,
13015966.19 3630498.48,
13015883.49 3630434.66,
13015778.63 3630364.34,
13015628.06 3630257.21,
13015478.03 3630157.36,
13015305.66 3630015.18,
13015154.77 3629863.76,
13015027.22 3629772.01,
13014907.07 3629657.71,
13014823.47 3629578.85,
13014726.24 3629485.12,
13014628.23 3629399.28,
13014500.27 3629284.78,
13014388.62 3629213.24,
13014276.09 3629136.11,
13014163.1 3629079.77,
13014073.61 3629015.95,
13013961.11 3628944.61,
13013751.46 3628817.12,
13013631.29 3628746.71,
13013496.77 3628645.76,
13013384.38 3628559.77,
13013271.13 3628451.83,
13013144.26 3628374.1,
13013047.2 3628309.75,
13012927.29 3628245.56,
13012808.52 3628194.94,
13012689.43 3628145.62,
13012450.49 3628009.72,
13012308.55 3627944.96,
13012159.1 3627859.32,
13012032.5 3627800.99,
13011958.15 3627757.99,
13011881.35 3627730.33,
13011876.68 3627729.04,
13011528.57 3627784.32,
13011321.91 3627846,
13010710.13 3628026,
13010466.65 3628069.86,
13010326.91 3628114.65,
13010231.7 3628129.68,
13010098.81 3628128.53,
13010002.32 3628121.4,
13009876.73 3628053.8,
13009727.9 3627963.96,
13009572.02 3627837.37,
13009430.46 3627724.79,
13009320.11 3627686.64,
13009194.58 3627649.29,
13009083.47 3627625.66,
13008921.19 3627579.43,
13008750.94 3627540.62,
13008655.02 3627510.89,
13008500.39 3627494.76,
13008396.83 3627464.6,
13008293.72 3627426.17,
13008204.25 3627344.06,
13008129.42 3627239.75,
13008039.96 3627119.76,
13007933.57 3626704.32,
13007880.87 3626585.37,
13007828.5 3626473.43,
13007782.88 3626377.16,
13007722.85 3626235.85,
13007692.4 3626116.08,
13007689.89 3625900.87,
13007710.19 3625708.75,
13007808.24 3625160.58,
13007835.69 3624931.47,
13007833.99 3624789.89,
13007782 3624701.38,
13007655.98 3624633.68,
13007552.44 3624573.2,
13007396.79 3624506.37,
13007286.36 3624468.15,
13007145.97 3624430.92,
13006953.43 3624407.99,
13006769.37 3624392.43,
13006591.47 3624370.35,
13006466.6 3624385.24,
13006355.66 3624355,
13006244.14 3624303.51,
13006110.24 3624192.67,
13005961.39 3624052.65,
13005803.55 3623823.9,
13005698.71 3623654.24,
13005608.68 3623542.58,
13005518.62 3623439.92,
13005436.67 3623336.72,
13005356.37 3623229.97,
13005354.25 3623226.73,
13005316 3623116.06,
13005240.66 3622975.89,
13005172.67 3622873.17,
13005075.71 3622748.66,
13004992.42 3622630.69,
13004903.45 3622528.2,
13004812.43 3622396.95,
13004752.84 3622301.07,
13004686.05 3622258.32,
13004551.7 3622222.89,
13004396.35 3622204.36,
13004039.3 3622160.06,
13003957.46 3622131.76,
13003844.97 3622053.45,
13003710.78 3621960.79,
13003650.17 3621866.28,
13003629.2 3621770.84,
13003619.33 3621725.19,
13003595.45 3621622.55,
13003557.83 3621512.62,
13003533.84 3621415.95,
13003459.25 3621329.51,
13003369.7 3621265.47,
13003272.42 3621201.61,
13003160.27 3621138.46,
13003077.52 3621088.17,
13002980.23 3621046.86,
13002898.22 3620982.93,
13002807.8 3620888.91,
13002747.56 3620787.36,
13002679.14 3620670.97,
13002633.86 3620545.87,
13002573.16 3620428.97,
13002504.86 3620327.77,
13002406.69 3620211.67,
13002316.56 3620096.72,
13002144.1 3619968.39,
13002076.62 3619926.35,
13001979.7 3619854.8,
13001860.19 3619800.41,
13001740.69 3619751.21,
13001628.02 3619711.81,
13001516.16 3619706.92,
13001450.07 3619738.56,
13001361.32 3619830.3,
13001288 3619914.72,
13001244.55 3619997.48,
13001200.3 3620058.91,
13001119.26 3620119.78,
13001000.66 3620191.2,
13000867.2 3620253.91,
13000778.56 3620279.25,
13000608.01 3620328.38,
13000510.81 3620367.59,
13000399.96 3620378.63,
13000303.67 3620410.45,
13000206.92 3620413.24,
13000125.17 3620430.62,
13000043.3 3620424.54,
12999969.26 3620419.61,
12999879.8 3620391.15,
12999804.76 3620362.84,
12999685.52 3620329.3,
12999566.32 3620294.08,
12999447.13 3620238.04,
12999298.18 3620173.44,
12999179.08 3620153.19,
12999060.03 3620147.69,
12998934.1 3620157.47,
12998830.49 3620159.14,
12998764.11 3620167.22,
12998682.58 3620168.42,
12998600.17 3620154.43,
12998503.28 3620102.72,
12998436.21 3620022.1,
12998369.05 3619934.19,
12998323.14 3619838.14,
12998240.72 3619689.59,
12998172.68 3619579.82,
12998067.85 3619461.32,
12998000.77 3619410.68,
12997977.86 3619391.42,
12997904.11 3619328.92,
12997770.03 3619255.59,
12997665.76 3619173.94,
12997487.78 3619129.89,
12997404.7 3619107.31,
12997346.62 3619091.8,
12997198.4 3619070,
12997035.78 3619032.05,
12996917.28 3619031.4,
12996777.28 3619038.08,
12996681.59 3619096.04,
12996556.41 3619154.23,
12996446.55 3619205.6,
12996336.17 3619279.27,
12996254.96 3619359.25,
12996116.37 3619491.51,
12996028.52 3619542.17,
12995932.56 3619562.89,
12995813.3 3619496.32,
12995694.91 3619390.67,
12995582.85 3619278.13,
12995493.19 3619195.66,
12995404.43 3619149.93,
12995322.89 3619140.95,
12995219.76 3619132.54,
12995094.69 3619137.75,
12994960.75 3619121.44,
12994813.62 3619089.74,
12994701.6 3619059.07,
12994576.5 3619020.92,
12994479.75 3618996.58,
12994353.74 3618951.38,
12994234.9 3618860.86,
12994093.63 3618681.53,
12993966.32 3618509.53,
12993846.09 3618337.76,
12993756.02 3618204.07,
12993629.01 3617972.36,
12993523.73 3617764.88,
12993425.55 3617556.69,
12993342.12 3617362.87,
12993267.68 3617222.36,
12993207.1 3617103.54,
12993131.75 3616991.55,
12993042.84 3616924.71,
12993005.21 3616895.07,
12992945.81 3616842.73,
12992782.76 3616812.57,
12992597.54 3616812.74,
12992434.24 3616835.34,
12992226.84 3616880.13,
12992041.37 3616918.4,
12991856.61 3616963.87,
12991708.85 3617016.99,
12991516.36 3617092.31,
12991323.34 3617191.54,
12991168.49 3617274.31,
12991012.36 3617335.34,
12990908.17 3617382.28,
12990811.55 3617428.59,
12990715.36 3617474.33,
12990596.49 3617543.04,
12990523.25 3617611.03,
12990397.64 3617769.89,
12990361.02 3617873.96,
12990317.58 3617978.13,
12990272.8 3618031.4,
12990220.87 3618084,
12990052.58 3618435.86,
12989956.74 3618526.9,
12989844.56 3618611.04,
12989726.56 3618710.36,
12989570.53 3618788.21,
12989377.34 3618830.69,
12989204.91 3618857.9,
12989048.33 3618861.34,
12988900.35 3618857.8,
12988735.58 3618855.95,
12988623.39 3618865.57,
12988511.21 3618928.21,
12988422.41 3618961.26,
12988325.55 3619008.05,
12988213.36 3619024.9,
12988093.88 3618991.96,
12987981.66 3618906.46,
12987845.5 3618761.16,
12987657.23 3618573.89,
12987499.88 3618444.36,
12987290.46 3618315.92,
12986818.55 3618127.78,
12986677.03 3618063.61,
12986571.84 3618043.43,
12986430.95 3618076.36,
12986342.34 3618136.22,
12986283.64 3618211.22,
12986224.4 3618309.85,
12986195.53 3618413.73,
12986181.05 3618539.82,
12986153.07 3618628.55,
12986116.5 3618718.77,
12986057.75 3618808.66,
12985983.39 3618912.62,
12985880.38 3618982.07,
12985747.3 3619071.77,
12985599 3619170.18,
12985488.5 3619230.48,
12985376.81 3619313.3,
12985237.56 3619417.88,
12985038.5 3619582.62,
12984736.03 3619806.73,
12984639.79 3619895.4,
12984559.24 3619977.61,
12984456.7 3620066.33,
12984294.42 3620169.47,
12984138.92 3620198.96,
12984037.64 3620212.02,
12984027.87 3620213.6,
12983938.95 3620235.62,
12983842.83 3620264.35,
12983747.19 3620316.16,
12983644.82 3620397.3,
12983578.86 3620470.74,
12983498.07 3620581.32,
12983492.2 3620589.51,
12983410.2 3620699.75,
12983308.73 3620861.26,
12983219.95 3620913.08,
12983095.25 3620962.92,
12982954.93 3620999.2,
12982844.09 3620997.77,
12982673.74 3620988.27,
12982556.15 3620972.47,
12982393.03 3620940.56,
12982259.78 3620849.3,
12982184.48 3620751.86,
12982124.31 3620595.1,
12982064.23 3620408.84,
12982032.72 3620186.46,
12981994.59 3619993.3,
12981942.1 3619866.84,
12981844.46 3619783.78,
12981748.03 3619693.9,
12981622.92 3619633.67,
12981540.93 3619601.92,
12981452.17 3619563.57,
12981378.57 3619510.42,
12981304.18 3619414.2,
12981244.08 3619324.11,
12981221.21 3619227.93,
12981176.4 3619122.77,
12981108.64 3618996.34,
12980522.96 3618678.5,
12980418.92 3618581.93,
12980336.82 3618484.49,
12980240.31 3618388.29,
12980173.4 3618328.68,
12980106.49 3618231.74,
12979924.46 3617638.27,
12979879.63 3617533.47,
12979842.34 3617444.17,
12979789.84 3617377.71,
12979738.22 3617355.12,
12979672.5 3617355.4,
12979598.86 3617443.94,
12979445.46 3617673.34,
12979356.47 3617717.56,
12979224.13 3617792.17,
12979076.62 3617851.35,
12978959.1 3617881.65,
12978855.56 3617919.76,
12978707.93 3617941.97,
12978152.8 3617954.9,
12977714.86 3617924.76,
12977611.03 3617933.88,
12977507.17 3617943.74,
12977419.14 3617930.78,
12977314.35 3617872.94,
12977201.94 3617764.22,
12977075.45 3617633.94,
12976963.01 3617488.07,
12976805.88 3617306.89,
12976723.12 3617183.28,
12976656.33 3617074.19,
12976588.09 3616935.29,
12976542.98 3616818.27,
12976511.55 3616588.7,
12976465.59 3616383.05,
12976404.22 3616184.53,
12976344.22 3615926.57,
12976297.51 3615720.74,
12976244.72 3615588.58,
12976185.09 3615487.06,
12976124.99 3615421.76,
12976035.49 3615335.72,
12975946.32 3615249.71,
12975893.47 3615184.46,
12975803.97 3615091.26,
12975743.51 3615026.06,
12975646.72 3614947.52,
12975557.51 3614890.83,
12975445.8 3614842.89,
12975334.42 3614808.78,
12975221.83 3614745.77,
12975073.07 3614595.78,
12974961 3614442.94,
12974915.78 3614341.41,
12974847.64 3614239.98,
12974773 3614145.91,
12974683.48 3614066.87,
12974602.1 3614017.37,
12974460.01 3613947.5,
12974304.38 3613915.43,
12974141.04 3613890.4,
12974007.58 3613873.1,
12973873.67 3613876.52,
12973703.13 3613873.09,
12973539.89 3613885,
12973369.86 3613904.58,
12973274.02 3613929.17,
12973162.34 3613961.25,
12973096.34 3613985.07,
12973022.21 3614009.34,
12973000.53 3614031.55,
12972985.92 3614150.73,
12972986.69 3614291.71,
12973010.3 3614482.7,
12973048.11 3614645.61,
12973100.76 3614829.27,
12973138.92 3614991.25,
12973177.22 3615123.5,
12973185.15 3615264.4,
12973126.63 3615443.62,
12973053.28 3615615.76,
12973025.49 3615779.46,
12973049.26 3615970.48,
12973101.52 3616140.47,
12973214.22 3616470.09,
12973327.44 3616764.08,
12973379.79 3616859,
12973455.59 3616991.05,
12973478.57 3617101.51,
12973434.2 3617221.24,
12973412.95 3617369.84,
12973429.12 3617539.88,
12973445.31 3617694.54,
12973446.09 3617865.2,
12973424.76 3618057.79,
12973359.66 3618215.32,
12973309.45 3618424.87,
12973302.68 3618602.25,
12973370.85 3618756.49,
12973476.12 3618924.36,
12973603.68 3619136.18,
12973708.1 3619259.42,
12973760.21 3619332.27,
12973820.73 3619448.04,
12973880.95 3619610.35,
12973904.97 3619772.51,
12973876.44 3619898.61,
12973817.73 3620026.91,
12973818.75 3620204.24,
12973842.68 3620367.34,
12973896.16 3620588.13,
12973934.64 3620756.91,
12974010.72 3620977.67,
12974078.74 3621168.14,
12974131.74 3621307,
12974229.53 3621512.14,
12974297.98 3621659.22,
12974410.26 3621788.84,
12974423.92 3621810.3,
12974560.21 3622020.77,
12974609.38 3622101.02,
12974711.42 3622268.8,
12974764.11 3622408.22,
12974802.59 3622547.54,
12974871 3622759.98,
12974924.97 3622980.62,
12974985.29 3623172.13,
12975060.45 3623302.52,
12975120.68 3623375,
12975195.91 3623469.15,
12975323.53 3623613.83,
12975428.64 3623736.6,
12975517.86 3623851.96,
12975623.75 3623989.89,
12975728.29 3624134.86,
12975781.23 3624214.39,
12975998.51 3624475.56,
12976126.14 3624627.45,
12976208.91 3624735.87,
12976298.94 3624881.58,
12976366.26 3625027.74,
12976434.58 3625181.53,
12976472.93 3625336.51,
12976526.69 3625513.24,
12976556.53 3625601.23,
12976586.81 3625696.47,
12976587.58 3625815.87,
12976566.33 3625956.09,
12976522.4 3626061.88,
12976456.65 3626189.03,
12976353.44 3626370.43,
12976272.79 3626535.17,
12976191.7 3626634.36,
12976073.6 3626711.25,
12975969.47 3626773.83,
12975873.38 3626836.28,
12975829.83 3626949.07,
12975808.48 3627135.41,
12975839.06 3627252.53,
12975907.31 3627406.8,
12975982.26 3627537.29,
12976064.49 3627645.53,
12976125.02 3627748.39,
12976170.99 3627888.02,
12976186.22 3628005.47,
12976201.59 3628086.82,
12976180.15 3628199.39,
12976105.8 3628335.09,
12976025.51 3628492.93,
12975959.33 3628605.75,
12975937.86 3628739.7,
12975930.91 3628887.98,
12975924.71 3629073.63,
12975910.94 3629230.46,
12975904.81 3629459.34,
12975906.33 3629644.85,
12975913.93 3629777.71,
12976012.69 3630012.95,
12976057.79 3630099.93,
12976088 3630210.24,
12976095.62 3630329.27,
12976060.09 3630433.6,
12976001.16 3630576.31,
12975927.65 3630786.62,
12975862.24 3630929.11,
12975803.33 3631049.22,
12975722.17 3631148.84,
12975648.29 3631254.4,
12975567.11 3631382.78,
12975516.24 3631540.57,
12975480.36 3631689.77,
12975451.19 3631875.59,
12975452.76 3632105.69,
12975454.48 3632313.66,
12975462.92 3632521.63,
12975465.47 3632574.13,
12975472.26 3632720.91,
12975511.5 3632864.95,
12975518.32 3632889.4,
12975593.03 3633014.33,
12975668.07 3633144.83,
12975736.29 3633239.23,
12975803.29 3633356.07,
12975863.91 3633466.07,
12975902.28 3633583.25,
12975910 3633678.9,
12975903.99 3633827.53,
12975867.34 3633977.43,
12975808.99 3634104.98,
12975743.27 3634249.01,
12975654.62 3634376.56,
12975566.29 3634454.14,
12975454.57 3634531.19,
12975298.78 3634602.98,
12975158.41 3634652.7,
12975032.57 3634685.67,
12974899.03 3634734.98,
12974875.99 3634745.95,
12974801.73 3634782.48,
12974683.64 3634838.55,
12974557.7 3634857.32,
12974445.97 3634845.47,
12974349.1 3634803.49,
12974259.95 3634753.95,
12974169.45 3634675.01,
12974080.32 3634610.77,
12973997.54 3634502.61,
12973937.36 3634399.71,
12973950.94 3634228.8,
12973943.21 3634147.23,
12973904.83 3634015.58,
12973881.78 3633896.87,
12973739.7 3633730.98,
12973657.01 3633680.95,
12973567.95 3633616.85,
12973448.19 3633546.31,
12973195.12 3633418.41,
12973061.38 3633354.56,
12972971.64 3633281.8,
12972852.79 3633195.54,
12972784.74 3633123.45,
12972740.01 3633027.67,
12972682.25 3632914,
12972672.88 3632895.31,
12972612.9 3632784.86,
12972560.18 3632667.34,
12972540.11 3632632.02,
12972515.04 3632586.76,
12972462.77 3632506.47,
12972373.51 3632404.23,
12972269.43 3632353.12,
12972172.95 3632295.28,
12972046.77 3632200.79,
12971927.4 3632150.66,
12971831.78 3632114.24,
12971757.11 3632049,
12971697.29 3631915.64,
12971607.36 3631717.09,
12971517.88 3631561.57,
12971391.2 3631362.21,
12971242.42 3631185.1,
12971108.15 3631000.63,
12971026.35 3630852.9,
12970846.53 3630482.18,
12970786.55 3630348.33,
12970712.49 3630193.59,
12970660.05 3630088.73,
12970600.42 3629956.47,
12970555.64 3629873.91,
12970503.56 3629755.53,
12970436.81 3629673.7,
12970370.37 3629621.46,
12970303.49 3629584.06,
12970230.28 3629555.08,
12970145.63 3629547.62,
12970134.21 3629546.81,
12970023.84 3629546,
12969928.1 3629567.48,
12969795.65 3629627.09,
12969678.49 3629707.97,
12969579.86 3629806.83,
12969575.64 3629811.16,
12969510.06 3629891.45,
12969437.15 3630001.99,
12969393.61 3630113.34,
12969335.95 3630275.78,
12969277.87 3630430.95,
12969241.45 3630527.02,
12969219.46 3630586.5,
12969169.12 3630622.24,
12969087.9 3630628.87,
12969021.54 3630621.55,
12968939.6 3630553.33,
12968851.4 3630434.37,
12968754 3630188.52,
12968694.07 3630053.58,
12968627.83 3629913.32,
12968568 3629786.01,
12968456.9 3629651.4,
12968308.69 3629509,
12968154.16 3629366.89,
12967932.4 3629200.64,
12967769.82 3629103.51,
12967600.8 3629005.43,
12967400.94 3628906.76,
12967305.73 3628891.94,
12967187.78 3628875.99,
12967047.77 3628829.86,
12966937.43 3628822.1,
12966833.28 3628807.28,
12966700.82 3628806.36,
12966553.99 3628820.39,
12966420.56 3628827.89,
12966288 3628820.15,
12966162.24 3628738.24,
12966014.47 3628641.79,
12965888.65 3628546.09,
12965777.12 3628464.72,
12965650.9 3628369.18,
12965517.34 3628221.23,
12965450.79 3628118.01,
12965397.81 3627999.74,
12965345.71 3627889.78,
12965278.38 3627741.27,
12965218.16 3627593.72,
12965173.15 3627482.82,
12965002.51 3627418.1,
12964794.81 3627376.17,
12964602.61 3627341.44,
12964446.54 3627336.18,
12964358.12 3627337.86,
12964224.92 3627377.13,
12964113.33 3627423.59,
12964032.08 3627462.59,
12963906.04 3627524.15,
12963839.94 3627555.21,
12963743.67 3627601.58,
12963602.72 3627641.65,
12963425 3627660.94,
12963313.27 3627663.73,
12963216.17 3627665.97,
12963126.99 3627668.77,
12963045.17 3627677.83,
12962942.41 3627733.23,
12962808.43 3627802.86,
12962726.44 3627849.44,
12962668.03 3627903.23,
12962593.73 3627972.21,
12962482.74 3628056.97,
12962401.25 3628147.75,
12962364.52 3628208.4,
12962297.74 3628365.67,
12962261.81 3628529.69,
12962254.87 3628671.25,
12962248.78 3628863.89,
12962226.4 3629005.51,
12962212.64 3629139.06,
12962183.93 3629355.47,
12962081.25 3629610.7,
12962044.41 3629781.62,
12961978.56 3629998.83,
12961890.93 3630187.27,
12961824.17 3630299.32,
12961720.04 3630414.35,
12961631.13 3630527.78,
12961513.65 3630716.85,
12961380.39 3630920.73,
12961217.73 3631117.29,
12961121.13 3631239.35,
12961010.24 3631346.26,
12960899.24 3631468.14,
12960773.27 3631590,
12960670.01 3631703.61,
12960559.06 3631825.72,
12960425.21 3631976.76,
12960314.28 3632134.71,
12960203.4 3632256.21,
12960077.68 3632370.73,
12959974.57 3632446.25,
12959847.59 3632501.29,
12959707.03 3632525.83,
12959609.92 3632535.48,
12959506.02 3632544.48,
12959215.89 3632533.6,
12958925.83 3632501.09,
12958658.91 3632443.12,
12958480.53 3632422.64,
12958235.13 3632357.85,
12958101.98 3632313.73,
12958004.77 3632305.46,
12957893.69 3632306.6,
12957782.51 3632328.59,
12957686.7 3632357.95,
12957575.67 3632432.15,
12957487.44 3632476.42,
12957391.68 3632549.53,
12957273.5 3632623.36,
12957217.98 3632658.32,
12957192.1 3632674.46,
12957001.64 3632655.62,
12956962.68 3632651.47,
12956872.91 3632653.44,
12956259.52 3632665.75,
12956252.75 3632666.66,
12956141.9 3632679.36,
12955979.19 3632773.06,
12955784.69 3632886.72,
12955744.13 3632910.23,
12955665.46 3632961.31,
12955610.95 3632996.69,
12955471.46 3633090.6,
12955352.7 3633163.44,
12955191.29 3633287.14,
12955021.8 3633409.62,
12954881.56 3633489.62,
12954749.3 3633569.04,
12954630.98 3633626.24,
12954505.46 3633683.6,
12954409.96 3633719.46,
12954283.97 3633754.49,
12954150.77 3633789.45,
12954062.88 3633817.43,
12953973.64 3633787.16,
12953884.83 3633695.96,
12953817.08 3633599.59,
12953683 3633433.66,
12953563.33 3633276.61,
12953437.21 3633156.85,
12953355.45 3633066.32,
12953221.75 3632968.66,
12953117.2 3632894.29,
12952999.87 3632834.62,
12952969.01 3632818.74,
12952820.32 3632772.75,
12952493.14 3632668.22,
12952441.36 3632600.68,
12952420.25 3632569.35,
12952381.24 3632511.12,
12952336.25 3632385.43,
12952321.07 3632245.17,
12952313.43 3632132.77,
12952311.39 3631970.58,
12952356.79 3631881.26,
12952362.85 3631754.84,
12952369.72 3631584.81,
12952368.9 3631473.89,
12952346.95 3631362.46,
12952271.1 3631206.12,
12952196.57 3631079.99,
12952129.11 3630998.72,
12952054.52 3630917.22,
12951951 3630828.87,
12951853.8 3630739.2,
12951757.11 3630673.05,
12951659.84 3630629,
12951540.95 3630555.84,
12951428.4 3630459.38,
12951411.37 3630445.34,
12951369.39 3630412.25,
12951309.03 3630364.51,
12951127.54 3630259.15,
12951117.42 3630227.38,
12951092.39 3630164.92,
12951041.84 3630074.57,
12950987.82 3630005.49,
12950964.88 3629959.86,
12950944.52 3629905.81,
12950941.53 3629873.88,
12950941.24 3629823.93,
12950931.46 3629780.8,
12950924.25 3629738.16,
12950912.42 3629619.73,
12950910.31 3629602.02,
12950893.37 3629587.42,
12950863.17 3629552.36,
12950858.5 3629537.65,
12950854.3 3629489.69,
12950849.19 3629476.15,
12950835.15 3629457.51,
12950807.03 3629458.81,
12950777.68 3629469.57,
12950748.32 3629487.32,
12950698.88 3629497.79,
12950684.04 3629505.19,
12950669.97 3629515.82,
12950630.3 3629575.5,
12950580.47 3629630.66,
12950567.18 3629642.33,
12950523.4 3629672.08,
12950429.21 3629772.34,
12950412.56 3629783.04,
12950356.27 3629807.18,
12950263.76 3629869.64,
12950178.94 3629929.97,
12950166.2 3629936.76,
12950151.23 3629941.25,
12950100.51 3629945.62,
12950066.89 3629943.55,
12950050.25 3629938.88,
12950035.4 3629932.13,
12950021.77 3629922.5,
12949997.88 3629901.14,
12949968.96 3629882.84,
12949950.64 3629875.62,
12949894.34 3629859.46,
12949860.72 3629842.16,
12949846.21 3629825.71,
12949817.29 3629799.15,
12949766.14 3629760.56,
12949748.61 3629737.18,
12949718.81 3629688.66,
12949706.97 3629675.41,
12949693.35 3629663.74,
12949676.26 3629652.79,
12949566.3 3629606.5,
12949497.7 3629566.23,
12949443.5 3629541.89,
12949415.79 3629514.85,
12949397.48 3629485.18,
12949385.2 3629458.76,
12949370.23 3629452.45,
12949336.13 3629443.66,
12949226.49 3629380.97,
12949151.92 3629361.75,
12949077.23 3629334.53,
12949031.61 3629324.33,
12949009.94 3629291.77,
12949005.26 3629275.82,
12949007.85 3629257.66,
12949015.92 3629238.73,
12949037.75 3629206.07,
12949048.83 3629196.27,
12949046.28 3629177.95,
12949031.75 3629168.28,
12949012.17 3629167.78,
12948992.94 3629170.5,
12948975.82 3629177.32,
12948961.38 3629189.13,
12948937.87 3629214.07,
12948913.93 3629213.13,
12948889.22 3629203.81,
12948864.52 3629185.3,
12948821.83 3629142.33,
12948769.85 3629105.24,
12948758.34 3629088.15,
12948754.56 3629067.78,
12948759.31 3629005.19,
12948755.41 3628990.39,
12948746.02 3628979.72,
12948730.7 3628975.77,
12948694.01 3628974.99,
12948678.69 3628968.71,
12948657.78 3628956.34,
12948637.33 3628935.94,
12948623.59 3628912.94,
12948610.42 3628881.91,
12948609.99 3628859.28,
12948630.95 3628805.14,
12948627.61 3628786.45,
12948622.03 3628768.84,
12948564.1 3628618.04,
12948538.98 3628563.98,
12948517.3 3628530.54,
12948503.22 3628514.4,
12948476.4 3628489.22,
12948470.82 3628472.65,
12948477.66 3628446.11,
12948496.02 3628432.27,
12948514.37 3628423.6,
12948535.74 3628417.97,
12948604.88 3628405.91,
12948638.55 3628409.47,
12948688.06 3628460.83,
12948718.37 3628468.34,
12948743.1 3628461.87,
12948787.08 3628431.71,
12948804.09 3628422.43,
12948821.66 3628415.6,
12948840.45 3628411.84,
12948858.34 3628416.78,
12948907.75 3628449.65,
12948919.71 3628462.86,
12948931.65 3628501.82,
12948948.3 3628518.55,
12948984.08 3628531.02,
12949039.52 3628574.26,
12949058.75 3628580.08,
12949144.42 3628581.34,
12949161.97 3628583.7,
12949178.19 3628580.4,
12949192.63 3628570.54,
12949204.15 3628559.45,
12949223.4 3628549.23,
12949272.05 3628541.29,
12949310.4 3628541.19,
12949328.73 3628546.14,
12949346.28 3628556.28,
12949363.27 3628562.94,
12949379.04 3628561.61,
12949392.8 3628544.65,
12949397.51 3628530.72,
12949397.53 3628514.55,
12949402.59 3628473.05,
12949400.93 3628453.03,
12949389.87 3628438.36,
12949367.63 3628427.66,
12949316.53 3628430.84,
12949298.64 3628427.96,
12949282.77 3628420.76,
12949178.37 3628356.58,
12949165.97 3628342.07,
12949137.8 3628321.55,
12949071.29 3628288.34,
12949057.65 3628279.69,
12949044.46 3628268.44,
12949032.06 3628253.94,
12949028.27 3628234.61,
12949021.46 3628216.38,
12948992.54 3628158.62,
12948978.46 3628141.57,
12948881.21 3628061.29,
12948867.13 3628048.38,
12948856.85 3628034.11,
12948823.21 3627997.93,
12948808.34 3627986.47,
12948792.13 3627977.62,
12948774.12 3627971.01,
12948717.87 3627956.14,
12948684.54 3627941.06,
12948668.33 3627938.04,
12948629.96 3627942.21,
12948571.92 3627910.83,
12948550.23 3627904.69,
12948527.18 3627905.17,
12948502.79 3627908.92,
12948414.07 3627928,
12948397.85 3627923.69,
12948388.02 3627911.09,
12948381.22 3627882.9,
12948366.68 3627869.36,
12948322.73 3627858.52,
12948293.77 3627840.37,
12948281.03 3627827.44,
12948273.32 3627811.82,
12948263.93 3627798.82,
12948237.1 3627781.66,
12948108.27 3627728.73,
12948074.51 3627703.82,
12948060.42 3627688.33,
12948051.15 3627669.11,
12948008.51 3627538.29,
12947995.32 3627520.84,
12947984.57 3627533.22,
12947988.92 3627555.26,
12948008.89 3627636.09,
12948028.87 3627696.87,
12948037.03 3627712.75,
12948059.6 3627741.65,
12948086.88 3627770.71,
12948132.93 3627810.5,
12948160.66 3627838.25,
12948171.27 3627851.48,
12948177.3 3627865.46,
12948169.13 3627881.8,
12948155.47 3627894.25,
12948137.12 3627906.41,
12948116.2 3627912.8,
12948091.04 3627903.1,
12947984.03 3627850.47,
12947946.02 3627820.59,
12947876.04 3627759.92,
12947867.19 3627623.87,
12947799.96 3627469.21,
12947754.74 3627336.6,
12947716.44 3627226.1,
12947626.45 3627177.3,
12947469.49 3627106.59,
12947394.93 3627077.98,
12947364.26 3626930.15,
12947334.44 3626864.98,
12946857.38 3626771.05,
12946985.37 3626607.79,
12946906.97 3626505.81,
12946759.96 3626478.72,
12946613.41 3626511.11,
12946466.33 3626571.68,
12946407.64 3626454.94,
12946494.54 3626308.81,
12946553.42 3626161.18,
12946464.79 3626046.29,
12946318.33 3625989.85,
12946171.46 3626020.31,
12946025.08 3626052.39,
12945878.62 3626082.6,
12945907.64 3625936.56,
12945965.54 3625789.62,
12945847.67 3625645.13,
12945701.39 3625587.64,
12945554.26 3625531.11,
12945436.48 3625415.14,
12945290.33 3625298.7,
12945144.24 3625386.75,
12944998.08 3625415.71,
12944910.27 3625299.4,
12944938.18 3625154.02,
12945026.08 3625007.65,
12944908.41 3624861.82,
12944761.65 3624803.87,
12944615.61 3624774.31,
12944470.64 3624803.16,
12944381.9 3624686.14,
12944439.54 3624539.67,
12944368.27 3624468.81,
12944409 3624324.69,
12944268.2 3624324.56,
12944091 3624323,
12943972.38 3624323.48,
12943869.12 3624314.05,
12943809.82 3624276.87,
12943830.85 3624136.35,
12943830.78 3623996.09,
12943814.6 3623854.74,
12943665.88 3623690.35,
12943554.03 3623525.93,
12943546.41 3623407.69,
12943545.44 3623288.65,
12943545.35 3623155.64,
12943536.82 3623022.25,
12943507.11 3622910.68,
12943469.75 3622805.73,
12943439.18 3622731.66,
12943379.86 3622627.77,
12943342.58 3622486.68,
12943282.37 3622367.22,
12943207.71 3622217.35,
12943129.89 3622116.94,
12943103.58 3622082.99,
12943007.48 3621977.68,
12942874.26 3621894.93,
12942758.71 3621829.25,
12942740.97 3621819.47,
12942593.43 3621787.63,
12942431.05 3621740.01,
12942322.01 3621721.23,
12942245.44 3621708.13,
12942053.53 3621667.37,
12941906.81 3621613.55,
12941755.1 3621549.51,
12941743.69 3621544.22,
12941551.76 3621466.52,
12941418.99 3621405.76,
12941219.49 3621314.14,
12941064.32 3621244.48,
12940931.51 3621183.87,
12940761.45 3621114.21,
12940591.45 3621023.47,
12940428.53 3620961.98,
12940236.39 3620878.57,
12940096.37 3620825.42,
12940044.62 3620772.91,
12940007.41 3620712.6,
12939948.07 3620601.54,
12939917.45 3620475.12,
12939864.83 3620304.56,
12939835.11 3620178.17,
12939782.53 3620066.38,
12939709.23 3620014.51,
12939553.27 3619902.13,
12939375.71 3619782.26,
12939234.88 3619679.13,
12939049.54 3619559.74,
12938864.59 3619478.44,
12938708.84 3619426.66,
12938494.47 3619324.88,
12938323.65 3619243.04,
12938227.19 3619185.63,
12938115.88 3619111.54,
12938004.88 3619046.24,
12937893.52 3618973.69,
12937774.78 3618908.29,
12937611.07 3618866.65,
12937485.14 3618845.99,
12937255.62 3618849.33,
12937084.94 3618823.66,
12936906.39 3618797.17,
12936676.72 3618750.23,
12936528.01 3618739.02,
12936275.98 3618743.99,
12936127.2 3618770.76,
12935963.85 3618789.37,
12935719.4 3618818.51,
12935533.86 3618837.69,
12935400.32 3618886.5,
12935244.64 3618964.77,
12935111.11 3619042.6,
12935014.59 3619103.87,
12934903.32 3619181.22,
12934800.45 3619258.66,
12934710.89 3619341.75,
12934578.3 3619464.26,
12934518.61 3619539.85,
12934438.03 3619646,
12934378.36 3619721.57,
12934327.22 3619856.46,
12934275.76 3619998.6,
12934202.39 3620133.91,
12934113.39 3620306.76,
12934040.17 3620464.02,
12934003.59 3620583.84,
12933967.49 3620740.78,
12933967.57 3620836.12,
12933975.27 3620955.39,
12934012.91 3621050.43,
12934072.24 3621130.3,
12934155.08 3621254.09,
12934185.03 3621357.79,
12934208.14 3621446.23,
12934215.54 3621600.95,
12934201.56 3621727.23,
12934170.13 3621838.07,
12934150.62 3621906.89,
12934099.55 3622071.14,
12934088.07 3622109.78,
12934048.04 3622243.15,
12933989.35 3622378.55,
12933937.36 3622505.13,
12933886.7 3622617.96,
12933782.85 3622746.47,
12933709.16 3622844.51,
12933628.23 3622965.29,
12933510.22 3623093.85,
12933414.44 3623184.86,
12933310.51 3623284.07,
12933199.46 3623381.75,
12933073.8 3623473.83,
12932969.9 3623520.11,
12932859.67 3623596.39,
12932763.1 3623686.92,
12932712.14 3623746.93,
12932622.84 3623829.77,
12932460.74 3623943.88,
12932357.42 3624004.47,
12932216.78 3624058.16,
12932091.34 3624110.32,
12931935.85 3624149.57,
12931736.22 3624187.7,
12931566.82 3624188.86,
12931470.01 3624166.92,
12931329.59 3624077.97,
12931203.58 3624048.79,
12931011.46 3623989.4,
12930885.85 3623930.22,
12930737.96 3623870.94,
12930605.32 3623817.58,
12930346.49 3623727.7,
12930131.83 3623659.29,
12929888.52 3623575.58,
12929748.43 3623536.37,
12929660.95 3623535.5,
12929520.46 3623490.15,
12929327.98 3623390.78,
12929114.5 3623321.1,
12928966.99 3623266.87,
12928797 3623204.99,
12928613.16 3623135.39,
12928443.62 3623058.43,
12928437.3 3623055.32,
12928236.5 3622951.29,
12928082.13 3622882.55,
12927843.76 3622800.6,
12927471.24 3622673.09,
12926883.94 3622755.81,
12926534.94 3622922.97,
12926506.53 3622930.22,
12926113.15 3623029.25,
12926009.77 3623065.78,
12925906.84 3623154.04,
12925833.22 3623220.23,
12925707.72 3623360.91,
12925634.06 3623434.74,
12925567.94 3623486.41,
12925501.68 3623501.89,
12925434.74 3623531.64,
12925368.56 3623538.93,
12925249.72 3623523.5,
12925153.33 3623486.5,
12925056.88 3623405.86,
12924960.39 3623294.39,
12924878.72 3623235.64,
12924707.91 3623163.39,
12924588.86 3623097.38,
12924455.29 3623046.73,
12924380.34 3623002.19,
12924342.51 3622958.97,
12924334.75 3622869.24,
12924357.17 3622735.81,
12924385.58 3622565.48,
12924429.71 3622409.28,
12924429.63 3622312.51,
12924421.86 3622223.8,
12924376.72 3622112.44,
12924343.44 3622047.2,
12924309.38 3621980.43,
12924249.77 3621913.99,
12924168.05 3621833.42,
12924127.97 3621791.34,
12924078.5 3621738.5,
12923959.27 3621665.3,
12923810.18 3621548.84,
12923676.36 3621461.73,
12923512.79 3621369.1,
12923385.3 3621318.3,
12923236.88 3621255.14,
12923042.46 3621146.58,
12922923.81 3621083.46,
12922647.03 3620992.84,
12922446.45 3620899.99,
12922266.78 3620830.64,
12922072.99 3620760.64,
12921886.45 3620675.92,
12921744.68 3620627.29,
12921662.77 3620598.75,
12921557.34 3620528.01,
12921445.39 3620426.35,
12921295.55 3620340.33,
12921243.48 3620260.43,
12921175.95 3620143.06,
12921123.88 3620054.61,
12921070.49 3619989.98,
12920996.18 3619917.08,
12920988.51 3619828.37,
12921018.31 3619731.42,
12921032.65 3619583.16,
12921039.44 3619523.52,
12921031.78 3619419.69,
12920994.16 3619339.86,
12920912.23 3619200.37,
12920844.32 3619120.08,
12920754.7 3618996.22,
12920680.41 3618871.91,
12920620.23 3618776.68,
12920485.5 3618661.03,
12920365.65 3618544.01,
12920283.83 3618472.06,
12920148.15 3618332.9,
12920009.71 3618118.54,
12919885.81 3617093.19,
12919683.65 3616621.38,
12919661.48 3616510.48,
12919593.92 3616326.29,
12919541.21 3616178.24,
12919489.27 3616046.31,
12919444.28 3615906.15,
12919391.15 3615751.19,
12919361.46 3615609.81,
12919331.75 3615492.09,
12919271.86 3615329.91,
12919234.59 3615189.87,
12919197.25 3615026.56,
12919167.56 3614916.68,
12919152.27 3614797.39,
12919129.47 3614583.03,
12919129.61 3614427.82,
12919128.67 3614094.29,
12919112.88 3613658.02,
12919083.31 3613428.22,
12919060 3613303.43,
12919047.83 3613193.42,
12919137.16 3613163.58,
12919218.38 3613133.74,
12919427.34 3613109.48,
12919545.76 3613085.22,
12919635.63 3613061.84,
12919731.86 3613053.68,
12919873.66 3613066.29,
12919970.37 3613072.55,
12920096.49 3613062.8,
12920208.56 3613046.18,
12920334.83 3613037.26,
12920402.13 3613013.37,
12920461.51 3612967.76,
12920483.72 3612870.72,
12920476.1 3612752.88,
12920483.02 3612626.9,
12920446.47 3612479.34,
12920415.49 3612339.16,
12920430.02 3612175.97,
12920467.2 3612072.08,
12920481.83 3611908.63,
12920526.28 3611774.37,
12920533.95 3611648.37,
12920600.68 3611440.17,
12920604.97 3611338.53,
12920607.57 3611270.13,
12920630.68 3611080.79,
12920629.34 3611069.58,
12920605.93 3610851.24,
12920636.72 3610651.82,
12920763.88 3610464.14,
12920862.41 3610262.74,
12920930.62 3609839.77,
12921014.21 3609400.71,
12921090.43 3608874.02,
12921159.8 3608450.06,
12921332.44 3608151.11,
12921527.55 3607681.31,
12921684.66 3607307.27,
12921753.5 3606935.86,
12921858.9 3606444.94,
12921874.75 3606022.72,
12921928.25 3605666.39,
12921540.06 3605768.75,
12921033.67 3605850.88,
12920743.13 3605824.37,
12920296.2 3605785.55,
12919797.22 3605798.03,
12919350.24 3605955.55,
12918934.33 3605992.53,
12918570.41 3605969.49,
12918340.63 3605879.76,
12918192.18 3605640.97,
12917986.43 3605171.91,
12917771.26 3605028.24,
12917519.5 3605128.66,
12917044.73 3605261.48,
12916674.81 3605313.47,
12916393.82 3605247.18,
12916068.86 3605075.47,
12915736.08 3604956.59,
12915396.2 3604864.85,
12915049.19 3604809.84,
12914813.62 3604573.6,
12914622.48 3604353.98,
12914504.55 3604288.24,
12914327.32 3604189.95,
12913928.55 3603997.02,
12913920.9 3603993.26,
12913685.62 3603750.51,
12913449.47 3603411.6,
12913214.49 3603110.31,
12912881.97 3602858.44,
12912630.73 3602780.36,
12912163.46 3602773.59,
12911829.93 3602688.3,
12911563.18 3602553.37,
12911260.17 3602388.31,
12911008.48 3602113.89,
12910831.98 3601803.26,
12910832.92 3601551.47,
12910842.27 3601225.24,
12910658.21 3600922.88,
12909967.48 3600629.74,
12909762.17 3600523.04,
12909462.5 3600367.7,
12909158.03 3600223.73,
12909142.71 3600216.54,
12908823.32 3600021.08,
12908645.51 3599823.13,
12908550.98 3599543.46,
12908411.84 3599116.06,
12908271.66 3598874.17,
12908107.88 3598773.19,
12907908.15 3598730.09,
12907818.24 3598710.64,
12907393.2 3598834.26,
12907020.91 3598897.37,
12906805.32 3598810.95,
12906703.76 3598442.36,
12906639.41 3597946.71,
12906552.88 3597509.81,
12906370.47 3597023.51,
12906096.49 3596824.42,
12905445.3 3596640.92,
12905090.13 3596580.78,
12904867.24 3596646.01,
12904704.13 3596800.24,
12904378.26 3596863.65,
12904017.64 3596754.85,
12903752.63 3596498.95,
12903466.49 3596309.79,
12902848.49 3596148.15,
12902077.06 3595921.59,
12901643.97 3595740.82,
12901211.68 3595448.21,
12900838.59 3595024.88,
12900579.05 3594620.14,
12900539.7 3594559.03,
12900093.02 3594209.88,
12900019.36 3594175.97,
12899623.03 3593994.34,
12899181.64 3593891.84,
12898990.34 3593749.03,
12898904.2 3593458.66,
12898876.92 3593081.41,
12898819.72 3592599.36,
12898461.32 3592153.87,
12897873.31 3591582.83,
12897432.3 3591125.74,
12897175.75 3590697.64,
12897044.62 3590335.89,
12896942.15 3589967.33,
12896956.05 3589760.04,
12896975.02 3589477.99,
12896987.73 3589444.28,
12897139.46 3589032.67,
12897392.14 3588786.23,
12897726.24 3588414.95,
12898039.02 3588141.83,
12898114.61 3588040.61,
12898527.3 3587757.73,
12898757.16 3587477.58,
12898758.76 3587159.77,
12898724.03 3586767.28,
12898674.32 3586492.92,
12899010.48 3585845.89,
12899122.05 3585588.26,
12899028.51 3585246.81,
12898684.7 3584844.5,
12898363.22 3584339.59,
12898173.6 3584050.59,
12898139.1 3583747.07,
12898053.91 3583454.28,
12898207.59 3583090.32,
12898337.6 3583001.15,
12898498.57 3582971.09,
12899164.94 3583032.24,
12899424.75 3582937.93,
12899670.23 3582703.65,
12899496.44 3582360.71,
12899327.98 3582194.8,
12898815.21 3581880.45,
12897690.55 3581226.92,
12897491.96 3581057.99,
12897368.27 3580851.32,
12897370.22 3580548.67,
12897431.62 3580201.02,
12897425.82 3579875.25,
12897086.53 3579737.5,
12896865.94 3579680.35,
12896548.07 3579617.09,
12896169.52 3579770.66,
12895828.28 3579871.78,
12895599.27 3579852.48,
12895252.37 3579652.09,
12894913.7 3579414.68,
12894559.48 3579183.98,
12894286.04 3579084.71,
12893974.64 3579104.36,
12893596.5 3579339.17,
12893350.56 3579571.19,
12893163.42 3579868.91,
12892864.32 3580224.64,
12892682.55 3580202.33,
12892628.11 3580197.05,
12892355.85 3580250.68,
12892067.01 3580354.46,
12891720.4 3580347.42,
12891352.72 3580285.39,
12890807.3 3580221.37,
12890220.73 3579953.8,
12890067.54 3579744.18,
12889892.87 3579437.38,
12889745.6 3579078.76,
12889712.22 3578996.61,
12889648.77 3578769.98,
12889553.92 3578431.66,
12889396.01 3577933.2,
12889245.15 3577249.54,
12889058.72 3576549.36,
12888863.41 3576042.22,
12888660.88 3575386.12,
12888407.29 3574876.72,
12888342.79 3574594.41,
12888022.47 3574127.19,
12887920.87 3573837.05,
12887856.38 3573517.43,
12887881.49 3573104.45,
12887892.91 3572446.68,
12888028.46 3571873.25,
12888011.17 3571841.07,
12887919.41 3571671.05,
12887737.24 3571496.62,
12887481.92 3571142.32,
12887529.31 3570670.93,
12887766.07 3570433.04,
12888046.31 3570315.08,
12888334.23 3570057.15,
12888600.17 3569725.09,
12888825.44 3569139.37,
12888916.7 3568587.83,
12889247.77 3568537.34,
12889549.34 3568477.36,
12889771.45 3568342.09,
12889807.07 3568320.4,
12889955.29 3568161.65,
12890085.32 3568034.56,
12890148.07 3567973.12,
12890406.04 3567830.41,
12890700.51 3567767.69,
12891203.3 3567434.2,
12891359.52 3567250.42,
12891435.81 3566897.11,
12891467.5 3566519.61,
12891550.57 3566173.45,
12891796.73 3565842.44,
12892026.8 3565591.74,
12892299.77 3565532.06,
12892530.01 3565377.18,
12892781.58 3565190.84,
12893065.41 3564745.19,
12893185.21 3564456.79,
12893172.44 3564168.87,
12893204.87 3563739.88,
12893310.96 3563281.76,
12893342.39 3562911.96,
12893159.6 3562677.18,
12892924.81 3562391.42,
12892668.35 3562112.89,
12892529.94 3561751.75,
12892634.81 3561433.62,
12892822.11 3560908.15,
12893008.78 3560545.25,
12893507.11 3559875.8,
12893745.51 3559532.36,
12894005.82 3559233.65,
12894353.01 3559220.35,
12894380.14 3559552.43,
12894453.07 3559949.75,
12894562.16 3560257.51,
12894789.75 3560490.18,
12895063.66 3560455.62,
12895381.86 3560303.02,
12895641.6 3560217.54,
12896055.7 3560144.41,
12896521.52 3560012.3,
12897090.99 3559917.25,
12897423.72 3559649.29,
12897595.68 3559249.89,
12897871.44 3558547.78,
12898049.31 3558318.9,
12898315.63 3558231.07,
12898690.09 3558225.46,
12899073.38 3558103.01,
12899618.56 3557785.55,
12899984.66 3557554.41,
12900015.5 3557534.92,
12900332.66 3557223.95,
12900377.88 3557179.81,
12900488.97 3556879.37,
12900468.58 3556569.17,
12900337.91 3556314.98,
12900163.54 3555957.4,
12900084.45 3555726.78,
12900173.75 3555551.55,
12900395.05 3555217.31,
12900571.84 3554979.41,
12900735.52 3554759.95,
12900942.46 3554447.7,
12900944.27 3554175.33,
12900916.52 3553879.37,
12900969.07 3553674.39,
12901110.94 3553323.74,
12901253.35 3552862.58,
12901292.15 3552450.75,
12901439.4 3552313.55,
12901895.52 3552193.05,
12902394.41 3551998.87,
12902726.6 3551719.46,
12902874.8 3551486.75,
12903054.35 3551195.3,
12903111.98 3551101.38,
12903247.42 3550521.26,
12903375.53 3550044.17,
12903642.41 3549673.18,
12903989.44 3549332.78,
12904314.16 3549189.04,
12904542.41 3549177.61,
12904843.63 3549231.53,
12905027.95 3549232.31,
12905176.63 3549107.59,
12904986.43 3548827.19,
12904744.55 3548566.49,
12904524.78 3548388.51,
12904150.75 3548007.81,
12903880.35 3547738.06,
12903669.3 3547307.6,
12903589.6 3547003.15,
12903572.69 3546904.83,
12903512.37 3546562.27,
12903503.91 3546514.84,
12903351.04 3546365.18,
12903175.68 3546213.32,
12903045.07 3545768.12,
12902929.78 3545375.41,
12902748.41 3544973.96,
12902581.03 3544585.88,
12902567.67 3544261,
12902583.49 3543973.98,
12902740.36 3543475.96,
12902866.91 3543000.72,
12902883.47 3542579.92,
12902731.12 3542363.4,
12902394.81 3542097.67,
12902241.67 3541886.38,
12902125.77 3541640.85,
12902157.73 3541110.76,
12902210.97 3540774.02,
12902461.62 3540558.82,
12902850.12 3540486.35,
12903195.34 3540360.89,
12903519.1 3540146.36,
12903829.97 3539849.69,
12904139.36 3539692.24,
12904536.3 3539675.56,
12904699.28 3539543.48,
12904863.22 3539146.64,
12905022.47 3538404.56,
12905038.81 3538124.3,
12905099.23 3537874.46,
12905321.2 3537720.16,
12905712.25 3537735.17,
12906237.08 3537630.06,
12906902.98 3537514.68,
12907674.56 3537314.38,
12907941.23 3537236.82,
12907846.28 3536980.38,
12907744.12 3536783.27,
12907530.66 3536527.83,
12907294.92 3536354.69,
12907021.25 3536291.6,
12906791.44 3536395.88,
12906501.07 3536619.56,
12906265.11 3536695.34,
12906035.71 3536666.21,
12905703.9 3536549.65,
12905454.02 3536409.39,
12905232.39 3536205.69,
12905197.11 3536173.4,
12905098.38 3535862.65,
12905016.65 3535606.26,
12904798.78 3535118.86,
12904514.9 3534630.7,
12904171.67 3534169.68,
12903813.25 3533869.71,
12903293.45 3533727.83,
12902875.01 3533734.24,
12902765.96 3533584.08,
12902636.22 3533308.27,
12902425.69 3533024,
12902325.96 3532918.26,
12902134.49 3532714.16,
12901725.76 3532454.06,
12901514.45 3532258.02,
12901333.02 3531957.97,
12901173.16 3531674.09,
12901138.44 3531334.76,
12901278.59 3531087.88,
12901522.16 3530615.54,
12901854.38 3530108.77,
12902112.28 3529665.88,
12902142.52 3529386.57,
12902166.63 3528952.81,
12902285.1 3528595.06,
12902645.84 3528220.68,
12903072.26 3527832.98,
12903250.11 3527469.18,
12903339.5 3527110.05,
12903443.51 3526817.08,
12903591.51 3526533.48,
12903811.72 3526456.18,
12904127.73 3526468.03,
12904399.52 3526353.56,
12904643.74 3526128.18,
12904784.48 3525827.6,
12904879.56 3525505.88,
12904882.12 3525496.51,
12904854.49 3525160.88,
12904854.04 3525158.31,
12904797.19 3524804.15,
12904645.3 3524383.35,
12904463.82 3523984.79,
12904464.84 3523682.65,
12904488.79 3523381.92,
12904276.79 3523209.75,
12904036.26 3522978.66,
12903744.01 3522753.77,
12903546.91 3522536.68,
12903578.44 3522243.48,
12903732.73 3522113.55,
12904005.58 3521985.14,
12904424.55 3521836.61,
12905220.24 3521614.63,
12905907.16 3521342.5,
12906209.32 3521311.42,
12906527.46 3521338.75,
12906724.88 3521630.77,
12906877.5 3522012.02,
12907008.45 3522423.14,
12907259.54 3522486.34,
12907371.44 3522197.2,
12907646.77 3521996.17,
12908038.81 3522049.15,
12908460.23 3522064.87,
12908756.92 3521972.91,
12909032.17 3521807.53,
12909241.06 3521517.69,
12909516.76 3521234.77,
12909945.56 3521179.25,
12910426.5 3521189.98,
12910766.67 3521115.38,
12911026.28 3520990.36,
12911234.45 3520784.62,
12911405.44 3520565.17,
12911635.59 3520220.27,
12911865.51 3520002.21,
12912110.5 3519834.88,
12912485.96 3519663.25,
12912774.61 3519484.08,
12912687.14 3519225.58,
12912462.34 3518663.4,
12912323.86 3518344.81,
12912362.07 3518110.87,
12912568.61 3517907.95,
12912834.69 3517728.1,
12912878.35 3517515.94,
12912728.87 3517167.99,
12912522.85 3516796.05,
12912545.69 3516568.45,
12912708.81 3516299.27,
12913224.54 3516044.32,
12913394.95 3515783.85,
12913388.08 3515584.57,
12913623.74 3515487.1,
12913931.6 3515390.11,
12913969.28 3515378.36,
12914410.12 3515293.92,
12914500.99 3515307.18,
12914659.57 3515330.96,
12915203.08 3515272.61,
12915226.5 3515015.44,
12915220.45 3514776.25,
12915219.53 3514728.27,
12915374.52 3514578.25,
12915514.37 3514463.41,
12915573.5 3514414.99,
12915727.62 3514258.05,
12915633.59 3514057.12,
12915546.03 3513907.34,
12915539.79 3513605.19,
12915744.73 3513544.7,
12916097.01 3513590.6,
12916456.65 3513659.24,
12916735.91 3513716.18,
12917176.92 3513499.14,
12917252.15 3513308.1,
12917186.32 3513078.87,
12917142.96 3512878.46,
12917239.42 3512703.71,
12917430.34 3512552.99,
12917763 3512330.04,
12918124.28 3512090.99,
12918500.38 3511932.33,
12918840.13 3511750.01,
12918958.3 3511624.51,
12919107.45 3511418.15,
12919189.38 3511153.68,
12919359.46 3510852.34,
12919552.22 3510719.6,
12919847.75 3510842.77,
12920105.55 3510913.42,
12920386.4 3510836.84,
12920387.22 3510631.07,
12920425.47 3510322.12,
12920515.39 3510100.07,
12920649.28 3509900.52,
12920798.06 3509670.23,
12921027.96 3509425.45,
12921325.38 3509030.34,
12921199.59 3508915.26,
12920861.41 3508670.66,
12920854.65 3508494.46,
12920951.13 3508279.49,
12921285.32 3507951.43,
12921413.04 3507597.6,
12921280.53 3507393.16,
12921089.13 3507168.6,
12920876.1 3506914.13,
12920242.27 3506620.41,
12920183.65 3506438.28,
12920214.61 3506173.52,
12920334.24 3505827.62,
12920439.45 3505376.87,
12920449.65 3504951.68,
12920650.35 3504581.9,
12920755.1 3504111.28,
12920763.73 3504073.59,
12920782.24 3503806.82,
12920795.54 3503618.28,
12920805.99 3503001.09,
12920882.07 3502383.39,
12921000.72 3502197.65,
12921282.65 3501825.76,
12921254.55 3501532.88,
12921314.99 3501252.52,
12921463.9 3501023.36,
12921752.1 3500665.47,
12921790.46 3500408.61,
12921851.11 3499900.46,
12921888.55 3499694.47,
12922183.83 3499704,
12922383.91 3499730.97,
12922545.9 3499684.11,
12922768.08 3499571.03,
12923321.6 3499306.61,
12923786.58 3499037.42,
12923860.55 3498868.51,
12923743.59 3498626.5,
12923435.66 3498137.73,
12923450.79 3497946.02,
12923562.24 3497687.78,
12923558.76 3497676.04,
12923554.06 3497661.12,
12923532.78 3497591.73,
12923529.31 3497580,
12923510.95 3497519.41,
12923408.18 3497366.16,
12923482.92 3497131.47,
12923726.71 3496761.57,
12923941.78 3496290.14,
12923912.76 3496106.43,
12923619.22 3495764.08,
12923391.48 3495436.13,
12923407.58 3495186.48,
12923445.03 3494928.71,
12923187.64 3494844.05,
12923078.1 3494610.16,
12923063.55 3494352.83,
12923124.12 3494037.04,
12923295 3493660.54,
12923469.42 3493386.47,
12923487.75 3493357.92,
12923655.67 3493084.58,
12923710.18 3492995.58,
12923726.51 3492695.65,
12923763.89 3492160.15,
12923726.12 3490916.88,
12923812.05 3489518.14,
12923735.81 3488275.56,
12923842.98 3487786.63,
12923984.38 3487426.64,
12924147.92 3486963.63,
12924296.05 3486750.06,
12924054.36 3486509.05,
12923760.64 3486385.96,
12923428.8 3486330.89,
12923252.4 3486200.77,
12923194.39 3486003.64,
12923291.48 3485612.92,
12923425.88 3485135.61,
12923466.82 3484485.47,
12923528.22 3483436.02,
12923243.79 3482728.02,
12923279.57 3482130.42,
12923178.89 3480949.92,
12923237.91 3480637.66,
12923266.32 3480283.59,
12923063.6 3479536.34,
12922374.38 3478648.32,
12922286.96 3478442.06,
12922232.85 3478365.42,
12922347.28 3477855.61,
12922558.11 3477701.27,
12922820.43 3477559.21,
12923063.56 3477227.34,
12923535.74 3476537.07,
12924284.92 3475460.14,
12925128.24 3474356.59,
12925889.74 3473318.61,
12926585.42 3472367,
12926787.36 3472073.61,
12926991.43 3471777.85,
12927780.07 3470634.93,
12927844.77 3470550.12,
12928077.15 3470246.56,
12928180.98 3469981.66,
12928239.67 3469696.53,
12928284.26 3469381.98,
12928321.3 3469097.03,
12928322.76 3468811.09,
12928249.83 3468489.85,
12928203.81 3468050.48,
12928323.64 3467860.31,
12928424.61 3467764.3,
12928493.91 3467698.81,
12928751.11 3467494,
12928900.09 3467303.11,
12928976.06 3467119.39,
12928963.21 3466871.51,
12928857.06 3466343.99,
12928588.21 3465686.62,
12928422.68 3465341.69,
12928328.73 3465146.19,
12928174.31 3464744.3,
12928176.77 3464510.23,
12928359.62 3464224.02,
12928422.2 3464146.41,
12928536.74 3464004.24,
12928720.54 3463769.39,
12928832.12 3463535.2,
12928927.33 3463249.83,
12928945.09 3463031.32,
12928918.11 3462789.83,
12928846.73 3462511.3,
12928733.58 3462293.07,
12928591.18 3462088.76,
12928483.2 3461790.19,
12928546.75 3461527.26,
12928584.33 3461490.65,
12928612.27 3461463.28,
12928708.74 3461369.29,
12928815.73 3461289.93,
12928822.49 3461284.97,
12928978.93 3461169.57,
12929019.73 3461006.11,
12929085.49 3460684.48,
12929154.44 3460149.63,
12929933.13 3460533.21,
12930629.54 3460773.27,
12931039.7 3460848.12,
12931784.87 3460912.05,
12932560.61 3460948.39,
12933584.26 3460889.04,
12934206.77 3460884.92,
12935182.4 3460834.2,
12936200.21 3460737.47,
12937236.95 3460564.96,
12938247.27 3460234.42,
12939145.46 3459564.56,
12939887.9 3458830.28,
12940291.77 3458278.93,
12940583.62 3457662.78,
12940971.79 3456783.99,
12941380.28 3456169.32,
12941757.35 3455826.75,
12941872.69 3455722.1,
12942227.76 3455333.47,
12942311.86 3455273.16,
12942628.88 3455047.04,
12942924.84 3454950.96,
12943281.03 3454898.26,
12943759.94 3455098.5,
12944203.93 3455392.97,
12944724.37 3455785.63,
12945938.99 3456676.89,
12947268.43 3457588.25,
12948922.69 3458747.12,
12949973.43 3459626.15,
12950496.31 3460323.3,
12950999.84 3460804.95,
12951308.27 3461105.97,
12951458.25 3461252.69,
12951701.59 3461414.19,
12951979.51 3461599.71,
12952579.59 3461911.43,
12952965.07 3462103.37,
12953965.39 3462747.15,
12955341.79 3463249.08,
12955665.56 3463394.07,
12956240.15 3463650.38,
12956447.4 3463742.39,
12956937.11 3464074.45,
12957638.24 3464366.2,
12958267.52 3464683.28,
12959069.4 3465115.53,
12959648.2 3465374.91,
12960133.53 3465714.47,
12960431.92 3465863.41,
12961533.59 3466415.13,
12962606.11 3466972.19,
12963217.4 3467347.17,
12963726.52 3467765.28,
12964274.39 3468258.03,
12964848.78 3468713.52,
12965494.91 3469114.17,
12965998.5 3469424.82,
12966645.03 3469647.4,
12966886.27 3469687.97,
12967454.85 3469740.27,
12968820.27 3469729.63,
12969582.12 3469729.35,
12969699.31 3469729.16,
12970135.88 3469737.57,
12970580.07 3469745.11,
12971469.94 3469784.87,
12972273.58 3469790.85,
12972895.82 3469833.98,
12973566.76 3469974.79,
12974094.07 3470220.89,
12974496.76 3470503.98,
12974542.51 3470535.91,
12974910.28 3470862.14,
12975160.72 3471171.77,
12975441.4 3471760.59,
12975471.25 3471822.71,
12975582.57 3472095.39,
12975790.93 3472605.21,
12976110.64 3473613.76,
12976335.57 3474409,
12976578.15 3475327.42,
12976739.4 3476212.75,
12976827.2 3477387.78,
12976859.41 3477803.63,
12976977.28 3478406.38,
12977162.02 3479028.28,
12977366.21 3479854.53,
12977496.78 3480641.52,
12977596.45 3481385.27,
12977641.21 3481976.89,
12977655.83 3482253.64,
12977625.37 3482374.02,
12977623.15 3482382.09,
12977637.94 3482716.12,
12977639.31 3482745.02,
12977742.91 3483513.99,
12977854.09 3484261.66,
12977964.89 3484781.61,
12978124.62 3485292.26,
12978246.28 3485612.75,
12978611.73 3486384.83,
12978876.08 3486883.64,
12979088.93 3487368.74,
12979308.44 3487736.88,
12979601.06 3488207.78,
12979974.27 3488805.6,
12980361.55 3489316.71,
12980822.12 3489911.4,
12981312.05 3490478.12,
12981794.04 3490868.21,
12982557.43 3491334.45,
12983492.26 3491771.1,
12984034.03 3492112.96,
12984336.15 3492171.83,
12985834.4 3492343.75,
12987028.66 3492315.1,
12987605.5 3492279.78,
12988566.75 3492172.65,
12989357.28 3492017.56,
12990155.28 3491615.19,
12990928.8 3491230.91,
12991599.24 3490867.88,
12992319.71 3490427.7,
12992935.99 3490138.4,
12993470.66 3489836.99,
12993953.97 3489675.55,
12994429.41 3489559.62,
12994670.34 3489553.74,
12995124.75 3489543.04,
12995849.09 3489667.8,
12996405.55 3489801.49,
12996814.13 3489954.23,
12997115.73 3490105.23,
12997407.03 3490270.13,
12997511.61 3490329.02,
12997970.46 3490727.57,
12997995.79 3490749.2,
12998803.71 3491334.48,
12999576.42 3491869.69,
13000394.52 3492692.27,
13000750.25 3493118.96,
13001015.41 3493436.51,
13001044.36 3493470.76,
13001569.39 3494081.97,
13002344.18 3494847.74,
13003252.91 3495655.77,
13004057.26 3496543.98,
13005015.45 3497611.83,
13006024.49 3498931.81,
13006877.13 3500192.21,
13007472.4 3501095.17,
13007722.36 3501619.88,
13008140.45 3502419.58,
13008757.06 3503566.84,
13008842.38 3503760.52,
13008993.52 3504101.19,
13009497.77 3505238.38,
13010026.94 3506199.69,
13010319.76 3506672.26,
13011091.8 3507639.74,
13011319.27 3507933.5,
13011869.74 3508641.59,
13011975.12 3508776.35,
13012336.57 3509325.52,
13013002.77 3510206.35,
13013646.98 3511114.05,
13014411.67 3512155.96,
13014894.78 3512842.74,
13015326.3 3513493.49,
13015468.11 3513737.01,
13015810.85 3514327.46,
13015854.43 3514415.33,
13016024 3514756.06,
13016258.9 3515228.79,
13016519.83 3515899.87,
13016819.8 3516775.91,
13017088.66 3517617.42,
13017269.37 3518584.64,
13017344.82 3519216.89,
13017339.31 3519767.76,
13017281.83 3520328.49,
13017187.74 3520837.15,
13017011.29 3521384.22,
13016732.44 3522094.9,
13016460.87 3522718.01,
13016086.43 3523588.2,
13015523.34 3525111.76,
13015499.52 3525175.91,
13015425.73 3525457.11,
13015141.88 3526538.94,
13014800.6 3527836.41,
13014604.06 3528584.87,
13014546.35 3528962.25,
13014224.62 3529816.53,
13013961.76 3530632.24,
13013685.17 3531603.17,
13013246.6 3533068.57,
13012969.28 3533861.98,
13012779.93 3534605.71,
13012647.96 3535862.71,
13012841.35 3536134.73,
13012866.84 3536201.52,
13013095.28 3536798.05,
13013005.22 3538166.52,
13013281.16 3538849.92,
13014090.97 3539386.55,
13014505.98 3539381.96,
13014787.67 3539156.96,
13015301.29 3538505.99,
13015539.48 3538241.29,
13015937.56 3538316.37,
13016475.45 3538793.28,
13017548.16 3539692.38,
13017928.86 3539893.45,
13018735.7 3539917.98,
13019297.85 3540045.57,
13019868.1 3540397.49,
13021366.19 3541340.74,
13022911.65 3542472.25,
13024237.32 3543690.24,
13025235.81 3544648.15,
13025733.76 3545121.47,
13025819.7 3545126.15,
13025896.17 3545130.57,
13026041.33 3545240.1,
13026123.45 3545284.82,
13026249.12 3545365.44,
13026315.99 3545417.1,
13026433.9 3545504.42,
13026627.27 3545548.24,
13026768.31 3545568.59,
13026916.57 3545627.26,
13027094.37 3545706.51,
13027250.41 3545771.67,
13027428.29 3545851.59,
13027614.25 3545929.79,
13027770.36 3545964.45,
13027911.62 3546007.42,
13028075.05 3546063.63,
13028261.06 3546090.5,
13028551.68 3546131.83,
13028729.68 3546187.48,
13028900.4 3546243.81,
13029079.17 3546300.53,
13029158.14 3546318.84,
13029316.4 3546355.89,
13029470.4 3546398.38,
13029633.32 3546441.31,
13029850.85 3546473.76,
13029863.6 3546475.72,
13030146.64 3546480.91,
13030532.19 3546500.81,
13030843.93 3546491.89,
13031170.33 3546521.37,
13031211.14 3546528.91,
13031444.59 3546573.67,
13031814.76 3546568.3,
13032243.91 3546549.66,
13032502.37 3546560.55,
13032856.8 3546579.85,
13033153.32 3546537.5,
13033270.99 3546520.91,
13033461.55 3546509.94,
13033639.29 3546499.57,
13033795.91 3546486.62,
13033805.55 3546485.88,
13033821.64 3546484.73,
13034111.37 3546465.99,
13034354.52 3546383.91,
13034729.88 3546424.1,
13035009.32 3546446.03,
13035281.42 3546446.29,
13035598.01 3546380.76,
13035980.45 3546296.09,
13036268.37 3546266.76,
13036540.61 3546206.3,
13036849.42 3546110.38,
13037174.03 3546042.81,
13037594.14 3545983.61,
13038080.03 3546000.2,
13038249.73 3545980.1,
13038500.05 3545944.01,
13038758.34 3545951.38,
13038972.41 3545929.59,
13039152.28 3545936.35,
13039164.2 3545936.54,
13039371.64 3545876.93,
13039489.93 3545831.22,
13039741.34 3545734.27,
13039765.66 3545731.43,
13039940.66 3545709.43,
13040126.31 3545656.83,
13040370.4 3545564.63,
13040570.71 3545517.9,
13040777.37 3545396.64,
13040985.35 3545318.84,
13041185.29 3545204.12,
13041385.75 3545141.46,
13041555.91 3545064.41,
13041711.23 3545011.14,
13041874.57 3544918.47,
13042059.6 3544819.03,
13042275.04 3544696.86,
13042467.36 3544641.19,
13042667.35 3544541.36,
13042823.49 3544464.75,
13043030.25 3544387.8,
13043118.85 3544333.99,
13043297.42 3544168.99,
13043504.01 3544033.82,
13043703.74 3543919.99,
13043963.04 3543813.53,
13044111.05 3543694.03,
13044236.62 3543597.3,
13044414.36 3543506.69,
13044554.27 3543453.91,
13044701.82 3543379.42,
13044909.22 3543253.46,
13045159.33 3543178.65,
13045350.88 3543113.14,
13045520.21 3543061.84,
13045741.18 3542988.28,
13045866.79 3542937.63,
13046139.28 3542880.66,
13046374.51 3542816.91,
13046587.45 3542775.75,
13046778.46 3542755.09,
13046983.73 3542758.71,
13047159.78 3542775.89,
13047336.24 3542809.61,
13047496.47 3542849.13,
13047651.35 3542866.3,
13047790.45 3542876.59,
13047944.3 3542916.3,
13048076.2 3542970.93,
13048171.42 3543031.39,
13048295.68 3543107.91,
13048449.2 3543200.12,
13048618.31 3543262.22,
13048837.8 3543422.57,
13049050.84 3543544.69,
13049233.12 3543688.07,
13049343.12 3543779.26,
13049497.16 3543893.14,
13049650.67 3544020.92,
13049790.35 3544149.36,
13049907.22 3544284.64,
13050060.97 3544470.98,
13050185.42 3544613.26,
13050246.5 3544686.46,
13050303.25 3544754.95,
13050479.51 3544942.61,
13050662.18 3545172.79,
13050802.16 3545410.47,
13050955.69 3545611.21,
13051146.55 3545900.37,
13051285.89 3546189.77,
13051410.21 3546425.29,
13051462.45 3546588.33,
13051464.68 3546599.78,
13051498.68 3546772.62,
13051519.54 3546914.27,
13051534.38 3547016.29,
13051571.36 3547170.47,
13051615.14 3547376.98,
13051592.7 3547489.66,
13051598.65 3547762.13,
13051604.69 3548034.85,
13051633.5 3548285.65,
13051704.75 3548765.93,
13051747.05 3549112.52,
13051825.89 3549547.2,
13051891.07 3549841.94,
13051956.38 3550100.89,
13052042.8 3550403.23,
13052159.88 3550646.31,
13052217.43 3550977.75,
13052252.87 3551236.33,
13052368.09 3551723.07,
13052433.25 3552010.14,
13052512.9 3552312.27,
13052569.56 3552650.36,
13052612.8 3552938.12,
13052669.5 3553270.4,
13052734.69 3553630.63,
13052784.71 3553866.6,
13052835.65 3554102.04,
13052863.6 3554375.6,
13052899.16 3554676.76,
13052971.42 3555066.96,
13052687.12 3556250.46,
13052684.72 3556946.18,
13052582.46 3557629.78,
13052579.13 3557651.81,
13052388 3558034.39,
13052167.38 3558446.82,
13051389.17 3559346.98,
13050831.1 3560043.92,
13050558.07 3560663.97,
13050260.15 3561423.89,
13050256.9 3562352.65,
13050407.79 3563020.68)))';
select st_intersects(st_geomfromtext(@geom2), st_buffer(st_geomfromtext(@geom1),50)) as a;
a
0
DROP TABLE IF EXISTS `t`;
Warnings:
Note	1051	Unknown table 'test.t'
CREATE TABLE `t` (
`geo` geometry DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t values (st_geomfromtext(@geom1));
DROP TABLE IF EXISTS `t1`;
Warnings:
Note	1051	Unknown table 'test.t1'
CREATE TABLE `t1` (
`geo` geometry DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (st_geomfromtext(@geom2));
select t1.geo from t,t1 where st_intersects(t1.geo, st_buffer(t.geo,50));
geo
DROP TABLE IF EXISTS `t`;
DROP TABLE IF EXISTS `t1`;
######################################################################################################
# BUG#21238614 : ST_BUFFER(LINESTRING) MAY PRODUCE AN INVALID POLYGON
######################################################################################################
SELECT ST_IsValid(
ST_Buffer(
ST_GeomFromText('LINESTRING(-155.9300341531310000 4.1672727531600900, -14.0079144546799000 -12.2485554508160000, '
                                   '176.9503531590800000 -3.0930641354495000, 32.6863251871831000 -17.9691125862157000, '
                                   '-17.7739746299451000 41.3177973084700000, -36.0310834162082000 59.9486214620753000, '
                                   '153.1574937017440000 46.3007892930418000, 172.7795126069240000 19.5367061763707000, '
                                   '-85.6306040220105000 35.0128339347489000, -61.1404987988716000 0.3278080608359490, '
                                   '-127.5034592987520000 18.6202802642343000, 114.5567005754250000 -83.7227732658958000, '
                                   '-66.1134822881378000 -75.2141906159065000, -93.7363999307791000 49.3124773443269000, '
                                   '-8.7182702071584100 56.2071174970861000, 7.7959787229988800 60.8845281744769000, '
                                   '13.0420633931840000 58.8150539662759000, -89.9754374613871000 26.4546501154335000, '
                                   '-44.5746548960799000 -88.8122262334508000, -178.4807616092640000 10.7770331393820000, '
                                   '161.8238702890570000 -42.3894892597522000, 136.2382890452810000 28.6261570633511000, '
                                   '49.6788041059295000 61.7724885566963000, 52.7876201424690000 -61.9246644395984000, '
                                   '-162.7456296900030000 11.7183989853218000, 115.6208648232840000 51.0941612539320000, '
                                   '-48.7772321835054000 50.4339743128205000)'),
5.9518403867035365));
ST_IsValid(
ST_Buffer(
ST_GeomFromText('LINESTRING(-155.9300341531310000 4.1672727531600900, -14.0079144546799000 -12.2485554508160000, '
                                   '176.9503531590800000 -3.0930641354495000, 32.6863251871831000 -17.969112586215700
1
######################################################################################################
# BUG#21238969 : ST_ISVALID(MULTIPOLYGON) MAY RETURN WRONG RESULT
######################################################################################################
SELECT ST_IsValid(
ST_GeomFromText('MULTIPOLYGON(((-40.314872143936725 -85.6567579487603,-53.1473643859603 -98.48925019078388, '
                                   '-41.29168745244485 -90.56754012573627,-40.314872143936725 -85.6567579487603)), '
                                   '((-186.91433298215597 -88.80210078879976,-192.54783347494038 -75.53420159905284, '
                                     '-192.7944062150986 -78.03769664281869,-186.91433298215597 -88.80210078879976)), '
                                   '((170.89089207158912 -44.35600339378721,191.8949969913326 -31.3460752560506, '
                                     '169.32805525181837 -43.07341636227523,170.89089207158912 -44.35600339378721)), '
                                   '((-2.6035109435630504 -13.058121512403435,26.839412016794036 43.97610638055074, '
                                     '26.974733141577826 43.72289807427111,26.97470368529088 43.722907009738066, '
                                     '-2.6035109435630504 -13.058121512403435)))'));
ST_IsValid(
ST_GeomFromText('MULTIPOLYGON(((-40.314872143936725 -85.6567579487603,-53.1473643859603 -98.48925019078388, '
                                   '-41.29168745244485 -90.56754012573627,-40.314872143936725 -85.6567579487603)), '
                
1
######################################################################################################
# BUG#21372946 : ST_BUFFER: FLATLINE CPU OR ASSERTION IN GEOMETRY::EQUALS
######################################################################################################
do st_buffer( polygon(linestring(point(9192,27876), point(3,9), point(-1,5), point(128,4503599627370500)), linestring(point(-11,3), point(15,-14), point(4.992937e+306,1.784325e+307)))  , "6[8"     );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(-15,-15), point(1.311283e+308,2.479644e+307), point(-20,-9), point(-15,-15)), linestring(point(11,-4), point(10284,-21812), point(-12,17), point(70368744177660,-1152921504606846974), point(1048578,536870909)), linestring(point(-28066,-10001), point(2199023255555,33554436), point(8.807841e+306,1.047731e+308), point(1.421765e+308,1.614104e+308)))  , 549755813889   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(-268435458,-12), point(27090,-14130), point(576460752303423492,549755813891)), linestring(point(268435453,65539), point(-4,10), point(-1,-8), point(1.428455e+308,1.631641e+308)), linestring(point(13,17), point(5,-4), point(17868,780), point(1.021010e+308,3.132483e+306), point(8796093022209,-2251799813685244)), linestring(point(-1,14), point(1.359055e+308,2.093312e+307), point(137438953475,-2047), point(-1,14)))  , 49316   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( geometrycollection(point(5,4), multipolygon(polygon(linestring(point(44,25), point(268435458,17592186044420), point(-68719476739,-68719476735), point(44,25)), linestring(point(-9704,8028), point(1.017763e+308,4.420275e+307), point(134217729,72057594037927939)), linestring(point(27945,15972), point(18,34), point(13,24)), linestring(point(3.344397e+307,1.474666e+308), point(-28578,24802), point(10,-88), point(122535660419016670000000000000000000000.000000,142546802935424300000000000000000000000.000000)))), multilinestring(linestring(point(6.328801e+307,4.657308e+307), point(79,-1)), linestring(point(-67,74), point(1.584157e+308,4.498260e+306), point(-86,0)), linestring(point(-65,-70), point(21410,14883), point(-12,57)), linestring(point(1.438847e+308,3.780434e+307), point(-37,71), point(-26,15))))   , '5526-01-22'    );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( geometrycollection(polygon(linestring(point(2199023255551,524287), point(1.136487e+308,1.364636e+308), point(-10,-19)), linestring(point(144115188075855874,-1099511627775), point(-14,0), point(-4347,16243))))   , 0x169532   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( multipolygon(polygon(linestring(point(19777,-21893), point(3.225945e+307,6.868234e+307), point(-40,-13))), polygon(linestring(point(-1322,4851), point(-1152921504606846977,72057594037927937), point(8.646364e+307,3.949086e+307), point(75,-69), point(8.499982e+307,3.944814e+307))))   , 948189399   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(9,3), point(-2,-4), point(-8193,8388609)), linestring(point(-10,-2), point(32268,-2557), point(1.720358e+308,5.678670e+307), point(4.916335e+307,1.410315e+308), point(-268435455,-19)), linestring(point(-5,4), point(9.501669e+307,1.058829e+308), point(-422,-25737)))   , 0x94fd   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(-14,14), point(1.336292e+308,3.975546e+307), point(-262148,2251799813685245), point(-14,14)), linestring(point(19,-8), point(-4398046511106,-1027), point(-13,7), point(19,-8)))    , 0x56   );
do st_buffer( polygon(linestring(point(1.050935e+308,1.405574e+308), point(12,-6), point(16270426021941148000000000000000000000.000000,220354304351410460000000000000000000000.000000), point(16,-1)), linestring(point(1073741825,7), point(4.395298e+307,1.391001e+308), point(2251799813685245,70368744177662)), linestring(point(6688,-12784), point(1.642959e+308,9.081471e+307), point(9,3), point(-1,15)), linestring(point(-20444,13227), point(1.421855e+308,6.359931e+307), point(-36028797018963966,144115188075855872), point(7.696665e+307,7.162705e+307), point(4.616100e+307,1.962868e+307)))  , 3147467309   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(6,-2), point(6.675936e+307,6.698695e+307), point(9,17), point(17,-8), point(-13267,10214)), linestring(point(-15,3), point(12,-14), point(1.595635e+308,9.405896e+307), point(-6,-5), point(23989412930405084000000000000000000000.000000,320342831451636520000000000000000000000.000000)))   , 21604   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( geometrycollection(multipoint(point(4094,262141), point(288230376151711744,65540)), polygon(linestring(point(21651,-296), point(7.682691e+307,5.782088e+307), point(131,-4099)), linestring(point(-34359738369,4094), point(-35184372088834,-576460752303423489), point(5,15), point(-11,-65532)), linestring(point(1,-6), point(2.077015e+307,9.133062e+307), point(-131069,-70368744177668))), polygon(linestring(point(-16380,-2097152), point(-16,6), point(64646494283694236000000000000000000000.000000,214181636942273530000000000000000000000.000000)), linestring(point(-1,17), point(91564461519059780000000000000000000000.000000,63319776236097772000000000000000000000.000000), point(-19,-18), point(12174,-1163)), linestring(point(4294967292,2305843009213693954), point(536870908,2251799813685248), point(-10,8), point(18,-15), point(-10,-5))))   , 0xd533   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( geometrycollection(multipolygon(polygon(linestring(point(-47,-32), point(33554434,34359738368), point(5.363618e+306,4.915149e+307), point(221186288793352260000000000000000000000.000000,318888116187633820000000000000000000000.000000)), linestring(point(3.373147e+307,1.097849e+307), point(-2,58), point(-43,-18), point(268435452,2))), polygon(linestring(point(94,-46), point(536870908,137438953468), point(-70,-79), point(1022,-17592186044419)))), linestring(point(1.428889e+308,6.363539e+307), point(-85,-39), point(4294967300,510), point(-144115188075855868,2147483644)), multipoint(point(1.216065e+308,1.498275e+308), point(524285,65538)))  , '2328-04-12'    );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(-20,-10), point(-2051,137438953470), point(9007199254740995,536870914), point(5,-8), point(-20,-10)), linestring(point(0,-17), point(-3,-17), point(-6120,31704), point(-13,15), point(2199023255556,33554436)), linestring(point(0,-11), point(1.240916e+307,1.230029e+308), point(-8589934590,-536870916)))   , "4"     );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( multipolygon(polygon(linestring(point(71,9), point(9.673785e+307,6.840020e+307), point(49,28), point(71,9)), linestring(point(8247,-23317), point(1.518888e+308,1.307053e+308), point(13002,-32416)), linestring(point(-1,32), point(576460752303423487,1), point(-48,-98), point(-59,-95), point(-1,32))))   , 0x63   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(9223372036854775810,4503599627370496), point(2,-14), point(1.057813e+308,6.695568e+307), point(-17795,-9201)), linestring(point(8589934595,65540), point(8,19), point(5.042026e+307,1.713291e+308), point(1073741826,9007199254740996)), linestring(point(-14,-7), point(2,-20), point(1.191471e+308,5.789499e+307), point(-14,-7)))  , 0xf9   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( geometrycollection(multilinestring(linestring(point(-100,-1), point(1.689397e+307,1.481746e+308)), linestring(point(9.569350e+307,6.040646e+307), point(2,52), point(20719,-20779))), multipolygon(polygon(linestring(point(20681,1270), point(3.045367e+307,1.280043e+308), point(58,-75)), linestring(point(92,-73), point(1.323951e+308,1.648866e+308), point(14,-61), point(-8763,21584), point(8.536381e+306,2.841766e+307)), linestring(point(-99,-34), point(-18,40), point(34359738366,2147483646), point(-30617,-24434)))))   , 3613057409   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(-18966,21965), point(1.797388e+308,1.945328e+307), point(-16,-11), point(-2199023255549,4611686018427387901), point(-18,-13)), linestring(point(31486,-9392), point(2,18), point(12,14), point(12,8), point(9,-15)), linestring(point(-13,2), point(36028797018963972,70368744177665), point(1.761152e+308,9.475919e+307), point(1.585099e+308,9.356514e+307), point(257321105224664410000000000000000000000.000000,313294288466175500000000000000000000000.000000)))   , 0xb79439   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(-9,14), point(9,12), point(65532,1073741826)), linestring(point(-3537,16635), point(-8,8), point(4.544898e+307,1.276958e+308), point(28858,-28514)))   , '1987-09-29 11:12:09.425076'    );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(70368744177662,4503599627370498), point(12,-3), point(7.561703e+307,1.021736e+308)), linestring(point(11,16), point(28824,15779), point(14,-12), point(-15,13), point(11,16)), linestring(point(-13,-18), point(-3,-3), point(1.760789e+308,1.455799e+308), point(0,-17), point(-13,-18)))   , '480:22:53.391613'    );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( multipolygon(polygon(linestring(point(-94,-64), point(26,-72), point(1.128438e+308,1.056662e+308)), linestring(point(85,-58), point(144115188075855870,32770), point(17179869186,1152921504606846978))))   , -17535   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(2,-8), point(1,17), point(-3,-15), point(1.149664e+307,1.717062e+307), point(1.410669e+308,5.228436e+307)), linestring(point(13,-11), point(6,9), point(536870910,72057594037927933)))   , -65534   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(13,-20), point(9.017452e+307,9.232528e+307), point(-15,-11), point(13,-20)), linestring(point(28170,-17887), point(-6,-20), point(-20301,3465), point(2.878956e+307,1.851687e+307), point(-18,8)))  , 0x2409be882ee34e7b   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(6,-1), point(1.703788e+308,1.736249e+308), point(8,-4), point(1.418474e+308,8.924021e+307)), linestring(point(-20,9), point(576460752303423487,288230376151711745), point(6,20), point(-14,-10), point(-23934,22390)), linestring(point(4194301,126), point(202376625515840940000000000000000000000.000000,115944597323370810000000000000000000000.000000), point(-16,7), point(4.096558e+307,8.103820e+307), point(16384,1099511627779)))   , 626278220   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(1.234361e+308,1.118625e+308), point(-13,1), point(11,-12)), linestring(point(29137,-3438), point(1.137847e+308,1.340074e+308), point(-437,2275), point(-15,4), point(-27269,23198)), linestring(point(-2,18), point(131069,30), point(125,7), point(-2,18)), linestring(point(11,-7), point(-6,9), point(4.698483e+307,2.207992e+306)))    , -1152921504606846972   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( polygon(linestring(point(262140,65537), point(11,-11), point(194743989698613950000000000000000000000.000000,289407735240774130000000000000000000000.000000), point(22933,26342)), linestring(point(2305843009213693956,34359738367), point(-30519,2568), point(7.523904e+307,1.274018e+307), point(5,12)))  , -2918556785199106584   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( multipolygon(polygon(linestring(point(-3,-32), point(5.983690e+307,5.252259e+307), point(24883,27429)), linestring(point(100,25), point(-90,-37), point(524286,268435456), point(71,-67), point(64,72057594037927932))))   , 1152921504606846977   );
ERROR 22023: Invalid GIS data provided to function polygon.
do st_buffer( multipolygon(polygon(linestring(point(-16,20), point(0,-43), point(-70,45), point(274877906948,288230376151711745), point(-16,20)), linestring(point(-48,81), point(5,92), point(76,-43), point(1.100472e+307,1.199671e+308), point(262146,17179869186)), linestring(point(-66,99), point(9,-46), point(1.519109e+308,1.506793e+308))))   , 0xf3   );
ERROR 22023: Invalid GIS data provided to function polygon.
######################################################################################################
# BUG#21547506 : ASSERTION FAILED: SEQUENCE NOT ORDERED ALGORITHM(2639)
######################################################################################################
do st_difference(
st_union(
multilinestring(
linestring(
point(3.1e+307,1),
point(5.1e+307,6.e+307),
point(-1,-4),
point(1,4294967295),
point(3,-2),
point(1,32732243452685474000000000),
point(-1,7),
point(1,1)
),
linestring(
point(9,5),
point(1,4503599627370492),
point(1,68719476734),
point(4,-5),
point(1.1e+308,2.1e+307),
point(-1,-8)
),
linestring(
point(3.5e+307,9.e+307),
point(1.1e+304,1.e+308),
point(-9,-4)
)
),
polygon(
linestring(
point(1.1e+308,1.2e+308),
point(-1,-9),
point(1,1000000000000),
point(1.1e+308,7.8e+307)
),
linestring(
point(3,2),
point(8.e+307,1.e+308),
point(1,1)
),
linestring(
point(258,2049),
point(1,-3),
point(1,1),
point(-6,9)
)
)
),
point(1,1)
);
ERROR 22023: Invalid GIS data provided to function polygon.
######################################################################################################
# BUG#21383714: Assertion failed: !thd->is_error() in select_lex::prepare
######################################################################################################
CREATE TABLE t(a INTEGER);
SELECT 1 FROM t WHERE 1 > SOME (SELECT POINT(1,1) FROM t);
ERROR HY000: Incorrect arguments to min
DROP TABLE t;
######################################################################################################
# BUG#21397107: ASSERTION FAILED: K < STATIC_CAST<SIZE_T>(GET_DIMENSION())
#              IN GIS_POINT::GET<0>(
######################################################################################################
SELECT ST_MakeEnvelope(POINT(1, 1), CONCAT((POINT(1, 1)), '1'));
ERROR 22023: Invalid GIS data provided to function st_makeenvelope.
SELECT ST_MakeEnvelope(POINT(2, -2), CAST(POINT(-7, -7) AS BINARY(48)));
ERROR 22023: Invalid GIS data provided to function st_makeenvelope.
SELECT ST_MakeEnvelope(Export_Set(1, POINT(1, 1), 1), POINT(1, 1));
ERROR 22023: Invalid GIS data provided to function st_makeenvelope.
SELECT ST_MakeEnvelope(Make_Set("38", 1, POINT(1, 1), 1, 1, 1), POINT(1,1));
ERROR 22023: Invalid GIS data provided to function st_makeenvelope.
######################################################################################################
# BUG#21616647: ASSERTION FAILED:
#               ITEM_IN->LEFT_EXPR->ELEMENT_INDEX(0)->MAYBE_NULL
######################################################################################################
CREATE TABLE t(col_a INTEGER) ENGINE=InnoDB;
INSERT INTO t VALUES (0), (0), (0);
SELECT ST_IsValid(NULL) NOT IN (SELECT col_a FROM t) FROM t;
ST_IsValid(NULL) NOT IN (SELECT col_a FROM t)
NULL
NULL
NULL
DROP TABLE t;
######################################################################################################
# BUG#21767301: ASSERTION `IT->OPERATIONS[0].OPERATION == OPERATION_UNION' FAILED
######################################################################################################
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('POLYGON((1 8,-17 -13,19
-9,1 8))'), ST_GEOMFROMTEXT('POLYGON((8 6,5 7,-1 4,-8 -7,0 -17,8 6),(3 6,5
5,0 -2,3 6))')));
ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('POLYGON((1 8,-17 -13,19
-9,1 8))'), ST_GEOMFROMTEXT('POLYGON((8 6,5 7,-1 4,-8 -7,0 -17,8 6),(3 6,5
5,0 -2,3 6))')))
GEOMETRYCOLLECTION(POINT(6.792727272727273 2.5290909090909075),MULTIPOLYGON(((6.792727272727273 2.5290909090909093,4.668246445497631 4.535545023696683,0 -2,3 6,-1 4,-8 -7,-4.326530612244898 -11.591836734693878,2.1306532663316586 -10.874371859296481,6.792727272727273 2.5290909090909093)),((3.076923076923077 6.038461538461538,3 6,3.25 5.875,3.076923076923077 6.038461538461538))))
#####################################################################################################
# BUG#21890717 ASSERTION FAILED: GEOMETRY::LESS<SEGMENTPOINT>()(P0, P1)
#####################################################################################################
do st_symdifference(
st_linefromtext( multilinestring( linestring(point(1,1),point(1,1)), linestring(point(1,1),point(1,1)) ) ),
st_distance(
multipoint( point(1,1),point(1,1),point(1,1),point(1,1), point(1,1),point(1,1),point(1,1),point(1,1), point(1,1) ) ,
st_intersection(
multilinestring( linestring(point(2,4),point(10,1)) ),
linestring(point(1,1),point(1e+308,1) ) )));
ERROR 22023: Invalid GIS data provided to function st_linefromtext.
#####################################################################################################
# BUG#21872795 ST_OVERLAPS(POLYGON, POLYGON) RETURNS WRONG RESULT
#####################################################################################################
SELECT ST_OVERLAPS(
ST_GEOMFROMTEXT('POLYGON((2 2,2 8,8 8,8 2,2 2))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(8 8,4 4,4 6,8 8))'));
ST_OVERLAPS(
ST_GEOMFROMTEXT('POLYGON((2 2,2 8,8 8,8 2,2 2))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(8 8,4 4,4 6,8 8))'))
1
SELECT ST_OVERLAPS(
ST_GEOMFROMTEXT('POLYGON((2 2,2 8,8 8,8 2,2 2))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(2 2,4 4,4 6,2 2))'));
ST_OVERLAPS(
ST_GEOMFROMTEXT('POLYGON((2 2,2 8,8 8,8 2,2 2))'),
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(2 2,4 4,4 6,2 2))'))
1
#####################################################################################################
# BUG#21873343 ST_TOUCHES(POLYGON, POLYGON) RETURNS INCORRECT RESULT
#####################################################################################################
SELECT ST_TOUCHES(
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(0 8,8 8,8 5,0 8))'),
ST_GEOMFROMTEXT('POLYGON((0 8,-8 8,-8 5,0 8))'));
ST_TOUCHES(
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(0 8,8 8,8 5,0 8))'),
ST_GEOMFROMTEXT('POLYGON((0 8,-8 8,-8 5,0 8))'))
1
SELECT ST_TOUCHES(
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(0 6,6 6,6 3,0 6))'),
ST_GEOMFROMTEXT('POLYGON((0 6,-6 6,-6 3,0 6))'));
ST_TOUCHES(
ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(0 6,6 6,6 3,0 6))'),
ST_GEOMFROMTEXT('POLYGON((0 6,-6 6,-6 3,0 6))'))
1
#####################################################################################################
# Bug#21783889 ASSERT GEOMETRY::EQUALS(CURRENT_ROBUST_RING.FRONT(),CURRENT_ROBUST_RING.BACK())
#####################################################################################################
do st_buffer(linestring(point(1,1),point(2,-1), point(1.765258e+308,4),point(-1,1),point(10,4)),'1');
ERROR HY000: Unknown GIS error occurred in function st_buffer.
do st_buffer( linestring( point(2199023255556,16777218), point(32770,8194), point(1.417733e+308,7.823620e+307), point(-8,-9), point(2147483649,20)),( 1099511627778 ) );
ERROR HY000: Unknown GIS error occurred in function st_buffer.
do st_buffer( linestring( point(-5,-8), point(2,8), point(2.160023e+307,1.937208e+307), point(-4,-3), point(-5,-4), point(8796093022208,281474976710653)), 0xbe );
ERROR HY000: Unknown GIS error occurred in function st_buffer.
do st_buffer( multilinestring( linestring( point(7,-4), point(-3,-5)), linestring( point(72057594037927936,15), point(72057594037927940,70368744177660), point(32771,36028797018963964), point(8589934589,2305843009213693953), point(7,2), point(9.300367e+307,9.649737e+307), point(-4092,-274877906946), point(5,10), point(-3,4))) , ( 4051744443 ) );
do st_buffer(
multilinestring(
linestring( point(-9,-10), point(0,-1), point(5,-10), point(-6,7), point(-7,7), point(5.041061e+307,9.926906e+307), point(6.870356e+307,1.064454e+307), point(35184372088830,288230376151711743), point(183673728842483250000000000000000000000.000000, 244323751784861950000000000000000000000.000000) ),
linestring( point(-23530,-7131), point(-6,1), point(1,1), point(2,-6), point(32766,-4194302), point(-4,-6)),
linestring( point(134217725,0), point(50336782742294697000000000000000000000.000000, 36696596077212901000000000000000000000.000000 ), point(7434,16486), point(3.025467e+307,8.926790e+307) ),
linestring( point(2147483646,67108868), point(71328904281592545000000000000000000000.000000, 225041650340452780000000000000000000000.000000 ), point(-7,4), point(1.667154e+307,3.990414e+307 ) ) ) , ( 2061380362 ) );
#####################################################################################################
# Bug#21965285 ST_DIFFERENCE(POLYGON, [MULTI]POLYGON) RETURNS INCORRECT RESULT
#####################################################################################################
SELECT ST_ASTEXT(ST_DIFFERENCE(ST_GEOMFROMTEXT('POLYGON((7 3,0 10,0
-6,9 -5,7 7,7 3),(0 8,2 3,2 -2,0 8))'), ST_GEOMFROMTEXT('POLYGON((0 6,0 8,-14
13,0 6))'))) AS result;
result
POLYGON((0 10,0 -6,9 -5,7 7,7 3,0 10),(0 8,2 3,2 -2,0 8))
#####################################################################################################
# Bug#21964079 ST_UNION(POLYGON, POLYGON) RETURNS INCORRECT RESULT
#####################################################################################################
SELECT ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((7 3,0 10,0 -6,9
-5,7 7,7 3),(0 8,2 3,2 -2,0 8))'), ST_GEOMFROMTEXT('POLYGON((0 8,-8 14,-11
18,-19 11,-3 1,0 8))')));
ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((7 3,0 10,0 -6,9
-5,7 7,7 3),(0 8,2 3,2 -2,0 8))'), ST_GEOMFROMTEXT('POLYGON((0 8,-8 14,-11
18,-19 11,-3 1,0 8))')))
MULTIPOLYGON(((0 10,0 -6,9 -5,7 7,7 3,0 10),(0 8,2 3,2 -2,0 8)),((0 8,-8 14,-11 18,-19 11,-3 1,0 8)))
SELECT ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10
0,0 0),(0 8,4 4,4 6,0 8))'), ST_GEOMFROMTEXT('POLYGON((0 8,-8 8,-10 4,0
8))')));
ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10
0,0 0),(0 8,4 4,4 6,0 8))'), ST_GEOMFROMTEXT('POLYGON((0 8,-8 8,-10 4,0
8))')))
MULTIPOLYGON(((0 10,0 0,10 0,10 10,0 10),(0 8,4 6,4 4,0 8)),((0 8,-8 8,-10 4,0 8)))
SELECT ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((5 0,15 18,-14 11,5
0),(0 3,-1 4,5 0,0 3))'), ST_GEOMFROMTEXT('POLYGON((6 0,-5 0,-1 -12,6 0))')));
ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((5 0,15 18,-14 11,5
0),(0 3,-1 4,5 0,0 3))'), ST_GEOMFROMTEXT('POLYGON((6 0,-5 0,-1 -12,6 0))')))
MULTIPOLYGON(((5 0,15 18,-14 11,5 0),(5 0,0 3,-1 4,5 0)),((6 0,-5 0,-1 -12,6 0)))
#####################################################################################################
# Bug#21964049 ST_UNION() RETURNS ERROR WITH VALID GEOMETRY INPUT
#####################################################################################################
SELECT ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((7 0,10 -3,7 1,7
0))'), ST_GEOMFROMTEXT('POLYGON((7 4,-14 10,7 -17,7 4),(7 1,0 3,-2 4,7
1))')));
ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((7 0,10 -3,7 1,7
0))'), ST_GEOMFROMTEXT('POLYGON((7 4,-14 10,7 -17,7 4),(7 1,0 3,-2 4,7
1))')))
POLYGON((7 1,7 4,-14 10,7 -17,7 0,10 -3,7 1),(7 1,0 3,-2 4,7 1))
SELECT ST_ISVALID(ST_UNION(ST_GEOMFROMTEXT('POLYGON((7 0,10 -3,7 1,7
0))'), ST_GEOMFROMTEXT('POLYGON((7 4,-14 10,7 -17,7 4),(7 1,0 3,-2 4,7
1))')));
ST_ISVALID(ST_UNION(ST_GEOMFROMTEXT('POLYGON((7 0,10 -3,7 1,7
0))'), ST_GEOMFROMTEXT('POLYGON((7 4,-14 10,7 -17,7 4),(7 1,0 3,-2 4,7
1))')))
1
#####################################################################################################
# Bug#21964465 ST_INTERSECTION(POLYGON, POLYGON) RETURNS INCORRECT RESULT
#####################################################################################################
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10
10,10 0,0 0 ),(0 8,4 4,4 6,0 8))'), ST_GEOMFROMTEXT('POLYGON((0 8,-8 8,-2 2,0
8))')));
ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10
10,10 0,0 0 ),(0 8,4 4,4 6,0 8))'), ST_GEOMFROMTEXT('POLYGON((0 8,-8 8,-2 2,0
8))')))
POINT(0 8)
#
# Bug #33290245 Item_func_st_distance_sphere::val_real():
#               Assertion `is_nullable()' failed.
do st_astext( ( _utf32', ,*') regexp 13946);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_aswkb( ( _utf32', ,*') regexp 13946);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_centroid( ( _utf32', ,*') regexp 13946);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_contains( ( _utf32', ,*') regexp 13946, ( _utf32', ,*') regexp 13946);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_convexhull( ( _utf32', ,*') regexp 13946);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_distance_sphere( ( ( _utf32', ,*') regexp 13946), 1, 42);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_envelope( ( _utf32', ,*') regexp 13946);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_geometrytype( ( _utf32', ,*') regexp 13946);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_geomfromtext( ( _utf32', ,*') regexp 13946);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_issimple( ( _utf32', ,*') regexp 13946);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_isvalid( ( _utf32', ,*') regexp 13946);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_latitude( ( _utf32', ,*') regexp 13946);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_makeenvelope( ( _utf32', ,*') regexp 13946, 'POINT(0 0)');
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_simplify( ( _utf32', ,*') regexp 13946, 'POINT(0 0)');
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_srid( st_pointfromtext(( _utf32', ,*') regexp 13946));
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
do st_validate( ( _utf32', ,*') regexp 13946);
ERROR HY000: Cannot convert string '\x2C\x20\x2C\x2A' from utf32 to utf16
