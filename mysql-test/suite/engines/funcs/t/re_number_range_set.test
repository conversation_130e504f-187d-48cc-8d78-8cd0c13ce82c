--source include/no_valgrind_without_big.inc
--disable_warnings
DROP TABLE IF EXISTS t1,t2,t3;
--enable_warnings
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 < 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 = 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 >= 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 != 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
REPLACE t1 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t1;
SELECT COUNT(c1),SUM(c1) FROM t1 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
REPLACE t2 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t2;
SELECT COUNT(c1),SUM(c1) FROM t2 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 TINYINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 SMALLINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 INTEGER NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;
CREATE TABLE t1(c1 BIGINT NOT NULL PRIMARY KEY);
CREATE TABLE t2(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t2 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY);
INSERT INTO t3 (c1) VALUES(5);
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
REPLACE t3 SET c1 = 5;
SELECT COUNT(c1),SUM(c1) FROM t3;
SELECT COUNT(c1),SUM(c1) FROM t3 WHERE c1 <=> 5;
DROP TABLE t1,t2,t3;

