-- MySQL dump 10.13  Distrib 5.7.30, for Linux (x86_64)
--
-- Host: localhost    Database: mysql
-- ------------------------------------------------------
-- Server version	5.7.30-debug-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb3 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `mysql`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `mysql` /*!40100 DEFAULT CHARACTER SET latin1 */;

USE `mysql`;

--
-- Table structure for table `columns_priv`
--

DROP TABLE IF EXISTS `columns_priv`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `columns_priv` (
  `Host` char(60) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Db` char(64) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `User` char(32) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Table_name` char(64) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Column_name` char(64) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Column_priv` set('Select','Insert','Update','References') CHARACTER SET utf8mb3 NOT NULL DEFAULT '',
  PRIMARY KEY (`Host`,`Db`,`User`,`Table_name`,`Column_name`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='Column privileges';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `columns_priv`
--

LOCK TABLES `columns_priv` WRITE;
/*!40000 ALTER TABLE `columns_priv` DISABLE KEYS */;
/*!40000 ALTER TABLE `columns_priv` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `db`
--

DROP TABLE IF EXISTS `db`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `db` (
  `Host` char(60) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Db` char(64) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `User` char(32) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Select_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Insert_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Update_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Delete_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Create_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Drop_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Grant_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `References_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Index_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Alter_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Create_tmp_table_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Lock_tables_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Create_view_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Show_view_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Create_routine_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Alter_routine_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Execute_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Event_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Trigger_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  PRIMARY KEY (`Host`,`Db`,`User`),
  KEY `User` (`User`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='Database privileges';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `db`
--

LOCK TABLES `db` WRITE;
/*!40000 ALTER TABLE `db` DISABLE KEYS */;
INSERT INTO `db` VALUES ('%','test','','Y','Y','Y','Y','Y','Y','N','Y','Y','Y','Y','Y','Y','Y','Y','N','N','Y','Y'),('%','test\\_%','','Y','Y','Y','Y','Y','Y','N','Y','Y','Y','Y','Y','Y','Y','Y','N','N','Y','Y'),('localhost','sys','mysql.sys','N','N','N','N','N','N','N','N','N','N','N','N','N','N','N','N','N','N','Y'),('localhost','performance_schema','mysql.session','Y','N','N','N','N','N','N','N','N','N','N','N','N','N','N','N','N','N','N');
/*!40000 ALTER TABLE `db` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `engine_cost`
--

DROP TABLE IF EXISTS `engine_cost`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `engine_cost` (
  `engine_name` varchar(64) NOT NULL,
  `device_type` int(11) NOT NULL,
  `cost_name` varchar(64) NOT NULL,
  `cost_value` float DEFAULT NULL,
  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `comment` varchar(1024) DEFAULT NULL,
  PRIMARY KEY (`cost_name`,`engine_name`,`device_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `engine_cost`
--

LOCK TABLES `engine_cost` WRITE;
/*!40000 ALTER TABLE `engine_cost` DISABLE KEYS */;
INSERT INTO `engine_cost` VALUES ('default',0,'io_block_read_cost',NULL,'2020-02-05 07:54:16',NULL),('default',0,'memory_block_read_cost',NULL,'2020-02-05 07:54:16',NULL);
/*!40000 ALTER TABLE `engine_cost` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `event`
--

DROP TABLE IF EXISTS `event`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `event` (
  `db` char(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `name` char(64) NOT NULL DEFAULT '',
  `body` longblob NOT NULL,
  `definer` char(93) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `execute_at` datetime DEFAULT NULL,
  `interval_value` int(11) DEFAULT NULL,
  `interval_field` enum('YEAR','QUARTER','MONTH','DAY','HOUR','MINUTE','WEEK','SECOND','MICROSECOND','YEAR_MONTH','DAY_HOUR','DAY_MINUTE','DAY_SECOND','HOUR_MINUTE','HOUR_SECOND','MINUTE_SECOND','DAY_MICROSECOND','HOUR_MICROSECOND','MINUTE_MICROSECOND','SECOND_MICROSECOND') DEFAULT NULL,
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `modified` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `last_executed` datetime DEFAULT NULL,
  `starts` datetime DEFAULT NULL,
  `ends` datetime DEFAULT NULL,
  `status` enum('ENABLED','DISABLED','SLAVESIDE_DISABLED') NOT NULL DEFAULT 'ENABLED',
  `on_completion` enum('DROP','PRESERVE') NOT NULL DEFAULT 'DROP',
  `sql_mode` set('REAL_AS_FLOAT','PIPES_AS_CONCAT','ANSI_QUOTES','IGNORE_SPACE','NOT_USED','ONLY_FULL_GROUP_BY','NO_UNSIGNED_SUBTRACTION','NO_DIR_IN_CREATE','POSTGRESQL','ORACLE','MSSQL','DB2','MAXDB','NO_KEY_OPTIONS','NO_TABLE_OPTIONS','NO_FIELD_OPTIONS','MYSQL323','MYSQL40','ANSI','NO_AUTO_VALUE_ON_ZERO','NO_BACKSLASH_ESCAPES','STRICT_TRANS_TABLES','STRICT_ALL_TABLES','NO_ZERO_IN_DATE','NO_ZERO_DATE','INVALID_DATES','ERROR_FOR_DIVISION_BY_ZERO','TRADITIONAL','NO_AUTO_CREATE_USER','HIGH_NOT_PRECEDENCE','NO_ENGINE_SUBSTITUTION','PAD_CHAR_TO_FULL_LENGTH') NOT NULL DEFAULT '',
  `comment` char(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `originator` int(10) unsigned NOT NULL,
  `time_zone` char(64) CHARACTER SET latin1 NOT NULL DEFAULT 'SYSTEM',
  `character_set_client` char(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `collation_connection` char(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `db_collation` char(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `body_utf8` longblob,
  PRIMARY KEY (`db`,`name`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COMMENT='Events';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `event`
--

LOCK TABLES `event` WRITE;
/*!40000 ALTER TABLE `event` DISABLE KEYS */;
/*!40000 ALTER TABLE `event` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `func`
--

DROP TABLE IF EXISTS `func`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `func` (
  `name` char(64) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `ret` tinyint(1) NOT NULL DEFAULT '0',
  `dl` char(128) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `type` enum('function','aggregate') CHARACTER SET utf8mb3 NOT NULL,
  PRIMARY KEY (`name`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='User defined functions';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `func`
--

LOCK TABLES `func` WRITE;
/*!40000 ALTER TABLE `func` DISABLE KEYS */;
/*!40000 ALTER TABLE `func` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gtid_executed`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE IF NOT EXISTS `gtid_executed` (
  `source_uuid` char(36) NOT NULL COMMENT 'uuid of the source where the transaction was originally executed.',
  `interval_start` bigint(20) NOT NULL COMMENT 'First number of interval.',
  `interval_end` bigint(20) NOT NULL COMMENT 'Last number of interval.',
  PRIMARY KEY (`source_uuid`,`interval_start`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `help_category`
--

DROP TABLE IF EXISTS `help_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `help_category` (
  `help_category_id` smallint(5) unsigned NOT NULL,
  `name` char(64) NOT NULL,
  `parent_category_id` smallint(5) unsigned DEFAULT NULL,
  `url` text NOT NULL,
  PRIMARY KEY (`help_category_id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='help categories';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `help_category`
--

LOCK TABLES `help_category` WRITE;
/*!40000 ALTER TABLE `help_category` DISABLE KEYS */;
/*!40000 ALTER TABLE `help_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `help_keyword`
--

DROP TABLE IF EXISTS `help_keyword`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `help_keyword` (
  `help_keyword_id` int(10) unsigned NOT NULL,
  `name` char(64) NOT NULL,
  PRIMARY KEY (`help_keyword_id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='help keywords';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `help_keyword`
--

LOCK TABLES `help_keyword` WRITE;
/*!40000 ALTER TABLE `help_keyword` DISABLE KEYS */;
/*!40000 ALTER TABLE `help_keyword` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `help_relation`
--

DROP TABLE IF EXISTS `help_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `help_relation` (
  `help_topic_id` int(10) unsigned NOT NULL,
  `help_keyword_id` int(10) unsigned NOT NULL,
  PRIMARY KEY (`help_keyword_id`,`help_topic_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='keyword-topic relation';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `help_relation`
--

LOCK TABLES `help_relation` WRITE;
/*!40000 ALTER TABLE `help_relation` DISABLE KEYS */;
/*!40000 ALTER TABLE `help_relation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `help_topic`
--

DROP TABLE IF EXISTS `help_topic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `help_topic` (
  `help_topic_id` int(10) unsigned NOT NULL,
  `name` char(64) NOT NULL,
  `help_category_id` smallint(5) unsigned NOT NULL,
  `description` text NOT NULL,
  `example` text NOT NULL,
  `url` text NOT NULL,
  PRIMARY KEY (`help_topic_id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='help topics';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `help_topic`
--

LOCK TABLES `help_topic` WRITE;
/*!40000 ALTER TABLE `help_topic` DISABLE KEYS */;
/*!40000 ALTER TABLE `help_topic` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ndb_binlog_index`
--

DROP TABLE IF EXISTS `ndb_binlog_index`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `ndb_binlog_index` (
  `Position` bigint(20) unsigned NOT NULL,
  `File` varchar(255) NOT NULL,
  `epoch` bigint(20) unsigned NOT NULL,
  `inserts` int(10) unsigned NOT NULL,
  `updates` int(10) unsigned NOT NULL,
  `deletes` int(10) unsigned NOT NULL,
  `schemaops` int(10) unsigned NOT NULL,
  `orig_server_id` int(10) unsigned NOT NULL,
  `orig_epoch` bigint(20) unsigned NOT NULL,
  `gci` int(10) unsigned NOT NULL,
  `next_position` bigint(20) unsigned NOT NULL,
  `next_file` varchar(255) NOT NULL,
  PRIMARY KEY (`epoch`,`orig_server_id`,`orig_epoch`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ndb_binlog_index`
--

LOCK TABLES `ndb_binlog_index` WRITE;
/*!40000 ALTER TABLE `ndb_binlog_index` DISABLE KEYS */;
/*!40000 ALTER TABLE `ndb_binlog_index` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `plugin`
--

DROP TABLE IF EXISTS `plugin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `plugin` (
  `name` varchar(64) NOT NULL DEFAULT '',
  `dl` varchar(128) NOT NULL DEFAULT '',
  PRIMARY KEY (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='MySQL plugins';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `plugin`
--

LOCK TABLES `plugin` WRITE;
/*!40000 ALTER TABLE `plugin` DISABLE KEYS */;
/*!40000 ALTER TABLE `plugin` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `proc`
--

DROP TABLE IF EXISTS `proc`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `proc` (
  `db` char(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `name` char(64) NOT NULL DEFAULT '',
  `type` enum('FUNCTION','PROCEDURE') NOT NULL,
  `specific_name` char(64) NOT NULL DEFAULT '',
  `language` enum('SQL') NOT NULL DEFAULT 'SQL',
  `sql_data_access` enum('CONTAINS_SQL','NO_SQL','READS_SQL_DATA','MODIFIES_SQL_DATA') NOT NULL DEFAULT 'CONTAINS_SQL',
  `is_deterministic` enum('YES','NO') NOT NULL DEFAULT 'NO',
  `security_type` enum('INVOKER','DEFINER') NOT NULL DEFAULT 'DEFINER',
  `param_list` blob NOT NULL,
  `returns` longblob NOT NULL,
  `body` longblob NOT NULL,
  `definer` char(93) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `modified` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `sql_mode` set('REAL_AS_FLOAT','PIPES_AS_CONCAT','ANSI_QUOTES','IGNORE_SPACE','NOT_USED','ONLY_FULL_GROUP_BY','NO_UNSIGNED_SUBTRACTION','NO_DIR_IN_CREATE','POSTGRESQL','ORACLE','MSSQL','DB2','MAXDB','NO_KEY_OPTIONS','NO_TABLE_OPTIONS','NO_FIELD_OPTIONS','MYSQL323','MYSQL40','ANSI','NO_AUTO_VALUE_ON_ZERO','NO_BACKSLASH_ESCAPES','STRICT_TRANS_TABLES','STRICT_ALL_TABLES','NO_ZERO_IN_DATE','NO_ZERO_DATE','INVALID_DATES','ERROR_FOR_DIVISION_BY_ZERO','TRADITIONAL','NO_AUTO_CREATE_USER','HIGH_NOT_PRECEDENCE','NO_ENGINE_SUBSTITUTION','PAD_CHAR_TO_FULL_LENGTH') NOT NULL DEFAULT '',
  `comment` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `character_set_client` char(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `collation_connection` char(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `db_collation` char(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `body_utf8` longblob,
  PRIMARY KEY (`db`,`name`,`type`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COMMENT='Stored Procedures';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `proc`
--

LOCK TABLES `proc` WRITE;
/*!40000 ALTER TABLE `proc` DISABLE KEYS */;
INSERT INTO `proc` VALUES ('sys','extract_schema_from_file_name','FUNCTION','extract_schema_from_file_name','SQL','NO_SQL','YES','INVOKER',_binary ' path VARCHAR(512) ',_binary 'varchar(64) CHARSET utf8mb3',_binary 'BEGIN RETURN LEFT(SUBSTRING_INDEX(SUBSTRING_INDEX(REPLACE(path, \'\\\\\', \'/\'), \'/\', -2), \'/\', 1), 64); END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Takes a raw file path, and attempts to extract the schema name from it.\n \n Useful for when interacting with Performance Schema data \n concerning IO statistics, for example.\n \n Currently relies on the fact that a table data file will be within a \n specified database directory (will not work with partitions or tables\n that specify an individual DATA_DIRECTORY).\n \n Parameters\n \n path (VARCHAR(512)):\n The full file path to a data file to extract the schema name from.\n \n Returns\n \n VARCHAR(64)\n \n Example\n \n mysql> SELECT sys.extract_schema_from_file_name(\'/var/lib/mysql/employees/employee.ibd\');\n +----------------------------------------------------------------------------+\n | sys.extract_schema_from_file_name(\'/var/lib/mysql/employees/employee.ibd\') |\n +----------------------------------------------------------------------------+\n | employees                                                                  |\n +----------------------------------------------------------------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN RETURN LEFT(SUBSTRING_INDEX(SUBSTRING_INDEX(REPLACE(path, \'\\\', \'/\'), \'/\', -2), \'/\', 1), 64); END'),('sys','extract_table_from_file_name','FUNCTION','extract_table_from_file_name','SQL','NO_SQL','YES','INVOKER',_binary ' path VARCHAR(512) ',_binary 'varchar(64) CHARSET utf8mb3',_binary 'BEGIN RETURN LEFT(SUBSTRING_INDEX(REPLACE(SUBSTRING_INDEX(REPLACE(path, \'\\\\\', \'/\'), \'/\', -1), \'@0024\', \'$\'), \'.\', 1), 64); END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Takes a raw file path, and extracts the table name from it.\n \n Useful for when interacting with Performance Schema data \n concerning IO statistics, for example.\n \n Parameters\n \n path (VARCHAR(512)):\n The full file path to a data file to extract the table name from.\n \n Returns\n \n VARCHAR(64)\n \n Example\n \n mysql> SELECT sys.extract_table_from_file_name(\'/var/lib/mysql/employees/employee.ibd\');\n +---------------------------------------------------------------------------+\n | sys.extract_table_from_file_name(\'/var/lib/mysql/employees/employee.ibd\') |\n +---------------------------------------------------------------------------+\n | employee                                                                  |\n +---------------------------------------------------------------------------+\n 1 row in set (0.02 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN RETURN LEFT(SUBSTRING_INDEX(REPLACE(SUBSTRING_INDEX(REPLACE(path, \'\\\', \'/\'), \'/\', -1), \'@0024\', \'$\'), \'.\', 1), 64); END'),('sys','format_bytes','FUNCTION','format_bytes','SQL','NO_SQL','YES','INVOKER',_binary ' bytes TEXT ',_binary 'text CHARSET utf8mb3',_binary 'BEGIN IF bytes IS NULL THEN RETURN NULL; ELSEIF bytes >= 1125899906842624 THEN RETURN CONCAT(ROUND(bytes / 1125899906842624, 2), \' PiB\'); ELSEIF bytes >= 1099511627776 THEN RETURN CONCAT(ROUND(bytes / 1099511627776, 2), \' TiB\'); ELSEIF bytes >= 1073741824 THEN RETURN CONCAT(ROUND(bytes / 1073741824, 2), \' GiB\'); ELSEIF bytes >= 1048576 THEN RETURN CONCAT(ROUND(bytes / 1048576, 2), \' MiB\'); ELSEIF bytes >= 1024 THEN RETURN CONCAT(ROUND(bytes / 1024, 2), \' KiB\'); ELSE RETURN CONCAT(ROUND(bytes, 0), \' bytes\'); END IF; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Takes a raw bytes value, and converts it to a human readable format.\n \n Parameters\n \n bytes (TEXT):\n A raw bytes value.\n \n Returns\n \n TEXT\n \n Example\n \n mysql> SELECT sys.format_bytes(2348723492723746) AS size;\n +----------+\n | size     |\n +----------+\n | 2.09 PiB |\n +----------+\n 1 row in set (0.00 sec)\n \n mysql> SELECT sys.format_bytes(2348723492723) AS size;\n +----------+\n | size     |\n +----------+\n | 2.14 TiB |\n +----------+\n 1 row in set (0.00 sec)\n \n mysql> SELECT sys.format_bytes(23487234) AS size;\n +-----------+\n | size      |\n +-----------+\n | 22.40 MiB |\n +-----------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN IF bytes IS NULL THEN RETURN NULL; ELSEIF bytes >= 1125899906842624 THEN RETURN CONCAT(ROUND(bytes / 1125899906842624, 2), \' PiB\'); ELSEIF bytes >= 1099511627776 THEN RETURN CONCAT(ROUND(bytes / 1099511627776, 2), \' TiB\'); ELSEIF bytes >= 1073741824 THEN RETURN CONCAT(ROUND(bytes / 1073741824, 2), \' GiB\'); ELSEIF bytes >= 1048576 THEN RETURN CONCAT(ROUND(bytes / 1048576, 2), \' MiB\'); ELSEIF bytes >= 1024 THEN RETURN CONCAT(ROUND(bytes / 1024, 2), \' KiB\'); ELSE RETURN CONCAT(ROUND(bytes, 0), \' bytes\'); END IF; END'),('sys','format_path','FUNCTION','format_path','SQL','NO_SQL','YES','INVOKER',_binary ' in_path VARCHAR(512) ',_binary 'varchar(512) CHARSET utf8mb3',_binary 'BEGIN DECLARE v_path VARCHAR(512); DECLARE v_undo_dir VARCHAR(1024);  DECLARE path_separator CHAR(1) DEFAULT \'/\';  IF @@global.version_compile_os LIKE \'win%\' THEN SET path_separator = \'\\\\\'; END IF;  IF in_path LIKE \'/private/%\' THEN SET v_path = REPLACE(in_path, \'/private\', \'\'); ELSE SET v_path = in_path; END IF;  SET v_undo_dir = IFNULL((SELECT VARIABLE_VALUE FROM performance_schema.global_variables WHERE VARIABLE_NAME = \'innodb_undo_directory\'), \'\');  IF v_path IS NULL THEN RETURN NULL; ELSEIF v_path LIKE CONCAT(@@global.datadir, IF(SUBSTRING(@@global.datadir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, @@global.datadir, CONCAT(\'@@datadir\', IF(SUBSTRING(@@global.datadir, -1) = path_separator, path_separator, \'\'))); ELSEIF v_path LIKE CONCAT(@@global.tmpdir, IF(SUBSTRING(@@global.tmpdir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, @@global.tmpdir, CONCAT(\'@@tmpdir\', IF(SUBSTRING(@@global.tmpdir, -1) = path_separator, path_separator, \'\'))); ELSEIF v_path LIKE CONCAT(@@global.slave_load_tmpdir, IF(SUBSTRING(@@global.slave_load_tmpdir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, @@global.slave_load_tmpdir, CONCAT(\'@@slave_load_tmpdir\', IF(SUBSTRING(@@global.slave_load_tmpdir, -1) = path_separator, path_separator, \'\'))); ELSEIF v_path LIKE CONCAT(@@global.innodb_data_home_dir, IF(SUBSTRING(@@global.innodb_data_home_dir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, @@global.innodb_data_home_dir, CONCAT(\'@@innodb_data_home_dir\', IF(SUBSTRING(@@global.innodb_data_home_dir, -1) = path_separator, path_separator, \'\'))); ELSEIF v_path LIKE CONCAT(@@global.innodb_log_group_home_dir, IF(SUBSTRING(@@global.innodb_log_group_home_dir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, @@global.innodb_log_group_home_dir, CONCAT(\'@@innodb_log_group_home_dir\', IF(SUBSTRING(@@global.innodb_log_group_home_dir, -1) = path_separator, path_separator, \'\'))); ELSEIF v_path LIKE CONCAT(v_undo_dir, IF(SUBSTRING(v_undo_dir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, v_undo_dir, CONCAT(\'@@innodb_undo_directory\', IF(SUBSTRING(v_undo_dir, -1) = path_separator, path_separator, \'\'))); ELSEIF v_path LIKE CONCAT(@@global.basedir, IF(SUBSTRING(@@global.basedir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, @@global.basedir, CONCAT(\'@@basedir\', IF(SUBSTRING(@@global.basedir, -1) = path_separator, path_separator, \'\'))); END IF;  RETURN v_path; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Takes a raw path value, and strips out the datadir or tmpdir\n replacing with @@datadir and @@tmpdir respectively.\n \n Also normalizes the paths across operating systems, so backslashes\n on Windows are converted to forward slashes\n \n Parameters\n \n path (VARCHAR(512)):\n The raw file path value to format.\n \n Returns\n \n VARCHAR(512) CHARSET utf8mb3\n \n Example\n \n mysql> select @@datadir;\n +-----------------------------------------------+\n | @@datadir                                     |\n +-----------------------------------------------+\n | /Users/<USER>/sandboxes/SmallTree/AMaster/data/ |\n +-----------------------------------------------+\n 1 row in set (0.06 sec)\n \n mysql> select format_path(\'/Users/<USER>/sandboxes/SmallTree/AMaster/data/mysql/proc.MYD\') AS path;\n +--------------------------+\n | path                     |\n +--------------------------+\n | @@datadir/mysql/proc.MYD |\n +--------------------------+\n 1 row in set (0.03 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_path VARCHAR(512); DECLARE v_undo_dir VARCHAR(1024);  DECLARE path_separator CHAR(1) DEFAULT \'/\';  IF @@global.version_compile_os LIKE \'win%\' THEN SET path_separator = \'\\\'; END IF;  IF in_path LIKE \'/private/%\' THEN SET v_path = REPLACE(in_path, \'/private\', \'\'); ELSE SET v_path = in_path; END IF;  SET v_undo_dir = IFNULL((SELECT VARIABLE_VALUE FROM performance_schema.global_variables WHERE VARIABLE_NAME = \'innodb_undo_directory\'), \'\');  IF v_path IS NULL THEN RETURN NULL; ELSEIF v_path LIKE CONCAT(@@global.datadir, IF(SUBSTRING(@@global.datadir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, @@global.datadir, CONCAT(\'@@datadir\', IF(SUBSTRING(@@global.datadir, -1) = path_separator, path_separator, \'\'))); ELSEIF v_path LIKE CONCAT(@@global.tmpdir, IF(SUBSTRING(@@global.tmpdir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, @@global.tmpdir, CONCAT(\'@@tmpdir\', IF(SUBSTRING(@@global.tmpdir, -1) = path_separator, path_separator, \'\'))); ELSEIF v_path LIKE CONCAT(@@global.slave_load_tmpdir, IF(SUBSTRING(@@global.slave_load_tmpdir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, @@global.slave_load_tmpdir, CONCAT(\'@@slave_load_tmpdir\', IF(SUBSTRING(@@global.slave_load_tmpdir, -1) = path_separator, path_separator, \'\'))); ELSEIF v_path LIKE CONCAT(@@global.innodb_data_home_dir, IF(SUBSTRING(@@global.innodb_data_home_dir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, @@global.innodb_data_home_dir, CONCAT(\'@@innodb_data_home_dir\', IF(SUBSTRING(@@global.innodb_data_home_dir, -1) = path_separator, path_separator, \'\'))); ELSEIF v_path LIKE CONCAT(@@global.innodb_log_group_home_dir, IF(SUBSTRING(@@global.innodb_log_group_home_dir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, @@global.innodb_log_group_home_dir, CONCAT(\'@@innodb_log_group_home_dir\', IF(SUBSTRING(@@global.innodb_log_group_home_dir, -1) = path_separator, path_separator, \'\'))); ELSEIF v_path LIKE CONCAT(v_undo_dir, IF(SUBSTRING(v_undo_dir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, v_undo_dir, CONCAT(\'@@innodb_undo_directory\', IF(SUBSTRING(v_undo_dir, -1) = path_separator, path_separator, \'\'))); ELSEIF v_path LIKE CONCAT(@@global.basedir, IF(SUBSTRING(@@global.basedir, -1) = path_separator, \'%\', CONCAT(path_separator, \'%\'))) ESCAPE \'|\' THEN SET v_path = REPLACE(v_path, @@global.basedir, CONCAT(\'@@basedir\', IF(SUBSTRING(@@global.basedir, -1) = path_separator, path_separator, \'\'))); END IF;  RETURN v_path; END'),('sys','format_statement','FUNCTION','format_statement','SQL','NO_SQL','YES','INVOKER',_binary ' statement LONGTEXT ',_binary 'longtext CHARSET utf8mb3',_binary 'BEGIN IF @sys.statement_truncate_len IS NULL THEN SET @sys.statement_truncate_len = sys_get_config(\'statement_truncate_len\', 64); END IF;  IF CHAR_LENGTH(statement) > @sys.statement_truncate_len THEN RETURN REPLACE(CONCAT(LEFT(statement, (@sys.statement_truncate_len/2)-2), \' ... \', RIGHT(statement, (@sys.statement_truncate_len/2)-2)), \'\\n\', \' \'); ELSE  RETURN REPLACE(statement, \'\\n\', \' \'); END IF; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Formats a normalized statement, truncating it if it is > 64 characters long by default.\n \n To configure the length to truncate the statement to by default, update the `statement_truncate_len`\n variable with `sys_config` table to a different value. Alternatively, to change it just for just \n your particular session, use `SET @sys.statement_truncate_len := <some new value>`.\n \n Useful for printing statement related data from Performance Schema from \n the command line.\n \n Parameters\n \n statement (LONGTEXT): \n The statement to format.\n \n Returns\n \n LONGTEXT\n \n Example\n \n mysql> SELECT sys.format_statement(digest_text)\n ->   FROM performance_schema.events_statements_summary_by_digest\n ->  ORDER by sum_timer_wait DESC limit 5;\n +-------------------------------------------------------------------+\n | sys.format_statement(digest_text)                                 |\n +-------------------------------------------------------------------+\n | CREATE SQL SECURITY INVOKER VI ... KE ? AND `variable_value` > ?  |\n | CREATE SQL SECURITY INVOKER VI ... ait` IS NOT NULL , `esc` . ... |\n | CREATE SQL SECURITY INVOKER VI ... ait` IS NOT NULL , `sys` . ... |\n | CREATE SQL SECURITY INVOKER VI ...  , `compressed_size` ) ) DESC  |\n | CREATE SQL SECURITY INVOKER VI ... LIKE ? ORDER BY `timer_start`  |\n +-------------------------------------------------------------------+\n 5 rows in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN IF @sys.statement_truncate_len IS NULL THEN SET @sys.statement_truncate_len = sys_get_config(\'statement_truncate_len\', 64); END IF;  IF CHAR_LENGTH(statement) > @sys.statement_truncate_len THEN RETURN REPLACE(CONCAT(LEFT(statement, (@sys.statement_truncate_len/2)-2), \' ... \', RIGHT(statement, (@sys.statement_truncate_len/2)-2)), \'\n\', \' \'); ELSE  RETURN REPLACE(statement, \'\n\', \' \'); END IF; END'),('sys','format_time','FUNCTION','format_time','SQL','NO_SQL','YES','INVOKER',_binary ' picoseconds TEXT ',_binary 'text CHARSET utf8mb3',_binary 'BEGIN IF picoseconds IS NULL THEN RETURN NULL; ELSEIF picoseconds >= 604800000000000000 THEN RETURN CONCAT(ROUND(picoseconds / 604800000000000000, 2), \' w\'); ELSEIF picoseconds >= 86400000000000000 THEN RETURN CONCAT(ROUND(picoseconds / 86400000000000000, 2), \' d\'); ELSEIF picoseconds >= 3600000000000000 THEN RETURN CONCAT(ROUND(picoseconds / 3600000000000000, 2), \' h\'); ELSEIF picoseconds >= 60000000000000 THEN RETURN CONCAT(ROUND(picoseconds / 60000000000000, 2), \' m\'); ELSEIF picoseconds >= 1000000000000 THEN RETURN CONCAT(ROUND(picoseconds / 1000000000000, 2), \' s\'); ELSEIF picoseconds >= 1000000000 THEN RETURN CONCAT(ROUND(picoseconds / 1000000000, 2), \' ms\'); ELSEIF picoseconds >= 1000000 THEN RETURN CONCAT(ROUND(picoseconds / 1000000, 2), \' us\'); ELSEIF picoseconds >= 1000 THEN RETURN CONCAT(ROUND(picoseconds / 1000, 2), \' ns\'); ELSE RETURN CONCAT(picoseconds, \' ps\'); END IF; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Takes a raw picoseconds value, and converts it to a human readable form.\n \n Picoseconds are the precision that all latency values are printed in\n within Performance Schema, however are not user friendly when wanting\n to scan output from the command line.\n \n Parameters\n \n picoseconds (TEXT):\n The raw picoseconds value to convert.\n \n Returns\n \n TEXT\n \n Example\n \n mysql> select format_time(342342342342345);\n +------------------------------+\n | format_time(342342342342345) |\n +------------------------------+\n | 00:05:42                     |\n +------------------------------+\n 1 row in set (0.00 sec)\n \n mysql> select format_time(342342342);\n +------------------------+\n | format_time(342342342) |\n +------------------------+\n | 342.34 us              |\n +------------------------+\n 1 row in set (0.00 sec)\n \n mysql> select format_time(34234);\n +--------------------+\n | format_time(34234) |\n +--------------------+\n | 34.23 ns           |\n +--------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN IF picoseconds IS NULL THEN RETURN NULL; ELSEIF picoseconds >= 604800000000000000 THEN RETURN CONCAT(ROUND(picoseconds / 604800000000000000, 2), \' w\'); ELSEIF picoseconds >= 86400000000000000 THEN RETURN CONCAT(ROUND(picoseconds / 86400000000000000, 2), \' d\'); ELSEIF picoseconds >= 3600000000000000 THEN RETURN CONCAT(ROUND(picoseconds / 3600000000000000, 2), \' h\'); ELSEIF picoseconds >= 60000000000000 THEN RETURN CONCAT(ROUND(picoseconds / 60000000000000, 2), \' m\'); ELSEIF picoseconds >= 1000000000000 THEN RETURN CONCAT(ROUND(picoseconds / 1000000000000, 2), \' s\'); ELSEIF picoseconds >= 1000000000 THEN RETURN CONCAT(ROUND(picoseconds / 1000000000, 2), \' ms\'); ELSEIF picoseconds >= 1000000 THEN RETURN CONCAT(ROUND(picoseconds / 1000000, 2), \' us\'); ELSEIF picoseconds >= 1000 THEN RETURN CONCAT(ROUND(picoseconds / 1000, 2), \' ns\'); ELSE RETURN CONCAT(picoseconds, \' ps\'); END IF; END'),('sys','list_add','FUNCTION','list_add','SQL','CONTAINS_SQL','YES','INVOKER',_binary ' in_list TEXT, in_add_value TEXT ',_binary 'text CHARSET utf8mb3',_binary 'BEGIN  IF (in_add_value IS NULL) THEN SIGNAL SQLSTATE \'02200\' SET MESSAGE_TEXT = \'Function sys.list_add: in_add_value input variable should not be NULL\', MYSQL_ERRNO = 1138; END IF;  IF (in_list IS NULL OR LENGTH(in_list) = 0) THEN RETURN in_add_value; END IF;  RETURN (SELECT CONCAT(TRIM(BOTH \',\' FROM TRIM(in_list)), \',\', in_add_value));  END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Takes a list, and a value to add to the list, and returns the resulting list.\n \n Useful for altering certain session variables, like sql_mode or optimizer_switch for instance.\n \n Parameters\n \n in_list (TEXT):\n The comma separated list to add a value to\n \n in_add_value (TEXT):\n The value to add to the input list\n \n Returns\n \n TEXT\n \n Example\n \n mysql> select @@sql_mode;\n +-----------------------------------------------------------------------------------+\n | @@sql_mode                                                                        |\n +-----------------------------------------------------------------------------------+\n | ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION |\n +-----------------------------------------------------------------------------------+\n 1 row in set (0.00 sec)\n \n mysql> set sql_mode = sys.list_add(@@sql_mode, \'ANSI_QUOTES\');\n Query OK, 0 rows affected (0.06 sec)\n \n mysql> select @@sql_mode;\n +-----------------------------------------------------------------------------------------------+\n | @@sql_mode                                                                                    |\n +-----------------------------------------------------------------------------------------------+\n | ANSI_QUOTES,ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION |\n +-----------------------------------------------------------------------------------------------+\n 1 row in set (0.00 sec)\n \n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN  IF (in_add_value IS NULL) THEN SIGNAL SQLSTATE \'02200\' SET MESSAGE_TEXT = \'Function sys.list_add: in_add_value input variable should not be NULL\', MYSQL_ERRNO = 1138; END IF;  IF (in_list IS NULL OR LENGTH(in_list) = 0) THEN RETURN in_add_value; END IF;  RETURN (SELECT CONCAT(TRIM(BOTH \',\' FROM TRIM(in_list)), \',\', in_add_value));  END'),('sys','list_drop','FUNCTION','list_drop','SQL','CONTAINS_SQL','YES','INVOKER',_binary ' in_list TEXT, in_drop_value TEXT ',_binary 'text CHARSET utf8mb3',_binary 'BEGIN  IF (in_drop_value IS NULL) THEN SIGNAL SQLSTATE \'02200\' SET MESSAGE_TEXT = \'Function sys.list_drop: in_drop_value input variable should not be NULL\', MYSQL_ERRNO = 1138; END IF;  IF (in_list IS NULL OR LENGTH(in_list) = 0) THEN RETURN in_list; END IF;  RETURN (SELECT TRIM(BOTH \',\' FROM REPLACE(REPLACE(CONCAT(\',\', in_list), CONCAT(\',\', in_drop_value), \'\'), CONCAT(\', \', in_drop_value), \'\')));  END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Takes a list, and a value to attempt to remove from the list, and returns the resulting list.\n \n Useful for altering certain session variables, like sql_mode or optimizer_switch for instance.\n \n Parameters\n \n in_list (TEXT):\n The comma separated list to drop a value from\n \n in_drop_value (TEXT):\n The value to drop from the input list\n \n Returns\n \n TEXT\n \n Example\n \n mysql> select @@sql_mode;\n +-----------------------------------------------------------------------------------------------+\n | @@sql_mode                                                                                    |\n +-----------------------------------------------------------------------------------------------+\n | ANSI_QUOTES,ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION |\n +-----------------------------------------------------------------------------------------------+\n 1 row in set (0.00 sec)\n \n mysql> set sql_mode = sys.list_drop(@@sql_mode, \'ONLY_FULL_GROUP_BY\');\n Query OK, 0 rows affected (0.03 sec)\n \n mysql> select @@sql_mode;\n +----------------------------------------------------------------------------+\n | @@sql_mode                                                                 |\n +----------------------------------------------------------------------------+\n | ANSI_QUOTES,STRICT_TRANS_TABLES,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION |\n +----------------------------------------------------------------------------+\n 1 row in set (0.00 sec)\n \n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN  IF (in_drop_value IS NULL) THEN SIGNAL SQLSTATE \'02200\' SET MESSAGE_TEXT = \'Function sys.list_drop: in_drop_value input variable should not be NULL\', MYSQL_ERRNO = 1138; END IF;  IF (in_list IS NULL OR LENGTH(in_list) = 0) THEN RETURN in_list; END IF;  RETURN (SELECT TRIM(BOTH \',\' FROM REPLACE(REPLACE(CONCAT(\',\', in_list), CONCAT(\',\', in_drop_value), \'\'), CONCAT(\', \', in_drop_value), \'\')));  END'),('sys','ps_is_account_enabled','FUNCTION','ps_is_account_enabled','SQL','READS_SQL_DATA','YES','INVOKER',_binary ' in_host VARCHAR(60),  in_user VARCHAR(32) ',_binary 'enum(\'YES\',\'NO\') CHARSET utf8mb3',_binary 'BEGIN RETURN IF(EXISTS(SELECT 1 FROM performance_schema.setup_actors WHERE (`HOST` = \'%\' OR in_host LIKE `HOST`) AND (`USER` = \'%\' OR `USER` = in_user) AND (`ENABLED` = \'YES\') ), \'YES\', \'NO\' ); END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Determines whether instrumentation of an account is enabled \n within Performance Schema.\n \n Parameters\n \n in_host VARCHAR(60): \n The hostname of the account to check.\n in_user VARCHAR(32):\n The username of the account to check.\n \n Returns\n \n ENUM(\'YES\', \'NO\', \'PARTIAL\')\n \n Example\n \n mysql> SELECT sys.ps_is_account_enabled(\'localhost\', \'root\');\n +------------------------------------------------+\n | sys.ps_is_account_enabled(\'localhost\', \'root\') |\n +------------------------------------------------+\n | YES                                            |\n +------------------------------------------------+\n 1 row in set (0.01 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN RETURN IF(EXISTS(SELECT 1 FROM performance_schema.setup_actors WHERE (`HOST` = \'%\' OR in_host LIKE `HOST`) AND (`USER` = \'%\' OR `USER` = in_user) AND (`ENABLED` = \'YES\') ), \'YES\', \'NO\' ); END'),('sys','ps_is_consumer_enabled','FUNCTION','ps_is_consumer_enabled','SQL','READS_SQL_DATA','YES','INVOKER',_binary ' in_consumer varchar(64) ',_binary 'enum(\'YES\',\'NO\') CHARSET utf8mb3',_binary 'BEGIN DECLARE v_is_enabled ENUM(\'YES\', \'NO\') DEFAULT NULL; DECLARE v_error_msg VARCHAR(128);  IF (in_consumer IS NULL) THEN RETURN NULL; END IF;  SET v_is_enabled = ( SELECT (CASE WHEN c.NAME = \'global_instrumentation\' THEN c.ENABLED WHEN c.NAME = \'thread_instrumentation\' THEN IF(cg.ENABLED = \'YES\' AND c.ENABLED = \'YES\', \'YES\', \'NO\') WHEN c.NAME LIKE \'%\\_digest\'           THEN IF(cg.ENABLED = \'YES\' AND c.ENABLED = \'YES\', \'YES\', \'NO\') WHEN c.NAME LIKE \'%\\_current\'          THEN IF(cg.ENABLED = \'YES\' AND ct.ENABLED = \'YES\' AND c.ENABLED = \'YES\', \'YES\', \'NO\') ELSE IF(cg.ENABLED = \'YES\' AND ct.ENABLED = \'YES\' AND c.ENABLED = \'YES\' AND ( SELECT cc.ENABLED FROM performance_schema.setup_consumers cc WHERE NAME = CONCAT(SUBSTRING_INDEX(c.NAME, \'_\', 2), \'_current\') ) = \'YES\', \'YES\', \'NO\') END) AS IsEnabled FROM performance_schema.setup_consumers c INNER JOIN performance_schema.setup_consumers cg INNER JOIN performance_schema.setup_consumers ct WHERE cg.NAME       = \'global_instrumentation\' AND ct.NAME   = \'thread_instrumentation\' AND c.NAME    = in_consumer );  IF (v_is_enabled IS NOT NULL) THEN RETURN v_is_enabled; ELSE SET v_error_msg = CONCAT(\'Invalid argument error: \', in_consumer, \' in function sys.ps_is_consumer_enabled.\'); SIGNAL SQLSTATE \'HY000\' SET MESSAGE_TEXT = v_error_msg, MYSQL_ERRNO  = 3047; END IF; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Determines whether a consumer is enabled (taking the consumer hierarchy into consideration)\n within the Performance Schema.\n \n An exception with errno 3047 is thrown if an unknown consumer name is passed to the function.\n A consumer name of NULL returns NULL.\n \n Parameters\n \n in_consumer VARCHAR(64): \n The name of the consumer to check.\n \n Returns\n \n ENUM(\'YES\', \'NO\')\n \n Example\n \n mysql> SELECT sys.ps_is_consumer_enabled(\'events_stages_history\');\n +-----------------------------------------------------+\n | sys.ps_is_consumer_enabled(\'events_stages_history\') |\n +-----------------------------------------------------+\n | NO                                                  |\n +-----------------------------------------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_is_enabled ENUM(\'YES\', \'NO\') DEFAULT NULL; DECLARE v_error_msg VARCHAR(128);  IF (in_consumer IS NULL) THEN RETURN NULL; END IF;  SET v_is_enabled = ( SELECT (CASE WHEN c.NAME = \'global_instrumentation\' THEN c.ENABLED WHEN c.NAME = \'thread_instrumentation\' THEN IF(cg.ENABLED = \'YES\' AND c.ENABLED = \'YES\', \'YES\', \'NO\') WHEN c.NAME LIKE \'%\\_digest\'           THEN IF(cg.ENABLED = \'YES\' AND c.ENABLED = \'YES\', \'YES\', \'NO\') WHEN c.NAME LIKE \'%\\_current\'          THEN IF(cg.ENABLED = \'YES\' AND ct.ENABLED = \'YES\' AND c.ENABLED = \'YES\', \'YES\', \'NO\') ELSE IF(cg.ENABLED = \'YES\' AND ct.ENABLED = \'YES\' AND c.ENABLED = \'YES\' AND ( SELECT cc.ENABLED FROM performance_schema.setup_consumers cc WHERE NAME = CONCAT(SUBSTRING_INDEX(c.NAME, \'_\', 2), \'_current\') ) = \'YES\', \'YES\', \'NO\') END) AS IsEnabled FROM performance_schema.setup_consumers c INNER JOIN performance_schema.setup_consumers cg INNER JOIN performance_schema.setup_consumers ct WHERE cg.NAME       = \'global_instrumentation\' AND ct.NAME   = \'thread_instrumentation\' AND c.NAME    = in_consumer );  IF (v_is_enabled IS NOT NULL) THEN RETURN v_is_enabled; ELSE SET v_error_msg = CONCAT(\'Invalid argument error: \', in_consumer, \' in function sys.ps_is_consumer_enabled.\'); SIGNAL SQLSTATE \'HY000\' SET MESSAGE_TEXT = v_error_msg, MYSQL_ERRNO  = 3047; END IF; END'),('sys','ps_is_instrument_default_enabled','FUNCTION','ps_is_instrument_default_enabled','SQL','READS_SQL_DATA','YES','INVOKER',_binary ' in_instrument VARCHAR(128) ',_binary 'enum(\'YES\',\'NO\') CHARSET utf8mb3',_binary 'BEGIN DECLARE v_enabled ENUM(\'YES\', \'NO\');  SET v_enabled = IF(in_instrument LIKE \'wait/io/file/%\' OR in_instrument LIKE \'wait/io/table/%\' OR in_instrument LIKE \'statement/%\' OR in_instrument LIKE \'memory/performance_schema/%\' OR in_instrument IN (\'wait/lock/table/sql/handler\', \'idle\')  OR in_instrument LIKE \'stage/innodb/%\' OR in_instrument = \'stage/sql/copy to tmp table\'  , \'YES\', \'NO\' );  RETURN v_enabled; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Returns whether an instrument is enabled by default in this version of MySQL.\n \n Parameters\n \n in_instrument VARCHAR(128): \n The instrument to check.\n \n Returns\n \n ENUM(\'YES\', \'NO\')\n \n Example\n \n mysql> SELECT sys.ps_is_instrument_default_enabled(\'statement/sql/select\');\n +--------------------------------------------------------------+\n | sys.ps_is_instrument_default_enabled(\'statement/sql/select\') |\n +--------------------------------------------------------------+\n | YES                                                          |\n +--------------------------------------------------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_enabled ENUM(\'YES\', \'NO\');  SET v_enabled = IF(in_instrument LIKE \'wait/io/file/%\' OR in_instrument LIKE \'wait/io/table/%\' OR in_instrument LIKE \'statement/%\' OR in_instrument LIKE \'memory/performance_schema/%\' OR in_instrument IN (\'wait/lock/table/sql/handler\', \'idle\')  OR in_instrument LIKE \'stage/innodb/%\' OR in_instrument = \'stage/sql/copy to tmp table\'  , \'YES\', \'NO\' );  RETURN v_enabled; END'),('sys','ps_is_instrument_default_timed','FUNCTION','ps_is_instrument_default_timed','SQL','READS_SQL_DATA','YES','INVOKER',_binary ' in_instrument VARCHAR(128) ',_binary 'enum(\'YES\',\'NO\') CHARSET utf8mb3',_binary 'BEGIN DECLARE v_timed ENUM(\'YES\', \'NO\');  SET v_timed = IF(in_instrument LIKE \'wait/io/file/%\' OR in_instrument LIKE \'wait/io/table/%\' OR in_instrument LIKE \'statement/%\' OR in_instrument IN (\'wait/lock/table/sql/handler\', \'idle\')  OR in_instrument LIKE \'stage/innodb/%\' OR in_instrument = \'stage/sql/copy to tmp table\'  , \'YES\', \'NO\' );  RETURN v_timed; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Returns whether an instrument is timed by default in this version of MySQL.\n \n Parameters\n \n in_instrument VARCHAR(128): \n The instrument to check.\n \n Returns\n \n ENUM(\'YES\', \'NO\')\n \n Example\n \n mysql> SELECT sys.ps_is_instrument_default_timed(\'statement/sql/select\');\n +------------------------------------------------------------+\n | sys.ps_is_instrument_default_timed(\'statement/sql/select\') |\n +------------------------------------------------------------+\n | YES                                                        |\n +------------------------------------------------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_timed ENUM(\'YES\', \'NO\');  SET v_timed = IF(in_instrument LIKE \'wait/io/file/%\' OR in_instrument LIKE \'wait/io/table/%\' OR in_instrument LIKE \'statement/%\' OR in_instrument IN (\'wait/lock/table/sql/handler\', \'idle\')  OR in_instrument LIKE \'stage/innodb/%\' OR in_instrument = \'stage/sql/copy to tmp table\'  , \'YES\', \'NO\' );  RETURN v_timed; END'),('sys','ps_is_thread_instrumented','FUNCTION','ps_is_thread_instrumented','SQL','READS_SQL_DATA','NO','INVOKER',_binary ' in_connection_id BIGINT UNSIGNED ',_binary 'enum(\'YES\',\'NO\',\'UNKNOWN\') CHARSET utf8mb3',_binary 'BEGIN DECLARE v_enabled ENUM(\'YES\', \'NO\', \'UNKNOWN\');  IF (in_connection_id IS NULL) THEN RETURN NULL; END IF;  SELECT INSTRUMENTED INTO v_enabled FROM performance_schema.threads  WHERE PROCESSLIST_ID = in_connection_id;  IF (v_enabled IS NULL) THEN RETURN \'UNKNOWN\'; ELSE RETURN v_enabled; END IF; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Checks whether the provided connection id is instrumented within Performance Schema.\n \n Parameters\n \n in_connection_id (BIGINT UNSIGNED):\n The id of the connection to check.\n \n Returns\n \n ENUM(\'YES\', \'NO\', \'UNKNOWN\')\n \n Example\n \n mysql> SELECT sys.ps_is_thread_instrumented(CONNECTION_ID());\n +------------------------------------------------+\n | sys.ps_is_thread_instrumented(CONNECTION_ID()) |\n +------------------------------------------------+\n | YES                                            |\n +------------------------------------------------+\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_enabled ENUM(\'YES\', \'NO\', \'UNKNOWN\');  IF (in_connection_id IS NULL) THEN RETURN NULL; END IF;  SELECT INSTRUMENTED INTO v_enabled FROM performance_schema.threads  WHERE PROCESSLIST_ID = in_connection_id;  IF (v_enabled IS NULL) THEN RETURN \'UNKNOWN\'; ELSE RETURN v_enabled; END IF; END'),('sys','ps_thread_id','FUNCTION','ps_thread_id','SQL','READS_SQL_DATA','NO','INVOKER',_binary ' in_connection_id BIGINT UNSIGNED ',_binary 'bigint(20) unsigned',_binary 'BEGIN RETURN (SELECT THREAD_ID FROM `performance_schema`.`threads` WHERE PROCESSLIST_ID = IFNULL(in_connection_id, CONNECTION_ID()) ); END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Return the Performance Schema THREAD_ID for the specified connection ID.\n \n Parameters\n \n in_connection_id (BIGINT UNSIGNED):\n The id of the connection to return the thread id for. If NULL, the current\n connection thread id is returned.\n \n Example\n \n mysql> SELECT sys.ps_thread_id(79);\n +----------------------+\n | sys.ps_thread_id(79) |\n +----------------------+\n |                   98 |\n +----------------------+\n 1 row in set (0.00 sec)\n \n mysql> SELECT sys.ps_thread_id(CONNECTION_ID());\n +-----------------------------------+\n | sys.ps_thread_id(CONNECTION_ID()) |\n +-----------------------------------+\n |                                98 |\n +-----------------------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN RETURN (SELECT THREAD_ID FROM `performance_schema`.`threads` WHERE PROCESSLIST_ID = IFNULL(in_connection_id, CONNECTION_ID()) ); END'),('sys','ps_thread_account','FUNCTION','ps_thread_account','SQL','READS_SQL_DATA','NO','INVOKER',_binary ' in_thread_id BIGINT UNSIGNED ',_binary 'text CHARSET utf8mb3',_binary 'BEGIN RETURN (SELECT IF( type = \'FOREGROUND\', CONCAT(processlist_user, \'@\', processlist_host), type ) AS account FROM `performance_schema`.`threads` WHERE thread_id = in_thread_id); END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Return the user@host account for the given Performance Schema thread id.\n \n Parameters\n \n in_thread_id (BIGINT UNSIGNED):\n The id of the thread to return the account for.\n \n Example\n \n mysql> select thread_id, processlist_user, processlist_host from performance_schema.threads where type = \'foreground\';\n +-----------+------------------+------------------+\n | thread_id | processlist_user | processlist_host |\n +-----------+------------------+------------------+\n |        23 | NULL             | NULL             |\n |        30 | root             | localhost        |\n |        31 | msandbox         | localhost        |\n |        32 | msandbox         | localhost        |\n +-----------+------------------+------------------+\n 4 rows in set (0.00 sec)\n \n mysql> select sys.ps_thread_account(31);\n +---------------------------+\n | sys.ps_thread_account(31) |\n +---------------------------+\n | msandbox@localhost        |\n +---------------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN RETURN (SELECT IF( type = \'FOREGROUND\', CONCAT(processlist_user, \'@\', processlist_host), type ) AS account FROM `performance_schema`.`threads` WHERE thread_id = in_thread_id); END'),('sys','ps_thread_stack','FUNCTION','ps_thread_stack','SQL','READS_SQL_DATA','NO','INVOKER',_binary ' thd_id BIGINT UNSIGNED, debug BOOLEAN ',_binary 'longtext CHARSET latin1',_binary 'BEGIN  DECLARE json_objects LONGTEXT;   UPDATE performance_schema.threads SET instrumented = \'NO\' WHERE processlist_id = CONNECTION_ID();   SET SESSION group_concat_max_len=@@global.max_allowed_packet;  SELECT GROUP_CONCAT(CONCAT( \'{\' , CONCAT_WS( \', \' , CONCAT(\'\"nesting_event_id\": \"\', IF(nesting_event_id IS NULL, \'0\', nesting_event_id), \'\"\') , CONCAT(\'\"event_id\": \"\', event_id, \'\"\') , CONCAT( \'\"timer_wait\": \', ROUND(timer_wait/1000000, 2))   , CONCAT( \'\"event_info\": \"\' , CASE WHEN event_name NOT LIKE \'wait/io%\' THEN REPLACE(SUBSTRING_INDEX(event_name, \'/\', -2), \'\\\\\', \'\\\\\\\\\') WHEN event_name NOT LIKE \'wait/io/file%\' OR event_name NOT LIKE \'wait/io/socket%\' THEN REPLACE(SUBSTRING_INDEX(event_name, \'/\', -4), \'\\\\\', \'\\\\\\\\\') ELSE event_name END , \'\"\' ) , CONCAT( \'\"wait_info\": \"\', IFNULL(wait_info, \'\'), \'\"\') , CONCAT( \'\"source\": \"\', IF(true AND event_name LIKE \'wait%\', IFNULL(wait_info, \'\'), \'\'), \'\"\') , CASE  WHEN event_name LIKE \'wait/io/file%\'      THEN \'\"event_type\": \"io/file\"\' WHEN event_name LIKE \'wait/io/table%\'     THEN \'\"event_type\": \"io/table\"\' WHEN event_name LIKE \'wait/io/socket%\'    THEN \'\"event_type\": \"io/socket\"\' WHEN event_name LIKE \'wait/synch/mutex%\'  THEN \'\"event_type\": \"synch/mutex\"\' WHEN event_name LIKE \'wait/synch/cond%\'   THEN \'\"event_type\": \"synch/cond\"\' WHEN event_name LIKE \'wait/synch/rwlock%\' THEN \'\"event_type\": \"synch/rwlock\"\' WHEN event_name LIKE \'wait/lock%\'         THEN \'\"event_type\": \"lock\"\' WHEN event_name LIKE \'statement/%\'        THEN \'\"event_type\": \"stmt\"\' WHEN event_name LIKE \'stage/%\'            THEN \'\"event_type\": \"stage\"\' WHEN event_name LIKE \'%idle%\'             THEN \'\"event_type\": \"idle\"\' ELSE \'\'  END                    ) , \'}\' ) ORDER BY event_id ASC SEPARATOR \',\') event INTO json_objects FROM (  (SELECT thread_id, event_id, event_name, timer_wait, timer_start, nesting_event_id,  CONCAT(sql_text, \'\\\\n\', \'errors: \', errors, \'\\\\n\', \'warnings: \', warnings, \'\\\\n\', \'lock time: \', ROUND(lock_time/1000000, 2),\'us\\\\n\', \'rows affected: \', rows_affected, \'\\\\n\', \'rows sent: \', rows_sent, \'\\\\n\', \'rows examined: \', rows_examined, \'\\\\n\', \'tmp tables: \', created_tmp_tables, \'\\\\n\', \'tmp disk tables: \', created_tmp_disk_tables, \'\\\\n\', \'select scan: \', select_scan, \'\\\\n\', \'select full join: \', select_full_join, \'\\\\n\', \'select full range join: \', select_full_range_join, \'\\\\n\', \'select range: \', select_range, \'\\\\n\', \'select range check: \', select_range_check, \'\\\\n\',  \'sort merge passes: \', sort_merge_passes, \'\\\\n\', \'sort rows: \', sort_rows, \'\\\\n\', \'sort range: \', sort_range, \'\\\\n\', \'sort scan: \', sort_scan, \'\\\\n\', \'no index used: \', IF(no_index_used, \'TRUE\', \'FALSE\'), \'\\\\n\', \'no good index used: \', IF(no_good_index_used, \'TRUE\', \'FALSE\'), \'\\\\n\' ) AS wait_info FROM performance_schema.events_statements_history_long WHERE thread_id = thd_id) UNION  (SELECT thread_id, event_id, event_name, timer_wait, timer_start, nesting_event_id, null AS wait_info FROM performance_schema.events_stages_history_long WHERE thread_id = thd_id)  UNION  (SELECT thread_id, event_id,  CONCAT(event_name ,  IF(event_name NOT LIKE \'wait/synch/mutex%\', IFNULL(CONCAT(\' - \', operation), \'\'), \'\'),  IF(number_of_bytes IS NOT NULL, CONCAT(\' \', number_of_bytes, \' bytes\'), \'\'), IF(event_name LIKE \'wait/io/file%\', \'\\\\n\', \'\'), IF(object_schema IS NOT NULL, CONCAT(\'\\\\nObject: \', object_schema, \'.\'), \'\'),  IF(object_name IS NOT NULL,  IF (event_name LIKE \'wait/io/socket%\', CONCAT(IF (object_name LIKE \':0%\', @@socket, object_name)), object_name), \'\'),  IF(index_name IS NOT NULL, CONCAT(\' Index: \', index_name), \'\'), \'\\\\n\' ) AS event_name, timer_wait, timer_start, nesting_event_id, source AS wait_info FROM performance_schema.events_waits_history_long WHERE thread_id = thd_id)) events  ORDER BY event_id;  RETURN CONCAT(\'{\',  CONCAT_WS(\',\',  \'\"rankdir\": \"LR\"\', \'\"nodesep\": \"0.10\"\', CONCAT(\'\"stack_created\": \"\', NOW(), \'\"\'), CONCAT(\'\"mysql_version\": \"\', VERSION(), \'\"\'), CONCAT(\'\"mysql_user\": \"\', CURRENT_USER(), \'\"\'), CONCAT(\'\"events\": [\', IFNULL(json_objects,\'\'), \']\') ), \'}\');  END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Outputs a JSON formatted stack of all statements, stages and events\n within Performance Schema for the specified thread.\n \n Parameters\n \n thd_id (BIGINT UNSIGNED):\n The id of the thread to trace. This should match the thread_id\n column from the performance_schema.threads table.\n in_verbose (BOOLEAN):\n Include file:lineno information in the events.\n \n Example\n \n (line separation added for output)\n \n mysql> SELECT sys.ps_thread_stack(37, FALSE) AS thread_stack\\G\n *************************** 1. row ***************************\n thread_stack: {\"rankdir\": \"LR\",\"nodesep\": \"0.10\",\"stack_created\": \"2014-02-19 13:39:03\",\n \"mysql_version\": \"5.7.3-m13\",\"mysql_user\": \"root@localhost\",\"events\": \n [{\"nesting_event_id\": \"0\", \"event_id\": \"10\", \"timer_wait\": 256.35, \"event_info\": \n \"sql/select\", \"wait_info\": \"select @@version_comment limit 1\\nerrors: 0\\nwarnings: 0\\nlock time:\n ...\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN  DECLARE json_objects LONGTEXT;   UPDATE performance_schema.threads SET instrumented = \'NO\' WHERE processlist_id = CONNECTION_ID();   SET SESSION group_concat_max_len=@@global.max_allowed_packet;  SELECT GROUP_CONCAT(CONCAT( \'{\' , CONCAT_WS( \', \' , CONCAT(\'\"nesting_event_id\": \"\', IF(nesting_event_id IS NULL, \'0\', nesting_event_id), \'\"\') , CONCAT(\'\"event_id\": \"\', event_id, \'\"\') , CONCAT( \'\"timer_wait\": \', ROUND(timer_wait/1000000, 2))   , CONCAT( \'\"event_info\": \"\' , CASE WHEN event_name NOT LIKE \'wait/io%\' THEN REPLACE(SUBSTRING_INDEX(event_name, \'/\', -2), \'\\\', \'\\\\\') WHEN event_name NOT LIKE \'wait/io/file%\' OR event_name NOT LIKE \'wait/io/socket%\' THEN REPLACE(SUBSTRING_INDEX(event_name, \'/\', -4), \'\\\', \'\\\\\') ELSE event_name END , \'\"\' ) , CONCAT( \'\"wait_info\": \"\', IFNULL(wait_info, \'\'), \'\"\') , CONCAT( \'\"source\": \"\', IF(true AND event_name LIKE \'wait%\', IFNULL(wait_info, \'\'), \'\'), \'\"\') , CASE  WHEN event_name LIKE \'wait/io/file%\'      THEN \'\"event_type\": \"io/file\"\' WHEN event_name LIKE \'wait/io/table%\'     THEN \'\"event_type\": \"io/table\"\' WHEN event_name LIKE \'wait/io/socket%\'    THEN \'\"event_type\": \"io/socket\"\' WHEN event_name LIKE \'wait/synch/mutex%\'  THEN \'\"event_type\": \"synch/mutex\"\' WHEN event_name LIKE \'wait/synch/cond%\'   THEN \'\"event_type\": \"synch/cond\"\' WHEN event_name LIKE \'wait/synch/rwlock%\' THEN \'\"event_type\": \"synch/rwlock\"\' WHEN event_name LIKE \'wait/lock%\'         THEN \'\"event_type\": \"lock\"\' WHEN event_name LIKE \'statement/%\'        THEN \'\"event_type\": \"stmt\"\' WHEN event_name LIKE \'stage/%\'            THEN \'\"event_type\": \"stage\"\' WHEN event_name LIKE \'%idle%\'             THEN \'\"event_type\": \"idle\"\' ELSE \'\'  END                    ) , \'}\' ) ORDER BY event_id ASC SEPARATOR \',\') event INTO json_objects FROM (  (SELECT thread_id, event_id, event_name, timer_wait, timer_start, nesting_event_id,  CONCAT(sql_text, \'\\n\', \'errors: \', errors, \'\\n\', \'warnings: \', warnings, \'\\n\', \'lock time: \', ROUND(lock_time/1000000, 2),\'us\\n\', \'rows affected: \', rows_affected, \'\\n\', \'rows sent: \', rows_sent, \'\\n\', \'rows examined: \', rows_examined, \'\\n\', \'tmp tables: \', created_tmp_tables, \'\\n\', \'tmp disk tables: \', created_tmp_disk_tables, \'\\n\', \'select scan: \', select_scan, \'\\n\', \'select full join: \', select_full_join, \'\\n\', \'select full range join: \', select_full_range_join, \'\\n\', \'select range: \', select_range, \'\\n\', \'select range check: \', select_range_check, \'\\n\',  \'sort merge passes: \', sort_merge_passes, \'\\n\', \'sort rows: \', sort_rows, \'\\n\', \'sort range: \', sort_range, \'\\n\', \'sort scan: \', sort_scan, \'\\n\', \'no index used: \', IF(no_index_used, \'TRUE\', \'FALSE\'), \'\\n\', \'no good index used: \', IF(no_good_index_used, \'TRUE\', \'FALSE\'), \'\\n\' ) AS wait_info FROM performance_schema.events_statements_history_long WHERE thread_id = thd_id) UNION  (SELECT thread_id, event_id, event_name, timer_wait, timer_start, nesting_event_id, null AS wait_info FROM performance_schema.events_stages_history_long WHERE thread_id = thd_id)  UNION  (SELECT thread_id, event_id,  CONCAT(event_name ,  IF(event_name NOT LIKE \'wait/synch/mutex%\', IFNULL(CONCAT(\' - \', operation), \'\'), \'\'),  IF(number_of_bytes IS NOT NULL, CONCAT(\' \', number_of_bytes, \' bytes\'), \'\'), IF(event_name LIKE \'wait/io/file%\', \'\\n\', \'\'), IF(object_schema IS NOT NULL, CONCAT(\'\\nObject: \', object_schema, \'.\'), \'\'),  IF(object_name IS NOT NULL,  IF (event_name LIKE \'wait/io/socket%\', CONCAT(IF (object_name LIKE \':0%\', @@socket, object_name)), object_name), \'\'),  IF(index_name IS NOT NULL, CONCAT(\' Index: \', index_name), \'\'), \'\\n\' ) AS event_name, timer_wait, timer_start, nesting_event_id, source AS wait_info FROM performance_schema.events_waits_history_long WHERE thread_id = thd_id)) events  ORDER BY event_id;  RETURN CONCAT(\'{\',  CONCAT_WS(\',\',  \'\"rankdir\": \"LR\"\', \'\"nodesep\": \"0.10\"\', CONCAT(\'\"stack_created\": \"\', NOW(), \'\"\'), CONCAT(\'\"mysql_version\": \"\', VERSION(), \'\"\'), CONCAT(\'\"mysql_user\": \"\', CURRENT_USER(), \'\"\'), CONCAT(\'\"events\": [\', IFNULL(json_objects,\'\'), \']\') ), \'}\');  END'),('sys','ps_thread_trx_info','FUNCTION','ps_thread_trx_info','SQL','READS_SQL_DATA','NO','INVOKER',_binary ' in_thread_id BIGINT UNSIGNED ',_binary 'longtext CHARSET utf8mb3',_binary 'BEGIN DECLARE v_output LONGTEXT DEFAULT \'{}\'; DECLARE v_msg_text TEXT DEFAULT \'\'; DECLARE v_signal_msg TEXT DEFAULT \'\'; DECLARE v_mysql_errno INT; DECLARE v_max_output_len BIGINT; DECLARE EXIT HANDLER FOR SQLWARNING, SQLEXCEPTION BEGIN GET DIAGNOSTICS CONDITION 1 v_msg_text = MESSAGE_TEXT, v_mysql_errno = MYSQL_ERRNO;  IF v_mysql_errno = 1260 THEN SET v_signal_msg = CONCAT(\'{ \"error\": \"Trx info truncated: \', v_msg_text, \'\" }\'); ELSE SET v_signal_msg = CONCAT(\'{ \"error\": \"\', v_msg_text, \'\" }\'); END IF;  RETURN v_signal_msg; END;  IF (@sys.ps_thread_trx_info.max_length IS NULL) THEN SET @sys.ps_thread_trx_info.max_length = sys.sys_get_config(\'ps_thread_trx_info.max_length\', 65535); END IF;  IF (@sys.ps_thread_trx_info.max_length != @@session.group_concat_max_len) THEN SET @old_group_concat_max_len = @@session.group_concat_max_len; SET v_max_output_len = (@sys.ps_thread_trx_info.max_length - 5); SET SESSION group_concat_max_len = v_max_output_len; END IF;  SET v_output = ( SELECT CONCAT(\'[\', IFNULL(GROUP_CONCAT(trx_info ORDER BY event_id), \'\'), \'\\n]\') AS trx_info FROM (SELECT trxi.thread_id,  trxi.event_id, GROUP_CONCAT( IFNULL( CONCAT(\'\\n  {\\n\', \'    \"time\": \"\', IFNULL(sys.format_time(trxi.timer_wait), \'\'), \'\",\\n\', \'    \"state\": \"\', IFNULL(trxi.state, \'\'), \'\",\\n\', \'    \"mode\": \"\', IFNULL(trxi.access_mode, \'\'), \'\",\\n\', \'    \"autocommitted\": \"\', IFNULL(trxi.autocommit, \'\'), \'\",\\n\', \'    \"gtid\": \"\', IFNULL(trxi.gtid, \'\'), \'\",\\n\', \'    \"isolation\": \"\', IFNULL(trxi.isolation_level, \'\'), \'\",\\n\', \'    \"statements_executed\": [\', IFNULL(s.stmts, \'\'), IF(s.stmts IS NULL, \' ]\\n\', \'\\n    ]\\n\'), \'  }\' ),  \'\')  ORDER BY event_id) AS trx_info  FROM ( (SELECT thread_id, event_id, timer_wait, state,access_mode, autocommit, gtid, isolation_level FROM performance_schema.events_transactions_current WHERE thread_id = in_thread_id AND end_event_id IS NULL) UNION (SELECT thread_id, event_id, timer_wait, state,access_mode, autocommit, gtid, isolation_level FROM performance_schema.events_transactions_history WHERE thread_id = in_thread_id) ) AS trxi LEFT JOIN (SELECT thread_id, nesting_event_id, GROUP_CONCAT( IFNULL( CONCAT(\'\\n      {\\n\', \'        \"sql_text\": \"\', IFNULL(sys.format_statement(REPLACE(sql_text, \'\\\\\', \'\\\\\\\\\')), \'\'), \'\",\\n\', \'        \"time\": \"\', IFNULL(sys.format_time(timer_wait), \'\'), \'\",\\n\', \'        \"schema\": \"\', IFNULL(current_schema, \'\'), \'\",\\n\', \'        \"rows_examined\": \', IFNULL(rows_examined, \'\'), \',\\n\', \'        \"rows_affected\": \', IFNULL(rows_affected, \'\'), \',\\n\', \'        \"rows_sent\": \', IFNULL(rows_sent, \'\'), \',\\n\', \'        \"tmp_tables\": \', IFNULL(created_tmp_tables, \'\'), \',\\n\', \'        \"tmp_disk_tables\": \', IFNULL(created_tmp_disk_tables, \'\'), \',\\n\', \'        \"sort_rows\": \', IFNULL(sort_rows, \'\'), \',\\n\', \'        \"sort_merge_passes\": \', IFNULL(sort_merge_passes, \'\'), \'\\n\', \'      }\'), \'\') ORDER BY event_id) AS stmts FROM performance_schema.events_statements_history WHERE sql_text IS NOT NULL AND thread_id = in_thread_id GROUP BY thread_id, nesting_event_id ) AS s  ON trxi.thread_id = s.thread_id  AND trxi.event_id = s.nesting_event_id WHERE trxi.thread_id = in_thread_id GROUP BY trxi.thread_id, trxi.event_id ) trxs GROUP BY thread_id );  IF (@old_group_concat_max_len IS NOT NULL) THEN SET SESSION group_concat_max_len = @old_group_concat_max_len; END IF;  RETURN v_output; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Returns a JSON object with info on the given threads current transaction, \n and the statements it has already executed, derived from the\n performance_schema.events_transactions_current and\n performance_schema.events_statements_history tables (so the consumers \n for these also have to be enabled within Performance Schema to get full\n data in the object).\n \n When the output exceeds the default truncation length (65535), a JSON error\n object is returned, such as:\n \n { \"error\": \"Trx info truncated: Row 6 was cut by GROUP_CONCAT()\" }\n \n Similar error objects are returned for other warnings/and exceptions raised\n when calling the function.\n \n The max length of the output of this function can be controlled with the\n ps_thread_trx_info.max_length variable set via sys_config, or the\n @sys.ps_thread_trx_info.max_length user variable, as appropriate.\n \n Parameters\n \n in_thread_id (BIGINT UNSIGNED):\n The id of the thread to return the transaction info for.\n \n Example\n \n SELECT sys.ps_thread_trx_info(48)\\G\n *************************** 1. row ***************************\n sys.ps_thread_trx_info(48): [\n {\n \"time\": \"790.70 us\",\n \"state\": \"COMMITTED\",\n \"mode\": \"READ WRITE\",\n \"autocommitted\": \"NO\",\n \"gtid\": \"AUTOMATIC\",\n \"isolation\": \"REPEATABLE READ\",\n \"statements_executed\": [\n {\n \"sql_text\": \"INSERT INTO info VALUES (1, \'foo\')\",\n \"time\": \"471.02 us\",\n \"schema\": \"trx\",\n \"rows_examined\": 0,\n \"rows_affected\": 1,\n \"rows_sent\": 0,\n \"tmp_tables\": 0,\n \"tmp_disk_tables\": 0,\n \"sort_rows\": 0,\n \"sort_merge_passes\": 0\n },\n {\n \"sql_text\": \"COMMIT\",\n \"time\": \"254.42 us\",\n \"schema\": \"trx\",\n \"rows_examined\": 0,\n \"rows_affected\": 0,\n \"rows_sent\": 0,\n \"tmp_tables\": 0,\n \"tmp_disk_tables\": 0,\n \"sort_rows\": 0,\n \"sort_merge_passes\": 0\n }\n ]\n },\n {\n \"time\": \"426.20 us\",\n \"state\": \"COMMITTED\",\n \"mode\": \"READ WRITE\",\n \"autocommitted\": \"NO\",\n \"gtid\": \"AUTOMATIC\",\n \"isolation\": \"REPEATABLE READ\",\n \"statements_executed\": [\n {\n \"sql_text\": \"INSERT INTO info VALUES (2, \'bar\')\",\n \"time\": \"107.33 us\",\n \"schema\": \"trx\",\n \"rows_examined\": 0,\n \"rows_affected\": 1,\n \"rows_sent\": 0,\n \"tmp_tables\": 0,\n \"tmp_disk_tables\": 0,\n \"sort_rows\": 0,\n \"sort_merge_passes\": 0\n },\n {\n \"sql_text\": \"COMMIT\",\n \"time\": \"213.23 us\",\n \"schema\": \"trx\",\n \"rows_examined\": 0,\n \"rows_affected\": 0,\n \"rows_sent\": 0,\n \"tmp_tables\": 0,\n \"tmp_disk_tables\": 0,\n \"sort_rows\": 0,\n \"sort_merge_passes\": 0\n }\n ]\n }\n ]\n 1 row in set (0.03 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_output LONGTEXT DEFAULT \'{}\'; DECLARE v_msg_text TEXT DEFAULT \'\'; DECLARE v_signal_msg TEXT DEFAULT \'\'; DECLARE v_mysql_errno INT; DECLARE v_max_output_len BIGINT; DECLARE EXIT HANDLER FOR SQLWARNING, SQLEXCEPTION BEGIN GET DIAGNOSTICS CONDITION 1 v_msg_text = MESSAGE_TEXT, v_mysql_errno = MYSQL_ERRNO;  IF v_mysql_errno = 1260 THEN SET v_signal_msg = CONCAT(\'{ \"error\": \"Trx info truncated: \', v_msg_text, \'\" }\'); ELSE SET v_signal_msg = CONCAT(\'{ \"error\": \"\', v_msg_text, \'\" }\'); END IF;  RETURN v_signal_msg; END;  IF (@sys.ps_thread_trx_info.max_length IS NULL) THEN SET @sys.ps_thread_trx_info.max_length = sys.sys_get_config(\'ps_thread_trx_info.max_length\', 65535); END IF;  IF (@sys.ps_thread_trx_info.max_length != @@session.group_concat_max_len) THEN SET @old_group_concat_max_len = @@session.group_concat_max_len; SET v_max_output_len = (@sys.ps_thread_trx_info.max_length - 5); SET SESSION group_concat_max_len = v_max_output_len; END IF;  SET v_output = ( SELECT CONCAT(\'[\', IFNULL(GROUP_CONCAT(trx_info ORDER BY event_id), \'\'), \'\n]\') AS trx_info FROM (SELECT trxi.thread_id,  trxi.event_id, GROUP_CONCAT( IFNULL( CONCAT(\'\n  {\n\', \'    \"time\": \"\', IFNULL(sys.format_time(trxi.timer_wait), \'\'), \'\",\n\', \'    \"state\": \"\', IFNULL(trxi.state, \'\'), \'\",\n\', \'    \"mode\": \"\', IFNULL(trxi.access_mode, \'\'), \'\",\n\', \'    \"autocommitted\": \"\', IFNULL(trxi.autocommit, \'\'), \'\",\n\', \'    \"gtid\": \"\', IFNULL(trxi.gtid, \'\'), \'\",\n\', \'    \"isolation\": \"\', IFNULL(trxi.isolation_level, \'\'), \'\",\n\', \'    \"statements_executed\": [\', IFNULL(s.stmts, \'\'), IF(s.stmts IS NULL, \' ]\n\', \'\n    ]\n\'), \'  }\' ),  \'\')  ORDER BY event_id) AS trx_info  FROM ( (SELECT thread_id, event_id, timer_wait, state,access_mode, autocommit, gtid, isolation_level FROM performance_schema.events_transactions_current WHERE thread_id = in_thread_id AND end_event_id IS NULL) UNION (SELECT thread_id, event_id, timer_wait, state,access_mode, autocommit, gtid, isolation_level FROM performance_schema.events_transactions_history WHERE thread_id = in_thread_id) ) AS trxi LEFT JOIN (SELECT thread_id, nesting_event_id, GROUP_CONCAT( IFNULL( CONCAT(\'\n      {\n\', \'        \"sql_text\": \"\', IFNULL(sys.format_statement(REPLACE(sql_text, \'\\\', \'\\\\\')), \'\'), \'\",\n\', \'        \"time\": \"\', IFNULL(sys.format_time(timer_wait), \'\'), \'\",\n\', \'        \"schema\": \"\', IFNULL(current_schema, \'\'), \'\",\n\', \'        \"rows_examined\": \', IFNULL(rows_examined, \'\'), \',\n\', \'        \"rows_affected\": \', IFNULL(rows_affected, \'\'), \',\n\', \'        \"rows_sent\": \', IFNULL(rows_sent, \'\'), \',\n\', \'        \"tmp_tables\": \', IFNULL(created_tmp_tables, \'\'), \',\n\', \'        \"tmp_disk_tables\": \', IFNULL(created_tmp_disk_tables, \'\'), \',\n\', \'        \"sort_rows\": \', IFNULL(sort_rows, \'\'), \',\n\', \'        \"sort_merge_passes\": \', IFNULL(sort_merge_passes, \'\'), \'\n\', \'      }\'), \'\') ORDER BY event_id) AS stmts FROM performance_schema.events_statements_history WHERE sql_text IS NOT NULL AND thread_id = in_thread_id GROUP BY thread_id, nesting_event_id ) AS s  ON trxi.thread_id = s.thread_id  AND trxi.event_id = s.nesting_event_id WHERE trxi.thread_id = in_thread_id GROUP BY trxi.thread_id, trxi.event_id ) trxs GROUP BY thread_id );  IF (@old_group_concat_max_len IS NOT NULL) THEN SET SESSION group_concat_max_len = @old_group_concat_max_len; END IF;  RETURN v_output; END'),('sys','quote_identifier','FUNCTION','quote_identifier','SQL','NO_SQL','YES','INVOKER',_binary 'in_identifier TEXT',_binary 'text CHARSET utf8mb3',_binary 'BEGIN RETURN CONCAT(\'`\', REPLACE(in_identifier, \'`\', \'``\'), \'`\'); END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Takes an unquoted identifier (schema name, table name, etc.) and\n returns the identifier quoted with backticks.\n \n Parameters\n \n in_identifier (TEXT):\n The identifier to quote.\n \n Returns\n \n TEXT\n \n Example\n \n mysql> SELECT sys.quote_identifier(\'my_identifier\') AS Identifier;\n +-----------------+\n | Identifier      |\n +-----------------+\n | `my_identifier` |\n +-----------------+\n 1 row in set (0.00 sec)\n \n mysql> SELECT sys.quote_identifier(\'my`idenfier\') AS Identifier;\n +----------------+\n | Identifier     |\n +----------------+\n | `my``idenfier` |\n +----------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN RETURN CONCAT(\'`\', REPLACE(in_identifier, \'`\', \'``\'), \'`\'); END'),('sys','sys_get_config','FUNCTION','sys_get_config','SQL','READS_SQL_DATA','YES','INVOKER',_binary ' in_variable_name VARCHAR(128), in_default_value VARCHAR(128) ',_binary 'varchar(128) CHARSET utf8mb3',_binary 'BEGIN DECLARE v_value VARCHAR(128) DEFAULT NULL;  SET v_value = (SELECT value FROM sys.sys_config WHERE variable = in_variable_name);  IF (v_value IS NULL) THEN SET v_value = in_default_value; END IF;  RETURN v_value; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Returns the value for the requested variable using the following logic:\n \n 1. If the option exists in sys.sys_config return the value from there.\n 2. Else fall back on the provided default value.\n \n Notes for using sys_get_config():\n \n * If the default value argument to sys_get_config() is NULL and case 2. is reached, NULL is returned.\n It is then expected that the caller is able to handle NULL for the given configuration option.\n * The convention is to name the user variables @sys.<name of variable>. It is <name of variable> that\n is stored in the sys_config table and is what is expected as the argument to sys_get_config().\n * If you want to check whether the configuration option has already been set and if not assign with\n the return value of sys_get_config() you can use IFNULL(...) (see example below). However this should\n not be done inside a loop (e.g. for each row in a result set) as for repeated calls where assignment\n is only needed in the first iteration using IFNULL(...) is expected to be significantly slower than\n using an IF (...) THEN ... END IF; block (see example below).\n \n Parameters\n \n in_variable_name (VARCHAR(128)):\n The name of the config option to return the value for.\n \n in_default_value (VARCHAR(128)):\n The default value to return if the variable does not exist in sys.sys_config.\n \n Returns\n \n VARCHAR(128)\n \n Example\n \n mysql> SELECT sys.sys_get_config(\'statement_truncate_len\', 128) AS Value;\n +-------+\n | Value |\n +-------+\n | 64    |\n +-------+\n 1 row in set (0.00 sec)\n \n mysql> SET @sys.statement_truncate_len = IFNULL(@sys.statement_truncate_len, sys.sys_get_config(\'statement_truncate_len\', 64));\n Query OK, 0 rows affected (0.00 sec)\n \n IF (@sys.statement_truncate_len IS NULL) THEN\n SET @sys.statement_truncate_len = sys.sys_get_config(\'statement_truncate_len\', 64);\n END IF;\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_value VARCHAR(128) DEFAULT NULL;  SET v_value = (SELECT value FROM sys.sys_config WHERE variable = in_variable_name);  IF (v_value IS NULL) THEN SET v_value = in_default_value; END IF;  RETURN v_value; END'),('sys','version_major','FUNCTION','version_major','SQL','NO_SQL','NO','INVOKER','',_binary 'tinyint(3) unsigned',_binary 'BEGIN RETURN SUBSTRING_INDEX(SUBSTRING_INDEX(VERSION(), \'-\', 1), \'.\', 1); END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Returns the major version of MySQL Server.\n \n Returns\n \n TINYINT UNSIGNED\n \n Example\n \n mysql> SELECT VERSION(), sys.version_major();\n +--------------------------------------+---------------------+\n | VERSION()                            | sys.version_major() |\n +--------------------------------------+---------------------+\n | 5.7.9-enterprise-commercial-advanced | 5                   |\n +--------------------------------------+---------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN RETURN SUBSTRING_INDEX(SUBSTRING_INDEX(VERSION(), \'-\', 1), \'.\', 1); END'),('sys','version_minor','FUNCTION','version_minor','SQL','NO_SQL','NO','INVOKER','',_binary 'tinyint(3) unsigned',_binary 'BEGIN RETURN SUBSTRING_INDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(VERSION(), \'-\', 1), \'.\', 2), \'.\', -1); END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Returns the minor (release series) version of MySQL Server.\n \n Returns\n \n TINYINT UNSIGNED\n \n Example\n \n mysql> SELECT VERSION(), sys.server_minor();\n +--------------------------------------+---------------------+\n | VERSION()                            | sys.version_minor() |\n +--------------------------------------+---------------------+\n | 5.7.9-enterprise-commercial-advanced | 7                   |\n +--------------------------------------+---------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN RETURN SUBSTRING_INDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(VERSION(), \'-\', 1), \'.\', 2), \'.\', -1); END'),('sys','version_patch','FUNCTION','version_patch','SQL','NO_SQL','NO','INVOKER','',_binary 'tinyint(3) unsigned',_binary 'BEGIN RETURN SUBSTRING_INDEX(SUBSTRING_INDEX(VERSION(), \'-\', 1), \'.\', -1); END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Returns the patch release version of MySQL Server.\n \n Returns\n \n TINYINT UNSIGNED\n \n Example\n \n mysql> SELECT VERSION(), sys.version_patch();\n +--------------------------------------+---------------------+\n | VERSION()                            | sys.version_patch() |\n +--------------------------------------+---------------------+\n | 5.7.9-enterprise-commercial-advanced | 9                   |\n +--------------------------------------+---------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN RETURN SUBSTRING_INDEX(SUBSTRING_INDEX(VERSION(), \'-\', 1), \'.\', -1); END'),('sys','create_synonym_db','PROCEDURE','create_synonym_db','SQL','MODIFIES_SQL_DATA','NO','INVOKER',_binary ' IN in_db_name VARCHAR(64),  IN in_synonym VARCHAR(64) ','',_binary 'BEGIN DECLARE v_done bool DEFAULT FALSE; DECLARE v_db_name_check VARCHAR(64); DECLARE v_db_err_msg TEXT; DECLARE v_table VARCHAR(64); DECLARE v_views_created INT DEFAULT 0;  DECLARE db_doesnt_exist CONDITION FOR SQLSTATE \'42000\'; DECLARE db_name_exists CONDITION FOR SQLSTATE \'HY000\';  DECLARE c_table_names CURSOR FOR  SELECT TABLE_NAME  FROM INFORMATION_SCHEMA.TABLES  WHERE TABLE_SCHEMA = in_db_name;  DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;  SELECT SCHEMA_NAME INTO v_db_name_check FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = in_db_name;  IF v_db_name_check IS NULL THEN SET v_db_err_msg = CONCAT(\'Unknown database \', in_db_name); SIGNAL SQLSTATE \'HY000\' SET MESSAGE_TEXT = v_db_err_msg; END IF;  SELECT SCHEMA_NAME INTO v_db_name_check FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = in_synonym;  IF v_db_name_check = in_synonym THEN SET v_db_err_msg = CONCAT(\'Can\\\'t create database \', in_synonym, \'; database exists\'); SIGNAL SQLSTATE \'HY000\' SET MESSAGE_TEXT = v_db_err_msg; END IF;  SET @create_db_stmt := CONCAT(\'CREATE DATABASE \', sys.quote_identifier(in_synonym)); PREPARE create_db_stmt FROM @create_db_stmt; EXECUTE create_db_stmt; DEALLOCATE PREPARE create_db_stmt;  SET v_done = FALSE; OPEN c_table_names; c_table_names: LOOP FETCH c_table_names INTO v_table; IF v_done THEN LEAVE c_table_names; END IF;  SET @create_view_stmt = CONCAT( \'CREATE SQL SECURITY INVOKER VIEW \', sys.quote_identifier(in_synonym), \'.\', sys.quote_identifier(v_table), \' AS SELECT * FROM \', sys.quote_identifier(in_db_name), \'.\', sys.quote_identifier(v_table) ); PREPARE create_view_stmt FROM @create_view_stmt; EXECUTE create_view_stmt; DEALLOCATE PREPARE create_view_stmt;  SET v_views_created = v_views_created + 1; END LOOP; CLOSE c_table_names;  SELECT CONCAT( \'Created \', v_views_created, \' view\', IF(v_views_created != 1, \'s\', \'\'), \' in the \', sys.quote_identifier(in_synonym), \' database\' ) AS summary;  END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Takes a source database name and synonym name, and then creates the \n synonym database with views that point to all of the tables within\n the source database.\n \n Useful for creating a \"ps\" synonym for \"performance_schema\",\n or \"is\" instead of \"information_schema\", for example.\n \n Parameters\n \n in_db_name (VARCHAR(64)):\n The database name that you would like to create a synonym for.\n in_synonym (VARCHAR(64)):\n The database synonym name.\n \n Example\n \n mysql> SHOW DATABASES;\n +--------------------+\n | Database           |\n +--------------------+\n | information_schema |\n | mysql              |\n | performance_schema |\n | sys                |\n | test               |\n +--------------------+\n 5 rows in set (0.00 sec)\n \n mysql> CALL sys.create_synonym_db(\'performance_schema\', \'ps\');\n +---------------------------------------+\n | summary                               |\n +---------------------------------------+\n | Created 74 views in the `ps` database |\n +---------------------------------------+\n 1 row in set (8.57 sec)\n \n Query OK, 0 rows affected (8.57 sec)\n \n mysql> SHOW DATABASES;\n +--------------------+\n | Database           |\n +--------------------+\n | information_schema |\n | mysql              |\n | performance_schema |\n | ps                 |\n | sys                |\n | test               |\n +--------------------+\n 6 rows in set (0.00 sec)\n \n mysql> SHOW FULL TABLES FROM ps;\n +------------------------------------------------------+------------+\n | Tables_in_ps                                         | Table_type |\n +------------------------------------------------------+------------+\n | accounts                                             | VIEW       |\n | cond_instances                                       | VIEW       |\n | events_stages_current                                | VIEW       |\n | events_stages_history                                | VIEW       |\n ...\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_done bool DEFAULT FALSE; DECLARE v_db_name_check VARCHAR(64); DECLARE v_db_err_msg TEXT; DECLARE v_table VARCHAR(64); DECLARE v_views_created INT DEFAULT 0;  DECLARE db_doesnt_exist CONDITION FOR SQLSTATE \'42000\'; DECLARE db_name_exists CONDITION FOR SQLSTATE \'HY000\';  DECLARE c_table_names CURSOR FOR  SELECT TABLE_NAME  FROM INFORMATION_SCHEMA.TABLES  WHERE TABLE_SCHEMA = in_db_name;  DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;  SELECT SCHEMA_NAME INTO v_db_name_check FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = in_db_name;  IF v_db_name_check IS NULL THEN SET v_db_err_msg = CONCAT(\'Unknown database \', in_db_name); SIGNAL SQLSTATE \'HY000\' SET MESSAGE_TEXT = v_db_err_msg; END IF;  SELECT SCHEMA_NAME INTO v_db_name_check FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = in_synonym;  IF v_db_name_check = in_synonym THEN SET v_db_err_msg = CONCAT(\'Can\'t create database \', in_synonym, \'; database exists\'); SIGNAL SQLSTATE \'HY000\' SET MESSAGE_TEXT = v_db_err_msg; END IF;  SET @create_db_stmt := CONCAT(\'CREATE DATABASE \', sys.quote_identifier(in_synonym)); PREPARE create_db_stmt FROM @create_db_stmt; EXECUTE create_db_stmt; DEALLOCATE PREPARE create_db_stmt;  SET v_done = FALSE; OPEN c_table_names; c_table_names: LOOP FETCH c_table_names INTO v_table; IF v_done THEN LEAVE c_table_names; END IF;  SET @create_view_stmt = CONCAT( \'CREATE SQL SECURITY INVOKER VIEW \', sys.quote_identifier(in_synonym), \'.\', sys.quote_identifier(v_table), \' AS SELECT * FROM \', sys.quote_identifier(in_db_name), \'.\', sys.quote_identifier(v_table) ); PREPARE create_view_stmt FROM @create_view_stmt; EXECUTE create_view_stmt; DEALLOCATE PREPARE create_view_stmt;  SET v_views_created = v_views_created + 1; END LOOP; CLOSE c_table_names;  SELECT CONCAT( \'Created \', v_views_created, \' view\', IF(v_views_created != 1, \'s\', \'\'), \' in the \', sys.quote_identifier(in_synonym), \' database\' ) AS summary;  END'),('sys','execute_prepared_stmt','PROCEDURE','execute_prepared_stmt','SQL','READS_SQL_DATA','NO','INVOKER',_binary ' IN in_query longtext CHARACTER SET utf8mb3 ','',_binary 'BEGIN IF (@sys.debug IS NULL) THEN SET @sys.debug = sys.sys_get_config(\'debug\', \'OFF\'); END IF;  IF (in_query IS NULL OR LENGTH(in_query) < 4) THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \"The @sys.execute_prepared_stmt.sql must contain a query\"; END IF;  SET @sys.execute_prepared_stmt.sql = in_query;  IF (@sys.debug = \'ON\') THEN SELECT @sys.execute_prepared_stmt.sql AS \'Debug\'; END IF; PREPARE sys_execute_prepared_stmt FROM @sys.execute_prepared_stmt.sql; EXECUTE sys_execute_prepared_stmt; DEALLOCATE PREPARE sys_execute_prepared_stmt;  SET @sys.execute_prepared_stmt.sql = NULL; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Takes the query in the argument and executes it using a prepared statement. The prepared statement is deallocated,\n so the procedure is mainly useful for executing one off dynamically created queries.\n \n The sys_execute_prepared_stmt prepared statement name is used for the query and is required not to exist.\n \n \n Parameters\n \n in_query (longtext CHARACTER SET utf8mb3):\n The query to execute.\n \n \n Configuration Options\n \n sys.debug\n Whether to provide debugging output.\n Default is \'OFF\'. Set to \'ON\' to include.\n \n \n Example\n \n mysql> CALL sys.execute_prepared_stmt(\'SELECT * FROM sys.sys_config\');\n +------------------------+-------+---------------------+--------+\n | variable               | value | set_time            | set_by |\n +------------------------+-------+---------------------+--------+\n | statement_truncate_len | 64    | 2015-06-30 13:06:00 | NULL   |\n +------------------------+-------+---------------------+--------+\n 1 row in set (0.00 sec)\n \n Query OK, 0 rows affected (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN IF (@sys.debug IS NULL) THEN SET @sys.debug = sys.sys_get_config(\'debug\', \'OFF\'); END IF;  IF (in_query IS NULL OR LENGTH(in_query) < 4) THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \"The @sys.execute_prepared_stmt.sql must contain a query\"; END IF;  SET @sys.execute_prepared_stmt.sql = in_query;  IF (@sys.debug = \'ON\') THEN SELECT @sys.execute_prepared_stmt.sql AS \'Debug\'; END IF; PREPARE sys_execute_prepared_stmt FROM @sys.execute_prepared_stmt.sql; EXECUTE sys_execute_prepared_stmt; DEALLOCATE PREPARE sys_execute_prepared_stmt;  SET @sys.execute_prepared_stmt.sql = NULL; END'),('sys','diagnostics','PROCEDURE','diagnostics','SQL','READS_SQL_DATA','NO','INVOKER',_binary ' IN in_max_runtime int unsigned, IN in_interval int unsigned, IN in_auto_config enum (\'current\', \'medium\', \'full\') ','',_binary 'BEGIN DECLARE v_start, v_runtime, v_iter_start, v_sleep DECIMAL(20,2) DEFAULT 0.0; DECLARE v_has_innodb, v_has_ndb, v_has_ps, v_has_replication, v_has_ps_replication VARCHAR(8) CHARSET utf8mb3 DEFAULT \'NO\'; DECLARE v_this_thread_enabled, v_has_ps_vars, v_has_metrics ENUM(\'YES\', \'NO\'); DECLARE v_table_name, v_banner VARCHAR(64) CHARSET utf8mb3; DECLARE v_sql_status_summary_select, v_sql_status_summary_delta, v_sql_status_summary_from, v_no_delta_names TEXT; DECLARE v_output_time, v_output_time_prev DECIMAL(20,3) UNSIGNED; DECLARE v_output_count, v_count, v_old_group_concat_max_len INT UNSIGNED DEFAULT 0; DECLARE v_status_summary_width TINYINT UNSIGNED DEFAULT 50; DECLARE v_done BOOLEAN DEFAULT FALSE; DECLARE c_ndbinfo CURSOR FOR SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = \'ndbinfo\' AND TABLE_NAME NOT IN ( \'blocks\', \'config_params\', \'dict_obj_types\', \'disk_write_speed_base\', \'memory_per_fragment\', \'memoryusage\', \'operations_per_fragment\', \'threadblocks\' ); DECLARE c_sysviews_w_delta CURSOR FOR SELECT table_name FROM tmp_sys_views_delta ORDER BY table_name; DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;  SELECT INSTRUMENTED INTO v_this_thread_enabled FROM performance_schema.threads WHERE PROCESSLIST_ID = CONNECTION_ID(); IF (v_this_thread_enabled = \'YES\') THEN CALL sys.ps_setup_disable_thread(CONNECTION_ID()); END IF;  IF (in_max_runtime < in_interval) THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'in_max_runtime must be greater than or equal to in_interval\'; END IF; IF (in_max_runtime = 0) THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'in_max_runtime must be greater than 0\'; END IF; IF (in_interval = 0) THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'in_interval must be greater than 0\'; END IF;  IF (@sys.diagnostics.allow_i_s_tables IS NULL) THEN SET @sys.diagnostics.allow_i_s_tables = sys.sys_get_config(\'diagnostics.allow_i_s_tables\', \'OFF\'); END IF; IF (@sys.diagnostics.include_raw IS NULL) THEN SET @sys.diagnostics.include_raw      = sys.sys_get_config(\'diagnostics.include_raw\'     , \'OFF\'); END IF; IF (@sys.debug IS NULL) THEN SET @sys.debug                        = sys.sys_get_config(\'debug\'                       , \'OFF\'); END IF; IF (@sys.statement_truncate_len IS NULL) THEN SET @sys.statement_truncate_len       = sys.sys_get_config(\'statement_truncate_len\'      , \'64\' ); END IF;  SET @log_bin := @@sql_log_bin; IF (@log_bin = 1) THEN SET sql_log_bin = 0; END IF;  SET v_no_delta_names = CONCAT(\'s%{COUNT}.Variable_name NOT IN (\', \'\'\'innodb_buffer_pool_pages_total\'\', \', \'\'\'innodb_page_size\'\', \', \'\'\'last_query_cost\'\', \', \'\'\'last_query_partial_plans\'\', \', \'\'\'qcache_total_blocks\'\', \', \'\'\'slave_last_heartbeat\'\', \', \'\'\'ssl_ctx_verify_depth\'\', \', \'\'\'ssl_ctx_verify_mode\'\', \', \'\'\'ssl_session_cache_size\'\', \', \'\'\'ssl_verify_depth\'\', \', \'\'\'ssl_verify_mode\'\', \', \'\'\'ssl_version\'\', \', \'\'\'buffer_flush_lsn_avg_rate\'\', \', \'\'\'buffer_flush_pct_for_dirty\'\', \', \'\'\'buffer_flush_pct_for_lsn\'\', \', \'\'\'buffer_pool_pages_total\'\', \', \'\'\'lock_row_lock_time_avg\'\', \', \'\'\'lock_row_lock_time_max\'\', \', \'\'\'innodb_page_size\'\'\', \')\');  IF (in_auto_config <> \'current\') THEN IF (@sys.debug = \'ON\') THEN SELECT CONCAT(\'Updating Performance Schema configuration to \', in_auto_config) AS \'Debug\'; END IF; CALL sys.ps_setup_save(0);  IF (in_auto_config = \'medium\') THEN UPDATE performance_schema.setup_consumers SET ENABLED = \'YES\' WHERE NAME NOT LIKE \'%\\_history%\';  UPDATE performance_schema.setup_instruments SET ENABLED = \'YES\', TIMED   = \'YES\' WHERE NAME NOT LIKE \'wait/synch/%\'; ELSEIF (in_auto_config = \'full\') THEN UPDATE performance_schema.setup_consumers SET ENABLED = \'YES\';  UPDATE performance_schema.setup_instruments SET ENABLED = \'YES\', TIMED   = \'YES\'; END IF;  UPDATE performance_schema.threads SET INSTRUMENTED = \'YES\' WHERE PROCESSLIST_ID <> CONNECTION_ID(); END IF;  SET v_start        = UNIX_TIMESTAMP(NOW(2)), in_interval    = IFNULL(in_interval, 30), in_max_runtime = IFNULL(in_max_runtime, 60);  SET v_banner = REPEAT( \'-\', LEAST( GREATEST( 36, CHAR_LENGTH(VERSION()), CHAR_LENGTH(@@global.version_comment), CHAR_LENGTH(@@global.version_compile_os), CHAR_LENGTH(@@global.version_compile_machine), CHAR_LENGTH(@@global.socket), CHAR_LENGTH(@@global.datadir) ), 64 ) ); SELECT \'Hostname\' AS \'Name\', @@global.hostname AS \'Value\' UNION ALL SELECT \'Port\' AS \'Name\', @@global.port AS \'Value\' UNION ALL SELECT \'Socket\' AS \'Name\', @@global.socket AS \'Value\' UNION ALL SELECT \'Datadir\' AS \'Name\', @@global.datadir AS \'Value\' UNION ALL SELECT \'Server UUID\' AS \'Name\', @@global.server_uuid AS \'Value\' UNION ALL SELECT REPEAT(\'-\', 23) AS \'Name\', v_banner AS \'Value\' UNION ALL SELECT \'MySQL Version\' AS \'Name\', VERSION() AS \'Value\' UNION ALL SELECT \'Sys Schema Version\' AS \'Name\', (SELECT sys_version FROM sys.version) AS \'Value\' UNION ALL SELECT \'Version Comment\' AS \'Name\', @@global.version_comment AS \'Value\' UNION ALL SELECT \'Version Compile OS\' AS \'Name\', @@global.version_compile_os AS \'Value\' UNION ALL SELECT \'Version Compile Machine\' AS \'Name\', @@global.version_compile_machine AS \'Value\' UNION ALL SELECT REPEAT(\'-\', 23) AS \'Name\', v_banner AS \'Value\' UNION ALL SELECT \'UTC Time\' AS \'Name\', UTC_TIMESTAMP() AS \'Value\' UNION ALL SELECT \'Local Time\' AS \'Name\', NOW() AS \'Value\' UNION ALL SELECT \'Time Zone\' AS \'Name\', @@global.time_zone AS \'Value\' UNION ALL SELECT \'System Time Zone\' AS \'Name\', @@global.system_time_zone AS \'Value\' UNION ALL SELECT \'Time Zone Offset\' AS \'Name\', TIMEDIFF(NOW(), UTC_TIMESTAMP()) AS \'Value\';  SET v_has_innodb         = IFNULL((SELECT SUPPORT FROM information_schema.ENGINES WHERE ENGINE = \'InnoDB\'), \'NO\'), v_has_ndb            = IFNULL((SELECT SUPPORT FROM information_schema.ENGINES WHERE ENGINE = \'NDBCluster\'), \'NO\'), v_has_ps             = IFNULL((SELECT SUPPORT FROM information_schema.ENGINES WHERE ENGINE = \'PERFORMANCE_SCHEMA\'), \'NO\'), v_has_ps_replication = IF(v_has_ps = \'YES\' AND EXISTS(SELECT 1 FROM information_schema.TABLES WHERE TABLE_SCHEMA = \'performance_schema\' AND TABLE_NAME = \'replication_applier_status\'), \'YES\', \'NO\' ), v_has_replication    =  IF(v_has_ps_replication = \'YES\', IF((SELECT COUNT(*) FROM performance_schema.replication_connection_status) > 0, \'YES\', \'NO\'), IF(@@master_info_repository = \'TABLE\', IF((SELECT COUNT(*) FROM mysql.slave_master_info) > 0, \'YES\', \'NO\'), IF(@@relay_log_info_repository = \'TABLE\', IF((SELECT COUNT(*) FROM mysql.slave_relay_log_info) > 0, \'YES\', \'NO\'), \'MAYBE\')) ) , v_has_metrics        = IF(v_has_ps = \'YES\' OR (sys.version_major() = 5 AND sys.version_minor() = 6), \'YES\', \'NO\'), v_has_ps_vars        = \'NO\';   SET v_has_ps_vars = IF(@@global.show_compatibility_56, \'NO\', \'YES\');  SET v_has_ps_vars = \'YES\';  IF (@sys.debug = \'ON\') THEN SELECT v_has_innodb AS \'Has_InnoDB\', v_has_ndb AS \'Has_NDBCluster\', v_has_ps AS \'Has_Performance_Schema\', v_has_ps_vars AS \'Has_P_S_SHOW_Variables\', v_has_metrics AS \'Has_metrics\', v_has_ps_replication \'AS Has_P_S_Replication\', v_has_replication AS \'Has_Replication\'; END IF;  IF (v_has_innodb IN (\'DEFAULT\', \'YES\')) THEN SET @sys.diagnostics.sql = \'SHOW ENGINE InnoDB STATUS\'; PREPARE stmt_innodb_status FROM @sys.diagnostics.sql; END IF;  IF (v_has_ps = \'YES\') THEN SET @sys.diagnostics.sql = \'SHOW ENGINE PERFORMANCE_SCHEMA STATUS\'; PREPARE stmt_ps_status FROM @sys.diagnostics.sql; END IF;  IF (v_has_ndb IN (\'DEFAULT\', \'YES\')) THEN SET @sys.diagnostics.sql = \'SHOW ENGINE NDBCLUSTER STATUS\'; PREPARE stmt_ndbcluster_status FROM @sys.diagnostics.sql; END IF;  SET @sys.diagnostics.sql_gen_query_template = \'SELECT CONCAT( \'\'SELECT \'\', GROUP_CONCAT( CASE WHEN (SUBSTRING(TABLE_NAME, 3), COLUMN_NAME) IN ( (\'\'io_global_by_file_by_bytes\'\', \'\'total\'\'), (\'\'io_global_by_wait_by_bytes\'\', \'\'total_requested\'\') ) THEN CONCAT(\'\'sys.format_bytes(\'\', COLUMN_NAME, \'\') AS \'\', COLUMN_NAME) WHEN COLUMN_NAME LIKE \'\'%latency\'\' THEN CONCAT(\'\'sys.format_time(\'\', COLUMN_NAME, \'\') AS \'\', COLUMN_NAME) WHEN SUBSTRING(COLUMN_NAME, -7) = \'\'_memory\'\' OR SUBSTRING(COLUMN_NAME, -17) = \'\'_memory_allocated\'\' OR ((SUBSTRING(COLUMN_NAME, -5) = \'\'_read\'\' OR SUBSTRING(COLUMN_NAME, -8) = \'\'_written\'\' OR SUBSTRING(COLUMN_NAME, -6) = \'\'_write\'\') AND SUBSTRING(COLUMN_NAME, 1, 6) <> \'\'COUNT_\'\') THEN CONCAT(\'\'sys.format_bytes(\'\', COLUMN_NAME, \'\') AS \'\', COLUMN_NAME) ELSE COLUMN_NAME END ORDER BY ORDINAL_POSITION SEPARATOR \'\',\\n       \'\' ), \'\'\\n  FROM tmp_\'\', SUBSTRING(TABLE_NAME FROM 3), \'\'_%{OUTPUT}\'\' ) AS Query INTO @sys.diagnostics.sql_select FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = \'\'sys\'\' AND TABLE_NAME = ? GROUP BY TABLE_NAME\';  SET @sys.diagnostics.sql_gen_query_delta = \'SELECT CONCAT( \'\'SELECT \'\', GROUP_CONCAT( CASE WHEN FIND_IN_SET(COLUMN_NAME, diag.pk) THEN COLUMN_NAME WHEN diag.TABLE_NAME = \'\'io_global_by_file_by_bytes\'\' AND COLUMN_NAME = \'\'write_pct\'\' THEN CONCAT(\'\'IFNULL(ROUND(100-(((e.total_read-IFNULL(s.total_read, 0))\'\', \'\'/NULLIF(((e.total_read-IFNULL(s.total_read, 0))+(e.total_written-IFNULL(s.total_written, 0))), 0))*100), 2), 0.00) AS \'\', COLUMN_NAME) WHEN (diag.TABLE_NAME, COLUMN_NAME) IN ( (\'\'io_global_by_file_by_bytes\'\', \'\'total\'\'), (\'\'io_global_by_wait_by_bytes\'\', \'\'total_requested\'\') ) THEN CONCAT(\'\'sys.format_bytes(e.\'\', COLUMN_NAME, \'\'-IFNULL(s.\'\', COLUMN_NAME, \'\', 0)) AS \'\', COLUMN_NAME) WHEN SUBSTRING(COLUMN_NAME, 1, 4) IN (\'\'max_\'\', \'\'min_\'\') AND SUBSTRING(COLUMN_NAME, -8) = \'\'_latency\'\' THEN CONCAT(\'\'sys.format_time(e.\'\', COLUMN_NAME, \'\') AS \'\', COLUMN_NAME) WHEN COLUMN_NAME = \'\'avg_latency\'\' THEN CONCAT(\'\'sys.format_time((e.total_latency - IFNULL(s.total_latency, 0))\'\', \'\'/NULLIF(e.total - IFNULL(s.total, 0), 0)) AS \'\', COLUMN_NAME) WHEN SUBSTRING(COLUMN_NAME, -12) = \'\'_avg_latency\'\' THEN CONCAT(\'\'sys.format_time((e.\'\', SUBSTRING(COLUMN_NAME FROM 1 FOR CHAR_LENGTH(COLUMN_NAME)-12), \'\'_latency - IFNULL(s.\'\', SUBSTRING(COLUMN_NAME FROM 1 FOR CHAR_LENGTH(COLUMN_NAME)-12), \'\'_latency, 0))\'\', \'\'/NULLIF(e.\'\', SUBSTRING(COLUMN_NAME FROM 1 FOR CHAR_LENGTH(COLUMN_NAME)-12), \'\'s - IFNULL(s.\'\', SUBSTRING(COLUMN_NAME FROM 1 FOR CHAR_LENGTH(COLUMN_NAME)-12), \'\'s, 0), 0)) AS \'\', COLUMN_NAME) WHEN COLUMN_NAME LIKE \'\'%latency\'\' THEN CONCAT(\'\'sys.format_time(e.\'\', COLUMN_NAME, \'\' - IFNULL(s.\'\', COLUMN_NAME, \'\', 0)) AS \'\', COLUMN_NAME) WHEN COLUMN_NAME IN (\'\'avg_read\'\', \'\'avg_write\'\', \'\'avg_written\'\') THEN CONCAT(\'\'sys.format_bytes(IFNULL((e.total_\'\', IF(COLUMN_NAME = \'\'avg_read\'\', \'\'read\'\', \'\'written\'\'), \'\'-IFNULL(s.total_\'\', IF(COLUMN_NAME = \'\'avg_read\'\', \'\'read\'\', \'\'written\'\'), \'\', 0))\'\', \'\'/NULLIF(e.count_\'\', IF(COLUMN_NAME = \'\'avg_read\'\', \'\'read\'\', \'\'write\'\'), \'\'-IFNULL(s.count_\'\', IF(COLUMN_NAME = \'\'avg_read\'\', \'\'read\'\', \'\'write\'\'), \'\', 0), 0), 0)) AS \'\', COLUMN_NAME) WHEN SUBSTRING(COLUMN_NAME, -7) = \'\'_memory\'\' OR SUBSTRING(COLUMN_NAME, -17) = \'\'_memory_allocated\'\' OR ((SUBSTRING(COLUMN_NAME, -5) = \'\'_read\'\' OR SUBSTRING(COLUMN_NAME, -8) = \'\'_written\'\' OR SUBSTRING(COLUMN_NAME, -6) = \'\'_write\'\') AND SUBSTRING(COLUMN_NAME, 1, 6) <> \'\'COUNT_\'\') THEN CONCAT(\'\'sys.format_bytes(e.\'\', COLUMN_NAME, \'\' - IFNULL(s.\'\', COLUMN_NAME, \'\', 0)) AS \'\', COLUMN_NAME) ELSE CONCAT(\'\'(e.\'\', COLUMN_NAME, \'\' - IFNULL(s.\'\', COLUMN_NAME, \'\', 0)) AS \'\', COLUMN_NAME) END ORDER BY ORDINAL_POSITION SEPARATOR \'\',\\n       \'\' ), \'\'\\n  FROM tmp_\'\', diag.TABLE_NAME, \'\'_end e LEFT OUTER JOIN tmp_\'\', diag.TABLE_NAME, \'\'_start s USING (\'\', diag.pk, \'\')\'\' ) AS Query INTO @sys.diagnostics.sql_select FROM tmp_sys_views_delta diag INNER JOIN information_schema.COLUMNS c ON c.TABLE_NAME = CONCAT(\'\'x$\'\', diag.TABLE_NAME) WHERE c.TABLE_SCHEMA = \'\'sys\'\' AND diag.TABLE_NAME = ? GROUP BY diag.TABLE_NAME\';  IF (v_has_ps = \'YES\') THEN DROP TEMPORARY TABLE IF EXISTS tmp_sys_views_delta; CREATE TEMPORARY TABLE tmp_sys_views_delta ( TABLE_NAME varchar(64) NOT NULL, order_by text COMMENT \'ORDER BY clause for the initial and overall views\', order_by_delta text COMMENT \'ORDER BY clause for the delta views\', where_delta text COMMENT \'WHERE clause to use for delta views to only include rows with a \"count\" > 0\', limit_rows int unsigned COMMENT \'The maximum number of rows to include for the view\', pk varchar(128) COMMENT \'Used with the FIND_IN_SET() function so use comma separated list without whitespace\', PRIMARY KEY (TABLE_NAME) );  IF (@sys.debug = \'ON\') THEN SELECT \'Populating tmp_sys_views_delta\' AS \'Debug\'; END IF; INSERT INTO tmp_sys_views_delta VALUES (\'host_summary\'                       , \'%{TABLE}.statement_latency DESC\', \'(e.statement_latency-IFNULL(s.statement_latency, 0)) DESC\', \'(e.statements - IFNULL(s.statements, 0)) > 0\', NULL, \'host\'), (\'host_summary_by_file_io\'            , \'%{TABLE}.io_latency DESC\', \'(e.io_latency-IFNULL(s.io_latency, 0)) DESC\', \'(e.ios - IFNULL(s.ios, 0)) > 0\', NULL, \'host\'), (\'host_summary_by_file_io_type\'       , \'%{TABLE}.host, %{TABLE}.total_latency DESC\', \'e.host, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'host,event_name\'), (\'host_summary_by_stages\'             , \'%{TABLE}.host, %{TABLE}.total_latency DESC\', \'e.host, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'host,event_name\'), (\'host_summary_by_statement_latency\'  , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'host\'), (\'host_summary_by_statement_type\'     , \'%{TABLE}.host, %{TABLE}.total_latency DESC\', \'e.host, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'host,statement\'), (\'io_by_thread_by_latency\'            , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'user,thread_id,processlist_id\'), (\'io_global_by_file_by_bytes\'         , \'%{TABLE}.total DESC\', \'(e.total-IFNULL(s.total, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', 100, \'file\'), (\'io_global_by_file_by_latency\'       , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', 100, \'file\'), (\'io_global_by_wait_by_bytes\'         , \'%{TABLE}.total_requested DESC\', \'(e.total_requested-IFNULL(s.total_requested, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'event_name\'), (\'io_global_by_wait_by_latency\'       , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'event_name\'), (\'schema_index_statistics\'            , \'(%{TABLE}.select_latency+%{TABLE}.insert_latency+%{TABLE}.update_latency+%{TABLE}.delete_latency) DESC\', \'((e.select_latency+e.insert_latency+e.update_latency+e.delete_latency)-IFNULL(s.select_latency+s.insert_latency+s.update_latency+s.delete_latency, 0)) DESC\', \'((e.rows_selected+e.insert_latency+e.rows_updated+e.rows_deleted)-IFNULL(s.rows_selected+s.rows_inserted+s.rows_updated+s.rows_deleted, 0)) > 0\', 100, \'table_schema,table_name,index_name\'), (\'schema_table_statistics\'            , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) > 0\', 100, \'table_schema,table_name\'), (\'schema_tables_with_full_table_scans\', \'%{TABLE}.rows_full_scanned DESC\', \'(e.rows_full_scanned-IFNULL(s.rows_full_scanned, 0)) DESC\', \'(e.rows_full_scanned-IFNULL(s.rows_full_scanned, 0)) > 0\', 100, \'object_schema,object_name\'), (\'user_summary\'                       , \'%{TABLE}.statement_latency DESC\', \'(e.statement_latency-IFNULL(s.statement_latency, 0)) DESC\', \'(e.statements - IFNULL(s.statements, 0)) > 0\', NULL, \'user\'), (\'user_summary_by_file_io\'            , \'%{TABLE}.io_latency DESC\', \'(e.io_latency-IFNULL(s.io_latency, 0)) DESC\', \'(e.ios - IFNULL(s.ios, 0)) > 0\', NULL, \'user\'), (\'user_summary_by_file_io_type\'       , \'%{TABLE}.user, %{TABLE}.latency DESC\', \'e.user, (e.latency-IFNULL(s.latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'user,event_name\'), (\'user_summary_by_stages\'             , \'%{TABLE}.user, %{TABLE}.total_latency DESC\', \'e.user, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'user,event_name\'), (\'user_summary_by_statement_latency\'  , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'user\'), (\'user_summary_by_statement_type\'     , \'%{TABLE}.user, %{TABLE}.total_latency DESC\', \'e.user, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'user,statement\'), (\'wait_classes_global_by_avg_latency\' , \'IFNULL(%{TABLE}.total_latency / NULLIF(%{TABLE}.total, 0), 0) DESC\', \'IFNULL((e.total_latency-IFNULL(s.total_latency, 0)) / NULLIF((e.total - IFNULL(s.total, 0)), 0), 0) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'event_class\'), (\'wait_classes_global_by_latency\'     , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'event_class\'), (\'waits_by_host_by_latency\'           , \'%{TABLE}.host, %{TABLE}.total_latency DESC\', \'e.host, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'host,event\'), (\'waits_by_user_by_latency\'           , \'%{TABLE}.user, %{TABLE}.total_latency DESC\', \'e.user, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'user,event\'), (\'waits_global_by_latency\'            , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'events\') ; END IF;   SELECT \'  =======================  Configuration  =======================  \' AS \'\'; SELECT \'GLOBAL VARIABLES\' AS \'The following output is:\'; IF (v_has_ps_vars = \'YES\') THEN SELECT LOWER(VARIABLE_NAME) AS Variable_name, VARIABLE_VALUE AS Variable_value FROM performance_schema.global_variables ORDER BY VARIABLE_NAME; ELSE SELECT LOWER(VARIABLE_NAME) AS Variable_name, VARIABLE_VALUE AS Variable_value FROM information_schema.GLOBAL_VARIABLES ORDER BY VARIABLE_NAME; END IF;  IF (v_has_ps = \'YES\') THEN SELECT \'Performance Schema Setup - Actors\' AS \'The following output is:\'; SELECT * FROM performance_schema.setup_actors;  SELECT \'Performance Schema Setup - Consumers\' AS \'The following output is:\'; SELECT NAME AS Consumer, ENABLED, sys.ps_is_consumer_enabled(NAME) AS COLLECTS FROM performance_schema.setup_consumers;  SELECT \'Performance Schema Setup - Instruments\' AS \'The following output is:\'; SELECT SUBSTRING_INDEX(NAME, \'/\', 2) AS \'InstrumentClass\', ROUND(100*SUM(IF(ENABLED = \'YES\', 1, 0))/COUNT(*), 2) AS \'EnabledPct\', ROUND(100*SUM(IF(TIMED = \'YES\', 1, 0))/COUNT(*), 2) AS \'TimedPct\' FROM performance_schema.setup_instruments GROUP BY SUBSTRING_INDEX(NAME, \'/\', 2) ORDER BY SUBSTRING_INDEX(NAME, \'/\', 2);  SELECT \'Performance Schema Setup - Objects\' AS \'The following output is:\'; SELECT * FROM performance_schema.setup_objects;  SELECT \'Performance Schema Setup - Threads\' AS \'The following output is:\'; SELECT `TYPE` AS ThreadType, COUNT(*) AS \'Total\', ROUND(100*SUM(IF(INSTRUMENTED = \'YES\', 1, 0))/COUNT(*), 2) AS \'InstrumentedPct\' FROM performance_schema.threads GROUP BY TYPE; END IF;   IF (v_has_replication = \'NO\') THEN SELECT \'No Replication Configured\' AS \'Replication Status\'; ELSE SELECT CONCAT(\'Replication Configured: \', v_has_replication, \' - Performance Schema Replication Tables: \', v_has_ps_replication) AS \'Replication Status\';  IF (v_has_ps_replication = \'YES\') THEN SELECT \'Replication - Connection Configuration\' AS \'The following output is:\'; SELECT * FROM performance_schema.replication_connection_configuration ORDER BY CHANNEL_NAME ; END IF;  IF (v_has_ps_replication = \'YES\') THEN SELECT \'Replication - Applier Configuration\' AS \'The following output is:\'; SELECT * FROM performance_schema.replication_applier_configuration ORDER BY CHANNEL_NAME; END IF;  IF (@@master_info_repository = \'TABLE\') THEN SELECT \'Replication - Master Info Repository Configuration\' AS \'The following output is:\'; SELECT  Channel_name, Host, User_name, Port, Connect_retry, Enabled_ssl, Ssl_ca, Ssl_capath, Ssl_cert, Ssl_cipher, Ssl_key, Ssl_verify_server_cert, Heartbeat, Bind, Ignored_server_ids, Uuid, Retry_count, Ssl_crl, Ssl_crlpath, Tls_version, Enabled_auto_position FROM mysql.slave_master_info ORDER BY Channel_name ; END IF;  IF (@@relay_log_info_repository = \'TABLE\') THEN SELECT \'Replication - Relay Log Repository Configuration\' AS \'The following output is:\'; SELECT  Channel_name, Sql_delay, Number_of_workers, Id FROM mysql.slave_relay_log_info ORDER BY Channel_name ; END IF; END IF;   IF (v_has_ndb IN (\'DEFAULT\', \'YES\')) THEN SELECT \'Cluster Thread Blocks\' AS \'The following output is:\'; SELECT * FROM ndbinfo.threadblocks; END IF;  IF (v_has_ps = \'YES\') THEN IF (@sys.diagnostics.include_raw = \'ON\') THEN SELECT \'  ========================  Initial Status  ========================  \' AS \'\'; END IF;  DROP TEMPORARY TABLE IF EXISTS tmp_digests_start; CALL sys.statement_performance_analyzer(\'create_tmp\', \'tmp_digests_start\', NULL); CALL sys.statement_performance_analyzer(\'snapshot\', NULL, NULL); CALL sys.statement_performance_analyzer(\'save\', \'tmp_digests_start\', NULL);  IF (@sys.diagnostics.include_raw = \'ON\') THEN SET @sys.diagnostics.sql = REPLACE(@sys.diagnostics.sql_gen_query_template, \'%{OUTPUT}\', \'start\'); IF (@sys.debug = \'ON\') THEN SELECT \'The following query will be used to generate the query for each sys view\' AS \'Debug\'; SELECT @sys.diagnostics.sql AS \'Debug\'; END IF; PREPARE stmt_gen_query FROM @sys.diagnostics.sql; END IF; SET v_done = FALSE; OPEN c_sysviews_w_delta; c_sysviews_w_delta_loop: LOOP FETCH c_sysviews_w_delta INTO v_table_name; IF v_done THEN LEAVE c_sysviews_w_delta_loop; END IF;  IF (@sys.debug = \'ON\') THEN SELECT CONCAT(\'The following queries are for storing the initial content of \', v_table_name) AS \'Debug\'; END IF;  CALL sys.execute_prepared_stmt(CONCAT(\'DROP TEMPORARY TABLE IF EXISTS `tmp_\', v_table_name, \'_start`\')); CALL sys.execute_prepared_stmt(CONCAT(\'CREATE TEMPORARY TABLE `tmp_\', v_table_name, \'_start` SELECT * FROM `sys`.`x$\', v_table_name, \'`\'));  IF (@sys.diagnostics.include_raw = \'ON\') THEN SET @sys.diagnostics.table_name = CONCAT(\'x$\', v_table_name); EXECUTE stmt_gen_query USING @sys.diagnostics.table_name; SELECT CONCAT(@sys.diagnostics.sql_select, IF(order_by IS NOT NULL, CONCAT(\'\\n ORDER BY \', REPLACE(order_by, \'%{TABLE}\', CONCAT(\'tmp_\', v_table_name, \'_start\'))), \'\'), IF(limit_rows IS NOT NULL, CONCAT(\'\\n LIMIT \', limit_rows), \'\') ) INTO @sys.diagnostics.sql_select FROM tmp_sys_views_delta WHERE TABLE_NAME = v_table_name; SELECT CONCAT(\'Initial \', v_table_name) AS \'The following output is:\'; CALL sys.execute_prepared_stmt(@sys.diagnostics.sql_select); END IF; END LOOP; CLOSE c_sysviews_w_delta;  IF (@sys.diagnostics.include_raw = \'ON\') THEN DEALLOCATE PREPARE stmt_gen_query; END IF; END IF;  SET v_sql_status_summary_select = \'SELECT Variable_name\', v_sql_status_summary_delta  = \'\', v_sql_status_summary_from   = \'\';  REPEAT SET v_output_count = v_output_count + 1; IF (v_output_count > 1) THEN SET v_sleep = in_interval-(UNIX_TIMESTAMP(NOW(2))-v_iter_start); SELECT NOW() AS \'Time\', CONCAT(\'Going to sleep for \', v_sleep, \' seconds. Please do not interrupt\') AS \'The following output is:\'; DO SLEEP(in_interval); END IF; SET v_iter_start = UNIX_TIMESTAMP(NOW(2));  SELECT NOW(), CONCAT(\'Iteration Number \', IFNULL(v_output_count, \'NULL\')) AS \'The following output is:\';  IF (@@log_bin = 1) THEN SELECT \'SHOW MASTER STATUS\' AS \'The following output is:\'; SHOW MASTER STATUS; END IF;  IF (v_has_replication <> \'NO\') THEN SELECT \'SHOW SLAVE STATUS\' AS \'The following output is:\'; SHOW SLAVE STATUS;  IF (v_has_ps_replication = \'YES\') THEN SELECT \'Replication Connection Status\' AS \'The following output is:\'; SELECT * FROM performance_schema.replication_connection_status;  SELECT \'Replication Applier Status\' AS \'The following output is:\'; SELECT * FROM performance_schema.replication_applier_status ORDER BY CHANNEL_NAME;  SELECT \'Replication Applier Status - Coordinator\' AS \'The following output is:\'; SELECT * FROM performance_schema.replication_applier_status_by_coordinator ORDER BY CHANNEL_NAME;  SELECT \'Replication Applier Status - Worker\' AS \'The following output is:\'; SELECT * FROM performance_schema.replication_applier_status_by_worker ORDER BY CHANNEL_NAME, WORKER_ID; END IF;  IF (@@master_info_repository = \'TABLE\') THEN SELECT \'Replication - Master Log Status\' AS \'The following output is:\'; SELECT Master_log_name, Master_log_pos FROM mysql.slave_master_info; END IF;  IF (@@relay_log_info_repository = \'TABLE\') THEN SELECT \'Replication - Relay Log Status\' AS \'The following output is:\'; SELECT sys.format_path(Relay_log_name) AS Relay_log_name, Relay_log_pos, Master_log_name, Master_log_pos FROM mysql.slave_relay_log_info;  SELECT \'Replication - Worker Status\' AS \'The following output is:\'; SELECT Id, sys.format_path(Relay_log_name) AS Relay_log_name, Relay_log_pos, Master_log_name, Master_log_pos, sys.format_path(Checkpoint_relay_log_name) AS Checkpoint_relay_log_name, Checkpoint_relay_log_pos, Checkpoint_master_log_name, Checkpoint_master_log_pos, Checkpoint_seqno, Checkpoint_group_size, HEX(Checkpoint_group_bitmap) AS Checkpoint_group_bitmap , Channel_name FROM mysql.slave_worker_info ORDER BY  Channel_name, Id; END IF; END IF;  SET v_table_name = CONCAT(\'tmp_metrics_\', v_output_count); CALL sys.execute_prepared_stmt(CONCAT(\'DROP TEMPORARY TABLE IF EXISTS \', v_table_name));  CALL sys.execute_prepared_stmt(CONCAT(\'CREATE TEMPORARY TABLE \', v_table_name, \' ( Variable_name VARCHAR(193) NOT NULL, Variable_value VARCHAR(1024), Type VARCHAR(225) NOT NULL, Enabled ENUM(\'\'YES\'\', \'\'NO\'\', \'\'PARTIAL\'\') NOT NULL, PRIMARY KEY (Type, Variable_name) ) ENGINE = InnoDB DEFAULT CHARSET=utf8mb3\'));  IF (v_has_metrics) THEN SET @sys.diagnostics.sql = CONCAT( \'INSERT INTO \', v_table_name, \' SELECT Variable_name, REPLACE(Variable_value, \'\'\\n\'\', \'\'\\\\\\\\n\'\') AS Variable_value, Type, Enabled FROM sys.metrics\' ); ELSE SET @sys.diagnostics.sql = CONCAT( \'INSERT INTO \', v_table_name, \'(SELECT LOWER(VARIABLE_NAME) AS Variable_name, REPLACE(VARIABLE_VALUE, \'\'\\n\'\', \'\'\\\\\\\\n\'\') AS Variable_value, \'\'Global Status\'\' AS Type, \'\'YES\'\' AS Enabled FROM performance_schema.global_status ) UNION ALL ( SELECT NAME AS Variable_name, COUNT AS Variable_value, CONCAT(\'\'InnoDB Metrics - \'\', SUBSYSTEM) AS Type, IF(STATUS = \'\'enabled\'\', \'\'YES\'\', \'\'NO\'\') AS Enabled FROM information_schema.INNODB_METRICS WHERE NAME NOT IN ( \'\'lock_row_lock_time\'\', \'\'lock_row_lock_time_avg\'\', \'\'lock_row_lock_time_max\'\', \'\'lock_row_lock_waits\'\', \'\'buffer_pool_reads\'\', \'\'buffer_pool_read_requests\'\', \'\'buffer_pool_write_requests\'\', \'\'buffer_pool_wait_free\'\', \'\'buffer_pool_read_ahead\'\', \'\'buffer_pool_read_ahead_evicted\'\', \'\'buffer_pool_pages_total\'\', \'\'buffer_pool_pages_misc\'\', \'\'buffer_pool_pages_data\'\', \'\'buffer_pool_bytes_data\'\', \'\'buffer_pool_pages_dirty\'\', \'\'buffer_pool_bytes_dirty\'\', \'\'buffer_pool_pages_free\'\', \'\'buffer_pages_created\'\', \'\'buffer_pages_written\'\', \'\'buffer_pages_read\'\', \'\'buffer_data_reads\'\', \'\'buffer_data_written\'\', \'\'file_num_open_files\'\', \'\'os_log_bytes_written\'\', \'\'os_log_fsyncs\'\', \'\'os_log_pending_fsyncs\'\', \'\'os_log_pending_writes\'\', \'\'log_waits\'\', \'\'log_write_requests\'\', \'\'log_writes\'\', \'\'innodb_dblwr_writes\'\', \'\'innodb_dblwr_pages_written\'\', \'\'innodb_page_size\'\') ) UNION ALL ( SELECT \'\'NOW()\'\' AS Variable_name, NOW(3) AS Variable_value, \'\'System Time\'\' AS Type, \'\'YES\'\' AS Enabled ) UNION ALL ( SELECT \'\'UNIX_TIMESTAMP()\'\' AS Variable_name, ROUND(UNIX_TIMESTAMP(NOW(3)), 3) AS Variable_value, \'\'System Time\'\' AS Type, \'\'YES\'\' AS Enabled ) ORDER BY Type, Variable_name;\' ); END IF; CALL sys.execute_prepared_stmt(@sys.diagnostics.sql);  CALL sys.execute_prepared_stmt( CONCAT(\'(SELECT Variable_value INTO @sys.diagnostics.output_time FROM \', v_table_name, \' WHERE Type = \'\'System Time\'\' AND Variable_name = \'\'UNIX_TIMESTAMP()\'\')\') ); SET v_output_time = @sys.diagnostics.output_time;  SET v_sql_status_summary_select = CONCAT(v_sql_status_summary_select, \', CONCAT( LEFT(s\', v_output_count, \'.Variable_value, \', v_status_summary_width, \'), IF(\', REPLACE(v_no_delta_names, \'%{COUNT}\', v_output_count), \' AND s\', v_output_count, \'.Variable_value REGEXP \'\'^[0-9]+(\\\\\\\\.[0-9]+)?$\'\', CONCAT(\'\' (\'\', ROUND(s\', v_output_count, \'.Variable_value/\', v_output_time, \', 2), \'\'/sec)\'\'), \'\'\'\') ) AS \'\'Output \', v_output_count, \'\'\'\'), v_sql_status_summary_from   = CONCAT(v_sql_status_summary_from, \' \', IF(v_output_count = 1, \'  FROM \', \'       INNER JOIN \'), v_table_name, \' s\', v_output_count, IF (v_output_count = 1, \'\', \' USING (Type, Variable_name)\')); IF (v_output_count > 1) THEN SET v_sql_status_summary_delta  = CONCAT(v_sql_status_summary_delta, \', IF(\', REPLACE(v_no_delta_names, \'%{COUNT}\', v_output_count), \' AND s\', (v_output_count-1), \'.Variable_value REGEXP \'\'^[0-9]+(\\\\\\\\.[0-9]+)?$\'\' AND s\', v_output_count, \'.Variable_value REGEXP \'\'^[0-9]+(\\\\\\\\.[0-9]+)?$\'\', CONCAT(IF(s\', (v_output_count-1), \'.Variable_value REGEXP \'\'^[0-9]+\\\\\\\\.[0-9]+$\'\' OR s\', v_output_count, \'.Variable_value REGEXP \'\'^[0-9]+\\\\\\\\.[0-9]+$\'\', ROUND((s\', v_output_count, \'.Variable_value-s\', (v_output_count-1), \'.Variable_value), 2), (s\', v_output_count, \'.Variable_value-s\', (v_output_count-1), \'.Variable_value) ), \'\' (\'\', ROUND((s\', v_output_count, \'.Variable_value-s\', (v_output_count-1), \'.Variable_value)/(\', v_output_time, \'-\', v_output_time_prev, \'), 2), \'\'/sec)\'\' ), \'\'\'\' ) AS \'\'Delta (\', (v_output_count-1), \' -> \', v_output_count, \')\'\'\'); END IF;  SET v_output_time_prev = v_output_time;  IF (@sys.diagnostics.include_raw = \'ON\') THEN IF (v_has_metrics) THEN SELECT \'SELECT * FROM sys.metrics\' AS \'The following output is:\'; ELSE SELECT \'sys.metrics equivalent\' AS \'The following output is:\'; END IF; CALL sys.execute_prepared_stmt(CONCAT(\'SELECT Type, Variable_name, Enabled, Variable_value FROM \', v_table_name, \' ORDER BY Type, Variable_name\')); END IF;  IF (v_has_innodb IN (\'DEFAULT\', \'YES\')) THEN SELECT \'SHOW ENGINE INNODB STATUS\' AS \'The following output is:\'; EXECUTE stmt_innodb_status; SELECT \'InnoDB - Transactions\' AS \'The following output is:\'; SELECT * FROM information_schema.INNODB_TRX; END IF;  IF (v_has_ndb IN (\'DEFAULT\', \'YES\')) THEN SELECT \'SHOW ENGINE NDBCLUSTER STATUS\' AS \'The following output is:\'; EXECUTE stmt_ndbcluster_status;  SELECT \'ndbinfo.memoryusage\' AS \'The following output is:\'; SELECT node_id, memory_type, sys.format_bytes(used) AS used, used_pages, sys.format_bytes(total) AS total, total_pages, ROUND(100*(used/total), 2) AS \'Used %\' FROM ndbinfo.memoryusage;  SET v_done = FALSE; OPEN c_ndbinfo; c_ndbinfo_loop: LOOP FETCH c_ndbinfo INTO v_table_name; IF v_done THEN LEAVE c_ndbinfo_loop; END IF;  SELECT CONCAT(\'SELECT * FROM ndbinfo.\', v_table_name) AS \'The following output is:\'; CALL sys.execute_prepared_stmt(CONCAT(\'SELECT * FROM `ndbinfo`.`\', v_table_name, \'`\')); END LOOP; CLOSE c_ndbinfo;  SELECT * FROM information_schema.FILES; END IF;  SELECT \'SELECT * FROM sys.processlist\' AS \'The following output is:\'; SELECT processlist.* FROM sys.processlist;  IF (v_has_ps = \'YES\') THEN IF (sys.ps_is_consumer_enabled(\'events_waits_history_long\') = \'YES\') THEN SELECT \'SELECT * FROM sys.latest_file_io\' AS \'The following output is:\'; SELECT * FROM sys.latest_file_io; END IF;  IF (EXISTS(SELECT 1 FROM performance_schema.setup_instruments WHERE NAME LIKE \'memory/%\' AND ENABLED = \'YES\')) THEN SELECT \'SELECT * FROM sys.memory_by_host_by_current_bytes\' AS \'The following output is:\'; SELECT * FROM sys.memory_by_host_by_current_bytes;  SELECT \'SELECT * FROM sys.memory_by_thread_by_current_bytes\' AS \'The following output is:\'; SELECT * FROM sys.memory_by_thread_by_current_bytes;  SELECT \'SELECT * FROM sys.memory_by_user_by_current_bytes\' AS \'The following output is:\'; SELECT * FROM sys.memory_by_user_by_current_bytes;  SELECT \'SELECT * FROM sys.memory_global_by_current_bytes\' AS \'The following output is:\'; SELECT * FROM sys.memory_global_by_current_bytes; END IF; END IF;  SET v_runtime = (UNIX_TIMESTAMP(NOW(2)) - v_start); UNTIL (v_runtime + in_interval >= in_max_runtime) END REPEAT;  IF (v_has_ps = \'YES\') THEN SELECT \'SHOW ENGINE PERFORMANCE_SCHEMA STATUS\' AS \'The following output is:\'; EXECUTE stmt_ps_status; END IF;  IF (v_has_innodb IN (\'DEFAULT\', \'YES\')) THEN DEALLOCATE PREPARE stmt_innodb_status; END IF; IF (v_has_ps = \'YES\') THEN DEALLOCATE PREPARE stmt_ps_status; END IF; IF (v_has_ndb IN (\'DEFAULT\', \'YES\')) THEN DEALLOCATE PREPARE stmt_ndbcluster_status; END IF;   SELECT \'  ============================  Schema Information  ============================  \' AS \'\';  SELECT COUNT(*) AS \'Total Number of Tables\' FROM information_schema.TABLES;  IF (@sys.diagnostics.allow_i_s_tables = \'ON\') THEN SELECT \'Storage Engine Usage\' AS \'The following output is:\'; SELECT ENGINE, COUNT(*) AS NUM_TABLES, sys.format_bytes(SUM(DATA_LENGTH)) AS DATA_LENGTH, sys.format_bytes(SUM(INDEX_LENGTH)) AS INDEX_LENGTH, sys.format_bytes(SUM(DATA_LENGTH+INDEX_LENGTH)) AS TOTAL FROM information_schema.TABLES GROUP BY ENGINE;  SELECT \'Schema Object Overview\' AS \'The following output is:\'; SELECT * FROM sys.schema_object_overview;  SELECT \'Tables without a PRIMARY KEY\' AS \'The following output is:\'; SELECT TABLES.TABLE_SCHEMA, ENGINE, COUNT(*) AS NumTables FROM information_schema.TABLES LEFT OUTER JOIN information_schema.STATISTICS ON STATISTICS.TABLE_SCHEMA = TABLES.TABLE_SCHEMA AND STATISTICS.TABLE_NAME = TABLES.TABLE_NAME AND STATISTICS.INDEX_NAME = \'PRIMARY\' WHERE STATISTICS.TABLE_NAME IS NULL AND TABLES.TABLE_SCHEMA NOT IN (\'mysql\', \'information_schema\', \'performance_schema\', \'sys\') AND TABLES.TABLE_TYPE = \'BASE TABLE\' GROUP BY TABLES.TABLE_SCHEMA, ENGINE; END IF;  IF (v_has_ps = \'YES\') THEN SELECT \'Unused Indexes\' AS \'The following output is:\'; SELECT object_schema, COUNT(*) AS NumUnusedIndexes FROM performance_schema.table_io_waits_summary_by_index_usage WHERE index_name IS NOT NULL AND count_star = 0 AND object_schema NOT IN (\'mysql\', \'sys\') AND index_name != \'PRIMARY\' GROUP BY object_schema; END IF;  IF (v_has_ps = \'YES\') THEN SELECT \'  =========================  Overall Status  =========================  \' AS \'\';  SELECT \'CALL sys.ps_statement_avg_latency_histogram()\' AS \'The following output is:\'; CALL sys.ps_statement_avg_latency_histogram();  CALL sys.statement_performance_analyzer(\'snapshot\', NULL, NULL); CALL sys.statement_performance_analyzer(\'overall\', NULL, \'with_runtimes_in_95th_percentile\');  SET @sys.diagnostics.sql = REPLACE(@sys.diagnostics.sql_gen_query_template, \'%{OUTPUT}\', \'end\'); IF (@sys.debug = \'ON\') THEN SELECT \'The following query will be used to generate the query for each sys view\' AS \'Debug\'; SELECT @sys.diagnostics.sql AS \'Debug\'; END IF; PREPARE stmt_gen_query FROM @sys.diagnostics.sql;  SET v_done = FALSE; OPEN c_sysviews_w_delta; c_sysviews_w_delta_loop: LOOP FETCH c_sysviews_w_delta INTO v_table_name; IF v_done THEN LEAVE c_sysviews_w_delta_loop; END IF;  IF (@sys.debug = \'ON\') THEN SELECT CONCAT(\'The following queries are for storing the final content of \', v_table_name) AS \'Debug\'; END IF;  CALL sys.execute_prepared_stmt(CONCAT(\'DROP TEMPORARY TABLE IF EXISTS `tmp_\', v_table_name, \'_end`\')); CALL sys.execute_prepared_stmt(CONCAT(\'CREATE TEMPORARY TABLE `tmp_\', v_table_name, \'_end` SELECT * FROM `sys`.`x$\', v_table_name, \'`\'));  SET @sys.diagnostics.table_name = CONCAT(\'x$\', v_table_name); EXECUTE stmt_gen_query USING @sys.diagnostics.table_name; SELECT CONCAT(@sys.diagnostics.sql_select, IF(order_by IS NOT NULL, CONCAT(\'\\n ORDER BY \', REPLACE(order_by, \'%{TABLE}\', CONCAT(\'tmp_\', v_table_name, \'_end\'))), \'\'), IF(limit_rows IS NOT NULL, CONCAT(\'\\n LIMIT \', limit_rows), \'\') ) INTO @sys.diagnostics.sql_select FROM tmp_sys_views_delta WHERE TABLE_NAME = v_table_name; SELECT CONCAT(\'Overall \', v_table_name) AS \'The following output is:\'; CALL sys.execute_prepared_stmt(@sys.diagnostics.sql_select); END LOOP; CLOSE c_sysviews_w_delta;  DEALLOCATE PREPARE stmt_gen_query;   SELECT \'  ======================  Delta Status  ======================  \' AS \'\';  CALL sys.statement_performance_analyzer(\'delta\', \'tmp_digests_start\', \'with_runtimes_in_95th_percentile\'); CALL sys.statement_performance_analyzer(\'cleanup\', NULL, NULL);  DROP TEMPORARY TABLE tmp_digests_start;  IF (@sys.debug = \'ON\') THEN SELECT \'The following query will be used to generate the query for each sys view delta\' AS \'Debug\'; SELECT @sys.diagnostics.sql_gen_query_delta AS \'Debug\'; END IF; PREPARE stmt_gen_query_delta FROM @sys.diagnostics.sql_gen_query_delta;  SET v_old_group_concat_max_len = @@session.group_concat_max_len; SET @@session.group_concat_max_len = 2048; SET v_done = FALSE; OPEN c_sysviews_w_delta; c_sysviews_w_delta_loop: LOOP FETCH c_sysviews_w_delta INTO v_table_name; IF v_done THEN LEAVE c_sysviews_w_delta_loop; END IF;  SET @sys.diagnostics.table_name = v_table_name; EXECUTE stmt_gen_query_delta USING @sys.diagnostics.table_name; SELECT CONCAT(@sys.diagnostics.sql_select, IF(where_delta IS NOT NULL, CONCAT(\'\\n WHERE \', where_delta), \'\'), IF(order_by_delta IS NOT NULL, CONCAT(\'\\n ORDER BY \', order_by_delta), \'\'), IF(limit_rows IS NOT NULL, CONCAT(\'\\n LIMIT \', limit_rows), \'\') ) INTO @sys.diagnostics.sql_select FROM tmp_sys_views_delta WHERE TABLE_NAME = v_table_name;  SELECT CONCAT(\'Delta \', v_table_name) AS \'The following output is:\'; CALL sys.execute_prepared_stmt(@sys.diagnostics.sql_select);  CALL sys.execute_prepared_stmt(CONCAT(\'DROP TEMPORARY TABLE `tmp_\', v_table_name, \'_end`\')); CALL sys.execute_prepared_stmt(CONCAT(\'DROP TEMPORARY TABLE `tmp_\', v_table_name, \'_start`\')); END LOOP; CLOSE c_sysviews_w_delta; SET @@session.group_concat_max_len = v_old_group_concat_max_len;  DEALLOCATE PREPARE stmt_gen_query_delta; DROP TEMPORARY TABLE tmp_sys_views_delta; END IF;  IF (v_has_metrics) THEN SELECT \'SELECT * FROM sys.metrics\' AS \'The following output is:\'; ELSE SELECT \'sys.metrics equivalent\' AS \'The following output is:\'; END IF; CALL sys.execute_prepared_stmt( CONCAT(v_sql_status_summary_select, v_sql_status_summary_delta, \', Type, s1.Enabled\', v_sql_status_summary_from, \' ORDER BY Type, Variable_name\' ) );  SET v_count = 0; WHILE (v_count < v_output_count) DO SET v_count = v_count + 1; SET v_table_name = CONCAT(\'tmp_metrics_\', v_count); CALL sys.execute_prepared_stmt(CONCAT(\'DROP TEMPORARY TABLE IF EXISTS \', v_table_name)); END WHILE;  IF (in_auto_config <> \'current\') THEN CALL sys.ps_setup_reload_saved(); SET sql_log_bin = @log_bin; END IF;  SET @sys.diagnostics.output_time            = NULL, @sys.diagnostics.sql                    = NULL, @sys.diagnostics.sql_gen_query_delta    = NULL, @sys.diagnostics.sql_gen_query_template = NULL, @sys.diagnostics.sql_select             = NULL, @sys.diagnostics.table_name             = NULL;  IF (v_this_thread_enabled = \'YES\') THEN CALL sys.ps_setup_enable_thread(CONNECTION_ID()); END IF;  IF (@log_bin = 1) THEN SET sql_log_bin = @log_bin; END IF; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Create a report of the current status of the server for diagnostics purposes. Data collected includes (some items depends on versions and settings):\n \n * The GLOBAL VARIABLES\n * Several sys schema views including metrics or equivalent (depending on version and settings)\n * Queries in the 95th percentile\n * Several ndbinfo views for MySQL Cluster\n * Replication (both master and slave) information.\n \n Some of the sys schema views are calculated as initial (optional), overall, delta:\n \n * The initial view is the content of the view at the start of this procedure.\n This output will be the same as the the start values used for the delta view.\n The initial view is included if @sys.diagnostics.include_raw = \'ON\'.\n * The overall view is the content of the view at the end of this procedure.\n This output is the same as the end values used for the delta view.\n The overall view is always included.\n * The delta view is the difference from the beginning to the end. Note that for min and max values\n they are simply the min or max value from the end view respectively, so does not necessarily reflect\n the minimum/maximum value in the monitored period.\n Note: except for the metrics views the delta is only calculation between the first and last outputs.\n \n Requires the SUPER privilege for \"SET sql_log_bin = 0;\".\n \n Versions supported:\n * MySQL 5.6: 5.6.10 and later\n * MySQL 5.7: 5.7.9 and later\n \n Parameters\n \n in_max_runtime (INT UNSIGNED):\n The maximum time to keep collecting data.\n Use NULL to get the default which is 60 seconds, otherwise enter a value greater than 0.\n in_interval (INT UNSIGNED):\n How long to sleep between data collections.\n Use NULL to get the default which is 30 seconds, otherwise enter a value greater than 0.\n in_auto_config (ENUM(\'current\', \'medium\', \'full\'))\n Automatically enable Performance Schema instruments and consumers.\n NOTE: The more that are enabled, the more impact on the performance.\n Supported values are:\n * current - use the current settings.\n * medium - enable some settings.\n * full - enables all settings. This will have a big impact on the\n performance - be careful using this option.\n If another setting the \'current\' is chosen, the current settings\n are restored at the end of the procedure.\n \n \n Configuration Options\n \n sys.diagnostics.allow_i_s_tables\n Specifies whether it is allowed to do table scan queries on information_schema.TABLES. This can be expensive if there\n are many tables. Set to \'ON\' to allow, \'OFF\' to not allow.\n Default is \'OFF\'.\n \n sys.diagnostics.include_raw\n Set to \'ON\' to include the raw data (e.g. the original output of \"SELECT * FROM sys.metrics\").\n Use this to get the initial values of the various views.\n Default is \'OFF\'.\n \n sys.statement_truncate_len\n How much of queries in the process list output to include.\n Default is 64.\n \n sys.debug\n Whether to provide debugging output.\n Default is \'OFF\'. Set to \'ON\' to include.\n \n \n Example\n \n To create a report and append it to the file diag.out:\n \n mysql> TEE diag.out;\n mysql> CALL sys.diagnostics(120, 30, \'current\');\n ...\n mysql> NOTEE;\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_start, v_runtime, v_iter_start, v_sleep DECIMAL(20,2) DEFAULT 0.0; DECLARE v_has_innodb, v_has_ndb, v_has_ps, v_has_replication, v_has_ps_replication VARCHAR(8) CHARSET utf8mb3 DEFAULT \'NO\'; DECLARE v_this_thread_enabled, v_has_ps_vars, v_has_metrics ENUM(\'YES\', \'NO\'); DECLARE v_table_name, v_banner VARCHAR(64) CHARSET utf8mb3; DECLARE v_sql_status_summary_select, v_sql_status_summary_delta, v_sql_status_summary_from, v_no_delta_names TEXT; DECLARE v_output_time, v_output_time_prev DECIMAL(20,3) UNSIGNED; DECLARE v_output_count, v_count, v_old_group_concat_max_len INT UNSIGNED DEFAULT 0; DECLARE v_status_summary_width TINYINT UNSIGNED DEFAULT 50; DECLARE v_done BOOLEAN DEFAULT FALSE; DECLARE c_ndbinfo CURSOR FOR SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = \'ndbinfo\' AND TABLE_NAME NOT IN ( \'blocks\', \'config_params\', \'dict_obj_types\', \'disk_write_speed_base\', \'memory_per_fragment\', \'memoryusage\', \'operations_per_fragment\', \'threadblocks\' ); DECLARE c_sysviews_w_delta CURSOR FOR SELECT table_name FROM tmp_sys_views_delta ORDER BY table_name; DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;  SELECT INSTRUMENTED INTO v_this_thread_enabled FROM performance_schema.threads WHERE PROCESSLIST_ID = CONNECTION_ID(); IF (v_this_thread_enabled = \'YES\') THEN CALL sys.ps_setup_disable_thread(CONNECTION_ID()); END IF;  IF (in_max_runtime < in_interval) THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'in_max_runtime must be greater than or equal to in_interval\'; END IF; IF (in_max_runtime = 0) THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'in_max_runtime must be greater than 0\'; END IF; IF (in_interval = 0) THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'in_interval must be greater than 0\'; END IF;  IF (@sys.diagnostics.allow_i_s_tables IS NULL) THEN SET @sys.diagnostics.allow_i_s_tables = sys.sys_get_config(\'diagnostics.allow_i_s_tables\', \'OFF\'); END IF; IF (@sys.diagnostics.include_raw IS NULL) THEN SET @sys.diagnostics.include_raw      = sys.sys_get_config(\'diagnostics.include_raw\'     , \'OFF\'); END IF; IF (@sys.debug IS NULL) THEN SET @sys.debug                        = sys.sys_get_config(\'debug\'                       , \'OFF\'); END IF; IF (@sys.statement_truncate_len IS NULL) THEN SET @sys.statement_truncate_len       = sys.sys_get_config(\'statement_truncate_len\'      , \'64\' ); END IF;  SET @log_bin := @@sql_log_bin; IF (@log_bin = 1) THEN SET sql_log_bin = 0; END IF;  SET v_no_delta_names = CONCAT(\'s%{COUNT}.Variable_name NOT IN (\', \'\'innodb_buffer_pool_pages_total\', \', \'\'innodb_page_size\', \', \'\'last_query_cost\', \', \'\'last_query_partial_plans\', \', \'\'qcache_total_blocks\', \', \'\'slave_last_heartbeat\', \', \'\'ssl_ctx_verify_depth\', \', \'\'ssl_ctx_verify_mode\', \', \'\'ssl_session_cache_size\', \', \'\'ssl_verify_depth\', \', \'\'ssl_verify_mode\', \', \'\'ssl_version\', \', \'\'buffer_flush_lsn_avg_rate\', \', \'\'buffer_flush_pct_for_dirty\', \', \'\'buffer_flush_pct_for_lsn\', \', \'\'buffer_pool_pages_total\', \', \'\'lock_row_lock_time_avg\', \', \'\'lock_row_lock_time_max\', \', \'\'innodb_page_size\'\', \')\');  IF (in_auto_config <> \'current\') THEN IF (@sys.debug = \'ON\') THEN SELECT CONCAT(\'Updating Performance Schema configuration to \', in_auto_config) AS \'Debug\'; END IF; CALL sys.ps_setup_save(0);  IF (in_auto_config = \'medium\') THEN UPDATE performance_schema.setup_consumers SET ENABLED = \'YES\' WHERE NAME NOT LIKE \'%\\_history%\';  UPDATE performance_schema.setup_instruments SET ENABLED = \'YES\', TIMED   = \'YES\' WHERE NAME NOT LIKE \'wait/synch/%\'; ELSEIF (in_auto_config = \'full\') THEN UPDATE performance_schema.setup_consumers SET ENABLED = \'YES\';  UPDATE performance_schema.setup_instruments SET ENABLED = \'YES\', TIMED   = \'YES\'; END IF;  UPDATE performance_schema.threads SET INSTRUMENTED = \'YES\' WHERE PROCESSLIST_ID <> CONNECTION_ID(); END IF;  SET v_start        = UNIX_TIMESTAMP(NOW(2)), in_interval    = IFNULL(in_interval, 30), in_max_runtime = IFNULL(in_max_runtime, 60);  SET v_banner = REPEAT( \'-\', LEAST( GREATEST( 36, CHAR_LENGTH(VERSION()), CHAR_LENGTH(@@global.version_comment), CHAR_LENGTH(@@global.version_compile_os), CHAR_LENGTH(@@global.version_compile_machine), CHAR_LENGTH(@@global.socket), CHAR_LENGTH(@@global.datadir) ), 64 ) ); SELECT \'Hostname\' AS \'Name\', @@global.hostname AS \'Value\' UNION ALL SELECT \'Port\' AS \'Name\', @@global.port AS \'Value\' UNION ALL SELECT \'Socket\' AS \'Name\', @@global.socket AS \'Value\' UNION ALL SELECT \'Datadir\' AS \'Name\', @@global.datadir AS \'Value\' UNION ALL SELECT \'Server UUID\' AS \'Name\', @@global.server_uuid AS \'Value\' UNION ALL SELECT REPEAT(\'-\', 23) AS \'Name\', v_banner AS \'Value\' UNION ALL SELECT \'MySQL Version\' AS \'Name\', VERSION() AS \'Value\' UNION ALL SELECT \'Sys Schema Version\' AS \'Name\', (SELECT sys_version FROM sys.version) AS \'Value\' UNION ALL SELECT \'Version Comment\' AS \'Name\', @@global.version_comment AS \'Value\' UNION ALL SELECT \'Version Compile OS\' AS \'Name\', @@global.version_compile_os AS \'Value\' UNION ALL SELECT \'Version Compile Machine\' AS \'Name\', @@global.version_compile_machine AS \'Value\' UNION ALL SELECT REPEAT(\'-\', 23) AS \'Name\', v_banner AS \'Value\' UNION ALL SELECT \'UTC Time\' AS \'Name\', UTC_TIMESTAMP() AS \'Value\' UNION ALL SELECT \'Local Time\' AS \'Name\', NOW() AS \'Value\' UNION ALL SELECT \'Time Zone\' AS \'Name\', @@global.time_zone AS \'Value\' UNION ALL SELECT \'System Time Zone\' AS \'Name\', @@global.system_time_zone AS \'Value\' UNION ALL SELECT \'Time Zone Offset\' AS \'Name\', TIMEDIFF(NOW(), UTC_TIMESTAMP()) AS \'Value\';  SET v_has_innodb         = IFNULL((SELECT SUPPORT FROM information_schema.ENGINES WHERE ENGINE = \'InnoDB\'), \'NO\'), v_has_ndb            = IFNULL((SELECT SUPPORT FROM information_schema.ENGINES WHERE ENGINE = \'NDBCluster\'), \'NO\'), v_has_ps             = IFNULL((SELECT SUPPORT FROM information_schema.ENGINES WHERE ENGINE = \'PERFORMANCE_SCHEMA\'), \'NO\'), v_has_ps_replication = IF(v_has_ps = \'YES\' AND EXISTS(SELECT 1 FROM information_schema.TABLES WHERE TABLE_SCHEMA = \'performance_schema\' AND TABLE_NAME = \'replication_applier_status\'), \'YES\', \'NO\' ), v_has_replication    =  IF(v_has_ps_replication = \'YES\', IF((SELECT COUNT(*) FROM performance_schema.replication_connection_status) > 0, \'YES\', \'NO\'), IF(@@master_info_repository = \'TABLE\', IF((SELECT COUNT(*) FROM mysql.slave_master_info) > 0, \'YES\', \'NO\'), IF(@@relay_log_info_repository = \'TABLE\', IF((SELECT COUNT(*) FROM mysql.slave_relay_log_info) > 0, \'YES\', \'NO\'), \'MAYBE\')) ) , v_has_metrics        = IF(v_has_ps = \'YES\' OR (sys.version_major() = 5 AND sys.version_minor() = 6), \'YES\', \'NO\'), v_has_ps_vars        = \'NO\';   SET v_has_ps_vars = IF(@@global.show_compatibility_56, \'NO\', \'YES\');  SET v_has_ps_vars = \'YES\';  IF (@sys.debug = \'ON\') THEN SELECT v_has_innodb AS \'Has_InnoDB\', v_has_ndb AS \'Has_NDBCluster\', v_has_ps AS \'Has_Performance_Schema\', v_has_ps_vars AS \'Has_P_S_SHOW_Variables\', v_has_metrics AS \'Has_metrics\', v_has_ps_replication \'AS Has_P_S_Replication\', v_has_replication AS \'Has_Replication\'; END IF;  IF (v_has_innodb IN (\'DEFAULT\', \'YES\')) THEN SET @sys.diagnostics.sql = \'SHOW ENGINE InnoDB STATUS\'; PREPARE stmt_innodb_status FROM @sys.diagnostics.sql; END IF;  IF (v_has_ps = \'YES\') THEN SET @sys.diagnostics.sql = \'SHOW ENGINE PERFORMANCE_SCHEMA STATUS\'; PREPARE stmt_ps_status FROM @sys.diagnostics.sql; END IF;  IF (v_has_ndb IN (\'DEFAULT\', \'YES\')) THEN SET @sys.diagnostics.sql = \'SHOW ENGINE NDBCLUSTER STATUS\'; PREPARE stmt_ndbcluster_status FROM @sys.diagnostics.sql; END IF;  SET @sys.diagnostics.sql_gen_query_template = \'SELECT CONCAT( \'SELECT \', GROUP_CONCAT( CASE WHEN (SUBSTRING(TABLE_NAME, 3), COLUMN_NAME) IN ( (\'io_global_by_file_by_bytes\', \'total\'), (\'io_global_by_wait_by_bytes\', \'total_requested\') ) THEN CONCAT(\'sys.format_bytes(\', COLUMN_NAME, \') AS \', COLUMN_NAME) WHEN COLUMN_NAME LIKE \'%latency\' THEN CONCAT(\'sys.format_time(\', COLUMN_NAME, \') AS \', COLUMN_NAME) WHEN SUBSTRING(COLUMN_NAME, -7) = \'_memory\' OR SUBSTRING(COLUMN_NAME, -17) = \'_memory_allocated\' OR ((SUBSTRING(COLUMN_NAME, -5) = \'_read\' OR SUBSTRING(COLUMN_NAME, -8) = \'_written\' OR SUBSTRING(COLUMN_NAME, -6) = \'_write\') AND SUBSTRING(COLUMN_NAME, 1, 6) <> \'COUNT_\') THEN CONCAT(\'sys.format_bytes(\', COLUMN_NAME, \') AS \', COLUMN_NAME) ELSE COLUMN_NAME END ORDER BY ORDINAL_POSITION SEPARATOR \',\n       \' ), \'\n  FROM tmp_\', SUBSTRING(TABLE_NAME FROM 3), \'_%{OUTPUT}\' ) AS Query INTO @sys.diagnostics.sql_select FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = \'sys\' AND TABLE_NAME = ? GROUP BY TABLE_NAME\';  SET @sys.diagnostics.sql_gen_query_delta = \'SELECT CONCAT( \'SELECT \', GROUP_CONCAT( CASE WHEN FIND_IN_SET(COLUMN_NAME, diag.pk) THEN COLUMN_NAME WHEN diag.TABLE_NAME = \'io_global_by_file_by_bytes\' AND COLUMN_NAME = \'write_pct\' THEN CONCAT(\'IFNULL(ROUND(100-(((e.total_read-IFNULL(s.total_read, 0))\', \'/NULLIF(((e.total_read-IFNULL(s.total_read, 0))+(e.total_written-IFNULL(s.total_written, 0))), 0))*100), 2), 0.00) AS \', COLUMN_NAME) WHEN (diag.TABLE_NAME, COLUMN_NAME) IN ( (\'io_global_by_file_by_bytes\', \'total\'), (\'io_global_by_wait_by_bytes\', \'total_requested\') ) THEN CONCAT(\'sys.format_bytes(e.\', COLUMN_NAME, \'-IFNULL(s.\', COLUMN_NAME, \', 0)) AS \', COLUMN_NAME) WHEN SUBSTRING(COLUMN_NAME, 1, 4) IN (\'max_\', \'min_\') AND SUBSTRING(COLUMN_NAME, -8) = \'_latency\' THEN CONCAT(\'sys.format_time(e.\', COLUMN_NAME, \') AS \', COLUMN_NAME) WHEN COLUMN_NAME = \'avg_latency\' THEN CONCAT(\'sys.format_time((e.total_latency - IFNULL(s.total_latency, 0))\', \'/NULLIF(e.total - IFNULL(s.total, 0), 0)) AS \', COLUMN_NAME) WHEN SUBSTRING(COLUMN_NAME, -12) = \'_avg_latency\' THEN CONCAT(\'sys.format_time((e.\', SUBSTRING(COLUMN_NAME FROM 1 FOR CHAR_LENGTH(COLUMN_NAME)-12), \'_latency - IFNULL(s.\', SUBSTRING(COLUMN_NAME FROM 1 FOR CHAR_LENGTH(COLUMN_NAME)-12), \'_latency, 0))\', \'/NULLIF(e.\', SUBSTRING(COLUMN_NAME FROM 1 FOR CHAR_LENGTH(COLUMN_NAME)-12), \'s - IFNULL(s.\', SUBSTRING(COLUMN_NAME FROM 1 FOR CHAR_LENGTH(COLUMN_NAME)-12), \'s, 0), 0)) AS \', COLUMN_NAME) WHEN COLUMN_NAME LIKE \'%latency\' THEN CONCAT(\'sys.format_time(e.\', COLUMN_NAME, \' - IFNULL(s.\', COLUMN_NAME, \', 0)) AS \', COLUMN_NAME) WHEN COLUMN_NAME IN (\'avg_read\', \'avg_write\', \'avg_written\') THEN CONCAT(\'sys.format_bytes(IFNULL((e.total_\', IF(COLUMN_NAME = \'avg_read\', \'read\', \'written\'), \'-IFNULL(s.total_\', IF(COLUMN_NAME = \'avg_read\', \'read\', \'written\'), \', 0))\', \'/NULLIF(e.count_\', IF(COLUMN_NAME = \'avg_read\', \'read\', \'write\'), \'-IFNULL(s.count_\', IF(COLUMN_NAME = \'avg_read\', \'read\', \'write\'), \', 0), 0), 0)) AS \', COLUMN_NAME) WHEN SUBSTRING(COLUMN_NAME, -7) = \'_memory\' OR SUBSTRING(COLUMN_NAME, -17) = \'_memory_allocated\' OR ((SUBSTRING(COLUMN_NAME, -5) = \'_read\' OR SUBSTRING(COLUMN_NAME, -8) = \'_written\' OR SUBSTRING(COLUMN_NAME, -6) = \'_write\') AND SUBSTRING(COLUMN_NAME, 1, 6) <> \'COUNT_\') THEN CONCAT(\'sys.format_bytes(e.\', COLUMN_NAME, \' - IFNULL(s.\', COLUMN_NAME, \', 0)) AS \', COLUMN_NAME) ELSE CONCAT(\'(e.\', COLUMN_NAME, \' - IFNULL(s.\', COLUMN_NAME, \', 0)) AS \', COLUMN_NAME) END ORDER BY ORDINAL_POSITION SEPARATOR \',\n       \' ), \'\n  FROM tmp_\', diag.TABLE_NAME, \'_end e LEFT OUTER JOIN tmp_\', diag.TABLE_NAME, \'_start s USING (\', diag.pk, \')\' ) AS Query INTO @sys.diagnostics.sql_select FROM tmp_sys_views_delta diag INNER JOIN information_schema.COLUMNS c ON c.TABLE_NAME = CONCAT(\'x$\', diag.TABLE_NAME) WHERE c.TABLE_SCHEMA = \'sys\' AND diag.TABLE_NAME = ? GROUP BY diag.TABLE_NAME\';  IF (v_has_ps = \'YES\') THEN DROP TEMPORARY TABLE IF EXISTS tmp_sys_views_delta; CREATE TEMPORARY TABLE tmp_sys_views_delta ( TABLE_NAME varchar(64) NOT NULL, order_by text COMMENT \'ORDER BY clause for the initial and overall views\', order_by_delta text COMMENT \'ORDER BY clause for the delta views\', where_delta text COMMENT \'WHERE clause to use for delta views to only include rows with a \"count\" > 0\', limit_rows int unsigned COMMENT \'The maximum number of rows to include for the view\', pk varchar(128) COMMENT \'Used with the FIND_IN_SET() function so use comma separated list without whitespace\', PRIMARY KEY (TABLE_NAME) );  IF (@sys.debug = \'ON\') THEN SELECT \'Populating tmp_sys_views_delta\' AS \'Debug\'; END IF; INSERT INTO tmp_sys_views_delta VALUES (\'host_summary\'                       , \'%{TABLE}.statement_latency DESC\', \'(e.statement_latency-IFNULL(s.statement_latency, 0)) DESC\', \'(e.statements - IFNULL(s.statements, 0)) > 0\', NULL, \'host\'), (\'host_summary_by_file_io\'            , \'%{TABLE}.io_latency DESC\', \'(e.io_latency-IFNULL(s.io_latency, 0)) DESC\', \'(e.ios - IFNULL(s.ios, 0)) > 0\', NULL, \'host\'), (\'host_summary_by_file_io_type\'       , \'%{TABLE}.host, %{TABLE}.total_latency DESC\', \'e.host, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'host,event_name\'), (\'host_summary_by_stages\'             , \'%{TABLE}.host, %{TABLE}.total_latency DESC\', \'e.host, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'host,event_name\'), (\'host_summary_by_statement_latency\'  , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'host\'), (\'host_summary_by_statement_type\'     , \'%{TABLE}.host, %{TABLE}.total_latency DESC\', \'e.host, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'host,statement\'), (\'io_by_thread_by_latency\'            , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'user,thread_id,processlist_id\'), (\'io_global_by_file_by_bytes\'         , \'%{TABLE}.total DESC\', \'(e.total-IFNULL(s.total, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', 100, \'file\'), (\'io_global_by_file_by_latency\'       , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', 100, \'file\'), (\'io_global_by_wait_by_bytes\'         , \'%{TABLE}.total_requested DESC\', \'(e.total_requested-IFNULL(s.total_requested, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'event_name\'), (\'io_global_by_wait_by_latency\'       , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'event_name\'), (\'schema_index_statistics\'            , \'(%{TABLE}.select_latency+%{TABLE}.insert_latency+%{TABLE}.update_latency+%{TABLE}.delete_latency) DESC\', \'((e.select_latency+e.insert_latency+e.update_latency+e.delete_latency)-IFNULL(s.select_latency+s.insert_latency+s.update_latency+s.delete_latency, 0)) DESC\', \'((e.rows_selected+e.insert_latency+e.rows_updated+e.rows_deleted)-IFNULL(s.rows_selected+s.rows_inserted+s.rows_updated+s.rows_deleted, 0)) > 0\', 100, \'table_schema,table_name,index_name\'), (\'schema_table_statistics\'            , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) > 0\', 100, \'table_schema,table_name\'), (\'schema_tables_with_full_table_scans\', \'%{TABLE}.rows_full_scanned DESC\', \'(e.rows_full_scanned-IFNULL(s.rows_full_scanned, 0)) DESC\', \'(e.rows_full_scanned-IFNULL(s.rows_full_scanned, 0)) > 0\', 100, \'object_schema,object_name\'), (\'user_summary\'                       , \'%{TABLE}.statement_latency DESC\', \'(e.statement_latency-IFNULL(s.statement_latency, 0)) DESC\', \'(e.statements - IFNULL(s.statements, 0)) > 0\', NULL, \'user\'), (\'user_summary_by_file_io\'            , \'%{TABLE}.io_latency DESC\', \'(e.io_latency-IFNULL(s.io_latency, 0)) DESC\', \'(e.ios - IFNULL(s.ios, 0)) > 0\', NULL, \'user\'), (\'user_summary_by_file_io_type\'       , \'%{TABLE}.user, %{TABLE}.latency DESC\', \'e.user, (e.latency-IFNULL(s.latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'user,event_name\'), (\'user_summary_by_stages\'             , \'%{TABLE}.user, %{TABLE}.total_latency DESC\', \'e.user, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'user,event_name\'), (\'user_summary_by_statement_latency\'  , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'user\'), (\'user_summary_by_statement_type\'     , \'%{TABLE}.user, %{TABLE}.total_latency DESC\', \'e.user, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'user,statement\'), (\'wait_classes_global_by_avg_latency\' , \'IFNULL(%{TABLE}.total_latency / NULLIF(%{TABLE}.total, 0), 0) DESC\', \'IFNULL((e.total_latency-IFNULL(s.total_latency, 0)) / NULLIF((e.total - IFNULL(s.total, 0)), 0), 0) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'event_class\'), (\'wait_classes_global_by_latency\'     , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'event_class\'), (\'waits_by_host_by_latency\'           , \'%{TABLE}.host, %{TABLE}.total_latency DESC\', \'e.host, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'host,event\'), (\'waits_by_user_by_latency\'           , \'%{TABLE}.user, %{TABLE}.total_latency DESC\', \'e.user, (e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'user,event\'), (\'waits_global_by_latency\'            , \'%{TABLE}.total_latency DESC\', \'(e.total_latency-IFNULL(s.total_latency, 0)) DESC\', \'(e.total - IFNULL(s.total, 0)) > 0\', NULL, \'events\') ; END IF;   SELECT \'  =======================  Configuration  =======================  \' AS \'\'; SELECT \'GLOBAL VARIABLES\' AS \'The following output is:\'; IF (v_has_ps_vars = \'YES\') THEN SELECT LOWER(VARIABLE_NAME) AS Variable_name, VARIABLE_VALUE AS Variable_value FROM performance_schema.global_variables ORDER BY VARIABLE_NAME; ELSE SELECT LOWER(VARIABLE_NAME) AS Variable_name, VARIABLE_VALUE AS Variable_value FROM information_schema.GLOBAL_VARIABLES ORDER BY VARIABLE_NAME; END IF;  IF (v_has_ps = \'YES\') THEN SELECT \'Performance Schema Setup - Actors\' AS \'The following output is:\'; SELECT * FROM performance_schema.setup_actors;  SELECT \'Performance Schema Setup - Consumers\' AS \'The following output is:\'; SELECT NAME AS Consumer, ENABLED, sys.ps_is_consumer_enabled(NAME) AS COLLECTS FROM performance_schema.setup_consumers;  SELECT \'Performance Schema Setup - Instruments\' AS \'The following output is:\'; SELECT SUBSTRING_INDEX(NAME, \'/\', 2) AS \'InstrumentClass\', ROUND(100*SUM(IF(ENABLED = \'YES\', 1, 0))/COUNT(*), 2) AS \'EnabledPct\', ROUND(100*SUM(IF(TIMED = \'YES\', 1, 0))/COUNT(*), 2) AS \'TimedPct\' FROM performance_schema.setup_instruments GROUP BY SUBSTRING_INDEX(NAME, \'/\', 2) ORDER BY SUBSTRING_INDEX(NAME, \'/\', 2);  SELECT \'Performance Schema Setup - Objects\' AS \'The following output is:\'; SELECT * FROM performance_schema.setup_objects;  SELECT \'Performance Schema Setup - Threads\' AS \'The following output is:\'; SELECT `TYPE` AS ThreadType, COUNT(*) AS \'Total\', ROUND(100*SUM(IF(INSTRUMENTED = \'YES\', 1, 0))/COUNT(*), 2) AS \'InstrumentedPct\' FROM performance_schema.threads GROUP BY TYPE; END IF;   IF (v_has_replication = \'NO\') THEN SELECT \'No Replication Configured\' AS \'Replication Status\'; ELSE SELECT CONCAT(\'Replication Configured: \', v_has_replication, \' - Performance Schema Replication Tables: \', v_has_ps_replication) AS \'Replication Status\';  IF (v_has_ps_replication = \'YES\') THEN SELECT \'Replication - Connection Configuration\' AS \'The following output is:\'; SELECT * FROM performance_schema.replication_connection_configuration ORDER BY CHANNEL_NAME ; END IF;  IF (v_has_ps_replication = \'YES\') THEN SELECT \'Replication - Applier Configuration\' AS \'The following output is:\'; SELECT * FROM performance_schema.replication_applier_configuration ORDER BY CHANNEL_NAME; END IF;  IF (@@master_info_repository = \'TABLE\') THEN SELECT \'Replication - Master Info Repository Configuration\' AS \'The following output is:\'; SELECT  Channel_name, Host, User_name, Port, Connect_retry, Enabled_ssl, Ssl_ca, Ssl_capath, Ssl_cert, Ssl_cipher, Ssl_key, Ssl_verify_server_cert, Heartbeat, Bind, Ignored_server_ids, Uuid, Retry_count, Ssl_crl, Ssl_crlpath, Tls_version, Enabled_auto_position FROM mysql.slave_master_info ORDER BY Channel_name ; END IF;  IF (@@relay_log_info_repository = \'TABLE\') THEN SELECT \'Replication - Relay Log Repository Configuration\' AS \'The following output is:\'; SELECT  Channel_name, Sql_delay, Number_of_workers, Id FROM mysql.slave_relay_log_info ORDER BY Channel_name ; END IF; END IF;   IF (v_has_ndb IN (\'DEFAULT\', \'YES\')) THEN SELECT \'Cluster Thread Blocks\' AS \'The following output is:\'; SELECT * FROM ndbinfo.threadblocks; END IF;  IF (v_has_ps = \'YES\') THEN IF (@sys.diagnostics.include_raw = \'ON\') THEN SELECT \'  ========================  Initial Status  ========================  \' AS \'\'; END IF;  DROP TEMPORARY TABLE IF EXISTS tmp_digests_start; CALL sys.statement_performance_analyzer(\'create_tmp\', \'tmp_digests_start\', NULL); CALL sys.statement_performance_analyzer(\'snapshot\', NULL, NULL); CALL sys.statement_performance_analyzer(\'save\', \'tmp_digests_start\', NULL);  IF (@sys.diagnostics.include_raw = \'ON\') THEN SET @sys.diagnostics.sql = REPLACE(@sys.diagnostics.sql_gen_query_template, \'%{OUTPUT}\', \'start\'); IF (@sys.debug = \'ON\') THEN SELECT \'The following query will be used to generate the query for each sys view\' AS \'Debug\'; SELECT @sys.diagnostics.sql AS \'Debug\'; END IF; PREPARE stmt_gen_query FROM @sys.diagnostics.sql; END IF; SET v_done = FALSE; OPEN c_sysviews_w_delta; c_sysviews_w_delta_loop: LOOP FETCH c_sysviews_w_delta INTO v_table_name; IF v_done THEN LEAVE c_sysviews_w_delta_loop; END IF;  IF (@sys.debug = \'ON\') THEN SELECT CONCAT(\'The following queries are for storing the initial content of \', v_table_name) AS \'Debug\'; END IF;  CALL sys.execute_prepared_stmt(CONCAT(\'DROP TEMPORARY TABLE IF EXISTS `tmp_\', v_table_name, \'_start`\')); CALL sys.execute_prepared_stmt(CONCAT(\'CREATE TEMPORARY TABLE `tmp_\', v_table_name, \'_start` SELECT * FROM `sys`.`x$\', v_table_name, \'`\'));  IF (@sys.diagnostics.include_raw = \'ON\') THEN SET @sys.diagnostics.table_name = CONCAT(\'x$\', v_table_name); EXECUTE stmt_gen_query USING @sys.diagnostics.table_name; SELECT CONCAT(@sys.diagnostics.sql_select, IF(order_by IS NOT NULL, CONCAT(\'\n ORDER BY \', REPLACE(order_by, \'%{TABLE}\', CONCAT(\'tmp_\', v_table_name, \'_start\'))), \'\'), IF(limit_rows IS NOT NULL, CONCAT(\'\n LIMIT \', limit_rows), \'\') ) INTO @sys.diagnostics.sql_select FROM tmp_sys_views_delta WHERE TABLE_NAME = v_table_name; SELECT CONCAT(\'Initial \', v_table_name) AS \'The following output is:\'; CALL sys.execute_prepared_stmt(@sys.diagnostics.sql_select); END IF; END LOOP; CLOSE c_sysviews_w_delta;  IF (@sys.diagnostics.include_raw = \'ON\') THEN DEALLOCATE PREPARE stmt_gen_query; END IF; END IF;  SET v_sql_status_summary_select = \'SELECT Variable_name\', v_sql_status_summary_delta  = \'\', v_sql_status_summary_from   = \'\';  REPEAT SET v_output_count = v_output_count + 1; IF (v_output_count > 1) THEN SET v_sleep = in_interval-(UNIX_TIMESTAMP(NOW(2))-v_iter_start); SELECT NOW() AS \'Time\', CONCAT(\'Going to sleep for \', v_sleep, \' seconds. Please do not interrupt\') AS \'The following output is:\'; DO SLEEP(in_interval); END IF; SET v_iter_start = UNIX_TIMESTAMP(NOW(2));  SELECT NOW(), CONCAT(\'Iteration Number \', IFNULL(v_output_count, \'NULL\')) AS \'The following output is:\';  IF (@@log_bin = 1) THEN SELECT \'SHOW MASTER STATUS\' AS \'The following output is:\'; SHOW MASTER STATUS; END IF;  IF (v_has_replication <> \'NO\') THEN SELECT \'SHOW SLAVE STATUS\' AS \'The following output is:\'; SHOW SLAVE STATUS;  IF (v_has_ps_replication = \'YES\') THEN SELECT \'Replication Connection Status\' AS \'The following output is:\'; SELECT * FROM performance_schema.replication_connection_status;  SELECT \'Replication Applier Status\' AS \'The following output is:\'; SELECT * FROM performance_schema.replication_applier_status ORDER BY CHANNEL_NAME;  SELECT \'Replication Applier Status - Coordinator\' AS \'The following output is:\'; SELECT * FROM performance_schema.replication_applier_status_by_coordinator ORDER BY CHANNEL_NAME;  SELECT \'Replication Applier Status - Worker\' AS \'The following output is:\'; SELECT * FROM performance_schema.replication_applier_status_by_worker ORDER BY CHANNEL_NAME, WORKER_ID; END IF;  IF (@@master_info_repository = \'TABLE\') THEN SELECT \'Replication - Master Log Status\' AS \'The following output is:\'; SELECT Master_log_name, Master_log_pos FROM mysql.slave_master_info; END IF;  IF (@@relay_log_info_repository = \'TABLE\') THEN SELECT \'Replication - Relay Log Status\' AS \'The following output is:\'; SELECT sys.format_path(Relay_log_name) AS Relay_log_name, Relay_log_pos, Master_log_name, Master_log_pos FROM mysql.slave_relay_log_info;  SELECT \'Replication - Worker Status\' AS \'The following output is:\'; SELECT Id, sys.format_path(Relay_log_name) AS Relay_log_name, Relay_log_pos, Master_log_name, Master_log_pos, sys.format_path(Checkpoint_relay_log_name) AS Checkpoint_relay_log_name, Checkpoint_relay_log_pos, Checkpoint_master_log_name, Checkpoint_master_log_pos, Checkpoint_seqno, Checkpoint_group_size, HEX(Checkpoint_group_bitmap) AS Checkpoint_group_bitmap , Channel_name FROM mysql.slave_worker_info ORDER BY  Channel_name, Id; END IF; END IF;  SET v_table_name = CONCAT(\'tmp_metrics_\', v_output_count); CALL sys.execute_prepared_stmt(CONCAT(\'DROP TEMPORARY TABLE IF EXISTS \', v_table_name));  CALL sys.execute_prepared_stmt(CONCAT(\'CREATE TEMPORARY TABLE \', v_table_name, \' ( Variable_name VARCHAR(193) NOT NULL, Variable_value VARCHAR(1024), Type VARCHAR(225) NOT NULL, Enabled ENUM(\'YES\', \'NO\', \'PARTIAL\') NOT NULL, PRIMARY KEY (Type, Variable_name) ) ENGINE = InnoDB DEFAULT CHARSET=utf8mb3\'));  IF (v_has_metrics) THEN SET @sys.diagnostics.sql = CONCAT( \'INSERT INTO \', v_table_name, \' SELECT Variable_name, REPLACE(Variable_value, \'\n\', \'\\\\n\') AS Variable_value, Type, Enabled FROM sys.metrics\' ); ELSE SET @sys.diagnostics.sql = CONCAT( \'INSERT INTO \', v_table_name, \'(SELECT LOWER(VARIABLE_NAME) AS Variable_name, REPLACE(VARIABLE_VALUE, \'\n\', \'\\\\n\') AS Variable_value, \'Global Status\' AS Type, \'YES\' AS Enabled FROM performance_schema.global_status ) UNION ALL ( SELECT NAME AS Variable_name, COUNT AS Variable_value, CONCAT(\'InnoDB Metrics - \', SUBSYSTEM) AS Type, IF(STATUS = \'enabled\', \'YES\', \'NO\') AS Enabled FROM information_schema.INNODB_METRICS WHERE NAME NOT IN ( \'lock_row_lock_time\', \'lock_row_lock_time_avg\', \'lock_row_lock_time_max\', \'lock_row_lock_waits\', \'buffer_pool_reads\', \'buffer_pool_read_requests\', \'buffer_pool_write_requests\', \'buffer_pool_wait_free\', \'buffer_pool_read_ahead\', \'buffer_pool_read_ahead_evicted\', \'buffer_pool_pages_total\', \'buffer_pool_pages_misc\', \'buffer_pool_pages_data\', \'buffer_pool_bytes_data\', \'buffer_pool_pages_dirty\', \'buffer_pool_bytes_dirty\', \'buffer_pool_pages_free\', \'buffer_pages_created\', \'buffer_pages_written\', \'buffer_pages_read\', \'buffer_data_reads\', \'buffer_data_written\', \'file_num_open_files\', \'os_log_bytes_written\', \'os_log_fsyncs\', \'os_log_pending_fsyncs\', \'os_log_pending_writes\', \'log_waits\', \'log_write_requests\', \'log_writes\', \'innodb_dblwr_writes\', \'innodb_dblwr_pages_written\', \'innodb_page_size\') ) UNION ALL ( SELECT \'NOW()\' AS Variable_name, NOW(3) AS Variable_value, \'System Time\' AS Type, \'YES\' AS Enabled ) UNION ALL ( SELECT \'UNIX_TIMESTAMP()\' AS Variable_name, ROUND(UNIX_TIMESTAMP(NOW(3)), 3) AS Variable_value, \'System Time\' AS Type, \'YES\' AS Enabled ) ORDER BY Type, Variable_name;\' ); END IF; CALL sys.execute_prepared_stmt(@sys.diagnostics.sql);  CALL sys.execute_prepared_stmt( CONCAT(\'(SELECT Variable_value INTO @sys.diagnostics.output_time FROM \', v_table_name, \' WHERE Type = \'System Time\' AND Variable_name = \'UNIX_TIMESTAMP()\')\') ); SET v_output_time = @sys.diagnostics.output_time;  SET v_sql_status_summary_select = CONCAT(v_sql_status_summary_select, \', CONCAT( LEFT(s\', v_output_count, \'.Variable_value, \', v_status_summary_width, \'), IF(\', REPLACE(v_no_delta_names, \'%{COUNT}\', v_output_count), \' AND s\', v_output_count, \'.Variable_value REGEXP \'^[0-9]+(\\\\.[0-9]+)?$\', CONCAT(\' (\', ROUND(s\', v_output_count, \'.Variable_value/\', v_output_time, \', 2), \'/sec)\'), \'\') ) AS \'Output \', v_output_count, \'\'\'), v_sql_status_summary_from   = CONCAT(v_sql_status_summary_from, \' \', IF(v_output_count = 1, \'  FROM \', \'       INNER JOIN \'), v_table_name, \' s\', v_output_count, IF (v_output_count = 1, \'\', \' USING (Type, Variable_name)\')); IF (v_output_count > 1) THEN SET v_sql_status_summary_delta  = CONCAT(v_sql_status_summary_delta, \', IF(\', REPLACE(v_no_delta_names, \'%{COUNT}\', v_output_count), \' AND s\', (v_output_count-1), \'.Variable_value REGEXP \'^[0-9]+(\\\\.[0-9]+)?$\' AND s\', v_output_count, \'.Variable_value REGEXP \'^[0-9]+(\\\\.[0-9]+)?$\', CONCAT(IF(s\', (v_output_count-1), \'.Variable_value REGEXP \'^[0-9]+\\\\.[0-9]+$\' OR s\', v_output_count, \'.Variable_value REGEXP \'^[0-9]+\\\\.[0-9]+$\', ROUND((s\', v_output_count, \'.Variable_value-s\', (v_output_count-1), \'.Variable_value), 2), (s\', v_output_count, \'.Variable_value-s\', (v_output_count-1), \'.Variable_value) ), \' (\', ROUND((s\', v_output_count, \'.Variable_value-s\', (v_output_count-1), \'.Variable_value)/(\', v_output_time, \'-\', v_output_time_prev, \'), 2), \'/sec)\' ), \'\' ) AS \'Delta (\', (v_output_count-1), \' -> \', v_output_count, \')\'\'); END IF;  SET v_output_time_prev = v_output_time;  IF (@sys.diagnostics.include_raw = \'ON\') THEN IF (v_has_metrics) THEN SELECT \'SELECT * FROM sys.metrics\' AS \'The following output is:\'; ELSE SELECT \'sys.metrics equivalent\' AS \'The following output is:\'; END IF; CALL sys.execute_prepared_stmt(CONCAT(\'SELECT Type, Variable_name, Enabled, Variable_value FROM \', v_table_name, \' ORDER BY Type, Variable_name\')); END IF;  IF (v_has_innodb IN (\'DEFAULT\', \'YES\')) THEN SELECT \'SHOW ENGINE INNODB STATUS\' AS \'The following output is:\'; EXECUTE stmt_innodb_status; SELECT \'InnoDB - Transactions\' AS \'The following output is:\'; SELECT * FROM information_schema.INNODB_TRX; END IF;  IF (v_has_ndb IN (\'DEFAULT\', \'YES\')) THEN SELECT \'SHOW ENGINE NDBCLUSTER STATUS\' AS \'The following output is:\'; EXECUTE stmt_ndbcluster_status;  SELECT \'ndbinfo.memoryusage\' AS \'The following output is:\'; SELECT node_id, memory_type, sys.format_bytes(used) AS used, used_pages, sys.format_bytes(total) AS total, total_pages, ROUND(100*(used/total), 2) AS \'Used %\' FROM ndbinfo.memoryusage;  SET v_done = FALSE; OPEN c_ndbinfo; c_ndbinfo_loop: LOOP FETCH c_ndbinfo INTO v_table_name; IF v_done THEN LEAVE c_ndbinfo_loop; END IF;  SELECT CONCAT(\'SELECT * FROM ndbinfo.\', v_table_name) AS \'The following output is:\'; CALL sys.execute_prepared_stmt(CONCAT(\'SELECT * FROM `ndbinfo`.`\', v_table_name, \'`\')); END LOOP; CLOSE c_ndbinfo;  SELECT * FROM information_schema.FILES; END IF;  SELECT \'SELECT * FROM sys.processlist\' AS \'The following output is:\'; SELECT processlist.* FROM sys.processlist;  IF (v_has_ps = \'YES\') THEN IF (sys.ps_is_consumer_enabled(\'events_waits_history_long\') = \'YES\') THEN SELECT \'SELECT * FROM sys.latest_file_io\' AS \'The following output is:\'; SELECT * FROM sys.latest_file_io; END IF;  IF (EXISTS(SELECT 1 FROM performance_schema.setup_instruments WHERE NAME LIKE \'memory/%\' AND ENABLED = \'YES\')) THEN SELECT \'SELECT * FROM sys.memory_by_host_by_current_bytes\' AS \'The following output is:\'; SELECT * FROM sys.memory_by_host_by_current_bytes;  SELECT \'SELECT * FROM sys.memory_by_thread_by_current_bytes\' AS \'The following output is:\'; SELECT * FROM sys.memory_by_thread_by_current_bytes;  SELECT \'SELECT * FROM sys.memory_by_user_by_current_bytes\' AS \'The following output is:\'; SELECT * FROM sys.memory_by_user_by_current_bytes;  SELECT \'SELECT * FROM sys.memory_global_by_current_bytes\' AS \'The following output is:\'; SELECT * FROM sys.memory_global_by_current_bytes; END IF; END IF;  SET v_runtime = (UNIX_TIMESTAMP(NOW(2)) - v_start); UNTIL (v_runtime + in_interval >= in_max_runtime) END REPEAT;  IF (v_has_ps = \'YES\') THEN SELECT \'SHOW ENGINE PERFORMANCE_SCHEMA STATUS\' AS \'The following output is:\'; EXECUTE stmt_ps_status; END IF;  IF (v_has_innodb IN (\'DEFAULT\', \'YES\')) THEN DEALLOCATE PREPARE stmt_innodb_status; END IF; IF (v_has_ps = \'YES\') THEN DEALLOCATE PREPARE stmt_ps_status; END IF; IF (v_has_ndb IN (\'DEFAULT\', \'YES\')) THEN DEALLOCATE PREPARE stmt_ndbcluster_status; END IF;   SELECT \'  ============================  Schema Information  ============================  \' AS \'\';  SELECT COUNT(*) AS \'Total Number of Tables\' FROM information_schema.TABLES;  IF (@sys.diagnostics.allow_i_s_tables = \'ON\') THEN SELECT \'Storage Engine Usage\' AS \'The following output is:\'; SELECT ENGINE, COUNT(*) AS NUM_TABLES, sys.format_bytes(SUM(DATA_LENGTH)) AS DATA_LENGTH, sys.format_bytes(SUM(INDEX_LENGTH)) AS INDEX_LENGTH, sys.format_bytes(SUM(DATA_LENGTH+INDEX_LENGTH)) AS TOTAL FROM information_schema.TABLES GROUP BY ENGINE;  SELECT \'Schema Object Overview\' AS \'The following output is:\'; SELECT * FROM sys.schema_object_overview;  SELECT \'Tables without a PRIMARY KEY\' AS \'The following output is:\'; SELECT TABLES.TABLE_SCHEMA, ENGINE, COUNT(*) AS NumTables FROM information_schema.TABLES LEFT OUTER JOIN information_schema.STATISTICS ON STATISTICS.TABLE_SCHEMA = TABLES.TABLE_SCHEMA AND STATISTICS.TABLE_NAME = TABLES.TABLE_NAME AND STATISTICS.INDEX_NAME = \'PRIMARY\' WHERE STATISTICS.TABLE_NAME IS NULL AND TABLES.TABLE_SCHEMA NOT IN (\'mysql\', \'information_schema\', \'performance_schema\', \'sys\') AND TABLES.TABLE_TYPE = \'BASE TABLE\' GROUP BY TABLES.TABLE_SCHEMA, ENGINE; END IF;  IF (v_has_ps = \'YES\') THEN SELECT \'Unused Indexes\' AS \'The following output is:\'; SELECT object_schema, COUNT(*) AS NumUnusedIndexes FROM performance_schema.table_io_waits_summary_by_index_usage WHERE index_name IS NOT NULL AND count_star = 0 AND object_schema NOT IN (\'mysql\', \'sys\') AND index_name != \'PRIMARY\' GROUP BY object_schema; END IF;  IF (v_has_ps = \'YES\') THEN SELECT \'  =========================  Overall Status  =========================  \' AS \'\';  SELECT \'CALL sys.ps_statement_avg_latency_histogram()\' AS \'The following output is:\'; CALL sys.ps_statement_avg_latency_histogram();  CALL sys.statement_performance_analyzer(\'snapshot\', NULL, NULL); CALL sys.statement_performance_analyzer(\'overall\', NULL, \'with_runtimes_in_95th_percentile\');  SET @sys.diagnostics.sql = REPLACE(@sys.diagnostics.sql_gen_query_template, \'%{OUTPUT}\', \'end\'); IF (@sys.debug = \'ON\') THEN SELECT \'The following query will be used to generate the query for each sys view\' AS \'Debug\'; SELECT @sys.diagnostics.sql AS \'Debug\'; END IF; PREPARE stmt_gen_query FROM @sys.diagnostics.sql;  SET v_done = FALSE; OPEN c_sysviews_w_delta; c_sysviews_w_delta_loop: LOOP FETCH c_sysviews_w_delta INTO v_table_name; IF v_done THEN LEAVE c_sysviews_w_delta_loop; END IF;  IF (@sys.debug = \'ON\') THEN SELECT CONCAT(\'The following queries are for storing the final content of \', v_table_name) AS \'Debug\'; END IF;  CALL sys.execute_prepared_stmt(CONCAT(\'DROP TEMPORARY TABLE IF EXISTS `tmp_\', v_table_name, \'_end`\')); CALL sys.execute_prepared_stmt(CONCAT(\'CREATE TEMPORARY TABLE `tmp_\', v_table_name, \'_end` SELECT * FROM `sys`.`x$\', v_table_name, \'`\'));  SET @sys.diagnostics.table_name = CONCAT(\'x$\', v_table_name); EXECUTE stmt_gen_query USING @sys.diagnostics.table_name; SELECT CONCAT(@sys.diagnostics.sql_select, IF(order_by IS NOT NULL, CONCAT(\'\n ORDER BY \', REPLACE(order_by, \'%{TABLE}\', CONCAT(\'tmp_\', v_table_name, \'_end\'))), \'\'), IF(limit_rows IS NOT NULL, CONCAT(\'\n LIMIT \', limit_rows), \'\') ) INTO @sys.diagnostics.sql_select FROM tmp_sys_views_delta WHERE TABLE_NAME = v_table_name; SELECT CONCAT(\'Overall \', v_table_name) AS \'The following output is:\'; CALL sys.execute_prepared_stmt(@sys.diagnostics.sql_select); END LOOP; CLOSE c_sysviews_w_delta;  DEALLOCATE PREPARE stmt_gen_query;   SELECT \'  ======================  Delta Status  ======================  \' AS \'\';  CALL sys.statement_performance_analyzer(\'delta\', \'tmp_digests_start\', \'with_runtimes_in_95th_percentile\'); CALL sys.statement_performance_analyzer(\'cleanup\', NULL, NULL);  DROP TEMPORARY TABLE tmp_digests_start;  IF (@sys.debug = \'ON\') THEN SELECT \'The following query will be used to generate the query for each sys view delta\' AS \'Debug\'; SELECT @sys.diagnostics.sql_gen_query_delta AS \'Debug\'; END IF; PREPARE stmt_gen_query_delta FROM @sys.diagnostics.sql_gen_query_delta;  SET v_old_group_concat_max_len = @@session.group_concat_max_len; SET @@session.group_concat_max_len = 2048; SET v_done = FALSE; OPEN c_sysviews_w_delta; c_sysviews_w_delta_loop: LOOP FETCH c_sysviews_w_delta INTO v_table_name; IF v_done THEN LEAVE c_sysviews_w_delta_loop; END IF;  SET @sys.diagnostics.table_name = v_table_name; EXECUTE stmt_gen_query_delta USING @sys.diagnostics.table_name; SELECT CONCAT(@sys.diagnostics.sql_select, IF(where_delta IS NOT NULL, CONCAT(\'\n WHERE \', where_delta), \'\'), IF(order_by_delta IS NOT NULL, CONCAT(\'\n ORDER BY \', order_by_delta), \'\'), IF(limit_rows IS NOT NULL, CONCAT(\'\n LIMIT \', limit_rows), \'\') ) INTO @sys.diagnostics.sql_select FROM tmp_sys_views_delta WHERE TABLE_NAME = v_table_name;  SELECT CONCAT(\'Delta \', v_table_name) AS \'The following output is:\'; CALL sys.execute_prepared_stmt(@sys.diagnostics.sql_select);  CALL sys.execute_prepared_stmt(CONCAT(\'DROP TEMPORARY TABLE `tmp_\', v_table_name, \'_end`\')); CALL sys.execute_prepared_stmt(CONCAT(\'DROP TEMPORARY TABLE `tmp_\', v_table_name, \'_start`\')); END LOOP; CLOSE c_sysviews_w_delta; SET @@session.group_concat_max_len = v_old_group_concat_max_len;  DEALLOCATE PREPARE stmt_gen_query_delta; DROP TEMPORARY TABLE tmp_sys_views_delta; END IF;  IF (v_has_metrics) THEN SELECT \'SELECT * FROM sys.metrics\' AS \'The following output is:\'; ELSE SELECT \'sys.metrics equivalent\' AS \'The following output is:\'; END IF; CALL sys.execute_prepared_stmt( CONCAT(v_sql_status_summary_select, v_sql_status_summary_delta, \', Type, s1.Enabled\', v_sql_status_summary_from, \' ORDER BY Type, Variable_name\' ) );  SET v_count = 0; WHILE (v_count < v_output_count) DO SET v_count = v_count + 1; SET v_table_name = CONCAT(\'tmp_metrics_\', v_count); CALL sys.execute_prepared_stmt(CONCAT(\'DROP TEMPORARY TABLE IF EXISTS \', v_table_name)); END WHILE;  IF (in_auto_config <> \'current\') THEN CALL sys.ps_setup_reload_saved(); SET sql_log_bin = @log_bin; END IF;  SET @sys.diagnostics.output_time            = NULL, @sys.diagnostics.sql                    = NULL, @sys.diagnostics.sql_gen_query_delta    = NULL, @sys.diagnostics.sql_gen_query_template = NULL, @sys.diagnostics.sql_select             = NULL, @sys.diagnostics.table_name             = NULL;  IF (v_this_thread_enabled = \'YES\') THEN CALL sys.ps_setup_enable_thread(CONNECTION_ID()); END IF;  IF (@log_bin = 1) THEN SET sql_log_bin = @log_bin; END IF; END'),('sys','ps_statement_avg_latency_histogram','PROCEDURE','ps_statement_avg_latency_histogram','SQL','READS_SQL_DATA','NO','INVOKER','','',_binary 'BEGIN SELECT CONCAT(\'\\n\', \'\\n  . = 1 unit\', \'\\n  * = 2 units\', \'\\n  # = 3 units\\n\', @label := CONCAT(@label_inner := CONCAT(\'\\n(0 - \', ROUND((@bucket_size := (SELECT ROUND((MAX(avg_us) - MIN(avg_us)) / (@buckets := 16)) AS size FROM sys.x$ps_digest_avg_latency_distribution)) / (@unit_div := 1000)), (@unit := \'ms\'), \')\'), REPEAT(\' \', (@max_label_size := ((1 + LENGTH(ROUND((@bucket_size * 15) / @unit_div)) + 3 + LENGTH(ROUND(@bucket_size * 16) / @unit_div)) + 1)) - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us <= @bucket_size), 0)), REPEAT(\' \', (@max_label_len := (@max_label_size + LENGTH((@total_queries := (SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution)))) + 1) - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < (@one_unit := 40), \'.\', IF(@count_in_bucket < (@two_unit := 80), \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'),  @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND(@bucket_size / @unit_div), \' - \', ROUND((@bucket_size * 2) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size AND b1.avg_us <= @bucket_size * 2), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 2) / @unit_div), \' - \', ROUND((@bucket_size * 3) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 2 AND b1.avg_us <= @bucket_size * 3), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 3) / @unit_div), \' - \', ROUND((@bucket_size * 4) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 3 AND b1.avg_us <= @bucket_size * 4), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 4) / @unit_div), \' - \', ROUND((@bucket_size * 5) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 4 AND b1.avg_us <= @bucket_size * 5), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 5) / @unit_div), \' - \', ROUND((@bucket_size * 6) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 5 AND b1.avg_us <= @bucket_size * 6), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 6) / @unit_div), \' - \', ROUND((@bucket_size * 7) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 6 AND b1.avg_us <= @bucket_size * 7), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 7) / @unit_div), \' - \', ROUND((@bucket_size * 8) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 7 AND b1.avg_us <= @bucket_size * 8), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 8) / @unit_div), \' - \', ROUND((@bucket_size * 9) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 8 AND b1.avg_us <= @bucket_size * 9), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 9) / @unit_div), \' - \', ROUND((@bucket_size * 10) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 9 AND b1.avg_us <= @bucket_size * 10), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 10) / @unit_div), \' - \', ROUND((@bucket_size * 11) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 10 AND b1.avg_us <= @bucket_size * 11), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 11) / @unit_div), \' - \', ROUND((@bucket_size * 12) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 11 AND b1.avg_us <= @bucket_size * 12), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 12) / @unit_div), \' - \', ROUND((@bucket_size * 13) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 12 AND b1.avg_us <= @bucket_size * 13), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 13) / @unit_div), \' - \', ROUND((@bucket_size * 14) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 13 AND b1.avg_us <= @bucket_size * 14), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 14) / @unit_div), \' - \', ROUND((@bucket_size * 15) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 14 AND b1.avg_us <= @bucket_size * 15), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\\n(\', ROUND((@bucket_size * 15) / @unit_div), \' - \', ROUND((@bucket_size * 16) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 15 AND b1.avg_us <= @bucket_size * 16), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'),  \'\\n\\n  Total Statements: \', @total_queries, \'; Buckets: \', @buckets , \'; Bucket Size: \', ROUND(@bucket_size / @unit_div) , \' \', @unit, \';\\n\'  ) AS `Performance Schema Statement Digest Average Latency Histogram`;  END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Outputs a textual histogram graph of the average latency values\n across all normalized queries tracked within the Performance Schema\n events_statements_summary_by_digest table.\n \n Can be used to show a very high level picture of what kind of \n latency distribution statements running within this instance have.\n \n Parameters\n \n None.\n \n Example\n \n mysql> CALL sys.ps_statement_avg_latency_histogram()\\G\n *************************** 1. row ***************************\n Performance Schema Statement Digest Average Latency Histogram:\n \n . = 1 unit\n * = 2 units\n # = 3 units\n \n (0 - 38ms)     240 | ################################################################################\n (38 - 77ms)    38  | ......................................\n (77 - 115ms)   3   | ...\n (115 - 154ms)  62  | *******************************\n (154 - 192ms)  3   | ...\n (192 - 231ms)  0   |\n (231 - 269ms)  0   |\n (269 - 307ms)  0   |\n (307 - 346ms)  0   |\n (346 - 384ms)  1   | .\n (384 - 423ms)  1   | .\n (423 - 461ms)  0   |\n (461 - 499ms)  0   |\n (499 - 538ms)  0   |\n (538 - 576ms)  0   |\n (576 - 615ms)  1   | .\n \n Total Statements: 350; Buckets: 16; Bucket Size: 38 ms;\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN SELECT CONCAT(\'\n\', \'\n  . = 1 unit\', \'\n  * = 2 units\', \'\n  # = 3 units\n\', @label := CONCAT(@label_inner := CONCAT(\'\n(0 - \', ROUND((@bucket_size := (SELECT ROUND((MAX(avg_us) - MIN(avg_us)) / (@buckets := 16)) AS size FROM sys.x$ps_digest_avg_latency_distribution)) / (@unit_div := 1000)), (@unit := \'ms\'), \')\'), REPEAT(\' \', (@max_label_size := ((1 + LENGTH(ROUND((@bucket_size * 15) / @unit_div)) + 3 + LENGTH(ROUND(@bucket_size * 16) / @unit_div)) + 1)) - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us <= @bucket_size), 0)), REPEAT(\' \', (@max_label_len := (@max_label_size + LENGTH((@total_queries := (SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution)))) + 1) - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < (@one_unit := 40), \'.\', IF(@count_in_bucket < (@two_unit := 80), \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'),  @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND(@bucket_size / @unit_div), \' - \', ROUND((@bucket_size * 2) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size AND b1.avg_us <= @bucket_size * 2), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 2) / @unit_div), \' - \', ROUND((@bucket_size * 3) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 2 AND b1.avg_us <= @bucket_size * 3), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 3) / @unit_div), \' - \', ROUND((@bucket_size * 4) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 3 AND b1.avg_us <= @bucket_size * 4), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 4) / @unit_div), \' - \', ROUND((@bucket_size * 5) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 4 AND b1.avg_us <= @bucket_size * 5), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 5) / @unit_div), \' - \', ROUND((@bucket_size * 6) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 5 AND b1.avg_us <= @bucket_size * 6), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 6) / @unit_div), \' - \', ROUND((@bucket_size * 7) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 6 AND b1.avg_us <= @bucket_size * 7), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 7) / @unit_div), \' - \', ROUND((@bucket_size * 8) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 7 AND b1.avg_us <= @bucket_size * 8), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 8) / @unit_div), \' - \', ROUND((@bucket_size * 9) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 8 AND b1.avg_us <= @bucket_size * 9), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 9) / @unit_div), \' - \', ROUND((@bucket_size * 10) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 9 AND b1.avg_us <= @bucket_size * 10), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 10) / @unit_div), \' - \', ROUND((@bucket_size * 11) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 10 AND b1.avg_us <= @bucket_size * 11), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 11) / @unit_div), \' - \', ROUND((@bucket_size * 12) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 11 AND b1.avg_us <= @bucket_size * 12), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 12) / @unit_div), \' - \', ROUND((@bucket_size * 13) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 12 AND b1.avg_us <= @bucket_size * 13), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 13) / @unit_div), \' - \', ROUND((@bucket_size * 14) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 13 AND b1.avg_us <= @bucket_size * 14), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 14) / @unit_div), \' - \', ROUND((@bucket_size * 15) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 14 AND b1.avg_us <= @bucket_size * 15), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'), @label := CONCAT(@label_inner := CONCAT(\'\n(\', ROUND((@bucket_size * 15) / @unit_div), \' - \', ROUND((@bucket_size * 16) / @unit_div), @unit, \')\'), REPEAT(\' \', @max_label_size - LENGTH(@label_inner)), @count_in_bucket := IFNULL((SELECT SUM(cnt) FROM sys.x$ps_digest_avg_latency_distribution AS b1  WHERE b1.avg_us > @bucket_size * 15 AND b1.avg_us <= @bucket_size * 16), 0)), REPEAT(\' \', @max_label_len - LENGTH(@label)), \'| \', IFNULL(REPEAT(IF(@count_in_bucket < @one_unit, \'.\', IF(@count_in_bucket < @two_unit, \'*\', \'#\')),  	             IF(@count_in_bucket < @one_unit, @count_in_bucket, 	             	IF(@count_in_bucket < @two_unit, @count_in_bucket / 2, @count_in_bucket / 3))), \'\'),  \'\n\n  Total Statements: \', @total_queries, \'; Buckets: \', @buckets , \'; Bucket Size: \', ROUND(@bucket_size / @unit_div) , \' \', @unit, \';\n\'  ) AS `Performance Schema Statement Digest Average Latency Histogram`;  END'),('sys','ps_trace_statement_digest','PROCEDURE','ps_trace_statement_digest','SQL','MODIFIES_SQL_DATA','NO','INVOKER',_binary ' IN in_digest VARCHAR(32), IN in_runtime INT, IN in_interval DECIMAL(2,2), IN in_start_fresh BOOLEAN, IN in_auto_enable BOOLEAN ','',_binary 'BEGIN  DECLARE v_start_fresh BOOLEAN DEFAULT false; DECLARE v_auto_enable BOOLEAN DEFAULT false; DECLARE v_explain     BOOLEAN DEFAULT true; DECLARE v_this_thread_enabed ENUM(\'YES\', \'NO\'); DECLARE v_runtime INT DEFAULT 0; DECLARE v_start INT DEFAULT 0; DECLARE v_found_stmts INT;  SET @log_bin := @@sql_log_bin; SET sql_log_bin = 0;  SELECT INSTRUMENTED INTO v_this_thread_enabed FROM performance_schema.threads WHERE PROCESSLIST_ID = CONNECTION_ID(); CALL sys.ps_setup_disable_thread(CONNECTION_ID());  DROP TEMPORARY TABLE IF EXISTS stmt_trace; CREATE TEMPORARY TABLE stmt_trace ( thread_id BIGINT UNSIGNED, timer_start BIGINT UNSIGNED, event_id BIGINT UNSIGNED, sql_text longtext, timer_wait BIGINT UNSIGNED, lock_time BIGINT UNSIGNED, errors BIGINT UNSIGNED, mysql_errno INT, rows_sent BIGINT UNSIGNED, rows_affected BIGINT UNSIGNED, rows_examined BIGINT UNSIGNED, created_tmp_tables BIGINT UNSIGNED, created_tmp_disk_tables BIGINT UNSIGNED, no_index_used BIGINT UNSIGNED, PRIMARY KEY (thread_id, timer_start) );  DROP TEMPORARY TABLE IF EXISTS stmt_stages; CREATE TEMPORARY TABLE stmt_stages ( event_id BIGINT UNSIGNED, stmt_id BIGINT UNSIGNED, event_name VARCHAR(128), timer_wait BIGINT UNSIGNED, PRIMARY KEY (event_id) );  SET v_start_fresh = in_start_fresh; IF v_start_fresh THEN TRUNCATE TABLE performance_schema.events_statements_history_long; TRUNCATE TABLE performance_schema.events_stages_history_long; END IF;  SET v_auto_enable = in_auto_enable; IF v_auto_enable THEN CALL sys.ps_setup_save(0);  UPDATE performance_schema.threads SET INSTRUMENTED = IF(PROCESSLIST_ID IS NOT NULL, \'YES\', \'NO\');  UPDATE performance_schema.setup_consumers SET ENABLED = \'YES\' WHERE NAME NOT LIKE \'%\\_history\' AND NAME NOT LIKE \'events_wait%\' AND NAME NOT LIKE \'events_transactions%\' AND NAME <> \'statements_digest\';  UPDATE performance_schema.setup_instruments SET ENABLED = \'YES\', TIMED   = \'YES\' WHERE NAME LIKE \'statement/%\' OR NAME LIKE \'stage/%\'; END IF;  WHILE v_runtime < in_runtime DO SELECT UNIX_TIMESTAMP() INTO v_start;  INSERT IGNORE INTO stmt_trace SELECT thread_id, timer_start, event_id, sql_text, timer_wait, lock_time, errors, mysql_errno,  rows_sent, rows_affected, rows_examined, created_tmp_tables, created_tmp_disk_tables, no_index_used FROM performance_schema.events_statements_history_long WHERE digest = in_digest;  INSERT IGNORE INTO stmt_stages SELECT stages.event_id, stmt_trace.event_id, stages.event_name, stages.timer_wait FROM performance_schema.events_stages_history_long AS stages JOIN stmt_trace ON stages.nesting_event_id = stmt_trace.event_id;  SELECT SLEEP(in_interval) INTO @sleep; SET v_runtime = v_runtime + (UNIX_TIMESTAMP() - v_start); END WHILE;  SELECT \"SUMMARY STATISTICS\";  SELECT COUNT(*) executions, sys.format_time(SUM(timer_wait)) AS exec_time, sys.format_time(SUM(lock_time)) AS lock_time, SUM(rows_sent) AS rows_sent, SUM(rows_affected) AS rows_affected, SUM(rows_examined) AS rows_examined, SUM(created_tmp_tables) AS tmp_tables, SUM(no_index_used) AS full_scans FROM stmt_trace;  SELECT event_name, COUNT(*) as count, sys.format_time(SUM(timer_wait)) as latency FROM stmt_stages GROUP BY event_name ORDER BY SUM(timer_wait) DESC;  SELECT \"LONGEST RUNNING STATEMENT\";  SELECT thread_id, sys.format_time(timer_wait) AS exec_time, sys.format_time(lock_time) AS lock_time, rows_sent, rows_affected, rows_examined, created_tmp_tables AS tmp_tables, no_index_used AS full_scan FROM stmt_trace ORDER BY timer_wait DESC LIMIT 1;  SELECT sql_text FROM stmt_trace ORDER BY timer_wait DESC LIMIT 1;  SELECT sql_text, event_id INTO @sql, @sql_id FROM stmt_trace ORDER BY timer_wait DESC LIMIT 1;  IF (@sql_id IS NOT NULL) THEN SELECT event_name, sys.format_time(timer_wait) as latency FROM stmt_stages WHERE stmt_id = @sql_id ORDER BY event_id; END IF;  DROP TEMPORARY TABLE stmt_trace; DROP TEMPORARY TABLE stmt_stages;  IF (@sql IS NOT NULL) THEN SET @stmt := CONCAT(\"EXPLAIN FORMAT=JSON \", @sql); BEGIN DECLARE CONTINUE HANDLER FOR 1064, 1146 SET v_explain = false;  PREPARE explain_stmt FROM @stmt; END;  IF (v_explain) THEN EXECUTE explain_stmt; DEALLOCATE PREPARE explain_stmt; END IF; END IF;  IF v_auto_enable THEN CALL sys.ps_setup_reload_saved(); END IF; IF (v_this_thread_enabed = \'YES\') THEN CALL sys.ps_setup_enable_thread(CONNECTION_ID()); END IF;  SET sql_log_bin = @log_bin; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Traces all instrumentation within Performance Schema for a specific\n Statement Digest.\n \n When finding a statement of interest within the\n performance_schema.events_statements_summary_by_digest table, feed\n the DIGEST MD5 value in to this procedure, set how long to poll for,\n and at what interval to poll, and it will generate a report of all\n statistics tracked within Performance Schema for that digest for the\n interval.\n \n It will also attempt to generate an EXPLAIN for the longest running\n example of the digest during the interval. Note this may fail, as:\n \n * Performance Schema truncates long SQL_TEXT values (and hence the\n EXPLAIN will fail due to parse errors)\n * the default schema is sys (so tables that are not fully qualified\n in the query may not be found)\n * some queries such as SHOW are not supported in EXPLAIN.\n \n When the EXPLAIN fails, the error will be ignored and no EXPLAIN\n output generated.\n \n Requires the SUPER privilege for \"SET sql_log_bin = 0;\".\n \n Parameters\n \n in_digest (VARCHAR(32)):\n The statement digest identifier you would like to analyze\n in_runtime (INT):\n The number of seconds to run analysis for\n in_interval (DECIMAL(2,2)):\n The interval (in seconds, may be fractional) at which to try\n and take snapshots\n in_start_fresh (BOOLEAN):\n Whether to TRUNCATE the events_statements_history_long and\n events_stages_history_long tables before starting\n in_auto_enable (BOOLEAN):\n Whether to automatically turn on required consumers\n \n Example\n \n mysql> call ps_trace_statement_digest(\'891ec6860f98ba46d89dd20b0c03652c\', 10, 0.1, true, true);\n +--------------------+\n | SUMMARY STATISTICS |\n +--------------------+\n | SUMMARY STATISTICS |\n +--------------------+\n 1 row in set (9.11 sec)\n \n +------------+-----------+-----------+-----------+---------------+------------+------------+\n | executions | exec_time | lock_time | rows_sent | rows_examined | tmp_tables | full_scans |\n +------------+-----------+-----------+-----------+---------------+------------+------------+\n |         21 | 4.11 ms   | 2.00 ms   |         0 |            21 |          0 |          0 |\n +------------+-----------+-----------+-----------+---------------+------------+------------+\n 1 row in set (9.11 sec)\n \n +------------------------------------------+-------+-----------+\n | event_name                               | count | latency   |\n +------------------------------------------+-------+-----------+\n | stage/sql/checking query cache for query |    16 | 724.37 us |\n | stage/sql/statistics                     |    16 | 546.92 us |\n | stage/sql/freeing items                  |    18 | 520.11 us |\n | stage/sql/init                           |    51 | 466.80 us |\n ...\n | stage/sql/cleaning up                    |    18 | 11.92 us  |\n | stage/sql/executing                      |    16 | 6.95 us   |\n +------------------------------------------+-------+-----------+\n 17 rows in set (9.12 sec)\n \n +---------------------------+\n | LONGEST RUNNING STATEMENT |\n +---------------------------+\n | LONGEST RUNNING STATEMENT |\n +---------------------------+\n 1 row in set (9.16 sec)\n \n +-----------+-----------+-----------+-----------+---------------+------------+-----------+\n | thread_id | exec_time | lock_time | rows_sent | rows_examined | tmp_tables | full_scan |\n +-----------+-----------+-----------+-----------+---------------+------------+-----------+\n |    166646 | 618.43 us | 1.00 ms   |         0 |             1 |          0 |         0 |\n +-----------+-----------+-----------+-----------+---------------+------------+-----------+\n 1 row in set (9.16 sec)\n \n // Truncated for clarity...\n +-----------------------------------------------------------------+\n | sql_text                                                        |\n +-----------------------------------------------------------------+\n | select hibeventhe0_.id as id1382_, hibeventhe0_.createdTime ... |\n +-----------------------------------------------------------------+\n 1 row in set (9.17 sec)\n \n +------------------------------------------+-----------+\n | event_name                               | latency   |\n +------------------------------------------+-----------+\n | stage/sql/init                           | 8.61 us   |\n | stage/sql/Waiting for query cache lock   | 453.23 us |\n | stage/sql/init                           | 331.07 ns |\n | stage/sql/checking query cache for query | 43.04 us  |\n ...\n | stage/sql/freeing items                  | 30.46 us  |\n | stage/sql/cleaning up                    | 662.13 ns |\n +------------------------------------------+-----------+\n 18 rows in set (9.23 sec)\n \n +----+-------------+--------------+-------+---------------+-----------+---------+-------------+------+-------+\n | id | select_type | table        | type  | possible_keys | key       | key_len | ref         | rows | Extra |\n +----+-------------+--------------+-------+---------------+-----------+---------+-------------+------+-------+\n |  1 | SIMPLE      | hibeventhe0_ | const | fixedTime     | fixedTime | 775     | const,const |    1 | NULL  |\n +----+-------------+--------------+-------+---------------+-----------+---------+-------------+------+-------+\n 1 row in set (9.27 sec)\n \n Query OK, 0 rows affected (9.28 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN  DECLARE v_start_fresh BOOLEAN DEFAULT false; DECLARE v_auto_enable BOOLEAN DEFAULT false; DECLARE v_explain     BOOLEAN DEFAULT true; DECLARE v_this_thread_enabed ENUM(\'YES\', \'NO\'); DECLARE v_runtime INT DEFAULT 0; DECLARE v_start INT DEFAULT 0; DECLARE v_found_stmts INT;  SET @log_bin := @@sql_log_bin; SET sql_log_bin = 0;  SELECT INSTRUMENTED INTO v_this_thread_enabed FROM performance_schema.threads WHERE PROCESSLIST_ID = CONNECTION_ID(); CALL sys.ps_setup_disable_thread(CONNECTION_ID());  DROP TEMPORARY TABLE IF EXISTS stmt_trace; CREATE TEMPORARY TABLE stmt_trace ( thread_id BIGINT UNSIGNED, timer_start BIGINT UNSIGNED, event_id BIGINT UNSIGNED, sql_text longtext, timer_wait BIGINT UNSIGNED, lock_time BIGINT UNSIGNED, errors BIGINT UNSIGNED, mysql_errno INT, rows_sent BIGINT UNSIGNED, rows_affected BIGINT UNSIGNED, rows_examined BIGINT UNSIGNED, created_tmp_tables BIGINT UNSIGNED, created_tmp_disk_tables BIGINT UNSIGNED, no_index_used BIGINT UNSIGNED, PRIMARY KEY (thread_id, timer_start) );  DROP TEMPORARY TABLE IF EXISTS stmt_stages; CREATE TEMPORARY TABLE stmt_stages ( event_id BIGINT UNSIGNED, stmt_id BIGINT UNSIGNED, event_name VARCHAR(128), timer_wait BIGINT UNSIGNED, PRIMARY KEY (event_id) );  SET v_start_fresh = in_start_fresh; IF v_start_fresh THEN TRUNCATE TABLE performance_schema.events_statements_history_long; TRUNCATE TABLE performance_schema.events_stages_history_long; END IF;  SET v_auto_enable = in_auto_enable; IF v_auto_enable THEN CALL sys.ps_setup_save(0);  UPDATE performance_schema.threads SET INSTRUMENTED = IF(PROCESSLIST_ID IS NOT NULL, \'YES\', \'NO\');  UPDATE performance_schema.setup_consumers SET ENABLED = \'YES\' WHERE NAME NOT LIKE \'%\\_history\' AND NAME NOT LIKE \'events_wait%\' AND NAME NOT LIKE \'events_transactions%\' AND NAME <> \'statements_digest\';  UPDATE performance_schema.setup_instruments SET ENABLED = \'YES\', TIMED   = \'YES\' WHERE NAME LIKE \'statement/%\' OR NAME LIKE \'stage/%\'; END IF;  WHILE v_runtime < in_runtime DO SELECT UNIX_TIMESTAMP() INTO v_start;  INSERT IGNORE INTO stmt_trace SELECT thread_id, timer_start, event_id, sql_text, timer_wait, lock_time, errors, mysql_errno,  rows_sent, rows_affected, rows_examined, created_tmp_tables, created_tmp_disk_tables, no_index_used FROM performance_schema.events_statements_history_long WHERE digest = in_digest;  INSERT IGNORE INTO stmt_stages SELECT stages.event_id, stmt_trace.event_id, stages.event_name, stages.timer_wait FROM performance_schema.events_stages_history_long AS stages JOIN stmt_trace ON stages.nesting_event_id = stmt_trace.event_id;  SELECT SLEEP(in_interval) INTO @sleep; SET v_runtime = v_runtime + (UNIX_TIMESTAMP() - v_start); END WHILE;  SELECT \"SUMMARY STATISTICS\";  SELECT COUNT(*) executions, sys.format_time(SUM(timer_wait)) AS exec_time, sys.format_time(SUM(lock_time)) AS lock_time, SUM(rows_sent) AS rows_sent, SUM(rows_affected) AS rows_affected, SUM(rows_examined) AS rows_examined, SUM(created_tmp_tables) AS tmp_tables, SUM(no_index_used) AS full_scans FROM stmt_trace;  SELECT event_name, COUNT(*) as count, sys.format_time(SUM(timer_wait)) as latency FROM stmt_stages GROUP BY event_name ORDER BY SUM(timer_wait) DESC;  SELECT \"LONGEST RUNNING STATEMENT\";  SELECT thread_id, sys.format_time(timer_wait) AS exec_time, sys.format_time(lock_time) AS lock_time, rows_sent, rows_affected, rows_examined, created_tmp_tables AS tmp_tables, no_index_used AS full_scan FROM stmt_trace ORDER BY timer_wait DESC LIMIT 1;  SELECT sql_text FROM stmt_trace ORDER BY timer_wait DESC LIMIT 1;  SELECT sql_text, event_id INTO @sql, @sql_id FROM stmt_trace ORDER BY timer_wait DESC LIMIT 1;  IF (@sql_id IS NOT NULL) THEN SELECT event_name, sys.format_time(timer_wait) as latency FROM stmt_stages WHERE stmt_id = @sql_id ORDER BY event_id; END IF;  DROP TEMPORARY TABLE stmt_trace; DROP TEMPORARY TABLE stmt_stages;  IF (@sql IS NOT NULL) THEN SET @stmt := CONCAT(\"EXPLAIN FORMAT=JSON \", @sql); BEGIN DECLARE CONTINUE HANDLER FOR 1064, 1146 SET v_explain = false;  PREPARE explain_stmt FROM @stmt; END;  IF (v_explain) THEN EXECUTE explain_stmt; DEALLOCATE PREPARE explain_stmt; END IF; END IF;  IF v_auto_enable THEN CALL sys.ps_setup_reload_saved(); END IF; IF (v_this_thread_enabed = \'YES\') THEN CALL sys.ps_setup_enable_thread(CONNECTION_ID()); END IF;  SET sql_log_bin = @log_bin; END'),('sys','ps_trace_thread','PROCEDURE','ps_trace_thread','SQL','MODIFIES_SQL_DATA','NO','INVOKER',_binary ' IN in_thread_id BIGINT UNSIGNED, IN in_outfile VARCHAR(255), IN in_max_runtime DECIMAL(20,2), IN in_interval DECIMAL(20,2), IN in_start_fresh BOOLEAN, IN in_auto_setup BOOLEAN, IN in_debug BOOLEAN ','',_binary 'BEGIN DECLARE v_done bool DEFAULT FALSE; DECLARE v_start, v_runtime DECIMAL(20,2) DEFAULT 0.0; DECLARE v_min_event_id bigint unsigned DEFAULT 0; DECLARE v_this_thread_enabed ENUM(\'YES\', \'NO\'); DECLARE v_event longtext; DECLARE c_stack CURSOR FOR SELECT CONCAT(IF(nesting_event_id IS NOT NULL, CONCAT(nesting_event_id, \' -> \'), \'\'),  event_id, \'; \', event_id, \' [label=\"\', \'(\', sys.format_time(timer_wait), \') \', IF (event_name NOT LIKE \'wait/io%\',  SUBSTRING_INDEX(event_name, \'/\', -2),  IF (event_name NOT LIKE \'wait/io/file%\' OR event_name NOT LIKE \'wait/io/socket%\', SUBSTRING_INDEX(event_name, \'/\', -4), event_name) ), IF (event_name LIKE \'transaction\', IFNULL(CONCAT(\'\\\\n\', wait_info), \'\'), \'\'), IF (event_name LIKE \'statement/%\', IFNULL(CONCAT(\'\\\\n\', wait_info), \'\'), \'\'), IF (in_debug AND event_name LIKE \'wait%\', wait_info, \'\'), \'\", \',  CASE WHEN event_name LIKE \'wait/io/file%\' THEN  \'shape=box, style=filled, color=red\' WHEN event_name LIKE \'wait/io/table%\' THEN  \'shape=box, style=filled, color=green\' WHEN event_name LIKE \'wait/io/socket%\' THEN \'shape=box, style=filled, color=yellow\' WHEN event_name LIKE \'wait/synch/mutex%\' THEN \'style=filled, color=lightskyblue\' WHEN event_name LIKE \'wait/synch/cond%\' THEN \'style=filled, color=darkseagreen3\' WHEN event_name LIKE \'wait/synch/rwlock%\' THEN \'style=filled, color=orchid\' WHEN event_name LIKE \'wait/synch/sxlock%\' THEN \'style=filled, color=palevioletred\'  WHEN event_name LIKE \'wait/lock%\' THEN \'shape=box, style=filled, color=tan\' WHEN event_name LIKE \'statement/%\' THEN CONCAT(\'shape=box, style=bold\', CASE WHEN event_name LIKE \'statement/com/%\' THEN \' style=filled, color=darkseagreen\' ELSE IF((timer_wait/1000000000000) > @@long_query_time,  \' style=filled, color=red\',  \' style=filled, color=lightblue\') END ) WHEN event_name LIKE \'transaction\' THEN \'shape=box, style=filled, color=lightblue3\' WHEN event_name LIKE \'stage/%\' THEN \'style=filled, color=slategray3\' WHEN event_name LIKE \'%idle%\' THEN \'shape=box, style=filled, color=firebrick3\' ELSE \'\' END, \'];\\n\' ) event, event_id FROM ( (SELECT thread_id, event_id, event_name, timer_wait, timer_start, nesting_event_id, CONCAT(\'trx_id: \',  IFNULL(trx_id, \'\'), \'\\\\n\', \'gtid: \', IFNULL(gtid, \'\'), \'\\\\n\', \'state: \', state, \'\\\\n\', \'mode: \', access_mode, \'\\\\n\', \'isolation: \', isolation_level, \'\\\\n\', \'autocommit: \', autocommit, \'\\\\n\', \'savepoints: \', number_of_savepoints, \'\\\\n\' ) AS wait_info FROM performance_schema.events_transactions_history_long WHERE thread_id = in_thread_id AND event_id > v_min_event_id) UNION (SELECT thread_id, event_id, event_name, timer_wait, timer_start, nesting_event_id,  CONCAT(\'statement: \', sql_text, \'\\\\n\', \'errors: \', errors, \'\\\\n\', \'warnings: \', warnings, \'\\\\n\', \'lock time: \', sys.format_time(lock_time),\'\\\\n\', \'rows affected: \', rows_affected, \'\\\\n\', \'rows sent: \', rows_sent, \'\\\\n\', \'rows examined: \', rows_examined, \'\\\\n\', \'tmp tables: \', created_tmp_tables, \'\\\\n\', \'tmp disk tables: \', created_tmp_disk_tables, \'\\\\n\' \'select scan: \', select_scan, \'\\\\n\', \'select full join: \', select_full_join, \'\\\\n\', \'select full range join: \', select_full_range_join, \'\\\\n\', \'select range: \', select_range, \'\\\\n\', \'select range check: \', select_range_check, \'\\\\n\',  \'sort merge passes: \', sort_merge_passes, \'\\\\n\', \'sort rows: \', sort_rows, \'\\\\n\', \'sort range: \', sort_range, \'\\\\n\', \'sort scan: \', sort_scan, \'\\\\n\', \'no index used: \', IF(no_index_used, \'TRUE\', \'FALSE\'), \'\\\\n\', \'no good index used: \', IF(no_good_index_used, \'TRUE\', \'FALSE\'), \'\\\\n\' ) AS wait_info FROM performance_schema.events_statements_history_long WHERE thread_id = in_thread_id AND event_id > v_min_event_id) UNION (SELECT thread_id, event_id, event_name, timer_wait, timer_start, nesting_event_id, null AS wait_info FROM performance_schema.events_stages_history_long  WHERE thread_id = in_thread_id AND event_id > v_min_event_id) UNION  (SELECT thread_id, event_id,  CONCAT(event_name,  IF(event_name NOT LIKE \'wait/synch/mutex%\', IFNULL(CONCAT(\' - \', operation), \'\'), \'\'),  IF(number_of_bytes IS NOT NULL, CONCAT(\' \', number_of_bytes, \' bytes\'), \'\'), IF(event_name LIKE \'wait/io/file%\', \'\\\\n\', \'\'), IF(object_schema IS NOT NULL, CONCAT(\'\\\\nObject: \', object_schema, \'.\'), \'\'),  IF(object_name IS NOT NULL,  IF (event_name LIKE \'wait/io/socket%\', CONCAT(\'\\\\n\', IF (object_name LIKE \':0%\', @@socket, object_name)), object_name), \'\' ), IF(index_name IS NOT NULL, CONCAT(\' Index: \', index_name), \'\'), \'\\\\n\' ) AS event_name, timer_wait, timer_start, nesting_event_id, source AS wait_info FROM performance_schema.events_waits_history_long WHERE thread_id = in_thread_id AND event_id > v_min_event_id) ) events  ORDER BY event_id; DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;  SET @log_bin := @@sql_log_bin; SET sql_log_bin = 0;  SELECT INSTRUMENTED INTO v_this_thread_enabed FROM performance_schema.threads WHERE PROCESSLIST_ID = CONNECTION_ID(); CALL sys.ps_setup_disable_thread(CONNECTION_ID());  IF (in_auto_setup) THEN CALL sys.ps_setup_save(0);  DELETE FROM performance_schema.setup_actors;  UPDATE performance_schema.threads SET INSTRUMENTED = IF(THREAD_ID = in_thread_id, \'YES\', \'NO\');  UPDATE performance_schema.setup_consumers SET ENABLED = \'YES\' WHERE NAME NOT LIKE \'%\\_history\';  UPDATE performance_schema.setup_instruments SET ENABLED = \'YES\', TIMED   = \'YES\'; END IF;  IF (in_start_fresh) THEN TRUNCATE performance_schema.events_transactions_history_long; TRUNCATE performance_schema.events_statements_history_long; TRUNCATE performance_schema.events_stages_history_long; TRUNCATE performance_schema.events_waits_history_long; END IF;  DROP TEMPORARY TABLE IF EXISTS tmp_events; CREATE TEMPORARY TABLE tmp_events ( event_id bigint unsigned NOT NULL, event longblob, PRIMARY KEY (event_id) );  INSERT INTO tmp_events VALUES (0, CONCAT(\'digraph events { rankdir=LR; nodesep=0.10;\\n\', \'// Stack created .....: \', NOW(), \'\\n\', \'// MySQL version .....: \', VERSION(), \'\\n\', \'// MySQL hostname ....: \', @@hostname, \'\\n\', \'// MySQL port ........: \', @@port, \'\\n\', \'// MySQL socket ......: \', @@socket, \'\\n\', \'// MySQL user ........: \', CURRENT_USER(), \'\\n\'));  SELECT CONCAT(\'Data collection starting for THREAD_ID = \', in_thread_id) AS \'Info\';  SET v_min_event_id = 0, v_start        = UNIX_TIMESTAMP(), in_interval    = IFNULL(in_interval, 1.00), in_max_runtime = IFNULL(in_max_runtime, 60.00);  WHILE (v_runtime < in_max_runtime AND (SELECT INSTRUMENTED FROM performance_schema.threads WHERE THREAD_ID = in_thread_id) = \'YES\') DO SET v_done = FALSE; OPEN c_stack; c_stack_loop: LOOP FETCH c_stack INTO v_event, v_min_event_id; IF v_done THEN LEAVE c_stack_loop; END IF;  IF (LENGTH(v_event) > 0) THEN INSERT INTO tmp_events VALUES (v_min_event_id, v_event); END IF; END LOOP; CLOSE c_stack;  SELECT SLEEP(in_interval) INTO @sleep; SET v_runtime = (UNIX_TIMESTAMP() - v_start); END WHILE;  INSERT INTO tmp_events VALUES (v_min_event_id+1, \'}\');  SET @query = CONCAT(\'SELECT event FROM tmp_events ORDER BY event_id INTO OUTFILE \'\'\', in_outfile, \'\'\' FIELDS ESCAPED BY \'\'\'\' LINES TERMINATED BY \'\'\'\'\'); PREPARE stmt_output FROM @query; EXECUTE stmt_output; DEALLOCATE PREPARE stmt_output;  SELECT CONCAT(\'Stack trace written to \', in_outfile) AS \'Info\'; SELECT CONCAT(\'dot -Tpdf -o /tmp/stack_\', in_thread_id, \'.pdf \', in_outfile) AS \'Convert to PDF\'; SELECT CONCAT(\'dot -Tpng -o /tmp/stack_\', in_thread_id, \'.png \', in_outfile) AS \'Convert to PNG\'; DROP TEMPORARY TABLE tmp_events;  IF (in_auto_setup) THEN CALL sys.ps_setup_reload_saved(); END IF; IF (v_this_thread_enabed = \'YES\') THEN CALL sys.ps_setup_enable_thread(CONNECTION_ID()); END IF;  SET sql_log_bin = @log_bin; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Dumps all data within Performance Schema for an instrumented thread,\n to create a DOT formatted graph file. \n \n Each resultset returned from the procedure should be used for a complete graph\n \n Requires the SUPER privilege for \"SET sql_log_bin = 0;\".\n \n Parameters\n \n in_thread_id (BIGINT UNSIGNED):\n The thread that you would like a stack trace for\n in_outfile  (VARCHAR(255)):\n The filename the dot file will be written to\n in_max_runtime (DECIMAL(20,2)):\n The maximum time to keep collecting data.\n Use NULL to get the default which is 60 seconds.\n in_interval (DECIMAL(20,2)): \n How long to sleep between data collections. \n Use NULL to get the default which is 1 second.\n in_start_fresh (BOOLEAN):\n Whether to reset all Performance Schema data before tracing.\n in_auto_setup (BOOLEAN):\n Whether to disable all other threads and enable all consumers/instruments. \n This will also reset the settings at the end of the run.\n in_debug (BOOLEAN):\n Whether you would like to include file:lineno in the graph\n \n Example\n \n mysql> CALL sys.ps_trace_thread(25, CONCAT(\'/tmp/stack-\', REPLACE(NOW(), \' \', \'-\'), \'.dot\'), NULL, NULL, TRUE, TRUE, TRUE);\n +-------------------+\n | summary           |\n +-------------------+\n | Disabled 1 thread |\n +-------------------+\n 1 row in set (0.00 sec)\n \n +---------------------------------------------+\n | Info                                        |\n +---------------------------------------------+\n | Data collection starting for THREAD_ID = 25 |\n +---------------------------------------------+\n 1 row in set (0.03 sec)\n \n +-----------------------------------------------------------+\n | Info                                                      |\n +-----------------------------------------------------------+\n | Stack trace written to /tmp/stack-2014-02-16-21:18:41.dot |\n +-----------------------------------------------------------+\n 1 row in set (60.07 sec)\n \n +-------------------------------------------------------------------+\n | Convert to PDF                                                    |\n +-------------------------------------------------------------------+\n | dot -Tpdf -o /tmp/stack_25.pdf /tmp/stack-2014-02-16-21:18:41.dot |\n +-------------------------------------------------------------------+\n 1 row in set (60.07 sec)\n \n +-------------------------------------------------------------------+\n | Convert to PNG                                                    |\n +-------------------------------------------------------------------+\n | dot -Tpng -o /tmp/stack_25.png /tmp/stack-2014-02-16-21:18:41.dot |\n +-------------------------------------------------------------------+\n 1 row in set (60.07 sec)\n \n +------------------+\n | summary          |\n +------------------+\n | Enabled 1 thread |\n +------------------+\n 1 row in set (60.32 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_done bool DEFAULT FALSE; DECLARE v_start, v_runtime DECIMAL(20,2) DEFAULT 0.0; DECLARE v_min_event_id bigint unsigned DEFAULT 0; DECLARE v_this_thread_enabed ENUM(\'YES\', \'NO\'); DECLARE v_event longtext; DECLARE c_stack CURSOR FOR SELECT CONCAT(IF(nesting_event_id IS NOT NULL, CONCAT(nesting_event_id, \' -> \'), \'\'),  event_id, \'; \', event_id, \' [label=\"\', \'(\', sys.format_time(timer_wait), \') \', IF (event_name NOT LIKE \'wait/io%\',  SUBSTRING_INDEX(event_name, \'/\', -2),  IF (event_name NOT LIKE \'wait/io/file%\' OR event_name NOT LIKE \'wait/io/socket%\', SUBSTRING_INDEX(event_name, \'/\', -4), event_name) ), IF (event_name LIKE \'transaction\', IFNULL(CONCAT(\'\\n\', wait_info), \'\'), \'\'), IF (event_name LIKE \'statement/%\', IFNULL(CONCAT(\'\\n\', wait_info), \'\'), \'\'), IF (in_debug AND event_name LIKE \'wait%\', wait_info, \'\'), \'\", \',  CASE WHEN event_name LIKE \'wait/io/file%\' THEN  \'shape=box, style=filled, color=red\' WHEN event_name LIKE \'wait/io/table%\' THEN  \'shape=box, style=filled, color=green\' WHEN event_name LIKE \'wait/io/socket%\' THEN \'shape=box, style=filled, color=yellow\' WHEN event_name LIKE \'wait/synch/mutex%\' THEN \'style=filled, color=lightskyblue\' WHEN event_name LIKE \'wait/synch/cond%\' THEN \'style=filled, color=darkseagreen3\' WHEN event_name LIKE \'wait/synch/rwlock%\' THEN \'style=filled, color=orchid\' WHEN event_name LIKE \'wait/synch/sxlock%\' THEN \'style=filled, color=palevioletred\'  WHEN event_name LIKE \'wait/lock%\' THEN \'shape=box, style=filled, color=tan\' WHEN event_name LIKE \'statement/%\' THEN CONCAT(\'shape=box, style=bold\', CASE WHEN event_name LIKE \'statement/com/%\' THEN \' style=filled, color=darkseagreen\' ELSE IF((timer_wait/1000000000000) > @@long_query_time,  \' style=filled, color=red\',  \' style=filled, color=lightblue\') END ) WHEN event_name LIKE \'transaction\' THEN \'shape=box, style=filled, color=lightblue3\' WHEN event_name LIKE \'stage/%\' THEN \'style=filled, color=slategray3\' WHEN event_name LIKE \'%idle%\' THEN \'shape=box, style=filled, color=firebrick3\' ELSE \'\' END, \'];\n\' ) event, event_id FROM ( (SELECT thread_id, event_id, event_name, timer_wait, timer_start, nesting_event_id, CONCAT(\'trx_id: \',  IFNULL(trx_id, \'\'), \'\\n\', \'gtid: \', IFNULL(gtid, \'\'), \'\\n\', \'state: \', state, \'\\n\', \'mode: \', access_mode, \'\\n\', \'isolation: \', isolation_level, \'\\n\', \'autocommit: \', autocommit, \'\\n\', \'savepoints: \', number_of_savepoints, \'\\n\' ) AS wait_info FROM performance_schema.events_transactions_history_long WHERE thread_id = in_thread_id AND event_id > v_min_event_id) UNION (SELECT thread_id, event_id, event_name, timer_wait, timer_start, nesting_event_id,  CONCAT(\'statement: \', sql_text, \'\\n\', \'errors: \', errors, \'\\n\', \'warnings: \', warnings, \'\\n\', \'lock time: \', sys.format_time(lock_time),\'\\n\', \'rows affected: \', rows_affected, \'\\n\', \'rows sent: \', rows_sent, \'\\n\', \'rows examined: \', rows_examined, \'\\n\', \'tmp tables: \', created_tmp_tables, \'\\n\', \'tmp disk tables: \', created_tmp_disk_tables, \'\\n\' \'select scan: \', select_scan, \'\\n\', \'select full join: \', select_full_join, \'\\n\', \'select full range join: \', select_full_range_join, \'\\n\', \'select range: \', select_range, \'\\n\', \'select range check: \', select_range_check, \'\\n\',  \'sort merge passes: \', sort_merge_passes, \'\\n\', \'sort rows: \', sort_rows, \'\\n\', \'sort range: \', sort_range, \'\\n\', \'sort scan: \', sort_scan, \'\\n\', \'no index used: \', IF(no_index_used, \'TRUE\', \'FALSE\'), \'\\n\', \'no good index used: \', IF(no_good_index_used, \'TRUE\', \'FALSE\'), \'\\n\' ) AS wait_info FROM performance_schema.events_statements_history_long WHERE thread_id = in_thread_id AND event_id > v_min_event_id) UNION (SELECT thread_id, event_id, event_name, timer_wait, timer_start, nesting_event_id, null AS wait_info FROM performance_schema.events_stages_history_long  WHERE thread_id = in_thread_id AND event_id > v_min_event_id) UNION  (SELECT thread_id, event_id,  CONCAT(event_name,  IF(event_name NOT LIKE \'wait/synch/mutex%\', IFNULL(CONCAT(\' - \', operation), \'\'), \'\'),  IF(number_of_bytes IS NOT NULL, CONCAT(\' \', number_of_bytes, \' bytes\'), \'\'), IF(event_name LIKE \'wait/io/file%\', \'\\n\', \'\'), IF(object_schema IS NOT NULL, CONCAT(\'\\nObject: \', object_schema, \'.\'), \'\'),  IF(object_name IS NOT NULL,  IF (event_name LIKE \'wait/io/socket%\', CONCAT(\'\\n\', IF (object_name LIKE \':0%\', @@socket, object_name)), object_name), \'\' ), IF(index_name IS NOT NULL, CONCAT(\' Index: \', index_name), \'\'), \'\\n\' ) AS event_name, timer_wait, timer_start, nesting_event_id, source AS wait_info FROM performance_schema.events_waits_history_long WHERE thread_id = in_thread_id AND event_id > v_min_event_id) ) events  ORDER BY event_id; DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;  SET @log_bin := @@sql_log_bin; SET sql_log_bin = 0;  SELECT INSTRUMENTED INTO v_this_thread_enabed FROM performance_schema.threads WHERE PROCESSLIST_ID = CONNECTION_ID(); CALL sys.ps_setup_disable_thread(CONNECTION_ID());  IF (in_auto_setup) THEN CALL sys.ps_setup_save(0);  DELETE FROM performance_schema.setup_actors;  UPDATE performance_schema.threads SET INSTRUMENTED = IF(THREAD_ID = in_thread_id, \'YES\', \'NO\');  UPDATE performance_schema.setup_consumers SET ENABLED = \'YES\' WHERE NAME NOT LIKE \'%\\_history\';  UPDATE performance_schema.setup_instruments SET ENABLED = \'YES\', TIMED   = \'YES\'; END IF;  IF (in_start_fresh) THEN TRUNCATE performance_schema.events_transactions_history_long; TRUNCATE performance_schema.events_statements_history_long; TRUNCATE performance_schema.events_stages_history_long; TRUNCATE performance_schema.events_waits_history_long; END IF;  DROP TEMPORARY TABLE IF EXISTS tmp_events; CREATE TEMPORARY TABLE tmp_events ( event_id bigint unsigned NOT NULL, event longblob, PRIMARY KEY (event_id) );  INSERT INTO tmp_events VALUES (0, CONCAT(\'digraph events { rankdir=LR; nodesep=0.10;\n\', \'// Stack created .....: \', NOW(), \'\n\', \'// MySQL version .....: \', VERSION(), \'\n\', \'// MySQL hostname ....: \', @@hostname, \'\n\', \'// MySQL port ........: \', @@port, \'\n\', \'// MySQL socket ......: \', @@socket, \'\n\', \'// MySQL user ........: \', CURRENT_USER(), \'\n\'));  SELECT CONCAT(\'Data collection starting for THREAD_ID = \', in_thread_id) AS \'Info\';  SET v_min_event_id = 0, v_start        = UNIX_TIMESTAMP(), in_interval    = IFNULL(in_interval, 1.00), in_max_runtime = IFNULL(in_max_runtime, 60.00);  WHILE (v_runtime < in_max_runtime AND (SELECT INSTRUMENTED FROM performance_schema.threads WHERE THREAD_ID = in_thread_id) = \'YES\') DO SET v_done = FALSE; OPEN c_stack; c_stack_loop: LOOP FETCH c_stack INTO v_event, v_min_event_id; IF v_done THEN LEAVE c_stack_loop; END IF;  IF (LENGTH(v_event) > 0) THEN INSERT INTO tmp_events VALUES (v_min_event_id, v_event); END IF; END LOOP; CLOSE c_stack;  SELECT SLEEP(in_interval) INTO @sleep; SET v_runtime = (UNIX_TIMESTAMP() - v_start); END WHILE;  INSERT INTO tmp_events VALUES (v_min_event_id+1, \'}\');  SET @query = CONCAT(\'SELECT event FROM tmp_events ORDER BY event_id INTO OUTFILE \'\', in_outfile, \'\' FIELDS ESCAPED BY \'\' LINES TERMINATED BY \'\'\'); PREPARE stmt_output FROM @query; EXECUTE stmt_output; DEALLOCATE PREPARE stmt_output;  SELECT CONCAT(\'Stack trace written to \', in_outfile) AS \'Info\'; SELECT CONCAT(\'dot -Tpdf -o /tmp/stack_\', in_thread_id, \'.pdf \', in_outfile) AS \'Convert to PDF\'; SELECT CONCAT(\'dot -Tpng -o /tmp/stack_\', in_thread_id, \'.png \', in_outfile) AS \'Convert to PNG\'; DROP TEMPORARY TABLE tmp_events;  IF (in_auto_setup) THEN CALL sys.ps_setup_reload_saved(); END IF; IF (v_this_thread_enabed = \'YES\') THEN CALL sys.ps_setup_enable_thread(CONNECTION_ID()); END IF;  SET sql_log_bin = @log_bin; END'),('sys','ps_setup_disable_background_threads','PROCEDURE','ps_setup_disable_background_threads','SQL','MODIFIES_SQL_DATA','NO','INVOKER','','',_binary 'BEGIN UPDATE performance_schema.threads SET instrumented = \'NO\' WHERE type = \'BACKGROUND\';  SELECT CONCAT(\'Disabled \', @rows := ROW_COUNT(), \' background thread\', IF(@rows != 1, \'s\', \'\')) AS summary; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Disable all background thread instrumentation within Performance Schema.\n \n Parameters\n \n None.\n \n Example\n \n mysql> CALL sys.ps_setup_disable_background_threads();\n +--------------------------------+\n | summary                        |\n +--------------------------------+\n | Disabled 18 background threads |\n +--------------------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN UPDATE performance_schema.threads SET instrumented = \'NO\' WHERE type = \'BACKGROUND\';  SELECT CONCAT(\'Disabled \', @rows := ROW_COUNT(), \' background thread\', IF(@rows != 1, \'s\', \'\')) AS summary; END'),('sys','ps_setup_disable_consumer','PROCEDURE','ps_setup_disable_consumer','SQL','MODIFIES_SQL_DATA','NO','INVOKER',_binary ' IN consumer VARCHAR(128) ','',_binary 'BEGIN UPDATE performance_schema.setup_consumers SET enabled = \'NO\' WHERE name LIKE CONCAT(\'%\', consumer, \'%\');  SELECT CONCAT(\'Disabled \', @rows := ROW_COUNT(), \' consumer\', IF(@rows != 1, \'s\', \'\')) AS summary; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Disables consumers within Performance Schema \n matching the input pattern.\n \n Parameters\n \n consumer (VARCHAR(128)):\n A LIKE pattern match (using \"%consumer%\") of consumers to disable\n \n Example\n \n To disable all consumers:\n \n mysql> CALL sys.ps_setup_disable_consumer(\'\');\n +--------------------------+\n | summary                  |\n +--------------------------+\n | Disabled 15 consumers    |\n +--------------------------+\n 1 row in set (0.02 sec)\n \n To disable just the event_stage consumers:\n \n mysql> CALL sys.ps_setup_disable_comsumers(\'stage\');\n +------------------------+\n | summary                |\n +------------------------+\n | Disabled 3 consumers   |\n +------------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN UPDATE performance_schema.setup_consumers SET enabled = \'NO\' WHERE name LIKE CONCAT(\'%\', consumer, \'%\');  SELECT CONCAT(\'Disabled \', @rows := ROW_COUNT(), \' consumer\', IF(@rows != 1, \'s\', \'\')) AS summary; END'),('sys','ps_setup_disable_instrument','PROCEDURE','ps_setup_disable_instrument','SQL','MODIFIES_SQL_DATA','NO','INVOKER',_binary ' IN in_pattern VARCHAR(128) ','',_binary 'BEGIN UPDATE performance_schema.setup_instruments SET enabled = \'NO\', timed = \'NO\' WHERE name LIKE CONCAT(\'%\', in_pattern, \'%\');  SELECT CONCAT(\'Disabled \', @rows := ROW_COUNT(), \' instrument\', IF(@rows != 1, \'s\', \'\')) AS summary; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Disables instruments within Performance Schema \n matching the input pattern.\n \n Parameters\n \n in_pattern (VARCHAR(128)):\n A LIKE pattern match (using \"%in_pattern%\") of events to disable\n \n Example\n \n To disable all mutex instruments:\n \n mysql> CALL sys.ps_setup_disable_instrument(\'wait/synch/mutex\');\n +--------------------------+\n | summary                  |\n +--------------------------+\n | Disabled 155 instruments |\n +--------------------------+\n 1 row in set (0.02 sec)\n \n To disable just a specific TCP/IP based network IO instrument:\n \n mysql> CALL sys.ps_setup_disable_instrument(\'wait/io/socket/sql/server_tcpip_socket\');\n +------------------------+\n | summary                |\n +------------------------+\n | Disabled 1 instruments |\n +------------------------+\n 1 row in set (0.00 sec)\n \n To disable all instruments:\n \n mysql> CALL sys.ps_setup_disable_instrument(\'\');\n +--------------------------+\n | summary                  |\n +--------------------------+\n | Disabled 547 instruments |\n +--------------------------+\n 1 row in set (0.01 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN UPDATE performance_schema.setup_instruments SET enabled = \'NO\', timed = \'NO\' WHERE name LIKE CONCAT(\'%\', in_pattern, \'%\');  SELECT CONCAT(\'Disabled \', @rows := ROW_COUNT(), \' instrument\', IF(@rows != 1, \'s\', \'\')) AS summary; END'),('sys','ps_setup_disable_thread','PROCEDURE','ps_setup_disable_thread','SQL','MODIFIES_SQL_DATA','NO','INVOKER',_binary ' IN in_connection_id BIGINT ','',_binary 'BEGIN UPDATE performance_schema.threads SET instrumented = \'NO\' WHERE processlist_id = in_connection_id;  SELECT CONCAT(\'Disabled \', @rows := ROW_COUNT(), \' thread\', IF(@rows != 1, \'s\', \'\')) AS summary; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Disable the given connection/thread in Performance Schema.\n \n Parameters\n \n in_connection_id (BIGINT):\n The connection ID (PROCESSLIST_ID from performance_schema.threads\n or the ID shown within SHOW PROCESSLIST)\n \n Example\n \n mysql> CALL sys.ps_setup_disable_thread(3);\n +-------------------+\n | summary           |\n +-------------------+\n | Disabled 1 thread |\n +-------------------+\n 1 row in set (0.01 sec)\n \n To disable the current connection:\n \n mysql> CALL sys.ps_setup_disable_thread(CONNECTION_ID());\n +-------------------+\n | summary           |\n +-------------------+\n | Disabled 1 thread |\n +-------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN UPDATE performance_schema.threads SET instrumented = \'NO\' WHERE processlist_id = in_connection_id;  SELECT CONCAT(\'Disabled \', @rows := ROW_COUNT(), \' thread\', IF(@rows != 1, \'s\', \'\')) AS summary; END'),('sys','ps_setup_enable_background_threads','PROCEDURE','ps_setup_enable_background_threads','SQL','MODIFIES_SQL_DATA','NO','INVOKER','','',_binary 'BEGIN UPDATE performance_schema.threads SET instrumented = \'YES\' WHERE type = \'BACKGROUND\';  SELECT CONCAT(\'Enabled \', @rows := ROW_COUNT(), \' background thread\', IF(@rows != 1, \'s\', \'\')) AS summary; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Enable all background thread instrumentation within Performance Schema.\n \n Parameters\n \n None.\n \n Example\n \n mysql> CALL sys.ps_setup_enable_background_threads();\n +-------------------------------+\n | summary                       |\n +-------------------------------+\n | Enabled 18 background threads |\n +-------------------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN UPDATE performance_schema.threads SET instrumented = \'YES\' WHERE type = \'BACKGROUND\';  SELECT CONCAT(\'Enabled \', @rows := ROW_COUNT(), \' background thread\', IF(@rows != 1, \'s\', \'\')) AS summary; END'),('sys','ps_setup_enable_consumer','PROCEDURE','ps_setup_enable_consumer','SQL','MODIFIES_SQL_DATA','NO','INVOKER',_binary ' IN consumer VARCHAR(128) ','',_binary 'BEGIN UPDATE performance_schema.setup_consumers SET enabled = \'YES\' WHERE name LIKE CONCAT(\'%\', consumer, \'%\');  SELECT CONCAT(\'Enabled \', @rows := ROW_COUNT(), \' consumer\', IF(@rows != 1, \'s\', \'\')) AS summary; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Enables consumers within Performance Schema \n matching the input pattern.\n \n Parameters\n \n consumer (VARCHAR(128)):\n A LIKE pattern match (using \"%consumer%\") of consumers to enable\n \n Example\n \n To enable all consumers:\n \n mysql> CALL sys.ps_setup_enable_consumer(\'\');\n +-------------------------+\n | summary                 |\n +-------------------------+\n | Enabled 10 consumers    |\n +-------------------------+\n 1 row in set (0.02 sec)\n \n Query OK, 0 rows affected (0.02 sec)\n \n To enable just \"waits\" consumers:\n \n mysql> CALL sys.ps_setup_enable_consumer(\'waits\');\n +-----------------------+\n | summary               |\n +-----------------------+\n | Enabled 3 consumers   |\n +-----------------------+\n 1 row in set (0.00 sec)\n \n Query OK, 0 rows affected (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN UPDATE performance_schema.setup_consumers SET enabled = \'YES\' WHERE name LIKE CONCAT(\'%\', consumer, \'%\');  SELECT CONCAT(\'Enabled \', @rows := ROW_COUNT(), \' consumer\', IF(@rows != 1, \'s\', \'\')) AS summary; END'),('sys','ps_setup_enable_instrument','PROCEDURE','ps_setup_enable_instrument','SQL','MODIFIES_SQL_DATA','NO','INVOKER',_binary ' IN in_pattern VARCHAR(128) ','',_binary 'BEGIN UPDATE performance_schema.setup_instruments SET enabled = \'YES\', timed = \'YES\' WHERE name LIKE CONCAT(\'%\', in_pattern, \'%\');  SELECT CONCAT(\'Enabled \', @rows := ROW_COUNT(), \' instrument\', IF(@rows != 1, \'s\', \'\')) AS summary; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Enables instruments within Performance Schema \n matching the input pattern.\n \n Parameters\n \n in_pattern (VARCHAR(128)):\n A LIKE pattern match (using \"%in_pattern%\") of events to enable\n \n Example\n \n To enable all mutex instruments:\n \n mysql> CALL sys.ps_setup_enable_instrument(\'wait/synch/mutex\');\n +-------------------------+\n | summary                 |\n +-------------------------+\n | Enabled 155 instruments |\n +-------------------------+\n 1 row in set (0.02 sec)\n \n Query OK, 0 rows affected (0.02 sec)\n \n To enable just a specific TCP/IP based network IO instrument:\n \n mysql> CALL sys.ps_setup_enable_instrument(\'wait/io/socket/sql/server_tcpip_socket\');\n +-----------------------+\n | summary               |\n +-----------------------+\n | Enabled 1 instruments |\n +-----------------------+\n 1 row in set (0.00 sec)\n \n Query OK, 0 rows affected (0.00 sec)\n \n To enable all instruments:\n \n mysql> CALL sys.ps_setup_enable_instrument(\'\');\n +-------------------------+\n | summary                 |\n +-------------------------+\n | Enabled 547 instruments |\n +-------------------------+\n 1 row in set (0.01 sec)\n \n Query OK, 0 rows affected (0.01 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN UPDATE performance_schema.setup_instruments SET enabled = \'YES\', timed = \'YES\' WHERE name LIKE CONCAT(\'%\', in_pattern, \'%\');  SELECT CONCAT(\'Enabled \', @rows := ROW_COUNT(), \' instrument\', IF(@rows != 1, \'s\', \'\')) AS summary; END'),('sys','ps_setup_enable_thread','PROCEDURE','ps_setup_enable_thread','SQL','MODIFIES_SQL_DATA','NO','INVOKER',_binary ' IN in_connection_id BIGINT ','',_binary 'BEGIN UPDATE performance_schema.threads SET instrumented = \'YES\' WHERE processlist_id = in_connection_id;  SELECT CONCAT(\'Enabled \', @rows := ROW_COUNT(), \' thread\', IF(@rows != 1, \'s\', \'\')) AS summary; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Enable the given connection/thread in Performance Schema.\n \n Parameters\n \n in_connection_id (BIGINT):\n The connection ID (PROCESSLIST_ID from performance_schema.threads\n or the ID shown within SHOW PROCESSLIST)\n \n Example\n \n mysql> CALL sys.ps_setup_enable_thread(3);\n +------------------+\n | summary          |\n +------------------+\n | Enabled 1 thread |\n +------------------+\n 1 row in set (0.01 sec)\n \n To enable the current connection:\n \n mysql> CALL sys.ps_setup_enable_thread(CONNECTION_ID());\n +------------------+\n | summary          |\n +------------------+\n | Enabled 1 thread |\n +------------------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN UPDATE performance_schema.threads SET instrumented = \'YES\' WHERE processlist_id = in_connection_id;  SELECT CONCAT(\'Enabled \', @rows := ROW_COUNT(), \' thread\', IF(@rows != 1, \'s\', \'\')) AS summary; END'),('sys','ps_setup_reload_saved','PROCEDURE','ps_setup_reload_saved','SQL','MODIFIES_SQL_DATA','NO','INVOKER','','',_binary 'BEGIN DECLARE v_done bool DEFAULT FALSE; DECLARE v_lock_result INT; DECLARE v_lock_used_by BIGINT; DECLARE v_signal_message TEXT; DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SIGNAL SQLSTATE VALUE \'90001\' SET MESSAGE_TEXT = \'An error occurred, was sys.ps_setup_save() run before this procedure?\'; END;  SET @log_bin := @@sql_log_bin; SET sql_log_bin = 0;  SELECT IS_USED_LOCK(\'sys.ps_setup_save\') INTO v_lock_used_by;  IF (v_lock_used_by != CONNECTION_ID()) THEN SET v_signal_message = CONCAT(\'The sys.ps_setup_save lock is currently owned by \', v_lock_used_by); SIGNAL SQLSTATE VALUE \'90002\' SET MESSAGE_TEXT = v_signal_message; END IF;  DELETE FROM performance_schema.setup_actors; INSERT INTO performance_schema.setup_actors SELECT * FROM tmp_setup_actors;  BEGIN DECLARE v_name varchar(64); DECLARE v_enabled enum(\'YES\', \'NO\'); DECLARE c_consumers CURSOR FOR SELECT NAME, ENABLED FROM tmp_setup_consumers; DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;  SET v_done = FALSE; OPEN c_consumers; c_consumers_loop: LOOP FETCH c_consumers INTO v_name, v_enabled; IF v_done THEN LEAVE c_consumers_loop; END IF;  UPDATE performance_schema.setup_consumers SET ENABLED = v_enabled WHERE NAME = v_name; END LOOP; CLOSE c_consumers; END;  UPDATE performance_schema.setup_instruments INNER JOIN tmp_setup_instruments USING (NAME) SET performance_schema.setup_instruments.ENABLED = tmp_setup_instruments.ENABLED, performance_schema.setup_instruments.TIMED   = tmp_setup_instruments.TIMED; BEGIN DECLARE v_thread_id bigint unsigned; DECLARE v_instrumented enum(\'YES\', \'NO\'); DECLARE c_threads CURSOR FOR SELECT THREAD_ID, INSTRUMENTED FROM tmp_threads; DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;  SET v_done = FALSE; OPEN c_threads; c_threads_loop: LOOP FETCH c_threads INTO v_thread_id, v_instrumented; IF v_done THEN LEAVE c_threads_loop; END IF;  UPDATE performance_schema.threads SET INSTRUMENTED = v_instrumented WHERE THREAD_ID = v_thread_id; END LOOP; CLOSE c_threads; END;  UPDATE performance_schema.threads SET INSTRUMENTED = IF(PROCESSLIST_USER IS NOT NULL, sys.ps_is_account_enabled(PROCESSLIST_HOST, PROCESSLIST_USER), \'YES\') WHERE THREAD_ID NOT IN (SELECT THREAD_ID FROM tmp_threads);  DROP TEMPORARY TABLE tmp_setup_actors; DROP TEMPORARY TABLE tmp_setup_consumers; DROP TEMPORARY TABLE tmp_setup_instruments; DROP TEMPORARY TABLE tmp_threads;  SELECT RELEASE_LOCK(\'sys.ps_setup_save\') INTO v_lock_result;  SET sql_log_bin = @log_bin;  END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Reloads a saved Performance Schema configuration,\n so that you can alter the setup for debugging purposes, \n but restore it to a previous state.\n \n Use the companion procedure - ps_setup_save(), to \n save a configuration.\n \n Requires the SUPER privilege for \"SET sql_log_bin = 0;\".\n \n Parameters\n \n None.\n \n Example\n \n mysql> CALL sys.ps_setup_save();\n Query OK, 0 rows affected (0.08 sec)\n \n mysql> UPDATE performance_schema.setup_instruments SET enabled = \'YES\', timed = \'YES\';\n Query OK, 547 rows affected (0.40 sec)\n Rows matched: 784  Changed: 547  Warnings: 0\n \n /* Run some tests that need more detailed instrumentation here */\n \n mysql> CALL sys.ps_setup_reload_saved();\n Query OK, 0 rows affected (0.32 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_done bool DEFAULT FALSE; DECLARE v_lock_result INT; DECLARE v_lock_used_by BIGINT; DECLARE v_signal_message TEXT; DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SIGNAL SQLSTATE VALUE \'90001\' SET MESSAGE_TEXT = \'An error occurred, was sys.ps_setup_save() run before this procedure?\'; END;  SET @log_bin := @@sql_log_bin; SET sql_log_bin = 0;  SELECT IS_USED_LOCK(\'sys.ps_setup_save\') INTO v_lock_used_by;  IF (v_lock_used_by != CONNECTION_ID()) THEN SET v_signal_message = CONCAT(\'The sys.ps_setup_save lock is currently owned by \', v_lock_used_by); SIGNAL SQLSTATE VALUE \'90002\' SET MESSAGE_TEXT = v_signal_message; END IF;  DELETE FROM performance_schema.setup_actors; INSERT INTO performance_schema.setup_actors SELECT * FROM tmp_setup_actors;  BEGIN DECLARE v_name varchar(64); DECLARE v_enabled enum(\'YES\', \'NO\'); DECLARE c_consumers CURSOR FOR SELECT NAME, ENABLED FROM tmp_setup_consumers; DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;  SET v_done = FALSE; OPEN c_consumers; c_consumers_loop: LOOP FETCH c_consumers INTO v_name, v_enabled; IF v_done THEN LEAVE c_consumers_loop; END IF;  UPDATE performance_schema.setup_consumers SET ENABLED = v_enabled WHERE NAME = v_name; END LOOP; CLOSE c_consumers; END;  UPDATE performance_schema.setup_instruments INNER JOIN tmp_setup_instruments USING (NAME) SET performance_schema.setup_instruments.ENABLED = tmp_setup_instruments.ENABLED, performance_schema.setup_instruments.TIMED   = tmp_setup_instruments.TIMED; BEGIN DECLARE v_thread_id bigint unsigned; DECLARE v_instrumented enum(\'YES\', \'NO\'); DECLARE c_threads CURSOR FOR SELECT THREAD_ID, INSTRUMENTED FROM tmp_threads; DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;  SET v_done = FALSE; OPEN c_threads; c_threads_loop: LOOP FETCH c_threads INTO v_thread_id, v_instrumented; IF v_done THEN LEAVE c_threads_loop; END IF;  UPDATE performance_schema.threads SET INSTRUMENTED = v_instrumented WHERE THREAD_ID = v_thread_id; END LOOP; CLOSE c_threads; END;  UPDATE performance_schema.threads SET INSTRUMENTED = IF(PROCESSLIST_USER IS NOT NULL, sys.ps_is_account_enabled(PROCESSLIST_HOST, PROCESSLIST_USER), \'YES\') WHERE THREAD_ID NOT IN (SELECT THREAD_ID FROM tmp_threads);  DROP TEMPORARY TABLE tmp_setup_actors; DROP TEMPORARY TABLE tmp_setup_consumers; DROP TEMPORARY TABLE tmp_setup_instruments; DROP TEMPORARY TABLE tmp_threads;  SELECT RELEASE_LOCK(\'sys.ps_setup_save\') INTO v_lock_result;  SET sql_log_bin = @log_bin;  END'),('sys','ps_setup_reset_to_default','PROCEDURE','ps_setup_reset_to_default','SQL','MODIFIES_SQL_DATA','NO','INVOKER',_binary ' IN in_verbose BOOLEAN ','',_binary 'BEGIN SET @query = \'DELETE FROM performance_schema.setup_actors WHERE NOT (HOST = \'\'%\'\' AND USER = \'\'%\'\' AND `ROLE` = \'\'%\'\')\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: setup_actors\\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt;  SET @query = \'INSERT IGNORE INTO performance_schema.setup_actors VALUES (\'\'%\'\', \'\'%\'\', \'\'%\'\', \'\'YES\'\', \'\'YES\'\')\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: setup_actors\\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt;  SET @query = \'UPDATE performance_schema.setup_instruments SET ENABLED = sys.ps_is_instrument_default_enabled(NAME), TIMED   = sys.ps_is_instrument_default_timed(NAME)\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: setup_instruments\\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt;  SET @query = \'UPDATE performance_schema.setup_consumers SET ENABLED = IF(NAME IN (\'\'events_statements_current\'\', \'\'events_transactions_current\'\', \'\'global_instrumentation\'\', \'\'thread_instrumentation\'\', \'\'statements_digest\'\'), \'\'YES\'\', \'\'NO\'\')\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: setup_consumers\\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt;  SET @query = \'DELETE FROM performance_schema.setup_objects WHERE NOT (OBJECT_TYPE IN (\'\'EVENT\'\', \'\'FUNCTION\'\', \'\'PROCEDURE\'\', \'\'TABLE\'\', \'\'TRIGGER\'\') AND OBJECT_NAME = \'\'%\'\'  AND (OBJECT_SCHEMA = \'\'mysql\'\'              AND ENABLED = \'\'NO\'\'  AND TIMED = \'\'NO\'\' ) OR (OBJECT_SCHEMA = \'\'performance_schema\'\' AND ENABLED = \'\'NO\'\'  AND TIMED = \'\'NO\'\' ) OR (OBJECT_SCHEMA = \'\'information_schema\'\' AND ENABLED = \'\'NO\'\'  AND TIMED = \'\'NO\'\' ) OR (OBJECT_SCHEMA = \'\'%\'\'                  AND ENABLED = \'\'YES\'\' AND TIMED = \'\'YES\'\'))\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: setup_objects\\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt;  SET @query = \'INSERT IGNORE INTO performance_schema.setup_objects VALUES (\'\'EVENT\'\'    , \'\'mysql\'\'             , \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'EVENT\'\'    , \'\'performance_schema\'\', \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'EVENT\'\'    , \'\'information_schema\'\', \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'EVENT\'\'    , \'\'%\'\'                 , \'\'%\'\', \'\'YES\'\', \'\'YES\'\'), (\'\'FUNCTION\'\' , \'\'mysql\'\'             , \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'FUNCTION\'\' , \'\'performance_schema\'\', \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'FUNCTION\'\' , \'\'information_schema\'\', \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'FUNCTION\'\' , \'\'%\'\'                 , \'\'%\'\', \'\'YES\'\', \'\'YES\'\'), (\'\'PROCEDURE\'\', \'\'mysql\'\'             , \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'PROCEDURE\'\', \'\'performance_schema\'\', \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'PROCEDURE\'\', \'\'information_schema\'\', \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'PROCEDURE\'\', \'\'%\'\'                 , \'\'%\'\', \'\'YES\'\', \'\'YES\'\'), (\'\'TABLE\'\'    , \'\'mysql\'\'             , \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'TABLE\'\'    , \'\'performance_schema\'\', \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'TABLE\'\'    , \'\'information_schema\'\', \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'TABLE\'\'    , \'\'%\'\'                 , \'\'%\'\', \'\'YES\'\', \'\'YES\'\'), (\'\'TRIGGER\'\'  , \'\'mysql\'\'             , \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'TRIGGER\'\'  , \'\'performance_schema\'\', \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'TRIGGER\'\'  , \'\'information_schema\'\', \'\'%\'\', \'\'NO\'\' , \'\'NO\'\' ), (\'\'TRIGGER\'\'  , \'\'%\'\'                 , \'\'%\'\', \'\'YES\'\', \'\'YES\'\')\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: setup_objects\\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt;  SET @query = \'UPDATE performance_schema.threads SET INSTRUMENTED = \'\'YES\'\'\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: threads\\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Resets the Performance Schema setup to the default settings.\n \n Parameters\n \n in_verbose (BOOLEAN):\n Whether to print each setup stage (including the SQL) whilst running.\n \n Example\n \n mysql> CALL sys.ps_setup_reset_to_default(true)\\G\n *************************** 1. row ***************************\n status: Resetting: setup_actors\n DELETE\n FROM performance_schema.setup_actors\n WHERE NOT (HOST = \'%\' AND USER = \'%\' AND `ROLE` = \'%\')\n 1 row in set (0.00 sec)\n \n *************************** 1. row ***************************\n status: Resetting: setup_actors\n INSERT IGNORE INTO performance_schema.setup_actors\n VALUES (\'%\', \'%\', \'%\')\n 1 row in set (0.00 sec)\n ...\n \n mysql> CALL sys.ps_setup_reset_to_default(false)\\G\n Query OK, 0 rows affected (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN SET @query = \'DELETE FROM performance_schema.setup_actors WHERE NOT (HOST = \'%\' AND USER = \'%\' AND `ROLE` = \'%\')\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: setup_actors\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt;  SET @query = \'INSERT IGNORE INTO performance_schema.setup_actors VALUES (\'%\', \'%\', \'%\', \'YES\', \'YES\')\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: setup_actors\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt;  SET @query = \'UPDATE performance_schema.setup_instruments SET ENABLED = sys.ps_is_instrument_default_enabled(NAME), TIMED   = sys.ps_is_instrument_default_timed(NAME)\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: setup_instruments\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt;  SET @query = \'UPDATE performance_schema.setup_consumers SET ENABLED = IF(NAME IN (\'events_statements_current\', \'events_transactions_current\', \'global_instrumentation\', \'thread_instrumentation\', \'statements_digest\'), \'YES\', \'NO\')\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: setup_consumers\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt;  SET @query = \'DELETE FROM performance_schema.setup_objects WHERE NOT (OBJECT_TYPE IN (\'EVENT\', \'FUNCTION\', \'PROCEDURE\', \'TABLE\', \'TRIGGER\') AND OBJECT_NAME = \'%\'  AND (OBJECT_SCHEMA = \'mysql\'              AND ENABLED = \'NO\'  AND TIMED = \'NO\' ) OR (OBJECT_SCHEMA = \'performance_schema\' AND ENABLED = \'NO\'  AND TIMED = \'NO\' ) OR (OBJECT_SCHEMA = \'information_schema\' AND ENABLED = \'NO\'  AND TIMED = \'NO\' ) OR (OBJECT_SCHEMA = \'%\'                  AND ENABLED = \'YES\' AND TIMED = \'YES\'))\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: setup_objects\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt;  SET @query = \'INSERT IGNORE INTO performance_schema.setup_objects VALUES (\'EVENT\'    , \'mysql\'             , \'%\', \'NO\' , \'NO\' ), (\'EVENT\'    , \'performance_schema\', \'%\', \'NO\' , \'NO\' ), (\'EVENT\'    , \'information_schema\', \'%\', \'NO\' , \'NO\' ), (\'EVENT\'    , \'%\'                 , \'%\', \'YES\', \'YES\'), (\'FUNCTION\' , \'mysql\'             , \'%\', \'NO\' , \'NO\' ), (\'FUNCTION\' , \'performance_schema\', \'%\', \'NO\' , \'NO\' ), (\'FUNCTION\' , \'information_schema\', \'%\', \'NO\' , \'NO\' ), (\'FUNCTION\' , \'%\'                 , \'%\', \'YES\', \'YES\'), (\'PROCEDURE\', \'mysql\'             , \'%\', \'NO\' , \'NO\' ), (\'PROCEDURE\', \'performance_schema\', \'%\', \'NO\' , \'NO\' ), (\'PROCEDURE\', \'information_schema\', \'%\', \'NO\' , \'NO\' ), (\'PROCEDURE\', \'%\'                 , \'%\', \'YES\', \'YES\'), (\'TABLE\'    , \'mysql\'             , \'%\', \'NO\' , \'NO\' ), (\'TABLE\'    , \'performance_schema\', \'%\', \'NO\' , \'NO\' ), (\'TABLE\'    , \'information_schema\', \'%\', \'NO\' , \'NO\' ), (\'TABLE\'    , \'%\'                 , \'%\', \'YES\', \'YES\'), (\'TRIGGER\'  , \'mysql\'             , \'%\', \'NO\' , \'NO\' ), (\'TRIGGER\'  , \'performance_schema\', \'%\', \'NO\' , \'NO\' ), (\'TRIGGER\'  , \'information_schema\', \'%\', \'NO\' , \'NO\' ), (\'TRIGGER\'  , \'%\'                 , \'%\', \'YES\', \'YES\')\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: setup_objects\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt;  SET @query = \'UPDATE performance_schema.threads SET INSTRUMENTED = \'YES\'\';  IF (in_verbose) THEN SELECT CONCAT(\'Resetting: threads\n\', REPLACE(@query, \'  \', \'\')) AS status; END IF;  PREPARE reset_stmt FROM @query; EXECUTE reset_stmt; DEALLOCATE PREPARE reset_stmt; END'),('sys','ps_setup_save','PROCEDURE','ps_setup_save','SQL','MODIFIES_SQL_DATA','NO','INVOKER',_binary ' IN in_timeout INT ','',_binary 'BEGIN DECLARE v_lock_result INT;  SET @log_bin := @@sql_log_bin; SET sql_log_bin = 0;  SELECT GET_LOCK(\'sys.ps_setup_save\', in_timeout) INTO v_lock_result;  IF v_lock_result THEN DROP TEMPORARY TABLE IF EXISTS tmp_setup_actors; DROP TEMPORARY TABLE IF EXISTS tmp_setup_consumers; DROP TEMPORARY TABLE IF EXISTS tmp_setup_instruments; DROP TEMPORARY TABLE IF EXISTS tmp_threads;  CREATE TEMPORARY TABLE tmp_setup_actors SELECT * FROM performance_schema.setup_actors LIMIT 0; CREATE TEMPORARY TABLE tmp_setup_consumers LIKE performance_schema.setup_consumers; CREATE TEMPORARY TABLE tmp_setup_instruments LIKE performance_schema.setup_instruments; CREATE TEMPORARY TABLE tmp_threads (THREAD_ID bigint unsigned NOT NULL PRIMARY KEY, INSTRUMENTED enum(\'YES\',\'NO\') NOT NULL);  INSERT INTO tmp_setup_actors SELECT * FROM performance_schema.setup_actors; INSERT INTO tmp_setup_consumers SELECT * FROM performance_schema.setup_consumers; INSERT INTO tmp_setup_instruments SELECT * FROM performance_schema.setup_instruments; INSERT INTO tmp_threads SELECT THREAD_ID, INSTRUMENTED FROM performance_schema.threads; ELSE SIGNAL SQLSTATE VALUE \'90000\' SET MESSAGE_TEXT = \'Could not lock the sys.ps_setup_save user lock, another thread has a saved configuration\'; END IF;  SET sql_log_bin = @log_bin; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Saves the current configuration of Performance Schema, \n so that you can alter the setup for debugging purposes, \n but restore it to a previous state.\n \n Use the companion procedure - ps_setup_reload_saved(), to \n restore the saved config.\n \n The named lock \"sys.ps_setup_save\" is taken before the\n current configuration is saved. If the attempt to get the named\n lock times out, an error occurs.\n \n The lock is released after the settings have been restored by\n calling ps_setup_reload_saved().\n \n Requires the SUPER privilege for \"SET sql_log_bin = 0;\".\n \n Parameters\n \n in_timeout INT\n The timeout in seconds used when trying to obtain the lock.\n A negative timeout means infinite timeout.\n \n Example\n \n mysql> CALL sys.ps_setup_save(-1);\n Query OK, 0 rows affected (0.08 sec)\n \n mysql> UPDATE performance_schema.setup_instruments \n ->    SET enabled = \'YES\', timed = \'YES\';\n Query OK, 547 rows affected (0.40 sec)\n Rows matched: 784  Changed: 547  Warnings: 0\n \n /* Run some tests that need more detailed instrumentation here */\n \n mysql> CALL sys.ps_setup_reload_saved();\n Query OK, 0 rows affected (0.32 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_lock_result INT;  SET @log_bin := @@sql_log_bin; SET sql_log_bin = 0;  SELECT GET_LOCK(\'sys.ps_setup_save\', in_timeout) INTO v_lock_result;  IF v_lock_result THEN DROP TEMPORARY TABLE IF EXISTS tmp_setup_actors; DROP TEMPORARY TABLE IF EXISTS tmp_setup_consumers; DROP TEMPORARY TABLE IF EXISTS tmp_setup_instruments; DROP TEMPORARY TABLE IF EXISTS tmp_threads;  CREATE TEMPORARY TABLE tmp_setup_actors SELECT * FROM performance_schema.setup_actors LIMIT 0; CREATE TEMPORARY TABLE tmp_setup_consumers LIKE performance_schema.setup_consumers; CREATE TEMPORARY TABLE tmp_setup_instruments LIKE performance_schema.setup_instruments; CREATE TEMPORARY TABLE tmp_threads (THREAD_ID bigint unsigned NOT NULL PRIMARY KEY, INSTRUMENTED enum(\'YES\',\'NO\') NOT NULL);  INSERT INTO tmp_setup_actors SELECT * FROM performance_schema.setup_actors; INSERT INTO tmp_setup_consumers SELECT * FROM performance_schema.setup_consumers; INSERT INTO tmp_setup_instruments SELECT * FROM performance_schema.setup_instruments; INSERT INTO tmp_threads SELECT THREAD_ID, INSTRUMENTED FROM performance_schema.threads; ELSE SIGNAL SQLSTATE VALUE \'90000\' SET MESSAGE_TEXT = \'Could not lock the sys.ps_setup_save user lock, another thread has a saved configuration\'; END IF;  SET sql_log_bin = @log_bin; END'),('sys','ps_setup_show_disabled','PROCEDURE','ps_setup_show_disabled','SQL','READS_SQL_DATA','NO','INVOKER',_binary ' IN in_show_instruments BOOLEAN, IN in_show_threads BOOLEAN ','',_binary 'BEGIN SELECT @@performance_schema AS performance_schema_enabled;   SELECT CONCAT(\'\\\'\', user, \'\\\'@\\\'\', host, \'\\\'\') AS disabled_users FROM performance_schema.setup_actors WHERE enabled = \'NO\' ORDER BY disabled_users;   SELECT object_type, CONCAT(object_schema, \'.\', object_name) AS objects, enabled, timed FROM performance_schema.setup_objects WHERE enabled = \'NO\' ORDER BY object_type, objects;  SELECT name AS disabled_consumers FROM performance_schema.setup_consumers WHERE enabled = \'NO\' ORDER BY disabled_consumers;  IF (in_show_threads) THEN SELECT IF(name = \'thread/sql/one_connection\',  CONCAT(processlist_user, \'@\', processlist_host),  REPLACE(name, \'thread/\', \'\')) AS disabled_threads, TYPE AS thread_type FROM performance_schema.threads WHERE INSTRUMENTED = \'NO\' ORDER BY disabled_threads; END IF;  IF (in_show_instruments) THEN SELECT name AS disabled_instruments, timed FROM performance_schema.setup_instruments WHERE enabled = \'NO\' ORDER BY disabled_instruments; END IF; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Shows all currently disable Performance Schema configuration.\n \n Disabled users is only available for MySQL 5.7.6 and later.\n In earlier versions it was only possible to enable users.\n \n Parameters\n \n in_show_instruments (BOOLEAN):\n Whether to print disabled instruments (can print many items)\n \n in_show_threads (BOOLEAN):\n Whether to print disabled threads\n \n Example\n \n mysql> CALL sys.ps_setup_show_disabled(TRUE, TRUE);\n +----------------------------+\n | performance_schema_enabled |\n +----------------------------+\n |                          1 |\n +----------------------------+\n 1 row in set (0.00 sec)\n \n +--------------------+\n | disabled_users     |\n +--------------------+\n | \'mark\'@\'localhost\' |\n +--------------------+\n 1 row in set (0.00 sec)\n \n +-------------+----------------------+---------+-------+\n | object_type | objects              | enabled | timed |\n +-------------+----------------------+---------+-------+\n | EVENT       | mysql.%              | NO      | NO    |\n | EVENT       | performance_schema.% | NO      | NO    |\n | EVENT       | information_schema.% | NO      | NO    |\n | FUNCTION    | mysql.%              | NO      | NO    |\n | FUNCTION    | performance_schema.% | NO      | NO    |\n | FUNCTION    | information_schema.% | NO      | NO    |\n | PROCEDURE   | mysql.%              | NO      | NO    |\n | PROCEDURE   | performance_schema.% | NO      | NO    |\n | PROCEDURE   | information_schema.% | NO      | NO    |\n | TABLE       | mysql.%              | NO      | NO    |\n | TABLE       | performance_schema.% | NO      | NO    |\n | TABLE       | information_schema.% | NO      | NO    |\n | TRIGGER     | mysql.%              | NO      | NO    |\n | TRIGGER     | performance_schema.% | NO      | NO    |\n | TRIGGER     | information_schema.% | NO      | NO    |\n +-------------+----------------------+---------+-------+\n 15 rows in set (0.00 sec)\n \n +----------------------------------+\n | disabled_consumers               |\n +----------------------------------+\n | events_stages_current            |\n | events_stages_history            |\n | events_stages_history_long       |\n | events_statements_history        |\n | events_statements_history_long   |\n | events_transactions_history      |\n | events_transactions_history_long |\n | events_waits_current             |\n | events_waits_history             |\n | events_waits_history_long        |\n +----------------------------------+\n 10 rows in set (0.00 sec)\n \n Empty set (0.00 sec)\n \n +---------------------------------------------------------------------------------------+-------+\n | disabled_instruments                                                                  | timed |\n +---------------------------------------------------------------------------------------+-------+\n | wait/synch/mutex/sql/TC_LOG_MMAP::LOCK_tc                                             | NO    |\n | wait/synch/mutex/sql/LOCK_des_key_file                                                | NO    |\n | wait/synch/mutex/sql/MYSQL_BIN_LOG::LOCK_commit                                       | NO    |\n ...\n | memory/sql/servers_cache                                                              | NO    |\n | memory/sql/udf_mem                                                                    | NO    |\n | wait/lock/metadata/sql/mdl                                                            | NO    |\n +---------------------------------------------------------------------------------------+-------+\n 547 rows in set (0.00 sec)\n \n Query OK, 0 rows affected (0.01 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN SELECT @@performance_schema AS performance_schema_enabled;   SELECT CONCAT(\'\'\', user, \'\'@\'\', host, \'\'\') AS disabled_users FROM performance_schema.setup_actors WHERE enabled = \'NO\' ORDER BY disabled_users;   SELECT object_type, CONCAT(object_schema, \'.\', object_name) AS objects, enabled, timed FROM performance_schema.setup_objects WHERE enabled = \'NO\' ORDER BY object_type, objects;  SELECT name AS disabled_consumers FROM performance_schema.setup_consumers WHERE enabled = \'NO\' ORDER BY disabled_consumers;  IF (in_show_threads) THEN SELECT IF(name = \'thread/sql/one_connection\',  CONCAT(processlist_user, \'@\', processlist_host),  REPLACE(name, \'thread/\', \'\')) AS disabled_threads, TYPE AS thread_type FROM performance_schema.threads WHERE INSTRUMENTED = \'NO\' ORDER BY disabled_threads; END IF;  IF (in_show_instruments) THEN SELECT name AS disabled_instruments, timed FROM performance_schema.setup_instruments WHERE enabled = \'NO\' ORDER BY disabled_instruments; END IF; END'),('sys','ps_setup_show_disabled_consumers','PROCEDURE','ps_setup_show_disabled_consumers','SQL','READS_SQL_DATA','YES','INVOKER','','',_binary 'BEGIN SELECT name AS disabled_consumers FROM performance_schema.setup_consumers WHERE enabled = \'NO\' ORDER BY disabled_consumers; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Shows all currently disabled consumers.\n \n Parameters\n \n None\n \n Example\n \n mysql> CALL sys.ps_setup_show_disabled_consumers();\n \n +---------------------------+\n | disabled_consumers        |\n +---------------------------+\n | events_statements_current |\n | global_instrumentation    |\n | thread_instrumentation    |\n | statements_digest         |\n +---------------------------+\n 4 rows in set (0.05 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN SELECT name AS disabled_consumers FROM performance_schema.setup_consumers WHERE enabled = \'NO\' ORDER BY disabled_consumers; END'),('sys','ps_setup_show_disabled_instruments','PROCEDURE','ps_setup_show_disabled_instruments','SQL','READS_SQL_DATA','YES','INVOKER','','',_binary 'BEGIN SELECT name AS disabled_instruments, timed FROM performance_schema.setup_instruments WHERE enabled = \'NO\' ORDER BY disabled_instruments; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Shows all currently disabled instruments.\n \n Parameters\n \n None\n \n Example\n \n mysql> CALL sys.ps_setup_show_disabled_instruments();\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN SELECT name AS disabled_instruments, timed FROM performance_schema.setup_instruments WHERE enabled = \'NO\' ORDER BY disabled_instruments; END'),('sys','ps_setup_show_enabled','PROCEDURE','ps_setup_show_enabled','SQL','READS_SQL_DATA','YES','INVOKER',_binary ' IN in_show_instruments BOOLEAN, IN in_show_threads BOOLEAN ','',_binary 'BEGIN SELECT @@performance_schema AS performance_schema_enabled;  SELECT CONCAT(\'\\\'\', user, \'\\\'@\\\'\', host, \'\\\'\') AS enabled_users FROM performance_schema.setup_actors  WHERE enabled = \'YES\'  ORDER BY enabled_users;  SELECT object_type, CONCAT(object_schema, \'.\', object_name) AS objects, enabled, timed FROM performance_schema.setup_objects WHERE enabled = \'YES\' ORDER BY object_type, objects;  SELECT name AS enabled_consumers FROM performance_schema.setup_consumers WHERE enabled = \'YES\' ORDER BY enabled_consumers;  IF (in_show_threads) THEN SELECT IF(name = \'thread/sql/one_connection\',  CONCAT(processlist_user, \'@\', processlist_host),  REPLACE(name, \'thread/\', \'\')) AS enabled_threads, TYPE AS thread_type FROM performance_schema.threads WHERE INSTRUMENTED = \'YES\' ORDER BY enabled_threads; END IF;  IF (in_show_instruments) THEN SELECT name AS enabled_instruments, timed FROM performance_schema.setup_instruments WHERE enabled = \'YES\' ORDER BY enabled_instruments; END IF; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Shows all currently enabled Performance Schema configuration.\n \n Parameters\n \n in_show_instruments (BOOLEAN):\n Whether to print enabled instruments (can print many items)\n \n in_show_threads (BOOLEAN):\n Whether to print enabled threads\n \n Example\n \n mysql> CALL sys.ps_setup_show_enabled(TRUE, TRUE);\n +----------------------------+\n | performance_schema_enabled |\n +----------------------------+\n |                          1 |\n +----------------------------+\n 1 row in set (0.00 sec)\n \n +---------------+\n | enabled_users |\n +---------------+\n | \'%\'@\'%\'       |\n +---------------+\n 1 row in set (0.01 sec)\n \n +-------------+---------+---------+-------+\n | object_type | objects | enabled | timed |\n +-------------+---------+---------+-------+\n | EVENT       | %.%     | YES     | YES   |\n | FUNCTION    | %.%     | YES     | YES   |\n | PROCEDURE   | %.%     | YES     | YES   |\n | TABLE       | %.%     | YES     | YES   |\n | TRIGGER     | %.%     | YES     | YES   |\n +-------------+---------+---------+-------+\n 5 rows in set (0.01 sec)\n \n +---------------------------+\n | enabled_consumers         |\n +---------------------------+\n | events_statements_current |\n | global_instrumentation    |\n | thread_instrumentation    |\n | statements_digest         |\n +---------------------------+\n 4 rows in set (0.05 sec)\n \n +---------------------------------+-------------+\n | enabled_threads                 | thread_type |\n +---------------------------------+-------------+\n | sql/main                        | BACKGROUND  |\n | sql/thread_timer_notifier       | BACKGROUND  |\n | innodb/io_ibuf_thread           | BACKGROUND  |\n | innodb/io_log_thread            | BACKGROUND  |\n | innodb/io_read_thread           | BACKGROUND  |\n | innodb/io_read_thread           | BACKGROUND  |\n | innodb/io_write_thread          | BACKGROUND  |\n | innodb/io_write_thread          | BACKGROUND  |\n | innodb/page_cleaner_thread      | BACKGROUND  |\n | innodb/srv_lock_timeout_thread  | BACKGROUND  |\n | innodb/srv_error_monitor_thread | BACKGROUND  |\n | innodb/srv_monitor_thread       | BACKGROUND  |\n | innodb/srv_master_thread        | BACKGROUND  |\n | innodb/srv_purge_thread         | BACKGROUND  |\n | innodb/srv_worker_thread        | BACKGROUND  |\n | innodb/srv_worker_thread        | BACKGROUND  |\n | innodb/srv_worker_thread        | BACKGROUND  |\n | innodb/buf_dump_thread          | BACKGROUND  |\n | innodb/dict_stats_thread        | BACKGROUND  |\n | sql/signal_handler              | BACKGROUND  |\n | sql/compress_gtid_table         | FOREGROUND  |\n | root@localhost                  | FOREGROUND  |\n +---------------------------------+-------------+\n 22 rows in set (0.01 sec)\n \n +-------------------------------------+-------+\n | enabled_instruments                 | timed |\n +-------------------------------------+-------+\n | wait/io/file/sql/map                | YES   |\n | wait/io/file/sql/binlog             | YES   |\n ...\n | statement/com/Error                 | YES   |\n | statement/com/                      | YES   |\n | idle                                | YES   |\n +-------------------------------------+-------+\n 210 rows in set (0.08 sec)\n \n Query OK, 0 rows affected (0.89 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN SELECT @@performance_schema AS performance_schema_enabled;  SELECT CONCAT(\'\'\', user, \'\'@\'\', host, \'\'\') AS enabled_users FROM performance_schema.setup_actors  WHERE enabled = \'YES\'  ORDER BY enabled_users;  SELECT object_type, CONCAT(object_schema, \'.\', object_name) AS objects, enabled, timed FROM performance_schema.setup_objects WHERE enabled = \'YES\' ORDER BY object_type, objects;  SELECT name AS enabled_consumers FROM performance_schema.setup_consumers WHERE enabled = \'YES\' ORDER BY enabled_consumers;  IF (in_show_threads) THEN SELECT IF(name = \'thread/sql/one_connection\',  CONCAT(processlist_user, \'@\', processlist_host),  REPLACE(name, \'thread/\', \'\')) AS enabled_threads, TYPE AS thread_type FROM performance_schema.threads WHERE INSTRUMENTED = \'YES\' ORDER BY enabled_threads; END IF;  IF (in_show_instruments) THEN SELECT name AS enabled_instruments, timed FROM performance_schema.setup_instruments WHERE enabled = \'YES\' ORDER BY enabled_instruments; END IF; END'),('sys','ps_setup_show_enabled_consumers','PROCEDURE','ps_setup_show_enabled_consumers','SQL','READS_SQL_DATA','YES','INVOKER','','',_binary 'BEGIN SELECT name AS enabled_consumers FROM performance_schema.setup_consumers WHERE enabled = \'YES\' ORDER BY enabled_consumers; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Shows all currently enabled consumers.\n \n Parameters\n \n None\n \n Example\n \n mysql> CALL sys.ps_setup_show_enabled_consumers();\n \n +---------------------------+\n | enabled_consumers         |\n +---------------------------+\n | events_statements_current |\n | global_instrumentation    |\n | thread_instrumentation    |\n | statements_digest         |\n +---------------------------+\n 4 rows in set (0.05 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN SELECT name AS enabled_consumers FROM performance_schema.setup_consumers WHERE enabled = \'YES\' ORDER BY enabled_consumers; END'),('sys','ps_setup_show_enabled_instruments','PROCEDURE','ps_setup_show_enabled_instruments','SQL','READS_SQL_DATA','YES','INVOKER','','',_binary 'BEGIN SELECT name AS enabled_instruments, timed FROM performance_schema.setup_instruments WHERE enabled = \'YES\' ORDER BY enabled_instruments; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Shows all currently enabled instruments.\n \n Parameters\n \n None\n \n Example\n \n mysql> CALL sys.ps_setup_show_enabled_instruments();\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN SELECT name AS enabled_instruments, timed FROM performance_schema.setup_instruments WHERE enabled = \'YES\' ORDER BY enabled_instruments; END'),('sys','ps_truncate_all_tables','PROCEDURE','ps_truncate_all_tables','SQL','MODIFIES_SQL_DATA','YES','INVOKER',_binary ' IN in_verbose BOOLEAN ','',_binary 'BEGIN DECLARE v_done INT DEFAULT FALSE; DECLARE v_total_tables INT DEFAULT 0; DECLARE v_ps_table VARCHAR(64); DECLARE ps_tables CURSOR FOR SELECT table_name  FROM INFORMATION_SCHEMA.TABLES  WHERE table_schema = \'performance_schema\'  AND (table_name LIKE \'%summary%\'  OR table_name LIKE \'%history%\'); DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;  OPEN ps_tables;  ps_tables_loop: LOOP FETCH ps_tables INTO v_ps_table; IF v_done THEN LEAVE ps_tables_loop; END IF;  SET @truncate_stmt := CONCAT(\'TRUNCATE TABLE performance_schema.\', v_ps_table); IF in_verbose THEN SELECT CONCAT(\'Running: \', @truncate_stmt) AS status; END IF;  PREPARE truncate_stmt FROM @truncate_stmt; EXECUTE truncate_stmt; DEALLOCATE PREPARE truncate_stmt;  SET v_total_tables = v_total_tables + 1; END LOOP;  CLOSE ps_tables;  SELECT CONCAT(\'Truncated \', v_total_tables, \' tables\') AS summary;  END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Truncates all summary tables within Performance Schema, \n resetting all aggregated instrumentation as a snapshot.\n \n Parameters\n \n in_verbose (BOOLEAN):\n Whether to print each TRUNCATE statement before running\n \n Example\n \n mysql> CALL sys.ps_truncate_all_tables(false);\n +---------------------+\n | summary             |\n +---------------------+\n | Truncated 44 tables |\n +---------------------+\n 1 row in set (0.10 sec)\n \n Query OK, 0 rows affected (0.10 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_done INT DEFAULT FALSE; DECLARE v_total_tables INT DEFAULT 0; DECLARE v_ps_table VARCHAR(64); DECLARE ps_tables CURSOR FOR SELECT table_name  FROM INFORMATION_SCHEMA.TABLES  WHERE table_schema = \'performance_schema\'  AND (table_name LIKE \'%summary%\'  OR table_name LIKE \'%history%\'); DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;  OPEN ps_tables;  ps_tables_loop: LOOP FETCH ps_tables INTO v_ps_table; IF v_done THEN LEAVE ps_tables_loop; END IF;  SET @truncate_stmt := CONCAT(\'TRUNCATE TABLE performance_schema.\', v_ps_table); IF in_verbose THEN SELECT CONCAT(\'Running: \', @truncate_stmt) AS status; END IF;  PREPARE truncate_stmt FROM @truncate_stmt; EXECUTE truncate_stmt; DEALLOCATE PREPARE truncate_stmt;  SET v_total_tables = v_total_tables + 1; END LOOP;  CLOSE ps_tables;  SELECT CONCAT(\'Truncated \', v_total_tables, \' tables\') AS summary;  END'),('sys','statement_performance_analyzer','PROCEDURE','statement_performance_analyzer','SQL','CONTAINS_SQL','NO','INVOKER',_binary ' IN in_action ENUM(\'snapshot\', \'overall\', \'delta\', \'create_table\', \'create_tmp\', \'save\', \'cleanup\'), IN in_table VARCHAR(129), IN in_views SET (\'with_runtimes_in_95th_percentile\', \'analysis\', \'with_errors_or_warnings\', \'with_full_table_scans\', \'with_sorting\', \'with_temp_tables\', \'custom\') ','',_binary 'BEGIN DECLARE v_table_exists, v_tmp_digests_table_exists, v_custom_view_exists ENUM(\'\', \'BASE TABLE\', \'VIEW\', \'TEMPORARY\') DEFAULT \'\'; DECLARE v_this_thread_enabled ENUM(\'YES\', \'NO\'); DECLARE v_force_new_snapshot BOOLEAN DEFAULT FALSE; DECLARE v_digests_table VARCHAR(133); DECLARE v_quoted_table, v_quoted_custom_view VARCHAR(133) DEFAULT \'\'; DECLARE v_table_db, v_table_name, v_custom_db, v_custom_name VARCHAR(64); DECLARE v_digest_table_template, v_checksum_ref, v_checksum_table text; DECLARE v_sql longtext; DECLARE v_error_msg VARCHAR(128);   SELECT INSTRUMENTED INTO v_this_thread_enabled FROM performance_schema.threads WHERE PROCESSLIST_ID = CONNECTION_ID(); IF (v_this_thread_enabled = \'YES\') THEN CALL sys.ps_setup_disable_thread(CONNECTION_ID()); END IF;  SET @log_bin := @@sql_log_bin; IF (@log_bin = 1) THEN SET sql_log_bin = 0; END IF;   IF (@sys.statement_performance_analyzer.limit IS NULL) THEN SET @sys.statement_performance_analyzer.limit = sys.sys_get_config(\'statement_performance_analyzer.limit\', \'100\'); END IF; IF (@sys.debug IS NULL) THEN SET @sys.debug                                = sys.sys_get_config(\'debug\'                               , \'OFF\'); END IF;   IF (in_table = \'NOW()\') THEN SET v_force_new_snapshot = TRUE, in_table             = NULL; ELSEIF (in_table IS NOT NULL) THEN IF (NOT INSTR(in_table, \'.\')) THEN SET v_table_db   = DATABASE(), v_table_name = in_table; ELSE SET v_table_db   = SUBSTRING_INDEX(in_table, \'.\', 1); SET v_table_name = SUBSTRING(in_table, CHAR_LENGTH(v_table_db)+2); END IF;  SET v_quoted_table = CONCAT(\'`\', v_table_db, \'`.`\', v_table_name, \'`\');  IF (@sys.debug = \'ON\') THEN SELECT CONCAT(\'in_table is: db = \'\'\', v_table_db, \'\'\', table = \'\'\', v_table_name, \'\'\'\') AS \'Debug\'; END IF;  IF (v_table_db = DATABASE() AND (v_table_name = \'tmp_digests\' OR v_table_name = \'tmp_digests_delta\')) THEN SET v_error_msg = CONCAT(\'Invalid value for in_table: \', v_quoted_table, \' is reserved table name.\'); SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = v_error_msg; END IF;  CALL sys.table_exists(v_table_db, v_table_name, v_table_exists); IF (@sys.debug = \'ON\') THEN SELECT CONCAT(\'v_table_exists = \', v_table_exists) AS \'Debug\'; END IF;  IF (v_table_exists = \'BASE TABLE\') THEN SET v_checksum_ref = ( SELECT GROUP_CONCAT(CONCAT(COLUMN_NAME, COLUMN_TYPE) ORDER BY ORDINAL_POSITION) AS Checksum FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = \'performance_schema\' AND TABLE_NAME = \'events_statements_summary_by_digest\' ), v_checksum_table = ( SELECT GROUP_CONCAT(CONCAT(COLUMN_NAME, COLUMN_TYPE) ORDER BY ORDINAL_POSITION) AS Checksum FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = v_table_db AND TABLE_NAME = v_table_name );  IF (v_checksum_ref <> v_checksum_table) THEN SET v_error_msg = CONCAT(\'The table \', IF(CHAR_LENGTH(v_quoted_table) > 93, CONCAT(\'...\', SUBSTRING(v_quoted_table, -90)), v_quoted_table), \' has the wrong definition.\'); SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = v_error_msg; END IF; END IF; END IF;   IF (in_views IS NULL OR in_views = \'\') THEN SET in_views = \'with_runtimes_in_95th_percentile,analysis,with_errors_or_warnings,with_full_table_scans,with_sorting,with_temp_tables\'; END IF;   CALL sys.table_exists(DATABASE(), \'tmp_digests\', v_tmp_digests_table_exists); IF (@sys.debug = \'ON\') THEN SELECT CONCAT(\'v_tmp_digests_table_exists = \', v_tmp_digests_table_exists) AS \'Debug\'; END IF;  CASE WHEN in_action IN (\'snapshot\', \'overall\') THEN IF (in_table IS NOT NULL) THEN IF (NOT v_table_exists IN (\'TEMPORARY\', \'BASE TABLE\')) THEN SET v_error_msg = CONCAT(\'The \', in_action, \' action requires in_table to be NULL, NOW() or specify an existing table.\', \' The table \', IF(CHAR_LENGTH(v_quoted_table) > 16, CONCAT(\'...\', SUBSTRING(v_quoted_table, -13)), v_quoted_table), \' does not exist.\'); SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = v_error_msg; END IF; END IF;  WHEN in_action IN (\'delta\', \'save\') THEN IF (v_table_exists NOT IN (\'TEMPORARY\', \'BASE TABLE\')) THEN SET v_error_msg = CONCAT(\'The \', in_action, \' action requires in_table to be an existing table.\', IF(in_table IS NOT NULL, CONCAT(\' The table \', IF(CHAR_LENGTH(v_quoted_table) > 39, CONCAT(\'...\', SUBSTRING(v_quoted_table, -36)), v_quoted_table), \' does not exist.\'), \'\')); SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = v_error_msg; END IF;  IF (in_action = \'delta\' AND v_tmp_digests_table_exists <> \'TEMPORARY\') THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'An existing snapshot generated with the statement_performance_analyzer() must exist.\'; END IF; WHEN in_action = \'create_tmp\' THEN IF (v_table_exists = \'TEMPORARY\') THEN SET v_error_msg = CONCAT(\'Cannot create the table \', IF(CHAR_LENGTH(v_quoted_table) > 72, CONCAT(\'...\', SUBSTRING(v_quoted_table, -69)), v_quoted_table), \' as it already exists.\'); SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = v_error_msg; END IF;  WHEN in_action = \'create_table\' THEN IF (v_table_exists <> \'\') THEN SET v_error_msg = CONCAT(\'Cannot create the table \', IF(CHAR_LENGTH(v_quoted_table) > 52, CONCAT(\'...\', SUBSTRING(v_quoted_table, -49)), v_quoted_table), \' as it already exists\', IF(v_table_exists = \'TEMPORARY\', \' as a temporary table.\', \'.\')); SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = v_error_msg; END IF;  WHEN in_action = \'cleanup\' THEN DO (SELECT 1); ELSE SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'Unknown action. Supported actions are: cleanup, create_table, create_tmp, delta, overall, save, snapshot\'; END CASE;  SET v_digest_table_template = \'CREATE %{TEMPORARY}TABLE %{TABLE_NAME} ( `SCHEMA_NAME` varchar(64) DEFAULT NULL, `DIGEST` varchar(32) DEFAULT NULL, `DIGEST_TEXT` longtext, `COUNT_STAR` bigint(20) unsigned NOT NULL, `SUM_TIMER_WAIT` bigint(20) unsigned NOT NULL, `MIN_TIMER_WAIT` bigint(20) unsigned NOT NULL, `AVG_TIMER_WAIT` bigint(20) unsigned NOT NULL, `MAX_TIMER_WAIT` bigint(20) unsigned NOT NULL, `SUM_LOCK_TIME` bigint(20) unsigned NOT NULL, `SUM_ERRORS` bigint(20) unsigned NOT NULL, `SUM_WARNINGS` bigint(20) unsigned NOT NULL, `SUM_ROWS_AFFECTED` bigint(20) unsigned NOT NULL, `SUM_ROWS_SENT` bigint(20) unsigned NOT NULL, `SUM_ROWS_EXAMINED` bigint(20) unsigned NOT NULL, `SUM_CREATED_TMP_DISK_TABLES` bigint(20) unsigned NOT NULL, `SUM_CREATED_TMP_TABLES` bigint(20) unsigned NOT NULL, `SUM_SELECT_FULL_JOIN` bigint(20) unsigned NOT NULL, `SUM_SELECT_FULL_RANGE_JOIN` bigint(20) unsigned NOT NULL, `SUM_SELECT_RANGE` bigint(20) unsigned NOT NULL, `SUM_SELECT_RANGE_CHECK` bigint(20) unsigned NOT NULL, `SUM_SELECT_SCAN` bigint(20) unsigned NOT NULL, `SUM_SORT_MERGE_PASSES` bigint(20) unsigned NOT NULL, `SUM_SORT_RANGE` bigint(20) unsigned NOT NULL, `SUM_SORT_ROWS` bigint(20) unsigned NOT NULL, `SUM_SORT_SCAN` bigint(20) unsigned NOT NULL, `SUM_NO_INDEX_USED` bigint(20) unsigned NOT NULL, `SUM_NO_GOOD_INDEX_USED` bigint(20) unsigned NOT NULL, `FIRST_SEEN` timestamp NULL DEFAULT NULL, `LAST_SEEN` timestamp NULL DEFAULT NULL, INDEX (SCHEMA_NAME, DIGEST) ) DEFAULT CHARSET=utf8mb3\';  IF (v_force_new_snapshot OR in_action = \'snapshot\' OR (in_action = \'overall\' AND in_table IS NULL) OR (in_action = \'save\' AND v_tmp_digests_table_exists <> \'TEMPORARY\') ) THEN IF (v_tmp_digests_table_exists = \'TEMPORARY\') THEN IF (@sys.debug = \'ON\') THEN SELECT \'DROP TEMPORARY TABLE IF EXISTS tmp_digests\' AS \'Debug\'; END IF; DROP TEMPORARY TABLE IF EXISTS tmp_digests; END IF; CALL sys.execute_prepared_stmt(REPLACE(REPLACE(v_digest_table_template, \'%{TEMPORARY}\', \'TEMPORARY \'), \'%{TABLE_NAME}\', \'tmp_digests\'));  SET v_sql = CONCAT(\'INSERT INTO tmp_digests SELECT * FROM \', IF(in_table IS NULL OR in_action = \'save\', \'performance_schema.events_statements_summary_by_digest\', v_quoted_table)); CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (in_action IN (\'create_table\', \'create_tmp\')) THEN IF (in_action = \'create_table\') THEN CALL sys.execute_prepared_stmt(REPLACE(REPLACE(v_digest_table_template, \'%{TEMPORARY}\', \'\'), \'%{TABLE_NAME}\', v_quoted_table)); ELSE CALL sys.execute_prepared_stmt(REPLACE(REPLACE(v_digest_table_template, \'%{TEMPORARY}\', \'TEMPORARY \'), \'%{TABLE_NAME}\', v_quoted_table)); END IF; ELSEIF (in_action = \'save\') THEN CALL sys.execute_prepared_stmt(CONCAT(\'DELETE FROM \', v_quoted_table)); CALL sys.execute_prepared_stmt(CONCAT(\'INSERT INTO \', v_quoted_table, \' SELECT * FROM tmp_digests\')); ELSEIF (in_action = \'cleanup\') THEN DROP TEMPORARY TABLE IF EXISTS sys.tmp_digests; DROP TEMPORARY TABLE IF EXISTS sys.tmp_digests_delta; ELSEIF (in_action IN (\'overall\', \'delta\')) THEN IF (in_action = \'overall\') THEN IF (in_table IS NULL) THEN SET v_digests_table = \'tmp_digests\'; ELSE SET v_digests_table = v_quoted_table; END IF; ELSE SET v_digests_table = \'tmp_digests_delta\'; DROP TEMPORARY TABLE IF EXISTS tmp_digests_delta; CREATE TEMPORARY TABLE tmp_digests_delta LIKE tmp_digests; SET v_sql = CONCAT(\'INSERT INTO tmp_digests_delta SELECT `d_end`.`SCHEMA_NAME`, `d_end`.`DIGEST`, `d_end`.`DIGEST_TEXT`, `d_end`.`COUNT_STAR`-IFNULL(`d_start`.`COUNT_STAR`, 0) AS \'\'COUNT_STAR\'\', `d_end`.`SUM_TIMER_WAIT`-IFNULL(`d_start`.`SUM_TIMER_WAIT`, 0) AS \'\'SUM_TIMER_WAIT\'\', `d_end`.`MIN_TIMER_WAIT` AS \'\'MIN_TIMER_WAIT\'\', IFNULL((`d_end`.`SUM_TIMER_WAIT`-IFNULL(`d_start`.`SUM_TIMER_WAIT`, 0))/NULLIF(`d_end`.`COUNT_STAR`-IFNULL(`d_start`.`COUNT_STAR`, 0), 0), 0) AS \'\'AVG_TIMER_WAIT\'\', `d_end`.`MAX_TIMER_WAIT` AS \'\'MAX_TIMER_WAIT\'\', `d_end`.`SUM_LOCK_TIME`-IFNULL(`d_start`.`SUM_LOCK_TIME`, 0) AS \'\'SUM_LOCK_TIME\'\', `d_end`.`SUM_ERRORS`-IFNULL(`d_start`.`SUM_ERRORS`, 0) AS \'\'SUM_ERRORS\'\', `d_end`.`SUM_WARNINGS`-IFNULL(`d_start`.`SUM_WARNINGS`, 0) AS \'\'SUM_WARNINGS\'\', `d_end`.`SUM_ROWS_AFFECTED`-IFNULL(`d_start`.`SUM_ROWS_AFFECTED`, 0) AS \'\'SUM_ROWS_AFFECTED\'\', `d_end`.`SUM_ROWS_SENT`-IFNULL(`d_start`.`SUM_ROWS_SENT`, 0) AS \'\'SUM_ROWS_SENT\'\', `d_end`.`SUM_ROWS_EXAMINED`-IFNULL(`d_start`.`SUM_ROWS_EXAMINED`, 0) AS \'\'SUM_ROWS_EXAMINED\'\', `d_end`.`SUM_CREATED_TMP_DISK_TABLES`-IFNULL(`d_start`.`SUM_CREATED_TMP_DISK_TABLES`, 0) AS \'\'SUM_CREATED_TMP_DISK_TABLES\'\', `d_end`.`SUM_CREATED_TMP_TABLES`-IFNULL(`d_start`.`SUM_CREATED_TMP_TABLES`, 0) AS \'\'SUM_CREATED_TMP_TABLES\'\', `d_end`.`SUM_SELECT_FULL_JOIN`-IFNULL(`d_start`.`SUM_SELECT_FULL_JOIN`, 0) AS \'\'SUM_SELECT_FULL_JOIN\'\', `d_end`.`SUM_SELECT_FULL_RANGE_JOIN`-IFNULL(`d_start`.`SUM_SELECT_FULL_RANGE_JOIN`, 0) AS \'\'SUM_SELECT_FULL_RANGE_JOIN\'\', `d_end`.`SUM_SELECT_RANGE`-IFNULL(`d_start`.`SUM_SELECT_RANGE`, 0) AS \'\'SUM_SELECT_RANGE\'\', `d_end`.`SUM_SELECT_RANGE_CHECK`-IFNULL(`d_start`.`SUM_SELECT_RANGE_CHECK`, 0) AS \'\'SUM_SELECT_RANGE_CHECK\'\', `d_end`.`SUM_SELECT_SCAN`-IFNULL(`d_start`.`SUM_SELECT_SCAN`, 0) AS \'\'SUM_SELECT_SCAN\'\', `d_end`.`SUM_SORT_MERGE_PASSES`-IFNULL(`d_start`.`SUM_SORT_MERGE_PASSES`, 0) AS \'\'SUM_SORT_MERGE_PASSES\'\', `d_end`.`SUM_SORT_RANGE`-IFNULL(`d_start`.`SUM_SORT_RANGE`, 0) AS \'\'SUM_SORT_RANGE\'\', `d_end`.`SUM_SORT_ROWS`-IFNULL(`d_start`.`SUM_SORT_ROWS`, 0) AS \'\'SUM_SORT_ROWS\'\', `d_end`.`SUM_SORT_SCAN`-IFNULL(`d_start`.`SUM_SORT_SCAN`, 0) AS \'\'SUM_SORT_SCAN\'\', `d_end`.`SUM_NO_INDEX_USED`-IFNULL(`d_start`.`SUM_NO_INDEX_USED`, 0) AS \'\'SUM_NO_INDEX_USED\'\', `d_end`.`SUM_NO_GOOD_INDEX_USED`-IFNULL(`d_start`.`SUM_NO_GOOD_INDEX_USED`, 0) AS \'\'SUM_NO_GOOD_INDEX_USED\'\', `d_end`.`FIRST_SEEN`, `d_end`.`LAST_SEEN` FROM tmp_digests d_end LEFT OUTER JOIN \', v_quoted_table, \' d_start ON `d_start`.`DIGEST` = `d_end`.`DIGEST` AND (`d_start`.`SCHEMA_NAME` = `d_end`.`SCHEMA_NAME` OR (`d_start`.`SCHEMA_NAME` IS NULL AND `d_end`.`SCHEMA_NAME` IS NULL) ) WHERE `d_end`.`COUNT_STAR`-IFNULL(`d_start`.`COUNT_STAR`, 0) > 0\'); CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (FIND_IN_SET(\'with_runtimes_in_95th_percentile\', in_views)) THEN SELECT \'Queries with Runtime in 95th Percentile\' AS \'Next Output\';  DROP TEMPORARY TABLE IF EXISTS tmp_digest_avg_latency_distribution1; DROP TEMPORARY TABLE IF EXISTS tmp_digest_avg_latency_distribution2; DROP TEMPORARY TABLE IF EXISTS tmp_digest_95th_percentile_by_avg_us;  CREATE TEMPORARY TABLE tmp_digest_avg_latency_distribution1 ( cnt bigint unsigned NOT NULL, avg_us decimal(21,0) NOT NULL, PRIMARY KEY (avg_us) ) ENGINE=InnoDB;  SET v_sql = CONCAT(\'INSERT INTO tmp_digest_avg_latency_distribution1 SELECT COUNT(*) cnt, ROUND(avg_timer_wait/1000000) AS avg_us FROM \', v_digests_table, \' GROUP BY avg_us\'); CALL sys.execute_prepared_stmt(v_sql);  CREATE TEMPORARY TABLE tmp_digest_avg_latency_distribution2 LIKE tmp_digest_avg_latency_distribution1; INSERT INTO tmp_digest_avg_latency_distribution2 SELECT * FROM tmp_digest_avg_latency_distribution1;  CREATE TEMPORARY TABLE tmp_digest_95th_percentile_by_avg_us ( avg_us decimal(21,0) NOT NULL, percentile decimal(46,4) NOT NULL, PRIMARY KEY (avg_us) ) ENGINE=InnoDB;  SET v_sql = CONCAT(\'INSERT INTO tmp_digest_95th_percentile_by_avg_us SELECT s2.avg_us avg_us, IFNULL(SUM(s1.cnt)/NULLIF((SELECT COUNT(*) FROM \', v_digests_table, \'), 0), 0) percentile FROM tmp_digest_avg_latency_distribution1 AS s1 JOIN tmp_digest_avg_latency_distribution2 AS s2 ON s1.avg_us <= s2.avg_us GROUP BY s2.avg_us HAVING percentile > 0.95 ORDER BY percentile LIMIT 1\'); CALL sys.execute_prepared_stmt(v_sql);  SET v_sql = REPLACE( REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'statements_with_runtimes_in_95th_percentile\' ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ), \'sys.x$ps_digest_95th_percentile_by_avg_us\', \'`sys`.`x$ps_digest_95th_percentile_by_avg_us`\' ); CALL sys.execute_prepared_stmt(v_sql);  DROP TEMPORARY TABLE tmp_digest_avg_latency_distribution1; DROP TEMPORARY TABLE tmp_digest_avg_latency_distribution2; DROP TEMPORARY TABLE tmp_digest_95th_percentile_by_avg_us; END IF;  IF (FIND_IN_SET(\'analysis\', in_views)) THEN SELECT CONCAT(\'Top \', @sys.statement_performance_analyzer.limit, \' Queries Ordered by Total Latency\') AS \'Next Output\'; SET v_sql = REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'statement_analysis\' ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ); IF (@sys.statement_performance_analyzer.limit > 0) THEN SET v_sql = CONCAT(v_sql, \' LIMIT \', @sys.statement_performance_analyzer.limit); END IF; CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (FIND_IN_SET(\'with_errors_or_warnings\', in_views)) THEN SELECT CONCAT(\'Top \', @sys.statement_performance_analyzer.limit, \' Queries with Errors\') AS \'Next Output\'; SET v_sql = REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'statements_with_errors_or_warnings\' ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ); IF (@sys.statement_performance_analyzer.limit > 0) THEN SET v_sql = CONCAT(v_sql, \' LIMIT \', @sys.statement_performance_analyzer.limit); END IF; CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (FIND_IN_SET(\'with_full_table_scans\', in_views)) THEN SELECT CONCAT(\'Top \', @sys.statement_performance_analyzer.limit, \' Queries with Full Table Scan\') AS \'Next Output\'; SET v_sql = REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'statements_with_full_table_scans\' ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ); IF (@sys.statement_performance_analyzer.limit > 0) THEN SET v_sql = CONCAT(v_sql, \' LIMIT \', @sys.statement_performance_analyzer.limit); END IF; CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (FIND_IN_SET(\'with_sorting\', in_views)) THEN SELECT CONCAT(\'Top \', @sys.statement_performance_analyzer.limit, \' Queries with Sorting\') AS \'Next Output\'; SET v_sql = REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'statements_with_sorting\' ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ); IF (@sys.statement_performance_analyzer.limit > 0) THEN SET v_sql = CONCAT(v_sql, \' LIMIT \', @sys.statement_performance_analyzer.limit); END IF; CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (FIND_IN_SET(\'with_temp_tables\', in_views)) THEN SELECT CONCAT(\'Top \', @sys.statement_performance_analyzer.limit, \' Queries with Internal Temporary Tables\') AS \'Next Output\'; SET v_sql = REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'statements_with_temp_tables\' ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ); IF (@sys.statement_performance_analyzer.limit > 0) THEN SET v_sql = CONCAT(v_sql, \' LIMIT \', @sys.statement_performance_analyzer.limit); END IF; CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (FIND_IN_SET(\'custom\', in_views)) THEN SELECT CONCAT(\'Top \', @sys.statement_performance_analyzer.limit, \' Queries Using Custom View\') AS \'Next Output\';  IF (@sys.statement_performance_analyzer.view IS NULL) THEN SET @sys.statement_performance_analyzer.view = sys.sys_get_config(\'statement_performance_analyzer.view\', NULL); END IF; IF (@sys.statement_performance_analyzer.view IS NULL) THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'The @sys.statement_performance_analyzer.view user variable must be set with the view or query to use.\'; END IF;  IF (NOT INSTR(@sys.statement_performance_analyzer.view, \' \')) THEN IF (NOT INSTR(@sys.statement_performance_analyzer.view, \'.\')) THEN SET v_custom_db   = DATABASE(), v_custom_name = @sys.statement_performance_analyzer.view; ELSE SET v_custom_db   = SUBSTRING_INDEX(@sys.statement_performance_analyzer.view, \'.\', 1); SET v_custom_name = SUBSTRING(@sys.statement_performance_analyzer.view, CHAR_LENGTH(v_custom_db)+2); END IF;  CALL sys.table_exists(v_custom_db, v_custom_name, v_custom_view_exists); IF (v_custom_view_exists <> \'VIEW\') THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'The @sys.statement_performance_analyzer.view user variable is set but specified neither an existing view nor a query.\'; END IF;  SET v_sql = REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = v_custom_db AND TABLE_NAME = v_custom_name ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ); ELSE SET v_sql = REPLACE(@sys.statement_performance_analyzer.view, \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table); END IF;  IF (@sys.statement_performance_analyzer.limit > 0) THEN SET v_sql = CONCAT(v_sql, \' LIMIT \', @sys.statement_performance_analyzer.limit); END IF;  CALL sys.execute_prepared_stmt(v_sql); END IF; END IF;  IF (v_this_thread_enabled = \'YES\') THEN CALL sys.ps_setup_enable_thread(CONNECTION_ID()); END IF;  IF (@log_bin = 1) THEN SET sql_log_bin = @log_bin; END IF; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Create a report of the statements running on the server.\n \n The views are calculated based on the overall and/or delta activity.\n \n Requires the SUPER privilege for \"SET sql_log_bin = 0;\".\n \n Parameters\n \n in_action (ENUM(\'snapshot\', \'overall\', \'delta\', \'create_tmp\', \'create_table\', \'save\', \'cleanup\')):\n The action to take. Supported actions are:\n * snapshot      Store a snapshot. The default is to make a snapshot of the current content of\n performance_schema.events_statements_summary_by_digest, but by setting in_table\n this can be overwritten to copy the content of the specified table.\n The snapshot is stored in the sys.tmp_digests temporary table.\n * overall       Generate analyzis based on the content specified by in_table. For the overall analyzis,\n in_table can be NOW() to use a fresh snapshot. This will overwrite an existing snapshot.\n Use NULL for in_table to use the existing snapshot. If in_table IS NULL and no snapshot\n exists, a new will be created.\n See also in_views and @sys.statement_performance_analyzer.limit.\n * delta         Generate a delta analysis. The delta will be calculated between the reference table in\n in_table and the snapshot. An existing snapshot must exist.\n The action uses the sys.tmp_digests_delta temporary table.\n See also in_views and @sys.statement_performance_analyzer.limit.\n * create_table  Create a regular table suitable for storing the snapshot for later use, e.g. for\n calculating deltas.\n * create_tmp    Create a temporary table suitable for storing the snapshot for later use, e.g. for\n calculating deltas.\n * save          Save the snapshot in the table specified by in_table. The table must exists and have\n the correct structure.\n If no snapshot exists, a new is created.\n * cleanup       Remove the temporary tables used for the snapshot and delta.\n \n in_table (VARCHAR(129)):\n The table argument used for some actions. Use the format \'db1.t1\' or \'t1\' without using any backticks (`)\n for quoting. Periods (.) are not supported in the database and table names.\n \n The meaning of the table for each action supporting the argument is:\n \n * snapshot      The snapshot is created based on the specified table. Set to NULL or NOW() to use\n the current content of performance_schema.events_statements_summary_by_digest.\n * overall       The table with the content to create the overall analyzis for. The following values\n can be used:\n - A table name - use the content of that table.\n - NOW()        - create a fresh snapshot and overwrite the existing snapshot.\n - NULL         - use the last stored snapshot.\n * delta         The table name is mandatory and specified the reference view to compare the currently\n stored snapshot against. If no snapshot exists, a new will be created.\n * create_table  The name of the regular table to create.\n * create_tmp    The name of the temporary table to create.\n * save          The name of the table to save the currently stored snapshot into.\n \n in_views (SET (\'with_runtimes_in_95th_percentile\', \'analysis\', \'with_errors_or_warnings\',\n \'with_full_table_scans\', \'with_sorting\', \'with_temp_tables\', \'custom\'))\n Which views to include:  * with_runtimes_in_95th_percentile  Based on the sys.statements_with_runtimes_in_95th_percentile view * analysis                          Based on the sys.statement_analysis view * with_errors_or_warnings           Based on the sys.statements_with_errors_or_warnings view * with_full_table_scans             Based on the sys.statements_with_full_table_scans view * with_sorting                      Based on the sys.statements_with_sorting view * with_temp_tables                  Based on the sys.statements_with_temp_tables view * custom                            Use a custom view. This view must be specified in @sys.statement_performance_analyzer.view to an existing view or a query  Default is to include all except \'custom\'.   Configuration Options  sys.statement_performance_analyzer.limit The maximum number of rows to include for the views that does not have a built-in limit (e.g. the 95th percentile view). If not set the limit is 100.  sys.statement_performance_analyzer.view Used together with the \'custom\' view. If the value contains a space, it is considered a query, otherwise it must be an existing view querying the performance_schema.events_statements_summary_by_digest table. There cannot be any limit clause including in the query or view definition if @sys.statement_performance_analyzer.limit > 0. If specifying a view, use the same format as for in_table.  sys.debug Whether to provide debugging output. Default is \'OFF\'. Set to \'ON\' to include.   Example  To create a report with the queries in the 95th percentile since last truncate of performance_schema.events_statements_summary_by_digest and the delta for a 1 minute period:  1. Create a temporary table to store the initial snapshot. 2. Create the initial snapshot. 3. Save the initial snapshot in the temporary table. 4. Wait one minute. 5. Create a new snapshot. 6. Perform analyzis based on the new snapshot. 7. Perform analyzis based on the delta between the initial and new snapshots.  mysql> CALL sys.statement_performance_analyzer(\'create_tmp\', \'mydb.tmp_digests_ini\', NULL); Query OK, 0 rows affected (0.08 sec)  mysql> CALL sys.statement_performance_analyzer(\'snapshot\', NULL, NULL); Query OK, 0 rows affected (0.02 sec)  mysql> CALL sys.statement_performance_analyzer(\'save\', \'mydb.tmp_digests_ini\', NULL); Query OK, 0 rows affected (0.00 sec)  mysql> DO SLEEP(60); Query OK, 0 rows affected (1 min 0.00 sec)  mysql> CALL sys.statement_performance_analyzer(\'snapshot\', NULL, NULL); Query OK, 0 rows affected (0.02 sec)  mysql> CALL sys.statement_performance_analyzer(\'overall\', NULL, \'with_runtimes_in_95th_percentile\'); +-----------------------------------------+ | Next Output                             | +-----------------------------------------+ | Queries with Runtime in 95th Percentile | +-----------------------------------------+ 1 <USER> <GROUP> set (0.05 sec)  ...  mysql> CALL sys.statement_performance_analyzer(\'delta\', \'mydb.tmp_digests_ini\', \'with_runtimes_in_95th_percentile\'); +-----------------------------------------+ | Next Output                             | +-----------------------------------------+ | Queries with Runtime in 95th Percentile | +-----------------------------------------+ 1 <USER> <GROUP> set (0.03 sec)  ...   To create an overall report of the 95th percentile queries and the top 10 queries with full table scans:  mysql> CALL sys.statement_performance_analyzer(\'snapshot\', NULL, NULL); Query OK, 0 rows affected (0.01 sec)  mysql> SET @sys.statement_performance_analyzer.limit = 10; Query OK, 0 rows affected (0.00 sec)  mysql> CALL sys.statement_performance_analyzer(\'overall\', NULL, \'with_runtimes_in_95th_percentile,with_full_table_scans\'); +-----------------------------------------+ | Next Output                             | +-----------------------------------------+ | Queries with Runtime in 95th Percentile | +-----------------------------------------+ 1 <USER> <GROUP> set (0.01 sec)  ...  +-------------------------------------+ | Next Output                         | +-------------------------------------+ | Top 10 Queries with Full Table Scan | +-------------------------------------+ 1 <USER> <GROUP> set (0.09 sec)  ...   Use a custom view showing the top 10 query sorted by total execution time refreshing the view every minute using the watch command in Linux.  mysql> CREATE OR REPLACE VIEW mydb.my_statements AS -> SELECT sys.format_statement(DIGEST_TEXT) AS query, ->        SCHEMA_NAME AS db, ->        COUNT_STAR AS exec_count, ->        sys.format_time(SUM_TIMER_WAIT) AS total_latency, ->        sys.format_time(AVG_TIMER_WAIT) AS avg_latency, ->        ROUND(IFNULL(SUM_ROWS_SENT / NULLIF(COUNT_STAR, 0), 0)) AS rows_sent_avg, ->        ROUND(IFNULL(SUM_ROWS_EXAMINED / NULLIF(COUNT_STAR, 0), 0)) AS rows_examined_avg, ->        ROUND(IFNULL(SUM_ROWS_AFFECTED / NULLIF(COUNT_STAR, 0), 0)) AS rows_affected_avg, ->        DIGEST AS digest ->   FROM performance_schema.events_statements_summary_by_digest -> ORDER BY SUM_TIMER_WAIT DESC; Query OK, 0 rows affected (0.01 sec)  mysql> CALL sys.statement_performance_analyzer(\'create_table\', \'mydb.digests_prev\', NULL); Query OK, 0 rows affected (0.10 sec)  shell$ watch -n 60 \"mysql sys --table -e \" > SET @sys.statement_performance_analyzer.view = \'mydb.my_statements\'; > SET @sys.statement_performance_analyzer.limit = 10; > CALL statement_performance_analyzer(\'snapshot\', NULL, NULL); > CALL statement_performance_analyzer(\'delta\', \'mydb.digests_prev\', \'custom\'); > CALL statement_performance_analyzer(\'save\', \'mydb.digests_prev\', NULL); > \"\"  Every 60.0s: mysql sys --table -e \"                                                                                                   ...  Mon Dec 22 10:58:51 2014  +----------------------------------+ | Next Output                      | +----------------------------------+ | Top 10 Queries Using Custom View | +----------------------------------+ +-------------------+-------+------------+---------------+-------------+---------------+-------------------+-------------------+----------------------------------+ | query             | db    | exec_count | total_latency | avg_latency | rows_sent_avg | rows_examined_avg | rows_affected_avg | digest                           | +-------------------+-------+------------+---------------+-------------+---------------+-------------------+-------------------+----------------------------------+ ... ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_table_exists, v_tmp_digests_table_exists, v_custom_view_exists ENUM(\'\', \'BASE TABLE\', \'VIEW\', \'TEMPORARY\') DEFAULT \'\'; DECLARE v_this_thread_enabled ENUM(\'YES\', \'NO\'); DECLARE v_force_new_snapshot BOOLEAN DEFAULT FALSE; DECLARE v_digests_table VARCHAR(133); DECLARE v_quoted_table, v_quoted_custom_view VARCHAR(133) DEFAULT \'\'; DECLARE v_table_db, v_table_name, v_custom_db, v_custom_name VARCHAR(64); DECLARE v_digest_table_template, v_checksum_ref, v_checksum_table text; DECLARE v_sql longtext; DECLARE v_error_msg VARCHAR(128);   SELECT INSTRUMENTED INTO v_this_thread_enabled FROM performance_schema.threads WHERE PROCESSLIST_ID = CONNECTION_ID(); IF (v_this_thread_enabled = \'YES\') THEN CALL sys.ps_setup_disable_thread(CONNECTION_ID()); END IF;  SET @log_bin := @@sql_log_bin; IF (@log_bin = 1) THEN SET sql_log_bin = 0; END IF;   IF (@sys.statement_performance_analyzer.limit IS NULL) THEN SET @sys.statement_performance_analyzer.limit = sys.sys_get_config(\'statement_performance_analyzer.limit\', \'100\'); END IF; IF (@sys.debug IS NULL) THEN SET @sys.debug                                = sys.sys_get_config(\'debug\'                               , \'OFF\'); END IF;   IF (in_table = \'NOW()\') THEN SET v_force_new_snapshot = TRUE, in_table             = NULL; ELSEIF (in_table IS NOT NULL) THEN IF (NOT INSTR(in_table, \'.\')) THEN SET v_table_db   = DATABASE(), v_table_name = in_table; ELSE SET v_table_db   = SUBSTRING_INDEX(in_table, \'.\', 1); SET v_table_name = SUBSTRING(in_table, CHAR_LENGTH(v_table_db)+2); END IF;  SET v_quoted_table = CONCAT(\'`\', v_table_db, \'`.`\', v_table_name, \'`\');  IF (@sys.debug = \'ON\') THEN SELECT CONCAT(\'in_table is: db = \'\', v_table_db, \'\', table = \'\', v_table_name, \'\'\') AS \'Debug\'; END IF;  IF (v_table_db = DATABASE() AND (v_table_name = \'tmp_digests\' OR v_table_name = \'tmp_digests_delta\')) THEN SET v_error_msg = CONCAT(\'Invalid value for in_table: \', v_quoted_table, \' is reserved table name.\'); SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = v_error_msg; END IF;  CALL sys.table_exists(v_table_db, v_table_name, v_table_exists); IF (@sys.debug = \'ON\') THEN SELECT CONCAT(\'v_table_exists = \', v_table_exists) AS \'Debug\'; END IF;  IF (v_table_exists = \'BASE TABLE\') THEN SET v_checksum_ref = ( SELECT GROUP_CONCAT(CONCAT(COLUMN_NAME, COLUMN_TYPE) ORDER BY ORDINAL_POSITION) AS Checksum FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = \'performance_schema\' AND TABLE_NAME = \'events_statements_summary_by_digest\' ), v_checksum_table = ( SELECT GROUP_CONCAT(CONCAT(COLUMN_NAME, COLUMN_TYPE) ORDER BY ORDINAL_POSITION) AS Checksum FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = v_table_db AND TABLE_NAME = v_table_name );  IF (v_checksum_ref <> v_checksum_table) THEN SET v_error_msg = CONCAT(\'The table \', IF(CHAR_LENGTH(v_quoted_table) > 93, CONCAT(\'...\', SUBSTRING(v_quoted_table, -90)), v_quoted_table), \' has the wrong definition.\'); SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = v_error_msg; END IF; END IF; END IF;   IF (in_views IS NULL OR in_views = \'\') THEN SET in_views = \'with_runtimes_in_95th_percentile,analysis,with_errors_or_warnings,with_full_table_scans,with_sorting,with_temp_tables\'; END IF;   CALL sys.table_exists(DATABASE(), \'tmp_digests\', v_tmp_digests_table_exists); IF (@sys.debug = \'ON\') THEN SELECT CONCAT(\'v_tmp_digests_table_exists = \', v_tmp_digests_table_exists) AS \'Debug\'; END IF;  CASE WHEN in_action IN (\'snapshot\', \'overall\') THEN IF (in_table IS NOT NULL) THEN IF (NOT v_table_exists IN (\'TEMPORARY\', \'BASE TABLE\')) THEN SET v_error_msg = CONCAT(\'The \', in_action, \' action requires in_table to be NULL, NOW() or specify an existing table.\', \' The table \', IF(CHAR_LENGTH(v_quoted_table) > 16, CONCAT(\'...\', SUBSTRING(v_quoted_table, -13)), v_quoted_table), \' does not exist.\'); SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = v_error_msg; END IF; END IF;  WHEN in_action IN (\'delta\', \'save\') THEN IF (v_table_exists NOT IN (\'TEMPORARY\', \'BASE TABLE\')) THEN SET v_error_msg = CONCAT(\'The \', in_action, \' action requires in_table to be an existing table.\', IF(in_table IS NOT NULL, CONCAT(\' The table \', IF(CHAR_LENGTH(v_quoted_table) > 39, CONCAT(\'...\', SUBSTRING(v_quoted_table, -36)), v_quoted_table), \' does not exist.\'), \'\')); SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = v_error_msg; END IF;  IF (in_action = \'delta\' AND v_tmp_digests_table_exists <> \'TEMPORARY\') THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'An existing snapshot generated with the statement_performance_analyzer() must exist.\'; END IF; WHEN in_action = \'create_tmp\' THEN IF (v_table_exists = \'TEMPORARY\') THEN SET v_error_msg = CONCAT(\'Cannot create the table \', IF(CHAR_LENGTH(v_quoted_table) > 72, CONCAT(\'...\', SUBSTRING(v_quoted_table, -69)), v_quoted_table), \' as it already exists.\'); SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = v_error_msg; END IF;  WHEN in_action = \'create_table\' THEN IF (v_table_exists <> \'\') THEN SET v_error_msg = CONCAT(\'Cannot create the table \', IF(CHAR_LENGTH(v_quoted_table) > 52, CONCAT(\'...\', SUBSTRING(v_quoted_table, -49)), v_quoted_table), \' as it already exists\', IF(v_table_exists = \'TEMPORARY\', \' as a temporary table.\', \'.\')); SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = v_error_msg; END IF;  WHEN in_action = \'cleanup\' THEN DO (SELECT 1); ELSE SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'Unknown action. Supported actions are: cleanup, create_table, create_tmp, delta, overall, save, snapshot\'; END CASE;  SET v_digest_table_template = \'CREATE %{TEMPORARY}TABLE %{TABLE_NAME} ( `SCHEMA_NAME` varchar(64) DEFAULT NULL, `DIGEST` varchar(32) DEFAULT NULL, `DIGEST_TEXT` longtext, `COUNT_STAR` bigint(20) unsigned NOT NULL, `SUM_TIMER_WAIT` bigint(20) unsigned NOT NULL, `MIN_TIMER_WAIT` bigint(20) unsigned NOT NULL, `AVG_TIMER_WAIT` bigint(20) unsigned NOT NULL, `MAX_TIMER_WAIT` bigint(20) unsigned NOT NULL, `SUM_LOCK_TIME` bigint(20) unsigned NOT NULL, `SUM_ERRORS` bigint(20) unsigned NOT NULL, `SUM_WARNINGS` bigint(20) unsigned NOT NULL, `SUM_ROWS_AFFECTED` bigint(20) unsigned NOT NULL, `SUM_ROWS_SENT` bigint(20) unsigned NOT NULL, `SUM_ROWS_EXAMINED` bigint(20) unsigned NOT NULL, `SUM_CREATED_TMP_DISK_TABLES` bigint(20) unsigned NOT NULL, `SUM_CREATED_TMP_TABLES` bigint(20) unsigned NOT NULL, `SUM_SELECT_FULL_JOIN` bigint(20) unsigned NOT NULL, `SUM_SELECT_FULL_RANGE_JOIN` bigint(20) unsigned NOT NULL, `SUM_SELECT_RANGE` bigint(20) unsigned NOT NULL, `SUM_SELECT_RANGE_CHECK` bigint(20) unsigned NOT NULL, `SUM_SELECT_SCAN` bigint(20) unsigned NOT NULL, `SUM_SORT_MERGE_PASSES` bigint(20) unsigned NOT NULL, `SUM_SORT_RANGE` bigint(20) unsigned NOT NULL, `SUM_SORT_ROWS` bigint(20) unsigned NOT NULL, `SUM_SORT_SCAN` bigint(20) unsigned NOT NULL, `SUM_NO_INDEX_USED` bigint(20) unsigned NOT NULL, `SUM_NO_GOOD_INDEX_USED` bigint(20) unsigned NOT NULL, `FIRST_SEEN` timestamp NULL DEFAULT NULL, `LAST_SEEN` timestamp NULL DEFAULT NULL, INDEX (SCHEMA_NAME, DIGEST) ) DEFAULT CHARSET=utf8mb3\';  IF (v_force_new_snapshot OR in_action = \'snapshot\' OR (in_action = \'overall\' AND in_table IS NULL) OR (in_action = \'save\' AND v_tmp_digests_table_exists <> \'TEMPORARY\') ) THEN IF (v_tmp_digests_table_exists = \'TEMPORARY\') THEN IF (@sys.debug = \'ON\') THEN SELECT \'DROP TEMPORARY TABLE IF EXISTS tmp_digests\' AS \'Debug\'; END IF; DROP TEMPORARY TABLE IF EXISTS tmp_digests; END IF; CALL sys.execute_prepared_stmt(REPLACE(REPLACE(v_digest_table_template, \'%{TEMPORARY}\', \'TEMPORARY \'), \'%{TABLE_NAME}\', \'tmp_digests\'));  SET v_sql = CONCAT(\'INSERT INTO tmp_digests SELECT * FROM \', IF(in_table IS NULL OR in_action = \'save\', \'performance_schema.events_statements_summary_by_digest\', v_quoted_table)); CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (in_action IN (\'create_table\', \'create_tmp\')) THEN IF (in_action = \'create_table\') THEN CALL sys.execute_prepared_stmt(REPLACE(REPLACE(v_digest_table_template, \'%{TEMPORARY}\', \'\'), \'%{TABLE_NAME}\', v_quoted_table)); ELSE CALL sys.execute_prepared_stmt(REPLACE(REPLACE(v_digest_table_template, \'%{TEMPORARY}\', \'TEMPORARY \'), \'%{TABLE_NAME}\', v_quoted_table)); END IF; ELSEIF (in_action = \'save\') THEN CALL sys.execute_prepared_stmt(CONCAT(\'DELETE FROM \', v_quoted_table)); CALL sys.execute_prepared_stmt(CONCAT(\'INSERT INTO \', v_quoted_table, \' SELECT * FROM tmp_digests\')); ELSEIF (in_action = \'cleanup\') THEN DROP TEMPORARY TABLE IF EXISTS sys.tmp_digests; DROP TEMPORARY TABLE IF EXISTS sys.tmp_digests_delta; ELSEIF (in_action IN (\'overall\', \'delta\')) THEN IF (in_action = \'overall\') THEN IF (in_table IS NULL) THEN SET v_digests_table = \'tmp_digests\'; ELSE SET v_digests_table = v_quoted_table; END IF; ELSE SET v_digests_table = \'tmp_digests_delta\'; DROP TEMPORARY TABLE IF EXISTS tmp_digests_delta; CREATE TEMPORARY TABLE tmp_digests_delta LIKE tmp_digests; SET v_sql = CONCAT(\'INSERT INTO tmp_digests_delta SELECT `d_end`.`SCHEMA_NAME`, `d_end`.`DIGEST`, `d_end`.`DIGEST_TEXT`, `d_end`.`COUNT_STAR`-IFNULL(`d_start`.`COUNT_STAR`, 0) AS \'COUNT_STAR\', `d_end`.`SUM_TIMER_WAIT`-IFNULL(`d_start`.`SUM_TIMER_WAIT`, 0) AS \'SUM_TIMER_WAIT\', `d_end`.`MIN_TIMER_WAIT` AS \'MIN_TIMER_WAIT\', IFNULL((`d_end`.`SUM_TIMER_WAIT`-IFNULL(`d_start`.`SUM_TIMER_WAIT`, 0))/NULLIF(`d_end`.`COUNT_STAR`-IFNULL(`d_start`.`COUNT_STAR`, 0), 0), 0) AS \'AVG_TIMER_WAIT\', `d_end`.`MAX_TIMER_WAIT` AS \'MAX_TIMER_WAIT\', `d_end`.`SUM_LOCK_TIME`-IFNULL(`d_start`.`SUM_LOCK_TIME`, 0) AS \'SUM_LOCK_TIME\', `d_end`.`SUM_ERRORS`-IFNULL(`d_start`.`SUM_ERRORS`, 0) AS \'SUM_ERRORS\', `d_end`.`SUM_WARNINGS`-IFNULL(`d_start`.`SUM_WARNINGS`, 0) AS \'SUM_WARNINGS\', `d_end`.`SUM_ROWS_AFFECTED`-IFNULL(`d_start`.`SUM_ROWS_AFFECTED`, 0) AS \'SUM_ROWS_AFFECTED\', `d_end`.`SUM_ROWS_SENT`-IFNULL(`d_start`.`SUM_ROWS_SENT`, 0) AS \'SUM_ROWS_SENT\', `d_end`.`SUM_ROWS_EXAMINED`-IFNULL(`d_start`.`SUM_ROWS_EXAMINED`, 0) AS \'SUM_ROWS_EXAMINED\', `d_end`.`SUM_CREATED_TMP_DISK_TABLES`-IFNULL(`d_start`.`SUM_CREATED_TMP_DISK_TABLES`, 0) AS \'SUM_CREATED_TMP_DISK_TABLES\', `d_end`.`SUM_CREATED_TMP_TABLES`-IFNULL(`d_start`.`SUM_CREATED_TMP_TABLES`, 0) AS \'SUM_CREATED_TMP_TABLES\', `d_end`.`SUM_SELECT_FULL_JOIN`-IFNULL(`d_start`.`SUM_SELECT_FULL_JOIN`, 0) AS \'SUM_SELECT_FULL_JOIN\', `d_end`.`SUM_SELECT_FULL_RANGE_JOIN`-IFNULL(`d_start`.`SUM_SELECT_FULL_RANGE_JOIN`, 0) AS \'SUM_SELECT_FULL_RANGE_JOIN\', `d_end`.`SUM_SELECT_RANGE`-IFNULL(`d_start`.`SUM_SELECT_RANGE`, 0) AS \'SUM_SELECT_RANGE\', `d_end`.`SUM_SELECT_RANGE_CHECK`-IFNULL(`d_start`.`SUM_SELECT_RANGE_CHECK`, 0) AS \'SUM_SELECT_RANGE_CHECK\', `d_end`.`SUM_SELECT_SCAN`-IFNULL(`d_start`.`SUM_SELECT_SCAN`, 0) AS \'SUM_SELECT_SCAN\', `d_end`.`SUM_SORT_MERGE_PASSES`-IFNULL(`d_start`.`SUM_SORT_MERGE_PASSES`, 0) AS \'SUM_SORT_MERGE_PASSES\', `d_end`.`SUM_SORT_RANGE`-IFNULL(`d_start`.`SUM_SORT_RANGE`, 0) AS \'SUM_SORT_RANGE\', `d_end`.`SUM_SORT_ROWS`-IFNULL(`d_start`.`SUM_SORT_ROWS`, 0) AS \'SUM_SORT_ROWS\', `d_end`.`SUM_SORT_SCAN`-IFNULL(`d_start`.`SUM_SORT_SCAN`, 0) AS \'SUM_SORT_SCAN\', `d_end`.`SUM_NO_INDEX_USED`-IFNULL(`d_start`.`SUM_NO_INDEX_USED`, 0) AS \'SUM_NO_INDEX_USED\', `d_end`.`SUM_NO_GOOD_INDEX_USED`-IFNULL(`d_start`.`SUM_NO_GOOD_INDEX_USED`, 0) AS \'SUM_NO_GOOD_INDEX_USED\', `d_end`.`FIRST_SEEN`, `d_end`.`LAST_SEEN` FROM tmp_digests d_end LEFT OUTER JOIN \', v_quoted_table, \' d_start ON `d_start`.`DIGEST` = `d_end`.`DIGEST` AND (`d_start`.`SCHEMA_NAME` = `d_end`.`SCHEMA_NAME` OR (`d_start`.`SCHEMA_NAME` IS NULL AND `d_end`.`SCHEMA_NAME` IS NULL) ) WHERE `d_end`.`COUNT_STAR`-IFNULL(`d_start`.`COUNT_STAR`, 0) > 0\'); CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (FIND_IN_SET(\'with_runtimes_in_95th_percentile\', in_views)) THEN SELECT \'Queries with Runtime in 95th Percentile\' AS \'Next Output\';  DROP TEMPORARY TABLE IF EXISTS tmp_digest_avg_latency_distribution1; DROP TEMPORARY TABLE IF EXISTS tmp_digest_avg_latency_distribution2; DROP TEMPORARY TABLE IF EXISTS tmp_digest_95th_percentile_by_avg_us;  CREATE TEMPORARY TABLE tmp_digest_avg_latency_distribution1 ( cnt bigint unsigned NOT NULL, avg_us decimal(21,0) NOT NULL, PRIMARY KEY (avg_us) ) ENGINE=InnoDB;  SET v_sql = CONCAT(\'INSERT INTO tmp_digest_avg_latency_distribution1 SELECT COUNT(*) cnt, ROUND(avg_timer_wait/1000000) AS avg_us FROM \', v_digests_table, \' GROUP BY avg_us\'); CALL sys.execute_prepared_stmt(v_sql);  CREATE TEMPORARY TABLE tmp_digest_avg_latency_distribution2 LIKE tmp_digest_avg_latency_distribution1; INSERT INTO tmp_digest_avg_latency_distribution2 SELECT * FROM tmp_digest_avg_latency_distribution1;  CREATE TEMPORARY TABLE tmp_digest_95th_percentile_by_avg_us ( avg_us decimal(21,0) NOT NULL, percentile decimal(46,4) NOT NULL, PRIMARY KEY (avg_us) ) ENGINE=InnoDB;  SET v_sql = CONCAT(\'INSERT INTO tmp_digest_95th_percentile_by_avg_us SELECT s2.avg_us avg_us, IFNULL(SUM(s1.cnt)/NULLIF((SELECT COUNT(*) FROM \', v_digests_table, \'), 0), 0) percentile FROM tmp_digest_avg_latency_distribution1 AS s1 JOIN tmp_digest_avg_latency_distribution2 AS s2 ON s1.avg_us <= s2.avg_us GROUP BY s2.avg_us HAVING percentile > 0.95 ORDER BY percentile LIMIT 1\'); CALL sys.execute_prepared_stmt(v_sql);  SET v_sql = REPLACE( REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'statements_with_runtimes_in_95th_percentile\' ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ), \'sys.x$ps_digest_95th_percentile_by_avg_us\', \'`sys`.`x$ps_digest_95th_percentile_by_avg_us`\' ); CALL sys.execute_prepared_stmt(v_sql);  DROP TEMPORARY TABLE tmp_digest_avg_latency_distribution1; DROP TEMPORARY TABLE tmp_digest_avg_latency_distribution2; DROP TEMPORARY TABLE tmp_digest_95th_percentile_by_avg_us; END IF;  IF (FIND_IN_SET(\'analysis\', in_views)) THEN SELECT CONCAT(\'Top \', @sys.statement_performance_analyzer.limit, \' Queries Ordered by Total Latency\') AS \'Next Output\'; SET v_sql = REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'statement_analysis\' ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ); IF (@sys.statement_performance_analyzer.limit > 0) THEN SET v_sql = CONCAT(v_sql, \' LIMIT \', @sys.statement_performance_analyzer.limit); END IF; CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (FIND_IN_SET(\'with_errors_or_warnings\', in_views)) THEN SELECT CONCAT(\'Top \', @sys.statement_performance_analyzer.limit, \' Queries with Errors\') AS \'Next Output\'; SET v_sql = REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'statements_with_errors_or_warnings\' ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ); IF (@sys.statement_performance_analyzer.limit > 0) THEN SET v_sql = CONCAT(v_sql, \' LIMIT \', @sys.statement_performance_analyzer.limit); END IF; CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (FIND_IN_SET(\'with_full_table_scans\', in_views)) THEN SELECT CONCAT(\'Top \', @sys.statement_performance_analyzer.limit, \' Queries with Full Table Scan\') AS \'Next Output\'; SET v_sql = REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'statements_with_full_table_scans\' ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ); IF (@sys.statement_performance_analyzer.limit > 0) THEN SET v_sql = CONCAT(v_sql, \' LIMIT \', @sys.statement_performance_analyzer.limit); END IF; CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (FIND_IN_SET(\'with_sorting\', in_views)) THEN SELECT CONCAT(\'Top \', @sys.statement_performance_analyzer.limit, \' Queries with Sorting\') AS \'Next Output\'; SET v_sql = REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'statements_with_sorting\' ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ); IF (@sys.statement_performance_analyzer.limit > 0) THEN SET v_sql = CONCAT(v_sql, \' LIMIT \', @sys.statement_performance_analyzer.limit); END IF; CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (FIND_IN_SET(\'with_temp_tables\', in_views)) THEN SELECT CONCAT(\'Top \', @sys.statement_performance_analyzer.limit, \' Queries with Internal Temporary Tables\') AS \'Next Output\'; SET v_sql = REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'statements_with_temp_tables\' ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ); IF (@sys.statement_performance_analyzer.limit > 0) THEN SET v_sql = CONCAT(v_sql, \' LIMIT \', @sys.statement_performance_analyzer.limit); END IF; CALL sys.execute_prepared_stmt(v_sql); END IF;  IF (FIND_IN_SET(\'custom\', in_views)) THEN SELECT CONCAT(\'Top \', @sys.statement_performance_analyzer.limit, \' Queries Using Custom View\') AS \'Next Output\';  IF (@sys.statement_performance_analyzer.view IS NULL) THEN SET @sys.statement_performance_analyzer.view = sys.sys_get_config(\'statement_performance_analyzer.view\', NULL); END IF; IF (@sys.statement_performance_analyzer.view IS NULL) THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'The @sys.statement_performance_analyzer.view user variable must be set with the view or query to use.\'; END IF;  IF (NOT INSTR(@sys.statement_performance_analyzer.view, \' \')) THEN IF (NOT INSTR(@sys.statement_performance_analyzer.view, \'.\')) THEN SET v_custom_db   = DATABASE(), v_custom_name = @sys.statement_performance_analyzer.view; ELSE SET v_custom_db   = SUBSTRING_INDEX(@sys.statement_performance_analyzer.view, \'.\', 1); SET v_custom_name = SUBSTRING(@sys.statement_performance_analyzer.view, CHAR_LENGTH(v_custom_db)+2); END IF;  CALL sys.table_exists(v_custom_db, v_custom_name, v_custom_view_exists); IF (v_custom_view_exists <> \'VIEW\') THEN SIGNAL SQLSTATE \'45000\' SET MESSAGE_TEXT = \'The @sys.statement_performance_analyzer.view user variable is set but specified neither an existing view nor a query.\'; END IF;  SET v_sql = REPLACE( (SELECT VIEW_DEFINITION FROM information_schema.VIEWS WHERE TABLE_SCHEMA = v_custom_db AND TABLE_NAME = v_custom_name ), \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table ); ELSE SET v_sql = REPLACE(@sys.statement_performance_analyzer.view, \'`performance_schema`.`events_statements_summary_by_digest`\', v_digests_table); END IF;  IF (@sys.statement_performance_analyzer.limit > 0) THEN SET v_sql = CONCAT(v_sql, \' LIMIT \', @sys.statement_performance_analyzer.limit); END IF;  CALL sys.execute_prepared_stmt(v_sql); END IF; END IF;  IF (v_this_thread_enabled = \'YES\') THEN CALL sys.ps_setup_enable_thread(CONNECTION_ID()); END IF;  IF (@log_bin = 1) THEN SET sql_log_bin = @log_bin; END IF; END'),('sys','table_exists','PROCEDURE','table_exists','SQL','CONTAINS_SQL','NO','INVOKER',_binary ' IN in_db VARCHAR(64), IN in_table VARCHAR(64), OUT out_exists ENUM(\'\', \'BASE TABLE\', \'VIEW\', \'TEMPORARY\') ','',_binary 'BEGIN DECLARE v_error BOOLEAN DEFAULT FALSE; DECLARE CONTINUE HANDLER FOR 1050 SET v_error = TRUE; DECLARE CONTINUE HANDLER FOR 1146 SET v_error = TRUE;  SET out_exists = \'\';  IF (EXISTS(SELECT 1 FROM information_schema.TABLES WHERE TABLE_SCHEMA = in_db AND TABLE_NAME = in_table)) THEN SET @sys.tmp.table_exists.SQL = CONCAT(\'CREATE TEMPORARY TABLE `\', in_db, \'`.`\', in_table, \'` (id INT PRIMARY KEY)\'); PREPARE stmt_create_table FROM @sys.tmp.table_exists.SQL; EXECUTE stmt_create_table; DEALLOCATE PREPARE stmt_create_table; IF (v_error) THEN SET out_exists = \'TEMPORARY\'; ELSE SET @sys.tmp.table_exists.SQL = CONCAT(\'DROP TEMPORARY TABLE `\', in_db, \'`.`\', in_table, \'`\'); PREPARE stmt_drop_table FROM @sys.tmp.table_exists.SQL; EXECUTE stmt_drop_table; DEALLOCATE PREPARE stmt_drop_table; SET out_exists = (SELECT TABLE_TYPE FROM information_schema.TABLES WHERE TABLE_SCHEMA = in_db AND TABLE_NAME = in_table); END IF; ELSE SET @sys.tmp.table_exists.SQL = CONCAT(\'SELECT COUNT(*) FROM `\', in_db, \'`.`\', in_table, \'`\'); PREPARE stmt_select FROM @sys.tmp.table_exists.SQL; IF (NOT v_error) THEN DEALLOCATE PREPARE stmt_select; SET out_exists = \'TEMPORARY\'; END IF; END IF; END','mysql.sys@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','\n Description\n \n Tests whether the table specified in in_db and in_table exists either as a regular\n table, or as a temporary table. The returned value corresponds to the table that\n will be used, so if there\'s both a temporary and a permanent table with the given\n name, then \'TEMPORARY\' will be returned.\n \n Parameters\n \n in_db (VARCHAR(64)):\n The database name to check for the existance of the table in.\n \n in_table (VARCHAR(64)):\n The name of the table to check the existance of.\n \n out_exists ENUM(\'\', \'BASE TABLE\', \'VIEW\', \'TEMPORARY\'):\n The return value: whether the table exists. The value is one of:\n * \'\'           - the table does not exist neither as a base table, view, nor temporary table.\n * \'BASE TABLE\' - the table name exists as a permanent base table table.\n * \'VIEW\'       - the table name exists as a view.\n * \'TEMPORARY\'  - the table name exists as a temporary table.\n \n Example\n \n mysql> CREATE DATABASE db1;\n Query OK, 1 row affected (0.07 sec)\n \n mysql> use db1;\n Database changed\n mysql> CREATE TABLE t1 (id INT PRIMARY KEY);\n Query OK, 0 rows affected (0.08 sec)\n \n mysql> CREATE TABLE t2 (id INT PRIMARY KEY);\n Query OK, 0 rows affected (0.08 sec)\n \n mysql> CREATE view v_t1 AS SELECT * FROM t1;\n Query OK, 0 rows affected (0.00 sec)\n \n mysql> CREATE TEMPORARY TABLE t1 (id INT PRIMARY KEY);\n Query OK, 0 rows affected (0.00 sec)\n \n mysql> CALL sys.table_exists(\'db1\', \'t1\', @exists); SELECT @exists;\n Query OK, 0 rows affected (0.00 sec)\n \n +------------+\n | @exists    |\n +------------+\n | TEMPORARY  |\n +------------+\n 1 row in set (0.00 sec)\n \n mysql> CALL sys.table_exists(\'db1\', \'t2\', @exists); SELECT @exists;\n Query OK, 0 rows affected (0.00 sec)\n \n +------------+\n | @exists    |\n +------------+\n | BASE TABLE |\n +------------+\n 1 row in set (0.01 sec)\n \n mysql> CALL sys.table_exists(\'db1\', \'v_t1\', @exists); SELECT @exists;\n Query OK, 0 rows affected (0.00 sec)\n \n +---------+\n | @exists |\n +---------+\n | VIEW    |\n +---------+\n 1 row in set (0.00 sec)\n \n mysql> CALL sys.table_exists(\'db1\', \'t3\', @exists); SELECT @exists;\n Query OK, 0 rows affected (0.01 sec)\n \n +---------+\n | @exists |\n +---------+\n |         |\n +---------+\n 1 row in set (0.00 sec)\n ','utf8mb3','utf8mb3_general_ci','utf8mb3_general_ci',_binary 'BEGIN DECLARE v_error BOOLEAN DEFAULT FALSE; DECLARE CONTINUE HANDLER FOR 1050 SET v_error = TRUE; DECLARE CONTINUE HANDLER FOR 1146 SET v_error = TRUE;  SET out_exists = \'\';  IF (EXISTS(SELECT 1 FROM information_schema.TABLES WHERE TABLE_SCHEMA = in_db AND TABLE_NAME = in_table)) THEN SET @sys.tmp.table_exists.SQL = CONCAT(\'CREATE TEMPORARY TABLE `\', in_db, \'`.`\', in_table, \'` (id INT PRIMARY KEY)\'); PREPARE stmt_create_table FROM @sys.tmp.table_exists.SQL; EXECUTE stmt_create_table; DEALLOCATE PREPARE stmt_create_table; IF (v_error) THEN SET out_exists = \'TEMPORARY\'; ELSE SET @sys.tmp.table_exists.SQL = CONCAT(\'DROP TEMPORARY TABLE `\', in_db, \'`.`\', in_table, \'`\'); PREPARE stmt_drop_table FROM @sys.tmp.table_exists.SQL; EXECUTE stmt_drop_table; DEALLOCATE PREPARE stmt_drop_table; SET out_exists = (SELECT TABLE_TYPE FROM information_schema.TABLES WHERE TABLE_SCHEMA = in_db AND TABLE_NAME = in_table); END IF; ELSE SET @sys.tmp.table_exists.SQL = CONCAT(\'SELECT COUNT(*) FROM `\', in_db, \'`.`\', in_table, \'`\'); PREPARE stmt_select FROM @sys.tmp.table_exists.SQL; IF (NOT v_error) THEN DEALLOCATE PREPARE stmt_select; SET out_exists = \'TEMPORARY\'; END IF; END IF; END'),('mtr','check_warnings','PROCEDURE','check_warnings','SQL','CONTAINS_SQL','NO','DEFINER',_binary 'OUT result INT','',_binary 'BEGIN   DECLARE `pos` bigint unsigned;    SET SQL_LOG_BIN=0;    UPDATE error_log el, global_suppressions gs     SET suspicious=0       WHERE el.suspicious=1 AND el.line REGEXP gs.pattern;    UPDATE error_log el, test_suppressions ts     SET suspicious=0       WHERE el.suspicious=1 AND el.line REGEXP ts.pattern;    SELECT COUNT(*) INTO @num_warnings FROM error_log     WHERE suspicious=1;    IF @num_warnings > 0 THEN     SELECT line         FROM error_log WHERE suspicious=1;     SELECT 2 INTO result;   ELSE     SELECT 0 INTO RESULT;   END IF;    TRUNCATE test_suppressions;   DROP TABLE error_log;  END','root@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','','utf8mb3','utf8mb3_general_ci','latin1_swedish_ci',_binary 'BEGIN   DECLARE `pos` bigint unsigned;    SET SQL_LOG_BIN=0;    UPDATE error_log el, global_suppressions gs     SET suspicious=0       WHERE el.suspicious=1 AND el.line REGEXP gs.pattern;    UPDATE error_log el, test_suppressions ts     SET suspicious=0       WHERE el.suspicious=1 AND el.line REGEXP ts.pattern;    SELECT COUNT(*) INTO @num_warnings FROM error_log     WHERE suspicious=1;    IF @num_warnings > 0 THEN     SELECT line         FROM error_log WHERE suspicious=1;     SELECT 2 INTO result;   ELSE     SELECT 0 INTO RESULT;   END IF;    TRUNCATE test_suppressions;   DROP TABLE error_log;  END'),('mtr','add_suppression','PROCEDURE','add_suppression','SQL','CONTAINS_SQL','NO','DEFINER',_binary 'pattern VARCHAR(255)','',_binary 'BEGIN   INSERT INTO test_suppressions (pattern) VALUES (pattern); END','root@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','','utf8mb3','utf8mb3_general_ci','latin1_swedish_ci',_binary 'BEGIN   INSERT INTO test_suppressions (pattern) VALUES (pattern); END'),('mtr','check_testcase_perfschema','PROCEDURE','check_testcase_perfschema','SQL','CONTAINS_SQL','NO','DEFINER','','',_binary 'BEGIN   IF ((SELECT count(*) from information_schema.engines        where engine=\'PERFORMANCE_SCHEMA\' and support=\'YES\') = 1) THEN   BEGIN      BEGIN       DECLARE CONTINUE HANDLER for SQLEXCEPTION       BEGIN       END;        SELECT * from performance_schema.setup_instruments         where enabled=\'NO\' order by NAME;     END;      SELECT * from performance_schema.setup_consumers       order by NAME;      SELECT * from performance_schema.setup_actors       order by USER, HOST;      SELECT * from performance_schema.setup_objects       order by OBJECT_TYPE, OBJECT_SCHEMA, OBJECT_NAME;   END;   END IF; END','root@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','','utf8mb3','utf8mb3_general_ci','latin1_swedish_ci',_binary 'BEGIN   IF ((SELECT count(*) from information_schema.engines        where engine=\'PERFORMANCE_SCHEMA\' and support=\'YES\') = 1) THEN   BEGIN      BEGIN       DECLARE CONTINUE HANDLER for SQLEXCEPTION       BEGIN       END;        SELECT * from performance_schema.setup_instruments         where enabled=\'NO\' order by NAME;     END;      SELECT * from performance_schema.setup_consumers       order by NAME;      SELECT * from performance_schema.setup_actors       order by USER, HOST;      SELECT * from performance_schema.setup_objects       order by OBJECT_TYPE, OBJECT_SCHEMA, OBJECT_NAME;   END;   END IF; END'),('mtr','check_testcase','PROCEDURE','check_testcase','SQL','CONTAINS_SQL','NO','DEFINER','','',_binary 'BEGIN    CALL check_testcase_perfschema();    SELECT * FROM INFORMATION_SCHEMA.GLOBAL_VARIABLES     WHERE variable_name NOT IN (\'timestamp\', \'server_uuid\',                                 \'innodb_file_format_max\',                                 \'gtid_executed\', \'gtid_purged\',                                 \'group_replication_group_name\')     ORDER BY VARIABLE_NAME;    SELECT * FROM INFORMATION_SCHEMA.SCHEMATA;    SELECT table_name AS tables_in_test FROM INFORMATION_SCHEMA.TABLES     WHERE table_schema=\'test\';    SELECT CONCAT(table_schema, \'.\', table_name) AS tables_in_mysql     FROM INFORMATION_SCHEMA.TABLES       WHERE table_schema=\'mysql\' AND table_name != \'ndb_apply_status\'         ORDER BY tables_in_mysql;   SELECT CONCAT(table_schema, \'.\', table_name) AS columns_in_mysql,          column_name, ordinal_position, column_default, is_nullable,          data_type, character_maximum_length, character_octet_length,          numeric_precision, numeric_scale, character_set_name,          collation_name, column_type, column_key, extra, column_comment     FROM INFORMATION_SCHEMA.COLUMNS       WHERE table_schema=\'mysql\' AND table_name != \'ndb_apply_status\'         ORDER BY columns_in_mysql;    SELECT * FROM INFORMATION_SCHEMA.EVENTS;   SELECT TRIGGER_CATALOG, TRIGGER_SCHEMA, TRIGGER_NAME, EVENT_MANIPULATION,          EVENT_OBJECT_CATALOG, EVENT_OBJECT_SCHEMA, EVENT_OBJECT_TABLE, ACTION_ORDER, ACTION_CONDITION,          ACTION_STATEMENT, ACTION_ORIENTATION, ACTION_TIMING ACTION_REFERENCE_OLD_TABLE, ACTION_REFERENCE_NEW_TABLE,          ACTION_REFERENCE_OLD_ROW, ACTION_REFERENCE_NEW_ROW, SQL_MODE, DEFINER CHARACTER_SET_CLIENT,          COLLATION_CONNECTION, DATABASE_COLLATION     FROM INFORMATION_SCHEMA.TRIGGERS    WHERE TRIGGER_NAME NOT IN (\'gs_insert\', \'ts_insert\');   SELECT SPECIFIC_NAME,ROUTINE_CATALOG,ROUTINE_SCHEMA,ROUTINE_NAME,ROUTINE_TYPE,DATA_TYPE,CHARACTER_MAXIMUM_LENGTH,          CHARACTER_OCTET_LENGTH,NUMERIC_PRECISION,NUMERIC_SCALE,DATETIME_PRECISION,CHARACTER_SET_NAME,COLLATION_NAME,          DTD_IDENTIFIER,ROUTINE_BODY,ROUTINE_DEFINITION,EXTERNAL_NAME,EXTERNAL_LANGUAGE,PARAMETER_STYLE,          IS_DETERMINISTIC,SQL_DATA_ACCESS,SQL_PATH,SECURITY_TYPE,SQL_MODE,ROUTINE_COMMENT,DEFINER,          CHARACTER_SET_CLIENT,COLLATION_CONNECTION,DATABASE_COLLATION     FROM INFORMATION_SCHEMA.ROUTINES;   SELECT * FROM INFORMATION_SCHEMA.VIEWS;    SHOW GLOBAL STATUS LIKE \'slave_open_temp_tables\';    checksum table     mysql.columns_priv,     mysql.db,     mysql.func,     mysql.help_category,     mysql.help_keyword,     mysql.help_relation,     mysql.host,     mysql.procs_priv,     mysql.tables_priv,     mysql.time_zone,     mysql.time_zone_leap_second,     mysql.time_zone_name,     mysql.time_zone_transition,     mysql.time_zone_transition_type,     mysql.user;  END','root@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','','utf8mb3','utf8mb3_general_ci','latin1_swedish_ci',_binary 'BEGIN    CALL check_testcase_perfschema();    SELECT * FROM INFORMATION_SCHEMA.GLOBAL_VARIABLES     WHERE variable_name NOT IN (\'timestamp\', \'server_uuid\',                                 \'innodb_file_format_max\',                                 \'gtid_executed\', \'gtid_purged\',                                 \'group_replication_group_name\')     ORDER BY VARIABLE_NAME;    SELECT * FROM INFORMATION_SCHEMA.SCHEMATA;    SELECT table_name AS tables_in_test FROM INFORMATION_SCHEMA.TABLES     WHERE table_schema=\'test\';    SELECT CONCAT(table_schema, \'.\', table_name) AS tables_in_mysql     FROM INFORMATION_SCHEMA.TABLES       WHERE table_schema=\'mysql\' AND table_name != \'ndb_apply_status\'         ORDER BY tables_in_mysql;   SELECT CONCAT(table_schema, \'.\', table_name) AS columns_in_mysql,          column_name, ordinal_position, column_default, is_nullable,          data_type, character_maximum_length, character_octet_length,          numeric_precision, numeric_scale, character_set_name,          collation_name, column_type, column_key, extra, column_comment     FROM INFORMATION_SCHEMA.COLUMNS       WHERE table_schema=\'mysql\' AND table_name != \'ndb_apply_status\'         ORDER BY columns_in_mysql;    SELECT * FROM INFORMATION_SCHEMA.EVENTS;   SELECT TRIGGER_CATALOG, TRIGGER_SCHEMA, TRIGGER_NAME, EVENT_MANIPULATION,          EVENT_OBJECT_CATALOG, EVENT_OBJECT_SCHEMA, EVENT_OBJECT_TABLE, ACTION_ORDER, ACTION_CONDITION,          ACTION_STATEMENT, ACTION_ORIENTATION, ACTION_TIMING ACTION_REFERENCE_OLD_TABLE, ACTION_REFERENCE_NEW_TABLE,          ACTION_REFERENCE_OLD_ROW, ACTION_REFERENCE_NEW_ROW, SQL_MODE, DEFINER CHARACTER_SET_CLIENT,          COLLATION_CONNECTION, DATABASE_COLLATION     FROM INFORMATION_SCHEMA.TRIGGERS    WHERE TRIGGER_NAME NOT IN (\'gs_insert\', \'ts_insert\');   SELECT SPECIFIC_NAME,ROUTINE_CATALOG,ROUTINE_SCHEMA,ROUTINE_NAME,ROUTINE_TYPE,DATA_TYPE,CHARACTER_MAXIMUM_LENGTH,          CHARACTER_OCTET_LENGTH,NUMERIC_PRECISION,NUMERIC_SCALE,DATETIME_PRECISION,CHARACTER_SET_NAME,COLLATION_NAME,          DTD_IDENTIFIER,ROUTINE_BODY,ROUTINE_DEFINITION,EXTERNAL_NAME,EXTERNAL_LANGUAGE,PARAMETER_STYLE,          IS_DETERMINISTIC,SQL_DATA_ACCESS,SQL_PATH,SECURITY_TYPE,SQL_MODE,ROUTINE_COMMENT,DEFINER,          CHARACTER_SET_CLIENT,COLLATION_CONNECTION,DATABASE_COLLATION     FROM INFORMATION_SCHEMA.ROUTINES;   SELECT * FROM INFORMATION_SCHEMA.VIEWS;    SHOW GLOBAL STATUS LIKE \'slave_open_temp_tables\';    checksum table     mysql.columns_priv,     mysql.db,     mysql.func,     mysql.help_category,     mysql.help_keyword,     mysql.help_relation,     mysql.host,     mysql.procs_priv,     mysql.tables_priv,     mysql.time_zone,     mysql.time_zone_leap_second,     mysql.time_zone_name,     mysql.time_zone_transition,     mysql.time_zone_transition_type,     mysql.user;  END'),('mtr','force_restart','PROCEDURE','force_restart','SQL','CONTAINS_SQL','NO','DEFINER','','',_binary 'BEGIN   SELECT 1 INTO OUTFILE \'force_restart\'; END','root@localhost','2020-02-05 07:54:17','2020-02-05 07:54:17','','','utf8mb3','utf8mb3_general_ci','latin1_swedish_ci',_binary 'BEGIN   SELECT 1 INTO OUTFILE \'force_restart\'; END');
/*!40000 ALTER TABLE `proc` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `procs_priv`
--

DROP TABLE IF EXISTS `procs_priv`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `procs_priv` (
  `Host` char(60) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Db` char(64) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `User` char(32) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Routine_name` char(64) CHARACTER SET utf8mb3 NOT NULL DEFAULT '',
  `Routine_type` enum('FUNCTION','PROCEDURE') COLLATE utf8mb3_bin NOT NULL,
  `Grantor` char(93) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Proc_priv` set('Execute','Alter Routine','Grant') CHARACTER SET utf8mb3 NOT NULL DEFAULT '',
  `Timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Host`,`Db`,`User`,`Routine_name`,`Routine_type`),
  KEY `Grantor` (`Grantor`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='Procedure privileges';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `procs_priv`
--

LOCK TABLES `procs_priv` WRITE;
/*!40000 ALTER TABLE `procs_priv` DISABLE KEYS */;
/*!40000 ALTER TABLE `procs_priv` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `proxies_priv`
--

DROP TABLE IF EXISTS `proxies_priv`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `proxies_priv` (
  `Host` char(60) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `User` char(32) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Proxied_host` char(60) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Proxied_user` char(32) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `With_grant` tinyint(1) NOT NULL DEFAULT '0',
  `Grantor` char(93) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Host`,`User`,`Proxied_host`,`Proxied_user`),
  KEY `Grantor` (`Grantor`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='User proxy privileges';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `proxies_priv`
--

LOCK TABLES `proxies_priv` WRITE;
/*!40000 ALTER TABLE `proxies_priv` DISABLE KEYS */;
INSERT INTO `proxies_priv` VALUES ('localhost','root','','',1,'','2020-02-05 07:54:16'),('loki05','root','','',1,'','2020-02-05 07:54:16');
/*!40000 ALTER TABLE `proxies_priv` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `server_cost`
--

DROP TABLE IF EXISTS `server_cost`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `server_cost` (
  `cost_name` varchar(64) NOT NULL,
  `cost_value` float DEFAULT NULL,
  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `comment` varchar(1024) DEFAULT NULL,
  PRIMARY KEY (`cost_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `server_cost`
--

LOCK TABLES `server_cost` WRITE;
/*!40000 ALTER TABLE `server_cost` DISABLE KEYS */;
INSERT INTO `server_cost` VALUES ('disk_temptable_create_cost',NULL,'2020-02-05 07:54:16',NULL),('disk_temptable_row_cost',NULL,'2020-02-05 07:54:16',NULL),('key_compare_cost',NULL,'2020-02-05 07:54:16',NULL),('memory_temptable_create_cost',NULL,'2020-02-05 07:54:16',NULL),('memory_temptable_row_cost',NULL,'2020-02-05 07:54:16',NULL),('row_evaluate_cost',NULL,'2020-02-05 07:54:16',NULL);
/*!40000 ALTER TABLE `server_cost` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `servers`
--

DROP TABLE IF EXISTS `servers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `servers` (
  `Server_name` char(64) NOT NULL DEFAULT '',
  `Host` char(64) NOT NULL DEFAULT '',
  `Db` char(64) NOT NULL DEFAULT '',
  `Username` char(64) NOT NULL DEFAULT '',
  `Password` char(64) NOT NULL DEFAULT '',
  `Port` int(4) NOT NULL DEFAULT '0',
  `Socket` char(64) NOT NULL DEFAULT '',
  `Wrapper` char(64) NOT NULL DEFAULT '',
  `Owner` char(64) NOT NULL DEFAULT '',
  PRIMARY KEY (`Server_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='MySQL Foreign Servers table';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `servers`
--

LOCK TABLES `servers` WRITE;
/*!40000 ALTER TABLE `servers` DISABLE KEYS */;
/*!40000 ALTER TABLE `servers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `slave_master_info`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE IF NOT EXISTS `slave_master_info` (
  `Number_of_lines` int(10) unsigned NOT NULL COMMENT 'Number of lines in the file.',
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'The name of the master binary log currently being read from the master.',
  `Master_log_pos` bigint(20) unsigned NOT NULL COMMENT 'The master log position of the last read event.',
  `Host` char(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'The host name of the master.',
  `User_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The user name used to connect to the master.',
  `User_password` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The password used to connect to the master.',
  `Port` int(10) unsigned NOT NULL COMMENT 'The network port used to connect to the master.',
  `Connect_retry` int(10) unsigned NOT NULL COMMENT 'The period (in seconds) that the slave will wait before trying to reconnect to the master.',
  `Enabled_ssl` tinyint(1) NOT NULL COMMENT 'Indicates whether the server supports SSL connections.',
  `Ssl_ca` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The file used for the Certificate Authority (CA) certificate.',
  `Ssl_capath` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The path to the Certificate Authority (CA) certificates.',
  `Ssl_cert` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the SSL certificate file.',
  `Ssl_cipher` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the cipher in use for the SSL connection.',
  `Ssl_key` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The name of the SSL key file.',
  `Ssl_verify_server_cert` tinyint(1) NOT NULL COMMENT 'Whether to verify the server certificate.',
  `Heartbeat` float NOT NULL,
  `Bind` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Displays which interface is employed when connecting to the MySQL server',
  `Ignored_server_ids` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The number of server IDs to be ignored, followed by the actual server IDs',
  `Uuid` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The master server uuid.',
  `Retry_count` bigint(20) unsigned NOT NULL COMMENT 'Number of reconnect attempts, to the master, before giving up.',
  `Ssl_crl` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The file used for the Certificate Revocation List (CRL)',
  `Ssl_crlpath` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'The path used for Certificate Revocation List (CRL) files',
  `Enabled_auto_position` tinyint(1) NOT NULL COMMENT 'Indicates whether GTIDs will be used to retrieve events from the master.',
  `Channel_name` char(64) NOT NULL COMMENT 'The channel on which the slave is connected to a source. Used in Multisource Replication',
  `Tls_version` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'Tls version',
  PRIMARY KEY (`Channel_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='Master Information';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `slave_relay_log_info`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE IF NOT EXISTS `slave_relay_log_info` (
  `Number_of_lines` int(10) unsigned NOT NULL COMMENT 'Number of lines in the file or rows in the table. Used to version table definitions.',
  `Relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'The name of the current relay log file.',
  `Relay_log_pos` bigint(20) unsigned NOT NULL COMMENT 'The relay log position of the last executed event.',
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'The name of the master binary log file from which the events in the relay log file were read.',
  `Master_log_pos` bigint(20) unsigned NOT NULL COMMENT 'The master log position of the last executed event.',
  `Sql_delay` int(11) NOT NULL COMMENT 'The number of seconds that the slave must lag behind the master.',
  `Number_of_workers` int(10) unsigned NOT NULL,
  `Id` int(10) unsigned NOT NULL COMMENT 'Internal Id that uniquely identifies this record.',
  `Channel_name` char(64) NOT NULL COMMENT 'The channel on which the slave is connected to a source. Used in Multisource Replication',
  PRIMARY KEY (`Channel_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='Relay Log Information';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `slave_worker_info`
--

DROP TABLE IF EXISTS `slave_worker_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `slave_worker_info` (
  `Id` int(10) unsigned NOT NULL,
  `Relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Relay_log_pos` bigint(20) unsigned NOT NULL,
  `Master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Master_log_pos` bigint(20) unsigned NOT NULL,
  `Checkpoint_relay_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Checkpoint_relay_log_pos` bigint(20) unsigned NOT NULL,
  `Checkpoint_master_log_name` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `Checkpoint_master_log_pos` bigint(20) unsigned NOT NULL,
  `Checkpoint_seqno` int(10) unsigned NOT NULL,
  `Checkpoint_group_size` int(10) unsigned NOT NULL,
  `Checkpoint_group_bitmap` blob NOT NULL,
  `Channel_name` char(64) NOT NULL COMMENT 'The channel on which the slave is connected to a source. Used in Multisource Replication',
  PRIMARY KEY (`Channel_name`,`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='Worker Information';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `slave_worker_info`
--

LOCK TABLES `slave_worker_info` WRITE;
/*!40000 ALTER TABLE `slave_worker_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `slave_worker_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tables_priv`
--

DROP TABLE IF EXISTS `tables_priv`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `tables_priv` (
  `Host` char(60) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Db` char(64) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `User` char(32) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Table_name` char(64) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Grantor` char(93) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Table_priv` set('Select','Insert','Update','Delete','Create','Drop','Grant','References','Index','Alter','Create View','Show view','Trigger') CHARACTER SET utf8mb3 NOT NULL DEFAULT '',
  `Column_priv` set('Select','Insert','Update','References') CHARACTER SET utf8mb3 NOT NULL DEFAULT '',
  PRIMARY KEY (`Host`,`Db`,`User`,`Table_name`),
  KEY `Grantor` (`Grantor`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='Table privileges';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tables_priv`
--

LOCK TABLES `tables_priv` WRITE;
/*!40000 ALTER TABLE `tables_priv` DISABLE KEYS */;
INSERT INTO `tables_priv` VALUES ('localhost','sys','mysql.sys','sys_config','root@localhost','2020-02-05 07:54:17','Select',''),('localhost','mysql','mysql.session','user','root@localhost','2020-02-05 07:54:17','Select','');
/*!40000 ALTER TABLE `tables_priv` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `time_zone`
--

DROP TABLE IF EXISTS `time_zone`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `time_zone` (
  `Time_zone_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Use_leap_seconds` enum('Y','N') NOT NULL DEFAULT 'N',
  PRIMARY KEY (`Time_zone_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='Time zones';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `time_zone`
--

LOCK TABLES `time_zone` WRITE;
/*!40000 ALTER TABLE `time_zone` DISABLE KEYS */;
INSERT INTO `time_zone` VALUES (1,'N'),(2,'N'),(3,'N'),(4,'Y'),(5,'N');
/*!40000 ALTER TABLE `time_zone` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `time_zone_leap_second`
--

DROP TABLE IF EXISTS `time_zone_leap_second`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `time_zone_leap_second` (
  `Transition_time` bigint(20) NOT NULL,
  `Correction` int(11) NOT NULL,
  PRIMARY KEY (`Transition_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='Leap seconds information for time zones';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `time_zone_leap_second`
--

LOCK TABLES `time_zone_leap_second` WRITE;
/*!40000 ALTER TABLE `time_zone_leap_second` DISABLE KEYS */;
INSERT INTO `time_zone_leap_second` VALUES (78796800,1),(94694401,2),(126230402,3),(157766403,4),(189302404,5),(220924805,6),(252460806,7),(283996807,8),(315532808,9),(362793609,10),(394329610,11),(425865611,12),(489024012,13),(567993613,14),(631152014,15),(662688015,16),(709948816,17),(741484817,18),(773020818,19),(820454419,20),(867715220,21),(915148821,22);
/*!40000 ALTER TABLE `time_zone_leap_second` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `time_zone_name`
--

DROP TABLE IF EXISTS `time_zone_name`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `time_zone_name` (
  `Name` char(64) NOT NULL,
  `Time_zone_id` int(10) unsigned NOT NULL,
  PRIMARY KEY (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='Time zone names';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `time_zone_name`
--

LOCK TABLES `time_zone_name` WRITE;
/*!40000 ALTER TABLE `time_zone_name` DISABLE KEYS */;
INSERT INTO `time_zone_name` VALUES ('Europe/Moscow',3),('Japan',5),('leap/Europe/Moscow',4),('MET',1),('Universal',2),('UTC',2);
/*!40000 ALTER TABLE `time_zone_name` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `time_zone_transition`
--

DROP TABLE IF EXISTS `time_zone_transition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `time_zone_transition` (
  `Time_zone_id` int(10) unsigned NOT NULL,
  `Transition_time` bigint(20) NOT NULL,
  `Transition_type_id` int(10) unsigned NOT NULL,
  PRIMARY KEY (`Time_zone_id`,`Transition_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='Time zone transitions';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `time_zone_transition`
--

LOCK TABLES `time_zone_transition` WRITE;
/*!40000 ALTER TABLE `time_zone_transition` DISABLE KEYS */;
INSERT INTO `time_zone_transition` VALUES (1,-1693706400,0),(1,-1680483600,1),(1,-1663455600,2),(1,-1650150000,3),(1,-1632006000,2),(1,-1618700400,3),(1,-938905200,2),(1,-857257200,3),(1,-844556400,2),(1,-828226800,3),(1,-812502000,2),(1,-796777200,3),(1,228877200,2),(1,243997200,3),(1,260326800,2),(1,276051600,3),(1,291776400,2),(1,307501200,3),(1,323830800,2),(1,338950800,3),(1,354675600,2),(1,370400400,3),(1,386125200,2),(1,401850000,3),(1,417574800,2),(1,433299600,3),(1,449024400,2),(1,465354000,3),(1,481078800,2),(1,496803600,3),(1,512528400,2),(1,528253200,3),(1,543978000,2),(1,559702800,3),(1,575427600,2),(1,591152400,3),(1,606877200,2),(1,622602000,3),(1,638326800,2),(1,654656400,3),(1,670381200,2),(1,686106000,3),(1,701830800,2),(1,717555600,3),(1,733280400,2),(1,749005200,3),(1,764730000,2),(1,780454800,3),(1,796179600,2),(1,811904400,3),(1,828234000,2),(1,846378000,3),(1,859683600,2),(1,877827600,3),(1,891133200,2),(1,909277200,3),(1,922582800,2),(1,941331600,3),(1,954032400,2),(1,972781200,3),(1,985482000,2),(1,1004230800,3),(1,1017536400,2),(1,1035680400,3),(1,1048986000,2),(1,1067130000,3),(1,1080435600,2),(1,1099184400,3),(1,1111885200,2),(1,1130634000,3),(1,1143334800,2),(1,1162083600,3),(1,1174784400,2),(1,1193533200,3),(1,1206838800,2),(1,1224982800,3),(1,1238288400,2),(1,1256432400,3),(1,1269738000,2),(1,1288486800,3),(1,1301187600,2),(1,1319936400,3),(1,1332637200,2),(1,1351386000,3),(1,1364691600,2),(1,1382835600,3),(1,1396141200,2),(1,1414285200,3),(1,1427590800,2),(1,1445734800,3),(1,1459040400,2),(1,1477789200,3),(1,1490490000,2),(1,1509238800,3),(1,1521939600,2),(1,1540688400,3),(1,1553994000,2),(1,1572138000,3),(1,1585443600,2),(1,1603587600,3),(1,1616893200,2),(1,1635642000,3),(1,1648342800,2),(1,1667091600,3),(1,1679792400,2),(1,1698541200,3),(1,1711846800,2),(1,1729990800,3),(1,1743296400,2),(1,1761440400,3),(1,1774746000,2),(1,1792890000,3),(1,1806195600,2),(1,1824944400,3),(1,1837645200,2),(1,1856394000,3),(1,1869094800,2),(1,1887843600,3),(1,1901149200,2),(1,1919293200,3),(1,1932598800,2),(1,1950742800,3),(1,1964048400,2),(1,1982797200,3),(1,1995498000,2),(1,2014246800,3),(1,2026947600,2),(1,2045696400,3),(1,2058397200,2),(1,2077146000,3),(1,2090451600,2),(1,2108595600,3),(1,2121901200,2),(1,2140045200,3),(3,-1688265000,2),(3,-1656819048,1),(3,-1641353448,2),(3,-1627965048,3),(3,-1618716648,1),(3,-1596429048,3),(3,-1593829848,5),(3,-1589860800,4),(3,-1542427200,5),(3,-1539493200,6),(3,-1525323600,5),(3,-1522728000,4),(3,-1491188400,7),(3,-1247536800,4),(3,354920400,5),(3,370728000,4),(3,386456400,5),(3,402264000,4),(3,417992400,5),(3,433800000,4),(3,449614800,5),(3,465346800,8),(3,481071600,9),(3,496796400,8),(3,512521200,9),(3,528246000,8),(3,543970800,9),(3,559695600,8),(3,575420400,9),(3,591145200,8),(3,606870000,9),(3,622594800,8),(3,638319600,9),(3,654649200,8),(3,670374000,10),(3,686102400,11),(3,695779200,8),(3,701812800,5),(3,717534000,4),(3,733273200,9),(3,748998000,8),(3,764722800,9),(3,780447600,8),(3,796172400,9),(3,811897200,8),(3,828226800,9),(3,846370800,8),(3,859676400,9),(3,877820400,8),(3,891126000,9),(3,909270000,8),(3,922575600,9),(3,941324400,8),(3,954025200,9),(3,972774000,8),(3,985474800,9),(3,1004223600,8),(3,1017529200,9),(3,1035673200,8),(3,1048978800,9),(3,1067122800,8),(3,1080428400,9),(3,1099177200,8),(3,1111878000,9),(3,1130626800,8),(3,1143327600,9),(3,1162076400,8),(3,1174777200,9),(3,1193526000,8),(3,1206831600,9),(3,1224975600,8),(3,1238281200,9),(3,1256425200,8),(3,1269730800,9),(3,1288479600,8),(3,1301180400,9),(3,1319929200,8),(3,1332630000,9),(3,1351378800,8),(3,1364684400,9),(3,1382828400,8),(3,1396134000,9),(3,1414278000,8),(3,1427583600,9),(3,1445727600,8),(3,1459033200,9),(3,1477782000,8),(3,1490482800,9),(3,1509231600,8),(3,1521932400,9),(3,1540681200,8),(3,1553986800,9),(3,1572130800,8),(3,1585436400,9),(3,1603580400,8),(3,1616886000,9),(3,1635634800,8),(3,1648335600,9),(3,1667084400,8),(3,1679785200,9),(3,1698534000,8),(3,1711839600,9),(3,1729983600,8),(3,1743289200,9),(3,1761433200,8),(3,1774738800,9),(3,1792882800,8),(3,1806188400,9),(3,1824937200,8),(3,1837638000,9),(3,1856386800,8),(3,1869087600,9),(3,1887836400,8),(3,1901142000,9),(3,1919286000,8),(3,1932591600,9),(3,1950735600,8),(3,1964041200,9),(3,1982790000,8),(3,1995490800,9),(3,2014239600,8),(3,2026940400,9),(3,2045689200,8),(3,2058390000,9),(3,2077138800,8),(3,2090444400,9),(3,2108588400,8),(3,2121894000,9),(3,2140038000,8),(4,-1688265000,2),(4,-1656819048,1),(4,-1641353448,2),(4,-1627965048,3),(4,-1618716648,1),(4,-1596429048,3),(4,-1593829848,5),(4,-1589860800,4),(4,-1542427200,5),(4,-1539493200,6),(4,-1525323600,5),(4,-1522728000,4),(4,-1491188400,7),(4,-1247536800,4),(4,354920409,5),(4,370728010,4),(4,386456410,5),(4,402264011,4),(4,417992411,5),(4,433800012,4),(4,449614812,5),(4,465346812,8),(4,481071612,9),(4,496796413,8),(4,512521213,9),(4,528246013,8),(4,543970813,9),(4,559695613,8),(4,575420414,9),(4,591145214,8),(4,606870014,9),(4,622594814,8),(4,638319615,9),(4,654649215,8),(4,670374016,10),(4,686102416,11),(4,695779216,8),(4,701812816,5),(4,717534017,4),(4,733273217,9),(4,748998018,8),(4,764722818,9),(4,780447619,8),(4,796172419,9),(4,811897219,8),(4,828226820,9),(4,846370820,8),(4,859676420,9),(4,877820421,8),(4,891126021,9),(4,909270021,8),(4,922575622,9),(4,941324422,8),(4,954025222,9),(4,972774022,8),(4,985474822,9),(4,1004223622,8),(4,1017529222,9),(4,1035673222,8),(4,1048978822,9),(4,1067122822,8),(4,1080428422,9),(4,1099177222,8),(4,1111878022,9),(4,1130626822,8),(4,1143327622,9),(4,1162076422,8),(4,1174777222,9),(4,1193526022,8),(4,1206831622,9),(4,1224975622,8),(4,1238281222,9),(4,1256425222,8),(4,1269730822,9),(4,1288479622,8),(4,1301180422,9),(4,1319929222,8),(4,1332630022,9),(4,1351378822,8),(4,1364684422,9),(4,1382828422,8),(4,1396134022,9),(4,1414278022,8),(4,1427583622,9),(4,1445727622,8),(4,1459033222,9),(4,1477782022,8),(4,1490482822,9),(4,1509231622,8),(4,1521932422,9),(4,1540681222,8),(4,1553986822,9),(4,1572130822,8),(4,1585436422,9),(4,1603580422,8),(4,1616886022,9),(4,1635634822,8),(4,1648335622,9),(4,1667084422,8),(4,1679785222,9),(4,1698534022,8),(4,1711839622,9),(4,1729983622,8),(4,1743289222,9),(4,1761433222,8),(4,1774738822,9),(4,1792882822,8),(4,1806188422,9),(4,1824937222,8),(4,1837638022,9),(4,1856386822,8),(4,1869087622,9),(4,1887836422,8),(4,1901142022,9),(4,1919286022,8),(4,1932591622,9),(4,1950735622,8),(4,1964041222,9),(4,1982790022,8),(4,1995490822,9),(4,2014239622,8),(4,2026940422,9),(4,2045689222,8),(4,2058390022,9),(4,2077138822,8),(4,2090444422,9),(4,2108588422,8),(4,2121894022,9),(4,2140038022,8),(5,-1009875600,1);
/*!40000 ALTER TABLE `time_zone_transition` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `time_zone_transition_type`
--

DROP TABLE IF EXISTS `time_zone_transition_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `time_zone_transition_type` (
  `Time_zone_id` int(10) unsigned NOT NULL,
  `Transition_type_id` int(10) unsigned NOT NULL,
  `Offset` int(11) NOT NULL DEFAULT '0',
  `Is_DST` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `Abbreviation` char(8) NOT NULL DEFAULT '',
  PRIMARY KEY (`Time_zone_id`,`Transition_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 STATS_PERSISTENT=0 COMMENT='Time zone transition types';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `time_zone_transition_type`
--

LOCK TABLES `time_zone_transition_type` WRITE;
/*!40000 ALTER TABLE `time_zone_transition_type` DISABLE KEYS */;
INSERT INTO `time_zone_transition_type` VALUES (1,0,7200,1,'MEST'),(1,1,3600,0,'MET'),(1,2,7200,1,'MEST'),(1,3,3600,0,'MET'),(2,0,0,0,'UTC'),(3,0,9000,0,'MMT'),(3,1,12648,1,'MST'),(3,2,9048,0,'MMT'),(3,3,16248,1,'MDST'),(3,4,10800,0,'MSK'),(3,5,14400,1,'MSD'),(3,6,18000,1,'MSD'),(3,7,7200,0,'EET'),(3,8,10800,0,'MSK'),(3,9,14400,1,'MSD'),(3,10,10800,1,'EEST'),(3,11,7200,0,'EET'),(4,0,9000,0,'MMT'),(4,1,12648,1,'MST'),(4,2,9048,0,'MMT'),(4,3,16248,1,'MDST'),(4,4,10800,0,'MSK'),(4,5,14400,1,'MSD'),(4,6,18000,1,'MSD'),(4,7,7200,0,'EET'),(4,8,10800,0,'MSK'),(4,9,14400,1,'MSD'),(4,10,10800,1,'EEST'),(4,11,7200,0,'EET'),(5,0,32400,0,'CJT'),(5,1,32400,0,'JST');
/*!40000 ALTER TABLE `time_zone_transition_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE `user` (
  `Host` char(60) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `User` char(32) COLLATE utf8mb3_bin NOT NULL DEFAULT '',
  `Select_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Insert_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Update_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Delete_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Create_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Drop_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Reload_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Shutdown_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Process_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `File_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Grant_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `References_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Index_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Alter_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Show_db_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Super_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Create_tmp_table_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Lock_tables_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Execute_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Repl_slave_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Repl_client_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Create_view_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Show_view_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Create_routine_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Alter_routine_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Create_user_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Event_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Trigger_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `Create_tablespace_priv` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `ssl_type` enum('','ANY','X509','SPECIFIED') CHARACTER SET utf8mb3 NOT NULL DEFAULT '',
  `ssl_cipher` blob NOT NULL,
  `x509_issuer` blob NOT NULL,
  `x509_subject` blob NOT NULL,
  `max_questions` int(11) unsigned NOT NULL DEFAULT '0',
  `max_updates` int(11) unsigned NOT NULL DEFAULT '0',
  `max_connections` int(11) unsigned NOT NULL DEFAULT '0',
  `max_user_connections` int(11) unsigned NOT NULL DEFAULT '0',
  `authentication_string` text COLLATE utf8mb3_bin,
  `password_expired` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  `password_last_changed` timestamp NULL DEFAULT NULL,
  `password_lifetime` smallint(5) unsigned DEFAULT NULL,
  `account_locked` enum('N','Y') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'N',
  PRIMARY KEY (`Host`,`User`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='Users and global privileges';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `general_log`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE IF NOT EXISTS `general_log` (
  `event_time` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `user_host` mediumtext NOT NULL,
  `thread_id` bigint(21) unsigned NOT NULL,
  `server_id` int(10) unsigned NOT NULL,
  `command_type` varchar(64) NOT NULL,
  `argument` mediumblob NOT NULL
) ENGINE=CSV DEFAULT CHARSET=utf8mb3 COMMENT='General log';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `slow_log`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb3 */;
CREATE TABLE IF NOT EXISTS `slow_log` (
  `start_time` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `user_host` mediumtext NOT NULL,
  `query_time` time(6) NOT NULL,
  `lock_time` time(6) NOT NULL,
  `rows_sent` int(11) NOT NULL,
  `rows_examined` int(11) NOT NULL,
  `db` varchar(512) NOT NULL,
  `last_insert_id` int(11) NOT NULL,
  `insert_id` int(11) NOT NULL,
  `server_id` int(10) unsigned NOT NULL,
  `sql_text` mediumblob NOT NULL,
  `thread_id` bigint(21) unsigned NOT NULL
) ENGINE=CSV DEFAULT CHARSET=utf8mb3 COMMENT='Slow log';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2020-02-05 10:24:59
