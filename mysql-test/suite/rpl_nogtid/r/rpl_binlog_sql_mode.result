include/rpl/init_source_replica.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection master]
==== Initialize ====
SET @old_relay_log_purge= @@global.relay_log_purge;
include/rpl/setup_fake_relay_log.inc
Setting up fake replication from MYSQL_TEST_DIR/std_data/bug27213339-bin.000001
==== Test ====
include/rpl/start_applier.inc
include/rpl/wait_for_replica_status.inc [Exec_Source_Log_Pos]
==== Check ====
SHOW CREATE EVENT db1.e_DB2;
Event	sql_mode	time_zone	Create Event	character_set_client	collation_connection	Database Collation
e_DB2	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	SYSTEM	CREATE DEFINER=`root`@`localhost` EVENT `e_DB2` ON SCHEDULE EVERY 100 WEEK STARTS '2018-01-23 12:11:33' ON COMPLETION NOT PRESERVE DISABLE ON REPLICA DO SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE EVENT db1.e_MAXDB;
Event	sql_mode	time_zone	Create Event	character_set_client	collation_connection	Database Collation
e_MAXDB	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	SYSTEM	CREATE DEFINER=`root`@`localhost` EVENT `e_MAXDB` ON SCHEDULE EVERY 100 WEEK STARTS '2018-01-23 12:11:33' ON COMPLETION NOT PRESERVE DISABLE ON REPLICA DO SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE EVENT db1.e_MSSQL;
Event	sql_mode	time_zone	Create Event	character_set_client	collation_connection	Database Collation
e_MSSQL	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	SYSTEM	CREATE DEFINER=`root`@`localhost` EVENT `e_MSSQL` ON SCHEDULE EVERY 100 WEEK STARTS '2018-01-23 12:11:33' ON COMPLETION NOT PRESERVE DISABLE ON REPLICA DO SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE EVENT db1.e_MYSQL323;
Event	sql_mode	time_zone	Create Event	character_set_client	collation_connection	Database Collation
e_MYSQL323	HIGH_NOT_PRECEDENCE	SYSTEM	CREATE DEFINER=`root`@`localhost` EVENT `e_MYSQL323` ON SCHEDULE EVERY 100 WEEK STARTS '2018-01-23 12:11:33' ON COMPLETION NOT PRESERVE DISABLE ON REPLICA DO SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE EVENT db1.e_MYSQL40;
Event	sql_mode	time_zone	Create Event	character_set_client	collation_connection	Database Collation
e_MYSQL40	HIGH_NOT_PRECEDENCE	SYSTEM	CREATE DEFINER=`root`@`localhost` EVENT `e_MYSQL40` ON SCHEDULE EVERY 100 WEEK STARTS '2018-01-23 12:11:33' ON COMPLETION NOT PRESERVE DISABLE ON REPLICA DO SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE EVENT db1.e_NO_FIELD_OPTIONS;
Event	sql_mode	time_zone	Create Event	character_set_client	collation_connection	Database Collation
e_NO_FIELD_OPTIONS		SYSTEM	CREATE DEFINER=`root`@`localhost` EVENT `e_NO_FIELD_OPTIONS` ON SCHEDULE EVERY 100 WEEK STARTS '2018-01-23 12:11:33' ON COMPLETION NOT PRESERVE DISABLE ON REPLICA DO SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE EVENT db1.e_NO_KEY_OPTIONS;
Event	sql_mode	time_zone	Create Event	character_set_client	collation_connection	Database Collation
e_NO_KEY_OPTIONS		SYSTEM	CREATE DEFINER=`root`@`localhost` EVENT `e_NO_KEY_OPTIONS` ON SCHEDULE EVERY 100 WEEK STARTS '2018-01-23 12:11:33' ON COMPLETION NOT PRESERVE DISABLE ON REPLICA DO SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE EVENT db1.e_NO_TABLE_OPTIONS;
Event	sql_mode	time_zone	Create Event	character_set_client	collation_connection	Database Collation
e_NO_TABLE_OPTIONS		SYSTEM	CREATE DEFINER=`root`@`localhost` EVENT `e_NO_TABLE_OPTIONS` ON SCHEDULE EVERY 100 WEEK STARTS '2018-01-23 12:11:33' ON COMPLETION NOT PRESERVE DISABLE ON REPLICA DO SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE EVENT db1.e_ORACLE;
Event	sql_mode	time_zone	Create Event	character_set_client	collation_connection	Database Collation
e_ORACLE	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	SYSTEM	CREATE DEFINER=`root`@`localhost` EVENT `e_ORACLE` ON SCHEDULE EVERY 100 WEEK STARTS '2018-01-23 12:11:33' ON COMPLETION NOT PRESERVE DISABLE ON REPLICA DO SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE EVENT db1.e_POSTGRESQL;
Event	sql_mode	time_zone	Create Event	character_set_client	collation_connection	Database Collation
e_POSTGRESQL	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	SYSTEM	CREATE DEFINER=`root`@`localhost` EVENT `e_POSTGRESQL` ON SCHEDULE EVERY 100 WEEK STARTS '2018-01-23 12:11:33' ON COMPLETION NOT PRESERVE DISABLE ON REPLICA DO SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE EVENT db1.e1;
Event	sql_mode	time_zone	Create Event	character_set_client	collation_connection	Database Collation
e1	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE,HIGH_NOT_PRECEDENCE	SYSTEM	CREATE DEFINER=`root`@`localhost` EVENT `e1` ON SCHEDULE EVERY 100 WEEK STARTS '2018-01-23 12:11:33' ON COMPLETION NOT PRESERVE DISABLE ON REPLICA DO SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE FUNCTION db1.f_DB2;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f_DB2	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER="root"@"localhost" FUNCTION "f_DB2"() RETURNS int
RETURN 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE FUNCTION db1.f_MAXDB;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f_MAXDB	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER="root"@"localhost" FUNCTION "f_MAXDB"() RETURNS int
RETURN 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE FUNCTION db1.f_MSSQL;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f_MSSQL	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER="root"@"localhost" FUNCTION "f_MSSQL"() RETURNS int
RETURN 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE FUNCTION db1.f_MYSQL323;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f_MYSQL323	HIGH_NOT_PRECEDENCE	CREATE DEFINER=`root`@`localhost` FUNCTION `f_MYSQL323`() RETURNS int
RETURN 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE FUNCTION db1.f_MYSQL40;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f_MYSQL40	HIGH_NOT_PRECEDENCE	CREATE DEFINER=`root`@`localhost` FUNCTION `f_MYSQL40`() RETURNS int
RETURN 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE FUNCTION db1.f_NO_FIELD_OPTIONS;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f_NO_FIELD_OPTIONS		CREATE DEFINER=`root`@`localhost` FUNCTION `f_NO_FIELD_OPTIONS`() RETURNS int
RETURN 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE FUNCTION db1.f_NO_KEY_OPTIONS;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f_NO_KEY_OPTIONS		CREATE DEFINER=`root`@`localhost` FUNCTION `f_NO_KEY_OPTIONS`() RETURNS int
RETURN 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE FUNCTION db1.f_NO_TABLE_OPTIONS;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f_NO_TABLE_OPTIONS		CREATE DEFINER=`root`@`localhost` FUNCTION `f_NO_TABLE_OPTIONS`() RETURNS int
RETURN 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE FUNCTION db1.f_ORACLE;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f_ORACLE	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER="root"@"localhost" FUNCTION "f_ORACLE"() RETURNS int
RETURN 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE FUNCTION db1.f_POSTGRESQL;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f_POSTGRESQL	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER="root"@"localhost" FUNCTION "f_POSTGRESQL"() RETURNS int
RETURN 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE FUNCTION db1.f1;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f1	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE,HIGH_NOT_PRECEDENCE	CREATE DEFINER="root"@"localhost" FUNCTION "f1"() RETURNS int
RETURN 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE PROCEDURE db1.p_DB2;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p_DB2	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER="root"@"localhost" PROCEDURE "p_DB2"()
SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE PROCEDURE db1.p_MAXDB;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p_MAXDB	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER="root"@"localhost" PROCEDURE "p_MAXDB"()
SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE PROCEDURE db1.p_MSSQL;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p_MSSQL	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER="root"@"localhost" PROCEDURE "p_MSSQL"()
SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE PROCEDURE db1.p_MYSQL323;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p_MYSQL323	HIGH_NOT_PRECEDENCE	CREATE DEFINER=`root`@`localhost` PROCEDURE `p_MYSQL323`()
SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE PROCEDURE db1.p_MYSQL40;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p_MYSQL40	HIGH_NOT_PRECEDENCE	CREATE DEFINER=`root`@`localhost` PROCEDURE `p_MYSQL40`()
SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE PROCEDURE db1.p_NO_FIELD_OPTIONS;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p_NO_FIELD_OPTIONS		CREATE DEFINER=`root`@`localhost` PROCEDURE `p_NO_FIELD_OPTIONS`()
SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE PROCEDURE db1.p_NO_KEY_OPTIONS;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p_NO_KEY_OPTIONS		CREATE DEFINER=`root`@`localhost` PROCEDURE `p_NO_KEY_OPTIONS`()
SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE PROCEDURE db1.p_NO_TABLE_OPTIONS;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p_NO_TABLE_OPTIONS		CREATE DEFINER=`root`@`localhost` PROCEDURE `p_NO_TABLE_OPTIONS`()
SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE PROCEDURE db1.p_ORACLE;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p_ORACLE	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER="root"@"localhost" PROCEDURE "p_ORACLE"()
SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE PROCEDURE db1.p_POSTGRESQL;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p_POSTGRESQL	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER="root"@"localhost" PROCEDURE "p_POSTGRESQL"()
SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE PROCEDURE db1.p1;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p1	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE,HIGH_NOT_PRECEDENCE	CREATE DEFINER="root"@"localhost" PROCEDURE "p1"()
SELECT 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci
SHOW CREATE TRIGGER db1.t_DB2;
Trigger	sql_mode	SQL Original Statement	character_set_client	collation_connection	Database Collation	Created
t_DB2	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER=`root`@`localhost` TRIGGER `t_DB2` BEFORE INSERT ON `t1` FOR EACH ROW SET @foo = 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci	2018-01-23 12:11:33.63
SHOW CREATE TRIGGER db1.t_MAXDB;
Trigger	sql_mode	SQL Original Statement	character_set_client	collation_connection	Database Collation	Created
t_MAXDB	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER=`root`@`localhost` TRIGGER `t_MAXDB` BEFORE INSERT ON `t1` FOR EACH ROW SET @foo = 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci	2018-01-23 12:11:33.63
SHOW CREATE TRIGGER db1.t_MSSQL;
Trigger	sql_mode	SQL Original Statement	character_set_client	collation_connection	Database Collation	Created
t_MSSQL	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER=`root`@`localhost` TRIGGER `t_MSSQL` BEFORE INSERT ON `t1` FOR EACH ROW SET @foo = 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci	2018-01-23 12:11:33.64
SHOW CREATE TRIGGER db1.t_MYSQL323;
Trigger	sql_mode	SQL Original Statement	character_set_client	collation_connection	Database Collation	Created
t_MYSQL323	HIGH_NOT_PRECEDENCE	CREATE DEFINER=`root`@`localhost` TRIGGER `t_MYSQL323` BEFORE INSERT ON `t1` FOR EACH ROW SET @foo = 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci	2018-01-23 12:11:33.64
SHOW CREATE TRIGGER db1.t_MYSQL40;
Trigger	sql_mode	SQL Original Statement	character_set_client	collation_connection	Database Collation	Created
t_MYSQL40	HIGH_NOT_PRECEDENCE	CREATE DEFINER=`root`@`localhost` TRIGGER `t_MYSQL40` BEFORE INSERT ON `t1` FOR EACH ROW SET @foo = 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci	2018-01-23 12:11:33.65
SHOW CREATE TRIGGER db1.t_NO_FIELD_OPTIONS;
Trigger	sql_mode	SQL Original Statement	character_set_client	collation_connection	Database Collation	Created
t_NO_FIELD_OPTIONS		CREATE DEFINER=`root`@`localhost` TRIGGER `t_NO_FIELD_OPTIONS` BEFORE INSERT ON `t1` FOR EACH ROW SET @foo = 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci	2018-01-23 12:11:33.65
SHOW CREATE TRIGGER db1.t_NO_KEY_OPTIONS;
Trigger	sql_mode	SQL Original Statement	character_set_client	collation_connection	Database Collation	Created
t_NO_KEY_OPTIONS		CREATE DEFINER=`root`@`localhost` TRIGGER `t_NO_KEY_OPTIONS` BEFORE INSERT ON `t1` FOR EACH ROW SET @foo = 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci	2018-01-23 12:11:33.66
SHOW CREATE TRIGGER db1.t_NO_TABLE_OPTIONS;
Trigger	sql_mode	SQL Original Statement	character_set_client	collation_connection	Database Collation	Created
t_NO_TABLE_OPTIONS		CREATE DEFINER=`root`@`localhost` TRIGGER `t_NO_TABLE_OPTIONS` BEFORE INSERT ON `t1` FOR EACH ROW SET @foo = 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci	2018-01-23 12:11:33.66
SHOW CREATE TRIGGER db1.t_ORACLE;
Trigger	sql_mode	SQL Original Statement	character_set_client	collation_connection	Database Collation	Created
t_ORACLE	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER=`root`@`localhost` TRIGGER `t_ORACLE` BEFORE INSERT ON `t1` FOR EACH ROW SET @foo = 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci	2018-01-23 12:11:33.67
SHOW CREATE TRIGGER db1.t_POSTGRESQL;
Trigger	sql_mode	SQL Original Statement	character_set_client	collation_connection	Database Collation	Created
t_POSTGRESQL	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE	CREATE DEFINER=`root`@`localhost` TRIGGER `t_POSTGRESQL` BEFORE INSERT ON `t1` FOR EACH ROW SET @foo = 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci	2018-01-23 12:11:33.67
SHOW CREATE TRIGGER db1.t1;
Trigger	sql_mode	SQL Original Statement	character_set_client	collation_connection	Database Collation	Created
t1	PIPES_AS_CONCAT,ANSI_QUOTES,IGNORE_SPACE,HIGH_NOT_PRECEDENCE	CREATE DEFINER=`root`@`localhost` TRIGGER `t1` BEFORE INSERT ON `t1` FOR EACH ROW SET @foo = 1	utf8mb3	utf8mb3_general_ci	latin1_swedish_ci	2018-01-23 12:11:33.68
==== Clean up ====
DROP SCHEMA db1;
include/rpl/stop_applier.inc
include/rpl/cleanup_fake_relay_log.inc
include/rpl/mysqlbinlog.inc
# The proper term is pseudo_replica_mode, but we use this compatibility alias
# to make the statement usable on server versions 8.0.24 and older.
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=1*/;
/*!50003 SET @OLD_COMPLETION_TYPE=@@COMPLETION_TYPE,COMPLETION_TYPE=0*/;
DELIMITER /*!*/;
ROLLBACK/*!*/;
# [empty]
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.pseudo_thread_id=#/*!*/;
SET @@session.foreign_key_checks=1, @@session.sql_auto_is_null=0, @@session.unique_checks=1, @@session.autocommit=1/*!*/;
SET @@session.sql_mode=1436549152/*!80005 &~0x1003ff00*//*!*/;
SET @@session.auto_increment_increment=1, @@session.auto_increment_offset=1/*!*/;
/*!\C utf8mb3 *//*!*/;
SET @@session.character_set_client=33,@@session.collation_connection=33,@@session.collation_server=8/*!*/;
SET @@session.lc_time_names=0/*!*/;
SET @@session.collation_database=DEFAULT/*!*/;
CREATE SCHEMA db1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
use `db1`/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE TABLE t1 (i INT)
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.sql_mode=59406/*!80005 &~0x1003ff00*//*!*/;
CREATE DEFINER="root"@"localhost" PROCEDURE "p_DB2"()
SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER="root"@"localhost" FUNCTION "f_DB2"() RETURNS int(11)
RETURN 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#.631960/*!*/;
CREATE DEFINER="root"@"localhost" TRIGGER t_DB2 BEFORE INSERT ON t1 FOR EACH ROW SET @foo = 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.time_zone='SYSTEM'/*!*/;
CREATE DEFINER="root"@"localhost" EVENT e_DB2 ON SCHEDULE EVERY 100 WEEK DO SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.sql_mode=268496910/*!80005 &~0x1003ff00*//*!*/;
CREATE DEFINER="root"@"localhost" PROCEDURE "p_MAXDB"()
SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER="root"@"localhost" FUNCTION "f_MAXDB"() RETURNS int(11)
RETURN 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#.638945/*!*/;
CREATE DEFINER="root"@"localhost" TRIGGER t_MAXDB BEFORE INSERT ON t1 FOR EACH ROW SET @foo = 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER="root"@"localhost" EVENT e_MAXDB ON SCHEDULE EVERY 100 WEEK DO SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.sql_mode=58382/*!80005 &~0x1003ff00*//*!*/;
CREATE DEFINER="root"@"localhost" PROCEDURE "p_MSSQL"()
SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER="root"@"localhost" FUNCTION "f_MSSQL"() RETURNS int(11)
RETURN 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#.643491/*!*/;
CREATE DEFINER="root"@"localhost" TRIGGER t_MSSQL BEFORE INSERT ON t1 FOR EACH ROW SET @foo = 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER="root"@"localhost" EVENT e_MSSQL ON SCHEDULE EVERY 100 WEEK DO SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.sql_mode=536936448/*!80005 &~0x1003ff00*//*!*/;
CREATE DEFINER=`root`@`localhost` PROCEDURE `p_MYSQL323`()
SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER=`root`@`localhost` FUNCTION `f_MYSQL323`() RETURNS int(11)
RETURN 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#.647664/*!*/;
CREATE DEFINER=`root`@`localhost` TRIGGER t_MYSQL323 BEFORE INSERT ON t1 FOR EACH ROW SET @foo = 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER=`root`@`localhost` EVENT e_MYSQL323 ON SCHEDULE EVERY 100 WEEK DO SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.sql_mode=537001984/*!80005 &~0x1003ff00*//*!*/;
CREATE DEFINER=`root`@`localhost` PROCEDURE `p_MYSQL40`()
SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER=`root`@`localhost` FUNCTION `f_MYSQL40`() RETURNS int(11)
RETURN 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#.652147/*!*/;
CREATE DEFINER=`root`@`localhost` TRIGGER t_MYSQL40 BEFORE INSERT ON t1 FOR EACH ROW SET @foo = 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER=`root`@`localhost` EVENT e_MYSQL40 ON SCHEDULE EVERY 100 WEEK DO SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.sql_mode=32768/*!80005 &~0x1003ff00*//*!*/;
CREATE DEFINER=`root`@`localhost` PROCEDURE `p_NO_FIELD_OPTIONS`()
SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER=`root`@`localhost` FUNCTION `f_NO_FIELD_OPTIONS`() RETURNS int(11)
RETURN 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#.656900/*!*/;
CREATE DEFINER=`root`@`localhost` TRIGGER t_NO_FIELD_OPTIONS BEFORE INSERT ON t1 FOR EACH ROW SET @foo = 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER=`root`@`localhost` EVENT e_NO_FIELD_OPTIONS ON SCHEDULE EVERY 100 WEEK DO SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.sql_mode=8192/*!80005 &~0x1003ff00*//*!*/;
CREATE DEFINER=`root`@`localhost` PROCEDURE `p_NO_KEY_OPTIONS`()
SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER=`root`@`localhost` FUNCTION `f_NO_KEY_OPTIONS`() RETURNS int(11)
RETURN 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#.661728/*!*/;
CREATE DEFINER=`root`@`localhost` TRIGGER t_NO_KEY_OPTIONS BEFORE INSERT ON t1 FOR EACH ROW SET @foo = 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER=`root`@`localhost` EVENT e_NO_KEY_OPTIONS ON SCHEDULE EVERY 100 WEEK DO SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.sql_mode=16384/*!80005 &~0x1003ff00*//*!*/;
CREATE DEFINER=`root`@`localhost` PROCEDURE `p_NO_TABLE_OPTIONS`()
SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER=`root`@`localhost` FUNCTION `f_NO_TABLE_OPTIONS`() RETURNS int(11)
RETURN 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#.666717/*!*/;
CREATE DEFINER=`root`@`localhost` TRIGGER t_NO_TABLE_OPTIONS BEFORE INSERT ON t1 FOR EACH ROW SET @foo = 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER=`root`@`localhost` EVENT e_NO_TABLE_OPTIONS ON SCHEDULE EVERY 100 WEEK DO SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.sql_mode=268493326/*!80005 &~0x1003ff00*//*!*/;
CREATE DEFINER="root"@"localhost" PROCEDURE "p_ORACLE"()
SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER="root"@"localhost" FUNCTION "f_ORACLE"() RETURNS int(11)
RETURN 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#.671967/*!*/;
CREATE DEFINER="root"@"localhost" TRIGGER t_ORACLE BEFORE INSERT ON t1 FOR EACH ROW SET @foo = 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER="root"@"localhost" EVENT e_ORACLE ON SCHEDULE EVERY 100 WEEK DO SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.sql_mode=57614/*!80005 &~0x1003ff00*//*!*/;
CREATE DEFINER="root"@"localhost" PROCEDURE "p_POSTGRESQL"()
SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER="root"@"localhost" FUNCTION "f_POSTGRESQL"() RETURNS int(11)
RETURN 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#.677247/*!*/;
CREATE DEFINER="root"@"localhost" TRIGGER t_POSTGRESQL BEFORE INSERT ON t1 FOR EACH ROW SET @foo = 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER="root"@"localhost" EVENT e_POSTGRESQL ON SCHEDULE EVERY 100 WEEK DO SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
SET @@session.sql_mode=805568270/*!80005 &~0x1003ff00*//*!*/;
CREATE DEFINER="root"@"localhost" PROCEDURE "p1"()
SELECT 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER="root"@"localhost" FUNCTION "f1"() RETURNS int(11)
RETURN 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#.682763/*!*/;
CREATE DEFINER="root"@"localhost" TRIGGER t1 BEFORE INSERT ON t1 FOR EACH ROW SET @foo = 1
/*!*/;
# original_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
# immediate_commit_timestamp= MICROSECONDS-FROM-EPOCH (YYYY-MM-DD HOURS:MINUTES:SECONDS TZ)
/*!80001 SET @@session.original_commit_timestamp= MICROSECONDS-FROM-EPOCH*//*!*/;
/*!80014 SET @@session.original_server_version= ORIGINAL_SERVER_VERSION*//*!*/;
/*!80014 SET @@session.immediate_server_version= IMMEDIATE_SERVER_VERSION*//*!*/;
SET @@SESSION.GTID_NEXT= '#'/*!*/;
SET TIMESTAMP=#/*!*/;
CREATE DEFINER="root"@"localhost" EVENT e1 ON SCHEDULE EVERY 100 WEEK DO SELECT 1
/*!*/;
SET @@SESSION.GTID_NEXT= '#' /* added by mysqlbinlog */ /*!*/;
DELIMITER ;
# End of log file
/*!50003 SET COMPLETION_TYPE=@OLD_COMPLETION_TYPE*/;
/*!50530 SET @@SESSION.PSEUDO_SLAVE_MODE=0*/;
SET @@global.relay_log_purge= @old_relay_log_purge;
include/rpl/deinit.inc
