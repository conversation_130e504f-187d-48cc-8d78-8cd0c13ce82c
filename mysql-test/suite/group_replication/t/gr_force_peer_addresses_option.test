################################################################################
# Verify that group_replication_force_members does behave as expected
# when:
#   0) The test requires two servers: M1 and M2.
#   1) A empty value is set on a OFFLINE member. It should succeed.
#   2) A value is set on a OFFLINE member. It should fail.
#   3) A empty value is set on a ONLINE member. It should succeed.
#   4) An IP is set on group with majority reachable. It should fail.
#   5) A empty value is set on a RECOVERING member. It should succeed.
#   6) A value is set on a RECOVERING member. It should fail.
#   7) Restart member with group_replication_force_members set. Group
#      Replication automatic start will error out.
#   8) Start Group Replication with group_replication_force_members set,
#      start will error out.
#   9) Clear group_replication_force_members, start will work.
#   10) Kill and restart a member to group loose majority.
#   11) Invalid address is set on a ONLINE member with majority unreachable. It
#       should fail.
#   12) Address of an UNREACHABLE member is set on a ONLINE member with majority
#       unreachable. It should fail.
#   13) IP address is set on a ONLINE member with majority unreachable. It
#       should succeed.
#   14) Clean up.
################################################################################
--source include/big_test.inc
--source include/have_debug_sync.inc
--source include/have_group_replication_plugin.inc
--source include/force_restart.inc
--let $rpl_skip_group_replication_start= 1
--source include/group_replication.inc

--let $rpl_connection_name= server2
--source include/connection.inc
--let $uuid_server2= `SELECT @@GLOBAL.SERVER_UUID`
--let $member2_group_replication_local_address= `SELECT @@GLOBAL.group_replication_local_address`
SET SESSION sql_log_bin= 0;
call mtr.add_suppression("Member is not ONLINE, it is not possible to force a new group membership.");
call mtr.add_suppression("group_replication_force_members must be empty on group start. Current value: '127.0.0.1:10000'");
call mtr.add_suppression("Unable to start Group Replication on boot");
call mtr.add_suppression("force_members can only be updated when Group Replication is running*");
call mtr.add_suppression("The member lost contact with a majority of the members in the group. Until the network is restored.*");
call mtr.add_suppression("\\[GCS\\] Peer address .* is not valid.");
call mtr.add_suppression("\\[GCS\\] The peers list contains invalid addresses.Please provide a list with only valid addresses.");
call mtr.add_suppression("Error setting group_replication_force_members value .* on group communication interfaces");
call mtr.add_suppression("The member resumed contact with a majority of the members in the group.*");
call mtr.add_suppression("Replica SQL for channel 'group_replication_applier': ... The replica coordinator and worker threads are stopped.*");
call mtr.add_suppression(".*Only alive members in the current configuration should be present in a forced configuration list.*");
call mtr.add_suppression("\\[GCS\\] Error reconfiguring group.");

SET SESSION sql_log_bin= 1;

--let $rpl_connection_name= server1
--source include/connection.inc

--let $member1_group_replication_local_address= `SELECT @@GLOBAL.group_replication_local_address`
--let $group_replication_group_seeds= `SELECT @@GLOBAL.group_replication_group_seeds`
--let $member1_uuid= query_get_value(SELECT @@SERVER_UUID, @@SERVER_UUID, 1)
--let $uuid_server1= `SELECT @@GLOBAL.SERVER_UUID`
SET SESSION sql_log_bin= 0;
call mtr.add_suppression("Member is OFFLINE, it is not possible to force a new group membership");
call mtr.add_suppression("force_members can only be updated when Group Replication is running*");
SET SESSION sql_log_bin= 1;


--echo
--echo ############################################################
--echo # 1. Set empty string value to
--echo #    group_replication_force_members on a OFFLINE
--echo #    member.
SET GLOBAL group_replication_force_members= "";

--let $assert_text= group_replication_force_members is empty
--let $assert_cond= "[SELECT @@GLOBAL.group_replication_force_members]" = ""
--source include/assert.inc


--echo
--echo ############################################################
--echo # 2. Set group_replication_force_members on a OFFLINE
--echo #    member.
--error ER_GROUP_REPLICATION_FORCE_MEMBERS_COMMAND_FAILURE
SET GLOBAL group_replication_force_members= "127.0.0.1:10000";

--let $assert_text= group_replication_force_members is empty
--let $assert_cond= "[SELECT @@GLOBAL.group_replication_force_members]" = ""
--source include/assert.inc


--echo
--echo ############################################################
--echo # 3. Set empty string value to
--echo #    group_replication_force_members on a ONLINE
--echo #    member.
--source include/start_and_bootstrap_group_replication.inc
SET GLOBAL group_replication_force_members= "";

--let $assert_text= group_replication_force_members is empty
--let $assert_cond= "[SELECT @@GLOBAL.group_replication_force_members]" = ""
--source include/assert.inc

--echo
--echo ############################################################
--echo # 4. Set valid IP string value to
--echo #    group_replication_force_members on a ONLINE member and
--echo #    majority of group members are reachable
--error ER_GROUP_REPLICATION_FORCE_MEMBERS_COMMAND_FAILURE
SET GLOBAL group_replication_force_members= "127.0.0.1:10001";

--let $assert_text= group_replication_force_members is empty
--let $assert_cond= "[SELECT @@GLOBAL.group_replication_force_members]" = ""
--source include/assert.inc


--echo
--echo ############################################################
--echo # 5. Set empty string value to
--echo #    group_replication_force_members on a RECOVERING
--echo #    member.
--let $rpl_connection_name= server2
--source include/connection.inc
SET @@GLOBAL.DEBUG='+d,recovery_thread_wait_before_finish';

--let $group_replication_start_member_state= RECOVERING
--source include/start_group_replication.inc

SET GLOBAL group_replication_force_members= "";

--let $assert_text= group_replication_force_members is empty
--let $assert_cond= "[SELECT @@GLOBAL.group_replication_force_members]" = ""
--source include/assert.inc


--echo
--echo ############################################################
--echo # 6. Set group_replication_force_members on a RECOVERING
--echo #    member.
--let $group_replication_member_state= RECOVERING
--source include/gr_wait_for_member_state.inc

--error ER_GROUP_REPLICATION_FORCE_MEMBERS_COMMAND_FAILURE
SET GLOBAL group_replication_force_members= "127.0.0.1:10000";

SET DEBUG_SYNC= "now WAIT_FOR signal.recovery_thread_wait_before_finish_reached";
SET @@GLOBAL.DEBUG='-d,recovery_thread_wait_before_finish';
SET DEBUG_SYNC= "now SIGNAL signal.recovery_end";

--let $rpl_connection_name= server2
--source include/connection.inc
--let $group_replication_member_state= ONLINE
--source include/gr_wait_for_member_state.inc

SET DEBUG_SYNC= 'RESET';


--echo
--echo ############################################################
--echo # 7. Restart member with group_replication_force_members
--echo #    set. Group Replication start will error out.
--echo #    2 members.
--let $rpl_connection_name= server2
--source include/connection.inc

--let $allow_rpl_inited=1
--let $_group_replication_local_address= `SELECT @@GLOBAL.group_replication_local_address`
--let $_group_replication_group_seeds= `SELECT @@GLOBAL.group_replication_group_seeds`
--let $restart_parameters=restart:--group_replication_local_address=$_group_replication_local_address --group_replication_group_seeds=$_group_replication_group_seeds --group_replication_start_on_boot=1 --group-replication-group-name=$group_replication_group_name --group_replication_force_members="127.0.0.1:10000"
--replace_result $_group_replication_local_address GROUP_REPLICATION_LOCAL_ADDRESS $_group_replication_group_seeds GROUP_REPLICATION_GROUP_SEEDS $group_replication_group_name GROUP_REPLICATION_GROUP_NAME
--source include/restart_mysqld.inc

--let $rpl_server_number= 2
--source include/rpl/reconnect.inc

--let $assert_text= Member 2 is OFFLINE
--let $assert_cond= [SELECT COUNT(*) FROM performance_schema.replication_group_members WHERE member_state="OFFLINE"] = 1
--source include/assert.inc


--echo
--echo ############################################################
--echo # 8. Start Group Replication while
--echo #    group_replication_force_members is set. Start
--echo #    will error out.

--let $assert_text= group_replication_force_members must be 127.0.0.1:10000
--let $assert_cond= "[SELECT @@GLOBAL.group_replication_force_members]" = "127.0.0.1:10000"
--source include/assert.inc

--error ER_GROUP_REPLICATION_CONFIGURATION
START GROUP_REPLICATION;

--let $assert_text= Member 2 is OFFLINE
--let $assert_cond= [SELECT COUNT(*) FROM performance_schema.replication_group_members WHERE member_state="OFFLINE"] = 1
--source include/assert.inc


--echo
--echo ############################################################
--echo # 9. Clear group_replication_force_members and start
--echo #    Group Replication. Group will have the 2 members.
SET GLOBAL group_replication_force_members= "";

--let $assert_text= group_replication_force_members is empty
--let $assert_cond= "[SELECT @@GLOBAL.group_replication_force_members]" = ""
--source include/assert.inc

--source include/start_group_replication.inc

# Server 1
--let $rpl_connection_name= server1
--source include/connection.inc
--let $group_replication_number_of_members= 2
--source include/gr_wait_for_number_of_members.inc

--let $assert_text= Server 1 must be present on group members
--let $assert_cond= [SELECT COUNT(*) FROM performance_schema.replication_group_members WHERE member_id="$uuid_server1"] = 1
--source include/assert.inc
--let $assert_text= Server 2 must be present on group members
--let $assert_cond= [SELECT COUNT(*) FROM performance_schema.replication_group_members WHERE member_id="$uuid_server2"] = 1
--source include/assert.inc

# Server 2
--let $rpl_connection_name= server2
--source include/connection.inc
--let $group_replication_number_of_members= 2
--source include/gr_wait_for_number_of_members.inc

--let $assert_text= Server 1 must be present on group members
--let $assert_cond= [SELECT COUNT(*) FROM performance_schema.replication_group_members WHERE member_id="$uuid_server1"] = 1
--source include/assert.inc
--let $assert_text= Server 2 must be present on group members
--let $assert_cond= [SELECT COUNT(*) FROM performance_schema.replication_group_members WHERE member_id="$uuid_server2"] = 1
--source include/assert.inc

--let $assert_text= group_replication_force_members must be empty
--let $assert_cond= "[SELECT @@GLOBAL.group_replication_force_members]" = ""
--source include/assert.inc

--echo
--echo ############################################################
--echo # 10. Kill and restart a member to group loose majority.

--let $rpl_connection_name= server1
--source include/connection.inc

--let $restart_parameters=restart:--group_replication_local_address=$member1_group_replication_local_address --group_replication_group_seeds=$group_replication_group_seeds --group_replication_group_name=$group_replication_group_name
--replace_result $member1_group_replication_local_address GROUP_REPLICATION_LOCAL_ADDRESS1 $group_replication_group_seeds GROUP_REPLICATION_GROUP_SEEDS $group_replication_group_name GROUP_REPLICATION_GROUP_NAME
--source include/kill_and_restart_mysqld.inc

--let $rpl_server_number= 1
--source include/rpl/reconnect.inc

--let $rpl_connection_name= server2
--source include/connection.inc

# confirm member is unreachable
--let $group_replication_member_state= UNREACHABLE
--let $group_replication_member_id= $member1_uuid
--source include/gr_wait_for_member_state.inc


--echo
--echo ############################################################
--echo # 11. Set invalid  IP string value to
--echo #    group_replication_force_members on a ONLINE member and
--echo #    majority of group members are unreachable.

--error ER_GROUP_REPLICATION_FORCE_MEMBERS_COMMAND_FAILURE
SET GLOBAL group_replication_force_members= "256.256.256.777:1234";

--let $assert_text= group_replication_force_members is empty
--let $assert_cond= "[SELECT @@GLOBAL.group_replication_force_members]" = ""
--source include/assert.inc

--echo
--echo ############################################################
--echo # 12. Set IP of an UNREACHABLE member to
--echo #    group_replication_force_members on a ONLINE member .

--replace_result $member1_group_replication_local_address GROUP_REPLICATION_LOCAL_ADDRESS1
--error ER_GROUP_REPLICATION_FORCE_MEMBERS_COMMAND_FAILURE
--eval SET GLOBAL group_replication_force_members= "$member1_group_replication_local_address";

--let $assert_text= group_replication_force_members is empty
--let $assert_cond= "[SELECT @@GLOBAL.group_replication_force_members]" = ""
--source include/assert.inc


--echo
--echo ############################################################
--echo # 13. Set valid  IP string value to
--echo #    group_replication_force_members on a ONLINE member and
--echo #    majority of group members are unreachable

# unblock group
--replace_result $member2_group_replication_local_address GROUP_REPLICATION_LOCAL_ADDRESS1
eval SET GLOBAL group_replication_force_members= "$member2_group_replication_local_address";

--let $assert_text= group_replication_force_members has member2 local address
--let $assert_cond= "[SELECT @@GLOBAL.group_replication_force_members]" = "$member2_group_replication_local_address"
--source include/assert.inc


--echo
--echo ############################################################
--echo # 14. Clean up.

# rejoin M1 to group
--let $rpl_connection_name= server1
--source include/connection.inc
--source include/start_group_replication.inc

--source include/group_replication_end.inc
