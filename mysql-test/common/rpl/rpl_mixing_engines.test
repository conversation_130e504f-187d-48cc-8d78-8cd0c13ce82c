################################################################################
# - Introduction
#   This checks if transactions that mixes transactional and non-transactional
#   are correctly handled. There are several types of statements that require
#   attention because of their special behavior in transactions:
#
#   * Non-transactional updates that take place inside a transaction present
#   problems for logging because (1) they are visible to other clients before
#   the transaction is committed, and (2) they are not rolled back even if the
#   transaction is rolled back. It is not always possible to log correctly in
#   statement format when both transactional and non-transactional tables are
#   used in the same transaction.
#
#   * Statements that do an implicit commit (i.e., most but not all DDL, and
#   some utility commands) are logged specially due to unspecified requirements by
#   NDB.
# 
#   * Statements that update temporary tables need special treatment since they
#   are not logged in row format.
#
# - Definitions
#
#   To reason about logging different table types, we make some preliminary
#   definitions.
#
#   * A table that has a transactional engine is called a T-table.
#
#   * A table that has a non-transactional engine is called an N-table.
#
#   * A statement makes an N-write if it makes any type of change to the server
#   state that will not be changed by a ROLLBACK.
#
#   * Events are either appended to the Transaction Cache (TC) or to the
#   Statement Cache (SC) or written directly to the binlog. 
#
# - Preliminary Rules
#
#   The following preliminary rules are actually consequences of the principle
#   that statements shall be correctly logged when binlog_format=MIXED or ROW.
#   They also apply when binlog_format=STATEMENT: this makes statement format
#   work in many practical cases.
#
#   * (Causality) If statement A is executed before statement B, and B is logged
#   in statement format, and B reads tables that A may modifies, then B shall be
#   logged after A.
#
#   * (Durability) Events shall be written to the binary log at the moment they
#   take effect. In particular, changes to N-tables shall be written to the
#   binary log when they have been executed, and changes to T-tables shall be
#   written to the binary log on commit. If --sync-binlog has been specified,
#   then it suffices that events are be written to the binary log at the next
#   synchronization point.
#
#   * (causality-precedence) If causality and durability cannot both be
#   fulfilled, then casuality is considered more important.
#
# - Rules for non-committing statements, except CREATE TEMPORARY TABLE...SELECT
#
#   The preliminary rules above, together with the principles for logging format
#   , have been used to construct the following rules.
#
#   CALL statements are unrolled, so that each statement executed by the stored
#   procedure is logged separately. (If a stored procedure A invokes a stored
#   procedure B, then B is unrolled recursively). In the following, we assume
#   that unrolling has already been done, and the word "statement" refers to a
#   non-CALL top-level statement or a non-CALL sub-statement.
#
#   Let S be a logged statement that does not have an implicit commit, except
#   CREATE TEMPORARY TABLE...SELECT (This includes all "pure DML": INSERT,
#   UPDATE, DELETE, REPLACE, TRUNCATE, SELECT, DO, CALL, EXECUTE, LOAD DATA
#   INFILE, and BINLOG. It also includes CREATE TEMPORARY TABLE without SELECT,
#   and DROP TEMPORARY TABLE. CREATE TEMPORARY TABLE...SELECT is handled in the
#   next subsection).
#
#   Before executing S, determine unsafeness:
#
#     * If S either makes N-writes or reads from an N-table, and either S or a
#     previous statement in the same transaction reads or writes to a T-table,
#     then S is marked as unsafe. 
#
#   When logging S, determine where to log it by applying the following rules in
#   order: 
#
#     * If S is to be logged in statement format (i.e., if one of the following
#     holds: (1) STATEMENT; (2) MIXED and S is safe; (3) S is of DDL type, i.e.,
#     CREATE TEMPORARY TABLE):
#       1. If S produces an error and does not do any N-write, do not log.
#       2. Otherwise, if either S or any previous statement in the same
#       transaction reads or writes in any T-tables, log to TC.
#       3. Otherwise, log to SC.
#
#     * If S is to be logged in row format (i.e., if S is DML and one of the
#     following holds: (1) ROW; (2) MIXED and S is unsafe):
#       1. Do not log row events that write to temporary tables.
#       2. Log row events that write to non-temporary N-tables to SC.
#       3. Log row events that write to non-temporary T-tables to TC, except
#       rows that are rolled back due to an error. (Note: if there is an error,
#       rows written to a T-table are kept if there are subsequent rows written
#       to an N-table.)
#
#     * At the end of S, write BEGIN + SC + COMMIT to the binlog and clear the
#     SC.
#
#   At end of transaction: 
#
#     * At COMMIT or implicit commit, where all XA tables in the transaction
#     succeed in the "prepare" phase:
#       1. If the TC is non-empty, write BEGIN + TC + COMMIT to the binlog.
#       2. If the TC is empty, do nothing. 
#     
#     * At ROLLBACK; or at COMMIT or implicit commit where some XA table fails
#     in the "prepare" phase:
#       1. If the TC contains any N-write, write BEGIN + TC + ROLLBACK to the
#       binlog.
#       2. If the TC does not contain any N-write, do nothing. 
#
#     * At ROLLBACK TO SAVEPOINT:
#       1. If the TC contains any N-write after the savepoint, write ROLLBACK TO
#       SAVEPOINT to the TC.
#       2. Otherwise, clear the part of the TC that starts at the savepoint and
#       extends to the end of the TC. (Bug#47327 breaks this rule)
#
#     * Clear the TC at the end of the transaction.
#
# - Rules for CREATE [TEMPORARY] TABLE...SELECT
#     
#   First, unsafeness is determined as above (R-unsafe-transaction). Then the
#   logging format is decided. Then the following rules apply.
#
#     * If logging in statement format (i.e., one of the following holds: (1)
#     STATEMENT; (2) MIXED and statement is safe):
#       1. If there is an error, do not write anything.
#       2. If there is no error and the TEMPORARY keyword is used, write the
#       entire CREATE...SELECT statement to the TC.
#       3. If there is no error and the TEMPORARY keyword is not used, write the
#       entire CREATE...SELECT directly to the binlog. 
#
#     * If logging in row format (i.e., one of the following holds: (1) ROW; (2)
#     MIXED and statement is unsafe):
#       1. If the TEMPORARY keyword is used, do not write anything.
#       2. If the TEMPORARY keyword is not used, write CREATE TABLE (without
#       select) + BEGIN + row events + COMMIT to the TC. If there is an error,
#       clear the TC; otherwise flush the TC to the binlog at the end of the
#       statement and then clear the TC. (Note: currently Bug#47899 breaks this
#       rule)
#
# - Rules for committing statements, except CREATE [TEMPORARY] TABLE...SELECT
#
#   * All other statements that have a pre-commit are written directly to the
#   binlog. (Note: this is semantically equivalent to writing it to the SC and
#   flushing the SC. However, due to requirements by NDB (which have not been
#   clarified), we write directly to the binlog.)
#
# We use the include file common/rpl/mixing_engines.inc to generate sql commands from a
# format string. The format string consists of a sequence of 'codes' separated
# by spaces. The following codes exist:
#
# - Define the scope of a transaction:
# B - Begin.
# C - Commit.
# R - Rollback.
# Sn - Savepoint Sn.
# Rn - Rollback to Sn.
#
# - Change only T-Tables:
# T - Updates a T-Table.
# T-trig - Updates T-Tables through a trigger.
# T-func - Updates T-Tables through a function.
# T-proc - Updates T-Tables through a procedure.
# eT - Fails while updating the first tuple in a T-Table.
# Te - Fails while updating an n-tuple (n > 1) in a T-Table.
# Te-trig - Fails while updating an n-tuple (n > 1) in a T-Table.
# Te-func - Fails while updating an n-tuple (n > 1) in a T-Table.
#
# - Change only N-Tables 
# N - Updates a N-Table.
# N-trig - Updates N-Tables through a trigger.
# N-func - Updates N-Tables through a function.
# N-proc - Updates N-Tables through a procedure.
# eN - Fails while updating the first tuple in a N-Table.
# Ne - Fails while updating an n-tuple (n > 1) in a N-Table.
# Ne-trig - Fails while updating an n-tuple (n > 1) in a N-Table.
# Ne-func - Fails while updating an n-tuple (n > 1) in a N-Table.
#
# - Read T-table and write N-table:
# tN - Updates a N-Table
# tNe - Fails while updating an n-tuple (n > 1) in a N-Table.
#
# - Read N-table and write T-table:
# nT - Updates a T-Table.
# nTe - Fails while updating an n-tuple (n > 1) in a T-Table.
#
# - Update both types of tables. First a N-Table and the a T-Table:
# NT - Upates both types of tables through an update statement.
# NT-trig - Updates both types of tables through a trigger.
# NT-func - Updates both types of tables through a procedure.
# NeT-trig - Fails while updating an n-tuple (n > 1) in a T-Table.
# NeT-func - Fails while updating an n-tuple (n > 1) in a T-Table.
#
# - Update both types of tables. First a T-Table and the a N-Table:
# TN - Upates both types of tables through an update statement.
# TN-trig - Updates both types of tables through a trigger.
# TN-func - Updates both types of tables through a procedure.
# TeN-trig - Fails while updating an n-tuple (n > 1) in a N-Table.
# TeN-func - Fails while updating an n-tuple (n > 1) in a N-Table.
#
# - This is CREATE...SELECT:
# CS-T->T - Creates a T-table selecting from a T-table.
# CS-N->N - Creates a N-table selecting from a N-table.
# CS-T->N - Creates a T-table selecting form a N-table.
# CS-N->T - Creates a N-table selecting from a T-table.
# CSe-T->T - Fails while creating a T-table selecting from a T-table.
# CSe-N->N - Fails while creating a N-table selecting from a N-table.
# CSe-T->N - Fails while creating a T-table selecting from a a N-table.
# CSe-N->T - Fails while creating a N-table selecting from a T-table.
# drop-CS - Drops any of the tables previously created.
# trunc-CS-T - Truncates a T-table previously created.
# trunc-CS-N - Truncates a N-table previously created.
# CT - Creates a temporary T-table.
# drop-CT - Drops a temporary T-table.
#
# - This is INSERT...SELECT:
# IS-T<-T - Inserts data from a T-table into a T-table.
# IS-T<-N - Inserts data from a N-table into a T-table.
# IS-N<-T - Inserts data from a T-table into a N-table.
# IS-N<-N - Inserts data from a N-table into a N-table.
# ISe-T<-T - Fails while inserting data from a T-table into a T-table.
# ISe-T<-N - Fails while inserting data from a N-table into a T-table.
# ISe-N<-T - Fails while inserting data from a T-table into a N-table.
# ISe-N<-N - Fails while inserting data from a N-table into a N-table.
#
# For the CREATE...SELECT and INSERT...SELECT, the table names are defined based
# on the variable $tb_id which is automatically incremented after each drop.
# This indirectly means that two tables cannot co-exist unless we manually keep
# the variable $tb_id.
#
# The format of the entries in the binlog depends on the mode and on the type
# statements: S - statement and R - row. And when it is clear from the context
# which statement is referred to, we sometimes use "M" to denote a "Mixed"
# statement, i.e., one that accesses both a T-table and an N-table.
#
# For further details, please, read WL#2687 and WL#5072.
################################################################################
--echo #########################################################################
--echo #                            CONFIGURATION
--echo #########################################################################

call mtr.add_suppression("Unsafe statement written to the binary log using statement format since BINLOG_FORMAT = STATEMENT");

--let $verbose= 1
--let $commands= configure
--source common/rpl/mixing_engines.inc

--echo #########################################################################
--echo #         1 - MIXING TRANSACTIONAL and NON-TRANSACTIONAL TABLES
--echo #########################################################################
connection master;

--echo
--echo
--echo
--echo
--echo #
--echo #1) Generates in the binlog what follows:
--echo #      --> STMT  "B T C" entries, format S.
--echo #      --> ROW   "B T C" entries, format R.
--echo #      --> MIXED "B T C" entries, format S.
--echo #
--let $commands= T
--source common/rpl/mixing_engines.inc

--let $commands= T-trig
--source common/rpl/mixing_engines.inc

--let $commands= T-func
--source common/rpl/mixing_engines.inc

--let $commands= T-proc
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #1.e) Generates in the binlog what follows:
--echo #        --> STMT  empty.
--echo #        --> ROW   empty.
--echo #        --> MIXED empty.
--echo #
--let $commands= eT
--source common/rpl/mixing_engines.inc

--let $commands= Te
--source common/rpl/mixing_engines.inc

--let $commands= Te-trig
--source common/rpl/mixing_engines.inc

--let $commands= Te-func
--source common/rpl/mixing_engines.inc

--echo
--echo
--echo
--echo
--echo #
--echo #2) Generates in the binlog what follows:
--echo #      --> STMT  "B N C" entry, format S.
--echo #      --> ROW   "B N C" entry, format R.
--echo #      --> MIXED "B N C" entry, format S.
--echo #
--let $commands= N
--source common/rpl/mixing_engines.inc

--let $commands= N-trig
--source common/rpl/mixing_engines.inc

--let $commands= N-func
--source common/rpl/mixing_engines.inc

--let $commands= N-proc
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #2.e) Generates in the binlog what follows if a N-table is changed:
--echo #        --> STMT  "B N C" entry, format S.
--echo #        --> ROW   "B N C" entry, format R.
--echo #        --> MIXED "B N C" entry, format S.
--echo #
--let $commands= eN
--source common/rpl/mixing_engines.inc

--let $commands= Ne
--source common/rpl/mixing_engines.inc

--let $commands= Ne-trig
--source common/rpl/mixing_engines.inc

--let $commands= Ne-func
--source common/rpl/mixing_engines.inc

--echo
--echo
--echo
--echo
--echo #
--echo #3) Generates in the binlog what follows:
--echo #      --> STMT  "B M C" entry if only N-Table is changed, format S.
--echo #      --> STMT  "B M C" entries, format S.
--echo #      --> ROW   "B N T B T C" entries, format R.
--echo #      --> MIXED "B N T B T C" entries, format R.
--echo #
--let $commands= tN
--source common/rpl/mixing_engines.inc

--let $commands= nT
--source common/rpl/mixing_engines.inc

--let $commands= NT
--source common/rpl/mixing_engines.inc

--let $commands= NT-trig
--source common/rpl/mixing_engines.inc

--let $commands= NT-func
--source common/rpl/mixing_engines.inc

--let $commands= TN
--source common/rpl/mixing_engines.inc

--let $commands= TN-trig
--source common/rpl/mixing_engines.inc

--let $commands= TN-func
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #3.e) Generates in the binlog what follows:
--echo #      --> STMT  "B M C" entry if only N-Table is changed, format S.
--echo #      --> STMT  "B M R" entries, format S.
--echo #      --> ROW   "B N C" entry, format R.
--echo #      --> MIXED "B N C" entry, format R.
--let $commands= tNe
--source common/rpl/mixing_engines.inc

--let $commands= nTe
--source common/rpl/mixing_engines.inc

--let $commands= NeT-trig
--source common/rpl/mixing_engines.inc

--let $commands= NeT-func
--source common/rpl/mixing_engines.inc

--let $commands= TeN-trig
--source common/rpl/mixing_engines.inc

--let $commands= TeN-func
--source common/rpl/mixing_engines.inc

--echo
--echo
--echo
--echo
--echo #
--echo #4) Generates in the binlog what follows:
--echo #     --> STMT  "B T T C" entries, format S.
--echo #     --> ROW   "B T T C" entries, format R.
--echo #     --> MIXED "B T T C" entries, format S
--echo #
--let $commands= B T T C
--source common/rpl/mixing_engines.inc

--let $commands= B T T-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T T-func C
--source common/rpl/mixing_engines.inc

--let $commands= B T T-proc C
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig T C
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig T-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig T-func C
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig T-proc C
--source common/rpl/mixing_engines.inc

--let $commands= B T-func T C
--source common/rpl/mixing_engines.inc

--let $commands= B T-func T-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T-func T-func C
--source common/rpl/mixing_engines.inc

--let $commands= B T-func T-proc C
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc T C
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc T-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc T-func C
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc T-proc C
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #4.e) Generates in the binlog what follows:
--echo #       --> STMT  "B T C" entries, format S.
--echo #       --> ROW   "B T C" entries, format R.
--echo #       --> MIXED "B T C" entries, format S.
--echo #
--let $commands= B T eT C
--source common/rpl/mixing_engines.inc

--let $commands= B T Te C
--source common/rpl/mixing_engines.inc

--let $commands= B T Te-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T Te-func C
--source common/rpl/mixing_engines.inc

--let $commands= B eT T C
--source common/rpl/mixing_engines.inc

--let $commands= B Te T C
--source common/rpl/mixing_engines.inc

--let $commands= B Te-trig T C
--source common/rpl/mixing_engines.inc

--let $commands= B Te-func T C
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #5) Generates in the binlog what follows:
--echo #     --> STMT  empty.
--echo #     --> ROW   empty.
--echo #     --> MIXED empty.
--echo #
--let $commands= B T T R
--source common/rpl/mixing_engines.inc

--let $commands= B T T-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T T-func R
--source common/rpl/mixing_engines.inc

--let $commands= B T T-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig T R
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig T-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig T-func R
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig T-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B T-func T R
--source common/rpl/mixing_engines.inc

--let $commands= B T-func T-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T-func T-func R
--source common/rpl/mixing_engines.inc

--let $commands= B T-func T-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc T R
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc T-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc T-func R
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc T-proc R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #5.e) Generates in the binlog what follows:
--echo #       --> STMT  empty.
--echo #       --> ROW   empty.
--echo #       --> MIXED empty.
--echo #
--let $commands= B T eT R
--source common/rpl/mixing_engines.inc

--let $commands= B T Te R
--source common/rpl/mixing_engines.inc

--let $commands= B T Te-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T Te-func R
--source common/rpl/mixing_engines.inc

--let $commands= B eT T R
--source common/rpl/mixing_engines.inc

--let $commands= B Te T R
--source common/rpl/mixing_engines.inc

--let $commands= B Te-trig T R
--source common/rpl/mixing_engines.inc

--let $commands= B Te-func T R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #6) Generates in the binlog what follows:
--echo #     --> STMT  "B N C B N C" entries, format S.
--echo #     --> ROW   "B N C B N C" entries, format R.
--echo #     --> MIXED "B N C B N C" entries, format S.
--echo #
--let $commands= B N N C
--source common/rpl/mixing_engines.inc

--let $commands= B N N-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N N-func C
--source common/rpl/mixing_engines.inc

--let $commands= B N N-proc C
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig N C
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig N-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig N-func C
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig N-proc C
--source common/rpl/mixing_engines.inc

--let $commands= B N-func N C
--source common/rpl/mixing_engines.inc

--let $commands= B N-func N-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N-func N-func C
--source common/rpl/mixing_engines.inc

--let $commands= B N-func N-proc C
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc N C
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc N-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc N-func C
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc N-proc C
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #6.e) Generates in the binlog what follows if a N-Table is changed:
--echo #     --> STMT  "B N C B N C" entries, format S.
--echo #     --> ROW   "B N C B N C" entries, format R.
--echo #     --> MIXED "B N C B N C" entries, format S.
--echo #
--let $commands= B N eN C
--source common/rpl/mixing_engines.inc

--let $commands= B N Ne C
--source common/rpl/mixing_engines.inc

--let $commands= B N Ne-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N Ne-func C
--source common/rpl/mixing_engines.inc

--let $commands= B eN N C
--source common/rpl/mixing_engines.inc

--let $commands= B Ne N C
--source common/rpl/mixing_engines.inc

--let $commands= B Ne-trig N C
--source common/rpl/mixing_engines.inc

--let $commands= B Ne-func N C
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #7) Generates in the binlog what follows:
--echo #      --> STMT  "B N C B N C" entries, format S.
--echo #      --> ROW   "B N C B N C" entries, format R.
--echo #      --> MIXED "B N C B N C" entries, format S.
--echo #
--let $commands= B N N R
--source common/rpl/mixing_engines.inc

--let $commands= B N N-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N N-func R
--source common/rpl/mixing_engines.inc

--let $commands= B N N-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig N R
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig N-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig N-func R
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig N-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B N-func N R
--source common/rpl/mixing_engines.inc

--let $commands= B N-func N-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N-func N-func R
--source common/rpl/mixing_engines.inc

--let $commands= B N-func N-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc N R
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc N-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc N-func R
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc N-proc R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #7.e) Generates in the binlog what follows if a N-Table is changed:
--echo #     --> STMT  "B N C B N C" entries, format S.
--echo #     --> ROW   "B N C B N C" entries, format R.
--echo #     --> MIXED "B N C B N C" entries, format S.
--echo #
--let $commands= B N eN R
--source common/rpl/mixing_engines.inc

--let $commands= B N Ne R
--source common/rpl/mixing_engines.inc

--let $commands= B N Ne-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N Ne-func R
--source common/rpl/mixing_engines.inc

--let $commands= B eN N R
--source common/rpl/mixing_engines.inc

--let $commands= B Ne N R
--source common/rpl/mixing_engines.inc

--let $commands= B Ne-trig N R
--source common/rpl/mixing_engines.inc

--let $commands= B Ne-func N R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #8) Generates in the binlog what follows:
--echo #       --> STMT  "B T N C" entries, format S.
--echo #       --> ROW   "B N C B T C" entries, format R.
--echo #       --> MIXED "B N C B T C" entries, format R in N and S in T.
--echo #
--let $commands= B T N C
--source common/rpl/mixing_engines.inc

--let $commands= B T N-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T N-func C
--source common/rpl/mixing_engines.inc

--let $commands= B T N-proc C
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig N C
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig N-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig N-func C
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig N-proc C
--source common/rpl/mixing_engines.inc

--let $commands= B T-func N C
--source common/rpl/mixing_engines.inc

--let $commands= B T-func N-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T-func N-func C
--source common/rpl/mixing_engines.inc

--let $commands= B T-func N-proc C
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc N C
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc N-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc N-func C
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc N-proc C
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #8.e) Generates in the binlog what follows if T-* fails:
--echo #       --> STMT  "B N C" entry, format S.
--echo #       --> ROW   "B N C" entry, format R.
--echo #       --> MIXED "B N C" entry, format R.
--echo #     Otherwise, what follows if N-* fails and a N-Table is changed:
--echo #       --> STMT  "B T N C" entries, format S.
--echo #       --> ROW   "B N C B T C" entries, format R.
--echo #       --> MIXED "B N C B T C" entries, format R in N and S in T.
--echo #
--let $commands= B eT N C
--source common/rpl/mixing_engines.inc

--let $commands= B Te N C
--source common/rpl/mixing_engines.inc

--let $commands= B T eN C
--source common/rpl/mixing_engines.inc

--let $commands= B T Ne C
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #9) Generates in the binlog what follows:
--echo #     --> STMT  "B T N R" entries, format S.
--echo #     --> ROW   "B N C" entry, format R.
--echo #     --> MIXED "B N C" entry, format R.
--echo #
--let $commands= B T N R
--source common/rpl/mixing_engines.inc

--let $commands= B T N-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T N-func R
--source common/rpl/mixing_engines.inc

--let $commands= B T N-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig N R
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig N-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig N-func R
--source common/rpl/mixing_engines.inc

--let $commands= B T-trig N-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B T-func N R
--source common/rpl/mixing_engines.inc

--let $commands= B T-func N-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T-func N-func R
--source common/rpl/mixing_engines.inc

--let $commands= B T-func N-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc N R
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc N-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc N-func R
--source common/rpl/mixing_engines.inc

--let $commands= B T-proc N-proc R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #9.e) Generates in the binlog what follows if T* fails:
--echo #       --> STMT  "B N C" entry, format S.
--echo #       --> ROW   "B N C" entry, format R.
--echo #       --> MIXED "B N C" entry, format R.
--echo #     Otherwise, what follows if N* fails and a N-Table is changed:
--echo #       --> STMT  "B T N R" entries, format S.
--echo #       --> ROW   "B N C" entry, format R.
--echo #       --> MIXED "B N C" entry, format R.
--echo #
--let $commands= B eT N R
--source common/rpl/mixing_engines.inc

--let $commands= B Te N R
--source common/rpl/mixing_engines.inc

--let $commands= B T eN R
--source common/rpl/mixing_engines.inc

--let $commands= B T Ne R
--source common/rpl/mixing_engines.inc



--echo
--echo
--echo
--echo
--echo #
--echo #10) Generates in the binlog:
--echo #     --> STMT  "B N C B T C" entries, format S.
--echo #     --> ROW   "B N C B T C" entries, format R.
--echo #     --> MIXED "B N C B T C" entries, format S.
--echo #
--let $commands= B N T C
--source common/rpl/mixing_engines.inc

--let $commands= B N T-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N T-func C
--source common/rpl/mixing_engines.inc

--let $commands= B N T-proc C
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig T C
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig T-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig T-func C
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig T-proc C
--source common/rpl/mixing_engines.inc

--let $commands= B N-func T C
--source common/rpl/mixing_engines.inc

--let $commands= B N-func T-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N-func T-func C
--source common/rpl/mixing_engines.inc

--let $commands= B N-func T-proc C
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc T C
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc T-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc T-func C
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc T-proc C
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #11) Generates in the binlog what follows:
--echo #     --> STMT  "B N C" entries, format S.
--echo #     --> ROW   "B N C" entries, format R.
--echo #     --> MIXED "B N C" entries, format S.
--echo #

--let $commands= B N T R
--source common/rpl/mixing_engines.inc

--let $commands= B N T-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B N T-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N T-func R
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig T R
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig T-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig T-func R
--source common/rpl/mixing_engines.inc

--let $commands= B N-trig T-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B N-func T R
--source common/rpl/mixing_engines.inc

--let $commands= B N-func T-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N-func T-func R
--source common/rpl/mixing_engines.inc

--let $commands= B N-func T-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc T R
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc T-proc R
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc T-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N-proc T-func R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #12) Generates in the binlog what follows:
--echo #      --> STMT  "B M C B T C" entries if in M only N-Table is changed, format S.
--echo #      --> STMT  "B M T C" entries, format S.
--echo #      --> ROW   "B N C B T T C" entries, format R.
--echo #      --> MIXED "B N C B T T C" entries, format R in N/T and format S in T.
--echo #
--let $commands= B tN T C
--source common/rpl/mixing_engines.inc

--let $commands= B nT T C
--source common/rpl/mixing_engines.inc

--let $commands= B NT T C
--source common/rpl/mixing_engines.inc

--let $commands= B NT-trig T C
--source common/rpl/mixing_engines.inc

--let $commands= B NT-func T C
--source common/rpl/mixing_engines.inc

--let $commands= B TN T C
--source common/rpl/mixing_engines.inc

--let $commands= B TN-trig T C
--source common/rpl/mixing_engines.inc

--let $commands= B TN-func T C
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #12.e) Generates in the binlog what follows if a N-Table is changed:
--echo #        --> STMT  "B M C B T C" entries if in M only N-Table is changed, format S.
--echo #        --> STMT  "B M T C" entries, format S.
--echo #        --> ROW   "B N C B T T C" entries, format R.
--echo #        --> MIXED "B N C B T T C" entries, format R in N/T and format S in T.
  --echo #  
--let $commands= B tNe T C
--source common/rpl/mixing_engines.inc

--let $commands= B nTe T C
--source common/rpl/mixing_engines.inc

--let $commands= B NeT-trig T C
--source common/rpl/mixing_engines.inc

--let $commands= B NeT-func T C
--source common/rpl/mixing_engines.inc

--let $commands= B TeN-trig T C
--source common/rpl/mixing_engines.inc

--let $commands= B TeN-func T C
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #13) "B M T R" generates in the binlog:
--echo #      --> STMT  "B M C B T R" entries if in M only N-Table is changed, format S.
--echo #      --> STMT  "B M T R" entries, format S.
--echo #      --> ROW   "B N C" entry, format R.
--echo #      --> MIXED "B N C" entry, format R.
--echo #
--let $commands= B tN T R
--source common/rpl/mixing_engines.inc

--let $commands= B nT T R
--source common/rpl/mixing_engines.inc

--let $commands= B NT T R
--source common/rpl/mixing_engines.inc

--let $commands= B NT-trig T R
--source common/rpl/mixing_engines.inc

--let $commands= B NT-func T R
--source common/rpl/mixing_engines.inc

--let $commands= B TN T R
--source common/rpl/mixing_engines.inc

--let $commands= B TN-trig T R
--source common/rpl/mixing_engines.inc

--let $commands= B TN-func T R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #13.e) Generates in the binlog what follows if a N-Table is changed:
--echo #        --> STMT  "B M C B T R" entries if in M only N-Table is changed, format S.
--echo #        --> STMT  "B M T R" entries, format S.
--echo #        --> ROW   "B N C" entry, format R.
--echo #        --> MIXED "B N C" entry, format R.
--echo #
--let $commands= B tNe T R
--source common/rpl/mixing_engines.inc

--let $commands= B nTe T R
--source common/rpl/mixing_engines.inc

--let $commands= B NeT-trig T R
--source common/rpl/mixing_engines.inc

--let $commands= B NeT-func T R
--source common/rpl/mixing_engines.inc

--let $commands= B TeN-trig T R
--source common/rpl/mixing_engines.inc

--let $commands= B TeN-func T R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #14) Generates in the binlog what follows if a N-Table is changed:
--echo #      --> STMT  "B T M C" entries, format S.
--echo #      --> ROW   "B N C B T T C" entries, format R.
--echo #      --> MIXED "B N C B T T C" entries, format R in N/T and format S in T.
--echo #
--let $commands= B T tN C
--source common/rpl/mixing_engines.inc

--let $commands= B T nT C
--source common/rpl/mixing_engines.inc

--let $commands= B T NT C
--source common/rpl/mixing_engines.inc

--let $commands= B T NT-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T NT-func C
--source common/rpl/mixing_engines.inc

--let $commands= B T TN C
--source common/rpl/mixing_engines.inc

--let $commands= B T TN-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T TN-func C
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #14.e) Generates in the binlog what follows if a N-Table is changed:
--echo #        --> STMT  "B T M C" entries, format S.
--echo #        --> ROW   "B N C B T C" entry, format R.
--echo #        --> MIXED "B N C B T C" entry, format R.
--echo #
--let $commands= B T tNe C
--source common/rpl/mixing_engines.inc

--let $commands= B T nTe C
--source common/rpl/mixing_engines.inc

--let $commands= B T NeT-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T NeT-func C
--source common/rpl/mixing_engines.inc

--let $commands= B T TeN-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B T TeN-func C
--source common/rpl/mixing_engines.inc

--echo
--echo
--echo
--echo
--echo #
--echo #15) Generates in the binlog what follows if a N-Table is changed:
--echo #        --> STMT  "B T M R" entries, format S.
--echo #        --> ROW   "B N C" entry, format R.
--echo #        --> MIXED "B N C" entry, format R.
--echo #
--let $commands= B T tN R
--source common/rpl/mixing_engines.inc

--let $commands= B T nT R
--source common/rpl/mixing_engines.inc

--let $commands= B T NT R
--source common/rpl/mixing_engines.inc

--let $commands= B T NT-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T NT-func R
--source common/rpl/mixing_engines.inc

--let $commands= B T TN R
--source common/rpl/mixing_engines.inc

--let $commands= B T TN-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T TN-func R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #15.e) Generates in the binlog what follows if a N-Table is changed:
--echo #        --> STMT  "B T M R" entries, format S.
--echo #        --> ROW   "B N C" entry, format R.
--echo #        --> MIXED "B N C" entry, format R.
--echo #
--let $commands= B T tNe R
--source common/rpl/mixing_engines.inc

--let $commands= B T nTe R
--source common/rpl/mixing_engines.inc

--let $commands= B T NeT-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T NeT-func R
--source common/rpl/mixing_engines.inc

--let $commands= B T TeN-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B T TeN-func R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #16) Generates in the binlog what follows if a N-Table is changed:
--echo #      --> STMT  "B M C B N C" entries if in M only N-Table is changed, format S.
--echo #      --> STMT  "B M N C" entries, format S.
--echo #      --> ROW   "B N C B N C B T C" entries, format R.
--echo #      --> MIXED "B M C B N C" entries if in M only N-Table is changed, format S.
--echo #      --> MIXED "B N C B N C B T C" entries, format R.
--echo #
--let $commands= B tN N C
--source common/rpl/mixing_engines.inc

--let $commands= B nT N C
--source common/rpl/mixing_engines.inc

--let $commands= B NT N C
--source common/rpl/mixing_engines.inc

--let $commands= B NT-trig N C
--source common/rpl/mixing_engines.inc

--let $commands= B NT-func N C
--source common/rpl/mixing_engines.inc

--let $commands= B TN N C
--source common/rpl/mixing_engines.inc

--let $commands= B TN-trig N C
--source common/rpl/mixing_engines.inc

--let $commands= B TN-func N C
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #16.e) Generates in the binlog what follows if a N-Table is changed:
--echo #      --> STMT  "B M C B N C" entries if in M only N-Table is changed, format S.
--echo #      --> STMT  "B M N C" entries, format S.
--echo #      --> ROW   "B N C B N C B T C" entries, format R.
--echo #      --> MIXED "B M C B N C" entries if in M only N-Table is changed, format S.
--echo #      --> MIXED "B N C B N C B T C" entries, format R.
--echo #
--let $commands= B tNe N C
--source common/rpl/mixing_engines.inc

--let $commands= B nTe N C
--source common/rpl/mixing_engines.inc

--let $commands= B NeT-trig N C
--source common/rpl/mixing_engines.inc

--let $commands= B NeT-func N C
--source common/rpl/mixing_engines.inc

--let $commands= B TeN-trig N C
--source common/rpl/mixing_engines.inc

--let $commands= B TeN-func N C
--source common/rpl/mixing_engines.inc

--echo
--echo
--echo
--echo
--echo #
--echo #17) Generates in the binlog what follows if a N-Table is changed:
--echo #    --> STMT  "B M C B N C" entries if in M only N-Table is changed, format S.
--echo #    --> STMT  "B M N R" entries, format S.
--echo #    --> ROW   "B N C B N C" entries, format R.
--echo #    --> MIXED "B M C B N C" entries if in M only N-Table is changed, format S.
--echo #    --> MIXED "B N C B N C" entries, format R.
--echo #
--let $commands= B tN N R
--source common/rpl/mixing_engines.inc

--let $commands= B nT N R
--source common/rpl/mixing_engines.inc

--let $commands= B NT N R
--source common/rpl/mixing_engines.inc

--let $commands= B NT-trig N R
--source common/rpl/mixing_engines.inc

--let $commands= B NT-func N R
--source common/rpl/mixing_engines.inc

--let $commands= B TN N R
--source common/rpl/mixing_engines.inc

--let $commands= B TN-trig N R
--source common/rpl/mixing_engines.inc

--let $commands= B TN-func N R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #17.e) Generates in the binlog what follows if a N-Table is changed:
--echo #      --> STMT  "B M C B N C" entries if in M only N-Table is changed, format S.
--echo #      --> STMT  "B M N R" entries, format S.
--echo #      --> ROW   "B N C B N C" entries, format R.
--echo #      --> MIXED "B M C B N C" entries if in M only N-Table is changed, format S.
--echo #      --> MIXED "B N C B N C" entries, format R.
--echo #
--let $commands= B tNe N R
--source common/rpl/mixing_engines.inc

--let $commands= B nTe N R
--source common/rpl/mixing_engines.inc

--let $commands= B NeT-trig N R
--source common/rpl/mixing_engines.inc

--let $commands= B NeT-func N R
--source common/rpl/mixing_engines.inc

--let $commands= B TeN-trig N R
--source common/rpl/mixing_engines.inc

--let $commands= B TeN-func N R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #18) Generates in the binlog what follows if a N-Table is changed:
--echo #    --> STMT  "B N C B M C" entries if in M only N-Table is changed, format S.
--echo #    --> STMT  "B N C B M C" entries, format S.
--echo #    --> ROW   "B N C B N C B T C" entries, format R.
--echo #    --> MIXED "B N C B N C B T C" entries, format S in first N and format R in the other.
--echo #

--let $commands= B N tN C
--source common/rpl/mixing_engines.inc

--let $commands= B N nT C
--source common/rpl/mixing_engines.inc

--let $commands= B N NT C
--source common/rpl/mixing_engines.inc

--let $commands= B N NT-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N NT-func C
--source common/rpl/mixing_engines.inc

--let $commands= B N TN C
--source common/rpl/mixing_engines.inc

--let $commands= B N TN-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N TN-func C
--source common/rpl/mixing_engines.inc



--echo
--echo
--echo
--echo
--echo #
--echo #18.e) Generates in the binlog what follows if a N-Table is changed:
--echo #      --> STMT  "B N C B M C" entries if in M only N-Table is changed, format S.
--echo #      --> STMT  "B N C B M C" entries, format S.
--echo #      --> ROW   "B N C B N C" entries, format R.
--echo #      --> MIXED "B N C B N C" entries, format S in first N and format R in the other.
--echo #
--let $commands= B N tNe C
--source common/rpl/mixing_engines.inc

--let $commands= B N nTe C
--source common/rpl/mixing_engines.inc

--let $commands= B N NeT-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N NeT-func C
--source common/rpl/mixing_engines.inc

--let $commands= B N TeN-trig C
--source common/rpl/mixing_engines.inc

--let $commands= B N TeN-func C
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #19) Generates in the binlog what follows if a N-Table is changed:
--echo #    --> STMT  "B N C B M C" entries if in M only N-Table is changed, format S.
--echo #    --> STMT  "B N C B M R" entries, format S.
--echo #    --> ROW   "B N C B N C" entries, format R.
--echo #    --> MIXED "B N C B N C" entries, format S in first N and format R in the other.
--echo #

--let $commands= B N tN R
--source common/rpl/mixing_engines.inc

--let $commands= B N nT R
--source common/rpl/mixing_engines.inc

--let $commands= B N NT R
--source common/rpl/mixing_engines.inc

--let $commands= B N NT-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N NT-func R
--source common/rpl/mixing_engines.inc

--let $commands= B N TN R
--source common/rpl/mixing_engines.inc

--let $commands= B N TN-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N TN-func R
--source common/rpl/mixing_engines.inc


--echo
--echo
--echo
--echo
--echo #
--echo #19.e) Generates in the binlog what follows if a N-Table is changed:
--echo #      --> STMT  "B N C B M C" entries if in M only N-Table is changed, format S.
--echo #      --> STMT  "B N C B M R" entries, format S.
--echo #      --> ROW   "B N C B N C" entries, format R.
--echo #      --> MIXED "B N C B N C" entries, format S in first N and format R in the other.
--echo #
--let $commands= B N tNe R
--source common/rpl/mixing_engines.inc

--let $commands= B N nTe R
--source common/rpl/mixing_engines.inc

--let $commands= B N NeT-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N NeT-func R
--source common/rpl/mixing_engines.inc

--let $commands= B N TeN-trig R
--source common/rpl/mixing_engines.inc

--let $commands= B N TeN-func R
--source common/rpl/mixing_engines.inc

--echo ###################################################################################
--echo #                               2 - SAVEPOINT
--echo ###################################################################################
--let $commands= B T Sn T Rn C
--source common/rpl/mixing_engines.inc

--let $commands= B N T Sn T Rn C
--source common/rpl/mixing_engines.inc

--let $commands= B T N Sn T Rn C
--source common/rpl/mixing_engines.inc

--let $commands= B T Sn N T Rn C
--source common/rpl/mixing_engines.inc

--echo ###################################################################################
--echo #                            3 - CREATE TABLE...SELECT
--echo ###################################################################################
--let $commands= CSe-T->T CS-T->T drop-CS
--source common/rpl/mixing_engines.inc

--let $commands= CSe-N->N CS-N->N drop-CS
--source common/rpl/mixing_engines.inc

--let $commands= CSe-T->N CS-T->N drop-CS
--source common/rpl/mixing_engines.inc

--let $commands= CSe-N->T CS-N->T drop-CS
--source common/rpl/mixing_engines.inc

--let $commands= CSe-N->T CS-N->T drop-CS
--source common/rpl/mixing_engines.inc

--let $commands= CSe-N->T CS-N->T drop-CS
--source common/rpl/mixing_engines.inc


--echo ###################################################################################
--echo #                            4 - INSERT TABLE...SELECT
--echo ###################################################################################

--let $commands= CS-T->T
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-T B T IS-T<-N T C
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-T B T ISe-T<-N T C
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-T B IS-T<-N T C
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-T B ISe-T<-N T C
--source common/rpl/mixing_engines.inc

--let $commands= drop-CS
--source common/rpl/mixing_engines.inc

--let $commands= CS-T->T
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-T B T IS-T<-T T C
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-T B T ISe-T<-T T C
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-T B IS-T<-T T C
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-T B ISe-T<-T T C
--source common/rpl/mixing_engines.inc

--let $commands= drop-CS
--source common/rpl/mixing_engines.inc

--let $commands= CS-N->N
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-N B T IS-N<-T T C
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-N B T ISe-N<-T T C
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-N B IS-N<-T T C
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-N B ISe-N<-T T C
--source common/rpl/mixing_engines.inc

--let $commands= drop-CS
--source common/rpl/mixing_engines.inc

--let $commands= CS-N->N
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-N B T IS-N<-N T C
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-N B T ISe-N<-N T C
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-N B IS-N<-N T C
--source common/rpl/mixing_engines.inc

--let $commands= trunc-CS-N B ISe-N<-N T C
--source common/rpl/mixing_engines.inc

--let $commands= drop-CS
--source common/rpl/mixing_engines.inc


--echo ###################################################################################
--echo #                         5 - ROLLBACK TEMPORARY TABLE
--echo ###################################################################################
--let $commands= B T CT R
--source common/rpl/mixing_engines.inc

--let $commands= drop-CT
--source common/rpl/mixing_engines.inc

--let $commands= B T Sn T CT Rn R
--source common/rpl/mixing_engines.inc

--let $commands= drop-CT
--source common/rpl/mixing_engines.inc

--let $commands= B T CT T R
--source common/rpl/mixing_engines.inc

--let $commands= drop-CT
--source common/rpl/mixing_engines.inc

--let $commands= B tN CT T R
--source common/rpl/mixing_engines.inc

--let $commands= drop-CT
--source common/rpl/mixing_engines.inc

--let $commands= B CT T R
--source common/rpl/mixing_engines.inc

--let $commands= drop-CT
--source common/rpl/mixing_engines.inc

--let $commands= B N CT T R
--source common/rpl/mixing_engines.inc

--let $commands= drop-CT
--source common/rpl/mixing_engines.inc


--echo ###################################################################################
--echo #                            5 - SET WITH ROW CHANGES
--echo ###################################################################################
--let $commands= set-T
--source common/rpl/mixing_engines.inc

--let $commands= set-N
--source common/rpl/mixing_engines.inc

--let $commands= set-NT
--source common/rpl/mixing_engines.inc

--let $commands= B set-N set-T C
--source common/rpl/mixing_engines.inc

--let $commands= B set-T set-N C
--source common/rpl/mixing_engines.inc

--let $commands= B set-N set-T R
--source common/rpl/mixing_engines.inc

--let $commands= B set-T set-N R
--source common/rpl/mixing_engines.inc

--let $commands= B set-NT set-T C
--source common/rpl/mixing_engines.inc

--let $commands= B set-T set-NT C
--source common/rpl/mixing_engines.inc

--let $commands= B set-NT set-T R
--source common/rpl/mixing_engines.inc

--let $commands= B set-T set-NT R
--source common/rpl/mixing_engines.inc


--echo ###################################################################################
--echo #                               CHECK CONSISTENCY 
--echo ###################################################################################
connection master;
sync_slave_with_master;
  
--exec $MYSQL_DUMP --compact --order-by-primary --skip-extended-insert --no-create-info test > $MYSQLTEST_VARDIR/tmp/test-nmt-master.sql
--exec $MYSQL_DUMP_SLAVE --compact --order-by-primary --skip-extended-insert --no-create-info test > $MYSQLTEST_VARDIR/tmp/test-nmt-slave.sql
if (`select @@session.binlog_direct_non_transactional_updates = 0 || @@session.binlog_format != 'STATEMENT'`)
{
  --diff_files $MYSQLTEST_VARDIR/tmp/test-nmt-master.sql $MYSQLTEST_VARDIR/tmp/test-nmt-slave.sql
}
  
--echo ###################################################################################
--echo #                                        CLEAN
--echo ###################################################################################
--remove_file $MYSQLTEST_VARDIR/tmp/test-nmt-master.sql
--remove_file $MYSQLTEST_VARDIR/tmp/test-nmt-slave.sql
--let $commands= clean
--source common/rpl/mixing_engines.inc
