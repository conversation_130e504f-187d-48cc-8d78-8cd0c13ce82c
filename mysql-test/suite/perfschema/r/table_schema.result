select * from information_schema.columns where table_schema="performance_schema"
   order by table_name COLLATE utf8mb3_general_ci, ordinal_position;
TABLE_CATALOG	TABLE_SCHEMA	TABLE_NAME	COLUMN_NAME	ORDINAL_POSITION	COLUMN_DEFAULT	IS_NULLABLE	DATA_TYPE	CHARACTER_MAXIMUM_LENGTH	CHARACTER_OCTET_LENGTH	NUMERIC_PRECISION	NUMERIC_SCALE	DATETIME_PRECISION	CHARACTER_SET_NAME	COLLATION_NAME	COLUMN_TYPE	COLUMN_KEY	EXTRA	PRIVILEGES	COLUMN_COMMENT	GENERATION_EXPRESSION	SRS_ID
def	performance_schema	accounts	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	accounts	HOST	2	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)			select,insert,update,references			NULL
def	performance_schema	accounts	CURRENT_CONNECTIONS	3	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	accounts	TOTAL_CONNECTIONS	4	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	accounts	MAX_SESSION_CONTROLLED_MEMORY	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	accounts	MAX_SESSION_TOTAL_MEMORY	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	binary_log_transaction_compression_stats	LOG_TYPE	1	NULL	NO	enum	6	24	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('BINARY','RELAY')			select,insert,update,references	The log type to which the transactions were written.		NULL
def	performance_schema	binary_log_transaction_compression_stats	COMPRESSION_TYPE	2	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references	The transaction compression algorithm used.		NULL
def	performance_schema	binary_log_transaction_compression_stats	TRANSACTION_COUNTER	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references	Number of transactions written to the log		NULL
def	performance_schema	binary_log_transaction_compression_stats	COMPRESSED_BYTES_COUNTER	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references	The total number of bytes compressed.		NULL
def	performance_schema	binary_log_transaction_compression_stats	UNCOMPRESSED_BYTES_COUNTER	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references	The total number of bytes uncompressed.		NULL
def	performance_schema	binary_log_transaction_compression_stats	COMPRESSION_PERCENTAGE	6	NULL	NO	smallint	NULL	NULL	5	0	NULL	NULL	NULL	smallint			select,insert,update,references	The compression ratio as a percentage.		NULL
def	performance_schema	binary_log_transaction_compression_stats	FIRST_TRANSACTION_ID	7	NULL	YES	text	65535	65535	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	text			select,insert,update,references	The first transaction written.		NULL
def	performance_schema	binary_log_transaction_compression_stats	FIRST_TRANSACTION_COMPRESSED_BYTES	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references	First transaction written compressed bytes.		NULL
def	performance_schema	binary_log_transaction_compression_stats	FIRST_TRANSACTION_UNCOMPRESSED_BYTES	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references	First transaction written uncompressed bytes.		NULL
def	performance_schema	binary_log_transaction_compression_stats	FIRST_TRANSACTION_TIMESTAMP	10	NULL	YES	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references	When the first transaction was written.		NULL
def	performance_schema	binary_log_transaction_compression_stats	LAST_TRANSACTION_ID	11	NULL	YES	text	65535	65535	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	text			select,insert,update,references	The last transaction written.		NULL
def	performance_schema	binary_log_transaction_compression_stats	LAST_TRANSACTION_COMPRESSED_BYTES	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references	Last transaction written compressed bytes.		NULL
def	performance_schema	binary_log_transaction_compression_stats	LAST_TRANSACTION_UNCOMPRESSED_BYTES	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references	Last transaction written uncompressed bytes.		NULL
def	performance_schema	binary_log_transaction_compression_stats	LAST_TRANSACTION_TIMESTAMP	14	NULL	YES	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references	When the last transaction was written.		NULL
def	performance_schema	cond_instances	NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	MUL		select,insert,update,references			NULL
def	performance_schema	cond_instances	OBJECT_INSTANCE_BEGIN	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	data_locks	ENGINE	1	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)	PRI		select,insert,update,references			NULL
def	performance_schema	data_locks	ENGINE_LOCK_ID	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	data_locks	ENGINE_TRANSACTION_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	data_locks	THREAD_ID	4	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	data_locks	EVENT_ID	5	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	data_locks	OBJECT_SCHEMA	6	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	MUL		select,insert,update,references			NULL
def	performance_schema	data_locks	OBJECT_NAME	7	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	data_locks	PARTITION_NAME	8	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	data_locks	SUBPARTITION_NAME	9	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	data_locks	INDEX_NAME	10	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	data_locks	OBJECT_INSTANCE_BEGIN	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	data_locks	LOCK_TYPE	12	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)			select,insert,update,references			NULL
def	performance_schema	data_locks	LOCK_MODE	13	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)			select,insert,update,references			NULL
def	performance_schema	data_locks	LOCK_STATUS	14	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)			select,insert,update,references			NULL
def	performance_schema	data_locks	LOCK_DATA	15	NULL	YES	varchar	8192	32768	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(8192)			select,insert,update,references			NULL
def	performance_schema	data_lock_waits	ENGINE	1	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)	PRI		select,insert,update,references			NULL
def	performance_schema	data_lock_waits	REQUESTING_ENGINE_LOCK_ID	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	data_lock_waits	REQUESTING_ENGINE_TRANSACTION_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	data_lock_waits	REQUESTING_THREAD_ID	4	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	data_lock_waits	REQUESTING_EVENT_ID	5	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	data_lock_waits	REQUESTING_OBJECT_INSTANCE_BEGIN	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	data_lock_waits	BLOCKING_ENGINE_LOCK_ID	7	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	data_lock_waits	BLOCKING_ENGINE_TRANSACTION_ID	8	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	data_lock_waits	BLOCKING_THREAD_ID	9	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	data_lock_waits	BLOCKING_EVENT_ID	10	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	data_lock_waits	BLOCKING_OBJECT_INSTANCE_BEGIN	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	error_log	LOGGED	1	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)	PRI		select,insert,update,references			NULL
def	performance_schema	error_log	THREAD_ID	2	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	error_log	PRIO	3	NULL	NO	enum	7	28	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('System','Error','Warning','Note')	MUL		select,insert,update,references			NULL
def	performance_schema	error_log	ERROR_CODE	4	NULL	YES	varchar	10	40	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(10)	MUL		select,insert,update,references			NULL
def	performance_schema	error_log	SUBSYSTEM	5	NULL	YES	varchar	7	28	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(7)	MUL		select,insert,update,references			NULL
def	performance_schema	error_log	DATA	6	NULL	NO	text	65535	65535	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	text			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_account_by_error	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_account_by_error	HOST	2	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_account_by_error	ERROR_NUMBER	3	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_account_by_error	ERROR_NAME	4	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_account_by_error	SQL_STATE	5	NULL	YES	varchar	5	20	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(5)			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_account_by_error	SUM_ERROR_RAISED	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_account_by_error	SUM_ERROR_HANDLED	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_account_by_error	FIRST_SEEN	8	NULL	YES	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_account_by_error	LAST_SEEN	9	NULL	YES	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_host_by_error	HOST	1	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)	MUL		select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_host_by_error	ERROR_NUMBER	2	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_host_by_error	ERROR_NAME	3	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_host_by_error	SQL_STATE	4	NULL	YES	varchar	5	20	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(5)			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_host_by_error	SUM_ERROR_RAISED	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_host_by_error	SUM_ERROR_HANDLED	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_host_by_error	FIRST_SEEN	7	NULL	YES	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_host_by_error	LAST_SEEN	8	NULL	YES	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_thread_by_error	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_thread_by_error	ERROR_NUMBER	2	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_thread_by_error	ERROR_NAME	3	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_thread_by_error	SQL_STATE	4	NULL	YES	varchar	5	20	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(5)			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_thread_by_error	SUM_ERROR_RAISED	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_thread_by_error	SUM_ERROR_HANDLED	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_thread_by_error	FIRST_SEEN	7	NULL	YES	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_thread_by_error	LAST_SEEN	8	NULL	YES	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_user_by_error	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_user_by_error	ERROR_NUMBER	2	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_user_by_error	ERROR_NAME	3	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_user_by_error	SQL_STATE	4	NULL	YES	varchar	5	20	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(5)			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_user_by_error	SUM_ERROR_RAISED	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_user_by_error	SUM_ERROR_HANDLED	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_user_by_error	FIRST_SEEN	7	NULL	YES	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_by_user_by_error	LAST_SEEN	8	NULL	YES	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_global_by_error	ERROR_NUMBER	1	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int	UNI		select,insert,update,references			NULL
def	performance_schema	events_errors_summary_global_by_error	ERROR_NAME	2	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_global_by_error	SQL_STATE	3	NULL	YES	varchar	5	20	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(5)			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_global_by_error	SUM_ERROR_RAISED	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_global_by_error	SUM_ERROR_HANDLED	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_global_by_error	FIRST_SEEN	6	NULL	YES	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	events_errors_summary_global_by_error	LAST_SEEN	7	NULL	YES	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	events_stages_current	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_stages_current	EVENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_stages_current	END_EVENT_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_current	EVENT_NAME	4	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_stages_current	SOURCE	5	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_stages_current	TIMER_START	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_current	TIMER_END	7	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_current	TIMER_WAIT	8	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_current	WORK_COMPLETED	9	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_current	WORK_ESTIMATED	10	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_current	NESTING_EVENT_ID	11	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_current	NESTING_EVENT_TYPE	12	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('TRANSACTION','STATEMENT','STAGE','WAIT')			select,insert,update,references			NULL
def	performance_schema	events_stages_history	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_stages_history	EVENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_stages_history	END_EVENT_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history	EVENT_NAME	4	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_stages_history	SOURCE	5	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_stages_history	TIMER_START	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history	TIMER_END	7	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history	TIMER_WAIT	8	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history	WORK_COMPLETED	9	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history	WORK_ESTIMATED	10	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history	NESTING_EVENT_ID	11	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history	NESTING_EVENT_TYPE	12	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('TRANSACTION','STATEMENT','STAGE','WAIT')			select,insert,update,references			NULL
def	performance_schema	events_stages_history_long	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history_long	EVENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history_long	END_EVENT_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history_long	EVENT_NAME	4	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_stages_history_long	SOURCE	5	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_stages_history_long	TIMER_START	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history_long	TIMER_END	7	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history_long	TIMER_WAIT	8	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history_long	WORK_COMPLETED	9	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history_long	WORK_ESTIMATED	10	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history_long	NESTING_EVENT_ID	11	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_history_long	NESTING_EVENT_TYPE	12	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('TRANSACTION','STATEMENT','STAGE','WAIT')			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_account_by_event_name	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_account_by_event_name	HOST	2	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_account_by_event_name	EVENT_NAME	3	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_account_by_event_name	COUNT_STAR	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_account_by_event_name	SUM_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_account_by_event_name	MIN_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_account_by_event_name	AVG_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_account_by_event_name	MAX_TIMER_WAIT	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_host_by_event_name	HOST	1	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)	MUL		select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_host_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_host_by_event_name	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_host_by_event_name	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_host_by_event_name	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_host_by_event_name	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_host_by_event_name	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_thread_by_event_name	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_thread_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_thread_by_event_name	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_thread_by_event_name	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_thread_by_event_name	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_thread_by_event_name	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_thread_by_event_name	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_user_by_event_name	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_user_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_user_by_event_name	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_user_by_event_name	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_user_by_event_name	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_user_by_event_name	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_by_user_by_event_name	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_global_by_event_name	EVENT_NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	events_stages_summary_global_by_event_name	COUNT_STAR	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_global_by_event_name	SUM_TIMER_WAIT	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_global_by_event_name	MIN_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_global_by_event_name	AVG_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_stages_summary_global_by_event_name	MAX_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_statements_current	EVENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_statements_current	END_EVENT_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	EVENT_NAME	4	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_statements_current	SOURCE	5	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_current	TIMER_START	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	TIMER_END	7	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	TIMER_WAIT	8	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	LOCK_TIME	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	SQL_TEXT	10	NULL	YES	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	events_statements_current	DIGEST	11	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_current	DIGEST_TEXT	12	NULL	YES	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	events_statements_current	CURRENT_SCHEMA	13	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_current	OBJECT_TYPE	14	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_current	OBJECT_SCHEMA	15	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_current	OBJECT_NAME	16	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_current	OBJECT_INSTANCE_BEGIN	17	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	MYSQL_ERRNO	18	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_statements_current	RETURNED_SQLSTATE	19	NULL	YES	varchar	5	20	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(5)			select,insert,update,references			NULL
def	performance_schema	events_statements_current	MESSAGE_TEXT	20	NULL	YES	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_statements_current	ERRORS	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	WARNINGS	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	ROWS_AFFECTED	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	ROWS_SENT	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	ROWS_EXAMINED	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	CREATED_TMP_DISK_TABLES	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	CREATED_TMP_TABLES	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	SELECT_FULL_JOIN	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	SELECT_FULL_RANGE_JOIN	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	SELECT_RANGE	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	SELECT_RANGE_CHECK	31	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	SELECT_SCAN	32	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	SORT_MERGE_PASSES	33	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	SORT_RANGE	34	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	SORT_ROWS	35	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	SORT_SCAN	36	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	NO_INDEX_USED	37	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	NO_GOOD_INDEX_USED	38	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	NESTING_EVENT_ID	39	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	NESTING_EVENT_TYPE	40	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('TRANSACTION','STATEMENT','STAGE','WAIT')			select,insert,update,references			NULL
def	performance_schema	events_statements_current	NESTING_EVENT_LEVEL	41	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_statements_current	STATEMENT_ID	42	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	CPU_TIME	43	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	MAX_CONTROLLED_MEMORY	44	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	MAX_TOTAL_MEMORY	45	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_current	EXECUTION_ENGINE	46	NULL	YES	enum	9	36	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('PRIMARY','SECONDARY')			select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_by_digest	SCHEMA_NAME	1	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	MUL		select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_by_digest	DIGEST	2	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_by_digest	BUCKET_NUMBER	3	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_by_digest	BUCKET_TIMER_LOW	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_by_digest	BUCKET_TIMER_HIGH	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_by_digest	COUNT_BUCKET	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_by_digest	COUNT_BUCKET_AND_LOWER	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_by_digest	BUCKET_QUANTILE	8	NULL	NO	double	NULL	NULL	7	6	NULL	NULL	NULL	double(7,6)			select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_global	BUCKET_NUMBER	1	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_global	BUCKET_TIMER_LOW	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_global	BUCKET_TIMER_HIGH	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_global	COUNT_BUCKET	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_global	COUNT_BUCKET_AND_LOWER	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_histogram_global	BUCKET_QUANTILE	6	NULL	NO	double	NULL	NULL	7	6	NULL	NULL	NULL	double(7,6)			select,insert,update,references			NULL
def	performance_schema	events_statements_history	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_statements_history	EVENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_statements_history	END_EVENT_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	EVENT_NAME	4	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_statements_history	SOURCE	5	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_history	TIMER_START	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	TIMER_END	7	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	TIMER_WAIT	8	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	LOCK_TIME	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	SQL_TEXT	10	NULL	YES	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	events_statements_history	DIGEST	11	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_history	DIGEST_TEXT	12	NULL	YES	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	events_statements_history	CURRENT_SCHEMA	13	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_history	OBJECT_TYPE	14	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_history	OBJECT_SCHEMA	15	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_history	OBJECT_NAME	16	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_history	OBJECT_INSTANCE_BEGIN	17	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	MYSQL_ERRNO	18	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_statements_history	RETURNED_SQLSTATE	19	NULL	YES	varchar	5	20	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(5)			select,insert,update,references			NULL
def	performance_schema	events_statements_history	MESSAGE_TEXT	20	NULL	YES	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_statements_history	ERRORS	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	WARNINGS	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	ROWS_AFFECTED	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	ROWS_SENT	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	ROWS_EXAMINED	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	CREATED_TMP_DISK_TABLES	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	CREATED_TMP_TABLES	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	SELECT_FULL_JOIN	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	SELECT_FULL_RANGE_JOIN	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	SELECT_RANGE	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	SELECT_RANGE_CHECK	31	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	SELECT_SCAN	32	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	SORT_MERGE_PASSES	33	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	SORT_RANGE	34	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	SORT_ROWS	35	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	SORT_SCAN	36	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	NO_INDEX_USED	37	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	NO_GOOD_INDEX_USED	38	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	NESTING_EVENT_ID	39	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	NESTING_EVENT_TYPE	40	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('TRANSACTION','STATEMENT','STAGE','WAIT')			select,insert,update,references			NULL
def	performance_schema	events_statements_history	NESTING_EVENT_LEVEL	41	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_statements_history	STATEMENT_ID	42	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	CPU_TIME	43	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	MAX_CONTROLLED_MEMORY	44	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	MAX_TOTAL_MEMORY	45	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history	EXECUTION_ENGINE	46	NULL	YES	enum	9	36	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('PRIMARY','SECONDARY')			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	EVENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	END_EVENT_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	EVENT_NAME	4	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	SOURCE	5	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	TIMER_START	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	TIMER_END	7	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	TIMER_WAIT	8	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	LOCK_TIME	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	SQL_TEXT	10	NULL	YES	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	DIGEST	11	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	DIGEST_TEXT	12	NULL	YES	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	CURRENT_SCHEMA	13	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	OBJECT_TYPE	14	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	OBJECT_SCHEMA	15	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	OBJECT_NAME	16	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	OBJECT_INSTANCE_BEGIN	17	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	MYSQL_ERRNO	18	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	RETURNED_SQLSTATE	19	NULL	YES	varchar	5	20	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(5)			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	MESSAGE_TEXT	20	NULL	YES	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	ERRORS	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	WARNINGS	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	ROWS_AFFECTED	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	ROWS_SENT	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	ROWS_EXAMINED	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	CREATED_TMP_DISK_TABLES	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	CREATED_TMP_TABLES	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	SELECT_FULL_JOIN	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	SELECT_FULL_RANGE_JOIN	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	SELECT_RANGE	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	SELECT_RANGE_CHECK	31	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	SELECT_SCAN	32	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	SORT_MERGE_PASSES	33	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	SORT_RANGE	34	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	SORT_ROWS	35	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	SORT_SCAN	36	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	NO_INDEX_USED	37	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	NO_GOOD_INDEX_USED	38	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	NESTING_EVENT_ID	39	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	NESTING_EVENT_TYPE	40	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('TRANSACTION','STATEMENT','STAGE','WAIT')			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	NESTING_EVENT_LEVEL	41	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	STATEMENT_ID	42	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	CPU_TIME	43	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	MAX_CONTROLLED_MEMORY	44	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	MAX_TOTAL_MEMORY	45	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_history_long	EXECUTION_ENGINE	46	NULL	YES	enum	9	36	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('PRIMARY','SECONDARY')			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	HOST	2	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	EVENT_NAME	3	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	COUNT_STAR	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	MIN_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	AVG_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	MAX_TIMER_WAIT	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_LOCK_TIME	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_ERRORS	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_WARNINGS	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_ROWS_AFFECTED	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_ROWS_SENT	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_ROWS_EXAMINED	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_CREATED_TMP_DISK_TABLES	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_CREATED_TMP_TABLES	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_SELECT_FULL_JOIN	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_SELECT_FULL_RANGE_JOIN	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_SELECT_RANGE	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_SELECT_RANGE_CHECK	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_SELECT_SCAN	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_SORT_MERGE_PASSES	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_SORT_RANGE	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_SORT_ROWS	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_SORT_SCAN	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_NO_INDEX_USED	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_NO_GOOD_INDEX_USED	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	SUM_CPU_TIME	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	MAX_CONTROLLED_MEMORY	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	MAX_TOTAL_MEMORY	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_account_by_event_name	COUNT_SECONDARY	31	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SCHEMA_NAME	1	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	MUL		select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	DIGEST	2	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	DIGEST_TEXT	3	NULL	YES	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	COUNT_STAR	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	MIN_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	AVG_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	MAX_TIMER_WAIT	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_LOCK_TIME	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_ERRORS	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_WARNINGS	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_ROWS_AFFECTED	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_ROWS_SENT	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_ROWS_EXAMINED	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_CREATED_TMP_DISK_TABLES	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_CREATED_TMP_TABLES	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_SELECT_FULL_JOIN	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_SELECT_FULL_RANGE_JOIN	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_SELECT_RANGE	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_SELECT_RANGE_CHECK	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_SELECT_SCAN	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_SORT_MERGE_PASSES	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_SORT_RANGE	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_SORT_ROWS	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_SORT_SCAN	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_NO_INDEX_USED	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_NO_GOOD_INDEX_USED	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	SUM_CPU_TIME	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	MAX_CONTROLLED_MEMORY	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	MAX_TOTAL_MEMORY	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	COUNT_SECONDARY	31	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	FIRST_SEEN	32	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	LAST_SEEN	33	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	QUANTILE_95	34	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	QUANTILE_99	35	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	QUANTILE_999	36	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	QUERY_SAMPLE_TEXT	37	NULL	YES	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	QUERY_SAMPLE_SEEN	38	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_digest	QUERY_SAMPLE_TIMER_WAIT	39	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	HOST	1	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)	MUL		select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_LOCK_TIME	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_ERRORS	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_WARNINGS	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_ROWS_AFFECTED	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_ROWS_SENT	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_ROWS_EXAMINED	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_CREATED_TMP_DISK_TABLES	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_CREATED_TMP_TABLES	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_SELECT_FULL_JOIN	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_SELECT_FULL_RANGE_JOIN	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_SELECT_RANGE	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_SELECT_RANGE_CHECK	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_SELECT_SCAN	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_SORT_MERGE_PASSES	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_SORT_RANGE	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_SORT_ROWS	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_SORT_SCAN	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_NO_INDEX_USED	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_NO_GOOD_INDEX_USED	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	SUM_CPU_TIME	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	MAX_CONTROLLED_MEMORY	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	MAX_TOTAL_MEMORY	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_host_by_event_name	COUNT_SECONDARY	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	OBJECT_TYPE	1	NULL	NO	enum	9	36	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('EVENT','FUNCTION','PROCEDURE','TABLE','TRIGGER')	PRI		select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	OBJECT_SCHEMA	2	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	OBJECT_NAME	3	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	COUNT_STAR	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	MIN_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	AVG_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	MAX_TIMER_WAIT	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	COUNT_STATEMENTS	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_STATEMENTS_WAIT	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	MIN_STATEMENTS_WAIT	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	AVG_STATEMENTS_WAIT	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	MAX_STATEMENTS_WAIT	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_LOCK_TIME	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_ERRORS	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_WARNINGS	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_ROWS_AFFECTED	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_ROWS_SENT	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_ROWS_EXAMINED	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_CREATED_TMP_DISK_TABLES	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_CREATED_TMP_TABLES	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_SELECT_FULL_JOIN	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_SELECT_FULL_RANGE_JOIN	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_SELECT_RANGE	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_SELECT_RANGE_CHECK	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_SELECT_SCAN	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_SORT_MERGE_PASSES	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_SORT_RANGE	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_SORT_ROWS	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_SORT_SCAN	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_NO_INDEX_USED	31	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_NO_GOOD_INDEX_USED	32	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	SUM_CPU_TIME	33	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	MAX_CONTROLLED_MEMORY	34	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	MAX_TOTAL_MEMORY	35	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_program	COUNT_SECONDARY	36	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_LOCK_TIME	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_ERRORS	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_WARNINGS	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_ROWS_AFFECTED	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_ROWS_SENT	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_ROWS_EXAMINED	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_CREATED_TMP_DISK_TABLES	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_CREATED_TMP_TABLES	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_SELECT_FULL_JOIN	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_SELECT_FULL_RANGE_JOIN	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_SELECT_RANGE	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_SELECT_RANGE_CHECK	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_SELECT_SCAN	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_SORT_MERGE_PASSES	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_SORT_RANGE	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_SORT_ROWS	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_SORT_SCAN	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_NO_INDEX_USED	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_NO_GOOD_INDEX_USED	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	SUM_CPU_TIME	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	MAX_CONTROLLED_MEMORY	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	MAX_TOTAL_MEMORY	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_thread_by_event_name	COUNT_SECONDARY	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_LOCK_TIME	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_ERRORS	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_WARNINGS	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_ROWS_AFFECTED	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_ROWS_SENT	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_ROWS_EXAMINED	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_CREATED_TMP_DISK_TABLES	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_CREATED_TMP_TABLES	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_SELECT_FULL_JOIN	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_SELECT_FULL_RANGE_JOIN	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_SELECT_RANGE	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_SELECT_RANGE_CHECK	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_SELECT_SCAN	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_SORT_MERGE_PASSES	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_SORT_RANGE	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_SORT_ROWS	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_SORT_SCAN	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_NO_INDEX_USED	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_NO_GOOD_INDEX_USED	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	SUM_CPU_TIME	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	MAX_CONTROLLED_MEMORY	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	MAX_TOTAL_MEMORY	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_by_user_by_event_name	COUNT_SECONDARY	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	EVENT_NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	COUNT_STAR	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_TIMER_WAIT	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	MIN_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	AVG_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	MAX_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_LOCK_TIME	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_ERRORS	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_WARNINGS	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_ROWS_AFFECTED	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_ROWS_SENT	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_ROWS_EXAMINED	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_CREATED_TMP_DISK_TABLES	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_CREATED_TMP_TABLES	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_SELECT_FULL_JOIN	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_SELECT_FULL_RANGE_JOIN	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_SELECT_RANGE	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_SELECT_RANGE_CHECK	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_SELECT_SCAN	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_SORT_MERGE_PASSES	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_SORT_RANGE	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_SORT_ROWS	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_SORT_SCAN	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_NO_INDEX_USED	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_NO_GOOD_INDEX_USED	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	SUM_CPU_TIME	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	MAX_CONTROLLED_MEMORY	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	MAX_TOTAL_MEMORY	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_statements_summary_global_by_event_name	COUNT_SECONDARY	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_transactions_current	EVENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_transactions_current	END_EVENT_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	EVENT_NAME	4	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	STATE	5	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('ACTIVE','COMMITTED','ROLLED BACK')			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	TRX_ID	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	GTID	7	NULL	YES	varchar	90	360	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(90)			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	XID_FORMAT_ID	8	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	XID_GTRID	9	NULL	YES	varchar	130	520	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(130)			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	XID_BQUAL	10	NULL	YES	varchar	130	520	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(130)			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	XA_STATE	11	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	SOURCE	12	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	TIMER_START	13	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	TIMER_END	14	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	TIMER_WAIT	15	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	ACCESS_MODE	16	NULL	YES	enum	10	40	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('READ ONLY','READ WRITE')			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	ISOLATION_LEVEL	17	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	AUTOCOMMIT	18	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	NUMBER_OF_SAVEPOINTS	19	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	NUMBER_OF_ROLLBACK_TO_SAVEPOINT	20	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	NUMBER_OF_RELEASE_SAVEPOINT	21	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	OBJECT_INSTANCE_BEGIN	22	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	NESTING_EVENT_ID	23	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_current	NESTING_EVENT_TYPE	24	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('TRANSACTION','STATEMENT','STAGE','WAIT')			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_transactions_history	EVENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_transactions_history	END_EVENT_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	EVENT_NAME	4	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	STATE	5	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('ACTIVE','COMMITTED','ROLLED BACK')			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	TRX_ID	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	GTID	7	NULL	YES	varchar	90	360	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(90)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	XID_FORMAT_ID	8	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	XID_GTRID	9	NULL	YES	varchar	130	520	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(130)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	XID_BQUAL	10	NULL	YES	varchar	130	520	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(130)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	XA_STATE	11	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	SOURCE	12	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	TIMER_START	13	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	TIMER_END	14	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	TIMER_WAIT	15	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	ACCESS_MODE	16	NULL	YES	enum	10	40	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('READ ONLY','READ WRITE')			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	ISOLATION_LEVEL	17	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	AUTOCOMMIT	18	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	NUMBER_OF_SAVEPOINTS	19	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	NUMBER_OF_ROLLBACK_TO_SAVEPOINT	20	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	NUMBER_OF_RELEASE_SAVEPOINT	21	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	OBJECT_INSTANCE_BEGIN	22	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	NESTING_EVENT_ID	23	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history	NESTING_EVENT_TYPE	24	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('TRANSACTION','STATEMENT','STAGE','WAIT')			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	EVENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	END_EVENT_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	EVENT_NAME	4	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	STATE	5	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('ACTIVE','COMMITTED','ROLLED BACK')			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	TRX_ID	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	GTID	7	NULL	YES	varchar	90	360	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(90)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	XID_FORMAT_ID	8	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	XID_GTRID	9	NULL	YES	varchar	130	520	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(130)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	XID_BQUAL	10	NULL	YES	varchar	130	520	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(130)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	XA_STATE	11	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	SOURCE	12	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	TIMER_START	13	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	TIMER_END	14	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	TIMER_WAIT	15	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	ACCESS_MODE	16	NULL	YES	enum	10	40	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('READ ONLY','READ WRITE')			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	ISOLATION_LEVEL	17	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	AUTOCOMMIT	18	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	NUMBER_OF_SAVEPOINTS	19	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	NUMBER_OF_ROLLBACK_TO_SAVEPOINT	20	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	NUMBER_OF_RELEASE_SAVEPOINT	21	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	OBJECT_INSTANCE_BEGIN	22	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	NESTING_EVENT_ID	23	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_history_long	NESTING_EVENT_TYPE	24	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('TRANSACTION','STATEMENT','STAGE','WAIT')			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	HOST	2	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	EVENT_NAME	3	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	COUNT_STAR	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	SUM_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	MIN_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	AVG_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	MAX_TIMER_WAIT	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	COUNT_READ_WRITE	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	SUM_TIMER_READ_WRITE	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	MIN_TIMER_READ_WRITE	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	AVG_TIMER_READ_WRITE	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	MAX_TIMER_READ_WRITE	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	COUNT_READ_ONLY	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	SUM_TIMER_READ_ONLY	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	MIN_TIMER_READ_ONLY	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	AVG_TIMER_READ_ONLY	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_account_by_event_name	MAX_TIMER_READ_ONLY	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	HOST	1	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)	MUL		select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	COUNT_READ_WRITE	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	SUM_TIMER_READ_WRITE	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	MIN_TIMER_READ_WRITE	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	AVG_TIMER_READ_WRITE	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	MAX_TIMER_READ_WRITE	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	COUNT_READ_ONLY	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	SUM_TIMER_READ_ONLY	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	MIN_TIMER_READ_ONLY	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	AVG_TIMER_READ_ONLY	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_host_by_event_name	MAX_TIMER_READ_ONLY	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	COUNT_READ_WRITE	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	SUM_TIMER_READ_WRITE	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	MIN_TIMER_READ_WRITE	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	AVG_TIMER_READ_WRITE	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	MAX_TIMER_READ_WRITE	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	COUNT_READ_ONLY	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	SUM_TIMER_READ_ONLY	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	MIN_TIMER_READ_ONLY	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	AVG_TIMER_READ_ONLY	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_thread_by_event_name	MAX_TIMER_READ_ONLY	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	COUNT_READ_WRITE	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	SUM_TIMER_READ_WRITE	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	MIN_TIMER_READ_WRITE	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	AVG_TIMER_READ_WRITE	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	MAX_TIMER_READ_WRITE	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	COUNT_READ_ONLY	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	SUM_TIMER_READ_ONLY	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	MIN_TIMER_READ_ONLY	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	AVG_TIMER_READ_ONLY	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_by_user_by_event_name	MAX_TIMER_READ_ONLY	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	EVENT_NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	COUNT_STAR	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	SUM_TIMER_WAIT	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	MIN_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	AVG_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	MAX_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	COUNT_READ_WRITE	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	SUM_TIMER_READ_WRITE	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	MIN_TIMER_READ_WRITE	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	AVG_TIMER_READ_WRITE	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	MAX_TIMER_READ_WRITE	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	COUNT_READ_ONLY	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	SUM_TIMER_READ_ONLY	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	MIN_TIMER_READ_ONLY	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	AVG_TIMER_READ_ONLY	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_transactions_summary_global_by_event_name	MAX_TIMER_READ_ONLY	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_current	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_waits_current	EVENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_waits_current	END_EVENT_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_current	EVENT_NAME	4	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_waits_current	SOURCE	5	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_waits_current	TIMER_START	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_current	TIMER_END	7	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_current	TIMER_WAIT	8	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_current	SPINS	9	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_current	OBJECT_SCHEMA	10	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_waits_current	OBJECT_NAME	11	NULL	YES	varchar	512	2048	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(512)			select,insert,update,references			NULL
def	performance_schema	events_waits_current	INDEX_NAME	12	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_waits_current	OBJECT_TYPE	13	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_waits_current	OBJECT_INSTANCE_BEGIN	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_current	NESTING_EVENT_ID	15	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_current	NESTING_EVENT_TYPE	16	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('TRANSACTION','STATEMENT','STAGE','WAIT')			select,insert,update,references			NULL
def	performance_schema	events_waits_current	OPERATION	17	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)			select,insert,update,references			NULL
def	performance_schema	events_waits_current	NUMBER_OF_BYTES	18	NULL	YES	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	events_waits_current	FLAGS	19	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_waits_history	EVENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_waits_history	END_EVENT_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history	EVENT_NAME	4	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_waits_history	SOURCE	5	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_waits_history	TIMER_START	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history	TIMER_END	7	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history	TIMER_WAIT	8	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history	SPINS	9	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history	OBJECT_SCHEMA	10	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_waits_history	OBJECT_NAME	11	NULL	YES	varchar	512	2048	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(512)			select,insert,update,references			NULL
def	performance_schema	events_waits_history	INDEX_NAME	12	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_waits_history	OBJECT_TYPE	13	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_waits_history	OBJECT_INSTANCE_BEGIN	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history	NESTING_EVENT_ID	15	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history	NESTING_EVENT_TYPE	16	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('TRANSACTION','STATEMENT','STAGE','WAIT')			select,insert,update,references			NULL
def	performance_schema	events_waits_history	OPERATION	17	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)			select,insert,update,references			NULL
def	performance_schema	events_waits_history	NUMBER_OF_BYTES	18	NULL	YES	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	events_waits_history	FLAGS	19	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	EVENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	END_EVENT_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	EVENT_NAME	4	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	SOURCE	5	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	TIMER_START	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	TIMER_END	7	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	TIMER_WAIT	8	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	SPINS	9	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	OBJECT_SCHEMA	10	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	OBJECT_NAME	11	NULL	YES	varchar	512	2048	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(512)			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	INDEX_NAME	12	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	OBJECT_TYPE	13	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	OBJECT_INSTANCE_BEGIN	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	NESTING_EVENT_ID	15	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	NESTING_EVENT_TYPE	16	NULL	YES	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('TRANSACTION','STATEMENT','STAGE','WAIT')			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	OPERATION	17	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	NUMBER_OF_BYTES	18	NULL	YES	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	events_waits_history_long	FLAGS	19	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_account_by_event_name	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_account_by_event_name	HOST	2	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_account_by_event_name	EVENT_NAME	3	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_account_by_event_name	COUNT_STAR	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_account_by_event_name	SUM_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_account_by_event_name	MIN_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_account_by_event_name	AVG_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_account_by_event_name	MAX_TIMER_WAIT	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_host_by_event_name	HOST	1	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)	MUL		select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_host_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_host_by_event_name	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_host_by_event_name	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_host_by_event_name	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_host_by_event_name	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_host_by_event_name	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_instance	EVENT_NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	MUL		select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_instance	OBJECT_INSTANCE_BEGIN	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_instance	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_instance	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_instance	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_instance	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_instance	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_thread_by_event_name	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_thread_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_thread_by_event_name	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_thread_by_event_name	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_thread_by_event_name	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_thread_by_event_name	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_thread_by_event_name	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_user_by_event_name	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_user_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_user_by_event_name	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_user_by_event_name	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_user_by_event_name	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_user_by_event_name	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_by_user_by_event_name	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_global_by_event_name	EVENT_NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	events_waits_summary_global_by_event_name	COUNT_STAR	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_global_by_event_name	SUM_TIMER_WAIT	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_global_by_event_name	MIN_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_global_by_event_name	AVG_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	events_waits_summary_global_by_event_name	MAX_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_instances	FILE_NAME	1	NULL	NO	varchar	512	2048	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(512)	PRI		select,insert,update,references			NULL
def	performance_schema	file_instances	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	MUL		select,insert,update,references			NULL
def	performance_schema	file_instances	OPEN_COUNT	3	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	EVENT_NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	COUNT_STAR	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	SUM_TIMER_WAIT	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	MIN_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	AVG_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	MAX_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	COUNT_READ	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	SUM_TIMER_READ	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	MIN_TIMER_READ	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	AVG_TIMER_READ	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	MAX_TIMER_READ	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	SUM_NUMBER_OF_BYTES_READ	12	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	COUNT_WRITE	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	SUM_TIMER_WRITE	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	MIN_TIMER_WRITE	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	AVG_TIMER_WRITE	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	MAX_TIMER_WRITE	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	SUM_NUMBER_OF_BYTES_WRITE	18	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	COUNT_MISC	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	SUM_TIMER_MISC	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	MIN_TIMER_MISC	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	AVG_TIMER_MISC	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_event_name	MAX_TIMER_MISC	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	FILE_NAME	1	NULL	NO	varchar	512	2048	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(512)	MUL		select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	MUL		select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	OBJECT_INSTANCE_BEGIN	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	COUNT_STAR	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	SUM_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	MIN_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	AVG_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	MAX_TIMER_WAIT	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	COUNT_READ	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	SUM_TIMER_READ	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	MIN_TIMER_READ	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	AVG_TIMER_READ	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	MAX_TIMER_READ	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	SUM_NUMBER_OF_BYTES_READ	14	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	COUNT_WRITE	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	SUM_TIMER_WRITE	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	MIN_TIMER_WRITE	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	AVG_TIMER_WRITE	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	MAX_TIMER_WRITE	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	SUM_NUMBER_OF_BYTES_WRITE	20	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	COUNT_MISC	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	SUM_TIMER_MISC	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	MIN_TIMER_MISC	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	AVG_TIMER_MISC	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	file_summary_by_instance	MAX_TIMER_MISC	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	global_status	VARIABLE_NAME	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	global_status	VARIABLE_VALUE	2	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	global_variables	VARIABLE_NAME	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	global_variables	VARIABLE_VALUE	2	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	global_variable_attributes	VARIABLE_NAME	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	global_variable_attributes	ATTR_NAME	2	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)			select,insert,update,references			NULL
def	performance_schema	global_variable_attributes	ATTR_VALUE	3	NULL	NO	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	hosts	HOST	1	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)	UNI		select,insert,update,references			NULL
def	performance_schema	hosts	CURRENT_CONNECTIONS	2	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	hosts	TOTAL_CONNECTIONS	3	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	hosts	MAX_SESSION_CONTROLLED_MEMORY	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	hosts	MAX_SESSION_TOTAL_MEMORY	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	host_cache	IP	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	host_cache	HOST	2	NULL	YES	varchar	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	varchar(255)	MUL		select,insert,update,references			NULL
def	performance_schema	host_cache	HOST_VALIDATED	3	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	host_cache	SUM_CONNECT_ERRORS	4	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_HOST_BLOCKED_ERRORS	5	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_NAMEINFO_TRANSIENT_ERRORS	6	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_NAMEINFO_PERMANENT_ERRORS	7	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_FORMAT_ERRORS	8	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_ADDRINFO_TRANSIENT_ERRORS	9	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_ADDRINFO_PERMANENT_ERRORS	10	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_FCRDNS_ERRORS	11	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_HOST_ACL_ERRORS	12	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_NO_AUTH_PLUGIN_ERRORS	13	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_AUTH_PLUGIN_ERRORS	14	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_HANDSHAKE_ERRORS	15	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_PROXY_USER_ERRORS	16	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_PROXY_USER_ACL_ERRORS	17	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_AUTHENTICATION_ERRORS	18	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_SSL_ERRORS	19	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_MAX_USER_CONNECTIONS_ERRORS	20	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_MAX_USER_CONNECTIONS_PER_HOUR_ERRORS	21	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_DEFAULT_DATABASE_ERRORS	22	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_INIT_CONNECT_ERRORS	23	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_LOCAL_ERRORS	24	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	COUNT_UNKNOWN_ERRORS	25	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	host_cache	FIRST_SEEN	26	NULL	NO	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	host_cache	LAST_SEEN	27	NULL	NO	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	host_cache	FIRST_ERROR_SEEN	28	NULL	YES	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	host_cache	LAST_ERROR_SEEN	29	NULL	YES	timestamp	NULL	NULL	NULL	NULL	0	NULL	NULL	timestamp			select,insert,update,references			NULL
def	performance_schema	innodb_redo_log_files	FILE_ID	1	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references	Id of the file.		NULL
def	performance_schema	innodb_redo_log_files	FILE_NAME	2	NULL	NO	varchar	2000	8000	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(2000)			select,insert,update,references	Path to the file.		NULL
def	performance_schema	innodb_redo_log_files	START_LSN	3	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references	LSN of the first block in the file.		NULL
def	performance_schema	innodb_redo_log_files	END_LSN	4	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references	LSN after the last block in the file.		NULL
def	performance_schema	innodb_redo_log_files	SIZE_IN_BYTES	5	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references	Size of the file (in bytes).		NULL
def	performance_schema	innodb_redo_log_files	IS_FULL	6	NULL	NO	tinyint	NULL	NULL	3	0	NULL	NULL	NULL	tinyint			select,insert,update,references	1 iff file has no free space inside.		NULL
def	performance_schema	innodb_redo_log_files	CONSUMER_LEVEL	7	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references	All redo log consumers registered on smaller levels than this value, have already consumed this file.		NULL
def	performance_schema	keyring_component_status	STATUS_KEY	1	NULL	NO	varchar	256	1024	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(256)			select,insert,update,references			NULL
def	performance_schema	keyring_component_status	STATUS_VALUE	2	NULL	NO	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	keyring_keys	KEY_ID	1	NULL	NO	varchar	255	1020	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	varchar(255)			select,insert,update,references			NULL
def	performance_schema	keyring_keys	KEY_OWNER	2	NULL	YES	varchar	255	1020	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	varchar(255)			select,insert,update,references			NULL
def	performance_schema	keyring_keys	BACKEND_KEY_ID	3	NULL	YES	varchar	255	1020	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	varchar(255)			select,insert,update,references			NULL
def	performance_schema	log_status	SERVER_UUID	1	NULL	NO	char	36	144	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(36)			select,insert,update,references			NULL
def	performance_schema	log_status	LOCAL	2	NULL	NO	json	NULL	NULL	NULL	NULL	NULL	NULL	NULL	json			select,insert,update,references			NULL
def	performance_schema	log_status	REPLICATION	3	NULL	NO	json	NULL	NULL	NULL	NULL	NULL	NULL	NULL	json			select,insert,update,references			NULL
def	performance_schema	log_status	STORAGE_ENGINES	4	NULL	NO	json	NULL	NULL	NULL	NULL	NULL	NULL	NULL	json			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	HOST	2	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	EVENT_NAME	3	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	COUNT_ALLOC	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	COUNT_FREE	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	SUM_NUMBER_OF_BYTES_ALLOC	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	SUM_NUMBER_OF_BYTES_FREE	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	LOW_COUNT_USED	8	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	CURRENT_COUNT_USED	9	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	HIGH_COUNT_USED	10	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	LOW_NUMBER_OF_BYTES_USED	11	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	CURRENT_NUMBER_OF_BYTES_USED	12	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_account_by_event_name	HIGH_NUMBER_OF_BYTES_USED	13	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_host_by_event_name	HOST	1	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)	MUL		select,insert,update,references			NULL
def	performance_schema	memory_summary_by_host_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_host_by_event_name	COUNT_ALLOC	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_host_by_event_name	COUNT_FREE	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_host_by_event_name	SUM_NUMBER_OF_BYTES_ALLOC	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_host_by_event_name	SUM_NUMBER_OF_BYTES_FREE	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_host_by_event_name	LOW_COUNT_USED	7	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_host_by_event_name	CURRENT_COUNT_USED	8	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_host_by_event_name	HIGH_COUNT_USED	9	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_host_by_event_name	LOW_NUMBER_OF_BYTES_USED	10	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_host_by_event_name	CURRENT_NUMBER_OF_BYTES_USED	11	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_host_by_event_name	HIGH_NUMBER_OF_BYTES_USED	12	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_thread_by_event_name	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	memory_summary_by_thread_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	memory_summary_by_thread_by_event_name	COUNT_ALLOC	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_thread_by_event_name	COUNT_FREE	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_thread_by_event_name	SUM_NUMBER_OF_BYTES_ALLOC	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_thread_by_event_name	SUM_NUMBER_OF_BYTES_FREE	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_thread_by_event_name	LOW_COUNT_USED	7	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_thread_by_event_name	CURRENT_COUNT_USED	8	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_thread_by_event_name	HIGH_COUNT_USED	9	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_thread_by_event_name	LOW_NUMBER_OF_BYTES_USED	10	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_thread_by_event_name	CURRENT_NUMBER_OF_BYTES_USED	11	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_thread_by_event_name	HIGH_NUMBER_OF_BYTES_USED	12	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_user_by_event_name	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	memory_summary_by_user_by_event_name	EVENT_NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_user_by_event_name	COUNT_ALLOC	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_user_by_event_name	COUNT_FREE	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_user_by_event_name	SUM_NUMBER_OF_BYTES_ALLOC	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_user_by_event_name	SUM_NUMBER_OF_BYTES_FREE	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_user_by_event_name	LOW_COUNT_USED	7	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_user_by_event_name	CURRENT_COUNT_USED	8	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_user_by_event_name	HIGH_COUNT_USED	9	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_user_by_event_name	LOW_NUMBER_OF_BYTES_USED	10	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_user_by_event_name	CURRENT_NUMBER_OF_BYTES_USED	11	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_by_user_by_event_name	HIGH_NUMBER_OF_BYTES_USED	12	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_global_by_event_name	EVENT_NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	memory_summary_global_by_event_name	COUNT_ALLOC	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_global_by_event_name	COUNT_FREE	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_global_by_event_name	SUM_NUMBER_OF_BYTES_ALLOC	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_global_by_event_name	SUM_NUMBER_OF_BYTES_FREE	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	memory_summary_global_by_event_name	LOW_COUNT_USED	6	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_global_by_event_name	CURRENT_COUNT_USED	7	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_global_by_event_name	HIGH_COUNT_USED	8	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_global_by_event_name	LOW_NUMBER_OF_BYTES_USED	9	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_global_by_event_name	CURRENT_NUMBER_OF_BYTES_USED	10	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	memory_summary_global_by_event_name	HIGH_NUMBER_OF_BYTES_USED	11	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	metadata_locks	OBJECT_TYPE	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	MUL		select,insert,update,references			NULL
def	performance_schema	metadata_locks	OBJECT_SCHEMA	2	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	metadata_locks	OBJECT_NAME	3	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	metadata_locks	COLUMN_NAME	4	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	metadata_locks	OBJECT_INSTANCE_BEGIN	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	metadata_locks	LOCK_TYPE	6	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)			select,insert,update,references			NULL
def	performance_schema	metadata_locks	LOCK_DURATION	7	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)			select,insert,update,references			NULL
def	performance_schema	metadata_locks	LOCK_STATUS	8	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)			select,insert,update,references			NULL
def	performance_schema	metadata_locks	SOURCE	9	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	metadata_locks	OWNER_THREAD_ID	10	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	metadata_locks	OWNER_EVENT_ID	11	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	mutex_instances	NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	MUL		select,insert,update,references			NULL
def	performance_schema	mutex_instances	OBJECT_INSTANCE_BEGIN	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	mutex_instances	LOCKED_BY_THREAD_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	objects_summary_global_by_type	OBJECT_TYPE	1	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	MUL		select,insert,update,references			NULL
def	performance_schema	objects_summary_global_by_type	OBJECT_SCHEMA	2	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	objects_summary_global_by_type	OBJECT_NAME	3	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	objects_summary_global_by_type	COUNT_STAR	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	objects_summary_global_by_type	SUM_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	objects_summary_global_by_type	MIN_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	objects_summary_global_by_type	AVG_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	objects_summary_global_by_type	MAX_TIMER_WAIT	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	performance_timers	TIMER_NAME	1	NULL	NO	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('CYCLE','NANOSECOND','MICROSECOND','MILLISECOND','THREAD_CPU')			select,insert,update,references			NULL
def	performance_schema	performance_timers	TIMER_FREQUENCY	2	NULL	YES	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	performance_timers	TIMER_RESOLUTION	3	NULL	YES	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	performance_timers	TIMER_OVERHEAD	4	NULL	YES	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	persisted_variables	VARIABLE_NAME	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	persisted_variables	VARIABLE_VALUE	2	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	OBJECT_INSTANCE_BEGIN	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	STATEMENT_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	STATEMENT_NAME	3	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	MUL		select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SQL_TEXT	4	NULL	NO	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	OWNER_THREAD_ID	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	OWNER_EVENT_ID	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	OWNER_OBJECT_TYPE	7	NULL	YES	enum	9	36	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('EVENT','FUNCTION','PROCEDURE','TABLE','TRIGGER')	MUL		select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	OWNER_OBJECT_SCHEMA	8	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	OWNER_OBJECT_NAME	9	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	EXECUTION_ENGINE	10	NULL	YES	enum	9	36	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('PRIMARY','SECONDARY')			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	TIMER_PREPARE	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	COUNT_REPREPARE	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	COUNT_EXECUTE	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_TIMER_EXECUTE	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	MIN_TIMER_EXECUTE	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	AVG_TIMER_EXECUTE	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	MAX_TIMER_EXECUTE	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_LOCK_TIME	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_ERRORS	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_WARNINGS	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_ROWS_AFFECTED	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_ROWS_SENT	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_ROWS_EXAMINED	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_CREATED_TMP_DISK_TABLES	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_CREATED_TMP_TABLES	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_SELECT_FULL_JOIN	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_SELECT_FULL_RANGE_JOIN	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_SELECT_RANGE	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_SELECT_RANGE_CHECK	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_SELECT_SCAN	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_SORT_MERGE_PASSES	31	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_SORT_RANGE	32	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_SORT_ROWS	33	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_SORT_SCAN	34	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_NO_INDEX_USED	35	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_NO_GOOD_INDEX_USED	36	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	SUM_CPU_TIME	37	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	MAX_CONTROLLED_MEMORY	38	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	MAX_TOTAL_MEMORY	39	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	prepared_statements_instances	COUNT_SECONDARY	40	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	processlist	ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	processlist	USER	2	NULL	YES	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)			select,insert,update,references			NULL
def	performance_schema	processlist	HOST	3	NULL	YES	varchar	261	261	NULL	NULL	NULL	ascii	ascii_general_ci	varchar(261)			select,insert,update,references			NULL
def	performance_schema	processlist	DB	4	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	processlist	COMMAND	5	NULL	YES	varchar	16	64	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(16)			select,insert,update,references			NULL
def	performance_schema	processlist	TIME	6	NULL	YES	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	processlist	STATE	7	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	processlist	INFO	8	NULL	YES	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	processlist	EXECUTION_ENGINE	9	NULL	YES	enum	9	36	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('PRIMARY','SECONDARY')			select,insert,update,references			NULL
def	performance_schema	replication_applier_configuration	CHANNEL_NAME	1	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(64)	PRI		select,insert,update,references			NULL
def	performance_schema	replication_applier_configuration	DESIRED_DELAY	2	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	replication_applier_configuration	PRIVILEGE_CHECKS_USER	3	NULL	YES	text	65535	65535	NULL	NULL	NULL	utf8mb3	utf8mb3_bin	text			select,insert,update,references	User name for the security context of the applier.		NULL
def	performance_schema	replication_applier_configuration	REQUIRE_ROW_FORMAT	4	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references	Indicates whether the channel shall only accept row based events.		NULL
def	performance_schema	replication_applier_configuration	REQUIRE_TABLE_PRIMARY_KEY_CHECK	5	NULL	NO	enum	8	32	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('STREAM','ON','OFF','GENERATE')			select,insert,update,references	Indicates what is the channel policy regarding tables without primary keys on create and alter table queries		NULL
def	performance_schema	replication_applier_configuration	ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS_TYPE	6	NULL	NO	enum	5	20	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('OFF','LOCAL','UUID')			select,insert,update,references	Indicates whether the channel will generate a new GTID for anonymous transactions. OFF means that anonymous transactions will remain anonymous. LOCAL means that anonymous transactions will be assigned a newly generated GTID based on server_uuid. UUID indicates that anonymous transactions will be assigned a newly generated GTID based on Assign_gtids_to_anonymous_transactions_value		NULL
def	performance_schema	replication_applier_configuration	ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS_VALUE	7	NULL	YES	text	65535	65535	NULL	NULL	NULL	utf8mb3	utf8mb3_bin	text			select,insert,update,references	Indicates the UUID used while generating GTIDs for anonymous transactions		NULL
def	performance_schema	replication_applier_filters	CHANNEL_NAME	1	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(64)			select,insert,update,references			NULL
def	performance_schema	replication_applier_filters	FILTER_NAME	2	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(64)			select,insert,update,references			NULL
def	performance_schema	replication_applier_filters	FILTER_RULE	3	NULL	NO	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	replication_applier_filters	CONFIGURED_BY	4	NULL	NO	enum	37	148	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('STARTUP_OPTIONS','CHANGE_REPLICATION_FILTER','STARTUP_OPTIONS_FOR_CHANNEL','CHANGE_REPLICATION_FILTER_FOR_CHANNEL')			select,insert,update,references			NULL
def	performance_schema	replication_applier_filters	ACTIVE_SINCE	5	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_filters	COUNTER	6	0	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_applier_global_filters	FILTER_NAME	1	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(64)			select,insert,update,references			NULL
def	performance_schema	replication_applier_global_filters	FILTER_RULE	2	NULL	NO	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	replication_applier_global_filters	CONFIGURED_BY	3	NULL	NO	enum	25	100	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('STARTUP_OPTIONS','CHANGE_REPLICATION_FILTER')			select,insert,update,references			NULL
def	performance_schema	replication_applier_global_filters	ACTIVE_SINCE	4	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status	CHANNEL_NAME	1	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(64)	PRI		select,insert,update,references			NULL
def	performance_schema	replication_applier_status	SERVICE_STATE	2	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('ON','OFF')			select,insert,update,references			NULL
def	performance_schema	replication_applier_status	REMAINING_DELAY	3	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int unsigned			select,insert,update,references			NULL
def	performance_schema	replication_applier_status	COUNT_TRANSACTIONS_RETRIES	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	CHANNEL_NAME	1	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(64)	PRI		select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	THREAD_ID	2	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	SERVICE_STATE	3	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('ON','OFF')			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	LAST_ERROR_NUMBER	4	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	LAST_ERROR_MESSAGE	5	NULL	NO	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	LAST_ERROR_TIMESTAMP	6	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	LAST_PROCESSED_TRANSACTION	7	NULL	YES	char	90	360	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(90)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	LAST_PROCESSED_TRANSACTION_ORIGINAL_COMMIT_TIMESTAMP	8	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	LAST_PROCESSED_TRANSACTION_IMMEDIATE_COMMIT_TIMESTAMP	9	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	LAST_PROCESSED_TRANSACTION_START_BUFFER_TIMESTAMP	10	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	LAST_PROCESSED_TRANSACTION_END_BUFFER_TIMESTAMP	11	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	PROCESSING_TRANSACTION	12	NULL	YES	char	90	360	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(90)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	PROCESSING_TRANSACTION_ORIGINAL_COMMIT_TIMESTAMP	13	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	PROCESSING_TRANSACTION_IMMEDIATE_COMMIT_TIMESTAMP	14	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_coordinator	PROCESSING_TRANSACTION_START_BUFFER_TIMESTAMP	15	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	CHANNEL_NAME	1	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(64)	PRI		select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	WORKER_ID	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	THREAD_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	SERVICE_STATE	4	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('ON','OFF')			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	LAST_ERROR_NUMBER	5	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	LAST_ERROR_MESSAGE	6	NULL	NO	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	LAST_ERROR_TIMESTAMP	7	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	LAST_APPLIED_TRANSACTION	8	NULL	YES	char	90	360	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(90)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	LAST_APPLIED_TRANSACTION_ORIGINAL_COMMIT_TIMESTAMP	9	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	LAST_APPLIED_TRANSACTION_IMMEDIATE_COMMIT_TIMESTAMP	10	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	LAST_APPLIED_TRANSACTION_START_APPLY_TIMESTAMP	11	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	LAST_APPLIED_TRANSACTION_END_APPLY_TIMESTAMP	12	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	APPLYING_TRANSACTION	13	NULL	YES	char	90	360	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(90)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	APPLYING_TRANSACTION_ORIGINAL_COMMIT_TIMESTAMP	14	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	APPLYING_TRANSACTION_IMMEDIATE_COMMIT_TIMESTAMP	15	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	APPLYING_TRANSACTION_START_APPLY_TIMESTAMP	16	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	LAST_APPLIED_TRANSACTION_RETRIES_COUNT	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	LAST_APPLIED_TRANSACTION_LAST_TRANSIENT_ERROR_NUMBER	18	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	LAST_APPLIED_TRANSACTION_LAST_TRANSIENT_ERROR_MESSAGE	19	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	LAST_APPLIED_TRANSACTION_LAST_TRANSIENT_ERROR_TIMESTAMP	20	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	APPLYING_TRANSACTION_RETRIES_COUNT	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	APPLYING_TRANSACTION_LAST_TRANSIENT_ERROR_NUMBER	22	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	APPLYING_TRANSACTION_LAST_TRANSIENT_ERROR_MESSAGE	23	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	replication_applier_status_by_worker	APPLYING_TRANSACTION_LAST_TRANSIENT_ERROR_TIMESTAMP	24	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_asynchronous_connection_failover	CHANNEL_NAME	1	NULL	NO	char	64	192	NULL	NULL	NULL	utf8mb3	utf8mb3_general_ci	char(64)			select,insert,update,references	The replication channel name that connects source and replica.		NULL
def	performance_schema	replication_asynchronous_connection_failover	HOST	2	NULL	NO	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)			select,insert,update,references	The source hostname that the replica will attempt to switch over the replication connection to in case of a failure.		NULL
def	performance_schema	replication_asynchronous_connection_failover	PORT	3	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references	The source port that the replica will attempt to switch over the replication connection to in case of a failure.		NULL
def	performance_schema	replication_asynchronous_connection_failover	NETWORK_NAMESPACE	4	NULL	YES	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(64)			select,insert,update,references	The source network namespace that the replica will attempt to switch over the replication connection to in case of a failure. If its value is empty, connections use the default (global) namespace.		NULL
def	performance_schema	replication_asynchronous_connection_failover	WEIGHT	5	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int unsigned			select,insert,update,references	The order in which the replica shall try to switch the connection over to when there are failures. Weight can be set to a number between 1 and 100, where 100 is the highest weight and 1 the lowest.		NULL
def	performance_schema	replication_asynchronous_connection_failover	MANAGED_NAME	6		NO	char	64	192	NULL	NULL	NULL	utf8mb3	utf8mb3_general_ci	char(64)			select,insert,update,references	The name of the group which this server belongs to.		NULL
def	performance_schema	replication_asynchronous_connection_failover_managed	CHANNEL_NAME	1	NULL	NO	char	64	192	NULL	NULL	NULL	utf8mb3	utf8mb3_general_ci	char(64)			select,insert,update,references	The replication channel name that connects source and replica.		NULL
def	performance_schema	replication_asynchronous_connection_failover_managed	MANAGED_NAME	2		NO	char	64	192	NULL	NULL	NULL	utf8mb3	utf8mb3_general_ci	char(64)			select,insert,update,references	The name of the source which needs to be managed.		NULL
def	performance_schema	replication_asynchronous_connection_failover_managed	MANAGED_TYPE	3		NO	char	64	192	NULL	NULL	NULL	utf8mb3	utf8mb3_general_ci	char(64)			select,insert,update,references	Determines the managed type.		NULL
def	performance_schema	replication_asynchronous_connection_failover_managed	CONFIGURATION	4	NULL	YES	json	NULL	NULL	NULL	NULL	NULL	NULL	NULL	json			select,insert,update,references	The data to help manage group. For Managed_type = GroupReplication, Configuration value should contain {"Primary_weight": 80, "Secondary_weight": 60}, so that it assigns weight=80 to PRIMARY of the group, and weight=60 for rest of the members in mysql.replication_asynchronous_connection_failover table.		NULL
def	performance_schema	replication_connection_configuration	CHANNEL_NAME	1	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(64)	PRI		select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	HOST	2	NULL	NO	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	PORT	3	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	USER	4	NULL	NO	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	NETWORK_INTERFACE	5	NULL	NO	char	60	240	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(60)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	AUTO_POSITION	6	NULL	NO	enum	1	4	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('1','0')			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	SSL_ALLOWED	7	NULL	NO	enum	7	28	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO','IGNORED')			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	SSL_CA_FILE	8	NULL	NO	varchar	512	2048	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(512)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	SSL_CA_PATH	9	NULL	NO	varchar	512	2048	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(512)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	SSL_CERTIFICATE	10	NULL	NO	varchar	512	2048	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(512)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	SSL_CIPHER	11	NULL	NO	varchar	512	2048	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(512)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	SSL_KEY	12	NULL	NO	varchar	512	2048	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(512)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	SSL_VERIFY_SERVER_CERTIFICATE	13	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	SSL_CRL_FILE	14	NULL	NO	varchar	255	1020	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(255)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	SSL_CRL_PATH	15	NULL	NO	varchar	255	1020	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(255)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	CONNECTION_RETRY_INTERVAL	16	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	CONNECTION_RETRY_COUNT	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	HEARTBEAT_INTERVAL	18	NULL	NO	double	NULL	NULL	10	3	NULL	NULL	NULL	double(10,3)			select,insert,update,references	Number of seconds after which a heartbeat will be sent .		NULL
def	performance_schema	replication_connection_configuration	TLS_VERSION	19	NULL	NO	varchar	255	1020	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(255)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	PUBLIC_KEY_PATH	20	NULL	NO	varchar	512	2048	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(512)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	GET_PUBLIC_KEY	21	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	NETWORK_NAMESPACE	22	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	COMPRESSION_ALGORITHM	23	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(64)			select,insert,update,references	Compression algorithm used for data transfer between master and slave.		NULL
def	performance_schema	replication_connection_configuration	ZSTD_COMPRESSION_LEVEL	24	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references	Compression level associated with zstd compression algorithm.		NULL
def	performance_schema	replication_connection_configuration	TLS_CIPHERSUITES	25	NULL	YES	text	65535	65535	NULL	NULL	NULL	utf8mb3	utf8mb3_bin	text			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	SOURCE_CONNECTION_AUTO_FAILOVER	26	NULL	NO	enum	1	4	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('1','0')			select,insert,update,references			NULL
def	performance_schema	replication_connection_configuration	GTID_ONLY	27	NULL	NO	enum	1	4	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('1','0')			select,insert,update,references	Indicates if this channel only uses GTIDs and does not persist positions.		NULL
def	performance_schema	replication_connection_status	CHANNEL_NAME	1	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(64)	PRI		select,insert,update,references			NULL
def	performance_schema	replication_connection_status	GROUP_NAME	2	NULL	NO	char	36	144	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(36)			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	SOURCE_UUID	3	NULL	NO	char	36	144	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(36)			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	THREAD_ID	4	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	replication_connection_status	SERVICE_STATE	5	NULL	NO	enum	10	40	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('ON','OFF','CONNECTING')			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	COUNT_RECEIVED_HEARTBEATS	6	0	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	LAST_HEARTBEAT_TIMESTAMP	7	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references	Shows when the most recent heartbeat signal was received.		NULL
def	performance_schema	replication_connection_status	RECEIVED_TRANSACTION_SET	8	NULL	NO	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	LAST_ERROR_NUMBER	9	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	LAST_ERROR_MESSAGE	10	NULL	NO	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	LAST_ERROR_TIMESTAMP	11	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	LAST_QUEUED_TRANSACTION	12	NULL	YES	char	90	360	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(90)			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	LAST_QUEUED_TRANSACTION_ORIGINAL_COMMIT_TIMESTAMP	13	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	LAST_QUEUED_TRANSACTION_IMMEDIATE_COMMIT_TIMESTAMP	14	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	LAST_QUEUED_TRANSACTION_START_QUEUE_TIMESTAMP	15	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	LAST_QUEUED_TRANSACTION_END_QUEUE_TIMESTAMP	16	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	QUEUEING_TRANSACTION	17	NULL	YES	char	90	360	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(90)			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	QUEUEING_TRANSACTION_ORIGINAL_COMMIT_TIMESTAMP	18	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	QUEUEING_TRANSACTION_IMMEDIATE_COMMIT_TIMESTAMP	19	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_connection_status	QUEUEING_TRANSACTION_START_QUEUE_TIMESTAMP	20	NULL	NO	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	replication_group_members	CHANNEL_NAME	1	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(64)			select,insert,update,references			NULL
def	performance_schema	replication_group_members	MEMBER_ID	2	NULL	NO	char	36	144	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(36)			select,insert,update,references			NULL
def	performance_schema	replication_group_members	MEMBER_HOST	3	NULL	NO	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)			select,insert,update,references			NULL
def	performance_schema	replication_group_members	MEMBER_PORT	4	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	replication_group_members	MEMBER_STATE	5	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(64)			select,insert,update,references			NULL
def	performance_schema	replication_group_members	MEMBER_ROLE	6	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(64)			select,insert,update,references			NULL
def	performance_schema	replication_group_members	MEMBER_VERSION	7	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(64)			select,insert,update,references			NULL
def	performance_schema	replication_group_members	MEMBER_COMMUNICATION_STACK	8	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(64)			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	CHANNEL_NAME	1	NULL	NO	char	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	char(64)			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	VIEW_ID	2	NULL	NO	char	60	240	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(60)			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	MEMBER_ID	3	NULL	NO	char	36	144	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(36)			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	COUNT_TRANSACTIONS_IN_QUEUE	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	COUNT_TRANSACTIONS_CHECKED	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	COUNT_CONFLICTS_DETECTED	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	COUNT_TRANSACTIONS_ROWS_VALIDATING	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	TRANSACTIONS_COMMITTED_ALL_MEMBERS	8	NULL	NO	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	LAST_CONFLICT_FREE_TRANSACTION	9	NULL	NO	text	65535	65535	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	text			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	COUNT_TRANSACTIONS_REMOTE_IN_APPLIER_QUEUE	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	COUNT_TRANSACTIONS_REMOTE_APPLIED	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	COUNT_TRANSACTIONS_LOCAL_PROPOSED	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	replication_group_member_stats	COUNT_TRANSACTIONS_LOCAL_ROLLBACK	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	rwlock_instances	NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	MUL		select,insert,update,references			NULL
def	performance_schema	rwlock_instances	OBJECT_INSTANCE_BEGIN	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	rwlock_instances	WRITE_LOCKED_BY_THREAD_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	rwlock_instances	READ_LOCKED_BY_COUNT	4	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int unsigned			select,insert,update,references			NULL
def	performance_schema	session_account_connect_attrs	PROCESSLIST_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	session_account_connect_attrs	ATTR_NAME	2	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	varchar(32)	PRI		select,insert,update,references			NULL
def	performance_schema	session_account_connect_attrs	ATTR_VALUE	3	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	session_account_connect_attrs	ORDINAL_POSITION	4	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	session_connect_attrs	PROCESSLIST_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	session_connect_attrs	ATTR_NAME	2	NULL	NO	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	varchar(32)	PRI		select,insert,update,references			NULL
def	performance_schema	session_connect_attrs	ATTR_VALUE	3	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	session_connect_attrs	ORDINAL_POSITION	4	NULL	YES	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	session_status	VARIABLE_NAME	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	session_status	VARIABLE_VALUE	2	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	session_variables	VARIABLE_NAME	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	session_variables	VARIABLE_VALUE	2	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	setup_actors	HOST	1	%	NO	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)	PRI		select,insert,update,references			NULL
def	performance_schema	setup_actors	USER	2	%	NO	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	PRI		select,insert,update,references			NULL
def	performance_schema	setup_actors	ROLE	3	%	NO	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	PRI		select,insert,update,references			NULL
def	performance_schema	setup_actors	ENABLED	4	YES	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	setup_actors	HISTORY	5	YES	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	setup_consumers	NAME	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	setup_consumers	ENABLED	2	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	setup_instruments	NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	setup_instruments	ENABLED	2	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	setup_instruments	TIMED	3	NULL	YES	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	setup_instruments	PROPERTIES	4	NULL	NO	set	71	284	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	set('singleton','progress','user','global_statistics','mutable','controlled_by_default')			select,insert,update,references			NULL
def	performance_schema	setup_instruments	FLAGS	5	NULL	YES	set	10	40	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	set('controlled')			select,insert,update,references			NULL
def	performance_schema	setup_instruments	VOLATILITY	6	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	setup_instruments	DOCUMENTATION	7	NULL	YES	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	setup_loggers	NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	setup_loggers	LEVEL	2	NULL	NO	enum	5	20	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('none','error','warn','info','debug')			select,insert,update,references			NULL
def	performance_schema	setup_loggers	DESCRIPTION	3	NULL	YES	varchar	1023	4092	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1023)			select,insert,update,references			NULL
def	performance_schema	setup_meters	NAME	1	NULL	NO	varchar	63	252	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(63)	PRI		select,insert,update,references			NULL
def	performance_schema	setup_meters	FREQUENCY	2	NULL	NO	mediumint	NULL	NULL	7	0	NULL	NULL	NULL	mediumint unsigned			select,insert,update,references			NULL
def	performance_schema	setup_meters	ENABLED	3	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	setup_meters	DESCRIPTION	4	NULL	YES	varchar	1023	4092	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1023)			select,insert,update,references			NULL
def	performance_schema	setup_metrics	NAME	1	NULL	NO	varchar	63	252	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(63)	PRI		select,insert,update,references			NULL
def	performance_schema	setup_metrics	METER	2	NULL	NO	varchar	63	252	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(63)			select,insert,update,references			NULL
def	performance_schema	setup_metrics	METRIC_TYPE	3	NULL	NO	enum	20	80	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('ASYNC COUNTER','ASYNC UPDOWN COUNTER','ASYNC GAUGE COUNTER')			select,insert,update,references			NULL
def	performance_schema	setup_metrics	NUM_TYPE	4	NULL	NO	enum	7	28	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('INTEGER','DOUBLE')			select,insert,update,references			NULL
def	performance_schema	setup_metrics	UNIT	5	NULL	YES	varchar	63	252	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(63)			select,insert,update,references			NULL
def	performance_schema	setup_metrics	DESCRIPTION	6	NULL	YES	varchar	1023	4092	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1023)			select,insert,update,references			NULL
def	performance_schema	setup_objects	OBJECT_TYPE	1	TABLE	NO	enum	9	36	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('EVENT','FUNCTION','PROCEDURE','TABLE','TRIGGER')	MUL		select,insert,update,references			NULL
def	performance_schema	setup_objects	OBJECT_SCHEMA	2	%	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	setup_objects	OBJECT_NAME	3	%	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	setup_objects	ENABLED	4	YES	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	setup_objects	TIMED	5	YES	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	setup_threads	NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	setup_threads	ENABLED	2	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	setup_threads	HISTORY	3	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	setup_threads	PROPERTIES	4	NULL	NO	set	14	56	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	set('singleton','user')			select,insert,update,references			NULL
def	performance_schema	setup_threads	VOLATILITY	5	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	setup_threads	DOCUMENTATION	6	NULL	YES	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	socket_instances	EVENT_NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	socket_instances	OBJECT_INSTANCE_BEGIN	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	socket_instances	THREAD_ID	3	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	socket_instances	SOCKET_ID	4	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int	MUL		select,insert,update,references			NULL
def	performance_schema	socket_instances	IP	5	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	MUL		select,insert,update,references			NULL
def	performance_schema	socket_instances	PORT	6	NULL	NO	int	NULL	NULL	10	0	NULL	NULL	NULL	int			select,insert,update,references			NULL
def	performance_schema	socket_instances	STATE	7	NULL	NO	enum	6	24	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('IDLE','ACTIVE')			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	EVENT_NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	PRI		select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	COUNT_STAR	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	SUM_TIMER_WAIT	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	MIN_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	AVG_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	MAX_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	COUNT_READ	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	SUM_TIMER_READ	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	MIN_TIMER_READ	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	AVG_TIMER_READ	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	MAX_TIMER_READ	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	SUM_NUMBER_OF_BYTES_READ	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	COUNT_WRITE	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	SUM_TIMER_WRITE	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	MIN_TIMER_WRITE	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	AVG_TIMER_WRITE	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	MAX_TIMER_WRITE	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	SUM_NUMBER_OF_BYTES_WRITE	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	COUNT_MISC	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	SUM_TIMER_MISC	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	MIN_TIMER_MISC	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	AVG_TIMER_MISC	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_event_name	MAX_TIMER_MISC	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	EVENT_NAME	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	MUL		select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	OBJECT_INSTANCE_BEGIN	2	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	COUNT_STAR	3	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	SUM_TIMER_WAIT	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	MIN_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	AVG_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	MAX_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	COUNT_READ	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	SUM_TIMER_READ	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	MIN_TIMER_READ	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	AVG_TIMER_READ	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	MAX_TIMER_READ	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	SUM_NUMBER_OF_BYTES_READ	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	COUNT_WRITE	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	SUM_TIMER_WRITE	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	MIN_TIMER_WRITE	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	AVG_TIMER_WRITE	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	MAX_TIMER_WRITE	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	SUM_NUMBER_OF_BYTES_WRITE	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	COUNT_MISC	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	SUM_TIMER_MISC	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	MIN_TIMER_MISC	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	AVG_TIMER_MISC	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	socket_summary_by_instance	MAX_TIMER_MISC	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	status_by_account	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	status_by_account	HOST	2	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)			select,insert,update,references			NULL
def	performance_schema	status_by_account	VARIABLE_NAME	3	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	status_by_account	VARIABLE_VALUE	4	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	status_by_host	HOST	1	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)	MUL		select,insert,update,references			NULL
def	performance_schema	status_by_host	VARIABLE_NAME	2	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	status_by_host	VARIABLE_VALUE	3	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	status_by_thread	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	status_by_thread	VARIABLE_NAME	2	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	status_by_thread	VARIABLE_VALUE	3	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	status_by_user	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	MUL		select,insert,update,references			NULL
def	performance_schema	status_by_user	VARIABLE_NAME	2	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	status_by_user	VARIABLE_VALUE	3	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	table_handles	OBJECT_TYPE	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	MUL		select,insert,update,references			NULL
def	performance_schema	table_handles	OBJECT_SCHEMA	2	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	table_handles	OBJECT_NAME	3	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	table_handles	OBJECT_INSTANCE_BEGIN	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	table_handles	OWNER_THREAD_ID	5	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	table_handles	OWNER_EVENT_ID	6	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_handles	INTERNAL_LOCK	7	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	table_handles	EXTERNAL_LOCK	8	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	OBJECT_TYPE	1	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	MUL		select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	OBJECT_SCHEMA	2	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	OBJECT_NAME	3	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	INDEX_NAME	4	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	COUNT_STAR	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	SUM_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MIN_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	AVG_TIMER_WAIT	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MAX_TIMER_WAIT	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	COUNT_READ	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	SUM_TIMER_READ	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MIN_TIMER_READ	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	AVG_TIMER_READ	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MAX_TIMER_READ	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	COUNT_WRITE	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	SUM_TIMER_WRITE	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MIN_TIMER_WRITE	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	AVG_TIMER_WRITE	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MAX_TIMER_WRITE	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	COUNT_FETCH	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	SUM_TIMER_FETCH	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MIN_TIMER_FETCH	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	AVG_TIMER_FETCH	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MAX_TIMER_FETCH	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	COUNT_INSERT	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	SUM_TIMER_INSERT	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MIN_TIMER_INSERT	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	AVG_TIMER_INSERT	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MAX_TIMER_INSERT	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	COUNT_UPDATE	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	SUM_TIMER_UPDATE	31	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MIN_TIMER_UPDATE	32	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	AVG_TIMER_UPDATE	33	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MAX_TIMER_UPDATE	34	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	COUNT_DELETE	35	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	SUM_TIMER_DELETE	36	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MIN_TIMER_DELETE	37	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	AVG_TIMER_DELETE	38	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_index_usage	MAX_TIMER_DELETE	39	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	OBJECT_TYPE	1	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	MUL		select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	OBJECT_SCHEMA	2	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	OBJECT_NAME	3	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	COUNT_STAR	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	SUM_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MIN_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	AVG_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MAX_TIMER_WAIT	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	COUNT_READ	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	SUM_TIMER_READ	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MIN_TIMER_READ	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	AVG_TIMER_READ	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MAX_TIMER_READ	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	COUNT_WRITE	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	SUM_TIMER_WRITE	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MIN_TIMER_WRITE	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	AVG_TIMER_WRITE	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MAX_TIMER_WRITE	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	COUNT_FETCH	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	SUM_TIMER_FETCH	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MIN_TIMER_FETCH	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	AVG_TIMER_FETCH	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MAX_TIMER_FETCH	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	COUNT_INSERT	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	SUM_TIMER_INSERT	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MIN_TIMER_INSERT	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	AVG_TIMER_INSERT	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MAX_TIMER_INSERT	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	COUNT_UPDATE	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	SUM_TIMER_UPDATE	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MIN_TIMER_UPDATE	31	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	AVG_TIMER_UPDATE	32	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MAX_TIMER_UPDATE	33	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	COUNT_DELETE	34	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	SUM_TIMER_DELETE	35	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MIN_TIMER_DELETE	36	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	AVG_TIMER_DELETE	37	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_io_waits_summary_by_table	MAX_TIMER_DELETE	38	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	OBJECT_TYPE	1	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	MUL		select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	OBJECT_SCHEMA	2	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	OBJECT_NAME	3	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_STAR	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_WAIT	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_WAIT	6	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_WAIT	7	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_WAIT	8	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_READ	9	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_READ	10	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_READ	11	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_READ	12	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_READ	13	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_WRITE	14	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_WRITE	15	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_WRITE	16	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_WRITE	17	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_WRITE	18	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_READ_NORMAL	19	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_READ_NORMAL	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_READ_NORMAL	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_READ_NORMAL	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_READ_NORMAL	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_READ_WITH_SHARED_LOCKS	24	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_READ_WITH_SHARED_LOCKS	25	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_READ_WITH_SHARED_LOCKS	26	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_READ_WITH_SHARED_LOCKS	27	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_READ_WITH_SHARED_LOCKS	28	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_READ_HIGH_PRIORITY	29	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_READ_HIGH_PRIORITY	30	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_READ_HIGH_PRIORITY	31	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_READ_HIGH_PRIORITY	32	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_READ_HIGH_PRIORITY	33	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_READ_NO_INSERT	34	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_READ_NO_INSERT	35	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_READ_NO_INSERT	36	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_READ_NO_INSERT	37	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_READ_NO_INSERT	38	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_READ_EXTERNAL	39	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_READ_EXTERNAL	40	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_READ_EXTERNAL	41	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_READ_EXTERNAL	42	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_READ_EXTERNAL	43	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_WRITE_ALLOW_WRITE	44	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_WRITE_ALLOW_WRITE	45	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_WRITE_ALLOW_WRITE	46	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_WRITE_ALLOW_WRITE	47	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_WRITE_ALLOW_WRITE	48	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_WRITE_CONCURRENT_INSERT	49	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_WRITE_CONCURRENT_INSERT	50	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_WRITE_CONCURRENT_INSERT	51	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_WRITE_CONCURRENT_INSERT	52	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_WRITE_CONCURRENT_INSERT	53	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_WRITE_LOW_PRIORITY	54	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_WRITE_LOW_PRIORITY	55	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_WRITE_LOW_PRIORITY	56	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_WRITE_LOW_PRIORITY	57	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_WRITE_LOW_PRIORITY	58	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_WRITE_NORMAL	59	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_WRITE_NORMAL	60	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_WRITE_NORMAL	61	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_WRITE_NORMAL	62	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_WRITE_NORMAL	63	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	COUNT_WRITE_EXTERNAL	64	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	SUM_TIMER_WRITE_EXTERNAL	65	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MIN_TIMER_WRITE_EXTERNAL	66	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	AVG_TIMER_WRITE_EXTERNAL	67	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	table_lock_waits_summary_by_table	MAX_TIMER_WRITE_EXTERNAL	68	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	threads	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	threads	NAME	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)	MUL		select,insert,update,references			NULL
def	performance_schema	threads	TYPE	3	NULL	NO	varchar	10	40	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(10)			select,insert,update,references			NULL
def	performance_schema	threads	PROCESSLIST_ID	4	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	threads	PROCESSLIST_USER	5	NULL	YES	varchar	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(32)	MUL		select,insert,update,references			NULL
def	performance_schema	threads	PROCESSLIST_HOST	6	NULL	YES	varchar	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	varchar(255)	MUL		select,insert,update,references			NULL
def	performance_schema	threads	PROCESSLIST_DB	7	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	threads	PROCESSLIST_COMMAND	8	NULL	YES	varchar	16	64	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(16)			select,insert,update,references			NULL
def	performance_schema	threads	PROCESSLIST_TIME	9	NULL	YES	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	threads	PROCESSLIST_STATE	10	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	threads	PROCESSLIST_INFO	11	NULL	YES	longtext	**********	**********	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	longtext			select,insert,update,references			NULL
def	performance_schema	threads	PARENT_THREAD_ID	12	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	threads	ROLE	13	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	threads	INSTRUMENTED	14	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	threads	HISTORY	15	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	threads	CONNECTION_TYPE	16	NULL	YES	varchar	16	64	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(16)			select,insert,update,references			NULL
def	performance_schema	threads	THREAD_OS_ID	17	NULL	YES	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	MUL		select,insert,update,references			NULL
def	performance_schema	threads	RESOURCE_GROUP	18	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	MUL		select,insert,update,references			NULL
def	performance_schema	threads	EXECUTION_ENGINE	19	NULL	YES	enum	9	36	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('PRIMARY','SECONDARY')			select,insert,update,references			NULL
def	performance_schema	threads	CONTROLLED_MEMORY	20	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	threads	MAX_CONTROLLED_MEMORY	21	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	threads	TOTAL_MEMORY	22	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	threads	MAX_TOTAL_MEMORY	23	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	threads	TELEMETRY_ACTIVE	24	NULL	NO	enum	3	12	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('YES','NO')			select,insert,update,references			NULL
def	performance_schema	tls_channel_status	CHANNEL	1	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	tls_channel_status	PROPERTY	2	NULL	NO	varchar	128	512	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(128)			select,insert,update,references			NULL
def	performance_schema	tls_channel_status	VALUE	3	NULL	NO	varchar	2048	8192	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(2048)			select,insert,update,references			NULL
def	performance_schema	users	USER	1	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)	UNI		select,insert,update,references			NULL
def	performance_schema	users	CURRENT_CONNECTIONS	2	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	users	TOTAL_CONNECTIONS	3	NULL	NO	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	users	MAX_SESSION_CONTROLLED_MEMORY	4	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	users	MAX_SESSION_TOTAL_MEMORY	5	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned			select,insert,update,references			NULL
def	performance_schema	user_defined_functions	UDF_NAME	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	user_defined_functions	UDF_RETURN_TYPE	2	NULL	NO	varchar	20	80	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(20)			select,insert,update,references			NULL
def	performance_schema	user_defined_functions	UDF_TYPE	3	NULL	NO	varchar	20	80	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(20)			select,insert,update,references			NULL
def	performance_schema	user_defined_functions	UDF_LIBRARY	4	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	user_defined_functions	UDF_USAGE_COUNT	5	NULL	YES	bigint	NULL	NULL	19	0	NULL	NULL	NULL	bigint			select,insert,update,references			NULL
def	performance_schema	user_variables_by_thread	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	user_variables_by_thread	VARIABLE_NAME	2	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	user_variables_by_thread	VARIABLE_VALUE	3	NULL	YES	longblob	**********	**********	NULL	NULL	NULL	NULL	NULL	longblob			select,insert,update,references			NULL
def	performance_schema	variables_by_thread	THREAD_ID	1	NULL	NO	bigint	NULL	NULL	20	0	NULL	NULL	NULL	bigint unsigned	PRI		select,insert,update,references			NULL
def	performance_schema	variables_by_thread	VARIABLE_NAME	2	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)	PRI		select,insert,update,references			NULL
def	performance_schema	variables_by_thread	VARIABLE_VALUE	3	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	variables_info	VARIABLE_NAME	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	variables_info	VARIABLE_SOURCE	2	COMPILED	YES	enum	12	48	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('COMPILED','GLOBAL','SERVER','EXPLICIT','EXTRA','USER','LOGIN','COMMAND_LINE','PERSISTED','DYNAMIC')			select,insert,update,references			NULL
def	performance_schema	variables_info	VARIABLE_PATH	3	NULL	YES	varchar	1024	4096	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(1024)			select,insert,update,references			NULL
def	performance_schema	variables_info	MIN_VALUE	4	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	variables_info	MAX_VALUE	5	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	variables_info	SET_TIME	6	NULL	YES	timestamp	NULL	NULL	NULL	NULL	6	NULL	NULL	timestamp(6)			select,insert,update,references			NULL
def	performance_schema	variables_info	SET_USER	7	NULL	YES	char	32	128	NULL	NULL	NULL	utf8mb4	utf8mb4_bin	char(32)			select,insert,update,references			NULL
def	performance_schema	variables_info	SET_HOST	8	NULL	YES	char	255	255	NULL	NULL	NULL	ascii	ascii_general_ci	char(255)			select,insert,update,references			NULL
def	performance_schema	variables_metadata	VARIABLE_NAME	1	NULL	NO	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	variables_metadata	VARIABLE_SCOPE	2	NULL	NO	enum	12	48	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('GLOBAL','SESSION','SESSION_ONLY')			select,insert,update,references			NULL
def	performance_schema	variables_metadata	DATA_TYPE	3	NULL	NO	enum	11	44	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	enum('Integer','Numeric','String','Enumeration','Boolean','Set')			select,insert,update,references			NULL
def	performance_schema	variables_metadata	MIN_VALUE	4	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	variables_metadata	MAX_VALUE	5	NULL	YES	varchar	64	256	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	varchar(64)			select,insert,update,references			NULL
def	performance_schema	variables_metadata	DOCUMENTATION	6	NULL	NO	mediumtext	16777215	16777215	NULL	NULL	NULL	utf8mb4	utf8mb4_0900_ai_ci	mediumtext			select,insert,update,references			NULL
select count(*) from information_schema.columns
where table_schema="performance_schema" and data_type = "bigint"
   and column_name like "%number_of_bytes" into @count_byte_columns;
select @count_byte_columns > 0;
@count_byte_columns > 0
1
select count(*) from information_schema.columns
where table_schema="performance_schema" and data_type="bigint"
   and column_name like "%number_of_bytes"
   and column_type not like "%unsigned" into @count_byte_signed;
select (@count_byte_columns - @count_byte_signed) = 0;
(@count_byte_columns - @count_byte_signed) = 0
1
select count(*) from information_schema.columns
where table_schema="performance_schema" and data_type = "bigint"
   and column_name like "%object_instance_begin" into @count_object_columns;
select @count_object_columns > 0;
@count_object_columns > 0
1
select count(*) from information_schema.columns
where table_schema="performance_schema" and data_type="bigint"
   and column_name like "%object_instance_begin"
   and column_type like "%unsigned" into @count_object_unsigned;
select (@count_object_columns - @count_object_unsigned) = 0;
(@count_object_columns - @count_object_unsigned) = 0
1
