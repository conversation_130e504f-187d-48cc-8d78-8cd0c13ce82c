# Pre WL#15257 test using tmp table de-duplication
SET optimizer_switch='hash_set_operations=off';
CREATE TABLE t(a INT);
CREATE TABLE t1(a INT);
CREATE TABLE r(a INT);
INSERT INTO t VALUES (1),(2),(3);
INSERT INTO t1 VALUES (1),(2);
INSERT INTO r VALUES (2);
ANALYZE TABLE t, t1, r;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
test.t1	analyze	status	OK
test.r	analyze	status	OK
EXPLAIN FORMAT = tree
(SELECT * FROM t UNION SELECT * FROM t1)  UNION ALL (SELECT * FROM r);
EXPLAIN
-> Append  (rows=6)
    -> Stream results  (rows=5)
        -> Table scan on <union temporary>  (rows=5)
            -> Union materialize with deduplication  (rows=5)
                -> Table scan on t  (rows=3)
                -> Table scan on t1  (rows=2)
    -> Stream results  (rows=1)
        -> Table scan on r  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM t UNION SELECT * FROM t1)  UNION ALL SELECT * FROM r
UNION ALL SELECT * FROM t;
EXPLAIN
-> Append  (rows=9)
    -> Stream results  (rows=5)
        -> Table scan on <union temporary>  (rows=5)
            -> Union materialize with deduplication  (rows=5)
                -> Table scan on t  (rows=3)
                -> Table scan on t1  (rows=2)
    -> Stream results  (rows=1)
        -> Table scan on r  (rows=1)
    -> Stream results  (rows=3)
        -> Table scan on t  (rows=3)

EXPLAIN FORMAT = tree
(SELECT * FROM t UNION SELECT * FROM t1)  UNION ALL SELECT * FROM r
UNION DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=9)
    -> Union materialize with deduplication  (rows=9)
        -> Table scan on t  (rows=3)
        -> Table scan on t1  (rows=2)
        -> Table scan on r  (rows=1)
        -> Table scan on t  (rows=3)

EXPLAIN FORMAT = tree
(SELECT * FROM t UNION SELECT * FROM t1)  UNION ALL SELECT * FROM r
UNION ALL (SELECT * FROM t UNION DISTINCT SELECT * FROM r);
EXPLAIN
-> Append  (rows=10)
    -> Stream results  (rows=5)
        -> Table scan on <union temporary>  (rows=5)
            -> Union materialize with deduplication  (rows=5)
                -> Table scan on t  (rows=3)
                -> Table scan on t1  (rows=2)
    -> Stream results  (rows=1)
        -> Table scan on r  (rows=1)
    -> Stream results  (rows=4)
        -> Table scan on <union temporary>  (rows=4)
            -> Union materialize with deduplication  (rows=4)
                -> Table scan on t  (rows=3)
                -> Table scan on r  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM t UNION SELECT * FROM t1)  UNION ALL SELECT * FROM r
UNION ALL (SELECT * FROM t UNION ALL SELECT * FROM r);
EXPLAIN
-> Append  (rows=10)
    -> Stream results  (rows=5)
        -> Table scan on <union temporary>  (rows=5)
            -> Union materialize with deduplication  (rows=5)
                -> Table scan on t  (rows=3)
                -> Table scan on t1  (rows=2)
    -> Stream results  (rows=1)
        -> Table scan on r  (rows=1)
    -> Stream results  (rows=3)
        -> Table scan on t  (rows=3)
    -> Stream results  (rows=1)
        -> Table scan on r  (rows=1)

DROP TABLE t, t1, r;
#
# INTERSECT and EXCEPT support
#
CREATE TABLE r(a INT);
CREATE TABLE s(a INT);
CREATE TABLE t(a INT);
INSERT INTO r VALUES (1),(2),(3);
INSERT INTO s VALUES (1),(2);
INSERT INTO t VALUES (2);
ANALYZE TABLE r, s, t;
Table	Op	Msg_type	Msg_text
test.r	analyze	status	OK
test.s	analyze	status	OK
test.t	analyze	status	OK
#
# Test operator precedence
#
EXPLAIN FORMAT = tree
(SELECT * FROM r UNION ALL SELECT * FROM s) INTERSECT ALL SELECT * FROM t;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=1)
    -> Intersect all materialize  (rows=1)
        -> Table scan on <union temporary>  (rows=5)
            -> Union all materialize  (rows=5)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r UNION ALL SELECT * FROM s INTERSECT ALL SELECT * FROM t;
EXPLAIN
-> Append  (rows=4)
    -> Stream results  (rows=3)
        -> Table scan on r  (rows=3)
    -> Stream results  (rows=1)
        -> Table scan on <intersect temporary>  (rows=1)
            -> Intersect all materialize  (rows=1)
                -> Table scan on s  (rows=2)
                -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r EXCEPT ALL SELECT * FROM s) INTERSECT ALL SELECT * FROM t;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=1)
    -> Intersect all materialize  (rows=1)
        -> Table scan on <except temporary>  (rows=3)
            -> Except all materialize  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r EXCEPT ALL SELECT * FROM s INTERSECT ALL SELECT * FROM t;
EXPLAIN
-> Table scan on <except temporary>  (rows=3)
    -> Except all materialize  (rows=3)
        -> Table scan on r  (rows=3)
        -> Table scan on <intersect temporary>  (rows=1)
            -> Intersect all materialize  (rows=1)
                -> Table scan on s  (rows=2)
                -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r EXCEPT ALL SELECT * FROM s) UNION ALL SELECT * FROM t;
EXPLAIN
-> Append  (rows=4)
    -> Stream results  (rows=3)
        -> Table scan on <except temporary>  (rows=3)
            -> Except all materialize  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
    -> Stream results  (rows=1)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r EXCEPT ALL SELECT * FROM s UNION ALL SELECT * FROM t;
EXPLAIN
-> Append  (rows=4)
    -> Stream results  (rows=3)
        -> Table scan on <except temporary>  (rows=3)
            -> Except all materialize  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
    -> Stream results  (rows=1)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r UNION DISTINCT SELECT * FROM s) INTERSECT DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=1)
    -> Intersect materialize with deduplication  (rows=1)
        -> Table scan on <union temporary>  (rows=5)
            -> Union materialize with deduplication  (rows=5)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r UNION DISTINCT SELECT * FROM s INTERSECT DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=4)
    -> Union materialize with deduplication  (rows=4)
        -> Table scan on r  (rows=3)
        -> Table scan on <intersect temporary>  (rows=1)
            -> Intersect materialize with deduplication  (rows=1)
                -> Table scan on s  (rows=2)
                -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s) INTERSECT DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=1)
    -> Intersect materialize with deduplication  (rows=1)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s INTERSECT DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <except temporary>  (rows=3)
    -> Except materialize with deduplication  (rows=3)
        -> Table scan on r  (rows=3)
        -> Table scan on <intersect temporary>  (rows=1)
            -> Intersect materialize with deduplication  (rows=1)
                -> Table scan on s  (rows=2)
                -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s) UNION DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=4)
    -> Union materialize with deduplication  (rows=4)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s UNION DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=4)
    -> Union materialize with deduplication  (rows=4)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r UNION SELECT * FROM s) INTERSECT SELECT * FROM t;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=1)
    -> Intersect materialize with deduplication  (rows=1)
        -> Table scan on <union temporary>  (rows=5)
            -> Union materialize with deduplication  (rows=5)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r UNION SELECT * FROM s INTERSECT SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=4)
    -> Union materialize with deduplication  (rows=4)
        -> Table scan on r  (rows=3)
        -> Table scan on <intersect temporary>  (rows=1)
            -> Intersect materialize with deduplication  (rows=1)
                -> Table scan on s  (rows=2)
                -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r EXCEPT SELECT * FROM s) INTERSECT SELECT * FROM t;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=1)
    -> Intersect materialize with deduplication  (rows=1)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r EXCEPT SELECT * FROM s INTERSECT SELECT * FROM t;
EXPLAIN
-> Table scan on <except temporary>  (rows=3)
    -> Except materialize with deduplication  (rows=3)
        -> Table scan on r  (rows=3)
        -> Table scan on <intersect temporary>  (rows=1)
            -> Intersect materialize with deduplication  (rows=1)
                -> Table scan on s  (rows=2)
                -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r EXCEPT SELECT * FROM s) UNION SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=4)
    -> Union materialize with deduplication  (rows=4)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r EXCEPT SELECT * FROM s UNION SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=4)
    -> Union materialize with deduplication  (rows=4)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

#
# Bug#33905918 Server crashes after using explain of a query with except/intersect.
#
EXPLAIN SELECT * FROM r UNION ALL SELECT * FROM s;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	UNION	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` union all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN SELECT * FROM r EXCEPT ALL SELECT * FROM s;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	EXCEPT	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	EXCEPT RESULT	<except1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN SELECT * FROM r INTERSECT ALL SELECT * FROM s;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	INTERSECT	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	INTERSECT RESULT	<intersect1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` intersect all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN SELECT * FROM r UNION DISTINCT SELECT * FROM s;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	UNION	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	UNION RESULT	<union1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` union /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	EXCEPT	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	EXCEPT RESULT	<except1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN SELECT * FROM r INTERSECT DISTINCT SELECT * FROM s;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	INTERSECT	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	INTERSECT RESULT	<intersect1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` intersect /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r UNION ALL SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": false,
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "24"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "16"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` union all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r EXCEPT ALL SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "except_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<except1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "24"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "16"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r INTERSECT ALL SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "intersect_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<intersect1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "24"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "16"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` intersect all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r UNION DISTINCT SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<union1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "24"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "16"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` union /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "except_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<except1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "24"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "16"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r INTERSECT DISTINCT SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "intersect_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<intersect1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "24"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "16"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` intersect /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT=json SELECT * FROM (SELECT 1 INTERSECT SELECT 2) AS dt;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "no matching row in const table",
    "table": {
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "intersect_result": {
            "using_temporary_table": true,
            "select_id": 4,
            "table_name": "<intersect2,3>",
            "access_type": "ALL",
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 2,
                  "message": "No tables used"
                }
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "message": "No tables used"
                }
              }
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select NULL AS `1` from (/* select#2 */ select 1 AS `1` intersect /* select#3 */ select 2 AS `2`) `dt`
EXPLAIN FORMAT=json SELECT * FROM (SELECT 1 EXCEPT SELECT 2) AS dt;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "elided"
    },
    "table": {
      "table_name": "dt",
      "access_type": "system",
      "rows_examined_per_scan": 1,
      "rows_produced_per_join": 1,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "elided",
        "eval_cost": "elided",
        "prefix_cost": "elided",
        "data_read_per_join": "32"
      },
      "used_columns": [
        "<hash_field>",
        "<set counter>",
        "1"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "except_result": {
            "using_temporary_table": true,
            "select_id": 4,
            "table_name": "<except2,3>",
            "access_type": "ALL",
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 2,
                  "message": "No tables used"
                }
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "message": "No tables used"
                }
              }
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select '1' AS `1` from dual
EXPLAIN (SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s) UNION DISTINCT SELECT * FROM t;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	EXCEPT	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
4	EXCEPT RESULT	<except1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
3	UNION	t	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
5	UNION RESULT	<union4,3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	(/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`) union /* select#3 */ select `test`.`t`.`a` AS `a` from `test`.`t`
EXPLAIN (SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s) UNION DISTINCT
(SELECT * FROM t INTERSECT DISTINCT SELECT * FROM t);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	EXCEPT	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
5	EXCEPT RESULT	<except1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
3	PRIMARY	t	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
4	INTERSECT	t	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
6	INTERSECT RESULT	<intersect3,4>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
7	UNION RESULT	<union5,6>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	(/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`) union (/* select#3 */ select `test`.`t`.`a` AS `a` from `test`.`t` intersect /* select#4 */ select `test`.`t`.`a` AS `a` from `test`.`t`)
EXPLAIN FORMAT = json
(SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s) UNION DISTINCT SELECT * FROM t;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": true,
      "select_id": 5,
      "table_name": "<union4,3>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "except_result": {
            "using_temporary_table": true,
            "select_id": 4,
            "table_name": "<except1,2>",
            "access_type": "ALL",
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 1,
                  "cost_info": {
                    "query_cost": "elided"
                  },
                  "table": {
                    "table_name": "r",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 3,
                    "rows_produced_per_join": 3,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "elided",
                      "eval_cost": "elided",
                      "prefix_cost": "elided",
                      "data_read_per_join": "24"
                    },
                    "used_columns": [
                      "a"
                    ]
                  }
                }
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 2,
                  "cost_info": {
                    "query_cost": "elided"
                  },
                  "table": {
                    "table_name": "s",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 2,
                    "rows_produced_per_join": 2,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "elided",
                      "eval_cost": "elided",
                      "prefix_cost": "elided",
                      "data_read_per_join": "16"
                    },
                    "used_columns": [
                      "a"
                    ]
                  }
                }
              }
            ]
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 3,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "t",
              "access_type": "ALL",
              "rows_examined_per_scan": 1,
              "rows_produced_per_join": 1,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "8"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	(/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`) union /* select#3 */ select `test`.`t`.`a` AS `a` from `test`.`t`
EXPLAIN FORMAT = json
(SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s) UNION DISTINCT
(SELECT * FROM t INTERSECT DISTINCT SELECT * FROM t);
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": true,
      "select_id": 7,
      "table_name": "<union5,6>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "except_result": {
            "using_temporary_table": true,
            "select_id": 5,
            "table_name": "<except1,2>",
            "access_type": "ALL",
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 1,
                  "cost_info": {
                    "query_cost": "elided"
                  },
                  "table": {
                    "table_name": "r",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 3,
                    "rows_produced_per_join": 3,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "elided",
                      "eval_cost": "elided",
                      "prefix_cost": "elided",
                      "data_read_per_join": "24"
                    },
                    "used_columns": [
                      "a"
                    ]
                  }
                }
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 2,
                  "cost_info": {
                    "query_cost": "elided"
                  },
                  "table": {
                    "table_name": "s",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 2,
                    "rows_produced_per_join": 2,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "elided",
                      "eval_cost": "elided",
                      "prefix_cost": "elided",
                      "data_read_per_join": "16"
                    },
                    "used_columns": [
                      "a"
                    ]
                  }
                }
              }
            ]
          }
        },
        {
          "intersect_result": {
            "using_temporary_table": true,
            "select_id": 6,
            "table_name": "<intersect3,4>",
            "access_type": "ALL",
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "cost_info": {
                    "query_cost": "elided"
                  },
                  "table": {
                    "table_name": "t",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 1,
                    "rows_produced_per_join": 1,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "elided",
                      "eval_cost": "elided",
                      "prefix_cost": "elided",
                      "data_read_per_join": "8"
                    },
                    "used_columns": [
                      "a"
                    ]
                  }
                }
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 4,
                  "cost_info": {
                    "query_cost": "elided"
                  },
                  "table": {
                    "table_name": "t",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 1,
                    "rows_produced_per_join": 1,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "elided",
                      "eval_cost": "elided",
                      "prefix_cost": "elided",
                      "data_read_per_join": "8"
                    },
                    "used_columns": [
                      "a"
                    ]
                  }
                }
              }
            ]
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	(/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`) union (/* select#3 */ select `test`.`t`.`a` AS `a` from `test`.`t` intersect /* select#4 */ select `test`.`t`.`a` AS `a` from `test`.`t`)
DROP TABLE r, s, t;
#
# Check that more set ops don't interfere with
# WITH RECURSIVE
#
WITH RECURSIVE qn AS
(SELECT 1 AS n, 1 AS un, 1 AS unp1 EXCEPT ALL
SELECT 1+n, unp1, un+unp1 FROM qn WHERE n<10)
SELECT * FROM qn;
ERROR HY000: Recursive Common Table Expression 'qn' should contain a UNION
WITH RECURSIVE qn AS
(SELECT 1 AS n, 1 AS un, 1 AS unp1 INTERSECT ALL
SELECT 1+n, unp1, un+unp1 FROM qn WHERE n<10)
SELECT * FROM qn;
ERROR HY000: Recursive Common Table Expression 'qn' should contain a UNION
CREATE TABLE t(n int, un INT, unp1 INT);
INSERT INTO t VALUES (1, 1, 1);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
Non-union set op in seed works
WITH RECURSIVE qn AS
( SELECT * FROM t INTERSECT
VALUES ROW(1, 1, 1) UNION ALL
SELECT 1+n, unp1, un+unp1 FROM qn WHERE n<10)
SELECT * FROM qn;
n	un	unp1
1	1	1
2	1	2
3	2	3
4	3	5
5	5	8
6	8	13
7	13	21
8	21	34
9	34	55
10	55	89
WITH RECURSIVE qn AS
( SELECT * FROM t UNION ALL
(SELECT 1,1,1 INTERSECT SELECT 1+n, unp1, un+unp1 FROM qn WHERE n < 20)) SELECT * FROM qn;
ERROR HY000: Recursive table reference in EXCEPT or INTERSECT operand is not allowed.
WITH RECURSIVE qn AS
( SELECT * FROM t UNION ALL
(SELECT 1+n, unp1, un+unp1 FROM qn WHERE n<10 INTERSECT SELECT 1,1,1)) SELECT * FROM qn;
ERROR HY000: Recursive table reference in EXCEPT or INTERSECT operand is not allowed.
WITH RECURSIVE cte AS
( ( (SELECT 1 AS n UNION SELECT 2) EXCEPT SELECT 2)  UNION ALL
SELECT n+1  FROM cte WHERE n<100)
SELECT n FROM cte LIMIT 10;
n
1
2
3
4
5
6
7
8
9
10
DROP TABLE t;
CREATE TABLE t1(i INT);
CREATE TABLE t2(i INT);
CREATE TABLE t3(i INT);
INSERT INTO t1 VALUES (1),(1),(1);
INSERT INTO t2 VALUES (2),(2),(1),(1);
INSERT INTO t3 VALUES (2),(3),(3),(1),(1);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
# DISTINCT. Note: a mix of ALL and DISTINCT also gives all DISTINCT
SELECT * FROM t1 INTERSECT DISTINCT SELECT * FROM t2;
i
1
SELECT * FROM t2 INTERSECT DISTINCT SELECT * FROM t1;
i
1
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3;
i
1
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3;
i
1
1
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2;
i
1
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2;
i
1
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1;
i
1
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
i
1
1
SELECT * FROM t1 INTERSECT DISTINCT SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3;
i
1
SELECT * FROM t2 INTERSECT DISTINCT SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3;
i
1
SELECT * FROM t1 INTERSECT DISTINCT SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2;
i
1
SELECT * FROM t3 INTERSECT DISTINCT SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2;
i
1
SELECT * FROM t2 INTERSECT DISTINCT SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1;
i
1
SELECT * FROM t3 INTERSECT DISTINCT SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
i
1
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2 INTERSECT DISTINCT SELECT * FROM t3;
i
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1 INTERSECT DISTINCT SELECT * FROM t3;
i
1
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3 INTERSECT DISTINCT SELECT * FROM t2;
i
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1 INTERSECT DISTINCT SELECT * FROM t2;
i
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3 INTERSECT DISTINCT SELECT * FROM t1;
i
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT DISTINCT SELECT * FROM t1;
i
1
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3;
i
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3;
i
2
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2;
i
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2;
i
3
3
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1;
i
2
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1;
i
3
3
SELECT * FROM t1 EXCEPT    DISTINCT SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3;
i
SELECT * FROM t2 EXCEPT    DISTINCT SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3;
i
SELECT * FROM t1 EXCEPT    DISTINCT SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2;
i
SELECT * FROM t3 EXCEPT    DISTINCT SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2;
i
3
SELECT * FROM t2 EXCEPT    DISTINCT SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1;
i
SELECT * FROM t3 EXCEPT    DISTINCT SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1;
i
3
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2 EXCEPT    DISTINCT SELECT * FROM t3;
i
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1 EXCEPT    DISTINCT SELECT * FROM t3;
i
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3 EXCEPT    DISTINCT SELECT * FROM t2;
i
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1 EXCEPT    DISTINCT SELECT * FROM t2;
i
3
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3 EXCEPT    DISTINCT SELECT * FROM t1;
i
2
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2 EXCEPT    DISTINCT SELECT * FROM t1;
i
3
# ALL
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2;
i
1
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
i
1
1
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3;
i
1
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1;
i
1
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3;
i
1
1
2
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2;
i
1
1
2
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2;
i
1
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1;
i
2
2
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3;
i
1
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1;
i
2
3
3
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3;
i
2
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2;
i
3
3
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3;
i
1
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3;
i
1
1
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2;
i
1
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2;
i
1
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1;
i
1
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
i
1
1
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3;
i
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3;
i
2
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2;
i
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2;
i
3
3
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1;
i
2
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1;
i
3
3
TRUNCATE t1;
TRUNCATE t2;
TRUNCATE t3;
INSERT INTO t3 VALUES (3),(3),(2),(2),(1),(1);
INSERT INTO t2 VALUES (2),(1),(1);
INSERT INTO t1 VALUES (1);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
# EXCEPT is not right associative, so make a right parentheses
# nest to evaluate it first if wanted. Also test mix of
# DISTINCT & ALL.
#
# DISTINCT
#
SELECT * FROM t3 EXCEPT SELECT * FROM t2 EXCEPT SELECT * FROM t1;
i
3
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT SELECT * FROM t2 EXCEPT SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

(SELECT * FROM t3 EXCEPT SELECT * FROM t2) EXCEPT SELECT * FROM t1;
i
3
EXPLAIN FORMAT=tree (SELECT * FROM t3 EXCEPT SELECT * FROM t2) EXCEPT SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

SELECT * FROM t3 EXCEPT (SELECT * FROM t2 EXCEPT SELECT * FROM t1);
i
1
3
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT (SELECT * FROM t2 EXCEPT SELECT * FROM t1);
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on t2  (rows=3)
                -> Table scan on t1  (rows=1)

#
# ALL
#
SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1;
i
3
3
2
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except all materialize  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

(SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2) EXCEPT ALL SELECT * FROM t1;
i
3
3
2
EXPLAIN FORMAT=tree (SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2) EXCEPT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except all materialize  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

SELECT * FROM t3 EXCEPT ALL (SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1);
i
1
2
3
3
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT ALL (SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1);
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except all materialize  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on <except temporary>  (rows=3)
            -> Except all materialize  (rows=3)
                -> Table scan on t2  (rows=3)
                -> Table scan on t1  (rows=1)

#
# Mix of DISTINCT and ALL
#
SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2 EXCEPT SELECT * FROM t1;
i
3
2
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2 EXCEPT SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Disable deduplication
            -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

(SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2) EXCEPT SELECT * FROM t1;
i
3
2
EXPLAIN FORMAT=tree (SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2) EXCEPT SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Disable deduplication
            -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

SELECT * FROM t3 EXCEPT ALL (SELECT * FROM t2 EXCEPT SELECT * FROM t1);
i
1
1
2
3
3
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT ALL (SELECT * FROM t2 EXCEPT SELECT * FROM t1);
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except all materialize  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on t2  (rows=3)
                -> Table scan on t1  (rows=1)

SELECT * FROM t3 EXCEPT SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1;
i
3
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

(SELECT * FROM t3 EXCEPT SELECT * FROM t2) EXCEPT ALL SELECT * FROM t1;
i
3
EXPLAIN FORMAT=tree (SELECT * FROM t3 EXCEPT SELECT * FROM t2) EXCEPT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

SELECT * FROM t3 EXCEPT (SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1);
i
3
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT (SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1);
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on <except temporary>  (rows=3)
            -> Except all materialize  (rows=3)
                -> Table scan on t2  (rows=3)
                -> Table scan on t1  (rows=1)

TRUNCATE t1;
TRUNCATE t2;
TRUNCATE t3;
INSERT INTO t3 VALUES (3),(3),(2),(2),(1),(1);
INSERT INTO t2 VALUES (2),(2),(1),(1);
INSERT INTO t1 VALUES (1),(1);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
# INTERSECT is left and right associative. If evaluation is
# INTERSECT ALL, each operation can only handle two operands,
# so for N operands we must set up N-1 operation nodes. If
# evaluation is INTERSECT UNIQUE, collapse all operands and use
# a single operation node. The restriction for INTERSECT ALL is
# due to the fact we need an extra read pass to check counters
# after each right-hand operand has been processed.  Also, test
# mix of DISTINCT & ALL: DISTINCT wins always.
#
# DISTINCT
#
SELECT * FROM t3 INTERSECT SELECT * FROM t2 INTERSECT SELECT * FROM t1;
i
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT SELECT * FROM t2 INTERSECT SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

(SELECT * FROM t3 INTERSECT SELECT * FROM t2) INTERSECT SELECT * FROM t1;
i
1
EXPLAIN FORMAT=tree (SELECT * FROM t3 INTERSECT SELECT * FROM t2) INTERSECT SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

SELECT * FROM t3 INTERSECT (SELECT * FROM t2 INTERSECT SELECT * FROM t1);
i
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT (SELECT * FROM t2 INTERSECT SELECT * FROM t1);
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

#
# ALL
#
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
i
1
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect all materialize  (rows=2)
        -> Table scan on <intersect temporary>  (rows=4)
            -> Intersect all materialize  (rows=4)
                -> Table scan on t3  (rows=6)
                -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

(SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2) INTERSECT ALL SELECT * FROM t1;
i
1
1
EXPLAIN FORMAT=tree (SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2) INTERSECT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect all materialize  (rows=2)
        -> Table scan on <intersect temporary>  (rows=4)
            -> Intersect all materialize  (rows=4)
                -> Table scan on t3  (rows=6)
                -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

SELECT * FROM t3 INTERSECT ALL (SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1);
i
1
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT ALL (SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1);
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect all materialize  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on <intersect temporary>  (rows=2)
            -> Intersect all materialize  (rows=2)
                -> Table scan on t2  (rows=4)
                -> Table scan on t1  (rows=2)

#
# Mix of DISTINCT and ALL
#
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT SELECT * FROM t1;
i
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

(SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2) INTERSECT SELECT * FROM t1;
i
1
EXPLAIN FORMAT=tree (SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2) INTERSECT SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

SELECT * FROM t3 INTERSECT ALL (SELECT * FROM t2 INTERSECT SELECT * FROM t1);
i
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT ALL (SELECT * FROM t2 INTERSECT SELECT * FROM t1);
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

SELECT * FROM t3 INTERSECT SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
i
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

(SELECT * FROM t3 INTERSECT SELECT * FROM t2) INTERSECT ALL SELECT * FROM t1;
i
1
EXPLAIN FORMAT=tree (SELECT * FROM t3 INTERSECT SELECT * FROM t2) INTERSECT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

SELECT * FROM t3 INTERSECT (SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1);
i
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT (SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1);
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

SELECT * FROM (SELECT * FROM t3 UNION DISTINCT SELECT * FROM t3 UNION ALL SELECT * FROM t3) x1
INTERSECT
SELECT * FROM (SELECT * FROM t3 UNION DISTINCT SELECT * FROM t3 UNION ALL SELECT * FROM t3) x2
WHERE i > 1 ORDER BY i;
i
2
3
EXPLAIN FORMAT=tree SELECT * FROM (SELECT * FROM t3 UNION DISTINCT SELECT * FROM t3 UNION ALL SELECT * FROM t3) x1
INTERSECT
SELECT * FROM (SELECT * FROM t3 UNION DISTINCT SELECT * FROM t3 UNION ALL SELECT * FROM t3) x2
WHERE i > 1 ORDER BY i;
EXPLAIN
-> Sort: i  (rows=6)
    -> Table scan on <intersect temporary>  (rows=6)
        -> Intersect materialize with deduplication  (rows=6)
            -> Table scan on x1  (rows=18)
                -> Union materialize with deduplication  (rows=18)
                    -> Table scan on t3  (rows=6)
                    -> Table scan on t3  (rows=6)
                    -> Disable deduplication
                        -> Table scan on t3  (rows=6)
            -> Table scan on x2  (rows=6)
                -> Union materialize with deduplication  (rows=6)
                    -> Filter: (t3.i > 1)  (rows=2)
                        -> Table scan on t3  (rows=6)
                    -> Filter: (t3.i > 1)  (rows=2)
                        -> Table scan on t3  (rows=6)
                    -> Disable deduplication
                        -> Filter: (t3.i > 1)  (rows=2)
                            -> Table scan on t3  (rows=6)

SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 ORDER BY i) x1 WHERE i < 5;
i
1
2
EXPLAIN FORMAT=tree SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 ORDER BY i) x1 WHERE i < 5;
EXPLAIN
-> Table scan on x1  (rows=1.33)
    -> Materialize  (rows=1.33)
        -> Sort: i  (rows=1.33)
            -> Table scan on <intersect temporary>  (rows=1.33)
                -> Intersect materialize with deduplication  (rows=1.33)
                    -> Filter: (t2.i < 5)  (rows=1.33)
                        -> Table scan on t2  (rows=4)
                    -> Filter: (t2.i < 5)  (rows=1.33)
                        -> Table scan on t2  (rows=4)

#
# Test LIMIT, OFFSET
#
SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1;
i
CORRECT
EXPLAIN FORMAT=tree SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1;
EXPLAIN
-> Limit: 1 row(s)  (rows=1)
    -> Table scan on <intersect temporary>  (rows=4)
        -> Intersect materialize with deduplication  (rows=4)
            -> Table scan on t2  (rows=4)
            -> Table scan on t2  (rows=4)

SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1 OFFSET 1;
i
CORRECT
EXPLAIN FORMAT=tree SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1 OFFSET 1;
EXPLAIN
-> Limit/Offset: 1/1 row(s)  (rows=1)
    -> Table scan on <intersect temporary>  (rows=4)
        -> Intersect materialize with deduplication  (rows=4)
            -> Table scan on t2  (rows=4)
            -> Table scan on t2  (rows=4)

#
# LIMIT, OFFSET with derived table
#
SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1) t;
i
CORRECT
EXPLAIN FORMAT=tree SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1) t;
EXPLAIN
-> Table scan on t  (rows=4)
    -> Intersect materialize with deduplication  (rows=4)
        -> Table scan on t2  (rows=4)
        -> Table scan on t2  (rows=4)

SELECT * FROM (SELECT i FROM t1 INTERSECT SELECT i FROM t3 LIMIT 1) t;
i
1
EXPLAIN FORMAT=tree SELECT * FROM (SELECT i FROM t1 INTERSECT SELECT i FROM t3 LIMIT 1) t;
EXPLAIN
-> Table scan on t  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t1  (rows=2)
        -> Table scan on t3  (rows=6)

SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2) t LIMIT 1;
i
CORRECT
EXPLAIN FORMAT=tree SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2) t LIMIT 1;
EXPLAIN
-> Limit: 1 row(s)  (rows=1)
    -> Table scan on t  (rows=4)
        -> Intersect materialize with deduplication  (rows=4)
            -> Table scan on t2  (rows=4)
            -> Table scan on t2  (rows=4)

SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1 OFFSET 1) t;
i
CORRECT
EXPLAIN FORMAT=tree SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1 OFFSET 1) t;
EXPLAIN
-> Limit/Offset: 1/1 row(s)  (rows=1)
    -> Table scan on t  (rows=4)
        -> Intersect materialize with deduplication  (rows=4)
            -> Table scan on t2  (rows=4)
            -> Table scan on t2  (rows=4)

SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2) t LIMIT 1 OFFSET 1 ;
i
CORRECT
EXPLAIN FORMAT=tree SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2) t LIMIT 1 OFFSET 1 ;
EXPLAIN
-> Limit/Offset: 1/1 row(s)  (rows=1)
    -> Table scan on t  (rows=4)
        -> Intersect materialize with deduplication  (rows=4)
            -> Table scan on t2  (rows=4)
            -> Table scan on t2  (rows=4)

SELECT SQL_CALC_FOUND_ROWS i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1;
i
CORRECT
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
2
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
SELECT SQL_CALC_FOUND_ROWS * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2) t LIMIT 1;
i
CORRECT
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
2
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
SELECT SQL_CALC_FOUND_ROWS * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1) t;
i
CORRECT
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
1
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
(SELECT i FROM t1 WHERE i > 100) INTERSECT SELECT i FROM t2;
i
EXPLAIN analyze (SELECT i FROM t1 WHERE i > 100) INTERSECT SELECT i FROM t2;
EXPLAIN
-> Table scan on <intersect temporary>  (...) (actual rows=0 loops=1)
    -> Intersect materialize with deduplication  (...) (actual rows=0 loops=1)
        -> Filter: (t1.i > 100)  (...) (actual rows=0 loops=1)
            -> Table scan on t1  (...) (actual rows=2 loops=1)
        -> Table scan on t2  (...) (never executed)

(SELECT i FROM t1 LIMIT 0) INTERSECT SELECT i FROM t2;
i
EXPLAIN analyze (SELECT i FROM t1 LIMIT 0) INTERSECT SELECT i FROM t2;
EXPLAIN
-> Table scan on <intersect temporary>  (...) (actual rows=0 loops=1)
    -> Intersect materialize with deduplication  (...) (actual rows=0 loops=1)
        -> Zero rows (Zero limit)  (...) (actual rows=0 loops=1)
        -> Table scan on t2  (...) (never executed)

DROP TABLE t1, t2, t3;
#
# Test of T101 "enhanced nullability determination". To interpret results
# read SQL 2014, Vol 2. section 7.17 <query expression>, SR 18 and 20.
#
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 UNION SELECT 2,2;
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 UNION SELECT null, null;
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 UNION SELECT 2,2;
CREATE TABLE t4 AS SELECT null AS c1, null AS c2 UNION SELECT null, null;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t4;
Table	Create Table
t4	CREATE TABLE `t4` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3, t4;
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 INTERSECT SELECT 2,2;
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 INTERSECT SELECT null, null;
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 INTERSECT SELECT 2,2;
CREATE TABLE t4 AS SELECT null AS c1, null AS c2 INTERSECT SELECT null, null;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t4;
Table	Create Table
t4	CREATE TABLE `t4` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3, t4;
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 EXCEPT SELECT 2,2;
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 EXCEPT SELECT null, null;
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 EXCEPT SELECT 2,2;
CREATE TABLE t4 AS SELECT null AS c1, null AS c2 EXCEPT SELECT null, null;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t4;
Table	Create Table
t4	CREATE TABLE `t4` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3, t4;
# Test T101 hierarchy correctness
# EXCEPT on top, UNION in its right side
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 EXCEPT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT null, null);
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 EXCEPT
(SELECT null AS c1, null AS c2 UNION SELECT null, null);
CREATE TABLE t3 AS SELECT 1 AS c1, 1 AS c2 EXCEPT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
CREATE TABLE t1 AS SELECT null AS c1, null AS c2 EXCEPT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT null, null);
CREATE TABLE t2 AS SELECT null AS c1, null AS c2 EXCEPT
(SELECT null AS c1, null AS c2 UNION SELECT null, null);
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 EXCEPT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
# INTERSECT on top, UNION in its right side
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 INTERSECT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT null, null);
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 INTERSECT
(SELECT null AS c1, null AS c2 UNION SELECT null, null);
CREATE TABLE t3 AS SELECT 1 AS c1, 1 AS c2 INTERSECT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
CREATE TABLE t1 AS SELECT null AS c1, null AS c2 INTERSECT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT null, null);
CREATE TABLE t2 AS SELECT null AS c1, null AS c2 INTERSECT
(SELECT null AS c1, null AS c2 UNION SELECT null, null);
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 INTERSECT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
# UNION on top, INTERSECT in its right side
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 INTERSECT SELECT null, null);
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 UNION
(SELECT null AS c1, null AS c2 INTERSECT SELECT null, null);
CREATE TABLE t3 AS SELECT 1 AS c1, 1 AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 INTERSECT SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
CREATE TABLE t1 AS SELECT null AS c1, null AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 INTERSECT SELECT null, null);
CREATE TABLE t2 AS SELECT null AS c1, null AS c2 UNION
(SELECT null AS c1, null AS c2 INTERSECT SELECT null, null);
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 INTERSECT SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
# UNION on top, EXCEPT in its right side
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 EXCEPT SELECT null, null);
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 UNION
(SELECT null AS c1, null AS c2 EXCEPT SELECT null, null);
CREATE TABLE t3 AS SELECT 1 AS c1, 1 AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 EXCEPT SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
CREATE TABLE t1 AS SELECT null AS c1, null AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 EXCEPT SELECT null, null);
CREATE TABLE t2 AS SELECT null AS c1, null AS c2 UNION
(SELECT null AS c1, null AS c2 EXCEPT SELECT null, null);
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 EXCEPT SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
# T101 for unary
CREATE TABLE t1 AS ((VALUES ROW (1, 1), ROW (2,2) ORDER BY column_0 LIMIT 2)
ORDER BY column_1 LIMIT 1);
CREATE TABLE t2 AS ((VALUES ROW (null, null), ROW (2,2)
ORDER BY column_0 LIMIT 2) ORDER BY column_1 LIMIT 1);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `column_0` bigint NOT NULL DEFAULT '0',
  `column_1` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `column_0` bigint DEFAULT NULL,
  `column_1` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2;
#
# Row count estimates. For UNION, the optimizer adds the
# numbers of rows for the operands, (10, the worst case in
# example below). For INTERSECT, the estimate should be the
# lowest estimate of the operands (3). For EXCEPT it should be
# estimate of the left operand (7, worst case no rows are
# removed from the set).
#
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES (1), (1), (2), (3), (2), (3), (3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN FORMAT=tree
SELECT * FROM t1 UNION     (SELECT * FROM t1 ORDER BY 1 LIMIT 3);
EXPLAIN
-> Table scan on <union temporary>  (rows=10)
    -> Union materialize with deduplication  (rows=10)
        -> Table scan on t1  (rows=7)
        -> Limit: 3 row(s)  (rows=3)
            -> Sort: t1.a, limit input to 3 row(s) per chunk  (rows=7)
                -> Table scan on t1  (rows=7)

EXPLAIN FORMAT=tree
SELECT * FROM t1 INTERSECT (SELECT * FROM t1 ORDER BY 1 LIMIT 3);
EXPLAIN
-> Table scan on <intersect temporary>  (rows=3)
    -> Intersect materialize with deduplication  (rows=3)
        -> Table scan on t1  (rows=7)
        -> Limit: 3 row(s)  (rows=3)
            -> Sort: t1.a, limit input to 3 row(s) per chunk  (rows=7)
                -> Table scan on t1  (rows=7)

EXPLAIN FORMAT=tree
SELECT * FROM t1 EXCEPT    (SELECT * FROM t1 ORDER BY 1 LIMIT 3);
EXPLAIN
-> Table scan on <except temporary>  (rows=7)
    -> Except materialize with deduplication  (rows=7)
        -> Table scan on t1  (rows=7)
        -> Limit: 3 row(s)  (rows=3)
            -> Sort: t1.a, limit input to 3 row(s) per chunk  (rows=7)
                -> Table scan on t1  (rows=7)

DROP TABLE t1;
#
# Bug#34843764 Wrong Results when INTERSECT/EXCEPT combine with JOIN
#
CREATE TABLE t1 (a INT, b INT);
INSERT INTO t1 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5), (6, 5);
INSERT INTO t1 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5), (6, 5);
INSERT INTO t1 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5), (6, 5);
CREATE TABLE t2 (a INT, b INT);
INSERT INTO t2 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5), (6, 5), (7, 5), (8, 9);
INSERT INTO t2 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5), (6, 5), (7, 5), (8, 9);
CREATE TABLE t3 (a INT, b INT);
INSERT INTO t3 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5);
INSERT INTO t3 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
SELECT *
FROM (SELECT a, b FROM t1 INTERSECT SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
a	b	a	b
2	3	2	3
2	3	2	3
4	5	4	5
4	5	4	5
EXPLAIN FORMAT=tree SELECT *
FROM (SELECT a, b FROM t1 INTERSECT SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
EXPLAIN
-> Sort: x.a, x.b
    -> Stream results  (rows=14.9)
        -> Inner hash join (x.b = t2.b), (x.a = t2.a)  (rows=14.9)
            -> Table scan on x  (rows=8)
                -> Intersect materialize with deduplication  (rows=8)
                    -> Table scan on t1  (rows=15)
                    -> Table scan on t3  (rows=8)
            -> Hash
                -> Table scan on t2  (rows=14)

SELECT *
FROM (SELECT a, b FROM t1 INTERSECT ALL SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
a	b	a	b
2	3	2	3
2	3	2	3
2	3	2	3
2	3	2	3
4	5	4	5
4	5	4	5
4	5	4	5
4	5	4	5
EXPLAIN FORMAT=tree SELECT *
FROM (SELECT a, b FROM t1 INTERSECT ALL SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
EXPLAIN
-> Sort: x.a, x.b
    -> Stream results  (rows=14.9)
        -> Inner hash join (x.b = t2.b), (x.a = t2.a)  (rows=14.9)
            -> Table scan on x  (rows=8)
                -> Intersect all materialize  (rows=8)
                    -> Table scan on t1  (rows=15)
                    -> Table scan on t3  (rows=8)
            -> Hash
                -> Table scan on t2  (rows=14)

SELECT *
FROM (SELECT a, b FROM t1 EXCEPT SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
a	b	a	b
6	5	6	5
6	5	6	5
EXPLAIN FORMAT=tree SELECT *
FROM (SELECT a, b FROM t1 EXCEPT SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
EXPLAIN
-> Sort: x.a, x.b
    -> Stream results  (rows=28)
        -> Inner hash join (x.b = t2.b), (x.a = t2.a)  (rows=28)
            -> Table scan on x  (rows=15)
                -> Except materialize with deduplication  (rows=15)
                    -> Table scan on t1  (rows=15)
                    -> Table scan on t3  (rows=8)
            -> Hash
                -> Table scan on t2  (rows=14)

SELECT *
FROM (SELECT a, b FROM t1 EXCEPT ALL SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
a	b	a	b
2	3	2	3
2	3	2	3
4	5	4	5
4	5	4	5
6	5	6	5
6	5	6	5
6	5	6	5
6	5	6	5
6	5	6	5
6	5	6	5
EXPLAIN FORMAT=tree SELECT *
FROM (SELECT a, b FROM t1 EXCEPT ALL SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
EXPLAIN
-> Sort: x.a, x.b
    -> Stream results  (rows=28)
        -> Inner hash join (x.b = t2.b), (x.a = t2.a)  (rows=28)
            -> Table scan on x  (rows=15)
                -> Except all materialize  (rows=15)
                    -> Table scan on t1  (rows=15)
                    -> Table scan on t3  (rows=8)
            -> Hash
                -> Table scan on t2  (rows=14)

DROP TABLE t1, t2, t3;
#
# Bug#34704011 When using a single select in parenthesis, a
#              global order by cannot be used
#
CREATE TABLE t1 (id INT PRIMARY KEY AUTO_INCREMENT, d DATE);
CREATE TABLE t2 (id INT PRIMARY KEY AUTO_INCREMENT, d DATE);
INSERT INTO t1 (d) VALUES ('2020-01-01'), ('2021-04-21'), ('2022-03-02');
INSERT INTO t2 (d) VALUES ('2020-05-01'), ('2021-05-21'), ('2022-05-02');
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
( SELECT t1.d, t2.d
FROM t1 INNER JOIN t2 USING (id) )
ORDER BY t1.d;
d	d
2020-01-01	2020-05-01
2021-04-21	2021-05-21
2022-03-02	2022-05-02
(( SELECT t1.d, t2.d
FROM t1 INNER JOIN t2 USING (id) ))
ORDER BY t1.d;
d	d
2020-01-01	2020-05-01
2021-04-21	2021-05-21
2022-03-02	2022-05-02
(( SELECT t1.d, t2.d
FROM t1 INNER JOIN t2 USING (id) ) LIMIT 1)
ORDER BY t1.d;
ERROR 42S22: Unknown column 't1.d' in 'order clause'
DROP TABLE t1, t2;
#
# Follow-up to Bug#36739383 Assertion 'false'
# materialize_iterator::SpillState::read_next_row_secondary_over.
# This is a case of too large a row for default hash table
# space, which next also overflows the normal tmp table (error
# 135 HA_ERR_RECORD_FILE_FULL) and must go to InnoDB tmp table.
#
CREATE TABLE t1 (a MEDIUMTEXT);
INSERT INTO t1 VALUES ('a');
CREATE TABLE t2 AS
SELECT REPEAT(a,20000000) AS a FROM t1
INTERSECT
SELECT REPEAT(a,20000000) AS a FROM t1;
SELECT LENGTH(a), SUBSTRING(a FROM 20000000-4 FOR 4) FROM t2;
LENGTH(a)	SUBSTRING(a FROM 20000000-4 FOR 4)
20000000	aaaa
DROP TABLE t1, t2;
# Pre WL#15257 tests using hash table of WL#15257
SET optimizer_switch='hash_set_operations=default';
CREATE TABLE t(a INT);
CREATE TABLE t1(a INT);
CREATE TABLE r(a INT);
INSERT INTO t VALUES (1),(2),(3);
INSERT INTO t1 VALUES (1),(2);
INSERT INTO r VALUES (2);
ANALYZE TABLE t, t1, r;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
test.t1	analyze	status	OK
test.r	analyze	status	OK
EXPLAIN FORMAT = tree
(SELECT * FROM t UNION SELECT * FROM t1)  UNION ALL (SELECT * FROM r);
EXPLAIN
-> Append  (rows=6)
    -> Stream results  (rows=5)
        -> Table scan on <union temporary>  (rows=5)
            -> Union materialize with deduplication  (rows=5)
                -> Table scan on t  (rows=3)
                -> Table scan on t1  (rows=2)
    -> Stream results  (rows=1)
        -> Table scan on r  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM t UNION SELECT * FROM t1)  UNION ALL SELECT * FROM r
UNION ALL SELECT * FROM t;
EXPLAIN
-> Append  (rows=9)
    -> Stream results  (rows=5)
        -> Table scan on <union temporary>  (rows=5)
            -> Union materialize with deduplication  (rows=5)
                -> Table scan on t  (rows=3)
                -> Table scan on t1  (rows=2)
    -> Stream results  (rows=1)
        -> Table scan on r  (rows=1)
    -> Stream results  (rows=3)
        -> Table scan on t  (rows=3)

EXPLAIN FORMAT = tree
(SELECT * FROM t UNION SELECT * FROM t1)  UNION ALL SELECT * FROM r
UNION DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=9)
    -> Union materialize with deduplication  (rows=9)
        -> Table scan on t  (rows=3)
        -> Table scan on t1  (rows=2)
        -> Table scan on r  (rows=1)
        -> Table scan on t  (rows=3)

EXPLAIN FORMAT = tree
(SELECT * FROM t UNION SELECT * FROM t1)  UNION ALL SELECT * FROM r
UNION ALL (SELECT * FROM t UNION DISTINCT SELECT * FROM r);
EXPLAIN
-> Append  (rows=10)
    -> Stream results  (rows=5)
        -> Table scan on <union temporary>  (rows=5)
            -> Union materialize with deduplication  (rows=5)
                -> Table scan on t  (rows=3)
                -> Table scan on t1  (rows=2)
    -> Stream results  (rows=1)
        -> Table scan on r  (rows=1)
    -> Stream results  (rows=4)
        -> Table scan on <union temporary>  (rows=4)
            -> Union materialize with deduplication  (rows=4)
                -> Table scan on t  (rows=3)
                -> Table scan on r  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM t UNION SELECT * FROM t1)  UNION ALL SELECT * FROM r
UNION ALL (SELECT * FROM t UNION ALL SELECT * FROM r);
EXPLAIN
-> Append  (rows=10)
    -> Stream results  (rows=5)
        -> Table scan on <union temporary>  (rows=5)
            -> Union materialize with deduplication  (rows=5)
                -> Table scan on t  (rows=3)
                -> Table scan on t1  (rows=2)
    -> Stream results  (rows=1)
        -> Table scan on r  (rows=1)
    -> Stream results  (rows=3)
        -> Table scan on t  (rows=3)
    -> Stream results  (rows=1)
        -> Table scan on r  (rows=1)

DROP TABLE t, t1, r;
#
# INTERSECT and EXCEPT support
#
CREATE TABLE r(a INT);
CREATE TABLE s(a INT);
CREATE TABLE t(a INT);
INSERT INTO r VALUES (1),(2),(3);
INSERT INTO s VALUES (1),(2);
INSERT INTO t VALUES (2);
ANALYZE TABLE r, s, t;
Table	Op	Msg_type	Msg_text
test.r	analyze	status	OK
test.s	analyze	status	OK
test.t	analyze	status	OK
#
# Test operator precedence
#
EXPLAIN FORMAT = tree
(SELECT * FROM r UNION ALL SELECT * FROM s) INTERSECT ALL SELECT * FROM t;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=1)
    -> Intersect all materialize  (rows=1)
        -> Table scan on <union temporary>  (rows=5)
            -> Union all materialize  (rows=5)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r UNION ALL SELECT * FROM s INTERSECT ALL SELECT * FROM t;
EXPLAIN
-> Append  (rows=4)
    -> Stream results  (rows=3)
        -> Table scan on r  (rows=3)
    -> Stream results  (rows=1)
        -> Table scan on <intersect temporary>  (rows=1)
            -> Intersect all materialize  (rows=1)
                -> Table scan on s  (rows=2)
                -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r EXCEPT ALL SELECT * FROM s) INTERSECT ALL SELECT * FROM t;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=1)
    -> Intersect all materialize  (rows=1)
        -> Table scan on <except temporary>  (rows=3)
            -> Except all materialize  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r EXCEPT ALL SELECT * FROM s INTERSECT ALL SELECT * FROM t;
EXPLAIN
-> Table scan on <except temporary>  (rows=3)
    -> Except all materialize  (rows=3)
        -> Table scan on r  (rows=3)
        -> Table scan on <intersect temporary>  (rows=1)
            -> Intersect all materialize  (rows=1)
                -> Table scan on s  (rows=2)
                -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r EXCEPT ALL SELECT * FROM s) UNION ALL SELECT * FROM t;
EXPLAIN
-> Append  (rows=4)
    -> Stream results  (rows=3)
        -> Table scan on <except temporary>  (rows=3)
            -> Except all materialize  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
    -> Stream results  (rows=1)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r EXCEPT ALL SELECT * FROM s UNION ALL SELECT * FROM t;
EXPLAIN
-> Append  (rows=4)
    -> Stream results  (rows=3)
        -> Table scan on <except temporary>  (rows=3)
            -> Except all materialize  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
    -> Stream results  (rows=1)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r UNION DISTINCT SELECT * FROM s) INTERSECT DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=1)
    -> Intersect materialize with deduplication  (rows=1)
        -> Table scan on <union temporary>  (rows=5)
            -> Union materialize with deduplication  (rows=5)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r UNION DISTINCT SELECT * FROM s INTERSECT DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=4)
    -> Union materialize with deduplication  (rows=4)
        -> Table scan on r  (rows=3)
        -> Table scan on <intersect temporary>  (rows=1)
            -> Intersect materialize with deduplication  (rows=1)
                -> Table scan on s  (rows=2)
                -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s) INTERSECT DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=1)
    -> Intersect materialize with deduplication  (rows=1)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s INTERSECT DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <except temporary>  (rows=3)
    -> Except materialize with deduplication  (rows=3)
        -> Table scan on r  (rows=3)
        -> Table scan on <intersect temporary>  (rows=1)
            -> Intersect materialize with deduplication  (rows=1)
                -> Table scan on s  (rows=2)
                -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s) UNION DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=4)
    -> Union materialize with deduplication  (rows=4)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s UNION DISTINCT SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=4)
    -> Union materialize with deduplication  (rows=4)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r UNION SELECT * FROM s) INTERSECT SELECT * FROM t;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=1)
    -> Intersect materialize with deduplication  (rows=1)
        -> Table scan on <union temporary>  (rows=5)
            -> Union materialize with deduplication  (rows=5)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r UNION SELECT * FROM s INTERSECT SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=4)
    -> Union materialize with deduplication  (rows=4)
        -> Table scan on r  (rows=3)
        -> Table scan on <intersect temporary>  (rows=1)
            -> Intersect materialize with deduplication  (rows=1)
                -> Table scan on s  (rows=2)
                -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r EXCEPT SELECT * FROM s) INTERSECT SELECT * FROM t;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=1)
    -> Intersect materialize with deduplication  (rows=1)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r EXCEPT SELECT * FROM s INTERSECT SELECT * FROM t;
EXPLAIN
-> Table scan on <except temporary>  (rows=3)
    -> Except materialize with deduplication  (rows=3)
        -> Table scan on r  (rows=3)
        -> Table scan on <intersect temporary>  (rows=1)
            -> Intersect materialize with deduplication  (rows=1)
                -> Table scan on s  (rows=2)
                -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
(SELECT * FROM r EXCEPT SELECT * FROM s) UNION SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=4)
    -> Union materialize with deduplication  (rows=4)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

EXPLAIN FORMAT = tree
SELECT * FROM r EXCEPT SELECT * FROM s UNION SELECT * FROM t;
EXPLAIN
-> Table scan on <union temporary>  (rows=4)
    -> Union materialize with deduplication  (rows=4)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on r  (rows=3)
                -> Table scan on s  (rows=2)
        -> Table scan on t  (rows=1)

#
# Bug#33905918 Server crashes after using explain of a query with except/intersect.
#
EXPLAIN SELECT * FROM r UNION ALL SELECT * FROM s;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	UNION	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` union all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN SELECT * FROM r EXCEPT ALL SELECT * FROM s;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	EXCEPT	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	EXCEPT RESULT	<except1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN SELECT * FROM r INTERSECT ALL SELECT * FROM s;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	INTERSECT	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	INTERSECT RESULT	<intersect1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` intersect all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN SELECT * FROM r UNION DISTINCT SELECT * FROM s;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	UNION	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	UNION RESULT	<union1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` union /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	EXCEPT	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	EXCEPT RESULT	<except1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN SELECT * FROM r INTERSECT DISTINCT SELECT * FROM s;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	INTERSECT	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
3	INTERSECT RESULT	<intersect1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` intersect /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r UNION ALL SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": false,
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "24"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "16"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` union all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r EXCEPT ALL SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "except_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<except1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "24"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "16"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r INTERSECT ALL SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "intersect_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<intersect1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "24"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "16"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` intersect all /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r UNION DISTINCT SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<union1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "24"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "16"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` union /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "except_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<except1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "24"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "16"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT = json SELECT * FROM r INTERSECT DISTINCT SELECT * FROM s;
EXPLAIN
{
  "query_block": {
    "intersect_result": {
      "using_temporary_table": true,
      "select_id": 3,
      "table_name": "<intersect1,2>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 1,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "r",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 3,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "24"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "s",
              "access_type": "ALL",
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "16"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` intersect /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`
EXPLAIN FORMAT=json SELECT * FROM (SELECT 1 INTERSECT SELECT 2) AS dt;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "no matching row in const table",
    "table": {
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "intersect_result": {
            "using_temporary_table": true,
            "select_id": 4,
            "table_name": "<intersect2,3>",
            "access_type": "ALL",
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 2,
                  "message": "No tables used"
                }
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "message": "No tables used"
                }
              }
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select NULL AS `1` from (/* select#2 */ select 1 AS `1` intersect /* select#3 */ select 2 AS `2`) `dt`
EXPLAIN FORMAT=json SELECT * FROM (SELECT 1 EXCEPT SELECT 2) AS dt;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "elided"
    },
    "table": {
      "table_name": "dt",
      "access_type": "system",
      "rows_examined_per_scan": 1,
      "rows_produced_per_join": 1,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "elided",
        "eval_cost": "elided",
        "prefix_cost": "elided",
        "data_read_per_join": "32"
      },
      "used_columns": [
        "<hash_field>",
        "<set counter>",
        "1"
      ],
      "materialized_from_subquery": {
        "using_temporary_table": true,
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "except_result": {
            "using_temporary_table": true,
            "select_id": 4,
            "table_name": "<except2,3>",
            "access_type": "ALL",
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 2,
                  "message": "No tables used"
                }
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "message": "No tables used"
                }
              }
            ]
          }
        }
      }
    }
  }
}
Warnings:
Note	1003	/* select#1 */ select '1' AS `1` from dual
EXPLAIN (SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s) UNION DISTINCT SELECT * FROM t;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	EXCEPT	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
4	EXCEPT RESULT	<except1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
3	UNION	t	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
5	UNION RESULT	<union4,3>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	(/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`) union /* select#3 */ select `test`.`t`.`a` AS `a` from `test`.`t`
EXPLAIN (SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s) UNION DISTINCT
(SELECT * FROM t INTERSECT DISTINCT SELECT * FROM t);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	r	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	EXCEPT	s	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
5	EXCEPT RESULT	<except1,2>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
3	PRIMARY	t	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
4	INTERSECT	t	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
6	INTERSECT RESULT	<intersect3,4>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
7	UNION RESULT	<union5,6>	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	Using temporary
Warnings:
Note	1003	(/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`) union (/* select#3 */ select `test`.`t`.`a` AS `a` from `test`.`t` intersect /* select#4 */ select `test`.`t`.`a` AS `a` from `test`.`t`)
EXPLAIN FORMAT = json
(SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s) UNION DISTINCT SELECT * FROM t;
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": true,
      "select_id": 5,
      "table_name": "<union4,3>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "except_result": {
            "using_temporary_table": true,
            "select_id": 4,
            "table_name": "<except1,2>",
            "access_type": "ALL",
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 1,
                  "cost_info": {
                    "query_cost": "elided"
                  },
                  "table": {
                    "table_name": "r",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 3,
                    "rows_produced_per_join": 3,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "elided",
                      "eval_cost": "elided",
                      "prefix_cost": "elided",
                      "data_read_per_join": "24"
                    },
                    "used_columns": [
                      "a"
                    ]
                  }
                }
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 2,
                  "cost_info": {
                    "query_cost": "elided"
                  },
                  "table": {
                    "table_name": "s",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 2,
                    "rows_produced_per_join": 2,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "elided",
                      "eval_cost": "elided",
                      "prefix_cost": "elided",
                      "data_read_per_join": "16"
                    },
                    "used_columns": [
                      "a"
                    ]
                  }
                }
              }
            ]
          }
        },
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 3,
            "cost_info": {
              "query_cost": "elided"
            },
            "table": {
              "table_name": "t",
              "access_type": "ALL",
              "rows_examined_per_scan": 1,
              "rows_produced_per_join": 1,
              "filtered": "100.00",
              "cost_info": {
                "read_cost": "elided",
                "eval_cost": "elided",
                "prefix_cost": "elided",
                "data_read_per_join": "8"
              },
              "used_columns": [
                "a"
              ]
            }
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	(/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`) union /* select#3 */ select `test`.`t`.`a` AS `a` from `test`.`t`
EXPLAIN FORMAT = json
(SELECT * FROM r EXCEPT DISTINCT SELECT * FROM s) UNION DISTINCT
(SELECT * FROM t INTERSECT DISTINCT SELECT * FROM t);
EXPLAIN
{
  "query_block": {
    "union_result": {
      "using_temporary_table": true,
      "select_id": 7,
      "table_name": "<union5,6>",
      "access_type": "ALL",
      "query_specifications": [
        {
          "except_result": {
            "using_temporary_table": true,
            "select_id": 5,
            "table_name": "<except1,2>",
            "access_type": "ALL",
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 1,
                  "cost_info": {
                    "query_cost": "elided"
                  },
                  "table": {
                    "table_name": "r",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 3,
                    "rows_produced_per_join": 3,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "elided",
                      "eval_cost": "elided",
                      "prefix_cost": "elided",
                      "data_read_per_join": "24"
                    },
                    "used_columns": [
                      "a"
                    ]
                  }
                }
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 2,
                  "cost_info": {
                    "query_cost": "elided"
                  },
                  "table": {
                    "table_name": "s",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 2,
                    "rows_produced_per_join": 2,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "elided",
                      "eval_cost": "elided",
                      "prefix_cost": "elided",
                      "data_read_per_join": "16"
                    },
                    "used_columns": [
                      "a"
                    ]
                  }
                }
              }
            ]
          }
        },
        {
          "intersect_result": {
            "using_temporary_table": true,
            "select_id": 6,
            "table_name": "<intersect3,4>",
            "access_type": "ALL",
            "query_specifications": [
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "cost_info": {
                    "query_cost": "elided"
                  },
                  "table": {
                    "table_name": "t",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 1,
                    "rows_produced_per_join": 1,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "elided",
                      "eval_cost": "elided",
                      "prefix_cost": "elided",
                      "data_read_per_join": "8"
                    },
                    "used_columns": [
                      "a"
                    ]
                  }
                }
              },
              {
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 4,
                  "cost_info": {
                    "query_cost": "elided"
                  },
                  "table": {
                    "table_name": "t",
                    "access_type": "ALL",
                    "rows_examined_per_scan": 1,
                    "rows_produced_per_join": 1,
                    "filtered": "100.00",
                    "cost_info": {
                      "read_cost": "elided",
                      "eval_cost": "elided",
                      "prefix_cost": "elided",
                      "data_read_per_join": "8"
                    },
                    "used_columns": [
                      "a"
                    ]
                  }
                }
              }
            ]
          }
        }
      ]
    }
  }
}
Warnings:
Note	1003	(/* select#1 */ select `test`.`r`.`a` AS `a` from `test`.`r` except /* select#2 */ select `test`.`s`.`a` AS `a` from `test`.`s`) union (/* select#3 */ select `test`.`t`.`a` AS `a` from `test`.`t` intersect /* select#4 */ select `test`.`t`.`a` AS `a` from `test`.`t`)
DROP TABLE r, s, t;
#
# Check that more set ops don't interfere with
# WITH RECURSIVE
#
WITH RECURSIVE qn AS
(SELECT 1 AS n, 1 AS un, 1 AS unp1 EXCEPT ALL
SELECT 1+n, unp1, un+unp1 FROM qn WHERE n<10)
SELECT * FROM qn;
ERROR HY000: Recursive Common Table Expression 'qn' should contain a UNION
WITH RECURSIVE qn AS
(SELECT 1 AS n, 1 AS un, 1 AS unp1 INTERSECT ALL
SELECT 1+n, unp1, un+unp1 FROM qn WHERE n<10)
SELECT * FROM qn;
ERROR HY000: Recursive Common Table Expression 'qn' should contain a UNION
CREATE TABLE t(n int, un INT, unp1 INT);
INSERT INTO t VALUES (1, 1, 1);
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
Non-union set op in seed works
WITH RECURSIVE qn AS
( SELECT * FROM t INTERSECT
VALUES ROW(1, 1, 1) UNION ALL
SELECT 1+n, unp1, un+unp1 FROM qn WHERE n<10)
SELECT * FROM qn;
n	un	unp1
1	1	1
2	1	2
3	2	3
4	3	5
5	5	8
6	8	13
7	13	21
8	21	34
9	34	55
10	55	89
WITH RECURSIVE qn AS
( SELECT * FROM t UNION ALL
(SELECT 1,1,1 INTERSECT SELECT 1+n, unp1, un+unp1 FROM qn WHERE n < 20)) SELECT * FROM qn;
ERROR HY000: Recursive table reference in EXCEPT or INTERSECT operand is not allowed.
WITH RECURSIVE qn AS
( SELECT * FROM t UNION ALL
(SELECT 1+n, unp1, un+unp1 FROM qn WHERE n<10 INTERSECT SELECT 1,1,1)) SELECT * FROM qn;
ERROR HY000: Recursive table reference in EXCEPT or INTERSECT operand is not allowed.
WITH RECURSIVE cte AS
( ( (SELECT 1 AS n UNION SELECT 2) EXCEPT SELECT 2)  UNION ALL
SELECT n+1  FROM cte WHERE n<100)
SELECT n FROM cte LIMIT 10;
n
1
2
3
4
5
6
7
8
9
10
DROP TABLE t;
CREATE TABLE t1(i INT);
CREATE TABLE t2(i INT);
CREATE TABLE t3(i INT);
INSERT INTO t1 VALUES (1),(1),(1);
INSERT INTO t2 VALUES (2),(2),(1),(1);
INSERT INTO t3 VALUES (2),(3),(3),(1),(1);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
# DISTINCT. Note: a mix of ALL and DISTINCT also gives all DISTINCT
SELECT * FROM t1 INTERSECT DISTINCT SELECT * FROM t2;
i
1
SELECT * FROM t2 INTERSECT DISTINCT SELECT * FROM t1;
i
1
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3;
i
1
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3;
i
1
1
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2;
i
1
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2;
i
1
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1;
i
1
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
i
1
1
SELECT * FROM t1 INTERSECT DISTINCT SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3;
i
1
SELECT * FROM t2 INTERSECT DISTINCT SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3;
i
1
SELECT * FROM t1 INTERSECT DISTINCT SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2;
i
1
SELECT * FROM t3 INTERSECT DISTINCT SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2;
i
1
SELECT * FROM t2 INTERSECT DISTINCT SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1;
i
1
SELECT * FROM t3 INTERSECT DISTINCT SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
i
1
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2 INTERSECT DISTINCT SELECT * FROM t3;
i
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1 INTERSECT DISTINCT SELECT * FROM t3;
i
1
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3 INTERSECT DISTINCT SELECT * FROM t2;
i
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1 INTERSECT DISTINCT SELECT * FROM t2;
i
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3 INTERSECT DISTINCT SELECT * FROM t1;
i
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT DISTINCT SELECT * FROM t1;
i
1
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3;
i
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3;
i
2
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2;
i
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2;
i
3
3
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1;
i
2
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1;
i
3
3
SELECT * FROM t1 EXCEPT    DISTINCT SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3;
i
SELECT * FROM t2 EXCEPT    DISTINCT SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3;
i
SELECT * FROM t1 EXCEPT    DISTINCT SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2;
i
SELECT * FROM t3 EXCEPT    DISTINCT SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2;
i
3
SELECT * FROM t2 EXCEPT    DISTINCT SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1;
i
SELECT * FROM t3 EXCEPT    DISTINCT SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1;
i
3
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2 EXCEPT    DISTINCT SELECT * FROM t3;
i
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1 EXCEPT    DISTINCT SELECT * FROM t3;
i
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3 EXCEPT    DISTINCT SELECT * FROM t2;
i
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1 EXCEPT    DISTINCT SELECT * FROM t2;
i
3
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3 EXCEPT    DISTINCT SELECT * FROM t1;
i
2
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2 EXCEPT    DISTINCT SELECT * FROM t1;
i
3
# ALL
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2;
i
1
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
i
1
1
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3;
i
1
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1;
i
1
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3;
i
1
1
2
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2;
i
1
1
2
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2;
i
1
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1;
i
2
2
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3;
i
1
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1;
i
2
3
3
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3;
i
2
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2;
i
3
3
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3;
i
1
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3;
i
1
1
SELECT * FROM t1 INTERSECT ALL SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2;
i
1
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1 INTERSECT ALL SELECT * FROM t2;
i
1
1
SELECT * FROM t2 INTERSECT ALL SELECT * FROM t3 INTERSECT ALL SELECT * FROM t1;
i
1
1
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
i
1
1
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3;
i
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3;
i
2
SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2;
i
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1 EXCEPT    ALL SELECT * FROM t2;
i
3
3
SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t1;
i
2
SELECT * FROM t3 EXCEPT    ALL SELECT * FROM t2 EXCEPT    ALL SELECT * FROM t1;
i
3
3
TRUNCATE t1;
TRUNCATE t2;
TRUNCATE t3;
INSERT INTO t3 VALUES (3),(3),(2),(2),(1),(1);
INSERT INTO t2 VALUES (2),(1),(1);
INSERT INTO t1 VALUES (1);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
# EXCEPT is not right associative, so make a right parentheses
# nest to evaluate it first if wanted. Also test mix of
# DISTINCT & ALL.
#
# DISTINCT
#
SELECT * FROM t3 EXCEPT SELECT * FROM t2 EXCEPT SELECT * FROM t1;
i
3
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT SELECT * FROM t2 EXCEPT SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

(SELECT * FROM t3 EXCEPT SELECT * FROM t2) EXCEPT SELECT * FROM t1;
i
3
EXPLAIN FORMAT=tree (SELECT * FROM t3 EXCEPT SELECT * FROM t2) EXCEPT SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

SELECT * FROM t3 EXCEPT (SELECT * FROM t2 EXCEPT SELECT * FROM t1);
i
1
3
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT (SELECT * FROM t2 EXCEPT SELECT * FROM t1);
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on t2  (rows=3)
                -> Table scan on t1  (rows=1)

#
# ALL
#
SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1;
i
3
3
2
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except all materialize  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

(SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2) EXCEPT ALL SELECT * FROM t1;
i
3
3
2
EXPLAIN FORMAT=tree (SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2) EXCEPT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except all materialize  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

SELECT * FROM t3 EXCEPT ALL (SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1);
i
1
2
3
3
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT ALL (SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1);
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except all materialize  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on <except temporary>  (rows=3)
            -> Except all materialize  (rows=3)
                -> Table scan on t2  (rows=3)
                -> Table scan on t1  (rows=1)

#
# Mix of DISTINCT and ALL
#
SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2 EXCEPT SELECT * FROM t1;
i
3
2
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2 EXCEPT SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Disable deduplication
            -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

(SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2) EXCEPT SELECT * FROM t1;
i
3
2
EXPLAIN FORMAT=tree (SELECT * FROM t3 EXCEPT ALL SELECT * FROM t2) EXCEPT SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Disable deduplication
            -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

SELECT * FROM t3 EXCEPT ALL (SELECT * FROM t2 EXCEPT SELECT * FROM t1);
i
1
1
2
3
3
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT ALL (SELECT * FROM t2 EXCEPT SELECT * FROM t1);
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except all materialize  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on <except temporary>  (rows=3)
            -> Except materialize with deduplication  (rows=3)
                -> Table scan on t2  (rows=3)
                -> Table scan on t1  (rows=1)

SELECT * FROM t3 EXCEPT SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1;
i
3
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

(SELECT * FROM t3 EXCEPT SELECT * FROM t2) EXCEPT ALL SELECT * FROM t1;
i
3
EXPLAIN FORMAT=tree (SELECT * FROM t3 EXCEPT SELECT * FROM t2) EXCEPT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=3)
        -> Table scan on t1  (rows=1)

SELECT * FROM t3 EXCEPT (SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1);
i
3
EXPLAIN FORMAT=tree SELECT * FROM t3 EXCEPT (SELECT * FROM t2 EXCEPT ALL SELECT * FROM t1);
EXPLAIN
-> Table scan on <except temporary>  (rows=6)
    -> Except materialize with deduplication  (rows=6)
        -> Table scan on t3  (rows=6)
        -> Table scan on <except temporary>  (rows=3)
            -> Except all materialize  (rows=3)
                -> Table scan on t2  (rows=3)
                -> Table scan on t1  (rows=1)

TRUNCATE t1;
TRUNCATE t2;
TRUNCATE t3;
INSERT INTO t3 VALUES (3),(3),(2),(2),(1),(1);
INSERT INTO t2 VALUES (2),(2),(1),(1);
INSERT INTO t1 VALUES (1),(1);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
# INTERSECT is left and right associative. If evaluation is
# INTERSECT ALL, each operation can only handle two operands,
# so for N operands we must set up N-1 operation nodes. If
# evaluation is INTERSECT UNIQUE, collapse all operands and use
# a single operation node. The restriction for INTERSECT ALL is
# due to the fact we need an extra read pass to check counters
# after each right-hand operand has been processed.  Also, test
# mix of DISTINCT & ALL: DISTINCT wins always.
#
# DISTINCT
#
SELECT * FROM t3 INTERSECT SELECT * FROM t2 INTERSECT SELECT * FROM t1;
i
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT SELECT * FROM t2 INTERSECT SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

(SELECT * FROM t3 INTERSECT SELECT * FROM t2) INTERSECT SELECT * FROM t1;
i
1
EXPLAIN FORMAT=tree (SELECT * FROM t3 INTERSECT SELECT * FROM t2) INTERSECT SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

SELECT * FROM t3 INTERSECT (SELECT * FROM t2 INTERSECT SELECT * FROM t1);
i
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT (SELECT * FROM t2 INTERSECT SELECT * FROM t1);
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

#
# ALL
#
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
i
1
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect all materialize  (rows=2)
        -> Table scan on <intersect temporary>  (rows=4)
            -> Intersect all materialize  (rows=4)
                -> Table scan on t3  (rows=6)
                -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

(SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2) INTERSECT ALL SELECT * FROM t1;
i
1
1
EXPLAIN FORMAT=tree (SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2) INTERSECT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect all materialize  (rows=2)
        -> Table scan on <intersect temporary>  (rows=4)
            -> Intersect all materialize  (rows=4)
                -> Table scan on t3  (rows=6)
                -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

SELECT * FROM t3 INTERSECT ALL (SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1);
i
1
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT ALL (SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1);
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect all materialize  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on <intersect temporary>  (rows=2)
            -> Intersect all materialize  (rows=2)
                -> Table scan on t2  (rows=4)
                -> Table scan on t1  (rows=2)

#
# Mix of DISTINCT and ALL
#
SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT SELECT * FROM t1;
i
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2 INTERSECT SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

(SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2) INTERSECT SELECT * FROM t1;
i
1
EXPLAIN FORMAT=tree (SELECT * FROM t3 INTERSECT ALL SELECT * FROM t2) INTERSECT SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

SELECT * FROM t3 INTERSECT ALL (SELECT * FROM t2 INTERSECT SELECT * FROM t1);
i
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT ALL (SELECT * FROM t2 INTERSECT SELECT * FROM t1);
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

SELECT * FROM t3 INTERSECT SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
i
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

(SELECT * FROM t3 INTERSECT SELECT * FROM t2) INTERSECT ALL SELECT * FROM t1;
i
1
EXPLAIN FORMAT=tree (SELECT * FROM t3 INTERSECT SELECT * FROM t2) INTERSECT ALL SELECT * FROM t1;
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

SELECT * FROM t3 INTERSECT (SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1);
i
1
EXPLAIN FORMAT=tree SELECT * FROM t3 INTERSECT (SELECT * FROM t2 INTERSECT ALL SELECT * FROM t1);
EXPLAIN
-> Table scan on <intersect temporary>  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t3  (rows=6)
        -> Table scan on t2  (rows=4)
        -> Table scan on t1  (rows=2)

SELECT * FROM (SELECT * FROM t3 UNION DISTINCT SELECT * FROM t3 UNION ALL SELECT * FROM t3) x1
INTERSECT
SELECT * FROM (SELECT * FROM t3 UNION DISTINCT SELECT * FROM t3 UNION ALL SELECT * FROM t3) x2
WHERE i > 1 ORDER BY i;
i
2
3
EXPLAIN FORMAT=tree SELECT * FROM (SELECT * FROM t3 UNION DISTINCT SELECT * FROM t3 UNION ALL SELECT * FROM t3) x1
INTERSECT
SELECT * FROM (SELECT * FROM t3 UNION DISTINCT SELECT * FROM t3 UNION ALL SELECT * FROM t3) x2
WHERE i > 1 ORDER BY i;
EXPLAIN
-> Sort: i  (rows=6)
    -> Table scan on <intersect temporary>  (rows=6)
        -> Intersect materialize with deduplication  (rows=6)
            -> Table scan on x1  (rows=18)
                -> Union materialize with deduplication  (rows=18)
                    -> Table scan on t3  (rows=6)
                    -> Table scan on t3  (rows=6)
                    -> Disable deduplication
                        -> Table scan on t3  (rows=6)
            -> Table scan on x2  (rows=6)
                -> Union materialize with deduplication  (rows=6)
                    -> Filter: (t3.i > 1)  (rows=2)
                        -> Table scan on t3  (rows=6)
                    -> Filter: (t3.i > 1)  (rows=2)
                        -> Table scan on t3  (rows=6)
                    -> Disable deduplication
                        -> Filter: (t3.i > 1)  (rows=2)
                            -> Table scan on t3  (rows=6)

SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 ORDER BY i) x1 WHERE i < 5;
i
1
2
EXPLAIN FORMAT=tree SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 ORDER BY i) x1 WHERE i < 5;
EXPLAIN
-> Table scan on x1  (rows=1.33)
    -> Materialize  (rows=1.33)
        -> Sort: i  (rows=1.33)
            -> Table scan on <intersect temporary>  (rows=1.33)
                -> Intersect materialize with deduplication  (rows=1.33)
                    -> Filter: (t2.i < 5)  (rows=1.33)
                        -> Table scan on t2  (rows=4)
                    -> Filter: (t2.i < 5)  (rows=1.33)
                        -> Table scan on t2  (rows=4)

#
# Test LIMIT, OFFSET
#
SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1;
i
CORRECT
EXPLAIN FORMAT=tree SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1;
EXPLAIN
-> Limit: 1 row(s)  (rows=1)
    -> Table scan on <intersect temporary>  (rows=4)
        -> Intersect materialize with deduplication  (rows=4)
            -> Table scan on t2  (rows=4)
            -> Table scan on t2  (rows=4)

SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1 OFFSET 1;
i
CORRECT
EXPLAIN FORMAT=tree SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1 OFFSET 1;
EXPLAIN
-> Limit/Offset: 1/1 row(s)  (rows=1)
    -> Table scan on <intersect temporary>  (rows=4)
        -> Intersect materialize with deduplication  (rows=4)
            -> Table scan on t2  (rows=4)
            -> Table scan on t2  (rows=4)

#
# LIMIT, OFFSET with derived table
#
SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1) t;
i
CORRECT
EXPLAIN FORMAT=tree SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1) t;
EXPLAIN
-> Table scan on t  (rows=4)
    -> Intersect materialize with deduplication  (rows=4)
        -> Table scan on t2  (rows=4)
        -> Table scan on t2  (rows=4)

SELECT * FROM (SELECT i FROM t1 INTERSECT SELECT i FROM t3 LIMIT 1) t;
i
1
EXPLAIN FORMAT=tree SELECT * FROM (SELECT i FROM t1 INTERSECT SELECT i FROM t3 LIMIT 1) t;
EXPLAIN
-> Table scan on t  (rows=2)
    -> Intersect materialize with deduplication  (rows=2)
        -> Table scan on t1  (rows=2)
        -> Table scan on t3  (rows=6)

SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2) t LIMIT 1;
i
CORRECT
EXPLAIN FORMAT=tree SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2) t LIMIT 1;
EXPLAIN
-> Limit: 1 row(s)  (rows=1)
    -> Table scan on t  (rows=4)
        -> Intersect materialize with deduplication  (rows=4)
            -> Table scan on t2  (rows=4)
            -> Table scan on t2  (rows=4)

SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1 OFFSET 1) t;
i
CORRECT
EXPLAIN FORMAT=tree SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1 OFFSET 1) t;
EXPLAIN
-> Limit/Offset: 1/1 row(s)  (rows=1)
    -> Table scan on t  (rows=4)
        -> Intersect materialize with deduplication  (rows=4)
            -> Table scan on t2  (rows=4)
            -> Table scan on t2  (rows=4)

SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2) t LIMIT 1 OFFSET 1 ;
i
CORRECT
EXPLAIN FORMAT=tree SELECT * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2) t LIMIT 1 OFFSET 1 ;
EXPLAIN
-> Limit/Offset: 1/1 row(s)  (rows=1)
    -> Table scan on t  (rows=4)
        -> Intersect materialize with deduplication  (rows=4)
            -> Table scan on t2  (rows=4)
            -> Table scan on t2  (rows=4)

SELECT SQL_CALC_FOUND_ROWS i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1;
i
CORRECT
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
2
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
SELECT SQL_CALC_FOUND_ROWS * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2) t LIMIT 1;
i
CORRECT
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
2
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
SELECT SQL_CALC_FOUND_ROWS * FROM (SELECT i FROM t2 INTERSECT SELECT i FROM t2 LIMIT 1) t;
i
CORRECT
Warnings:
Warning	1287	SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead.
SELECT FOUND_ROWS();
FOUND_ROWS()
1
Warnings:
Warning	1287	FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead.
(SELECT i FROM t1 WHERE i > 100) INTERSECT SELECT i FROM t2;
i
EXPLAIN analyze (SELECT i FROM t1 WHERE i > 100) INTERSECT SELECT i FROM t2;
EXPLAIN
-> Table scan on <intersect temporary>  (...) (actual rows=0 loops=1)
    -> Intersect materialize with deduplication  (...) (actual rows=0 loops=1)
        -> Filter: (t1.i > 100)  (...) (actual rows=0 loops=1)
            -> Table scan on t1  (...) (actual rows=2 loops=1)
        -> Table scan on t2  (...) (never executed)

(SELECT i FROM t1 LIMIT 0) INTERSECT SELECT i FROM t2;
i
EXPLAIN analyze (SELECT i FROM t1 LIMIT 0) INTERSECT SELECT i FROM t2;
EXPLAIN
-> Table scan on <intersect temporary>  (...) (actual rows=0 loops=1)
    -> Intersect materialize with deduplication  (...) (actual rows=0 loops=1)
        -> Zero rows (Zero limit)  (...) (actual rows=0 loops=1)
        -> Table scan on t2  (...) (never executed)

DROP TABLE t1, t2, t3;
#
# Test of T101 "enhanced nullability determination". To interpret results
# read SQL 2014, Vol 2. section 7.17 <query expression>, SR 18 and 20.
#
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 UNION SELECT 2,2;
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 UNION SELECT null, null;
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 UNION SELECT 2,2;
CREATE TABLE t4 AS SELECT null AS c1, null AS c2 UNION SELECT null, null;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t4;
Table	Create Table
t4	CREATE TABLE `t4` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3, t4;
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 INTERSECT SELECT 2,2;
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 INTERSECT SELECT null, null;
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 INTERSECT SELECT 2,2;
CREATE TABLE t4 AS SELECT null AS c1, null AS c2 INTERSECT SELECT null, null;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t4;
Table	Create Table
t4	CREATE TABLE `t4` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3, t4;
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 EXCEPT SELECT 2,2;
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 EXCEPT SELECT null, null;
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 EXCEPT SELECT 2,2;
CREATE TABLE t4 AS SELECT null AS c1, null AS c2 EXCEPT SELECT null, null;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t4;
Table	Create Table
t4	CREATE TABLE `t4` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3, t4;
# Test T101 hierarchy correctness
# EXCEPT on top, UNION in its right side
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 EXCEPT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT null, null);
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 EXCEPT
(SELECT null AS c1, null AS c2 UNION SELECT null, null);
CREATE TABLE t3 AS SELECT 1 AS c1, 1 AS c2 EXCEPT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
CREATE TABLE t1 AS SELECT null AS c1, null AS c2 EXCEPT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT null, null);
CREATE TABLE t2 AS SELECT null AS c1, null AS c2 EXCEPT
(SELECT null AS c1, null AS c2 UNION SELECT null, null);
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 EXCEPT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
# INTERSECT on top, UNION in its right side
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 INTERSECT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT null, null);
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 INTERSECT
(SELECT null AS c1, null AS c2 UNION SELECT null, null);
CREATE TABLE t3 AS SELECT 1 AS c1, 1 AS c2 INTERSECT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
CREATE TABLE t1 AS SELECT null AS c1, null AS c2 INTERSECT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT null, null);
CREATE TABLE t2 AS SELECT null AS c1, null AS c2 INTERSECT
(SELECT null AS c1, null AS c2 UNION SELECT null, null);
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 INTERSECT
(SELECT 1 AS c1, 1 AS c2 UNION SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
# UNION on top, INTERSECT in its right side
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 INTERSECT SELECT null, null);
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 UNION
(SELECT null AS c1, null AS c2 INTERSECT SELECT null, null);
CREATE TABLE t3 AS SELECT 1 AS c1, 1 AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 INTERSECT SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
CREATE TABLE t1 AS SELECT null AS c1, null AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 INTERSECT SELECT null, null);
CREATE TABLE t2 AS SELECT null AS c1, null AS c2 UNION
(SELECT null AS c1, null AS c2 INTERSECT SELECT null, null);
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 INTERSECT SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
# UNION on top, EXCEPT in its right side
CREATE TABLE t1 AS SELECT 1 AS c1, 1 AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 EXCEPT SELECT null, null);
CREATE TABLE t2 AS SELECT 1 AS c1, 1 AS c2 UNION
(SELECT null AS c1, null AS c2 EXCEPT SELECT null, null);
CREATE TABLE t3 AS SELECT 1 AS c1, 1 AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 EXCEPT SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint NOT NULL DEFAULT '0',
  `c2` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
CREATE TABLE t1 AS SELECT null AS c1, null AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 EXCEPT SELECT null, null);
CREATE TABLE t2 AS SELECT null AS c1, null AS c2 UNION
(SELECT null AS c1, null AS c2 EXCEPT SELECT null, null);
CREATE TABLE t3 AS SELECT null AS c1, null AS c2 UNION
(SELECT 1 AS c1, 1 AS c2 EXCEPT SELECT 2,2);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `c1` varbinary(0) DEFAULT NULL,
  `c2` varbinary(0) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t3;
Table	Create Table
t3	CREATE TABLE `t3` (
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2, t3;
# T101 for unary
CREATE TABLE t1 AS ((VALUES ROW (1, 1), ROW (2,2) ORDER BY column_0 LIMIT 2)
ORDER BY column_1 LIMIT 1);
CREATE TABLE t2 AS ((VALUES ROW (null, null), ROW (2,2)
ORDER BY column_0 LIMIT 2) ORDER BY column_1 LIMIT 1);
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `column_0` bigint NOT NULL DEFAULT '0',
  `column_1` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
SHOW CREATE TABLE t2;
Table	Create Table
t2	CREATE TABLE `t2` (
  `column_0` bigint DEFAULT NULL,
  `column_1` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1, t2;
#
# Row count estimates. For UNION, the optimizer adds the
# numbers of rows for the operands, (10, the worst case in
# example below). For INTERSECT, the estimate should be the
# lowest estimate of the operands (3). For EXCEPT it should be
# estimate of the left operand (7, worst case no rows are
# removed from the set).
#
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES (1), (1), (2), (3), (2), (3), (3);
ANALYZE TABLE t1;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
EXPLAIN FORMAT=tree
SELECT * FROM t1 UNION     (SELECT * FROM t1 ORDER BY 1 LIMIT 3);
EXPLAIN
-> Table scan on <union temporary>  (rows=10)
    -> Union materialize with deduplication  (rows=10)
        -> Table scan on t1  (rows=7)
        -> Limit: 3 row(s)  (rows=3)
            -> Sort: t1.a, limit input to 3 row(s) per chunk  (rows=7)
                -> Table scan on t1  (rows=7)

EXPLAIN FORMAT=tree
SELECT * FROM t1 INTERSECT (SELECT * FROM t1 ORDER BY 1 LIMIT 3);
EXPLAIN
-> Table scan on <intersect temporary>  (rows=3)
    -> Intersect materialize with deduplication  (rows=3)
        -> Table scan on t1  (rows=7)
        -> Limit: 3 row(s)  (rows=3)
            -> Sort: t1.a, limit input to 3 row(s) per chunk  (rows=7)
                -> Table scan on t1  (rows=7)

EXPLAIN FORMAT=tree
SELECT * FROM t1 EXCEPT    (SELECT * FROM t1 ORDER BY 1 LIMIT 3);
EXPLAIN
-> Table scan on <except temporary>  (rows=7)
    -> Except materialize with deduplication  (rows=7)
        -> Table scan on t1  (rows=7)
        -> Limit: 3 row(s)  (rows=3)
            -> Sort: t1.a, limit input to 3 row(s) per chunk  (rows=7)
                -> Table scan on t1  (rows=7)

DROP TABLE t1;
#
# Bug#34843764 Wrong Results when INTERSECT/EXCEPT combine with JOIN
#
CREATE TABLE t1 (a INT, b INT);
INSERT INTO t1 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5), (6, 5);
INSERT INTO t1 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5), (6, 5);
INSERT INTO t1 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5), (6, 5);
CREATE TABLE t2 (a INT, b INT);
INSERT INTO t2 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5), (6, 5), (7, 5), (8, 9);
INSERT INTO t2 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5), (6, 5), (7, 5), (8, 9);
CREATE TABLE t3 (a INT, b INT);
INSERT INTO t3 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5);
INSERT INTO t3 VALUES (NULL, NULL), (1, NULL), (2, 3), (4, 5);
ANALYZE TABLE t1, t2, t3;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
test.t3	analyze	status	OK
SELECT *
FROM (SELECT a, b FROM t1 INTERSECT SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
a	b	a	b
2	3	2	3
2	3	2	3
4	5	4	5
4	5	4	5
EXPLAIN FORMAT=tree SELECT *
FROM (SELECT a, b FROM t1 INTERSECT SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
EXPLAIN
-> Sort: x.a, x.b
    -> Stream results  (rows=14.9)
        -> Inner hash join (x.b = t2.b), (x.a = t2.a)  (rows=14.9)
            -> Table scan on x  (rows=8)
                -> Intersect materialize with deduplication  (rows=8)
                    -> Table scan on t1  (rows=15)
                    -> Table scan on t3  (rows=8)
            -> Hash
                -> Table scan on t2  (rows=14)

SELECT *
FROM (SELECT a, b FROM t1 INTERSECT ALL SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
a	b	a	b
2	3	2	3
2	3	2	3
2	3	2	3
2	3	2	3
4	5	4	5
4	5	4	5
4	5	4	5
4	5	4	5
EXPLAIN FORMAT=tree SELECT *
FROM (SELECT a, b FROM t1 INTERSECT ALL SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
EXPLAIN
-> Sort: x.a, x.b
    -> Stream results  (rows=14.9)
        -> Inner hash join (x.b = t2.b), (x.a = t2.a)  (rows=14.9)
            -> Table scan on x  (rows=8)
                -> Intersect all materialize  (rows=8)
                    -> Table scan on t1  (rows=15)
                    -> Table scan on t3  (rows=8)
            -> Hash
                -> Table scan on t2  (rows=14)

SELECT *
FROM (SELECT a, b FROM t1 EXCEPT SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
a	b	a	b
6	5	6	5
6	5	6	5
EXPLAIN FORMAT=tree SELECT *
FROM (SELECT a, b FROM t1 EXCEPT SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
EXPLAIN
-> Sort: x.a, x.b
    -> Stream results  (rows=28)
        -> Inner hash join (x.b = t2.b), (x.a = t2.a)  (rows=28)
            -> Table scan on x  (rows=15)
                -> Except materialize with deduplication  (rows=15)
                    -> Table scan on t1  (rows=15)
                    -> Table scan on t3  (rows=8)
            -> Hash
                -> Table scan on t2  (rows=14)

SELECT *
FROM (SELECT a, b FROM t1 EXCEPT ALL SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
a	b	a	b
2	3	2	3
2	3	2	3
4	5	4	5
4	5	4	5
6	5	6	5
6	5	6	5
6	5	6	5
6	5	6	5
6	5	6	5
6	5	6	5
EXPLAIN FORMAT=tree SELECT *
FROM (SELECT a, b FROM t1 EXCEPT ALL SELECT a, b FROM t3) AS x
JOIN
t2
ON x.a = t2.a AND x.b = t2.b
ORDER BY 1, 2;
EXPLAIN
-> Sort: x.a, x.b
    -> Stream results  (rows=28)
        -> Inner hash join (x.b = t2.b), (x.a = t2.a)  (rows=28)
            -> Table scan on x  (rows=15)
                -> Except all materialize  (rows=15)
                    -> Table scan on t1  (rows=15)
                    -> Table scan on t3  (rows=8)
            -> Hash
                -> Table scan on t2  (rows=14)

DROP TABLE t1, t2, t3;
#
# Bug#34704011 When using a single select in parenthesis, a
#              global order by cannot be used
#
CREATE TABLE t1 (id INT PRIMARY KEY AUTO_INCREMENT, d DATE);
CREATE TABLE t2 (id INT PRIMARY KEY AUTO_INCREMENT, d DATE);
INSERT INTO t1 (d) VALUES ('2020-01-01'), ('2021-04-21'), ('2022-03-02');
INSERT INTO t2 (d) VALUES ('2020-05-01'), ('2021-05-21'), ('2022-05-02');
ANALYZE TABLE t1, t2;
Table	Op	Msg_type	Msg_text
test.t1	analyze	status	OK
test.t2	analyze	status	OK
( SELECT t1.d, t2.d
FROM t1 INNER JOIN t2 USING (id) )
ORDER BY t1.d;
d	d
2020-01-01	2020-05-01
2021-04-21	2021-05-21
2022-03-02	2022-05-02
(( SELECT t1.d, t2.d
FROM t1 INNER JOIN t2 USING (id) ))
ORDER BY t1.d;
d	d
2020-01-01	2020-05-01
2021-04-21	2021-05-21
2022-03-02	2022-05-02
(( SELECT t1.d, t2.d
FROM t1 INNER JOIN t2 USING (id) ) LIMIT 1)
ORDER BY t1.d;
ERROR 42S22: Unknown column 't1.d' in 'order clause'
DROP TABLE t1, t2;
#
# Follow-up to Bug#36739383 Assertion 'false'
# materialize_iterator::SpillState::read_next_row_secondary_over.
# This is a case of too large a row for default hash table
# space, which next also overflows the normal tmp table (error
# 135 HA_ERR_RECORD_FILE_FULL) and must go to InnoDB tmp table.
#
CREATE TABLE t1 (a MEDIUMTEXT);
INSERT INTO t1 VALUES ('a');
CREATE TABLE t2 AS
SELECT REPEAT(a,20000000) AS a FROM t1
INTERSECT
SELECT REPEAT(a,20000000) AS a FROM t1;
SELECT LENGTH(a), SUBSTRING(a FROM 20000000-4 FOR 4) FROM t2;
LENGTH(a)	SUBSTRING(a FROM 20000000-4 FOR 4)
20000000	aaaa
DROP TABLE t1, t2;
# Test hash table set operations against "old" approach
# and overflow of in-memory hash table
#
# WL#15257 Enhanced performance for set operations: INTERSECT,
# EXCEPT i.e. hashed implementation of EXCEPT, INTERSECT with
# spill to disk (chunk files) if hash table can't fit in memory
# and fallback to de-duplication via keyed temporary table as
# last resort. The latter is tested with error injection in
# query_expression_debug.
#
# The size of the VARCHAR column is an argument (VARCHAR(60)) so
# we can test short varchar fields as well as blobs.
#
CREATE TABLE t(i INT, d DATE, c VARCHAR(60) CHARSET latin1) ENGINE=innodb;
set @@cte_max_recursion_depth = 100000;
INSERT INTO t
WITH RECURSIVE cte AS (
SELECT 0 AS i, '2022-04-30' AS d, 'abracadabra' as c
UNION
SELECT 1 AS i, '2022-04-30' AS d, 'rabarbra' as c
UNION
SELECT i+2, d, c FROM cte
WHERE i+2 < 65536/2
)
SELECT i,d,c FROM cte;
set @@cte_max_recursion_depth = default;
INSERT INTO t select i, d, c FROM  t;
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
SELECT COUNT(*) FROM t;
COUNT(*)
65536
SELECT COUNT(*) FROM (SELECT DISTINCT i,d,c FROM t) derived;
COUNT(*)
32768
FLUSH STATUS;
SHOW STATUS LIKE 'Created_tmp_files%';
Variable_name	Value
Created_tmp_files	0
SET SESSION optimizer_switch = 'hash_set_operations=off';
SELECT * FROM (SELECT * FROM t INTERSECT SELECT * FROM t) AS derived ORDER BY i LIMIT 20;
i	d	c
0	2022-04-30	abracadabra
1	2022-04-30	rabarbra
2	2022-04-30	abracadabra
3	2022-04-30	rabarbra
4	2022-04-30	abracadabra
5	2022-04-30	rabarbra
6	2022-04-30	abracadabra
7	2022-04-30	rabarbra
8	2022-04-30	abracadabra
9	2022-04-30	rabarbra
10	2022-04-30	abracadabra
11	2022-04-30	rabarbra
12	2022-04-30	abracadabra
13	2022-04-30	rabarbra
14	2022-04-30	abracadabra
15	2022-04-30	rabarbra
16	2022-04-30	abracadabra
17	2022-04-30	rabarbra
18	2022-04-30	abracadabra
19	2022-04-30	rabarbra
SHOW STATUS LIKE 'Created_tmp_files%';
Variable_name	Value
Created_tmp_files	0
SET SESSION optimizer_switch = 'hash_set_operations=default';
SELECT * FROM (SELECT * FROM t INTERSECT SELECT * FROM t) AS derived ORDER BY i LIMIT 20;
i	d	c
0	2022-04-30	abracadabra
1	2022-04-30	rabarbra
2	2022-04-30	abracadabra
3	2022-04-30	rabarbra
4	2022-04-30	abracadabra
5	2022-04-30	rabarbra
6	2022-04-30	abracadabra
7	2022-04-30	rabarbra
8	2022-04-30	abracadabra
9	2022-04-30	rabarbra
10	2022-04-30	abracadabra
11	2022-04-30	rabarbra
12	2022-04-30	abracadabra
13	2022-04-30	rabarbra
14	2022-04-30	abracadabra
15	2022-04-30	rabarbra
16	2022-04-30	abracadabra
17	2022-04-30	rabarbra
18	2022-04-30	abracadabra
19	2022-04-30	rabarbra
SHOW STATUS LIKE 'Created_tmp_files%';
Variable_name	Value
Created_tmp_files	96
FLUSH STATUS;
SET SESSION set_operations_buffer_size = 16384;
# The number of Created_tmp_files will be 386, which is
# 128*2 (build, probe chunks) for left operand + 128 (probe) for right operand
# + 2 (REMAININGINPUT for left and right operand) = 386
# The last 128 (probe chunk files for right operand), could have been avoided
# if we had a way to reset IO_CACHE files; now we do a close and open, cf.
# HashJoinChunk::Init.
SELECT * FROM (SELECT * FROM t INTERSECT SELECT * FROM t) AS derived ORDER BY i LIMIT 20;
i	d	c
0	2022-04-30	abracadabra
1	2022-04-30	rabarbra
2	2022-04-30	abracadabra
3	2022-04-30	rabarbra
4	2022-04-30	abracadabra
5	2022-04-30	rabarbra
6	2022-04-30	abracadabra
7	2022-04-30	rabarbra
8	2022-04-30	abracadabra
9	2022-04-30	rabarbra
10	2022-04-30	abracadabra
11	2022-04-30	rabarbra
12	2022-04-30	abracadabra
13	2022-04-30	rabarbra
14	2022-04-30	abracadabra
15	2022-04-30	rabarbra
16	2022-04-30	abracadabra
17	2022-04-30	rabarbra
18	2022-04-30	abracadabra
19	2022-04-30	rabarbra
SHOW STATUS LIKE 'Created_tmp_files%';
Variable_name	Value
Created_tmp_files	386
SET SESSION set_operations_buffer_size = default;
# Test spill correctness and secondary overflow, the latter
# using injection. This query with set_operations_buffer_size ==
# 16384 will give 128 chunk files. With default setting it does
# not spill to disk.
SET SESSION optimizer_switch = 'hash_set_operations=off';
CREATE TABLE no_hashing AS SELECT * FROM t INTERSECT SELECT * FROM t;
SET SESSION optimizer_switch = 'hash_set_operations=default';
CREATE TABLE hashing_no_spill AS SELECT * FROM t INTERSECT SELECT * FROM t;
# Compare old approach (no hashing) with hashing
SET SESSION optimizer_switch = 'hash_set_operations=off';
SELECT COUNT(*) FROM (SELECT * FROM no_hashing EXCEPT ALL SELECT * FROM hashing_no_spill) derived;
COUNT(*)
0
SELECT COUNT(*) FROM (SELECT * FROM hashing_no_spill EXCEPT ALL SELECT * FROM no_hashing) derived;
COUNT(*)
0
SET SESSION optimizer_switch = 'hash_set_operations=default';
SET SESSION set_operations_buffer_size = 16384;
CREATE TABLE hashing_spill AS SELECT * FROM t INTERSECT SELECT * FROM t;
# Compare old approach (no hashing) with hashing w/spill
SET SESSION optimizer_switch = 'hash_set_operations=off';
SELECT COUNT(*) FROM (SELECT * FROM no_hashing EXCEPT ALL SELECT * FROM hashing_spill) derived;
COUNT(*)
0
SELECT COUNT(*) FROM (SELECT * FROM hashing_spill EXCEPT ALL SELECT * FROM no_hashing) derived;
COUNT(*)
0
SET SESSION optimizer_switch = 'hash_set_operations=default';
SET SESSION set_operations_buffer_size = default;
DROP TABLE no_hashing, hashing_no_spill, hashing_spill;
#
# Test overflow in resulting tmp table
#
SET SESSION optimizer_trace="enabled=on";
# a) When we spill
SET SESSION tmp_table_size=100000;
SELECT * FROM (SELECT * FROM t INTERSECT SELECT * FROM t) AS derived ORDER BY i LIMIT 20;
i	d	c
0	2022-04-30	abracadabra
1	2022-04-30	rabarbra
2	2022-04-30	abracadabra
3	2022-04-30	rabarbra
4	2022-04-30	abracadabra
5	2022-04-30	rabarbra
6	2022-04-30	abracadabra
7	2022-04-30	rabarbra
8	2022-04-30	abracadabra
9	2022-04-30	rabarbra
10	2022-04-30	abracadabra
11	2022-04-30	rabarbra
12	2022-04-30	abracadabra
13	2022-04-30	rabarbra
14	2022-04-30	abracadabra
15	2022-04-30	rabarbra
16	2022-04-30	abracadabra
17	2022-04-30	rabarbra
18	2022-04-30	abracadabra
19	2022-04-30	rabarbra
SELECT JSON_PRETTY(JSON_EXTRACT(trace,"$.steps[*].join_execution"))
FROM information_schema.optimizer_trace;
JSON_PRETTY(JSON_EXTRACT(trace,"$.steps[*].join_execution"))
[
  {
    "steps": [
      {
        "creating_tmp_table": {
          "tmp_table_info": {
            "table": "derived",
            "columns": 5,
            "location": "TempTable",
            "key_length": 0,
            "row_length": 86,
            "unique_constraint": true,
            "makes_grouped_rows": false,
            "in_plan_at_position": 0,
            "cannot_insert_duplicates": true
          }
        }
      },
      {
        "materialize for intersect": {
          "steps": [
            {
              "de-duplicate with hash table": {
                "steps": [
                  {
                    "spill to disk initiated": {
                      "chunk sets": 1,
                      "chunk files": 64
                    }
                  }
                ]
              }
            }
          ],
          "select#": 2
        }
      },
      {
        "materialize for intersect": {
          "steps": [
            {
              "de-duplicate with hash table": {
                "steps": [
                  {
                    "converting_tmp_table_to_ondisk": {
                      "cause": "memory_table_size_exceeded",
                      "tmp_table_info": {
                        "table": "derived",
                        "columns": 5,
                        "location": "disk (InnoDB)",
                        "key_length": 0,
                        "row_length": 86,
                        "record_format": "fixed",
                        "unique_constraint": true,
                        "makes_grouped_rows": false,
                        "in_plan_at_position": 0,
                        "cannot_insert_duplicates": true
                      }
                    }
                  }
                ]
              }
            }
          ],
          "select#": 3
        }
      },
      {
        "sorting_table": "derived",
        "filesort_summary": {
          "key_size": 9,
          "row_size": 94,
          "sort_mode": "<fixed_sort_key, additional_fields>",
          "num_rows_found": 32768,
          "sort_algorithm": "std::sort",
          "memory_available": 262144,
          "peak_memory_used": "elided",
          "num_rows_estimate": "elided",
          "max_rows_per_buffer": 21,
          "unpacked_addon_fields": "using_priority_queue",
          "num_initial_chunks_spilled_to_disk": "elided"
        },
        "filesort_execution": [],
        "filesort_information": [
          {
            "direction": "asc",
            "expression": "`derived`.`i`"
          }
        ],
        "filesort_priority_queue_optimization": {
          "limit": 20,
          "chosen": true
        }
      }
    ],
    "select#": 1
  }
]
# b) With secondary overflow (part of query_expression_debug
#    since it requires error injection)
# c) When we can hash in memory
SET SESSION set_operations_buffer_size=2621440;
SELECT * FROM (SELECT * FROM t INTERSECT SELECT * FROM t) AS derived ORDER BY i LIMIT 20;
i	d	c
0	2022-04-30	abracadabra
1	2022-04-30	rabarbra
2	2022-04-30	abracadabra
3	2022-04-30	rabarbra
4	2022-04-30	abracadabra
5	2022-04-30	rabarbra
6	2022-04-30	abracadabra
7	2022-04-30	rabarbra
8	2022-04-30	abracadabra
9	2022-04-30	rabarbra
10	2022-04-30	abracadabra
11	2022-04-30	rabarbra
12	2022-04-30	abracadabra
13	2022-04-30	rabarbra
14	2022-04-30	abracadabra
15	2022-04-30	rabarbra
16	2022-04-30	abracadabra
17	2022-04-30	rabarbra
18	2022-04-30	abracadabra
19	2022-04-30	rabarbra
SELECT JSON_PRETTY(JSON_EXTRACT(trace,"$.steps[*].join_execution"))
FROM information_schema.optimizer_trace;
JSON_PRETTY(JSON_EXTRACT(trace,"$.steps[*].join_execution"))
[
  {
    "steps": [
      {
        "creating_tmp_table": {
          "tmp_table_info": {
            "table": "derived",
            "columns": 5,
            "location": "TempTable",
            "key_length": 0,
            "row_length": 86,
            "unique_constraint": true,
            "makes_grouped_rows": false,
            "in_plan_at_position": 0,
            "cannot_insert_duplicates": true
          }
        }
      },
      {
        "materialize for intersect": {
          "steps": [
            {
              "de-duplicate with hash table": {
                "steps": []
              }
            }
          ],
          "select#": 2
        }
      },
      {
        "materialize for intersect": {
          "steps": [
            {
              "de-duplicate with hash table": {
                "steps": [
                  {
                    "converting_tmp_table_to_ondisk": {
                      "cause": "memory_table_size_exceeded",
                      "tmp_table_info": {
                        "table": "derived",
                        "columns": 5,
                        "location": "disk (InnoDB)",
                        "key_length": 0,
                        "row_length": 86,
                        "record_format": "fixed",
                        "unique_constraint": true,
                        "makes_grouped_rows": false,
                        "in_plan_at_position": 0,
                        "cannot_insert_duplicates": true
                      }
                    }
                  }
                ]
              }
            }
          ],
          "select#": 3
        }
      },
      {
        "sorting_table": "derived",
        "filesort_summary": {
          "key_size": 9,
          "row_size": 94,
          "sort_mode": "<fixed_sort_key, additional_fields>",
          "num_rows_found": 32768,
          "sort_algorithm": "std::sort",
          "memory_available": 262144,
          "peak_memory_used": "elided",
          "num_rows_estimate": "elided",
          "max_rows_per_buffer": 21,
          "unpacked_addon_fields": "using_priority_queue",
          "num_initial_chunks_spilled_to_disk": "elided"
        },
        "filesort_execution": [],
        "filesort_information": [
          {
            "direction": "asc",
            "expression": "`derived`.`i`"
          }
        ],
        "filesort_priority_queue_optimization": {
          "limit": 20,
          "chosen": true
        }
      }
    ],
    "select#": 1
  }
]
SET SESSION tmp_table_size=default;
SET SESSION set_operations_buffer_size=default;
SET SESSION optimizer_trace="enabled=default";
DROP TABLE t;
# Ditto, now with rows containing a blob
#
# WL#15257 Enhanced performance for set operations: INTERSECT,
# EXCEPT i.e. hashed implementation of EXCEPT, INTERSECT with
# spill to disk (chunk files) if hash table can't fit in memory
# and fallback to de-duplication via keyed temporary table as
# last resort. The latter is tested with error injection in
# query_expression_debug.
#
# The size of the VARCHAR column is an argument (TEXT) so
# we can test short varchar fields as well as blobs.
#
CREATE TABLE t(i INT, d DATE, c TEXT CHARSET latin1) ENGINE=innodb;
set @@cte_max_recursion_depth = 100000;
INSERT INTO t
WITH RECURSIVE cte AS (
SELECT 0 AS i, '2022-04-30' AS d, 'abracadabra' as c
UNION
SELECT 1 AS i, '2022-04-30' AS d, 'rabarbra' as c
UNION
SELECT i+2, d, c FROM cte
WHERE i+2 < 65536/2
)
SELECT i,d,c FROM cte;
set @@cte_max_recursion_depth = default;
INSERT INTO t select i, d, c FROM  t;
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
SELECT COUNT(*) FROM t;
COUNT(*)
65536
SELECT COUNT(*) FROM (SELECT DISTINCT i,d,c FROM t) derived;
COUNT(*)
32768
FLUSH STATUS;
SHOW STATUS LIKE 'Created_tmp_files%';
Variable_name	Value
Created_tmp_files	0
SET SESSION optimizer_switch = 'hash_set_operations=off';
SELECT * FROM (SELECT * FROM t INTERSECT SELECT * FROM t) AS derived ORDER BY i LIMIT 20;
i	d	c
0	2022-04-30	abracadabra
1	2022-04-30	rabarbra
2	2022-04-30	abracadabra
3	2022-04-30	rabarbra
4	2022-04-30	abracadabra
5	2022-04-30	rabarbra
6	2022-04-30	abracadabra
7	2022-04-30	rabarbra
8	2022-04-30	abracadabra
9	2022-04-30	rabarbra
10	2022-04-30	abracadabra
11	2022-04-30	rabarbra
12	2022-04-30	abracadabra
13	2022-04-30	rabarbra
14	2022-04-30	abracadabra
15	2022-04-30	rabarbra
16	2022-04-30	abracadabra
17	2022-04-30	rabarbra
18	2022-04-30	abracadabra
19	2022-04-30	rabarbra
SHOW STATUS LIKE 'Created_tmp_files%';
Variable_name	Value
Created_tmp_files	2
SET SESSION optimizer_switch = 'hash_set_operations=default';
SELECT * FROM (SELECT * FROM t INTERSECT SELECT * FROM t) AS derived ORDER BY i LIMIT 20;
i	d	c
0	2022-04-30	abracadabra
1	2022-04-30	rabarbra
2	2022-04-30	abracadabra
3	2022-04-30	rabarbra
4	2022-04-30	abracadabra
5	2022-04-30	rabarbra
6	2022-04-30	abracadabra
7	2022-04-30	rabarbra
8	2022-04-30	abracadabra
9	2022-04-30	rabarbra
10	2022-04-30	abracadabra
11	2022-04-30	rabarbra
12	2022-04-30	abracadabra
13	2022-04-30	rabarbra
14	2022-04-30	abracadabra
15	2022-04-30	rabarbra
16	2022-04-30	abracadabra
17	2022-04-30	rabarbra
18	2022-04-30	abracadabra
19	2022-04-30	rabarbra
SHOW STATUS LIKE 'Created_tmp_files%';
Variable_name	Value
Created_tmp_files	100
FLUSH STATUS;
SET SESSION set_operations_buffer_size = 16384;
# The number of Created_tmp_files will be 386, which is
# 128*2 (build, probe chunks) for left operand + 128 (probe) for right operand
# + 2 (REMAININGINPUT for left and right operand) = 386
# The last 128 (probe chunk files for right operand), could have been avoided
# if we had a way to reset IO_CACHE files; now we do a close and open, cf.
# HashJoinChunk::Init.
SELECT * FROM (SELECT * FROM t INTERSECT SELECT * FROM t) AS derived ORDER BY i LIMIT 20;
i	d	c
0	2022-04-30	abracadabra
1	2022-04-30	rabarbra
2	2022-04-30	abracadabra
3	2022-04-30	rabarbra
4	2022-04-30	abracadabra
5	2022-04-30	rabarbra
6	2022-04-30	abracadabra
7	2022-04-30	rabarbra
8	2022-04-30	abracadabra
9	2022-04-30	rabarbra
10	2022-04-30	abracadabra
11	2022-04-30	rabarbra
12	2022-04-30	abracadabra
13	2022-04-30	rabarbra
14	2022-04-30	abracadabra
15	2022-04-30	rabarbra
16	2022-04-30	abracadabra
17	2022-04-30	rabarbra
18	2022-04-30	abracadabra
19	2022-04-30	rabarbra
SHOW STATUS LIKE 'Created_tmp_files%';
Variable_name	Value
Created_tmp_files	388
SET SESSION set_operations_buffer_size = default;
# Test spill correctness and secondary overflow, the latter
# using injection. This query with set_operations_buffer_size ==
# 16384 will give 128 chunk files. With default setting it does
# not spill to disk.
SET SESSION optimizer_switch = 'hash_set_operations=off';
CREATE TABLE no_hashing AS SELECT * FROM t INTERSECT SELECT * FROM t;
SET SESSION optimizer_switch = 'hash_set_operations=default';
CREATE TABLE hashing_no_spill AS SELECT * FROM t INTERSECT SELECT * FROM t;
# Compare old approach (no hashing) with hashing
SET SESSION optimizer_switch = 'hash_set_operations=off';
SELECT COUNT(*) FROM (SELECT * FROM no_hashing EXCEPT ALL SELECT * FROM hashing_no_spill) derived;
COUNT(*)
0
SELECT COUNT(*) FROM (SELECT * FROM hashing_no_spill EXCEPT ALL SELECT * FROM no_hashing) derived;
COUNT(*)
0
SET SESSION optimizer_switch = 'hash_set_operations=default';
SET SESSION set_operations_buffer_size = 16384;
CREATE TABLE hashing_spill AS SELECT * FROM t INTERSECT SELECT * FROM t;
# Compare old approach (no hashing) with hashing w/spill
SET SESSION optimizer_switch = 'hash_set_operations=off';
SELECT COUNT(*) FROM (SELECT * FROM no_hashing EXCEPT ALL SELECT * FROM hashing_spill) derived;
COUNT(*)
0
SELECT COUNT(*) FROM (SELECT * FROM hashing_spill EXCEPT ALL SELECT * FROM no_hashing) derived;
COUNT(*)
0
SET SESSION optimizer_switch = 'hash_set_operations=default';
SET SESSION set_operations_buffer_size = default;
DROP TABLE no_hashing, hashing_no_spill, hashing_spill;
#
# Test overflow in resulting tmp table
#
SET SESSION optimizer_trace="enabled=on";
# a) When we spill
SET SESSION tmp_table_size=100000;
SELECT * FROM (SELECT * FROM t INTERSECT SELECT * FROM t) AS derived ORDER BY i LIMIT 20;
i	d	c
0	2022-04-30	abracadabra
1	2022-04-30	rabarbra
2	2022-04-30	abracadabra
3	2022-04-30	rabarbra
4	2022-04-30	abracadabra
5	2022-04-30	rabarbra
6	2022-04-30	abracadabra
7	2022-04-30	rabarbra
8	2022-04-30	abracadabra
9	2022-04-30	rabarbra
10	2022-04-30	abracadabra
11	2022-04-30	rabarbra
12	2022-04-30	abracadabra
13	2022-04-30	rabarbra
14	2022-04-30	abracadabra
15	2022-04-30	rabarbra
16	2022-04-30	abracadabra
17	2022-04-30	rabarbra
18	2022-04-30	abracadabra
19	2022-04-30	rabarbra
SELECT JSON_PRETTY(JSON_EXTRACT(trace,"$.steps[*].join_execution"))
FROM information_schema.optimizer_trace;
JSON_PRETTY(JSON_EXTRACT(trace,"$.steps[*].join_execution"))
[
  {
    "steps": [
      {
        "creating_tmp_table": {
          "tmp_table_info": {
            "table": "derived",
            "columns": 5,
            "location": "TempTable",
            "key_length": 0,
            "row_length": 34,
            "unique_constraint": true,
            "makes_grouped_rows": false,
            "in_plan_at_position": 0,
            "cannot_insert_duplicates": true
          }
        }
      },
      {
        "materialize for intersect": {
          "steps": [
            {
              "de-duplicate with hash table": {
                "steps": [
                  {
                    "spill to disk initiated": {
                      "chunk sets": 1,
                      "chunk files": 64
                    }
                  }
                ]
              }
            }
          ],
          "select#": 2
        }
      },
      {
        "materialize for intersect": {
          "steps": [
            {
              "de-duplicate with hash table": {
                "steps": [
                  {
                    "converting_tmp_table_to_ondisk": {
                      "cause": "memory_table_size_exceeded",
                      "tmp_table_info": {
                        "table": "derived",
                        "columns": 5,
                        "location": "disk (InnoDB)",
                        "key_length": 0,
                        "row_length": 34,
                        "record_format": "fixed",
                        "unique_constraint": true,
                        "makes_grouped_rows": false,
                        "in_plan_at_position": 0,
                        "cannot_insert_duplicates": true
                      }
                    }
                  }
                ]
              }
            }
          ],
          "select#": 3
        }
      },
      {
        "sorting_table": "derived",
        "filesort_summary": {
          "key_size": 9,
          "row_size": 65574,
          "sort_mode": "<fixed_sort_key, packed_additional_fields>",
          "num_rows_found": 32768,
          "sort_algorithm": "std::stable_sort",
          "memory_available": 262144,
          "peak_memory_used": "elided",
          "num_rows_estimate": "elided",
          "max_rows_per_buffer": 3,
          "num_initial_chunks_spilled_to_disk": "elided"
        },
        "filesort_execution": [],
        "filesort_information": [
          {
            "direction": "asc",
            "expression": "`derived`.`i`"
          }
        ],
        "filesort_priority_queue_optimization": {
          "limit": 20
        }
      }
    ],
    "select#": 1
  }
]
# b) With secondary overflow (part of query_expression_debug
#    since it requires error injection)
# c) When we can hash in memory
SET SESSION set_operations_buffer_size=2621440;
SELECT * FROM (SELECT * FROM t INTERSECT SELECT * FROM t) AS derived ORDER BY i LIMIT 20;
i	d	c
0	2022-04-30	abracadabra
1	2022-04-30	rabarbra
2	2022-04-30	abracadabra
3	2022-04-30	rabarbra
4	2022-04-30	abracadabra
5	2022-04-30	rabarbra
6	2022-04-30	abracadabra
7	2022-04-30	rabarbra
8	2022-04-30	abracadabra
9	2022-04-30	rabarbra
10	2022-04-30	abracadabra
11	2022-04-30	rabarbra
12	2022-04-30	abracadabra
13	2022-04-30	rabarbra
14	2022-04-30	abracadabra
15	2022-04-30	rabarbra
16	2022-04-30	abracadabra
17	2022-04-30	rabarbra
18	2022-04-30	abracadabra
19	2022-04-30	rabarbra
SELECT JSON_PRETTY(JSON_EXTRACT(trace,"$.steps[*].join_execution"))
FROM information_schema.optimizer_trace;
JSON_PRETTY(JSON_EXTRACT(trace,"$.steps[*].join_execution"))
[
  {
    "steps": [
      {
        "creating_tmp_table": {
          "tmp_table_info": {
            "table": "derived",
            "columns": 5,
            "location": "TempTable",
            "key_length": 0,
            "row_length": 34,
            "unique_constraint": true,
            "makes_grouped_rows": false,
            "in_plan_at_position": 0,
            "cannot_insert_duplicates": true
          }
        }
      },
      {
        "materialize for intersect": {
          "steps": [
            {
              "de-duplicate with hash table": {
                "steps": []
              }
            }
          ],
          "select#": 2
        }
      },
      {
        "materialize for intersect": {
          "steps": [
            {
              "de-duplicate with hash table": {
                "steps": [
                  {
                    "converting_tmp_table_to_ondisk": {
                      "cause": "memory_table_size_exceeded",
                      "tmp_table_info": {
                        "table": "derived",
                        "columns": 5,
                        "location": "disk (InnoDB)",
                        "key_length": 0,
                        "row_length": 34,
                        "record_format": "fixed",
                        "unique_constraint": true,
                        "makes_grouped_rows": false,
                        "in_plan_at_position": 0,
                        "cannot_insert_duplicates": true
                      }
                    }
                  }
                ]
              }
            }
          ],
          "select#": 3
        }
      },
      {
        "sorting_table": "derived",
        "filesort_summary": {
          "key_size": 9,
          "row_size": 65574,
          "sort_mode": "<fixed_sort_key, packed_additional_fields>",
          "num_rows_found": 32768,
          "sort_algorithm": "std::stable_sort",
          "memory_available": 262144,
          "peak_memory_used": "elided",
          "num_rows_estimate": "elided",
          "max_rows_per_buffer": 3,
          "num_initial_chunks_spilled_to_disk": "elided"
        },
        "filesort_execution": [],
        "filesort_information": [
          {
            "direction": "asc",
            "expression": "`derived`.`i`"
          }
        ],
        "filesort_priority_queue_optimization": {
          "limit": 20
        }
      }
    ],
    "select#": 1
  }
]
SET SESSION tmp_table_size=default;
SET SESSION set_operations_buffer_size=default;
SET SESSION optimizer_trace="enabled=default";
DROP TABLE t;
# query_expression.inc tests spilling to disk with two table shapes:
#
#     CREATE TABLE t(i INT, d DATE, c VARCHAR(6) CHARSET latin1
#     CREATE TABLE t(i INT, d DATE, c TEXT CHARSET latin1
#
# At least on MacOS debug builds, this triggers an overflow when we
# attempt store the row payload in a mem_root.  But there is another
# overflow situation: when extending the Robin Hood hash map (it grows
# by doubling as needed on the heap).  We track this memory usage
# against set_operations_buffer_size. The next test hopefully triggers this
# logic path instead.  The shorter record size (a 32 bits integer)
# makes this situation more likely.  Verified with on MacOS build.
CREATE TABLE t(i INT) ENGINE=innodb;
INSERT INTO t
WITH RECURSIVE cte AS (
SELECT 0 AS i
UNION
SELECT 1 AS i
UNION
SELECT i+2 FROM cte
WHERE i+2 < 1024
)
SELECT i FROM cte;
INSERT INTO t select i FROM  t;
ANALYZE TABLE t;
Table	Op	Msg_type	Msg_text
test.t	analyze	status	OK
SET SESSION set_operations_buffer_size = 16384;
SELECT * FROM (SELECT * FROM t INTERSECT SELECT * FROM t) AS derived ORDER BY i LIMIT 2;
i
0
1
SET SESSION set_operations_buffer_size = default;
DROP TABLE t;
