--source include/no_valgrind_without_big.inc

--disable_warnings
let $ENGINE=`select variable_value from performance_schema.global_variables where variable_name='DEFAULT_STORAGE_ENGINE'`;
--enable_warnings

--disable_warnings
DROP TABLE IF EXISTS t1;
--enable_warnings
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 SMALLINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 INTEGER  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BIGINT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 REAL  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DOUBLE  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 FLOAT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DECIMAL  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 NUMERIC  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 DATE  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIME  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TIMESTAMP  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 YEAR  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYBLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 BLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMBLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGBLOB  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TINYTEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 TEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 MEDIUMTEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 CHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 BINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL);
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; 
ALTER TABLE t1 ADD c2 LONGTEXT  NOT NULL FIRST;
SHOW TABLES; 
--replace_result $ENGINE ENGINE " PAGE_CHECKSUM=0" ""
 SHOW CREATE TABLE t1; DROP TABLE t1; SHOW TABLES;
