--echo # Test of SQL window functions LEAD/LAG
--echo # ----------------------------------------------------------------------

SET NAMES utf8mb4;

SELECT LEAD(6, 0)    OVER ();
SELECT LEAD(NULL, 0) OVER ();
SELECT LEAD(6, 0)    OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SELECT LEAD(NULL, 0) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SELECT LEAD(6, 1)    OVER ();
SELECT LEAD(NULL, 1) OVER ();
SELECT LEAD(6, 1)    OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SELECT LEAD(NULL, 1) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SELECT LEAD(6, 1, 7)    OVER ();
SELECT LEAD(NULL, 1, 7) OVER ();
SELECT LEAD(6, 1, 7)    OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SELECT LEAD(NULL, 1, 7) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);

SELECT LAG(6, 0)    OVER ();
SELECT LAG(NULL, 0) OVER ();
SELECT LAG(6, 0)    OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SELECT LAG(NULL, 0) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SELECT LAG(6, 1)    OVER ();
SELECT LAG(NULL, 1) OVER ();
SELECT LAG(6, 1)    OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SELECT LAG(NULL, 1) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SELECT LAG(6, 1, 7)    OVER ();
SELECT LAG(NULL, 1, 7) OVER ();
SELECT LAG(6, 1, 7)    OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);
SELECT LAG(NULL, 1, 7) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW);



CREATE TABLE t1 (d DOUBLE, id INT, sex CHAR(1), n INT NOT NULL AUTO_INCREMENT, PRIMARY KEY(n));
INSERT INTO t1(d, id, sex) VALUES (1.0, 1, 'M'),
                      (2.0, 2, 'F'),
                      (3.0, 3, 'F'),
                      (4.0, 4, 'F'),
                      (5.0, 5, 'M'),
                      (NULL, NULL, 'M'),
                      (10.0, 10, NULL),
                      (10.0, 10, NULL),
                      (11.0, 11, NULL);

--error ER_PARSE_ERROR
SELECT id, sex, LEAD(id, -1) RESPECT NULLS OVER () FROM t1;
--error ER_PARSE_ERROR
SELECT id, sex, LEAD(id, 1.2) RESPECT NULLS OVER () FROM t1;
--error ER_PARSE_ERROR
SELECT id, sex, LEAD(id, 'a') RESPECT NULLS OVER () FROM t1;
--error ER_PARSE_ERROR
SELECT id, sex, LEAD(id, NULL) RESPECT NULLS OVER () FROM t1;

PREPARE p FROM "SELECT id, sex, LEAD(id, ?) OVER () FROM t1";
SET @p1= 3;
EXECUTE p USING @p1;
SET @p1= -3;
--error ER_WRONG_ARGUMENTS
EXECUTE p USING @p1;
SET @p1= 'a';
--error ER_WRONG_ARGUMENTS
EXECUTE p USING @p1;

PREPARE p FROM "SELECT id, sex, LEAD(id+?, ?, ?) RESPECT NULLS OVER () FROM t1";
SET @p1= 8;
SET @p2= 3;
SET @p3= 7;
EXECUTE p USING @p1, @p2, @p3;
EXECUTE p USING @p1, @p2, @p3;

DROP PREPARE p;

--error ER_NOT_SUPPORTED_YET
SELECT id, sex, LEAD(id, 1, 10) IGNORE NULLS OVER () FROM t1;

--echo Ok, default semantics:
--echo First without default:
SELECT id, sex, LEAD(id, 1) RESPECT NULLS OVER () FROM t1;
SELECT id, sex, LAG(id, 1) RESPECT NULLS OVER () FROM t1;
SELECT id, sex, LEAD(id, 0) RESPECT NULLS OVER () FROM t1;
--echo Now with default:
SELECT id, sex, LEAD(id, 1, id) RESPECT NULLS OVER () FROM t1;
SELECT id, sex, LAG(id, 1, id) RESPECT NULLS OVER () FROM t1;
SELECT id, sex, LEAD(id, 0, 7) RESPECT NULLS OVER () FROM t1;

SELECT n, id, LEAD(id, 1, 3) OVER
    (ORDER BY id DESC, n ASC ROWS BETWEEN  CURRENT ROW AND 2 FOLLOWING) L FROM t1;
SELECT n, id,  LAG(id, 0, n*n) OVER
    (ORDER BY id DESC, n ASC ROWS BETWEEN  CURRENT ROW AND 2 FOLLOWING) L FROM t1;
SELECT n, id,  LAG(id, 1, n*n) OVER
    (ORDER BY id DESC, n ASC ROWS BETWEEN  CURRENT ROW AND 2 FOLLOWING) L FROM t1;
SELECT n, id,  LEAD(id, 1, n*n) OVER
    (ORDER BY id DESC, n ASC ROWS BETWEEN  CURRENT ROW AND 2 FOLLOWING) L FROM t1;


--echo Check imcompatible character sets
CREATE TABLE t (c1 CHAR(10) CHARACTER SET big5,
                i INT,
                c2 VARCHAR(10) CHARACTER SET euckr);
--error ER_CANT_AGGREGATE_2COLLATIONS
SELECT c1, c2, LEAD(c1, 0, c2) OVER () l0 FROM t;

DROP TABLE t;

--echo Check default char set & collation promotion to result
CREATE TABLE t (c1 CHAR(10) CHARACTER SET utf8mb4,
                i INT,
                c2 VARCHAR(10) CHARACTER SET latin1);

INSERT INTO t VALUES('A', 1, '1');
INSERT INTO t VALUES('A', 3, '3');
INSERT INTO t VALUES(x'F09F90AC' /* dolphin */, 5, null);
INSERT INTO t VALUES('A', 5, null);
INSERT INTO t VALUES(null, 10, '0');
--echo Result sets should contain dolphin in columns c1 and l1.
SELECT c1, c2, LEAD(c1, 0, c2) OVER () l0 FROM t;
SELECT c1, c2, LEAD(c1, 1, c2) OVER () l1 FROM t;
SELECT c1, c2, LEAD(c2, 1, c1) OVER () l1 FROM t;
--echo Use CREATE TABLE AS to show type of the resulting LEAD function
CREATE TABLE tt AS SELECT LEAD(c1, 0, c2) OVER () c FROM t;
CREATE TABLE ts AS SELECT LEAD(c2, 1, c1) OVER () c FROM t;
--echo Both tables should have c as VARCHAR(10) CHARACTER SET utf8mb4
--echo even though only c1 has that type.
SHOW CREATE TABLE tt;
SHOW CREATE TABLE ts;
DROP TABLE t, tt, ts;

--echo Defaults: check non obvious type pairs, comparing with IFNULL
--echo whose type reconciliation behavior we emulate when combining
--echo types in LEAD/LAG with default value.

CREATE TABLE t (c1 VARCHAR(10),
                j1 JSON,
                g1 POINT,
                i1 INT,
                b1 BLOB,
                d1 DOUBLE,
                e1 DECIMAL(5,4),
                e2 DECIMAL(5,2));

INSERT INTO t VALUES (null, '[6]', ST_POINTFROMTEXT('POINT(6 6)'), 6, '6', 6.0, 10.0/3, 20.0/3),
                     ('7', null ,   ST_POINTFROMTEXT('POINT(7 7)'), 7, '7', 7.0, 10.0/3, 20.0/3),
                     ('8', '[8]' ,  null,                           7, '8', 8.0, 10.0/3, 20.0/3),
                     ('9', '[9]' , ST_POINTFROMTEXT('POINT(9 9)'), null, '9', 9.0, 10.0/3, 20.0/3),
                     ('0', '[0]' , ST_POINTFROMTEXT('POINT(0 0)'), 0, null, 0.0, 10.0/3, 20.0/3),
                     ('1', '[1]' , ST_POINTFROMTEXT('POINT(1 1)'), 1, '1', null, 10.0/3, 20.0/3),
                     ('2', '[2]' , ST_POINTFROMTEXT('POINT(2 2)'), 2, '2', 2.0, null, 20.0/3),
                     ('3', '[3]' , ST_POINTFROMTEXT('POINT(3 3)'), 3, '3', 3.0, 10.0/3, null);

SELECT LEAD(c1, 100, j1) OVER () lcj, IFNULL(c1, j1) ifn_cj FROM t;
SELECT LEAD(j1, 100, j1) OVER () lcj, IFNULL(c1, j1) ifn_cj FROM t;
SELECT HEX(LEAD(c1, 100, g1) OVER ()) lcg, HEX(IFNULL(c1, g1)) ifn_cg FROM t;
SELECT LEAD(c1, 100, i1) OVER () lci, IFNULL(c1, i1) ifn_ci FROM t;
SELECT LEAD(c1, 100, b1) OVER () lcb, IFNULL(c1, b1) ifn_cb FROM t;
SELECT LEAD(c1, 100, d1) OVER () lcd, IFNULL(c1, d1) ifn_cd FROM t;
SELECT LEAD(c1, 100, e1) OVER () lce1, IFNULL(c1, e1) ifn_ce1 FROM t;
SELECT LEAD(c1, 100, e2) OVER () lce2, IFNULL(c1, e2) ifn_ce2 FROM t;

SELECT LEAD(j1, 100, c1) OVER () ljc, IFNULL(j1, c1) ifn_jc FROM t;
SELECT HEX(LEAD(j1, 100, g1) OVER ()) ljg, HEX(IFNULL(j1, g1)) ifn_jg FROM t;
SELECT LEAD(j1, 100, i1) OVER () lji, IFNULL(j1, i1) ifn_ji FROM t;
SELECT LEAD(j1, 100, b1) OVER () ljb, IFNULL(j1, b1) ifn_jb FROM t;
SELECT LEAD(j1, 100, d1) OVER () ljd, IFNULL(j1, d1) ifn_jd FROM t;
SELECT LEAD(j1, 100, e1) OVER () lje1, IFNULL(j1, e1) ifn_je1 FROM t;
SELECT LEAD(j1, 100, e2) OVER () lje2, IFNULL(j1, e2) ifn_je2 FROM t;

SELECT HEX(LEAD(g1, 100, c1) OVER ()) lgc, HEX(IFNULL(g1, c1)) ifn_gc FROM t;
SELECT HEX(LEAD(g1, 100, j1) OVER ()) lgj, HEX(IFNULL(g1, j1)) ifn_gj FROM t;
SELECT HEX(LEAD(g1, 100, i1) OVER ()) lgi, HEX(IFNULL(g1, i1)) ifn_gi FROM t;
SELECT HEX(LEAD(g1, 100, b1) OVER ()) lgb, HEX(IFNULL(g1, b1)) ifn_gb FROM t;
SELECT HEX(LEAD(g1, 100, d1) OVER ()) lgd, HEX(IFNULL(g1, d1)) ifn_gd FROM t;
SELECT HEX(LEAD(g1, 100, e1) OVER ()) lge1, HEX(IFNULL(g1, e1)) ifn_ge1 FROM t;
SELECT HEX(LEAD(g1, 100, e2) OVER ()) lge2, HEX(IFNULL(g1, e2)) ifn_ge2 FROM t;

SELECT LEAD(i1, 100, c1) OVER () lic, IFNULL(i1, c1) ifn_ic FROM t;
SELECT LEAD(i1, 100, j1) OVER () lij, IFNULL(i1, j1) ifn_ij FROM t;
SELECT HEX(LEAD(i1, 100, g1) OVER ()) lig, HEX(IFNULL(i1, g1)) ifn_ig FROM t;
SELECT LEAD(i1, 100, b1) OVER () lib, IFNULL(i1, b1) ifn_ib FROM t;
SELECT LEAD(i1, 100, d1) OVER () lid, IFNULL(i1, d1) ifn_id FROM t;
SELECT LEAD(i1, 100, e1) OVER () lie1, IFNULL(i1, e1) ifn_ie1 FROM t;
SELECT LEAD(i1, 100, e2) OVER () lie2, IFNULL(i1, e2) ifn_ie2 FROM t;

SELECT LEAD(b1, 100, c1) OVER () lbc, IFNULL(b1, c1) ifn_bc FROM t;
SELECT LEAD(b1, 100, j1) OVER () lbj, IFNULL(b1, j1) ifn_bj FROM t;
SELECT HEX(LEAD(b1, 100, g1) OVER ()) lbg, HEX(IFNULL(b1, g1)) ifn_bg FROM t;
SELECT LEAD(b1, 100, i1) OVER () lbi, IFNULL(b1, i1) ifn_bi FROM t;
SELECT LEAD(b1, 100, d1) OVER () lbd, IFNULL(b1, d1) ifn_bd FROM t;
SELECT LEAD(b1, 100, e1) OVER () lbe1, IFNULL(b1, e1) ifn_be1 FROM t;
SELECT LEAD(b1, 100, e2) OVER () lbe2, IFNULL(b1, e2) ifn_be2 FROM t;

SELECT LEAD(d1, 100, c1) OVER () ldc, IFNULL(d1, c1) ifn_dc FROM t;
SELECT LEAD(d1, 100, j1) OVER () ldj, IFNULL(d1, j1) ifn_dj FROM t;
SELECT HEX(LEAD(d1, 100, g1) OVER ()) ldg, HEX(IFNULL(d1, g1)) ifn_dg FROM t;
SELECT LEAD(d1, 100, i1) OVER () ldi, IFNULL(d1, i1) ifn_di FROM t;
SELECT LEAD(d1, 100, b1) OVER () ldd, IFNULL(d1, b1) ifn_db FROM t;
SELECT LEAD(d1, 100, e1) OVER () lde1, IFNULL(d1, e1) ifn_de1 FROM t;
SELECT LEAD(d1, 100, e2) OVER () lde2, IFNULL(d1, e2) ifn_de2 FROM t;

SELECT LEAD(e1, 100, c1) OVER () le1c, IFNULL(e1, c1) ifn_e1c FROM t;
SELECT LEAD(e1, 100, j1) OVER () le1j, IFNULL(e1, j1) ifn_e1j FROM t;
SELECT HEX(LEAD(e1, 100, g1) OVER ()) le1g, HEX(IFNULL(e1, g1)) ifn_e1g FROM t;
SELECT LEAD(e1, 100, i1) OVER () le1i, IFNULL(e1, i1) ifn_e1i FROM t;
SELECT LEAD(e1, 100, b1) OVER () le1d, IFNULL(e1, b1) ifn_e1d FROM t;
SELECT LEAD(e1, 100, d1) OVER () le1d, IFNULL(e1, d1) ifn_e1d FROM t;
SELECT LEAD(e1, 100, e2) OVER () le1e2, IFNULL(e1, e2) ifn_e1e2 FROM t;

SELECT LEAD(e2, 100, c1) OVER () le2c, IFNULL(e2, c1) ifn_e2c FROM t;
SELECT LEAD(e2, 100, j1) OVER () le2j, IFNULL(e2, j1) ifn_e2j FROM t;
SELECT HEX(LEAD(e2, 100, g1) OVER ()) le2g, HEX(IFNULL(e2, g1)) ifn_e2g FROM t;
SELECT LEAD(e2, 100, i1) OVER () le2i, IFNULL(e2, i1) ifn_e2i FROM t;
SELECT LEAD(e2, 100, b1) OVER () le2d, IFNULL(e2, b1) ifn_e2d FROM t;
SELECT LEAD(e2, 100, d1) OVER () le2d, IFNULL(e2, d1) ifn_e2d FROM t;
SELECT LEAD(e2, 100, e1) OVER () le2e1, IFNULL(e2, e1) ifn_e2e1 FROM t;

DROP TABLE t;

--echo static wf
SELECT id, sex, COUNT(*) OVER w cnt, NTILE(3) OVER w `ntile`,
       LEAD(id, 1) OVER w le1,
       LAG(id, 1) OVER w la1,
       LEAD(id, 100) OVER w le100,
       LAG(id, 2, 777) OVER w la2 FROM t1
       WINDOW w AS (ORDER BY id);


SELECT id, sex, COUNT(*) OVER w cnt, NTH_VALUE(id, 2) OVER w nth,
       LEAD(id, 1) OVER w le1,
       LAG(id, 1) OVER w la1,
       LEAD(id, 100) OVER w le100,
       LAG(id, 2, 777) OVER w la2 FROM t1
       WINDOW w as (PARTITION BY sex ORDER BY id ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING);

SELECT id, sex, COUNT(*) OVER w cnt, NTH_VALUE(id, 2) OVER w nth,
       LEAD(id, 1) OVER w le1,
       LAG(id, 1) OVER w la1,
       LEAD(id, 100) OVER w le100,
       LAG(id, 2, 777) OVER w la2 FROM t1
       WINDOW w as (PARTITION BY id ORDER BY sex ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING);

--echo unbuffered
SELECT id, sex, COUNT(*) OVER w cnt,
       LEAD(id, 1) OVER w le1,
       LAG(id, 1) OVER w la1,
       LEAD(id, 100) OVER w le100,
       LAG(id, 2, 777) OVER w la2 FROM t1
       WINDOW w as (PARTITION BY SEX ORDER BY ID ROWS UNBOUNDED PRECEDING);

SELECT id, sex, COUNT(*) OVER w cnt, NTH_VALUE(id, 2) OVER w nth,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (PARTITION BY SEX ORDER BY ID RANGE UNBOUNDED PRECEDING);

--echo test unoptimized path: trick: add DOUBLE type w/SUM which is unoptimized by default
--echo ascending
SELECT d, SUM(d) OVER w `sum`, sex,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (PARTITION BY SEX ORDER BY d ROWS 2 PRECEDING);

SELECT d, SUM(d) OVER w `sum`, sex,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (PARTITION BY SEX ORDER BY d RANGE 2 PRECEDING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY d ROWS 2 PRECEDING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY d ASC ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY d ASC ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY d RANGE 2 PRECEDING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY d RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY d RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING);

--echo descending
SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (PARTITION BY SEX ORDER BY d DESC ROWS 2 PRECEDING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (PARTITION BY SEX ORDER BY d DESC RANGE 2 PRECEDING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY d DESC ROWS 2 PRECEDING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY d DESC ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY d DESC ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY d DESC RANGE 2 PRECEDING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY d DESC RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING);

SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY d DESC RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING);

--echo  Dynamic upper
SELECT id, sex, COUNT(*) OVER w cnt, NTILE(3) OVER w `ntile`,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1 WINDOW w as (ORDER BY id);


--echo optimized path
--echo ascending
SELECT id, SUM(id) OVER w `sum`, sex,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (PARTITION BY SEX ORDER BY id ROWS 2 PRECEDING);

SELECT id, SUM(id) OVER w `sum`, sex,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (PARTITION BY SEX ORDER BY id RANGE 2 PRECEDING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY id ROWS 2 PRECEDING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY id ASC ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY id ASC ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY id RANGE 2 PRECEDING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY id RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY id RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING);

--echo descending
SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (PARTITION BY SEX ORDER BY id DESC ROWS 2 PRECEDING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (PARTITION BY SEX ORDER BY id DESC RANGE 2 PRECEDING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY id DESC ROWS 2 PRECEDING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY id DESC ROWS BETWEEN 2 PRECEDING AND 1 PRECEDING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY id DESC ROWS BETWEEN 1 FOLLOWING AND 2 FOLLOWING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY id DESC RANGE 2 PRECEDING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY id DESC RANGE BETWEEN 1 PRECEDING AND 1 FOLLOWING);

SELECT id, SUM(id) OVER w `sum`, COUNT(*) OVER w cnt, sex,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w as (ORDER BY id DESC RANGE BETWEEN 1 FOLLOWING AND 2 FOLLOWING);

--echo many nth_value calls on one window, unoptimized path
SELECT d, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(d, 3) OVER w le3,
       FIRST_VALUE(d) OVER w fv,
       LEAD(d, 1) OVER w le1,
       LEAD(d, 2) OVER w le2,
       LAG(d, 2) OVER w la2 FROM t1
       WINDOW w AS (ORDER BY d ASC ROWS BETWEEN 2 PRECEDING AND CURRENT ROW);

--echo many nth_value calls on one window, optimized path
SELECT id, SUM(d) OVER w `sum`, COUNT(*) OVER w cnt,
       LEAD(id, 3) OVER w le3,
       FIRST_VALUE(id) OVER w fv,
       LEAD(id, 1) OVER w le1,
       LEAD(id, 2) OVER w le2,
       LAG(id, 2) OVER w la2 FROM t1
       WINDOW w AS (ORDER BY id ASC ROWS BETWEEN 2 PRECEDING AND CURRENT ROW);

DROP TABLE t1;

--echo Check interference with other two pass window functions

CREATE TABLE t(i INT);
INSERT INTO t VALUES (NULL), (1), (2), (3), (3), (4), (5), (6), (6), (7), (8), (9), (10);

SELECT i, PERCENT_RANK() OVER w cd
       FROM t WINDOW w AS (ORDER BY i);
SELECT i, PERCENT_RANK() OVER w cd FROM t
       WINDOW w AS (ORDER BY i ROWS BETWEEN 1 PRECEDING AND 2 FOLLOWING);
SELECT i, PERCENT_RANK() OVER w cd, NTILE(3) OVER w `ntile`, COUNT(*) OVER w cnt, SUM(i) OVER W `sum` FROM t
       WINDOW w AS (ORDER BY i ROWS BETWEEN 1 PRECEDING AND 2 FOLLOWING);
SELECT i, PERCENT_RANK() OVER w cd, NTILE(3) OVER w `ntile`, COUNT(*) OVER w cnt, SUM(i) OVER W `sum`,
       LEAD(i,2) OVER w le2,
       LAG(i) OVER w la FROM t
       WINDOW w AS (ORDER BY i ROWS BETWEEN 1 PRECEDING AND 2 FOLLOWING);

DROP TABLE t;

--echo Nullability bug fixed
CREATE TABLE t(i INT NOT NULL);
INSERT INTO t VALUES (1), (2), (3), (3), (4), (5), (6);
SELECT LEAD(i, 3) OVER () FROM t;
SELECT LAG(i, 3) OVER () FROM t;
DROP TABLE t;

--echo
--echo Bug#25835149
--echo
SET @savmode=@@SESSION.SQL_MODE;
SET SESSION SQL_MODE='';
CREATE TABLE `test`(
  `pk` INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `dt` DATETIME DEFAULT NULL,
  `ge` GEOMETRY DEFAULT NULL
);

INSERT INTO `test` VALUES
    (1, '2007-05-08 12:10:55', ST_GeomFromText('POINT(1 1)')),
    (2, NULL, NULL), (3, NULL, NULL),
    (4, '2001-01-18 00:00:00', ST_GeomFromText('POINT(4 4)')),
    (5, '2009-11-24 00:00:00', ST_GeomFromText('POINT(5 5)')),
    (6, '2001-11-22 21:41:15', ST_GeomFromText('POINT(6 6)')),
    (7, NULL, NULL),
    (8, '0000-00-00 00:00:00', ST_GeomFromText('POINT(8 8)')),
    (9, '2003-05-13 18:03:04', ST_GeomFromText('POINT(9 9)')),
    (10, '2008-04-15 09:44:20', ST_GeomFromText('POINT(10 10)')),
    (11, '2009-07-15 00:00:00', ST_GeomFromText('POINT(11 11)')),
    (12, '2007-04-27 13:53:37', ST_GeomFromText('POINT(12 12)')),
    (13, '0000-00-00 00:00:00', ST_GeomFromText('POINT(13 13)')),
    (14, '2000-02-02 02:15:28', ST_GeomFromText('POINT(14 14)')),
    (15, '2004-06-06 00:00:00', ST_GeomFromText('POINT(15 15)')),
    (16, NULL, NULL),
    (17, '2002-06-21 00:00:00', ST_GeomFromText('POINT(17 17)')),
    (18, '2007-03-23 00:00:00', ST_GeomFromText('POINT(18 18)')),
    (19, '2006-10-06 00:00:00', ST_GeomFromText('POINT(19 19)')),
    (20, '2008-07-07 00:00:00', ST_GeomFromText('POINT(20 20)'));

SELECT dt, LEAD(dt, 1) OVER w1 `lead`,
       CAST(LEAD(ge, 1) OVER w1 AS JSON) geo
   FROM test WHERE `pk` = 2 WINDOW w1 AS ();

SELECT dt, LAG(dt) OVER w1 `lag`,
       CAST(LAG(ge) OVER w1 AS JSON) geo
   FROM test WHERE `pk` > 3 WINDOW w1 AS ();

SELECT dt, LEAD(CAST(dt AS TIME), 1) OVER w1
   FROM test WHERE `pk` > 3 WINDOW w1 AS ();

SET SESSION SQL_MODE=@savmode;
DROP TABLE `test`;

--echo
--echo Bug with missing update of cached example after split_sum_func
--echo for NTH_VALUE
--echo
CREATE TABLE t(a INT, b INT, c INT, d INT);
INSERT INTO t VALUES (1,1,1,1), (2,2,4,2), (3,3,9,3);
SELECT SUM(c/d), LEAD(SUM(c/d), 1) OVER (ORDER BY a)  FROM t GROUP BY a,b;
SELECT SUM(c/d), LEAD(SUM(c/d), 1, SUM(c/d)) OVER (ORDER BY a)  FROM t GROUP BY a,b;
SELECT SUM(c/d), LAG(SUM(c/d), 1) OVER (ORDER BY a)  FROM t GROUP BY a,b;
SELECT SUM(c/d), LAG(SUM(c/d), 1, SUM(c/d)) OVER (ORDER BY a)  FROM t GROUP BY a,b;
SELECT LEAD(SUM(c/d), 2) OVER (ORDER BY a)  FROM t GROUP BY a,b;
SELECT 1+LEAD(SUM(c/d), 1) OVER (ORDER BY a)  FROM t GROUP BY a,b;
SELECT ROW_NUMBER() OVER () rn,
       1 + LEAD(SUM(c/d), 1) OVER (ORDER BY a) le1,
       1 + LAG(SUM(c/d), 1) OVER (ORDER BY a) la1,
       1 + LEAD(SUM(c/d), 2) OVER (ORDER BY a) le2,
       1 + LAG(SUM(c/d), 2) OVER (ORDER BY a) la2,
       1 + LEAD(SUM(c/d), 1, SUM(c/d)) OVER (ORDER BY a) le1d,
       1 + LAG(SUM(c/d), 1, SUM(c/d)) OVER (ORDER BY a) la1d,
       1 + LEAD(SUM(c/d), 2, SUM(c/d)) OVER (ORDER BY a) le2d,
       1 + LAG(SUM(c/d), 2, SUM(c/d)) OVER (ORDER BY a) la2d,
       1 + LEAD(SUM(c/d), 1, 1 + SUM(c/d)) OVER (ORDER BY a) le1dp,
       1 + LAG(SUM(c/d), 1, 1 + SUM(c/d)) OVER (ORDER BY a) la1dp,
       1 + LEAD(SUM(c/d), 2, 1 + SUM(c/d)) OVER (ORDER BY a) le2dp,
       1 + LAG(SUM(c/d), 2, 1 + SUM(c/d)) OVER (ORDER BY a) la2dp
       FROM t GROUP BY a,b;

DROP TABLE t;

--echo #
--echo # Bug#25883997 : WL#9603: SIG11 AT STRING::LENGTH() IN INCLUDE/SQL_STRING.H
--echo #

CREATE TABLE t1 (a int, b char(1), c varchar(1));
INSERT INTO t1 VALUES (1,'s','k'),(NULL,'e','t'),(NULL,'w','i'),(2,'i','k');

SELECT  LEAD(a, 7,'abc') OVER w1, LAG(a) OVER w1 FROM t1 WINDOW w1 AS (PARTITION BY a);

SELECT  a, LEAD(a, 7, 'abc') over w1,
           LEAD(a, 2, 'abc') over w1
    FROM t1 WINDOW w1 AS (ORDER BY a ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING);

SELECT  a, LEAD(a, 1, 'abc') OVER w1,
           LEAD(a, 2, 'abc') over w1
    FROM t1 WINDOW w1 AS (ORDER BY a ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING);

DROP TABLE t1;

--echo Coverage for LEAD/LAG ::get_time, get_date
SELECT ADDTIME(LEAD(time'18:00:00', 0) OVER (ORDER BY NULL), '01:00:00');
SELECT ADDTIME(LEAD(NULL, 1, time'18:00:00') OVER (ORDER BY NULL), '01:00:00');

SELECT ADDDATE(LEAD(NULL, 1, date'1955-05-15') OVER (ORDER BY NULL), 1);
SELECT ADDDATE(LEAD(date'1955-05-15', 0) OVER (ORDER BY NULL), 1);

--echo #
--echo # Bug#26100985 WL9603: LEAD IN VIEW GIVES WRONG RESULT
--echo #

CREATE VIEW v AS
SELECT LEAD(d, 2) OVER () FROM
 (SELECT 1 AS d UNION SELECT 2 UNION SELECT 3) dt;
SELECT * FROM v;
DROP VIEW v;

--echo #
--echo # Bug#26178042 WL#9603: LEAD(<>,0) RESET RESULTS OF OTHER WF'S ON SAME WINDOW
--echo #
CREATE TABLE t1e(a int);
INSERT INTO t1e VALUES(1),(2),(3),(3),(NULL);
--echo OK
SELECT LEAD(a,2) over w1, LEAD(a,1) OVER w1 FROM t1e WINDOW w1 as ();
--echo Used to give wrong results for (a,2) and (a,1)
SELECT LEAD(a,2) over w1, LEAD(a,0) OVER w1, LEAD(a,1) OVER w1 FROM t1e WINDOW w1 as ();
DROP TABLE t1e;


--echo #
--echo # Bug#26703246 ASSERTION FAILED: DELSUM+(INT) Y/4-TEMP >= 0
--echo # Also solves Bug#26703156.

--echo Assertion used to fail: delsum+(int) y/4-temp >= 0

DO TO_SECONDS(LAG(POINT(2.804466E+307,-2032),75) OVER());
DO IS_IPV4(TIMESTAMP(LAG(-19131 ,188) OVER(),@F));
DO WEEKOFYEAR(LAG(-16726 ,247)RESPECT NULLS OVER());
DO DAYOFYEAR(LEAD(-6653420797178186265 ,29)OVER());
DO YEARWEEK(LEAD(FOUND_ROWS(),250)OVER());
DO DAYOFYEAR(LAG('] .| /= ',63) OVER());
DO IFNULL((DAYNAME(LEAD(STDDEV(@F),162) OVER())),(0xD73E));
DO TO_DAYS(LEAD(((LOCATE(0xA812,0xE8DE))OR(@G)),148)OVER());
DO WEEKOFYEAR(LEAD(('5183-10-18 06:15:35.076079')SOUNDS LIKE(0x9E335D89),68)OVER());
DO DAYOFYEAR(LEAD(ROW_COUNT(),177)OVER());
DO MONTHNAME(LAG(' =',74) OVER());
DO SHA(BIN(MONTHNAME(LEAD(UNCOMPRESSED_LENGTH(SHA1("")),224)OVER())));
DO (((NOT(1)))^(HOUR(MONTHNAME(LEAD(DATABASE(),89)OVER()))));
DO MONTHNAME(LEAD(CHARSET(CONVERT((MOD(CURTIME(4),STDDEV(NULL)))USING CP850)),12) OVER());
DO LAST_DAY(LAG(NULL,113) OVER());
DO LAST_DAY(LEAD(-2201 ,98)RESPECT NULLS OVER());
DO OCTET_LENGTH(((LAST_DAY(LEAD(-28178,163)OVER()))OR(COLLATION(@E))));
DO LAST_DAY(LAG('*',36)RESPECT NULLS OVER());
DO LAST_DAY(LEAD( _CP932 '',241)OVER(RANGE UNBOUNDED PRECEDING));

--echo Assertion used to fail: !check_time_mmssff_range(ltime)

DO ((CAST((LEAD(NULL,152) OVER()) AS TIME))*(1));
DO CAST((LAG(SHA(STDDEV('-0E%_')),224) OVER()) AS TIME);
DO CAST((LAG(_UJIS '8CA} ',144) OVER()) AS TIME);
DO CAST((LAG(-16520,156) OVER()) AS TIME);
DO CAST((LEAD(-6011,202) OVER()) AS TIME);
DO CAST((LEAD(0x45,104) OVER())AS TIME);
DO CAST((LAG(POLYGON( LINESTRING( POINT(4099,17421),
                                  POINT(22259,-5875.0796),
                                  POINT(30179,6542),
                                  POINT(12331,-18840)),
                                  LINESTRING( POINT( 8162.2539,-29332),
                                              POINT(24157,-23393),
                                              POINT(268435459,-26835),
                                              POINT(3.933871E+307,-25808)),
                                  LINESTRING( POINT(30360,-1049),
                                              POINT(15405,5.816757E+307),
                                              POINT( 3717.3555,1.599730E+308),
                                              POINT(-23002,-19077))),
                      70)
         OVER())
        AS DATETIME );
DO UNIX_TIMESTAMP(
     LAG(ROUND(((UTC_TIME()) OR
                ((NOT (INET6_ATON(IS_IPV4(((MAKETIME(-10966,
                                                     233,
                                                     2795.439453))
                                           OR(MAKEDATE(-3030,19)))))))))),
         140) OVER());
DO LAST_DAY(SQRT(CAST((LAG(REPEAT('B',64),91)OVER())AS DATETIME)));
--echo We Must disable warning for next statement because we get two instead of
--echo one with -ps protocol vs plain protocol.
DO CAST((LEAD(-197994311,60)OVER())AS DATETIME);
DO IFNULL((NULL ),(UNIX_TIMESTAMP(LEAD(NULL,12)RESPECT NULLS OVER())));

--echo Assertion used to fail: !check_datetime_range(ltime)

DO ((CONVERT((VARIANCE(-18951))USING CP866)) <=
    (JSON_ARRAY(LEAD(CAST((35184372088833)AS DATETIME),126)OVER())));
DO ((LAG(LAST_DAY("]<$*_#[DB!^+ : 3"),89) OVER (RANGE UNBOUNDED PRECEDING)) >
    (CONVERT(("1985-10-19 03:36:29.304455" -
              INTERVAL(0x1446C5A2627FB06D88DC63D66B36DF) DAY_MICROSECOND)
             USING BIG5)));

--echo #
--echo # Bug#26740557: WINDOW FUNC + JSON: ASSERTION FAILED:
--echo #               FALSE IN ITEM::VAL_JSON
--echo #

SELECT ((MAKETIME(((QUARTER('| !*c>*{/'))<=>
               (LAG(JSON_OBJECTAGG('key4',0x067c13d0d0d7d8c8d768aef7)
                    ,7)OVER())),'9236-05-27',0xe2a7d4))^(0x1109));
SELECT ((LAG(JSON_MERGE_PATCH(1.755913e+308,'{ }'),246)OVER())<=(1));
--error ER_INVALID_TYPE_FOR_JSON
SELECT ((LAG(JSON_MERGE_PATCH(1.755913e+308,'{ }'),0)OVER())<=(1));
SELECT ((QUOTE(JSON_KEYS(LEAD(JSON_KEYS(EXP(-15676),ABS('d0')),
                          162)OVER())))>=(CONNECTION_ID()));
--error ER_INVALID_TYPE_FOR_JSON
SELECT ((QUOTE(JSON_KEYS(LEAD(JSON_KEYS(EXP(-15676),ABS('d0')),
                          0)OVER())))>=(CONNECTION_ID()));
SELECT JSON_LENGTH(LEAD(JSON_OBJECTAGG('key2','*B'),172)OVER());
SELECT JSON_LENGTH(LEAD(JSON_OBJECTAGG('key2','*B'),0)OVER());

--echo # End of test for Bug#26740557

--echo #
--echo # Bug#33045412: ASSERTION `M_TYPELIB != NULLPTR' FAILED|SQL/ITEM.CC
--echo #
CREATE TABLE t(id INT PRIMARY KEY,
               set_col SET('a', 'b', 'c'),
               enum_col ENUM('d', 'e'));

INSERT INTO t VALUES (1, 'a,b', 'd'), (2, 'b,c', 'e');

--sorted_result
SELECT LEAD(set_col) OVER w AS col1,
       LEAD(set_col, 1, set_col) OVER w AS col2,
       LEAD(set_col, 1, enum_col) OVER w AS col3,
       LEAD(set_col, 1, NULL) OVER w AS col4,
       LEAD(set_col, 1, '') OVER w AS col5,
       LEAD(NULL, 1, set_col) OVER w AS col6
FROM t WINDOW w AS (ORDER BY id) UNION SELECT 1, 2, 3, 4, 5, 6;

--sorted_result
SELECT LAG(set_col) OVER w AS col1,
       LAG(set_col, 1, set_col) OVER w AS col2,
       LAG(set_col, 1, enum_col) OVER w AS col3,
       LAG(set_col, 1, NULL) OVER w AS col4,
       LAG(set_col, 1, '') OVER w AS col5,
       LAG(NULL, 1, set_col) OVER w AS col6
FROM t WINDOW w AS (ORDER BY id) UNION SELECT 1, 2, 3, 4, 5, 6;

--sorted_result
SELECT LEAD(enum_col) OVER w AS col1,
       LEAD(enum_col, 1, enum_col) OVER w AS col2,
       LEAD(enum_col, 1, set_col) OVER w AS col3,
       LEAD(enum_col, 1, NULL) OVER w AS col4,
       LEAD(enum_col, 1, '') OVER w AS col5,
       LEAD(NULL, 1, enum_col) OVER w AS col6
FROM t WINDOW w AS (ORDER BY id) UNION SELECT 1, 2, 3, 4, 5, 6;

--sorted_result
SELECT LAG(enum_col) OVER w AS col1,
       LAG(enum_col, 1, enum_col) OVER w AS col2,
       LAG(enum_col, 1, set_col) OVER w AS col3,
       LAG(enum_col, 1, NULL) OVER w AS col4,
       LAG(enum_col, 1, '') OVER w AS col5,
       LAG(NULL, 1, enum_col) OVER w AS col6
FROM t WINDOW w AS (ORDER BY id) UNION SELECT 1, 2, 3, 4, 5, 6;

DROP TABLE t;

# Local Variables:
# mode: sql
# sql-product: mysql
# comment-column: 48
# comment-start: "# "
# fill-column: 80
# End:
