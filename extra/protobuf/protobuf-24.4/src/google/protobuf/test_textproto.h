// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

#ifndef GOOGLE_PROTOBUF_TEST_TEXTPROTO_H__
#define GOOGLE_PROTOBUF_TEST_TEXTPROTO_H__

#include <gmock/gmock.h>
#include "absl/log/absl_check.h"
#include "absl/memory/memory.h"
#include "google/protobuf/dynamic_message.h"
#include "google/protobuf/text_format.h"

// This file contains private helpers for dealing with textprotos in our
// tests.  We make no guarantees about the behavior in real-world situations,
// and these are only meant for basic unit-tests of protobuf internals.
namespace google {
namespace protobuf {

MATCHER_P(EqualsProto, textproto, "") {
  auto msg = absl::WrapUnique(arg.New());
  return TextFormat::ParseFromString(textproto, msg.get()) &&
         msg->DebugString() == arg.DebugString();
}

MATCHER_P3(EqualsProtoSerialized, pool, type, textproto, "") {
  const Descriptor* desc = pool->FindMessageTypeByName(type);
  DynamicMessageFactory factory(pool);
  auto msg = absl::WrapUnique(factory.GetPrototype(desc)->New());
  return TextFormat::ParseFromString(textproto, msg.get()) &&
         arg.SerializeAsString() == msg->SerializeAsString();
}

class ParseTextOrDie {
 public:
  explicit ParseTextOrDie(absl::string_view text) : text_(text) {}
  template <typename Proto>
  operator Proto() {  // NOLINT(google-explicit-constructor)
    Proto ret;
    ABSL_CHECK(TextFormat::ParseFromString(text_, &ret));
    return ret;
  }

 private:
  absl::string_view text_;
};

}  // namespace protobuf
}  // namespace google

#endif  // GOOGLE_PROTOBUF_TEST_TEXTPROTO_H__
