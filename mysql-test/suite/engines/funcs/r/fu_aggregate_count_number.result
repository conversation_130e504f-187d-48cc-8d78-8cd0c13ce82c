DROP TABLE IF EXISTS t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 < 0;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <= 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <= 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <= 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <= 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <= 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <= 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <= 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <= 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <= 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <= 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <= 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <= 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <= 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <= 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <= 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <= 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <= 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <= 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <= 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <= 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <= 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <= 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <= 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <= 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 = 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 = 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 = 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 = 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 = 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 = 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 = 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 = 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 = 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 = 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 = 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 = 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 = 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 = 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 = 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 = 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 = 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 = 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 = 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 = 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 = 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 = 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 = 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 = 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 >= 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 >= 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 >= 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 >= 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 >= 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 >= 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 >= 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 >= 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 >= 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 >= 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 >= 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 >= 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 >= 0;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 >= 0;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 >= 0;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 >= 0;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 >= 0;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 >= 0;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 >= 0;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 >= 0;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 >= 0;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 >= 0;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 >= 0;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 >= 0;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 != 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 != 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 != 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 != 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 != 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 != 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 != 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 != 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 != 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 != 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 != 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 != 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 != 0;
value
9
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 != 0;
value
9
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 != 0;
value
9
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 != 0;
value
9
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 != 0;
value
9
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 != 0;
value
9
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 != 0;
value
9
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 != 0;
value
9
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 != 0;
value
9
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 != 0;
value
9
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 != 0;
value
9
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 != 0;
value
9
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <> 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <> 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <> 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <> 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <> 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <> 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <> 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <> 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <> 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <> 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <> 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <> 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <> 0;
value
9
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <> 0;
value
9
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <> 0;
value
9
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <> 0;
value
9
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <> 0;
value
9
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <> 0;
value
9
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <> 0;
value
9
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <> 0;
value
9
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <> 0;
value
9
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <> 0;
value
9
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <> 0;
value
9
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <> 0;
value
9
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <=> 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <=> 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <=> 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <=> 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <=> 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t1;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 <=> 0;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t1 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <=> 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <=> 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <=> 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <=> 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <=> 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t2;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 <=> 0;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t2 WHERE c1 IS NOT NULL;
value
0
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <=> 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <=> 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <=> 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <=> 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <=> 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t3;
value
10
SELECT COUNT(c1) AS value FROM t3 WHERE c1 <=> 0;
value
1
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t3 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 TINYINT NOT NULL);
CREATE TABLE t2(c1 TINYINT NULL);
CREATE TABLE t3(c1 TINYINT NOT NULL);
CREATE TABLE t4(c1 TINYINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <=> 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 SMALLINT NOT NULL);
CREATE TABLE t2(c1 SMALLINT NULL);
CREATE TABLE t3(c1 SMALLINT NOT NULL);
CREATE TABLE t4(c1 SMALLINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <=> 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 MEDIUMINT NOT NULL);
CREATE TABLE t2(c1 MEDIUMINT NULL);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL);
CREATE TABLE t4(c1 MEDIUMINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <=> 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INT NOT NULL);
CREATE TABLE t2(c1 INT NULL);
CREATE TABLE t3(c1 INT NOT NULL);
CREATE TABLE t4(c1 INT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <=> 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 INTEGER NOT NULL);
CREATE TABLE t2(c1 INTEGER NULL);
CREATE TABLE t3(c1 INTEGER NOT NULL);
CREATE TABLE t4(c1 INTEGER NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <=> 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1(c1 BIGINT NOT NULL);
CREATE TABLE t2(c1 BIGINT NULL);
CREATE TABLE t3(c1 BIGINT NOT NULL);
CREATE TABLE t4(c1 BIGINT NULL);
INSERT INTO t2 (c1) VALUES(NULL);
INSERT INTO t3 (c1) VALUES(0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
INSERT INTO t4 (c1) VALUES(NULL), (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
SELECT COUNT(c1) AS value FROM t4;
value
10
SELECT COUNT(c1) AS value FROM t4 WHERE c1 <=> 0;
value
1
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NULL;
value
0
SELECT COUNT(c1) AS value FROM t4 WHERE c1 IS NOT NULL;
value
10
DROP TABLE t1,t2,t3,t4;
