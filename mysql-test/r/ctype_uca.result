call mtr.add_suppression("Plugin \'InnoDB\'");
DROP TABLE IF EXISTS t1;
set names utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
set collation_connection=utf8mb3_unicode_ci;
Warnings:
Warning	3778	'utf8mb3_unicode_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
select 'a' = 'a', 'a' = 'a ', 'a ' = 'a';
'a' = 'a'	'a' = 'a '	'a ' = 'a'
1	1	1
select 'a\t' = 'a' , 'a\t' < 'a' , 'a\t' > 'a';
'a\t' = 'a'	'a\t' < 'a'	'a\t' > 'a'
0	1	0
select 'a\t' = 'a ', 'a\t' < 'a ', 'a\t' > 'a ';
'a\t' = 'a '	'a\t' < 'a '	'a\t' > 'a '
0	1	0
select 'a' = 'a\t', 'a' < 'a\t', 'a' > 'a\t';
'a' = 'a\t'	'a' < 'a\t'	'a' > 'a\t'
0	0	1
select 'a ' = 'a\t', 'a ' < 'a\t', 'a ' > 'a\t';
'a ' = 'a\t'	'a ' < 'a\t'	'a ' > 'a\t'
0	0	1
select 'a  a' > 'a', 'a  \t' < 'a';
'a  a' > 'a'	'a  \t' < 'a'
1	1
select 'c' like '\_' as want0;
want0
0
create table t1 (c1 char(10) character set utf8mb3 COLLATE utf8mb3_bin);
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	3778	'utf8mb3_bin' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
insert into t1 values ('A'),('a');
insert into t1 values ('B'),('b');
insert into t1 values ('C'),('c');
insert into t1 values ('D'),('d');
insert into t1 values ('E'),('e');
insert into t1 values ('F'),('f');
insert into t1 values ('G'),('g');
insert into t1 values ('H'),('h');
insert into t1 values ('I'),('i');
insert into t1 values ('J'),('j');
insert into t1 values ('K'),('k');
insert into t1 values ('L'),('l');
insert into t1 values ('M'),('m');
insert into t1 values ('N'),('n');
insert into t1 values ('O'),('o');
insert into t1 values ('P'),('p');
insert into t1 values ('Q'),('q');
insert into t1 values ('R'),('r');
insert into t1 values ('S'),('s');
insert into t1 values ('T'),('t');
insert into t1 values ('U'),('u');
insert into t1 values ('V'),('v');
insert into t1 values ('W'),('w');
insert into t1 values ('X'),('x');
insert into t1 values ('Y'),('y');
insert into t1 values ('Z'),('z');
insert into t1 values (_ucs2 0x00e0),(_ucs2 0x00c0);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e1),(_ucs2 0x00c1);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e2),(_ucs2 0x00c2);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e3),(_ucs2 0x00c3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e4),(_ucs2 0x00c4);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e5),(_ucs2 0x00c5);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e6),(_ucs2 0x00c6);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e7),(_ucs2 0x00c7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e8),(_ucs2 0x00c8);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e9),(_ucs2 0x00c9);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ea),(_ucs2 0x00ca);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00eb),(_ucs2 0x00cb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ec),(_ucs2 0x00cc);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ed),(_ucs2 0x00cd);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ee),(_ucs2 0x00ce);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ef),(_ucs2 0x00cf);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f0),(_ucs2 0x00d0);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f1),(_ucs2 0x00d1);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f2),(_ucs2 0x00d2);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f3),(_ucs2 0x00d3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f4),(_ucs2 0x00d4);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f5),(_ucs2 0x00d5);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f6),(_ucs2 0x00d6);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f7),(_ucs2 0x00d7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f8),(_ucs2 0x00d8);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f9),(_ucs2 0x00d9);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fa),(_ucs2 0x00da);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fb),(_ucs2 0x00db);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fc),(_ucs2 0x00dc);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fd),(_ucs2 0x00dd);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fe),(_ucs2 0x00de);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ff),(_ucs2 0x00df);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0100),(_ucs2 0x0101),(_ucs2 0x0102),(_ucs2 0x0103);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0104),(_ucs2 0x0105),(_ucs2 0x0106),(_ucs2 0x0107);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0108),(_ucs2 0x0109),(_ucs2 0x010a),(_ucs2 0x010b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x010c),(_ucs2 0x010d),(_ucs2 0x010e),(_ucs2 0x010f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0110),(_ucs2 0x0111),(_ucs2 0x0112),(_ucs2 0x0113);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0114),(_ucs2 0x0115),(_ucs2 0x0116),(_ucs2 0x0117);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0118),(_ucs2 0x0119),(_ucs2 0x011a),(_ucs2 0x011b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x011c),(_ucs2 0x011d),(_ucs2 0x011e),(_ucs2 0x011f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0120),(_ucs2 0x0121),(_ucs2 0x0122),(_ucs2 0x0123);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0124),(_ucs2 0x0125),(_ucs2 0x0126),(_ucs2 0x0127);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0128),(_ucs2 0x0129),(_ucs2 0x012a),(_ucs2 0x012b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x012c),(_ucs2 0x012d),(_ucs2 0x012e),(_ucs2 0x012f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0130),(_ucs2 0x0131),(_ucs2 0x0132),(_ucs2 0x0133);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0134),(_ucs2 0x0135),(_ucs2 0x0136),(_ucs2 0x0137);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0138),(_ucs2 0x0139),(_ucs2 0x013a),(_ucs2 0x013b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x013c),(_ucs2 0x013d),(_ucs2 0x013e),(_ucs2 0x013f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0140),(_ucs2 0x0141),(_ucs2 0x0142),(_ucs2 0x0143);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0144),(_ucs2 0x0145),(_ucs2 0x0146),(_ucs2 0x0147);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0148),(_ucs2 0x0149),(_ucs2 0x014a),(_ucs2 0x014b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x014c),(_ucs2 0x014d),(_ucs2 0x014e),(_ucs2 0x014f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0150),(_ucs2 0x0151),(_ucs2 0x0152),(_ucs2 0x0153);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0154),(_ucs2 0x0155),(_ucs2 0x0156),(_ucs2 0x0157);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0158),(_ucs2 0x0159),(_ucs2 0x015a),(_ucs2 0x015b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x015c),(_ucs2 0x015d),(_ucs2 0x015e),(_ucs2 0x015f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0160),(_ucs2 0x0161),(_ucs2 0x0162),(_ucs2 0x0163);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0164),(_ucs2 0x0165),(_ucs2 0x0166),(_ucs2 0x0167);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0168),(_ucs2 0x0169),(_ucs2 0x016a),(_ucs2 0x016b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x016c),(_ucs2 0x016d),(_ucs2 0x016e),(_ucs2 0x016f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0170),(_ucs2 0x0171),(_ucs2 0x0172),(_ucs2 0x0173);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0174),(_ucs2 0x0175),(_ucs2 0x0176),(_ucs2 0x0177);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0178),(_ucs2 0x0179),(_ucs2 0x017a),(_ucs2 0x017b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x017c),(_ucs2 0x017d),(_ucs2 0x017e),(_ucs2 0x017f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0180),(_ucs2 0x0181),(_ucs2 0x0182),(_ucs2 0x0183);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0184),(_ucs2 0x0185),(_ucs2 0x0186),(_ucs2 0x0187);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0188),(_ucs2 0x0189),(_ucs2 0x018a),(_ucs2 0x018b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x018c),(_ucs2 0x018d),(_ucs2 0x018e),(_ucs2 0x018f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0190),(_ucs2 0x0191),(_ucs2 0x0192),(_ucs2 0x0193);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0194),(_ucs2 0x0195),(_ucs2 0x0196),(_ucs2 0x0197);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0198),(_ucs2 0x0199),(_ucs2 0x019a),(_ucs2 0x019b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x019c),(_ucs2 0x019d),(_ucs2 0x019e),(_ucs2 0x019f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01a0),(_ucs2 0x01a1),(_ucs2 0x01a2),(_ucs2 0x01a3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01a4),(_ucs2 0x01a5),(_ucs2 0x01a6),(_ucs2 0x01a7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01a8),(_ucs2 0x01a9),(_ucs2 0x01aa),(_ucs2 0x01ab);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01ac),(_ucs2 0x01ad),(_ucs2 0x01ae),(_ucs2 0x01af);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01b0),(_ucs2 0x01b1),(_ucs2 0x01b2),(_ucs2 0x01b3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01b4),(_ucs2 0x01b5),(_ucs2 0x01b6),(_ucs2 0x01b7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01b8),(_ucs2 0x01b9),(_ucs2 0x01ba),(_ucs2 0x01bb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01bc),(_ucs2 0x01bd),(_ucs2 0x01be),(_ucs2 0x01bf);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01c0),(_ucs2 0x01c1),(_ucs2 0x01c2),(_ucs2 0x01c3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01c4),(_ucs2 0x01c5),(_ucs2 0x01c6),(_ucs2 0x01c7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01c8),(_ucs2 0x01c9),(_ucs2 0x01ca),(_ucs2 0x01cb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01cc),(_ucs2 0x01cd),(_ucs2 0x01ce),(_ucs2 0x01cf);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01d0),(_ucs2 0x01d1),(_ucs2 0x01d2),(_ucs2 0x01d3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01d4),(_ucs2 0x01d5),(_ucs2 0x01d6),(_ucs2 0x01d7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01d8),(_ucs2 0x01d9),(_ucs2 0x01da),(_ucs2 0x01db);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01dc),(_ucs2 0x01dd),(_ucs2 0x01de),(_ucs2 0x01df);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01e0),(_ucs2 0x01e1),(_ucs2 0x01e2),(_ucs2 0x01e3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01e4),(_ucs2 0x01e5),(_ucs2 0x01e6),(_ucs2 0x01e7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01e8),(_ucs2 0x01e9),(_ucs2 0x01ea),(_ucs2 0x01eb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01ec),(_ucs2 0x01ed),(_ucs2 0x01ee),(_ucs2 0x01ef);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01f0),(_ucs2 0x01f1),(_ucs2 0x01f2),(_ucs2 0x01f3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01f4),(_ucs2 0x01f5),(_ucs2 0x01f6),(_ucs2 0x01f7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01f8),(_ucs2 0x01f9),(_ucs2 0x01fa),(_ucs2 0x01fb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01fc),(_ucs2 0x01fd),(_ucs2 0x01fe),(_ucs2 0x01ff);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EA0),(_ucs2 0x1EA1),(_ucs2 0x1EA2),(_ucs2 0x1EA3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EA4),(_ucs2 0x1EA5),(_ucs2 0x1EA6),(_ucs2 0x1EA7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EA8),(_ucs2 0x1EA9),(_ucs2 0x1EAA),(_ucs2 0x1EAB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EAC),(_ucs2 0x1EAD),(_ucs2 0x1EAE),(_ucs2 0x1EAF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EB0),(_ucs2 0x1EB1),(_ucs2 0x1EB2),(_ucs2 0x1EB3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EB4),(_ucs2 0x1EB5),(_ucs2 0x1EB6),(_ucs2 0x1EB7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EB8),(_ucs2 0x1EB9),(_ucs2 0x1EBA),(_ucs2 0x1EBB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EBC),(_ucs2 0x1EBD),(_ucs2 0x1EBE),(_ucs2 0x1EBF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EC0),(_ucs2 0x1EC1),(_ucs2 0x1EC2),(_ucs2 0x1EC3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EC4),(_ucs2 0x1EC5),(_ucs2 0x1EC6),(_ucs2 0x1EC7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EC8),(_ucs2 0x1EC9),(_ucs2 0x1ECA),(_ucs2 0x1ECB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1ECC),(_ucs2 0x1ECD),(_ucs2 0x1ECE),(_ucs2 0x1ECF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1ED0),(_ucs2 0x1ED1),(_ucs2 0x1ED2),(_ucs2 0x1ED3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1ED4),(_ucs2 0x1ED5),(_ucs2 0x1ED6),(_ucs2 0x1ED7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1ED8),(_ucs2 0x1ED9),(_ucs2 0x1EDA),(_ucs2 0x1EDB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EDC),(_ucs2 0x1EDD),(_ucs2 0x1EDE),(_ucs2 0x1EDF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EE0),(_ucs2 0x1EE1),(_ucs2 0x1EE2),(_ucs2 0x1EE3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EE4),(_ucs2 0x1EE5),(_ucs2 0x1EE6),(_ucs2 0x1EE7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EE8),(_ucs2 0x1EE9),(_ucs2 0x1EEA),(_ucs2 0x1EEB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EEC),(_ucs2 0x1EED),(_ucs2 0x1EEE),(_ucs2 0x1EEF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EF0),(_ucs2 0x1EF1);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values ('AA'),('Aa'),('aa'),('aA');
insert into t1 values ('AE'),('Ae'),('ae'),('aE');
insert into t1 values ('CH'),('Ch'),('ch'),('cH');
insert into t1 values ('DZ'),('Dz'),('dz'),('dZ');
insert into t1 values ('DŽ'),('Dž'),('dž'),('dŽ');
insert into t1 values ('IJ'),('Ij'),('ij'),('iJ');
insert into t1 values ('LJ'),('Lj'),('lj'),('lJ');
insert into t1 values ('LL'),('Ll'),('ll'),('lL');
insert into t1 values ('NJ'),('Nj'),('nj'),('nJ');
insert into t1 values ('OE'),('Oe'),('oe'),('oE');
insert into t1 values ('SS'),('Ss'),('ss'),('sS');
insert into t1 values ('RR'),('Rr'),('rr'),('rR');
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_unicode_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_icelandic_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Â,Ã,à,â,ã,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Á,á
Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Ð,ð
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
E,e,È,Ê,Ë,è,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
É,é
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Î,Ï,ì,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
Í,í
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ô,Õ,ò,ô,õ,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ó,ó
Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Û,Ü,ù,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ú,ú
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,ÿ,Ŷ,ŷ,Ÿ
Ý,ý
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Þ,þ
Ä,Æ,ä,æ
Ö,Ø,ö,ø
Å,å
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_latvian_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ
CH,Ch,cH,ch
Č,č
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
DŽ,Dž,dŽ,dž
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ǧ,ǧ,Ǵ,ǵ
Ģ,ģ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
Y,y
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ǩ,ǩ
Ķ,ķ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ļ,ļ
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ņ,ņ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ř,ř
RR,Rr,rR,rr
Ŗ,ŗ
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż
ƍ
Ž,ž
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_romanian_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Ã,Ä,Å,à,á,ã,ä,å,Ā,ā,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Ă,ă
Â,â
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Ï,ì,í,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
Î,î
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Š,š,ſ
SS,Ss,sS,ss,ß
Ş,ş
Ʃ
ƪ
T,t,Ť,ť
ƾ
Ţ,ţ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_slovenian_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ
CH,Ch,cH,ch
Č,č
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
DŽ,Dž,dŽ,dž
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż
ƍ
Ž,ž
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_polish_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Ą,ą
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ć,ć
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ę,ę
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ń,ń
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ô,Õ,Ö,ò,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ó,ó
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ś,ś
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ž,ž
ƍ
Ź,ź
Ż,ż
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_estonian_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Å,à,á,â,ã,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz
DŽ,Dž,dŽ,dž
Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,ò,ó,ô,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Z,z
Ž,ž
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,ù,ú,û,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
Õ,õ
Ä,ä
Ö,ö
Ü,ü
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Ź,ź,Ż,ż
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_spanish_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ñ,ñ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_swedish_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,à,á,â,ã,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,ò,ó,ô,õ,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,ù,ú,û,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ü,Ý,ü,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Å,å
Ä,Æ,ä,æ
Ö,Ø,ö,ø
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_turkish_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ç,ç
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ğ,ğ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
I,ı
IJ,Ij
ƕ,Ƕ
Ħ,ħ
i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
iJ,ij,Ĳ,ĳ
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,ò,ó,ô,õ,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ö,ö
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Š,š,ſ
SS,Ss,sS,ss,ß
Ş,ş
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,ù,ú,û,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ü,ü
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_czech_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ
cH
Č,č
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
DŽ,Dž,dŽ,dž
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
CH,Ch,ch
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ
RR,Rr,rR,rr
Ř,ř
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż
ƍ
Ž,ž
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_danish_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,à,á,â,ã,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
aA
AE,Ae,aE,ae
Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,ò,ó,ô,õ,Ō,ō,Ŏ,ŏ,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,ù,ú,û,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ü,Ý,ü,ý,ÿ,Ű,ű,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ä,Æ,ä,æ
Ö,Ø,ö,ø,Ő,ő
AA,Aa,aa,Å,å
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_lithuanian_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,CH,Ch,c,ch,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ
cH
Č,č
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
DŽ,Dž,dŽ,dž
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,Y,i,y,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż
ƍ
Ž,ž
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_slovak_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Å,à,á,â,ã,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Ä,ä
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ
cH
Č,č
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
DŽ,Dž,dŽ,dž
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
CH,Ch,ch
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Õ,Ö,ò,ó,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ô,ô
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż
ƍ
Ž,ž
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_spanish2_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
cH
CH,Ch,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
lL
LL,Ll,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ñ,ñ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_roman_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,J,i,j,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij
Ĳ,ĳ
ı
Ɨ
Ɩ
Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj
Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj
Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
U,V,u,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_esperanto_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ĉ,ĉ
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ĝ,ĝ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h
Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,ǰ
Ĵ,ĵ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ŝ,ŝ
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ŭ,ŭ
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_hungarian_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,ò,ó,ô,õ,Ō,ō,Ŏ,ŏ,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ö,ö,Ő,ő
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,ù,ú,û,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ü,ü,Ű,ű
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_croatian_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ĉ,ĉ,Ċ,ċ
CH,Ch,cH,ch
Č,č
Ć,ć
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz,Ǳ,ǲ,ǳ
DŽ,Dž,dŽ,dž,Ǆ,ǅ,ǆ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LL,Ll,lL,ll
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż
ƍ
Ž,ž
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_german2_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Å,à,á,â,ã,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae,Ä,Æ,ä,æ
Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,ò,ó,ô,õ,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Ö,ö,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,ù,ú,û,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ü,ü
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_unicode_520_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae,Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ð,ð,Ď,ď,Đ,đ
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Ɖ
Ɗ
Ƌ,ƌ
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ,Ħ,ħ
ƕ,Ƕ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ,Ŀ,ŀ,Ł,ł
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,Ø,ò,ó,ô,õ,ö,ø,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ǿ,ǿ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
select group_concat(c1 order by c1) from t1 group by c1 COLLATE utf8mb3_vietnamese_ci;
group_concat(c1 order by c1)
÷
×
A,a,À,Á,Ã,Ä,Å,à,á,ã,ä,å,Ā,ā,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả
AA,Aa,aA,aa
AE,Ae,aE,ae
Ă,ă,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
Â,â,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ë,è,é,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ
Ê,ê,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Õ,Ö,ò,ó,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ
OE,Oe,oE,oe,Œ,œ
Ô,ô,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ
Ơ,ơ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ
Ư,ư,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
ALTER TABLE t1 CONVERT TO CHARACTER SET ucs2 COLLATE ucs2_bin;
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	4079	'ucs2_bin' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_unicode_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_icelandic_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Â,Ã,à,â,ã,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Á,á
Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Ð,ð
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
E,e,È,Ê,Ë,è,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
É,é
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Î,Ï,ì,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
Í,í
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ô,Õ,ò,ô,õ,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ó,ó
Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Û,Ü,ù,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ú,ú
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,ÿ,Ŷ,ŷ,Ÿ
Ý,ý
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Þ,þ
Ä,Æ,ä,æ
Ö,Ø,ö,ø
Å,å
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_latvian_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ
CH,Ch,cH,ch
Č,č
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
DŽ,Dž,dŽ,dž
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ǧ,ǧ,Ǵ,ǵ
Ģ,ģ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
Y,y
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ǩ,ǩ
Ķ,ķ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ļ,ļ
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ņ,ņ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ř,ř
RR,Rr,rR,rr
Ŗ,ŗ
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż
ƍ
Ž,ž
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_romanian_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Ã,Ä,Å,à,á,ã,ä,å,Ā,ā,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Ă,ă
Â,â
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Ï,ì,í,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
Î,î
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Š,š,ſ
SS,Ss,sS,ss,ß
Ş,ş
Ʃ
ƪ
T,t,Ť,ť
ƾ
Ţ,ţ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_slovenian_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ
CH,Ch,cH,ch
Č,č
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
DŽ,Dž,dŽ,dž
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż
ƍ
Ž,ž
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_polish_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Ą,ą
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ć,ć
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ę,ę
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ń,ń
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ô,Õ,Ö,ò,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ó,ó
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ś,ś
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ž,ž
ƍ
Ź,ź
Ż,ż
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_estonian_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Å,à,á,â,ã,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz
DŽ,Dž,dŽ,dž
Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,ò,ó,ô,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Z,z
Ž,ž
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,ù,ú,û,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
Õ,õ
Ä,ä
Ö,ö
Ü,ü
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Ź,ź,Ż,ż
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_spanish_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ñ,ñ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_swedish_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,à,á,â,ã,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,ò,ó,ô,õ,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,ù,ú,û,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ü,Ý,ü,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Å,å
Ä,Æ,ä,æ
Ö,Ø,ö,ø
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_turkish_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ç,ç
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ğ,ğ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
I,ı
IJ,Ij
ƕ,Ƕ
Ħ,ħ
i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
iJ,ij,Ĳ,ĳ
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,ò,ó,ô,õ,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ö,ö
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Š,š,ſ
SS,Ss,sS,ss,ß
Ş,ş
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,ù,ú,û,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ü,ü
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_czech_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ
cH
Č,č
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
DŽ,Dž,dŽ,dž
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
CH,Ch,ch
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ
RR,Rr,rR,rr
Ř,ř
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż
ƍ
Ž,ž
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_danish_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,à,á,â,ã,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
aA
AE,Ae,aE,ae
Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,ò,ó,ô,õ,Ō,ō,Ŏ,ŏ,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,ù,ú,û,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ü,Ý,ü,ý,ÿ,Ű,ű,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ä,Æ,ä,æ
Ö,Ø,ö,ø,Ő,ő
AA,Aa,aa,Å,å
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_lithuanian_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,CH,Ch,c,ch,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ
cH
Č,č
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
DŽ,Dž,dŽ,dž
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,Y,i,y,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż
ƍ
Ž,ž
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_slovak_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Å,à,á,â,ã,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Ä,ä
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ
cH
Č,č
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
DŽ,Dž,dŽ,dž
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
CH,Ch,ch
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Õ,Ö,ò,ó,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ô,ô
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż
ƍ
Ž,ž
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_spanish2_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
cH
CH,Ch,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
lL
LL,Ll,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ñ,ñ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_roman_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,J,i,j,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij
Ĳ,ĳ
ı
Ɨ
Ɩ
Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj
Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj
Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
U,V,u,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_esperanto_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ĉ,ĉ
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ĝ,ĝ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h
Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,ǰ
Ĵ,ĵ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ŝ,ŝ
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ŭ,ŭ
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_hungarian_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,ò,ó,ô,õ,Ō,ō,Ŏ,ŏ,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ö,ö,Ő,ő
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,ù,ú,û,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ü,ü,Ű,ű
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_croatian_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ĉ,ĉ,Ċ,ċ
CH,Ch,cH,ch
Č,č
Ć,ć
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,dZ,dz,Ǳ,ǲ,ǳ
DŽ,Dž,dŽ,dž,Ǆ,ǅ,ǆ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LL,Ll,lL,ll
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,ò,ó,ô,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,ſ
SS,Ss,sS,ss,ß
Š,š
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż
ƍ
Ž,ž
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_german2_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Å,à,á,â,ã,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae,Ä,Æ,ä,æ
Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,ò,ó,ô,õ,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Ö,ö,Œ,œ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,ù,ú,û,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ü,ü
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_unicode_520_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Â,Ã,Ä,Å,à,á,â,ã,ä,å,Ā,ā,Ă,ă,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
AA,Aa,aA,aa
AE,Ae,aE,ae,Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ð,ð,Ď,ď,Đ,đ
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Ɖ
Ɗ
Ƌ,ƌ
E,e,È,É,Ê,Ë,è,é,ê,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ,Ħ,ħ
ƕ,Ƕ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ,Ŀ,ŀ,Ł,ł
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Ô,Õ,Ö,Ø,ò,ó,ô,õ,ö,ø,Ō,ō,Ŏ,ŏ,Ő,ő,Ơ,ơ,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ǿ,ǿ,Ọ,ọ,Ỏ,ỏ,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
OE,Oe,oE,oe,Œ,œ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ư,ư,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
SELECT GROUP_CONCAT(c1 ORDER BY c1) FROM t1 GROUP BY c1 COLLATE ucs2_vietnamese_ci;
GROUP_CONCAT(c1 ORDER BY c1)
÷
×
A,a,À,Á,Ã,Ä,Å,à,á,ã,ä,å,Ā,ā,Ą,ą,Ǎ,ǎ,Ǟ,ǟ,Ǡ,ǡ,Ǻ,ǻ,Ạ,ạ,Ả,ả
AA,Aa,aA,aa
AE,Ae,aE,ae
Ă,ă,Ắ,ắ,Ằ,ằ,Ẳ,ẳ,Ẵ,ẵ,Ặ,ặ
Â,â,Ấ,ấ,Ầ,ầ,Ẩ,ẩ,Ẫ,ẫ,Ậ,ậ
Æ,æ,Ǣ,ǣ,Ǽ,ǽ
B,b
ƀ
Ɓ
Ƃ,ƃ
C,c,Ç,ç,Ć,ć,Ĉ,ĉ,Ċ,ċ,Č,č
CH,Ch,cH,ch
Ƈ,ƈ
D,d,Ď,ď
DZ,Dz,DŽ,Dž,dZ,dz,dŽ,dž,Ǆ,ǅ,ǆ,Ǳ,ǲ,ǳ
Đ,đ
Ɖ
Ɗ
Ƌ,ƌ
Ð,ð
E,e,È,É,Ë,è,é,ë,Ē,ē,Ĕ,ĕ,Ė,ė,Ę,ę,Ě,ě,Ẹ,ẹ,Ẻ,ẻ,Ẽ,ẽ
Ê,ê,Ế,ế,Ề,ề,Ể,ể,Ễ,ễ,Ệ,ệ
Ǝ,ǝ
Ə
Ɛ
F,f
Ƒ,ƒ
G,g,Ĝ,ĝ,Ğ,ğ,Ġ,ġ,Ģ,ģ,Ǧ,ǧ,Ǵ,ǵ
Ǥ,ǥ
Ɠ
Ɣ
Ƣ,ƣ
H,h,Ĥ,ĥ
ƕ,Ƕ
Ħ,ħ
I,i,Ì,Í,Î,Ï,ì,í,î,ï,Ĩ,ĩ,Ī,ī,Ĭ,ĭ,Į,į,İ,Ǐ,ǐ,Ỉ,ỉ,Ị,ị
IJ,Ij,iJ,ij,Ĳ,ĳ
ı
Ɨ
Ɩ
J,j,Ĵ,ĵ,ǰ
K,k,Ķ,ķ,Ǩ,ǩ
Ƙ,ƙ
L,l,Ĺ,ĺ,Ļ,ļ,Ľ,ľ
Ŀ,ŀ
LJ,Lj,lJ,lj,Ǉ,ǈ,ǉ
LL,Ll,lL,ll
Ł,ł
ƚ
ƛ
M,m
N,n,Ñ,ñ,Ń,ń,Ņ,ņ,Ň,ň,Ǹ,ǹ
NJ,Nj,nJ,nj,Ǌ,ǋ,ǌ
Ɲ
ƞ
Ŋ,ŋ
O,o,Ò,Ó,Õ,Ö,ò,ó,õ,ö,Ō,ō,Ŏ,ŏ,Ő,ő,Ǒ,ǒ,Ǫ,ǫ,Ǭ,ǭ,Ọ,ọ,Ỏ,ỏ
OE,Oe,oE,oe,Œ,œ
Ô,ô,Ố,ố,Ồ,ồ,Ổ,ổ,Ỗ,ỗ,Ộ,ộ
Ơ,ơ,Ớ,ớ,Ờ,ờ,Ở,ở,Ỡ,ỡ,Ợ,ợ
Ø,ø,Ǿ,ǿ
Ɔ
Ɵ
P,p
Ƥ,ƥ
Q,q
ĸ
R,r,Ŕ,ŕ,Ŗ,ŗ,Ř,ř
RR,Rr,rR,rr
Ʀ
S,s,Ś,ś,Ŝ,ŝ,Ş,ş,Š,š,ſ
SS,Ss,sS,ss,ß
Ʃ
ƪ
T,t,Ţ,ţ,Ť,ť
ƾ
Ŧ,ŧ
ƫ
Ƭ,ƭ
Ʈ
U,u,Ù,Ú,Û,Ü,ù,ú,û,ü,Ũ,ũ,Ū,ū,Ŭ,ŭ,Ů,ů,Ű,ű,Ų,ų,Ǔ,ǔ,Ǖ,ǖ,Ǘ,ǘ,Ǚ,ǚ,Ǜ,ǜ,Ụ,ụ,Ủ,ủ
Ư,ư,Ứ,ứ,Ừ,ừ,Ử,ử,Ữ,ữ,Ự,ự
Ɯ
Ʊ
V,v
Ʋ
W,w,Ŵ,ŵ
X,x
Y,y,Ý,ý,ÿ,Ŷ,ŷ,Ÿ
Ƴ,ƴ
Z,z,Ź,ź,Ż,ż,Ž,ž
ƍ
Ƶ,ƶ
Ʒ,Ǯ,ǯ
Ƹ,ƹ
ƺ
Þ,þ
ƿ,Ƿ
ƻ
Ƨ,ƨ
Ƽ,ƽ
Ƅ,ƅ
ŉ
ǀ
ǁ
ǂ
ǃ
drop table t1;
SET NAMES utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
CREATE TABLE t1 (c varchar(255) NOT NULL COLLATE utf8mb3_general_ci, INDEX (c));
Warnings:
Warning	3778	'utf8mb3_general_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x039C03C903B403B11F770308 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1 WHERE c LIKE CONVERT(_ucs2 0x039C0025 USING utf8mb3)
COLLATE utf8mb3_general_ci;
c
Μωδαί̈
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x039C03C903B4 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1 WHERE c LIKE CONVERT(_ucs2 0x039C0025 USING utf8mb3)
COLLATE utf8mb3_general_ci ORDER BY c;
c
Μωδ
Μωδαί̈
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
DROP TABLE t1;
CREATE TABLE t1 (c varchar(255) NOT NULL COLLATE ucs2_unicode_ci, INDEX (c));
Warnings:
Warning	4079	'ucs2_unicode_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
INSERT INTO t1 VALUES (_ucs2 0x039C03C903B403B11F770308);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1 WHERE c LIKE _ucs2 0x039C0025 COLLATE ucs2_unicode_ci;
c
Μωδαί̈
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x039C03C903B4);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1 WHERE c LIKE _ucs2 0x039C0025
COLLATE ucs2_unicode_ci ORDER BY c;
c
Μωδ
Μωδαί̈
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
DROP TABLE t1;
CREATE TABLE t1 (c varchar(255) NOT NULL COLLATE utf8mb3_unicode_ci, INDEX (c));
Warnings:
Warning	3778	'utf8mb3_unicode_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x039C03C903B403B11F770308 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1 WHERE c LIKE CONVERT(_ucs2 0x039C0025 USING utf8mb3) COLLATE utf8mb3_unicode_ci;
c
Μωδαί̈
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x039C03C903B4 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT * FROM t1 WHERE c LIKE CONVERT(_ucs2 0x039C0025 USING utf8mb3)
COLLATE utf8mb3_unicode_ci ORDER BY c;
c
Μωδ
Μωδαί̈
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
DROP TABLE t1;
CREATE TABLE t1 (
col1 CHAR(32) CHARACTER SET utf8mb3 NOT NULL
);
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0041004100410627 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0041004100410628 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0041004100410647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0041004100410648 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0633064A0651062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062D06330646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A0642064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06320627062F0647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062806310627064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064706450647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F062706460634062C0648064A06270646064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06A90647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A06270631064A062E USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062706460642064406270628 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627064A0631062706460650 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627062F064806270631062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06280631062706480646200C06310627 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062E064806270646062F0647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0648 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A062D062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A0623062B064A0631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06220646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0642063106270631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06AF06310641062A0647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06270646062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0634062E0635064A0651062A064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0628062706310632 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06270633062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x063906A90633 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06270648060C USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F0631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062D062F0648062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0628064A0633062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0648067E0646062C USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06330627064406AF064A060C USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x063306270644 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064606450627064A0646062F0647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A06280631064A0632 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0645062C06440633 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06280648062F060C USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0628064A0646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06350641062D0627062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627064A0646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06A9062A06270628 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06280647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x068606340645 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0645064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062E06480631062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0686064706310647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06420648064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06450635064506510645 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06310627 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0646063406270646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0645064A200C062F0647062F060C USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0647063106860646062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06390645064400BB USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06A9064806340634 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0628 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064706500646064A064606AF USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627062D063306270646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064A062706310634062706370631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06450646062A06340631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0634062F0647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F0633062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A064806270646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0647064506270646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627064806510644 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A0634062E064A0635 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F0627062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06280627 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A064106270648062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062D06270644062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A064106A906510631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x063A064406280647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F06270631062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064A06A9064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06270632 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x063106470628063106270646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064606470636062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064506340631064806370647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627064A063106270646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0646064A0632 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064A06A9 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0645062D064206510642 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0637063106270632 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064106310647064606AF USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A0645062F06510646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627064A063106270646064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06280648062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06A90627063106470627064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06270648 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0639063106350647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627064506480631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0633064A06270633064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627064A063106270646060C USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062D064806320647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x063906440645 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F062706460634 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06450642062706440627062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F064A06AF0631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0648064A06980647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0646062706450647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064506480631062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0628062D062B USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0628063106310633064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064606480634062A0647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06450646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A064606470627 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0622064606860647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F064806310647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627064206270645062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x067E0631062F062706320645 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0698062706460648064A0647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0648064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F06390648062A0650 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x063306500631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F0646064A0633064F0646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x063106270633 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0647064A0626062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0639064406480645200C063406310642064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06280639062F0627064B USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0645062F063106330647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062206410631064A06420627064A064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F06270646063406AF06270647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06440646062F0646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x067E064A06480633062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0647064606AF06270645064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x067E0633 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0622063A06270632 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062C064606AF USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062C064706270646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F064806510645 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x063406470631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06A9064506280631064A062C USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06450646062A06420644 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06A90631062F0646062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06470645 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06310641062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06220646062C0627 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064506270646062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A0627 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062706A9062A06280631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064606380631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F06480644062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F06480628062706310647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064606330628062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0645063306270639062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0634062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06480632064A0631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0645062E062A06270631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06330641064A0631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627064606AF0644064A0633 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A0642064A200C06320627062F0647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06280627063206AF0634062A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0647064506330631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06220644064506270646064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06270634 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06220645062F0647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06A906270631064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x067E0631062F0627062E062A0647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x063906440645064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x0627062F0628064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062D062F0651 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064606280648062F060C USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06480644064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x063906480636060C USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06340627064A062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064506470645 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062A0631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06220646060C USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06470645063306310634 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06A90627064606480646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062E0627064606480627062F06AF064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06AF06310645064A USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06280648062C0648062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062206480631062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F0648 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06A90627064506440627064B USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x064A06A9062F064A06AF0631 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06AF USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x062F064406280633062A0647 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06280648062F0646062F USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (CONVERT(_ucs2 0x06450647064506270646 USING utf8mb3));
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT HEX(CONVERT(col1 USING ucs2)) FROM t1 ORDER BY col1 COLLATE utf8mb3_persian_ci, col1 COLLATE utf8mb3_bin;
HEX(CONVERT(col1 USING ucs2))
0041004100410627
0041004100410628
0041004100410648
0041004100410647
0622063A06270632
062206410631064A06420627064A064A
06220644064506270646064A
06220645062F0647
06220646
06220646060C
06220646062C0627
0622064606860647
062206480631062F
0627062D063306270646
0627062F0628064A
0627062F064806270631062F
06270632
06270633062A
06270634
0627064206270645062A
062706A9062A06280631
0627064506480631
06270646062F
062706460642064406270628
0627064606AF0644064A0633
06270648
06270648060C
0627064806510644
0627064A
0627064A063106270646
0627064A063106270646060C
0627064A0631062706460650
0627064A063106270646064A
0627064A0646
0628
06280627
0628062706310632
06280627063206AF0634062A
0628062D062B
06280631062706480646200C06310627
062806310627064A
0628063106310633064A
06280639062F0627064B
06280648062C0648062F
06280648062F
06280648062F060C
06280648062F0646062F
06280647
0628064A0633062A
0628064A0646
067E0631062F0627062E062A0647
067E0631062F062706320645
067E0633
067E064A06480633062A
062A0627
062A06270631064A062E
062A0623062B064A0631
062A06280631064A0632
062A062D062A
062A0631
062A0634062E064A0635
062A064106270648062A
062A064106A906510631
062A0642064A
062A0642064A200C06320627062F0647
062A0645062F06510646
062A064606470627
062A064806270646
062C064606AF
062C064706270646
068606340645
0686064706310647
062D06270644062A
062D062F0651
062D062F0648062F
062D06330646
062D064806320647
062E0627064606480627062F06AF064A
062E064806270646062F0647
062E06480631062F
062F0627062F
062F06270631062F
062F062706460634
062F062706460634062C0648064A06270646064A
062F06270646063406AF06270647
062F0631
062F0633062A
062F06390648062A0650
062F064406280633062A0647
062F0646064A0633064F0646
062F0648
062F06480628062706310647
062F064806310647
062F06480644062A
062F064806510645
062F064A06AF0631
06310627
063106270633
06310641062A
063106470628063106270646
06320627062F0647
0698062706460648064A0647
063306500631
063306270644
06330627064406AF064A060C
06330641064A0631
0633064A06270633064A
0633064A0651062F
06340627064A062F
0634062E0635064A0651062A064A
0634062F
0634062F0647
063406470631
06350641062D0627062A
0637063106270632
0639063106350647
063906A90633
063906440645
063906440645064A
0639064406480645200C063406310642064A
06390645064400BB
063906480636060C
063A064406280647
064106310647064606AF
0642063106270631
06420648064A
06A90627063106470627064A
06A906270631064A
06A90627064506440627064B
06A90627064606480646
06A9062A06270628
06A90631062F0646062F
06A9064506280631064A062C
06A9064806340634
06A90647
06AF
06AF06310641062A0647
06AF06310645064A
06440646062F0646
064506270646062F
0645062C06440633
0645062D064206510642
0645062E062A06270631
0645062F063106330647
0645063306270639062F
064506340631064806370647
06450635064506510645
06450642062706440627062A
06450646
06450646062A06340631
06450646062A06420644
064506480631062F
064506470645
06450647064506270646
0645064A
0645064A200C062F0647062F060C
0646062706450647
064606280648062F060C
064606330628062A
0646063406270646
064606380631
064606450627064A0646062F0647
064606480634062A0647
064606470636062A
0646064A0632
0648
0648067E0646062C
06480632064A0631
06480644064A
0648064A
0648064A06980647
064706500646064A064606AF
0647063106860646062F
06470645
0647064506270646
0647064506330631
06470645063306310634
064706450647
0647064606AF06270645064A
0647064A0626062A
064A062706310634062706370631
064A06A9
064A06A9062F064A06AF0631
064A06A9064A
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
DROP TABLE t1;
CREATE TABLE t1 (
a VARCHAR(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_persian_ci,
offs INT NOT NULL
);
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	3778	'utf8mb3_persian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
INSERT INTO t1 VALUES
(_ucs2 0x066D, 1),(_ucs2 0x064E, 2),(_ucs2 0xFE76, 3),(_ucs2 0xFE77, 4),
(_ucs2 0x0650, 5),(_ucs2 0xFE7A, 6),(_ucs2 0xFE7B, 7),(_ucs2 0x064F, 8),
(_ucs2 0xFE78, 9),(_ucs2 0xFE79,10),(_ucs2 0x064B,11),(_ucs2 0xFE70,12),
(_ucs2 0xFE71,13),(_ucs2 0x064D,14),(_ucs2 0xFE74,15),(_ucs2 0x064C,16),
(_ucs2 0xFE72,17),
(_ucs2 0xFE7F, 1),(_ucs2 0x0653, 2),(_ucs2 0x0654, 3),(_ucs2 0x0655, 4),
(_ucs2 0x0670, 5),
(_ucs2 0x0669, 1),(_ucs2 0x0622, 2),(_ucs2 0x0627, 3),(_ucs2 0x0671, 4),
(_ucs2 0x0621, 5),(_ucs2 0x0623, 6),(_ucs2 0x0625, 7),(_ucs2 0x0624, 8),
(_ucs2 0x0626, 9),
(_ucs2 0x0642, 1),(_ucs2 0x06A9, 2),(_ucs2 0x0643, 3),
(_ucs2 0x0648, 1),(_ucs2 0x0647, 2),(_ucs2 0x0629, 3),(_ucs2 0x06C0, 4),
(_ucs2 0x06CC, 5),(_ucs2 0x0649, 6),(_ucs2 0x064A, 7),
(_ucs2 0xFE80, 1),(_ucs2 0xFE81, 2),(_ucs2 0xFE82, 3),(_ucs2 0xFE8D, 4),
(_ucs2 0xFE8E, 5),(_ucs2 0xFB50, 6),(_ucs2 0xFB51, 7),(_ucs2 0xFE80, 8),
(_ucs2 0xFE83, 9),(_ucs2 0xFE84,10),(_ucs2 0xFE87,11),(_ucs2 0xFE88,12),
(_ucs2 0xFE85,13),(_ucs2 0xFE86,14),(_ucs2 0x0689,16),(_ucs2 0x068A,17),
(_ucs2 0xFEAE, 1),(_ucs2 0xFDFC, 2),
(_ucs2 0xFED8, 1),(_ucs2 0xFB8E, 2),(_ucs2 0xFB8F, 3),(_ucs2 0xFB90, 4),
(_ucs2 0xFB91, 5),(_ucs2 0xFED9, 6),(_ucs2 0xFEDA, 7),(_ucs2 0xFEDB, 8),
(_ucs2 0xFEDC, 9),
(_ucs2 0xFEEE, 1),(_ucs2 0xFEE9, 2),(_ucs2 0xFEEA, 3),(_ucs2 0xFEEB, 4),
(_ucs2 0xFEEC, 5),(_ucs2 0xFE93, 6),(_ucs2 0xFE94, 7),(_ucs2 0xFBA4, 8),
(_ucs2 0xFBA5, 9),(_ucs2 0xFBFC,10),(_ucs2 0xFBFD,11),(_ucs2 0xFBFE,12),
(_ucs2 0xFBFF,13),(_ucs2 0xFEEF,14),(_ucs2 0xFEF0,15),(_ucs2 0xFEF1,16),
(_ucs2 0xFEF2,17),(_ucs2 0xFEF3,18),(_ucs2 0xFEF4,19),(_ucs2 0xFEF5,20),
(_ucs2 0xFEF6,21),(_ucs2 0xFEF7,22),(_ucs2 0xFEF8,23),(_ucs2 0xFEF9,24),
(_ucs2 0xFEFA,25),(_ucs2 0xFEFB,26),(_ucs2 0xFEFC,27);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT HEX(CONVERT(a USING ucs2)), offs, hex(weight_string(a)), a
FROM t1 ORDER BY a, offs, BINARY a;
HEX(CONVERT(a USING ucs2))	offs	hex(weight_string(a))	a
0653	2	0001	ٓ
0654	3	0002	ٔ
0655	4	0003	ٕ
0670	5	0004	ٰ
FE7F	1		ﹿ
066D	1	02CB	٭
064E	2	02CC	َ
FE76	3	02CD	ﹶ
FE77	4	02CE	ﹷ
0650	5	02CF	ِ
FE7A	6	02D0	ﹺ
FE7B	7	02D1	ﹻ
064F	8	02D2	ُ
FE78	9	02D3	ﹸ
FE79	10	02D4	ﹹ
064B	11	02D5	ً
FE70	12	02D6	ﹰ
FE71	13	02D7	ﹱ
064D	14	02D8	ٍ
FE74	15	02D9	ﹴ
064C	16	02DA	ٌ
FE72	17	02DB	ﹲ
0669	1	0E32	٩
0622	2	0E33	آ
0627	3	0E34	ا
0671	4	0E35	ٱ
0621	5	0E36	ء
0623	6	0E37	أ
0625	7	0E38	إ
0624	8	0E39	ؤ
0626	9	0E3A	ئ
FE81	2	1348	ﺁ
FE82	3	1349	ﺂ
FE8D	4	134A	ﺍ
FE8E	5	134B	ﺎ
FB50	6	134C	ﭐ
FB51	7	134D	ﭑ
FE80	1	134E	ﺀ
FE80	8	134E	ﺀ
FE83	9	134F	ﺃ
FE84	10	1350	ﺄ
FE87	11	1351	ﺇ
FE88	12	1352	ﺈ
FE85	13	1353	ﺅ
FE86	14	1354	ﺆ
0689	16	1355	ډ
068A	17	1356	ڊ
FEAE	1	1375	ﺮ
FDFC	2	1376	﷼
0642	1	139B	ق
FED8	1	139B	ﻘ
06A9	2	139C	ک
FB8E	2	139C	ﮎ
0643	3	139D	ك
FB8F	3	139D	ﮏ
FB90	4	139E	ﮐ
FB91	5	139F	ﮑ
FED9	6	13A0	ﻙ
FEDA	7	13A1	ﻚ
FEDB	8	13A2	ﻛ
FEDC	9	13A3	ﻜ
0648	1	13BD	و
FEEE	1	13BD	ﻮ
0647	2	13BE	ه
FEE9	2	13BE	ﻩ
0629	3	13BF	ة
FEEA	3	13BF	ﻪ
06C0	4	13C0	ۀ
FEEB	4	13C0	ﻫ
06CC	5	13C1	ی
FEEC	5	13C1	ﻬ
0649	6	13C2	ى
FE93	6	13C2	ﺓ
064A	7	13C3	ي
FE94	7	13C3	ﺔ
FBA4	8	13C4	ﮤ
FBA5	9	13C5	ﮥ
FBFC	10	13C6	ﯼ
FBFD	11	13C7	ﯽ
FBFE	12	13C8	ﯾ
FBFF	13	13C9	ﯿ
FEEF	14	13CA	ﻯ
FEF0	15	13CB	ﻰ
FEF1	16	13CC	ﻱ
FEF2	17	13CD	ﻲ
FEF3	18	13CE	ﻳ
FEF4	19	13CF	ﻴ
FEF5	20	13D0	ﻵ
FEF6	21	13D1	ﻶ
FEF7	22	13D2	ﻷ
FEF8	23	13D3	ﻸ
FEF9	24	13D4	ﻹ
FEFA	25	13D5	ﻺ
FEFB	26	13D6	ﻻ
FEFC	27	13D7	ﻼ
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
DROP TABLE t1;
SET @test_character_set= 'utf8mb3';
SET @test_collation= 'utf8mb3_swedish_ci';
SET @safe_character_set_server= @@character_set_server;
SET @safe_collation_server= @@collation_server;
SET @safe_character_set_client= @@character_set_client;
SET @safe_character_set_results= @@character_set_results;
SET character_set_server= @test_character_set;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET collation_server= @test_collation;
Warnings:
Warning	3778	'utf8mb3_swedish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
CREATE DATABASE d1;
USE d1;
CREATE TABLE t1 (c CHAR(10), KEY(c));
SHOW FULL COLUMNS FROM t1;
Field	Type	Collation	Null	Key	Default	Extra	Privileges	Comment
c	char(10)	utf8mb3_swedish_ci	YES	MUL	NULL			
INSERT INTO t1 VALUES ('aaa'),('aaaa'),('aaaaa');
SELECT c as want3results FROM t1 WHERE c LIKE 'aaa%';
want3results
aaa
aaaa
aaaaa
DROP TABLE t1;
CREATE TABLE t1 (c1 varchar(15), KEY c1 (c1(2)));
SHOW FULL COLUMNS FROM t1;
Field	Type	Collation	Null	Key	Default	Extra	Privileges	Comment
c1	varchar(15)	utf8mb3_swedish_ci	YES	MUL	NULL			
INSERT INTO t1 VALUES ('location'),('loberge'),('lotre'),('boabab');
SELECT c1 as want3results from t1 where c1 like 'l%';
want3results
location
loberge
lotre
SELECT c1 as want3results from t1 where c1 like 'lo%';
want3results
location
loberge
lotre
SELECT c1 as want1result  from t1 where c1 like 'loc%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'loca%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'locat%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'locati%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'locatio%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'location%';
want1result
location
DROP TABLE t1;
create table t1 (a set('a') not null);
insert ignore into t1 values (),();
Warnings:
Warning	1364	Field 'a' doesn't have a default value
select cast(a as char(1)) from t1;
cast(a as char(1))


select a sounds like a from t1;
a sounds like a
1
1
select 1 from t1 order by cast(a as char(1));
1
1
1
drop table t1;
set names utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
create table t1 (
name varchar(10),
level smallint unsigned);
show create table t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `name` varchar(10) COLLATE utf8mb3_swedish_ci DEFAULT NULL,
  `level` smallint unsigned DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_swedish_ci
insert into t1 values ('string',1);
select concat(name,space(level)), concat(name, repeat(' ',level)) from t1;
concat(name,space(level))	concat(name, repeat(' ',level))
string 	string 
drop table t1;
DROP DATABASE d1;
USE test;
SET character_set_server= @safe_character_set_server;
SET collation_server= @safe_collation_server;
SET character_set_client= @safe_character_set_client;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SET character_set_results= @safe_character_set_results;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
create table t1 (a varchar(1)) character set utf8mb3 COLLATE utf8mb3_estonian_ci;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	3778	'utf8mb3_estonian_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
insert into t1 values ('A'),('B'),('C'),('a'),('b'),('c');
select a, a regexp '[a]' from t1 order by binary a;
a	a regexp '[a]'
A	1
B	0
C	0
a	1
b	0
c	0
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
drop table t1;
SET collation_connection='utf8mb3_unicode_ci';
Warnings:
Warning	3778	'utf8mb3_unicode_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
create table t1 select repeat('a',4000) a;
delete from t1;
insert into t1 values ('a'), ('a '), ('a\t');
select collation(a),hex(a) from t1 order by a;
collation(a)	hex(a)
utf8mb3_unicode_ci	6109
utf8mb3_unicode_ci	61
utf8mb3_unicode_ci	6120
drop table t1;
select @@collation_connection;
@@collation_connection
utf8mb3_unicode_ci
create table t1 ROW_FORMAT=DYNAMIC select repeat('a',50) as c1 ;
insert into t1 values('abcdef');
insert into t1 values('_bcdef');
insert into t1 values('a_cdef');
insert into t1 values('ab_def');
insert into t1 values('abc_ef');
insert into t1 values('abcd_f');
insert into t1 values('abcde_');
select c1 as c1u from t1 where c1 like 'ab\_def';
c1u
ab_def
select c1 as c2h from t1 where c1 like 'ab#_def' escape '#';
c2h
ab_def
drop table t1;
"BEGIN ctype_german.inc"
drop table if exists t1;
create table t1 as select repeat(' ', 64) as s1;
select collation(s1) from t1;
collation(s1)
utf8mb3_unicode_ci
delete from t1;
INSERT INTO t1 VALUES ('ud'),('uf');
INSERT INTO t1 VALUES ('od'),('of');
INSERT INTO t1 VALUES ('e');
INSERT INTO t1 VALUES ('ad'),('af');
insert into t1 values ('a'),('ae'),(_latin1 0xE4);
insert into t1 values ('o'),('oe'),(_latin1 0xF6);
insert into t1 values ('s'),('ss'),(_latin1 0xDF);
insert into t1 values ('u'),('ue'),(_latin1 0xFC);
INSERT INTO t1 VALUES (_latin1 0xE6), (_latin1 0xC6);
INSERT INTO t1 VALUES (_latin1 0x9C), (_latin1 0x8C);
select s1, hex(s1) from t1 order by s1, binary s1;
s1	hex(s1)
a	61
ä	C3A4
ad	6164
ae	6165
af	6166
Æ	C386
æ	C3A6
e	65
o	6F
ö	C3B6
od	6F64
oe	6F65
Œ	C592
œ	C593
of	6F66
s	73
ss	7373
ß	C39F
u	75
ü	C3BC
ud	7564
ue	7565
uf	7566
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(s1 order by binary s1) from t1 group by s1;
group_concat(s1 order by binary s1)
a,ä
ad
ae
af
Æ,æ
e
o,ö
od
oe,Œ,œ
of
s
ss,ß
u,ü
ud
ue
uf
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
SELECT s1, hex(s1), hex(weight_string(s1)) FROM t1 ORDER BY s1, BINARY(s1);
s1	hex(s1)	hex(weight_string(s1))
a	61	0E33
ä	C3A4	0E33
ad	6164	0E330E6D
ae	6165	0E330E8B
af	6166	0E330EB9
Æ	C386	0E38
æ	C3A6	0E38
e	65	0E8B
o	6F	0F82
ö	C3B6	0F82
od	6F64	0F820E6D
oe	6F65	0F820E8B
Œ	C592	0F820E8B
œ	C593	0F820E8B
of	6F66	0F820EB9
s	73	0FEA
ss	7373	0FEA0FEA
ß	C39F	0FEA0FEA
u	75	101F
ü	C3BC	101F
ud	7564	101F0E6D
ue	7565	101F0E8B
uf	7566	101F0EB9
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
SELECT s1, hex(s1) FROM t1 WHERE s1='ae' ORDER BY s1, BINARY(s1);
s1	hex(s1)
ae	6165
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
drop table t1;
"END ctype_german.inc"
CREATE TABLE t1 (id int, a varchar(30) character set utf8mb3);
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (1, _ucs2 0x01310069), (2, _ucs2 0x01310131);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (3, _ucs2 0x00690069), (4, _ucs2 0x01300049);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (5, _ucs2 0x01300130), (6, _ucs2 0x00490049);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT a, length(a) la, @l:=lower(a) l, length(@l) ll, @u:=upper(a) u, length(@u) lu
FROM t1 ORDER BY id;
a	la	l	ll	u	lu
ıi	3	ıi	3	II	2
ıı	4	ıı	4	II	2
ii	2	ii	2	II	2
İI	3	ii	2	İI	3
İİ	4	ii	2	İİ	4
II	2	ii	2	II	2
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
ALTER TABLE t1 MODIFY a VARCHAR(30) character set utf8mb3 COLLATE utf8mb3_turkish_ci;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	3778	'utf8mb3_turkish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
SELECT a, length(a) la, @l:=lower(a) l, length(@l) ll, @u:=upper(a) u, length(@u) lu
FROM t1 ORDER BY id;
a	la	l	ll	u	lu
ıi	3	ıi	3	Iİ	3
ıı	4	ıı	4	II	2
ii	2	ii	2	İİ	4
İI	3	iı	3	İI	3
İİ	4	ii	2	İİ	4
II	2	ıı	4	II	2
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
DROP TABLE t1;
set names utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
create table t1 (
a varchar(255),
key a(a)
) character set utf8mb3 COLLATE utf8mb3_danish_ci;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	3778	'utf8mb3_danish_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
insert into t1 values ('åaaaa'),('ååaaa'),('aaaaa');
select a as like_a from t1 where a like 'a%';
like_a
aaaaa
select a as like_aa from t1 where a like 'aa%';
like_aa
aaaaa
select a as like_aaa from t1 where a like 'aaa%';
like_aaa
aaaaa
select a as like_aaaa from t1 where a like 'aaaa%';
like_aaaa
aaaaa
select a as like_aaaaa from t1 where a like 'aaaaa%';
like_aaaaa
aaaaa
alter table t1 convert to character set ucs2 collate ucs2_danish_ci;
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	4079	'ucs2_danish_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
select a as like_a from t1 where a like 'a%';
like_a
aaaaa
select a as like_aa from t1 where a like 'aa%';
like_aa
aaaaa
select a as like_aaa from t1 where a like 'aaa%';
like_aaa
aaaaa
select a as like_aaaa from t1 where a like 'aaaa%';
like_aaaa
aaaaa
select a as like_aaaaa from t1 where a like 'aaaaa%';
like_aaaaa
aaaaa
drop table t1;
create table t1 (
a varchar(255),
key(a)
) character set utf8mb3 COLLATE utf8mb3_spanish2_ci;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	3778	'utf8mb3_spanish2_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
insert into t1 values ('aaaaa'),('lllll'),('zzzzz');
select a as like_l from t1 where a like 'l%';
like_l
lllll
select a as like_ll from t1 where a like 'll%';
like_ll
lllll
select a as like_lll from t1 where a like 'lll%';
like_lll
lllll
select a as like_llll from t1 where a like 'llll%';
like_llll
lllll
select a as like_lllll from t1 where a like 'lllll%';
like_lllll
lllll
alter table t1 convert to character set ucs2 collate ucs2_spanish2_ci;
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	4079	'ucs2_spanish2_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
select a as like_l from t1 where a like 'l%';
like_l
lllll
select a as like_ll from t1 where a like 'll%';
like_ll
lllll
select a as like_lll from t1 where a like 'lll%';
like_lll
lllll
select a as like_llll from t1 where a like 'llll%';
like_llll
lllll
select a as like_lllll from t1 where a like 'lllll%';
like_lllll
lllll
drop table t1;
create table t1 (
a varchar(255),
key a(a)
) character set utf8mb3 COLLATE utf8mb3_czech_ci;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	3778	'utf8mb3_czech_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
insert into t1 values
('b'),('c'),('d'),('e'),('f'),('g'),('h'),('ch'),('i'),('j');
select * from t1 where a like 'c%';
a
c
ch
alter table t1 convert to character set ucs2 collate ucs2_czech_ci;
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	4079	'ucs2_czech_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
select * from t1 where a like 'c%';
a
c
ch
drop table t1;
set collation_connection=ucs2_unicode_ci;
Warnings:
Warning	4079	'ucs2_unicode_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
drop table if exists t1;
create table t1 as
select repeat(' ', 64) as s1, repeat(' ',64) as s2
union
select null, null;
show create table t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `s1` varchar(64) CHARACTER SET ucs2 COLLATE ucs2_unicode_ci DEFAULT NULL,
  `s2` varchar(64) CHARACTER SET ucs2 COLLATE ucs2_unicode_ci DEFAULT NULL
) ENGINE=default_engine DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
delete from t1;
insert into t1 values('aaa','aaa');
insert into t1 values('aaa|qqq','qqq');
insert into t1 values('gheis','^[^a-dXYZ]+$');
insert into t1 values('aab','^aa?b');
insert into t1 values('Baaan','^Ba*n');
insert into t1 values('aaa','qqq|aaa');
insert into t1 values('qqq','qqq|aaa');
insert into t1 values('bbb','qqq|aaa');
insert into t1 values('bbb','qqq');
insert into t1 values('aaa','aba');
insert into t1 values(null,'abc');
insert into t1 values('def',null);
insert into t1 values(null,null);
select HIGH_PRIORITY s1 regexp s2 from t1;
s1 regexp s2
1
1
1
1
1
1
1
0
0
0
NULL
NULL
NULL
SELECT 'ghi' REGEXP 'ghi[';
ERROR HY000: The regular expression contains an unclosed bracket expression.
drop table t1;
CREATE TABLE t1 AS
SELECT 10 AS a, REPEAT('a',20) AS b, REPEAT('a',8) AS c, REPEAT('a',8) AS d;
ALTER TABLE t1 ADD PRIMARY KEY(a), ADD KEY(b);
INSERT INTO t1 (a, b) VALUES (1, repeat(0xF1F2,5));
INSERT INTO t1 (a, b) VALUES (2, repeat(0xF1F2,10));
INSERT INTO t1 (a, b) VALUES (3, repeat(0xF1F2,11));
INSERT INTO t1 (a, b) VALUES (4, repeat(0xF1F2,12));
SELECT hex(concat(repeat(0xF1F2, 10), '%'));
hex(concat(repeat(0xF1F2, 10), '%'))
F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F20025
3 rows expected
SELECT a, hex(b), c FROM t1 WHERE b LIKE concat(repeat(0xF1F2,10), '%');
a	hex(b)	c
2	F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2	NULL
3	F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2	NULL
4	F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2F1F2	NULL
DROP TABLE t1;
set names utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
End for 5.0 tests
End of 5.1 tests
#
# Start of 5.5 tests
#
SET collation_connection=utf8mb3_czech_ci;
Warnings:
Warning	3778	'utf8mb3_czech_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
SELECT @@collation_connection;
@@collation_connection
utf8mb3_czech_ci
#
# Bug#57737 Character sets: search fails with like, contraction, index
#
CREATE TABLE t1 AS SELECT REPEAT(' ', 10) AS s1 LIMIT 0;
INSERT INTO t1 VALUES ('c'),('ce'),('cé'),('ch');
SELECT * FROM t1 WHERE s1 LIKE 'c%';
s1
c
ce
cé
ch
ALTER TABLE t1 ADD KEY s1 (s1);
SELECT * FROM t1 WHERE s1 LIKE 'c%';
s1
c
ce
cé
ch
ALTER TABLE t1 DROP KEY s1, ADD KEY(s1(1));
SELECT * FROM t1 WHERE s1 LIKE 'ch';
s1
ch
DROP TABLE t1;
SELECT @@collation_connection;
@@collation_connection
utf8mb3_czech_ci
#
# Bug#57737 Character sets: search fails with like, contraction, index
# Part#2 - ignorable characters
#
CREATE TABLE t1 AS SELECT REPEAT(' ', 10) AS s1 LIMIT 0;
INSERT INTO t1 VALUES ('a\0\0\0\0\0\t'),('a'),('b'),('c'),('d'),('e');
SELECT HEX(s1) FROM t1 WHERE s1 LIKE 'a%';
HEX(s1)
61000000000009
61
ALTER TABLE t1 ADD KEY s1 (s1);
SELECT HEX(s1) FROM t1 WHERE s1 LIKE 'a%';
HEX(s1)
61000000000009
61
DROP TABLE t1;
SET collation_connection=ucs2_czech_ci;
Warnings:
Warning	4079	'ucs2_czech_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
SELECT @@collation_connection;
@@collation_connection
ucs2_czech_ci
#
# Bug#57737 Character sets: search fails with like, contraction, index
#
CREATE TABLE t1 AS SELECT REPEAT(' ', 10) AS s1 LIMIT 0;
INSERT INTO t1 VALUES ('c'),('ce'),('cé'),('ch');
SELECT * FROM t1 WHERE s1 LIKE 'c%';
s1
c
ce
cé
ch
ALTER TABLE t1 ADD KEY s1 (s1);
SELECT * FROM t1 WHERE s1 LIKE 'c%';
s1
c
ce
cé
ch
ALTER TABLE t1 DROP KEY s1, ADD KEY(s1(1));
SELECT * FROM t1 WHERE s1 LIKE 'ch';
s1
ch
DROP TABLE t1;
SELECT @@collation_connection;
@@collation_connection
ucs2_czech_ci
#
# Bug#57737 Character sets: search fails with like, contraction, index
# Part#2 - ignorable characters
#
CREATE TABLE t1 AS SELECT REPEAT(' ', 10) AS s1 LIMIT 0;
INSERT INTO t1 VALUES ('a\0\0\0\0\0\t'),('a'),('b'),('c'),('d'),('e');
SELECT HEX(s1) FROM t1 WHERE s1 LIKE 'a%';
HEX(s1)
0061000000000000000000000009
0061
ALTER TABLE t1 ADD KEY s1 (s1);
SELECT HEX(s1) FROM t1 WHERE s1 LIKE 'a%';
HEX(s1)
0061000000000000000000000009
0061
DROP TABLE t1;
#
# End of 5.5 tests
#
#
# Start of 5.6 tests
#
#
# WL#3664 WEIGHT_STRING
#
set collation_connection=ucs2_unicode_ci;
Warnings:
Warning	4079	'ucs2_unicode_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
select @@collation_connection;
@@collation_connection
ucs2_unicode_ci
select hex(weight_string('a'));
hex(weight_string('a'))
0E33
select hex(weight_string('A'));
hex(weight_string('A'))
0E33
select hex(weight_string('abc'));
hex(weight_string('abc'))
0E330E4A0E60
select hex(weight_string('abc' as char(2)));
hex(weight_string('abc' as char(2)))
0E330E4A
select hex(weight_string('abc' as char(3)));
hex(weight_string('abc' as char(3)))
0E330E4A0E60
select hex(weight_string('abc' as char(5)));
hex(weight_string('abc' as char(5)))
0E330E4A0E6002090209
select hex(weight_string('abc', 1, 2, 0xC0));
hex(weight_string('abc', 1, 2, 0xC0))
0E
select hex(weight_string('abc', 2, 2, 0xC0));
hex(weight_string('abc', 2, 2, 0xC0))
0E33
select hex(weight_string('abc', 3, 2, 0xC0));
hex(weight_string('abc', 3, 2, 0xC0))
0E330E
select hex(weight_string('abc', 4, 2, 0xC0));
hex(weight_string('abc', 4, 2, 0xC0))
0E330E4A
select hex(weight_string('abc', 5, 2, 0xC0));
hex(weight_string('abc', 5, 2, 0xC0))
0E330E4A02
select hex(weight_string('abc',25, 2, 0xC0));
hex(weight_string('abc',25, 2, 0xC0))
0E330E4A020902090209020902090209020902090209020902
select hex(weight_string('abc', 1, 3, 0xC0));
hex(weight_string('abc', 1, 3, 0xC0))
0E
select hex(weight_string('abc', 2, 3, 0xC0));
hex(weight_string('abc', 2, 3, 0xC0))
0E33
select hex(weight_string('abc', 3, 3, 0xC0));
hex(weight_string('abc', 3, 3, 0xC0))
0E330E
select hex(weight_string('abc', 4, 3, 0xC0));
hex(weight_string('abc', 4, 3, 0xC0))
0E330E4A
select hex(weight_string('abc', 5, 3, 0xC0));
hex(weight_string('abc', 5, 3, 0xC0))
0E330E4A0E
select hex(weight_string('abc',25, 3, 0xC0));
hex(weight_string('abc',25, 3, 0xC0))
0E330E4A0E6002090209020902090209020902090209020902
select hex(weight_string('abc', 1, 4, 0xC0));
hex(weight_string('abc', 1, 4, 0xC0))
0E
select hex(weight_string('abc', 2, 4, 0xC0));
hex(weight_string('abc', 2, 4, 0xC0))
0E33
select hex(weight_string('abc', 3, 4, 0xC0));
hex(weight_string('abc', 3, 4, 0xC0))
0E330E
select hex(weight_string('abc', 4, 4, 0xC0));
hex(weight_string('abc', 4, 4, 0xC0))
0E330E4A
select hex(weight_string('abc', 5, 4, 0xC0));
hex(weight_string('abc', 5, 4, 0xC0))
0E330E4A0E
select hex(weight_string('abc',25, 4, 0xC0));
hex(weight_string('abc',25, 4, 0xC0))
0E330E4A0E6002090209020902090209020902090209020902
select @@collation_connection;
@@collation_connection
ucs2_unicode_ci
select hex(weight_string(cast(_latin1 0x80 as char)));
hex(weight_string(cast(_latin1 0x80 as char)))
0E23
select hex(weight_string(cast(_latin1 0x808080 as char)));
hex(weight_string(cast(_latin1 0x808080 as char)))
0E230E230E23
select hex(weight_string(cast(_latin1 0x808080 as char) as char(2)));
hex(weight_string(cast(_latin1 0x808080 as char) as char(2)))
0E230E23
select hex(weight_string(cast(_latin1 0x808080 as char) as char(3)));
hex(weight_string(cast(_latin1 0x808080 as char) as char(3)))
0E230E230E23
select hex(weight_string(cast(_latin1 0x808080 as char) as char(5)));
hex(weight_string(cast(_latin1 0x808080 as char) as char(5)))
0E230E230E2302090209
select hex(weight_string(cast(_latin1 0x808080 as char), 1, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 1, 2, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x808080 as char), 2, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 2, 2, 0xC0))
0E23
select hex(weight_string(cast(_latin1 0x808080 as char), 3, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 3, 2, 0xC0))
0E230E
select hex(weight_string(cast(_latin1 0x808080 as char), 4, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 4, 2, 0xC0))
0E230E23
select hex(weight_string(cast(_latin1 0x808080 as char), 5, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 5, 2, 0xC0))
0E230E2302
select hex(weight_string(cast(_latin1 0x808080 as char),25, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char),25, 2, 0xC0))
0E230E23020902090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0x808080 as char), 1, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 1, 3, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x808080 as char), 2, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 2, 3, 0xC0))
0E23
select hex(weight_string(cast(_latin1 0x808080 as char), 3, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 3, 3, 0xC0))
0E230E
select hex(weight_string(cast(_latin1 0x808080 as char), 4, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 4, 3, 0xC0))
0E230E23
select hex(weight_string(cast(_latin1 0x808080 as char), 5, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 5, 3, 0xC0))
0E230E230E
select hex(weight_string(cast(_latin1 0x808080 as char),25, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char),25, 3, 0xC0))
0E230E230E2302090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0x808080 as char), 1, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 1, 4, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x808080 as char), 2, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 2, 4, 0xC0))
0E23
select hex(weight_string(cast(_latin1 0x808080 as char), 3, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 3, 4, 0xC0))
0E230E
select hex(weight_string(cast(_latin1 0x808080 as char), 4, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 4, 4, 0xC0))
0E230E23
select hex(weight_string(cast(_latin1 0x808080 as char), 5, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 5, 4, 0xC0))
0E230E230E
select hex(weight_string(cast(_latin1 0x808080 as char),25, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char),25, 4, 0xC0))
0E230E230E2302090209020902090209020902090209020902
set @@collation_connection=utf8mb3_unicode_ci;
Warnings:
Warning	3778	'utf8mb3_unicode_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
select @@collation_connection;
@@collation_connection
utf8mb3_unicode_ci
select hex(weight_string('a'));
hex(weight_string('a'))
0E33
select hex(weight_string('A'));
hex(weight_string('A'))
0E33
select hex(weight_string('abc'));
hex(weight_string('abc'))
0E330E4A0E60
select hex(weight_string('abc' as char(2)));
hex(weight_string('abc' as char(2)))
0E330E4A
select hex(weight_string('abc' as char(3)));
hex(weight_string('abc' as char(3)))
0E330E4A0E60
select hex(weight_string('abc' as char(5)));
hex(weight_string('abc' as char(5)))
0E330E4A0E6002090209
select hex(weight_string('abc', 1, 2, 0xC0));
hex(weight_string('abc', 1, 2, 0xC0))
0E
select hex(weight_string('abc', 2, 2, 0xC0));
hex(weight_string('abc', 2, 2, 0xC0))
0E33
select hex(weight_string('abc', 3, 2, 0xC0));
hex(weight_string('abc', 3, 2, 0xC0))
0E330E
select hex(weight_string('abc', 4, 2, 0xC0));
hex(weight_string('abc', 4, 2, 0xC0))
0E330E4A
select hex(weight_string('abc', 5, 2, 0xC0));
hex(weight_string('abc', 5, 2, 0xC0))
0E330E4A02
select hex(weight_string('abc',25, 2, 0xC0));
hex(weight_string('abc',25, 2, 0xC0))
0E330E4A020902090209020902090209020902090209020902
select hex(weight_string('abc', 1, 3, 0xC0));
hex(weight_string('abc', 1, 3, 0xC0))
0E
select hex(weight_string('abc', 2, 3, 0xC0));
hex(weight_string('abc', 2, 3, 0xC0))
0E33
select hex(weight_string('abc', 3, 3, 0xC0));
hex(weight_string('abc', 3, 3, 0xC0))
0E330E
select hex(weight_string('abc', 4, 3, 0xC0));
hex(weight_string('abc', 4, 3, 0xC0))
0E330E4A
select hex(weight_string('abc', 5, 3, 0xC0));
hex(weight_string('abc', 5, 3, 0xC0))
0E330E4A0E
select hex(weight_string('abc',25, 3, 0xC0));
hex(weight_string('abc',25, 3, 0xC0))
0E330E4A0E6002090209020902090209020902090209020902
select hex(weight_string('abc', 1, 4, 0xC0));
hex(weight_string('abc', 1, 4, 0xC0))
0E
select hex(weight_string('abc', 2, 4, 0xC0));
hex(weight_string('abc', 2, 4, 0xC0))
0E33
select hex(weight_string('abc', 3, 4, 0xC0));
hex(weight_string('abc', 3, 4, 0xC0))
0E330E
select hex(weight_string('abc', 4, 4, 0xC0));
hex(weight_string('abc', 4, 4, 0xC0))
0E330E4A
select hex(weight_string('abc', 5, 4, 0xC0));
hex(weight_string('abc', 5, 4, 0xC0))
0E330E4A0E
select hex(weight_string('abc',25, 4, 0xC0));
hex(weight_string('abc',25, 4, 0xC0))
0E330E4A0E6002090209020902090209020902090209020902
select @@collation_connection;
@@collation_connection
utf8mb3_unicode_ci
select hex(weight_string(cast(_latin1 0x80 as char)));
hex(weight_string(cast(_latin1 0x80 as char)))
0E23
select hex(weight_string(cast(_latin1 0x808080 as char)));
hex(weight_string(cast(_latin1 0x808080 as char)))
0E230E230E23
select hex(weight_string(cast(_latin1 0x808080 as char) as char(2)));
hex(weight_string(cast(_latin1 0x808080 as char) as char(2)))
0E230E23
select hex(weight_string(cast(_latin1 0x808080 as char) as char(3)));
hex(weight_string(cast(_latin1 0x808080 as char) as char(3)))
0E230E230E23
select hex(weight_string(cast(_latin1 0x808080 as char) as char(5)));
hex(weight_string(cast(_latin1 0x808080 as char) as char(5)))
0E230E230E2302090209
select hex(weight_string(cast(_latin1 0x808080 as char), 1, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 1, 2, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x808080 as char), 2, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 2, 2, 0xC0))
0E23
select hex(weight_string(cast(_latin1 0x808080 as char), 3, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 3, 2, 0xC0))
0E230E
select hex(weight_string(cast(_latin1 0x808080 as char), 4, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 4, 2, 0xC0))
0E230E23
select hex(weight_string(cast(_latin1 0x808080 as char), 5, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 5, 2, 0xC0))
0E230E2302
select hex(weight_string(cast(_latin1 0x808080 as char),25, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char),25, 2, 0xC0))
0E230E23020902090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0x808080 as char), 1, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 1, 3, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x808080 as char), 2, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 2, 3, 0xC0))
0E23
select hex(weight_string(cast(_latin1 0x808080 as char), 3, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 3, 3, 0xC0))
0E230E
select hex(weight_string(cast(_latin1 0x808080 as char), 4, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 4, 3, 0xC0))
0E230E23
select hex(weight_string(cast(_latin1 0x808080 as char), 5, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 5, 3, 0xC0))
0E230E230E
select hex(weight_string(cast(_latin1 0x808080 as char),25, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char),25, 3, 0xC0))
0E230E230E2302090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0x808080 as char), 1, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 1, 4, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x808080 as char), 2, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 2, 4, 0xC0))
0E23
select hex(weight_string(cast(_latin1 0x808080 as char), 3, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 3, 4, 0xC0))
0E230E
select hex(weight_string(cast(_latin1 0x808080 as char), 4, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 4, 4, 0xC0))
0E230E23
select hex(weight_string(cast(_latin1 0x808080 as char), 5, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 5, 4, 0xC0))
0E230E230E
select hex(weight_string(cast(_latin1 0x808080 as char),25, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char),25, 4, 0xC0))
0E230E230E2302090209020902090209020902090209020902
set @@collation_connection=utf8mb3_czech_ci;
Warnings:
Warning	3778	'utf8mb3_czech_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
select @@collation_connection;
@@collation_connection
utf8mb3_czech_ci
select collation(cast(_latin1 0xDF as char));
collation(cast(_latin1 0xDF as char))
utf8mb3_czech_ci
select hex(weight_string('s'));
hex(weight_string('s'))
0FEA
select hex(weight_string(cast(_latin1 0xDF as char)));
hex(weight_string(cast(_latin1 0xDF as char)))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF as char) as char(1)));
hex(weight_string(cast(_latin1 0xDF as char) as char(1)))
0FEA0FEA
select hex(weight_string('c'));
hex(weight_string('c'))
0E60
select hex(weight_string('h'));
hex(weight_string('h'))
0EE1
select hex(weight_string('ch'));
hex(weight_string('ch'))
0EE2
select hex(weight_string('i'));
hex(weight_string('i'))
0EFB
select hex(weight_string(cast(_latin1 0x6368DF as char)));
hex(weight_string(cast(_latin1 0x6368DF as char)))
0EE20FEA0FEA
select hex(weight_string(cast(_latin1 0x6368DF as char) as char(1)));
hex(weight_string(cast(_latin1 0x6368DF as char) as char(1)))
0E60
select hex(weight_string(cast(_latin1 0x6368DF as char) as char(2)));
hex(weight_string(cast(_latin1 0x6368DF as char) as char(2)))
0EE2
select hex(weight_string(cast(_latin1 0x6368DF as char) as char(3)));
hex(weight_string(cast(_latin1 0x6368DF as char) as char(3)))
0EE20FEA0FEA
select hex(weight_string(cast(_latin1 0x6368DF as char) as char(4)));
hex(weight_string(cast(_latin1 0x6368DF as char) as char(4)))
0EE20FEA0FEA0209
select hex(weight_string(cast(_latin1 0xDF6368 as char)));
hex(weight_string(cast(_latin1 0xDF6368 as char)))
0FEA0FEA0EE2
select hex(weight_string(cast(_latin1 0xDF6368 as char) as char(1)));
hex(weight_string(cast(_latin1 0xDF6368 as char) as char(1)))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char) as char(2)));
hex(weight_string(cast(_latin1 0xDF6368 as char) as char(2)))
0FEA0FEA0E60
select hex(weight_string(cast(_latin1 0xDF6368 as char) as char(3)));
hex(weight_string(cast(_latin1 0xDF6368 as char) as char(3)))
0FEA0FEA0EE2
select hex(weight_string(cast(_latin1 0xDF6368 as char) as char(4)));
hex(weight_string(cast(_latin1 0xDF6368 as char) as char(4)))
0FEA0FEA0EE20209
select hex(weight_string(cast(_latin1 0x6368DF as char), 1, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 1, 2, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x6368DF as char), 2, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 2, 2, 0xC0))
0EE2
select hex(weight_string(cast(_latin1 0x6368DF as char), 3, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 3, 2, 0xC0))
0EE202
select hex(weight_string(cast(_latin1 0x6368DF as char), 4, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 4, 2, 0xC0))
0EE20209
select hex(weight_string(cast(_latin1 0x6368DF as char),25, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char),25, 2, 0xC0))
0EE20209020902090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0x6368DF as char), 1, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 1, 3, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x6368DF as char), 2, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 2, 3, 0xC0))
0EE2
select hex(weight_string(cast(_latin1 0x6368DF as char), 3, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 3, 3, 0xC0))
0EE20F
select hex(weight_string(cast(_latin1 0x6368DF as char), 4, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 4, 3, 0xC0))
0EE20FEA
select hex(weight_string(cast(_latin1 0x6368DF as char),25, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char),25, 3, 0xC0))
0EE20FEA0FEA02090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0x6368DF as char), 1, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 1, 4, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x6368DF as char), 2, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 2, 4, 0xC0))
0EE2
select hex(weight_string(cast(_latin1 0x6368DF as char), 3, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 3, 4, 0xC0))
0EE20F
select hex(weight_string(cast(_latin1 0x6368DF as char), 4, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 4, 4, 0xC0))
0EE20FEA
select hex(weight_string(cast(_latin1 0x6368DF as char),25, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char),25, 4, 0xC0))
0EE20FEA0FEA02090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 2,0xC0))
0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 2,0xC0))
0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 2,0xC0))
0FEA0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 2,0xC0))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char),25, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char),25, 2,0xC0))
0FEA0FEA0E6002090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 3,0xC0))
0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 3,0xC0))
0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 3,0xC0))
0FEA0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 3,0xC0))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char),25, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char),25, 3,0xC0))
0FEA0FEA0EE202090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 4,0xC0))
0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 4,0xC0))
0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 4,0xC0))
0FEA0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 4,0xC0))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char),25, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char),25, 4,0xC0))
0FEA0FEA0EE202090209020902090209020902090209020902
set @@collation_connection=ucs2_czech_ci;
Warnings:
Warning	4079	'ucs2_czech_ci' is a collation of the deprecated character set ucs2. Please consider using utf8mb4 with an appropriate collation instead.
select @@collation_connection;
@@collation_connection
ucs2_czech_ci
select collation(cast(_latin1 0xDF as char));
collation(cast(_latin1 0xDF as char))
ucs2_czech_ci
select hex(weight_string('s'));
hex(weight_string('s'))
0FEA
select hex(weight_string(cast(_latin1 0xDF as char)));
hex(weight_string(cast(_latin1 0xDF as char)))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF as char) as char(1)));
hex(weight_string(cast(_latin1 0xDF as char) as char(1)))
0FEA0FEA
select hex(weight_string('c'));
hex(weight_string('c'))
0E60
select hex(weight_string('h'));
hex(weight_string('h'))
0EE1
select hex(weight_string('ch'));
hex(weight_string('ch'))
0EE2
select hex(weight_string('i'));
hex(weight_string('i'))
0EFB
select hex(weight_string(cast(_latin1 0x6368DF as char)));
hex(weight_string(cast(_latin1 0x6368DF as char)))
0EE20FEA0FEA
select hex(weight_string(cast(_latin1 0x6368DF as char) as char(1)));
hex(weight_string(cast(_latin1 0x6368DF as char) as char(1)))
0E60
select hex(weight_string(cast(_latin1 0x6368DF as char) as char(2)));
hex(weight_string(cast(_latin1 0x6368DF as char) as char(2)))
0EE2
select hex(weight_string(cast(_latin1 0x6368DF as char) as char(3)));
hex(weight_string(cast(_latin1 0x6368DF as char) as char(3)))
0EE20FEA0FEA
select hex(weight_string(cast(_latin1 0x6368DF as char) as char(4)));
hex(weight_string(cast(_latin1 0x6368DF as char) as char(4)))
0EE20FEA0FEA0209
select hex(weight_string(cast(_latin1 0xDF6368 as char)));
hex(weight_string(cast(_latin1 0xDF6368 as char)))
0FEA0FEA0EE2
select hex(weight_string(cast(_latin1 0xDF6368 as char) as char(1)));
hex(weight_string(cast(_latin1 0xDF6368 as char) as char(1)))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char) as char(2)));
hex(weight_string(cast(_latin1 0xDF6368 as char) as char(2)))
0FEA0FEA0E60
select hex(weight_string(cast(_latin1 0xDF6368 as char) as char(3)));
hex(weight_string(cast(_latin1 0xDF6368 as char) as char(3)))
0FEA0FEA0EE2
select hex(weight_string(cast(_latin1 0xDF6368 as char) as char(4)));
hex(weight_string(cast(_latin1 0xDF6368 as char) as char(4)))
0FEA0FEA0EE20209
select hex(weight_string(cast(_latin1 0x6368DF as char), 1, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 1, 2, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x6368DF as char), 2, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 2, 2, 0xC0))
0EE2
select hex(weight_string(cast(_latin1 0x6368DF as char), 3, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 3, 2, 0xC0))
0EE202
select hex(weight_string(cast(_latin1 0x6368DF as char), 4, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 4, 2, 0xC0))
0EE20209
select hex(weight_string(cast(_latin1 0x6368DF as char),25, 2, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char),25, 2, 0xC0))
0EE20209020902090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0x6368DF as char), 1, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 1, 3, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x6368DF as char), 2, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 2, 3, 0xC0))
0EE2
select hex(weight_string(cast(_latin1 0x6368DF as char), 3, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 3, 3, 0xC0))
0EE20F
select hex(weight_string(cast(_latin1 0x6368DF as char), 4, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 4, 3, 0xC0))
0EE20FEA
select hex(weight_string(cast(_latin1 0x6368DF as char),25, 3, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char),25, 3, 0xC0))
0EE20FEA0FEA02090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0x6368DF as char), 1, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 1, 4, 0xC0))
0E
select hex(weight_string(cast(_latin1 0x6368DF as char), 2, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 2, 4, 0xC0))
0EE2
select hex(weight_string(cast(_latin1 0x6368DF as char), 3, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 3, 4, 0xC0))
0EE20F
select hex(weight_string(cast(_latin1 0x6368DF as char), 4, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char), 4, 4, 0xC0))
0EE20FEA
select hex(weight_string(cast(_latin1 0x6368DF as char),25, 4, 0xC0));
hex(weight_string(cast(_latin1 0x6368DF as char),25, 4, 0xC0))
0EE20FEA0FEA02090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 2,0xC0))
0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 2,0xC0))
0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 2,0xC0))
0FEA0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 2,0xC0))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char),25, 2,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char),25, 2,0xC0))
0FEA0FEA0E6002090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 3,0xC0))
0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 3,0xC0))
0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 3,0xC0))
0FEA0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 3,0xC0))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char),25, 3,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char),25, 3,0xC0))
0FEA0FEA0EE202090209020902090209020902090209020902
select hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 1, 4,0xC0))
0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 2, 4,0xC0))
0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 3, 4,0xC0))
0FEA0F
select hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char), 4, 4,0xC0))
0FEA0FEA
select hex(weight_string(cast(_latin1 0xDF6368 as char),25, 4,0xC0));
hex(weight_string(cast(_latin1 0xDF6368 as char),25, 4,0xC0))
0FEA0FEA0EE202090209020902090209020902090209020902
#
# Bug#33077 weight of supplementary characters is not 0xfffd
#
select hex(weight_string(_utf8mb4 0xF0908080 /* U+10000 */ collate utf8mb4_unicode_ci));
hex(weight_string(_utf8mb4 0xF0908080 /* U+10000 */ collate utf8mb4_unicode_ci))
FFFD
#
# Bug#53064 garbled data when using utf8_german2_ci collation
#
CREATE TABLE t1 (s1 VARCHAR(10) COLLATE utf8mb3_german2_ci);
Warnings:
Warning	3778	'utf8mb3_german2_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
INSERT INTO t1 VALUES ('a'),('ae'),('af');
SELECT s1,hex(s1),hex(weight_string(s1)) FROM t1 ORDER BY s1;
s1	hex(s1)	hex(weight_string(s1))
a	61	0E33
ae	6165	0E330E8B
af	6166	0E330EB9
DROP TABLE t1;
#
# WL#4013 Unicode german2 collation
#
SET collation_connection=utf8mb3_german2_ci;
Warnings:
Warning	3778	'utf8mb3_german2_ci' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead.
"BEGIN ctype_german.inc"
drop table if exists t1;
create table t1 as select repeat(' ', 64) as s1;
select collation(s1) from t1;
collation(s1)
utf8mb3_german2_ci
delete from t1;
INSERT INTO t1 VALUES ('ud'),('uf');
INSERT INTO t1 VALUES ('od'),('of');
INSERT INTO t1 VALUES ('e');
INSERT INTO t1 VALUES ('ad'),('af');
insert into t1 values ('a'),('ae'),(_latin1 0xE4);
insert into t1 values ('o'),('oe'),(_latin1 0xF6);
insert into t1 values ('s'),('ss'),(_latin1 0xDF);
insert into t1 values ('u'),('ue'),(_latin1 0xFC);
INSERT INTO t1 VALUES (_latin1 0xE6), (_latin1 0xC6);
INSERT INTO t1 VALUES (_latin1 0x9C), (_latin1 0x8C);
select s1, hex(s1) from t1 order by s1, binary s1;
s1	hex(s1)
a	61
ad	6164
ae	6165
Æ	C386
ä	C3A4
æ	C3A6
af	6166
e	65
o	6F
od	6F64
oe	6F65
ö	C3B6
Œ	C592
œ	C593
of	6F66
s	73
ss	7373
ß	C39F
u	75
ud	7564
ue	7565
ü	C3BC
uf	7566
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
select group_concat(s1 order by binary s1) from t1 group by s1;
group_concat(s1 order by binary s1)
a
ad
ae,Æ,ä,æ
af
e
o
od
oe,ö,Œ,œ
of
s
ss,ß
u
ud
ue,ü
uf
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
SELECT s1, hex(s1), hex(weight_string(s1)) FROM t1 ORDER BY s1, BINARY(s1);
s1	hex(s1)	hex(weight_string(s1))
a	61	0E33
ad	6164	0E330E6D
ae	6165	0E330E8B
Æ	C386	0E330E8B
ä	C3A4	0E330E8B
æ	C3A6	0E330E8B
af	6166	0E330EB9
e	65	0E8B
o	6F	0F82
od	6F64	0F820E6D
oe	6F65	0F820E8B
ö	C3B6	0F820E8B
Œ	C592	0F820E8B
œ	C593	0F820E8B
of	6F66	0F820EB9
s	73	0FEA
ss	7373	0FEA0FEA
ß	C39F	0FEA0FEA
u	75	101F
ud	7564	101F0E6D
ue	7565	101F0E8B
ü	C3BC	101F0E8B
uf	7566	101F0EB9
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
SELECT s1, hex(s1) FROM t1 WHERE s1='ae' ORDER BY s1, BINARY(s1);
s1	hex(s1)
ae	6165
Æ	C386
ä	C3A4
æ	C3A6
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
drop table t1;
"END ctype_german.inc"
#
# WL#2673 Unicode Collation Algorithm new version
#
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_520_ci;
CREATE TABLE t1 AS SELECT repeat('a', 10) as c LIMIT 0;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES (_utf32 0x0180),(_utf32 0x023A);
INSERT INTO t1 VALUES (_utf32 0x023B),(_utf32 0x023C);
INSERT INTO t1 VALUES (_utf32 0x023D),(_utf32 0x023E);
INSERT INTO t1 VALUES (_utf32 0x0241),(_utf32 0x0242);
INSERT INTO t1 VALUES (_utf32 0x0243),(_utf32 0x0244);
INSERT INTO t1 VALUES (_utf32 0x0245),(_utf32 0x0246);
INSERT INTO t1 VALUES (_utf32 0x0247),(_utf32 0x0248);
INSERT INTO t1 VALUES (_utf32 0x0249),(_utf32 0x024A);
INSERT INTO t1 VALUES (_utf32 0x024B),(_utf32 0x024C);
INSERT INTO t1 VALUES (_utf32 0x024D),(_utf32 0x024E);
INSERT INTO t1 VALUES (_utf32 0x024F),(_utf32 0x026B);
INSERT INTO t1 VALUES (_utf32 0x027D),(_utf32 0x0289);
INSERT INTO t1 VALUES (_utf32 0x028C);
INSERT INTO t1 VALUES (_utf32 0x037B), (_utf32 0x037C);
INSERT INTO t1 VALUES (_utf32 0x037D), (_utf32 0x03FD);
INSERT INTO t1 VALUES (_utf32 0x03FE), (_utf32 0x03FF);
INSERT INTO t1 VALUES (_utf32 0x04C0), (_utf32 0x04CF);
INSERT INTO t1 VALUES (_utf32 0x04F6), (_utf32 0x04F7);
INSERT INTO t1 VALUES (_utf32 0x04FA), (_utf32 0x04FB);
INSERT INTO t1 VALUES (_utf32 0x04FC), (_utf32 0x04FD);
INSERT INTO t1 VALUES (_utf32 0x04FE), (_utf32 0x04FF);
INSERT INTO t1 VALUES (_utf32 0x0510), (_utf32 0x0511);
INSERT INTO t1 VALUES (_utf32 0x0512), (_utf32 0x0513);
INSERT INTO t1 VALUES (_utf32 0x10A0), (_utf32 0x10A1);
INSERT INTO t1 VALUES (_utf32 0x10A2), (_utf32 0x10A3);
INSERT INTO t1 VALUES (_utf32 0x10A4), (_utf32 0x10A5);
INSERT INTO t1 VALUES (_utf32 0x10A6), (_utf32 0x10A7);
INSERT INTO t1 VALUES (_utf32 0x2D00), (_utf32 0x2D01);
INSERT INTO t1 VALUES (_utf32 0x2D02), (_utf32 0x2D03);
INSERT INTO t1 VALUES (_utf32 0x2D04), (_utf32 0x2D05);
INSERT INTO t1 VALUES (_utf32 0x2D06), (_utf32 0x2D07);
INSERT INTO t1 VALUES (_utf32 0x1D7D);
INSERT INTO t1 VALUES (_utf32 0x2132),(_utf32 0x214E);
INSERT INTO t1 VALUES (_utf32 0x2183),(_utf32 0x2184);
INSERT INTO t1 VALUES (_utf32 0x2C80), (_utf32 0x2C81);
INSERT INTO t1 VALUES (_utf32 0x2C82), (_utf32 0x2C83);
INSERT INTO t1 VALUES (_utf32 0x2C84), (_utf32 0x2C85);
INSERT INTO t1 VALUES (_utf32 0x2C86), (_utf32 0x2C87);
INSERT INTO t1 VALUES (_utf32 0x2C88), (_utf32 0x2C89);
INSERT INTO t1 VALUES (_utf32 0x2C8A), (_utf32 0x2C8B);
INSERT INTO t1 VALUES (_utf32 0x2C8C), (_utf32 0x2C8D);
INSERT INTO t1 VALUES (_utf32 0x2C8E), (_utf32 0x2C8F);
INSERT INTO t1 VALUES (_utf32 0x2C60), (_utf32 0x2C61);
INSERT INTO t1 VALUES (_utf32 0x2C62), (_utf32 0x2C63);
INSERT INTO t1 VALUES (_utf32 0x2C64), (_utf32 0x2C65);
INSERT INTO t1 VALUES (_utf32 0x2C66), (_utf32 0x2C67);
INSERT INTO t1 VALUES (_utf32 0x2C68), (_utf32 0x2C69);
INSERT INTO t1 VALUES (_utf32 0x2C6A), (_utf32 0x2C6B);
INSERT INTO t1 VALUES (_utf32 0x2C6C), (_utf32 0x2C75);
INSERT INTO t1 VALUES (_utf32 0x2C76);
INSERT INTO t1 VALUES (_utf32 0x2C00), (_utf32 0x2C01);
INSERT INTO t1 VALUES (_utf32 0x2C02), (_utf32 0x2C03);
INSERT INTO t1 VALUES (_utf32 0x2C04), (_utf32 0x2C05);
INSERT INTO t1 VALUES (_utf32 0x2C06), (_utf32 0x2C07);
INSERT INTO t1 VALUES (_utf32 0x2C30), (_utf32 0x2C31);
INSERT INTO t1 VALUES (_utf32 0x2C32), (_utf32 0x2C33);
INSERT INTO t1 VALUES (_utf32 0x2C34), (_utf32 0x2C35);
INSERT INTO t1 VALUES (_utf32 0x2C36), (_utf32 0x2C37);
INSERT INTO t1 VALUES (_utf32 0x10400), (_utf32 0x10401);
INSERT INTO t1 VALUES (_utf32 0x10402), (_utf32 0x10403);
INSERT INTO t1 VALUES (_utf32 0x10404), (_utf32 0x10405);
INSERT INTO t1 VALUES (_utf32 0x10406), (_utf32 0x10407);
INSERT INTO t1 VALUES (_utf32 0x10428), (_utf32 0x10429);
INSERT INTO t1 VALUES (_utf32 0x1042A), (_utf32 0x1042B);
INSERT INTO t1 VALUES (_utf32 0x1042C), (_utf32 0x1042D);
INSERT INTO t1 VALUES (_utf32 0x1042E), (_utf32 0x1042F);
INSERT INTO t1 VALUES (_utf32 0x0370);
INSERT INTO t1 VALUES (_utf32 0x0371);
INSERT INTO t1 VALUES (_utf32 0x0372);
INSERT INTO t1 VALUES (_utf32 0x0373);
INSERT INTO t1 VALUES (_utf32 0x0514);
INSERT INTO t1 VALUES (_utf32 0x0515);
INSERT INTO t1 VALUES (_utf32 0x0516);
INSERT INTO t1 VALUES (_utf32 0x0517);
INSERT INTO t1 VALUES (_utf32 0xA640);
INSERT INTO t1 VALUES (_utf32 0xA641);
INSERT INTO t1 VALUES (_utf32 0xA642);
INSERT INTO t1 VALUES (_utf32 0xA643);
INSERT INTO t1 VALUES (_utf32 0xA722);
INSERT INTO t1 VALUES (_utf32 0xA723);
INSERT INTO t1 VALUES (_utf32 0xA724);
INSERT INTO t1 VALUES (_utf32 0xA725);
INSERT INTO t1 VALUES (_utf32 0xA726);
INSERT INTO t1 VALUES (_utf32 0xA727);
INSERT INTO t1 VALUES (_utf32 0xA728);
INSERT INTO t1 VALUES (_utf32 0xA729);
INSERT INTO t1 VALUES (_utf32 0xA72A);
INSERT INTO t1 VALUES (_utf32 0xA72B);
INSERT INTO t1 VALUES (_utf32 0x2CEB);
INSERT INTO t1 VALUES (_utf32 0x2CEC);
INSERT INTO t1 VALUES (_utf32 0x2CED);
INSERT INTO t1 VALUES (_utf32 0x2CEE);
SELECT hex(c), hex(lower(c)), hex(upper(c)), hex(weight_string(c)), c
FROM t1 ORDER BY c, BINARY c;
hex(c)	hex(lower(c))	hex(upper(c))	hex(weight_string(c))	c
C8BA		C8BA	1214	Ⱥ
E2B1A5	E2B1A5	C8BA	1214	ⱥ
C680	C680	C983	122D	ƀ
C983	C680	C983	122D	Ƀ
C8BB	C8BC	C8BB	1242	Ȼ
C8BC	C8BC	C8BB	1242	ȼ
E28683	E28684	E28683	124E	Ↄ
E28684	E28684	E28683	124E	ↄ
C986	C987	C986	1270	Ɇ
C987	C987	C986	1270	ɇ
E284B2	E2858E	E284B2	12AE	Ⅎ
E2858E	E2858E	E284B2	12AE	ⅎ
E2B1A7	E2B1A8	E2B1A7	12E3	Ⱨ
E2B1A8	E2B1A8	E2B1A7	12E3	ⱨ
E2B1B5	E2B1B6	E2B1B5	12E4	Ⱶ
E2B1B6	E2B1B6	E2B1B5	12E4	ⱶ
EA9CA6	EA9CA7	EA9CA6	12E5	Ꜧ
EA9CA7	EA9CA7	EA9CA6	12E5	ꜧ
C988	C989	C988	130E	Ɉ
C989	C989	C988	130E	ɉ
E2B1A9	E2B1AA	E2B1A9	1328	Ⱪ
E2B1AA	E2B1AA	E2B1A9	1328	ⱪ
C8BD	C69A	C8BD	133B	Ƚ
E2B1A0	E2B1A1	E2B1A0	133F	Ⱡ
E2B1A1	E2B1A1	E2B1A0	133F	ⱡ
C9AB	C9AB		1340	ɫ
E2B1A2	C9AB	E2B1A2	1340	Ɫ
E1B5BD	E1B5BD	E2B1A3	13B8	ᵽ
E2B1A3	E1B5BD	E2B1A3	13B8	Ᵽ
C98A	C98B	C98A	13D2	Ɋ
C98B	C98B	C98A	13D2	ɋ
C98C	C98D	C98C	13E4	Ɍ
C98D	C98D	C98C	13E4	ɍ
C9BD	C9BD		13FC	ɽ
E2B1A4	C9BD	E2B1A4	13FC	Ɽ
EA9CA8	EA9CA9	EA9CA8	143314AD	Ꜩ
EA9CA9	EA9CA9	EA9CA8	143314AD	ꜩ
C8BE		C8BE	143C	Ⱦ
E2B1A6	E2B1A6	C8BE	143C	ⱦ
C984	CA89	C984	145B	Ʉ
CA89	CA89	C984	145B	ʉ
C985	CA8C	C985	1489	Ʌ
CA8C	CA8C	C985	1489	ʌ
C98E	C98F	C98E	14A4	Ɏ
C98F	C98F	C98E	14A4	ɏ
E2B1AB	E2B1AC	E2B1AB	14C8	Ⱬ
E2B1AC	E2B1AC	E2B1AB	14C8	ⱬ
EA9CAA	EA9CAB	EA9CAA	14F3	Ꜫ
EA9CAB	EA9CAB	EA9CAA	14F3	ꜫ
C981	C982	C981	1506	Ɂ
C982	C982	C981	1506	ɂ
EA9CA2	EA9CA3	EA9CA2	150E	Ꜣ
EA9CA3	EA9CA3	EA9CA2	150E	ꜣ
EA9CA4	EA9CA5	EA9CA4	1518	Ꜥ
EA9CA5	EA9CA5	EA9CA4	1518	ꜥ
CDB0	CDB1	CDB0	154F	Ͱ
CDB1	CDB1	CDB0	154F	ͱ
CDBC	CDBC	CFBE	1564	ͼ
CFBE	CDBC	CFBE	1564	Ͼ
CDBB	CDBB	CFBD	1565	ͻ
CFBD	CDBB	CFBD	1565	Ͻ
CDBD	CDBD	CFBF	1566	ͽ
CFBF	CDBD	CFBF	1566	Ͽ
CDB2	CDB3	CDB2	156F	Ͳ
CDB3	CDB3	CDB2	156F	ͳ
E2B280	E2B281	E2B280	1571	Ⲁ
E2B281	E2B281	E2B280	1571	ⲁ
E2B282	E2B283	E2B282	1572	Ⲃ
E2B283	E2B283	E2B282	1572	ⲃ
E2B284	E2B285	E2B284	1573	Ⲅ
E2B285	E2B285	E2B284	1573	ⲅ
E2B286	E2B287	E2B286	1574	Ⲇ
E2B287	E2B287	E2B286	1574	ⲇ
E2B288	E2B289	E2B288	1575	Ⲉ
E2B289	E2B289	E2B288	1575	ⲉ
E2B28A	E2B28B	E2B28A	1577	Ⲋ
E2B28B	E2B28B	E2B28A	1577	ⲋ
E2B28C	E2B28D	E2B28C	1578	Ⲍ
E2B28D	E2B28D	E2B28C	1578	ⲍ
E2B28E	E2B28F	E2B28E	1579	Ⲏ
E2B28F	E2B28F	E2B28E	1579	ⲏ
E2B3AB	E2B3AC	E2B3AB	1591	Ⳬ
E2B3AC	E2B3AC	E2B3AB	1591	ⳬ
E2B3AD	E2B3AE	E2B3AD	15A0	Ⳮ
E2B3AE	E2B3AE	E2B3AD	15A0	ⳮ
D3BA	D3BB	D3BA	15D4	Ӻ
D3BB	D3BB	D3BA	15D4	ӻ
D3B6	D3B7	D3B6	15DC	Ӷ
D3B7	D3B7	D3B6	15DC	ӷ
EA9980	EA9981	EA9980	1611	Ꙁ
EA9981	EA9981	EA9980	1611	ꙁ
D490	D491	D490	1613	Ԑ
D491	D491	D490	1613	ԑ
EA9982	EA9983	EA9982	1618	Ꙃ
EA9983	EA9983	EA9982	1618	ꙃ
D492	D493	D492	1666	Ԓ
D493	D493	D492	1666	ԓ
D494	D495	D494	166E	Ԕ
D495	D495	D494	166E	ԕ
D496	D497	D496	16B7	Ԗ
D497	D497	D496	16B7	ԗ
D3BC	D3BD	D3BC	16F9	Ӽ
D3BD	D3BD	D3BC	16F9	ӽ
D3BE	D3BF	D3BE	16FD	Ӿ
D3BF	D3BF	D3BE	16FD	ӿ
D380	D38F	D380	17B1	Ӏ
D38F	D38F	D380	17B1	ӏ
E2B080	E2B0B0	E2B080	17B5	Ⰰ
E2B0B0	E2B0B0	E2B080	17B5	ⰰ
E2B081	E2B0B1	E2B081	17B6	Ⰱ
E2B0B1	E2B0B1	E2B081	17B6	ⰱ
E2B082	E2B0B2	E2B082	17B7	Ⰲ
E2B0B2	E2B0B2	E2B082	17B7	ⰲ
E2B083	E2B0B3	E2B083	17B8	Ⰳ
E2B0B3	E2B0B3	E2B083	17B8	ⰳ
E2B084	E2B0B4	E2B084	17B9	Ⰴ
E2B0B4	E2B0B4	E2B084	17B9	ⰴ
E2B085	E2B0B5	E2B085	17BA	Ⰵ
E2B0B5	E2B0B5	E2B085	17BA	ⰵ
E2B086	E2B0B6	E2B086	17BB	Ⰶ
E2B0B6	E2B0B6	E2B086	17BB	ⰶ
E2B087	E2B0B7	E2B087	17BC	Ⰷ
E2B0B7	E2B0B7	E2B087	17BC	ⰷ
E182A0	E2B480	E182A0	17E5	Ⴀ
E2B480	E2B480	E182A0	17E5	ⴀ
E182A1	E2B481	E182A1	17E7	Ⴁ
E2B481	E2B481	E182A1	17E7	ⴁ
E182A2	E2B482	E182A2	17E9	Ⴂ
E2B482	E2B482	E182A2	17E9	ⴂ
E182A3	E2B483	E182A3	17EB	Ⴃ
E2B483	E2B483	E182A3	17EB	ⴃ
E182A4	E2B484	E182A4	17ED	Ⴄ
E2B484	E2B484	E182A4	17ED	ⴄ
E182A5	E2B485	E182A5	17EF	Ⴅ
E2B485	E2B485	E182A5	17EF	ⴅ
E182A6	E2B486	E182A6	17F1	Ⴆ
E2B486	E2B486	E182A6	17F1	ⴆ
E182A7	E2B487	E182A7	17F5	Ⴇ
E2B487	E2B487	E182A7	17F5	ⴇ
F0909080	F09090A8	F0909080	30D2	𐐀
F09090A8	F09090A8	F0909080	30D2	𐐨
F0909081	F09090A9	F0909081	30D3	𐐁
F09090A9	F09090A9	F0909081	30D3	𐐩
F0909082	F09090AA	F0909082	30D4	𐐂
F09090AA	F09090AA	F0909082	30D4	𐐪
F0909083	F09090AB	F0909083	30D5	𐐃
F09090AB	F09090AB	F0909083	30D5	𐐫
F0909084	F09090AC	F0909084	30D6	𐐄
F09090AC	F09090AC	F0909084	30D6	𐐬
F0909085	F09090AD	F0909085	30D7	𐐅
F09090AD	F09090AD	F0909085	30D7	𐐭
F0909086	F09090AE	F0909086	30D8	𐐆
F09090AE	F09090AE	F0909086	30D8	𐐮
F0909087	F09090AF	F0909087	30D9	𐐇
F09090AF	F09090AF	F0909087	30D9	𐐯
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
INSERT INTO t1 VALUES ('a');
INSERT INTO t1 VALUES (concat(_utf32 0x61, _utf32 0xFFFF));
INSERT INTO t1 VALUES (concat(_utf32 0x61, _utf32 0x10FFFF));
INSERT INTO t1 VALUES (concat(_utf32 0x61, _utf32 0x10400));
SELECT hex(c), hex(weight_string(c)) FROM t1 WHERE c LIKE 'a%' ORDER BY c;
hex(c)	hex(weight_string(c))
61	120F
61F0909080	120F30D2
61EFBFBF	120FFBC1FFFF
61F48FBFBF	120FFBE1FFFF
SELECT hex(c), hex(weight_string(c)), c FROM t1 WHERE c LIKE _utf32 0x10400 ORDER BY c, BINARY c;
hex(c)	hex(weight_string(c))	c
F0909080	30D2	𐐀
F09090A8	30D2	𐐨
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
SELECT hex(c), hex(weight_string(c)), c FROM t1 WHERE c LIKE _utf32 0x10428 ORDER BY c, BINARY c;
hex(c)	hex(weight_string(c))	c
F0909080	30D2	𐐀
F09090A8	30D2	𐐨
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
ALTER TABLE t1 ADD KEY(c);
EXPLAIN SELECT hex(c) FROM t1 WHERE c LIKE 'a%' ORDER BY c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	c	c	43	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select hex(`test`.`t1`.`c`) AS `hex(c)` from `test`.`t1` where (`test`.`t1`.`c` like 'a%') order by `test`.`t1`.`c`
SELECT hex(c), hex(weight_string(c)) FROM t1 WHERE c LIKE 'a%' ORDER BY c;
hex(c)	hex(weight_string(c))
61	120F
61F0909080	120F30D2
61EFBFBF	120FFBC1FFFF
61F48FBFBF	120FFBE1FFFF
SELECT hex(c), hex(weight_string(c)), c FROM t1 WHERE c LIKE _utf32 0x10400 ORDER BY c, BINARY c;
hex(c)	hex(weight_string(c))	c
F0909080	30D2	𐐀
F09090A8	30D2	𐐨
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
SELECT hex(c), hex(weight_string(c)), c FROM t1 WHERE c LIKE _utf32 0x10428 ORDER BY c, BINARY c;
hex(c)	hex(weight_string(c))	c
F0909080	30D2	𐐀
F09090A8	30D2	𐐨
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
DROP TABLE t1;
#
# Bug #16204175 : COLLATION NAME MISSING FROM LOG MESSAGES
#                 ABOUT LDML DEFINITION PROBLEMS
#
#
# Restart the server with a collation with invalid grammar rules.
# restart:--character-sets-dir=MYSQL_TEST_DIR/std_data/bug16204175/
# Check whether the new collation is loaded after restart
SHOW COLLATION LIKE 'utf8mb3_test';
Collation	Charset	Id	Default	Compiled	Sortlen	Pad_attribute
utf8mb3_test	utf8mb3	999			8	PAD SPACE
# Create a table with the unknown collation.
CREATE TABLE t1(id INT PRIMARY KEY, c CHAR(1) COLLATE utf8mb3_test);
ERROR HY000: Unknown collation: 'utf8mb3_test'
# Below error is caused as a result of this test. The error message
# contains collation name which is added as a part of the fix.
call mtr.add_suppression("Shift expected at '' for COLLATION : utf8mb3_test");
#
# Restart server with original character sets dir:
# restart:--character-sets-dir=MYSQL_CHARSETSDIR
#
# End of 5.6 tests
#
SET NAMES utf8mb4;
CREATE TABLE t1 (
f1 CHAR(4) NOT NULL
);
INSERT INTO t1 VALUES ('é');
INSERT INTO t1 VALUES (_utf16 0x00650301);
SELECT DISTINCT f1 FROM t1;
f1
é
SELECT SQL_BIG_RESULT DISTINCT f1 FROM t1;
f1
é
DROP TABLE t1;
SET NAMES DEFAULT;
#
# Bug #27578340: INCONSISTENT RESULTS FOR SELECTS FROM TABLE WITH CHAR(N) COLUMN
#                AND NO_PAD COLLATION
#
CREATE TABLE t1 (
f1 CHAR(20) COLLATE utf8mb4_0900_ai_ci  # A NO PAD collation.
);
INSERT INTO t1 VALUES ('ABC  ');
INSERT INTO t1 VALUES ('XYZ');
INSERT INTO t1 VALUES ('XYZ ');
INSERT INTO t1 VALUES ('ABC ');
#
# We should get the same results with and without an index.
# The former should match both XYZ values, the latter should match nothing.
# This is because CHAR conceptually never stores trailing spaces (in MySQL).
#
SELECT CONCAT(f1, '|') FROM t1 WHERE f1 = 'XYZ';
CONCAT(f1, '|')
XYZ|
XYZ|
SELECT CONCAT(f1, '|') FROM t1 WHERE f1 = 'XYZ ';
CONCAT(f1, '|')
CREATE INDEX f1_index ON t1 ( f1 );
SELECT CONCAT(f1, '|') FROM t1 WHERE f1 = 'XYZ';
CONCAT(f1, '|')
XYZ|
XYZ|
SELECT CONCAT(f1, '|') FROM t1 WHERE f1 = 'XYZ ';
CONCAT(f1, '|')
DROP TABLE t1;
#
# Bug #28691605 ASSERTION `NUM_CODEPOINTS >= SCANNER.GET_CHAR_INDEX()' FAILED.
#
CREATE TABLE t1(c1 float);
INSERT INTO t1 VALUES
(-9999999999999),
(-99999999999999),
(-999999999999999),
(-9999999999999999);
SELECT GROUP_CONCAT(c1) FROM t1 GROUP BY c1 COLLATE utf8mb3_icelandic_ci;
GROUP_CONCAT(c1)
-1e13
-1e14
-1e15
-1e16
DROP TABLE t1;
CREATE TABLE t1double(c1 double);
INSERT INTO t1double values(0.00000000000002123456789123456789);
SELECT c1 FROM t1double;
c1
0.000000000000021234567891234568
SELECT CONCAT(c1) FROM t1double;
CONCAT(c1)
2.1234567891234568e-14
SELECT CAST(CONCAT(c1) AS DOUBLE) FROM t1double;
CAST(CONCAT(c1) AS DOUBLE)
0.000000000000021234567891234568
SELECT GROUP_CONCAT(c1) FROM t1double GROUP BY c1 COLLATE utf8mb3_icelandic_ci;
GROUP_CONCAT(c1)
2.1234567891234568e-14
DROP TABLE t1double;
#
# Bug #32385934 ASSERTION `NUM_CODEPOINTS >= SCANNER.GET_CHAR_INDEX()'
#               FAILED|CTYPE-UCA.CC
#
CREATE TABLE t1 (
col_real_key double DEFAULT NULL,
col_bigint_key bigint DEFAULT NULL
);
INSERT INTO t1 VALUES (-20162.341,8262411111801246601);
SELECT alias1.col_real_key / alias1.col_bigint_key AS field1,
alias1.col_bigint_key AS field2
FROM t1 AS alias1
ORDER BY LEAST(field2, field1 COLLATE utf8mb4_croatian_ci);
field1	field2
-0.000000000000002440249066183843	8262411111801246601
DROP TABLE t1;
