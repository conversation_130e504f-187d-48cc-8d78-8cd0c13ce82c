include/group_replication.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection server1]
#
# 1. Create a table on server 1 and replicate
# Also add a value in the table without logging to the binlog
#
[connection server1]
CREATE TABLE t1 (a INT PRIMARY KEY);
SET SESSION sql_log_bin= 0;
INSERT INTO t1 VALUES (1);
SET SESSION sql_log_bin= 1;
include/rpl/sync.inc
#
# 2. Insert a value on server 2 that will clash with data on server1
#
[connection server2]
INSERT INTO t1 VALUES (1);
#
# 3. Check that server1 applier fails and the plugin goes to error mode.
#
[connection server1]
# Expect ERROR state.
include/gr_wait_for_member_state.inc
include/rpl/gr_wait_for_number_of_members.inc
[connection server2]
# Wait for one member on server 2
include/rpl/gr_wait_for_number_of_members.inc
#
# 4. Remove the value, restart the plugin and do a cleanup
#
[connection server1]
include/stop_group_replication.inc
SET SESSION sql_log_bin= 0;
DELETE FROM t1 WHERE a=1;
SET SESSION sql_log_bin= 1;
include/start_group_replication.inc
DROP TABLE t1;
SET SESSION sql_log_bin= 0;
call mtr.add_suppression("Replica SQL for channel 'group_replication_applier': Could not execute Write_rows event on table test.t1*.*");
call mtr.add_suppression("The applier thread execution was aborted. Unable to process more transactions, this member will now leave the group.");
call mtr.add_suppression("Fatal error during execution on the Applier process of Group Replication. The server will now leave the group.");
call mtr.add_suppression("The server was automatically set into read only mode after an error was detected.");
call mtr.add_suppression("The server was automatically set into offline mode after an error was detected.");
call mtr.add_suppression("Replica SQL for channel 'group_replication_applier': Worker .* failed executing transaction .*");
call mtr.add_suppression("Replica SQL for channel 'group_replication_applier': ... The replica coordinator and worker threads are stopped,.*");
SET SESSION sql_log_bin= 1;
include/group_replication_end.inc
