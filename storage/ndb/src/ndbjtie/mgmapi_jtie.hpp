/*
 Copyright (c) 2010, 2025, Oracle and/or its affiliates.

 This program is free software; you can redistribute it and/or modify
 it under the terms of the GNU General Public License, version 2.0,
 as published by the Free Software Foundation.

 This program is designed to work with certain software (including
 but not limited to OpenSSL) that is licensed under separate terms,
 as designated in a particular file or component or in included license
 documentation.  The authors of MySQL hereby grant you an additional
 permission to link the program and your derivative works with the
 separately licensed software that they have either included with
 the program or referenced in the documentation.

 This program is distributed in the hope that it will be useful,
 but WITHOUT ANY WARRANTY; without even the implied warranty of
 MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 GNU General Public License, version 2.0, for more details.

 You should have received a copy of the GNU General Public License
 along with this program; if not, write to the Free Software
 Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
*/
/*
 * mgmapi_jtie.hpp
 */

#ifndef mgmapi_jtie_hpp
#define mgmapi_jtie_hpp

// API to implement against
#include "mgmapi.h"
// #include "mgmapi_config_parameters.h"
// #include "mgmapi_debug.h"
// #include "mgmapi_error.h"
// #include "ndb_logevent.h"
// #include "ndbd_exit_codes.h"

// libraries
#include "helpers.hpp"
#include "jtie.hpp"
#include "ndbjtie_defs.hpp"

// ---------------------------------------------------------------------------
// MGMAPI JTie Type Definitions
// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------
// MGMAPI JTie Function Stubs
// ---------------------------------------------------------------------------

// The API stub functions in this file have mangled names that adhere
// to the JVM specification.  It is not necessary to include the
// function prototypes generated by the javah tool from the Java source,
// if they are declared to receive "C" linkage here.
extern "C" {

// A javah bug in JDK 5
//   http://forums.sun.com/thread.jspa?threadID=5115982&tstart=1499
// generates a wrong name for native methods in static nested classes:
//
// JDK 6 has this bug only partially fixed (nested classes as invocation
// targets but not as parameters).
//
// Outer$Inner is to be mangled as unicode escape: Outer_00024Inner, see:
//   http://java.sun.com/javase/6/docs/technotes/guides/jni/spec/design.html#wp615]Resolving%20Native%20Method%20Names

// ---------------------------------------------------------------------------

// ---------------------------------------------------------------------------

}  // extern "C"

#endif  // mgmapi_jtie_hpp
