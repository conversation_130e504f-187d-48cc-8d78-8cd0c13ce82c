<?xml version="1.0" encoding="UTF-8"?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at
 
 http://www.apache.org/licenses/LICENSE-2.0
 
 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.   
-->
<persistence xmlns="http://java.sun.com/xml/ns/persistence"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    version="1.0">

    <!--
        Need to enumerate each persistent class first in the persistence.xml
        See: http://issues.apache.org/jira/browse/OPENJPA-78
    -->
    <persistence-unit name="none" transaction-type="RESOURCE_LOCAL">
        <class>com.mysql.cluster.crund.A</class>
        <class>com.mysql.cluster.crund.B</class>
    </persistence-unit>

    <!--
        A persistence unit is a set of listed persistent entities as well
        the configuration of an EntityManagerFactory.
    -->
    <persistence-unit name="crundjpa" transaction-type="RESOURCE_LOCAL">
        <!--
            The default provider can be OpenJPA, or some other product.
            This element is optional if OpenJPA is the only JPA provider
            in the current classloading environment, but can be specified
            in cases where there are multiple JPA implementations available.
        -->

        <!--
        <provider>
            org.apache.openjpa.persistence.PersistenceProviderImpl
        </provider>
        -->
        <!--
        <provider>
            org.eclipse.persistence.jpa.PersistenceProvider
        </provider>
        -->
        <!--
        <provider>
            org.hibernate.ejb.HibernatePersistence
        </provider> 
        -->

        <!-- We must enumerate each entity in the persistence unit -->
        <class>com.mysql.cluster.crund.A</class>
        <class>com.mysql.cluster.crund.B</class>

        <properties>
            <!-- OpenJPA properties if not provided as System properties -->
            <!--
            <property name="openjpa.ConnectionURL" 
                value="***************************************"/>
            <property name="openjpa.ConnectionDriverName" 
                value="org.apache.derby.jdbc.EmbeddedDriver"/>
            <property name="openjpa.ConnectionUserName" 
                value="user"/>
            <property name="openjpa.ConnectionPassword" 
                value="secret"/>
            <property name="openjpa.Log" value="DefaultLevel=WARN, Tool=INFO"/>
            -->
        </properties>

    </persistence-unit>
</persistence>
