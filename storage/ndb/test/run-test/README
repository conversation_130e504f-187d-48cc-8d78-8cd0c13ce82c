# Copyright (c) 2009, 2024, Oracle and/or its affiliates.
#
#  This program is free software; you can redistribute it and/or modify
#  it under the terms of the GNU General Public License, version 2.0,
#  as published by the Free Software Foundation.
#
#  This program is designed to work with certain software (including
#  but not limited to OpenSSL) that is licensed under separate terms,
#  as designated in a particular file or component or in included license
#  documentation.  The authors of MySQL hereby grant you an additional
#  permission to link the program and your derivative works with the
#  separately licensed software that they have either included with
#  the program or referenced in the documentation.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU General Public License, version 2.0, for more details.
#
#  You should have received a copy of the GNU General Public License
#  along with this program; if not, write to the Free Software
#  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

run-test/README

This document describes how atrt works and how to use it.

atrt is a test program driver.
atrt supports fully distributed test and utilizes ndb_cpcd.

===================================================
/**
  * Pseudo code for main atrt algorithm
  */

  read config file (default d.txt)
  contact each ndb_cpcd

  read all test cases

  start cluster
  if cluster start has failures do
    report overall critical failure
    attempt to clean up any left over process
    exit
  end if

  for each test case do
    run test case
    compute overall return code
    write result to report file
  done processing tests


/**
 * End of pseudo code for main atrt algorithm
 */



/**
  * Pseudo code for atrt test run algorithm
  */
  while test_runs < total_test_run_attemps do
    if test_forces_restart or previous_retry_failed
      stop all processes
      deploy all configurations
      start all processes
    end if

    if cluster status not ok
      attempt new retry
    end if
    run test and wait for test to finish
    gather all log results

  done while retries

  rename result

  stop_cluster = (test status != ok) || cluster_configuration_changed
  if (stop_cluster)
  if (test failed or cluster configuration changed)
    if (stop_all_processes fails)
      set test result as critical failure
    end if
    if (start_all_processes fails)
      set test result as critical failure
    end if
  end if
  check cluster status

/**
  * End of pseudo code for atrt test run algorithm
  */

===================================================

