# MySQL Rest Service schema created.
# MySQL Rest Service, server accounts configured.
# DB `func_schema` - created


## I. Verify error messages while providing faulty configuration to rest function
#
# 1. POST request is now supported by object/procedures (but rejected when no payload)
# 2. DELETE request is not supported by object/procedures
# 3. PUT request is rejected when there is not payload proveided
# 4. PUT request is rejected when there is invalid payload proveided
# 5. PUT request is rejected when there is invalid parameter proveided
# 6. GET request is rejected when there is invalid parameter proveided
#
## II. Verify handling of function generating results as media.
#
# 1. GET request to function generating binary response, verify the response
# 2. PUT request to function generating binary response, verify the response
# 3. GET request to function generating image response, verify the response
# 4. PUT request to function generating image response, verify the response
#
# Registred SERVICE at path: /svc
# Registred DB_SCHEMA at path: /svc/func
# Registred DB_OBJECT at path: /svc/func/func_media
# Registred DB_OBJECT at path: /svc/func/func_media_auto
# Registred DB_OBJECT at path: /svc/func/move_char
# Registred DB_OBJECT at path: /svc/func/move_date
# Registred DB_OBJECT at path: /svc/func/move_year
# Registred DB_OBJECT at path: /svc/func/move_time
# Registred DB_OBJECT at path: /svc/func/move_bit
# Registred DB_OBJECT at path: /svc/func/move_tinyint1
# Registred DB_OBJECT at path: /svc/func/move_tinyint8
# Registred DB_OBJECT at path: /svc/func/move_decimal
# Registred DB_OBJECT at path: /svc/func/move_float
# Registred DB_OBJECT at path: /svc/func/move_double
# Registred DB_OBJECT at path: /svc/func/move_json
# Registred DB_OBJECT at path: /svc/func/move_geo
# Registred DB_OBJECT at path: /svc/func/move_line

#
# I.1
POST /svc/func/func_media
OK

#
# I.2
DELETE /svc/func/func_media
OK

#
# I.3
PUT /svc/func/func_media
OK

#
# I.4
PUT /svc/func/func_media (payload-size:2)
OK

#
# I.5
PUT /svc/func/func_media (payload-size:25)
OK

#
# I.6
GET /svc/func/func_media?not-existing-param=10
OK

#
# II.1
GET /svc/func/func_media
OK

#
# II.2
PUT /svc/func/func_media (payload-size:2)
OK

#
# II.3
GET /svc/func/func_media_auto
OK

#
# II.4
PUT /svc/func/func_media_auto (payload-size:2)
OK

#
# III.1
PUT /svc/func/move_char (payload-size:8)
10 appended
OK

#
# III.2
DISABLED

#
# III.3
DISABLED

#
# III.4
DISABLED

#
# III.5
PUT /svc/func/move_bit (payload-size:7)
TRUE
OK

#
# III.6
PUT /svc/func/move_tinyint1 (payload-size:7)
2
OK

#
# III.7
PUT /svc/func/move_tinyint8 (payload-size:8)
11
OK

#
# III.8
PUT /svc/func/move_decimal (payload-size:8)
11
OK

#
# III.9
PUT /svc/func/move_float (payload-size:8)
11
OK

#
# III.10
PUT /svc/func/move_double (payload-size:8)
11
OK

#
# III.10
PUT /svc/func/move_json (payload-size:9)
100
OK

#
# III.11
PUT /svc/func/move_json (payload-size:40)
["aaaa", 20, 30, {"field1": "value1"}]
OK
DROP SCHEMA func_schema;
DROP SCHEMA mysql_rest_service_metadata;
DROP ROLE mysql_rest_service_admin;
DROP ROLE mysql_rest_service_schema_admin;
DROP ROLE mysql_rest_service_meta_provider;
DROP ROLE mysql_rest_service_data_provider;
DROP ROLE mysql_rest_service_dev;
