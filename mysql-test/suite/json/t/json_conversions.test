########### suite/json/t/json_conversions.test                       #
# Tests conversion from json data types to mysql data types          #
#                                                                    #
# This test copies all tests originally in json_conversions.test     #
######################################################################

--echo # Test of the conversion from JSON data type to MySQL types
--echo # ----------------------------------------------------------------------

--echo # Set up auxiliary table with possible JSON values. The idea here is to
--echo # enumerate all possible types of JSON values, including the different
--echo # opaque values we could possibly end up with. A difficulty here is that
--echo # it is hard create scenarios that will actually employ all possible
--echo # MYSQL_TYPE_* types inside opaque: partly because some are used as
--echo # native scalars, e.g. signed int -> JSON (signed) INTEGER, partly
--echo # because not all MYSQL_TYPE_* values can actually label a column, but
--echo # are seemingly only used in intermediate steps, e.g. BINARY and
--echo # VARBINARY which end up as column with CHAR and VARCHAR with binary
--echo # character set internally.


create table t(c varchar(30) not null, j json, key(c));

create table blobs(b blob); insert into blobs values(x'cafebabe');
create table tinyblobs(b tinyblob); insert into tinyblobs values(x'cafebabe');
create table mediumblobs(b mediumblob); insert into mediumblobs values(x'cafebabe');
create table longblobs(b longblob); insert into longblobs values(x'cafebabe');
create table year(y year); insert into year values('1992');
create table varbin(b varbinary(40)); insert into varbin values(x'cafebabe');
create table bin(b binary(40)); insert into varbin values(x'cafebabe');
create table enum(e enum('a', 'b', 'c')); insert into enum values ('b');
create table sett(e set('a', 'b', 'c')); insert into sett values ('b,c');
create table varchar_binary(c varchar(30) character set 'binary'); insert into varchar_binary values ('foo');

insert into t values ('null'            ,'null');
insert into t values ('bool'            ,'true');
insert into t values ('uint'            ,cast(cast(12 as unsigned) as json));
insert into t values ('int'             ,'12');
insert into t values ('double'          ,cast(3.14E0 as json));
insert into t values ('stringany'       ,'"a"');
insert into t values ('stringint'       ,'"1"');
insert into t values ('stringdecimal'   ,'"3.14"');
insert into t values ('object'          ,'{"a": 3}');
insert into t values ('array'           ,'[1,2]');
insert into t values ('opaque_mysql_type_decimal'  ,cast(3.14 as json));
insert into t(c,j) (select
                      'opaque_mysql_type_set'      ,cast(e as json) from sett);

insert into t(c,j) (select
                      'opaque_mysql_type_enum'     ,cast(e as json) from enum);

insert into t values ('opaque_mysql_type_date'     ,cast(cast('2015-01-15' as date) as json));
insert into t values ('opaque_mysql_type_time'     ,cast(cast('23:24:25' as time) as json));
insert into t values ('opaque_mysql_type_datetime' ,cast(cast('2015-01-15 23:24:25' as datetime) as json));
insert into t values ('opaque_mysql_type_geom'     ,cast(st_geomfromtext('point(1 1)') as json));

insert into t(c,j) (select
                     'opaque_mysql_type_bit'       ,cast(x'cafe' as json));

insert into t(c,j) (select
                     'opaque_mysql_type_year'        ,cast(y as json) from year);

# can't use values: stringifies blob */
insert into t(c,j) (select
                     'opaque_mysql_type_blob'        ,cast(b as json) from blobs);

insert into t(c,j) (select
                     'opaque_mysql_type_longblob'    ,cast(b as json) from longblobs);

insert into t(c,j) (select
                     'opaque_mysql_type_mediumblob'  ,cast(b as json) from mediumblobs);

insert into t(c,j) (select
                     'opaque_mysql_type_tinyblob'    ,cast(b as json) from tinyblobs);

# BINARY and VARBINARY are seen as VARCHAR with binary charset
# (opaque_mysql_type_varchar below) so just use NULL

insert into t(c,j) (select
                     'opaque_mysql_type_varbinary'   ,NULL);

insert into t(c,j) (select
                     'opaque_mysql_type_binary'      ,NULL);

insert into t(c,j) (select
                     'opaque_mysql_type_varchar'     ,cast(c as json) from varchar_binary);

insert into t(c,j) (select
                     'opaque_mysql_type_string'      ,NULL);  # not used for field

insert into t values ('opaque_mysql_type_var_string' ,NULL);  # not used for field

drop table blobs;
drop table tinyblobs;
drop table mediumblobs;
drop table longblobs;
drop table year;
drop table varbin;
drop table bin;
drop table enum;
drop table sett;
drop table varchar_binary;

select c, json_type(j), j from t;

--echo # Auxiliary table containing columns of MySQL types
create table at(c varchar(36),
                _bit bit(64),
                _tin tinyint(8),
                _boo bool,
                _sms smallint signed,
                _smu smallint unsigned,
                _mes mediumint signed,
                _meu mediumint unsigned,
                _ins int signed,
                _inu int unsigned,
                _bis bigint signed,
                _biu bigint unsigned,
                _dec decimal (5,2),
                _flo float,
                _dou double,
                _dat date default '2000-01-01',
                _dtt datetime default '2000-01-01 00:00:00',
                _smp timestamp default '2000-01-01 00:00:00',
                _tim time default' 00:00:00',
                _yea year,
                _jsn json,
                _chr char(255),
                _vch varchar(255),
                _bin binary(255),
                _vbn varbinary(255),
                _tbl tinyblob,
                _ttx tinytext,
                _blb blob,
                _txt text,
                _mbb mediumblob,
                _mtx mediumtext,
                _lbb longblob,
                _ltx longtext,
                _enu enum('a', 'b', 'c'),
                _set set('a', 'b', 'c'),
                _geo geometry,
                _pnt point,
                _lst linestring,
                _pol polygon,
                _mpt multipoint,
                _mls multilinestring,
                _mpy multipolygon,
                _gco geometrycollection);

--echo # ----------------------------------------------------------------------
--echo #  I N S E R T   F R O M   J S O N   C O L U M N
--echo # ----------------------------------------------------------------------
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='null';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='bool';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='uint';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='int';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='double';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='stringany';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='stringint';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='stringdecimal';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='object';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='array';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_decimal';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_set';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_date';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_time';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_bit';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_year';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_bit) select concat('_bit: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='null';
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='bool';
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='uint';
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='int';
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='stringany';
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='stringdecimal'; # needs cast(j as decimal)
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='array';
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_WARN_DATA_OUT_OF_RANGE
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_tin) select concat('_tin: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='null';
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='bool';
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='uint';
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='int';
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='stringany';
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='array';
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_WARN_DATA_OUT_OF_RANGE
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_boo) select concat('_boo: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='null';
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='bool';
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='uint';
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='int';
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='stringany';
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='array';
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_bit';
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_sms) select concat('_sms: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='null';
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='bool';
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='uint';
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='int';
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='stringany';
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='array';
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_bit';
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_smu) select concat('_smu: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='null';
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='bool';
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='uint';
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='int';
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='stringany';
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='array';
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_bit';
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_mes) select concat('_mes: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='null';
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='bool';
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='uint';
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='int';
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='stringany';
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='array';
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_bit';
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_meu) select concat('_meu: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='null';
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='bool';
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='uint';
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='int';
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='stringany';
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='array';
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_bit';
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_ins) select concat('_ins: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='null';
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='bool';
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='uint';
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='int';
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='stringany';
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='array';
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_bit';
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_inu) select concat('_inu: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='null';
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='bool';
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='uint';
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='int';
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='stringany';
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='array';
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_bit';
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_bis) select concat('_bis: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='null';
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='bool';
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='uint';
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='int';
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='stringany';
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='array';
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_bit';
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_biu) select concat('_biu: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='null';
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='bool';
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='uint';
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='int';
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='stringany';
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='stringint';
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='array';
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_WARN_DATA_OUT_OF_RANGE
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_dec) select concat('_dec: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='null';
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='bool';
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='uint';
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='int';
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='stringany';
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='stringint';
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='array';
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_bit';
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_flo) select concat('_flo: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='null';
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='bool';
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='uint';
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='int';
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='stringany';
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='stringint';
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='array';
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_bit';
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_dou) select concat('_dou: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='null';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='bool';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='uint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='int';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='stringany';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='array';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_enum';
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_time';
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='null';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='bool';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='uint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='int';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='stringany';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='array';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_enum';
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_time';
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_dtt) select concat('_dtt: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_dat) select concat('_dat: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='null';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='bool';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='uint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='int';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='stringany';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='array';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_enum';
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_time';
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_smp) select concat('_smp: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='null';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='bool';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='uint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='int';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='stringany';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='array';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_date';
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_tim) select concat('_tim: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='null';
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='bool';
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='uint';
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='int';
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='stringany';
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='array';
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_bit';
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_yea) select concat('_yea: ',c),j from t where c='opaque_mysql_type_var_string';

insert into at(c,_jsn) select concat('_jsn: ',c),j from t;

insert into at(c,_chr) select concat('_chr: ',c),j from t;

insert into at(c,_vch) select concat('_vch: ',c),j from t;

insert into at(c,_bin) select concat('_bin: ',c),j from t;

insert into at(c,_vbn) select concat('_vbn: ',c),j from t;

insert into at(c,_tbl) select concat('_tbl: ',c),j from t;

insert into at(c,_ttx) select concat('_ttx: ',c),j from t;

insert into at(c,_blb) select concat('_blb: ',c),j from t;

insert into at(c,_txt) select concat('_txt: ',c),j from t;

insert into at(c,_mbb) select concat('_mbb: ',c),j from t;

insert into at(c,_mtx) select concat('_mtx: ',c),j from t;

insert into at(c,_lbb) select concat('_lbb: ',c),j from t;

insert into at(c,_ltx) select concat('_ltx: ',c),j from t;

--error 1265
# insert into at(c,_enu) select concat('_enu: ',c),j from t where c='null';
--error 1265
# insert into at(c,_enu) select concat('_enu: ',c),j from t where c='bool';
--error 1265
#insert into at(c,_enu) select concat('_enu: ',c),j from t where c='uint';
--error 1265
#insert into at(c,_enu) select concat('_enu: ',c),j from t where c='int';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='double';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='stringany'; # needs JSON_UNQUOTE
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='stringint';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='stringdecimal';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='object';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='array';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_decimal';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_set';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_enum';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_date';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_time';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_datetime';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_geom';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_bit';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_year';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_blob';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_longblob';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_binary';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_enu) select concat('_enu: ',c),j from t where c='opaque_mysql_type_var_string';

--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='null';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='bool';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='uint';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='int';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='double';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='stringany'; # needs JSON_UNQUOTE
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='stringint';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='stringdecimal';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='object';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='array';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_decimal';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_set';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_enum';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_date';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_time';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_datetime';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_geom';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_bit';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_year';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_blob';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_longblob';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_binary';
--error 1265
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_set) select concat('_set: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_geo) select concat('_geo: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_pnt) select concat('_pnt: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_lst) select concat('_lst: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_pol) select concat('_pol: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_mpt) select concat('_mpt: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_mls) select concat('_mls: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_mpy) select concat('_mpy: ',c),j from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_varbinary';
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_varchar';
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_string';
insert into at(c,_gco) select concat('_gco: ',c),j from t where c='opaque_mysql_type_var_string';

--echo # ----------------------------------------------------------------------
--echo #  I N S E R T   F R O M   J S O N   F U N C T I O N
--echo # ----------------------------------------------------------------------
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='double';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='stringint';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='stringdecimal';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='object';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_bit) select concat('_bit: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='stringdecimal'; # needs cast(j as decimal)
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_WARN_DATA_OUT_OF_RANGE
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_tin) select concat('_tin: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_WARN_DATA_OUT_OF_RANGE
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_boo) select concat('_boo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_sms) select concat('_sms: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_smu) select concat('_smu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_mes) select concat('_mes: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_meu) select concat('_meu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_ins) select concat('_ins: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_inu) select concat('_inu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_bis) select concat('_bis: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_biu) select concat('_biu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='stringint';
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_WARN_DATA_OUT_OF_RANGE
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_dec) select concat('_dec: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='stringint';
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_flo) select concat('_flo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='null';
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='stringany';
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='stringint';
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_dou) select concat('_dou: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='null';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='bool';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='uint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='int';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='array';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='null';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='bool';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='uint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='int';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='array';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_dtt) select concat('_dtt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_dat) select concat('_dat: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='null';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='bool';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='uint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='int';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='array';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_smp) select concat('_smp: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='null';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='bool';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='uint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='int';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='array';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_tim) select concat('_tim: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='null';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='bool';
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='uint';
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='int';
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='double';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='stringint'; # JSON_UNQUOTE helps
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='object';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='array';
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_yea) select concat('_yea: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

insert into at(c,_jsn) select concat('_jsn: ',c), json_extract(j, '$') from t;

insert into at(c,_chr) select concat('_chr: ',c), json_extract(j, '$') from t;

insert into at(c,_vch) select concat('_vch: ',c), json_extract(j, '$') from t;

insert into at(c,_bin) select concat('_bin: ',c), json_extract(j, '$') from t;

insert into at(c,_vbn) select concat('_vbn: ',c), json_extract(j, '$') from t;

insert into at(c,_tbl) select concat('_tbl: ',c), json_extract(j, '$') from t;

insert into at(c,_ttx) select concat('_ttx: ',c), json_extract(j, '$') from t;

insert into at(c,_blb) select concat('_blb: ',c), json_extract(j, '$') from t;

insert into at(c,_txt) select concat('_txt: ',c), json_extract(j, '$') from t;

insert into at(c,_mbb) select concat('_mbb: ',c), json_extract(j, '$') from t;

insert into at(c,_mtx) select concat('_mtx: ',c), json_extract(j, '$') from t;

insert into at(c,_lbb) select concat('_lbb: ',c), json_extract(j, '$') from t;

insert into at(c,_ltx) select concat('_ltx: ',c), json_extract(j, '$') from t;

--error 1265
# insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='null';
--error 1265
# insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='bool';
--error 1265
#insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='uint';
--error 1265
#insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='int';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='double';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='stringany'; # needs JSON_UNQUOTE
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='stringint';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='object';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='array';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_enu) select concat('_enu: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='null';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='bool';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='uint';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='int';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='double';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='stringany'; # needs JSON_UNQUOTE
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='stringint';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='object';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='array';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error 1265
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_set) select concat('_set: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_geo) select concat('_geo: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_pnt) select concat('_pnt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_lst) select concat('_lst: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_pol) select concat('_pol: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_mpt) select concat('_mpt: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_mls) select concat('_mls: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_mpy) select concat('_mpy: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_varchar';
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_string';
insert into at(c,_gco) select concat('_gco: ',c), json_extract(j, '$') from t where c='opaque_mysql_type_var_string';

insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='bool') from t where c='bool';

--echo # ----------------------------------------------------------------------
--echo #  I N S E R T   F R O M   J S O N   S U B S E L E C T
--echo # ----------------------------------------------------------------------
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_DATA_TOO_LONG
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_bit) select concat('_bit: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal'; # needs cast(j as decimal)
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_WARN_DATA_OUT_OF_RANGE
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_tin) select concat('_tin: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_WARN_DATA_OUT_OF_RANGE
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_boo) select concat('_boo: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_sms) select concat('_sms: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_smu) select concat('_smu: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_mes) select concat('_mes: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_meu) select concat('_meu: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_ins) select concat('_ins: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_inu) select concat('_inu: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_bis) select concat('_bis: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_biu) select concat('_biu: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_WARN_DATA_OUT_OF_RANGE
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_dec) select concat('_dec: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_flo) select concat('_flo: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_dou) select concat('_dou: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='null') from t where c='null';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='bool') from t where c='bool';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='uint') from t where c='uint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='int') from t where c='int';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='array') from t where c='array';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='null') from t where c='null';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='bool') from t where c='bool';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='uint') from t where c='uint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='int') from t where c='int';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='array') from t where c='array';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_dtt) select concat('_dtt: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_dat) select concat('_dat: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='null') from t where c='null';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='bool') from t where c='bool';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='uint') from t where c='uint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='int') from t where c='int';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='array') from t where c='array';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_smp) select concat('_smp: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='null') from t where c='null';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='bool') from t where c='bool';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='uint') from t where c='uint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='int') from t where c='int';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='double') from t where c='double';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='object') from t where c='object';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='array') from t where c='array';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_INVALID_JSON_VALUE_FOR_CAST
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_tim) select concat('_tim: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='null') from t where c='null';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='double') from t where c='double';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='stringint') from t where c='stringint'; # JSON_UNQUOTE helps
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='object') from t where c='object';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_yea) select concat('_yea: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_jsn) select concat('_jsn: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_chr) select concat('_chr: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_vch) select concat('_vch: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_bin) select concat('_bin: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_vbn) select concat('_vbn: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_tbl) select concat('_tbl: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_ttx) select concat('_ttx: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_blb) select concat('_blb: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_txt) select concat('_txt: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_mbb) select concat('_mbb: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_mtx) select concat('_mtx: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_lbb) select concat('_lbb: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='null') from t where c='null';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='bool') from t where c='bool';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='uint') from t where c='uint';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='int') from t where c='int';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='double') from t where c='double';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='stringany') from t where c='stringany';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='stringint') from t where c='stringint';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='object') from t where c='object';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='array') from t where c='array';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_ltx) select concat('_ltx: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error 1265
# insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='null') from t where c='null';
--error 1265
# insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='bool') from t where c='bool';
--error 1265
#insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='uint') from t where c='uint';
--error 1265
#insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='int') from t where c='int';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='double') from t where c='double';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='stringany') from t where c='stringany'; # needs JSON_UNQUOTE
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='stringint') from t where c='stringint';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='object') from t where c='object';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='array') from t where c='array';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error 1265
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_enu) select concat('_enu: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='null') from t where c='null';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='bool') from t where c='bool';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='uint') from t where c='uint';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='int') from t where c='int';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='double') from t where c='double';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='stringany') from t where c='stringany'; # needs JSON_UNQUOTE
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='stringint') from t where c='stringint';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='object') from t where c='object';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='array') from t where c='array';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error 1265
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_set) select concat('_set: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='null') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='bool') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='uint') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='int') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='double') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='object') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='array') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_geo) select concat('_geo: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='null') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='bool') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='uint') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='int') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='double') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='object') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='array') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_pnt) select concat('_pnt: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='null') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='bool') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='uint') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='int') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='double') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='object') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='array') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_lst) select concat('_lst: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='null') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='bool') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='uint') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='int') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='double') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='object') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='array') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_pol) select concat('_pol: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='null') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='bool') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='uint') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='int') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='double') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='object') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='array') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_mpt) select concat('_mpt: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='null') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='bool') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='uint') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='int') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='double') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='object') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='array') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_mls) select concat('_mls: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='null') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='bool') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='uint') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='int') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='double') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='object') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='array') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_mpy) select concat('_mpy: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='null') from t where c='null';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='bool') from t where c='bool';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='uint') from t where c='uint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='int') from t where c='int';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='double') from t where c='double';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='stringany') from t where c='stringany';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='stringint') from t where c='stringint';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='stringdecimal') from t where c='stringdecimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='object') from t where c='object';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='array') from t where c='array';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_decimal') from t where c='opaque_mysql_type_decimal';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_set') from t where c='opaque_mysql_type_set';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_enum') from t where c='opaque_mysql_type_enum';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_date') from t where c='opaque_mysql_type_date';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_time') from t where c='opaque_mysql_type_time';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_datetime') from t where c='opaque_mysql_type_datetime';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_geom') from t where c='opaque_mysql_type_geom';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_bit') from t where c='opaque_mysql_type_bit';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_year') from t where c='opaque_mysql_type_year';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_blob') from t where c='opaque_mysql_type_blob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_longblob') from t where c='opaque_mysql_type_longblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_mediumblob') from t where c='opaque_mysql_type_mediumblob';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_tinyblob') from t where c='opaque_mysql_type_tinyblob';
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_varbinary') from t where c='opaque_mysql_type_varbinary';
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_binary') from t where c='opaque_mysql_type_binary';
--error ER_CANT_CREATE_GEOMETRY_OBJECT
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_varchar') from t where c='opaque_mysql_type_varchar';
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_string') from t where c='opaque_mysql_type_string';
insert into at(c,_gco) select concat('_gco: ',c), (select j from t where c='opaque_mysql_type_var_string') from t where c='opaque_mysql_type_var_string';

--echo # ----------------------------------------------------------------------
--echo # Validate the data actually inserted
--echo # ----------------------------------------------------------------------

select c, _bit from at
where c like '_bit%';
select c, _tin from at
where c like '_tin%';
select c, _boo from at
where c like '_boo%';
select c, _sms from at
where c like '_sms%';
select c, _smu from at
where c like '_smu%';
select c, _mes from at
where c like '_mes%';
select c, _meu from at
where c like '_meu%';
select c, _ins from at
where c like '_ins%';
select c, _inu from at
where c like '_inu%';
select c, _bis from at
where c like '_bis%';
select c, _biu from at
where c like '_biu%';
select c, _dec from at
where c like '_dec%';
select c, _flo from at
where c like '_flo%';
select c, _dou from at
where c like '_dou%';
--replace_regex /2015-01-15/--EXPECTED_DATE--/ /[0-9]{4}-[0-9]{2}-[0-9]{2}/--DATE--/
select c, _dat from at
where c like '_dat%';
--replace_regex /2015-01-15 23:24:25/--EXPECTED_DATETIME--/ /[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}/--DATETIME--/
select c, _dtt from at
where c like '_dtt%';
--replace_regex /2015-01-15 23:24:25/--EXPECTED_TIMESTAMP--/ /[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}/--TIMESTAMP--/
select c, _smp from at
where c like '_smp%';
--replace_regex /23:24:25/--EXPECTED_TIME--/ /[0-9]{2}:[0-9]{2}:[0-9]{2}/--TIME--/
select c, _tim from at
where c like '_tim%';
select c, _yea from at
where c like '_yea%';
select c, _jsn from at
where c like '_jsn%';
select c, _chr from at
where c like '_chr%';
select c, _vch from at
where c like '_vch%';
select c, replace(_bin, '\0', '') from at
where c like '_bin%';
select c, _vbn from at
where c like '_vbn%';
select c, _tbl from at
where c like '_tbl%';
select c, _ttx from at
where c like '_ttx%';
select c, _blb from at
where c like '_blb%';
select c, _txt from at
where c like '_txt%';
select c, _mbb from at
where c like '_mbb%';
select c, _mtx from at
where c like '_mtx%';
select c, _lbb from at
where c like '_lbb%';
select c, _ltx from at
where c like '_ltx%';
select c, _enu from at
where c like '_enu%';
select c, _set from at
where c like '_set%';
select c, _geo from at
where c like '_geo%';
select c, _pnt from at
where c like '_pnt%';
select c, _lst from at
where c like '_lst%';
select c, _pol from at
where c like '_pol%';
select c, _mpt from at
where c like '_mpt%';
select c, _mls from at
where c like '_mls%';
select c, _mpy from at
where c like '_mpy%';
select c, _gco from at
where c like '_gco%';




--echo # Explicit coercions (CAST)
--echo # ----------------------------------------------------------------------

--echo # CAST to BINARY[(N)]
--echo # CAST to CHAR[(N)]
--echo # CAST to DATE
--echo # CAST to DATETIME
--echo # CAST to TIME
--echo # CAST to DECIMAL[(M[,D])]
--echo # CAST to SIGNED [INTEGER]
--echo # CAST to UNSIGNED [INTEGER]
--echo # CAST to JSON

--echo # ----------------------------------------------------------------------
--echo #  C A S T   F R O M   J S O N   C O L U M N
--echo # ----------------------------------------------------------------------
select concat('From JSON col ',c, ' as BINARY(35)'), replace(cast(j as BINARY(35)), '\0', '') from t;

select concat('From JSON col ',c, ' as CHAR(35))'), cast(j as CHAR(35)) from t;

select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='null';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='bool';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='uint';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='int';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='double';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='stringany';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='stringint';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='stringdecimal';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='object';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='array';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_decimal';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_set';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_enum';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_date';
--replace_regex /2015-01-15/--EXPECTED_DATE--/ /[0-9]{4}-[0-9]{2}-[0-9]{2}/--DATE--/
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_time';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_datetime';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_geom';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_bit';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_year';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_blob';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_longblob';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_mediumblob';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_tinyblob';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_varbinary';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_binary';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_varchar';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_string';
select concat('From JSON col ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_var_string';

select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='null';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='bool';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='uint';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='int';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='double';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='stringany';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='stringint';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='stringdecimal';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='object';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='array';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_decimal';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_set';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_enum';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_date';
--replace_regex /2015-01-15/--EXPECTED_DATE--/ /[0-9]{4}-[0-9]{2}-[0-9]{2}/--DATE--/
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_time';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_datetime';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_geom';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_bit';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_year';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_blob';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_longblob';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_mediumblob';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_tinyblob';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_varbinary';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_binary';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_varchar';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_string';
select concat('From JSON col ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_var_string';

select concat('From JSON col ',c, ' as TIME'), cast(j as TIME) from t;

select concat('From JSON col ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t;

select concat('From JSON col ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t;

select concat('From JSON col ',c, ' as SIGNED'), cast(j as SIGNED) from t;


--echo # ----------------------------------------------------------------------
--echo #  C A S T   F R O M   J S O N   F U N C T I O N
--echo # ----------------------------------------------------------------------
select concat('From JSON func ',c, ' as BINARY(35)'), replace(cast(json_extract(j, '$') as BINARY(35)), '\0', '') from t;

select concat('From JSON func ',c, ' as CHAR(35))'), cast(json_extract(j, '$') as CHAR(35)) from t;

select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='null';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='bool';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='uint';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='int';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='double';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='stringany';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='stringint';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='stringdecimal';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='object';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='array';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_decimal';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_set';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_enum';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_date';
--replace_regex /2015-01-15/--EXPECTED_DATE--/ /[0-9]{4}-[0-9]{2}-[0-9]{2}/--DATE--/
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_time';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_datetime';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_geom';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_bit';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_year';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_blob';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_longblob';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_mediumblob';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_tinyblob';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_varbinary';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_binary';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_varchar';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_string';
select concat('From JSON func ',c, ' as DATE'), cast(json_extract(j, '$') as DATE) from t where c='opaque_mysql_type_var_string';


select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='null';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='bool';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='uint';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='int';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='double';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='stringany';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='stringint';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='stringdecimal';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='object';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='array';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_decimal';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_set';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_enum';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_date';
--replace_regex /2015-01-15/--EXPECTED_DATE--/ /[0-9]{4}-[0-9]{2}-[0-9]{2}/--DATE--/
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_time';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_datetime';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_geom';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_bit';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_year';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_blob';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_longblob';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_mediumblob';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_tinyblob';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_varbinary';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_binary';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_varchar';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_string';
select concat('From JSON func ',c, ' as DATETIME'), cast(json_extract(j, '$') as DATETIME) from t where c='opaque_mysql_type_var_string';

select concat('From JSON func ',c, ' as TIME'), cast(json_extract(j, '$') as TIME) from t;

select concat('From JSON func ',c, ' as DECIMAL(5,2)'), cast(json_extract(j, '$') as DECIMAL(5,2)) from t;

select concat('From JSON func ',c, ' as UNSIGNED'), cast(json_extract(j, '$') as UNSIGNED) from t;

select concat('From JSON func ',c, ' as SIGNED'), cast(json_extract(j, '$') as SIGNED) from t;

--echo # ----------------------------------------------------------------------
--echo #  C A S T   F R O M   J S O N   S U B Q U E R Y
--echo # ----------------------------------------------------------------------

select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='null') as BINARY(35)), '\0', '') from t where c='null';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='bool') as BINARY(35)), '\0', '') from t where c='bool';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='uint') as BINARY(35)), '\0', '') from t where c='uint';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='int') as BINARY(35)), '\0', '') from t where c='int';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='double') as BINARY(35)), '\0', '') from t where c='double';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='stringany') as BINARY(35)), '\0', '') from t where c='stringany';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='stringint') as BINARY(35)), '\0', '') from t where c='stringint';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='stringdecimal') as BINARY(35)), '\0', '') from t where c='stringdecimal';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='object') as BINARY(35)), '\0', '') from t where c='object';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='array') as BINARY(35)), '\0', '') from t where c='array';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_decimal') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_decimal';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_set') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_set';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_enum') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_enum';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_date') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_date';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_time') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_time';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_datetime') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_datetime';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_geom') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_geom';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_bit') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_bit';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_year') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_blob') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_blob';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_longblob') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_longblob';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_mediumblob') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_mediumblob';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_tinyblob') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_tinyblob';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_varbinary') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_varbinary';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_binary') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_binary';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_varchar') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_varchar';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_string') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_string';
select concat('From JSON subselect ',c, ' as BINARY(35)'), replace(cast((select j from t where c='opaque_mysql_type_var_string') as BINARY(35)), '\0', '') from t where c='opaque_mysql_type_var_string';

select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='null') as CHAR(35)) from t where c='null';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='bool') as CHAR(35)) from t where c='bool';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='uint') as CHAR(35)) from t where c='uint';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='int') as CHAR(35)) from t where c='int';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='double') as CHAR(35)) from t where c='double';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='stringany') as CHAR(35)) from t where c='stringany';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='stringint') as CHAR(35)) from t where c='stringint';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='stringdecimal') as CHAR(35)) from t where c='stringdecimal';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='object') as CHAR(35)) from t where c='object';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='array') as CHAR(35)) from t where c='array';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_decimal') as CHAR(35)) from t where c='opaque_mysql_type_decimal';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_set') as CHAR(35)) from t where c='opaque_mysql_type_set';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_enum') as CHAR(35)) from t where c='opaque_mysql_type_enum';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_date') as CHAR(35)) from t where c='opaque_mysql_type_date';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_time') as CHAR(35)) from t where c='opaque_mysql_type_time';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_datetime') as CHAR(35)) from t where c='opaque_mysql_type_datetime';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_geom') as CHAR(35)) from t where c='opaque_mysql_type_geom';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_bit') as CHAR(35)) from t where c='opaque_mysql_type_bit';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_year') as CHAR(35)) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast(j as CHAR(35)) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_blob') as CHAR(35)) from t where c='opaque_mysql_type_blob';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast(j as CHAR(35)) from t where c='opaque_mysql_type_longblob';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_mediumblob') as CHAR(35)) from t where c='opaque_mysql_type_mediumblob';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast(j as CHAR(35)) from t where c='opaque_mysql_type_tinyblob';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_varbinary') as CHAR(35)) from t where c='opaque_mysql_type_varbinary';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast(j as CHAR(35)) from t where c='opaque_mysql_type_binary';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_varchar') as CHAR(35)) from t where c='opaque_mysql_type_varchar';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast(j as CHAR(35)) from t where c='opaque_mysql_type_string';
select concat('From JSON subselect ',c, ' as CHAR(35))'), cast((select j from t where c='opaque_mysql_type_var_string') as CHAR(35)) from t where c='opaque_mysql_type_var_string';

select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='null') as DATE) from t where c='null';
select concat('From JSON subselect ',c, ' as DATE'), cast(j as DATE) from t where c='bool';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='uint') as DATE) from t where c='uint';
select concat('From JSON subselect ',c, ' as DATE'), cast(j as DATE) from t where c='int';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='double') as DATE) from t where c='double';
select concat('From JSON subselect ',c, ' as DATE'), cast(j as DATE) from t where c='stringany';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='stringint') as DATE) from t where c='stringint';
select concat('From JSON subselect ',c, ' as DATE'), cast(j as DATE) from t where c='stringdecimal';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='object') as DATE) from t where c='object';
select concat('From JSON subselect ',c, ' as DATE'), cast(j as DATE) from t where c='array';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_decimal') as DATE) from t where c='opaque_mysql_type_decimal';
select concat('From JSON subselect ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_set';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_enum') as DATE) from t where c='opaque_mysql_type_enum';
select concat('From JSON subselect ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_date';
--replace_regex /2015-01-15/--EXPECTED_DATE--/ /[0-9]{4}-[0-9]{2}-[0-9]{2}/--DATE--/
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_time') as DATE) from t where c='opaque_mysql_type_time';
select concat('From JSON subselect ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_datetime';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_geom') as DATE) from t where c='opaque_mysql_type_geom';
select concat('From JSON subselect ',c, ' as DATE'), cast(j as DATE) from t where c='opaque_mysql_type_bit';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_year') as DATE) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_blob') as DATE) from t where c='opaque_mysql_type_blob';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_longblob') as DATE) from t where c='opaque_mysql_type_longblob';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_mediumblob') as DATE) from t where c='opaque_mysql_type_mediumblob';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_tinyblob') as DATE) from t where c='opaque_mysql_type_tinyblob';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_varbinary') as DATE) from t where c='opaque_mysql_type_varbinary';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_binary') as DATE) from t where c='opaque_mysql_type_binary';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_varchar') as DATE) from t where c='opaque_mysql_type_varchar';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_string') as DATE) from t where c='opaque_mysql_type_string';
select concat('From JSON subselect ',c, ' as DATE'), cast((select j from t where c='opaque_mysql_type_var_string') as DATE) from t where c='opaque_mysql_type_var_string';


select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='null') as DATETIME) from t where c='null';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='bool') as DATETIME) from t where c='bool';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='uint') as DATETIME) from t where c='uint';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='int') as DATETIME) from t where c='int';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='double') as DATETIME) from t where c='double';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='stringany') as DATETIME) from t where c='stringany';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='stringint') as DATETIME) from t where c='stringint';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='stringdecimal') as DATETIME) from t where c='stringdecimal';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='object') as DATETIME) from t where c='object';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='array') as DATETIME) from t where c='array';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='opaque_mysql_type_decimal') as DATETIME) from t where c='opaque_mysql_type_decimal';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='opaque_mysql_type_set') as DATETIME) from t where c='opaque_mysql_type_set';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='opaque_mysql_type_enum') as DATETIME) from t where c='opaque_mysql_type_enum';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='opaque_mysql_type_date') as DATETIME) from t where c='opaque_mysql_type_date';
--replace_regex /2015-01-15/--EXPECTED_DATE--/ /[0-9]{4}-[0-9]{2}-[0-9]{2}/--DATE--/
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='opaque_mysql_type_time') as DATETIME) from t where c='opaque_mysql_type_time';
select concat('From JSON subselect ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_datetime';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='opaque_mysql_type_geom') as DATETIME) from t where c='opaque_mysql_type_geom';
select concat('From JSON subselect ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_bit';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='opaque_mysql_type_year') as DATETIME) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='opaque_mysql_type_blob') as DATETIME) from t where c='opaque_mysql_type_blob';
select concat('From JSON subselect ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_longblob';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='opaque_mysql_type_mediumblob') as DATETIME) from t where c='opaque_mysql_type_mediumblob';
select concat('From JSON subselect ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_tinyblob';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='opaque_mysql_type_varbinary') as DATETIME) from t where c='opaque_mysql_type_varbinary';
select concat('From JSON subselect ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_binary';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='opaque_mysql_type_varchar') as DATETIME) from t where c='opaque_mysql_type_varchar';
select concat('From JSON subselect ',c, ' as DATETIME'), cast(j as DATETIME) from t where c='opaque_mysql_type_string';
select concat('From JSON subselect ',c, ' as DATETIME'), cast((select j from t where c='opaque_mysql_type_var_string') as DATETIME) from t where c='opaque_mysql_type_var_string';

select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='null') as TIME) from t where c='null';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='bool';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='uint') as TIME) from t where c='uint';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='int';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='double') as TIME) from t where c='double';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='stringany';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='stringint') as TIME) from t where c='stringint';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='stringdecimal';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='object') as TIME) from t where c='object';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='array';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='opaque_mysql_type_decimal') as TIME) from t where c='opaque_mysql_type_decimal';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='opaque_mysql_type_set';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='opaque_mysql_type_enum') as TIME) from t where c='opaque_mysql_type_enum';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='opaque_mysql_type_date';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='opaque_mysql_type_time') as TIME) from t where c='opaque_mysql_type_time';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='opaque_mysql_type_datetime';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='opaque_mysql_type_geom') as TIME) from t where c='opaque_mysql_type_geom';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='opaque_mysql_type_bit';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='opaque_mysql_type_year') as TIME) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='opaque_mysql_type_blob') as TIME) from t where c='opaque_mysql_type_blob';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='opaque_mysql_type_longblob';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='opaque_mysql_type_mediumblob') as TIME) from t where c='opaque_mysql_type_mediumblob';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='opaque_mysql_type_tinyblob';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='opaque_mysql_type_varbinary') as TIME) from t where c='opaque_mysql_type_varbinary';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='opaque_mysql_type_binary';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='opaque_mysql_type_varchar') as TIME) from t where c='opaque_mysql_type_varchar';
select concat('From JSON subselect ',c, ' as TIME'), cast(j as TIME) from t where c='opaque_mysql_type_string';
select concat('From JSON subselect ',c, ' as TIME'), cast((select j from t where c='opaque_mysql_type_var_string') as TIME) from t where c='opaque_mysql_type_var_string';

select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='null') as DECIMAL(5,2)) from t where c='null';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='bool';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='uint') as DECIMAL(5,2)) from t where c='uint';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='int';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='double') as DECIMAL(5,2)) from t where c='double';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='stringany';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='stringint') as DECIMAL(5,2)) from t where c='stringint';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='stringdecimal';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='object') as DECIMAL(5,2)) from t where c='object';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='array';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='opaque_mysql_type_decimal') as DECIMAL(5,2)) from t where c='opaque_mysql_type_decimal';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='opaque_mysql_type_set';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='opaque_mysql_type_enum') as DECIMAL(5,2)) from t where c='opaque_mysql_type_enum';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='opaque_mysql_type_date';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='opaque_mysql_type_time') as DECIMAL(5,2)) from t where c='opaque_mysql_type_time';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='opaque_mysql_type_datetime';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='opaque_mysql_type_geom') as DECIMAL(5,2)) from t where c='opaque_mysql_type_geom';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='opaque_mysql_type_bit';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='opaque_mysql_type_year') as DECIMAL(5,2)) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='opaque_mysql_type_blob') as DECIMAL(5,2)) from t where c='opaque_mysql_type_blob';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='opaque_mysql_type_longblob';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='opaque_mysql_type_mediumblob') as DECIMAL(5,2)) from t where c='opaque_mysql_type_mediumblob';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='opaque_mysql_type_tinyblob';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='opaque_mysql_type_varbinary') as DECIMAL(5,2)) from t where c='opaque_mysql_type_varbinary';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='opaque_mysql_type_binary';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='opaque_mysql_type_varchar') as DECIMAL(5,2)) from t where c='opaque_mysql_type_varchar';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast(j as DECIMAL(5,2)) from t where c='opaque_mysql_type_string';
select concat('From JSON subselect ',c, ' as DECIMAL(5,2)'), cast((select j from t where c='opaque_mysql_type_var_string') as DECIMAL(5,2)) from t where c='opaque_mysql_type_var_string';

select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='null') as UNSIGNED) from t where c='null';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='bool';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='uint') as UNSIGNED) from t where c='uint';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='int';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='double') as UNSIGNED) from t where c='double';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='stringany';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='stringint') as UNSIGNED) from t where c='stringint';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='stringdecimal';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='object') as UNSIGNED) from t where c='object';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='array';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='opaque_mysql_type_decimal') as UNSIGNED) from t where c='opaque_mysql_type_decimal';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='opaque_mysql_type_set';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='opaque_mysql_type_enum') as UNSIGNED) from t where c='opaque_mysql_type_enum';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='opaque_mysql_type_date';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='opaque_mysql_type_time') as UNSIGNED) from t where c='opaque_mysql_type_time';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='opaque_mysql_type_datetime';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='opaque_mysql_type_geom') as UNSIGNED) from t where c='opaque_mysql_type_geom';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='opaque_mysql_type_bit';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='opaque_mysql_type_year') as UNSIGNED) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='opaque_mysql_type_blob') as UNSIGNED) from t where c='opaque_mysql_type_blob';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='opaque_mysql_type_longblob';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='opaque_mysql_type_mediumblob') as UNSIGNED) from t where c='opaque_mysql_type_mediumblob';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='opaque_mysql_type_tinyblob';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='opaque_mysql_type_varbinary') as UNSIGNED) from t where c='opaque_mysql_type_varbinary';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='opaque_mysql_type_binary';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='opaque_mysql_type_varchar') as UNSIGNED) from t where c='opaque_mysql_type_varchar';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast(j as UNSIGNED) from t where c='opaque_mysql_type_string';
select concat('From JSON subselect ',c, ' as UNSIGNED'), cast((select j from t where c='opaque_mysql_type_var_string') as UNSIGNED) from t where c='opaque_mysql_type_var_string';

select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='null') as SIGNED) from t where c='null';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='bool';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='uint') as SIGNED) from t where c='uint';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='int';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='double') as SIGNED) from t where c='double';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='stringany';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='stringint') as SIGNED) from t where c='stringint';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='stringdecimal';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='object') as SIGNED) from t where c='object';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='array';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='opaque_mysql_type_decimal') as SIGNED) from t where c='opaque_mysql_type_decimal';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='opaque_mysql_type_set';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='opaque_mysql_type_enum') as SIGNED) from t where c='opaque_mysql_type_enum';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='opaque_mysql_type_date';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='opaque_mysql_type_time') as SIGNED) from t where c='opaque_mysql_type_time';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='opaque_mysql_type_datetime';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='opaque_mysql_type_geom') as SIGNED) from t where c='opaque_mysql_type_geom';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='opaque_mysql_type_bit';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='opaque_mysql_type_year') as SIGNED) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='opaque_mysql_type_year';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='opaque_mysql_type_blob') as SIGNED) from t where c='opaque_mysql_type_blob';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='opaque_mysql_type_longblob';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='opaque_mysql_type_mediumblob') as SIGNED) from t where c='opaque_mysql_type_mediumblob';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='opaque_mysql_type_tinyblob';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='opaque_mysql_type_varbinary') as SIGNED) from t where c='opaque_mysql_type_varbinary';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='opaque_mysql_type_binary';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='opaque_mysql_type_varchar') as SIGNED) from t where c='opaque_mysql_type_varchar';
select concat('From JSON subselect ',c, ' as SIGNED'), cast(j as SIGNED) from t where c='opaque_mysql_type_string';
select concat('From JSON subselect ',c, ' as SIGNED'), cast((select j from t where c='opaque_mysql_type_var_string') as SIGNED) from t where c='opaque_mysql_type_var_string';

# JSON Comparator Test Cases
# Table Setup for a new JSON table containing all kind of unique
# data taken from this test itself.
#---------------------------------------------------------------
--disable_query_log

create table t_bit select DISTINCT(_bit) FROM at;
create table t_tin select DISTINCT(_tin) FROM at;
create table t_boo select DISTINCT(_boo) FROM at;
create table t_sms select DISTINCT(_sms) FROM at;
create table t_smu select DISTINCT(_smu) FROM at;
create table t_mes select DISTINCT(_mes) FROM at;
create table t_meu select DISTINCT(_meu) FROM at;
create table t_ins select DISTINCT(_ins) FROM at;
create table t_inu select DISTINCT(_inu) FROM at;
create table t_bis select DISTINCT(_bis) FROM at;
create table t_biu select DISTINCT(_biu) FROM at;
create table t_dec select DISTINCT(_dec) FROM at;
create table t_flo select DISTINCT(_flo) FROM at;
create table t_dou select DISTINCT(_dou) FROM at;
create table t_dat select DISTINCT(_dat) FROM at;
create table t_dtt select DISTINCT(_dtt) FROM at;
create table t_smp select DISTINCT(_smp) FROM at;
create table t_tim select DISTINCT(_tim) FROM at;
create table t_yea select DISTINCT(_yea) FROM at;
create table t_chr select DISTINCT(_chr) FROM at;
create table t_vch select DISTINCT(_vch) FROM at;
create table t_bin select DISTINCT(_bin) FROM at;
create table t_vbn select DISTINCT(_vbn) FROM at;
create table t_tbl select DISTINCT(_tbl) FROM at;
create table t_ttx select DISTINCT(_ttx) FROM at;
create table t_blb select DISTINCT(_blb) FROM at;
create table t_txt select DISTINCT(_txt) FROM at;
create table t_mbb select DISTINCT(_mbb) FROM at;
create table t_mtx select DISTINCT(_mtx) FROM at;
create table t_lbb select DISTINCT(_lbb) FROM at;
create table t_ltx select DISTINCT(_ltx) FROM at;
create table t_enu select DISTINCT(_enu) FROM at;
create table t_set select DISTINCT(_set) FROM at;
create table t_geo select DISTINCT(_geo) FROM at;
create table t_pnt select DISTINCT(_pnt) FROM at;
create table t_lst select DISTINCT(_lst) FROM at;
create table t_pol select DISTINCT(_pol) FROM at;
create table t_mpt select DISTINCT(_mpt) FROM at;
create table t_mls select DISTINCT(_mls) FROM at;
create table t_mpy select DISTINCT(_mpy) FROM at;
create table t_gco select DISTINCT(_gco) FROM at;

CREATE TABLE jj (col JSON);
INSERT INTO jj
SELECT CAST(_bit AS JSON) FROM t_bit UNION
SELECT CAST(_tin AS JSON) FROM t_tin UNION
SELECT CAST(_boo AS JSON) FROM t_boo UNION
SELECT CAST(_sms AS JSON) FROM t_sms UNION
SELECT CAST(_smu AS JSON) FROM t_smu UNION
SELECT CAST(_mes AS JSON) FROM t_mes UNION
SELECT CAST(_meu AS JSON) FROM t_meu UNION
SELECT CAST(_ins AS JSON) FROM t_ins UNION
SELECT CAST(_inu AS JSON) FROM t_inu UNION
SELECT CAST(_bis AS JSON) FROM t_bis UNION
SELECT CAST(_biu AS JSON) FROM t_biu UNION
SELECT CAST(_dec AS JSON) FROM t_dec UNION
SELECT CAST(_dou AS JSON) FROM t_dou UNION
SELECT CAST(_dat AS JSON) FROM t_dat UNION
SELECT CAST(_dtt AS JSON) FROM t_dtt UNION
SELECT CAST(_smp AS JSON) FROM t_smp UNION
SELECT CAST(_tim AS JSON) FROM t_tim UNION
SELECT CAST(_yea AS JSON) FROM t_yea UNION
SELECT CAST(_chr AS JSON) FROM t_chr UNION
SELECT CAST(_vch AS JSON) FROM t_vch UNION
SELECT CAST(_bin AS JSON) FROM t_bin UNION
SELECT CAST(_vbn AS JSON) FROM t_vbn UNION
SELECT CAST(_tbl AS JSON) FROM t_tbl UNION
SELECT CAST(_ttx AS JSON) FROM t_ttx UNION
SELECT CAST(_blb AS JSON) FROM t_blb UNION
SELECT CAST(_txt AS JSON) FROM t_txt UNION
SELECT CAST(_mbb AS JSON) FROM t_mbb UNION
SELECT CAST(_mtx AS JSON) FROM t_mtx UNION
SELECT CAST(_lbb AS JSON) FROM t_lbb UNION
SELECT CAST(_ltx AS JSON) FROM t_ltx UNION
SELECT CAST(_enu AS JSON) FROM t_enu UNION
SELECT CAST(_set AS JSON) FROM t_set UNION
SELECT CAST(_geo AS JSON) FROM t_geo UNION
SELECT CAST(_pnt AS JSON) FROM t_pnt UNION
SELECT CAST(_lst AS JSON) FROM t_lst UNION
SELECT CAST(_pol AS JSON) FROM t_pol UNION
SELECT CAST(_mpt AS JSON) FROM t_mpt UNION
SELECT CAST(_mls AS JSON) FROM t_mls UNION
SELECT CAST(_mpy AS JSON) FROM t_mpy UNION
SELECT CAST(_gco AS JSON) FROM t_gco UNION
SELECT CAST(_bit AS JSON) FROM t_bit UNION
SELECT CAST(_tin AS JSON) FROM t_tin UNION
SELECT CAST(_boo AS JSON) FROM t_boo UNION
SELECT CAST(_sms AS JSON) FROM t_sms UNION
SELECT CAST(_smu AS JSON) FROM t_smu UNION
SELECT CAST(_mes AS JSON) FROM t_mes UNION
SELECT CAST(_meu AS JSON) FROM t_meu UNION
SELECT CAST(_ins AS JSON) FROM t_ins UNION
SELECT CAST(_inu AS JSON) FROM t_inu UNION
SELECT CAST(_bis AS JSON) FROM t_bis UNION
SELECT CAST(_biu AS JSON) FROM t_biu UNION
SELECT CAST(_dec AS JSON) FROM t_dec UNION
SELECT CAST(_dou AS JSON) FROM t_dou UNION
SELECT CAST(_dat AS JSON) FROM t_dat UNION
SELECT CAST(_dtt AS JSON) FROM t_dtt UNION
SELECT CAST(_smp AS JSON) FROM t_smp UNION
SELECT CAST(_tim AS JSON) FROM t_tim UNION
SELECT CAST(_yea AS JSON) FROM t_yea UNION
SELECT CAST(_chr AS JSON) FROM t_chr UNION
SELECT CAST(_vch AS JSON) FROM t_vch UNION
SELECT CAST(_bin AS JSON) FROM t_bin UNION
SELECT CAST(_vbn AS JSON) FROM t_vbn UNION
SELECT CAST(_tbl AS JSON) FROM t_tbl UNION
SELECT CAST(_ttx AS JSON) FROM t_ttx UNION
SELECT CAST(_blb AS JSON) FROM t_blb UNION
SELECT CAST(_txt AS JSON) FROM t_txt UNION
SELECT CAST(_mbb AS JSON) FROM t_mbb UNION
SELECT CAST(_mtx AS JSON) FROM t_mtx UNION
SELECT CAST(_lbb AS JSON) FROM t_lbb UNION
SELECT CAST(_ltx AS JSON) FROM t_ltx UNION
SELECT CAST(_enu AS JSON) FROM t_enu UNION
SELECT CAST(_set AS JSON) FROM t_set UNION
SELECT CAST(_geo AS JSON) FROM t_geo UNION
SELECT CAST(_pnt AS JSON) FROM t_pnt UNION
SELECT CAST(_lst AS JSON) FROM t_lst UNION
SELECT CAST(_pol AS JSON) FROM t_pol UNION
SELECT CAST(_mpt AS JSON) FROM t_mpt UNION
SELECT CAST(_mls AS JSON) FROM t_mls UNION
SELECT CAST(_mpy AS JSON) FROM t_mpy UNION
SELECT CAST(_gco AS JSON) FROM t_gco UNION
SELECT _jsn FROM at;

# JSON Data Type Weigtage Function. This is used in
# first level and second level validation
#---------------------------------------------

CREATE FUNCTION GET_JSON_WEIGHT(json_type VARCHAR(100))
RETURNS INT DETERMINISTIC
RETURN CASE json_type
WHEN 'NULL' THEN 1
WHEN 'DECIMAL' THEN 2
WHEN 'DOUBLE' THEN 2
WHEN 'UNSIGNED INTEGER' THEN 2
WHEN 'INTEGER' THEN 2
WHEN 'BLOB' THEN 3
WHEN 'STRING' THEN 3
WHEN 'OBJECT' THEN 4
WHEN 'ARRAY' THEN 5
WHEN 'BOOLEAN' THEN 6
WHEN 'DATE' THEN 7
WHEN 'TIME' THEN 8
WHEN 'DATETIME' THEN 9
WHEN 'TIMESTAMP' THEN 9
WHEN 'OPAQUE' THEN 10
END;


# In this part comparision tests for each comparator
# for JSON vs JSON datatypes is present
#--------------------------------------------------------

--source suite/json/inc/json_vs_json_comparator.inc

# In this part comparision tests for each comparator
# for JSON vs Other datatypes is present
#--------------------------------------------------------
--source suite/json/inc/sql_vs_json_equalto.inc
--source suite/json/inc/sql_vs_json_lessthan.inc
--source suite/json/inc/sql_vs_json_greaterthan.inc
--source suite/json/inc/sql_vs_json_lessthanequalto.inc
--source suite/json/inc/sql_vs_json_greaterthanequalto.inc
--source suite/json/inc/sql_vs_json_notequal.inc
--source suite/json/inc/sql_vs_json_null_safe_equal_to.inc

drop table t_bit;
drop table t_tin;
drop table t_boo;
drop table t_sms;
drop table t_smu;
drop table t_mes;
drop table t_meu;
drop table t_ins;
drop table t_inu;
drop table t_bis;
drop table t_biu;
drop table t_dec;
drop table t_flo;
drop table t_dou;
drop table t_dat;
drop table t_dtt;
drop table t_smp;
drop table t_tim;
drop table t_yea;
drop table t_chr;
drop table t_vch;
drop table t_bin;
drop table t_vbn;
drop table t_tbl;
drop table t_ttx;
drop table t_blb;
drop table t_txt;
drop table t_mbb;
drop table t_mtx;
drop table t_lbb;
drop table t_ltx;
drop table t_enu;
drop table t_set;
drop table t_geo;
drop table t_pnt;
drop table t_lst;
drop table t_pol;
drop table t_mpt;
drop table t_mls;
drop table t_mpy;
drop table t_gco;
drop table jj;
drop table t;
drop table at;

drop function get_json_weight;

--echo #
--echo # Bug #34416304 WriteRowToChunk : Assertion `!thd->is_error()' failed
--echo #

CREATE TABLE t (
  `a` DOUBLE,
  `b` DATETIME
) engine=innodb;
INSERT INTO t VALUES();
INSERT INTO t VALUES();
INSERT INTO t VALUES();
INSERT INTO t VALUES();
INSERT INTO t VALUES();

--error ER_CANT_CREATE_GEOMETRY_OBJECT
SELECT * FROM t,
JSON_TABLE(
 '[{"w":[{"a":true},false],"e":"r"},"x","c",1]','$[*]'
COLUMNS(
 b VARCHAR(6595)
   EXISTS PATH '$[0].a'  ,
 a GEOMETRY
   EXISTS PATH '$.ba'
 )
) q;

DROP TABLE t;
