# Usage:
#
#  [--let $rpl_test_optimize_for_static_plugin_config = 1|0]
#  --source suite/rpl/t/rpl_semi_sync.test
#
# Parameters:
#
#  $rpl_test_optimize_for_static_plugin_config
#    When set to `1`, will run the contained test-cases with
#    `replication_optimize_for_static_plugin_config` option enabled.
#

# Want to skip this test from daily Valgrind execution
--source include/no_valgrind_without_big.inc
# Adding big test option for this test.
--source include/big_test.inc

source include/have_semisync_plugin.inc;
source include/rpl/init_source_replica.inc;

let $engine_type= InnoDB;

# Suppress warnings that might be generated during the test
connection master;
disable_query_log;
call mtr.add_suppression("Timeout waiting for reply of binlog");
call mtr.add_suppression("Read semi-sync reply");
call mtr.add_suppression("Unsafe statement written to the binary log using statement format since BINLOG_FORMAT = STATEMENT.");
call mtr.add_suppression("Semi-sync source failed on net_flush.. before waiting for replica reply");
call mtr.add_suppression("Source server does not support semi-sync");
call mtr.add_suppression("Semi-sync replica .* reply");
call mtr.add_suppression("Replica SQL.*Request to stop replica SQL Thread received while applying a group that has non-transactional changes; waiting for completion of the group");
call mtr.add_suppression(".*Got an error reading communication packets.*");
enable_query_log;
--source include/rpl/sync_to_replica.inc

# After fix of BUG#45848, semi-sync slave should not create any extra
# connections on source, save the count of connections before start
# semi-sync replica for comparison below.
connection master;
let $_connections_normal_slave= query_get_value(SHOW STATUS LIKE 'Threads_connected', Value, 1);

--echo #
--echo # Uninstall semi-sync plugins on source and replica
--echo #
connection slave;
source include/rpl/stop_replica.inc;
disable_warnings;
error 0,1305;
UNINSTALL PLUGIN rpl_semi_sync_replica;
error 0,1305;
UNINSTALL PLUGIN rpl_semi_sync_source;
enable_warnings;

connection master;
disable_warnings;
error 0,1305;
UNINSTALL PLUGIN rpl_semi_sync_replica;
error 0,1305;
UNINSTALL PLUGIN rpl_semi_sync_source;
enable_warnings;
--let $rpl_only_running_threads= 1
--let $rpl_no_start_slave= 1
--source include/rpl/reset.inc
--let $rpl_only_running_threads= 0

--echo #
--echo # Main test of semi-sync replication starts here
--echo #

source include/rpl/connection_source.inc;
let $value = query_get_value(show variables like 'rpl_semi_sync_source_enabled', Value, 1);
if ($value == No such row)
{
  disable_query_log;
  eval INSTALL PLUGIN rpl_semi_sync_source SONAME '$SEMISYNC_SOURCE_PLUGIN';
  set global rpl_semi_sync_source_timeout= 60000 /* 60s */;
  set global rpl_semi_sync_source_wait_point= AFTER_COMMIT;
  enable_query_log;
}

echo [ default state of semi-sync on source should be OFF ];
show variables like 'rpl_semi_sync_source_enabled';

echo [ enable semi-sync on source ];
set global rpl_semi_sync_source_enabled = 1;
show variables like 'rpl_semi_sync_source_enabled';

echo [ status of semi-sync on source should be ON even without any semi-sync slaves ];
show status like 'Rpl_semi_sync_source_clients';
show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_yes_tx';

--echo #
--echo # BUG#45672 Semisync repl: ActiveTranx:insert_tranx_node: transaction node allocation failed
--echo # BUG#45673 Semisync reports correct operation even if no slave is connected
--echo #

# BUG#45672 When semi-sync is enabled on source, it would allocate
# transaction node even without semi-sync replica connected, and would
# finally result in transaction node allocation error.
#
# Semi-sync source will pre-allocate 'max_connections' transaction
# nodes, so here we do more than that much transactions to check if it
# will fail or not.
# select @@global.max_connections + 1;
let $i= `select @@global.max_connections + 1`;
disable_query_log;
eval create table t1 (a int) engine=$engine_type;
while ($i)
{
  eval insert into t1 values ($i);
  dec $i;
}
drop table t1;
enable_query_log;

# BUG#45673
echo [ status of semi-sync on source should be OFF ];
show status like 'Rpl_semi_sync_source_clients';
show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_yes_tx';

if ($rpl_test_optimize_for_static_plugin_config == 1)
{
  SET GLOBAL replication_optimize_for_static_plugin_config = 1;
}

# reset binary logs and gtids and replica so the following test starts in a clean environment
--let $rpl_only_running_threads= 1
--let $rpl_no_start_slave= 1
--source include/rpl/reset.inc
--let $rpl_only_running_threads= 0

--echo #
--echo # INSTALL PLUGIN semi-sync on replica
--echo #

source include/rpl/connection_replica.inc;
let $value= query_get_value(show variables like 'rpl_semi_sync_replica_enabled', Value, 1);
if ($value == No such row)
{
  disable_query_log;
  eval INSTALL PLUGIN rpl_semi_sync_replica SONAME '$SEMISYNC_REPLICA_PLUGIN';
  enable_query_log;
}

echo [ default state of semi-sync on replica should be OFF ];
show variables like 'rpl_semi_sync_replica_enabled';

echo [ enable semi-sync on replica ];
set global rpl_semi_sync_replica_enabled = 1;
show variables like 'rpl_semi_sync_replica_enabled';
source include/rpl/start_replica.inc;

source include/rpl/connection_source.inc;
# NOTE: Rpl_semi_sync_source_clients will only be updated when
# semi-sync replica has started binlog dump request
let $status_var= Rpl_semi_sync_source_clients;
let $status_var_value= 1;
source include/wait_for_status_var.inc;

echo [ initial source state after the semi-sync replica connected ];
show status like 'Rpl_semi_sync_source_clients';
show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_no_tx';
show status like 'Rpl_semi_sync_source_yes_tx';

--let $yes_tx_before= query_get_value(SHOW STATUS LIKE 'Rpl_semi_sync_source_yes_tx', Value, 1)

replace_result $engine_type ENGINE_TYPE;
eval create table t1(a int) engine = $engine_type;

# wait for the ack to arrive
--let $yes_tx_after= `SELECT $yes_tx_before + 1`
--let $status_var= Rpl_semi_sync_source_yes_tx
--let $status_var_value= $yes_tx_after
--source include/wait_for_status_var.inc

echo [ source state after CREATE TABLE statement ];
show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_no_tx';
show status like 'Rpl_semi_sync_source_yes_tx';

# After fix of BUG#45848, semi-sync replica should not create any extra
# connections on source.
let $_connections_semisync_replica= query_get_value(SHOW STATUS LIKE 'Threads_connected', Value, 1);
replace_result $_connections_semisync_replica CONNECTIONS_SEMISYNC_SLAVE;
replace_result $_connections_normal_slave CONNECTIONS_NORMAL_SLAVE;
eval select $_connections_semisync_replica - $_connections_normal_slave as 'Should be 0';

--let $yes_tx_before= query_get_value(SHOW STATUS LIKE 'Rpl_semi_sync_source_yes_tx', Value, 1)

let $i=10;
--let $acks_to_wait= 0
echo [ insert records to table ];
disable_query_log;
while ($i)
{
  eval insert into t1 values ($i);
  dec $i;
  inc $acks_to_wait;
}
enable_query_log;

# Wait for acks from replica to avoid test failures on slow platforms.
--let $yes_tx_after= `SELECT $yes_tx_before + $acks_to_wait`
let $status_var= Rpl_semi_sync_source_yes_tx;
let $status_var_value= $yes_tx_after;
let $status_timeout= 2400;
let $status_fail_query= SHOW STATUS LIKE 'Rpl_semi_sync%';
source include/wait_for_status_var.inc;

echo [ source status after inserts ];
show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_no_tx';
show status like 'Rpl_semi_sync_source_yes_tx';

--source include/rpl/sync_to_replica.inc
echo [ on replica ];

echo [ replica status after replicated inserts ];
show status like 'Rpl_semi_sync_replica_status';

select count(distinct a) from t1;
select min(a) from t1;
select max(a) from t1;

--echo
--echo # BUG#50157
--echo # semi-sync replication crashes when replicating a transaction which
--echo # include 'CREATE TEMPORARY TABLE `MyISAM_t` SELECT * FROM `Innodb_t` ;

source include/rpl/connection_source.inc;
SET SESSION AUTOCOMMIT= 0;
CREATE TABLE t2(c1 INT) ENGINE=innodb;
--source include/rpl/sync_to_replica.inc

connection master;
BEGIN;
--echo
--echo # Even though it is in a transaction, this statement is binlogged into binlog
--echo # file immediately.
--disable_warnings
CREATE TEMPORARY TABLE t3 SELECT c1 FROM t2 where 1=1;
--enable_warnings
--echo
--echo # These statements will not be binlogged until the transaction is committed
INSERT INTO t2 VALUES(11);
INSERT INTO t2 VALUES(22);
COMMIT;

DROP TABLE t2, t3;
SET SESSION AUTOCOMMIT= 1;

# The temporary table above will be binlogged in statement mode only, resulting
# in different Rpl_semi_sync_source_yes_tx values in statement vs row/mixed. Reset it.
FLUSH STATUS;

--source include/rpl/sync_to_replica.inc


--echo #
--echo # Test semi-sync source will switch OFF after one transaction
--echo # timeout waiting for replica reply.
--echo #
connection slave;
source include/rpl/stop_replica.inc;

source include/rpl/connection_source.inc;
# Kill the dump thread(s) on source for previous replica connection
source include/rpl/stop_dump_threads.inc;
# Rpl_semi_sync_source_clients should be 0, once the dump thread(s) have exited.
let $status_var= Rpl_semi_sync_source_clients;
let $status_var_value= 0;
source include/wait_for_status_var.inc;

# The first semi-sync check should be on because after replica stop,
# there are no transactions on the source.
echo [ source status should be ON ];
show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_no_tx';
show status like 'Rpl_semi_sync_source_yes_tx';
show status like 'Rpl_semi_sync_source_clients';

echo [ semi-sync replication of these transactions will fail ];
insert into t1 values (500);

# Wait for the semi-sync replication of this transaction to timeout
let $status_var= Rpl_semi_sync_source_status;
let $status_var_value= OFF;
source include/wait_for_status_var.inc;

# The second semi-sync check should be off because one transaction
# times out during waiting.
echo [ source status should be OFF ];
show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_no_tx';
show status like 'Rpl_semi_sync_source_yes_tx';

# Semi-sync status on source is now OFF, so all these transactions
# will be replicated asynchronously.
let $i=10;
disable_query_log;
while ($i)
{
  eval delete from t1 where a=$i;
  dec $i;
}
enable_query_log;

insert into t1 values (100);

echo [ source status should be OFF ];
show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_no_tx';
show status like 'Rpl_semi_sync_source_yes_tx';

--echo #
--echo # Test semi-sync status on source will be ON again when replica catches up
--echo #

# Save the source position for later use.
save_master_pos;

source include/rpl/connection_replica.inc;
echo [ replica status should be OFF ];
show status like 'Rpl_semi_sync_replica_status';
source include/rpl/start_replica.inc;
sync_with_master;

echo [ replica status should be ON ];
show status like 'Rpl_semi_sync_replica_status';

select count(distinct a) from t1;
select min(a) from t1;
select max(a) from t1;

source include/rpl/connection_source.inc;
# Wait until source semi-sync status is on again after replica catches up
# to avoid test failures on slow platforms.
let $status_var= Rpl_semi_sync_source_status;
let $status_var_value= ON;
source include/wait_for_status_var.inc;

echo [ source status should be ON again after replica catches up ];
show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_no_tx';
show status like 'Rpl_semi_sync_source_yes_tx';
show status like 'Rpl_semi_sync_source_clients';

--echo #
--echo # Test disable/enable source semi-sync on the fly.
--echo #

drop table t1;
--source include/rpl/sync_to_replica.inc
echo [ on replica ];

source include/rpl/stop_replica.inc;

--echo #
--echo # Flush status
--echo #
connection master;
echo [ Semi-sync source status variables before FLUSH STATUS ];
SHOW STATUS LIKE 'Rpl_semi_sync_source_no_tx';
SHOW STATUS LIKE 'Rpl_semi_sync_source_yes_tx';
# Do not write the FLUSH STATUS to binlog, to make sure we'll get a
# clean status after this.
FLUSH NO_WRITE_TO_BINLOG STATUS;
echo [ Semi-sync source status variables after FLUSH STATUS ];
SHOW STATUS LIKE 'Rpl_semi_sync_source_no_tx';
SHOW STATUS LIKE 'Rpl_semi_sync_source_yes_tx';

source include/rpl/connection_source.inc;
source include/rpl/deprecated/show_binary_logs_vertical.inc;
show variables like 'rpl_semi_sync_source_enabled';

echo [ disable semi-sync on the fly ];
set global rpl_semi_sync_source_enabled=0;
show variables like 'rpl_semi_sync_source_enabled';
show status like 'Rpl_semi_sync_source_status';

echo [ enable semi-sync on the fly ];
set global rpl_semi_sync_source_enabled=1;
show variables like 'rpl_semi_sync_source_enabled';
show status like 'Rpl_semi_sync_source_status';

--echo #
--echo # Test RESET BINARY LOGS AND GTIDS/REPLICA
--echo #

source include/rpl/connection_replica.inc;
source include/rpl/start_replica.inc;

source include/rpl/connection_source.inc;
replace_result $engine_type ENGINE_TYPE;
eval create table t1 (a int) engine = $engine_type;
drop table t1;

--source include/rpl/sync_to_replica.inc
--replace_column 2 #
show status like 'Rpl_relay%';

echo [ test reset binary logs and gtids ];
source include/rpl/connection_source.inc;

reset binary logs and gtids;

show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_no_tx';
show status like 'Rpl_semi_sync_source_yes_tx';

source include/rpl/connection_replica.inc;
source include/rpl/stop_replica.inc;
reset replica;
reset binary logs and gtids;

source include/rpl/connection_source.inc;
# Kill the dump thread(s) on source for previous replica connection
source include/rpl/stop_dump_threads.inc;
# Rpl_semi_sync_source_clients should be 0, once the dump thread(s) have exited.
let $status_var= Rpl_semi_sync_source_clients;
let $status_var_value= 0;
source include/wait_for_status_var.inc;

connection slave;
source include/rpl/start_replica.inc;

source include/rpl/connection_source.inc;
# Wait for dump thread to start, Rpl_semi_sync_source_clients should be
# 1 after dump thread started.
let $status_var= Rpl_semi_sync_source_clients;
let $status_var_value= 1;
source include/wait_for_status_var.inc;

replace_result $engine_type ENGINE_TYPE;
eval create table t1 (a int) engine = $engine_type;
insert into t1 values (1);
insert into t1 values (2), (3);

--source include/rpl/sync_to_replica.inc
echo [ on replica ];
select * from t1;

source include/rpl/connection_source.inc;
# Wait until source semi-sync status is on again after replica catches up
# to avoid test failures on slow platforms.
let $status_var= Rpl_semi_sync_source_status;
let $status_var_value= ON;
source include/wait_for_status_var.inc;

echo [ source semi-sync status should be ON ];
show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_no_tx';
show status like 'Rpl_semi_sync_source_yes_tx';


--echo #
--echo # Start semi-sync replication without SUPER privilege
--echo #
--source include/rpl/reset.inc

source include/rpl/connection_source.inc;
# Kill the dump thread on source for previous replica connection
source include/rpl/stop_dump_threads.inc;
# Rpl_semi_sync_source_clients should be 0, once dump thread(s) have exited
let $status_var= Rpl_semi_sync_source_clients;
let $status_var_value= 0;
source include/wait_for_status_var.inc;

# Do not binlog the following statement because it will generate
# different events for ROW and STATEMENT format
set sql_log_bin=0;
create user rpl@127.0.0.1 identified by 'rpl';
grant replication slave on *.* to rpl@127.0.0.1;
flush privileges;
set sql_log_bin=1;
# Prove of bug#77732 fixes:
# Created user's privileges - REPLICATION and SLAVE -
# alone must suffice to successful connecting even with show handler
# not compatible to 5.6.

source include/rpl/connection_replica.inc;
create user rpl@127.0.0.1 identified by 'rpl';
grant replication slave on *.* to rpl@127.0.0.1;
flush privileges;
--replace_column 2 ####
CHANGE REPLICATION SOURCE to SOURCE_USER='rpl',SOURCE_PASSWORD='rpl', GET_SOURCE_PUBLIC_KEY=1;
source include/rpl/start_replica.inc;
show status like 'Rpl_semi_sync_replica_status';
source include/rpl/connection_source.inc;

# bug#77732 related: reconnecting to the source while the show handler
# is compatible with 5.6
--source include/rpl/connection_replica.inc
source include/rpl/stop_replica.inc;

--source include/rpl/connection_source.inc
# Added for Bug#19372027: rpl_semi_sync fails sporadically on trunk
# To ensure that source knows that the replica is stopped
Insert into t1 values (4);
# wait for semi sync binlog dump thread to stop
let $status_var= Rpl_semi_sync_source_clients;
let $status_var_value= 0;
source include/wait_for_status_var.inc;
show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_clients';

source include/rpl/connection_replica.inc;
source include/rpl/start_replica.inc;
show status like 'Rpl_semi_sync_replica_status';

--source include/rpl/connection_source.inc
# bug#77732 cleanup
# Added for Bug#19372027:rpl_semi_sync fails sporadically on trunk
let $status_var= Rpl_semi_sync_source_status;
let $status_var_value= ON;
source include/wait_for_status_var.inc;
show status like 'Rpl_semi_sync_source_status';

# Wait for the semi-sync binlog dump thread to start
let $status_var= Rpl_semi_sync_source_clients;
let $status_var_value= 1;
source include/wait_for_status_var.inc;
echo [ source semi-sync should be ON ];
show status like 'Rpl_semi_sync_source_clients';
show status like 'Rpl_semi_sync_source_no_tx';
show status like 'Rpl_semi_sync_source_yes_tx';
insert into t1 values (5);
insert into t1 values (6);

# Wait for acks from replica to avoid test failures on slow platforms.
let $status_var= Rpl_semi_sync_source_yes_tx;
let $status_var_value= 2;
source include/wait_for_status_var.inc;

echo [ source semi-sync should be ON ];
show status like 'Rpl_semi_sync_source_clients';
show status like 'Rpl_semi_sync_source_status';
show status like 'Rpl_semi_sync_source_no_tx';
show status like 'Rpl_semi_sync_source_yes_tx';

# Bug#31327337: SUBSYSTEM NAME FOR SEMISYNC ERRORS IS [SERVER] - pt2
select subsystem, error_code
  from performance_schema.error_log
 where error_code="MY-011170" limit 1;

--echo #
--echo # Test semi-sync replica connect to non-semi-sync source
--echo #

# Disable semi-sync on source
--source include/rpl/sync_to_replica.inc
echo [ on replica ];
source include/rpl/stop_replica.inc;
SHOW STATUS LIKE 'Rpl_semi_sync_replica_status';

source include/rpl/connection_source.inc;
# Kill the dump thread(s) on source for previous replica connection
source include/rpl/stop_dump_threads.inc;
# Rpl_semi_sync_source_clients should be 0, once dump thread(s) have exited
let $status_var= Rpl_semi_sync_source_clients;
let $status_var_value= 0;
source include/wait_for_status_var.inc;

echo [ Semi-sync status on source should be ON ];
show status like 'Rpl_semi_sync_source_clients';
show status like 'Rpl_semi_sync_source_status';
set global rpl_semi_sync_source_enabled= 0;

source include/rpl/connection_replica.inc;
SHOW VARIABLES LIKE 'rpl_semi_sync_replica_enabled';
source include/rpl/start_replica.inc;

source include/rpl/connection_source.inc;
insert into t1 values (8);
let $status_var= Rpl_semi_sync_source_clients;
let $status_var_value= 1;
source include/wait_for_status_var.inc;
echo [ source semi-sync clients should be 1, status should be OFF ];
show status like 'Rpl_semi_sync_source_clients';
show status like 'Rpl_semi_sync_source_status';
--source include/rpl/sync_to_replica.inc
echo [ on replica ];
show status like 'Rpl_semi_sync_replica_status';

# Uninstall semi-sync plugin on source
connection slave;
source include/rpl/stop_replica.inc;
connection master;
echo [ on source ];
--source include/rpl/stop_dump_threads.inc
if ($rpl_test_optimize_for_static_plugin_config == 1)
{
  SET GLOBAL replication_optimize_for_static_plugin_config = 0;
}
UNINSTALL PLUGIN rpl_semi_sync_source;
enable_query_log;
SHOW VARIABLES LIKE 'rpl_semi_sync_source_enabled';

source include/rpl/connection_replica.inc;
SHOW VARIABLES LIKE 'rpl_semi_sync_replica_enabled';
source include/rpl/start_replica.inc;

source include/rpl/connection_source.inc;
insert into t1 values (10);
--source include/rpl/sync_to_replica.inc
echo [ on replica ];
SHOW STATUS LIKE 'Rpl_semi_sync_replica_status';

--echo #
--echo # Test non-semi-sync replica connect to semi-sync source
--echo #

connection master;
replace_result $SEMISYNC_SOURCE_PLUGIN SEMISYNC_SOURCE_PLUGIN;
eval INSTALL PLUGIN rpl_semi_sync_source SONAME '$SEMISYNC_SOURCE_PLUGIN';
if ($rpl_test_optimize_for_static_plugin_config == 1)
{
  SET GLOBAL replication_optimize_for_static_plugin_config = 1;
}
set global rpl_semi_sync_source_timeout= 60000; /* 60s */
set global rpl_semi_sync_source_enabled= 1;

source include/rpl/connection_replica.inc;
source include/rpl/stop_replica.inc;
SHOW STATUS LIKE 'Rpl_semi_sync_replica_status';

echo [ uninstall semi-sync replica plugin ];
UNINSTALL PLUGIN rpl_semi_sync_replica;
SHOW VARIABLES LIKE 'rpl_semi_sync_replica_enabled';
source include/rpl/start_replica.inc;
SHOW STATUS LIKE 'Rpl_semi_sync_replica_status';
source include/rpl/stop_replica.inc;

echo [ reinstall semi-sync replica plugin and disable semi-sync ];
replace_result $SEMISYNC_REPLICA_PLUGIN SEMISYNC_REPLICA_PLUGIN;
eval INSTALL PLUGIN rpl_semi_sync_replica SONAME '$SEMISYNC_REPLICA_PLUGIN';
set global rpl_semi_sync_replica_enabled= 0;
SHOW VARIABLES LIKE 'rpl_semi_sync_replica_enabled';
SHOW STATUS LIKE 'Rpl_semi_sync_replica_status';
source include/rpl/start_replica.inc;
SHOW STATUS LIKE 'Rpl_semi_sync_replica_status';

--echo #
--echo # Clean up
--echo #
if ($rpl_test_optimize_for_static_plugin_config == 1)
{
  connection master;
  SET GLOBAL replication_optimize_for_static_plugin_config = 0;
}
--source include/rpl/uninstall_semisync.inc

connection slave;
--source include/rpl/stop_replica.inc
--replace_column 2 ####
CHANGE REPLICATION SOURCE to SOURCE_USER='root',SOURCE_PASSWORD='', GET_SOURCE_PUBLIC_KEY=0;
source include/rpl/start_replica.inc;

connection master;
drop table t1;
--source include/rpl/sync_to_replica.inc

connection master;
drop user rpl@127.0.0.1;
flush privileges;
--source include/rpl/deinit.inc
