/*****************************************************************************

Copyright (c) 1997, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/

/** @file include/row0sel.ic
 Select

 Created 12/19/1997 Heikki Tuuri
 *******************************************************/

#include "que0que.h"

/** Gets the plan node for the nth table in a join.
 @return plan node */
static inline plan_t *sel_node_get_nth_plan(
    sel_node_t *node, /*!< in: select node */
    ulint i)          /*!< in: get ith plan node */
{
  ut_ad(i < node->n_tables);

  return (node->plans + i);
}

/** Resets the cursor defined by sel_node to the SEL_NODE_OPEN state, which
 means that it will start fetching from the start of the result set again,
 regardless of where it was before, and it will set intention locks on the
 tables. */
static inline void sel_node_reset_cursor(
    sel_node_t *node) /*!< in: select node */
{
  node->state = SEL_NODE_OPEN;
}

/** Performs an execution step of an open or close cursor statement node.
 @return query thread to run next or NULL */
static inline que_thr_t *open_step(que_thr_t *thr) /*!< in: query thread */
{
  sel_node_t *sel_node;
  open_node_t *node;
  ulint err;

  ut_ad(thr);

  node = (open_node_t *)thr->run_node;
  ut_ad(que_node_get_type(node) == QUE_NODE_OPEN);

  sel_node = node->cursor_def;

  err = DB_SUCCESS;

  if (node->op_type == ROW_SEL_OPEN_CURSOR) {
    /*          if (sel_node->state == SEL_NODE_CLOSED) { */

    sel_node_reset_cursor(sel_node);
    /*          } else {
    err = DB_ERROR;
    } */
  } else {
    if (sel_node->state != SEL_NODE_CLOSED) {
      sel_node->state = SEL_NODE_CLOSED;
    } else {
      err = DB_ERROR;
    }
  }

  if (err != DB_SUCCESS) {
    /* SQL error detected */
    fprintf(stderr, "SQL error %lu\n", (ulong)err);

    ut_error;
  }

  thr->run_node = que_node_get_parent(node);

  return (thr);
}

/** Searches for rows in the database. This is used in the interface to
MySQL. This function opens a cursor, and also implements fetch next
and fetch prev. NOTE that if we do a search with a full key value
from a unique index (ROW_SEL_EXACT), then we will not store the cursor
position and fetch next or fetch prev must not be tried to the cursor!

@param[out]     buf             buffer for the fetched row in MySQL format
@param[in]      mode            search mode PAGE_CUR_L
@param[in,out]  prebuilt        prebuilt struct for the table handler;
                                this contains the info to search_tuple,
                                index; if search tuple contains 0 field then
                                we position the cursor at start or the end of
                                index, depending on 'mode'
@param[in]      match_mode      0 or ROW_SEL_EXACT or ROW_SEL_EXACT_PREFIX
@param[in]      direction       0 or ROW_SEL_NEXT or ROW_SEL_PREV;
                                Note: if this is != 0, then prebuilt must has a
                                pcur with stored position! In opening of a
                                cursor 'direction' should be 0.
@return DB_SUCCESS, DB_RECORD_NOT_FOUND, DB_END_OF_INDEX, DB_DEADLOCK,
DB_LOCK_TABLE_FULL, DB_CORRUPTION, or DB_TOO_BIG_RECORD */
static inline dberr_t row_search_for_mysql(byte *buf, page_cur_mode_t mode,
                                           row_prebuilt_t *prebuilt,
                                           ulint match_mode, ulint direction) {
  if (!prebuilt->table->is_intrinsic()) {
    return (row_search_mvcc(buf, mode, prebuilt, match_mode, direction));
  } else {
    return (row_search_no_mvcc(buf, mode, prebuilt, match_mode, direction));
  }
}
