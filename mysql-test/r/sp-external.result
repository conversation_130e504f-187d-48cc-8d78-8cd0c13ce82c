#
# WL#15254: SQL syntax extensions to support external language routines
#
#
# Tests for LANGUAGE clause
#
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE SQL
BEGIN RETURN a-1; END //
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_name = "foo" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	SQL	NULL	SQL	SQL	BEGIN RETURN a-1; END
DROP FUNCTION foo//
# SQL is default
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER DETERMINISTIC
BEGIN RETURN a-1; END //
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS where Language like "%JAVA%"//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_name = "foo" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION JAVASCRIPT(x INTEGER) RETURNS INTEGER DETERMINISTIC
LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION JAVASCRIPT//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
JAVASCRIPT	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `JAVASCRIPT`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'JAVASCRIPT'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	JAVASCRIPT	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
JAVASCRIPT	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT JAVASCRIPT(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION JAVASCRIPT//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE PYTHON
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE PYTHON
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	PYTHON	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	PYTHON	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE RUBY
AS $$
x-1
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE RUBY
AS $$
x-1
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	RUBY	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	RUBY	SQL	
x-1

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
# Test lower-case
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE javascript
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
# Test mixed-case
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE jAvaScRIpt
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
# Any valid identifier can be used for language name
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE JAVA1
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVA1
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVA1	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVA1	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE $NOLANG
AS $$
return x-1;
$$
//
Warnings:
Warning	1681	'$ as the first character of an unquoted identifier' is deprecated and will be removed in a future release.
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE $NOLANG
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	$NOLANG	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	$NOLANG	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC
LANGUAGE $$JAVASCRIPT
AS $$
return x-1;
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$JAVASCRIPT
AS $$
return x-1;
$$' at line 2
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE 123j
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE 123J
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	123J	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	123J	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE __
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE __
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	__	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	__	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC
LANGUAGE "JAVASCRIPT"
AS $$
return x-1;
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '"JAVASCRIPT"
AS $$
return x-1;
$$' at line 2
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC
LANGUAGE 'JAVASCRIPT'
AS $$
return x-1;
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ''JAVASCRIPT'
AS $$
return x-1;
$$' at line 2
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE " "
AS $$
return x-1;
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" "
AS $$
return x-1;
$$' at line 1
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE J?
AS $$
return x-1;
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '?
AS $$
return x-1;
$$' at line 1
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC
LANGUAGE \JAVASCRIPT
AS $$
return x-1;
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '\JAVASCRIPT
AS $$
return x-1;
$$' at line 2
# An identifier may not consist solely of digits
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE 123
AS $$
return x-1;
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '123
AS $$
return x-1;
$$' at line 1
# MySQL allows multiple clauses for the same characteristics
# (This is not according to SQL standard). Last one will take effect.
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER
LANGUAGE JAVASCRIPT DETERMINISTIC LANGUAGE SQL
BEGIN RETURN a-1; END //
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
LANGUAGE SQL DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
# Multiple languages in one clause is not allowed
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
LANGUAGE SQL DETERMINISTIC LANGUAGE JAVASCRIPT PYTHON
AS $$
return x-1;
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS $$
return x-1;
$$' at line 3
Check "weird" white space before and after JAVASCRIPT.
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
LANGUAGE
JAVASCRIPT
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
LANGUAGE	
JAVASCRIPT
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
LANGUAGE	  	 JAVASCRIPT  DETERMINISTIC	
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
LANGUAGE
-- Comment
JAVASCRIPT
-- Comment
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
The language will here be interpreted to be BEGIN
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE
BEGIN RETURN a-1; END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '; END' at line 3
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER
LANGUAGE SQL DETERMINISTIC LANGUAGE
BEGIN RETURN a-1; END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '; END' at line 3
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE
BEGIN a-1; END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '-1; END' at line 3
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE
AS $$
return x-1;
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS $$
return x-1;
$$' at line 3
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
LANGUAGE JAVASCRIPT DETERMINISTIC LANGUAGE
AS $$
return x-1;
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS $$
return x-1;
$$' at line 3
#Combinations with other characteristics
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC NO SQL
LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    NO SQL
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER DETERMINISTIC NO SQL
LANGUAGE SQL
BEGIN RETURN a-1; END //
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE SQL NO SQL
BEGIN RETURN a-1; END //
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE JAVASCRIPT
CONTAINS SQL
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC READS SQL DATA
LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    READS SQL DATA
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE JAVASCRIPT
MODIFIES SQL DATA
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    MODIFIES SQL DATA
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE SQL
CONTAINS SQL
BEGIN RETURN a-1; END //
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER DETERMINISTIC READS SQL DATA
LANGUAGE SQL
BEGIN RETURN a-1; END //
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE SQL
MODIFIES SQL DATA
BEGIN RETURN a-1; END //
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER NOT DETERMINISTIC LANGUAGE SQL
BEGIN RETURN a-1; END //
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER NOT DETERMINISTIC
LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
# This is not SQL
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE SQL
AS $$ return a-1; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$ return a-1; $$' at line 3
# This is not Javascript
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
BEGIN RETURN a-1; END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'END' at line 3
# Repeat test cases for PROCEDURE
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE SQL
BEGIN DECLARE b INTEGER DEFAULT a; END //
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_name = "bar" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		SQL	NULL	SQL	SQL	BEGIN DECLARE b INTEGER DEFAULT a; END
DROP PROCEDURE bar//
# SQL is default
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC
BEGIN DECLARE b INTEGER DEFAULT a; END //
DROP PROCEDURE bar//
# Currently, no other language than SQL is supported
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS where Language like "%JAVA%"//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_name = "bar" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP PROCEDURE bar//
CREATE PROCEDURE JAVASCRIPT(a INTEGER) DETERMINISTIC
LANGUAGE JAVASCRIPT
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure JAVASCRIPT//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
JAVASCRIPT	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `JAVASCRIPT`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'JAVASCRIPT'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	JAVASCRIPT	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
JAVASCRIPT	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL JAVASCRIPT(2)//
ERROR HY000: Language component: Not available.
DROP procedure JAVASCRIPT//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE PYTHON
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE PYTHON
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	PYTHON	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	PYTHON	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE RUBY
AS $$ b = a $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE RUBY
AS $$ b = a $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	RUBY	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	RUBY	SQL	 b = a 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
# Test lower-case
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE javascript
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
# Test mixed-case
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE jAvaScRIpt
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
# Any valid identifier can be used for language name
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE JAVA1
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVA1
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVA1	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVA1	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE $NOLANG
AS $$ let b = a; $$ //
Warnings:
Warning	1681	'$ as the first character of an unquoted identifier' is deprecated and will be removed in a future release.
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE $NOLANG
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	$NOLANG	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	$NOLANG	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE $$JAVASCRIPT
AS $$ let b = a; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$JAVASCRIPT
AS $$ let b = a; $$' at line 1
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE 123j
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE 123J
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	123J	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	123J	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE __
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE __
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	__	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	__	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE "JAVASCRIPT"
AS $$ let b = a; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '"JAVASCRIPT"
AS $$ let b = a; $$' at line 1
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE 'JAVASCRIPT'
AS $$ let b = a; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ''JAVASCRIPT'
AS $$ let b = a; $$' at line 1
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE " "
AS $$ let b = a; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" "
AS $$ let b = a; $$' at line 1
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE J?
AS $$ let b = a; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '?
AS $$ let b = a; $$' at line 1
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE \JAVASCRIPT
AS $$ let b = a; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '\JAVASCRIPT
AS $$ let b = a; $$' at line 1
# An identifier may not consist solely of digits
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE 123
AS $$ let b = a; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '123
AS $$ let b = a; $$' at line 1
# MySQL allows multiple clauses for the same characteristics
# (This is not according to SQL standard). Last one will take effect.
CREATE PROCEDURE bar(a INTEGER)
LANGUAGE JAVASCRIPT DETERMINISTIC LANGUAGE SQL
BEGIN DECLARE b INTEGER DEFAULT a; END //
DROP PROCEDURE bar//
CREATE PROCEDURE bar(a INTEGER)
LANGUAGE SQL DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
# Multiple languages in one clause is not allowed
CREATE PROCEDURE bar(a INTEGER)
LANGUAGE SQL DETERMINISTIC LANGUAGE JAVASCRIPT PYTHON
AS $$ b = a $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS $$ b = a $$' at line 3
Check "weird" white space before and after JAVASCRIPT.
CREATE PROCEDURE bar(a INTEGER)
LANGUAGE
JAVASCRIPT
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
LANGUAGE	
JAVASCRIPT
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
LANGUAGE	  	 JAVASCRIPT  DETERMINISTIC	
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
LANGUAGE
-- Comment
JAVASCRIPT
-- Comment
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
The language will here be interpreted to be BEGIN
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE
BEGIN DECLARE b INTEGER DEFAULT a; END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'DECLARE b INTEGER DEFAULT a; END' at line 3
CREATE PROCEDURE bar(a INTEGER)
LANGUAGE SQL DETERMINISTIC LANGUAGE
BEGIN DECLARE b INTEGER DEFAULT a; END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'DECLARE b INTEGER DEFAULT a; END' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE
BEGIN a-1; END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '-1; END' at line 3
#Combinations with other characteristics
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC NO SQL
LANGUAGE JAVASCRIPT
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    NO SQL
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC NO SQL
LANGUAGE SQL
BEGIN DECLARE b INTEGER DEFAULT a; END //
DROP PROCEDURE bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE SQL NO SQL
BEGIN DECLARE b INTEGER DEFAULT a; END //
DROP PROCEDURE bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE JAVASCRIPT
CONTAINS SQL
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC READS SQL DATA
LANGUAGE JAVASCRIPT
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    READS SQL DATA
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE JAVASCRIPT
MODIFIES SQL DATA
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    MODIFIES SQL DATA
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE SQL
CONTAINS SQL
BEGIN DECLARE b INTEGER DEFAULT a; END //
DROP PROCEDURE bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC READS SQL DATA
LANGUAGE SQL
BEGIN DECLARE b INTEGER DEFAULT a; END //
DROP PROCEDURE bar//
CREATE PROCEDURE bar(a INTEGER) DETERMINISTIC LANGUAGE SQL
MODIFIES SQL DATA
BEGIN DECLARE b INTEGER DEFAULT a; END //
DROP PROCEDURE bar//
CREATE PROCEDURE bar(a INTEGER) NOT DETERMINISTIC LANGUAGE SQL
BEGIN DECLARE b INTEGER DEFAULT a; END //
DROP PROCEDURE bar//
CREATE PROCEDURE bar(a INTEGER) NOT DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$ let b = a; $$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    LANGUAGE JAVASCRIPT
AS $$ let b = a; $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b = a; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
# This is not SQL
CREATE PROCEDURE bar(a INTEGER)
LANGUAGE SQL DETERMINISTIC LANGUAGE SQL
$$ let b = a; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$ let b = a; $$' at line 3
# This is not Javascript
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
BEGIN DECLARE b INTEGER DEFAULT a; END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'END' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE SQL
BEGIN DECLARE b INTEGER DEFAULT a; END //
# Changing language will not be allowed
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER DETERMINISTIC LANGUAGE SQL
BEGIN RETURN a-1; END //
ALTER FUNCTION foo LANGUAGE JAVASCRIPT //
ERROR HY000: Altering the language of an existing routine is not possible.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS int
    DETERMINISTIC
BEGIN RETURN a-1; END	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
ALTER FUNCTION foo LANGUAGE SQL NO SQL //
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS int
    NO SQL
    DETERMINISTIC
BEGIN RETURN a-1; END	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
ALTER FUNCTION foo LANGUAGE NO SQL //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1
DROP FUNCTION foo //
ALTER PROCEDURE bar LANGUAGE JAVASCRIPT //
ERROR HY000: Altering the language of an existing routine is not possible.
ALTER PROCEDURE bar LANGUAGE SQL NO SQL //
SHOW CREATE PROCEDURE bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    NO SQL
    DETERMINISTIC
BEGIN DECLARE b INTEGER DEFAULT a; END	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
ALTER PROCEDURE bar LANGUAGE NO SQL //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1
DROP PROCEDURE bar //
#
# Tests for inline code in external language
#
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS 'return x-1;' //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$return x-1;$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	return x-1;
SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS "return x-1;" //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$return x-1;$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	return x-1;
SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS '
  return x-1;
' //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
  return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
  return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
"
   return x-1;
" //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
   return x-1;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
   return x-1;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
Routine body must come after characteristics
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
AS $$
return x-1;
$$
DETERMINISTIC LANGUAGE JAVASCRIPT
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$
return x-1;
$$
DETERMINISTIC LANGUAGE JAVASCRIPT' at line 2
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
'
  return "$$" + a + "$$";
'
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
  return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
  return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$code$
return "$$" + a + "$$";
$code$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$coDE$
return "$$" + a + "$$";
$coDE$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$CODE$
return "$$" + a + "$$";
$CODE$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$tag$
return "$$" + a + "$$";
$tag$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$TAG$
return "$$" + a + "$$";
$TAG$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$NULL$
return "$$" + a + "$$";
$NULL$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$abdc_1234$
return "$$" + a + "$$";
$abdc_1234$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$_a$
return "$$" + a + "$$";
$_a$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$_$
return "$$" + a + "$$";
$_$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$0$
return "$$" + a + "$$";
$0$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$100$
return "$$" + a + "$$";
$100$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$12345678901234$
return "$$" + a + "$$";
$12345678901234$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM$
return "$$" + a + "$$";
$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$¡$
return "$$" + a + "$$";
$¡$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM¡™£¢∞§¶•ªº–≠œ∑´®†¥¨ˆøπ“‘«åß∂ƒ©˙∆˚¬…æΩ≈ç√∫µ≤≥÷⁄€‹›ﬁﬂ‡°·‚—±ÅÍÎÏ˝ÓÔÒÚÆ¸˛Ç◊ıÂ¯˘¿$
return "$$" + a + "$$";
$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM¡™£¢∞§¶•ªº–≠œ∑´®†¥¨ˆøπ“‘«åß∂ƒ©˙∆˚¬…æΩ≈ç√∫µ≤≥÷⁄€‹›ﬁﬂ‡°·‚—±ÅÍÎÏ˝ÓÔÒÚÆ¸˛Ç◊ıÂ¯˘¿$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$$" + a + "$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS	      $$
const $x = 3;
const $y$ = 4;
return $x + $y$ + a;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
const $x = 3;
const $y$ = 4;
return $x + $y$ + a;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
const $x = 3;
const $y$ = 4;
return $x + $y$ + a;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$code$
const x$$ = 3;
const $y$$z = 4;
return x$$ + $y$$z + a;
$code$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
const x$$ = 3;
const $y$$z = 4;
return x$$ + $y$$z + a;
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
const x$$ = 3;
const $y$$z = 4;
return x$$ + $y$$z + a;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
$$
return x-1;
$$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$
return x-1;
$$' at line 3
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE 'return x-1;' //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ''return x-1;'' at line 2
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE return x-1 //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'return x-1' at line 2
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC AS LANGUAGE JAVASCRIPT
"return x-1;" //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'LANGUAGE JAVASCRIPT
"return x-1;"' at line 2
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
$code$
return "$$" + a + "$$";
$code$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$code$
return "$$" + a + "$$";
$code$' at line 3
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $
return x-1;
$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$
return x-1;
$' at line 3
# Test conflicting dollar quotes
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
return "$$" + a + "$$";
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" + a + "$$";
$$' at line 4
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $code$
return "$code$" + a + "$code$";
$code$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" + a + "$code$";
$code$' at line 4
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
return x-1;
$code$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$
return x-1;
$code$' at line 3
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$code$
return "$$" + a + "$$";
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$code$
return "$$" + a + "$$";
$$' at line 4
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$Code$
return "$$" + a + "$$";
$CODE$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$Code$
return "$$" + a + "$$";
$CODE$' at line 4
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$CodE$
return "$$" + a + "$$";
$code$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$CodE$
return "$$" + a + "$$";
$code$' at line 4
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS	      $$
const $x = 3;
const $y$ = 4;
return $x + $y$ + a;
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$
const $x = 3;
const $y$ = 4;
return $x + $y$ + a' at line 3
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$code$
const x$$ = 3;
const $y$$z = 4;
return x$$ + $y$$z + a;
$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$code$
const x$$ = 3;
const $y$$z = 4;
return x$$ + $y$$z + a;
$' at line 4
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM™∞•–≠∑†π“‘∂ƒ˙∆˚…Ω≈√∫≤≥⁄€‹›ﬁﬂ‡‚—˝˛◊ı˘$
return "$$" + a + "$$";
$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM™∞•–≠∑†π“‘∂ƒ˙∆˚…Ω≈√∫≤≥⁄€‹›ﬁﬂ‡‚—˝˛◊ı˗$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM™∞•–≠â' at line 4
Check cases with invalid dollar tags
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $+a$
return x-1;
$+a$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$+a$
return x-1;
$+a$' at line 3
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $a-$
return x-1;
$a-$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$a-$
return x-1;
$a-$' at line 3
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $a(b$
return x-1;
$a(b$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$a(b$
return x-1;
$a(b$' at line 3
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $a~b$
return x-1;
$a~b$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$a~b$
return x-1;
$a~b$' at line 3
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $ $
return x-1;
$ $ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$ $
return x-1;
$ $' at line 3
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $I'm$
  return x-1;
$I'm$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$I'm$
  return x-1;
$I'm$' at line 3
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $'Hi'$
return x-1;
$'Hi'$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$'Hi'$
return x-1;
$'Hi'$' at line 3
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $a"b"$
return x-1;
$a"b"$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$a"b"$
return x-1;
$a"b"$' at line 3
# Test multi-byte characters in code (∂ is 3 bytes in utf8mb3)
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
return "∂" + a + "$∂$";
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return "∂" + a + "$∂$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "∂" + a + "$∂$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $code$
return "∂" + a + "$∂$$";
$code$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "∂" + a + "$∂$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "∂" + a + "$∂$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $code$
return "∂" + a + "$∂$$∂";
$code$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "∂" + a + "$∂$$∂";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "∂" + a + "$∂$$∂";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$∂
return "∂" + a + "$∂$";
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$∂
return "∂" + a + "$∂$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	∂
return "∂" + a + "$∂$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
Test multi-byte characters in quote tags
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $∂$
return "∂" + a + "∂$";
$∂$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return "∂" + a + "∂$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "∂" + a + "∂$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $co∂e$
return "∂" + a + "$∂$$";
$co∂e$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
return "∂" + a + "$∂$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "∂" + a + "$∂$$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $😀$
return "😀$😔" + a + "😀$😔$";
$😀$
//
Warnings:
Warning	1300	Cannot convert string '\x0Aretur...' from utf8mb4 to utf8mb3
Warning	1300	Cannot convert string '\x0Aretur...' from utf8mb4 to utf8mb3
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return "😀$😔" + a + "😀$😔$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "?$?" + a + "?$?$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $😀😔$
return "😀$😔" + a + "😀$😔$";
$😀😔$
//
Warnings:
Warning	1300	Cannot convert string '\x0Aretur...' from utf8mb4 to utf8mb3
Warning	1300	Cannot convert string '\x0Aretur...' from utf8mb4 to utf8mb3
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return "😀$😔" + a + "😀$😔$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "?$?" + a + "?$?$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $x😀$
return "$x😀😔" + a + "$x😀😔$";
$x😀$
//
Warnings:
Warning	1300	Cannot convert string '\x0Aretur...' from utf8mb4 to utf8mb3
Warning	1300	Cannot convert string '\x0Aretur...' from utf8mb4 to utf8mb3
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return "$x😀😔" + a + "$x😀😔$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$x??" + a + "$x??$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $😀x$
return "$😀x😔$" + a + "$😀😔x$";
$😀x$
//
Warnings:
Warning	1300	Cannot convert string '\x0Aretur...' from utf8mb4 to utf8mb3
Warning	1300	Cannot convert string '\x0Aretur...' from utf8mb4 to utf8mb3
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return "$😀x😔$" + a + "$😀😔x$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$?x?$" + a + "$??x$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $x😀y$
return "$x😀😔y$" + a + "$x😀y😔$";
$x😀y$
//
Warnings:
Warning	1300	Cannot convert string '\x0Aretur...' from utf8mb4 to utf8mb3
Warning	1300	Cannot convert string '\x0Aretur...' from utf8mb4 to utf8mb3
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return "$x😀😔y$" + a + "$x😀y😔$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "$x??y$" + a + "$x?y?$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $x😀y😔$
return "😀😔$" + a + "😀$😔$";
$x😀y😔$
//
Warnings:
Warning	1300	Cannot convert string '\x0Aretur...' from utf8mb4 to utf8mb3
Warning	1300	Cannot convert string '\x0Aretur...' from utf8mb4 to utf8mb3
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(a INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
return "😀😔$" + a + "😀$😔$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	
return "??$" + a + "?$?$";

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $😀😔$
return "😀$😔" + a + "😀$😔$";
$😔😀$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$??$
return "?$?" + a + "?$?$";
$??$' at line 3
# Escape sequences is not supported in dollar quoted strings
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
return "\$$" + a + "\$code$";
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" + a + "\$code$";
$$' at line 4
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $code$
return "\$$" + a + "\$code$";
$code$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '";
$code$' at line 4
# Check some variants that should give external parse error
# if the language is supported
CREATE FUNCTION foo(x INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $code$ rtrn "$$" + x + "$foo$"; $code$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS varchar(20) CHARSET utf8mb4
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$ rtrn "$$" + x + "$foo$"; $JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	varchar	EXTERNAL	NULL	JAVASCRIPT	SQL	 rtrn "$$" + x + "$foo$"; 
SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
CREATE FUNCTION foo(x INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
const $x = x,
const $y = 1
return $x + $y;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION foo//
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
foo	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `foo`(x INTEGER) RETURNS int
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
const $x = x,
const $y = 1
return $x + $y;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW FUNCTION STATUS WHERE Name = 'foo'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	foo	FUNCTION	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
foo	FUNCTION	int	EXTERNAL	NULL	JAVASCRIPT	SQL	
const $x = x,
const $y = 1
return $x + $y;

SELECT foo(2)//
ERROR HY000: Language component: Not available.
DROP FUNCTION foo//
# Multiple occurrences of routine body is not allowed
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS 'return "$$" + a + "$foo$";'
AS $$ return a-1; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS $$ return a-1; $$' at line 4
CREATE FUNCTION foo(a INTEGER) RETURNS VARCHAR(20)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $code$ return "$$" + a + "$foo$"; $code$
AS $$ return a-1; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS $$ return a-1; $$' at line 4
# Both external code and SQL code is not allowed
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$ return a-1; $$
BEGIN RETURN a-1; END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'BEGIN RETURN a-1; END' at line 4
CREATE FUNCTION foo(a INTEGER) RETURNS INTEGER
DETERMINISTIC LANGUAGE SQL
AS $$ return a-1; $$
BEGIN RETURN a-1; END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$ return a-1; $$
BEGIN RETURN a-1; END' at line 3
# Parse error if no routine body
CREATE FUNCTION foo() RETURNS INTEGER
DETERMINISTIC LANGUAGE JAVASCRIPT//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 2
# Repeat test cases for procedures
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
let b = a;
$$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
let b = a;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = a;

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS 'let b = a;' //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$let b = a;$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	let b = a;
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS "let b = a;" //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$let b = a;$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	let b = a;
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS '
  let b = a;
' //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
  let b = a;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
  let b = a;

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
"
   let b = a;
" //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
   let b = a;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
   let b = a;

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
Routine body must come after characteristics
CREATE PROCEDURE bar(a INTEGER)
AS $$
let b = a;
$$
DETERMINISTIC LANGUAGE JAVASCRIPT
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$
let b = a;
$$
DETERMINISTIC LANGUAGE JAVASCRIPT' at line 2
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
'
  let b = "$$" + a + "$$";
'
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
  let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
  let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$code$
let b = "$$" + a + "$$";
$code$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$coDE$
let b = "$$" + a + "$$";
$coDE$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$CODE$
let b = "$$" + a + "$$";
$CODE$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$tag$
let b = "$$" + a + "$$";
$tag$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$TAG$
let b = "$$" + a + "$$";
$TAG$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$NULL$
let b = "$$" + a + "$$";
$NULL$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$abdc_1234$
let b = "$$" + a + "$$";
$abdc_1234$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$_a$
let b = "$$" + a + "$$";
$_a$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$_$
let b = "$$" + a + "$$";
$_$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$0$
let b = "$$" + a + "$$";
$0$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$100$
let b = "$$" + a + "$$";
$100$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$12345678901234$
let b = "$$" + a + "$$";
$12345678901234$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM$
let b = "$$" + a + "$$";
$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$¡$
let b = "$$" + a + "$$";
$¡$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM¡™£¢∞§¶•ªº–≠œ∑´®†¥¨ˆøπ“‘«åß∂ƒ©˙∆˚¬…æΩ≈ç√∫µ≤≥÷⁄€‹›ﬁﬂ‡°·‚—±ÅÍÎÏ˝ÓÔÒÚÆ¸˛Ç◊ıÂ¯˘¿$
let b = "$$" + a + "$$";
$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM¡™£¢∞§¶•ªº–≠œ∑´®†¥¨ˆøπ“‘«åß∂ƒ©˙∆˚¬…æΩ≈ç√∫µ≤≥÷⁄€‹›ﬁﬂ‡°·‚—±ÅÍÎÏ˝ÓÔÒÚÆ¸˛Ç◊ıÂ¯˘¿$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "$$" + a + "$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$$" + a + "$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS	      $$
const $x = 3;
const $y$ = 4;
let b = $x + $y$ + a;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
const $x = 3;
const $y$ = 4;
let b = $x + $y$ + a;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
const $x = 3;
const $y$ = 4;
let b = $x + $y$ + a;

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$code$
const x$$ = 3;
const $y$$z = 4;
let b = x$$ + $y$$z + a;
$code$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
const x$$ = 3;
const $y$$z = 4;
let b = x$$ + $y$$z + a;
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
const x$$ = 3;
const $y$$z = 4;
let b = x$$ + $y$$z + a;

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
$$
let b = a;
$$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$
let b = a;
$$' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE 'let b = a;' //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ''let b = a;'' at line 2
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE let b = a //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '= a' at line 2
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC AS LANGUAGE JAVASCRIPT
"let b = a;" //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'LANGUAGE JAVASCRIPT
"let b = a;"' at line 2
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
$code$
let b = "$$" + a + "$$";
$code$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$code$
let b = "$$" + a + "$$";
$code$' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $
let b = a;
$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$
let b = a;
$' at line 3
# Test conflicting dollar quotes
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
let b = "$$" + a + "$$";
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" + a + "$$";
$$' at line 4
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $code$
let b = "$code$" + a + "$code$";
$code$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" + a + "$code$";
$code$' at line 4
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
let b = a;
$code$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$
let b = a;
$code$' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$code$
let b = "$$" + a + "$$";
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$code$
let b = "$$" + a + "$$";
$$' at line 4
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$Code$
let b = "$$" + a + "$$";
$CODE$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$Code$
let b = "$$" + a + "$$";
$CODE$' at line 4
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$CodE$
let b = "$$" + a + "$$";
$code$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$CodE$
let b = "$$" + a + "$$";
$code$' at line 4
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS	      $$
const $x = 3;
const $y$ = 4;
let b = $x + $y$ + a;
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$
const $x = 3;
const $y$ = 4;
let b = $x + $y$ + a' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$code$
const x$$ = 3;
const $y$$z = 4;
let b = x$$ + $y$$z + a;
$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$code$
const x$$ = 3;
const $y$$z = 4;
let b = x$$ + $y$$z + a;
$' at line 4
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS
$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM™∞•–≠∑†π“‘∂ƒ˙∆˚…Ω≈√∫≤≥⁄€‹›ﬁﬂ‡‚—˝˛◊ı˘$
let b = "$$" + a + "$$";
$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM™∞•–≠∑†π“‘∂ƒ˙∆˚…Ω≈√∫≤≥⁄€‹›ﬁﬂ‡‚—˝˛◊ı˗$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$1234567890qwertyuiopasdfghjklzxcvbnm_QWERTYUIOPASDFGHJKLZXCVBNM™∞•–≠â' at line 4
Check cases with invalid dollar tags
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $+a$
let b = a;
$+a$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$+a$
let b = a;
$+a$' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $a-$
let b = a;
$a-$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$a-$
let b = a;
$a-$' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $a(b$
let b = a;
$a(b$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$a(b$
let b = a;
$a(b$' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $a~b$
let b = a;
$a~b$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$a~b$
let b = a;
$a~b$' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $ $
let b = a;
$ $ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$ $
let b = a;
$ $' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $I'm$
  let b = a;
$I'm$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$I'm$
  let b = a;
$I'm$' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $'Hi'$
let b = a;
$'Hi'$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$'Hi'$
let b = a;
$'Hi'$' at line 3
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $a"b"$
let b = a;
$a"b"$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$a"b"$
let b = a;
$a"b"$' at line 3
# Test multi-byte characters in code (∂ is 3 bytes in utf8mb3)
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
let b = "∂" + a + "$∂$";
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
let b = "∂" + a + "$∂$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "∂" + a + "$∂$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $code$
let b = "∂" + a + "$∂$$";
$code$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "∂" + a + "$∂$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "∂" + a + "$∂$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $code$
let b = "∂" + a + "$∂$$∂";
$code$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "∂" + a + "$∂$$∂";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "∂" + a + "$∂$$∂";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$∂
let b = "∂" + a + "$∂$";
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$∂
let b = "∂" + a + "$∂$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	∂
let b = "∂" + a + "$∂$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
Test multi-byte characters in quote tags
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $∂$
let b = "∂" + a + "∂$";
$∂$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
let b = "∂" + a + "∂$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "∂" + a + "∂$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $co∂e$
let b = "∂" + a + "$∂$$";
$co∂e$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$
let b = "∂" + a + "$∂$$";
$JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "∂" + a + "$∂$$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $😀$
let b = "😀$😔" + a + "😀$😔$";
$😀$
//
Warnings:
Warning	1300	Cannot convert string '\x0Alet b...' from utf8mb4 to utf8mb3
Warning	1300	Cannot convert string '\x0Alet b...' from utf8mb4 to utf8mb3
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
let b = "😀$😔" + a + "😀$😔$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "?$?" + a + "?$?$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $😀😔$
let b = "😀$😔" + a + "😀$😔$";
$😀😔$
//
Warnings:
Warning	1300	Cannot convert string '\x0Alet b...' from utf8mb4 to utf8mb3
Warning	1300	Cannot convert string '\x0Alet b...' from utf8mb4 to utf8mb3
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
let b = "😀$😔" + a + "😀$😔$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "?$?" + a + "?$?$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $x😀$
let b = "$x😀😔" + a + "$x😀😔$";
$x😀$
//
Warnings:
Warning	1300	Cannot convert string '\x0Alet b...' from utf8mb4 to utf8mb3
Warning	1300	Cannot convert string '\x0Alet b...' from utf8mb4 to utf8mb3
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
let b = "$x😀😔" + a + "$x😀😔$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$x??" + a + "$x??$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $😀x$
let b = "$😀x😔$" + a + "$😀😔x$";
$😀x$
//
Warnings:
Warning	1300	Cannot convert string '\x0Alet b...' from utf8mb4 to utf8mb3
Warning	1300	Cannot convert string '\x0Alet b...' from utf8mb4 to utf8mb3
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
let b = "$😀x😔$" + a + "$😀😔x$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$?x?$" + a + "$??x$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $x😀y$
let b = "$x😀😔y$" + a + "$x😀y😔$";
$x😀y$
//
Warnings:
Warning	1300	Cannot convert string '\x0Alet b...' from utf8mb4 to utf8mb3
Warning	1300	Cannot convert string '\x0Alet b...' from utf8mb4 to utf8mb3
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
let b = "$x😀😔y$" + a + "$x😀y😔$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "$x??y$" + a + "$x?y?$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $x😀y😔$
let b = "😀😔$" + a + "😀$😔$";
$x😀y😔$
//
Warnings:
Warning	1300	Cannot convert string '\x0Alet b...' from utf8mb4 to utf8mb3
Warning	1300	Cannot convert string '\x0Alet b...' from utf8mb4 to utf8mb3
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(a INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
let b = "😀😔$" + a + "😀$😔$";
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
let b = "??$" + a + "?$?$";

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $😀😔$
let b = "😀$😔" + a + "😀$😔$";
$😔😀$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$??$
let b = "?$?" + a + "?$?$";
$??$' at line 3
# Escape sequences is not supported in dollar quoted strings
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
let b = "\$$" + a + "\$code$";
$$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '" + a + "\$code$";
$$' at line 4
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $code$
let b = "\$$" + a + "\$code$";
$code$
//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '";
$code$' at line 4
# Check some variants that should give external parse error
# if the language is supported
CREATE PROCEDURE bar(x INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $code$ let b := "$$" + x + "$foo$"; $code$ //
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(x INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $JAVASCRIPT$ let b := "$$" + x + "$foo$"; $JAVASCRIPT$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	 let b := "$$" + x + "$foo$"; 
CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
CREATE PROCEDURE bar(x INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$
const $x = x,
const $y = 1
let b = $x + $y;
$$
//
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE procedure bar//
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
bar	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `bar`(x INTEGER)
    DETERMINISTIC
    LANGUAGE JAVASCRIPT
AS $$
const $x = x,
const $y = 1
let b = $x + $y;
$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SHOW PROCEDURE STATUS WHERE Name = 'bar'//
Db	Name	Type	Language	Definer	Modified	Created	Security_type	Comment	character_set_client	collation_connection	Database Collation
test	bar	PROCEDURE	JAVASCRIPT	root@localhost	<modified>	<created>	DEFINER		utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT routine_name, routine_type, data_type, routine_body,
external_name, external_language, parameter_style, routine_definition
FROM information_schema.routines
WHERE routine_schema = "test" //
ROUTINE_NAME	ROUTINE_TYPE	DATA_TYPE	ROUTINE_BODY	EXTERNAL_NAME	EXTERNAL_LANGUAGE	PARAMETER_STYLE	ROUTINE_DEFINITION
bar	PROCEDURE		EXTERNAL	NULL	JAVASCRIPT	SQL	
const $x = x,
const $y = 1
let b = $x + $y;

CALL bar(2)//
ERROR HY000: Language component: Not available.
DROP procedure bar//
# Multiple occurrences of routine body is not allowed
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS 'let b = "$$" + a + "$foo$";'
AS $$ let b = a-1; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS $$ let b = a-1; $$' at line 4
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $code$ let b = "$$" + a + "$foo$"; $code$
AS $$ let b = a-1; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS $$ let b = a-1; $$' at line 4
# Both external code and SQL code is not allowed
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE JAVASCRIPT
AS $$ let b = a-1; $$
BEGIN RETURN a-1; END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'BEGIN RETURN a-1; END' at line 4
CREATE PROCEDURE bar(a INTEGER)
DETERMINISTIC LANGUAGE SQL
AS $$ let b = a-1; $$
BEGIN RETURN a-1; END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$ let b = a-1; $$
BEGIN RETURN a-1; END' at line 3
# Parse error if no routine body
CREATE PROCEDURE bar()
DETERMINISTIC LANGUAGE JAVASCRIPT//
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 2
# Check that dollar quotes can not be used in other context
CREATE FUNCTION $$foo(x INTEGER) INTEGER DETERMINISTIC
LANGUAGE JAVASCRIPT AS $$ let b = x-1; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$foo(x INTEGER) INTEGER DETERMINISTIC
LANGUAGE JAVASCRIPT AS $$ let b = x-1; $$' at line 1
CREATE PROCEDURE $$bar(a INTEGER) DETERMINISTIC
LANGUAGE JAVASCRIPT AS $$ let b = a; $$ //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$bar(a INTEGER) DETERMINISTIC
LANGUAGE JAVASCRIPT AS $$ let b = a; $$' at line 1
SELECT $$Hello world!$$;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$Hello world!$$' at line 1
SELECT $hi$Hello world!$hi$;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$hi$Hello world!$hi$' at line 1
# Check that dollars can still be used in identifiers
SELECT 1 AS $hi;
$hi
1
Warnings:
Warning	1681	'$ as the first character of an unquoted identifier' is deprecated and will be removed in a future release.
SELECT 1 AS h$i;
h$i
1
SELECT 1 AS h$$i;
h$$i
1
SELECT 1 AS h$i$;
h$i$
1
# Identifiers matching dollar quotes can no longer be used
SELECT 1 AS $$;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$' at line 1
SELECT 1 AS $hi$;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$hi$' at line 1
SELECT 1 AS $h$i;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$h$i' at line 1
# ... unless quoted
SELECT 1 AS `$$`;
$$
1
SELECT 1 AS `$hi$`;
$hi$
1
SELECT 1 AS `$h$i`;
$h$i
1
#
# WL#16359: SQL syntax for JavaScript Libraries
#
CREATE LIBRARY lib1 LANGUAGE JAVASCRIPT
AS $$
export function f(n) {
return n
}
$$;
Warnings:
Warning	6001	Language component: Not available.
# Tests for valid identifiers
CREATE LIBRARY
this_name_is_too_long23456789312345678941234567895123456789612345
LANGUAGE JAVASCRIPT
AS "export function f(n) {return n}";
ERROR 42000: Identifier name 'this_name_is_too_long23456789312345678941234567895123456789612345' is too long
CREATE LIBRARY
test.this_name_is_not_too_long678931234567894123456789512345678961234
LANGUAGE JAVASCRIPT
AS "export function f(n) {return n}";
Warnings:
Warning	6001	Language component: Not available.
DROP LIBRARY
this_name_is_not_too_long678931234567894123456789512345678961234;
CREATE LIBRARY 01234
LANGUAGE JAVASCRIPT
AS "export function f(n) {return n}";
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '01234
LANGUAGE JAVASCRIPT
AS "export function f(n) {return n}"' at line 1
CREATE LIBRARY
`01234`
LANGUAGE JAVASCRIPT
AS "export function f(n) {return n}";
Warnings:
Warning	6001	Language component: Not available.
DROP LIBRARY `01234`;
# Tests for IF NOT EXISTS
CREATE LIBRARY IF NOT EXISTS lib1 LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
Note	1304	LIBRARY lib1 already exists
CREATE LIBRARY IF EXISTS test.lib2 LANGUAGE JAVASCRIPT
AS "export function f(n) {return n}";
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'EXISTS test.lib2 LANGUAGE JAVASCRIPT
AS "export function f(n) {return n}"' at line 1
# This will not fail since component is not installed
CREATE LIBRARY IF NOT EXISTS test.lib2 LANGUAGE JAVASCRIPT
AS "whatever";
Warnings:
Warning	6001	Language component: Not available.
# Tests for language clause
CREATE LIBRARY IF NOT EXISTS lib1 LANGUAGE Javascript
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
Note	1304	LIBRARY lib1 already exists
CREATE LIBRARY IF NOT EXISTS lib1 LANGUAGE JavaScript
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
Note	1304	LIBRARY lib1 already exists
CREATE LIBRARY IF NOT EXISTS lib1 LANGUAGE javascript
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
Note	1304	LIBRARY lib1 already exists
CREATE LIBRARY IF NOT EXISTS lib1
AS 'export function f(n) {return n}'
LANGUAGE javascript;
ERROR 42000: Language is not specified for library. near 'AS 'export function f(n) {return n}'
LANGUAGE javascript' at line 2
CREATE LIBRARY IF NOT EXISTS lib1
AS 'export function f(n) {return n}';
ERROR 42000: Language is not specified for library. near 'AS 'export function f(n) {return n}'' at line 2
CREATE LIBRARY IF NOT EXISTS lib1 LANGUAGE
AS 'export function f(n) {return n}';
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS 'export function f(n) {return n}'' at line 2
CREATE LIBRARY IF NOT EXISTS test.lib2 LANGUAGE SQL
AS "WHATEVER";
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'SQL
AS "WHATEVER"' at line 1
CREATE LIBRARY IF NOT EXISTS test.lib3 LANGUAGE PYTHON
AS $$ def fib(n): return n $$;
Warnings:
Warning	6001	Language component: Not available.
DROP LIBRARY test.lib3;
CREATE LIBRARY IF NOT EXISTS test.lib3 LANGUAGE JAVA1
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
DROP LIBRARY test.lib3;
CREATE LIBRARY IF NOT EXISTS test.lib3 LANGUAGE $JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	1681	'$ as the first character of an unquoted identifier' is deprecated and will be removed in a future release.
Warning	6001	Language component: Not available.
DROP LIBRARY test.lib3;
CREATE LIBRARY IF NOT EXISTS test.lib2 LANGUAGE $$JAVASCRIPT
AS 'export function f(n) {return n}';
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '$$JAVASCRIPT
AS 'export function f(n) {return n}'' at line 1
CREATE LIBRARY IF NOT EXISTS test.lib3 LANGUAGE 123j
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
DROP LIBRARY test.lib3;
CREATE LIBRARY IF NOT EXISTS test.lib2 LANGUAGE 123
AS 'export function f(n) {return n}';
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '123
AS 'export function f(n) {return n}'' at line 1
CREATE LIBRARY IF NOT EXISTS test.lib3 LANGUAGE `123`
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
DROP LIBRARY test.lib3;
CREATE LIBRARY IF NOT EXISTS test.lib3 LANGUAGE __
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
DROP LIBRARY test.lib3;
# Tests for AS clause
CREATE LIBRARY IF NOT EXISTS lib1 LANGUAGE JAVASCRIPT;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1
CREATE LIBRARY IF NOT EXISTS lib1 LANGUAGE JAVASCRIPT
AS export function f(n) {return n};
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'export function f(n) {return n}' at line 2
CREATE LIBRARY IF NOT EXISTS lib1 LANGUAGE JAVASCRIPT
'export function f(n) {return n}';
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ''export function f(n) {return n}'' at line 2
# Tests for CREATE LIBRARY with a COMMENT clause
CREATE LIBRARY library_with_comment COMMENT "library comment" LANGUAGE JAVASCRIPT AS " export function f(n) {  return n+1 } ";
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY library_with_comment2 LANGUAGE JAVASCRIPT COMMENT "library comment" AS " export function f(n) {  return n+1 } ";
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY library_with_empty_comment COMMENT "" LANGUAGE JAVASCRIPT AS " export function f(n) {  return n+1 } ";
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY library_with_extra_longcomment COMMENT "01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789" LANGUAGE JAVASCRIPT AS " export function f(n) {  return n+1 } ";
Warnings:
Warning	6001	Language component: Not available.
SHOW LIBRARY STATUS WHERE db = 'test';
Db	Name	Language	Creator	Modified	Created	Comment
test	lib1	JAVASCRIPT	root@localhost	<modified>	<created>	
test	lib2	JAVASCRIPT	root@localhost	<modified>	<created>	
test	library_with_comment	JAVASCRIPT	root@localhost	<modified>	<created>	library comment
test	library_with_comment2	JAVASCRIPT	root@localhost	<modified>	<created>	library comment
test	library_with_empty_comment	JAVASCRIPT	root@localhost	<modified>	<created>	
test	library_with_extra_longcomment	JAVASCRIPT	root@localhost	<modified>	<created>	01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789
SHOW CREATE LIBRARY library_with_comment;
Library	sql_mode	Create Library
library_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `library_with_comment`
    COMMENT 'library comment'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {  return n+1 } $$
SHOW CREATE LIBRARY library_with_empty_comment;
Library	sql_mode	Create Library
library_with_empty_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `library_with_empty_comment`
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {  return n+1 } $$
SHOW CREATE LIBRARY library_with_extra_longcomment;
Library	sql_mode	Create Library
library_with_extra_longcomment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `library_with_extra_longcomment`
    COMMENT '01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {  return n+1 } $$
# Tests for ALTER LIBRARY
ALTER LIBRARY library_with_comment COMMENT "updated comment";
SHOW CREATE LIBRARY library_with_comment;
Library	sql_mode	Create Library
library_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `library_with_comment`
    COMMENT 'updated comment'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {  return n+1 } $$
CREATE LIBRARY  library_without_comment LANGUAGE JAVASCRIPT AS " export function f(n) {  return n+1 } ";
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE LIBRARY library_without_comment;
Library	sql_mode	Create Library
library_without_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `library_without_comment`
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {  return n+1 } $$
ALTER LIBRARY library_without_comment COMMENT "added comment";
SHOW CREATE LIBRARY library_without_comment;
Library	sql_mode	Create Library
library_without_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `library_without_comment`
    COMMENT 'added comment'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {  return n+1 } $$
ALTER LIBRARY library_with_empty_comment COMMENT "01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789";
SHOW CREATE LIBRARY library_with_empty_comment;
Library	sql_mode	Create Library
library_with_empty_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `library_with_empty_comment`
    COMMENT '01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {  return n+1 } $$
# BUG#37563225 : An empty comment is ignored.
ALTER LIBRARY library_with_empty_comment COMMENT '';
SHOW CREATE LIBRARY library_with_empty_comment;
Library	sql_mode	Create Library
library_with_empty_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `library_with_empty_comment`
    COMMENT '01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {  return n+1 } $$
ALTER LIBRARY library_with_extra_longcomment COMMENT "";
SHOW CREATE LIBRARY library_with_extra_longcomment;
Library	sql_mode	Create Library
library_with_extra_longcomment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `library_with_extra_longcomment`
    COMMENT '01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {  return n+1 } $$
ALTER LIBRARY library_without_comment COMMENT "added comment";
SHOW CREATE LIBRARY library_without_comment;
Library	sql_mode	Create Library
library_without_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `library_without_comment`
    COMMENT 'added comment'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {  return n+1 } $$
ALTER LIBRARY library_without_comment LANGUAGE JavaScript;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'LANGUAGE JavaScript' at line 1
ALTER LIBRARY library_without_comment AS " export function f(n) { return n+2 }";
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS " export function f(n) { return n+2 }"' at line 1
ALTER LIBRARY library_without_comment;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1
SHOW CREATE LIBRARY library_without_comment;
Library	sql_mode	Create Library
library_without_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `library_without_comment`
    COMMENT 'added comment'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {  return n+1 } $$
ALTER LIBRARY it_should_not_exist COMMENT "updated comment";
ERROR 42000: LIBRARY test.it_should_not_exist does not exist
SHOW LIBRARY STATUS WHERE db = 'test';
Db	Name	Language	Creator	Modified	Created	Comment
test	lib1	JAVASCRIPT	root@localhost	<modified>	<created>	
test	lib2	JAVASCRIPT	root@localhost	<modified>	<created>	
test	library_without_comment	JAVASCRIPT	root@localhost	<modified>	<created>	added comment
test	library_with_comment	JAVASCRIPT	root@localhost	<modified>	<created>	updated comment
test	library_with_comment2	JAVASCRIPT	root@localhost	<modified>	<created>	library comment
test	library_with_empty_comment	JAVASCRIPT	root@localhost	<modified>	<created>	01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789
test	library_with_extra_longcomment	JAVASCRIPT	root@localhost	<modified>	<created>	01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789
# Tests for DROP LIBRARY
DROP LIBRARY lib3;
ERROR 42000: LIBRARY test.lib3 does not exist
DROP LIBRARY IF EXISTS lib3;
Warnings:
Note	1305	LIBRARY test.lib3 does not exist
DROP LIBRARY IF NOT EXISTS lib3;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'NOT EXISTS lib3' at line 1
DROP LIBRARY
this_name_is_too_long23456789312345678941234567895123456789612345;
ERROR 42000: Identifier name 'this_name_is_too_long23456789312345678941234567895123456789612345' is too long
DROP LIBRARY lib1 LANGUAGE JAVASCRIPT;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'LANGUAGE JAVASCRIPT' at line 1
SHOW CREATE LIBRARY db2.lib1;
ERROR 42000: LIBRARY lib1 does not exist
# Test CREATE and ALTER FUNCTION/PROCEDURE WITH USING clause
CREATE FUNCTION f(n INTEGER) RETURNS INTEGER LANGUAGE JAVASCRIPT
USING (lib1 AS lib2)
AS $$ return lib2.f(n) $$;
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION f;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `f`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`lib1` AS `lib2`)
AS $$ return lib2.f(n) $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
ALTER FUNCTION f USING(lib2);
SHOW CREATE FUNCTION f;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `f`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`lib2`)
AS $$ return lib2.f(n) $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
ALTER FUNCTION f USING(lib1, lib2);
SHOW CREATE FUNCTION f;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `f`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`lib1`, `test`.`lib2`)
AS $$ return lib2.f(n) $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
ALTER FUNCTION f USING(lib2 as lib1, lib1);
SHOW CREATE FUNCTION f;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `f`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`lib2` AS `lib1`, `test`.`lib1`)
AS $$ return lib2.f(n) $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
ALTER FUNCTION f USING(lib1, lib2, lib1 as lib3, lib1);
SHOW CREATE FUNCTION f;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `f`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`lib1`, `test`.`lib2`, `test`.`lib1` AS `lib3`, `test`.`lib1`)
AS $$ return lib2.f(n) $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
ALTER FUNCTION f USING(lib1, lib2, lib3);
ERROR 42000: LIBRARY lib3 does not exist
SHOW CREATE FUNCTION f;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `f`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`lib1`, `test`.`lib2`, `test`.`lib1` AS `lib3`, `test`.`lib1`)
AS $$ return lib2.f(n) $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
ALTER FUNCTION f USING(lib0 as lib2, lib1);
ERROR 42000: LIBRARY lib0 does not exist
SHOW CREATE FUNCTION f;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `f`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`lib1`, `test`.`lib2`, `test`.`lib1` AS `lib3`, `test`.`lib1`)
AS $$ return lib2.f(n) $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
DROP FUNCTION f;
CREATE PROCEDURE p(n INTEGER)
USING (lib1, lib2 AS lib3)
LANGUAGE JAVASCRIPT
AS $$ let a = n $$;
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE PROCEDURE p;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `p`(n INTEGER)
    LANGUAGE JAVASCRIPT
    USING (`test`.`lib1`, `test`.`lib2` AS `lib3`)
AS $$ let a = n $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
ALTER PROCEDURE p USING(lib2);
SHOW CREATE PROCEDURE p;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
p	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `p`(n INTEGER)
    LANGUAGE JAVASCRIPT
    USING (`test`.`lib2`)
AS $$ let a = n $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
DROP PROCEDURE p;
CREATE FUNCTION f(n INTEGER) RETURNS INTEGER
USING (lib1 AS lib2)
LANGUAGE JAVASCRIPT
AS $$ return lib2.f(n) $$;
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION f;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `f`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`lib1` AS `lib2`)
AS $$ return lib2.f(n) $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
DROP FUNCTION f;
CREATE FUNCTION f(n INTEGER) RETURNS INTEGER LANGUAGE JAVASCRIPT
USING (lib1 AS lib2)
USING (lib3)
AS $$ return lib2.f(n) $$;
ERROR 42000: You have an error in your SQL syntax; Multiple USING clauses are not supported near 'USING (lib3)
AS $$ return lib2.f(n) $$' at line 3
CREATE FUNCTION f(n INTEGER) RETURNS INTEGER
USING (lib3)
LANGUAGE JAVASCRIPT
USING (lib3 AS lib1, lib2)
AS $$ return lib2.f(n) $$;
ERROR 42000: You have an error in your SQL syntax; Multiple USING clauses are not supported near 'USING (lib3 AS lib1, lib2)
AS $$ return lib2.f(n) $$' at line 4
CREATE FUNCTION f(n INTEGER) RETURNS INTEGER LANGUAGE JAVASCRIPT
USING (lib1 AS lib2)
AS $$ return lib2.f(n) $$
USING (lib3);
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'USING (lib3)' at line 4
CREATE FUNCTION f(n INTEGER) RETURNS INTEGER LANGUAGE SQL
USING (lib1)
RETURN n;
ERROR HY000: Libraries are not supported for SQL.
CREATE PROCEDURE procedure_with_comment() COMMENT "procedure comment" LANGUAGE JAVASCRIPT USING (library_with_comment) AS "let i=2";
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE PROCEDURE procedure_with_comment;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
procedure_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `procedure_with_comment`()
    LANGUAGE JAVASCRIPT
    USING (`test`.`library_with_comment`)
    COMMENT 'procedure comment'
AS $$let i=2$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'procedure_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	procedure_with_comment	PROCEDURE	def	test	library_with_comment	NULL
ALTER PROCEDURE procedure_with_comment USING (library_without_comment);
SHOW CREATE PROCEDURE procedure_with_comment;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
procedure_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `procedure_with_comment`()
    LANGUAGE JAVASCRIPT
    USING (`test`.`library_without_comment`)
    COMMENT 'procedure comment'
AS $$let i=2$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'procedure_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	procedure_with_comment	PROCEDURE	def	test	library_without_comment	NULL
ALTER PROCEDURE procedure_with_comment USING ();
SHOW CREATE PROCEDURE procedure_with_comment;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
procedure_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `procedure_with_comment`()
    LANGUAGE JAVASCRIPT
    COMMENT 'procedure comment'
AS $$let i=2$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'procedure_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
ALTER PROCEDURE procedure_with_comment USING (library_without_comment);
SHOW CREATE PROCEDURE procedure_with_comment;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
procedure_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `procedure_with_comment`()
    LANGUAGE JAVASCRIPT
    USING (`test`.`library_without_comment`)
    COMMENT 'procedure comment'
AS $$let i=2$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'procedure_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	procedure_with_comment	PROCEDURE	def	test	library_without_comment	NULL
ALTER PROCEDURE procedure_with_comment USING (non_existing_library);
ERROR 42000: LIBRARY non_existing_library does not exist
SHOW CREATE PROCEDURE procedure_with_comment;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
procedure_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `procedure_with_comment`()
    LANGUAGE JAVASCRIPT
    USING (`test`.`library_without_comment`)
    COMMENT 'procedure comment'
AS $$let i=2$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'procedure_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	procedure_with_comment	PROCEDURE	def	test	library_without_comment	NULL
ALTER PROCEDURE procedure_with_comment COMMENT 'Updated Temporary Procedure Comment';
SHOW CREATE PROCEDURE procedure_with_comment;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
procedure_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `procedure_with_comment`()
    LANGUAGE JAVASCRIPT
    USING (`test`.`library_without_comment`)
    COMMENT 'Updated Temporary Procedure Comment'
AS $$let i=2$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'procedure_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	procedure_with_comment	PROCEDURE	def	test	library_without_comment	NULL
ALTER PROCEDURE procedure_with_comment COMMENT 'Updated Procedure Comment' USING (library_with_comment AS library_without_comment);
SHOW CREATE PROCEDURE procedure_with_comment;
Procedure	sql_mode	Create Procedure	character_set_client	collation_connection	Database Collation
procedure_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` PROCEDURE `procedure_with_comment`()
    LANGUAGE JAVASCRIPT
    USING (`test`.`library_with_comment` AS `library_without_comment`)
    COMMENT 'Updated Procedure Comment'
AS $$let i=2$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'procedure_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	procedure_with_comment	PROCEDURE	def	test	library_with_comment	NULL
create function function_with_comment(n INTEGER) RETURNS INTEGER LANGUAGE JAVASCRIPT COMMENT "function comment" USING (library_with_comment AS lib) AS "return lib.f(42)";
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION function_with_comment;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
function_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `function_with_comment`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`library_with_comment` AS `lib`)
    COMMENT 'function comment'
AS $$return lib.f(42)$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'function_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	function_with_comment	FUNCTION	def	test	library_with_comment	NULL
ALTER FUNCTION function_with_comment USING (library_without_comment lib);
SHOW CREATE FUNCTION function_with_comment;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
function_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `function_with_comment`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`library_without_comment` AS `lib`)
    COMMENT 'function comment'
AS $$return lib.f(42)$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'function_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	function_with_comment	FUNCTION	def	test	library_without_comment	NULL
ALTER FUNCTION function_with_comment USING ();
SHOW CREATE FUNCTION function_with_comment;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
function_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `function_with_comment`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    COMMENT 'function comment'
AS $$return lib.f(42)$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'function_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
ALTER FUNCTION function_with_comment COMMENT 'updated comment' USING (library_with_comment AS lib);
SHOW CREATE FUNCTION function_with_comment;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
function_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `function_with_comment`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`library_with_comment` AS `lib`)
    COMMENT 'updated comment'
AS $$return lib.f(42)$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'function_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	function_with_comment	FUNCTION	def	test	library_with_comment	NULL
ALTER FUNCTION function_with_comment USING () COMMENT 'updated comment';
SHOW CREATE FUNCTION function_with_comment;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
function_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `function_with_comment`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    COMMENT 'updated comment'
AS $$return lib.f(42)$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'function_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
ALTER FUNCTION function_with_comment USING (library_with_comment AS lib) COMMENT 'invalid syntax comment' USING();
ERROR 42000: You have an error in your SQL syntax; Multiple USING clauses are not supported near 'USING()' at line 1
SHOW CREATE FUNCTION function_with_comment;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
function_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `function_with_comment`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    COMMENT 'updated comment'
AS $$return lib.f(42)$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'function_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
ALTER FUNCTION function_with_comment USING () COMMENT 'invalid inverted syntax comment' USING(library_with_comment AS lib);
ERROR 42000: You have an error in your SQL syntax; Multiple USING clauses are not supported near 'USING(library_with_comment AS lib)' at line 1
SHOW CREATE FUNCTION function_with_comment;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
function_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `function_with_comment`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    COMMENT 'updated comment'
AS $$return lib.f(42)$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'function_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
ALTER FUNCTION function_with_comment USING (library_without_comment lib) COMMENT "temporary comment";
SHOW CREATE FUNCTION function_with_comment;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
function_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `function_with_comment`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`library_without_comment` AS `lib`)
    COMMENT 'temporary comment'
AS $$return lib.f(42)$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'function_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	function_with_comment	FUNCTION	def	test	library_without_comment	NULL
ALTER FUNCTION function_with_comment USING (non_existing_library) COMMENT "invalid comment";
ERROR 42000: LIBRARY non_existing_library does not exist
SHOW CREATE FUNCTION function_with_comment;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
function_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `function_with_comment`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`library_without_comment` AS `lib`)
    COMMENT 'temporary comment'
AS $$return lib.f(42)$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'function_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	function_with_comment	FUNCTION	def	test	library_without_comment	NULL
ALTER FUNCTION function_with_comment COMMENT "altered function comment";
SHOW CREATE FUNCTION function_with_comment;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
function_with_comment	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `function_with_comment`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`library_without_comment` AS `lib`)
    COMMENT 'altered function comment'
AS $$return lib.f(42)$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_NAME = 'function_with_comment';
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	function_with_comment	FUNCTION	def	test	library_without_comment	NULL
CREATE DATABASE db2;
CREATE LIBRARY db2.lib1 LANGUAGE JAVASCRIPT
AS $$ export function f(n) {return n} $$;
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY test.lib1 LANGUAGE JAVASCRIPT
AS $$ export function f(n) {return n} $$;
ERROR 42000: LIBRARY lib1 already exists
# lib1 should refer to db2.lib1 (function's schema)
CREATE FUNCTION db2.f(n INTEGER) RETURNS INTEGER LANGUAGE JAVASCRIPT
USING (lib1 AS lib2)
AS $$ return lib2.f(n) $$;
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION db2.f;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `f`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`db2`.`lib1` AS `lib2`)
AS $$ return lib2.f(n) $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
DROP FUNCTION db2.f;
# db2.lib2 does not exist
CREATE FUNCTION db2.f(n INTEGER) RETURNS INTEGER LANGUAGE JAVASCRIPT
USING (lib1 AS lib2, db2.lib2)
AS $$ return lib2.f(n) $$;
ERROR 42000: LIBRARY lib2 does not exist
USE db2;
CREATE FUNCTION f(n INTEGER) RETURNS INTEGER LANGUAGE JAVASCRIPT
USING (lib1 AS lib2)
AS $$ return lib2.f(n) $$;
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION f;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
f	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `f`(n INTEGER) RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`db2`.`lib1` AS `lib2`)
AS $$ return lib2.f(n) $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
DROP FUNCTION f;
USE test;
DROP LIBRARY lib1;
DROP LIBRARY test.lib2;
DROP LIBRARY IF EXISTS db2.lib1;
DROP LIBRARY IF EXISTS lib1;
Warnings:
Note	1305	LIBRARY test.lib1 does not exist
DROP LIBRARY IF EXISTS db2.lib1;
Warnings:
Note	1305	LIBRARY db2.lib1 does not exist
DROP FUNCTION function_with_comment;
DROP PROCEDURE procedure_with_comment;
DROP LIBRARY library_without_comment;
DROP LIBRARY library_with_comment;
DROP LIBRARY library_with_comment2;
DROP LIBRARY library_with_empty_comment;
DROP LIBRARY library_with_extra_longcomment;
# Test use of CREATE/DROP LIBRARY in stored procedures and prepared statements
CREATE PROCEDURE p1() LANGUAGE SQL
CREATE LIBRARY lib LANGUAGE JAVASCRIPT
AS $$ export function f(n) {return n} $$;
CALL p1();
Warnings:
Warning	6001	Language component: Not available.
# Verify re-execution behavior of SP
DROP LIBRARY lib;
CALL p1();
Warnings:
Warning	6001	Language component: Not available.
CALL p1();
ERROR 42000: LIBRARY lib already exists
DROP PROCEDURE p1;
# Test use of ALTER LIBRARY COMMENT as an SP argument.
CREATE PROCEDURE p1(in comment_text varchar(25)) LANGUAGE SQL
BEGIN
PREPARE stmt FROM 'ALTER LIBRARY lib COMMENT ?';
EXECUTE stmt USING comment_text;
END //
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'comment_text;
END' at line 4
CREATE PROCEDURE p2() LANGUAGE SQL
ALTER LIBRARY lib COMMENT 'Comment from p2()';
CALL p2();
CREATE PROCEDURE p3() LANGUAGE SQL
SHOW CREATE LIBRARY lib;
SHOW LIBRARY STATUS WHERE name = 'lib';
Db	Name	Language	Creator	Modified	Created	Comment
test	lib	JAVASCRIPT	root@localhost	<modified>	<created>	Comment from p2()
CALL p3();
Library	sql_mode	Create Library
lib	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `lib`
    COMMENT 'Comment from p2()'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {return n} $$
CREATE PROCEDURE p4() LANGUAGE SQL
DROP LIBRARY lib;
CALL p4();
DROP PROCEDURE p2;
DROP PROCEDURE p3;
DROP PROCEDURE p4;
PREPARE stmt1 FROM 'CREATE LIBRARY lib LANGUAGE JAVASCRIPT AS $$ export function f(n) {return n} $$';
EXECUTE stmt1;
Warnings:
Warning	6001	Language component: Not available.
DROP LIBRARY lib;
EXECUTE stmt1;
Warnings:
Warning	6001	Language component: Not available.
EXECUTE stmt1;
ERROR 42000: LIBRARY lib already exists
PREPARE stmt2 FROM 'ALTER LIBRARY lib COMMENT "Comment from stmt2"';
EXECUTE stmt2;
PREPARE stmt3 FROM 'SHOW CREATE LIBRARY lib';
EXECUTE stmt3;
Library	sql_mode	Create Library
lib	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `lib`
    COMMENT 'Comment from stmt2'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {return n} $$
PREPARE stmt4 FROM 'DROP LIBRARY lib';
EXECUTE stmt4;
DROP PREPARE stmt1;
DROP PREPARE stmt2;
DROP PREPARE stmt3;
DROP PREPARE stmt4;
CREATE PROCEDURE p1() LANGUAGE SQL
BEGIN
PREPARE stmt1 FROM 'CREATE LIBRARY lib LANGUAGE JAVASCRIPT AS $$ export function f(n) {return n} $$';
PREPARE stmt2 FROM 'ALTER LIBRARY lib COMMENT "Comment from p1.stmt2()"';
PREPARE stmt3 FROM 'SHOW CREATE LIBRARY lib';
PREPARE stmt4 FROM 'DROP LIBRARY lib';
EXECUTE stmt1;
EXECUTE stmt2;
EXECUTE stmt3;
EXECUTE stmt4;
END //
CALL p1()//
Library	sql_mode	Create Library
lib	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `lib`
    COMMENT 'Comment from p1.stmt2()'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {return n} $$
DROP PREPARE stmt1//
DROP PREPARE stmt2//
DROP PREPARE stmt3//
DROP PREPARE stmt4//
DROP PROCEDURE p1//
CREATE PROCEDURE p1() LANGUAGE SQL
BEGIN
PREPARE stmt1 FROM 'CREATE LIBRARY lib LANGUAGE JAVASCRIPT AS $$ export function f(n) {return n} $$';
PREPARE stmt2 FROM 'ALTER LIBRARY lib COMMENT "p1.stmt2 again"';
PREPARE stmt3 FROM 'SHOW CREATE LIBRARY lib';
PREPARE stmt4 FROM 'DROP LIBRARY lib';
EXECUTE stmt1;
EXECUTE stmt2;
EXECUTE stmt3;
END//
CALL p1()//
Library	sql_mode	Create Library
lib	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `lib`
    COMMENT 'p1.stmt2 again'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {return n} $$
EXECUTE stmt4//
DROP PREPARE stmt1//
DROP PREPARE stmt2//
DROP PREPARE stmt3//
DROP PREPARE stmt4//
DROP PROCEDURE p1//
CREATE PROCEDURE p1() LANGUAGE SQL
BEGIN
PREPARE stmt1 FROM 'CREATE LIBRARY lib LANGUAGE JAVASCRIPT COMMENT "Created Comment" AS $$ export function f(n) {return n} $$';
PREPARE stmt2 FROM 'ALTER LIBRARY lib COMMENT "Comment Changed"';
PREPARE stmt3 FROM 'SHOW CREATE LIBRARY lib';
PREPARE stmt4 FROM 'DROP LIBRARY lib';
EXECUTE stmt1;
EXECUTE stmt2;
EXECUTE stmt3;
END//
CALL p1()//
Library	sql_mode	Create Library
lib	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `lib`
    COMMENT 'Comment Changed'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {return n} $$
EXECUTE stmt4//
DROP PREPARE stmt1//
DROP PREPARE stmt2//
DROP PREPARE stmt3//
DROP PREPARE stmt4//
DROP PROCEDURE p1//
CREATE PROCEDURE p1() LANGUAGE SQL
BEGIN
PREPARE stmt1 FROM 'CREATE LIBRARY lib LANGUAGE JAVASCRIPT AS $$ export function f(n) {return n} $$';
PREPARE stmt2 FROM 'DROP LIBRARY lib';
EXECUTE stmt1;
EXECUTE stmt2;
DROP PREPARE stmt1;
DROP PREPARE stmt2;
END//
CALL p1()//
# Verify re-execution behavior of SP
CALL p1()//
CALL p1()//
DROP PROCEDURE p1//
CREATE PROCEDURE p1()
BEGIN
PREPARE create_stmt FROM 'CREATE LIBRARY js_lib LANGUAGE JAVASCRIPT AS $$ export function foo() {return -1} $$;';
EXECUTE create_stmt;
DEALLOCATE PREPARE create_stmt;
PREPARE alter_stmt FROM 'ALTER LIBRARY js_lib COMMENT "DEALLOCATED comment";';
EXECUTE alter_stmt;
DEALLOCATE PREPARE alter_stmt;
END//
CALL p1()//
# Verify re-execution behavior of SP
DROP LIBRARY js_lib//
CALL p1()//
CALL p1()//
ERROR 42000: LIBRARY js_lib already exists
DROP PROCEDURE p1//
SHOW CREATE LIBRARY js_lib//
Library	sql_mode	Create Library
js_lib	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `js_lib`
    COMMENT 'DEALLOCATED comment'
    LANGUAGE JAVASCRIPT
AS $$ export function foo() {return -1} $$
CREATE PROCEDURE p1()
BEGIN
PREPARE drop_stmt FROM 'DROP LIBRARY js_lib;';
EXECUTE drop_stmt;
DEALLOCATE PREPARE drop_stmt;
END//
CALL p1()//
DROP PROCEDURE p1//
# Check what happens if another database than current DB is used.
CREATE PROCEDURE db2.p1() LANGUAGE SQL
CREATE LIBRARY lib LANGUAGE JAVASCRIPT
AS $$ export function f(n) {return n} $$;
CALL db2.p1();
Warnings:
Warning	6001	Language component: Not available.
CREATE PROCEDURE db2.p2() LANGUAGE SQL
ALTER LIBRARY lib COMMENT 'another DB';
CALL db2.p2();
CREATE PROCEDURE db2.p3() LANGUAGE SQL
SHOW CREATE LIBRARY lib;
CALL db2.p3();
Library	sql_mode	Create Library
lib	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE LIBRARY `lib`
    COMMENT 'another DB'
    LANGUAGE JAVASCRIPT
AS $$ export function f(n) {return n} $$
CREATE PROCEDURE p3() LANGUAGE SQL
DROP LIBRARY db2.lib;
CALL p3();
DROP PROCEDURE db2.p1;
DROP PROCEDURE p3;
DROP DATABASE db2;
# extra long library name
CREATE DATABASE schema_891123456789212345678931234567894123456789512345678961234;
USE schema_891123456789212345678931234567894123456789512345678961234;
CREATE LIBRARY library_91123456789212345678931234567894123456789512345678961234 LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
USE test;
CREATE FUNCTION
function_1123456789212345678931234567894123456789512345678961234()
RETURNS INT LANGUAGE JAVASCRIPT
USING (schema_891123456789212345678931234567894123456789512345678961234.library_91123456789212345678931234567894123456789512345678961234 AS alias_7891123456789212345678931234567894123456789512345678961234)
AS $$ return 42 $$;
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION function_1123456789212345678931234567894123456789512345678961234;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
function_1123456789212345678931234567894123456789512345678961234	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `function_1123456789212345678931234567894123456789512345678961234`() RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`schema_891123456789212345678931234567894123456789512345678961234`.`library_91123456789212345678931234567894123456789512345678961234` AS `alias_7891123456789212345678931234567894123456789512345678961234`)
AS $$ return 42 $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
DROP FUNCTION function_1123456789212345678931234567894123456789512345678961234;
DROP DATABASE schema_891123456789212345678931234567894123456789512345678961234;
CREATE DATABASE schema_1_1123456789212345678931234567894123456789512345678961234;
CREATE DATABASE schema_2_1123456789212345678931234567894123456789512345678961234;
CREATE DATABASE schema_3_1123456789212345678931234567894123456789512345678961234;
CREATE DATABASE schema_4_1123456789212345678931234567894123456789512345678961234;
CREATE DATABASE schema_5_1123456789212345678931234567894123456789512345678961234;
CREATE DATABASE schema_6_1123456789212345678931234567894123456789512345678961234;
CREATE DATABASE schema_7_1123456789212345678931234567894123456789512345678961234;
CREATE DATABASE schema_8_1123456789212345678931234567894123456789512345678961234;
CREATE DATABASE schema_9_1123456789212345678931234567894123456789512345678961234;
CREATE DATABASE schema_0_1123456789212345678931234567894123456789512345678961234;
CREATE LIBRARY schema_1_1123456789212345678931234567894123456789512345678961234.library_1_123456789212345678931234567894123456789512345678961234
LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY schema_2_1123456789212345678931234567894123456789512345678961234.library_2_123456789212345678931234567894123456789512345678961234
LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY schema_3_1123456789212345678931234567894123456789512345678961234.library_3_123456789212345678931234567894123456789512345678961234
LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY schema_4_1123456789212345678931234567894123456789512345678961234.library_4_123456789212345678931234567894123456789512345678961234
LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY schema_5_1123456789212345678931234567894123456789512345678961234.library_5_123456789212345678931234567894123456789512345678961234
LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY schema_6_1123456789212345678931234567894123456789512345678961234.library_6_123456789212345678931234567894123456789512345678961234
LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY schema_7_1123456789212345678931234567894123456789512345678961234.library_7_123456789212345678931234567894123456789512345678961234
LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY schema_8_1123456789212345678931234567894123456789512345678961234.library_8_123456789212345678931234567894123456789512345678961234
LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY schema_9_1123456789212345678931234567894123456789512345678961234.library_9_123456789212345678931234567894123456789512345678961234
LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
CREATE LIBRARY schema_0_1123456789212345678931234567894123456789512345678961234.library_0_123456789212345678931234567894123456789512345678961234
LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
# extra long USING clause
CREATE FUNCTION
function_2123456789212345678931234567894123456789512345678961234()
RETURNS INT LANGUAGE JAVASCRIPT
USING (
schema_1_1123456789212345678931234567894123456789512345678961234.library_1_123456789212345678931234567894123456789512345678961234 AS alias_1_91123456789212345678931234567894123456789512345678961234,
schema_2_1123456789212345678931234567894123456789512345678961234.library_2_123456789212345678931234567894123456789512345678961234 AS alias_2_91123456789212345678931234567894123456789512345678961234,
schema_3_1123456789212345678931234567894123456789512345678961234.library_3_123456789212345678931234567894123456789512345678961234 AS alias_3_91123456789212345678931234567894123456789512345678961234,
schema_4_1123456789212345678931234567894123456789512345678961234.library_4_123456789212345678931234567894123456789512345678961234 AS alias_4_91123456789212345678931234567894123456789512345678961234,
schema_5_1123456789212345678931234567894123456789512345678961234.library_5_123456789212345678931234567894123456789512345678961234 AS alias_5_91123456789212345678931234567894123456789512345678961234,
schema_6_1123456789212345678931234567894123456789512345678961234.library_6_123456789212345678931234567894123456789512345678961234 AS alias_6_91123456789212345678931234567894123456789512345678961234,
schema_7_1123456789212345678931234567894123456789512345678961234.library_7_123456789212345678931234567894123456789512345678961234 AS alias_7_91123456789212345678931234567894123456789512345678961234,
schema_8_1123456789212345678931234567894123456789512345678961234.library_8_123456789212345678931234567894123456789512345678961234 AS alias_8_91123456789212345678931234567894123456789512345678961234,
schema_9_1123456789212345678931234567894123456789512345678961234.library_9_123456789212345678931234567894123456789512345678961234 AS alias_9_91123456789212345678931234567894123456789512345678961234,
schema_0_1123456789212345678931234567894123456789512345678961234.library_0_123456789212345678931234567894123456789512345678961234 AS alias_0_91123456789212345678931234567894123456789512345678961234
) AS $$ return 42 $$;
Warnings:
Warning	6001	Language component: Not available.
SHOW CREATE FUNCTION function_2123456789212345678931234567894123456789512345678961234;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
function_2123456789212345678931234567894123456789512345678961234	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `function_2123456789212345678931234567894123456789512345678961234`() RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`schema_1_1123456789212345678931234567894123456789512345678961234`.`library_1_123456789212345678931234567894123456789512345678961234` AS `alias_1_91123456789212345678931234567894123456789512345678961234`, `schema_2_1123456789212345678931234567894123456789512345678961234`.`library_2_123456789212345678931234567894123456789512345678961234` AS `alias_2_91123456789212345678931234567894123456789512345678961234`, `schema_3_1123456789212345678931234567894123456789512345678961234`.`library_3_123456789212345678931234567894123456789512345678961234` AS `alias_3_91123456789212345678931234567894123456789512345678961234`, `schema_4_1123456789212345678931234567894123456789512345678961234`.`library_4_123456789212345678931234567894123456789512345678961234` AS `alias_4_91123456789212345678931234567894123456789512345678961234`, `schema_5_1123456789212345678931234567894123456789512345678961234`.`library_5_123456789212345678931234567894123456789512345678961234` AS `alias_5_91123456789212345678931234567894123456789512345678961234`, `schema_6_1123456789212345678931234567894123456789512345678961234`.`library_6_123456789212345678931234567894123456789512345678961234` AS `alias_6_91123456789212345678931234567894123456789512345678961234`, `schema_7_1123456789212345678931234567894123456789512345678961234`.`library_7_123456789212345678931234567894123456789512345678961234` AS `alias_7_91123456789212345678931234567894123456789512345678961234`, `schema_8_1123456789212345678931234567894123456789512345678961234`.`library_8_123456789212345678931234567894123456789512345678961234` AS `alias_8_91123456789212345678931234567894123456789512345678961234`, `schema_9_1123456789212345678931234567894123456789512345678961234`.`library_9_123456789212345678931234567894123456789512345678961234` AS `alias_9_91123456789212345678931234567894123456789512345678961234`, `schema_0_1123456789212345678931234567894123456789512345678961234`.`library_0_123456789212345678931234567894123456789512345678961234` AS `alias_0_91123456789212345678931234567894123456789512345678961234`)
AS $$ return 42 $$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
SELECT * FROM INFORMATION_SCHEMA.ROUTINE_LIBRARIES
ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME, ROUTINE_TYPE, LIBRARY_CATALOG, LIBRARY_SCHEMA, LIBRARY_NAME, LIBRARY_VERSION;
ROUTINE_CATALOG	ROUTINE_SCHEMA	ROUTINE_NAME	ROUTINE_TYPE	LIBRARY_CATALOG	LIBRARY_SCHEMA	LIBRARY_NAME	LIBRARY_VERSION
def	test	function_2123456789212345678931234567894123456789512345678961234	FUNCTION	def	schema_0_1123456789212345678931234567894123456789512345678961234	library_0_123456789212345678931234567894123456789512345678961234	NULL
def	test	function_2123456789212345678931234567894123456789512345678961234	FUNCTION	def	schema_1_1123456789212345678931234567894123456789512345678961234	library_1_123456789212345678931234567894123456789512345678961234	NULL
def	test	function_2123456789212345678931234567894123456789512345678961234	FUNCTION	def	schema_2_1123456789212345678931234567894123456789512345678961234	library_2_123456789212345678931234567894123456789512345678961234	NULL
def	test	function_2123456789212345678931234567894123456789512345678961234	FUNCTION	def	schema_3_1123456789212345678931234567894123456789512345678961234	library_3_123456789212345678931234567894123456789512345678961234	NULL
def	test	function_2123456789212345678931234567894123456789512345678961234	FUNCTION	def	schema_4_1123456789212345678931234567894123456789512345678961234	library_4_123456789212345678931234567894123456789512345678961234	NULL
def	test	function_2123456789212345678931234567894123456789512345678961234	FUNCTION	def	schema_5_1123456789212345678931234567894123456789512345678961234	library_5_123456789212345678931234567894123456789512345678961234	NULL
def	test	function_2123456789212345678931234567894123456789512345678961234	FUNCTION	def	schema_6_1123456789212345678931234567894123456789512345678961234	library_6_123456789212345678931234567894123456789512345678961234	NULL
def	test	function_2123456789212345678931234567894123456789512345678961234	FUNCTION	def	schema_7_1123456789212345678931234567894123456789512345678961234	library_7_123456789212345678931234567894123456789512345678961234	NULL
def	test	function_2123456789212345678931234567894123456789512345678961234	FUNCTION	def	schema_8_1123456789212345678931234567894123456789512345678961234	library_8_123456789212345678931234567894123456789512345678961234	NULL
def	test	function_2123456789212345678931234567894123456789512345678961234	FUNCTION	def	schema_9_1123456789212345678931234567894123456789512345678961234	library_9_123456789212345678931234567894123456789512345678961234	NULL
DROP FUNCTION function_2123456789212345678931234567894123456789512345678961234;
# Orphaned SP where one of its libraries is deleted.
CREATE LIBRARY deleted_library LANGUAGE JAVASCRIPT
AS 'export function f(n) {return n}';
Warnings:
Warning	6001	Language component: Not available.
CREATE FUNCTION orphaned_function() RETURNS INT LANGUAGE JAVASCRIPT USING (deleted_library)
AS 'return deleted_library.f(42)';
Warnings:
Warning	6001	Language component: Not available.
DROP LIBRARY deleted_library;
SHOW CREATE FUNCTION orphaned_function;
Function	sql_mode	Create Function	character_set_client	collation_connection	Database Collation
orphaned_function	ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION	CREATE DEFINER=`root`@`localhost` FUNCTION `orphaned_function`() RETURNS int
    LANGUAGE JAVASCRIPT
    USING (`test`.`deleted_library`)
AS $$return deleted_library.f(42)$$	utf8mb4	utf8mb4_0900_ai_ci	utf8mb4_0900_ai_ci
Warnings:
Warning	6436	The routine 'orphaned_function' references a missing library 'test.deleted_library' or definer/invoker of the routine lack rights to use it.
SHOW WARNINGS;
Level	Code	Message
Warning	6436	The routine 'orphaned_function' references a missing library 'test.deleted_library' or definer/invoker of the routine lack rights to use it.
DROP FUNCTION orphaned_function;
DROP DATABASE schema_1_1123456789212345678931234567894123456789512345678961234;
DROP DATABASE schema_2_1123456789212345678931234567894123456789512345678961234;
DROP DATABASE schema_3_1123456789212345678931234567894123456789512345678961234;
DROP DATABASE schema_4_1123456789212345678931234567894123456789512345678961234;
DROP DATABASE schema_5_1123456789212345678931234567894123456789512345678961234;
DROP DATABASE schema_6_1123456789212345678931234567894123456789512345678961234;
DROP DATABASE schema_7_1123456789212345678931234567894123456789512345678961234;
DROP DATABASE schema_8_1123456789212345678931234567894123456789512345678961234;
DROP DATABASE schema_9_1123456789212345678931234567894123456789512345678961234;
DROP DATABASE schema_0_1123456789212345678931234567894123456789512345678961234;
SHOW STATUS LIKE 'Com%library';
Variable_name	Value
Com_alter_library	15
Com_create_library	49
Com_drop_library	34
Com_show_create_library	19
