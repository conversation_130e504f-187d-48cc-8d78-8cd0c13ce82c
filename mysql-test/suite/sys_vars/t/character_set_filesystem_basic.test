############## mysql-test\t\character_set_filesystem_basic.test ###############
#                                                                             #
# Variable Name: character_set_filesystem                                     #
# Scope: GLOBAL | SESSION                                                     #
# Access Type: Dynamic                                                        #
# Data Type: string                                                           #
# Default Value: latin5                                                       #
# Range:                                                                      #
#                                                                             #
#                                                                             #
# Creation Date: 2008-02-07                                                   #
# Author:  Rizwan                                                             #
#                                                                             #
# Description: Test Cases of Dynamic System Variable character_set_filesystem #
#              that checks the behavior of this variable in the following ways#
#              * Default Value                                                #
#              * Valid & Invalid values                                       #
#              * Scope & Access method                                        #
#              * Data Integrity                                               #
#                                                                             #
# Reference: http://dev.mysql.com/doc/refman/5.1/en/                          #
#  server-system-variables.html                                               #
#                                                                             #
###############################################################################
--source include/load_sysvars.inc

###################################################
##       START OF character_set_filesystem TESTS ##
###################################################

#############################################################
#                 Save initial value                        #
#############################################################
SET @global_start_value = @@global.character_set_filesystem;
SELECT @global_start_value;
# Save initial session value
SET @session_start_value = @@character_set_filesystem;
SELECT @session_start_value;
# now save using local access 
SET @session_start_value = @@local.character_set_filesystem;
SELECT @session_start_value;
# save using default access (session)
SET @session_start_value = @@session.character_set_filesystem;
SELECT @session_start_value;

--echo '#--------------------FN_DYNVARS_008_01------------------#'
###############################################################################
#       Test Variable access and assignment with and without @@               #
###############################################################################
# select without @@
--error ER_BAD_FIELD_ERROR
SELECT character_set_filesystem;
# assign value without @@
SET character_set_filesystem=utf8mb3;
SELECT @@session.character_set_filesystem;
# assign global variable without @@
--Error ER_PARSE_ERROR
SET global.character_set_filesystem=utf8mb3;
# using another syntax for accessing session variable
SET session character_set_filesystem=utf8mb3;
# accessing variable with scope the wrong way
--Error ER_BAD_FIELD_ERROR
SELECT session character_set_filesystem;
# using another syntax for accessing dynamic variable
SET global character_set_filesystem=utf8mb3;
--Error ER_BAD_FIELD_ERROR
SELECT global character_set_filesystem;

--echo '#--------------------FN_DYNVARS_008_02-------------------------#'
###############################################################################
# Check the DEFAULT value of character_set_filesystem for session and global  #
###############################################################################
SET @@character_set_filesystem = latin5;
SET @@character_set_filesystem = DEFAULT;
SELECT @@character_set_filesystem AS DEFAULT_VALUE;

SET @@global.character_set_filesystem = latin5;
SET @@global.character_set_filesystem = DEFAULT;
SELECT @@global.character_set_filesystem AS DEFAULT_VALUE;

--echo '#--------------------FN_DYNVARS_008_03-------------------------#'
############################################################################
#     see if setting global value changes session value and vice versa     #
############################################################################
SET @@session.character_set_filesystem = utf8mb3;
SELECT @@session.character_set_filesystem;
SET @@global.character_set_filesystem = latin2;
SELECT @@global.character_set_filesystem;
SELECT @@session.character_set_filesystem AS res_is_utf8;

SET @@session.character_set_filesystem = latin5;
SELECT @@session.character_set_filesystem;
SELECT @@global.character_set_filesystem AS res_is_latin2;
# composite check
SELECT @@global.character_set_filesystem=
 @@session.character_set_filesystem AS res_is_false;

--echo '#--------------------FN_DYNVARS_008_04-------------------------#'
################################################################################
# Check if accessing variable with & without session point to session variable #
################################################################################
SELECT @@character_set_filesystem = @@session.character_set_filesystem AS res;
SELECT @@character_set_filesystem = @@local.character_set_filesystem AS res;

--echo '#--------------------FN_DYNVARS_008_05-------------------------#'
###########################################################################
#     Check if combining character set works                              #
###########################################################################
--Error ER_BAD_FIELD_ERROR
SET @@character_set_filesystem = utf8mb3 + latin2;

--echo '#--------------------FN_DYNVARS_008_06-------------------------#'
###############################################################################
# Change the value of character_set_filesystem to a valid value for session   #
###############################################################################

let charset_variable = @@session.character_set_filesystem;
--source suite/sys_vars/inc/charset_basic.inc


--echo '#--------------------FN_DYNVARS_008_07-------------------------#'
##############################################################################
# Change the value of character_set_filesystem to a valid value for global   #
##############################################################################

let charset_variable = @@global.character_set_filesystem;
--source suite/sys_vars/inc/charset_basic.inc


--echo '#--------------------FN_DYNVARS_008_08-------------------------#'
#########################################################################
#  Change the value of character_set_filesystem to a valid value with   #
#  uppercase,lowercase and mixedcase                                    #
#########################################################################
SET @@character_set_filesystem = utf8mb3;
SELECT @@character_set_filesystem;
SET @@character_set_filesystem = utf8mb3;
SELECT @@character_set_filesystem;
SET @@character_set_filesystem = utf8mb3;
SELECT @@character_set_filesystem;

--echo '#--------------------FN_DYNVARS_008_09-------------------------#'
##############################################################
#     Check if 1,2,3, ... values can be used on variable     #
##############################################################
SET @@character_set_filesystem = 1;
SELECT @@character_set_filesystem;
SET @@character_set_filesystem = 2;
SELECT @@character_set_filesystem;
SET @@character_set_filesystem = 3;
SELECT @@character_set_filesystem;
SET @@character_set_filesystem = 36;
SELECT @@character_set_filesystem;
SET @@character_set_filesystem = 99;
SELECT @@character_set_filesystem;

--Error ER_UNKNOWN_CHARACTER_SET
SET @@character_set_filesystem = 100;

SET @total_charset = (SELECT count(*) FROM INFORMATION_SCHEMA.CHARACTER_SETS);
SELECT @total_charset;

--echo '#--------------------FN_DYNVARS_008_10-------------------------#'
################################################################################
# Change the value of character_set_filesystem to an invalid value for session #
################################################################################
--Error ER_UNKNOWN_CHARACTER_SET
SET @@character_set_filesystem = abc;
--Error ER_UNKNOWN_CHARACTER_SET
SET @@character_set_filesystem = 1utf8;
--Error ER_UNKNOWN_CHARACTER_SET
SET @@character_set_filesystem = 0;
--Error ER_WRONG_TYPE_FOR_VAR
SET @@character_set_filesystem = 1.1;
--Error ER_UNKNOWN_CHARACTER_SET
SET @@character_set_filesystem = -1;
--Error ER_UNKNOWN_CHARACTER_SET
SET @@character_set_filesystem = '';
--Error ER_UNKNOWN_CHARACTER_SET
SET @@character_set_filesystem = 'utf';
SET @@character_set_filesystem = true;
SELECT @@character_set_filesystem AS res_with_true;

--Error ER_UNKNOWN_CHARACTER_SET
SET @@character_set_filesystem = ON;

--echo '#--------------------FN_DYNVARS_008_11-------------------------#'
################################################################################
#  Change the value of character_set_filesystem to an invalid value for global #
################################################################################
--Error ER_UNKNOWN_CHARACTER_SET
SET @@global.character_set_filesystem = abc;
--Error ER_UNKNOWN_CHARACTER_SET
SET @@global.character_set_filesystem = 1utf8;
--Error ER_UNKNOWN_CHARACTER_SET
SET @@global.character_set_filesystem = 0;
--Error ER_WRONG_TYPE_FOR_VAR
SET @@global.character_set_filesystem = 1.1;
--Error ER_UNKNOWN_CHARACTER_SET
SET @@global.character_set_filesystem = -1;
--Error ER_UNKNOWN_CHARACTER_SET
SET @@global.character_set_filesystem = '';
--Error ER_UNKNOWN_CHARACTER_SET
SET @@global.character_set_filesystem = 'utf';

SET @@global.character_set_filesystem = true;
--Error ER_UNKNOWN_CHARACTER_SET
SET @@global.character_set_filesystem = ON;

--echo '#--------------------FN_DYNVARS_008_12-------------------------#'
##############################################################################
#     Check if the value in GLOBAL Table matches value in variable           #
##############################################################################
--disable_warnings
SELECT @@global.character_set_filesystem =
 (SELECT VARIABLE_VALUE FROM performance_schema.global_variables
  WHERE VARIABLE_NAME='character_set_filesystem') AS res;
--enable_warnings
SET @@global.character_set_filesystem = 1;
SELECT @@global.character_set_filesystem;
--disable_warnings
SELECT @@global.character_set_filesystem =
 (SELECT VARIABLE_VALUE FROM performance_schema.global_variables
  WHERE VARIABLE_NAME='character_set_filesystem') AS res;
--enable_warnings

--echo '#--------------------FN_DYNVARS_008_13-------------------------#'
#############################################################################
#     Check if the value in SESSION Table matches value in variable         #
#############################################################################
--disable_warnings
SELECT @@character_set_filesystem =
 (SELECT VARIABLE_VALUE FROM performance_schema.session_variables
  WHERE VARIABLE_NAME='character_set_filesystem') AS res;
SELECT @@local.character_set_filesystem =
 (SELECT VARIABLE_VALUE FROM performance_schema.session_variables
  WHERE VARIABLE_NAME='character_set_filesystem') AS res;
SELECT @@session.character_set_filesystem =
 (SELECT VARIABLE_VALUE FROM performance_schema.session_variables
  WHERE VARIABLE_NAME='character_set_filesystem') AS res;
--enable_warnings

####################################
#     Restore initial value        #
####################################
SET @@global.character_set_filesystem = @global_start_value;
SELECT @@global.character_set_filesystem;
SET @@session.character_set_filesystem = @session_start_value;
SELECT @@session.character_set_filesystem;

#############################################################
#                 END OF character_set_filesystem TESTS     #
#############################################################
