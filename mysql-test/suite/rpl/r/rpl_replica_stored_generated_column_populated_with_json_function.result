#
# 0. Create source-replica topology
include/rpl/init_source_replica.inc
Warnings:
Note	####	Sending passwords in plain text without SSL/TLS is extremely insecure.
Note	####	Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information.
[connection master]
# Store the initial values
include/save_sysvars.inc [ "SESSION.binlog_row_image" ]
[connection slave]
# Setup and test each case and verify the result
[connection master]
#
# 1. Create table on the source with primary key
#        and binlog_row_image = 'MINIMAL'
SET @@SESSION.binlog_row_image = 'MINIMAL';
CREATE TABLE `log_data` (
`id` INT UNSIGNED NOT NULL,
`data` MEDIUMTEXT NOT NULL,
`gcol` JSON GENERATED ALWAYS AS (json_extract(`data`,'$.x')) STORED,
KEY `idx` ((cast(json_extract(`gcol`,_utf8mb4'$') as char(128) array))),
PRIMARY KEY (`id`)
) ENGINE = InnoDB;
#
# 1.1. Insert row on the source
INSERT INTO log_data (id, data) VALUES(1, '{"x":"aaa"}');
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column populated]
#
# 1.2. Update row on the source and ensure that row is
#          updated on the replica
[connection master]
UPDATE log_data SET data = '{}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated to null]
[connection master]
UPDATE log_data SET data='{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated]
#
# 1.3. Delete row on the source and ensure that row is
#          deleted on the replica
[connection master]
DELETE FROM log_data WHERE data = '{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table doesn't contain any row]
# Clean up
[connection master]
DROP TABLE log_data;
include/rpl/sync_to_replica.inc
[connection master]
#
# 2. Create table on the source without keys
#        and binlog_row_image = 'MINIMAL'
SET @@SESSION.binlog_row_image = 'MINIMAL';
CREATE TABLE `log_data` (
`id` INT UNSIGNED NOT NULL,
`data` MEDIUMTEXT NOT NULL,
`gcol` JSON GENERATED ALWAYS AS (json_extract(`data`,'$.x')) STORED
) ENGINE = InnoDB;
#
# 2.1. Insert row on the source
INSERT INTO log_data (id, data) VALUES(1, '{"x":"aaa"}');
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column populated]
#
# 2.2. Update row on the source and ensure that row is
#          updated on the replica
[connection master]
UPDATE log_data SET data = '{}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated to null]
[connection master]
UPDATE log_data SET data='{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated]
#
# 2.3. Delete row on the source and ensure that row is
#          deleted on the replica
[connection master]
DELETE FROM log_data WHERE data = '{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table doesn't contain any row]
# Clean up
[connection master]
DROP TABLE log_data;
include/rpl/sync_to_replica.inc
[connection master]
#
# 3. Create table on the source with primary key
#        and binlog_row_image = 'FULL'
SET @@SESSION.binlog_row_image = 'FULL';
CREATE TABLE `log_data` (
`id` INT UNSIGNED NOT NULL,
`data` MEDIUMTEXT NOT NULL,
`gcol` JSON GENERATED ALWAYS AS (json_extract(`data`,'$.x')) STORED,
KEY `idx` ((cast(json_extract(`gcol`,_utf8mb4'$') as char(128) array))),
PRIMARY KEY (`id`)
) ENGINE = InnoDB;
#
# 3.1. Insert row on the source
INSERT INTO log_data (id, data) VALUES(1, '{"x":"aaa"}');
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column populated]
#
# 3.2. Update row on the source and ensure that row is
#          updated on the replica
[connection master]
UPDATE log_data SET data = '{}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated to null]
[connection master]
UPDATE log_data SET data='{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated]
#
# 3.3. Delete row on the source and ensure that row is
#          deleted on the replica
[connection master]
DELETE FROM log_data WHERE data = '{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table doesn't contain any row]
# Clean up
[connection master]
DROP TABLE log_data;
include/rpl/sync_to_replica.inc
[connection master]
#
# 4. Create table on the source without keys
#        and binlog_row_image = 'FULL'
SET @@SESSION.binlog_row_image = 'FULL';
CREATE TABLE `log_data` (
`id` INT UNSIGNED NOT NULL,
`data` MEDIUMTEXT NOT NULL,
`gcol` JSON GENERATED ALWAYS AS (json_extract(`data`,'$.x')) STORED
) ENGINE = InnoDB;
#
# 4.1. Insert row on the source
INSERT INTO log_data (id, data) VALUES(1, '{"x":"aaa"}');
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column populated]
#
# 4.2. Update row on the source and ensure that row is
#          updated on the replica
[connection master]
UPDATE log_data SET data = '{}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated to null]
[connection master]
UPDATE log_data SET data='{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated]
#
# 4.3. Delete row on the source and ensure that row is
#          deleted on the replica
[connection master]
DELETE FROM log_data WHERE data = '{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table doesn't contain any row]
# Clean up
[connection master]
DROP TABLE log_data;
include/rpl/sync_to_replica.inc
[connection master]
#
# 5. Create table on the replica with an an extra stored
#        column and binlog_row_image = 'MINIMAL'
[connection master]
include/rpl/disable_binlog.inc
CREATE TABLE `log_data` (
`id` INT UNSIGNED NOT NULL,
`data` MEDIUMTEXT NOT NULL,
PRIMARY KEY (`id`)
) ENGINE = InnoDB;
include/rpl/restore_binlog.inc
[connection slave]
include/rpl/disable_binlog.inc
CREATE TABLE `log_data` (
`id` INT UNSIGNED NOT NULL,
`data` MEDIUMTEXT NOT NULL,
`gcol` JSON GENERATED ALWAYS AS (json_extract(`data`,'$.x')) STORED,
PRIMARY KEY (`id`)
) ENGINE = InnoDB;
include/rpl/restore_binlog.inc
[connection master]
SET @@SESSION.binlog_row_image = 'MINIMAL';
#
# 5.1. Insert row on the source
INSERT INTO log_data (id, data) VALUES(1, '{"x":"aaa"}');
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column populated]
#
# 5.2. Update row on the source and ensure that row is
#          updated on the replica
[connection master]
UPDATE log_data SET data = '{}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated to null]
[connection master]
UPDATE log_data SET data='{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated]
#
# 5.3. Delete row on the source and ensure that row is
#          deleted on the replica
[connection master]
DELETE FROM log_data WHERE data = '{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table doesn't contain any row]
# Clean up
[connection master]
DROP TABLE log_data;
include/rpl/sync_to_replica.inc
[connection master]
#
# 6. The replica has REQUIRE_TABLE_PRIMARY_KEY_CHECK = GENERATE,
#        then create table on the source without keys
#        and binlog_row_image = 'MINIMAL'
[connection slave]
include/rpl/stop_replica.inc
CHANGE REPLICATION SOURCE TO REQUIRE_TABLE_PRIMARY_KEY_CHECK = GENERATE;
include/rpl/start_replica.inc
[connection master]
SET @@SESSION.binlog_row_image = 'MINIMAL';
CREATE TABLE `log_data` (
`id` INT UNSIGNED NOT NULL,
`data` MEDIUMTEXT NOT NULL,
`gcol` JSON GENERATED ALWAYS AS (json_extract(`data`,'$.x')) STORED
) ENGINE = InnoDB;
#
# 6.1. Insert row on the source
INSERT INTO log_data (id, data) VALUES(1, '{"x":"aaa"}');
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column populated]
#
# 6.2. Update row on the source and ensure that row is
#          updated on the replica
[connection master]
UPDATE log_data SET data = '{}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated to null]
[connection master]
UPDATE log_data SET data='{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated]
#
# 6.3. Delete row on the source and ensure that row is
#          deleted on the replica
[connection master]
DELETE FROM log_data WHERE data = '{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table doesn't contain any row]
# Clean up
[connection master]
DROP TABLE log_data;
include/rpl/sync_to_replica.inc
[connection master]
#
# 7. The replica has REQUIRE_TABLE_PRIMARY_KEY_CHECK = GENERATE,
#        then create table on the source without keys
#        and binlog_row_image = 'FULL'
[connection slave]
include/assert.inc [require_table_primary_key_check in performance_schema.replication_applier_configuration is set to GENERATE]
[connection master]
SET @@SESSION.binlog_row_image='FULL';
CREATE TABLE `log_data` (
`id` INT UNSIGNED NOT NULL,
`data` MEDIUMTEXT NOT NULL,
`gcol` JSON GENERATED ALWAYS AS (json_extract(`data`,'$.x')) STORED
) ENGINE = InnoDB;
#
# 7.1. Insert row on the source
INSERT INTO log_data (id, data) VALUES(1, '{"x":"aaa"}');
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column populated]
#
# 7.2. Update row on the source and ensure that row is
#          updated on the replica
[connection master]
UPDATE log_data SET data = '{}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated to null]
[connection master]
UPDATE log_data SET data='{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table contains a row with generated column updated]
#
# 7.3. Delete row on the source and ensure that row is
#          deleted on the replica
[connection master]
DELETE FROM log_data WHERE data = '{"x":"a"}';
include/rpl/sync_to_replica.inc
# verify that log_data has the correct values in the replica
include/assert.inc [The table doesn't contain any row]
# Clean up
[connection master]
DROP TABLE log_data;
include/rpl/sync_to_replica.inc
#
# 6. Clean up
[connection slave]
include/rpl/stop_replica.inc
CHANGE REPLICATION SOURCE TO REQUIRE_TABLE_PRIMARY_KEY_CHECK = PRIMARY_KEY_CHECK_VALUE;
include/rpl/start_replica.inc
[connection master]
include/restore_sysvars.inc
include/rpl/deinit.inc
