#############################################################################
# Original Author: JBM                                                      #
# Original Date: Aug/18/2005                                                #
# Updated: 08/29/2005  turned on diff and commented out debug SQL statements#
# Updated: sep/16/2015                                                      #
#          This testcase requires minimum stack size of 300KB to run on WIN.#
#          Modified *-master.opt file to increase the thread_stack to 400KB.#
#############################################################################
# TEST: SP to test alter table and nested SP calls                          #
#############################################################################

# clang/UBSAN needs to override the small thread stack in our -master.opt
call mtr.add_suppression("option 'thread_stack':");

# Includes
-- source include/have_binlog_format_row.inc
-- source include/rpl/set_privilege_checks_user_as_system_user.inc
-- source include/rpl/init_source_replica.inc


# Begin clean up test section
connection master;

# Begin test section 1 
CREATE TABLE test.t1 (a int, PRIMARY KEY(a));
INSERT INTO test.t1 VALUES (1);

delimiter |;
CREATE PROCEDURE test.p1()
BEGIN
  ALTER TABLE test.t1 ADD COLUMN b CHAR(4) AFTER a;
  UPDATE test.t1 SET b = 'rbr' WHERE a = 1; 
  CALL test.p2();
END|
CREATE PROCEDURE test.p2()
BEGIN
  ALTER TABLE test.t1 ADD COLUMN f FLOAT AFTER b;
  UPDATE test.t1 SET f = RAND() WHERE a = 1; 
  CALL test.p3();
END|
CREATE PROCEDURE test.p3()
BEGIN
 ALTER TABLE test.t1 RENAME test.t2;
 CALL test.p4();
END|
CREATE PROCEDURE test.p4()
BEGIN
 ALTER TABLE test.t2 ADD INDEX (f);
 ALTER TABLE test.t2 CHANGE a a INT UNSIGNED NOT NULL AUTO_INCREMENT;
 INSERT INTO test.t2 VALUES (NULL,'TEST',RAND());
 CALL test.p5();
END|
CREATE PROCEDURE test.p5()
BEGIN
 ALTER TABLE test.t2 ORDER BY f;
 INSERT INTO test.t2 VALUES (NULL,'STM',RAND());
 CALL test.p6();
END|
CREATE PROCEDURE test.p6()
BEGIN
 ALTER TABLE test.t2 ADD COLUMN b2 CHAR(4) FIRST;
 ALTER TABLE test.t2 ADD COLUMN to_drop BIT(8) AFTER b2;
 INSERT INTO test.t2 VALUES ('new',1,NULL,'STM',RAND());
 CALL test.p7();
END|
CREATE PROCEDURE test.p7()
BEGIN
 ALTER TABLE test.t2 DROP COLUMN to_drop;
 INSERT INTO test.t2 VALUES ('gone',NULL,'STM',RAND());
END|
delimiter ;|
CALL test.p1();

#SELECT * FROM test.t2;
--source include/rpl/sync_to_replica.inc
#SELECT * FROM test.t2;

--exec $MYSQL_DUMP --compact --order-by-primary --skip-extended-insert --no-create-info test > $MYSQLTEST_VARDIR/tmp/sp011_master.sql
--exec $MYSQL_DUMP_SLAVE --compact --order-by-primary --skip-extended-insert --no-create-info test > $MYSQLTEST_VARDIR/tmp/sp011_slave.sql

# Cleanup
connection master;
#show binlog events;
DROP PROCEDURE IF EXISTS test.p1;
DROP PROCEDURE IF EXISTS test.p2;
DROP PROCEDURE IF EXISTS test.p3;
DROP PROCEDURE IF EXISTS test.p4;
DROP PROCEDURE IF EXISTS test.p5;
DROP PROCEDURE IF EXISTS test.p6;
DROP PROCEDURE IF EXISTS test.p7;
--disable_warnings ER_BAD_TABLE_ERROR
DROP TABLE IF EXISTS test.t1;
--enable_warnings ER_BAD_TABLE_ERROR
DROP TABLE IF EXISTS test.t2;
--source include/rpl/sync_to_replica.inc

# Lets compare. Note: If they match test will pass, if they do not match
# the test will show that the diff statement failed and not reject file
# will be created. You will need to go to the mysql-test dir and diff
# the files your self to see what is not matching :-) Failed test 
# Will leave dump files in $MYSQLTEST_VARDIR/tmp

diff_files $MYSQLTEST_VARDIR/tmp/sp011_master.sql $MYSQLTEST_VARDIR/tmp/sp011_slave.sql;

# If all is good, when can cleanup our dump files.
--remove_file $MYSQLTEST_VARDIR/tmp/sp011_master.sql
--remove_file $MYSQLTEST_VARDIR/tmp/sp011_slave.sql

# End of 5.0 test case
--source include/rpl/deinit.inc
