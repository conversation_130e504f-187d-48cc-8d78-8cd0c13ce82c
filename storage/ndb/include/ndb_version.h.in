/*
   Copyright (c) 2004, 2025, Oracle and/or its affiliates.

   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.

   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.

   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
*/

#ifndef NDB_VERSION_H
#define NDB_VERSION_H

#include <stdbool.h>
#include <ndb_types.h>
#include <mysql_version.h>

/*
  Creates a composite version number from major, minor and build
  ex: NDB_MAKE_VERSION(5,1,47) => 0x00050147
*/
#define NDB_MAKE_VERSION(A,B,C) (((A) << 16) | ((B) << 8)  | ((C) << 0))

/*
  Creates a stringified version from major, minor and build
  ex: NDB_MAKE_STRING_VERSION(7,0,22) => "7.0.22"
*/
#define NDB_MAKE_QUOTED_VERSION(A,B,C) #A "." #B "." #C
#define NDB_MAKE_STRING_VERSION(A,B,C) NDB_MAKE_QUOTED_VERSION(A,B,C)

/* NDB version numbers and status  */
#define NDB_VERSION_MAJOR @NDB_VERSION_MAJOR@
#define NDB_VERSION_MINOR @NDB_VERSION_MINOR@
#define NDB_VERSION_BUILD @NDB_VERSION_BUILD@
#define NDB_VERSION_STATUS "@NDB_VERSION_STATUS@"

/* Composite version number for NDB */
#define NDB_VERSION_D \
  NDB_MAKE_VERSION(NDB_VERSION_MAJOR, NDB_VERSION_MINOR, NDB_VERSION_BUILD)

/* Version string for NDB, ex: "ndb-7.0.22" */
#define NDB_NDB_VERSION_STRING \
  "ndb-" NDB_MAKE_STRING_VERSION(NDB_VERSION_MAJOR, \
                                 NDB_VERSION_MINOR, \
                                 NDB_VERSION_BUILD) NDB_VERSION_STATUS

/*
  The version number of the MySQL Server that NDB is built
  with. Extracted from MYSQL_VERSION_ID
*/
#define NDB_MYSQL_VERSION_MAJOR ((MYSQL_VERSION_ID / 10000) % 100)
#define NDB_MYSQL_VERSION_MINOR ((MYSQL_VERSION_ID /100) % 100)
#define NDB_MYSQL_VERSION_BUILD (MYSQL_VERSION_ID % 100)

/* Composite version number for MYSQL Server */
#define NDB_MYSQL_VERSION_D \
  NDB_MAKE_VERSION(NDB_MYSQL_VERSION_MAJOR, \
                   NDB_MYSQL_VERSION_MINOR, \
                   NDB_MYSQL_VERSION_BUILD)

#define NDB_VERSION_STRING_BUF_SZ 100

#ifdef __cplusplus
extern "C" {
#endif

void ndbPrintVersion();

Uint32 ndbMakeVersion(Uint32 major, Uint32 minor, Uint32 build);

Uint32 ndbGetMajor(Uint32 version);
  
Uint32 ndbGetMinor(Uint32 version);
  
Uint32 ndbGetBuild(Uint32 version);

const char* ndbGetVersionString(Uint32 version, Uint32 mysql_version, const char * status,
                                char *buf, unsigned sz);
const char* ndbGetOwnVersionString();

Uint32 ndbGetOwnVersion();

#ifdef __cplusplus
}
#endif

#define NDB_VERSION_STRING ndbGetOwnVersionString()

#define NDB_VERSION ndbGetOwnVersion()

/**
 * This is updated each time a version of backup/lcp format is changed
 *   when it's updated, it's set to version that made the change
 */
#define NDB_BACKUP_VERSION NDB_MAKE_VERSION(6,3,11)

#define NDB_DISK_V2 NDB_MAKE_VERSION(7,6,0)
/**
 * From which version do we support rowid
 */ 
#define NDBD_ROWID_VERSION (NDB_MAKE_VERSION(5,1,6))
#define NDBD_FRAGID_VERSION (NDB_MAKE_VERSION(5,1,6))

#define NDBD_MICRO_GCP_62 NDB_MAKE_VERSION(6,2,5)
#define NDBD_MICRO_GCP_63 NDB_MAKE_VERSION(6,3,2)
#define NDBD_RAW_LCP MAKE_VERSION(6,3,11)
#define NDBD_MAX_RECVBYTESIZE_32K MAKE_VERSION(6,3,18)

#define NDBD_LOCAL_SYSFILE_VERSION NDB_MAKE_VERSION(7,6,3)

/**
 * Including vers. 'NDBD_SPJ_API_ONLY_LONG_TRANSID_AI',
 * TRANSID_AI received from a SPJ query execution
 * was required to always be a 'long signal'.
 * (As it was always sent with the FLUSH_AI pseudo-read)
 * From Cluster 7.6.2 and up, TRANSID_AI results rows from
 * SPJ execution can be sent as (packed) 'short' signals.
 */
#define NDBD_SPJ_API_ONLY_LONG_TRANSID_AI NDB_MAKE_VERSION(7,6,1)

static
inline
int
ndbd_spj_api_support_short_TRANSID_AI(Uint32 x)
{
  return x > NDBD_SPJ_API_ONLY_LONG_TRANSID_AI;
}

/**
 * Does the SPJ-block (contrary to SPJ_API above) support
 * short TRANSID_AI signals to be received.
 */
#define NDBD_SPJ_SHORT_TRANSID_AI NDB_MAKE_VERSION(8,0,22)

static
inline
int
ndbd_spj_support_short_TRANSID_AI(Uint32 x)
{
  return x >= NDBD_SPJ_SHORT_TRANSID_AI;
}

/**
 * An enhanced version of the SCAN_FRAGREQ signal has been
 * introduced in 7.6.4, where multiple fragments to be scanned
 * can be specified in a single 'REQ'.
 *
 * At the same time an updated version of QueryNode introduced
 * a new QN_SCAN_FRAG OpNode type, with an extended format
 * for specifying the scan 'batchSize'.
 *
 * That also deprecated (still supported) the former 'SCAN' type
 * QueryNode into QN_SCAN_FRAG_v1 and QN_SCAN_INDEX_v1.
 */
#define NDBD_SPJ_MULTIFRAG_SCAN NDB_MAKE_VERSION(7,6,4)

static
inline
int
ndbd_spj_multifrag_scan(Uint32 x)
{
  return x >= NDBD_SPJ_MULTIFRAG_SCAN;
}

/**
 * Support for wl#9819 ndbinfo.processes
 */
#define NDBD_PROCESSINFO_VERSION_75 NDB_MAKE_VERSION(7,5,7)
#define NDBD_PROCESSINFO_VERSION_76 NDB_MAKE_VERSION(7,6,2)
#define RELEASE_7_6_0 NDB_MAKE_VERSION(7,6,0)
static
inline
int
ndbd_supports_processinfo(Uint32 x)
{
  if(x < NDBD_PROCESSINFO_VERSION_75 ||
    (x >= RELEASE_7_6_0 && x < NDBD_PROCESSINFO_VERSION_76))
  {
    return 0;
  }
  return 1;
}

/**
 * Support for NDB_TABLE=PARTITION_BALANCE=...
 * This is a table property that can change the number of fragments per
 * table and still maintaining the automatic behaviour of NDB tables.
 * Thus the real fragment number is still a function of cluster size.
 */
#define NDBD_SUPPORT_PARTITION_BALANCE NDB_MAKE_VERSION(7,5,2)

static
inline
int
ndbd_support_partition_balance(Uint32 x)
{
  const Uint32 major = (x >> 16) & 0xFF;
  const Uint32 minor = (x >>  8) & 0xFF;
  if (major < 7)
  {
    return 0;
  }
  else if (major == 7)
  {
    if (minor < 5)
    {
      return 0;
    }
    else if (minor == 5)
    {
      return x >= NDBD_SUPPORT_PARTITION_BALANCE;
    }
  }
  return 1;
}

/**
 * Support for NDB_TABLE=READ_BACKUP=...
 * This is a table property that adapts the transaction protocol
 * on these tables. It requires all data nodes to support this
 * feature to be useful.
 */
#define NDBD_SUPPORT_READ_BACKUP NDB_MAKE_VERSION(7,5,2)

static
inline
int
ndbd_support_read_backup(Uint32 x)
{
  const Uint32 major = (x >> 16) & 0xFF;
  const Uint32 minor = (x >>  8) & 0xFF;
  if (major < 7)
  {
    return 0;
  }
  else if (major == 7)
  {
    if (minor < 5)
    {
      return 0;
    }
    else if (minor == 5)
    {
      return x >= NDBD_SUPPORT_READ_BACKUP;
    }
  }
  return 1;
}

/**
 * Support for NDB_TABLE=FULLY_REPLICATED=...
 * This is a table property that adapts the transaction protocol
 * on these tables. It requires all data nodes to support this
 * feature to be useful.
 */
#define NDBD_SUPPORT_FULLY_REPLICATED NDB_MAKE_VERSION(7,5,2)

static
inline
int
ndbd_support_fully_replicated(Uint32 x)
{
  const Uint32 major = (x >> 16) & 0xFF;
  const Uint32 minor = (x >>  8) & 0xFF;
  if (major < 7)
  {
    return 0;
  }
  else if (major == 7)
  {
    if (minor < 5)
    {
      return 0;
    }
    else if (minor == 5)
    {
      return x >= NDBD_SUPPORT_FULLY_REPLICATED;
    }
  }
  return 1;
}

/**
 * Uses new Partial LCP format for LCPs
 */
#define NDBD_USE_PARTIAL_LCP_v1 NDB_MAKE_VERSION(7,6,3)
#define NDBD_USE_PARTIAL_LCP_v2 NDB_MAKE_VERSION(7,6,4)

static
inline
int
ndbd_use_partial_lcp(Uint32 x)
{
  const Uint32 major = (x >> 16) & 0xFF;
  const Uint32 minor = (x >>  8) & 0xFF;
  if (major < 7)
  {
    return 0;
  }
  else if (major == 7)
  {
    if (minor < 5)
    {
      return 0;
    }
    else if (minor == 5)
    {
      return x >= NDBD_USE_PARTIAL_LCP_v2;
    }
  }
  return 1;
}

/**
 * Enable Redo control for LCPs
 */
#define NDBD_ENABLE_REDO_CONTROL_76 NDB_MAKE_VERSION(7,6,8)
#define NDBD_ENABLE_REDO_CONTROL_80 NDB_MAKE_VERSION(8,0,14)
static
inline
int
ndbd_enable_redo_control(Uint32 x)
{
  const Uint32 major = (x >> 16) & 0xFF;
  if (major < 8)
  {
    return x >= NDBD_ENABLE_REDO_CONTROL_76;
  }
  return x >= NDBD_ENABLE_REDO_CONTROL_80;
}

/**
 * Drop6 version, have impact on some file formats.
 */

static
inline
int
ndbd_drop6(Uint32 x)
{
  const Uint32 major = (x >> 16) & 0xFF;
  const Uint32 minor = (x >>  8) & 0xFF;
  return (major == 5) && (minor == 2);
}

static
inline
int
ndbd_backup_file_fragid(Uint32 x)
{
  return (x >= NDBD_FRAGID_VERSION) && !ndbd_drop6(x);
}

/**
 * Fragmented SUB_TABLE_DATA signal.
 * Introduced with wl#11160 for supporting 30'000 bytes rows.
 */

#define NDBD_FRAG_SUB_TABLE_DATA NDB_MAKE_VERSION(8,0,18)

static
inline
int
ndbd_frag_sub_table_data(Uint32 x)
{
  return x >= NDBD_FRAG_SUB_TABLE_DATA;
}

/**
 * Fragmented FIRE_TRIG_ORD signal.
 * Introduced with wl#11160 for supporting 30'000 bytes rows.
 */

#define NDBD_FRAG_FIRE_TRIG_ORD NDB_MAKE_VERSION(8,0,18)

static
inline
int
ndbd_frag_fire_trig_ord(Uint32 x)
{
  return x >= NDBD_FRAG_FIRE_TRIG_ORD;
}

/**
 * Fragmented LQHKEYREQ signal.
 * Introduced with wl#11160 for supporting 30'000 bytes rows.
 */

#define NDBD_FRAG_LQHKEYREQ NDB_MAKE_VERSION(8,0,18)

static
inline
int
ndbd_frag_lqhkeyreq(Uint32 x)
{
  return x >= NDBD_FRAG_LQHKEYREQ;
}

/**
 * Fragmented TCKEYREQ and TCINDXREQ signals.
 * Introduced with wl#11160 for supporting 30'000 bytes rows.
 */

#define NDBD_FRAG_TCKEYREQ NDB_MAKE_VERSION(8,0,18)

static
inline
int
ndbd_frag_tckeyreq(Uint32 x)
{
  return x >= NDBD_FRAG_TCKEYREQ;
}

#define NDBD_NOWAIT_KEYREQ NDB_MAKE_VERSION(8,0,18)

/**
 * wl#13120 extended NdbScanFilter and NdbInterpretedCode to allow
 * two columns to be compared.
 */

#define NDBD_SUPPORT_CMP_COLUMNS NDB_MAKE_VERSION(8,0,18)

static
inline
bool
ndbd_support_column_cmp(Uint32 x)
{
  return x >= NDBD_SUPPORT_CMP_COLUMNS;
}

/**
 * Send the node bitmask in a long signal section:
 */
#define NDBD_NODE_BITMASK_IN_SECTION_80 NDB_MAKE_VERSION(8,0,18)

static
inline
int
ndbd_send_node_bitmask_in_section(Uint32 x)
{
  return x >= NDBD_NODE_BITMASK_IN_SECTION_80;
}

#define NDBD_UPGRADE_OK_74 NDB_MAKE_VERSION(7,4,6)
#define NDBD_UPGRADE_OK_75 NDB_MAKE_VERSION(7,5,4)
#define NDBD_UPGRADE_OK_76 NDB_MAKE_VERSION(7,6,4)

static
inline
int
ndbd_upgrade_ok(Uint32 x)
{
  const Uint32 major = (x >> 16) & 0xFF;
  const Uint32 minor = (x >>  8) & 0xFF;
  if (major < 7)
  {
    return 0;
  }
  else if (major == 7)
  {
    if (minor < 4)
    {
      return 0;
    }
    else if (minor == 4)
    {
      return x >= NDBD_UPGRADE_OK_74;
    }
    else if (minor == 5)
    {
      return x >= NDBD_UPGRADE_OK_75;
    }
    else if (minor == 6)
    {
      return x >= NDBD_UPGRADE_OK_76;
    }
    else
    {
      return 0;
    }
  }
  else
  {
    return 1;
  }
}

#define NDB_USE_CONFIG_VERSION_V2_80 NDB_MAKE_VERSION(8,0,18)

static
inline
int
ndb_config_version_v2(Uint32 x)
{
  return x >= NDB_USE_CONFIG_VERSION_V2_80;
}

/**
 * Formerly defined as
 * #define MAX_FRM_DATA_SIZE 6000
 */
#define NDB_SHORT_OPAQUE_METADATA_MAX_BYTES 6000

/**
 * Multiple transporters per node in same node group is available.
 */
#define NDBD_MULTI_NODE_GROUP_TRANSPORTERS_v8 NDB_MAKE_VERSION(8,0,20)
static
inline
int
ndbd_use_multi_ng_trps(Uint32 x)
{
  return x >= NDBD_MULTI_NODE_GROUP_TRANSPORTERS_v8;
}

/*
 * Send both cntr_started and started bitmasks in CNTR_START_CONF
 */
#define NDBD_SEND_STARTED_BITMASK NDB_MAKE_VERSION(8,0,19)
static
inline
int
ndbd_send_started_bitmask(Uint32 x)
{
  return x >= NDBD_SEND_STARTED_BITMASK;
}

// New limitations from WL#12757

#define NDBD_80_PROTOCOL_ACCEPT_74 NDB_MAKE_VERSION(7,4,6)
#define NDBD_80_PROTOCOL_ACCEPT_75 NDB_MAKE_VERSION(7,5,4)
#define NDBD_80_PROTOCOL_ACCEPT_76 NDB_MAKE_VERSION(7,6,4)

static
inline
int
ndbd_protocol_accepted_by_8_0(Uint32 x)
{
  const Uint32 major = (x >> 16) & 0xFF;
  const Uint32 minor = (x >>  8) & 0xFF;
  if (major < 7)
  {
    return 0;
  }
  else if (major == 7)
  {
    if (minor < 4)
    {
      return 0;
    }
    else if (minor == 4)
    {
      return x >= NDBD_80_PROTOCOL_ACCEPT_74;
    }
    else if (minor == 5)
    {
      return x >= NDBD_80_PROTOCOL_ACCEPT_75;
    }
    else if (minor == 6)
    {
      return x >= NDBD_80_PROTOCOL_ACCEPT_76;
    }
  }
  return 1;
}

/*
 * Send/expect the extra 'still active' word as part of ScanTabConf and
 * ScanFragConf signals.
 */
#define NDBD_SEND_ACTIVE_BITMASK NDB_MAKE_VERSION(8,0,20)
static
inline
int
ndbd_send_active_bitmask(Uint32 x)
{
  return x >= NDBD_SEND_ACTIVE_BITMASK;
}



#define NDBD_WAITGCP_SHUTDOWNSYNC_73 NDB_MAKE_VERSION(7,3,30)
#define NDBD_WAITGCP_SHUTDOWNSYNC_74 NDB_MAKE_VERSION(7,4,29)
#define NDBD_WAITGCP_SHUTDOWNSYNC_75 NDB_MAKE_VERSION(7,5,19)
#define NDBD_WAITGCP_SHUTDOWNSYNC_76 NDB_MAKE_VERSION(7,6,15)
#define NDBD_WAITGCP_SHUTDOWNSYNC_80 NDB_MAKE_VERSION(8,0,21)

static
inline
int
ndbd_support_waitgcp_shutdownsync(Uint32 x)
{
  const Uint32 major = (x >> 16) & 0xFF;
  const Uint32 minor = (x >>  8) & 0xFF;
  if (major < 7)
  {
    return 0;
  }
  else if (major == 7)
  {
    if (minor < 3)
    {
      return 0;
    }
    else if (minor == 3)
    {
      return x >= NDBD_WAITGCP_SHUTDOWNSYNC_73;
    }
    else if (minor == 4)
    {
      return x >= NDBD_WAITGCP_SHUTDOWNSYNC_74;
    }
    else if (minor == 5)
    {
      return x >= NDBD_WAITGCP_SHUTDOWNSYNC_75;
    }
    else if (minor == 6)
    {
      return x >= NDBD_WAITGCP_SHUTDOWNSYNC_76;
    }
  }
  else if (major == 8)
  {
    return x >= NDBD_WAITGCP_SHUTDOWNSYNC_80;
  }
  return 1;
}

/*
 * Data Node sends minimum API version connected to cluster
 * as a part of ApiRegConf reply since 8.0.21
 */
#define NDBD_SEND_MIN_API_VERSION NDB_MAKE_VERSION(8,0,21)
static
inline
int
ndbd_send_min_api_version(Uint32 x)
{
  return x >= NDBD_SEND_MIN_API_VERSION;
}


// Backup file encryption

#define NDBD_SUPPORT_BACKUP_FILE_ENCRYPTION NDB_MAKE_VERSION(8,0,22)

static
inline
bool
ndbd_support_backup_file_encryption(Uint32 x)
{
  return x >= NDBD_SUPPORT_BACKUP_FILE_ENCRYPTION;
}

/*
 * NdbInterpreter may compare NULL values according to the semantic
 * specified by the SQL standard, which is what MySQL expect. (WL#14476)
 */
#define NDBD_SUPPORT_SQL_COMPARE_SEMANTICS NDB_MAKE_VERSION(8,0,26)
static
inline
int
ndbd_support_sql_compare_semantics(Uint32 x)
{
  return x >= NDBD_SUPPORT_SQL_COMPARE_SEMANTICS;
}

/*
 * Only send GSN_TRP_KEEP_ALIVE to data nodes from 8.0.27 and newer.
 */
#define NDBD_SUPPORT_TRP_KEEP_ALIVE NDB_MAKE_VERSION(8,0,27)
static
inline
bool
ndbd_support_trp_keep_alive(Uint32 x)
{
  return x >= NDBD_SUPPORT_TRP_KEEP_ALIVE;
}

/*
 * SPJ implemented support for creating parameters to be sent in attrInfo
 * section to LDMs, together with the Interpreter code. (WL#14388)
 */
#define NDBD_SUPPORT_PARAM_CMP NDB_MAKE_VERSION(8,0,27)
static
inline
int
ndbd_support_param_cmp(Uint32 x)
{
  return x >= NDBD_SUPPORT_PARAM_CMP;
}

/*
 * Replace EncryptionPasswordData with EncryptionKeyMaterial. Mostly compatible
 * but old code had some redundant checks. New ndb_mgmd need to maintain the
 * conditions when sending to old data nodes.
 */
#define NDBD_SUPPORT_ENCRYPTION_KEY_MATERIAL NDB_MAKE_VERSION(8,0,29)
static
inline
bool
ndbd_support_encryption_key_material(Uint32 x)
{
  return x >= NDBD_SUPPORT_ENCRYPTION_KEY_MATERIAL;
}

/*
 * Along with the newly added conflcit detection and resolution algorithms
 * for INSERTs, support for Interpreted write is also added from 8.0.30.
 */
#define NDBD_INTERPRETED_WRITE NDB_MAKE_VERSION(8, 0, 30)
static
inline
bool
ndbd_interpreted_write_supported(Uint32 v)
{
  return v >= NDBD_INTERPRETED_WRITE;
}

/*
 * Data nodes reply to MGMD START BACKUP NOWAIT REQ from
 * MGMD with BACKUP_REF/CONF.
 */
#define NDBD_START_BACKUP_NOWAIT_REPLY_75 NDB_MAKE_VERSION(7,5,31)
#define NDBD_START_BACKUP_NOWAIT_REPLY_76 NDB_MAKE_VERSION(7,6,27)
#define NDBD_START_BACKUP_NOWAIT_REPLY_80 NDB_MAKE_VERSION(8,0,35)
#define NDBD_START_BACKUP_NOWAIT_REPLY_82 NDB_MAKE_VERSION(8,2,0)
static
inline
int
ndbd_start_backup_nowait_reply(Uint32 x)
{
  const Uint32 major = (x >> 16) & 0xFF;
  const Uint32 minor = (x >>  8) & 0xFF;
  if (major < 7)
  {
    return 0;
  }
  else if (major == 7)
  {
    if (minor < 5)
    {
      return 0;
    }
    else if (minor == 5)
    {
      return x >= NDBD_START_BACKUP_NOWAIT_REPLY_75;
    }
    else if (minor == 6)
    {
      return x >= NDBD_START_BACKUP_NOWAIT_REPLY_76;
    }
  }
  else if (major == 8)
  {
    if (minor == 0)
    {
      return x >= NDBD_START_BACKUP_NOWAIT_REPLY_80;
    }
    else if (minor == 1)
    {
      return 0;
    }
    else
    {
      return x >= NDBD_START_BACKUP_NOWAIT_REPLY_82;
    }
  }
  return 1;
}
#endif
