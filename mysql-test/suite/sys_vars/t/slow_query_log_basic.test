##################### mysql-test\t\slow_query_log_basic.test ###################
#                                                                              #
# Variable Name: slow_query_log                                                #
# Scope: GLOBAL                                                                #
# Access Type: Dynamic                                                         #
# Data Type: BOOLEAN                                                           #
# Default Value: OFF                                                           #
# Valid Values: ON, OFF                                                        #
#                                                                              #
#                                                                              #
# Creation Date: 2008-03-16                                                    #
# Author:  Salman Rawala                                                       #
#                                                                              #
# Description: Test Cases of Dynamic System Variable "slow_query_log"          #
#              that checks behavior of this variable in the following ways     #
#              * Default Value                                                 #
#              * Valid & Invalid values                                        #
#              * Scope & Access method                                         #
#              * Data Integrity                                                #
#                                                                              #
# Reference: http://dev.mysql.com/doc/refman/5.1/en/                           #
#              server-options.html#option_mysqld_event-scheduler               #
#                                                                              #
################################################################################

--source include/load_sysvars.inc

###########################################################
#           START OF slow_query_log TESTS                 #
###########################################################


######################################################################
#   Saving initial value of slow_query_log in a temporary variable   #
######################################################################

SET @start_value = @@global.slow_query_log;
SELECT @start_value;


--echo '#---------------------FN_DYNVARS_004_01-------------------------#'
############################################### 
#     Verify default value of variable        #
############################################### 

SET @@global.slow_query_log = DEFAULT;
SELECT @@global.slow_query_log = 0;


--echo '#--------------------FN_DYNVARS_004_02------------------------#'
######################################################################
#        Change the value of slow_query_log to a valid value         #
######################################################################

SET @@global.slow_query_log = ON;
SELECT @@global.slow_query_log;
SET @@global.slow_query_log = OFF;
SELECT @@global.slow_query_log;

--echo '#--------------------FN_DYNVARS_004_03-------------------------#'
######################################################################
#        Change the value of slow_query_log to invalid value         #
######################################################################

--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.slow_query_log = 2;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.slow_query_log = -1;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.slow_query_log = TRUEF;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.slow_query_log = TRUE_F;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.slow_query_log = FALSE0;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.slow_query_log = OON;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.slow_query_log = ONN;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.slow_query_log = OOFF;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.slow_query_log = 0FF;
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.slow_query_log = ' ';
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.slow_query_log = " ";
--Error ER_WRONG_VALUE_FOR_VAR
SET @@global.slow_query_log = '';


--echo '#-------------------FN_DYNVARS_004_04----------------------------#'
##################################################################
#         Test if accessing session slow_query_log gives error   #
##################################################################

--Error ER_GLOBAL_VARIABLE
SET @@session.slow_query_log = OFF;
--Error ER_INCORRECT_GLOBAL_LOCAL_VAR
SELECT @@session.slow_query_log;


--echo '#----------------------FN_DYNVARS_004_05------------------------#'
############################################################################## 
# Check if the value in GLOBAL Tables matches values in variable             #
##############################################################################

--disable_warnings
SELECT IF(@@global.slow_query_log, "ON", "OFF") = VARIABLE_VALUE 
FROM performance_schema.global_variables 
WHERE VARIABLE_NAME='slow_query_log';
--enable_warnings


--echo '#---------------------FN_DYNVARS_004_06----------------------#'
################################################################
#        Check if 0 and 1 values can be used on variable       #
################################################################

SET @@global.slow_query_log = 0;
SELECT @@global.slow_query_log;
SET @@global.slow_query_log = 1;
SELECT @@global.slow_query_log;

--echo '#---------------------FN_DYNVARS_004_07----------------------#'
################################################################### 
#      Check if TRUE and FALSE values can be used on variable     #
################################################################### 

SET @@global.slow_query_log = TRUE;
SELECT @@global.slow_query_log;
SET @@global.slow_query_log = FALSE;
SELECT @@global.slow_query_log;

--echo '#---------------------FN_DYNVARS_004_08----------------------#'
##############################################################################
#    Check if accessing variable with SESSION,LOCAL and without SCOPE points #
#    to same session variable                                                #
##############################################################################

SET @@global.slow_query_log = ON;
SELECT @@slow_query_log = @@global.slow_query_log;

--echo '#---------------------FN_DYNVARS_004_09----------------------#'
######################################################################
#   Check if slow_query_log can be accessed with and without @@ sign #
######################################################################
--Error ER_GLOBAL_VARIABLE
SET slow_query_log = ON;
--Error ER_PARSE_ERROR
SET local.slow_query_log = OFF;
--Error ER_UNKNOWN_TABLE
SELECT local.slow_query_log;
--Error ER_PARSE_ERROR
SET global.slow_query_log = ON;
--Error ER_UNKNOWN_TABLE
SELECT global.slow_query_log;
--Error ER_BAD_FIELD_ERROR
SELECT slow_query_log;
--Error ER_INCORRECT_GLOBAL_LOCAL_VAR
SELECT @@session.slow_query_log;



##############################  
#   Restore initial value    #
##############################

SET @@global.slow_query_log = @start_value;
SELECT @@global.slow_query_log;


####################################################
#       END OF slow_query_log TESTS                #
####################################################
