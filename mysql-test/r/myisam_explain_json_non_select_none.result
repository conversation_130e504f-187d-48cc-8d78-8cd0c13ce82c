set @save_storage_engine= @@session.default_storage_engine;
set session default_storage_engine = MyISAM;
set end_markers_in_json=on;
#1
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
#
# query:  UPDATE t1 SET a = 10 WHERE a < 10
# select: SELECT * FROM t1     WHERE a < 10
#
EXPLAIN UPDATE t1 SET a = 10 WHERE a < 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 10 where (`test`.`t1`.`a` < 10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t1 SET a = 10 WHERE a < 10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00",
      "attached_condition": "(`test`.`t1`.`a` < 10)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 10 where (`test`.`t1`.`a` < 10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1     WHERE a < 10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 1,
      "filtered": "33.33",
      "cost_info": {
        "read_cost": "0.70",
        "eval_cost": "0.10",
        "prefix_cost": "0.80",
        "data_read_per_join": "8"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "(`test`.`t1`.`a` < 10)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 10)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	4
Handler_update	3

DROP TABLE t1;
#2
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
#
# query:  DELETE   FROM t1 WHERE a < 10
# select: SELECT * FROM t1 WHERE a < 10
#
EXPLAIN DELETE   FROM t1 WHERE a < 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` < 10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE a < 10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "delete": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00",
      "attached_condition": "(`test`.`t1`.`a` < 10)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` < 10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE a < 10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 1,
      "filtered": "33.33",
      "cost_info": {
        "read_cost": "0.70",
        "eval_cost": "0.10",
        "prefix_cost": "0.80",
        "data_read_per_join": "8"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "(`test`.`t1`.`a` < 10)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 10)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	3
Handler_read_key	1
Handler_read_rnd_next	4

DROP TABLE t1;
#3
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
#
# query:  DELETE   FROM t1 USING t1 WHERE a = 1
# select: SELECT * FROM t1          WHERE a = 1
#
EXPLAIN DELETE   FROM t1 USING t1 WHERE a = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	delete `test`.`t1` from `test`.`t1` where (`test`.`t1`.`a` = 1)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 USING t1 WHERE a = 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "delete": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 1,
      "filtered": "33.33",
      "cost_info": {
        "read_cost": "0.70",
        "eval_cost": "0.10",
        "prefix_cost": "0.80",
        "data_read_per_join": "8"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "(`test`.`t1`.`a` = 1)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	delete `test`.`t1` from `test`.`t1` where (`test`.`t1`.`a` = 1)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1          WHERE a = 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 1,
      "filtered": "33.33",
      "cost_info": {
        "read_cost": "0.70",
        "eval_cost": "0.10",
        "prefix_cost": "0.80",
        "data_read_per_join": "8"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "(`test`.`t1`.`a` = 1)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` = 1)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	1
Handler_read_key	1
Handler_read_rnd_next	4

DROP TABLE t1;
#4
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (1), (2), (3);
#
# query:  UPDATE        t1, t2 SET t1.a = 10 WHERE t1.a = 1
# select: SELECT * FROM t1, t2               WHERE t1.a = 1
#
EXPLAIN UPDATE        t1, t2 SET t1.a = 10 WHERE t1.a = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`a` = 10 where (`test`.`t1`.`a` = 1)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1, t2 SET t1.a = 10 WHERE t1.a = 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.60"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 1,
          "filtered": "33.33",
          "cost_info": {
            "read_cost": "0.70",
            "eval_cost": "0.10",
            "prefix_cost": "0.80",
            "data_read_per_join": "8"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t1`.`a` = 1)"
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "1.60",
            "data_read_per_join": "24"
          } /* cost_info */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`a` = 10 where (`test`.`t1`.`a` = 1)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1, t2               WHERE t1.a = 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.60"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 1,
          "filtered": "33.33",
          "cost_info": {
            "read_cost": "0.70",
            "eval_cost": "0.10",
            "prefix_cost": "0.80",
            "data_read_per_join": "8"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t1`.`a` = 1)"
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "1.60",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "b"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where (`test`.`t1`.`a` = 1)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	8
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	8
Handler_update	1

DROP TABLE t1, t2;
#5
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (1), (2), (3);
#
# query:  UPDATE        t1 t11, (SELECT * FROM t2) t12 SET t11.a = 10 WHERE t11.a = 1
# select: SELECT * FROM t1 t11, (SELECT * FROM t2) t12                WHERE t11.a = 1
#
EXPLAIN UPDATE        t1 t11, (SELECT * FROM t2) t12 SET t11.a = 10 WHERE t11.a = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t11	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
Warnings:
Note	1003	update `test`.`t1` `t11` join `test`.`t2` set `test`.`t11`.`a` = 10 where (`test`.`t11`.`a` = 1)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1 t11, (SELECT * FROM t2) t12 SET t11.a = 10 WHERE t11.a = 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.60"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 1,
          "filtered": "33.33",
          "cost_info": {
            "read_cost": "0.70",
            "eval_cost": "0.10",
            "prefix_cost": "0.80",
            "data_read_per_join": "8"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t11`.`a` = 1)"
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "1.60",
            "data_read_per_join": "24"
          } /* cost_info */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` `t11` join `test`.`t2` set `test`.`t11`.`a` = 10 where (`test`.`t11`.`a` = 1)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 t11, (SELECT * FROM t2) t12                WHERE t11.a = 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.60"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 1,
          "filtered": "33.33",
          "cost_info": {
            "read_cost": "0.70",
            "eval_cost": "0.10",
            "prefix_cost": "0.80",
            "data_read_per_join": "8"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t11`.`a` = 1)"
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "1.60",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "b"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t11`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` `t11` join `test`.`t2` where (`test`.`t11`.`a` = 1)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	8
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	8
Handler_update	1

DROP TABLE t1, t2;
#6
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (1), (2), (3);
#
# query:  UPDATE        t1 SET a = 10 WHERE 1 IN (SELECT 1 FROM t2 WHERE t2.b < 3)
# select: SELECT * FROM t1            WHERE 1 IN (SELECT 1 FROM t2 WHERE t2.b < 3)
#
EXPLAIN UPDATE        t1 SET a = 10 WHERE 1 IN (SELECT 1 FROM t2 WHERE t2.b < 3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 10 where <in_optimizer>(1,<exists>(/* select#2 */ select 1 from `test`.`t2` where (`test`.`t2`.`b` < 3)))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1 SET a = 10 WHERE 1 IN (SELECT 1 FROM t2 WHERE t2.b < 3);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00",
      "attached_subqueries": [
        {
          "dependent": false,
          "cacheable": true,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.80"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 1,
              "filtered": "33.33",
              "cost_info": {
                "read_cost": "0.70",
                "eval_cost": "0.10",
                "prefix_cost": "0.80",
                "data_read_per_join": "8"
              } /* cost_info */,
              "used_columns": [
                "b"
              ] /* used_columns */,
              "attached_condition": "(`test`.`t2`.`b` < 3)"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 10 where <in_optimizer>(1,<exists>(/* select#2 */ select 1 from `test`.`t2` where (`test`.`t2`.`b` < 3)))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1            WHERE 1 IN (SELECT 1 FROM t2 WHERE t2.b < 3);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 3,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.30",
        "prefix_cost": "0.80",
        "data_read_per_join": "24"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */
    } /* table */,
    "optimized_away_subqueries": [
      {
        "dependent": false,
        "cacheable": true,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "0.80"
          } /* cost_info */,
          "table": {
            "table_name": "t2",
            "access_type": "ALL",
            "rows_examined_per_scan": 3,
            "rows_produced_per_join": 1,
            "filtered": "33.33",
            "cost_info": {
              "read_cost": "0.70",
              "eval_cost": "0.10",
              "prefix_cost": "0.80",
              "data_read_per_join": "8"
            } /* cost_info */,
            "used_columns": [
              "b"
            ] /* used_columns */,
            "attached_condition": "(`test`.`t2`.`b` < 3)"
          } /* table */
        } /* query_block */
      }
    ] /* optimized_away_subqueries */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where true
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	5
Handler_update	3

DROP TABLE t1, t2;
#7
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (1), (2), (3);
#
# query:  UPDATE        t1 SET a = 10 WHERE a IN (SELECT b FROM t2 WHERE t1.a < 3)
# select: SELECT * FROM t1            WHERE a IN (SELECT b FROM t2 WHERE t1.a < 3)
#
EXPLAIN UPDATE        t1 SET a = 10 WHERE a IN (SELECT b FROM t2 WHERE t1.a < 3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 10 where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`b` from `test`.`t2` where ((`test`.`t1`.`a` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1 SET a = 10 WHERE a IN (SELECT b FROM t2 WHERE t1.a < 3);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00",
      "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`b` from `test`.`t2` where ((`test`.`t1`.`a` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.80"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 1,
              "filtered": "33.33",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.10",
                "prefix_cost": "0.80",
                "data_read_per_join": "8"
              } /* cost_info */,
              "used_columns": [
                "b"
              ] /* used_columns */,
              "attached_condition": "((`test`.`t1`.`a` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 10 where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`b` from `test`.`t2` where ((`test`.`t1`.`a` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1            WHERE a IN (SELECT b FROM t2 WHERE t1.a < 3);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 3,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.30",
        "prefix_cost": "0.80",
        "data_read_per_join": "24"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`b` from `test`.`t2` where ((`test`.`t1`.`a` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.80"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 1,
              "filtered": "33.33",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.10",
                "prefix_cost": "0.80",
                "data_read_per_join": "8"
              } /* cost_info */,
              "used_columns": [
                "b"
              ] /* used_columns */,
              "attached_condition": "((`test`.`t1`.`a` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1276	Field or reference 'test.t1.a' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`b` from `test`.`t2` where ((`test`.`t1`.`a` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))))
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	11
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	11
Handler_update	2

DROP TABLE t1, t2;
#7
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (1), (2), (3);
#
# query:  UPDATE        t1, t2 SET a = 10 WHERE a IN (SELECT b FROM t2 WHERE t2.b < 3)
# select: SELECT * FROM t1, t2            WHERE a IN (SELECT b FROM t2 WHERE t2.b < 3)
#
EXPLAIN UPDATE        t1, t2 SET a = 10 WHERE a IN (SELECT b FROM t2 WHERE t2.b < 3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`a` = 10 where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`b` from `test`.`t2` where ((`test`.`t2`.`b` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1, t2 SET a = 10 WHERE a IN (SELECT b FROM t2 WHERE t2.b < 3);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.20"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`b` from `test`.`t2` where ((`test`.`t2`.`b` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))))",
          "attached_subqueries": [
            {
              "dependent": true,
              "cacheable": false,
              "query_block": {
                "select_id": 2,
                "cost_info": {
                  "query_cost": "0.80"
                } /* cost_info */,
                "table": {
                  "table_name": "t2",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 3,
                  "rows_produced_per_join": 1,
                  "filtered": "33.33",
                  "cost_info": {
                    "read_cost": "0.70",
                    "eval_cost": "0.10",
                    "prefix_cost": "0.80",
                    "data_read_per_join": "8"
                  } /* cost_info */,
                  "used_columns": [
                    "b"
                  ] /* used_columns */,
                  "attached_condition": "((`test`.`t2`.`b` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))"
                } /* table */
              } /* query_block */
            }
          ] /* attached_subqueries */
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 9,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.90",
            "prefix_cost": "2.20",
            "data_read_per_join": "72"
          } /* cost_info */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`a` = 10 where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`b` from `test`.`t2` where ((`test`.`t2`.`b` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1, t2            WHERE a IN (SELECT b FROM t2 WHERE t2.b < 3);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.20"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`b` from `test`.`t2` where ((`test`.`t2`.`b` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))))",
          "attached_subqueries": [
            {
              "dependent": true,
              "cacheable": false,
              "query_block": {
                "select_id": 2,
                "cost_info": {
                  "query_cost": "0.80"
                } /* cost_info */,
                "table": {
                  "table_name": "t2",
                  "access_type": "ALL",
                  "rows_examined_per_scan": 3,
                  "rows_produced_per_join": 1,
                  "filtered": "33.33",
                  "cost_info": {
                    "read_cost": "0.70",
                    "eval_cost": "0.10",
                    "prefix_cost": "0.80",
                    "data_read_per_join": "8"
                  } /* cost_info */,
                  "used_columns": [
                    "b"
                  ] /* used_columns */,
                  "attached_condition": "((`test`.`t2`.`b` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))"
                } /* table */
              } /* query_block */
            }
          ] /* attached_subqueries */
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 9,
          "filtered": "100.00",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.90",
            "prefix_cost": "2.20",
            "data_read_per_join": "72"
          } /* cost_info */,
          "used_columns": [
            "b"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`b` from `test`.`t2` where ((`test`.`t2`.`b` < 3) and (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`b`))))
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	15
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	19
Handler_update	2

DROP TABLE t1, t2;
#8
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (1), (2), (3);
#
# query:  UPDATE        t1 t11, (SELECT * FROM t2) t12 SET t11.a = t11.a + 10
# select: SELECT * FROM t1 t11, (SELECT * FROM t2) t12
#
EXPLAIN UPDATE        t1 t11, (SELECT * FROM t2) t12 SET t11.a = t11.a + 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t11	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
Warnings:
Note	1003	update `test`.`t1` `t11` join `test`.`t2` set `test`.`t11`.`a` = (`test`.`t11`.`a` + 10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1 t11, (SELECT * FROM t2) t12 SET t11.a = t11.a + 10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.20"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 9,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.90",
            "prefix_cost": "2.20",
            "data_read_per_join": "72"
          } /* cost_info */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` `t11` join `test`.`t2` set `test`.`t11`.`a` = (`test`.`t11`.`a` + 10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 t11, (SELECT * FROM t2) t12;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.20"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 9,
          "filtered": "100.00",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.90",
            "prefix_cost": "2.20",
            "data_read_per_join": "72"
          } /* cost_info */,
          "used_columns": [
            "b"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t11`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` `t11` join `test`.`t2`
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	8
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	16
Handler_update	3

DROP TABLE t1, t2;
#9
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (1), (2), (3);
#
# query:  UPDATE        t1 t11, (SELECT 1 FROM DUAL) t12 SET t11.a = t11.a + 10
# select: SELECT * FROM t1 t11, (SELECT 1 FROM DUAL) t12
#
EXPLAIN UPDATE        t1 t11, (SELECT 1 FROM DUAL) t12 SET t11.a = t11.a + 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	<derived2>	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
1	UPDATE	t11	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	DERIVED	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	No tables used
Warnings:
Note	1003	update `test`.`t1` `t11` join (/* select#2 */ select 1 AS `1`) `t12` set `test`.`t11`.`a` = (`test`.`t11`.`a` + 10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1 t11, (SELECT 1 FROM DUAL) t12 SET t11.a = t11.a + 10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t12",
          "access_type": "system",
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 1,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.00",
            "eval_cost": "0.10",
            "prefix_cost": "0.00",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "1"
          ] /* used_columns */,
          "materialized_from_subquery": {
            "using_temporary_table": true,
            "dependent": false,
            "cacheable": true,
            "query_block": {
              "select_id": 2,
              "message": "No tables used"
            } /* query_block */
          } /* materialized_from_subquery */
        } /* table */
      },
      {
        "table": {
          "update": true,
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` `t11` join (/* select#2 */ select 1 AS `1`) `t12` set `test`.`t11`.`a` = (`test`.`t11`.`a` + 10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 t11, (SELECT 1 FROM DUAL) t12;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t12",
          "access_type": "system",
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 1,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.00",
            "eval_cost": "0.10",
            "prefix_cost": "0.00",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "1"
          ] /* used_columns */,
          "materialized_from_subquery": {
            "using_temporary_table": true,
            "dependent": false,
            "cacheable": true,
            "query_block": {
              "select_id": 2,
              "message": "No tables used"
            } /* query_block */
          } /* materialized_from_subquery */
        } /* table */
      },
      {
        "table": {
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t11`.`a` AS `a`,'1' AS `1` from `test`.`t1` `t11`
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	5
Handler_write	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd	3
Handler_read_rnd_next	9
Handler_update	3
Handler_write	4

DROP TABLE t1, t2;
#10
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (1), (2), (3);
#
# query:  UPDATE        t1 t11, (SELECT * FROM t2) t12 SET t11.a = 10 WHERE t11.a > 1
# select: SELECT * FROM t1 t11, (SELECT * FROM t2) t12                WHERE t11.a > 1
#
EXPLAIN UPDATE        t1 t11, (SELECT * FROM t2) t12 SET t11.a = 10 WHERE t11.a > 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t11	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
Warnings:
Note	1003	update `test`.`t1` `t11` join `test`.`t2` set `test`.`t11`.`a` = 10 where (`test`.`t11`.`a` > 1)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1 t11, (SELECT * FROM t2) t12 SET t11.a = 10 WHERE t11.a > 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.60"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 1,
          "filtered": "33.33",
          "cost_info": {
            "read_cost": "0.70",
            "eval_cost": "0.10",
            "prefix_cost": "0.80",
            "data_read_per_join": "8"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t11`.`a` > 1)"
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "1.60",
            "data_read_per_join": "24"
          } /* cost_info */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` `t11` join `test`.`t2` set `test`.`t11`.`a` = 10 where (`test`.`t11`.`a` > 1)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 t11, (SELECT * FROM t2) t12                WHERE t11.a > 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.60"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 1,
          "filtered": "33.33",
          "cost_info": {
            "read_cost": "0.70",
            "eval_cost": "0.10",
            "prefix_cost": "0.80",
            "data_read_per_join": "8"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t11`.`a` > 1)"
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "1.60",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "b"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t11`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` `t11` join `test`.`t2` where (`test`.`t11`.`a` > 1)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	8
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	12
Handler_update	2

DROP TABLE t1, t2;
#11
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
#
# query:  DELETE   FROM t1 WHERE a > 1 LIMIT 1
# select: SELECT * FROM t1 WHERE a > 1 LIMIT 1
#
EXPLAIN DELETE   FROM t1 WHERE a > 1 LIMIT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` > 1) limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE a > 1 LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "delete": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00",
      "attached_condition": "(`test`.`t1`.`a` > 1)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` > 1) limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE a > 1 LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 1,
      "filtered": "33.33",
      "cost_info": {
        "read_cost": "0.70",
        "eval_cost": "0.10",
        "prefix_cost": "0.80",
        "data_read_per_join": "8"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "(`test`.`t1`.`a` > 1)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 1) limit 1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	2
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	1
Handler_read_key	1
Handler_read_rnd_next	2

DROP TABLE t1;
#12
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
#
# query:  DELETE   FROM t1 WHERE 0
# select: SELECT * FROM t1 WHERE 0
#
EXPLAIN DELETE   FROM t1 WHERE 0;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
Warnings:
Note	1003	delete from `test`.`t1` where false
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE 0;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "Impossible WHERE"
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1` where false
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE 0;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "Impossible WHERE"
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where false
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	1

DROP TABLE t1;
#13
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
#
# query:  DELETE   FROM t1 USING t1 WHERE 0
# select: SELECT * FROM t1          WHERE 0
#
EXPLAIN DELETE   FROM t1 USING t1 WHERE 0;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
Warnings:
Note	1003	delete `test`.`t1` from `test`.`t1` where false
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 USING t1 WHERE 0;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "Impossible WHERE"
  } /* query_block */
}
Warnings:
Note	1003	delete `test`.`t1` from `test`.`t1` where false
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1          WHERE 0;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "Impossible WHERE"
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where false
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	1

DROP TABLE t1;
#14
CREATE TABLE t1 (a INT, b INT, UNIQUE KEY (a), KEY (b));
INSERT INTO t1 VALUES (3, 3), (7, 7);
#
# query:  DELETE   FROM t1 WHERE a = 3
# select: SELECT * FROM t1 WHERE a = 3
#
EXPLAIN DELETE   FROM t1 WHERE a = 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	range	a	a	5	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` = 3)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE a = 3;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "delete": true,
      "table_name": "t1",
      "access_type": "range",
      "possible_keys": [
        "a"
      ] /* possible_keys */,
      "key": "a",
      "used_key_parts": [
        "a"
      ] /* used_key_parts */,
      "key_length": "5",
      "ref": [
        "const"
      ] /* ref */,
      "rows_examined_per_scan": 1,
      "filtered": "100.00",
      "attached_condition": "(`test`.`t1`.`a` = 3)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` = 3)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE a = 3;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.00"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "const",
      "possible_keys": [
        "a"
      ] /* possible_keys */,
      "key": "a",
      "used_key_parts": [
        "a"
      ] /* used_key_parts */,
      "key_length": "5",
      "ref": [
        "const"
      ] /* ref */,
      "rows_examined_per_scan": 1,
      "rows_produced_per_join": 1,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.00",
        "eval_cost": "0.10",
        "prefix_cost": "0.00",
        "data_read_per_join": "16"
      } /* cost_info */,
      "used_columns": [
        "a",
        "b"
      ] /* used_columns */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select '3' AS `a`,'3' AS `b` from `test`.`t1` where true
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	3
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	1
Handler_read_key	3

DROP TABLE t1;
#15
CREATE TABLE t1 (a INT, b INT, UNIQUE KEY (a), KEY (b));
INSERT INTO t1 VALUES (3, 3), (7, 7);
#
# query:  DELETE   FROM t1 WHERE a < 3
# select: SELECT * FROM t1 WHERE a < 3
#
EXPLAIN DELETE   FROM t1 WHERE a < 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	range	a	a	5	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` < 3)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE a < 3;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "delete": true,
      "table_name": "t1",
      "access_type": "range",
      "possible_keys": [
        "a"
      ] /* possible_keys */,
      "key": "a",
      "used_key_parts": [
        "a"
      ] /* used_key_parts */,
      "key_length": "5",
      "ref": [
        "const"
      ] /* ref */,
      "rows_examined_per_scan": 1,
      "filtered": "100.00",
      "attached_condition": "(`test`.`t1`.`a` < 3)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` < 3)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE a < 3;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.71"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "range",
      "possible_keys": [
        "a"
      ] /* possible_keys */,
      "key": "a",
      "used_key_parts": [
        "a"
      ] /* used_key_parts */,
      "key_length": "5",
      "rows_examined_per_scan": 1,
      "rows_produced_per_join": 1,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.61",
        "eval_cost": "0.10",
        "prefix_cost": "0.71",
        "data_read_per_join": "16"
      } /* cost_info */,
      "used_columns": [
        "a",
        "b"
      ] /* used_columns */,
      "attached_condition": "(`test`.`t1`.`a` < 3)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b` from `test`.`t1` where (`test`.`t1`.`a` < 3)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	3
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	3

DROP TABLE t1;
#16
CREATE TABLE t1 ( a int PRIMARY KEY );
#
# query:  DELETE   FROM t1 WHERE t1.a > 0 ORDER BY t1.a
# select: SELECT * FROM t1 WHERE t1.a > 0 ORDER BY t1.a
#
EXPLAIN DELETE   FROM t1 WHERE t1.a > 0 ORDER BY t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	range	PRIMARY	PRIMARY	4	const	1	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` > 0) order by `test`.`t1`.`a`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE t1.a > 0 ORDER BY t1.a;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "delete": true,
        "table_name": "t1",
        "access_type": "range",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "a"
        ] /* used_key_parts */,
        "key_length": "4",
        "ref": [
          "const"
        ] /* ref */,
        "rows_examined_per_scan": 1,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t1`.`a` > 0)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` > 0) order by `test`.`t1`.`a`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE t1.a > 0 ORDER BY t1.a;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "no matching row in const table"
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select NULL AS `a` from `test`.`t1` where (NULL > 0) order by NULL
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2

INSERT INTO t1 VALUES (1), (2), (3);
#
# query:  DELETE   FROM t1 WHERE t1.a > 0 ORDER BY t1.a
# select: SELECT * FROM t1 WHERE t1.a > 0 ORDER BY t1.a
#
EXPLAIN DELETE   FROM t1 WHERE t1.a > 0 ORDER BY t1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	range	PRIMARY	PRIMARY	4	const	3	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` > 0) order by `test`.`t1`.`a`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE t1.a > 0 ORDER BY t1.a;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "delete": true,
        "table_name": "t1",
        "access_type": "range",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "a"
        ] /* used_key_parts */,
        "key_length": "4",
        "ref": [
          "const"
        ] /* ref */,
        "rows_examined_per_scan": 3,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t1`.`a` > 0)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`a` > 0) order by `test`.`t1`.`a`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE t1.a > 0 ORDER BY t1.a;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t1",
        "access_type": "index",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "a"
        ] /* used_key_parts */,
        "key_length": "4",
        "rows_examined_per_scan": 3,
        "rows_produced_per_join": 3,
        "filtered": "100.00",
        "using_index": true,
        "cost_info": {
          "read_cost": "0.50",
          "eval_cost": "0.30",
          "prefix_cost": "0.80",
          "data_read_per_join": "24"
        } /* cost_info */,
        "used_columns": [
          "a"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t1`.`a` > 0)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 0) order by `test`.`t1`.`a`
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_first	1
Handler_read_key	1
Handler_read_next	3
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	3
Handler_read_key	2
Handler_read_next	3

DROP TABLE t1;
#17
CREATE TABLE t1(a INT PRIMARY KEY);
INSERT INTO t1 VALUES (4),(3),(1),(2);
#
# query:  DELETE   FROM t1 WHERE (@a:= a) ORDER BY a LIMIT 1
# select: SELECT * FROM t1 WHERE (@a:= a) ORDER BY a LIMIT 1
#
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
EXPLAIN DELETE   FROM t1 WHERE (@a:= a) ORDER BY a LIMIT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	index	NULL	PRIMARY	4	NULL	4	100.00	Using where
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
Note	1003	delete from `test`.`t1` where (0 <> (@a:=`test`.`t1`.`a`)) order by `test`.`t1`.`a` limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE (@a:= a) ORDER BY a LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "delete": true,
        "table_name": "t1",
        "access_type": "index",
        "key": "PRIMARY",
        "used_key_parts": [
          "a"
        ] /* used_key_parts */,
        "key_length": "4",
        "rows_examined_per_scan": 4,
        "filtered": "100.00",
        "attached_condition": "(0 <> (@a:=`test`.`t1`.`a`))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
Note	1003	delete from `test`.`t1` where (0 <> (@a:=`test`.`t1`.`a`)) order by `test`.`t1`.`a` limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE (@a:= a) ORDER BY a LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.90"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t1",
        "access_type": "index",
        "key": "PRIMARY",
        "used_key_parts": [
          "a"
        ] /* used_key_parts */,
        "key_length": "4",
        "rows_examined_per_scan": 1,
        "rows_produced_per_join": 4,
        "filtered": "100.00",
        "using_index": true,
        "cost_info": {
          "read_cost": "0.50",
          "eval_cost": "0.40",
          "prefix_cost": "0.90",
          "data_read_per_join": "32"
        } /* cost_info */,
        "used_columns": [
          "a"
        ] /* used_columns */,
        "attached_condition": "(0 <> (@a:=`test`.`t1`.`a`))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (0 <> (@a:=`test`.`t1`.`a`)) order by `test`.`t1`.`a` limit 1
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_first	1
Handler_read_key	1
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	1
Handler_read_first	1
Handler_read_key	1

DROP TABLE t1;
#18
CREATE TABLE t1 (a DATE, b TIME, c INT, KEY c(c), KEY b(b), KEY a(a));
INSERT INTO t1 VALUES (), (), (), (), (), (), (), (), (), ();
UPDATE t1 SET a = c, b = c;
#
# query:  DELETE   FROM t1 ORDER BY a ASC, b ASC LIMIT 1
# select: SELECT * FROM t1 ORDER BY a ASC, b ASC LIMIT 1
#
EXPLAIN DELETE   FROM t1 ORDER BY a ASC, b ASC LIMIT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	ALL	NULL	NULL	NULL	NULL	10	100.00	Using filesort
Warnings:
Note	1003	delete from `test`.`t1` order by `test`.`t1`.`a`,`test`.`t1`.`b` limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 ORDER BY a ASC, b ASC LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "delete": true,
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "filtered": "100.00"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1` order by `test`.`t1`.`a`,`test`.`t1`.`b` limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 ORDER BY a ASC, b ASC LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.51"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 10,
        "rows_produced_per_join": 10,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.51",
          "eval_cost": "1.00",
          "prefix_cost": "1.51",
          "data_read_per_join": "160"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c"
        ] /* used_columns */
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`b` AS `b`,`test`.`t1`.`c` AS `c` from `test`.`t1` order by `test`.`t1`.`a`,`test`.`t1`.`b` limit 1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	3
Handler_read_rnd_next	11
Sort_rows	1
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	1
Handler_read_key	3
Handler_read_rnd	1
Handler_read_rnd_next	11
Sort_rows	10
Sort_scan	1

DROP TABLE t1;
#19
CREATE TABLE t1 (a1 INT NOT NULL, b1 INT NOT NULL);
CREATE TABLE t2 (a2 INT NOT NULL, b2 INT NOT NULL, PRIMARY KEY (a2,b2));
CREATE TABLE t3 (a3 INT NOT NULL, b3 INT NOT NULL, PRIMARY KEY (a3,b3));
INSERT INTO t1 VALUES (1,1), (2,1), (1,3);
INSERT INTO t2 VALUES (1,1), (2,2), (3,3);
INSERT INTO t3 VALUES (1,1), (2,1), (1,3);
#
# query:  DELETE t1,t2,t3 FROM t1,t2,t3 WHERE a1=a2 AND b2=a3 AND b1=b3
# select: SELECT *        FROM t1,t2,t3 WHERE a1=a2 AND b2=a3 AND b1=b3
#
EXPLAIN DELETE t1,t2,t3 FROM t1,t2,t3 WHERE a1=a2 AND b2=a3 AND b1=b3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
1	DELETE	t2	NULL	ref	PRIMARY	PRIMARY	4	test.t1.a1	1	100.00	NULL
1	DELETE	t3	NULL	eq_ref	PRIMARY	PRIMARY	8	test.t2.b2,test.t1.b1	1	100.00	NULL
Warnings:
Note	1003	delete `test`.`t1`, `test`.`t2`, `test`.`t3` from `test`.`t1` join `test`.`t2` join `test`.`t3` where ((`test`.`t3`.`b3` = `test`.`t1`.`b1`) and (`test`.`t3`.`a3` = `test`.`t2`.`b2`) and (`test`.`t2`.`a2` = `test`.`t1`.`a1`))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE t1,t2,t3 FROM t1,t2,t3 WHERE a1=a2 AND b2=a3 AND b1=b3;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.90"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "delete": true,
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "48"
          } /* cost_info */,
          "used_columns": [
            "a1",
            "b1"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "delete": true,
          "table_name": "t2",
          "access_type": "ref",
          "possible_keys": [
            "PRIMARY"
          ] /* possible_keys */,
          "key": "PRIMARY",
          "used_key_parts": [
            "a2"
          ] /* used_key_parts */,
          "key_length": "4",
          "ref": [
            "test.t1.a1"
          ] /* ref */,
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.75",
            "eval_cost": "0.30",
            "prefix_cost": "1.85",
            "data_read_per_join": "48"
          } /* cost_info */,
          "used_columns": [
            "a2",
            "b2"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "delete": true,
          "table_name": "t3",
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ] /* possible_keys */,
          "key": "PRIMARY",
          "used_key_parts": [
            "a3",
            "b3"
          ] /* used_key_parts */,
          "key_length": "8",
          "ref": [
            "test.t2.b2",
            "test.t1.b1"
          ] /* ref */,
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.75",
            "eval_cost": "0.30",
            "prefix_cost": "2.90",
            "data_read_per_join": "48"
          } /* cost_info */,
          "used_columns": [
            "a3",
            "b3"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	delete `test`.`t1`, `test`.`t2`, `test`.`t3` from `test`.`t1` join `test`.`t2` join `test`.`t3` where ((`test`.`t3`.`b3` = `test`.`t1`.`b1`) and (`test`.`t3`.`a3` = `test`.`t2`.`b2`) and (`test`.`t2`.`a2` = `test`.`t1`.`a1`))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT *        FROM t1,t2,t3 WHERE a1=a2 AND b2=a3 AND b1=b3;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.90"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "48"
          } /* cost_info */,
          "used_columns": [
            "a1",
            "b1"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ref",
          "possible_keys": [
            "PRIMARY"
          ] /* possible_keys */,
          "key": "PRIMARY",
          "used_key_parts": [
            "a2"
          ] /* used_key_parts */,
          "key_length": "4",
          "ref": [
            "test.t1.a1"
          ] /* ref */,
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "using_index": true,
          "cost_info": {
            "read_cost": "0.75",
            "eval_cost": "0.30",
            "prefix_cost": "1.85",
            "data_read_per_join": "48"
          } /* cost_info */,
          "used_columns": [
            "a2",
            "b2"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "table_name": "t3",
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ] /* possible_keys */,
          "key": "PRIMARY",
          "used_key_parts": [
            "a3",
            "b3"
          ] /* used_key_parts */,
          "key_length": "8",
          "ref": [
            "test.t2.b2",
            "test.t1.b1"
          ] /* ref */,
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "using_index": true,
          "cost_info": {
            "read_cost": "0.75",
            "eval_cost": "0.30",
            "prefix_cost": "2.90",
            "data_read_per_join": "48"
          } /* cost_info */,
          "used_columns": [
            "a3",
            "b3"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a1` AS `a1`,`test`.`t1`.`b1` AS `b1`,`test`.`t2`.`a2` AS `a2`,`test`.`t2`.`b2` AS `b2`,`test`.`t3`.`a3` AS `a3`,`test`.`t3`.`b3` AS `b3` from `test`.`t1` join `test`.`t2` join `test`.`t3` where ((`test`.`t3`.`b3` = `test`.`t1`.`b1`) and (`test`.`t3`.`a3` = `test`.`t2`.`b2`) and (`test`.`t2`.`a2` = `test`.`t1`.`a1`))
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	12
Handler_read_next	3
Handler_read_rnd_next	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	8
Handler_read_key	12
Handler_read_next	3
Handler_read_rnd	5
Handler_read_rnd_next	4

DROP TABLE t1, t2, t3;
#20
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (a INT);
INSERT INTO t2 VALUES (1), (2), (3);
#
# query:  UPDATE t1 SET a = 10 WHERE a IN (SELECT a FROM t2)
# select: SELECT * FROM t1     WHERE a IN (SELECT a FROM t2)
#
EXPLAIN UPDATE t1 SET a = 10 WHERE a IN (SELECT a FROM t2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 10 where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`a` from `test`.`t2` where (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`a`)))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t1 SET a = 10 WHERE a IN (SELECT a FROM t2);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00",
      "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`a` from `test`.`t2` where (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`a`)))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.80"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 1,
              "filtered": "33.33",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.10",
                "prefix_cost": "0.80",
                "data_read_per_join": "8"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */,
              "attached_condition": "(<cache>(`test`.`t1`.`a`) = `test`.`t2`.`a`)"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 10 where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`a` from `test`.`t2` where (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`a`)))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1     WHERE a IN (SELECT a FROM t2);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 3,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.30",
        "prefix_cost": "0.80",
        "data_read_per_join": "24"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`a` from `test`.`t2` where (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`a`)))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.80"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 3,
              "rows_produced_per_join": 1,
              "filtered": "33.33",
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.10",
                "prefix_cost": "0.80",
                "data_read_per_join": "8"
              } /* cost_info */,
              "used_columns": [
                "a"
              ] /* used_columns */,
              "attached_condition": "(<cache>(`test`.`t1`.`a`) = `test`.`t2`.`a`)"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a`,<exists>(/* select#2 */ select `test`.`t2`.`a` from `test`.`t2` where (<cache>(`test`.`t1`.`a`) = `test`.`t2`.`a`)))
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	10
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	10
Handler_update	3

DROP TABLE t1, t2;
#21
CREATE TABLE t1 (a1 INT);
INSERT INTO t1 VALUES (1), (2), (3), (4), (5);
CREATE TABLE t2 (a2 VARCHAR(10)) charset utf8mb4;
INSERT INTO t2 VALUES (1), (2), (3), (4), (5);
SET @save_optimizer_switch= @@optimizer_switch;
#
# query:  DELETE   FROM t1 WHERE a1 IN (SELECT a2 FROM t2 WHERE a2 > 2)
# select: SELECT * FROM t1 WHERE a1 IN (SELECT a2 FROM t2 WHERE a2 > 2)
#
EXPLAIN DELETE   FROM t1 WHERE a1 IN (SELECT a2 FROM t2 WHERE a2 > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	ALL	NULL	NULL	NULL	NULL	5	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	5	20.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a1`,<exists>(/* select#2 */ select `test`.`t2`.`a2` from `test`.`t2` where ((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE a1 IN (SELECT a2 FROM t2 WHERE a2 > 2);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "delete": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "filtered": "100.00",
      "attached_condition": "<in_optimizer>(`test`.`t1`.`a1`,<exists>(/* select#2 */ select `test`.`t2`.`a2` from `test`.`t2` where ((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.01"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 1,
              "filtered": "20.00",
              "cost_info": {
                "read_cost": "0.84",
                "eval_cost": "0.10",
                "prefix_cost": "1.01",
                "data_read_per_join": "48"
              } /* cost_info */,
              "used_columns": [
                "a2"
              ] /* used_columns */,
              "attached_condition": "((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a1`,<exists>(/* select#2 */ select `test`.`t2`.`a2` from `test`.`t2` where ((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE a1 IN (SELECT a2 FROM t2 WHERE a2 > 2);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.00"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "rows_produced_per_join": 5,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.50",
        "prefix_cost": "1.00",
        "data_read_per_join": "40"
      } /* cost_info */,
      "used_columns": [
        "a1"
      ] /* used_columns */,
      "attached_condition": "<in_optimizer>(`test`.`t1`.`a1`,<exists>(/* select#2 */ select `test`.`t2`.`a2` from `test`.`t2` where ((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.01"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 1,
              "filtered": "20.00",
              "cost_info": {
                "read_cost": "0.84",
                "eval_cost": "0.10",
                "prefix_cost": "1.01",
                "data_read_per_join": "48"
              } /* cost_info */,
              "used_columns": [
                "a2"
              ] /* used_columns */,
              "attached_condition": "((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a1` AS `a1` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a1`,<exists>(/* select#2 */ select `test`.`t2`.`a2` from `test`.`t2` where ((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))))
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	30
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	3
Handler_read_key	2
Handler_read_rnd_next	30

SET @@optimizer_switch= @save_optimizer_switch;
TRUNCATE t1;
INSERT INTO t1 VALUES (1), (2), (3), (4), (5);
#
# query:  DELETE   FROM t1 WHERE a1 IN (SELECT a2 FROM t2 WHERE a2 > 2)
# select: SELECT * FROM t1 WHERE a1 IN (SELECT a2 FROM t2 WHERE a2 > 2)
#
EXPLAIN DELETE   FROM t1 WHERE a1 IN (SELECT a2 FROM t2 WHERE a2 > 2);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	ALL	NULL	NULL	NULL	NULL	5	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	5	20.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a1`,<exists>(/* select#2 */ select `test`.`t2`.`a2` from `test`.`t2` where ((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE a1 IN (SELECT a2 FROM t2 WHERE a2 > 2);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "delete": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "filtered": "100.00",
      "attached_condition": "<in_optimizer>(`test`.`t1`.`a1`,<exists>(/* select#2 */ select `test`.`t2`.`a2` from `test`.`t2` where ((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.01"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 1,
              "filtered": "20.00",
              "cost_info": {
                "read_cost": "0.84",
                "eval_cost": "0.10",
                "prefix_cost": "1.01",
                "data_read_per_join": "48"
              } /* cost_info */,
              "used_columns": [
                "a2"
              ] /* used_columns */,
              "attached_condition": "((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a1`,<exists>(/* select#2 */ select `test`.`t2`.`a2` from `test`.`t2` where ((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE a1 IN (SELECT a2 FROM t2 WHERE a2 > 2);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.00"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "rows_produced_per_join": 5,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.50",
        "prefix_cost": "1.00",
        "data_read_per_join": "40"
      } /* cost_info */,
      "used_columns": [
        "a1"
      ] /* used_columns */,
      "attached_condition": "<in_optimizer>(`test`.`t1`.`a1`,<exists>(/* select#2 */ select `test`.`t2`.`a2` from `test`.`t2` where ((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "1.01"
            } /* cost_info */,
            "table": {
              "table_name": "t2",
              "access_type": "ALL",
              "rows_examined_per_scan": 5,
              "rows_produced_per_join": 1,
              "filtered": "20.00",
              "cost_info": {
                "read_cost": "0.84",
                "eval_cost": "0.10",
                "prefix_cost": "1.01",
                "data_read_per_join": "48"
              } /* cost_info */,
              "used_columns": [
                "a2"
              ] /* used_columns */,
              "attached_condition": "((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))"
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a1` AS `a1` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a1`,<exists>(/* select#2 */ select `test`.`t2`.`a2` from `test`.`t2` where ((`test`.`t2`.`a2` > 2) and (<cache>(`test`.`t1`.`a1`) = `test`.`t2`.`a2`))))
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	30
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	3
Handler_read_key	2
Handler_read_rnd_next	30

DROP TABLE t1, t2;
#22
CREATE TABLE t1 (i INT, j INT);
INSERT INTO t1 VALUES (1, 1), (2, 2), (3, 3), (4, 4), (5, 5);
#
# query:  UPDATE t1 SET i = 10
# select: SELECT * FROM t1
#
EXPLAIN UPDATE t1 SET i = 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	5	100.00	NULL
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`i` = 10
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t1 SET i = 10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "filtered": "100.00"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`i` = 10
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.00"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "rows_produced_per_join": 5,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.50",
        "prefix_cost": "1.00",
        "data_read_per_join": "80"
      } /* cost_info */,
      "used_columns": [
        "i",
        "j"
      ] /* used_columns */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i`,`test`.`t1`.`j` AS `j` from `test`.`t1`
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	6
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	6
Handler_update	5

DROP TABLE t1;
#23
CREATE TABLE t1 (i INT, j INT);
INSERT INTO t1 VALUES (1, 1), (2, 2), (3, 3), (4, 4), (5, 5);
#
# query:  DELETE FROM t1
# select: SELECT * FROM t1
#
EXPLAIN DELETE FROM t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	ALL	NULL	NULL	NULL	NULL	5	100.00	Deleting all rows
Warnings:
Note	1003	delete from `test`.`t1`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE FROM t1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "delete": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "filtered": "100.00",
      "message": "Deleting all rows"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.00"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 5,
      "rows_produced_per_join": 5,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.50",
        "prefix_cost": "1.00",
        "data_read_per_join": "80"
      } /* cost_info */,
      "used_columns": [
        "i",
        "j"
      ] /* used_columns */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i`,`test`.`t1`.`j` AS `j` from `test`.`t1`
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	6
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2

DROP TABLE t1;
#24
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2(a INT, b INT, c INT, d INT, INDEX(a, b, c));
INSERT INTO t2 (a, b, c) SELECT i, i, i FROM t1;
INSERT INTO t2 (a, b, c) SELECT t1.i, t1.i, t1.i FROM t1, t1 x1, t1 x2;
#
# query:  DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5
# select: SELECT * FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5
#
EXPLAIN DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	NULL	index	NULL	a	15	NULL	17602	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "delete": true,
        "table_name": "t2",
        "access_type": "index",
        "key": "a",
        "used_key_parts": [
          "a",
          "b",
          "c"
        ] /* used_key_parts */,
        "key_length": "15",
        "rows_examined_per_scan": 17602,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1778.96"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t2",
        "access_type": "index",
        "key": "a",
        "used_key_parts": [
          "a",
          "b",
          "c"
        ] /* used_key_parts */,
        "key_length": "15",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 1760,
        "filtered": "10.00",
        "cost_info": {
          "read_cost": "1602.94",
          "eval_cost": "176.02",
          "prefix_cost": "1778.96",
          "data_read_per_join": "41K"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c",
          "d"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_first	1
Handler_read_key	4
Handler_read_next	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	5
Handler_read_first	1
Handler_read_key	4
Handler_read_next	4

DROP TABLE t1, t2;
#25
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (i INT);
#
# query:  INSERT INTO t2 SELECT * FROM t1
# select: SELECT * FROM t1
#
EXPLAIN INSERT INTO t2 SELECT * FROM t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t2	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
Warnings:
Note	1003	insert into `test`.`t2` /* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON INSERT INTO t2 SELECT * FROM t1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "insert": true,
      "table_name": "t2",
      "access_type": "ALL"
    } /* table */,
    "insert_from": {
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 3,
        "rows_produced_per_join": 3,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.50",
          "eval_cost": "0.30",
          "prefix_cost": "0.80",
          "data_read_per_join": "24"
        } /* cost_info */,
        "used_columns": [
          "i"
        ] /* used_columns */
      } /* table */
    } /* insert_from */
  } /* query_block */
}
Warnings:
Note	1003	insert into `test`.`t2` /* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 3,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.30",
        "prefix_cost": "0.80",
        "data_read_per_join": "24"
      } /* cost_info */,
      "used_columns": [
        "i"
      ] /* used_columns */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1`
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	4
Handler_write	3

#
# query:  INSERT INTO t2 SELECT * FROM t1 JOIN t1 AS tt USING(i)
# select: SELECT * FROM t1 JOIN t1 AS tt USING(i)
#
EXPLAIN INSERT INTO t2 SELECT * FROM t1 JOIN t1 AS tt USING(i);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t2	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
1	SIMPLE	tt	NULL	ALL	NULL	NULL	NULL	NULL	3	33.33	Using where; Using join buffer (hash join)
Warnings:
Note	1003	insert into `test`.`t2` /* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` join `test`.`t1` `tt` where (`test`.`tt`.`i` = `test`.`t1`.`i`)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON INSERT INTO t2 SELECT * FROM t1 JOIN t1 AS tt USING(i);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.20"
    } /* cost_info */,
    "table": {
      "insert": true,
      "table_name": "t2",
      "access_type": "ALL"
    } /* table */,
    "insert_from": {
      "nested_loop": [
        {
          "table": {
            "table_name": "t1",
            "access_type": "ALL",
            "rows_examined_per_scan": 3,
            "rows_produced_per_join": 3,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.50",
              "eval_cost": "0.30",
              "prefix_cost": "0.80",
              "data_read_per_join": "24"
            } /* cost_info */,
            "used_columns": [
              "i"
            ] /* used_columns */
          } /* table */
        },
        {
          "table": {
            "table_name": "tt",
            "access_type": "ALL",
            "rows_examined_per_scan": 3,
            "rows_produced_per_join": 3,
            "filtered": "33.33",
            "using_join_buffer": "hash join",
            "cost_info": {
              "read_cost": "0.50",
              "eval_cost": "0.30",
              "prefix_cost": "2.20",
              "data_read_per_join": "24"
            } /* cost_info */,
            "used_columns": [
              "i"
            ] /* used_columns */,
            "attached_condition": "(`test`.`tt`.`i` = `test`.`t1`.`i`)"
          } /* table */
        }
      ] /* nested_loop */
    } /* insert_from */
  } /* query_block */
}
Warnings:
Note	1003	insert into `test`.`t2` /* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` join `test`.`t1` `tt` where (`test`.`tt`.`i` = `test`.`t1`.`i`)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 JOIN t1 AS tt USING(i);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.20"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "i"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "table_name": "tt",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "33.33",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "2.20",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "i"
          ] /* used_columns */,
          "attached_condition": "(`test`.`tt`.`i` = `test`.`t1`.`i`)"
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1` join `test`.`t1` `tt` where (`test`.`tt`.`i` = `test`.`t1`.`i`)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	8
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	8
Handler_write	3

DROP TABLE t1, t2;
#26
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (i INT);
#
# query:  REPLACE INTO t2 SELECT * FROM t1
# select: SELECT * FROM t1
#
EXPLAIN REPLACE INTO t2 SELECT * FROM t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	REPLACE	t2	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
Warnings:
Note	1003	replace into `test`.`t2` /* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON REPLACE INTO t2 SELECT * FROM t1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "replace": true,
      "table_name": "t2",
      "access_type": "ALL"
    } /* table */,
    "insert_from": {
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 3,
        "rows_produced_per_join": 3,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.50",
          "eval_cost": "0.30",
          "prefix_cost": "0.80",
          "data_read_per_join": "24"
        } /* cost_info */,
        "used_columns": [
          "i"
        ] /* used_columns */
      } /* table */
    } /* insert_from */
  } /* query_block */
}
Warnings:
Note	1003	replace into `test`.`t2` /* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 3,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.30",
        "prefix_cost": "0.80",
        "data_read_per_join": "24"
      } /* cost_info */,
      "used_columns": [
        "i"
      ] /* used_columns */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i` AS `i` from `test`.`t1`
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	4
Handler_write	3

DROP TABLE t1, t2;
#27
CREATE TABLE t1 (i INT);
#
# query:  INSERT INTO t1 SET i = 10
# select: 
#
EXPLAIN INSERT INTO t1 SET i = 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`i`) values (10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON INSERT INTO t1 SET i = 10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "insert": true,
      "table_name": "t1",
      "access_type": "ALL"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	insert into `test`.`t1` (`test`.`t1`.`i`) values (10)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	1
Handler_write	1

DROP TABLE t1;
#28
CREATE TABLE t1 (i INT);
#
# query:  REPLACE INTO t1 SET i = 10
# select: 
#
EXPLAIN REPLACE INTO t1 SET i = 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	REPLACE	t1	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	replace into `test`.`t1` (`test`.`t1`.`i`) values (10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON REPLACE INTO t1 SET i = 10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "replace": true,
      "table_name": "t1",
      "access_type": "ALL"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	replace into `test`.`t1` (`test`.`t1`.`i`) values (10)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	1
Handler_write	1

DROP TABLE t1;
#29
CREATE TABLE t1 (a INT, i INT PRIMARY KEY);
INSERT INTO t1 (i) VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
#
# query:  DELETE   FROM t1 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 1
# select: SELECT * FROM t1 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 1
#
EXPLAIN DELETE   FROM t1 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	range	PRIMARY	PRIMARY	4	const	8	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t1` where ((`test`.`t1`.`i` > 10) and (`test`.`t1`.`i` <= 18)) order by `test`.`t1`.`i` limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "delete": true,
        "table_name": "t1",
        "access_type": "range",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "i"
        ] /* used_key_parts */,
        "key_length": "4",
        "ref": [
          "const"
        ] /* ref */,
        "rows_examined_per_scan": 8,
        "filtered": "100.00",
        "attached_condition": "((`test`.`t1`.`i` > 10) and (`test`.`t1`.`i` <= 18))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1` where ((`test`.`t1`.`i` > 10) and (`test`.`t1`.`i` <= 18)) order by `test`.`t1`.`i` limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.86"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t1",
        "access_type": "range",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "i"
        ] /* used_key_parts */,
        "key_length": "4",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "3.06",
          "eval_cost": "0.80",
          "prefix_cost": "3.86",
          "data_read_per_join": "128"
        } /* cost_info */,
        "used_columns": [
          "a",
          "i"
        ] /* used_columns */,
        "attached_condition": "((`test`.`t1`.`i` > 10) and (`test`.`t1`.`i` <= 18))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`i` AS `i` from `test`.`t1` where ((`test`.`t1`.`i` > 10) and (`test`.`t1`.`i` <= 18)) order by `test`.`t1`.`i` limit 1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	3
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	1
Handler_read_key	3

DROP TABLE t1;
#30
CREATE TABLE t1(a INT, i CHAR(2), INDEX(i(1))) charset utf8mb4;
INSERT INTO t1 (i) VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
#
# query:  DELETE   FROM t1 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 5
# select: SELECT * FROM t1 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 5
#
EXPLAIN DELETE   FROM t1 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	ALL	NULL	NULL	NULL	NULL	26	100.00	Using where; Using filesort
Warnings:
Warning	1739	Cannot use range access on index 'i' due to type or collation conversion on field 'i'
Warning	1739	Cannot use range access on index 'i' due to type or collation conversion on field 'i'
Note	1003	delete from `test`.`t1` where ((`test`.`t1`.`i` > 10) and (`test`.`t1`.`i` <= 18)) order by `test`.`t1`.`i` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "delete": true,
        "table_name": "t1",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "filtered": "100.00",
        "attached_condition": "((`test`.`t1`.`i` > 10) and (`test`.`t1`.`i` <= 18))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Warning	1739	Cannot use range access on index 'i' due to type or collation conversion on field 'i'
Warning	1739	Cannot use range access on index 'i' due to type or collation conversion on field 'i'
Note	1003	delete from `test`.`t1` where ((`test`.`t1`.`i` > 10) and (`test`.`t1`.`i` <= 18)) order by `test`.`t1`.`i` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.01"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "2.89"
      } /* cost_info */,
      "table": {
        "table_name": "t1",
        "access_type": "ALL",
        "possible_keys": [
          "i"
        ] /* possible_keys */,
        "rows_examined_per_scan": 26,
        "rows_produced_per_join": 2,
        "filtered": "11.11",
        "cost_info": {
          "read_cost": "2.83",
          "eval_cost": "0.29",
          "prefix_cost": "3.12",
          "data_read_per_join": "46"
        } /* cost_info */,
        "used_columns": [
          "a",
          "i"
        ] /* used_columns */,
        "attached_condition": "((`test`.`t1`.`i` > 10) and (`test`.`t1`.`i` <= 18))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Warning	1739	Cannot use range access on index 'i' due to type or collation conversion on field 'i'
Warning	1739	Cannot use range access on index 'i' due to type or collation conversion on field 'i'
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t1`.`i` AS `i` from `test`.`t1` where ((`test`.`t1`.`i` > 10) and (`test`.`t1`.`i` <= 18)) order by `test`.`t1`.`i` limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	27
Sort_rows	5
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	5
Handler_read_key	2
Handler_read_rnd	5
Handler_read_rnd_next	27
Sort_rows	8
Sort_scan	1

DROP TABLE t1;
#31
CREATE TABLE t1 (i INT);
INSERT INTO t1 (i) VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2(a INT, b INT, c INT, d INT, INDEX(a, b, c));
INSERT INTO t2 (a, b, c) SELECT i, i, i FROM t1;
#
# query:  DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5
# select: SELECT * FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5
#
EXPLAIN DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	NULL	ALL	NULL	NULL	NULL	NULL	26	100.00	Using where; Using filesort
Warnings:
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "delete": true,
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.13"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t2",
        "access_type": "index",
        "key": "a",
        "used_key_parts": [
          "a",
          "b",
          "c"
        ] /* used_key_parts */,
        "key_length": "15",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 2,
        "filtered": "10.00",
        "cost_info": {
          "read_cost": "2.87",
          "eval_cost": "0.26",
          "prefix_cost": "3.13",
          "data_read_per_join": "62"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c",
          "d"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_first	1
Handler_read_key	4
Handler_read_next	26
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	1
Handler_read_key	4
Handler_read_rnd	1
Handler_read_rnd_next	27
Sort_rows	1
Sort_scan	1

DROP TABLE t1, t2;
#32
CREATE TABLE t1 (i INT);
INSERT INTO t1 (i) VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2(a INT, b INT, c INT, d INT, INDEX(a, b, c));
INSERT INTO t2 (a, b, c) SELECT i, i, i FROM t1;
INSERT INTO t2 (a, b, c) SELECT t1.i, t1.i, t1.i FROM t1, t1 x1, t1 x2;
#
# query:  DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5
# select: SELECT * FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5
#
EXPLAIN DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	NULL	index	NULL	a	15	NULL	17602	100.00	Using where
Warnings:
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "delete": true,
        "table_name": "t2",
        "access_type": "index",
        "key": "a",
        "used_key_parts": [
          "a",
          "b",
          "c"
        ] /* used_key_parts */,
        "key_length": "15",
        "rows_examined_per_scan": 17602,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1778.96"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t2",
        "access_type": "index",
        "key": "a",
        "used_key_parts": [
          "a",
          "b",
          "c"
        ] /* used_key_parts */,
        "key_length": "15",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 1760,
        "filtered": "10.00",
        "cost_info": {
          "read_cost": "1602.94",
          "eval_cost": "176.02",
          "prefix_cost": "1778.96",
          "data_read_per_join": "41K"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c",
          "d"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_first	1
Handler_read_key	4
Handler_read_next	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	5
Handler_read_first	1
Handler_read_key	4
Handler_read_next	4

DROP TABLE t1, t2;
#33
CREATE TABLE t1 (i INT);
INSERT INTO t1 (i) VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2 (a CHAR(2), b CHAR(2), c CHAR(2), d CHAR(2), INDEX (a,b(1),c))
charset utf8mb4;
INSERT INTO t2 SELECT i, i, i, i FROM t1;
#
# query:  DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5
# select: SELECT * FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5
#
EXPLAIN DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	NULL	ALL	NULL	NULL	NULL	NULL	26	100.00	Using where; Using filesort
Warnings:
Warning	1739	Cannot use range access on index 'a' due to type or collation conversion on field 'b'
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "delete": true,
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Warning	1739	Cannot use range access on index 'a' due to type or collation conversion on field 'b'
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "5.75"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "2.60"
      } /* cost_info */,
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "rows_produced_per_join": 2,
        "filtered": "10.00",
        "cost_info": {
          "read_cost": "2.89",
          "eval_cost": "0.26",
          "prefix_cost": "3.15",
          "data_read_per_join": "104"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c",
          "d"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	4
Handler_read_rnd_next	27
Sort_rows	1
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	1
Handler_read_key	4
Handler_read_rnd	1
Handler_read_rnd_next	27
Sort_rows	1
Sort_scan	1

DROP TABLE t1, t2;
#34
CREATE TABLE t1 (i INT);
INSERT INTO t1 (i) VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2 (a CHAR(2), b CHAR(2), c CHAR(2), d CHAR(2), INDEX (a,b,c))
charset utf8mb4  ENGINE=HEAP;
INSERT INTO t2 SELECT i, i, i, i FROM t1;
#
# query:  DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5
# select: SELECT * FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5
#
EXPLAIN DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	NULL	ALL	NULL	NULL	NULL	NULL	26	100.00	Using where; Using filesort
Warnings:
Warning	1739	Cannot use range access on index 'a' due to type or collation conversion on field 'b'
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "delete": true,
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Warning	1739	Cannot use range access on index 'a' due to type or collation conversion on field 'b'
Note	1003	delete from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "8.02"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "2.60"
      } /* cost_info */,
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "rows_produced_per_join": 2,
        "filtered": "10.00",
        "cost_info": {
          "read_cost": "5.16",
          "eval_cost": "0.26",
          "prefix_cost": "5.42",
          "data_read_per_join": "104"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c",
          "d"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Warning	1739	Cannot use range access on index 'a' due to type or collation conversion on field 'b'
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	4
Handler_read_rnd_next	27
Sort_rows	1
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	1
Handler_read_key	4
Handler_read_rnd	1
Handler_read_rnd_next	27
Sort_rows	1
Sort_scan	1

DROP TABLE t1, t2;
#35
CREATE TABLE t1 (i INT);
INSERT INTO t1 (i) VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35),(36),(37),(38),(39),
(40),(41),(42),(43),(44);
CREATE TABLE t2 (i INT, key1 INT, key2 INT, INDEX (key1), INDEX (key2));
INSERT INTO t2 (key1, key2) SELECT i, i FROM t1;
#
# query:  DELETE   FROM t2 WHERE key1 < 13 or key2 < 14 ORDER BY key1
# select: SELECT * FROM t2 WHERE key1 < 13 or key2 < 14 ORDER BY key1
#
EXPLAIN DELETE   FROM t2 WHERE key1 < 13 or key2 < 14 ORDER BY key1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	NULL	index_merge	key1,key2	key1,key2	5,5	NULL	7	100.00	Using sort_union(key1,key2); Using where; Using filesort
Warnings:
Note	1003	delete from `test`.`t2` where ((`test`.`t2`.`key1` < 13) or (`test`.`t2`.`key2` < 14)) order by `test`.`t2`.`key1`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t2 WHERE key1 < 13 or key2 < 14 ORDER BY key1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "delete": true,
        "table_name": "t2",
        "access_type": "index_merge",
        "possible_keys": [
          "key1",
          "key2"
        ] /* possible_keys */,
        "key": "sort_union(key1,key2)",
        "key_length": "5,5",
        "rows_examined_per_scan": 7,
        "filtered": "100.00",
        "attached_condition": "((`test`.`t2`.`key1` < 13) or (`test`.`t2`.`key2` < 14))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t2` where ((`test`.`t2`.`key1` < 13) or (`test`.`t2`.`key2` < 14)) order by `test`.`t2`.`key1`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2 WHERE key1 < 13 or key2 < 14 ORDER BY key1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.73"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "7.00"
      } /* cost_info */,
      "table": {
        "table_name": "t2",
        "access_type": "index_merge",
        "possible_keys": [
          "key1",
          "key2"
        ] /* possible_keys */,
        "key": "sort_union(key1,key2)",
        "key_length": "5,5",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "3.03",
          "eval_cost": "0.70",
          "prefix_cost": "3.73",
          "data_read_per_join": "112"
        } /* cost_info */,
        "used_columns": [
          "i",
          "key1",
          "key2"
        ] /* used_columns */,
        "attached_condition": "((`test`.`t2`.`key1` < 13) or (`test`.`t2`.`key2` < 14))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`i` AS `i`,`test`.`t2`.`key1` AS `key1`,`test`.`t2`.`key2` AS `key2` from `test`.`t2` where ((`test`.`t2`.`key1` < 13) or (`test`.`t2`.`key2` < 14)) order by `test`.`t2`.`key1`
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_next	7
Handler_read_rnd	4
Sort_rows	4
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	4
Handler_read_key	5
Handler_read_next	7
Handler_read_rnd	8
Sort_rows	4
Sort_scan	1

DROP TABLE t1, t2;
#36
CREATE TABLE t1 (i INT);
INSERT INTO t1 (i) VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2(a INT, i INT PRIMARY KEY);
INSERT INTO t2 (i) SELECT i FROM t1;
#
# query:  DELETE   FROM t2 WHERE i > 10 AND i <= 18 ORDER BY i DESC LIMIT 1
# select: SELECT * FROM t2 WHERE i > 10 AND i <= 18 ORDER BY i DESC LIMIT 1
#
EXPLAIN DELETE   FROM t2 WHERE i > 10 AND i <= 18 ORDER BY i DESC LIMIT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	NULL	range	PRIMARY	PRIMARY	4	const	8	100.00	Using where; Backward index scan
Warnings:
Note	1003	delete from `test`.`t2` where ((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18)) order by `test`.`t2`.`i` desc limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t2 WHERE i > 10 AND i <= 18 ORDER BY i DESC LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "delete": true,
        "table_name": "t2",
        "access_type": "range",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "i"
        ] /* used_key_parts */,
        "key_length": "4",
        "ref": [
          "const"
        ] /* ref */,
        "rows_examined_per_scan": 8,
        "filtered": "100.00",
        "backward_index_scan": true,
        "attached_condition": "((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t2` where ((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18)) order by `test`.`t2`.`i` desc limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2 WHERE i > 10 AND i <= 18 ORDER BY i DESC LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.86"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t2",
        "access_type": "range",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "i"
        ] /* used_key_parts */,
        "key_length": "4",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "backward_index_scan": true,
        "cost_info": {
          "read_cost": "3.06",
          "eval_cost": "0.80",
          "prefix_cost": "3.86",
          "data_read_per_join": "128"
        } /* cost_info */,
        "used_columns": [
          "a",
          "i"
        ] /* used_columns */,
        "attached_condition": "((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`i` AS `i` from `test`.`t2` where ((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18)) order by `test`.`t2`.`i` desc limit 1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	3
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	1
Handler_read_key	3

DROP TABLE t1, t2;
#37
CREATE TABLE t1 (i INT);
INSERT INTO t1 (i) VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2 (a CHAR(2), b CHAR(2), c CHAR(2), INDEX (a, b)) charset utf8mb4;
INSERT INTO t2 SELECT i, i, i FROM t1;
#
# query:  DELETE   FROM t2 ORDER BY a, b DESC LIMIT 5
# select: SELECT * FROM t2 ORDER BY a, b DESC LIMIT 5
#
EXPLAIN DELETE   FROM t2 ORDER BY a, b DESC LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	NULL	ALL	NULL	NULL	NULL	NULL	26	100.00	Using filesort
Warnings:
Note	1003	delete from `test`.`t2` order by `test`.`t2`.`a`,`test`.`t2`.`b` desc limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t2 ORDER BY a, b DESC LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "delete": true,
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "filtered": "100.00"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t2` order by `test`.`t2`.`a`,`test`.`t2`.`b` desc limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2 ORDER BY a, b DESC LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.14"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "rows_produced_per_join": 26,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.54",
          "eval_cost": "2.60",
          "prefix_cost": "3.14",
          "data_read_per_join": "832"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c"
        ] /* used_columns */
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c` from `test`.`t2` order by `test`.`t2`.`a`,`test`.`t2`.`b` desc limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	3
Handler_read_rnd_next	27
Sort_rows	5
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	5
Handler_read_key	3
Handler_read_rnd	5
Handler_read_rnd_next	27
Sort_rows	26
Sort_scan	1

DROP TABLE t1, t2;
#38
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2 (a CHAR(2), b CHAR(2), c INT, INDEX (a, b)) charset utf8mb4;
INSERT INTO t2 (a, b) SELECT i, i FROM t1;
INSERT INTO t2 (a, b) SELECT t1.i, t1.i FROM t1, t1 x1, t1 x2;
#
# query:  DELETE   FROM t2 ORDER BY a DESC, b DESC LIMIT 5
# select: SELECT * FROM t2 ORDER BY a DESC, b DESC LIMIT 5
#
EXPLAIN DELETE   FROM t2 ORDER BY a DESC, b DESC LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t2	NULL	index	NULL	a	18	NULL	5	100.00	NULL
Warnings:
Note	1003	delete from `test`.`t2` order by `test`.`t2`.`a` desc,`test`.`t2`.`b` desc limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t2 ORDER BY a DESC, b DESC LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "delete": true,
        "table_name": "t2",
        "access_type": "index",
        "key": "a",
        "used_key_parts": [
          "a",
          "b"
        ] /* used_key_parts */,
        "key_length": "18",
        "rows_examined_per_scan": 5,
        "filtered": "100.00"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t2` order by `test`.`t2`.`a` desc,`test`.`t2`.`b` desc limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2 ORDER BY a DESC, b DESC LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1783.26"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t2",
        "access_type": "index",
        "key": "a",
        "used_key_parts": [
          "a",
          "b"
        ] /* used_key_parts */,
        "key_length": "18",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 17602,
        "filtered": "100.00",
        "backward_index_scan": true,
        "cost_info": {
          "read_cost": "23.06",
          "eval_cost": "1760.20",
          "prefix_cost": "1783.26",
          "data_read_per_join": "412K"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c"
        ] /* used_columns */
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c` from `test`.`t2` order by `test`.`t2`.`a` desc,`test`.`t2`.`b` desc limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	3
Handler_read_last	1
Handler_read_prev	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	5
Handler_read_key	3
Handler_read_last	1
Handler_read_prev	4

DROP TABLE t1, t2;
#39
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2(a INT, i INT PRIMARY KEY);
INSERT INTO t2 (i) SELECT i FROM t1;
#
# query:  UPDATE t2 SET a = 10 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 1
# select: SELECT * FROM  t2    WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 1
#
EXPLAIN UPDATE t2 SET a = 10 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	NULL	range	PRIMARY	PRIMARY	4	const	8	100.00	Using where; Using temporary
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`a` = 10 where ((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18)) order by `test`.`t2`.`i` limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t2 SET a = 10 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "table": {
        "update": true,
        "table_name": "t2",
        "access_type": "range",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "i"
        ] /* used_key_parts */,
        "key_length": "4",
        "ref": [
          "const"
        ] /* ref */,
        "rows_examined_per_scan": 8,
        "filtered": "100.00",
        "attached_condition": "((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`a` = 10 where ((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18)) order by `test`.`t2`.`i` limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM  t2    WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.86"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t2",
        "access_type": "range",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "i"
        ] /* used_key_parts */,
        "key_length": "4",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "3.06",
          "eval_cost": "0.80",
          "prefix_cost": "3.86",
          "data_read_per_join": "128"
        } /* cost_info */,
        "used_columns": [
          "a",
          "i"
        ] /* used_columns */,
        "attached_condition": "((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`i` AS `i` from `test`.`t2` where ((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18)) order by `test`.`t2`.`i` limit 1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	3
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	3
Handler_read_rnd	1
Handler_update	1

DROP TABLE t1, t2;
#40
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2(a INT, i CHAR(2), INDEX(i(1))) charset utf8mb4;
INSERT INTO t2 (i) SELECT i FROM t1;
#
# query:  UPDATE t2 SET a = 10 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 5
# select: SELECT * FROM t2     WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 5
#
EXPLAIN UPDATE t2 SET a = 10 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	NULL	ALL	NULL	NULL	NULL	NULL	26	100.00	Using where; Using filesort
Warnings:
Warning	1739	Cannot use range access on index 'i' due to type or collation conversion on field 'i'
Warning	1739	Cannot use range access on index 'i' due to type or collation conversion on field 'i'
Note	1003	update `test`.`t2` set `test`.`t2`.`a` = 10 where ((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18)) order by `test`.`t2`.`i` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t2 SET a = 10 WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "update": true,
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "filtered": "100.00",
        "attached_condition": "((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Warning	1739	Cannot use range access on index 'i' due to type or collation conversion on field 'i'
Warning	1739	Cannot use range access on index 'i' due to type or collation conversion on field 'i'
Note	1003	update `test`.`t2` set `test`.`t2`.`a` = 10 where ((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18)) order by `test`.`t2`.`i` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2     WHERE i > 10 AND i <= 18 ORDER BY i LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "6.01"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "2.89"
      } /* cost_info */,
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "possible_keys": [
          "i"
        ] /* possible_keys */,
        "rows_examined_per_scan": 26,
        "rows_produced_per_join": 2,
        "filtered": "11.11",
        "cost_info": {
          "read_cost": "2.83",
          "eval_cost": "0.29",
          "prefix_cost": "3.12",
          "data_read_per_join": "46"
        } /* cost_info */,
        "used_columns": [
          "a",
          "i"
        ] /* used_columns */,
        "attached_condition": "((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Warning	1739	Cannot use range access on index 'i' due to type or collation conversion on field 'i'
Warning	1739	Cannot use range access on index 'i' due to type or collation conversion on field 'i'
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`i` AS `i` from `test`.`t2` where ((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18)) order by `test`.`t2`.`i` limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	27
Sort_rows	5
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd	5
Handler_read_rnd_next	27
Handler_update	5
Sort_rows	5
Sort_scan	1

DROP TABLE t1, t2;
#41
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2(a INT, b INT, c INT, d INT, INDEX(a, b, c));
INSERT INTO t2 (a, b, c) SELECT i, i, i FROM t1;
#
# query:  UPDATE t2 SET d = 10 WHERE b = 10 ORDER BY a, c LIMIT 5
# select: SELECT * FROM t2     WHERE b = 10 ORDER BY a, c LIMIT 5
#
EXPLAIN UPDATE t2 SET d = 10 WHERE b = 10 ORDER BY a, c LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	NULL	ALL	NULL	NULL	NULL	NULL	26	100.00	Using where; Using filesort
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`d` = 10 where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t2 SET d = 10 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "update": true,
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`d` = 10 where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2     WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.13"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t2",
        "access_type": "index",
        "key": "a",
        "used_key_parts": [
          "a",
          "b",
          "c"
        ] /* used_key_parts */,
        "key_length": "15",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 2,
        "filtered": "10.00",
        "cost_info": {
          "read_cost": "2.87",
          "eval_cost": "0.26",
          "prefix_cost": "3.13",
          "data_read_per_join": "62"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c",
          "d"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_first	1
Handler_read_key	4
Handler_read_next	26
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	4
Handler_read_rnd	1
Handler_read_rnd_next	27
Handler_update	1
Sort_rows	1
Sort_scan	1

DROP TABLE t1, t2;
#42
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2(a INT, b INT, c INT, d INT, INDEX(a, b, c));
INSERT INTO t2 (a, b, c) SELECT i, i, i FROM t1;
INSERT INTO t2 (a, b, c) SELECT t1.i, t1.i, t1.i FROM t1, t1 x1, t1 x2;
#
# query:  UPDATE t2 SET d = 10 WHERE b = 10 ORDER BY a, c LIMIT 5
# select: SELECT * FROM t2     WHERE b = 10 ORDER BY a, c LIMIT 5
#
EXPLAIN UPDATE t2 SET d = 10 WHERE b = 10 ORDER BY a, c LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	NULL	index	NULL	a	15	NULL	17602	100.00	Using where; Using temporary
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`d` = 10 where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t2 SET d = 10 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "table": {
        "update": true,
        "table_name": "t2",
        "access_type": "index",
        "key": "a",
        "used_key_parts": [
          "a",
          "b",
          "c"
        ] /* used_key_parts */,
        "key_length": "15",
        "rows_examined_per_scan": 17602,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`d` = 10 where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2     WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1778.96"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t2",
        "access_type": "index",
        "key": "a",
        "used_key_parts": [
          "a",
          "b",
          "c"
        ] /* used_key_parts */,
        "key_length": "15",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 1760,
        "filtered": "10.00",
        "cost_info": {
          "read_cost": "1602.94",
          "eval_cost": "176.02",
          "prefix_cost": "1778.96",
          "data_read_per_join": "41K"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c",
          "d"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_first	1
Handler_read_key	4
Handler_read_next	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_first	1
Handler_read_key	4
Handler_read_next	4
Handler_read_rnd	5
Handler_update	5

DROP TABLE t1, t2;
#43
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2 (a CHAR(2), b CHAR(2), c CHAR(2), d CHAR(2), INDEX (a,b(1),c))
charset utf8mb4;
INSERT INTO t2 SELECT i, i, i, i FROM t1;
#
# query:  UPDATE t2 SET d = 10 WHERE b = 10 ORDER BY a, c LIMIT 5
# select: SELECT * FROM t2     WHERE b = 10 ORDER BY a, c LIMIT 5
#
EXPLAIN UPDATE t2 SET d = 10 WHERE b = 10 ORDER BY a, c LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	NULL	ALL	NULL	NULL	NULL	NULL	26	100.00	Using where; Using filesort
Warnings:
Warning	1739	Cannot use range access on index 'a' due to type or collation conversion on field 'b'
Note	1003	update `test`.`t2` set `test`.`t2`.`d` = 10 where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t2 SET d = 10 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "update": true,
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Warning	1739	Cannot use range access on index 'a' due to type or collation conversion on field 'b'
Note	1003	update `test`.`t2` set `test`.`t2`.`d` = 10 where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2     WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "5.75"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "2.60"
      } /* cost_info */,
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "rows_produced_per_join": 2,
        "filtered": "10.00",
        "cost_info": {
          "read_cost": "2.89",
          "eval_cost": "0.26",
          "prefix_cost": "3.15",
          "data_read_per_join": "104"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c",
          "d"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	4
Handler_read_rnd_next	27
Sort_rows	1
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	4
Handler_read_rnd	1
Handler_read_rnd_next	27
Sort_rows	1
Sort_scan	1

DROP TABLE t1, t2;
#44
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2 (a CHAR(2), b CHAR(2), c CHAR(2), d CHAR(2), INDEX (a,b,c))
charset utf8mb4  ENGINE=HEAP;
INSERT INTO t2 SELECT i, i, i, i FROM t1;
#
# query:  UPDATE t2 SET d = 10 WHERE b = 10 ORDER BY a, c LIMIT 5
# select: SELECT * FROM t2     WHERE b = 10 ORDER BY a, c LIMIT 5
#
EXPLAIN UPDATE t2 SET d = 10 WHERE b = 10 ORDER BY a, c LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	NULL	ALL	NULL	NULL	NULL	NULL	26	100.00	Using where; Using filesort
Warnings:
Warning	1739	Cannot use range access on index 'a' due to type or collation conversion on field 'b'
Note	1003	update `test`.`t2` set `test`.`t2`.`d` = 10 where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t2 SET d = 10 WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "update": true,
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Warning	1739	Cannot use range access on index 'a' due to type or collation conversion on field 'b'
Note	1003	update `test`.`t2` set `test`.`t2`.`d` = 10 where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2     WHERE b = 10 ORDER BY a, c LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "8.02"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "2.60"
      } /* cost_info */,
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "rows_produced_per_join": 2,
        "filtered": "10.00",
        "cost_info": {
          "read_cost": "5.16",
          "eval_cost": "0.26",
          "prefix_cost": "5.42",
          "data_read_per_join": "104"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c",
          "d"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t2`.`b` = 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Warning	1739	Cannot use range access on index 'a' due to type or collation conversion on field 'b'
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c`,`test`.`t2`.`d` AS `d` from `test`.`t2` where (`test`.`t2`.`b` = 10) order by `test`.`t2`.`a`,`test`.`t2`.`c` limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	4
Handler_read_rnd_next	27
Sort_rows	1
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	4
Handler_read_rnd	1
Handler_read_rnd_next	27
Sort_rows	1
Sort_scan	1

DROP TABLE t1, t2;
#45
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35),(36),(37),(38),(39),
(40),(41),(42),(43),(44);
CREATE TABLE t2 (i INT, key1 INT, key2 INT, INDEX (key1), INDEX (key2));
INSERT INTO t2 (key1, key2) SELECT i, i FROM t1;
#
# query:  UPDATE t2 SET i = 123 WHERE key1 < 13 or key2 < 14 ORDER BY key1
# select: SELECT * FROM t2      WHERE key1 < 13 or key2 < 14 ORDER BY key1
#
EXPLAIN UPDATE t2 SET i = 123 WHERE key1 < 13 or key2 < 14 ORDER BY key1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	NULL	index_merge	key1,key2	key1,key2	5,5	NULL	7	100.00	Using sort_union(key1,key2); Using where; Using filesort
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`i` = 123 where ((`test`.`t2`.`key1` < 13) or (`test`.`t2`.`key2` < 14)) order by `test`.`t2`.`key1`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t2 SET i = 123 WHERE key1 < 13 or key2 < 14 ORDER BY key1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "update": true,
        "table_name": "t2",
        "access_type": "index_merge",
        "possible_keys": [
          "key1",
          "key2"
        ] /* possible_keys */,
        "key": "sort_union(key1,key2)",
        "key_length": "5,5",
        "rows_examined_per_scan": 7,
        "filtered": "100.00",
        "attached_condition": "((`test`.`t2`.`key1` < 13) or (`test`.`t2`.`key2` < 14))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`i` = 123 where ((`test`.`t2`.`key1` < 13) or (`test`.`t2`.`key2` < 14)) order by `test`.`t2`.`key1`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2      WHERE key1 < 13 or key2 < 14 ORDER BY key1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "10.73"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "7.00"
      } /* cost_info */,
      "table": {
        "table_name": "t2",
        "access_type": "index_merge",
        "possible_keys": [
          "key1",
          "key2"
        ] /* possible_keys */,
        "key": "sort_union(key1,key2)",
        "key_length": "5,5",
        "rows_examined_per_scan": 7,
        "rows_produced_per_join": 7,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "3.03",
          "eval_cost": "0.70",
          "prefix_cost": "3.73",
          "data_read_per_join": "112"
        } /* cost_info */,
        "used_columns": [
          "i",
          "key1",
          "key2"
        ] /* used_columns */,
        "attached_condition": "((`test`.`t2`.`key1` < 13) or (`test`.`t2`.`key2` < 14))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`i` AS `i`,`test`.`t2`.`key1` AS `key1`,`test`.`t2`.`key2` AS `key2` from `test`.`t2` where ((`test`.`t2`.`key1` < 13) or (`test`.`t2`.`key2` < 14)) order by `test`.`t2`.`key1`
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_next	7
Handler_read_rnd	4
Sort_rows	4
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_next	7
Handler_read_rnd	8
Handler_update	4
Sort_rows	4
Sort_scan	1

DROP TABLE t1, t2;
#46
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2(a INT, i INT PRIMARY KEY);
INSERT INTO t2 (i) SELECT i FROM t1;
#
# query:  UPDATE t2 SET a = 10 WHERE i > 10 AND i <= 18 ORDER BY i DESC LIMIT 1
# select: SELECT * FROM t2     WHERE i > 10 AND i <= 18 ORDER BY i DESC LIMIT 1
#
EXPLAIN UPDATE t2 SET a = 10 WHERE i > 10 AND i <= 18 ORDER BY i DESC LIMIT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	NULL	range	PRIMARY	PRIMARY	4	const	8	100.00	Using where; Backward index scan; Using temporary
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`a` = 10 where ((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18)) order by `test`.`t2`.`i` desc limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t2 SET a = 10 WHERE i > 10 AND i <= 18 ORDER BY i DESC LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "table": {
        "update": true,
        "table_name": "t2",
        "access_type": "range",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "i"
        ] /* used_key_parts */,
        "key_length": "4",
        "ref": [
          "const"
        ] /* ref */,
        "rows_examined_per_scan": 8,
        "filtered": "100.00",
        "backward_index_scan": true,
        "attached_condition": "((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`a` = 10 where ((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18)) order by `test`.`t2`.`i` desc limit 1
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2     WHERE i > 10 AND i <= 18 ORDER BY i DESC LIMIT 1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.86"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t2",
        "access_type": "range",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "i"
        ] /* used_key_parts */,
        "key_length": "4",
        "rows_examined_per_scan": 8,
        "rows_produced_per_join": 8,
        "filtered": "100.00",
        "backward_index_scan": true,
        "cost_info": {
          "read_cost": "3.06",
          "eval_cost": "0.80",
          "prefix_cost": "3.86",
          "data_read_per_join": "128"
        } /* cost_info */,
        "used_columns": [
          "a",
          "i"
        ] /* used_columns */,
        "attached_condition": "((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18))"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`i` AS `i` from `test`.`t2` where ((`test`.`t2`.`i` > 10) and (`test`.`t2`.`i` <= 18)) order by `test`.`t2`.`i` desc limit 1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	3
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	3
Handler_read_rnd	1
Handler_update	1

DROP TABLE t1, t2;
#47
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2 (a CHAR(2), b CHAR(2), c CHAR(2), INDEX (a, b)) charset utf8mb4;
INSERT INTO t2 SELECT i, i, i FROM t1;
#
# query:  UPDATE t2 SET c = 10 ORDER BY a, b DESC LIMIT 5
# select: SELECT * FROM t2     ORDER BY a, b DESC LIMIT 5
#
EXPLAIN UPDATE t2 SET c = 10 ORDER BY a, b DESC LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	NULL	ALL	NULL	NULL	NULL	NULL	26	100.00	Using filesort
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`c` = 10 order by `test`.`t2`.`a`,`test`.`t2`.`b` desc limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t2 SET c = 10 ORDER BY a, b DESC LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "update": true,
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "filtered": "100.00"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`c` = 10 order by `test`.`t2`.`a`,`test`.`t2`.`b` desc limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2     ORDER BY a, b DESC LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "3.14"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "table_name": "t2",
        "access_type": "ALL",
        "rows_examined_per_scan": 26,
        "rows_produced_per_join": 26,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.54",
          "eval_cost": "2.60",
          "prefix_cost": "3.14",
          "data_read_per_join": "832"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c"
        ] /* used_columns */
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c` from `test`.`t2` order by `test`.`t2`.`a`,`test`.`t2`.`b` desc limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	3
Handler_read_rnd_next	27
Sort_rows	5
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	3
Handler_read_rnd	5
Handler_read_rnd_next	27
Handler_update	4
Sort_rows	5
Sort_scan	1

DROP TABLE t1, t2;
#48
CREATE TABLE t1 (i INT);
INSERT INTO t1 VALUES (10),(11),(12),(13),(14),(15),(16),(17),(18),(19),
(20),(21),(22),(23),(24),(25),(26),(27),(28),(29),
(30),(31),(32),(33),(34),(35);
CREATE TABLE t2 (a CHAR(2), b CHAR(2), c INT, INDEX (a, b)) charset utf8mb4;
INSERT INTO t2 (a, b) SELECT i, i FROM t1;
INSERT INTO t2 (a, b) SELECT t1.i, t1.i FROM t1, t1 x1, t1 x2;
#
# query:  UPDATE t2 SET c = 10 ORDER BY a DESC, b DESC LIMIT 5
# select: SELECT * FROM t2     ORDER BY a DESC, b DESC LIMIT 5
#
EXPLAIN UPDATE t2 SET c = 10 ORDER BY a DESC, b DESC LIMIT 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	NULL	index	NULL	a	18	NULL	5	100.00	Using temporary
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`c` = 10 order by `test`.`t2`.`a` desc,`test`.`t2`.`b` desc limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t2 SET c = 10 ORDER BY a DESC, b DESC LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_temporary_table": true,
      "using_filesort": false,
      "table": {
        "update": true,
        "table_name": "t2",
        "access_type": "index",
        "key": "a",
        "used_key_parts": [
          "a",
          "b"
        ] /* used_key_parts */,
        "key_length": "18",
        "rows_examined_per_scan": 5,
        "filtered": "100.00"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t2` set `test`.`t2`.`c` = 10 order by `test`.`t2`.`a` desc,`test`.`t2`.`b` desc limit 5
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t2     ORDER BY a DESC, b DESC LIMIT 5;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1783.26"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": false,
      "table": {
        "table_name": "t2",
        "access_type": "index",
        "key": "a",
        "used_key_parts": [
          "a",
          "b"
        ] /* used_key_parts */,
        "key_length": "18",
        "rows_examined_per_scan": 5,
        "rows_produced_per_join": 17602,
        "filtered": "100.00",
        "backward_index_scan": true,
        "cost_info": {
          "read_cost": "23.06",
          "eval_cost": "1760.20",
          "prefix_cost": "1783.26",
          "data_read_per_join": "412K"
        } /* cost_info */,
        "used_columns": [
          "a",
          "b",
          "c"
        ] /* used_columns */
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`a` AS `a`,`test`.`t2`.`b` AS `b`,`test`.`t2`.`c` AS `c` from `test`.`t2` order by `test`.`t2`.`a` desc,`test`.`t2`.`b` desc limit 5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	3
Handler_read_last	1
Handler_read_prev	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	3
Handler_read_last	1
Handler_read_prev	4
Handler_read_rnd	5
Handler_update	5

DROP TABLE t1, t2;
#49
CREATE TABLE t1 (
pk INT NOT NULL AUTO_INCREMENT,
c1_idx CHAR(1) DEFAULT 'y',
c2 INT,
PRIMARY KEY (pk),
INDEX c1_idx (c1_idx)
) charset utf8mb4;
INSERT INTO t1 VALUES (1,'y',1), (2,'n',2), (3,'y',3), (4,'n',4);
#
# query:  UPDATE t1 SET c2 = 0 WHERE c1_idx = 'y' ORDER BY pk DESC LIMIT 2
# select: SELECT * FROM t1     WHERE c1_idx = 'y' ORDER BY pk DESC LIMIT 2
#
EXPLAIN UPDATE t1 SET c2 = 0 WHERE c1_idx = 'y' ORDER BY pk DESC LIMIT 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	range	c1_idx	c1_idx	5	const	2	100.00	Using where; Using filesort
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`c2` = 0 where (`test`.`t1`.`c1_idx` = 'y') order by `test`.`t1`.`pk` desc limit 2
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t1 SET c2 = 0 WHERE c1_idx = 'y' ORDER BY pk DESC LIMIT 2;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "update": true,
        "table_name": "t1",
        "access_type": "range",
        "possible_keys": [
          "c1_idx"
        ] /* possible_keys */,
        "key": "c1_idx",
        "used_key_parts": [
          "c1_idx"
        ] /* used_key_parts */,
        "key_length": "5",
        "ref": [
          "const"
        ] /* ref */,
        "rows_examined_per_scan": 2,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t1`.`c1_idx` = 'y')"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`c2` = 0 where (`test`.`t1`.`c1_idx` = 'y') order by `test`.`t1`.`pk` desc limit 2
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1     WHERE c1_idx = 'y' ORDER BY pk DESC LIMIT 2;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "table_name": "t1",
        "access_type": "ref",
        "possible_keys": [
          "c1_idx"
        ] /* possible_keys */,
        "key": "c1_idx",
        "used_key_parts": [
          "c1_idx"
        ] /* used_key_parts */,
        "key_length": "5",
        "ref": [
          "const"
        ] /* ref */,
        "rows_examined_per_scan": 2,
        "rows_produced_per_join": 2,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.50",
          "eval_cost": "0.20",
          "prefix_cost": "0.70",
          "data_read_per_join": "32"
        } /* cost_info */,
        "used_columns": [
          "pk",
          "c1_idx",
          "c2"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t1`.`c1_idx` = 'y')"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c1_idx` AS `c1_idx`,`test`.`t1`.`c2` AS `c2` from `test`.`t1` where (`test`.`t1`.`c1_idx` = 'y') order by `test`.`t1`.`pk` desc limit 2
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	4
Handler_read_next	2
Sort_rows	2
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	4
Handler_read_next	2
Handler_read_rnd	2
Handler_update	2
Sort_rows	2
Sort_scan	1

#
# query:  DELETE   FROM t1 WHERE c1_idx = 'y' ORDER BY pk DESC LIMIT 2
# select: SELECT * FROM t1 WHERE c1_idx = 'y' ORDER BY pk DESC LIMIT 2
#
EXPLAIN DELETE   FROM t1 WHERE c1_idx = 'y' ORDER BY pk DESC LIMIT 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	range	c1_idx	c1_idx	5	const	2	100.00	Using where; Using filesort
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`c1_idx` = 'y') order by `test`.`t1`.`pk` desc limit 2
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM t1 WHERE c1_idx = 'y' ORDER BY pk DESC LIMIT 2;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "delete": true,
        "table_name": "t1",
        "access_type": "range",
        "possible_keys": [
          "c1_idx"
        ] /* possible_keys */,
        "key": "c1_idx",
        "used_key_parts": [
          "c1_idx"
        ] /* used_key_parts */,
        "key_length": "5",
        "ref": [
          "const"
        ] /* ref */,
        "rows_examined_per_scan": 2,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t1`.`c1_idx` = 'y')"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	delete from `test`.`t1` where (`test`.`t1`.`c1_idx` = 'y') order by `test`.`t1`.`pk` desc limit 2
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 WHERE c1_idx = 'y' ORDER BY pk DESC LIMIT 2;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "table_name": "t1",
        "access_type": "ref",
        "possible_keys": [
          "c1_idx"
        ] /* possible_keys */,
        "key": "c1_idx",
        "used_key_parts": [
          "c1_idx"
        ] /* used_key_parts */,
        "key_length": "5",
        "ref": [
          "const"
        ] /* ref */,
        "rows_examined_per_scan": 2,
        "rows_produced_per_join": 2,
        "filtered": "100.00",
        "cost_info": {
          "read_cost": "0.50",
          "eval_cost": "0.20",
          "prefix_cost": "0.70",
          "data_read_per_join": "32"
        } /* cost_info */,
        "used_columns": [
          "pk",
          "c1_idx",
          "c2"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t1`.`c1_idx` = 'y')"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c1_idx` AS `c1_idx`,`test`.`t1`.`c2` AS `c2` from `test`.`t1` where (`test`.`t1`.`c1_idx` = 'y') order by `test`.`t1`.`pk` desc limit 2
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	4
Handler_read_next	2
Sort_rows	2
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	2
Handler_read_key	4
Handler_read_next	2
Handler_read_rnd	2
Sort_rows	2
Sort_scan	1

DROP TABLE t1;
#50
CREATE TABLE t1 (a INT AUTO_INCREMENT PRIMARY KEY);
INSERT INTO t1 VALUES (),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),(),();
#
# query:  UPDATE t1 SET a=a+10 WHERE a > 34
# select: SELECT * FROM t1     WHERE a > 34
#
EXPLAIN UPDATE t1 SET a=a+10 WHERE a > 34;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	range	PRIMARY	PRIMARY	4	const	3	100.00	Using where; Using temporary
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = (`test`.`t1`.`a` + 10) where (`test`.`t1`.`a` > 34)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t1 SET a=a+10 WHERE a > 34;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t1",
      "access_type": "range",
      "possible_keys": [
        "PRIMARY"
      ] /* possible_keys */,
      "key": "PRIMARY",
      "used_key_parts": [
        "a"
      ] /* used_key_parts */,
      "key_length": "4",
      "ref": [
        "const"
      ] /* ref */,
      "rows_examined_per_scan": 3,
      "filtered": "100.00",
      "using_temporary_table": "for update",
      "attached_condition": "(`test`.`t1`.`a` > 34)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = (`test`.`t1`.`a` + 10) where (`test`.`t1`.`a` > 34)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1     WHERE a > 34;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.87"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "range",
      "possible_keys": [
        "PRIMARY"
      ] /* possible_keys */,
      "key": "PRIMARY",
      "used_key_parts": [
        "a"
      ] /* used_key_parts */,
      "key_length": "4",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 3,
      "filtered": "100.00",
      "using_index": true,
      "cost_info": {
        "read_cost": "0.57",
        "eval_cost": "0.30",
        "prefix_cost": "0.87",
        "data_read_per_join": "24"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "(`test`.`t1`.`a` > 34)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` > 34)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_next	2
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_next	2
Handler_read_rnd	2
Handler_update	2

DROP TABLE t1;
#51
CREATE TABLE t1 (c1 INT, c2 INT, c3 INT);
CREATE TABLE t2 (c1 INT, c2 INT);
INSERT INTO t1 VALUES (1, 1, 10), (2, 2, 20);
#
# query:  UPDATE        t1 LEFT JOIN t2 ON t1.c1 = t2.c1 SET t2.c2 = 10
# select: SELECT * FROM t1 LEFT JOIN t2 ON t1.c1 = t2.c1
#
EXPLAIN UPDATE        t1 LEFT JOIN t2 ON t1.c1 = t2.c1 SET t2.c2 = 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	NULL	system	NULL	NULL	NULL	NULL	0	0.00	const row not found
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
Warnings:
Note	1003	update `test`.`t1` left join `test`.`t2` on(multiple equal(`test`.`t1`.`c1`, `test`.`t2`.`c1`)) set `test`.`t2`.`c2` = 10 where true
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1 LEFT JOIN t2 ON t1.c1 = t2.c1 SET t2.c2 = 10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t2",
          "access_type": "system",
          "rows_examined_per_scan": 0,
          "rows_produced_per_join": 1,
          "filtered": "0.00",
          "const_row_not_found": true,
          "cost_info": {
            "read_cost": "0.00",
            "eval_cost": "0.10",
            "prefix_cost": "0.00",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "c1",
            "c2"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.20",
            "prefix_cost": "0.70",
            "data_read_per_join": "32"
          } /* cost_info */,
          "used_columns": [
            "c1"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` left join `test`.`t2` on(multiple equal(`test`.`t1`.`c1`, `test`.`t2`.`c1`)) set `test`.`t2`.`c2` = 10 where true
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 LEFT JOIN t2 ON t1.c1 = t2.c1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t2",
          "access_type": "system",
          "rows_examined_per_scan": 0,
          "rows_produced_per_join": 1,
          "filtered": "0.00",
          "const_row_not_found": true,
          "cost_info": {
            "read_cost": "0.00",
            "eval_cost": "0.10",
            "prefix_cost": "0.00",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "c1",
            "c2"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.20",
            "prefix_cost": "0.70",
            "data_read_per_join": "32"
          } /* cost_info */,
          "used_columns": [
            "c1",
            "c2",
            "c3"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,NULL AS `c1`,NULL AS `c2` from `test`.`t1` where true
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_rnd_next	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_rnd_next	4

#
# query:  UPDATE        t1 LEFT JOIN t2 ON t1.c1 = t2.c1 SET t2.c2 = 10 WHERE t1.c3 = 10
# select: SELECT * FROM t1 LEFT JOIN t2 ON t1.c1 = t2.c1                WHERE t1.c3 = 10
#
EXPLAIN UPDATE        t1 LEFT JOIN t2 ON t1.c1 = t2.c1 SET t2.c2 = 10 WHERE t1.c3 = 10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t2	NULL	system	NULL	NULL	NULL	NULL	0	0.00	const row not found
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
Warnings:
Note	1003	update `test`.`t1` left join `test`.`t2` on(multiple equal(`test`.`t1`.`c1`, `test`.`t2`.`c1`)) set `test`.`t2`.`c2` = 10 where (`test`.`t1`.`c3` = 10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1 LEFT JOIN t2 ON t1.c1 = t2.c1 SET t2.c2 = 10 WHERE t1.c3 = 10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t2",
          "access_type": "system",
          "rows_examined_per_scan": 0,
          "rows_produced_per_join": 1,
          "filtered": "0.00",
          "const_row_not_found": true,
          "cost_info": {
            "read_cost": "0.00",
            "eval_cost": "0.10",
            "prefix_cost": "0.00",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "c1",
            "c2"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 1,
          "filtered": "50.00",
          "cost_info": {
            "read_cost": "0.60",
            "eval_cost": "0.10",
            "prefix_cost": "0.70",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "c1",
            "c3"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t1`.`c3` = 10)"
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` left join `test`.`t2` on(multiple equal(`test`.`t1`.`c1`, `test`.`t2`.`c1`)) set `test`.`t2`.`c2` = 10 where (`test`.`t1`.`c3` = 10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1 LEFT JOIN t2 ON t1.c1 = t2.c1                WHERE t1.c3 = 10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t2",
          "access_type": "system",
          "rows_examined_per_scan": 0,
          "rows_produced_per_join": 1,
          "filtered": "0.00",
          "const_row_not_found": true,
          "cost_info": {
            "read_cost": "0.00",
            "eval_cost": "0.10",
            "prefix_cost": "0.00",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "c1",
            "c2"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 1,
          "filtered": "50.00",
          "cost_info": {
            "read_cost": "0.60",
            "eval_cost": "0.10",
            "prefix_cost": "0.70",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "c1",
            "c2",
            "c3"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t1`.`c3` = 10)"
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2`,`test`.`t1`.`c3` AS `c3`,NULL AS `c1`,NULL AS `c2` from `test`.`t1` where (`test`.`t1`.`c3` = 10)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_rnd_next	4
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_rnd_next	4

DROP TABLE t1, t2;
#52
CREATE TABLE t1(f1 INT, f2 INT);
CREATE TABLE t2(f3 INT, f4 INT);
CREATE INDEX IDX ON t2(f3);
INSERT INTO t1 VALUES(1,0),(2,0);
INSERT INTO t2 VALUES(1,1),(2,2);
#
# query:  UPDATE t1 SET t1.f2=(SELECT MAX(t2.f4) FROM t2 WHERE t2.f3=t1.f1)
# select: SELECT              (SELECT MAX(t2.f4) FROM t2 WHERE t2.f3=t1.f1) FROM t1
#
EXPLAIN UPDATE t1 SET t1.f2=(SELECT MAX(t2.f4) FROM t2 WHERE t2.f3=t1.f1);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
2	DEPENDENT SUBQUERY	t2	NULL	ref	IDX	IDX	5	test.t1.f1	1	100.00	NULL
Warnings:
Note	1276	Field or reference 'test.t1.f1' of SELECT #2 was resolved in SELECT #1
Note	1003	update `test`.`t1` set `test`.`t1`.`f2` = (/* select#2 */ select max(`test`.`t2`.`f4`) from `test`.`t2` where (`test`.`t2`.`f3` = `test`.`t1`.`f1`))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t1 SET t1.f2=(SELECT MAX(t2.f4) FROM t2 WHERE t2.f3=t1.f1);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 2,
      "filtered": "100.00"
    } /* table */,
    "update_value_subqueries": [
      {
        "dependent": true,
        "cacheable": false,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "0.35"
          } /* cost_info */,
          "table": {
            "table_name": "t2",
            "access_type": "ref",
            "possible_keys": [
              "IDX"
            ] /* possible_keys */,
            "key": "IDX",
            "used_key_parts": [
              "f3"
            ] /* used_key_parts */,
            "key_length": "5",
            "ref": [
              "test.t1.f1"
            ] /* ref */,
            "rows_examined_per_scan": 1,
            "rows_produced_per_join": 1,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.10",
              "prefix_cost": "0.35",
              "data_read_per_join": "16"
            } /* cost_info */,
            "used_columns": [
              "f3",
              "f4"
            ] /* used_columns */
          } /* table */
        } /* query_block */
      }
    ] /* update_value_subqueries */
  } /* query_block */
}
Warnings:
Note	1276	Field or reference 'test.t1.f1' of SELECT #2 was resolved in SELECT #1
Note	1003	update `test`.`t1` set `test`.`t1`.`f2` = (/* select#2 */ select max(`test`.`t2`.`f4`) from `test`.`t2` where (`test`.`t2`.`f3` = `test`.`t1`.`f1`))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT              (SELECT MAX(t2.f4) FROM t2 WHERE t2.f3=t1.f1) FROM t1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.70"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 2,
      "rows_produced_per_join": 2,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.20",
        "prefix_cost": "0.70",
        "data_read_per_join": "32"
      } /* cost_info */,
      "used_columns": [
        "f1"
      ] /* used_columns */
    } /* table */,
    "select_list_subqueries": [
      {
        "dependent": true,
        "cacheable": false,
        "query_block": {
          "select_id": 2,
          "cost_info": {
            "query_cost": "0.35"
          } /* cost_info */,
          "table": {
            "table_name": "t2",
            "access_type": "ref",
            "possible_keys": [
              "IDX"
            ] /* possible_keys */,
            "key": "IDX",
            "used_key_parts": [
              "f3"
            ] /* used_key_parts */,
            "key_length": "5",
            "ref": [
              "test.t1.f1"
            ] /* ref */,
            "rows_examined_per_scan": 1,
            "rows_produced_per_join": 1,
            "filtered": "100.00",
            "cost_info": {
              "read_cost": "0.25",
              "eval_cost": "0.10",
              "prefix_cost": "0.35",
              "data_read_per_join": "16"
            } /* cost_info */,
            "used_columns": [
              "f3",
              "f4"
            ] /* used_columns */
          } /* table */
        } /* query_block */
      }
    ] /* select_list_subqueries */
  } /* query_block */
}
Warnings:
Note	1276	Field or reference 'test.t1.f1' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select (/* select#2 */ select max(`test`.`t2`.`f4`) from `test`.`t2` where (`test`.`t2`.`f3` = `test`.`t1`.`f1`)) AS `(SELECT MAX(t2.f4) FROM t2 WHERE t2.f3=t1.f1)` from `test`.`t1`
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	6
Handler_read_next	2
Handler_read_rnd_next	3
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	6
Handler_read_next	2
Handler_read_rnd_next	3
Handler_update	2

DROP TABLE t1, t2;
#55
CREATE TABLE t1(a INT);
INSERT INTO t1 VALUES (1);
SET @a = NULL;
EXPLAIN DELETE FROM t1 WHERE (@a:= a);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
Note	1003	delete from `test`.`t1` where (0 <> (@a:=`test`.`t1`.`a`))
DROP TABLE t1;
#56
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
DELETE FROM t1 USING t1 WHERE uknown_column = 12345;
ERROR 42S22: Unknown column 'uknown_column' in 'where clause'
EXPLAIN DELETE FROM t1 USING t1 WHERE uknown_column = 12345;
ERROR 42S22: Unknown column 'uknown_column' in 'where clause'
DROP TABLE t1;
#57
CREATE TABLE t1(f1 INT);
EXPLAIN UPDATE t1 SET f2=1 ORDER BY f2;
ERROR 42S22: Unknown column 'f2' in 'field list'
UPDATE t1 SET f2=1 ORDER BY f2;
ERROR 42S22: Unknown column 'f2' in 'field list'
DROP TABLE t1;
#59
CREATE TABLE t1 ( a INT, KEY( a ) );
INSERT INTO t1 VALUES (0), (1);
CREATE VIEW v1 AS SELECT t11.a, t12.a AS b FROM t1 t11, t1 t12;
SET SESSION sql_safe_updates = 1;
EXPLAIN UPDATE IGNORE v1 SET a = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t11	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
1	SIMPLE	t12	NULL	index	NULL	a	5	NULL	2	100.00	Using index
Warnings:
Note	1003	update ignore (`test`.`t1` `t11` join `test`.`t1` `t12`) set `test`.`t11`.`a` = 1
UPDATE IGNORE v1 SET a = 1;
ERROR HY000: You are using safe update mode and you tried to update a table without a WHERE that uses a KEY column. 
SET SESSION sql_safe_updates = DEFAULT;
DROP TABLE t1;
DROP VIEW v1;
#62
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (0), (1);
CREATE VIEW v1 AS SELECT t11.a, t12.a AS b FROM t1 t11, t1 t12;
#
# query:  UPDATE        v1 SET a = 1 WHERE a > 0
# select: SELECT * FROM v1           WHERE a > 0
#
EXPLAIN UPDATE        v1 SET a = 1 WHERE a > 0;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t11	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
1	SIMPLE	t12	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
Warnings:
Note	1003	update (`test`.`t1` `t11` join `test`.`t1` `t12`) set `test`.`t11`.`a` = 1 where (`test`.`t11`.`a` > 0)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        v1 SET a = 1 WHERE a > 0;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.40"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 1,
          "filtered": "50.00",
          "cost_info": {
            "read_cost": "0.60",
            "eval_cost": "0.10",
            "prefix_cost": "0.70",
            "data_read_per_join": "8"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t11`.`a` > 0)"
        } /* table */
      },
      {
        "table": {
          "table_name": "t12",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.20",
            "prefix_cost": "1.40",
            "data_read_per_join": "16"
          } /* cost_info */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	update (`test`.`t1` `t11` join `test`.`t1` `t12`) set `test`.`t11`.`a` = 1 where (`test`.`t11`.`a` > 0)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM v1           WHERE a > 0;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.40"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 1,
          "filtered": "50.00",
          "cost_info": {
            "read_cost": "0.60",
            "eval_cost": "0.10",
            "prefix_cost": "0.70",
            "data_read_per_join": "8"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t11`.`a` > 0)"
        } /* table */
      },
      {
        "table": {
          "table_name": "t12",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.20",
            "prefix_cost": "1.40",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t11`.`a` AS `a`,`test`.`t12`.`a` AS `b` from `test`.`t1` `t11` join `test`.`t1` `t12` where (`test`.`t11`.`a` > 0)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	6
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd	1
Handler_read_rnd_next	8
Handler_write	2

#
# query:  UPDATE        t1, v1 SET v1.a = 1 WHERE t1.a = v1.a
# select: SELECT * FROM t1, v1              WHERE t1.a = v1.a
#
EXPLAIN UPDATE        t1, v1 SET v1.a = 1 WHERE t1.a = v1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
1	UPDATE	t11	NULL	ALL	NULL	NULL	NULL	NULL	2	50.00	Using where
1	SIMPLE	t12	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
Warnings:
Note	1003	update `test`.`t1` join `test`.`t1` `t11` join `test`.`t1` `t12` set `test`.`t11`.`a` = 1 where (`test`.`t11`.`a` = `test`.`t1`.`a`)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1, v1 SET v1.a = 1 WHERE t1.a = v1.a;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.50"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.20",
            "prefix_cost": "0.70",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "update": true,
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "50.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.20",
            "prefix_cost": "1.60",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t11`.`a` = `test`.`t1`.`a`)"
        } /* table */
      },
      {
        "table": {
          "table_name": "t12",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.40",
            "prefix_cost": "2.50",
            "data_read_per_join": "32"
          } /* cost_info */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` join `test`.`t1` `t11` join `test`.`t1` `t12` set `test`.`t11`.`a` = 1 where (`test`.`t11`.`a` = `test`.`t1`.`a`)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1, v1              WHERE t1.a = v1.a;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.50"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.20",
            "prefix_cost": "0.70",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */
        } /* table */
      },
      {
        "table": {
          "table_name": "t11",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 2,
          "filtered": "50.00",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.20",
            "prefix_cost": "1.60",
            "data_read_per_join": "16"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t11`.`a` = `test`.`t1`.`a`)"
        } /* table */
      },
      {
        "table": {
          "table_name": "t12",
          "access_type": "ALL",
          "rows_examined_per_scan": 2,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.40",
            "prefix_cost": "2.50",
            "data_read_per_join": "32"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t11`.`a` AS `a`,`test`.`t12`.`a` AS `b` from `test`.`t1` join `test`.`t1` `t11` join `test`.`t1` `t12` where (`test`.`t11`.`a` = `test`.`t1`.`a`)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	9
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd	2
Handler_read_rnd_next	18
Handler_update	1
Handler_write	4

DROP TABLE t1;
DROP VIEW v1;
#63
CREATE TABLE t1 (a INT, PRIMARY KEY(a));
INSERT INTO t1 VALUES (1), (2), (3), (4), (5);
CREATE VIEW v1 (a) AS SELECT a FROM t1;
#
# query:  DELETE   FROM v1 WHERE a < 4
# select: SELECT * FROM v1 WHERE a < 4
#
EXPLAIN DELETE   FROM v1 WHERE a < 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	range	PRIMARY	PRIMARY	4	const	3	100.00	Using where
Warnings:
Note	1003	delete from (`test`.`t1`) where (`test`.`t1`.`a` < 4)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE   FROM v1 WHERE a < 4;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "delete": true,
      "table_name": "t1",
      "access_type": "range",
      "possible_keys": [
        "PRIMARY"
      ] /* possible_keys */,
      "key": "PRIMARY",
      "used_key_parts": [
        "a"
      ] /* used_key_parts */,
      "key_length": "4",
      "ref": [
        "const"
      ] /* ref */,
      "rows_examined_per_scan": 3,
      "filtered": "100.00",
      "attached_condition": "(`test`.`t1`.`a` < 4)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	delete from (`test`.`t1`) where (`test`.`t1`.`a` < 4)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM v1 WHERE a < 4;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.87"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "range",
      "possible_keys": [
        "PRIMARY"
      ] /* possible_keys */,
      "key": "PRIMARY",
      "used_key_parts": [
        "a"
      ] /* used_key_parts */,
      "key_length": "4",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 3,
      "filtered": "100.00",
      "using_index": true,
      "cost_info": {
        "read_cost": "0.57",
        "eval_cost": "0.30",
        "prefix_cost": "0.87",
        "data_read_per_join": "24"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "(`test`.`t1`.`a` < 4)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where (`test`.`t1`.`a` < 4)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_first	1
Handler_read_key	1
Handler_read_next	3
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	3
Handler_read_first	1
Handler_read_key	1
Handler_read_next	3

DROP TABLE t1;
DROP VIEW v1;
#64
CREATE TABLE t1 (a INT, b INT, PRIMARY KEY(a));
INSERT INTO t1 VALUES (1,2), (2,3), (3,4), (4,5), (5,10);
CREATE TABLE t2 (x INT);
INSERT INTO t2 VALUES (1), (2), (3), (4);
CREATE VIEW v1 (a,c) AS SELECT a, b+1 FROM t1;
#
# query:  DELETE v1 FROM t2, v1 WHERE t2.x = v1.a
# select: SELECT *  FROM t2, v1 WHERE t2.x = v1.a
#
EXPLAIN DELETE v1 FROM t2, v1 WHERE t2.x = v1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	Using where
1	DELETE	t1	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t2.x	1	100.00	NULL
Warnings:
Note	1003	delete `test`.`t1` from `test`.`t2` join `test`.`t1` where (`test`.`t1`.`a` = `test`.`t2`.`x`)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE v1 FROM t2, v1 WHERE t2.x = v1.a;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.30"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 4,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.40",
            "prefix_cost": "0.90",
            "data_read_per_join": "32"
          } /* cost_info */,
          "used_columns": [
            "x"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t2`.`x` is not null)"
        } /* table */
      },
      {
        "table": {
          "delete": true,
          "table_name": "t1",
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ] /* possible_keys */,
          "key": "PRIMARY",
          "used_key_parts": [
            "a"
          ] /* used_key_parts */,
          "key_length": "4",
          "ref": [
            "test.t2.x"
          ] /* ref */,
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "1.00",
            "eval_cost": "0.40",
            "prefix_cost": "2.30",
            "data_read_per_join": "64"
          } /* cost_info */,
          "used_columns": [
            "a",
            "b"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	delete `test`.`t1` from `test`.`t2` join `test`.`t1` where (`test`.`t1`.`a` = `test`.`t2`.`x`)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT *  FROM t2, v1 WHERE t2.x = v1.a;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.30"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 4,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.40",
            "prefix_cost": "0.90",
            "data_read_per_join": "32"
          } /* cost_info */,
          "used_columns": [
            "x"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t2`.`x` is not null)"
        } /* table */
      },
      {
        "table": {
          "table_name": "t1",
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ] /* possible_keys */,
          "key": "PRIMARY",
          "used_key_parts": [
            "a"
          ] /* used_key_parts */,
          "key_length": "4",
          "ref": [
            "test.t2.x"
          ] /* ref */,
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "1.00",
            "eval_cost": "0.40",
            "prefix_cost": "2.30",
            "data_read_per_join": "64"
          } /* cost_info */,
          "used_columns": [
            "a",
            "b"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`x` AS `x`,`test`.`t1`.`a` AS `a`,(`test`.`t1`.`b` + 1) AS `c` from `test`.`t2` join `test`.`t1` where (`test`.`t1`.`a` = `test`.`t2`.`x`)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	7
Handler_read_rnd_next	5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	4
Handler_read_key	7
Handler_read_rnd	4
Handler_read_rnd_next	5

DROP TABLE t1,t2;
DROP VIEW v1;
#65
CREATE TABLE t1 (a INT, b INT, PRIMARY KEY(a));
INSERT INTO t1 VALUES (1,2), (2,3), (3,4), (4,5), (5,10);
CREATE TABLE t2 (x INT);
INSERT INTO t2 VALUES (1), (2), (3), (4);
CREATE VIEW v1 (a,c) AS SELECT a, b+1 FROM t1;
#
# query:  DELETE v1 FROM t2, v1 WHERE t2.x = v1.a
# select: SELECT *  FROM t2, v1 WHERE t2.x = v1.a
#
EXPLAIN DELETE v1 FROM t2, v1 WHERE t2.x = v1.a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	ALL	NULL	NULL	NULL	NULL	4	100.00	Using where
1	DELETE	t1	NULL	eq_ref	PRIMARY	PRIMARY	4	test.t2.x	1	100.00	NULL
Warnings:
Note	1003	delete `test`.`t1` from `test`.`t2` join `test`.`t1` where (`test`.`t1`.`a` = `test`.`t2`.`x`)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON DELETE v1 FROM t2, v1 WHERE t2.x = v1.a;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.30"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 4,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.40",
            "prefix_cost": "0.90",
            "data_read_per_join": "32"
          } /* cost_info */,
          "used_columns": [
            "x"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t2`.`x` is not null)"
        } /* table */
      },
      {
        "table": {
          "delete": true,
          "table_name": "t1",
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ] /* possible_keys */,
          "key": "PRIMARY",
          "used_key_parts": [
            "a"
          ] /* used_key_parts */,
          "key_length": "4",
          "ref": [
            "test.t2.x"
          ] /* ref */,
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "1.00",
            "eval_cost": "0.40",
            "prefix_cost": "2.30",
            "data_read_per_join": "64"
          } /* cost_info */,
          "used_columns": [
            "a",
            "b"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	delete `test`.`t1` from `test`.`t2` join `test`.`t1` where (`test`.`t1`.`a` = `test`.`t2`.`x`)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT *  FROM t2, v1 WHERE t2.x = v1.a;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.30"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 4,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.40",
            "prefix_cost": "0.90",
            "data_read_per_join": "32"
          } /* cost_info */,
          "used_columns": [
            "x"
          ] /* used_columns */,
          "attached_condition": "(`test`.`t2`.`x` is not null)"
        } /* table */
      },
      {
        "table": {
          "table_name": "t1",
          "access_type": "eq_ref",
          "possible_keys": [
            "PRIMARY"
          ] /* possible_keys */,
          "key": "PRIMARY",
          "used_key_parts": [
            "a"
          ] /* used_key_parts */,
          "key_length": "4",
          "ref": [
            "test.t2.x"
          ] /* ref */,
          "rows_examined_per_scan": 1,
          "rows_produced_per_join": 4,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "1.00",
            "eval_cost": "0.40",
            "prefix_cost": "2.30",
            "data_read_per_join": "64"
          } /* cost_info */,
          "used_columns": [
            "a",
            "b"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`x` AS `x`,`test`.`t1`.`a` AS `a`,(`test`.`t1`.`b` + 1) AS `c` from `test`.`t2` join `test`.`t1` where (`test`.`t1`.`a` = `test`.`t2`.`x`)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	7
Handler_read_rnd_next	5
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_delete	4
Handler_read_key	7
Handler_read_rnd	4
Handler_read_rnd_next	5

DROP TABLE t1,t2;
DROP VIEW v1;
#66
CREATE TABLE t1 (a INT);
CREATE VIEW v1 (x) AS SELECT a FROM t1;
#
# query:  INSERT INTO v1 VALUES (10)
# select: SELECT NULL
#
EXPLAIN INSERT INTO v1 VALUES (10);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` values (10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON INSERT INTO v1 VALUES (10);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "insert": true,
      "table_name": "t1",
      "access_type": "ALL"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	insert into `test`.`t1` values (10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT NULL;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "No tables used"
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select NULL AS `NULL`
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	1
Handler_write	1

DROP TABLE t1;
DROP VIEW v1;
#67
CREATE TABLE t1 (a INT);
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (1), (2), (3);
CREATE VIEW v1 (x) AS SELECT b FROM t2;
#
# query:  INSERT INTO v1 SELECT * FROM t1
# select: SELECT * FROM t1
#
EXPLAIN INSERT INTO v1 SELECT * FROM t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
Warnings:
Note	1003	insert into `test`.`t2` /* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON INSERT INTO v1 SELECT * FROM t1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "no matching row in const table"
  } /* query_block */
}
Warnings:
Note	1003	insert into `test`.`t2` /* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1`
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "message": "no matching row in const table"
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select NULL AS `a` from `test`.`t1`
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	1
Handler_read_rnd_next	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Handler_read_rnd_next	1

DROP TABLE t1, t2;
DROP VIEW v1;
#69
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (1), (2), (3);
CREATE TABLE t2 (b INT);
INSERT INTO t2 VALUES (1), (2), (3);
#
# query:  UPDATE        t1 SET a = 10 WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x)
# select: SELECT * FROM t1            WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x)
#
EXPLAIN UPDATE        t1 SET a = 10 WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
2	DEPENDENT SUBQUERY	<derived3>	NULL	index_subquery	<auto_key0>	<auto_key0>	5	func	2	100.00	Using index
3	DERIVED	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using filesort
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 10 where <in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1 SET a = 10 WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "filtered": "100.00",
      "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.70"
            } /* cost_info */,
            "table": {
              "table_name": "x",
              "access_type": "index_subquery",
              "possible_keys": [
                "<auto_key0>"
              ] /* possible_keys */,
              "key": "<auto_key0>",
              "used_key_parts": [
                "b"
              ] /* used_key_parts */,
              "key_length": "5",
              "ref": [
                "func"
              ] /* ref */,
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "using_index": true,
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.20",
                "prefix_cost": "0.70",
                "data_read_per_join": "32"
              } /* cost_info */,
              "used_columns": [
                "b"
              ] /* used_columns */,
              "materialized_from_subquery": {
                "using_temporary_table": true,
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "cost_info": {
                    "query_cost": "3.80"
                  } /* cost_info */,
                  "ordering_operation": {
                    "using_filesort": true,
                    "cost_info": {
                      "sort_cost": "3.00"
                    } /* cost_info */,
                    "table": {
                      "table_name": "t2",
                      "access_type": "ALL",
                      "rows_examined_per_scan": 3,
                      "rows_produced_per_join": 3,
                      "filtered": "100.00",
                      "cost_info": {
                        "read_cost": "0.50",
                        "eval_cost": "0.30",
                        "prefix_cost": "0.80",
                        "data_read_per_join": "24"
                      } /* cost_info */,
                      "used_columns": [
                        "b"
                      ] /* used_columns */
                    } /* table */
                  } /* ordering_operation */
                } /* query_block */
              } /* materialized_from_subquery */
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = 10 where <in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1            WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.80"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "ALL",
      "rows_examined_per_scan": 3,
      "rows_produced_per_join": 3,
      "filtered": "100.00",
      "cost_info": {
        "read_cost": "0.50",
        "eval_cost": "0.30",
        "prefix_cost": "0.80",
        "data_read_per_join": "24"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))",
      "attached_subqueries": [
        {
          "dependent": true,
          "cacheable": false,
          "query_block": {
            "select_id": 2,
            "cost_info": {
              "query_cost": "0.70"
            } /* cost_info */,
            "table": {
              "table_name": "x",
              "access_type": "index_subquery",
              "possible_keys": [
                "<auto_key0>"
              ] /* possible_keys */,
              "key": "<auto_key0>",
              "used_key_parts": [
                "b"
              ] /* used_key_parts */,
              "key_length": "5",
              "ref": [
                "func"
              ] /* ref */,
              "rows_examined_per_scan": 2,
              "rows_produced_per_join": 2,
              "filtered": "100.00",
              "using_index": true,
              "cost_info": {
                "read_cost": "0.50",
                "eval_cost": "0.20",
                "prefix_cost": "0.70",
                "data_read_per_join": "32"
              } /* cost_info */,
              "used_columns": [
                "b"
              ] /* used_columns */,
              "materialized_from_subquery": {
                "using_temporary_table": true,
                "dependent": false,
                "cacheable": true,
                "query_block": {
                  "select_id": 3,
                  "cost_info": {
                    "query_cost": "3.80"
                  } /* cost_info */,
                  "ordering_operation": {
                    "using_filesort": true,
                    "cost_info": {
                      "sort_cost": "3.00"
                    } /* cost_info */,
                    "table": {
                      "table_name": "t2",
                      "access_type": "ALL",
                      "rows_examined_per_scan": 3,
                      "rows_produced_per_join": 3,
                      "filtered": "100.00",
                      "cost_info": {
                        "read_cost": "0.50",
                        "eval_cost": "0.30",
                        "prefix_cost": "0.80",
                        "data_read_per_join": "24"
                      } /* cost_info */,
                      "used_columns": [
                        "b"
                      ] /* used_columns */
                    } /* table */
                  } /* ordering_operation */
                } /* query_block */
              } /* materialized_from_subquery */
            } /* table */
          } /* query_block */
        }
      ] /* attached_subqueries */
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_rnd_next	8
Handler_write	1
Sort_rows	3
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_rnd_next	8
Handler_update	1
Handler_write	1
Sort_rows	3
Sort_scan	1

#
# query:  UPDATE        t1, t2 SET a = 10 WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x)
# select: SELECT * FROM t1, t2            WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x)
#
EXPLAIN UPDATE        t1, t2 SET a = 10 WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
2	DEPENDENT SUBQUERY	<derived3>	NULL	index_subquery	<auto_key0>	<auto_key0>	5	func	2	100.00	Using index
3	DERIVED	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using filesort
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`a` = 10 where <in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1, t2 SET a = 10 WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.20"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))",
          "attached_subqueries": [
            {
              "dependent": true,
              "cacheable": false,
              "query_block": {
                "select_id": 2,
                "cost_info": {
                  "query_cost": "0.70"
                } /* cost_info */,
                "table": {
                  "table_name": "x",
                  "access_type": "index_subquery",
                  "possible_keys": [
                    "<auto_key0>"
                  ] /* possible_keys */,
                  "key": "<auto_key0>",
                  "used_key_parts": [
                    "b"
                  ] /* used_key_parts */,
                  "key_length": "5",
                  "ref": [
                    "func"
                  ] /* ref */,
                  "rows_examined_per_scan": 2,
                  "rows_produced_per_join": 2,
                  "filtered": "100.00",
                  "using_index": true,
                  "cost_info": {
                    "read_cost": "0.50",
                    "eval_cost": "0.20",
                    "prefix_cost": "0.70",
                    "data_read_per_join": "32"
                  } /* cost_info */,
                  "used_columns": [
                    "b"
                  ] /* used_columns */,
                  "materialized_from_subquery": {
                    "using_temporary_table": true,
                    "dependent": false,
                    "cacheable": true,
                    "query_block": {
                      "select_id": 3,
                      "cost_info": {
                        "query_cost": "3.80"
                      } /* cost_info */,
                      "ordering_operation": {
                        "using_filesort": true,
                        "cost_info": {
                          "sort_cost": "3.00"
                        } /* cost_info */,
                        "table": {
                          "table_name": "t2",
                          "access_type": "ALL",
                          "rows_examined_per_scan": 3,
                          "rows_produced_per_join": 3,
                          "filtered": "100.00",
                          "cost_info": {
                            "read_cost": "0.50",
                            "eval_cost": "0.30",
                            "prefix_cost": "0.80",
                            "data_read_per_join": "24"
                          } /* cost_info */,
                          "used_columns": [
                            "b"
                          ] /* used_columns */
                        } /* table */
                      } /* ordering_operation */
                    } /* query_block */
                  } /* materialized_from_subquery */
                } /* table */
              } /* query_block */
            }
          ] /* attached_subqueries */
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 9,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.90",
            "prefix_cost": "2.20",
            "data_read_per_join": "72"
          } /* cost_info */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`a` = 10 where <in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1, t2            WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.20"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))",
          "attached_subqueries": [
            {
              "dependent": true,
              "cacheable": false,
              "query_block": {
                "select_id": 2,
                "cost_info": {
                  "query_cost": "0.70"
                } /* cost_info */,
                "table": {
                  "table_name": "x",
                  "access_type": "index_subquery",
                  "possible_keys": [
                    "<auto_key0>"
                  ] /* possible_keys */,
                  "key": "<auto_key0>",
                  "used_key_parts": [
                    "b"
                  ] /* used_key_parts */,
                  "key_length": "5",
                  "ref": [
                    "func"
                  ] /* ref */,
                  "rows_examined_per_scan": 2,
                  "rows_produced_per_join": 2,
                  "filtered": "100.00",
                  "using_index": true,
                  "cost_info": {
                    "read_cost": "0.50",
                    "eval_cost": "0.20",
                    "prefix_cost": "0.70",
                    "data_read_per_join": "32"
                  } /* cost_info */,
                  "used_columns": [
                    "b"
                  ] /* used_columns */,
                  "materialized_from_subquery": {
                    "using_temporary_table": true,
                    "dependent": false,
                    "cacheable": true,
                    "query_block": {
                      "select_id": 3,
                      "cost_info": {
                        "query_cost": "3.80"
                      } /* cost_info */,
                      "ordering_operation": {
                        "using_filesort": true,
                        "cost_info": {
                          "sort_cost": "3.00"
                        } /* cost_info */,
                        "table": {
                          "table_name": "t2",
                          "access_type": "ALL",
                          "rows_examined_per_scan": 3,
                          "rows_produced_per_join": 3,
                          "filtered": "100.00",
                          "cost_info": {
                            "read_cost": "0.50",
                            "eval_cost": "0.30",
                            "prefix_cost": "0.80",
                            "data_read_per_join": "24"
                          } /* cost_info */,
                          "used_columns": [
                            "b"
                          ] /* used_columns */
                        } /* table */
                      } /* ordering_operation */
                    } /* query_block */
                  } /* materialized_from_subquery */
                } /* table */
              } /* query_block */
            }
          ] /* attached_subqueries */
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 9,
          "filtered": "100.00",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.90",
            "prefix_cost": "2.20",
            "data_read_per_join": "72"
          } /* cost_info */,
          "used_columns": [
            "b"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where <in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_rnd_next	8
Handler_write	1
Sort_rows	3
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_rnd_next	8
Handler_write	1
Sort_rows	3
Sort_scan	1

#
# query:  UPDATE        t1, (SELECT * FROM t2) y SET a = 10 WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x)
# select: SELECT * FROM t1, (SELECT * FROM t2) y            WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x)
#
EXPLAIN UPDATE        t1, (SELECT * FROM t2) y SET a = 10 WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
1	PRIMARY	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	NULL
3	DEPENDENT SUBQUERY	<derived4>	NULL	index_subquery	<auto_key0>	<auto_key0>	5	func	2	100.00	Using index
4	DERIVED	t2	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using filesort
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`a` = 10 where <in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE        t1, (SELECT * FROM t2) y SET a = 10 WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.20"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "update": true,
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))",
          "attached_subqueries": [
            {
              "dependent": true,
              "cacheable": false,
              "query_block": {
                "select_id": 3,
                "cost_info": {
                  "query_cost": "0.70"
                } /* cost_info */,
                "table": {
                  "table_name": "x",
                  "access_type": "index_subquery",
                  "possible_keys": [
                    "<auto_key0>"
                  ] /* possible_keys */,
                  "key": "<auto_key0>",
                  "used_key_parts": [
                    "b"
                  ] /* used_key_parts */,
                  "key_length": "5",
                  "ref": [
                    "func"
                  ] /* ref */,
                  "rows_examined_per_scan": 2,
                  "rows_produced_per_join": 2,
                  "filtered": "100.00",
                  "using_index": true,
                  "cost_info": {
                    "read_cost": "0.50",
                    "eval_cost": "0.20",
                    "prefix_cost": "0.70",
                    "data_read_per_join": "32"
                  } /* cost_info */,
                  "used_columns": [
                    "b"
                  ] /* used_columns */,
                  "materialized_from_subquery": {
                    "using_temporary_table": true,
                    "dependent": false,
                    "cacheable": true,
                    "query_block": {
                      "select_id": 4,
                      "cost_info": {
                        "query_cost": "3.80"
                      } /* cost_info */,
                      "ordering_operation": {
                        "using_filesort": true,
                        "cost_info": {
                          "sort_cost": "3.00"
                        } /* cost_info */,
                        "table": {
                          "table_name": "t2",
                          "access_type": "ALL",
                          "rows_examined_per_scan": 3,
                          "rows_produced_per_join": 3,
                          "filtered": "100.00",
                          "cost_info": {
                            "read_cost": "0.50",
                            "eval_cost": "0.30",
                            "prefix_cost": "0.80",
                            "data_read_per_join": "24"
                          } /* cost_info */,
                          "used_columns": [
                            "b"
                          ] /* used_columns */
                        } /* table */
                      } /* ordering_operation */
                    } /* query_block */
                  } /* materialized_from_subquery */
                } /* table */
              } /* query_block */
            }
          ] /* attached_subqueries */
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 9,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.90",
            "prefix_cost": "2.20",
            "data_read_per_join": "72"
          } /* cost_info */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`a` = 10 where <in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT * FROM t1, (SELECT * FROM t2) y            WHERE a IN (SELECT * FROM (SELECT b FROM t2 ORDER BY b LIMIT 2,2) x);;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "2.20"
    } /* cost_info */,
    "nested_loop": [
      {
        "table": {
          "table_name": "t1",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 3,
          "filtered": "100.00",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.30",
            "prefix_cost": "0.80",
            "data_read_per_join": "24"
          } /* cost_info */,
          "used_columns": [
            "a"
          ] /* used_columns */,
          "attached_condition": "<in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))",
          "attached_subqueries": [
            {
              "dependent": true,
              "cacheable": false,
              "query_block": {
                "select_id": 3,
                "cost_info": {
                  "query_cost": "0.70"
                } /* cost_info */,
                "table": {
                  "table_name": "x",
                  "access_type": "index_subquery",
                  "possible_keys": [
                    "<auto_key0>"
                  ] /* possible_keys */,
                  "key": "<auto_key0>",
                  "used_key_parts": [
                    "b"
                  ] /* used_key_parts */,
                  "key_length": "5",
                  "ref": [
                    "func"
                  ] /* ref */,
                  "rows_examined_per_scan": 2,
                  "rows_produced_per_join": 2,
                  "filtered": "100.00",
                  "using_index": true,
                  "cost_info": {
                    "read_cost": "0.50",
                    "eval_cost": "0.20",
                    "prefix_cost": "0.70",
                    "data_read_per_join": "32"
                  } /* cost_info */,
                  "used_columns": [
                    "b"
                  ] /* used_columns */,
                  "materialized_from_subquery": {
                    "using_temporary_table": true,
                    "dependent": false,
                    "cacheable": true,
                    "query_block": {
                      "select_id": 4,
                      "cost_info": {
                        "query_cost": "3.80"
                      } /* cost_info */,
                      "ordering_operation": {
                        "using_filesort": true,
                        "cost_info": {
                          "sort_cost": "3.00"
                        } /* cost_info */,
                        "table": {
                          "table_name": "t2",
                          "access_type": "ALL",
                          "rows_examined_per_scan": 3,
                          "rows_produced_per_join": 3,
                          "filtered": "100.00",
                          "cost_info": {
                            "read_cost": "0.50",
                            "eval_cost": "0.30",
                            "prefix_cost": "0.80",
                            "data_read_per_join": "24"
                          } /* cost_info */,
                          "used_columns": [
                            "b"
                          ] /* used_columns */
                        } /* table */
                      } /* ordering_operation */
                    } /* query_block */
                  } /* materialized_from_subquery */
                } /* table */
              } /* query_block */
            }
          ] /* attached_subqueries */
        } /* table */
      },
      {
        "table": {
          "table_name": "t2",
          "access_type": "ALL",
          "rows_examined_per_scan": 3,
          "rows_produced_per_join": 9,
          "filtered": "100.00",
          "using_join_buffer": "hash join",
          "cost_info": {
            "read_cost": "0.50",
            "eval_cost": "0.90",
            "prefix_cost": "2.20",
            "data_read_per_join": "72"
          } /* cost_info */,
          "used_columns": [
            "b"
          ] /* used_columns */
        } /* table */
      }
    ] /* nested_loop */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `a`,`test`.`t2`.`b` AS `b` from `test`.`t1` join `test`.`t2` where <in_optimizer>(`test`.`t1`.`a`,<exists>(<index_lookup>(<cache>(`test`.`t1`.`a`) in x on <auto_key0>)))
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_rnd_next	8
Handler_write	1
Sort_rows	3
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	5
Handler_read_rnd_next	8
Handler_write	1
Sort_rows	3
Sort_scan	1

DROP TABLE t1,t2;
#70
CREATE TABLE t1 (c1 INT KEY);
CREATE TABLE t2 (c2 INT);
CREATE TABLE t3 (c3 INT);
EXPLAIN UPDATE t3 SET c3 = (
SELECT COUNT(d1.c1)
FROM (
SELECT a11.c1 FROM t1 AS a11 
STRAIGHT_JOIN t2 AS a21 ON a21.c2 = a11.c1
JOIN t1 AS a12 ON a12.c1 = a11.c1
) d1
);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t3	NULL	ALL	NULL	NULL	NULL	NULL	0	100.00	NULL
2	SUBQUERY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
3	DERIVED	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
Warnings:
Note	1003	update `test`.`t3` set `test`.`t3`.`c3` = (/* select#2 */ select count(NULL) from (/* select#3 */ select NULL AS `c1` from `test`.`t1` `a11` straight_join `test`.`t2` `a21` join `test`.`t1` `a12` where multiple equal(NULL, NULL, NULL)) `d1`)
DROP TABLE t1, t2, t3;
#71
CREATE TABLE t1 (c1 INT NOT NULL, c2 INT NOT NULL, INDEX i1(c1));
INSERT INTO t1 VALUES (1,0),(2,0),(3,0),(4,0),(5,0),(6,0),(7,0),(8,0);
CREATE TABLE t2 LIKE t1;
EXPLAIN INSERT INTO t2 SELECT * FROM t1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t2	NULL	ALL	NULL	NULL	NULL	NULL	X	NULL	NULL
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	X	100.00	NULL
Warnings:
Note	1003	insert into `test`.`t2` /* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2` from `test`.`t1`
INSERT INTO t2 SELECT * FROM t1;
SELECT * FROM t1 LEFT JOIN t2 ON t1.c1 = t2.c1;
DROP TABLE t1, t2;
#73
CREATE TABLE t1 (id INT);
CREATE TABLE t2 (id INT);
INSERT INTO t1 VALUES (1), (2);
EXPLAIN SELECT * FROM t1 LEFT JOIN t2 USING(id) GROUP BY t1.id;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	system	NULL	NULL	NULL	NULL	0	0.00	const row not found
1	SIMPLE	t1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	Using temporary
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`id` AS `id` from `test`.`t1` where true group by `test`.`t1`.`id`
DROP TABLE t1,t2;
#74
CREATE TABLE t1(a INT PRIMARY KEY) ENGINE=InnoDB;
INSERT INTO t1 VALUES (1), (2), (3), (4), (5);
# used key is modified & Using temporary
#
# query:  UPDATE t1 SET a=a+1 WHERE a>10
# select: SELECT a t1 FROM t1 WHERE a>10
#
EXPLAIN UPDATE t1 SET a=a+1 WHERE a>10;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	range	PRIMARY	PRIMARY	4	const	1	100.00	Using where; Using temporary
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = (`test`.`t1`.`a` + 1) where (`test`.`t1`.`a` > 10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t1 SET a=a+1 WHERE a>10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "table": {
      "update": true,
      "table_name": "t1",
      "access_type": "range",
      "possible_keys": [
        "PRIMARY"
      ] /* possible_keys */,
      "key": "PRIMARY",
      "used_key_parts": [
        "a"
      ] /* used_key_parts */,
      "key_length": "4",
      "ref": [
        "const"
      ] /* ref */,
      "rows_examined_per_scan": 1,
      "filtered": "100.00",
      "using_temporary_table": "for update",
      "attached_condition": "(`test`.`t1`.`a` > 10)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = (`test`.`t1`.`a` + 1) where (`test`.`t1`.`a` > 10)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT a t1 FROM t1 WHERE a>10;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "0.46"
    } /* cost_info */,
    "table": {
      "table_name": "t1",
      "access_type": "range",
      "possible_keys": [
        "PRIMARY"
      ] /* possible_keys */,
      "key": "PRIMARY",
      "used_key_parts": [
        "a"
      ] /* used_key_parts */,
      "key_length": "4",
      "rows_examined_per_scan": 1,
      "rows_produced_per_join": 1,
      "filtered": "100.00",
      "using_index": true,
      "cost_info": {
        "read_cost": "0.36",
        "eval_cost": "0.10",
        "prefix_cost": "0.46",
        "data_read_per_join": "8"
      } /* cost_info */,
      "used_columns": [
        "a"
      ] /* used_columns */,
      "attached_condition": "(`test`.`t1`.`a` > 10)"
    } /* table */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `t1` from `test`.`t1` where (`test`.`t1`.`a` > 10)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2

# used key is modified & Using filesort
#
# query:  UPDATE t1 SET a=a+1 WHERE a>10 ORDER BY a+20
# select: SELECT a t1 FROM t1 WHERE a>10 ORDER BY a+20
#
EXPLAIN UPDATE t1 SET a=a+1 WHERE a>10 ORDER BY a+20;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	range	PRIMARY	PRIMARY	4	const	1	100.00	Using where; Using filesort
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = (`test`.`t1`.`a` + 1) where (`test`.`t1`.`a` > 10) order by (`test`.`t1`.`a` + 20)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON UPDATE t1 SET a=a+1 WHERE a>10 ORDER BY a+20;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "ordering_operation": {
      "using_filesort": true,
      "table": {
        "update": true,
        "table_name": "t1",
        "access_type": "range",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "a"
        ] /* used_key_parts */,
        "key_length": "4",
        "ref": [
          "const"
        ] /* ref */,
        "rows_examined_per_scan": 1,
        "filtered": "100.00",
        "attached_condition": "(`test`.`t1`.`a` > 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`a` = (`test`.`t1`.`a` + 1) where (`test`.`t1`.`a` > 10) order by (`test`.`t1`.`a` + 20)
FLUSH STATUS;
FLUSH TABLES;
Variable_name	Value
EXPLAIN FORMAT=JSON SELECT a t1 FROM t1 WHERE a>10 ORDER BY a+20;;
EXPLAIN
{
  "query_block": {
    "select_id": 1,
    "cost_info": {
      "query_cost": "1.46"
    } /* cost_info */,
    "ordering_operation": {
      "using_filesort": true,
      "cost_info": {
        "sort_cost": "1.00"
      } /* cost_info */,
      "table": {
        "table_name": "t1",
        "access_type": "range",
        "possible_keys": [
          "PRIMARY"
        ] /* possible_keys */,
        "key": "PRIMARY",
        "used_key_parts": [
          "a"
        ] /* used_key_parts */,
        "key_length": "4",
        "rows_examined_per_scan": 1,
        "rows_produced_per_join": 1,
        "filtered": "100.00",
        "using_index": true,
        "cost_info": {
          "read_cost": "0.36",
          "eval_cost": "0.10",
          "prefix_cost": "0.46",
          "data_read_per_join": "8"
        } /* cost_info */,
        "used_columns": [
          "a"
        ] /* used_columns */,
        "attached_condition": "(`test`.`t1`.`a` > 10)"
      } /* table */
    } /* ordering_operation */
  } /* query_block */
}
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`a` AS `t1` from `test`.`t1` where (`test`.`t1`.`a` > 10) order by (`test`.`t1`.`a` + 20)
# This line exists only to help --skip_if_hypergraph diffing.
# Status of "equivalent" SELECT query execution:
Variable_name	Value
Handler_read_key	2
Sort_scan	1
# This line exists only to help --skip_if_hypergraph diffing.
# Status of testing query execution:
Variable_name	Value
Handler_read_key	2
Sort_scan	1

DROP TABLE t1;
#
# Bug #12949629: CLIENT LOSES CONNECTION AFTER EXECUTING A PROCEDURE WITH
#                EXPLAIN UPDATE/DEL/INS
#
CREATE TABLE t1 (i INT);
CREATE TABLE t2 (i INT);
CREATE PROCEDURE p1() BEGIN EXPLAIN INSERT INTO t1 VALUES (1);END|
CREATE PROCEDURE p2() BEGIN         INSERT INTO t1 VALUES (1);END|
CREATE PROCEDURE p3() BEGIN EXPLAIN INSERT INTO t1 SELECT 1;END|
CREATE PROCEDURE p4() BEGIN         INSERT INTO t1 SELECT 1;END|
CREATE PROCEDURE p5() BEGIN EXPLAIN REPLACE INTO t1 VALUES (1);END|
CREATE PROCEDURE p6() BEGIN         REPLACE INTO t1 VALUES (1);END|
CREATE PROCEDURE p7() BEGIN EXPLAIN REPLACE INTO t1 SELECT 1;END|
CREATE PROCEDURE p8() BEGIN         REPLACE INTO t1 SELECT 1;END|
CREATE PROCEDURE p9() BEGIN EXPLAIN UPDATE t1 SET i = 10;END|
CREATE PROCEDURE p10() BEGIN         UPDATE t1 SET i = 10;END|
CREATE PROCEDURE p11() BEGIN EXPLAIN UPDATE t1,t2 SET t1.i = 10 WHERE t1.i = t2.i ;END|
CREATE PROCEDURE p12() BEGIN         UPDATE t1,t2 SET t1.i = 10 WHERE t1.i = t2.i ;END|
CREATE PROCEDURE p13() BEGIN
# Disabling binary logging temporarily as optimized delete is not used when log-bin is enabled in row mode
SET @save_log_bin= @@sql_log_bin;SET SQL_LOG_BIN=0;
EXPLAIN DELETE FROM t1;
SET SQL_LOG_BIN=@save_log_bin; END|
CREATE PROCEDURE p14() BEGIN         DELETE FROM t1;END|
CREATE PROCEDURE p15() BEGIN EXPLAIN DELETE FROM t1 USING t1;END|
CREATE PROCEDURE p16() BEGIN         DELETE FROM t1 USING t1;END|
CALL p16();
DROP PROCEDURE p16;
CALL p15();
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
Warnings:
Note	1003	delete `test`.`t1` from `test`.`t1`
DROP PROCEDURE p15;
CALL p14();
DROP PROCEDURE p14;
CALL p13();
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	DELETE	t1	NULL	ALL	NULL	NULL	NULL	NULL	0	100.00	Deleting all rows
DROP PROCEDURE p13;
CALL p12();
DROP PROCEDURE p12;
CALL p11();
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	no matching row in const table
Warnings:
Note	1003	update `test`.`t1` join `test`.`t2` set `test`.`t1`.`i` = 10 where multiple equal(`test`.`t1`.`i`, `test`.`t2`.`i`)
DROP PROCEDURE p11;
CALL p10();
DROP PROCEDURE p10;
CALL p9();
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	UPDATE	t1	NULL	ALL	NULL	NULL	NULL	NULL	0	100.00	NULL
Warnings:
Note	1003	update `test`.`t1` set `test`.`t1`.`i` = 10
DROP PROCEDURE p9;
CALL p8();
DROP PROCEDURE p8;
CALL p7();
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	REPLACE	t1	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	replace into `test`.`t1` /* select#1 */ select 1 AS `1` from dual
DROP PROCEDURE p7;
CALL p6();
DROP PROCEDURE p6;
CALL p5();
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	REPLACE	t1	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	replace into `test`.`t1` values (1)
DROP PROCEDURE p5;
CALL p4();
DROP PROCEDURE p4;
CALL p3();
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` /* select#1 */ select 1 AS `1` from dual
DROP PROCEDURE p3;
CALL p2();
DROP PROCEDURE p2;
CALL p1();
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	INSERT	t1	NULL	ALL	NULL	NULL	NULL	NULL	NULL	NULL	NULL
Warnings:
Note	1003	insert into `test`.`t1` values (1)
DROP PROCEDURE p1;
DROP TABLE t1, t2;
#
set default_storage_engine= @save_storage_engine;
set optimizer_switch=default;
