SET @number_of_srss =
(SELECT COUNT(*) FROM INFORMATION_SCHEMA.ST_SPATIAL_REFERENCE_SYSTEMS);
SELECT
ST_DISTANCE(
ST_GEOMFROMTEXT('POINT(0 0)', srs_id),
ST_GEOMFROMTEXT('POINT(0 0)', srs_id)
)
FROM information_schema.ST_SPATIAL_REFERENCE_SYSTEMS;
SELECT * FROM information_schema.ST_SPATIAL_REFERENCE_SYSTEMS WHERE srs_id=4326;
SRS_NAME	SRS_ID	ORGANIZATION	ORGANIZATION_COORDSYS_ID	DEFINITION	DESCRIPTION
WGS 84	4326	EPSG	4326	GEOGCS["WGS 84",D<PERSON>UM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],<PERSON><PERSON>["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]	NULL
CREATE USER srs_test@localhost;
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
# CREATE without SUPER or CREATE_SPATIAL_REFERENCE_SYSTEM (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000001
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR HY000: You need the SUPER or CREATE_SPATIAL_REFERENCE_SYSTEM privilege for command 'CREATE SPATIAL REFERENCE SYSTEM'
# CREATE OR REPLACE on existing SRS without SUPER or CREATE_SPATIAL_REFERENCE_SYSTEM (should raise error).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR HY000: You need the SUPER or CREATE_SPATIAL_REFERENCE_SYSTEM privilege for command 'CREATE OR REPLACE SPATIAL REFERENCE SYSTEM'
# CREATE SRS IF NOT EXISTS on existing SRS without SUPER or CREATE_SPATIAL_REFERENCE_SYSTEM (should raise error).
CREATE SPATIAL REFERENCE SYSTEM IF NOT EXISTS 1000000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR HY000: You need the SUPER or CREATE_SPATIAL_REFERENCE_SYSTEM privilege for command 'CREATE SPATIAL REFERENCE SYSTEM'
# DROP on existing SRS without SUPER or CREATE_SPATIAL_REFERENCE_SYSTEM (should raise error).
DROP SPATIAL REFERENCE SYSTEM 1000000000;
ERROR HY000: You need the SUPER or CREATE_SPATIAL_REFERENCE_SYSTEM privilege for command 'DROP SPATIAL REFERENCE SYSTEM'
# DROP on non-existing SRS without SUPER or CREATE_SPATIAL_REFERENCE_SYSTEM (should raise error).
DROP SPATIAL REFERENCE SYSTEM 1000000000;
ERROR HY000: You need the SUPER or CREATE_SPATIAL_REFERENCE_SYSTEM privilege for command 'DROP SPATIAL REFERENCE SYSTEM'
GRANT CREATE_SPATIAL_REFERENCE_SYSTEM ON *.* TO 'srs_test'@'localhost';
# CREATE with CREATE_SPATIAL_REFERENCE_SYSTEM privilege (should pass).
CREATE SPATIAL REFERENCE SYSTEM 33000 NAME 'MAGNA-SIRGAS 2018' ORGANIZATION 'EPSG' IDENTIFIED BY 33000 DEFINITION 'PROJCS["MAGNA-SIRGAS 2018 / Origen-Nacional",GEOGCS["MAGNA-SIRGAS 2018",DATUM["Marco_Geocentrico_Nacional_de_Referencia_2018",SPHEROID["GRS 1980",6378137,298.257222101],TOWGS84[0,0,0,0,0,0,0]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","20046"]],PROJECTION["Transverse_Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["latitude_of_origin",4],PARAMETER["central_meridian",-73],PARAMETER["scale_factor",0.9992],PARAMETER["false_easting",5000000],PARAMETER["false_northing",2000000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AUTHORITY["EPSG","33000"]]';
# CREATE OR REPLACE with CREATE_SPATIAL_REFERENCE_SYSTEM privilege (should pass).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 33000 NAME 'MAGNA-SIRGAS 2018' ORGANIZATION 'EPSG' IDENTIFIED BY 33000 DEFINITION 'PROJCS["MAGNA-SIRGAS 2018 / Origen-Nacional",GEOGCS["MAGNA-SIRGAS 2018",DATUM["Marco_Geocentrico_Nacional_de_Referencia_2018",SPHEROID["GRS 1980",6378137,298.257222101],TOWGS84[0,0,0,0,0,0,0]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","20046"]],PROJECTION["Transverse_Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["latitude_of_origin",4],PARAMETER["central_meridian",-73],PARAMETER["scale_factor",0.9992],PARAMETER["false_easting",5000000],PARAMETER["false_northing",2000000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AUTHORITY["EPSG","33000"]]';
# DROP with CREATE_SPATIAL_REFERENCE_SYSTEM privilege (should pass).
DROP SPATIAL REFERENCE SYSTEM 33000;
# CREATE IF NOT EXISTS with CREATE_SPATIAL_REFERENCE_SYSTEM privilege (should pass).
CREATE SPATIAL REFERENCE SYSTEM IF NOT EXISTS 33000 NAME 'MAGNA-SIRGAS 2018' ORGANIZATION 'EPSG' IDENTIFIED BY 33000 DEFINITION 'PROJCS["MAGNA-SIRGAS 2018 / Origen-Nacional",GEOGCS["MAGNA-SIRGAS 2018",DATUM["Marco_Geocentrico_Nacional_de_Referencia_2018",SPHEROID["GRS 1980",6378137,298.257222101],TOWGS84[0,0,0,0,0,0,0]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","20046"]],PROJECTION["Transverse_Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["latitude_of_origin",4],PARAMETER["central_meridian",-73],PARAMETER["scale_factor",0.9992],PARAMETER["false_easting",5000000],PARAMETER["false_northing",2000000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AUTHORITY["EPSG","33000"]]';
DROP SPATIAL REFERENCE SYSTEM 33000;
DROP SPATIAL REFERENCE SYSTEM 1000000000;
DROP USER srs_test@localhost;
# CREATE OR REPLACE on SRID 0 (should raise error).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 0
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR000: SRID 0 is not modifiable.
# CREATE with negative SRID (should raise error).
CREATE SPATIAL REFERENCE SYSTEM -1
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '-1
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPH' at line 1
# CREATE with too large SRID (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 5000000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR 22003: SRID value is out of range in 'CREATE SPATIAL REFERENCE SYSTEM'
# Without the NAME parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: Missing mandatory attribute NAME.
# Without the DEFINITION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo';
ERROR SR006: Missing mandatory attribute DEFINITION.
# Repeated NAME parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo' NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: Multiple definitions of attribute NAME.
# Repeated DEFINITION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: Multiple definitions of attribute DEFINITION.
# Repeated ORGANIZATION ... IDENTIFIED BY parameters (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: Multiple definitions of attribute ORGANIZATION.
# Repeated DESCRIPTION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]'
DESCRIPTION ''
DESCRIPTION '';
ERROR SR006: Multiple definitions of attribute DESCRIPTION.
# Empty NAME parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME ''
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: The spatial reference system name can't be an empty string or start or end with whitespace.
# Leading whitespace in NAME parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME ' foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: The spatial reference system name can't be an empty string or start or end with whitespace.
# Trailing whitespace in NAME parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo '
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: The spatial reference system name can't be an empty string or start or end with whitespace.
# Control character in NAME parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo\0'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: Invalid character in attribute NAME.
# Too long NAME parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME '123456789012345678901234567890123456789012345678901234567890123456789012345678901'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: Attribute NAME is too long. The maximum length is 80 characters.
# Empty ORGANIZATION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION '' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: The organization name can't be an empty string or start or end with whitespace.
# Leading whitespace in ORGANIZATION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION ' foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: The organization name can't be an empty string or start or end with whitespace.
# Trailing whitespace in ORGANIZATION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo ' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: The organization name can't be an empty string or start or end with whitespace.
# Control character in ORGANIZATION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo\0' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: Invalid character in attribute ORGANIZATION.
# Too long ORGANIZATION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION '12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: Attribute ORGANIZATION is too long. The maximum length is 256 characters.
# Negative organization SRS ID (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY -1
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '-1
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS ' at line 3
# Too large organization SRS ID (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 5000000000
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR 22003: IDENTIFIED BY value is out of range in 'CREATE SPATIAL REFERENCE SYSTEM'
# Control character in DEFINITION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
DEFINITION '\0GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR006: Invalid character in attribute DEFINITION.
# Too long DEFINITION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
DEFINITION '12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567';
ERROR SR006: Attribute DEFINITION is too long. The maximum length is 4096 characters.
# Too long DESCRIPTION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]'
DESCRIPTION '123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789';
ERROR SR006: Attribute DESCRIPTION is too long. The maximum length is 2048 characters.
# Invalid DEFINITION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
DEFINITION 'foo';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1000000000.
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 4326
NAME 'foo'
DEFINITION 'foo';
ERROR SR002: Can't parse the spatial reference system definition of SRID 4326.
# Control character in DESCRIPTION parameter (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]'
DESCRIPTION 'foo\0';
ERROR SR006: Invalid character in attribute DESCRIPTION.
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]'
DESCRIPTION 'foo';
# CREATE without IF NOT EXISTS on existing SRID (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foobar'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR004: There is already a spatial reference system with SRID 1000000000.
# CREATE IF NOT EXISTS on existing SRID (should raise warning).
CREATE SPATIAL REFERENCE SYSTEM IF NOT EXISTS 1000000000
NAME 'foobar'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3713	There is already a spatial reference system with SRID 1000000000.
# CREATE OR REPLACE on existing SRID (should pass).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
# CREATE OR REPLACE on SRID that is in use ...
CREATE TABLE t1 (g GEOMETRY SRID 1000000000);
# ... with the exact same definition (should succeed).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
# ... with changes that don't affect computations (should succeed).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["WGS 84",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
# ... with a definition with a conflicting semi-major axis (should raise error)
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",1,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR005: Can't modify SRID 1000000000. There is at least one column depending on it.
# ... with a definition with a conflicting inverse flattening (should raise error)
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,300,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR005: Can't modify SRID 1000000000. There is at least one column depending on it.
# ... with a definition adding an all-zero TOWGS84 clause to WGS 84 (should succeed)
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
# ... with a definition removing an all-zero TOWGS84 clause from WGS 84 (should succeed)
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
# ... with a definition adding a non-zero TOWGS84 clause to WGS 84 (should raise error)
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[1,1,1,1,1,1,1],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR005: Can't modify SRID 1000000000. There is at least one column depending on it.
# ... with a definition with a conflicting prime meridian (should raise error)
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",1,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR005: Can't modify SRID 1000000000. There is at least one column depending on it.
# ... with a definition with a conflicting unit (should raise error)
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",1,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR005: Can't modify SRID 1000000000. There is at least one column depending on it.
# ... with a definition with a conflicting first axis (should raise error)
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",SOUTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR005: Can't modify SRID 1000000000. There is at least one column depending on it.
# ... with a definition with a conflicting second axis (should raise error)
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",WEST],AUTHORITY["EPSG","4326"]]';
ERROR SR005: Can't modify SRID 1000000000. There is at least one column depending on it.
# ... with a definition that changes the property of being WGS 84 (should raise error)
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",WEST]]';
ERROR SR005: Can't modify SRID 1000000000. There is at least one column depending on it.
DROP TABLE t1;
# CREATE OR REPLACE to add TOWGS84 clause to non-WGS 84 SRS (should succeed).
CREATE SPATIAL REFERENCE SYSTEM 1000000001
NAME 'without_towgs84'
DEFINITION 'GEOGCS["foo",DATUM["foo",SPHEROID["foo",6378137,298.*********]],PRIMEM["Greenwich",0],UNIT["degree",0.017453292519943278],AXIS["Lat",NORTH],AXIS["Lon",EAST]]';
CREATE TABLE t1 (g GEOMETRY SRID 1000000001);
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000001
NAME 'with_towgs84'
DEFINITION 'GEOGCS["foo",DATUM["foo",SPHEROID["foo",6378137,298.*********],TOWGS84[0,0,0,0,0,0,0]],PRIMEM["Greenwich",0],UNIT["degree",0.017453292519943278],AXIS["Lat",NORTH],AXIS["Lon",EAST]]';
# CREATE OR REPLACE to modify TOWGS84 clause (should raise error).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000001
NAME 'with_towgs84'
DEFINITION 'GEOGCS["foo",DATUM["foo",SPHEROID["foo",6378137,298.*********],TOWGS84[1,1,1,1,1,1,1]],PRIMEM["Greenwich",0],UNIT["degree",0.017453292519943278],AXIS["Lat",NORTH],AXIS["Lon",EAST]]';
ERROR SR005: Can't modify SRID 1000000001. There is at least one column depending on it.
DROP TABLE t1;
DROP SPATIAL REFERENCE SYSTEM 1000000001;
# CREATE with colliding NAME (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000001
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR 23000: Duplicate entry '1-foo' for key 'st_spatial_reference_systems.SRS_NAME'
# CREATE with colliding NAME in different case (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000001
NAME 'FOO'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR 23000: Duplicate entry '1-FOO' for key 'st_spatial_reference_systems.SRS_NAME'
# CREATE with colliding ORGANIZATION ... IDENTIFIED BY (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000001
NAME 'foobar'
ORGANIZATION 'foo' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR 23000: Duplicate entry '1-foo-0' for key 'st_spatial_reference_systems.ORGANIZATION_AND_ID'
# CREATE with colliding ORGANIZATION ... IDENTIFIED BY in different case (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000001
NAME 'foobar'
ORGANIZATION 'FOO' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR 23000: Duplicate entry '1-FOO-0' for key 'st_spatial_reference_systems.ORGANIZATION_AND_ID'
# CREATE OR REPLACE with collision (in this case: NAME) (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000001
NAME 'foobar'
ORGANIZATION 'foobar' IDENTIFIED BY 0
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1000000001
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
ERROR 23000: Duplicate entry '1-foo' for key 'st_spatial_reference_systems.SRS_NAME'
DROP SPATIAL REFERENCE SYSTEM 1000000001;
DROP SPATIAL REFERENCE SYSTEM 1000000000;
# CREATE SRS in reserved region [0, 32767] (should raise warning).
CREATE SPATIAL REFERENCE SYSTEM 1
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3715	The SRID range [0, 32767] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# (CREATE OR) REPLACE SRS in reserved region [0, 32767] (should raise warning).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 1
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3715	The SRID range [0, 32767] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# CREATE SRS IF NOT EXISTS on existing SRS in reserved region [0, 32767] (should pass without warning).
CREATE SPATIAL REFERENCE SYSTEM IF NOT EXISTS 1
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3713	There is already a spatial reference system with SRID 1.
# DROP SRS in reserved region [0, 32767] (should raise warning).
DROP SPATIAL REFERENCE SYSTEM 1;
Warnings:
Warning	3715	The SRID range [0, 32767] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# CREATE SRS in reserved region [0, 32767] (should raise warning).
CREATE SPATIAL REFERENCE SYSTEM 32767
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3715	The SRID range [0, 32767] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# (CREATE OR) REPLACE SRS in reserved region [0, 32767] (should raise warning).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 32767
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3715	The SRID range [0, 32767] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# CREATE SRS IF NOT EXISTS on existing SRS in reserved region [0, 32767] (should pass without warning).
CREATE SPATIAL REFERENCE SYSTEM IF NOT EXISTS 32767
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3713	There is already a spatial reference system with SRID 32767.
# DROP SRS in reserved region [0, 32767] (should raise warning).
DROP SPATIAL REFERENCE SYSTEM 32767;
Warnings:
Warning	3715	The SRID range [0, 32767] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# CREATE SRS in reserved region [60000000, 69999999] (should raise warning).
CREATE SPATIAL REFERENCE SYSTEM 60000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3715	The SRID range [60000000, 69999999] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# (CREATE OR) REPLACE SRS in reserved region [60000000, 69999999] (should raise warning).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 60000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3715	The SRID range [60000000, 69999999] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# CREATE SRS IF NOT EXISTS on existing SRS in reserved region [60000000, 69999999] (should pass without warning).
CREATE SPATIAL REFERENCE SYSTEM IF NOT EXISTS 60000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3713	There is already a spatial reference system with SRID 60000000.
# DROP SRS in reserved region [60000000, 69999999] (should raise warning).
DROP SPATIAL REFERENCE SYSTEM 60000000;
Warnings:
Warning	3715	The SRID range [60000000, 69999999] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# CREATE SRS in reserved region [60000000, 69999999] (should raise warning).
CREATE SPATIAL REFERENCE SYSTEM 69999999
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3715	The SRID range [60000000, 69999999] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# (CREATE OR) REPLACE SRS in reserved region [60000000, 69999999] (should raise warning).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 69999999
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3715	The SRID range [60000000, 69999999] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# CREATE SRS IF NOT EXISTS on existing SRS in reserved region [60000000, 69999999] (should pass without warning).
CREATE SPATIAL REFERENCE SYSTEM IF NOT EXISTS 69999999
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3713	There is already a spatial reference system with SRID 69999999.
# DROP SRS in reserved region [60000000, 69999999] (should raise warning).
DROP SPATIAL REFERENCE SYSTEM 69999999;
Warnings:
Warning	3715	The SRID range [60000000, 69999999] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# CREATE SRS in reserved region [2000000000, 2147483647] (should raise warning).
CREATE SPATIAL REFERENCE SYSTEM 2000000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3715	The SRID range [2000000000, 2147483647] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# (CREATE OR) REPLACE SRS in reserved region [2000000000, 2147483647] (should raise warning).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 2000000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3715	The SRID range [2000000000, 2147483647] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# CREATE SRS IF NOT EXISTS on existing SRS in reserved region [2000000000, 2147483647] (should pass without warning).
CREATE SPATIAL REFERENCE SYSTEM IF NOT EXISTS 2000000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3713	There is already a spatial reference system with SRID 2000000000.
# DROP SRS in reserved region [2000000000, 2147483647] (should raise warning).
DROP SPATIAL REFERENCE SYSTEM 2000000000;
Warnings:
Warning	3715	The SRID range [2000000000, 2147483647] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# CREATE SRS in reserved region [2000000000, 2147483647] (should raise warning).
CREATE SPATIAL REFERENCE SYSTEM 2147483647
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3715	The SRID range [2000000000, 2147483647] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# (CREATE OR) REPLACE SRS in reserved region [2000000000, 2147483647] (should raise warning).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 2147483647
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3715	The SRID range [2000000000, 2147483647] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# CREATE SRS IF NOT EXISTS on existing SRS in reserved region [2000000000, 2147483647] (should pass without warning).
CREATE SPATIAL REFERENCE SYSTEM IF NOT EXISTS 2147483647
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
Warnings:
Warning	3713	There is already a spatial reference system with SRID 2147483647.
# DROP SRS in reserved region [2000000000, 2147483647] (should raise warning).
DROP SPATIAL REFERENCE SYSTEM 2147483647;
Warnings:
Warning	3715	The SRID range [2000000000, 2147483647] has been reserved for system use. SRSs in this range may be added, modified or removed without warning during upgrade.
# DROP on SRID 0 (should raise error).
DROP SPATIAL REFERENCE SYSTEM 0;
ERROR SR000: SRID 0 is not modifiable.
# DROP on negative SRID (should raise error).
DROP SPATIAL REFERENCE SYSTEM -1;
ERROR 42000: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '-1' at line 1
# DROP on too large SRID (should raise error).
DROP SPATIAL REFERENCE SYSTEM 5000000000;
ERROR 22003: SRID value is out of range in 'DROP SPATIAL REFERENCE SYSTEM'
# DROP IF EXISTS on non-existing SRS (should raise warning).
DROP SPATIAL REFERENCE SYSTEM IF EXISTS 1000000000;
Warnings:
Warning	3519	There's no spatial reference system with SRID 1000000000.
# DROP on non-existing SRS (should raise error).
DROP SPATIAL REFERENCE SYSTEM 1000000000;
ERROR SR001: There's no spatial reference system with SRID 1000000000.
# DROP on SRS that is in use (should raise error).
CREATE SPATIAL REFERENCE SYSTEM 1000000000
NAME 'foo'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]]';
CREATE TABLE t1 (g GEOMETRY SRID 1000000000);
DROP SPATIAL REFERENCE SYSTEM IF EXISTS 1000000000;
ERROR SR005: Can't modify SRID 1000000000. There is at least one column depending on it.
DROP SPATIAL REFERENCE SYSTEM 1000000000;
ERROR SR005: Can't modify SRID 1000000000. There is at least one column depending on it.
DROP TABLE t1;
DROP SPATIAL REFERENCE SYSTEM 1000000000;
SELECT COUNT(*) AS should_be_zero FROM information_schema.ST_SPATIAL_REFERENCE_SYSTEMS
WHERE srs_id >= 1000000 AND srs_id < 2000000;
should_be_zero
0
#
# Projection: Popular Visualisation Pseudo Mercator (EPSG 1024).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1000000 NAME 'TEST1000000 WGS 84 / Pseudo-Mercator' DEFINITION 'PROJCS["WGS 84 / Pseudo-Mercator",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Popular Visualisation Pseudo Mercator",AUTHORITY["EPSG","1024"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3857"]]';
DROP SPATIAL REFERENCE SYSTEM 1000000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1000001 NAME 'TEST1000001 WGS 84 / Pseudo-Mercator' DEFINITION 'PROJCS["WGS 84 / Pseudo-Mercator",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Popular Visualisation Pseudo Mercator",AUTHORITY["EPSG","1024"]],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",0],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3857"]]';
DROP SPATIAL REFERENCE SYSTEM 1000001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1000002 NAME 'TEST1000002 WGS 84 / Pseudo-Mercator' DEFINITION 'PROJCS["WGS 84 / Pseudo-Mercator",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Popular Visualisation Pseudo Mercator",AUTHORITY["EPSG","1024"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3857"]]';
ERROR SR003: The spatial reference system definition for SRID 1000002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1000003 NAME 'TEST1000003 WGS 84 / Pseudo-Mercator' DEFINITION 'PROJCS["WGS 84 / Pseudo-Mercator",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Popular Visualisation Pseudo Mercator",AUTHORITY["EPSG","1024"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3857"]]';
ERROR SR003: The spatial reference system definition for SRID 1000003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1000004 NAME 'TEST1000004 WGS 84 / Pseudo-Mercator' DEFINITION 'PROJCS["WGS 84 / Pseudo-Mercator",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Popular Visualisation Pseudo Mercator",AUTHORITY["EPSG","1024"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3857"]]';
ERROR SR003: The spatial reference system definition for SRID 1000004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1000005 NAME 'TEST1000005 WGS 84 / Pseudo-Mercator' DEFINITION 'PROJCS["WGS 84 / Pseudo-Mercator",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Popular Visualisation Pseudo Mercator",AUTHORITY["EPSG","1024"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3857"]]';
ERROR SR003: The spatial reference system definition for SRID 1000005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Lambert Azimuthal Equal Area (Spherical) (EPSG 1027).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1001000 NAME 'TEST1001000 US National Atlas Equal Area' DEFINITION 'PROJCS["US National Atlas Equal Area",GEOGCS["Unspecified datum based upon the Clarke 1866 Authalic Sphere",DATUM["Not specified (based on Clarke 1866 Authalic Sphere)",SPHEROID["Clarke 1866 Authalic Sphere",6370997,0,AUTHORITY["EPSG","7052"]],AUTHORITY["EPSG","6052"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4052"]],PROJECTION["Lambert Azimuthal Equal Area (Spherical)",AUTHORITY["EPSG","1027"]],PARAMETER["Latitude of natural origin",45,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-100,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2163"]]';
DROP SPATIAL REFERENCE SYSTEM 1001000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1001001 NAME 'TEST1001001 US National Atlas Equal Area' DEFINITION 'PROJCS["US National Atlas Equal Area",GEOGCS["Unspecified datum based upon the Clarke 1866 Authalic Sphere",DATUM["Not specified (based on Clarke 1866 Authalic Sphere)",SPHEROID["Clarke 1866 Authalic Sphere",6370997,0,AUTHORITY["EPSG","7052"]],AUTHORITY["EPSG","6052"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4052"]],PROJECTION["Lambert Azimuthal Equal Area (Spherical)",AUTHORITY["EPSG","1027"]],PARAMETER["latitude_of_origin",45],PARAMETER["central_meridian",-100],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2163"]]';
DROP SPATIAL REFERENCE SYSTEM 1001001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1001002 NAME 'TEST1001002 US National Atlas Equal Area' DEFINITION 'PROJCS["US National Atlas Equal Area",GEOGCS["Unspecified datum based upon the Clarke 1866 Authalic Sphere",DATUM["Not specified (based on Clarke 1866 Authalic Sphere)",SPHEROID["Clarke 1866 Authalic Sphere",6370997,0,AUTHORITY["EPSG","7052"]],AUTHORITY["EPSG","6052"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4052"]],PROJECTION["Lambert Azimuthal Equal Area (Spherical)",AUTHORITY["EPSG","1027"]],PARAMETER["Longitude of natural origin",-100,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2163"]]';
ERROR SR003: The spatial reference system definition for SRID 1001002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1001003 NAME 'TEST1001003 US National Atlas Equal Area' DEFINITION 'PROJCS["US National Atlas Equal Area",GEOGCS["Unspecified datum based upon the Clarke 1866 Authalic Sphere",DATUM["Not specified (based on Clarke 1866 Authalic Sphere)",SPHEROID["Clarke 1866 Authalic Sphere",6370997,0,AUTHORITY["EPSG","7052"]],AUTHORITY["EPSG","6052"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4052"]],PROJECTION["Lambert Azimuthal Equal Area (Spherical)",AUTHORITY["EPSG","1027"]],PARAMETER["Latitude of natural origin",45,AUTHORITY["EPSG","8801"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2163"]]';
ERROR SR003: The spatial reference system definition for SRID 1001003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1001004 NAME 'TEST1001004 US National Atlas Equal Area' DEFINITION 'PROJCS["US National Atlas Equal Area",GEOGCS["Unspecified datum based upon the Clarke 1866 Authalic Sphere",DATUM["Not specified (based on Clarke 1866 Authalic Sphere)",SPHEROID["Clarke 1866 Authalic Sphere",6370997,0,AUTHORITY["EPSG","7052"]],AUTHORITY["EPSG","6052"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4052"]],PROJECTION["Lambert Azimuthal Equal Area (Spherical)",AUTHORITY["EPSG","1027"]],PARAMETER["Latitude of natural origin",45,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-100,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2163"]]';
ERROR SR003: The spatial reference system definition for SRID 1001004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1001005 NAME 'TEST1001005 US National Atlas Equal Area' DEFINITION 'PROJCS["US National Atlas Equal Area",GEOGCS["Unspecified datum based upon the Clarke 1866 Authalic Sphere",DATUM["Not specified (based on Clarke 1866 Authalic Sphere)",SPHEROID["Clarke 1866 Authalic Sphere",6370997,0,AUTHORITY["EPSG","7052"]],AUTHORITY["EPSG","6052"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4052"]],PROJECTION["Lambert Azimuthal Equal Area (Spherical)",AUTHORITY["EPSG","1027"]],PARAMETER["Latitude of natural origin",45,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-100,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2163"]]';
ERROR SR003: The spatial reference system definition for SRID 1001005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Equidistant Cylindrical (EPSG 1028).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1002000 NAME 'TEST1002000 WGS 84 / World Equidistant Cylindrical' DEFINITION 'PROJCS["WGS 84 / World Equidistant Cylindrical",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Equidistant Cylindrical",AUTHORITY["EPSG","1028"]],PARAMETER["Latitude of 1st standard parallel",0,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","4087"]]';
DROP SPATIAL REFERENCE SYSTEM 1002000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1002001 NAME 'TEST1002001 WGS 84 / World Equidistant Cylindrical' DEFINITION 'PROJCS["WGS 84 / World Equidistant Cylindrical",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Equidistant Cylindrical",AUTHORITY["EPSG","1028"]],PARAMETER["standard_parallel_1",0],PARAMETER["central_meridian",0],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","4087"]]';
DROP SPATIAL REFERENCE SYSTEM 1002001;
# Missing parameter 8823 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1002002 NAME 'TEST1002002 WGS 84 / World Equidistant Cylindrical' DEFINITION 'PROJCS["WGS 84 / World Equidistant Cylindrical",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Equidistant Cylindrical",AUTHORITY["EPSG","1028"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","4087"]]';
ERROR SR003: The spatial reference system definition for SRID 1002002 does not specify the mandatory standard_parallel_1 (EPSG 8823) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1002003 NAME 'TEST1002003 WGS 84 / World Equidistant Cylindrical' DEFINITION 'PROJCS["WGS 84 / World Equidistant Cylindrical",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Equidistant Cylindrical",AUTHORITY["EPSG","1028"]],PARAMETER["Latitude of 1st standard parallel",0,AUTHORITY["EPSG","8823"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","4087"]]';
ERROR SR003: The spatial reference system definition for SRID 1002003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1002004 NAME 'TEST1002004 WGS 84 / World Equidistant Cylindrical' DEFINITION 'PROJCS["WGS 84 / World Equidistant Cylindrical",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Equidistant Cylindrical",AUTHORITY["EPSG","1028"]],PARAMETER["Latitude of 1st standard parallel",0,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","4087"]]';
ERROR SR003: The spatial reference system definition for SRID 1002004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1002005 NAME 'TEST1002005 WGS 84 / World Equidistant Cylindrical' DEFINITION 'PROJCS["WGS 84 / World Equidistant Cylindrical",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Equidistant Cylindrical",AUTHORITY["EPSG","1028"]],PARAMETER["Latitude of 1st standard parallel",0,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","4087"]]';
ERROR SR003: The spatial reference system definition for SRID 1002005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Equidistant Cylindrical (Spherical) (EPSG 1029).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1003000 NAME 'TEST1003000 World Equidistant Cylindrical (Sphere)' DEFINITION 'PROJCS["World Equidistant Cylindrical (Sphere)",GEOGCS["Unspecified datum based upon the GRS 1980 Authalic Sphere",DATUM["Not specified (based on GRS 1980 Authalic Sphere)",SPHEROID["GRS 1980 Authalic Sphere",6371007,0,AUTHORITY["EPSG","7048"]],AUTHORITY["EPSG","6047"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4047"]],PROJECTION["Equidistant Cylindrical (Spherical)",AUTHORITY["EPSG","1029"]],PARAMETER["Latitude of 1st standard parallel",0,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","4088"]]';
DROP SPATIAL REFERENCE SYSTEM 1003000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1003001 NAME 'TEST1003001 World Equidistant Cylindrical (Sphere)' DEFINITION 'PROJCS["World Equidistant Cylindrical (Sphere)",GEOGCS["Unspecified datum based upon the GRS 1980 Authalic Sphere",DATUM["Not specified (based on GRS 1980 Authalic Sphere)",SPHEROID["GRS 1980 Authalic Sphere",6371007,0,AUTHORITY["EPSG","7048"]],AUTHORITY["EPSG","6047"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4047"]],PROJECTION["Equidistant Cylindrical (Spherical)",AUTHORITY["EPSG","1029"]],PARAMETER["standard_parallel_1",0],PARAMETER["central_meridian",0],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","4088"]]';
DROP SPATIAL REFERENCE SYSTEM 1003001;
# Missing parameter 8823 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1003002 NAME 'TEST1003002 World Equidistant Cylindrical (Sphere)' DEFINITION 'PROJCS["World Equidistant Cylindrical (Sphere)",GEOGCS["Unspecified datum based upon the GRS 1980 Authalic Sphere",DATUM["Not specified (based on GRS 1980 Authalic Sphere)",SPHEROID["GRS 1980 Authalic Sphere",6371007,0,AUTHORITY["EPSG","7048"]],AUTHORITY["EPSG","6047"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4047"]],PROJECTION["Equidistant Cylindrical (Spherical)",AUTHORITY["EPSG","1029"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","4088"]]';
ERROR SR003: The spatial reference system definition for SRID 1003002 does not specify the mandatory standard_parallel_1 (EPSG 8823) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1003003 NAME 'TEST1003003 World Equidistant Cylindrical (Sphere)' DEFINITION 'PROJCS["World Equidistant Cylindrical (Sphere)",GEOGCS["Unspecified datum based upon the GRS 1980 Authalic Sphere",DATUM["Not specified (based on GRS 1980 Authalic Sphere)",SPHEROID["GRS 1980 Authalic Sphere",6371007,0,AUTHORITY["EPSG","7048"]],AUTHORITY["EPSG","6047"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4047"]],PROJECTION["Equidistant Cylindrical (Spherical)",AUTHORITY["EPSG","1029"]],PARAMETER["Latitude of 1st standard parallel",0,AUTHORITY["EPSG","8823"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","4088"]]';
ERROR SR003: The spatial reference system definition for SRID 1003003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1003004 NAME 'TEST1003004 World Equidistant Cylindrical (Sphere)' DEFINITION 'PROJCS["World Equidistant Cylindrical (Sphere)",GEOGCS["Unspecified datum based upon the GRS 1980 Authalic Sphere",DATUM["Not specified (based on GRS 1980 Authalic Sphere)",SPHEROID["GRS 1980 Authalic Sphere",6371007,0,AUTHORITY["EPSG","7048"]],AUTHORITY["EPSG","6047"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4047"]],PROJECTION["Equidistant Cylindrical (Spherical)",AUTHORITY["EPSG","1029"]],PARAMETER["Latitude of 1st standard parallel",0,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","4088"]]';
ERROR SR003: The spatial reference system definition for SRID 1003004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1003005 NAME 'TEST1003005 World Equidistant Cylindrical (Sphere)' DEFINITION 'PROJCS["World Equidistant Cylindrical (Sphere)",GEOGCS["Unspecified datum based upon the GRS 1980 Authalic Sphere",DATUM["Not specified (based on GRS 1980 Authalic Sphere)",SPHEROID["GRS 1980 Authalic Sphere",6371007,0,AUTHORITY["EPSG","7048"]],AUTHORITY["EPSG","6047"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4047"]],PROJECTION["Equidistant Cylindrical (Spherical)",AUTHORITY["EPSG","1029"]],PARAMETER["Latitude of 1st standard parallel",0,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","4088"]]';
ERROR SR003: The spatial reference system definition for SRID 1003005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Krovak (North Orientated) (EPSG 1041).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1004000 NAME 'TEST1004000 S-JTSK (Ferro) / Krovak East North' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak East North",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak (North Orientated)",AUTHORITY["EPSG","1041"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5221"]]';
DROP SPATIAL REFERENCE SYSTEM 1004000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1004001 NAME 'TEST1004001 S-JTSK (Ferro) / Krovak East North' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak East North",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak (North Orientated)",AUTHORITY["EPSG","1041"]],PARAMETER["latitude_of_center",49.5111111111111],PARAMETER["longitude_of_center",42.5111111111111],PARAMETER["azimuth",30.2881397222222],PARAMETER["pseudo_standard_parallel_1",78.5111111111111],PARAMETER["scale_factor",0.9999],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5221"]]';
DROP SPATIAL REFERENCE SYSTEM 1004001;
# Missing parameter 8811 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1004002 NAME 'TEST1004002 S-JTSK (Ferro) / Krovak East North' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak East North",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak (North Orientated)",AUTHORITY["EPSG","1041"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5221"]]';
ERROR SR003: The spatial reference system definition for SRID 1004002 does not specify the mandatory latitude_of_center (EPSG 8811) projection parameter.
# Missing parameter 8833 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1004003 NAME 'TEST1004003 S-JTSK (Ferro) / Krovak East North' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak East North",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak (North Orientated)",AUTHORITY["EPSG","1041"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5221"]]';
ERROR SR003: The spatial reference system definition for SRID 1004003 does not specify the mandatory longitude_of_center (EPSG 8833) projection parameter.
# Missing parameter 1036 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1004004 NAME 'TEST1004004 S-JTSK (Ferro) / Krovak East North' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak East North",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak (North Orientated)",AUTHORITY["EPSG","1041"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5221"]]';
ERROR SR003: The spatial reference system definition for SRID 1004004 does not specify the mandatory azimuth (EPSG 1036) projection parameter.
# Missing parameter 8818 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1004005 NAME 'TEST1004005 S-JTSK (Ferro) / Krovak East North' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak East North",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak (North Orientated)",AUTHORITY["EPSG","1041"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5221"]]';
ERROR SR003: The spatial reference system definition for SRID 1004005 does not specify the mandatory pseudo_standard_parallel_1 (EPSG 8818) projection parameter.
# Missing parameter 8819 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1004006 NAME 'TEST1004006 S-JTSK (Ferro) / Krovak East North' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak East North",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak (North Orientated)",AUTHORITY["EPSG","1041"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5221"]]';
ERROR SR003: The spatial reference system definition for SRID 1004006 does not specify the mandatory scale_factor (EPSG 8819) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1004007 NAME 'TEST1004007 S-JTSK (Ferro) / Krovak East North' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak East North",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak (North Orientated)",AUTHORITY["EPSG","1041"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5221"]]';
ERROR SR003: The spatial reference system definition for SRID 1004007 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1004008 NAME 'TEST1004008 S-JTSK (Ferro) / Krovak East North' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak East North",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak (North Orientated)",AUTHORITY["EPSG","1041"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5221"]]';
ERROR SR003: The spatial reference system definition for SRID 1004008 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Krovak Modified (EPSG 1042).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1005000 NAME 'TEST1005000 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
DROP SPATIAL REFERENCE SYSTEM 1005000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1005001 NAME 'TEST1005001 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["latitude_of_center",49.5111111111111],PARAMETER["longitude_of_center",42.5111111111111],PARAMETER["azimuth",30.2881397222222],PARAMETER["pseudo_standard_parallel_1",78.5111111111111],PARAMETER["scale_factor",0.9999],PARAMETER["false_easting",5000000],PARAMETER["false_northing",5000000],PARAMETER["evaluation_point_ordinate_1",1089000],PARAMETER["evaluation_point_ordinate_2",654000],PARAMETER["C1",0.02946529277],PARAMETER["C2",0.02515965696],PARAMETER["C3",1.193845912e-07],PARAMETER["C4",-4.668270147e-07],PARAMETER["C5",9.233980362e-12],PARAMETER["C6",1.523735715e-12],PARAMETER["C7",1.696780024e-18],PARAMETER["C8",4.408314235e-18],PARAMETER["C9",-8.331083518e-24],PARAMETER["C10",-3.689471323e-24],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
DROP SPATIAL REFERENCE SYSTEM 1005001;
# Missing parameter 8811 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005002 NAME 'TEST1005002 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005002 does not specify the mandatory latitude_of_center (EPSG 8811) projection parameter.
# Missing parameter 8833 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005003 NAME 'TEST1005003 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005003 does not specify the mandatory longitude_of_center (EPSG 8833) projection parameter.
# Missing parameter 1036 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005004 NAME 'TEST1005004 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005004 does not specify the mandatory azimuth (EPSG 1036) projection parameter.
# Missing parameter 8818 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005005 NAME 'TEST1005005 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005005 does not specify the mandatory pseudo_standard_parallel_1 (EPSG 8818) projection parameter.
# Missing parameter 8819 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005006 NAME 'TEST1005006 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005006 does not specify the mandatory scale_factor (EPSG 8819) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005007 NAME 'TEST1005007 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005007 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005008 NAME 'TEST1005008 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005008 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
# Missing parameter 8617 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005009 NAME 'TEST1005009 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005009 does not specify the mandatory evaluation_point_ordinate_1 (EPSG 8617) projection parameter.
# Missing parameter 8618 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005010 NAME 'TEST1005010 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005010 does not specify the mandatory evaluation_point_ordinate_2 (EPSG 8618) projection parameter.
# Missing parameter 1026 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005011 NAME 'TEST1005011 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005011 does not specify the mandatory c1 (EPSG 1026) projection parameter.
# Missing parameter 1027 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005012 NAME 'TEST1005012 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005012 does not specify the mandatory c2 (EPSG 1027) projection parameter.
# Missing parameter 1028 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005013 NAME 'TEST1005013 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005013 does not specify the mandatory c3 (EPSG 1028) projection parameter.
# Missing parameter 1029 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005014 NAME 'TEST1005014 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005014 does not specify the mandatory c4 (EPSG 1029) projection parameter.
# Missing parameter 1030 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005015 NAME 'TEST1005015 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005015 does not specify the mandatory c5 (EPSG 1030) projection parameter.
# Missing parameter 1031 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005016 NAME 'TEST1005016 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005016 does not specify the mandatory c6 (EPSG 1031) projection parameter.
# Missing parameter 1032 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005017 NAME 'TEST1005017 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005017 does not specify the mandatory c7 (EPSG 1032) projection parameter.
# Missing parameter 1033 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005018 NAME 'TEST1005018 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005018 does not specify the mandatory c8 (EPSG 1033) projection parameter.
# Missing parameter 1034 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005019 NAME 'TEST1005019 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005019 does not specify the mandatory c9 (EPSG 1034) projection parameter.
# Missing parameter 1035 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1005020 NAME 'TEST1005020 S-JTSK/05 (Ferro) / Modified Krovak' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified",AUTHORITY["EPSG","1042"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","5224"]]';
ERROR SR003: The spatial reference system definition for SRID 1005020 does not specify the mandatory c10 (EPSG 1035) projection parameter.
#
# Projection: Krovak Modified (EPSG 1043).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1006000 NAME 'TEST1006000 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
DROP SPATIAL REFERENCE SYSTEM 1006000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1006001 NAME 'TEST1006001 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["latitude_of_center",49.5111111111111],PARAMETER["longitude_of_center",42.5111111111111],PARAMETER["azimuth",30.2881397222222],PARAMETER["pseudo_standard_parallel_1",78.5111111111111],PARAMETER["scale_factor",0.9999],PARAMETER["false_easting",5000000],PARAMETER["false_northing",5000000],PARAMETER["evaluation_point_ordinate_1",1089000],PARAMETER["evaluation_point_ordinate_2",654000],PARAMETER["C1",0.02946529277],PARAMETER["C2",0.02515965696],PARAMETER["C3",1.193845912e-07],PARAMETER["C4",-4.668270147e-07],PARAMETER["C5",9.233980362e-12],PARAMETER["C6",1.523735715e-12],PARAMETER["C7",1.696780024e-18],PARAMETER["C8",4.408314235e-18],PARAMETER["C9",-8.331083518e-24],PARAMETER["C10",-3.689471323e-24],UNIT["metre",1],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
DROP SPATIAL REFERENCE SYSTEM 1006001;
# Missing parameter 8811 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006002 NAME 'TEST1006002 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006002 does not specify the mandatory latitude_of_center (EPSG 8811) projection parameter.
# Missing parameter 8833 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006003 NAME 'TEST1006003 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006003 does not specify the mandatory longitude_of_center (EPSG 8833) projection parameter.
# Missing parameter 1036 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006004 NAME 'TEST1006004 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006004 does not specify the mandatory azimuth (EPSG 1036) projection parameter.
# Missing parameter 8818 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006005 NAME 'TEST1006005 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006005 does not specify the mandatory pseudo_standard_parallel_1 (EPSG 8818) projection parameter.
# Missing parameter 8819 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006006 NAME 'TEST1006006 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006006 does not specify the mandatory scale_factor (EPSG 8819) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006007 NAME 'TEST1006007 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006007 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006008 NAME 'TEST1006008 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006008 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
# Missing parameter 8617 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006009 NAME 'TEST1006009 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006009 does not specify the mandatory evaluation_point_ordinate_1 (EPSG 8617) projection parameter.
# Missing parameter 8618 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006010 NAME 'TEST1006010 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006010 does not specify the mandatory evaluation_point_ordinate_2 (EPSG 8618) projection parameter.
# Missing parameter 1026 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006011 NAME 'TEST1006011 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006011 does not specify the mandatory c1 (EPSG 1026) projection parameter.
# Missing parameter 1027 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006012 NAME 'TEST1006012 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006012 does not specify the mandatory c2 (EPSG 1027) projection parameter.
# Missing parameter 1028 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006013 NAME 'TEST1006013 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006013 does not specify the mandatory c3 (EPSG 1028) projection parameter.
# Missing parameter 1029 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006014 NAME 'TEST1006014 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006014 does not specify the mandatory c4 (EPSG 1029) projection parameter.
# Missing parameter 1030 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006015 NAME 'TEST1006015 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006015 does not specify the mandatory c5 (EPSG 1030) projection parameter.
# Missing parameter 1031 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006016 NAME 'TEST1006016 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006016 does not specify the mandatory c6 (EPSG 1031) projection parameter.
# Missing parameter 1032 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006017 NAME 'TEST1006017 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006017 does not specify the mandatory c7 (EPSG 1032) projection parameter.
# Missing parameter 1033 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006018 NAME 'TEST1006018 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006018 does not specify the mandatory c8 (EPSG 1033) projection parameter.
# Missing parameter 1034 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006019 NAME 'TEST1006019 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C10",-3.689471323e-24,AUTHORITY["EPSG","1035"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006019 does not specify the mandatory c9 (EPSG 1034) projection parameter.
# Missing parameter 1035 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1006020 NAME 'TEST1006020 S-JTSK/05 (Ferro) / Modified Krovak East North' DEFINITION 'PROJCS["S-JTSK/05 (Ferro) / Modified Krovak East North",GEOGCS["S-JTSK/05 (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni/05 (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","1055"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5229"]],PROJECTION["Krovak Modified (North Orientated)",AUTHORITY["EPSG","1043"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",5000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5000000,AUTHORITY["EPSG","8807"]],PARAMETER["Ordinate 1 of evaluation point",1089000,AUTHORITY["EPSG","8617"]],PARAMETER["Ordinate 2 of evaluation point",654000,AUTHORITY["EPSG","8618"]],PARAMETER["C1",0.02946529277,AUTHORITY["EPSG","1026"]],PARAMETER["C2",0.02515965696,AUTHORITY["EPSG","1027"]],PARAMETER["C3",1.193845912e-07,AUTHORITY["EPSG","1028"]],PARAMETER["C4",-4.668270147e-07,AUTHORITY["EPSG","1029"]],PARAMETER["C5",9.233980362e-12,AUTHORITY["EPSG","1030"]],PARAMETER["C6",1.523735715e-12,AUTHORITY["EPSG","1031"]],PARAMETER["C7",1.696780024e-18,AUTHORITY["EPSG","1032"]],PARAMETER["C8",4.408314235e-18,AUTHORITY["EPSG","1033"]],PARAMETER["C9",-8.331083518e-24,AUTHORITY["EPSG","1034"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","5225"]]';
ERROR SR003: The spatial reference system definition for SRID 1006020 does not specify the mandatory c10 (EPSG 1035) projection parameter.
#
# Projection: Lambert Conic Conformal (2SP Michigan) (EPSG 1051).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1007000 NAME 'TEST1007000 NAD27 / Michigan Central' DEFINITION 'PROJCS["NAD27 / Michigan Central",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Lambert Conic Conformal (2SP Michigan)",AUTHORITY["EPSG","1051"]],PARAMETER["Latitude of false origin",43.3277777777778,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-84.3333333333333,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",44.1944444444444,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",45.7,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",2000000,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],PARAMETER["Ellipsoid scaling factor",1.0000382,AUTHORITY["EPSG","1038"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6201"]]';
DROP SPATIAL REFERENCE SYSTEM 1007000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1007001 NAME 'TEST1007001 NAD27 / Michigan Central' DEFINITION 'PROJCS["NAD27 / Michigan Central",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Lambert Conic Conformal (2SP Michigan)",AUTHORITY["EPSG","1051"]],PARAMETER["latitude_of_origin",43.3277777777778],PARAMETER["central_meridian",-84.3333333333333],PARAMETER["standard_parallel_1",44.1944444444444],PARAMETER["standard_parallel_2",45.7],PARAMETER["false_easting",2000000],PARAMETER["false_northing",0],PARAMETER["ellipsoid_scale_factor",1.0000382],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6201"]]';
DROP SPATIAL REFERENCE SYSTEM 1007001;
# Missing parameter 8821 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1007002 NAME 'TEST1007002 NAD27 / Michigan Central' DEFINITION 'PROJCS["NAD27 / Michigan Central",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Lambert Conic Conformal (2SP Michigan)",AUTHORITY["EPSG","1051"]],PARAMETER["Longitude of false origin",-84.3333333333333,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",44.1944444444444,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",45.7,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",2000000,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],PARAMETER["Ellipsoid scaling factor",1.0000382,AUTHORITY["EPSG","1038"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6201"]]';
ERROR SR003: The spatial reference system definition for SRID 1007002 does not specify the mandatory latitude_of_origin (EPSG 8821) projection parameter.
# Missing parameter 8822 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1007003 NAME 'TEST1007003 NAD27 / Michigan Central' DEFINITION 'PROJCS["NAD27 / Michigan Central",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Lambert Conic Conformal (2SP Michigan)",AUTHORITY["EPSG","1051"]],PARAMETER["Latitude of false origin",43.3277777777778,AUTHORITY["EPSG","8821"]],PARAMETER["Latitude of 1st standard parallel",44.1944444444444,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",45.7,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",2000000,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],PARAMETER["Ellipsoid scaling factor",1.0000382,AUTHORITY["EPSG","1038"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6201"]]';
ERROR SR003: The spatial reference system definition for SRID 1007003 does not specify the mandatory central_meridian (EPSG 8822) projection parameter.
# Missing parameter 8823 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1007004 NAME 'TEST1007004 NAD27 / Michigan Central' DEFINITION 'PROJCS["NAD27 / Michigan Central",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Lambert Conic Conformal (2SP Michigan)",AUTHORITY["EPSG","1051"]],PARAMETER["Latitude of false origin",43.3277777777778,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-84.3333333333333,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 2nd standard parallel",45.7,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",2000000,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],PARAMETER["Ellipsoid scaling factor",1.0000382,AUTHORITY["EPSG","1038"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6201"]]';
ERROR SR003: The spatial reference system definition for SRID 1007004 does not specify the mandatory standard_parallel_1 (EPSG 8823) projection parameter.
# Missing parameter 8824 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1007005 NAME 'TEST1007005 NAD27 / Michigan Central' DEFINITION 'PROJCS["NAD27 / Michigan Central",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Lambert Conic Conformal (2SP Michigan)",AUTHORITY["EPSG","1051"]],PARAMETER["Latitude of false origin",43.3277777777778,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-84.3333333333333,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",44.1944444444444,AUTHORITY["EPSG","8823"]],PARAMETER["Easting at false origin",2000000,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],PARAMETER["Ellipsoid scaling factor",1.0000382,AUTHORITY["EPSG","1038"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6201"]]';
ERROR SR003: The spatial reference system definition for SRID 1007005 does not specify the mandatory standard_parallel_2 (EPSG 8824) projection parameter.
# Missing parameter 8826 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1007006 NAME 'TEST1007006 NAD27 / Michigan Central' DEFINITION 'PROJCS["NAD27 / Michigan Central",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Lambert Conic Conformal (2SP Michigan)",AUTHORITY["EPSG","1051"]],PARAMETER["Latitude of false origin",43.3277777777778,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-84.3333333333333,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",44.1944444444444,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",45.7,AUTHORITY["EPSG","8824"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],PARAMETER["Ellipsoid scaling factor",1.0000382,AUTHORITY["EPSG","1038"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6201"]]';
ERROR SR003: The spatial reference system definition for SRID 1007006 does not specify the mandatory false_easting (EPSG 8826) projection parameter.
# Missing parameter 8827 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1007007 NAME 'TEST1007007 NAD27 / Michigan Central' DEFINITION 'PROJCS["NAD27 / Michigan Central",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Lambert Conic Conformal (2SP Michigan)",AUTHORITY["EPSG","1051"]],PARAMETER["Latitude of false origin",43.3277777777778,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-84.3333333333333,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",44.1944444444444,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",45.7,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",2000000,AUTHORITY["EPSG","8826"]],PARAMETER["Ellipsoid scaling factor",1.0000382,AUTHORITY["EPSG","1038"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6201"]]';
ERROR SR003: The spatial reference system definition for SRID 1007007 does not specify the mandatory false_northing (EPSG 8827) projection parameter.
# Missing parameter 1038 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1007008 NAME 'TEST1007008 NAD27 / Michigan Central' DEFINITION 'PROJCS["NAD27 / Michigan Central",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Lambert Conic Conformal (2SP Michigan)",AUTHORITY["EPSG","1051"]],PARAMETER["Latitude of false origin",43.3277777777778,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-84.3333333333333,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",44.1944444444444,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",45.7,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",2000000,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6201"]]';
ERROR SR003: The spatial reference system definition for SRID 1007008 does not specify the mandatory ellipsoid_scale_factor (EPSG 1038) projection parameter.
#
# Projection: Colombia Urban (EPSG 1052).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1008000 NAME 'TEST1008000 MAGNA-SIRGAS / Arauca urban grid' DEFINITION 'PROJCS["MAGNA-SIRGAS / Arauca urban grid",GEOGCS["MAGNA-SIRGAS",DATUM["Marco Geocentrico Nacional de Referencia",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6686"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4686"]],PROJECTION["Colombia Urban",AUTHORITY["EPSG","1052"]],PARAMETER["Latitude of natural origin",7.08760639166667,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-70.7583096555555,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",1035263.443,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",1275526.621,AUTHORITY["EPSG","8807"]],PARAMETER["Projection plane origin height",100,AUTHORITY["EPSG","1039"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["N",NORTH],AXIS["E",EAST],AUTHORITY["EPSG","6244"]]';
DROP SPATIAL REFERENCE SYSTEM 1008000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1008001 NAME 'TEST1008001 MAGNA-SIRGAS / Arauca urban grid' DEFINITION 'PROJCS["MAGNA-SIRGAS / Arauca urban grid",GEOGCS["MAGNA-SIRGAS",DATUM["Marco Geocentrico Nacional de Referencia",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6686"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4686"]],PROJECTION["Colombia Urban",AUTHORITY["EPSG","1052"]],PARAMETER["latitude_of_origin",7.08760639166667],PARAMETER["central_meridian",-70.7583096555555],PARAMETER["false_easting",1035263.443],PARAMETER["false_northing",1275526.621],PARAMETER["projection_plane_height_at_origin",100],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["N",NORTH],AXIS["E",EAST],AUTHORITY["EPSG","6244"]]';
DROP SPATIAL REFERENCE SYSTEM 1008001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1008002 NAME 'TEST1008002 MAGNA-SIRGAS / Arauca urban grid' DEFINITION 'PROJCS["MAGNA-SIRGAS / Arauca urban grid",GEOGCS["MAGNA-SIRGAS",DATUM["Marco Geocentrico Nacional de Referencia",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6686"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4686"]],PROJECTION["Colombia Urban",AUTHORITY["EPSG","1052"]],PARAMETER["Longitude of natural origin",-70.7583096555555,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",1035263.443,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",1275526.621,AUTHORITY["EPSG","8807"]],PARAMETER["Projection plane origin height",100,AUTHORITY["EPSG","1039"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["N",NORTH],AXIS["E",EAST],AUTHORITY["EPSG","6244"]]';
ERROR SR003: The spatial reference system definition for SRID 1008002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1008003 NAME 'TEST1008003 MAGNA-SIRGAS / Arauca urban grid' DEFINITION 'PROJCS["MAGNA-SIRGAS / Arauca urban grid",GEOGCS["MAGNA-SIRGAS",DATUM["Marco Geocentrico Nacional de Referencia",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6686"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4686"]],PROJECTION["Colombia Urban",AUTHORITY["EPSG","1052"]],PARAMETER["Latitude of natural origin",7.08760639166667,AUTHORITY["EPSG","8801"]],PARAMETER["False easting",1035263.443,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",1275526.621,AUTHORITY["EPSG","8807"]],PARAMETER["Projection plane origin height",100,AUTHORITY["EPSG","1039"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["N",NORTH],AXIS["E",EAST],AUTHORITY["EPSG","6244"]]';
ERROR SR003: The spatial reference system definition for SRID 1008003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1008004 NAME 'TEST1008004 MAGNA-SIRGAS / Arauca urban grid' DEFINITION 'PROJCS["MAGNA-SIRGAS / Arauca urban grid",GEOGCS["MAGNA-SIRGAS",DATUM["Marco Geocentrico Nacional de Referencia",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6686"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4686"]],PROJECTION["Colombia Urban",AUTHORITY["EPSG","1052"]],PARAMETER["Latitude of natural origin",7.08760639166667,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-70.7583096555555,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",1275526.621,AUTHORITY["EPSG","8807"]],PARAMETER["Projection plane origin height",100,AUTHORITY["EPSG","1039"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["N",NORTH],AXIS["E",EAST],AUTHORITY["EPSG","6244"]]';
ERROR SR003: The spatial reference system definition for SRID 1008004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1008005 NAME 'TEST1008005 MAGNA-SIRGAS / Arauca urban grid' DEFINITION 'PROJCS["MAGNA-SIRGAS / Arauca urban grid",GEOGCS["MAGNA-SIRGAS",DATUM["Marco Geocentrico Nacional de Referencia",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6686"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4686"]],PROJECTION["Colombia Urban",AUTHORITY["EPSG","1052"]],PARAMETER["Latitude of natural origin",7.08760639166667,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-70.7583096555555,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",1035263.443,AUTHORITY["EPSG","8806"]],PARAMETER["Projection plane origin height",100,AUTHORITY["EPSG","1039"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["N",NORTH],AXIS["E",EAST],AUTHORITY["EPSG","6244"]]';
ERROR SR003: The spatial reference system definition for SRID 1008005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
# Missing parameter 1039 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1008006 NAME 'TEST1008006 MAGNA-SIRGAS / Arauca urban grid' DEFINITION 'PROJCS["MAGNA-SIRGAS / Arauca urban grid",GEOGCS["MAGNA-SIRGAS",DATUM["Marco Geocentrico Nacional de Referencia",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6686"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4686"]],PROJECTION["Colombia Urban",AUTHORITY["EPSG","1052"]],PARAMETER["Latitude of natural origin",7.08760639166667,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-70.7583096555555,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",1035263.443,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",1275526.621,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["N",NORTH],AXIS["E",EAST],AUTHORITY["EPSG","6244"]]';
ERROR SR003: The spatial reference system definition for SRID 1008006 does not specify the mandatory projection_plane_height_at_origin (EPSG 1039) projection parameter.
#
# Projection: Lambert Conic Conformal (1SP) (EPSG 9801).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1009000 NAME 'TEST1009000 Madrid 1870 (Madrid) / Spain' DEFINITION 'PROJCS["Madrid 1870 (Madrid) / Spain",GEOGCS["Madrid 1870 (Madrid)",DATUM["Madrid 1870 (Madrid)",SPHEROID["Struve 1860",6378298.3,294.73,AUTHORITY["EPSG","7028"]],AUTHORITY["EPSG","6903"]],PRIMEM["Madrid",-3.6879388888888895,AUTHORITY["EPSG","8905"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4903"]],PROJECTION["Lambert Conic Conformal (1SP)",AUTHORITY["EPSG","9801"]],PARAMETER["Latitude of natural origin",40,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9988085293,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",600000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",600000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2062"]]';
DROP SPATIAL REFERENCE SYSTEM 1009000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1009001 NAME 'TEST1009001 Madrid 1870 (Madrid) / Spain' DEFINITION 'PROJCS["Madrid 1870 (Madrid) / Spain",GEOGCS["Madrid 1870 (Madrid)",DATUM["Madrid 1870 (Madrid)",SPHEROID["Struve 1860",6378298.3,294.73,AUTHORITY["EPSG","7028"]],AUTHORITY["EPSG","6903"]],PRIMEM["Madrid",-3.6879388888888895,AUTHORITY["EPSG","8905"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4903"]],PROJECTION["Lambert Conic Conformal (1SP)",AUTHORITY["EPSG","9801"]],PARAMETER["latitude_of_origin",40],PARAMETER["central_meridian",0],PARAMETER["scale_factor",0.9988085293],PARAMETER["false_easting",600000],PARAMETER["false_northing",600000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2062"]]';
DROP SPATIAL REFERENCE SYSTEM 1009001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1009002 NAME 'TEST1009002 Madrid 1870 (Madrid) / Spain' DEFINITION 'PROJCS["Madrid 1870 (Madrid) / Spain",GEOGCS["Madrid 1870 (Madrid)",DATUM["Madrid 1870 (Madrid)",SPHEROID["Struve 1860",6378298.3,294.73,AUTHORITY["EPSG","7028"]],AUTHORITY["EPSG","6903"]],PRIMEM["Madrid",-3.6879388888888895,AUTHORITY["EPSG","8905"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4903"]],PROJECTION["Lambert Conic Conformal (1SP)",AUTHORITY["EPSG","9801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9988085293,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",600000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",600000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2062"]]';
ERROR SR003: The spatial reference system definition for SRID 1009002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1009003 NAME 'TEST1009003 Madrid 1870 (Madrid) / Spain' DEFINITION 'PROJCS["Madrid 1870 (Madrid) / Spain",GEOGCS["Madrid 1870 (Madrid)",DATUM["Madrid 1870 (Madrid)",SPHEROID["Struve 1860",6378298.3,294.73,AUTHORITY["EPSG","7028"]],AUTHORITY["EPSG","6903"]],PRIMEM["Madrid",-3.6879388888888895,AUTHORITY["EPSG","8905"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4903"]],PROJECTION["Lambert Conic Conformal (1SP)",AUTHORITY["EPSG","9801"]],PARAMETER["Latitude of natural origin",40,AUTHORITY["EPSG","8801"]],PARAMETER["Scale factor at natural origin",0.9988085293,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",600000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",600000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2062"]]';
ERROR SR003: The spatial reference system definition for SRID 1009003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8805 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1009004 NAME 'TEST1009004 Madrid 1870 (Madrid) / Spain' DEFINITION 'PROJCS["Madrid 1870 (Madrid) / Spain",GEOGCS["Madrid 1870 (Madrid)",DATUM["Madrid 1870 (Madrid)",SPHEROID["Struve 1860",6378298.3,294.73,AUTHORITY["EPSG","7028"]],AUTHORITY["EPSG","6903"]],PRIMEM["Madrid",-3.6879388888888895,AUTHORITY["EPSG","8905"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4903"]],PROJECTION["Lambert Conic Conformal (1SP)",AUTHORITY["EPSG","9801"]],PARAMETER["Latitude of natural origin",40,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",600000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",600000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2062"]]';
ERROR SR003: The spatial reference system definition for SRID 1009004 does not specify the mandatory scale_factor (EPSG 8805) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1009005 NAME 'TEST1009005 Madrid 1870 (Madrid) / Spain' DEFINITION 'PROJCS["Madrid 1870 (Madrid) / Spain",GEOGCS["Madrid 1870 (Madrid)",DATUM["Madrid 1870 (Madrid)",SPHEROID["Struve 1860",6378298.3,294.73,AUTHORITY["EPSG","7028"]],AUTHORITY["EPSG","6903"]],PRIMEM["Madrid",-3.6879388888888895,AUTHORITY["EPSG","8905"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4903"]],PROJECTION["Lambert Conic Conformal (1SP)",AUTHORITY["EPSG","9801"]],PARAMETER["Latitude of natural origin",40,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9988085293,AUTHORITY["EPSG","8805"]],PARAMETER["False northing",600000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2062"]]';
ERROR SR003: The spatial reference system definition for SRID 1009005 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1009006 NAME 'TEST1009006 Madrid 1870 (Madrid) / Spain' DEFINITION 'PROJCS["Madrid 1870 (Madrid) / Spain",GEOGCS["Madrid 1870 (Madrid)",DATUM["Madrid 1870 (Madrid)",SPHEROID["Struve 1860",6378298.3,294.73,AUTHORITY["EPSG","7028"]],AUTHORITY["EPSG","6903"]],PRIMEM["Madrid",-3.6879388888888895,AUTHORITY["EPSG","8905"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4903"]],PROJECTION["Lambert Conic Conformal (1SP)",AUTHORITY["EPSG","9801"]],PARAMETER["Latitude of natural origin",40,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9988085293,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",600000,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2062"]]';
ERROR SR003: The spatial reference system definition for SRID 1009006 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Lambert Conic Conformal (2SP) (EPSG 9802).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1010000 NAME 'TEST1010000 NAD27(CGQ77) / Quebec Lambert' DEFINITION 'PROJCS["NAD27(CGQ77) / Quebec Lambert",GEOGCS["NAD27(CGQ77)",DATUM["North American Datum 1927 (CGQ77)",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","6609"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4609"]],PROJECTION["Lambert Conic Conformal (2SP)",AUTHORITY["EPSG","9802"]],PARAMETER["Latitude of false origin",44,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-68.5111111111111,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",60,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",46,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",0,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2138"]]';
DROP SPATIAL REFERENCE SYSTEM 1010000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1010001 NAME 'TEST1010001 NAD27(CGQ77) / Quebec Lambert' DEFINITION 'PROJCS["NAD27(CGQ77) / Quebec Lambert",GEOGCS["NAD27(CGQ77)",DATUM["North American Datum 1927 (CGQ77)",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","6609"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4609"]],PROJECTION["Lambert Conic Conformal (2SP)",AUTHORITY["EPSG","9802"]],PARAMETER["latitude_of_origin",44],PARAMETER["central_meridian",-68.5111111111111],PARAMETER["standard_parallel_1",60],PARAMETER["standard_parallel_2",46],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2138"]]';
DROP SPATIAL REFERENCE SYSTEM 1010001;
# Missing parameter 8821 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1010002 NAME 'TEST1010002 NAD27(CGQ77) / Quebec Lambert' DEFINITION 'PROJCS["NAD27(CGQ77) / Quebec Lambert",GEOGCS["NAD27(CGQ77)",DATUM["North American Datum 1927 (CGQ77)",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","6609"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4609"]],PROJECTION["Lambert Conic Conformal (2SP)",AUTHORITY["EPSG","9802"]],PARAMETER["Longitude of false origin",-68.5111111111111,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",60,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",46,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",0,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2138"]]';
ERROR SR003: The spatial reference system definition for SRID 1010002 does not specify the mandatory latitude_of_origin (EPSG 8821) projection parameter.
# Missing parameter 8822 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1010003 NAME 'TEST1010003 NAD27(CGQ77) / Quebec Lambert' DEFINITION 'PROJCS["NAD27(CGQ77) / Quebec Lambert",GEOGCS["NAD27(CGQ77)",DATUM["North American Datum 1927 (CGQ77)",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","6609"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4609"]],PROJECTION["Lambert Conic Conformal (2SP)",AUTHORITY["EPSG","9802"]],PARAMETER["Latitude of false origin",44,AUTHORITY["EPSG","8821"]],PARAMETER["Latitude of 1st standard parallel",60,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",46,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",0,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2138"]]';
ERROR SR003: The spatial reference system definition for SRID 1010003 does not specify the mandatory central_meridian (EPSG 8822) projection parameter.
# Missing parameter 8823 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1010004 NAME 'TEST1010004 NAD27(CGQ77) / Quebec Lambert' DEFINITION 'PROJCS["NAD27(CGQ77) / Quebec Lambert",GEOGCS["NAD27(CGQ77)",DATUM["North American Datum 1927 (CGQ77)",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","6609"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4609"]],PROJECTION["Lambert Conic Conformal (2SP)",AUTHORITY["EPSG","9802"]],PARAMETER["Latitude of false origin",44,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-68.5111111111111,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 2nd standard parallel",46,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",0,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2138"]]';
ERROR SR003: The spatial reference system definition for SRID 1010004 does not specify the mandatory standard_parallel_1 (EPSG 8823) projection parameter.
# Missing parameter 8824 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1010005 NAME 'TEST1010005 NAD27(CGQ77) / Quebec Lambert' DEFINITION 'PROJCS["NAD27(CGQ77) / Quebec Lambert",GEOGCS["NAD27(CGQ77)",DATUM["North American Datum 1927 (CGQ77)",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","6609"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4609"]],PROJECTION["Lambert Conic Conformal (2SP)",AUTHORITY["EPSG","9802"]],PARAMETER["Latitude of false origin",44,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-68.5111111111111,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",60,AUTHORITY["EPSG","8823"]],PARAMETER["Easting at false origin",0,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2138"]]';
ERROR SR003: The spatial reference system definition for SRID 1010005 does not specify the mandatory standard_parallel_2 (EPSG 8824) projection parameter.
# Missing parameter 8826 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1010006 NAME 'TEST1010006 NAD27(CGQ77) / Quebec Lambert' DEFINITION 'PROJCS["NAD27(CGQ77) / Quebec Lambert",GEOGCS["NAD27(CGQ77)",DATUM["North American Datum 1927 (CGQ77)",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","6609"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4609"]],PROJECTION["Lambert Conic Conformal (2SP)",AUTHORITY["EPSG","9802"]],PARAMETER["Latitude of false origin",44,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-68.5111111111111,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",60,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",46,AUTHORITY["EPSG","8824"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2138"]]';
ERROR SR003: The spatial reference system definition for SRID 1010006 does not specify the mandatory false_easting (EPSG 8826) projection parameter.
# Missing parameter 8827 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1010007 NAME 'TEST1010007 NAD27(CGQ77) / Quebec Lambert' DEFINITION 'PROJCS["NAD27(CGQ77) / Quebec Lambert",GEOGCS["NAD27(CGQ77)",DATUM["North American Datum 1927 (CGQ77)",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","6609"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4609"]],PROJECTION["Lambert Conic Conformal (2SP)",AUTHORITY["EPSG","9802"]],PARAMETER["Latitude of false origin",44,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-68.5111111111111,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",60,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",46,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",0,AUTHORITY["EPSG","8826"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2138"]]';
ERROR SR003: The spatial reference system definition for SRID 1010007 does not specify the mandatory false_northing (EPSG 8827) projection parameter.
#
# Projection: Lambert Conic Conformal (2SP Belgium) (EPSG 9803).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1011000 NAME 'TEST1011000 Belge 1972 / Belge Lambert 72' DEFINITION 'PROJCS["Belge 1972 / Belge Lambert 72",GEOGCS["Belge 1972",DATUM["Reseau National Belge 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[-106.8686,52.2978,-103.7239,0.3366,-0.457,1.8422,-1.2747],AUTHORITY["EPSG","6313"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4313"]],PROJECTION["Lambert Conic Conformal (2SP Belgium)",AUTHORITY["EPSG","9803"]],PARAMETER["Latitude of false origin",90,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",4.35693972222222,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",49.8333333333333,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",51.1666666666667,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",150000.01256,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",5400088.4378,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","31300"]]';
DROP SPATIAL REFERENCE SYSTEM 1011000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1011001 NAME 'TEST1011001 Belge 1972 / Belge Lambert 72' DEFINITION 'PROJCS["Belge 1972 / Belge Lambert 72",GEOGCS["Belge 1972",DATUM["Reseau National Belge 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[-106.8686,52.2978,-103.7239,0.3366,-0.457,1.8422,-1.2747],AUTHORITY["EPSG","6313"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4313"]],PROJECTION["Lambert Conic Conformal (2SP Belgium)",AUTHORITY["EPSG","9803"]],PARAMETER["latitude_of_origin",90],PARAMETER["central_meridian",4.35693972222222],PARAMETER["standard_parallel_1",49.8333333333333],PARAMETER["standard_parallel_2",51.1666666666667],PARAMETER["false_easting",150000.01256],PARAMETER["false_northing",5400088.4378],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","31300"]]';
DROP SPATIAL REFERENCE SYSTEM 1011001;
# Missing parameter 8821 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1011002 NAME 'TEST1011002 Belge 1972 / Belge Lambert 72' DEFINITION 'PROJCS["Belge 1972 / Belge Lambert 72",GEOGCS["Belge 1972",DATUM["Reseau National Belge 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[-106.8686,52.2978,-103.7239,0.3366,-0.457,1.8422,-1.2747],AUTHORITY["EPSG","6313"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4313"]],PROJECTION["Lambert Conic Conformal (2SP Belgium)",AUTHORITY["EPSG","9803"]],PARAMETER["Longitude of false origin",4.35693972222222,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",49.8333333333333,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",51.1666666666667,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",150000.01256,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",5400088.4378,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","31300"]]';
ERROR SR003: The spatial reference system definition for SRID 1011002 does not specify the mandatory latitude_of_origin (EPSG 8821) projection parameter.
# Missing parameter 8822 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1011003 NAME 'TEST1011003 Belge 1972 / Belge Lambert 72' DEFINITION 'PROJCS["Belge 1972 / Belge Lambert 72",GEOGCS["Belge 1972",DATUM["Reseau National Belge 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[-106.8686,52.2978,-103.7239,0.3366,-0.457,1.8422,-1.2747],AUTHORITY["EPSG","6313"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4313"]],PROJECTION["Lambert Conic Conformal (2SP Belgium)",AUTHORITY["EPSG","9803"]],PARAMETER["Latitude of false origin",90,AUTHORITY["EPSG","8821"]],PARAMETER["Latitude of 1st standard parallel",49.8333333333333,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",51.1666666666667,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",150000.01256,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",5400088.4378,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","31300"]]';
ERROR SR003: The spatial reference system definition for SRID 1011003 does not specify the mandatory central_meridian (EPSG 8822) projection parameter.
# Missing parameter 8823 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1011004 NAME 'TEST1011004 Belge 1972 / Belge Lambert 72' DEFINITION 'PROJCS["Belge 1972 / Belge Lambert 72",GEOGCS["Belge 1972",DATUM["Reseau National Belge 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[-106.8686,52.2978,-103.7239,0.3366,-0.457,1.8422,-1.2747],AUTHORITY["EPSG","6313"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4313"]],PROJECTION["Lambert Conic Conformal (2SP Belgium)",AUTHORITY["EPSG","9803"]],PARAMETER["Latitude of false origin",90,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",4.35693972222222,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 2nd standard parallel",51.1666666666667,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",150000.01256,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",5400088.4378,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","31300"]]';
ERROR SR003: The spatial reference system definition for SRID 1011004 does not specify the mandatory standard_parallel_1 (EPSG 8823) projection parameter.
# Missing parameter 8824 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1011005 NAME 'TEST1011005 Belge 1972 / Belge Lambert 72' DEFINITION 'PROJCS["Belge 1972 / Belge Lambert 72",GEOGCS["Belge 1972",DATUM["Reseau National Belge 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[-106.8686,52.2978,-103.7239,0.3366,-0.457,1.8422,-1.2747],AUTHORITY["EPSG","6313"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4313"]],PROJECTION["Lambert Conic Conformal (2SP Belgium)",AUTHORITY["EPSG","9803"]],PARAMETER["Latitude of false origin",90,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",4.35693972222222,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",49.8333333333333,AUTHORITY["EPSG","8823"]],PARAMETER["Easting at false origin",150000.01256,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",5400088.4378,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","31300"]]';
ERROR SR003: The spatial reference system definition for SRID 1011005 does not specify the mandatory standard_parallel_2 (EPSG 8824) projection parameter.
# Missing parameter 8826 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1011006 NAME 'TEST1011006 Belge 1972 / Belge Lambert 72' DEFINITION 'PROJCS["Belge 1972 / Belge Lambert 72",GEOGCS["Belge 1972",DATUM["Reseau National Belge 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[-106.8686,52.2978,-103.7239,0.3366,-0.457,1.8422,-1.2747],AUTHORITY["EPSG","6313"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4313"]],PROJECTION["Lambert Conic Conformal (2SP Belgium)",AUTHORITY["EPSG","9803"]],PARAMETER["Latitude of false origin",90,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",4.35693972222222,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",49.8333333333333,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",51.1666666666667,AUTHORITY["EPSG","8824"]],PARAMETER["Northing at false origin",5400088.4378,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","31300"]]';
ERROR SR003: The spatial reference system definition for SRID 1011006 does not specify the mandatory false_easting (EPSG 8826) projection parameter.
# Missing parameter 8827 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1011007 NAME 'TEST1011007 Belge 1972 / Belge Lambert 72' DEFINITION 'PROJCS["Belge 1972 / Belge Lambert 72",GEOGCS["Belge 1972",DATUM["Reseau National Belge 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[-106.8686,52.2978,-103.7239,0.3366,-0.457,1.8422,-1.2747],AUTHORITY["EPSG","6313"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4313"]],PROJECTION["Lambert Conic Conformal (2SP Belgium)",AUTHORITY["EPSG","9803"]],PARAMETER["Latitude of false origin",90,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",4.35693972222222,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",49.8333333333333,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",51.1666666666667,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",150000.01256,AUTHORITY["EPSG","8826"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","31300"]]';
ERROR SR003: The spatial reference system definition for SRID 1011007 does not specify the mandatory false_northing (EPSG 8827) projection parameter.
#
# Projection: Mercator (variant A) (EPSG 9804).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1012000 NAME 'TEST1012000 Segara / NEIEZ' DEFINITION 'PROJCS["Segara / NEIEZ",GEOGCS["Segara",DATUM["Gunung Segara",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[-403,684,41,0,0,0,0],AUTHORITY["EPSG","6613"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4613"]],PROJECTION["Mercator (variant A)",AUTHORITY["EPSG","9804"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",110,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.997,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",3900000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",900000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3000"]]';
DROP SPATIAL REFERENCE SYSTEM 1012000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1012001 NAME 'TEST1012001 Segara / NEIEZ' DEFINITION 'PROJCS["Segara / NEIEZ",GEOGCS["Segara",DATUM["Gunung Segara",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[-403,684,41,0,0,0,0],AUTHORITY["EPSG","6613"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4613"]],PROJECTION["Mercator (variant A)",AUTHORITY["EPSG","9804"]],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",110],PARAMETER["scale_factor",0.997],PARAMETER["false_easting",3900000],PARAMETER["False_northing",900000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3000"]]';
DROP SPATIAL REFERENCE SYSTEM 1012001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1012002 NAME 'TEST1012002 Segara / NEIEZ' DEFINITION 'PROJCS["Segara / NEIEZ",GEOGCS["Segara",DATUM["Gunung Segara",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[-403,684,41,0,0,0,0],AUTHORITY["EPSG","6613"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4613"]],PROJECTION["Mercator (variant A)",AUTHORITY["EPSG","9804"]],PARAMETER["Longitude of natural origin",110,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.997,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",3900000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",900000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3000"]]';
ERROR SR003: The spatial reference system definition for SRID 1012002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1012003 NAME 'TEST1012003 Segara / NEIEZ' DEFINITION 'PROJCS["Segara / NEIEZ",GEOGCS["Segara",DATUM["Gunung Segara",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[-403,684,41,0,0,0,0],AUTHORITY["EPSG","6613"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4613"]],PROJECTION["Mercator (variant A)",AUTHORITY["EPSG","9804"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Scale factor at natural origin",0.997,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",3900000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",900000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3000"]]';
ERROR SR003: The spatial reference system definition for SRID 1012003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8805 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1012004 NAME 'TEST1012004 Segara / NEIEZ' DEFINITION 'PROJCS["Segara / NEIEZ",GEOGCS["Segara",DATUM["Gunung Segara",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[-403,684,41,0,0,0,0],AUTHORITY["EPSG","6613"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4613"]],PROJECTION["Mercator (variant A)",AUTHORITY["EPSG","9804"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",110,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",3900000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",900000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3000"]]';
ERROR SR003: The spatial reference system definition for SRID 1012004 does not specify the mandatory scale_factor (EPSG 8805) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1012005 NAME 'TEST1012005 Segara / NEIEZ' DEFINITION 'PROJCS["Segara / NEIEZ",GEOGCS["Segara",DATUM["Gunung Segara",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[-403,684,41,0,0,0,0],AUTHORITY["EPSG","6613"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4613"]],PROJECTION["Mercator (variant A)",AUTHORITY["EPSG","9804"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",110,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.997,AUTHORITY["EPSG","8805"]],PARAMETER["False northing",900000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3000"]]';
ERROR SR003: The spatial reference system definition for SRID 1012005 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1012006 NAME 'TEST1012006 Segara / NEIEZ' DEFINITION 'PROJCS["Segara / NEIEZ",GEOGCS["Segara",DATUM["Gunung Segara",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[-403,684,41,0,0,0,0],AUTHORITY["EPSG","6613"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4613"]],PROJECTION["Mercator (variant A)",AUTHORITY["EPSG","9804"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",110,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.997,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",3900000,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3000"]]';
ERROR SR003: The spatial reference system definition for SRID 1012006 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Mercator (variant B) (EPSG 9805).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1013000 NAME 'TEST1013000 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
DROP SPATIAL REFERENCE SYSTEM 1013000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1013001 NAME 'TEST1013001 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["latitude_of_center",46.9524055555556],PARAMETER["longitude_of_center",7.43958333333333],PARAMETER["azimuth",90],PARAMETER["rectified_grid_angle",90],PARAMETER["scale_factor",1],PARAMETER["false_easting",2600000],PARAMETER["false_northing",1200000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
DROP SPATIAL REFERENCE SYSTEM 1013001;
# Missing parameter 8811 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1013002 NAME 'TEST1013002 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1013002 does not specify the mandatory latitude_of_center (EPSG 8811) projection parameter.
# Missing parameter 8812 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1013003 NAME 'TEST1013003 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1013003 does not specify the mandatory longitude_of_center (EPSG 8812) projection parameter.
# Missing parameter 8813 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1013004 NAME 'TEST1013004 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1013004 does not specify the mandatory azimuth (EPSG 8813) projection parameter.
# Missing parameter 8814 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1013005 NAME 'TEST1013005 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1013005 does not specify the mandatory rectified_grid_angle (EPSG 8814) projection parameter.
# Missing parameter 8815 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1013006 NAME 'TEST1013006 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1013006 does not specify the mandatory scale_factor (EPSG 8815) projection parameter.
# Missing parameter 8816 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1013007 NAME 'TEST1013007 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1013007 does not specify the mandatory false_easting (EPSG 8816) projection parameter.
# Missing parameter 8817 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1013008 NAME 'TEST1013008 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1013008 does not specify the mandatory false_northing (EPSG 8817) projection parameter.
#
# Projection: Cassini-Soldner (EPSG 9806).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1014000 NAME 'TEST1014000 Mount Dillon / Tobago Grid' DEFINITION 'PROJCS["Mount Dillon / Tobago Grid",GEOGCS["Mount Dillon",DATUM["Mount Dillon",SPHEROID["Clarke 1858",6378293.645208759,294.26067636926103,AUTHORITY["EPSG","7007"]],AUTHORITY["EPSG","6157"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4157"]],PROJECTION["Cassini-Soldner",AUTHORITY["EPSG","9806"]],PARAMETER["Latitude of natural origin",11.2521786111111,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-60.6860088888889,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",187500,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",180000,AUTHORITY["EPSG","8807"]],UNIT["Clarke\'s link",0.201166195164,AUTHORITY["EPSG","9039"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2066"]]';
DROP SPATIAL REFERENCE SYSTEM 1014000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1014001 NAME 'TEST1014001 Mount Dillon / Tobago Grid' DEFINITION 'PROJCS["Mount Dillon / Tobago Grid",GEOGCS["Mount Dillon",DATUM["Mount Dillon",SPHEROID["Clarke 1858",6378293.645208759,294.26067636926103,AUTHORITY["EPSG","7007"]],AUTHORITY["EPSG","6157"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4157"]],PROJECTION["Cassini-Soldner",AUTHORITY["EPSG","9806"]],PARAMETER["latitude_of_origin",11.2521786111111],PARAMETER["central_meridian",-60.6860088888889],PARAMETER["false_easting",187500],PARAMETER["false_northing",180000],UNIT["Clarke\'s link",0.201166195164,AUTHORITY["EPSG","9039"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2066"]]';
DROP SPATIAL REFERENCE SYSTEM 1014001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1014002 NAME 'TEST1014002 Mount Dillon / Tobago Grid' DEFINITION 'PROJCS["Mount Dillon / Tobago Grid",GEOGCS["Mount Dillon",DATUM["Mount Dillon",SPHEROID["Clarke 1858",6378293.645208759,294.26067636926103,AUTHORITY["EPSG","7007"]],AUTHORITY["EPSG","6157"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4157"]],PROJECTION["Cassini-Soldner",AUTHORITY["EPSG","9806"]],PARAMETER["Longitude of natural origin",-60.6860088888889,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",187500,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",180000,AUTHORITY["EPSG","8807"]],UNIT["Clarke\'s link",0.201166195164,AUTHORITY["EPSG","9039"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2066"]]';
ERROR SR003: The spatial reference system definition for SRID 1014002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1014003 NAME 'TEST1014003 Mount Dillon / Tobago Grid' DEFINITION 'PROJCS["Mount Dillon / Tobago Grid",GEOGCS["Mount Dillon",DATUM["Mount Dillon",SPHEROID["Clarke 1858",6378293.645208759,294.26067636926103,AUTHORITY["EPSG","7007"]],AUTHORITY["EPSG","6157"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4157"]],PROJECTION["Cassini-Soldner",AUTHORITY["EPSG","9806"]],PARAMETER["Latitude of natural origin",11.2521786111111,AUTHORITY["EPSG","8801"]],PARAMETER["False easting",187500,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",180000,AUTHORITY["EPSG","8807"]],UNIT["Clarke\'s link",0.201166195164,AUTHORITY["EPSG","9039"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2066"]]';
ERROR SR003: The spatial reference system definition for SRID 1014003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1014004 NAME 'TEST1014004 Mount Dillon / Tobago Grid' DEFINITION 'PROJCS["Mount Dillon / Tobago Grid",GEOGCS["Mount Dillon",DATUM["Mount Dillon",SPHEROID["Clarke 1858",6378293.645208759,294.26067636926103,AUTHORITY["EPSG","7007"]],AUTHORITY["EPSG","6157"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4157"]],PROJECTION["Cassini-Soldner",AUTHORITY["EPSG","9806"]],PARAMETER["Latitude of natural origin",11.2521786111111,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-60.6860088888889,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",180000,AUTHORITY["EPSG","8807"]],UNIT["Clarke\'s link",0.201166195164,AUTHORITY["EPSG","9039"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2066"]]';
ERROR SR003: The spatial reference system definition for SRID 1014004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1014005 NAME 'TEST1014005 Mount Dillon / Tobago Grid' DEFINITION 'PROJCS["Mount Dillon / Tobago Grid",GEOGCS["Mount Dillon",DATUM["Mount Dillon",SPHEROID["Clarke 1858",6378293.645208759,294.26067636926103,AUTHORITY["EPSG","7007"]],AUTHORITY["EPSG","6157"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4157"]],PROJECTION["Cassini-Soldner",AUTHORITY["EPSG","9806"]],PARAMETER["Latitude of natural origin",11.2521786111111,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-60.6860088888889,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",187500,AUTHORITY["EPSG","8806"]],UNIT["Clarke\'s link",0.201166195164,AUTHORITY["EPSG","9039"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2066"]]';
ERROR SR003: The spatial reference system definition for SRID 1014005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Transverse Mercator (EPSG 9807).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1015000 NAME 'TEST1015000 Anguilla 1957 / British West Indies Grid' DEFINITION 'PROJCS["Anguilla 1957 / British West Indies Grid",GEOGCS["Anguilla 1957",DATUM["Anguilla 1957",SPHEROID["Clarke 1880 (RGS)",6378249.145,293.465,AUTHORITY["EPSG","7012"]],AUTHORITY["EPSG","6600"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4600"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-62,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9995,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",400000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2000"]]';
DROP SPATIAL REFERENCE SYSTEM 1015000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1015001 NAME 'TEST1015001 Anguilla 1957 / British West Indies Grid' DEFINITION 'PROJCS["Anguilla 1957 / British West Indies Grid",GEOGCS["Anguilla 1957",DATUM["Anguilla 1957",SPHEROID["Clarke 1880 (RGS)",6378249.145,293.465,AUTHORITY["EPSG","7012"]],AUTHORITY["EPSG","6600"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4600"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",-62],PARAMETER["scale_factor",0.9995],PARAMETER["false_easting",400000],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2000"]]';
DROP SPATIAL REFERENCE SYSTEM 1015001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1015002 NAME 'TEST1015002 Anguilla 1957 / British West Indies Grid' DEFINITION 'PROJCS["Anguilla 1957 / British West Indies Grid",GEOGCS["Anguilla 1957",DATUM["Anguilla 1957",SPHEROID["Clarke 1880 (RGS)",6378249.145,293.465,AUTHORITY["EPSG","7012"]],AUTHORITY["EPSG","6600"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4600"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Longitude of natural origin",-62,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9995,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",400000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2000"]]';
ERROR SR003: The spatial reference system definition for SRID 1015002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1015003 NAME 'TEST1015003 Anguilla 1957 / British West Indies Grid' DEFINITION 'PROJCS["Anguilla 1957 / British West Indies Grid",GEOGCS["Anguilla 1957",DATUM["Anguilla 1957",SPHEROID["Clarke 1880 (RGS)",6378249.145,293.465,AUTHORITY["EPSG","7012"]],AUTHORITY["EPSG","6600"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4600"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Scale factor at natural origin",0.9995,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",400000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2000"]]';
ERROR SR003: The spatial reference system definition for SRID 1015003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8805 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1015004 NAME 'TEST1015004 Anguilla 1957 / British West Indies Grid' DEFINITION 'PROJCS["Anguilla 1957 / British West Indies Grid",GEOGCS["Anguilla 1957",DATUM["Anguilla 1957",SPHEROID["Clarke 1880 (RGS)",6378249.145,293.465,AUTHORITY["EPSG","7012"]],AUTHORITY["EPSG","6600"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4600"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-62,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",400000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2000"]]';
ERROR SR003: The spatial reference system definition for SRID 1015004 does not specify the mandatory scale_factor (EPSG 8805) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1015005 NAME 'TEST1015005 Anguilla 1957 / British West Indies Grid' DEFINITION 'PROJCS["Anguilla 1957 / British West Indies Grid",GEOGCS["Anguilla 1957",DATUM["Anguilla 1957",SPHEROID["Clarke 1880 (RGS)",6378249.145,293.465,AUTHORITY["EPSG","7012"]],AUTHORITY["EPSG","6600"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4600"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-62,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9995,AUTHORITY["EPSG","8805"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2000"]]';
ERROR SR003: The spatial reference system definition for SRID 1015005 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1015006 NAME 'TEST1015006 Anguilla 1957 / British West Indies Grid' DEFINITION 'PROJCS["Anguilla 1957 / British West Indies Grid",GEOGCS["Anguilla 1957",DATUM["Anguilla 1957",SPHEROID["Clarke 1880 (RGS)",6378249.145,293.465,AUTHORITY["EPSG","7012"]],AUTHORITY["EPSG","6600"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4600"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-62,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9995,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",400000,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2000"]]';
ERROR SR003: The spatial reference system definition for SRID 1015006 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Transverse Mercator (South Orientated) (EPSG 9808).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1016000 NAME 'TEST1016000 Hartebeesthoek94 / Lo15' DEFINITION 'PROJCS["Hartebeesthoek94 / Lo15",GEOGCS["Hartebeesthoek94",DATUM["Hartebeesthoek94",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6148"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4148"]],PROJECTION["Transverse Mercator (South Orientated)",AUTHORITY["EPSG","9808"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",15,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",1,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",WEST],AXIS["X",SOUTH],AUTHORITY["EPSG","2046"]]';
DROP SPATIAL REFERENCE SYSTEM 1016000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1016001 NAME 'TEST1016001 Hartebeesthoek94 / Lo15' DEFINITION 'PROJCS["Hartebeesthoek94 / Lo15",GEOGCS["Hartebeesthoek94",DATUM["Hartebeesthoek94",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6148"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4148"]],PROJECTION["Transverse Mercator (South Orientated)",AUTHORITY["EPSG","9808"]],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",15],PARAMETER["scale_factor",1],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",WEST],AXIS["X",SOUTH],AUTHORITY["EPSG","2046"]]';
DROP SPATIAL REFERENCE SYSTEM 1016001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1016002 NAME 'TEST1016002 Hartebeesthoek94 / Lo15' DEFINITION 'PROJCS["Hartebeesthoek94 / Lo15",GEOGCS["Hartebeesthoek94",DATUM["Hartebeesthoek94",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6148"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4148"]],PROJECTION["Transverse Mercator (South Orientated)",AUTHORITY["EPSG","9808"]],PARAMETER["Longitude of natural origin",15,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",1,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",WEST],AXIS["X",SOUTH],AUTHORITY["EPSG","2046"]]';
ERROR SR003: The spatial reference system definition for SRID 1016002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1016003 NAME 'TEST1016003 Hartebeesthoek94 / Lo15' DEFINITION 'PROJCS["Hartebeesthoek94 / Lo15",GEOGCS["Hartebeesthoek94",DATUM["Hartebeesthoek94",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6148"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4148"]],PROJECTION["Transverse Mercator (South Orientated)",AUTHORITY["EPSG","9808"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Scale factor at natural origin",1,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",WEST],AXIS["X",SOUTH],AUTHORITY["EPSG","2046"]]';
ERROR SR003: The spatial reference system definition for SRID 1016003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8805 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1016004 NAME 'TEST1016004 Hartebeesthoek94 / Lo15' DEFINITION 'PROJCS["Hartebeesthoek94 / Lo15",GEOGCS["Hartebeesthoek94",DATUM["Hartebeesthoek94",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6148"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4148"]],PROJECTION["Transverse Mercator (South Orientated)",AUTHORITY["EPSG","9808"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",15,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",WEST],AXIS["X",SOUTH],AUTHORITY["EPSG","2046"]]';
ERROR SR003: The spatial reference system definition for SRID 1016004 does not specify the mandatory scale_factor (EPSG 8805) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1016005 NAME 'TEST1016005 Hartebeesthoek94 / Lo15' DEFINITION 'PROJCS["Hartebeesthoek94 / Lo15",GEOGCS["Hartebeesthoek94",DATUM["Hartebeesthoek94",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6148"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4148"]],PROJECTION["Transverse Mercator (South Orientated)",AUTHORITY["EPSG","9808"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",15,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",1,AUTHORITY["EPSG","8805"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",WEST],AXIS["X",SOUTH],AUTHORITY["EPSG","2046"]]';
ERROR SR003: The spatial reference system definition for SRID 1016005 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1016006 NAME 'TEST1016006 Hartebeesthoek94 / Lo15' DEFINITION 'PROJCS["Hartebeesthoek94 / Lo15",GEOGCS["Hartebeesthoek94",DATUM["Hartebeesthoek94",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6148"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4148"]],PROJECTION["Transverse Mercator (South Orientated)",AUTHORITY["EPSG","9808"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",15,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",1,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",WEST],AXIS["X",SOUTH],AUTHORITY["EPSG","2046"]]';
ERROR SR003: The spatial reference system definition for SRID 1016006 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Oblique Stereographic (EPSG 9809).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1017000 NAME 'TEST1017000 Pulkovo 1942(58) / Poland zone II' DEFINITION 'PROJCS["Pulkovo 1942(58) / Poland zone II",GEOGCS["Pulkovo 1942(58)",DATUM["Pulkovo 1942(58)",SPHEROID["Krassowsky 1940",6378245,298.3,AUTHORITY["EPSG","7024"]],TOWGS84[33.4,-146.6,-76.3,-0.359,-0.053,0.844,-0.84],AUTHORITY["EPSG","6179"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4179"]],PROJECTION["Oblique Stereographic",AUTHORITY["EPSG","9809"]],PARAMETER["Latitude of natural origin",53.0019444444444,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",21.5027777777778,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9998,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",4603000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5806000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","2172"]]';
DROP SPATIAL REFERENCE SYSTEM 1017000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1017001 NAME 'TEST1017001 Pulkovo 1942(58) / Poland zone II' DEFINITION 'PROJCS["Pulkovo 1942(58) / Poland zone II",GEOGCS["Pulkovo 1942(58)",DATUM["Pulkovo 1942(58)",SPHEROID["Krassowsky 1940",6378245,298.3,AUTHORITY["EPSG","7024"]],TOWGS84[33.4,-146.6,-76.3,-0.359,-0.053,0.844,-0.84],AUTHORITY["EPSG","6179"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4179"]],PROJECTION["Oblique Stereographic",AUTHORITY["EPSG","9809"]],PARAMETER["latitude_of_origin",53.0019444444444],PARAMETER["central_meridian",21.5027777777778],PARAMETER["scale_factor",0.9998],PARAMETER["false_easting",4603000],PARAMETER["false_northing",5806000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","2172"]]';
DROP SPATIAL REFERENCE SYSTEM 1017001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1017002 NAME 'TEST1017002 Pulkovo 1942(58) / Poland zone II' DEFINITION 'PROJCS["Pulkovo 1942(58) / Poland zone II",GEOGCS["Pulkovo 1942(58)",DATUM["Pulkovo 1942(58)",SPHEROID["Krassowsky 1940",6378245,298.3,AUTHORITY["EPSG","7024"]],TOWGS84[33.4,-146.6,-76.3,-0.359,-0.053,0.844,-0.84],AUTHORITY["EPSG","6179"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4179"]],PROJECTION["Oblique Stereographic",AUTHORITY["EPSG","9809"]],PARAMETER["Longitude of natural origin",21.5027777777778,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9998,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",4603000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5806000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","2172"]]';
ERROR SR003: The spatial reference system definition for SRID 1017002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1017003 NAME 'TEST1017003 Pulkovo 1942(58) / Poland zone II' DEFINITION 'PROJCS["Pulkovo 1942(58) / Poland zone II",GEOGCS["Pulkovo 1942(58)",DATUM["Pulkovo 1942(58)",SPHEROID["Krassowsky 1940",6378245,298.3,AUTHORITY["EPSG","7024"]],TOWGS84[33.4,-146.6,-76.3,-0.359,-0.053,0.844,-0.84],AUTHORITY["EPSG","6179"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4179"]],PROJECTION["Oblique Stereographic",AUTHORITY["EPSG","9809"]],PARAMETER["Latitude of natural origin",53.0019444444444,AUTHORITY["EPSG","8801"]],PARAMETER["Scale factor at natural origin",0.9998,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",4603000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5806000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","2172"]]';
ERROR SR003: The spatial reference system definition for SRID 1017003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8805 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1017004 NAME 'TEST1017004 Pulkovo 1942(58) / Poland zone II' DEFINITION 'PROJCS["Pulkovo 1942(58) / Poland zone II",GEOGCS["Pulkovo 1942(58)",DATUM["Pulkovo 1942(58)",SPHEROID["Krassowsky 1940",6378245,298.3,AUTHORITY["EPSG","7024"]],TOWGS84[33.4,-146.6,-76.3,-0.359,-0.053,0.844,-0.84],AUTHORITY["EPSG","6179"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4179"]],PROJECTION["Oblique Stereographic",AUTHORITY["EPSG","9809"]],PARAMETER["Latitude of natural origin",53.0019444444444,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",21.5027777777778,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",4603000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",5806000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","2172"]]';
ERROR SR003: The spatial reference system definition for SRID 1017004 does not specify the mandatory scale_factor (EPSG 8805) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1017005 NAME 'TEST1017005 Pulkovo 1942(58) / Poland zone II' DEFINITION 'PROJCS["Pulkovo 1942(58) / Poland zone II",GEOGCS["Pulkovo 1942(58)",DATUM["Pulkovo 1942(58)",SPHEROID["Krassowsky 1940",6378245,298.3,AUTHORITY["EPSG","7024"]],TOWGS84[33.4,-146.6,-76.3,-0.359,-0.053,0.844,-0.84],AUTHORITY["EPSG","6179"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4179"]],PROJECTION["Oblique Stereographic",AUTHORITY["EPSG","9809"]],PARAMETER["Latitude of natural origin",53.0019444444444,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",21.5027777777778,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9998,AUTHORITY["EPSG","8805"]],PARAMETER["False northing",5806000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","2172"]]';
ERROR SR003: The spatial reference system definition for SRID 1017005 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1017006 NAME 'TEST1017006 Pulkovo 1942(58) / Poland zone II' DEFINITION 'PROJCS["Pulkovo 1942(58) / Poland zone II",GEOGCS["Pulkovo 1942(58)",DATUM["Pulkovo 1942(58)",SPHEROID["Krassowsky 1940",6378245,298.3,AUTHORITY["EPSG","7024"]],TOWGS84[33.4,-146.6,-76.3,-0.359,-0.053,0.844,-0.84],AUTHORITY["EPSG","6179"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4179"]],PROJECTION["Oblique Stereographic",AUTHORITY["EPSG","9809"]],PARAMETER["Latitude of natural origin",53.0019444444444,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",21.5027777777778,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9998,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",4603000,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","2172"]]';
ERROR SR003: The spatial reference system definition for SRID 1017006 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Polar Stereographic (variant A) (EPSG 9810).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1018000 NAME 'TEST1018000 WGS 84 / UPS North (E,N)' DEFINITION 'PROJCS["WGS 84 / UPS North (E,N)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant A)",AUTHORITY["EPSG","9810"]],PARAMETER["Latitude of natural origin",90,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.994,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",2000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",2000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",SOUTH],AXIS["N",SOUTH],AUTHORITY["EPSG","5041"]]';
DROP SPATIAL REFERENCE SYSTEM 1018000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1018001 NAME 'TEST1018001 WGS 84 / UPS North (E,N)' DEFINITION 'PROJCS["WGS 84 / UPS North (E,N)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant A)",AUTHORITY["EPSG","9810"]],PARAMETER["latitude_of_origin",90],PARAMETER["central_meridian",0],PARAMETER["scale_factor",0.994],PARAMETER["false_easting",2000000],PARAMETER["false_northing",2000000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",SOUTH],AXIS["N",SOUTH],AUTHORITY["EPSG","5041"]]';
DROP SPATIAL REFERENCE SYSTEM 1018001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1018002 NAME 'TEST1018002 WGS 84 / UPS North (E,N)' DEFINITION 'PROJCS["WGS 84 / UPS North (E,N)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant A)",AUTHORITY["EPSG","9810"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.994,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",2000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",2000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",SOUTH],AXIS["N",SOUTH],AUTHORITY["EPSG","5041"]]';
ERROR SR003: The spatial reference system definition for SRID 1018002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1018003 NAME 'TEST1018003 WGS 84 / UPS North (E,N)' DEFINITION 'PROJCS["WGS 84 / UPS North (E,N)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant A)",AUTHORITY["EPSG","9810"]],PARAMETER["Latitude of natural origin",90,AUTHORITY["EPSG","8801"]],PARAMETER["Scale factor at natural origin",0.994,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",2000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",2000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",SOUTH],AXIS["N",SOUTH],AUTHORITY["EPSG","5041"]]';
ERROR SR003: The spatial reference system definition for SRID 1018003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8805 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1018004 NAME 'TEST1018004 WGS 84 / UPS North (E,N)' DEFINITION 'PROJCS["WGS 84 / UPS North (E,N)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant A)",AUTHORITY["EPSG","9810"]],PARAMETER["Latitude of natural origin",90,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",2000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",2000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",SOUTH],AXIS["N",SOUTH],AUTHORITY["EPSG","5041"]]';
ERROR SR003: The spatial reference system definition for SRID 1018004 does not specify the mandatory scale_factor (EPSG 8805) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1018005 NAME 'TEST1018005 WGS 84 / UPS North (E,N)' DEFINITION 'PROJCS["WGS 84 / UPS North (E,N)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant A)",AUTHORITY["EPSG","9810"]],PARAMETER["Latitude of natural origin",90,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.994,AUTHORITY["EPSG","8805"]],PARAMETER["False northing",2000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",SOUTH],AXIS["N",SOUTH],AUTHORITY["EPSG","5041"]]';
ERROR SR003: The spatial reference system definition for SRID 1018005 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1018006 NAME 'TEST1018006 WGS 84 / UPS North (E,N)' DEFINITION 'PROJCS["WGS 84 / UPS North (E,N)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant A)",AUTHORITY["EPSG","9810"]],PARAMETER["Latitude of natural origin",90,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.994,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",2000000,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",SOUTH],AXIS["N",SOUTH],AUTHORITY["EPSG","5041"]]';
ERROR SR003: The spatial reference system definition for SRID 1018006 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: New Zealand Map Grid (EPSG 9811).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1019000 NAME 'TEST1019000 NZGD49 / New Zealand Map Grid' DEFINITION 'PROJCS["NZGD49 / New Zealand Map Grid",GEOGCS["NZGD49",DATUM["New Zealand Geodetic Datum 1949",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993],AUTHORITY["EPSG","6272"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4272"]],PROJECTION["New Zealand Map Grid",AUTHORITY["EPSG","9811"]],PARAMETER["Latitude of natural origin",-41,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",173,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",2510000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",6023150,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","27200"]]';
DROP SPATIAL REFERENCE SYSTEM 1019000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1019001 NAME 'TEST1019001 NZGD49 / New Zealand Map Grid' DEFINITION 'PROJCS["NZGD49 / New Zealand Map Grid",GEOGCS["NZGD49",DATUM["New Zealand Geodetic Datum 1949",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993],AUTHORITY["EPSG","6272"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4272"]],PROJECTION["New Zealand Map Grid",AUTHORITY["EPSG","9811"]],PARAMETER["latitude_of_origin",-41],PARAMETER["central_meridian",173],PARAMETER["false_easting",2510000],PARAMETER["false_northing",6023150],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","27200"]]';
DROP SPATIAL REFERENCE SYSTEM 1019001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1019002 NAME 'TEST1019002 NZGD49 / New Zealand Map Grid' DEFINITION 'PROJCS["NZGD49 / New Zealand Map Grid",GEOGCS["NZGD49",DATUM["New Zealand Geodetic Datum 1949",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993],AUTHORITY["EPSG","6272"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4272"]],PROJECTION["New Zealand Map Grid",AUTHORITY["EPSG","9811"]],PARAMETER["Longitude of natural origin",173,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",2510000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",6023150,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","27200"]]';
ERROR SR003: The spatial reference system definition for SRID 1019002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1019003 NAME 'TEST1019003 NZGD49 / New Zealand Map Grid' DEFINITION 'PROJCS["NZGD49 / New Zealand Map Grid",GEOGCS["NZGD49",DATUM["New Zealand Geodetic Datum 1949",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993],AUTHORITY["EPSG","6272"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4272"]],PROJECTION["New Zealand Map Grid",AUTHORITY["EPSG","9811"]],PARAMETER["Latitude of natural origin",-41,AUTHORITY["EPSG","8801"]],PARAMETER["False easting",2510000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",6023150,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","27200"]]';
ERROR SR003: The spatial reference system definition for SRID 1019003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1019004 NAME 'TEST1019004 NZGD49 / New Zealand Map Grid' DEFINITION 'PROJCS["NZGD49 / New Zealand Map Grid",GEOGCS["NZGD49",DATUM["New Zealand Geodetic Datum 1949",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993],AUTHORITY["EPSG","6272"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4272"]],PROJECTION["New Zealand Map Grid",AUTHORITY["EPSG","9811"]],PARAMETER["Latitude of natural origin",-41,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",173,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",6023150,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","27200"]]';
ERROR SR003: The spatial reference system definition for SRID 1019004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1019005 NAME 'TEST1019005 NZGD49 / New Zealand Map Grid' DEFINITION 'PROJCS["NZGD49 / New Zealand Map Grid",GEOGCS["NZGD49",DATUM["New Zealand Geodetic Datum 1949",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993],AUTHORITY["EPSG","6272"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4272"]],PROJECTION["New Zealand Map Grid",AUTHORITY["EPSG","9811"]],PARAMETER["Latitude of natural origin",-41,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",173,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",2510000,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","27200"]]';
ERROR SR003: The spatial reference system definition for SRID 1019005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Hotine Oblique Mercator (variant A) (EPSG 9812).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1020000 NAME 'TEST1020000 NAD83 / Michigan Oblique Mercator' DEFINITION 'PROJCS["NAD83 / Michigan Oblique Mercator",GEOGCS["NAD83",DATUM["North American Datum 1983",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[1,1,-1,0,0,0,0],AUTHORITY["EPSG","6269"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4269"]],PROJECTION["Hotine Oblique Mercator (variant A)",AUTHORITY["EPSG","9812"]],PARAMETER["Latitude of projection centre",45.3091666666667,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",-86,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",337.25556,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",337.25556,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",0.9996,AUTHORITY["EPSG","8815"]],PARAMETER["False easting",2546731.496,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",-4354009.816,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3078"]]';
DROP SPATIAL REFERENCE SYSTEM 1020000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1020001 NAME 'TEST1020001 NAD83 / Michigan Oblique Mercator' DEFINITION 'PROJCS["NAD83 / Michigan Oblique Mercator",GEOGCS["NAD83",DATUM["North American Datum 1983",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[1,1,-1,0,0,0,0],AUTHORITY["EPSG","6269"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4269"]],PROJECTION["Hotine Oblique Mercator (variant A)",AUTHORITY["EPSG","9812"]],PARAMETER["latitude_of_center",45.3091666666667],PARAMETER["longitude_of_center",-86],PARAMETER["azimuth",337.25556],PARAMETER["rectified_grid_angle",337.25556],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",2546731.496],PARAMETER["false_northing",-4354009.816],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3078"]]';
DROP SPATIAL REFERENCE SYSTEM 1020001;
# Missing parameter 8811 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1020002 NAME 'TEST1020002 NAD83 / Michigan Oblique Mercator' DEFINITION 'PROJCS["NAD83 / Michigan Oblique Mercator",GEOGCS["NAD83",DATUM["North American Datum 1983",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[1,1,-1,0,0,0,0],AUTHORITY["EPSG","6269"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4269"]],PROJECTION["Hotine Oblique Mercator (variant A)",AUTHORITY["EPSG","9812"]],PARAMETER["Longitude of projection centre",-86,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",337.25556,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",337.25556,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",0.9996,AUTHORITY["EPSG","8815"]],PARAMETER["False easting",2546731.496,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",-4354009.816,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3078"]]';
ERROR SR003: The spatial reference system definition for SRID 1020002 does not specify the mandatory latitude_of_center (EPSG 8811) projection parameter.
# Missing parameter 8812 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1020003 NAME 'TEST1020003 NAD83 / Michigan Oblique Mercator' DEFINITION 'PROJCS["NAD83 / Michigan Oblique Mercator",GEOGCS["NAD83",DATUM["North American Datum 1983",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[1,1,-1,0,0,0,0],AUTHORITY["EPSG","6269"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4269"]],PROJECTION["Hotine Oblique Mercator (variant A)",AUTHORITY["EPSG","9812"]],PARAMETER["Latitude of projection centre",45.3091666666667,AUTHORITY["EPSG","8811"]],PARAMETER["Azimuth of initial line",337.25556,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",337.25556,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",0.9996,AUTHORITY["EPSG","8815"]],PARAMETER["False easting",2546731.496,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",-4354009.816,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3078"]]';
ERROR SR003: The spatial reference system definition for SRID 1020003 does not specify the mandatory longitude_of_center (EPSG 8812) projection parameter.
# Missing parameter 8813 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1020004 NAME 'TEST1020004 NAD83 / Michigan Oblique Mercator' DEFINITION 'PROJCS["NAD83 / Michigan Oblique Mercator",GEOGCS["NAD83",DATUM["North American Datum 1983",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[1,1,-1,0,0,0,0],AUTHORITY["EPSG","6269"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4269"]],PROJECTION["Hotine Oblique Mercator (variant A)",AUTHORITY["EPSG","9812"]],PARAMETER["Latitude of projection centre",45.3091666666667,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",-86,AUTHORITY["EPSG","8812"]],PARAMETER["Angle from Rectified to Skew Grid",337.25556,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",0.9996,AUTHORITY["EPSG","8815"]],PARAMETER["False easting",2546731.496,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",-4354009.816,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3078"]]';
ERROR SR003: The spatial reference system definition for SRID 1020004 does not specify the mandatory azimuth (EPSG 8813) projection parameter.
# Missing parameter 8814 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1020005 NAME 'TEST1020005 NAD83 / Michigan Oblique Mercator' DEFINITION 'PROJCS["NAD83 / Michigan Oblique Mercator",GEOGCS["NAD83",DATUM["North American Datum 1983",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[1,1,-1,0,0,0,0],AUTHORITY["EPSG","6269"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4269"]],PROJECTION["Hotine Oblique Mercator (variant A)",AUTHORITY["EPSG","9812"]],PARAMETER["Latitude of projection centre",45.3091666666667,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",-86,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",337.25556,AUTHORITY["EPSG","8813"]],PARAMETER["Scale factor on initial line",0.9996,AUTHORITY["EPSG","8815"]],PARAMETER["False easting",2546731.496,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",-4354009.816,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3078"]]';
ERROR SR003: The spatial reference system definition for SRID 1020005 does not specify the mandatory rectified_grid_angle (EPSG 8814) projection parameter.
# Missing parameter 8815 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1020006 NAME 'TEST1020006 NAD83 / Michigan Oblique Mercator' DEFINITION 'PROJCS["NAD83 / Michigan Oblique Mercator",GEOGCS["NAD83",DATUM["North American Datum 1983",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[1,1,-1,0,0,0,0],AUTHORITY["EPSG","6269"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4269"]],PROJECTION["Hotine Oblique Mercator (variant A)",AUTHORITY["EPSG","9812"]],PARAMETER["Latitude of projection centre",45.3091666666667,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",-86,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",337.25556,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",337.25556,AUTHORITY["EPSG","8814"]],PARAMETER["False easting",2546731.496,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",-4354009.816,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3078"]]';
ERROR SR003: The spatial reference system definition for SRID 1020006 does not specify the mandatory scale_factor (EPSG 8815) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1020007 NAME 'TEST1020007 NAD83 / Michigan Oblique Mercator' DEFINITION 'PROJCS["NAD83 / Michigan Oblique Mercator",GEOGCS["NAD83",DATUM["North American Datum 1983",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[1,1,-1,0,0,0,0],AUTHORITY["EPSG","6269"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4269"]],PROJECTION["Hotine Oblique Mercator (variant A)",AUTHORITY["EPSG","9812"]],PARAMETER["Latitude of projection centre",45.3091666666667,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",-86,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",337.25556,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",337.25556,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",0.9996,AUTHORITY["EPSG","8815"]],PARAMETER["False northing",-4354009.816,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3078"]]';
ERROR SR003: The spatial reference system definition for SRID 1020007 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1020008 NAME 'TEST1020008 NAD83 / Michigan Oblique Mercator' DEFINITION 'PROJCS["NAD83 / Michigan Oblique Mercator",GEOGCS["NAD83",DATUM["North American Datum 1983",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[1,1,-1,0,0,0,0],AUTHORITY["EPSG","6269"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4269"]],PROJECTION["Hotine Oblique Mercator (variant A)",AUTHORITY["EPSG","9812"]],PARAMETER["Latitude of projection centre",45.3091666666667,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",-86,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",337.25556,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",337.25556,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",0.9996,AUTHORITY["EPSG","8815"]],PARAMETER["False easting",2546731.496,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3078"]]';
ERROR SR003: The spatial reference system definition for SRID 1020008 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Laborde Oblique Mercator (EPSG 9813).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1021000 NAME 'TEST1021000 Tananarive (Paris) / Laborde Grid' DEFINITION 'PROJCS["Tananarive (Paris) / Laborde Grid",GEOGCS["Tananarive (Paris)",DATUM["Tananarive 1925 (Paris)",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],AUTHORITY["EPSG","6810"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4810"]],PROJECTION["Laborde Oblique Mercator",AUTHORITY["EPSG","9813"]],PARAMETER["Latitude of projection centre",-21,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",49,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",21,AUTHORITY["EPSG","8813"]],PARAMETER["Scale factor on initial line",0.9995,AUTHORITY["EPSG","8815"]],PARAMETER["False easting",400000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",800000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","29701"]]';
DROP SPATIAL REFERENCE SYSTEM 1021000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1021001 NAME 'TEST1021001 Tananarive (Paris) / Laborde Grid' DEFINITION 'PROJCS["Tananarive (Paris) / Laborde Grid",GEOGCS["Tananarive (Paris)",DATUM["Tananarive 1925 (Paris)",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],AUTHORITY["EPSG","6810"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4810"]],PROJECTION["Laborde Oblique Mercator",AUTHORITY["EPSG","9813"]],PARAMETER["latitude_of_center",-21],PARAMETER["longitude_of_center",49],PARAMETER["azimuth",21],PARAMETER["scale_factor",0.9995],PARAMETER["false_easting",400000],PARAMETER["false_northing",800000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","29701"]]';
DROP SPATIAL REFERENCE SYSTEM 1021001;
# Missing parameter 8811 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1021002 NAME 'TEST1021002 Tananarive (Paris) / Laborde Grid' DEFINITION 'PROJCS["Tananarive (Paris) / Laborde Grid",GEOGCS["Tananarive (Paris)",DATUM["Tananarive 1925 (Paris)",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],AUTHORITY["EPSG","6810"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4810"]],PROJECTION["Laborde Oblique Mercator",AUTHORITY["EPSG","9813"]],PARAMETER["Longitude of projection centre",49,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",21,AUTHORITY["EPSG","8813"]],PARAMETER["Scale factor on initial line",0.9995,AUTHORITY["EPSG","8815"]],PARAMETER["False easting",400000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",800000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","29701"]]';
ERROR SR003: The spatial reference system definition for SRID 1021002 does not specify the mandatory latitude_of_center (EPSG 8811) projection parameter.
# Missing parameter 8812 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1021003 NAME 'TEST1021003 Tananarive (Paris) / Laborde Grid' DEFINITION 'PROJCS["Tananarive (Paris) / Laborde Grid",GEOGCS["Tananarive (Paris)",DATUM["Tananarive 1925 (Paris)",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],AUTHORITY["EPSG","6810"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4810"]],PROJECTION["Laborde Oblique Mercator",AUTHORITY["EPSG","9813"]],PARAMETER["Latitude of projection centre",-21,AUTHORITY["EPSG","8811"]],PARAMETER["Azimuth of initial line",21,AUTHORITY["EPSG","8813"]],PARAMETER["Scale factor on initial line",0.9995,AUTHORITY["EPSG","8815"]],PARAMETER["False easting",400000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",800000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","29701"]]';
ERROR SR003: The spatial reference system definition for SRID 1021003 does not specify the mandatory longitude_of_center (EPSG 8812) projection parameter.
# Missing parameter 8813 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1021004 NAME 'TEST1021004 Tananarive (Paris) / Laborde Grid' DEFINITION 'PROJCS["Tananarive (Paris) / Laborde Grid",GEOGCS["Tananarive (Paris)",DATUM["Tananarive 1925 (Paris)",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],AUTHORITY["EPSG","6810"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4810"]],PROJECTION["Laborde Oblique Mercator",AUTHORITY["EPSG","9813"]],PARAMETER["Latitude of projection centre",-21,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",49,AUTHORITY["EPSG","8812"]],PARAMETER["Scale factor on initial line",0.9995,AUTHORITY["EPSG","8815"]],PARAMETER["False easting",400000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",800000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","29701"]]';
ERROR SR003: The spatial reference system definition for SRID 1021004 does not specify the mandatory azimuth (EPSG 8813) projection parameter.
# Missing parameter 8815 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1021005 NAME 'TEST1021005 Tananarive (Paris) / Laborde Grid' DEFINITION 'PROJCS["Tananarive (Paris) / Laborde Grid",GEOGCS["Tananarive (Paris)",DATUM["Tananarive 1925 (Paris)",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],AUTHORITY["EPSG","6810"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4810"]],PROJECTION["Laborde Oblique Mercator",AUTHORITY["EPSG","9813"]],PARAMETER["Latitude of projection centre",-21,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",49,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",21,AUTHORITY["EPSG","8813"]],PARAMETER["False easting",400000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",800000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","29701"]]';
ERROR SR003: The spatial reference system definition for SRID 1021005 does not specify the mandatory scale_factor (EPSG 8815) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1021006 NAME 'TEST1021006 Tananarive (Paris) / Laborde Grid' DEFINITION 'PROJCS["Tananarive (Paris) / Laborde Grid",GEOGCS["Tananarive (Paris)",DATUM["Tananarive 1925 (Paris)",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],AUTHORITY["EPSG","6810"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4810"]],PROJECTION["Laborde Oblique Mercator",AUTHORITY["EPSG","9813"]],PARAMETER["Latitude of projection centre",-21,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",49,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",21,AUTHORITY["EPSG","8813"]],PARAMETER["Scale factor on initial line",0.9995,AUTHORITY["EPSG","8815"]],PARAMETER["False northing",800000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","29701"]]';
ERROR SR003: The spatial reference system definition for SRID 1021006 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1021007 NAME 'TEST1021007 Tananarive (Paris) / Laborde Grid' DEFINITION 'PROJCS["Tananarive (Paris) / Laborde Grid",GEOGCS["Tananarive (Paris)",DATUM["Tananarive 1925 (Paris)",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],AUTHORITY["EPSG","6810"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4810"]],PROJECTION["Laborde Oblique Mercator",AUTHORITY["EPSG","9813"]],PARAMETER["Latitude of projection centre",-21,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",49,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",21,AUTHORITY["EPSG","8813"]],PARAMETER["Scale factor on initial line",0.9995,AUTHORITY["EPSG","8815"]],PARAMETER["False easting",400000,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","29701"]]';
ERROR SR003: The spatial reference system definition for SRID 1021007 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Hotine Oblique Mercator (variant B) (EPSG 9815).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1022000 NAME 'TEST1022000 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
DROP SPATIAL REFERENCE SYSTEM 1022000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1022001 NAME 'TEST1022001 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["latitude_of_center",46.9524055555556],PARAMETER["longitude_of_center",7.43958333333333],PARAMETER["azimuth",90],PARAMETER["rectified_grid_angle",90],PARAMETER["scale_factor",1],PARAMETER["false_easting",2600000],PARAMETER["false_northing",1200000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
DROP SPATIAL REFERENCE SYSTEM 1022001;
# Missing parameter 8811 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1022002 NAME 'TEST1022002 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1022002 does not specify the mandatory latitude_of_center (EPSG 8811) projection parameter.
# Missing parameter 8812 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1022003 NAME 'TEST1022003 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1022003 does not specify the mandatory longitude_of_center (EPSG 8812) projection parameter.
# Missing parameter 8813 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1022004 NAME 'TEST1022004 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1022004 does not specify the mandatory azimuth (EPSG 8813) projection parameter.
# Missing parameter 8814 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1022005 NAME 'TEST1022005 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1022005 does not specify the mandatory rectified_grid_angle (EPSG 8814) projection parameter.
# Missing parameter 8815 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1022006 NAME 'TEST1022006 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1022006 does not specify the mandatory scale_factor (EPSG 8815) projection parameter.
# Missing parameter 8816 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1022007 NAME 'TEST1022007 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Northing at projection centre",1200000,AUTHORITY["EPSG","8817"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1022007 does not specify the mandatory false_easting (EPSG 8816) projection parameter.
# Missing parameter 8817 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1022008 NAME 'TEST1022008 CH1903+ / LV95' DEFINITION 'PROJCS["CH1903+ / LV95",GEOGCS["CH1903+",DATUM["CH1903+",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],TOWGS84[674.374,15.056,405.346,0,0,0,0],AUTHORITY["EPSG","6150"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4150"]],PROJECTION["Hotine Oblique Mercator (variant B)",AUTHORITY["EPSG","9815"]],PARAMETER["Latitude of projection centre",46.9524055555556,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of projection centre",7.43958333333333,AUTHORITY["EPSG","8812"]],PARAMETER["Azimuth of initial line",90,AUTHORITY["EPSG","8813"]],PARAMETER["Angle from Rectified to Skew Grid",90,AUTHORITY["EPSG","8814"]],PARAMETER["Scale factor on initial line",1,AUTHORITY["EPSG","8815"]],PARAMETER["Easting at projection centre",2600000,AUTHORITY["EPSG","8816"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",EAST],AXIS["X",NORTH],AUTHORITY["EPSG","2056"]]';
ERROR SR003: The spatial reference system definition for SRID 1022008 does not specify the mandatory false_northing (EPSG 8817) projection parameter.
#
# Projection: Tunisia Mining Grid (EPSG 9816).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1023000 NAME 'TEST1023000 Carthage (Paris) / Tunisia Mining Grid' DEFINITION 'PROJCS["Carthage (Paris) / Tunisia Mining Grid",GEOGCS["Carthage (Paris)",DATUM["Carthage (Paris)",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],AUTHORITY["EPSG","6816"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4816"]],PROJECTION["Tunisia Mining Grid",AUTHORITY["EPSG","9816"]],PARAMETER["Latitude of false origin",36.5964,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",7.83445,AUTHORITY["EPSG","8822"]],PARAMETER["Easting at false origin",270,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",360,AUTHORITY["EPSG","8827"]],UNIT["kilometre",1000,AUTHORITY["EPSG","9036"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22300"]]';
DROP SPATIAL REFERENCE SYSTEM 1023000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1023001 NAME 'TEST1023001 Carthage (Paris) / Tunisia Mining Grid' DEFINITION 'PROJCS["Carthage (Paris) / Tunisia Mining Grid",GEOGCS["Carthage (Paris)",DATUM["Carthage (Paris)",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],AUTHORITY["EPSG","6816"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4816"]],PROJECTION["Tunisia Mining Grid",AUTHORITY["EPSG","9816"]],PARAMETER["latitude_of_origin",36.5964],PARAMETER["central_meridian",7.83445],PARAMETER["false_easting",270],PARAMETER["false_northing",360],UNIT["kilometre",1000,AUTHORITY["EPSG","9036"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22300"]]';
DROP SPATIAL REFERENCE SYSTEM 1023001;
# Missing parameter 8821 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1023002 NAME 'TEST1023002 Carthage (Paris) / Tunisia Mining Grid' DEFINITION 'PROJCS["Carthage (Paris) / Tunisia Mining Grid",GEOGCS["Carthage (Paris)",DATUM["Carthage (Paris)",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],AUTHORITY["EPSG","6816"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4816"]],PROJECTION["Tunisia Mining Grid",AUTHORITY["EPSG","9816"]],PARAMETER["Longitude of false origin",7.83445,AUTHORITY["EPSG","8822"]],PARAMETER["Easting at false origin",270,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",360,AUTHORITY["EPSG","8827"]],UNIT["kilometre",1000,AUTHORITY["EPSG","9036"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22300"]]';
ERROR SR003: The spatial reference system definition for SRID 1023002 does not specify the mandatory latitude_of_origin (EPSG 8821) projection parameter.
# Missing parameter 8822 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1023003 NAME 'TEST1023003 Carthage (Paris) / Tunisia Mining Grid' DEFINITION 'PROJCS["Carthage (Paris) / Tunisia Mining Grid",GEOGCS["Carthage (Paris)",DATUM["Carthage (Paris)",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],AUTHORITY["EPSG","6816"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4816"]],PROJECTION["Tunisia Mining Grid",AUTHORITY["EPSG","9816"]],PARAMETER["Latitude of false origin",36.5964,AUTHORITY["EPSG","8821"]],PARAMETER["Easting at false origin",270,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",360,AUTHORITY["EPSG","8827"]],UNIT["kilometre",1000,AUTHORITY["EPSG","9036"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22300"]]';
ERROR SR003: The spatial reference system definition for SRID 1023003 does not specify the mandatory central_meridian (EPSG 8822) projection parameter.
# Missing parameter 8826 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1023004 NAME 'TEST1023004 Carthage (Paris) / Tunisia Mining Grid' DEFINITION 'PROJCS["Carthage (Paris) / Tunisia Mining Grid",GEOGCS["Carthage (Paris)",DATUM["Carthage (Paris)",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],AUTHORITY["EPSG","6816"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4816"]],PROJECTION["Tunisia Mining Grid",AUTHORITY["EPSG","9816"]],PARAMETER["Latitude of false origin",36.5964,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",7.83445,AUTHORITY["EPSG","8822"]],PARAMETER["Northing at false origin",360,AUTHORITY["EPSG","8827"]],UNIT["kilometre",1000,AUTHORITY["EPSG","9036"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22300"]]';
ERROR SR003: The spatial reference system definition for SRID 1023004 does not specify the mandatory false_easting (EPSG 8826) projection parameter.
# Missing parameter 8827 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1023005 NAME 'TEST1023005 Carthage (Paris) / Tunisia Mining Grid' DEFINITION 'PROJCS["Carthage (Paris) / Tunisia Mining Grid",GEOGCS["Carthage (Paris)",DATUM["Carthage (Paris)",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],AUTHORITY["EPSG","6816"]],PRIMEM["Paris",2.5969213,AUTHORITY["EPSG","8903"]],UNIT["grad",0.01570796326794895,AUTHORITY["EPSG","9105"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4816"]],PROJECTION["Tunisia Mining Grid",AUTHORITY["EPSG","9816"]],PARAMETER["Latitude of false origin",36.5964,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",7.83445,AUTHORITY["EPSG","8822"]],PARAMETER["Easting at false origin",270,AUTHORITY["EPSG","8826"]],UNIT["kilometre",1000,AUTHORITY["EPSG","9036"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22300"]]';
ERROR SR003: The spatial reference system definition for SRID 1023005 does not specify the mandatory false_northing (EPSG 8827) projection parameter.
#
# Projection: Lambert Conic Near-Conformal (EPSG 9817).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1024000 NAME 'TEST1024000 Deir ez Zor / Levant Zone' DEFINITION 'PROJCS["Deir ez Zor / Levant Zone",GEOGCS["Deir ez Zor",DATUM["Deir ez Zor",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],TOWGS84[-83.58,-397.54,458.78,-17.595,-2.847,4.256,3.225],AUTHORITY["EPSG","6227"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4227"]],PROJECTION["Lambert Conic Near-Conformal",AUTHORITY["EPSG","9817"]],PARAMETER["Latitude of natural origin",34.65,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",37.35,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9996256,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",300000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",300000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22700"]]';
DROP SPATIAL REFERENCE SYSTEM 1024000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1024001 NAME 'TEST1024001 Deir ez Zor / Levant Zone' DEFINITION 'PROJCS["Deir ez Zor / Levant Zone",GEOGCS["Deir ez Zor",DATUM["Deir ez Zor",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],TOWGS84[-83.58,-397.54,458.78,-17.595,-2.847,4.256,3.225],AUTHORITY["EPSG","6227"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4227"]],PROJECTION["Lambert Conic Near-Conformal",AUTHORITY["EPSG","9817"]],PARAMETER["latitude_of_origin",34.65],PARAMETER["central_meridian",37.35],PARAMETER["scale_factor",0.9996256],PARAMETER["false_easting",300000],PARAMETER["false_northing",300000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22700"]]';
DROP SPATIAL REFERENCE SYSTEM 1024001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1024002 NAME 'TEST1024002 Deir ez Zor / Levant Zone' DEFINITION 'PROJCS["Deir ez Zor / Levant Zone",GEOGCS["Deir ez Zor",DATUM["Deir ez Zor",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],TOWGS84[-83.58,-397.54,458.78,-17.595,-2.847,4.256,3.225],AUTHORITY["EPSG","6227"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4227"]],PROJECTION["Lambert Conic Near-Conformal",AUTHORITY["EPSG","9817"]],PARAMETER["Longitude of natural origin",37.35,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9996256,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",300000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",300000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22700"]]';
ERROR SR003: The spatial reference system definition for SRID 1024002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1024003 NAME 'TEST1024003 Deir ez Zor / Levant Zone' DEFINITION 'PROJCS["Deir ez Zor / Levant Zone",GEOGCS["Deir ez Zor",DATUM["Deir ez Zor",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],TOWGS84[-83.58,-397.54,458.78,-17.595,-2.847,4.256,3.225],AUTHORITY["EPSG","6227"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4227"]],PROJECTION["Lambert Conic Near-Conformal",AUTHORITY["EPSG","9817"]],PARAMETER["Latitude of natural origin",34.65,AUTHORITY["EPSG","8801"]],PARAMETER["Scale factor at natural origin",0.9996256,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",300000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",300000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22700"]]';
ERROR SR003: The spatial reference system definition for SRID 1024003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8805 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1024004 NAME 'TEST1024004 Deir ez Zor / Levant Zone' DEFINITION 'PROJCS["Deir ez Zor / Levant Zone",GEOGCS["Deir ez Zor",DATUM["Deir ez Zor",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],TOWGS84[-83.58,-397.54,458.78,-17.595,-2.847,4.256,3.225],AUTHORITY["EPSG","6227"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4227"]],PROJECTION["Lambert Conic Near-Conformal",AUTHORITY["EPSG","9817"]],PARAMETER["Latitude of natural origin",34.65,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",37.35,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",300000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",300000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22700"]]';
ERROR SR003: The spatial reference system definition for SRID 1024004 does not specify the mandatory scale_factor (EPSG 8805) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1024005 NAME 'TEST1024005 Deir ez Zor / Levant Zone' DEFINITION 'PROJCS["Deir ez Zor / Levant Zone",GEOGCS["Deir ez Zor",DATUM["Deir ez Zor",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],TOWGS84[-83.58,-397.54,458.78,-17.595,-2.847,4.256,3.225],AUTHORITY["EPSG","6227"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4227"]],PROJECTION["Lambert Conic Near-Conformal",AUTHORITY["EPSG","9817"]],PARAMETER["Latitude of natural origin",34.65,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",37.35,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9996256,AUTHORITY["EPSG","8805"]],PARAMETER["False northing",300000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22700"]]';
ERROR SR003: The spatial reference system definition for SRID 1024005 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1024006 NAME 'TEST1024006 Deir ez Zor / Levant Zone' DEFINITION 'PROJCS["Deir ez Zor / Levant Zone",GEOGCS["Deir ez Zor",DATUM["Deir ez Zor",SPHEROID["Clarke 1880 (IGN)",6378249.2,293.4660212936269,AUTHORITY["EPSG","7011"]],TOWGS84[-83.58,-397.54,458.78,-17.595,-2.847,4.256,3.225],AUTHORITY["EPSG","6227"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4227"]],PROJECTION["Lambert Conic Near-Conformal",AUTHORITY["EPSG","9817"]],PARAMETER["Latitude of natural origin",34.65,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",37.35,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9996256,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",300000,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","22700"]]';
ERROR SR003: The spatial reference system definition for SRID 1024006 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: American Polyconic (EPSG 9818).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1025000 NAME 'TEST1025000 Panama-Colon 1911 / Panama Polyconic' DEFINITION 'PROJCS["Panama-Colon 1911 / Panama Polyconic",GEOGCS["Panama-Colon 1911",DATUM["Panama-Colon 1911",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","1072"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5467"]],PROJECTION["American Polyconic",AUTHORITY["EPSG","9818"]],PARAMETER["Latitude of natural origin",8.25,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-81,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",1000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",1092972.1,AUTHORITY["EPSG","8807"]],UNIT["Clarke\'s yard",0.9143917962,AUTHORITY["EPSG","9037"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","5472"]]';
DROP SPATIAL REFERENCE SYSTEM 1025000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1025001 NAME 'TEST1025001 Panama-Colon 1911 / Panama Polyconic' DEFINITION 'PROJCS["Panama-Colon 1911 / Panama Polyconic",GEOGCS["Panama-Colon 1911",DATUM["Panama-Colon 1911",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","1072"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5467"]],PROJECTION["American Polyconic",AUTHORITY["EPSG","9818"]],PARAMETER["latitude_of_origin",8.25],PARAMETER["central_meridian",-81],PARAMETER["false_easting",1000000],PARAMETER["false_northing",1092972.1],UNIT["Clarke\'s yard",0.9143917962,AUTHORITY["EPSG","9037"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","5472"]]';
DROP SPATIAL REFERENCE SYSTEM 1025001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1025002 NAME 'TEST1025002 Panama-Colon 1911 / Panama Polyconic' DEFINITION 'PROJCS["Panama-Colon 1911 / Panama Polyconic",GEOGCS["Panama-Colon 1911",DATUM["Panama-Colon 1911",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","1072"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5467"]],PROJECTION["American Polyconic",AUTHORITY["EPSG","9818"]],PARAMETER["Longitude of natural origin",-81,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",1000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",1092972.1,AUTHORITY["EPSG","8807"]],UNIT["Clarke\'s yard",0.9143917962,AUTHORITY["EPSG","9037"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","5472"]]';
ERROR SR003: The spatial reference system definition for SRID 1025002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1025003 NAME 'TEST1025003 Panama-Colon 1911 / Panama Polyconic' DEFINITION 'PROJCS["Panama-Colon 1911 / Panama Polyconic",GEOGCS["Panama-Colon 1911",DATUM["Panama-Colon 1911",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","1072"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5467"]],PROJECTION["American Polyconic",AUTHORITY["EPSG","9818"]],PARAMETER["Latitude of natural origin",8.25,AUTHORITY["EPSG","8801"]],PARAMETER["False easting",1000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",1092972.1,AUTHORITY["EPSG","8807"]],UNIT["Clarke\'s yard",0.9143917962,AUTHORITY["EPSG","9037"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","5472"]]';
ERROR SR003: The spatial reference system definition for SRID 1025003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1025004 NAME 'TEST1025004 Panama-Colon 1911 / Panama Polyconic' DEFINITION 'PROJCS["Panama-Colon 1911 / Panama Polyconic",GEOGCS["Panama-Colon 1911",DATUM["Panama-Colon 1911",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","1072"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5467"]],PROJECTION["American Polyconic",AUTHORITY["EPSG","9818"]],PARAMETER["Latitude of natural origin",8.25,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-81,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",1092972.1,AUTHORITY["EPSG","8807"]],UNIT["Clarke\'s yard",0.9143917962,AUTHORITY["EPSG","9037"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","5472"]]';
ERROR SR003: The spatial reference system definition for SRID 1025004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1025005 NAME 'TEST1025005 Panama-Colon 1911 / Panama Polyconic' DEFINITION 'PROJCS["Panama-Colon 1911 / Panama Polyconic",GEOGCS["Panama-Colon 1911",DATUM["Panama-Colon 1911",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],AUTHORITY["EPSG","1072"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","5467"]],PROJECTION["American Polyconic",AUTHORITY["EPSG","9818"]],PARAMETER["Latitude of natural origin",8.25,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-81,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",1000000,AUTHORITY["EPSG","8806"]],UNIT["Clarke\'s yard",0.9143917962,AUTHORITY["EPSG","9037"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","5472"]]';
ERROR SR003: The spatial reference system definition for SRID 1025005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Krovak (EPSG 9819).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1026000 NAME 'TEST1026000 S-JTSK (Ferro) / Krovak' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak",AUTHORITY["EPSG","9819"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","2065"]]';
DROP SPATIAL REFERENCE SYSTEM 1026000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1026001 NAME 'TEST1026001 S-JTSK (Ferro) / Krovak' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak",AUTHORITY["EPSG","9819"]],PARAMETER["latitude_of_center",49.5111111111111],PARAMETER["longitude_of_center",42.5111111111111],PARAMETER["azimuth",30.2881397222222],PARAMETER["pseudo_standard_parallel_1",78.5111111111111],PARAMETER["scale_factor",0.9999],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","2065"]]';
DROP SPATIAL REFERENCE SYSTEM 1026001;
# Missing parameter 8811 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1026002 NAME 'TEST1026002 S-JTSK (Ferro) / Krovak' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak",AUTHORITY["EPSG","9819"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","2065"]]';
ERROR SR003: The spatial reference system definition for SRID 1026002 does not specify the mandatory latitude_of_center (EPSG 8811) projection parameter.
# Missing parameter 8833 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1026003 NAME 'TEST1026003 S-JTSK (Ferro) / Krovak' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak",AUTHORITY["EPSG","9819"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","2065"]]';
ERROR SR003: The spatial reference system definition for SRID 1026003 does not specify the mandatory longitude_of_center (EPSG 8833) projection parameter.
# Missing parameter 1036 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1026004 NAME 'TEST1026004 S-JTSK (Ferro) / Krovak' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak",AUTHORITY["EPSG","9819"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","2065"]]';
ERROR SR003: The spatial reference system definition for SRID 1026004 does not specify the mandatory azimuth (EPSG 1036) projection parameter.
# Missing parameter 8818 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1026005 NAME 'TEST1026005 S-JTSK (Ferro) / Krovak' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak",AUTHORITY["EPSG","9819"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","2065"]]';
ERROR SR003: The spatial reference system definition for SRID 1026005 does not specify the mandatory pseudo_standard_parallel_1 (EPSG 8818) projection parameter.
# Missing parameter 8819 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1026006 NAME 'TEST1026006 S-JTSK (Ferro) / Krovak' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak",AUTHORITY["EPSG","9819"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","2065"]]';
ERROR SR003: The spatial reference system definition for SRID 1026006 does not specify the mandatory scale_factor (EPSG 8819) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1026007 NAME 'TEST1026007 S-JTSK (Ferro) / Krovak' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak",AUTHORITY["EPSG","9819"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","2065"]]';
ERROR SR003: The spatial reference system definition for SRID 1026007 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1026008 NAME 'TEST1026008 S-JTSK (Ferro) / Krovak' DEFINITION 'PROJCS["S-JTSK (Ferro) / Krovak",GEOGCS["S-JTSK (Ferro)",DATUM["System Jednotne Trigonometricke Site Katastralni (Ferro)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6818"]],PRIMEM["Ferro",-17.677777777777774,AUTHORITY["EPSG","8909"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4818"]],PROJECTION["Krovak",AUTHORITY["EPSG","9819"]],PARAMETER["Latitude of projection centre",49.5111111111111,AUTHORITY["EPSG","8811"]],PARAMETER["Longitude of origin",42.5111111111111,AUTHORITY["EPSG","8833"]],PARAMETER["Co-latitude of cone axis",30.2881397222222,AUTHORITY["EPSG","1036"]],PARAMETER["Latitude of pseudo standard parallel",78.5111111111111,AUTHORITY["EPSG","8818"]],PARAMETER["Scale factor on pseudo standard parallel",0.9999,AUTHORITY["EPSG","8819"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",SOUTH],AXIS["Y",WEST],AUTHORITY["EPSG","2065"]]';
ERROR SR003: The spatial reference system definition for SRID 1026008 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Lambert Azimuthal Equal Area (EPSG 9820).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1027000 NAME 'TEST1027000 ETRS89 / LAEA Europe' DEFINITION 'PROJCS["ETRS89 / LAEA Europe",GEOGCS["ETRS89",DATUM["European Terrestrial Reference System 1989",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6258"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4258"]],PROJECTION["Lambert Azimuthal Equal Area",AUTHORITY["EPSG","9820"]],PARAMETER["Latitude of natural origin",52,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",10,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",4321000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",3210000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",EAST],AUTHORITY["EPSG","3035"]]';
DROP SPATIAL REFERENCE SYSTEM 1027000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1027001 NAME 'TEST1027001 ETRS89 / LAEA Europe' DEFINITION 'PROJCS["ETRS89 / LAEA Europe",GEOGCS["ETRS89",DATUM["European Terrestrial Reference System 1989",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6258"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4258"]],PROJECTION["Lambert Azimuthal Equal Area",AUTHORITY["EPSG","9820"]],PARAMETER["latitude_of_origin",52],PARAMETER["central_meridian",10],PARAMETER["false_easting",4321000],PARAMETER["false_northing",3210000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",EAST],AUTHORITY["EPSG","3035"]]';
DROP SPATIAL REFERENCE SYSTEM 1027001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1027002 NAME 'TEST1027002 ETRS89 / LAEA Europe' DEFINITION 'PROJCS["ETRS89 / LAEA Europe",GEOGCS["ETRS89",DATUM["European Terrestrial Reference System 1989",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6258"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4258"]],PROJECTION["Lambert Azimuthal Equal Area",AUTHORITY["EPSG","9820"]],PARAMETER["Longitude of natural origin",10,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",4321000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",3210000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",EAST],AUTHORITY["EPSG","3035"]]';
ERROR SR003: The spatial reference system definition for SRID 1027002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1027003 NAME 'TEST1027003 ETRS89 / LAEA Europe' DEFINITION 'PROJCS["ETRS89 / LAEA Europe",GEOGCS["ETRS89",DATUM["European Terrestrial Reference System 1989",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6258"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4258"]],PROJECTION["Lambert Azimuthal Equal Area",AUTHORITY["EPSG","9820"]],PARAMETER["Latitude of natural origin",52,AUTHORITY["EPSG","8801"]],PARAMETER["False easting",4321000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",3210000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",EAST],AUTHORITY["EPSG","3035"]]';
ERROR SR003: The spatial reference system definition for SRID 1027003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1027004 NAME 'TEST1027004 ETRS89 / LAEA Europe' DEFINITION 'PROJCS["ETRS89 / LAEA Europe",GEOGCS["ETRS89",DATUM["European Terrestrial Reference System 1989",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6258"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4258"]],PROJECTION["Lambert Azimuthal Equal Area",AUTHORITY["EPSG","9820"]],PARAMETER["Latitude of natural origin",52,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",10,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",3210000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",EAST],AUTHORITY["EPSG","3035"]]';
ERROR SR003: The spatial reference system definition for SRID 1027004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1027005 NAME 'TEST1027005 ETRS89 / LAEA Europe' DEFINITION 'PROJCS["ETRS89 / LAEA Europe",GEOGCS["ETRS89",DATUM["European Terrestrial Reference System 1989",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6258"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4258"]],PROJECTION["Lambert Azimuthal Equal Area",AUTHORITY["EPSG","9820"]],PARAMETER["Latitude of natural origin",52,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",10,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",4321000,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",EAST],AUTHORITY["EPSG","3035"]]';
ERROR SR003: The spatial reference system definition for SRID 1027005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Albers Equal Area (EPSG 9822).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1028000 NAME 'TEST1028000 NAD27 / Alaska Albers' DEFINITION 'PROJCS["NAD27 / Alaska Albers",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Albers Equal Area",AUTHORITY["EPSG","9822"]],PARAMETER["Latitude of false origin",50,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-154,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",55,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",65,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",0,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2964"]]';
DROP SPATIAL REFERENCE SYSTEM 1028000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1028001 NAME 'TEST1028001 NAD27 / Alaska Albers' DEFINITION 'PROJCS["NAD27 / Alaska Albers",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Albers Equal Area",AUTHORITY["EPSG","9822"]],PARAMETER["latitude_of_origin",50],PARAMETER["central_meridian",-154],PARAMETER["standard_parallel_1",55],PARAMETER["standard_parallel_2",65],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2964"]]';
DROP SPATIAL REFERENCE SYSTEM 1028001;
# Missing parameter 8821 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1028002 NAME 'TEST1028002 NAD27 / Alaska Albers' DEFINITION 'PROJCS["NAD27 / Alaska Albers",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Albers Equal Area",AUTHORITY["EPSG","9822"]],PARAMETER["Longitude of false origin",-154,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",55,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",65,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",0,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2964"]]';
ERROR SR003: The spatial reference system definition for SRID 1028002 does not specify the mandatory latitude_of_origin (EPSG 8821) projection parameter.
# Missing parameter 8822 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1028003 NAME 'TEST1028003 NAD27 / Alaska Albers' DEFINITION 'PROJCS["NAD27 / Alaska Albers",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Albers Equal Area",AUTHORITY["EPSG","9822"]],PARAMETER["Latitude of false origin",50,AUTHORITY["EPSG","8821"]],PARAMETER["Latitude of 1st standard parallel",55,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",65,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",0,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2964"]]';
ERROR SR003: The spatial reference system definition for SRID 1028003 does not specify the mandatory central_meridian (EPSG 8822) projection parameter.
# Missing parameter 8823 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1028004 NAME 'TEST1028004 NAD27 / Alaska Albers' DEFINITION 'PROJCS["NAD27 / Alaska Albers",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Albers Equal Area",AUTHORITY["EPSG","9822"]],PARAMETER["Latitude of false origin",50,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-154,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 2nd standard parallel",65,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",0,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2964"]]';
ERROR SR003: The spatial reference system definition for SRID 1028004 does not specify the mandatory standard_parallel_1 (EPSG 8823) projection parameter.
# Missing parameter 8824 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1028005 NAME 'TEST1028005 NAD27 / Alaska Albers' DEFINITION 'PROJCS["NAD27 / Alaska Albers",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Albers Equal Area",AUTHORITY["EPSG","9822"]],PARAMETER["Latitude of false origin",50,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-154,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",55,AUTHORITY["EPSG","8823"]],PARAMETER["Easting at false origin",0,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2964"]]';
ERROR SR003: The spatial reference system definition for SRID 1028005 does not specify the mandatory standard_parallel_2 (EPSG 8824) projection parameter.
# Missing parameter 8826 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1028006 NAME 'TEST1028006 NAD27 / Alaska Albers' DEFINITION 'PROJCS["NAD27 / Alaska Albers",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Albers Equal Area",AUTHORITY["EPSG","9822"]],PARAMETER["Latitude of false origin",50,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-154,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",55,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",65,AUTHORITY["EPSG","8824"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2964"]]';
ERROR SR003: The spatial reference system definition for SRID 1028006 does not specify the mandatory false_easting (EPSG 8826) projection parameter.
# Missing parameter 8827 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1028007 NAME 'TEST1028007 NAD27 / Alaska Albers' DEFINITION 'PROJCS["NAD27 / Alaska Albers",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Albers Equal Area",AUTHORITY["EPSG","9822"]],PARAMETER["Latitude of false origin",50,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-154,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",55,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",65,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",0,AUTHORITY["EPSG","8826"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","2964"]]';
ERROR SR003: The spatial reference system definition for SRID 1028007 does not specify the mandatory false_northing (EPSG 8827) projection parameter.
#
# Projection: Transverse Mercator Zoned Grid System (EPSG 9824).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1029000 NAME 'TEST1029000 WGS 84 / UTM grid system (northern hemisphere)' DEFINITION 'PROJCS["WGS 84 / UTM grid system (northern hemisphere)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator Zoned Grid System",AUTHORITY["EPSG","9824"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Initial longitude",-180,AUTHORITY["EPSG","8830"]],PARAMETER["Zone width",6,AUTHORITY["EPSG","8831"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","32600"]]';
DROP SPATIAL REFERENCE SYSTEM 1029000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1029001 NAME 'TEST1029001 WGS 84 / UTM grid system (northern hemisphere)' DEFINITION 'PROJCS["WGS 84 / UTM grid system (northern hemisphere)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator Zoned Grid System",AUTHORITY["EPSG","9824"]],PARAMETER["latitude_of_origin",0],PARAMETER["initial_longitude",-180],PARAMETER["zone_width",6],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","32600"]]';
DROP SPATIAL REFERENCE SYSTEM 1029001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1029002 NAME 'TEST1029002 WGS 84 / UTM grid system (northern hemisphere)' DEFINITION 'PROJCS["WGS 84 / UTM grid system (northern hemisphere)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator Zoned Grid System",AUTHORITY["EPSG","9824"]],PARAMETER["Initial longitude",-180,AUTHORITY["EPSG","8830"]],PARAMETER["Zone width",6,AUTHORITY["EPSG","8831"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","32600"]]';
ERROR SR003: The spatial reference system definition for SRID 1029002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8830 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1029003 NAME 'TEST1029003 WGS 84 / UTM grid system (northern hemisphere)' DEFINITION 'PROJCS["WGS 84 / UTM grid system (northern hemisphere)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator Zoned Grid System",AUTHORITY["EPSG","9824"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Zone width",6,AUTHORITY["EPSG","8831"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","32600"]]';
ERROR SR003: The spatial reference system definition for SRID 1029003 does not specify the mandatory initial_longitude (EPSG 8830) projection parameter.
# Missing parameter 8831 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1029004 NAME 'TEST1029004 WGS 84 / UTM grid system (northern hemisphere)' DEFINITION 'PROJCS["WGS 84 / UTM grid system (northern hemisphere)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator Zoned Grid System",AUTHORITY["EPSG","9824"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Initial longitude",-180,AUTHORITY["EPSG","8830"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","32600"]]';
ERROR SR003: The spatial reference system definition for SRID 1029004 does not specify the mandatory zone_width (EPSG 8831) projection parameter.
# Missing parameter 8805 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1029005 NAME 'TEST1029005 WGS 84 / UTM grid system (northern hemisphere)' DEFINITION 'PROJCS["WGS 84 / UTM grid system (northern hemisphere)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator Zoned Grid System",AUTHORITY["EPSG","9824"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Initial longitude",-180,AUTHORITY["EPSG","8830"]],PARAMETER["Zone width",6,AUTHORITY["EPSG","8831"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","32600"]]';
ERROR SR003: The spatial reference system definition for SRID 1029005 does not specify the mandatory scale_factor (EPSG 8805) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1029006 NAME 'TEST1029006 WGS 84 / UTM grid system (northern hemisphere)' DEFINITION 'PROJCS["WGS 84 / UTM grid system (northern hemisphere)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator Zoned Grid System",AUTHORITY["EPSG","9824"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Initial longitude",-180,AUTHORITY["EPSG","8830"]],PARAMETER["Zone width",6,AUTHORITY["EPSG","8831"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","32600"]]';
ERROR SR003: The spatial reference system definition for SRID 1029006 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1029007 NAME 'TEST1029007 WGS 84 / UTM grid system (northern hemisphere)' DEFINITION 'PROJCS["WGS 84 / UTM grid system (northern hemisphere)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator Zoned Grid System",AUTHORITY["EPSG","9824"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Initial longitude",-180,AUTHORITY["EPSG","8830"]],PARAMETER["Zone width",6,AUTHORITY["EPSG","8831"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","32600"]]';
ERROR SR003: The spatial reference system definition for SRID 1029007 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Lambert Conic Conformal (West Orientated) (EPSG 9826).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1030000 NAME 'TEST1030000 Scoresbysund 1952 / Greenland zone 5 east' DEFINITION 'PROJCS["Scoresbysund 1952 / Greenland zone 5 east",GEOGCS["Scoresbysund 1952",DATUM["Scoresbysund 1952",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[105,326,-102.5,0,0,0.814,-0.6],AUTHORITY["EPSG","6195"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4195"]],PROJECTION["Lambert Conic Conformal (West Orientated)",AUTHORITY["EPSG","9826"]],PARAMETER["Latitude of natural origin",70.5111111111111,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-24,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",1,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",WEST],AUTHORITY["EPSG","2218"]]';
DROP SPATIAL REFERENCE SYSTEM 1030000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1030001 NAME 'TEST1030001 Scoresbysund 1952 / Greenland zone 5 east' DEFINITION 'PROJCS["Scoresbysund 1952 / Greenland zone 5 east",GEOGCS["Scoresbysund 1952",DATUM["Scoresbysund 1952",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[105,326,-102.5,0,0,0.814,-0.6],AUTHORITY["EPSG","6195"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4195"]],PROJECTION["Lambert Conic Conformal (West Orientated)",AUTHORITY["EPSG","9826"]],PARAMETER["latitude_of_origin",70.5111111111111],PARAMETER["central_meridian",-24],PARAMETER["scale_factor",1],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",WEST],AUTHORITY["EPSG","2218"]]';
DROP SPATIAL REFERENCE SYSTEM 1030001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1030002 NAME 'TEST1030002 Scoresbysund 1952 / Greenland zone 5 east' DEFINITION 'PROJCS["Scoresbysund 1952 / Greenland zone 5 east",GEOGCS["Scoresbysund 1952",DATUM["Scoresbysund 1952",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[105,326,-102.5,0,0,0.814,-0.6],AUTHORITY["EPSG","6195"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4195"]],PROJECTION["Lambert Conic Conformal (West Orientated)",AUTHORITY["EPSG","9826"]],PARAMETER["Longitude of natural origin",-24,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",1,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",WEST],AUTHORITY["EPSG","2218"]]';
ERROR SR003: The spatial reference system definition for SRID 1030002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1030003 NAME 'TEST1030003 Scoresbysund 1952 / Greenland zone 5 east' DEFINITION 'PROJCS["Scoresbysund 1952 / Greenland zone 5 east",GEOGCS["Scoresbysund 1952",DATUM["Scoresbysund 1952",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[105,326,-102.5,0,0,0.814,-0.6],AUTHORITY["EPSG","6195"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4195"]],PROJECTION["Lambert Conic Conformal (West Orientated)",AUTHORITY["EPSG","9826"]],PARAMETER["Latitude of natural origin",70.5111111111111,AUTHORITY["EPSG","8801"]],PARAMETER["Scale factor at natural origin",1,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",WEST],AUTHORITY["EPSG","2218"]]';
ERROR SR003: The spatial reference system definition for SRID 1030003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8805 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1030004 NAME 'TEST1030004 Scoresbysund 1952 / Greenland zone 5 east' DEFINITION 'PROJCS["Scoresbysund 1952 / Greenland zone 5 east",GEOGCS["Scoresbysund 1952",DATUM["Scoresbysund 1952",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[105,326,-102.5,0,0,0.814,-0.6],AUTHORITY["EPSG","6195"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4195"]],PROJECTION["Lambert Conic Conformal (West Orientated)",AUTHORITY["EPSG","9826"]],PARAMETER["Latitude of natural origin",70.5111111111111,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-24,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",WEST],AUTHORITY["EPSG","2218"]]';
ERROR SR003: The spatial reference system definition for SRID 1030004 does not specify the mandatory scale_factor (EPSG 8805) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1030005 NAME 'TEST1030005 Scoresbysund 1952 / Greenland zone 5 east' DEFINITION 'PROJCS["Scoresbysund 1952 / Greenland zone 5 east",GEOGCS["Scoresbysund 1952",DATUM["Scoresbysund 1952",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[105,326,-102.5,0,0,0.814,-0.6],AUTHORITY["EPSG","6195"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4195"]],PROJECTION["Lambert Conic Conformal (West Orientated)",AUTHORITY["EPSG","9826"]],PARAMETER["Latitude of natural origin",70.5111111111111,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-24,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",1,AUTHORITY["EPSG","8805"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",WEST],AUTHORITY["EPSG","2218"]]';
ERROR SR003: The spatial reference system definition for SRID 1030005 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1030006 NAME 'TEST1030006 Scoresbysund 1952 / Greenland zone 5 east' DEFINITION 'PROJCS["Scoresbysund 1952 / Greenland zone 5 east",GEOGCS["Scoresbysund 1952",DATUM["Scoresbysund 1952",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[105,326,-102.5,0,0,0.814,-0.6],AUTHORITY["EPSG","6195"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4195"]],PROJECTION["Lambert Conic Conformal (West Orientated)",AUTHORITY["EPSG","9826"]],PARAMETER["Latitude of natural origin",70.5111111111111,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-24,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",1,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["Y",NORTH],AXIS["X",WEST],AUTHORITY["EPSG","2218"]]';
ERROR SR003: The spatial reference system definition for SRID 1030006 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Bonne (South Orientated) (EPSG 9828).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1031000 NAME 'TEST1031000 Lisbon 1890 (Lisbon) / Portugal Bonne' DEFINITION 'PROJCS["Lisbon 1890 (Lisbon) / Portugal Bonne",GEOGCS["Lisbon 1890 (Lisbon)",DATUM["Lisbon 1890 (Lisbon)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6904"]],PRIMEM["Lisbon",-9.131906111111112,AUTHORITY["EPSG","8902"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4904"]],PROJECTION["Bonne (South Orientated)",AUTHORITY["EPSG","9828"]],PARAMETER["Latitude of natural origin",39.6777777777778,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",1,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["P",SOUTH],AXIS["M",WEST],AUTHORITY["EPSG","2963"]]';
DROP SPATIAL REFERENCE SYSTEM 1031000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1031001 NAME 'TEST1031001 Lisbon 1890 (Lisbon) / Portugal Bonne' DEFINITION 'PROJCS["Lisbon 1890 (Lisbon) / Portugal Bonne",GEOGCS["Lisbon 1890 (Lisbon)",DATUM["Lisbon 1890 (Lisbon)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6904"]],PRIMEM["Lisbon",-9.131906111111112,AUTHORITY["EPSG","8902"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4904"]],PROJECTION["Bonne (South Orientated)",AUTHORITY["EPSG","9828"]],PARAMETER["latitude_of_origin",39.6777777777778],PARAMETER["central_meridian",1],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["P",SOUTH],AXIS["M",WEST],AUTHORITY["EPSG","2963"]]';
DROP SPATIAL REFERENCE SYSTEM 1031001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1031002 NAME 'TEST1031002 Lisbon 1890 (Lisbon) / Portugal Bonne' DEFINITION 'PROJCS["Lisbon 1890 (Lisbon) / Portugal Bonne",GEOGCS["Lisbon 1890 (Lisbon)",DATUM["Lisbon 1890 (Lisbon)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6904"]],PRIMEM["Lisbon",-9.131906111111112,AUTHORITY["EPSG","8902"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4904"]],PROJECTION["Bonne (South Orientated)",AUTHORITY["EPSG","9828"]],PARAMETER["Longitude of natural origin",1,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["P",SOUTH],AXIS["M",WEST],AUTHORITY["EPSG","2963"]]';
ERROR SR003: The spatial reference system definition for SRID 1031002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1031003 NAME 'TEST1031003 Lisbon 1890 (Lisbon) / Portugal Bonne' DEFINITION 'PROJCS["Lisbon 1890 (Lisbon) / Portugal Bonne",GEOGCS["Lisbon 1890 (Lisbon)",DATUM["Lisbon 1890 (Lisbon)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6904"]],PRIMEM["Lisbon",-9.131906111111112,AUTHORITY["EPSG","8902"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4904"]],PROJECTION["Bonne (South Orientated)",AUTHORITY["EPSG","9828"]],PARAMETER["Latitude of natural origin",39.6777777777778,AUTHORITY["EPSG","8801"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["P",SOUTH],AXIS["M",WEST],AUTHORITY["EPSG","2963"]]';
ERROR SR003: The spatial reference system definition for SRID 1031003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1031004 NAME 'TEST1031004 Lisbon 1890 (Lisbon) / Portugal Bonne' DEFINITION 'PROJCS["Lisbon 1890 (Lisbon) / Portugal Bonne",GEOGCS["Lisbon 1890 (Lisbon)",DATUM["Lisbon 1890 (Lisbon)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6904"]],PRIMEM["Lisbon",-9.131906111111112,AUTHORITY["EPSG","8902"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4904"]],PROJECTION["Bonne (South Orientated)",AUTHORITY["EPSG","9828"]],PARAMETER["Latitude of natural origin",39.6777777777778,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",1,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["P",SOUTH],AXIS["M",WEST],AUTHORITY["EPSG","2963"]]';
ERROR SR003: The spatial reference system definition for SRID 1031004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1031005 NAME 'TEST1031005 Lisbon 1890 (Lisbon) / Portugal Bonne' DEFINITION 'PROJCS["Lisbon 1890 (Lisbon) / Portugal Bonne",GEOGCS["Lisbon 1890 (Lisbon)",DATUM["Lisbon 1890 (Lisbon)",SPHEROID["Bessel 1841",6377397.155,299.1528128,AUTHORITY["EPSG","7004"]],AUTHORITY["EPSG","6904"]],PRIMEM["Lisbon",-9.131906111111112,AUTHORITY["EPSG","8902"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4904"]],PROJECTION["Bonne (South Orientated)",AUTHORITY["EPSG","9828"]],PARAMETER["Latitude of natural origin",39.6777777777778,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",1,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["P",SOUTH],AXIS["M",WEST],AUTHORITY["EPSG","2963"]]';
ERROR SR003: The spatial reference system definition for SRID 1031005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Polar Stereographic (variant B) (EPSG 9829).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1032000 NAME 'TEST1032000 WGS 84 / Antarctic Polar Stereographic' DEFINITION 'PROJCS["WGS 84 / Antarctic Polar Stereographic",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant B)",AUTHORITY["EPSG","9829"]],PARAMETER["Latitude of standard parallel",-71,AUTHORITY["EPSG","8832"]],PARAMETER["Longitude of origin",0,AUTHORITY["EPSG","8833"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",NORTH],AXIS["N",NORTH],AUTHORITY["EPSG","3031"]]';
DROP SPATIAL REFERENCE SYSTEM 1032000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1032001 NAME 'TEST1032001 WGS 84 / Antarctic Polar Stereographic' DEFINITION 'PROJCS["WGS 84 / Antarctic Polar Stereographic",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant B)",AUTHORITY["EPSG","9829"]],PARAMETER["standard_parallel",-71],PARAMETER["longitude_of_center",0],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",NORTH],AXIS["N",NORTH],AUTHORITY["EPSG","3031"]]';
DROP SPATIAL REFERENCE SYSTEM 1032001;
# Missing parameter 8832 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1032002 NAME 'TEST1032002 WGS 84 / Antarctic Polar Stereographic' DEFINITION 'PROJCS["WGS 84 / Antarctic Polar Stereographic",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant B)",AUTHORITY["EPSG","9829"]],PARAMETER["Longitude of origin",0,AUTHORITY["EPSG","8833"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",NORTH],AXIS["N",NORTH],AUTHORITY["EPSG","3031"]]';
ERROR SR003: The spatial reference system definition for SRID 1032002 does not specify the mandatory standard_parallel (EPSG 8832) projection parameter.
# Missing parameter 8833 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1032003 NAME 'TEST1032003 WGS 84 / Antarctic Polar Stereographic' DEFINITION 'PROJCS["WGS 84 / Antarctic Polar Stereographic",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant B)",AUTHORITY["EPSG","9829"]],PARAMETER["Latitude of standard parallel",-71,AUTHORITY["EPSG","8832"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",NORTH],AXIS["N",NORTH],AUTHORITY["EPSG","3031"]]';
ERROR SR003: The spatial reference system definition for SRID 1032003 does not specify the mandatory longitude_of_center (EPSG 8833) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1032004 NAME 'TEST1032004 WGS 84 / Antarctic Polar Stereographic' DEFINITION 'PROJCS["WGS 84 / Antarctic Polar Stereographic",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant B)",AUTHORITY["EPSG","9829"]],PARAMETER["Latitude of standard parallel",-71,AUTHORITY["EPSG","8832"]],PARAMETER["Longitude of origin",0,AUTHORITY["EPSG","8833"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",NORTH],AXIS["N",NORTH],AUTHORITY["EPSG","3031"]]';
ERROR SR003: The spatial reference system definition for SRID 1032004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1032005 NAME 'TEST1032005 WGS 84 / Antarctic Polar Stereographic' DEFINITION 'PROJCS["WGS 84 / Antarctic Polar Stereographic",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant B)",AUTHORITY["EPSG","9829"]],PARAMETER["Latitude of standard parallel",-71,AUTHORITY["EPSG","8832"]],PARAMETER["Longitude of origin",0,AUTHORITY["EPSG","8833"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",NORTH],AXIS["N",NORTH],AUTHORITY["EPSG","3031"]]';
ERROR SR003: The spatial reference system definition for SRID 1032005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Polar Stereographic (variant C) (EPSG 9830).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1033000 NAME 'TEST1033000 Petrels 1972 / Terre Adelie Polar Stereographic' DEFINITION 'PROJCS["Petrels 1972 / Terre Adelie Polar Stereographic",GEOGCS["Petrels 1972",DATUM["Petrels 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[365,194,166,0,0,0,0],AUTHORITY["EPSG","6636"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4636"]],PROJECTION["Polar Stereographic (variant C)",AUTHORITY["EPSG","9830"]],PARAMETER["Latitude of standard parallel",-67,AUTHORITY["EPSG","8832"]],PARAMETER["Longitude of origin",140,AUTHORITY["EPSG","8833"]],PARAMETER["Easting at false origin",300000,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",200000,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",NORTH],AUTHORITY["EPSG","2985"]]';
DROP SPATIAL REFERENCE SYSTEM 1033000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1033001 NAME 'TEST1033001 Petrels 1972 / Terre Adelie Polar Stereographic' DEFINITION 'PROJCS["Petrels 1972 / Terre Adelie Polar Stereographic",GEOGCS["Petrels 1972",DATUM["Petrels 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[365,194,166,0,0,0,0],AUTHORITY["EPSG","6636"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4636"]],PROJECTION["Polar Stereographic (variant C)",AUTHORITY["EPSG","9830"]],PARAMETER["standard_parallel",-67],PARAMETER["longitude_of_center",140],PARAMETER["false_easting",300000],PARAMETER["false_northing",200000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",NORTH],AUTHORITY["EPSG","2985"]]';
DROP SPATIAL REFERENCE SYSTEM 1033001;
# Missing parameter 8832 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1033002 NAME 'TEST1033002 Petrels 1972 / Terre Adelie Polar Stereographic' DEFINITION 'PROJCS["Petrels 1972 / Terre Adelie Polar Stereographic",GEOGCS["Petrels 1972",DATUM["Petrels 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[365,194,166,0,0,0,0],AUTHORITY["EPSG","6636"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4636"]],PROJECTION["Polar Stereographic (variant C)",AUTHORITY["EPSG","9830"]],PARAMETER["Longitude of origin",140,AUTHORITY["EPSG","8833"]],PARAMETER["Easting at false origin",300000,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",200000,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",NORTH],AUTHORITY["EPSG","2985"]]';
ERROR SR003: The spatial reference system definition for SRID 1033002 does not specify the mandatory standard_parallel (EPSG 8832) projection parameter.
# Missing parameter 8833 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1033003 NAME 'TEST1033003 Petrels 1972 / Terre Adelie Polar Stereographic' DEFINITION 'PROJCS["Petrels 1972 / Terre Adelie Polar Stereographic",GEOGCS["Petrels 1972",DATUM["Petrels 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[365,194,166,0,0,0,0],AUTHORITY["EPSG","6636"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4636"]],PROJECTION["Polar Stereographic (variant C)",AUTHORITY["EPSG","9830"]],PARAMETER["Latitude of standard parallel",-67,AUTHORITY["EPSG","8832"]],PARAMETER["Easting at false origin",300000,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",200000,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",NORTH],AUTHORITY["EPSG","2985"]]';
ERROR SR003: The spatial reference system definition for SRID 1033003 does not specify the mandatory longitude_of_center (EPSG 8833) projection parameter.
# Missing parameter 8826 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1033004 NAME 'TEST1033004 Petrels 1972 / Terre Adelie Polar Stereographic' DEFINITION 'PROJCS["Petrels 1972 / Terre Adelie Polar Stereographic",GEOGCS["Petrels 1972",DATUM["Petrels 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[365,194,166,0,0,0,0],AUTHORITY["EPSG","6636"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4636"]],PROJECTION["Polar Stereographic (variant C)",AUTHORITY["EPSG","9830"]],PARAMETER["Latitude of standard parallel",-67,AUTHORITY["EPSG","8832"]],PARAMETER["Longitude of origin",140,AUTHORITY["EPSG","8833"]],PARAMETER["Northing at false origin",200000,AUTHORITY["EPSG","8827"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",NORTH],AUTHORITY["EPSG","2985"]]';
ERROR SR003: The spatial reference system definition for SRID 1033004 does not specify the mandatory false_easting (EPSG 8826) projection parameter.
# Missing parameter 8827 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1033005 NAME 'TEST1033005 Petrels 1972 / Terre Adelie Polar Stereographic' DEFINITION 'PROJCS["Petrels 1972 / Terre Adelie Polar Stereographic",GEOGCS["Petrels 1972",DATUM["Petrels 1972",SPHEROID["International 1924",6378388,297,AUTHORITY["EPSG","7022"]],TOWGS84[365,194,166,0,0,0,0],AUTHORITY["EPSG","6636"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4636"]],PROJECTION["Polar Stereographic (variant C)",AUTHORITY["EPSG","9830"]],PARAMETER["Latitude of standard parallel",-67,AUTHORITY["EPSG","8832"]],PARAMETER["Longitude of origin",140,AUTHORITY["EPSG","8833"]],PARAMETER["Easting at false origin",300000,AUTHORITY["EPSG","8826"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",NORTH],AXIS["Y",NORTH],AUTHORITY["EPSG","2985"]]';
ERROR SR003: The spatial reference system definition for SRID 1033005 does not specify the mandatory false_northing (EPSG 8827) projection parameter.
#
# Projection: Guam Projection (EPSG 9831).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1034000 NAME 'TEST1034000 Guam 1963 / Guam SPCS' DEFINITION 'PROJCS["Guam 1963 / Guam SPCS",GEOGCS["Guam 1963",DATUM["Guam 1963",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-100,-248,259,0,0,0,0],AUTHORITY["EPSG","6675"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4675"]],PROJECTION["Guam Projection",AUTHORITY["EPSG","9831"]],PARAMETER["Latitude of natural origin",13.4724663527778,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",144.************,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",50000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",50000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3993"]]';
DROP SPATIAL REFERENCE SYSTEM 1034000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1034001 NAME 'TEST1034001 Guam 1963 / Guam SPCS' DEFINITION 'PROJCS["Guam 1963 / Guam SPCS",GEOGCS["Guam 1963",DATUM["Guam 1963",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-100,-248,259,0,0,0,0],AUTHORITY["EPSG","6675"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4675"]],PROJECTION["Guam Projection",AUTHORITY["EPSG","9831"]],PARAMETER["latitude_of_origin",13.4724663527778],PARAMETER["central_meridian",144.************],PARAMETER["false_easting",50000],PARAMETER["false_northing",50000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3993"]]';
DROP SPATIAL REFERENCE SYSTEM 1034001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1034002 NAME 'TEST1034002 Guam 1963 / Guam SPCS' DEFINITION 'PROJCS["Guam 1963 / Guam SPCS",GEOGCS["Guam 1963",DATUM["Guam 1963",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-100,-248,259,0,0,0,0],AUTHORITY["EPSG","6675"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4675"]],PROJECTION["Guam Projection",AUTHORITY["EPSG","9831"]],PARAMETER["Longitude of natural origin",144.************,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",50000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",50000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3993"]]';
ERROR SR003: The spatial reference system definition for SRID 1034002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1034003 NAME 'TEST1034003 Guam 1963 / Guam SPCS' DEFINITION 'PROJCS["Guam 1963 / Guam SPCS",GEOGCS["Guam 1963",DATUM["Guam 1963",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-100,-248,259,0,0,0,0],AUTHORITY["EPSG","6675"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4675"]],PROJECTION["Guam Projection",AUTHORITY["EPSG","9831"]],PARAMETER["Latitude of natural origin",13.4724663527778,AUTHORITY["EPSG","8801"]],PARAMETER["False easting",50000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",50000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3993"]]';
ERROR SR003: The spatial reference system definition for SRID 1034003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1034004 NAME 'TEST1034004 Guam 1963 / Guam SPCS' DEFINITION 'PROJCS["Guam 1963 / Guam SPCS",GEOGCS["Guam 1963",DATUM["Guam 1963",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-100,-248,259,0,0,0,0],AUTHORITY["EPSG","6675"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4675"]],PROJECTION["Guam Projection",AUTHORITY["EPSG","9831"]],PARAMETER["Latitude of natural origin",13.4724663527778,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",144.************,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",50000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3993"]]';
ERROR SR003: The spatial reference system definition for SRID 1034004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1034005 NAME 'TEST1034005 Guam 1963 / Guam SPCS' DEFINITION 'PROJCS["Guam 1963 / Guam SPCS",GEOGCS["Guam 1963",DATUM["Guam 1963",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-100,-248,259,0,0,0,0],AUTHORITY["EPSG","6675"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4675"]],PROJECTION["Guam Projection",AUTHORITY["EPSG","9831"]],PARAMETER["Latitude of natural origin",13.4724663527778,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",144.************,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",50000,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3993"]]';
ERROR SR003: The spatial reference system definition for SRID 1034005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Modified Azimuthal Equidistant (EPSG 9832).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1035000 NAME 'TEST1035000 Guam 1963 / Yap Islands' DEFINITION 'PROJCS["Guam 1963 / Yap Islands",GEOGCS["Guam 1963",DATUM["Guam 1963",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-100,-248,259,0,0,0,0],AUTHORITY["EPSG","6675"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4675"]],PROJECTION["Modified Azimuthal Equidistant",AUTHORITY["EPSG","9832"]],PARAMETER["Latitude of natural origin",9.54670833333333,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",138.168744444444,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",40000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",60000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3295"]]';
DROP SPATIAL REFERENCE SYSTEM 1035000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1035001 NAME 'TEST1035001 Guam 1963 / Yap Islands' DEFINITION 'PROJCS["Guam 1963 / Yap Islands",GEOGCS["Guam 1963",DATUM["Guam 1963",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-100,-248,259,0,0,0,0],AUTHORITY["EPSG","6675"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4675"]],PROJECTION["Modified Azimuthal Equidistant",AUTHORITY["EPSG","9832"]],PARAMETER["latitude_of_origin",9.54670833333333],PARAMETER["central_meridian",138.168744444444],PARAMETER["false_easting",40000],PARAMETER["false_northing",60000],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3295"]]';
DROP SPATIAL REFERENCE SYSTEM 1035001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1035002 NAME 'TEST1035002 Guam 1963 / Yap Islands' DEFINITION 'PROJCS["Guam 1963 / Yap Islands",GEOGCS["Guam 1963",DATUM["Guam 1963",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-100,-248,259,0,0,0,0],AUTHORITY["EPSG","6675"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4675"]],PROJECTION["Modified Azimuthal Equidistant",AUTHORITY["EPSG","9832"]],PARAMETER["Longitude of natural origin",138.168744444444,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",40000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",60000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3295"]]';
ERROR SR003: The spatial reference system definition for SRID 1035002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1035003 NAME 'TEST1035003 Guam 1963 / Yap Islands' DEFINITION 'PROJCS["Guam 1963 / Yap Islands",GEOGCS["Guam 1963",DATUM["Guam 1963",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-100,-248,259,0,0,0,0],AUTHORITY["EPSG","6675"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4675"]],PROJECTION["Modified Azimuthal Equidistant",AUTHORITY["EPSG","9832"]],PARAMETER["Latitude of natural origin",9.54670833333333,AUTHORITY["EPSG","8801"]],PARAMETER["False easting",40000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",60000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3295"]]';
ERROR SR003: The spatial reference system definition for SRID 1035003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1035004 NAME 'TEST1035004 Guam 1963 / Yap Islands' DEFINITION 'PROJCS["Guam 1963 / Yap Islands",GEOGCS["Guam 1963",DATUM["Guam 1963",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-100,-248,259,0,0,0,0],AUTHORITY["EPSG","6675"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4675"]],PROJECTION["Modified Azimuthal Equidistant",AUTHORITY["EPSG","9832"]],PARAMETER["Latitude of natural origin",9.54670833333333,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",138.168744444444,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",60000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3295"]]';
ERROR SR003: The spatial reference system definition for SRID 1035004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1035005 NAME 'TEST1035005 Guam 1963 / Yap Islands' DEFINITION 'PROJCS["Guam 1963 / Yap Islands",GEOGCS["Guam 1963",DATUM["Guam 1963",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-100,-248,259,0,0,0,0],AUTHORITY["EPSG","6675"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4675"]],PROJECTION["Modified Azimuthal Equidistant",AUTHORITY["EPSG","9832"]],PARAMETER["Latitude of natural origin",9.54670833333333,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",138.168744444444,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",40000,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3295"]]';
ERROR SR003: The spatial reference system definition for SRID 1035005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Hyperbolic Cassini-Soldner (EPSG 9833).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1036000 NAME 'TEST1036000 Vanua Levu 1915 / Vanua Levu Grid' DEFINITION 'PROJCS["Vanua Levu 1915 / Vanua Levu Grid",GEOGCS["Vanua Levu 1915",DATUM["Vanua Levu 1915",SPHEROID["Clarke 1880 (international foot)",6378306.3696,293.46630765562986,AUTHORITY["EPSG","7055"]],TOWGS84[51,391,-36,0,0,0,0],AUTHORITY["EPSG","6748"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4748"]],PROJECTION["Hyperbolic Cassini-Soldner",AUTHORITY["EPSG","9833"]],PARAMETER["Latitude of natural origin",-16.2611111111111,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",179.344444444444,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",1251331.8,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",1662888.5,AUTHORITY["EPSG","8807"]],UNIT["link",0.201168,AUTHORITY["EPSG","9098"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","3139"]]';
DROP SPATIAL REFERENCE SYSTEM 1036000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1036001 NAME 'TEST1036001 Vanua Levu 1915 / Vanua Levu Grid' DEFINITION 'PROJCS["Vanua Levu 1915 / Vanua Levu Grid",GEOGCS["Vanua Levu 1915",DATUM["Vanua Levu 1915",SPHEROID["Clarke 1880 (international foot)",6378306.3696,293.46630765562986,AUTHORITY["EPSG","7055"]],TOWGS84[51,391,-36,0,0,0,0],AUTHORITY["EPSG","6748"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4748"]],PROJECTION["Hyperbolic Cassini-Soldner",AUTHORITY["EPSG","9833"]],PARAMETER["latitude_of_origin",-16.2611111111111],PARAMETER["central_meridian",179.344444444444],PARAMETER["false_easting",1251331.8],PARAMETER["false_northing",1662888.5],UNIT["link",0.201168,AUTHORITY["EPSG","9098"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","3139"]]';
DROP SPATIAL REFERENCE SYSTEM 1036001;
# Missing parameter 8801 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1036002 NAME 'TEST1036002 Vanua Levu 1915 / Vanua Levu Grid' DEFINITION 'PROJCS["Vanua Levu 1915 / Vanua Levu Grid",GEOGCS["Vanua Levu 1915",DATUM["Vanua Levu 1915",SPHEROID["Clarke 1880 (international foot)",6378306.3696,293.46630765562986,AUTHORITY["EPSG","7055"]],TOWGS84[51,391,-36,0,0,0,0],AUTHORITY["EPSG","6748"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4748"]],PROJECTION["Hyperbolic Cassini-Soldner",AUTHORITY["EPSG","9833"]],PARAMETER["Longitude of natural origin",179.344444444444,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",1251331.8,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",1662888.5,AUTHORITY["EPSG","8807"]],UNIT["link",0.201168,AUTHORITY["EPSG","9098"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","3139"]]';
ERROR SR003: The spatial reference system definition for SRID 1036002 does not specify the mandatory latitude_of_origin (EPSG 8801) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1036003 NAME 'TEST1036003 Vanua Levu 1915 / Vanua Levu Grid' DEFINITION 'PROJCS["Vanua Levu 1915 / Vanua Levu Grid",GEOGCS["Vanua Levu 1915",DATUM["Vanua Levu 1915",SPHEROID["Clarke 1880 (international foot)",6378306.3696,293.46630765562986,AUTHORITY["EPSG","7055"]],TOWGS84[51,391,-36,0,0,0,0],AUTHORITY["EPSG","6748"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4748"]],PROJECTION["Hyperbolic Cassini-Soldner",AUTHORITY["EPSG","9833"]],PARAMETER["Latitude of natural origin",-16.2611111111111,AUTHORITY["EPSG","8801"]],PARAMETER["False easting",1251331.8,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",1662888.5,AUTHORITY["EPSG","8807"]],UNIT["link",0.201168,AUTHORITY["EPSG","9098"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","3139"]]';
ERROR SR003: The spatial reference system definition for SRID 1036003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1036004 NAME 'TEST1036004 Vanua Levu 1915 / Vanua Levu Grid' DEFINITION 'PROJCS["Vanua Levu 1915 / Vanua Levu Grid",GEOGCS["Vanua Levu 1915",DATUM["Vanua Levu 1915",SPHEROID["Clarke 1880 (international foot)",6378306.3696,293.46630765562986,AUTHORITY["EPSG","7055"]],TOWGS84[51,391,-36,0,0,0,0],AUTHORITY["EPSG","6748"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4748"]],PROJECTION["Hyperbolic Cassini-Soldner",AUTHORITY["EPSG","9833"]],PARAMETER["Latitude of natural origin",-16.2611111111111,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",179.344444444444,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",1662888.5,AUTHORITY["EPSG","8807"]],UNIT["link",0.201168,AUTHORITY["EPSG","9098"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","3139"]]';
ERROR SR003: The spatial reference system definition for SRID 1036004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1036005 NAME 'TEST1036005 Vanua Levu 1915 / Vanua Levu Grid' DEFINITION 'PROJCS["Vanua Levu 1915 / Vanua Levu Grid",GEOGCS["Vanua Levu 1915",DATUM["Vanua Levu 1915",SPHEROID["Clarke 1880 (international foot)",6378306.3696,293.46630765562986,AUTHORITY["EPSG","7055"]],TOWGS84[51,391,-36,0,0,0,0],AUTHORITY["EPSG","6748"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4748"]],PROJECTION["Hyperbolic Cassini-Soldner",AUTHORITY["EPSG","9833"]],PARAMETER["Latitude of natural origin",-16.2611111111111,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",179.344444444444,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",1251331.8,AUTHORITY["EPSG","8806"]],UNIT["link",0.201168,AUTHORITY["EPSG","9098"]],AXIS["X",NORTH],AXIS["Y",EAST],AUTHORITY["EPSG","3139"]]';
ERROR SR003: The spatial reference system definition for SRID 1036005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Lambert Cylindrical Equal Area (Spherical) (EPSG 9834).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1037000 NAME 'TEST1037000 NSIDC EASE-Grid Global' DEFINITION 'PROJCS["NSIDC EASE-Grid Global",GEOGCS["Unspecified datum based upon the International 1924 Authalic Sphere",DATUM["Not specified (based on International 1924 Authalic Sphere)",SPHEROID["International 1924 Authalic Sphere",6371228,0,AUTHORITY["EPSG","7057"]],AUTHORITY["EPSG","6053"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4053"]],PROJECTION["Lambert Cylindrical Equal Area (Spherical)",AUTHORITY["EPSG","9834"]],PARAMETER["Latitude of 1st standard parallel",30,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3410"]]';
DROP SPATIAL REFERENCE SYSTEM 1037000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1037001 NAME 'TEST1037001 NSIDC EASE-Grid Global' DEFINITION 'PROJCS["NSIDC EASE-Grid Global",GEOGCS["Unspecified datum based upon the International 1924 Authalic Sphere",DATUM["Not specified (based on International 1924 Authalic Sphere)",SPHEROID["International 1924 Authalic Sphere",6371228,0,AUTHORITY["EPSG","7057"]],AUTHORITY["EPSG","6053"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4053"]],PROJECTION["Lambert Cylindrical Equal Area (Spherical)",AUTHORITY["EPSG","9834"]],PARAMETER["standard_parallel_1",30],PARAMETER["central_meridian",0],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3410"]]';
DROP SPATIAL REFERENCE SYSTEM 1037001;
# Missing parameter 8823 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1037002 NAME 'TEST1037002 NSIDC EASE-Grid Global' DEFINITION 'PROJCS["NSIDC EASE-Grid Global",GEOGCS["Unspecified datum based upon the International 1924 Authalic Sphere",DATUM["Not specified (based on International 1924 Authalic Sphere)",SPHEROID["International 1924 Authalic Sphere",6371228,0,AUTHORITY["EPSG","7057"]],AUTHORITY["EPSG","6053"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4053"]],PROJECTION["Lambert Cylindrical Equal Area (Spherical)",AUTHORITY["EPSG","9834"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3410"]]';
ERROR SR003: The spatial reference system definition for SRID 1037002 does not specify the mandatory standard_parallel_1 (EPSG 8823) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1037003 NAME 'TEST1037003 NSIDC EASE-Grid Global' DEFINITION 'PROJCS["NSIDC EASE-Grid Global",GEOGCS["Unspecified datum based upon the International 1924 Authalic Sphere",DATUM["Not specified (based on International 1924 Authalic Sphere)",SPHEROID["International 1924 Authalic Sphere",6371228,0,AUTHORITY["EPSG","7057"]],AUTHORITY["EPSG","6053"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4053"]],PROJECTION["Lambert Cylindrical Equal Area (Spherical)",AUTHORITY["EPSG","9834"]],PARAMETER["Latitude of 1st standard parallel",30,AUTHORITY["EPSG","8823"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3410"]]';
ERROR SR003: The spatial reference system definition for SRID 1037003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1037004 NAME 'TEST1037004 NSIDC EASE-Grid Global' DEFINITION 'PROJCS["NSIDC EASE-Grid Global",GEOGCS["Unspecified datum based upon the International 1924 Authalic Sphere",DATUM["Not specified (based on International 1924 Authalic Sphere)",SPHEROID["International 1924 Authalic Sphere",6371228,0,AUTHORITY["EPSG","7057"]],AUTHORITY["EPSG","6053"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4053"]],PROJECTION["Lambert Cylindrical Equal Area (Spherical)",AUTHORITY["EPSG","9834"]],PARAMETER["Latitude of 1st standard parallel",30,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3410"]]';
ERROR SR003: The spatial reference system definition for SRID 1037004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1037005 NAME 'TEST1037005 NSIDC EASE-Grid Global' DEFINITION 'PROJCS["NSIDC EASE-Grid Global",GEOGCS["Unspecified datum based upon the International 1924 Authalic Sphere",DATUM["Not specified (based on International 1924 Authalic Sphere)",SPHEROID["International 1924 Authalic Sphere",6371228,0,AUTHORITY["EPSG","7057"]],AUTHORITY["EPSG","6053"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4053"]],PROJECTION["Lambert Cylindrical Equal Area (Spherical)",AUTHORITY["EPSG","9834"]],PARAMETER["Latitude of 1st standard parallel",30,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3410"]]';
ERROR SR003: The spatial reference system definition for SRID 1037005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Projection: Lambert Cylindrical Equal Area (EPSG 9835).
#
# With all parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1038000 NAME 'TEST1038000 WGS 84 / NSIDC EASE-Grid 2.0 Global' DEFINITION 'PROJCS["WGS 84 / NSIDC EASE-Grid 2.0 Global",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Lambert Cylindrical Equal Area",AUTHORITY["EPSG","9835"]],PARAMETER["Latitude of 1st standard parallel",30,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6933"]]';
DROP SPATIAL REFERENCE SYSTEM 1038000;
# With all parameters, but no authority codes (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1038001 NAME 'TEST1038001 WGS 84 / NSIDC EASE-Grid 2.0 Global' DEFINITION 'PROJCS["WGS 84 / NSIDC EASE-Grid 2.0 Global",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Lambert Cylindrical Equal Area",AUTHORITY["EPSG","9835"]],PARAMETER["standard_parallel_1",30],PARAMETER["central_meridian",0],PARAMETER["false_easting",0],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6933"]]';
DROP SPATIAL REFERENCE SYSTEM 1038001;
# Missing parameter 8823 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1038002 NAME 'TEST1038002 WGS 84 / NSIDC EASE-Grid 2.0 Global' DEFINITION 'PROJCS["WGS 84 / NSIDC EASE-Grid 2.0 Global",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Lambert Cylindrical Equal Area",AUTHORITY["EPSG","9835"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6933"]]';
ERROR SR003: The spatial reference system definition for SRID 1038002 does not specify the mandatory standard_parallel_1 (EPSG 8823) projection parameter.
# Missing parameter 8802 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1038003 NAME 'TEST1038003 WGS 84 / NSIDC EASE-Grid 2.0 Global' DEFINITION 'PROJCS["WGS 84 / NSIDC EASE-Grid 2.0 Global",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Lambert Cylindrical Equal Area",AUTHORITY["EPSG","9835"]],PARAMETER["Latitude of 1st standard parallel",30,AUTHORITY["EPSG","8823"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6933"]]';
ERROR SR003: The spatial reference system definition for SRID 1038003 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
# Missing parameter 8806 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1038004 NAME 'TEST1038004 WGS 84 / NSIDC EASE-Grid 2.0 Global' DEFINITION 'PROJCS["WGS 84 / NSIDC EASE-Grid 2.0 Global",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Lambert Cylindrical Equal Area",AUTHORITY["EPSG","9835"]],PARAMETER["Latitude of 1st standard parallel",30,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6933"]]';
ERROR SR003: The spatial reference system definition for SRID 1038004 does not specify the mandatory false_easting (EPSG 8806) projection parameter.
# Missing parameter 8807 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1038005 NAME 'TEST1038005 WGS 84 / NSIDC EASE-Grid 2.0 Global' DEFINITION 'PROJCS["WGS 84 / NSIDC EASE-Grid 2.0 Global",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Lambert Cylindrical Equal Area",AUTHORITY["EPSG","9835"]],PARAMETER["Latitude of 1st standard parallel",30,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6933"]]';
ERROR SR003: The spatial reference system definition for SRID 1038005 does not specify the mandatory false_northing (EPSG 8807) projection parameter.
#
# Geographic SRS parsing
#
# Maximal -- all optional elements (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039000 NAME 'TEST1039000 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]]';
DROP SPATIAL REFERENCE SYSTEM 1039000;
# Minimal -- no optional elements (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039001 NAME 'TEST1039001 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********]],PRIMEM["Greenwich",0],UNIT["degree",0.017453292519943278],AXIS["Lat",NORTH],AXIS["Long",EAST]]';
DROP SPATIAL REFERENCE SYSTEM 1039001;
# Axis directions NORTH and EAST (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039002 NAME 'TEST1039002 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]]';
DROP SPATIAL REFERENCE SYSTEM 1039002;
# Axis directions NORTH and WEST (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039002 NAME 'TEST1039002 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",WEST],AUTHORITY["EPSG","4326"]]';
DROP SPATIAL REFERENCE SYSTEM 1039002;
# Axis directions EAST and NORTH (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039002 NAME 'TEST1039002 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Long",EAST],AXIS["Lat",NORTH],AUTHORITY["EPSG","4326"]]';
DROP SPATIAL REFERENCE SYSTEM 1039002;
# Axis directions WEST and NORTH (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039002 NAME 'TEST1039002 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Long",WEST],AXIS["Lat",NORTH],AUTHORITY["EPSG","4326"]]';
DROP SPATIAL REFERENCE SYSTEM 1039002;
# Axis directions SOUTH and EAST (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039003 NAME 'TEST1039003 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",SOUTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]]';
DROP SPATIAL REFERENCE SYSTEM 1039003;
# Axis directions SOUTH and WEST (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039003 NAME 'TEST1039003 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",SOUTH],AXIS["Long",WEST],AUTHORITY["EPSG","4326"]]';
DROP SPATIAL REFERENCE SYSTEM 1039003;
# Axis directions EAST and SOUTH (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039002 NAME 'TEST1039002 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Long",EAST],AXIS["Lat",SOUTH],AUTHORITY["EPSG","4326"]]';
DROP SPATIAL REFERENCE SYSTEM 1039002;
# Axis directions WEST and SOUTH (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039002 NAME 'TEST1039002 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Long",WEST],AXIS["Lat",SOUTH],AUTHORITY["EPSG","4326"]]';
DROP SPATIAL REFERENCE SYSTEM 1039002;
# Axis direction OTHER (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",OTHER],AXIS["Long",OTHER],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'OTHER' and 'OTHER'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",OTHER],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'NORTH' and 'OTHER'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",SOUTH],AXIS["Long",OTHER],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'SOUTH' and 'OTHER'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",OTHER],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'OTHER' and 'EAST'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",OTHER],AXIS["Long",WEST],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'OTHER' and 'WEST'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
# Two latitude axis directions (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",NORTH],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'NORTH' and 'NORTH'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",SOUTH],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'NORTH' and 'SOUTH'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",SOUTH],AXIS["Long",NORTH],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'SOUTH' and 'NORTH'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",SOUTH],AXIS["Long",SOUTH],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'SOUTH' and 'SOUTH'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
# Two longitude axis directions (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",EAST],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'EAST' and 'EAST'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",EAST],AXIS["Long",WEST],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'EAST' and 'WEST'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",WEST],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'WEST' and 'EAST'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
CREATE SPATIAL REFERENCE SYSTEM 1039004 NAME 'TEST1039004 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",WEST],AXIS["Long",WEST],AUTHORITY["EPSG","4326"]]';
ERROR SR002: The spatial reference system definition for SRID 1039004 specifies invalid geographic axes 'WEST' and 'WEST'. One axis must be NORTH or SOUTH and the other must be EAST or WEST.
# Invalid axis directions (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1039005 NAME 'TEST1039005 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",FOO],AXIS["Long",BARE],AUTHORITY["EPSG","4326"]]';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1039005.
# Parentheses (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039006 NAME 'TEST1039006 WGS 84' DEFINITION 'GEOGCS("WGS 84",DATUM("World Geodetic System 1984",SPHEROID("WGS 84",6378137,298.*********,AUTHORITY("EPSG","7030")),AUTHORITY("EPSG","6326")),PRIMEM("Greenwich",0,AUTHORITY("EPSG","8901")),UNIT("degree",0.017453292519943278,AUTHORITY("EPSG","9122")),AXIS("Lat",NORTH),AXIS("Long",EAST),AUTHORITY("EPSG","4326"))';
DROP SPATIAL REFERENCE SYSTEM 1039006;
# Mixing square brackets and parentheses (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1039007 NAME 'TEST1039007 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM("World Geodetic System 1984",SPHEROID("WGS 84",6378137,298.*********,AUTHORITY("EPSG","7030")),AUTHORITY("EPSG","6326")),PRIMEM("Greenwich",0,AUTHORITY("EPSG","8901")),UNIT("degree",0.017453292519943278,AUTHORITY("EPSG","9122")),AXIS("Lat",NORTH),AXIS("Long",EAST),AUTHORITY("EPSG","4326")]';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1039007.
# Lowercase (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039008 NAME 'TEST1039008 WGS 84' DEFINITION 'geogcs["wgs 84",datum["world geodetic system 1984",spheroid["wgs 84",6378137,298.*********,authority["epsg","7030"]],authority["epsg","6326"]],primem["greenwich",0,authority["epsg","8901"]],unit["degree",0.017453292519943278,authority["epsg","9122"]],axis["lat",north],axis["long",east],authority["epsg","4326"]]';
DROP SPATIAL REFERENCE SYSTEM 1039008;
# With extra whitespace (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039009 NAME 'TEST1039009 WGS 84' DEFINITION 'GEOGCS[  "WGS 84",  DATUM  [    "World Geodetic System 1984",    SPHEROID    [      "WGS 84",      6378137,      298.*********,      AUTHORITY["EPSG","7030"]    ],    AUTHORITY["EPSG","6326"]  ],  PRIMEM  [    "Greenwich",    0,    AUTHORITY["EPSG","8901"]  ],  UNIT  [    "degree",    0.017453292519943278,    AUTHORITY["EPSG","9122"]  ],  AXIS  [    "Lat",    NORTH  ],  AXIS  [    "Long",    EAST  ],  AUTHORITY["EPSG","4326"]]';
DROP SPATIAL REFERENCE SYSTEM 1039009;
# Square bracket is missing at the end (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1039010 NAME 'TEST1039010 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1039010.
# Invalid SRS definition (GEOGCSS instead of GEOGCS, should fail)
CREATE SPATIAL REFERENCE SYSTEM 1039011 NAME 'TEST1039011 WGS 84' DEFINITION 'GEOGCSS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]]';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1039011.
# Non-numeric characters in the authority code (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039012 NAME 'TEST1039012 WGS 84' DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","foobar"]]';
DROP SPATIAL REFERENCE SYSTEM 1039012;
# Empty definition string (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1039013 NAME 'TEST1039013 WGS 84' DEFINITION '';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1039013.
# Only whitespace definition string (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1039014 NAME 'TEST1039014 WGS 84' DEFINITION '    ';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1039014.
# Parentheses and whitespace at the end (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1039015 NAME 'TEST1039015 WGS 84' DEFINITION 'GEOGCS("WGS 84",DATUM("World Geodetic System 1984",SPHEROID("WGS 84",6378137,298.*********,AUTHORITY("EPSG","7030")),AUTHORITY("EPSG","6326")),PRIMEM("Greenwich",0,AUTHORITY("EPSG","8901")),UNIT("degree",0.017453292519943278,AUTHORITY("EPSG","9122")),AXIS("Lat",NORTH),AXIS("Long",EAST),AUTHORITY("EPSG","4326"))  ';
DROP SPATIAL REFERENCE SYSTEM 1039015;
# Semi-major axis 0 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1000000
NAME 'test1'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",0,298.*********]],PRIMEM["Greenwich",0],UNIT["degree",0.017453292519943278],AXIS["Lat",NORTH],AXIS["Lon",EAST]]';
ERROR SR002: The length of the semi-major axis must be a positive number.
# Inverse flattening 1 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1000000
NAME 'test1'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,1]],PRIMEM["Greenwich",0],UNIT["degree",0.017453292519943278],AXIS["Lat",NORTH],AXIS["Lon",EAST]]';
ERROR SR002: The inverse flattening must be larger than 1.0, or 0.0 if the ellipsoid is a sphere.
# Prime meridian < -180 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1000000
NAME 'test1'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********]],PRIMEM["Foo",-180.0000001],UNIT["degree",0.017453292519943278],AXIS["Lat",NORTH],AXIS["Lon",EAST]]';
ERROR SR002: The prime meridian must be within (-180, 180] degrees, specified in the SRS angular unit.
# Prime meridian > 180 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1000000
NAME 'test1'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********]],PRIMEM["Foo",180.0000001],UNIT["degree",0.017453292519943278],AXIS["Lat",NORTH],AXIS["Lon",EAST]]';
ERROR SR002: The prime meridian must be within (-180, 180] degrees, specified in the SRS angular unit.
# Unit conversion factor 0 (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1000000
NAME 'test1'
DEFINITION 'GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********]],PRIMEM["Greenwich",0],UNIT["degree",0],AXIS["Lat",NORTH],AXIS["Lon",EAST]]';
ERROR SR002: The angular unit conversion factor must be a positive number.
#
# Projected SRS parsing
#
# Maximal -- all optional elements (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1040000 NAME 'TEST1040000 WGS 84 / TM 116 SE' DEFINITION 'PROJCS["WGS 84 / TM 116 SE",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",116,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",10000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2309"]]';
DROP SPATIAL REFERENCE SYSTEM 1040000;
# Minimal -- no optional elements (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1040001 NAME 'TEST1040001 WGS 84 / TM 116 SE' DEFINITION 'PROJCS["WGS 84 / TM 116 SE",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********]],PRIMEM["Greenwich",0],UNIT["degree",0.017453292519943278],AXIS["Lat",NORTH],AXIS["Long",EAST]],PROJECTION["Transverse Mercator"],UNIT["metre",1]]';
DROP SPATIAL REFERENCE SYSTEM 1040001;
# Lowercase (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1040002 NAME 'TEST1040002 WGS 84 / TM 116 SE' DEFINITION 'projcs["wgs 84 / tm 116 se",geogcs["wgs 84",datum["world geodetic system 1984",spheroid["wgs 84",6378137,298.*********,authority["epsg","7030"]],towgs84[0,0,0,0,0,0,0],authority["epsg","6326"]],primem["greenwich",0,authority["epsg","8901"]],unit["degree",0.017453292519943278,AUTHORITY["epsg","9122"]],axis["lat",north],axis["long",east],authority["epsg","4326"]],projection["transverse mercator",authority["epsg","9807"]],parameter["latitude of natural origin",0,authority["epsg","8801"]],parameter["longitude of natural origin",116,authority["epsg","8802"]],parameter["scale factor at natural origin",0.9996,authority["epsg","8805"]],parameter["false easting",500000,authority["epsg","8806"]],parameter["false northing",10000000,authority["epsg","8807"]],unit["metre",1,authority["epsg","9001"]],axis["e",east],axis["n",north],authority["epsg","2309"]]';
DROP SPATIAL REFERENCE SYSTEM 1040002;
# Extra/unused parameter (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1040003 NAME 'TEST1040003 WGS 84 / TM 116 SE' DEFINITION 'PROJCS["WGS 84 / TM 116 SE",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",116,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",10000000,AUTHORITY["EPSG","8807"]],PARAMETER["Foo",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2309"]]';
ERROR SR002: The spatial reference system definition for SRID 1040003 contains unused projection parameter "Foo".
# Square bracket is missing at the end (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1040004 NAME 'TEST1040004 WGS 84 / TM 116 SE' DEFINITION 'PROJCS["WGS 84 / TM 116 SE",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",116,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",10000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2309"]';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1040004.
# Invalid SRS definition (PROJCSS instead of PROJCS, should fail)
CREATE SPATIAL REFERENCE SYSTEM 1040005 NAME 'TEST1040005 WGS 84 / TM 116 SE' DEFINITION 'PROJCSS["WGS 84 / TM 116 SE",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",116,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",10000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2309"]]';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1040005.
#
# Projection: Unknown
#
# Without authority code (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1041000 NAME 'TEST1041000 WGS 84 / NSIDC EASE-Grid 2.0 Global' DEFINITION 'PROJCS["WGS 84 / NSIDC EASE-Grid 2.0 Global",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Lambert Cylindrical Equal Area"],PARAMETER["Latitude of 1st standard parallel",30,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6933"]]';
DROP SPATIAL REFERENCE SYSTEM 1041000;
# With unknown authority code (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1041001 NAME 'TEST1039001 WGS 84 / NSIDC EASE-Grid 2.0 Global' DEFINITION 'PROJCS["WGS 84 / NSIDC EASE-Grid 2.0 Global",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Lambert Cylindrical Equal Area",AUTHORITY["foo","bar"]],PARAMETER["Latitude of 1st standard parallel",30,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6933"]]';
DROP SPATIAL REFERENCE SYSTEM 1041001;
# With unknown EPSG authority code (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1041002 NAME 'TEST1039002 WGS 84 / NSIDC EASE-Grid 2.0 Global' DEFINITION 'PROJCS["WGS 84 / NSIDC EASE-Grid 2.0 Global",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Lambert Cylindrical Equal Area",AUTHORITY["EPSG","bar"]],PARAMETER["Latitude of 1st standard parallel",30,AUTHORITY["EPSG","8823"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",0,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6933"]]';
DROP SPATIAL REFERENCE SYSTEM 1041002;
# Without parameters (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1041003 NAME 'TEST1041003 WGS 84 / NSIDC EASE-Grid 2.0 Global' DEFINITION 'PROJCS["WGS 84 / NSIDC EASE-Grid 2.0 Global",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Lambert Cylindrical Equal Area"],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6933"]]';
DROP SPATIAL REFERENCE SYSTEM 1041003;
#
# Numeric values
#
# NaN value (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1050000 NAME 'TEST1050000 WGS 84 / TM 116 SE' DEFINITION 'PROJCS["WGS 84 / TM 116 SE",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",NaN,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",116,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",10000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2309"]]';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1050000.
# -NaN value (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1050001 NAME 'TEST1050001 WGS 84 / TM 116 SE' DEFINITION 'PROJCS["WGS 84 / TM 116 SE",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",-NaN,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",116,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",10000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2309"]]';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1050001.
# Inf value (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1050002 NAME 'TEST1050002 WGS 84 / TM 116 SE' DEFINITION 'PROJCS["WGS 84 / TM 116 SE",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",NaN,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",116,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",10000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2309"]]';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1050002.
# -Inf value (should fail)
CREATE SPATIAL REFERENCE SYSTEM 1050003 NAME 'TEST1050003 WGS 84 / TM 116 SE' DEFINITION 'PROJCS["WGS 84 / TM 116 SE",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",-NaN,298.*********,AUTHORITY["EPSG","7030"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator",AUTHORITY["EPSG","9807"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",116,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",10000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","2309"]]';
ERROR SR002: Can't parse the spatial reference system definition of SRID 1050003.
#
# Parameter name aliases
#
# With all parameters, but no authority codes. Aliases for
# parameters 8823 and 8824 (should pass)
CREATE SPATIAL REFERENCE SYSTEM 1060000 NAME 'TEST1060000 NAD27 / Michigan Central' DEFINITION 'PROJCS["NAD27 / Michigan Central",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[2.478,149.752,197.726,0.526,0.498,-0.501,0.685],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Long",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Lambert Conic Conformal (2SP Michigan)",AUTHORITY["EPSG","1051"]],PARAMETER["latitude_of_origin",43.3277777777778],PARAMETER["central_meridian",-84.3333333333333],PARAMETER["standard_parallel1",44.1944444444444],PARAMETER["standard_parallel2",45.7],PARAMETER["false_easting",2000000],PARAMETER["false_northing",0],PARAMETER["ellipsoid_scale_factor",1.0000382],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6201"]]';
DROP SPATIAL REFERENCE SYSTEM 1060000;
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 33001 NAME 'webmodairy' ORGANIZATION 'EPSG' IDENTIFIED BY 33001 DEFINITION 'PROJCS["Modified Airy / Pseudo-Mercator",GEOGCS["modairy",DATUM["modairy",SPHEROID["Bessel 1841",  6377340.189,299.324937365, AUTHORITY["EPSG","7004"]], TOWGS84[0,0,0,0,0,0,0],   AUTHORITY["EPSG","6120"]],PRIMEM["Greenwich",0, AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278, AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST], AUTHORITY["EPSG","4120"]],PROJECTION["Popular Visualisation Pseudo Mercator",AUTHORITY["EPSG","1024"]],PARAMETER["Latitude of natural origin",91,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0.01,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",100,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",-10,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3857"]]';
ERROR SR002: Latitude of origin must be within [-90, 90] degrees, specified in the SRS angular unit.
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 33001 NAME 'webmodairy' ORGANIZATION 'EPSG' IDENTIFIED BY 33001 DEFINITION 'PROJCS["Modified Airy / Pseudo-Mercator",GEOGCS["modairy",DATUM["modairy",SPHEROID["Bessel 1841",  6377340.189,299.324937365, AUTHORITY["EPSG","7004"]], TOWGS84[0,0,0,0,0,0,0],   AUTHORITY["EPSG","6120"]],PRIMEM["Greenwich",0, AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278, AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST], AUTHORITY["EPSG","4120"]],PROJECTION["Popular Visualisation Pseudo Mercator",AUTHORITY["EPSG","1024"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",180.01,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",100,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",-10,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3857"]]';
ERROR SR002: Longitude of origin must be within (-180, 180] degrees, specified in the SRS angular unit.
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 33001 NAME 'magnamodified' ORGANIZATION 'EPSG' IDENTIFIED BY 33001 DEFINITION 'PROJCS["MAGNA-SIRGAS / Arauca urban grid",GEOGCS["MAGNA-SIRGAS",DATUM["Marco Geocentrico Nacional de Referencia",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],TOWGS84[0,0,0,0,0,0,0],AUTHORITY["EPSG","6686"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4686"]],PROJECTION["Colombia Urban",AUTHORITY["EPSG","1052"]],PARAMETER["Latitude of natural origin",7.08760639166667,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",-70.7583096555555,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",1035263.443,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",1275526.621,AUTHORITY["EPSG","8807"]],PARAMETER["Projection plane origin height",-100,AUTHORITY["EPSG","1039"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["N",NORTH],AXIS["E",EAST],AUTHORITY["EPSG","6244"]]';
ERROR SR002: Height parameter must be non negative.
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 33001 NAME 'nad27modified' ORGANIZATION 'EPSG' IDENTIFIED BY 33001 DEFINITION 'PROJCS["NAD27 / Michigan North",GEOGCS["NAD27",DATUM["North American Datum 1927",SPHEROID["Clarke 1866",6378206.4,294.9786982138982,AUTHORITY["EPSG","7008"]],TOWGS84[-32.3841359,180.4090461,120.8442577,-2.1545854,-0.1498782,0.5742915,8.1049164],AUTHORITY["EPSG","6267"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4267"]],PROJECTION["Lambert Conic Conformal (2SP Michigan)",AUTHORITY["EPSG","1051"]],PARAMETER["Latitude of false origin",44.7944444444444,AUTHORITY["EPSG","8821"]],PARAMETER["Longitude of false origin",-87,AUTHORITY["EPSG","8822"]],PARAMETER["Latitude of 1st standard parallel",45.4944444444444,AUTHORITY["EPSG","8823"]],PARAMETER["Latitude of 2nd standard parallel",47.0944444444444,AUTHORITY["EPSG","8824"]],PARAMETER["Easting at false origin",2000000,AUTHORITY["EPSG","8826"]],PARAMETER["Northing at false origin",0,AUTHORITY["EPSG","8827"]],PARAMETER["Ellipsoid scaling factor",-1.0000382,AUTHORITY["EPSG","1038"]],UNIT["US survey foot",0.30480060960121924,AUTHORITY["EPSG","9003"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","6966"]]';
ERROR SR002: Scaling parameter must be non negative.
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 33001 NAME 'wgs84utmmodified' ORGANIZATION 'EPSG' IDENTIFIED BY 33001 DEFINITION 'PROJCS["WGS 84 / UTM grid system (northern hemisphere)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse Mercator Zoned Grid System",AUTHORITY["EPSG","9824"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Initial longitude",-180,AUTHORITY["EPSG","8830"]],PARAMETER["Zone width",61,AUTHORITY["EPSG","8831"]],PARAMETER["Scale factor at natural origin",0.9996,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",500000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",0,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",EAST],AXIS["N",NORTH],AUTHORITY["EPSG","32600"]]';
ERROR SR002: Zone width parameter must be an integer between 1-60.
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 33001 NAME 'wgs84upsmodified' ORGANIZATION 'EPSG' IDENTIFIED BY 33001 DEFINITION 'PROJCS["WGS 84 / UPS North (E,N)",GEOGCS["WGS 84",DATUM["World Geodetic System 1984",SPHEROID["WGS 84",6378137,298.*********,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278,AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST],AUTHORITY["EPSG","4326"]],PROJECTION["Polar Stereographic (variant A)",AUTHORITY["EPSG","9810"]],PARAMETER["Latitude of natural origin",89,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0,AUTHORITY["EPSG","8802"]],PARAMETER["Scale factor at natural origin",0.994,AUTHORITY["EPSG","8805"]],PARAMETER["False easting",2000000,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",2000000,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["E",SOUTH],AXIS["N",SOUTH],AUTHORITY["EPSG","5041"]]';
ERROR SR002: Latitude of origin must be +-90 degrees, specified in the SRS angular unit, in Polar Stereographic (variant A) projection method (EPSG:9810).
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 33001 NAME 'webmodairy' ORGANIZATION 'EPSG' IDENTIFIED BY 33001 DEFINITION 'PROJCS["Modified Airy / Pseudo-Mercator",GEOGCS["modairy",DATUM["modairy",SPHEROID["Bessel 1841",  6377340.189,299.324937365, AUTHORITY["EPSG","7004"]], TOWGS84[0,0,0,0,0,0,0],   AUTHORITY["EPSG","6120"]],PRIMEM["Greenwich",0, AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278, AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST], AUTHORITY["EPSG","4120"]],PROJECTION["Popular Visualisation Pseudo Mercator",AUTHORITY["EPSG","1024"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["False easting",100,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",-10,AUTHORITY["EPSG","8807"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3857"]]';
ERROR SR003: The spatial reference system definition for SRID 33001 does not specify the mandatory central_meridian (EPSG 8802) projection parameter.
CREATE OR REPLACE SPATIAL REFERENCE SYSTEM 33001 NAME 'webmodairy' ORGANIZATION 'EPSG' IDENTIFIED BY 33001 DEFINITION 'PROJCS["Modified Airy / Pseudo-Mercator",GEOGCS["modairy",DATUM["modairy",SPHEROID["Bessel 1841",  6377340.189,299.324937365, AUTHORITY["EPSG","7004"]], TOWGS84[0,0,0,0,0,0,0],   AUTHORITY["EPSG","6120"]],PRIMEM["Greenwich",0, AUTHORITY["EPSG","8901"]],UNIT["degree",0.017453292519943278, AUTHORITY["EPSG","9122"]],AXIS["Lat",NORTH],AXIS["Lon",EAST], AUTHORITY["EPSG","4120"]],PROJECTION["Popular Visualisation Pseudo Mercator",AUTHORITY["EPSG","1024"]],PARAMETER["Latitude of natural origin",0,AUTHORITY["EPSG","8801"]],PARAMETER["Longitude of natural origin",0.01,AUTHORITY["EPSG","8802"]],PARAMETER["False easting",100,AUTHORITY["EPSG","8806"]],PARAMETER["False northing",-10,AUTHORITY["EPSG","8807"]],PARAMETER["Scale factor at natural origin",0.996,AUTHORITY["EPSG","8805"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AXIS["X",EAST],AXIS["Y",NORTH],AUTHORITY["EPSG","3857"]]';
ERROR SR002: The spatial reference system definition for SRID 33001 contains unused projection parameter "Scale factor at natural origin".
SELECT @number_of_srss = COUNT(*) AS should_be_true
FROM INFORMATION_SCHEMA.ST_SPATIAL_REFERENCE_SYSTEMS;
should_be_true
1
#
# Tests for WL#16547
# Check that users with SUPER privilege (root@localhost and
# the new added user u1) gets CREATE_SPATIAL_REFERENCE_SYSTEM privilege
# after upgrade.
#
# Show privilege for root@localhost before the privilege CREATE_SPATIAL_REFERENCE_SYSTEM will be revoked
SHOW GRANTS FOR root@localhost;
Grants for root@localhost
GRANT <ALL_STATIC_PRIVILEGES> ON *.* TO `root`@`localhost` WITH GRANT OPTION
GRANT <ALL_DYNAMIC_PRIVILEGES> ON *.* TO `root`@`localhost` WITH GRANT OPTION
GRANT PROXY ON ``@`` TO `root`@`localhost` WITH GRANT OPTION
CREATE USER u1;
GRANT SUPER ON *.* TO u1;
Warnings:
Warning	1287	The SUPER privilege identifier is deprecated
# Revoke the privilege CREATE_SPATIAL_REFERENCE_SYSTEM in order to simulate
# the case when upgrade is run against a database that was created by
# mysql server without support for CREATE_SPATIAL_REFERENCE_SYSTEM.
REVOKE CREATE_SPATIAL_REFERENCE_SYSTEM ON *.* FROM root@localhost;
REVOKE CREATE_SPATIAL_REFERENCE_SYSTEM ON *.* FROM u1;
# We show here that the users root@localhost and u1 have the privilege
# SUPER and don't have the privilege CREATE_SPATIAL_REFERENCE_SYSTEM
SHOW GRANTS FOR root@localhost;
Grants for root@localhost
GRANT <ALL_STATIC_PRIVILEGES> ON *.* TO `root`@`localhost` WITH GRANT OPTION
GRANT ALLOW_NONEXISTENT_DEFINER,APPLICATION_PASSWORD_ADMIN,AUDIT_ABORT_EXEMPT,AUDIT_ADMIN,AUTHENTICATION_POLICY_ADMIN,BACKUP_ADMIN,BINLOG_ADMIN,BINLOG_ENCRYPTION_ADMIN,CLONE_ADMIN,CONNECTION_ADMIN,ENCRYPTION_KEY_ADMIN,FIREWALL_EXEMPT,FLUSH_OPTIMIZER_COSTS,FLUSH_PRIVILEGES,FLUSH_STATUS,FLUSH_TABLES,FLUSH_USER_RESOURCES,GROUP_REPLICATION_ADMIN,GROUP_REPLICATION_STREAM,INNODB_REDO_LOG_ARCHIVE,INNODB_REDO_LOG_ENABLE,OPTIMIZE_LOCAL_TABLE,PASSWORDLESS_USER_ADMIN,PERSIST_RO_VARIABLES_ADMIN,REPLICATION_APPLIER,REPLICATION_SLAVE_ADMIN,RESOURCE_GROUP_ADMIN,RESOURCE_GROUP_USER,ROLE_ADMIN,SENSITIVE_VARIABLES_OBSERVER,SERVICE_CONNECTION_ADMIN,SESSION_VARIABLES_ADMIN,SET_ANY_DEFINER,SHOW_ROUTINE,SYSTEM_USER,SYSTEM_VARIABLES_ADMIN,TABLE_ENCRYPTION_ADMIN,TELEMETRY_LOG_ADMIN,TRANSACTION_GTID_TAG,XA_RECOVER_ADMIN ON *.* TO `root`@`localhost` WITH GRANT OPTION
GRANT PROXY ON ``@`` TO `root`@`localhost` WITH GRANT OPTION
SHOW GRANTS FOR u1;
Grants for u1@%
GRANT SUPER ON *.* TO `u1`@`%`
# Start upgrade
# restart:--upgrade=FORCE --log-error=test_error_log
# Show privileges granted to the users root@localhost and u1
# after upgrade has been finished.
# It is expected that the users root@localhost and u1 have the
# privilege CREATE_SPATIAL_REFERENCE_SYSTEM granted since they had the privilge SUPER
# before upgrade
SHOW GRANTS FOR root@localhost;
Grants for root@localhost
GRANT <ALL_STATIC_PRIVILEGES> ON *.* TO `root`@`localhost` WITH GRANT OPTION
GRANT <ALL_DYNAMIC_PRIVILEGES> ON *.* TO `root`@`localhost` WITH GRANT OPTION
GRANT PROXY ON ``@`` TO `root`@`localhost` WITH GRANT OPTION
SHOW GRANTS FOR u1;
Grants for u1@%
GRANT SUPER ON *.* TO `u1`@`%`
GRANT CREATE_SPATIAL_REFERENCE_SYSTEM ON *.* TO `u1`@`%`
# Now run upgrade against database where there is a user with granted
# privilege CREATE_SPATIAL_REFERENCE_SYSTEM and check that for those users who have
# the privilege SUPER assigned the privilege CREATE_SPATIAL_REFERENCE_SYSTEM won't be
# granted during upgrade.
# Revoke the privilege CREATE_SPATIAL_REFERENCE_SYSTEM from the user u1 and
# mysql.session@localhost
REVOKE CREATE_SPATIAL_REFERENCE_SYSTEM ON *.* FROM u1;
# Start upgrade
# restart:--upgrade=FORCE --log-error=test_error_log
# It is expected that after upgrade be finished the privilege
# CREATE_SPATIAL_REFERENCE_SYSTEM won't be granted to the user u1 since
# there was another user (root@localhost) who had the privilege
# CREATE_SPATIAL_REFERENCE_SYSTEM at the time when upgrade was started
SHOW GRANTS FOR root@localhost;
Grants for root@localhost
GRANT <ALL_STATIC_PRIVILEGES> ON *.* TO `root`@`localhost` WITH GRANT OPTION
GRANT <ALL_DYNAMIC_PRIVILEGES> ON *.* TO `root`@`localhost` WITH GRANT OPTION
GRANT PROXY ON ``@`` TO `root`@`localhost` WITH GRANT OPTION
SHOW GRANTS FOR u1;
Grants for u1@%
GRANT SUPER ON *.* TO `u1`@`%`
# Cleaning up
DROP USER u1;
# End of tests for WL#16547
