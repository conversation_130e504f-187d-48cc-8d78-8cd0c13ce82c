DROP TABLE IF EXISTS t1,t2,t3;
CREATE TABLE t1 (c1 TINYINT NOT NULL PRIMARY KEY, c2 TINYINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 TINYINT, new1 TINYINT, old2 TINYINT, new2 TINYINT);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY, c2 TINYINT);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 SMALLINT NOT NULL PRIMARY KEY, c2 SMALLINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 SMALLINT, new1 SMALLINT, old2 SMALLINT, new2 SMALLINT);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY, c2 SMALLINT);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL PRIMARY KEY, c2 MEDIUMINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 MEDIUMINT, new1 MEDIUMINT, old2 MEDIUMINT, new2 MEDIUMINT);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY, c2 MEDIUMINT);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 INT NOT NULL PRIMARY KEY, c2 INT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 INT, new1 INT, old2 INT, new2 INT);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY, c2 INT);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 INTEGER, new1 INTEGER, old2 INTEGER, new2 INTEGER);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 BIGINT, new1 BIGINT, old2 BIGINT, new2 BIGINT);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 REAL NOT NULL PRIMARY KEY, c2 REAL);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 REAL, new1 REAL, old2 REAL, new2 REAL);
CREATE TABLE t3(c1 REAL NOT NULL PRIMARY KEY, c2 REAL);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 DOUBLE NOT NULL PRIMARY KEY, c2 DOUBLE);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 DOUBLE, new1 DOUBLE, old2 DOUBLE, new2 DOUBLE);
CREATE TABLE t3(c1 DOUBLE NOT NULL PRIMARY KEY, c2 DOUBLE);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 FLOAT NOT NULL PRIMARY KEY, c2 FLOAT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 FLOAT, new1 FLOAT, old2 FLOAT, new2 FLOAT);
CREATE TABLE t3(c1 FLOAT NOT NULL PRIMARY KEY, c2 FLOAT);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 DECIMAL NOT NULL PRIMARY KEY, c2 DECIMAL);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 DECIMAL, new1 DECIMAL, old2 DECIMAL, new2 DECIMAL);
CREATE TABLE t3(c1 DECIMAL NOT NULL PRIMARY KEY, c2 DECIMAL);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 NUMERIC NOT NULL PRIMARY KEY, c2 NUMERIC);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 NUMERIC, new1 NUMERIC, old2 NUMERIC, new2 NUMERIC);
CREATE TABLE t3(c1 NUMERIC NOT NULL PRIMARY KEY, c2 NUMERIC);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 BEFORE INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 TINYINT NOT NULL PRIMARY KEY, c2 TINYINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 TINYINT, new1 TINYINT, old2 TINYINT, new2 TINYINT);
CREATE TABLE t3(c1 TINYINT NOT NULL PRIMARY KEY, c2 TINYINT);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 SMALLINT NOT NULL PRIMARY KEY, c2 SMALLINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 SMALLINT, new1 SMALLINT, old2 SMALLINT, new2 SMALLINT);
CREATE TABLE t3(c1 SMALLINT NOT NULL PRIMARY KEY, c2 SMALLINT);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 MEDIUMINT NOT NULL PRIMARY KEY, c2 MEDIUMINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 MEDIUMINT, new1 MEDIUMINT, old2 MEDIUMINT, new2 MEDIUMINT);
CREATE TABLE t3(c1 MEDIUMINT NOT NULL PRIMARY KEY, c2 MEDIUMINT);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 INT NOT NULL PRIMARY KEY, c2 INT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 INT, new1 INT, old2 INT, new2 INT);
CREATE TABLE t3(c1 INT NOT NULL PRIMARY KEY, c2 INT);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 INTEGER, new1 INTEGER, old2 INTEGER, new2 INTEGER);
CREATE TABLE t3(c1 INTEGER NOT NULL PRIMARY KEY, c2 INTEGER);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 BIGINT, new1 BIGINT, old2 BIGINT, new2 BIGINT);
CREATE TABLE t3(c1 BIGINT NOT NULL PRIMARY KEY, c2 BIGINT);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 REAL NOT NULL PRIMARY KEY, c2 REAL);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 REAL, new1 REAL, old2 REAL, new2 REAL);
CREATE TABLE t3(c1 REAL NOT NULL PRIMARY KEY, c2 REAL);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 DOUBLE NOT NULL PRIMARY KEY, c2 DOUBLE);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 DOUBLE, new1 DOUBLE, old2 DOUBLE, new2 DOUBLE);
CREATE TABLE t3(c1 DOUBLE NOT NULL PRIMARY KEY, c2 DOUBLE);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 FLOAT NOT NULL PRIMARY KEY, c2 FLOAT);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 FLOAT, new1 FLOAT, old2 FLOAT, new2 FLOAT);
CREATE TABLE t3(c1 FLOAT NOT NULL PRIMARY KEY, c2 FLOAT);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 DECIMAL NOT NULL PRIMARY KEY, c2 DECIMAL);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 DECIMAL, new1 DECIMAL, old2 DECIMAL, new2 DECIMAL);
CREATE TABLE t3(c1 DECIMAL NOT NULL PRIMARY KEY, c2 DECIMAL);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
CREATE TABLE t1 (c1 NUMERIC NOT NULL PRIMARY KEY, c2 NUMERIC);
INSERT INTO t1 VALUES(1,1),(2,2),(3,3),(4,4),(6,6),(7,7),(8,8),(9,9),(10,10);
CREATE TABLE t2(c1 INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY, old1 NUMERIC, new1 NUMERIC, old2 NUMERIC, new2 NUMERIC);
CREATE TABLE t3(c1 NUMERIC NOT NULL PRIMARY KEY, c2 NUMERIC);
INSERT INTO t3 VALUES(1,0);
CREATE TRIGGER tr1 AFTER INSERT ON t1 FOR EACH ROW BEGIN 
INSERT INTO t2 (old1,new1,old2,new2) VALUES(0,NEW.c1,0,NEW.c2);
UPDATE t3 SET c2 = c2 + NEW.c2 WHERE c1 = 1; 
END//
INSERT INTO t1(c1,c2) VALUES(11,11);
REPLACE INTO t1(c1,c2) VALUES(12,12);
REPLACE INTO t1(c1,c2) VALUES(1,11);
REPLACE INTO t1(c1,c2) VALUES(3,11),(4,11);
SELECT * FROM t1 ORDER BY c1;
c1	c2
1	11
2	2
3	11
4	11
6	6
7	7
8	8
9	9
10	10
11	11
12	12
SELECT * FROM t2 ORDER BY c1;
c1	old1	new1	old2	new2
1	0	11	0	11
2	0	12	0	12
3	0	1	0	11
4	0	3	0	11
5	0	4	0	11
SELECT * FROM t3 ORDER BY c1;
c1	c2
1	56
DROP TABLE t1,t2,t3;
