DROP TABLE IF EXISTS t1;
#
# Start of 8.0 tests
#
#
# WL#9125: Add utf8mb4_0900_ai_ci
#
SET NAMES utf8mb4 COLLATE utf8mb4_0900_ai_ci;
SET @test_character_set= 'utf8mb4';
SET @test_collation= 'utf8mb4_0900_ai_ci';
SET @safe_character_set_server= @@character_set_server;
SET @safe_collation_server= @@collation_server;
SET @safe_character_set_client= @@character_set_client;
SET @safe_character_set_results= @@character_set_results;
SET character_set_server= @test_character_set;
SET collation_server= @test_collation;
CREATE DATABASE d1;
USE d1;
CREATE TABLE t1 (c CHAR(10), KEY(c));
SHOW FULL COLUMNS FROM t1;
Field	Type	Collation	Null	Key	Default	Extra	Privileges	Comment
c	char(10)	utf8mb4_0900_ai_ci	YES	MUL	NULL			
INSERT INTO t1 VALUES ('aaa'),('aaaa'),('aaaaa');
SELECT c as want3results FROM t1 WHERE c LIKE 'aaa%';
want3results
aaa
aaaa
aaaaa
DROP TABLE t1;
CREATE TABLE t1 (c1 varchar(15), KEY c1 (c1(2)));
SHOW FULL COLUMNS FROM t1;
Field	Type	Collation	Null	Key	Default	Extra	Privileges	Comment
c1	varchar(15)	utf8mb4_0900_ai_ci	YES	MUL	NULL			
INSERT INTO t1 VALUES ('location'),('loberge'),('lotre'),('boabab');
SELECT c1 as want3results from t1 where c1 like 'l%';
want3results
location
loberge
lotre
SELECT c1 as want3results from t1 where c1 like 'lo%';
want3results
location
loberge
lotre
SELECT c1 as want1result  from t1 where c1 like 'loc%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'loca%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'locat%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'locati%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'locatio%';
want1result
location
SELECT c1 as want1result  from t1 where c1 like 'location%';
want1result
location
DROP TABLE t1;
create table t1 (a set('a') not null);
insert ignore into t1 values (),();
Warnings:
Warning	1364	Field 'a' doesn't have a default value
select cast(a as char(1)) from t1;
cast(a as char(1))


select a sounds like a from t1;
a sounds like a
1
1
select 1 from t1 order by cast(a as char(1));
1
1
1
drop table t1;
set names utf8mb3;
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
create table t1 (
name varchar(10),
level smallint unsigned);
show create table t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `name` varchar(10) DEFAULT NULL,
  `level` smallint unsigned DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
insert into t1 values ('string',1);
select concat(name,space(level)), concat(name, repeat(' ',level)) from t1;
concat(name,space(level))	concat(name, repeat(' ',level))
string 	string 
drop table t1;
DROP DATABASE d1;
USE test;
SET character_set_server= @safe_character_set_server;
SET collation_server= @safe_collation_server;
SET character_set_client= @safe_character_set_client;
SET character_set_results= @safe_character_set_results;
SET NAMES utf8mb4 COLLATE utf8mb4_0900_ai_ci;
create table t1 select repeat('a',4000) a;
delete from t1;
insert into t1 values ('a'), ('a '), ('a\t');
select collation(a),hex(a) from t1 order by a;
collation(a)	hex(a)
utf8mb4_0900_ai_ci	61
utf8mb4_0900_ai_ci	6109
utf8mb4_0900_ai_ci	6120
drop table t1;
create table t1 engine=innodb select repeat('a',50) as c1;
alter table t1 add index(c1(5));
insert into t1 values ('abcdefg'),('abcde100'),('abcde110'),('abcde111');
select collation(c1) from t1 limit 1;
collation(c1)
utf8mb4_0900_ai_ci
select c1 from t1 where c1 like 'abcdef%' order by c1;
c1
abcdefg
select c1 from t1 where c1 like 'abcde1%' order by c1;
c1
abcde100
abcde110
abcde111
select c1 from t1 where c1 like 'abcde11%' order by c1;
c1
abcde110
abcde111
select c1 from t1 where c1 like 'abcde111%' order by c1;
c1
abcde111
drop table t1;
select @@collation_connection;
@@collation_connection
utf8mb4_0900_ai_ci
create table t1 ROW_FORMAT=DYNAMIC select repeat('a',50) as c1 ;
insert into t1 values('abcdef');
insert into t1 values('_bcdef');
insert into t1 values('a_cdef');
insert into t1 values('ab_def');
insert into t1 values('abc_ef');
insert into t1 values('abcd_f');
insert into t1 values('abcde_');
select c1 as c1u from t1 where c1 like 'ab\_def';
c1u
ab_def
select c1 as c2h from t1 where c1 like 'ab#_def' escape '#';
c2h
ab_def
drop table t1;
drop table if exists t1;
create table t1 select repeat('a',10) as c1;
delete from t1;
insert into t1 values (0x20),(0x21),(0x22),(0x23),(0x24),(0x25),(0x26),(0x27),(0x28),(0x29),(0x2A),(0x2B),(0x2C),(0x2D),(0x2E),(0x2F);
insert into t1 values (0x30),(0x31),(0x32),(0x33),(0x34),(0x35),(0x36),(0x37),(0x38),(0x39),(0x3A),(0x3B),(0x3C),(0x3D),(0x3E),(0x3F);
insert into t1 values (0x40),(0x41),(0x42),(0x43),(0x44),(0x45),(0x46),(0x47),(0x48),(0x49),(0x4A),(0x4B),(0x4C),(0x4D),(0x4E),(0x4F);
insert into t1 values (0x50),(0x51),(0x52),(0x53),(0x54),(0x55),(0x56),(0x57),(0x58),(0x59),(0x5A),(0x5B),(0x5C),(0x5D),(0x5E),(0x5F);
insert into t1 values (0x60),(0x61),(0x62),(0x63),(0x64),(0x65),(0x66),(0x67),(0x68),(0x69),(0x6A),(0x6B),(0x6C),(0x6D),(0x6E),(0x6F);
insert into t1 values (0x70),(0x71),(0x72),(0x73),(0x74),(0x75),(0x76),(0x77),(0x78),(0x79),(0x7A),(0x7B),(0x7C),(0x7D),(0x7E),(0x7F);
SELECT HEX(cx), cy
FROM (SELECT GROUP_CONCAT(c1 ORDER BY binary c1 SEPARATOR '') AS cx,
GROUP_CONCAT(HEX(c1) ORDER BY BINARY c1) AS cy
FROM t1
GROUP BY c1
) AS dt;
HEX(cx)	cy
7F	7F
20	20
5F	5F
2D	2D
2C	2C
3B	3B
3A	3A
21	21
3F	3F
2E	2E
27	27
22	22
28	28
29	29
5B	5B
5D	5D
7B	7B
7D	7D
40	40
2A	2A
2F	2F
5C	5C
26	26
23	23
25	25
60	60
5E	5E
2B	2B
3C	3C
3D	3D
3E	3E
7C	7C
7E	7E
24	24
30	30
31	31
32	32
33	33
34	34
35	35
36	36
37	37
38	38
39	39
4161	41,61
4262	42,62
4363	43,63
4464	44,64
4565	45,65
4666	46,66
4767	47,67
4868	48,68
4969	49,69
4A6A	4A,6A
4B6B	4B,6B
4C6C	4C,6C
4D6D	4D,6D
4E6E	4E,6E
4F6F	4F,6F
5070	50,70
5171	51,71
5272	52,72
5373	53,73
5474	54,74
5575	55,75
5676	56,76
5777	57,77
5878	58,78
5979	59,79
5A7A	5A,7A
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
drop table t1;
SET NAMES utf8mb4 COLLATE utf8mb4_0900_ai_ci;
SELECT HEX(CONVERT(_utf8mb4 0xF091AB9B41 USING ucs2));
HEX(CONVERT(_utf8mb4 0xF091AB9B41 USING ucs2))
003F0041
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT HEX(CONVERT(_utf8mb4 0xF091AB9B41 USING utf16));
HEX(CONVERT(_utf8mb4 0xF091AB9B41 USING utf16))
D806DEDB0041
SELECT HEX(CONVERT(_utf8mb4 0xF091AB9B41 USING utf32));
HEX(CONVERT(_utf8mb4 0xF091AB9B41 USING utf32))
00011ADB00000041
SELECT HEX(CONVERT(_ucs2 0xF8FF USING utf8mb4));
HEX(CONVERT(_ucs2 0xF8FF USING utf8mb4))
EFA3BF
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
SELECT HEX(CONVERT(_utf16 0xF8FF USING utf8mb4));
HEX(CONVERT(_utf16 0xF8FF USING utf8mb4))
EFA3BF
SELECT HEX(CONVERT(_utf32 0xF8FF USING utf8mb4));
HEX(CONVERT(_utf32 0xF8FF USING utf8mb4))
EFA3BF
SELECT HEX(CONVERT(_utf8mb4 0x8F USING ucs2));
ERROR HY000: Invalid utf8mb4 character string: '8F'
SELECT HEX(CONVERT(_utf8mb4 0xC230 USING ucs2));
ERROR HY000: Invalid utf8mb4 character string: 'C230'
SELECT HEX(CONVERT(_utf8mb4 0xE234F1 USING ucs2));
ERROR HY000: Invalid utf8mb4 character string: 'E234F1'
SELECT HEX(CONVERT(_utf8mb4 0xF4E25634 USING ucs2));
ERROR HY000: Invalid utf8mb4 character string: 'F4E256'
SELECT ASCII('ABC');
ASCII('ABC')
65
SELECT BIT_LENGTH('a');
BIT_LENGTH('a')
8
SELECT BIT_LENGTH('À');
BIT_LENGTH('À')
16
SELECT BIT_LENGTH('テ');
BIT_LENGTH('テ')
24
SELECT BIT_LENGTH('𝌆');
BIT_LENGTH('?')
32
SELECT CHAR_LENGTH('𝌆テÀa');
CHAR_LENGTH('?テÀa')
4
SELECT LENGTH('𝌆テÀa');
LENGTH('?テÀa')
10
SELECT FIELD('a', '𝌆テÀa');
FIELD('a', '?テÀa')
0
SELECT HEX('𝌆テÀa');
HEX('?テÀa')
F09D8C86E38386C38061
SELECT INSERT('𝌆テÀa', 2, 2, 'テb');
INSERT('?テÀa', 2, 2, 'テb')
𝌆テba
SELECT LOWER('𝌆テÀBcd');
LOWER('?テÀBcd')
𝌆テàbcd
SELECT ORD('𝌆');
ORD('?')
4036856966
SELECT UPPER('𝌆テàbCD');
UPPER('?テàbCD')
𝌆テÀBCD
SELECT LOCATE(_utf8mb4 0xF091AB9B41, _utf8mb4 0xF091AB9B42F091AB9B41F091AB9B43);
LOCATE(_utf8mb4 0xF091AB9B41, _utf8mb4 0xF091AB9B42F091AB9B41F091AB9B43)
3
SELECT HEX(REVERSE(_utf8mb4 0xF091AB9B41F091AB9B42F091AB9B43));
HEX(REVERSE(_utf8mb4 0xF091AB9B41F091AB9B42F091AB9B43))
43F091AB9B42F091AB9B41F091AB9B
SELECT HEX(SUBSTRING(_utf8mb4 0xF091AB9B41F091AB9B42F091AB9B43, 1, 2));
HEX(SUBSTRING(_utf8mb4 0xF091AB9B41F091AB9B42F091AB9B43, 1, 2))
F091AB9B41
SELECT HEX(SUBSTRING(_utf8mb4 0xF091AB9B41F091AB9B42F091AB9B43, -3, 2));
HEX(SUBSTRING(_utf8mb4 0xF091AB9B41F091AB9B42F091AB9B43, -3, 2))
42F091AB9B
SELECT HEX(TRIM(_utf8mb4 0x2020F091AB9B4120F091AB9B4120202020));
HEX(TRIM(_utf8mb4 0x2020F091AB9B4120F091AB9B4120202020))
F091AB9B4120F091AB9B41
SELECT HEX(WEIGHT_STRING('aA'));
HEX(WEIGHT_STRING('aA'))
1C471C47
SELECT HEX(WEIGHT_STRING(CAST(_utf32 x'337F' AS CHAR)));
HEX(WEIGHT_STRING(CAST(_utf32 x'337F' AS CHAR)))
FB40E82AFB40DF0FFB40CF1AFB40F93E
SELECT HEX(WEIGHT_STRING(CAST(_utf32 x'FDFA' AS CHAR)));
HEX(WEIGHT_STRING(CAST(_utf32 x'FDFA' AS CHAR)))
2364239C23C50209230B239C239C23B1
select @@collation_connection;
@@collation_connection
utf8mb4_0900_ai_ci
select hex(weight_string('a'));
hex(weight_string('a'))
1C47
select hex(weight_string('A'));
hex(weight_string('A'))
1C47
select hex(weight_string('abc'));
hex(weight_string('abc'))
1C471C601C7A
select hex(weight_string('abc' as char(2)));
hex(weight_string('abc' as char(2)))
1C471C60
select hex(weight_string('abc' as char(3)));
hex(weight_string('abc' as char(3)))
1C471C601C7A
select hex(weight_string('abc' as char(5)));
hex(weight_string('abc' as char(5)))
1C471C601C7A
select hex(weight_string('abc', 1, 2, 0xC0));
hex(weight_string('abc', 1, 2, 0xC0))
1C
select hex(weight_string('abc', 2, 2, 0xC0));
hex(weight_string('abc', 2, 2, 0xC0))
1C47
select hex(weight_string('abc', 3, 2, 0xC0));
hex(weight_string('abc', 3, 2, 0xC0))
1C471C
select hex(weight_string('abc', 4, 2, 0xC0));
hex(weight_string('abc', 4, 2, 0xC0))
1C471C60
select hex(weight_string('abc', 5, 2, 0xC0));
hex(weight_string('abc', 5, 2, 0xC0))
1C471C6000
select hex(weight_string('abc',25, 2, 0xC0));
hex(weight_string('abc',25, 2, 0xC0))
1C471C60000000000000000000000000000000000000000000
select hex(weight_string('abc', 1, 3, 0xC0));
hex(weight_string('abc', 1, 3, 0xC0))
1C
select hex(weight_string('abc', 2, 3, 0xC0));
hex(weight_string('abc', 2, 3, 0xC0))
1C47
select hex(weight_string('abc', 3, 3, 0xC0));
hex(weight_string('abc', 3, 3, 0xC0))
1C471C
select hex(weight_string('abc', 4, 3, 0xC0));
hex(weight_string('abc', 4, 3, 0xC0))
1C471C60
select hex(weight_string('abc', 5, 3, 0xC0));
hex(weight_string('abc', 5, 3, 0xC0))
1C471C601C
select hex(weight_string('abc',25, 3, 0xC0));
hex(weight_string('abc',25, 3, 0xC0))
1C471C601C7A00000000000000000000000000000000000000
select hex(weight_string('abc', 1, 4, 0xC0));
hex(weight_string('abc', 1, 4, 0xC0))
1C
select hex(weight_string('abc', 2, 4, 0xC0));
hex(weight_string('abc', 2, 4, 0xC0))
1C47
select hex(weight_string('abc', 3, 4, 0xC0));
hex(weight_string('abc', 3, 4, 0xC0))
1C471C
select hex(weight_string('abc', 4, 4, 0xC0));
hex(weight_string('abc', 4, 4, 0xC0))
1C471C60
select hex(weight_string('abc', 5, 4, 0xC0));
hex(weight_string('abc', 5, 4, 0xC0))
1C471C601C
select hex(weight_string('abc',25, 4, 0xC0));
hex(weight_string('abc',25, 4, 0xC0))
1C471C601C7A00000000000000000000000000000000000000
select @@collation_connection;
@@collation_connection
utf8mb4_0900_ai_ci
select hex(weight_string(cast(_latin1 0x80 as char)));
hex(weight_string(cast(_latin1 0x80 as char)))
1C2A
select hex(weight_string(cast(_latin1 0x808080 as char)));
hex(weight_string(cast(_latin1 0x808080 as char)))
1C2A1C2A1C2A
select hex(weight_string(cast(_latin1 0x808080 as char) as char(2)));
hex(weight_string(cast(_latin1 0x808080 as char) as char(2)))
1C2A1C2A
select hex(weight_string(cast(_latin1 0x808080 as char) as char(3)));
hex(weight_string(cast(_latin1 0x808080 as char) as char(3)))
1C2A1C2A1C2A
select hex(weight_string(cast(_latin1 0x808080 as char) as char(5)));
hex(weight_string(cast(_latin1 0x808080 as char) as char(5)))
1C2A1C2A1C2A
select hex(weight_string(cast(_latin1 0x808080 as char), 1, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 1, 2, 0xC0))
1C
select hex(weight_string(cast(_latin1 0x808080 as char), 2, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 2, 2, 0xC0))
1C2A
select hex(weight_string(cast(_latin1 0x808080 as char), 3, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 3, 2, 0xC0))
1C2A1C
select hex(weight_string(cast(_latin1 0x808080 as char), 4, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 4, 2, 0xC0))
1C2A1C2A
select hex(weight_string(cast(_latin1 0x808080 as char), 5, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 5, 2, 0xC0))
1C2A1C2A00
select hex(weight_string(cast(_latin1 0x808080 as char),25, 2, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char),25, 2, 0xC0))
1C2A1C2A000000000000000000000000000000000000000000
select hex(weight_string(cast(_latin1 0x808080 as char), 1, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 1, 3, 0xC0))
1C
select hex(weight_string(cast(_latin1 0x808080 as char), 2, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 2, 3, 0xC0))
1C2A
select hex(weight_string(cast(_latin1 0x808080 as char), 3, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 3, 3, 0xC0))
1C2A1C
select hex(weight_string(cast(_latin1 0x808080 as char), 4, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 4, 3, 0xC0))
1C2A1C2A
select hex(weight_string(cast(_latin1 0x808080 as char), 5, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 5, 3, 0xC0))
1C2A1C2A1C
select hex(weight_string(cast(_latin1 0x808080 as char),25, 3, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char),25, 3, 0xC0))
1C2A1C2A1C2A00000000000000000000000000000000000000
select hex(weight_string(cast(_latin1 0x808080 as char), 1, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 1, 4, 0xC0))
1C
select hex(weight_string(cast(_latin1 0x808080 as char), 2, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 2, 4, 0xC0))
1C2A
select hex(weight_string(cast(_latin1 0x808080 as char), 3, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 3, 4, 0xC0))
1C2A1C
select hex(weight_string(cast(_latin1 0x808080 as char), 4, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 4, 4, 0xC0))
1C2A1C2A
select hex(weight_string(cast(_latin1 0x808080 as char), 5, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char), 5, 4, 0xC0))
1C2A1C2A1C
select hex(weight_string(cast(_latin1 0x808080 as char),25, 4, 0xC0));
hex(weight_string(cast(_latin1 0x808080 as char),25, 4, 0xC0))
1C2A1C2A1C2A00000000000000000000000000000000000000
CREATE TABLE t1 AS SELECT repeat('a', 10) as c LIMIT 0;
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
INSERT INTO t1 VALUES (_utf32 0x0180),(_utf32 0x023A);
INSERT INTO t1 VALUES (_utf32 0x023B),(_utf32 0x023C);
INSERT INTO t1 VALUES (_utf32 0x023D),(_utf32 0x023E);
INSERT INTO t1 VALUES (_utf32 0x0241),(_utf32 0x0242);
INSERT INTO t1 VALUES (_utf32 0x0243),(_utf32 0x0244);
INSERT INTO t1 VALUES (_utf32 0x0245),(_utf32 0x0246);
INSERT INTO t1 VALUES (_utf32 0x0247),(_utf32 0x0248);
INSERT INTO t1 VALUES (_utf32 0x0249),(_utf32 0x024A);
INSERT INTO t1 VALUES (_utf32 0x024B),(_utf32 0x024C);
INSERT INTO t1 VALUES (_utf32 0x024D),(_utf32 0x024E);
INSERT INTO t1 VALUES (_utf32 0x024F),(_utf32 0x026B);
INSERT INTO t1 VALUES (_utf32 0x027D),(_utf32 0x0289);
INSERT INTO t1 VALUES (_utf32 0x028C);
INSERT INTO t1 VALUES (_utf32 0x037B), (_utf32 0x037C);
INSERT INTO t1 VALUES (_utf32 0x037D), (_utf32 0x03FD);
INSERT INTO t1 VALUES (_utf32 0x03FE), (_utf32 0x03FF);
INSERT INTO t1 VALUES (_utf32 0x04C0), (_utf32 0x04CF);
INSERT INTO t1 VALUES (_utf32 0x04F6), (_utf32 0x04F7);
INSERT INTO t1 VALUES (_utf32 0x04FA), (_utf32 0x04FB);
INSERT INTO t1 VALUES (_utf32 0x04FC), (_utf32 0x04FD);
INSERT INTO t1 VALUES (_utf32 0x04FE), (_utf32 0x04FF);
INSERT INTO t1 VALUES (_utf32 0x0510), (_utf32 0x0511);
INSERT INTO t1 VALUES (_utf32 0x0512), (_utf32 0x0513);
INSERT INTO t1 VALUES (_utf32 0x10A0), (_utf32 0x10A1);
INSERT INTO t1 VALUES (_utf32 0x10A2), (_utf32 0x10A3);
INSERT INTO t1 VALUES (_utf32 0x10A4), (_utf32 0x10A5);
INSERT INTO t1 VALUES (_utf32 0x10A6), (_utf32 0x10A7);
INSERT INTO t1 VALUES (_utf32 0x2D00), (_utf32 0x2D01);
INSERT INTO t1 VALUES (_utf32 0x2D02), (_utf32 0x2D03);
INSERT INTO t1 VALUES (_utf32 0x2D04), (_utf32 0x2D05);
INSERT INTO t1 VALUES (_utf32 0x2D06), (_utf32 0x2D07);
INSERT INTO t1 VALUES (_utf32 0x1D7D);
INSERT INTO t1 VALUES (_utf32 0x2132),(_utf32 0x214E);
INSERT INTO t1 VALUES (_utf32 0x2183),(_utf32 0x2184);
INSERT INTO t1 VALUES (_utf32 0x2C80), (_utf32 0x2C81);
INSERT INTO t1 VALUES (_utf32 0x2C82), (_utf32 0x2C83);
INSERT INTO t1 VALUES (_utf32 0x2C84), (_utf32 0x2C85);
INSERT INTO t1 VALUES (_utf32 0x2C86), (_utf32 0x2C87);
INSERT INTO t1 VALUES (_utf32 0x2C88), (_utf32 0x2C89);
INSERT INTO t1 VALUES (_utf32 0x2C8A), (_utf32 0x2C8B);
INSERT INTO t1 VALUES (_utf32 0x2C8C), (_utf32 0x2C8D);
INSERT INTO t1 VALUES (_utf32 0x2C8E), (_utf32 0x2C8F);
INSERT INTO t1 VALUES (_utf32 0x2C60), (_utf32 0x2C61);
INSERT INTO t1 VALUES (_utf32 0x2C62), (_utf32 0x2C63);
INSERT INTO t1 VALUES (_utf32 0x2C64), (_utf32 0x2C65);
INSERT INTO t1 VALUES (_utf32 0x2C66), (_utf32 0x2C67);
INSERT INTO t1 VALUES (_utf32 0x2C68), (_utf32 0x2C69);
INSERT INTO t1 VALUES (_utf32 0x2C6A), (_utf32 0x2C6B);
INSERT INTO t1 VALUES (_utf32 0x2C6C), (_utf32 0x2C75);
INSERT INTO t1 VALUES (_utf32 0x2C76);
INSERT INTO t1 VALUES (_utf32 0x2C00), (_utf32 0x2C01);
INSERT INTO t1 VALUES (_utf32 0x2C02), (_utf32 0x2C03);
INSERT INTO t1 VALUES (_utf32 0x2C04), (_utf32 0x2C05);
INSERT INTO t1 VALUES (_utf32 0x2C06), (_utf32 0x2C07);
INSERT INTO t1 VALUES (_utf32 0x2C30), (_utf32 0x2C31);
INSERT INTO t1 VALUES (_utf32 0x2C32), (_utf32 0x2C33);
INSERT INTO t1 VALUES (_utf32 0x2C34), (_utf32 0x2C35);
INSERT INTO t1 VALUES (_utf32 0x2C36), (_utf32 0x2C37);
INSERT INTO t1 VALUES (_utf32 0x10400), (_utf32 0x10401);
INSERT INTO t1 VALUES (_utf32 0x10402), (_utf32 0x10403);
INSERT INTO t1 VALUES (_utf32 0x10404), (_utf32 0x10405);
INSERT INTO t1 VALUES (_utf32 0x10406), (_utf32 0x10407);
INSERT INTO t1 VALUES (_utf32 0x10428), (_utf32 0x10429);
INSERT INTO t1 VALUES (_utf32 0x1042A), (_utf32 0x1042B);
INSERT INTO t1 VALUES (_utf32 0x1042C), (_utf32 0x1042D);
INSERT INTO t1 VALUES (_utf32 0x1042E), (_utf32 0x1042F);
INSERT INTO t1 VALUES (_utf32 0x0370);
INSERT INTO t1 VALUES (_utf32 0x0371);
INSERT INTO t1 VALUES (_utf32 0x0372);
INSERT INTO t1 VALUES (_utf32 0x0373);
INSERT INTO t1 VALUES (_utf32 0x0514);
INSERT INTO t1 VALUES (_utf32 0x0515);
INSERT INTO t1 VALUES (_utf32 0x0516);
INSERT INTO t1 VALUES (_utf32 0x0517);
INSERT INTO t1 VALUES (_utf32 0xA640);
INSERT INTO t1 VALUES (_utf32 0xA641);
INSERT INTO t1 VALUES (_utf32 0xA642);
INSERT INTO t1 VALUES (_utf32 0xA643);
INSERT INTO t1 VALUES (_utf32 0xA722);
INSERT INTO t1 VALUES (_utf32 0xA723);
INSERT INTO t1 VALUES (_utf32 0xA724);
INSERT INTO t1 VALUES (_utf32 0xA725);
INSERT INTO t1 VALUES (_utf32 0xA726);
INSERT INTO t1 VALUES (_utf32 0xA727);
INSERT INTO t1 VALUES (_utf32 0xA728);
INSERT INTO t1 VALUES (_utf32 0xA729);
INSERT INTO t1 VALUES (_utf32 0xA72A);
INSERT INTO t1 VALUES (_utf32 0xA72B);
INSERT INTO t1 VALUES (_utf32 0x2CEB);
INSERT INTO t1 VALUES (_utf32 0x2CEC);
INSERT INTO t1 VALUES (_utf32 0x2CED);
INSERT INTO t1 VALUES (_utf32 0x2CEE);
INSERT INTO t1 VALUES (_utf32 0x1FA01);
INSERT INTO t1 VALUES (_utf32 0x1FB01);
INSERT INTO t1 VALUES (_utf32 0x1FC01);
INSERT INTO t1 VALUES (_utf32 0x1FD01);
INSERT INTO t1 VALUES (_utf32 0x1F603);
INSERT INTO t1 VALUES (_utf32 0x1F604);
INSERT INTO t1 VALUES (_utf32 0x1F648);
INSERT INTO t1 VALUES (_utf32 0x1F64F);
INSERT INTO t1 VALUES (_utf32 0x2B759);
INSERT INTO t1 VALUES (_utf32 0x2B760);
INSERT INTO t1 VALUES (_utf32 0x2B761);
INSERT INTO t1 VALUES (_utf32 0x2B762);
INSERT INTO t1 VALUES (_utf32 0x16F00);
INSERT INTO t1 VALUES (_utf32 0x16F01);
INSERT INTO t1 VALUES (_utf32 0x08A2);
INSERT INTO t1 VALUES (_utf32 0x08A3);
INSERT INTO t1 VALUES (_utf32 0xA794);
INSERT INTO t1 VALUES (_utf32 0xA795);
INSERT INTO t1 VALUES (_utf32 0xA796);
INSERT INTO t1 VALUES (_utf32 0xA797);
INSERT INTO t1 VALUES (_utf32 0xAB37);
INSERT INTO t1 VALUES (_utf32 0xAB38);
INSERT INTO t1 VALUES (_utf32 0x10600);
INSERT INTO t1 VALUES (_utf32 0x10601);
INSERT INTO t1 VALUES (_utf32 0x10602);
INSERT INTO t1 VALUES (_utf32 0x10603);
INSERT INTO t1 VALUES (_utf32 0x1F800);
INSERT INTO t1 VALUES (_utf32 0x1F801);
INSERT INTO t1 VALUES (_utf32 0x1F802);
INSERT INTO t1 VALUES (_utf32 0x1F803);
INSERT INTO t1 VALUES (_utf32 0x10C92);
INSERT INTO t1 VALUES (_utf32 0x10C93);
INSERT INTO t1 VALUES (_utf32 0x10C94);
INSERT INTO t1 VALUES (_utf32 0x10C95);
INSERT INTO t1 VALUES (_utf32 0x2B836);
INSERT INTO t1 VALUES (_utf32 0x2B837);
INSERT INTO t1 VALUES (_utf32 0x2B838);
INSERT INTO t1 VALUES (_utf32 0x2B839);
SELECT hex(c), hex(lower(c)), hex(upper(c)), hex(weight_string(c)), c
FROM t1 ORDER BY c, BINARY c;
hex(c)	hex(lower(c))	hex(upper(c))	hex(weight_string(c))	c
F09F9883	F09F9883	F09F9883	15FE	😃
F09F9884	F09F9884	F09F9884	15FF	😄
F09F9988	F09F9988	F09F9988	1643	🙈
F09F998F	F09F998F	F09F998F	164A	🙏
F09FA080	F09FA080	F09FA080	17AB	🠀
F09FA081	F09FA081	F09FA081	17AC	🠁
F09FA082	F09FA082	F09FA082	17AD	🠂
F09FA083	F09FA083	F09FA083	17AE	🠃
C8BA		C8BA	1C4C	Ⱥ
E2B1A5	E2B1A5	C8BA	1C4C	ⱥ
C680	C680	C983	1C68	ƀ
C983	C680	C983	1C68	Ƀ
EA9E96	EA9E97	EA9E96	1C6F	Ꞗ
EA9E97	EA9E97	EA9E96	1C6F	ꞗ
C8BB	C8BC	C8BB	1C7F	Ȼ
C8BC	C8BC	C8BB	1C7F	ȼ
EA9E94	EA9E94	EA9E94	1C84	ꞔ
E28683	E28684	E28683	1C8D	Ↄ
E28684	E28684	E28683	1C8D	ↄ
C986	C987	C986	1CB1	Ɇ
C987	C987	C986	1CB1	ɇ
E284B2	E2858E	E284B2	1CF2	Ⅎ
E2858E	E2858E	E284B2	1CF2	ⅎ
EA9E95	EA9E95	EA9E95	1D24	ꞕ
E2B1A7	E2B1A8	E2B1A7	1D29	Ⱨ
E2B1A8	E2B1A8	E2B1A7	1D29	ⱨ
E2B1B5	E2B1B6	E2B1B5	1D2A	Ⱶ
E2B1B6	E2B1B6	E2B1B5	1D2A	ⱶ
EA9CA6	EA9CA7	EA9CA6	1D2B	Ꜧ
EA9CA7	EA9CA7	EA9CA6	1D2B	ꜧ
C988	C989	C988	1D55	Ɉ
C989	C989	C988	1D55	ɉ
E2B1A9	E2B1AA	E2B1A9	1D6F	Ⱪ
E2B1AA	E2B1AA	E2B1A9	1D6F	ⱪ
C8BD	C69A	C8BD	1D82	Ƚ
E2B1A0	E2B1A1	E2B1A0	1D86	Ⱡ
E2B1A1	E2B1A1	E2B1A0	1D86	ⱡ
C9AB	C9AB		1D87	ɫ
E2B1A2	C9AB	E2B1A2	1D87	Ɫ
EAACB8	EAACB8	EAACB8	1D8B	ꬸ
EAACB7	EAACB7	EAACB7	1D91	ꬷ
E1B5BD	E1B5BD	E2B1A3	1E11	ᵽ
E2B1A3	E1B5BD	E2B1A3	1E11	Ᵽ
C98A	C98B	C98A	1E2B	Ɋ
C98B	C98B	C98A	1E2B	ɋ
C98C	C98D	C98C	1E3F	Ɍ
C98D	C98D	C98C	1E3F	ɍ
C9BD	C9BD		1E57	ɽ
E2B1A4	C9BD	E2B1A4	1E57	Ɽ
EA9CA8	EA9CA9	EA9CA8	1E951F21	Ꜩ
EA9CA9	EA9CA9	EA9CA8	1E951F21	ꜩ
C8BE		C8BE	1E9E	Ⱦ
E2B1A6	E2B1A6	C8BE	1E9E	ⱦ
C984	CA89	C984	1EC0	Ʉ
CA89	CA89	C984	1EC0	ʉ
C985	CA8C	C985	1EF1	Ʌ
CA8C	CA8C	C985	1EF1	ʌ
C98E	C98F	C98E	1F13	Ɏ
C98F	C98F	C98E	1F13	ɏ
E2B1AB	E2B1AC	E2B1AB	1F3C	Ⱬ
E2B1AC	E2B1AC	E2B1AB	1F3C	ⱬ
EA9CAA	EA9CAB	EA9CAA	1F66	Ꜫ
EA9CAB	EA9CAB	EA9CAA	1F66	ꜫ
C981	C982	C981	1F79	Ɂ
C982	C982	C981	1F79	ɂ
EA9CA2	EA9CA3	EA9CA2	1F81	Ꜣ
EA9CA3	EA9CA3	EA9CA2	1F81	ꜣ
EA9CA4	EA9CA5	EA9CA4	1F8C	Ꜥ
EA9CA5	EA9CA5	EA9CA4	1F8C	ꜥ
CDB0	CDB1	CDB0	1FC3	Ͱ
CDB1	CDB1	CDB0	1FC3	ͱ
CDBC	CDBC	CFBE	1FD8	ͼ
CFBE	CDBC	CFBE	1FD8	Ͼ
CDBB	CDBB	CFBD	1FD9	ͻ
CFBD	CDBB	CFBD	1FD9	Ͻ
CDBD	CDBD	CFBF	1FDA	ͽ
CFBF	CDBD	CFBF	1FDA	Ͽ
CDB2	CDB3	CDB2	1FE4	Ͳ
CDB3	CDB3	CDB2	1FE4	ͳ
E2B280	E2B281	E2B280	1FE6	Ⲁ
E2B281	E2B281	E2B280	1FE6	ⲁ
E2B282	E2B283	E2B282	1FE7	Ⲃ
E2B283	E2B283	E2B282	1FE7	ⲃ
E2B284	E2B285	E2B284	1FE8	Ⲅ
E2B285	E2B285	E2B284	1FE8	ⲅ
E2B286	E2B287	E2B286	1FE9	Ⲇ
E2B287	E2B287	E2B286	1FE9	ⲇ
E2B288	E2B289	E2B288	1FEA	Ⲉ
E2B289	E2B289	E2B288	1FEA	ⲉ
E2B28A	E2B28B	E2B28A	1FEC	Ⲋ
E2B28B	E2B28B	E2B28A	1FEC	ⲋ
E2B28C	E2B28D	E2B28C	1FED	Ⲍ
E2B28D	E2B28D	E2B28C	1FED	ⲍ
E2B28E	E2B28F	E2B28E	1FEE	Ⲏ
E2B28F	E2B28F	E2B28E	1FEE	ⲏ
E2B3AB	E2B3AC	E2B3AB	2006	Ⳬ
E2B3AC	E2B3AC	E2B3AB	2006	ⳬ
E2B3AD	E2B3AE	E2B3AD	2016	Ⳮ
E2B3AE	E2B3AE	E2B3AD	2016	ⳮ
D3BA	D3BB	D3BA	203E	Ӻ
D3BB	D3BB	D3BA	203E	ӻ
D3B6	D3B7	D3B6	2046	Ӷ
D3B7	D3B7	D3B6	2046	ӷ
EA9980	EA9981	EA9980	2070	Ꙁ
EA9981	EA9981	EA9980	2070	ꙁ
D490	D491	D490	2072	Ԑ
D491	D491	D490	2072	ԑ
EA9982	EA9983	EA9982	2073	Ꙃ
EA9983	EA9983	EA9982	2073	ꙃ
D492	D493	D492	20BA	Ԓ
D493	D493	D492	20BA	ԓ
D494	D495	D494	20C2	Ԕ
D495	D495	D494	20C2	ԕ
D496	D497	D496	2104	Ԗ
D497	D497	D496	2104	ԗ
D3BC	D3BD	D3BC	2136	Ӽ
D3BD	D3BD	D3BC	2136	ӽ
D3BE	D3BF	D3BE	213A	Ӿ
D3BF	D3BF	D3BE	213A	ӿ
D380	D38F	D380	21E1	Ӏ
D38F	D38F	D380	21E1	ӏ
E2B080	E2B0B0	E2B080	21E5	Ⰰ
E2B0B0	E2B0B0	E2B080	21E5	ⰰ
E2B081	E2B0B1	E2B081	21E6	Ⰱ
E2B0B1	E2B0B1	E2B081	21E6	ⰱ
E2B082	E2B0B2	E2B082	21E7	Ⰲ
E2B0B2	E2B0B2	E2B082	21E7	ⰲ
E2B083	E2B0B3	E2B083	21E8	Ⰳ
E2B0B3	E2B0B3	E2B083	21E8	ⰳ
E2B084	E2B0B4	E2B084	21E9	Ⰴ
E2B0B4	E2B0B4	E2B084	21E9	ⰴ
E2B085	E2B0B5	E2B085	21EA	Ⰵ
E2B0B5	E2B0B5	E2B085	21EA	ⰵ
E2B086	E2B0B6	E2B086	21EB	Ⰶ
E2B0B6	E2B0B6	E2B086	21EB	ⰶ
E2B087	E2B0B7	E2B087	21EC	Ⰷ
E2B0B7	E2B0B7	E2B087	21EC	ⰷ
E182A0	E2B480	E182A0	223B	Ⴀ
E2B480	E2B480	E182A0	223B	ⴀ
E182A1	E2B481	E182A1	223D	Ⴁ
E2B481	E2B481	E182A1	223D	ⴁ
E182A2	E2B482	E182A2	223F	Ⴂ
E2B482	E2B482	E182A2	223F	ⴂ
E182A3	E2B483	E182A3	2241	Ⴃ
E2B483	E2B483	E182A3	2241	ⴃ
E182A4	E2B484	E182A4	2243	Ⴄ
E2B484	E2B484	E182A4	2243	ⴄ
E182A5	E2B485	E182A5	2245	Ⴅ
E2B485	E2B485	E182A5	2245	ⴅ
E182A6	E2B486	E182A6	2247	Ⴆ
E2B486	E2B486	E182A6	2247	ⴆ
E182A7	E2B487	E182A7	224B	Ⴇ
E2B487	E2B487	E182A7	224B	ⴇ
E0A2A2	E0A2A2	E0A2A2	232B	ࢢ
E0A2A3	E0A2A3	E0A2A3	236D	ࢣ
F090B292	F090B392	F090B292	3712	𐲒
F090B293	F090B393	F090B293	3713	𐲓
F090B294	F090B394	F090B294	3714	𐲔
F090B295	F090B395	F090B295	3715	𐲕
F096BC80	F096BC80	F096BC80	427A	𖼀
F096BC81	F096BC81	F096BC81	427B	𖼁
F0909080	F09090A8	F0909080	4452	𐐀
F09090A8	F09090A8	F0909080	4452	𐐨
F0909081	F09090A9	F0909081	4453	𐐁
F09090A9	F09090A9	F0909081	4453	𐐩
F0909082	F09090AA	F0909082	4454	𐐂
F09090AA	F09090AA	F0909082	4454	𐐪
F0909083	F09090AB	F0909083	4455	𐐃
F09090AB	F09090AB	F0909083	4455	𐐫
F0909084	F09090AC	F0909084	4456	𐐄
F09090AC	F09090AC	F0909084	4456	𐐬
F0909085	F09090AD	F0909085	4457	𐐅
F09090AD	F09090AD	F0909085	4457	𐐭
F0909086	F09090AE	F0909086	4458	𐐆
F09090AE	F09090AE	F0909086	4458	𐐮
F0909087	F09090AF	F0909087	4459	𐐇
F09090AF	F09090AF	F0909087	4459	𐐯
F0909880	F0909880	F0909880	46BA	𐘀
F0909881	F0909881	F0909881	46BB	𐘁
F0909882	F0909882	F0909882	46BC	𐘂
F0909883	F0909883	F0909883	46BD	𐘃
F0AB9D99	F0AB9D99	F0AB9D99	FB85B759	𫝙
F0AB9DA0	F0AB9DA0	F0AB9DA0	FB85B760	𫝠
F0AB9DA1	F0AB9DA1	F0AB9DA1	FB85B761	𫝡
F0AB9DA2	F0AB9DA2	F0AB9DA2	FB85B762	𫝢
F0ABA0B6	F0ABA0B6	F0ABA0B6	FB85B836	𫠶
F0ABA0B7	F0ABA0B7	F0ABA0B7	FB85B837	𫠷
F0ABA0B8	F0ABA0B8	F0ABA0B8	FB85B838	𫠸
F0ABA0B9	F0ABA0B9	F0ABA0B9	FB85B839	𫠹
F09FA881	F09FA881	F09FA881	FBC3FA01	🨁
F09FAC81	F09FAC81	F09FAC81	FBC3FB01	🬁
F09FB081	F09FB081	F09FB081	FBC3FC01	🰁
F09FB481	F09FB481	F09FB481	FBC3FD01	🴁
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
INSERT INTO t1 VALUES ('a');
INSERT INTO t1 VALUES (concat(_utf32 0x61, _utf32 0xFFFF));
INSERT INTO t1 VALUES (concat(_utf32 0x61, _utf32 0x10FFFF));
INSERT INTO t1 VALUES (concat(_utf32 0x61, _utf32 0x10400));
SELECT hex(c), hex(weight_string(c)) FROM t1 WHERE c LIKE 'a%' ORDER BY c;
hex(c)	hex(weight_string(c))
61	1C47
61F0909080	1C474452
61EFBFBF	1C47FBC1FFFF
61F48FBFBF	1C47FBE1FFFF
SELECT hex(c), hex(weight_string(c)), c FROM t1 WHERE c LIKE _utf32 0x10400 ORDER BY c, BINARY c;
hex(c)	hex(weight_string(c))	c
F0909080	4452	𐐀
F09090A8	4452	𐐨
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
SELECT hex(c), hex(weight_string(c)), c FROM t1 WHERE c LIKE _utf32 0x10428 ORDER BY c, BINARY c;
hex(c)	hex(weight_string(c))	c
F0909080	4452	𐐀
F09090A8	4452	𐐨
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
ALTER TABLE t1 ADD KEY(c);
EXPLAIN SELECT hex(c) FROM t1 WHERE c LIKE 'a%' ORDER BY c;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	c	c	43	NULL	4	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select hex(`test`.`t1`.`c`) AS `hex(c)` from `test`.`t1` where (`test`.`t1`.`c` like 'a%') order by `test`.`t1`.`c`
SELECT hex(c), hex(weight_string(c)) FROM t1 WHERE c LIKE 'a%' ORDER BY c;
hex(c)	hex(weight_string(c))
61	1C47
61F0909080	1C474452
61EFBFBF	1C47FBC1FFFF
61F48FBFBF	1C47FBE1FFFF
SELECT hex(c), hex(weight_string(c)), c FROM t1 WHERE c LIKE _utf32 0x10400 ORDER BY c, BINARY c;
hex(c)	hex(weight_string(c))	c
F0909080	4452	𐐀
F09090A8	4452	𐐨
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
SELECT hex(c), hex(weight_string(c)), c FROM t1 WHERE c LIKE _utf32 0x10428 ORDER BY c, BINARY c;
hex(c)	hex(weight_string(c))	c
F0909080	4452	𐐀
F09090A8	4452	𐐨
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
DROP TABLE t1;
SELECT 'a' = 'a ';
'a' = 'a '
0
SELECT 'a\0' < 'a';
'a\0' < 'a'
0
SELECT 'a\0' < 'a ';
'a\0' < 'a '
1
SELECT 'a\t' < 'a';
'a\t' < 'a'
0
SELECT 'a\t' < 'a ';
'a\t' < 'a '
1
SELECT 'a' LIKE 'a';
'a' LIKE 'a'
1
SELECT 'A' LIKE 'a';
'A' LIKE 'a'
1
SELECT _utf8mb4 0xD0B0D0B1D0B2 LIKE CONCAT(_utf8mb4'%',_utf8mb4 0xD0B1,_utf8mb4 '%');
_utf8mb4 0xD0B0D0B1D0B2 LIKE CONCAT(_utf8mb4'%',_utf8mb4 0xD0B1,_utf8mb4 '%')
1
SELECT is_ipv4(inet_ntoa('1'));
is_ipv4(inet_ntoa('1'))
1
SELECT is_ipv6(inet_ntoa('1'));
is_ipv6(inet_ntoa('1'))
0
SELECT HEX(inet6_aton(inet_ntoa('1')));
HEX(inet6_aton(inet_ntoa('1')))
00000001
SELECT inet6_ntoa(inet_ntoa('1'));
inet6_ntoa(inet_ntoa('1'))
NULL
#
# Bug#14040277 UNINITIALIZED VALUE REFERENCED IN STR_TO_IPV6
#
SELECT inet6_aton(soundex('a'));
inet6_aton(soundex('a'))
NULL
#
# Bug#19047425 UNINITIALISED VALUE IN STR_TO_IPV6
#
do is_ipv4_mapped(inet6_aton(convert(_ascii "a:" using utf8mb4)));
CREATE TABLE t1 (c VARCHAR(10) CHARACTER SET utf8mb4);
INSERT INTO t1 VALUES (_utf8mb4 0xF09090A7), (_utf8mb4 0xEA8B93), (_utf8mb4 0xC4BC), (_utf8mb4 0xC6AD), (_utf8mb4 0xF090918F), (_utf8mb4 0xEAAD8B);
SELECT HEX(ANY_VALUE(c)), COUNT(c) FROM t1 GROUP BY c COLLATE utf8mb4_0900_ai_ci;
HEX(ANY_VALUE(c))	COUNT(c)
C4BC	1
C6AD	1
EA8B93	1
EAAD8B	1
F09090A7	2
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10), b VARCHAR(10)) COLLATE utf8mb4_0900_ai_ci;
INSERT INTO t1 VALUES(_utf16 0xAC00, _utf16 0x326E), (_utf16 0xAD, _utf16 0xA0),
(_utf16 0xC6, _utf16 0x41), (_utf16 0xC6, _utf16 0xAA), (_utf16 0xA73A, _utf16 0xA738);
SELECT a = b FROM t1;
a = b
1
0
0
0
1
DROP TABLE t1;
SET NAMES utf8mb4;
CREATE TABLE t1 (c1 CHAR(10) CHARACTER SET utf8mb4);
insert into t1 values ('A'),('a');
insert into t1 values ('B'),('b');
insert into t1 values ('C'),('c');
insert into t1 values ('D'),('d');
insert into t1 values ('E'),('e');
insert into t1 values ('F'),('f');
insert into t1 values ('G'),('g');
insert into t1 values ('H'),('h');
insert into t1 values ('I'),('i');
insert into t1 values ('J'),('j');
insert into t1 values ('K'),('k');
insert into t1 values ('L'),('l');
insert into t1 values ('M'),('m');
insert into t1 values ('N'),('n');
insert into t1 values ('O'),('o');
insert into t1 values ('P'),('p');
insert into t1 values ('Q'),('q');
insert into t1 values ('R'),('r');
insert into t1 values ('S'),('s');
insert into t1 values ('T'),('t');
insert into t1 values ('U'),('u');
insert into t1 values ('V'),('v');
insert into t1 values ('W'),('w');
insert into t1 values ('X'),('x');
insert into t1 values ('Y'),('y');
insert into t1 values ('Z'),('z');
insert into t1 values (_ucs2 0x00e0),(_ucs2 0x00c0);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e1),(_ucs2 0x00c1);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e2),(_ucs2 0x00c2);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e3),(_ucs2 0x00c3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e4),(_ucs2 0x00c4);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e5),(_ucs2 0x00c5);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e6),(_ucs2 0x00c6);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e7),(_ucs2 0x00c7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e8),(_ucs2 0x00c8);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00e9),(_ucs2 0x00c9);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ea),(_ucs2 0x00ca);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00eb),(_ucs2 0x00cb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ec),(_ucs2 0x00cc);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ed),(_ucs2 0x00cd);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ee),(_ucs2 0x00ce);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ef),(_ucs2 0x00cf);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f0),(_ucs2 0x00d0);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f1),(_ucs2 0x00d1);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f2),(_ucs2 0x00d2);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f3),(_ucs2 0x00d3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f4),(_ucs2 0x00d4);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f5),(_ucs2 0x00d5);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f6),(_ucs2 0x00d6);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f7),(_ucs2 0x00d7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f8),(_ucs2 0x00d8);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00f9),(_ucs2 0x00d9);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fa),(_ucs2 0x00da);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fb),(_ucs2 0x00db);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fc),(_ucs2 0x00dc);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fd),(_ucs2 0x00dd);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00fe),(_ucs2 0x00de);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x00ff),(_ucs2 0x00df);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0100),(_ucs2 0x0101),(_ucs2 0x0102),(_ucs2 0x0103);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0104),(_ucs2 0x0105),(_ucs2 0x0106),(_ucs2 0x0107);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0108),(_ucs2 0x0109),(_ucs2 0x010a),(_ucs2 0x010b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x010c),(_ucs2 0x010d),(_ucs2 0x010e),(_ucs2 0x010f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0110),(_ucs2 0x0111),(_ucs2 0x0112),(_ucs2 0x0113);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0114),(_ucs2 0x0115),(_ucs2 0x0116),(_ucs2 0x0117);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0118),(_ucs2 0x0119),(_ucs2 0x011a),(_ucs2 0x011b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x011c),(_ucs2 0x011d),(_ucs2 0x011e),(_ucs2 0x011f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0120),(_ucs2 0x0121),(_ucs2 0x0122),(_ucs2 0x0123);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0124),(_ucs2 0x0125),(_ucs2 0x0126),(_ucs2 0x0127);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0128),(_ucs2 0x0129),(_ucs2 0x012a),(_ucs2 0x012b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x012c),(_ucs2 0x012d),(_ucs2 0x012e),(_ucs2 0x012f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0130),(_ucs2 0x0131),(_ucs2 0x0132),(_ucs2 0x0133);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0134),(_ucs2 0x0135),(_ucs2 0x0136),(_ucs2 0x0137);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0138),(_ucs2 0x0139),(_ucs2 0x013a),(_ucs2 0x013b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x013c),(_ucs2 0x013d),(_ucs2 0x013e),(_ucs2 0x013f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0140),(_ucs2 0x0141),(_ucs2 0x0142),(_ucs2 0x0143);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0144),(_ucs2 0x0145),(_ucs2 0x0146),(_ucs2 0x0147);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0148),(_ucs2 0x0149),(_ucs2 0x014a),(_ucs2 0x014b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x014c),(_ucs2 0x014d),(_ucs2 0x014e),(_ucs2 0x014f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0150),(_ucs2 0x0151),(_ucs2 0x0152),(_ucs2 0x0153);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0154),(_ucs2 0x0155),(_ucs2 0x0156),(_ucs2 0x0157);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0158),(_ucs2 0x0159),(_ucs2 0x015a),(_ucs2 0x015b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x015c),(_ucs2 0x015d),(_ucs2 0x015e),(_ucs2 0x015f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0160),(_ucs2 0x0161),(_ucs2 0x0162),(_ucs2 0x0163);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0164),(_ucs2 0x0165),(_ucs2 0x0166),(_ucs2 0x0167);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0168),(_ucs2 0x0169),(_ucs2 0x016a),(_ucs2 0x016b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x016c),(_ucs2 0x016d),(_ucs2 0x016e),(_ucs2 0x016f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0170),(_ucs2 0x0171),(_ucs2 0x0172),(_ucs2 0x0173);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0174),(_ucs2 0x0175),(_ucs2 0x0176),(_ucs2 0x0177);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0178),(_ucs2 0x0179),(_ucs2 0x017a),(_ucs2 0x017b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x017c),(_ucs2 0x017d),(_ucs2 0x017e),(_ucs2 0x017f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0180),(_ucs2 0x0181),(_ucs2 0x0182),(_ucs2 0x0183);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0184),(_ucs2 0x0185),(_ucs2 0x0186),(_ucs2 0x0187);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0188),(_ucs2 0x0189),(_ucs2 0x018a),(_ucs2 0x018b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x018c),(_ucs2 0x018d),(_ucs2 0x018e),(_ucs2 0x018f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0190),(_ucs2 0x0191),(_ucs2 0x0192),(_ucs2 0x0193);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0194),(_ucs2 0x0195),(_ucs2 0x0196),(_ucs2 0x0197);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0198),(_ucs2 0x0199),(_ucs2 0x019a),(_ucs2 0x019b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x019c),(_ucs2 0x019d),(_ucs2 0x019e),(_ucs2 0x019f);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01a0),(_ucs2 0x01a1),(_ucs2 0x01a2),(_ucs2 0x01a3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01a4),(_ucs2 0x01a5),(_ucs2 0x01a6),(_ucs2 0x01a7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01a8),(_ucs2 0x01a9),(_ucs2 0x01aa),(_ucs2 0x01ab);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01ac),(_ucs2 0x01ad),(_ucs2 0x01ae),(_ucs2 0x01af);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01b0),(_ucs2 0x01b1),(_ucs2 0x01b2),(_ucs2 0x01b3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01b4),(_ucs2 0x01b5),(_ucs2 0x01b6),(_ucs2 0x01b7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01b8),(_ucs2 0x01b9),(_ucs2 0x01ba),(_ucs2 0x01bb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01bc),(_ucs2 0x01bd),(_ucs2 0x01be),(_ucs2 0x01bf);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01c0),(_ucs2 0x01c1),(_ucs2 0x01c2),(_ucs2 0x01c3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01c4),(_ucs2 0x01c5),(_ucs2 0x01c6),(_ucs2 0x01c7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01c8),(_ucs2 0x01c9),(_ucs2 0x01ca),(_ucs2 0x01cb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01cc),(_ucs2 0x01cd),(_ucs2 0x01ce),(_ucs2 0x01cf);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01d0),(_ucs2 0x01d1),(_ucs2 0x01d2),(_ucs2 0x01d3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01d4),(_ucs2 0x01d5),(_ucs2 0x01d6),(_ucs2 0x01d7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01d8),(_ucs2 0x01d9),(_ucs2 0x01da),(_ucs2 0x01db);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01dc),(_ucs2 0x01dd),(_ucs2 0x01de),(_ucs2 0x01df);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01e0),(_ucs2 0x01e1),(_ucs2 0x01e2),(_ucs2 0x01e3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01e4),(_ucs2 0x01e5),(_ucs2 0x01e6),(_ucs2 0x01e7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01e8),(_ucs2 0x01e9),(_ucs2 0x01ea),(_ucs2 0x01eb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01ec),(_ucs2 0x01ed),(_ucs2 0x01ee),(_ucs2 0x01ef);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01f0),(_ucs2 0x01f1),(_ucs2 0x01f2),(_ucs2 0x01f3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01f4),(_ucs2 0x01f5),(_ucs2 0x01f6),(_ucs2 0x01f7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01f8),(_ucs2 0x01f9),(_ucs2 0x01fa),(_ucs2 0x01fb);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x01fc),(_ucs2 0x01fd),(_ucs2 0x01fe),(_ucs2 0x01ff);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EA0),(_ucs2 0x1EA1),(_ucs2 0x1EA2),(_ucs2 0x1EA3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EA4),(_ucs2 0x1EA5),(_ucs2 0x1EA6),(_ucs2 0x1EA7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EA8),(_ucs2 0x1EA9),(_ucs2 0x1EAA),(_ucs2 0x1EAB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EAC),(_ucs2 0x1EAD),(_ucs2 0x1EAE),(_ucs2 0x1EAF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EB0),(_ucs2 0x1EB1),(_ucs2 0x1EB2),(_ucs2 0x1EB3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EB4),(_ucs2 0x1EB5),(_ucs2 0x1EB6),(_ucs2 0x1EB7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EB8),(_ucs2 0x1EB9),(_ucs2 0x1EBA),(_ucs2 0x1EBB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EBC),(_ucs2 0x1EBD),(_ucs2 0x1EBE),(_ucs2 0x1EBF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EC0),(_ucs2 0x1EC1),(_ucs2 0x1EC2),(_ucs2 0x1EC3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EC4),(_ucs2 0x1EC5),(_ucs2 0x1EC6),(_ucs2 0x1EC7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EC8),(_ucs2 0x1EC9),(_ucs2 0x1ECA),(_ucs2 0x1ECB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1ECC),(_ucs2 0x1ECD),(_ucs2 0x1ECE),(_ucs2 0x1ECF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1ED0),(_ucs2 0x1ED1),(_ucs2 0x1ED2),(_ucs2 0x1ED3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1ED4),(_ucs2 0x1ED5),(_ucs2 0x1ED6),(_ucs2 0x1ED7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1ED8),(_ucs2 0x1ED9),(_ucs2 0x1EDA),(_ucs2 0x1EDB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EDC),(_ucs2 0x1EDD),(_ucs2 0x1EDE),(_ucs2 0x1EDF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EE0),(_ucs2 0x1EE1),(_ucs2 0x1EE2),(_ucs2 0x1EE3);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EE4),(_ucs2 0x1EE5),(_ucs2 0x1EE6),(_ucs2 0x1EE7);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EE8),(_ucs2 0x1EE9),(_ucs2 0x1EEA),(_ucs2 0x1EEB);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EEC),(_ucs2 0x1EED),(_ucs2 0x1EEE),(_ucs2 0x1EEF);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
INSERT INTO t1 VALUES (_ucs2 0x1EF0),(_ucs2 0x1EF1);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values ('AA'),('Aa'),('aa'),('aA');
insert into t1 values ('AE'),('Ae'),('ae'),('aE');
insert into t1 values ('CH'),('Ch'),('ch'),('cH');
insert into t1 values ('DZ'),('Dz'),('dz'),('dZ');
insert into t1 values ('DŽ'),('Dž'),('dž'),('dŽ');
insert into t1 values ('IJ'),('Ij'),('ij'),('iJ');
insert into t1 values ('LJ'),('Lj'),('lj'),('lJ');
insert into t1 values ('LL'),('Ll'),('ll'),('lL');
insert into t1 values ('NJ'),('Nj'),('nj'),('nJ');
insert into t1 values ('OE'),('Oe'),('oe'),('oE');
insert into t1 values ('SS'),('Ss'),('ss'),('sS');
insert into t1 values ('RR'),('Rr'),('rr'),('rR');
insert into t1 values (_ucs2 0x0218), (_ucs2 0x0219), (_ucs2 0x021a), (_ucs2 0x021b);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0d96), (_ucs2 0x0da4), (_ucs2 0x0da5);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values (_ucs2 0x0064017e), (_ucs2 0x0044017e), (_ucs2 0x0044017d);
Warnings:
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Warning	1287	'ucs2' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values ('CS'),('Cs'),('cs'),('cS');
insert into t1 values ('DZS'),('DZs'),('Dzs'),('DzS');
insert into t1 values ('dZS'),('dZs'),('dzs'),('dzS');
insert into t1 values ('GY'),('Gy'),('gy'),('gY');
insert into t1 values ('LY'),('Ly'),('ly'),('lY');
insert into t1 values ('NY'),('Ny'),('ny'),('nY');
insert into t1 values ('SZ'),('Sz'),('sz'),('sZ');
insert into t1 values ('TY'),('Ty'),('ty'),('tY');
insert into t1 values ('ZS'),('Zs'),('zs'),('zS');
insert into t1 values ('RR'),('Rr'),('rr'),('rR');
insert into t1 values ('ccs'),('Ccs'),('CCS'),('cCS');
insert into t1 values ('ddz'),('Ddz'),('DDZ'),('dDZ');
insert into t1 values ('ddzs'),('Ddzs'),('DDZS'),('dDZS');
insert into t1 values ('ggy'),('Ggy'),('GGY'),('gGY');
insert into t1 values ('lly'),('Lly'),('LLY'),('lLY');
insert into t1 values ('nny'),('Nny'),('NNY'),('nNY');
insert into t1 values ('ssz'),('Ssz'),('SSZ'),('sSZ');
insert into t1 values ('tty'),('Tty'),('TTY'),('tTY');
insert into t1 values ('zzs'),('Zzs'),('ZZS'),('zZS');
insert into t1 values ('UE'),('Ue'),('ue'),('uE');
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D6 00D8 00F2 00F3 00F4 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_de_pb_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C5 00E0 00E1 00E2 00E3 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00C4 00E4 01DE 01DF 00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D8 00F2 00F3 00F4 00F5 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
00D6 00F6 004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00F9 00FA 00FB 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00DC 00FC 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_is_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C2 00C3 00E0 00E2 00E3 0100 0101 0102 0103 0104 0105 01CD 01CE 01E0 01E1 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065
00C1 00E1
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0187 0188
0044 0064 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
00D0 00F0
0189
018A
018B 018C
0045 0065 00C8 00CA 00CB 00E8 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
00C9 00E9
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CE 00CF 00EC 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
00CD 00ED
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D4 00D5 00F2 00F4 00F5 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
00D3 00F3 1EDA 1EDB
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DB 00DC 00F9 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
00DA 00FA 1EE8 1EE9
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00FF 0176 0177 0178
01B3 01B4
00DD 00FD
005A 007A 0179 017A 017B 017C 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
00C4 00E4 01DE 01DF 00C6 00E6 01E2 01E3 01FC 01FD
00D6 00D8 00F6 00F8 01FE 01FF
00C5 00E5 01FA 01FB
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_lv_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0187 0188
010C 010D
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0064005A 0064007A 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0044017D 0044017D 0044017E 0044017E 0064017D 0064017E 0064017E
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0122 0123
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB 0059 0079 00DD 00FD 00FF 0176 0177 0178
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 01E8 01E9
0198 0199
0136 0137
004C 006C 0139 013A 013D 013E 013F 0140 0141 0142
004C0059 004C0079 006C0059 006C0079
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
019A
019B
013B 013C
004D 006D
004E 006E 00D1 00F1 0143 0144 0147 0148 01F8 01F9
004E0059 004E0079 006E0059 006E0079
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
019D
019E
014A 014B
0145 0146
004F 006F 00D2 00D3 00D4 00D5 00D6 00D8 00F2 00F3 00F4 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0156 0157
0053 0073 015A 015B 015C 015D 015E 015F 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
01A9
01AA
0160 0161
0054 0074 0162 0163 0164 0165 021A 021B
00540059 00540079 00740059 00740079
01BE
005400540059 005400740079 007400540059 007400740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
01B3 01B4
005A 007A 0179 017A 017B 017C
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
017D 017E
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_ro_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C3 00C4 00C5 00E0 00E1 00E3 00E4 00E5 0100 0101 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0102 0103 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00C2 00E2 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CF 00EC 00ED 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
00CE 00EE
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D6 00D8 00F2 00F3 00F4 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 0160 0161 017F
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
015E 015F 0218 0219
01A9
01AA
0054 0074 0164 0165
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0162 0163 021A 021B
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_sl_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0108 0109 010A 010B
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
010C 010D
0106 0107
0187 0188
0044 0064 00D0 00F0 010E 010F
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0064005A 0064007A 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0044017D 0044017D 0044017E 0044017E 0064017D 0064017E 0064017E
0110 0111
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D6 00D8 00F2 00F3 00F4 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
0160 0161
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
017D 017E
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_pl_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0104 0105
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0106 0107
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
0118 0119
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
0141 0142
019A
019B
004D 006D
004E 006E 00D1 00F1 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
0143 0144
019D
019E
014A 014B
004F 006F 00D2 00D4 00D5 00D6 00D8 00F2 00F4 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
00D3 00F3 1EDA 1EDB
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015C 015D 015E 015F 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
015A 015B
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
0179 017A
017B 017C
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_et_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C5 00E0 00E1 00E2 00E3 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0064005A 0064007A
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0044017D 0044017D 0044017E 0044017E 0064017D 0064017E 0064017E
01C4 01C5 01C6 01F1 01F2 01F3
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D8 00F2 00F3 00F4 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
01A9
01AA
0160 0161
005A 007A 0179 017A 017B 017C
005A0053 005A0073 007A0053 007A0073
005A005A0053 005A007A0073 007A005A0053 007A007A0073
017D 017E
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00F9 00FA 00FB 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
00D5 00F5 1EE0 1EE1
00C4 00E4 01DE 01DF
00D6 00F6
00DC 00FC 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
018D
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_es_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
00D1 00F1
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D6 00D8 00F2 00F3 00F4 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_sv_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00E0 00E1 00E2 00E3 0100 0101 0102 0103 0104 0105 01CD 01CE 01E0 01E1 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D5 00F2 00F3 00F5 014C 014D 014E 014F 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 1ECC 1ECD 1ECE 1ECF 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
00DE 00FE
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00F9 00FA 00FB 0168 0169 016A 016B 016C 016D 016E 016F 0172 0173 01AF 01B0 01D3 01D4 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
00DC 00FC 0170 0171 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
00C5 00E5 01FA 01FB
00C4 00E4 01DE 01DF 00C6 00E6 01E2 01E3 01FC 01FD 0118 0119
00D4 00D6 00D8 00F4 00F6 00F8 0150 0151 01FE 01FF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 0152 0153
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_tr_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 0106 0107 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
00C7 00E7
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
011E 011F
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 00CC 00CD 00CE 00CF 0128 012A 012C 012E 01CF 1EC8 1ECA 0131
0049004A 0049006A
0069 00EC 00ED 00EE 00EF 0129 012B 012D 012F 0130 01D0 1EC9 1ECB
0069004A 0069006A 0132 0133
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D8 00F2 00F3 00F4 00F5 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
00D6 00F6
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
015E 015F
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00F9 00FA 00FB 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
00DC 00FC 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_cs_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B
004300430053 004300630073 006300430053 006300630073
00430053 00430073 00630053 00630073
010C 010D
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0064005A 0064007A 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0044017D 0044017D 0044017E 0044017E 0064017D 0064017E 0064017E
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
00430048 00430068 00630048 00630068
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D6 00D8 00F2 00F3 00F4 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
0158 0159
01A6
0053 0073 015A 015B 015C 015D 015E 015F 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
0160 0161
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
017D 017E
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_da_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00E0 00E1 00E2 00E3 0100 0101 0102 0103 0104 0105 01CD 01CE 01E0 01E1 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00610041
00410045 00410065 00610045 00610065
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00F2 00F3 00F4 00F5 014C 014D 014E 014F 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
00DE 00FE
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00F9 00FA 00FB 0168 0169 016A 016B 016C 016D 016E 016F 0172 0173 01AF 01B0 01D3 01D4 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
00DC 00FC 0170 0171 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
00C4 00E4 01DE 01DF 00C6 00E6 01E2 01E3 01FC 01FD
00D6 00D8 00F6 00F8 0150 0151 01FE 01FF 0152 0153
00C5 00E5 01FA 01FB 00410041 00410061 00610061
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_lt_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
010C 010D
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0064005A 0064007A 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0044017D 0044017D 0044017E 0044017E 0064017D 0064017E 0064017E
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB 0059 0079 00DD 00FD 00FF 0176 0177 0178
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C0059 004C0079 006C0059 006C0079
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E0059 004E0079 006E0059 006E0079
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D6 00D8 00F2 00F3 00F4 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
0160 0161
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
00540059 00540079 00740059 00740079
01BE
005400540059 005400740079 007400540059 007400740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
01B3 01B4
005A 007A 0179 017A 017B 017C
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
017D 017E
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_sk_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C5 00E0 00E1 00E2 00E3 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
00C4 00E4 01DE 01DF
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B
004300430053 004300630073 006300430053 006300630073
00430053 00430073 00630053 00630073
010C 010D
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0064005A 0064007A 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0044017D 0044017D 0044017E 0044017E 0064017D 0064017E 0064017E
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
00430048 00430068 00630048 00630068
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D5 00D6 00D8 00F2 00F3 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
00D4 00F4 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
0158 0159
01A6
0053 0073 015A 015B 015C 015D 015E 015F 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
0160 0161
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
017D 017E
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_es_trad_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00630048
00430053 00430073 00630053 00630073
00430048 00430068 00630068
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
006C004C
006C004C0059
004C0059 004C0079 006C0059 006C0079
004C004C 004C006C 006C006C
004C004C0059 004C006C0079 006C006C0079
019A
019B
004D 006D
004E 006E 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
00D1 00F1
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D6 00D8 00F2 00F3 00F4 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_la_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB 004A 006A 0134 0135 01F0
0049004A 0049006A 0069004A 0069006A
0132 0133
0131
0197
0196
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A
01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A
01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D6 00D8 00F2 00F3 00F4 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
019C
01B1
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1 0056 0076
00550045 00550065 00750045 00750065
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_eo_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0108 0109
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
011C 011D
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0126 0127
0124 0125
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 01F0
0134 0135
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D6 00D8 00F2 00F3 00F4 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015E 015F 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
015C 015D
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
016C 016D
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_hu_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B 010C 010D
006300430053
00430048 00430068 00630048 00630068
00630053
00430053 00430073 00630073
004300430053 004300630073 006300630073
0187 0188
0044 0064 00D0 00F0 010E 010F 0110 0111
00640044005A
00640044005A0053
0044017D 0044017D 0044017E 0044017E 0064005A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0064005A0053 0064005A0073
0044005A 0044007A 0064007A
00440044005A 00440064007A 00640064007A
0044005A0073 0044007A0053 0064007A0053
0044005A0053 0044007A0073 0064007A0073
00440044005A0053 00440064007A0073 00640064007A0073
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
006700470059
00670059
00470059 00470079 00670079
004700470059 004700670079 006700670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
006C004C0059
006C0059
004C0059 004C0079 006C0079
004C004C0059 004C006C0079 006C006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
006E004E0059
006E0059
004E0059 004E0079 006E0079
004E004E0059 004E006E0079 006E006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D8 00F2 00F3 00F4 00F5 00F8 014C 014D 014E 014F 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
00D6 00F6 0150 0151
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00730053005A
0073005A
0053005A 0053007A 0073007A
00530053005A 00530073007A 00730073007A
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
007400540059
00740059
00540059 00540079 00740079
005400540059 005400740079 007400740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00F9 00FA 00FB 0168 0169 016A 016B 016C 016D 016E 016F 0172 0173 01AF 01B0 01D3 01D4 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
00DC 00FC 0170 0171 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C 017D 017E
007A0053
018D
007A005A0053
005A0053 005A0073 007A0073
005A005A0053 005A007A0073 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_hr_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C2 00C3 00C4 00C5 00E0 00E1 00E2 00E3 00E4 00E5 0100 0101 0102 0103 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0108 0109 010A 010B
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
010C 010D
0106 0107
0187 0188
0044 0064 00D0 00F0 010E 010F
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0064005A 0064007A 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0064017D
0044017D 0044017D 0044017E 0044017E 0064017E 0064017E 01C4 01C5 01C6
0110 0111
0189
018A
018B 018C
0045 0065 00C8 00C9 00CA 00CB 00E8 00E9 00EA 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
006C004A
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
004C004A 004C006A 006C006A 01C7 01C8 01C9
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
006E004A
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
004E004A 004E006A 006E006A 01CA 01CB 01CC
019D
019E
014A 014B
004F 006F 00D2 00D3 00D4 00D5 00D6 00D8 00F2 00F3 00F4 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01A0 01A1 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
004F0045 004F0065 006F0045 006F0065 0152 0153
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
0160 0161
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01AF 01B0 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
00550045 00550065 00750045 00750065
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
017D 017E
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
SELECT GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ') FROM t1 GROUP BY c1 COLLATE utf8mb4_vi_0900_ai_ci;
GROUP_CONCAT(HEX(CONVERT(c1 USING utf16)) ORDER BY c1, HEX(c1) SEPARATOR ' ')
00F7
00D7
0041 0061 00C0 00C1 00C3 00C4 00C5 00E0 00E1 00E3 00E4 00E5 0100 0101 0104 0105 01CD 01CE 01DE 01DF 01E0 01E1 01FA 01FB 1EA0 1EA1 1EA2 1EA3
00410041 00410061 00610041 00610061
00410045 00410065 00610045 00610065 00C6 00E6 01E2 01E3 01FC 01FD
0102 0103 1EAE 1EAF 1EB0 1EB1 1EB2 1EB3 1EB4 1EB5 1EB6 1EB7
00C2 00E2 1EA4 1EA5 1EA6 1EA7 1EA8 1EA9 1EAA 1EAB 1EAC 1EAD
0042 0062
0180
0181
0182 0183
0043 0063 00C7 00E7 0106 0107 0108 0109 010A 010B 010C 010D
004300430053 004300630073 006300430053 006300630073
00430048 00430068 00630048 00630068
00430053 00430073 00630053 00630073
0187 0188
0044 0064 00D0 00F0 010E 010F
00440044005A 00440064007A 00640044005A 00640064007A
00440044005A0053 00440064007A0073 00640044005A0053 00640064007A0073
0044005A 0044007A 0044017D 0044017D 0044017E 0044017E 0064005A 0064007A 0064017D 0064017E 0064017E 01C4 01C5 01C6 01F1 01F2 01F3
0044005A0053 0044005A0073 0044007A0053 0044007A0073 0064005A0053 0064005A0073 0064007A0053 0064007A0073
0110 0111
0189
018A
018B 018C
0045 0065 00C8 00C9 00CB 00E8 00E9 00EB 0112 0113 0114 0115 0116 0117 0118 0119 011A 011B 1EB8 1EB9 1EBA 1EBB 1EBC 1EBD
00CA 00EA 1EBE 1EBF 1EC0 1EC1 1EC2 1EC3 1EC4 1EC5 1EC6 1EC7
018E 01DD
018F
0190
0046 0066
0191 0192
0047 0067 011C 011D 011E 011F 0120 0121 0122 0123 01E6 01E7 01F4 01F5
004700470059 004700670079 006700470059 006700670079
00470059 00470079 00670059 00670079
01E4 01E5
0193
0194
01A2 01A3
0048 0068 0124 0125 0126 0127
0195 01F6
0049 0069 00CC 00CD 00CE 00CF 00EC 00ED 00EE 00EF 0128 0129 012A 012B 012C 012D 012E 012F 0130 01CF 01D0 1EC8 1EC9 1ECA 1ECB
0049004A 0049006A 0069004A 0069006A 0132 0133
0131
0197
0196
004A 006A 0134 0135 01F0
004B 006B 0136 0137 01E8 01E9
0198 0199
004C 006C 0139 013A 013B 013C 013D 013E 013F 0140 0141 0142
004C004A 004C006A 006C004A 006C006A 01C7 01C8 01C9
004C004C 004C006C 006C004C 006C006C
004C004C0059 004C006C0079 006C004C0059 006C006C0079
004C0059 004C0079 006C0059 006C0079
019A
019B
004D 006D
004E 006E 00D1 00F1 0143 0144 0145 0146 0147 0148 01F8 01F9
004E004A 004E006A 006E004A 006E006A 01CA 01CB 01CC
004E004E0059 004E006E0079 006E004E0059 006E006E0079
004E0059 004E0079 006E0059 006E0079
019D
019E
014A 014B
004F 006F 00D2 00D3 00D5 00D6 00D8 00F2 00F3 00F5 00F6 00F8 014C 014D 014E 014F 0150 0151 01D1 01D2 01EA 01EB 01EC 01ED 01FE 01FF 1ECC 1ECD 1ECE 1ECF
004F0045 004F0065 006F0045 006F0065 0152 0153
00D4 00F4 1ED0 1ED1 1ED2 1ED3 1ED4 1ED5 1ED6 1ED7 1ED8 1ED9
01A0 01A1 1EDA 1EDB 1EDC 1EDD 1EDE 1EDF 1EE0 1EE1 1EE2 1EE3
0186
019F
0050 0070
01A4 01A5
0051 0071
0138
0052 0072 0154 0155 0156 0157 0158 0159
00520052 00520052 00520072 00520072 00720052 00720052 00720072 00720072
01A6
0053 0073 015A 015B 015C 015D 015E 015F 0160 0161 017F 0218 0219
00530053 00530073 00730053 00730073 00DF
00530053005A 00530073007A 00730053005A 00730073007A
0053005A 0053007A 0073005A 0073007A
01A9
01AA
0054 0074 0162 0163 0164 0165 021A 021B
01BE
005400540059 005400740079 007400540059 007400740079
00540059 00540079 00740059 00740079
0166 0167
01AB
01AC 01AD
01AE
0055 0075 00D9 00DA 00DB 00DC 00F9 00FA 00FB 00FC 0168 0169 016A 016B 016C 016D 016E 016F 0170 0171 0172 0173 01D3 01D4 01D5 01D6 01D7 01D8 01D9 01DA 01DB 01DC 1EE4 1EE5 1EE6 1EE7
00550045 00550065 00750045 00750065
01AF 01B0 1EE8 1EE9 1EEA 1EEB 1EEC 1EED 1EEE 1EEF 1EF0 1EF1
019C
01B1
0056 0076
01B2
0057 0077 0174 0175
0058 0078
0059 0079 00DD 00FD 00FF 0176 0177 0178
01B3 01B4
005A 007A 0179 017A 017B 017C 017D 017E
005A0053 005A0073 007A0053 007A0073
018D
005A005A0053 005A007A0073 007A005A0053 007A007A0073
01B5 01B6
01B7 01EE 01EF
01B8 01B9
01BA
00DE 00FE
01BF 01F7
01BB
01A7 01A8
01BC 01BD
0184 0185
0149
01C0
01C1
01C2
01C3
0D96
0DA4
0DA5
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(1)) COLLATE utf8mb4_da_0900_ai_ci;
INSERT INTO t1 VALUES('a'), ('b'), ('c'), ('d'), ('e');
SELECT HEX(a), HEX(WEIGHT_STRING(a)) FROM t1 ORDER BY a;
HEX(a)	HEX(WEIGHT_STRING(a))
61	1C47
62	1C60
63	1C7A
64	1C8F
65	1CAA
TRUNCATE TABLE t1;
INSERT INTO t1 VALUES(_utf16 0x00C4), (_utf16 0x00C5), (_utf16 0x00C6), (_utf16 0x00D8), (_utf16 0x00D6);
SELECT HEX(a), HEX(WEIGHT_STRING(a)) FROM t1 ORDER BY a, BINARY a;
HEX(a)	HEX(WEIGHT_STRING(a))
C384	1F9854A5
C386	1F9854A5
C396	1F9854A6
C398	1F9854A6
C385	1F9854A7
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10), b VARCHAR(10)) COLLATE utf8mb4_da_0900_ai_ci;
INSERT INTO t1 VALUES(_utf16 0x015A, _utf16 0x00DF), (_utf16 0x0162, _utf16 0x00DE), (_utf16 0x01CF, _utf16 0x0132), (_utf16 0x01F8, _utf16 0x01CA), (_utf16 0x42, _utf16 0x1d2d);
SELECT HEX(CONVERT(a USING UTF16)) AS U16_a, HEX(CONVERT(b USING UTF16)) AS U16_b, a<b FROM t1;
U16_a	U16_b	a<b
015A	00DF	1
0162	00DE	1
01CF	0132	1
01F8	01CA	1
0042	1D2D	0
DROP TABLE t1;
CREATE TABLE t1(a VARCHAR(10), b VARCHAR(10)) COLLATE utf8mb4_hu_0900_ai_ci;
INSERT INTO t1 VALUES(_utf16 0x01c4, _utf16 0x01f1), (_utf16 0x01f1, _utf16 0x02a4), ('cukor', 'csak');
SELECT HEX(CONVERT(a USING UTF16)) AS U16_a, HEX(CONVERT(b USING UTF16)) AS U16_b, a<b FROM t1;
U16_a	U16_b	a<b
01C4	01F1	0
01F1	02A4	1
00630075006B006F0072	006300730061006B	1
DROP TABLE t1;
CREATE TABLE t1(a VARCHAR(10)) COLLATE utf8mb4_et_0900_ai_ci;
INSERT INTO t1 VALUES(_utf16 0x4F), (_utf16 0xD5), (_utf16 0x01A0), (_utf16 0x1EE0);
SELECT HEX(a), HEX(WEIGHT_STRING(a)) FROM t1 ORDER BY a, BINARY a;
HEX(a)	HEX(WEIGHT_STRING(a))
4F	1DDD
C6A0	1DDD
C395	1EFE54A5
E1BBA0	1EFE54A5
Warnings:
Warning	1287	'BINARY expr' is deprecated and will be removed in a future release. Please use CAST instead
DROP TABLE t1;
CREATE TABLE t1(a VARCHAR(10), b VARCHAR(10)) COLLATE utf8mb4_et_0900_ai_ci;
INSERT INTO t1 VALUES(_utf16 0x4F, _utf16 0x1EE0), (_utf16 0x1EE0, _utf16 0x1EE7), (_utf16 0x55, _utf16 0x1E7A), (_utf16 0x1E7A, _utf16 0x1E7C), (_utf16 0x1E7A, _utf16 0x1E80), (_utf16 0x4F, _utf16 0x1ED6), (_utf16 0x1ED6, _utf16 0x1EE4);
SELECT HEX(CONVERT(a USING UTF16)) AS U16_a, HEX(CONVERT(b USING UTF16)) AS U16_b, a<b FROM t1;
U16_a	U16_b	a<b
004F	1EE0	1
1EE0	1EE7	0
0055	1E7A	0
1E7A	1E7C	1
1E7A	1E80	1
004F	1ED6	0
1ED6	1EE4	1
DROP TABLE t1;
CREATE TABLE t1(a VARCHAR(10), b VARCHAR(10)) COLLATE utf8mb4_cs_0900_ai_ci;
INSERT INTO t1 VALUES(_utf16 0x53, _utf16 0x1E66), (_utf16 0x53, _utf16 0x1E67), (_utf16 0x1E66, _utf16 0x1E9E);
SELECT HEX(CONVERT(a USING UTF16)) AS U16_a, HEX(CONVERT(b USING UTF16)) AS U16_b, a<b FROM t1;
U16_a	U16_b	a<b
0053	1E66	1
0053	1E67	1
1E66	1E9E	0
DROP TABLE t1;
CREATE TABLE t1(grp int, ch VARCHAR(10)) COLLATE utf8mb4_hr_0900_ai_ci;
INSERT INTO t1 VALUES(0, '\t'), (0, ' '), (0, _utf16 0x5F), (0, _utf16 0x02DC), (0, '$'), (0, _utf16 0x20A0), (0, _utf16 0x2180);
INSERT INTO t1 VALUES(1, 'a'), (1, _utf16 0xA723), (1, _utf16 0x02AD);
INSERT INTO t1 VALUES(2, _utf16 0x03B1), (2, _utf16 0x1FA9), (2, _utf16 0x03F7);
INSERT INTO t1 VALUES(3, _utf16 0x0430), (3, _utf16 0x046A), (3, _utf16 0x04C0);
INSERT INTO t1 VALUES(4, _utf16 0x2C30), (4, _utf16 0x10D0), (4, _utf16 0x0561), (4, _utf16 0x3041);
SELECT grp, HEX(CONVERT(ch USING utf16)) FROM t1 ORDER BY ch COLLATE utf8mb4_0900_ai_ci;
grp	HEX(CONVERT(ch USING utf16))
0	0009
0	0020
0	005F
0	02DC
0	2180
0	0024
0	20A0
1	0061
1	A723
1	02AD
2	03B1
2	1FA9
2	03F7
3	0430
3	046A
3	04C0
4	2C30
4	10D0
4	0561
4	3041
SELECT grp, HEX(CONVERT(ch USING utf16)) FROM t1 ORDER BY ch COLLATE utf8mb4_hr_0900_ai_ci;
grp	HEX(CONVERT(ch USING utf16))
0	0009
0	0020
0	005F
0	02DC
0	2180
0	0024
0	20A0
1	0061
1	A723
1	02AD
3	0430
3	046A
3	04C0
2	03B1
2	1FA9
2	03F7
4	2C30
4	10D0
4	0561
4	3041
DROP TABLE t1;
#
# Bug #25090543: DIFFERENCES BETWEEN ICU AND MYSQL FOR HUNGARIAN
#                CONTRACTIONS/LIGATURES
#
CREATE TABLE t1 (v VARCHAR(20) COLLATE utf8mb4_hu_0900_ai_ci);
INSERT INTO t1 VALUES ('dd'), ('a'), (_utf16 0x00E5), ('e'), ('d'), ('dz'),
('dzs'), ('ddz'), ('ddzs'), (_utf16 0x01F3), ('dza'), ('dzsa'),
('ddza'), ('ddzsa'), (_utf16 0x01F30061), ('dzu'), ('dzsu'),
('ddzu'), ('ddzsu'), (_utf16 0x01F30075), ('da'), ('dx'), ('dy'),
(_utf16 0x006400E5), (_utf16 0x00640394);
SELECT v, HEX(CONVERT(v USING utf16)) AS HEX, HEX(WEIGHT_STRING(v)) FROM t1 ORDER BY v, HEX(v);
v	HEX	HEX(WEIGHT_STRING(v))
a	0061	1C47
å	00E5	1C47
d	0064	1C8F
da	00640061	1C8F1C47
då	006400E5	1C8F1C47
dd	00640064	1C8F1C8F
dx	00640078	1C8F1EFF
dy	00640079	1C8F1F0B
ǳ	01F3	1C8F1F21
ǳa	01F30061	1C8F1F211C47
ǳu	01F30075	1C8F1F211EB5
dΔ	00640394	1C8F1FBD
dz	0064007A	1C8F54A5
dza	0064007A0061	1C8F54A51C47
ddz	00640064007A	1C8F54A51C8F54A5
ddza	00640064007A0061	1C8F54A51C8F54A51C47
ddzu	00640064007A0075	1C8F54A51C8F54A51EB5
dzu	0064007A0075	1C8F54A51EB5
dzs	0064007A0073	1C8F54A554A5
dzsa	0064007A00730061	1C8F54A554A51C47
ddzs	00640064007A0073	1C8F54A554A51C8F54A554A5
ddzsa	00640064007A00730061	1C8F54A554A51C8F54A554A51C47
ddzsu	00640064007A00730075	1C8F54A554A51C8F54A554A51EB5
dzsu	0064007A00730075	1C8F54A554A51EB5
e	0065	1CAA
DROP TABLE t1;
CREATE TABLE t1 (a VARCHAR(10)) COLLATE utf8mb4_ru_0900_ai_ci;
INSERT INTO t1 VALUES(_utf16 0x0452), (_utf16 0x0453), (_utf16 0x0403),
(_utf16 0x0439), (_utf16 0x048B), (_utf16 0x048A), (_utf16 0x043B),
(_utf16 0x1D2B), (_utf16 0x045B), (_utf16 0x045C), (_utf16 0x040C);
SELECT HEX(CONVERT(a USING utf16)) AS codepoint FROM t1 ORDER BY a, HEX(a);
codepoint
0403
0453
0452
048A
048B
0439
040C
045C
043B
1D2B
045B
DROP TABLE t1;
CREATE TABLE t1 (
codepoint CHAR(1) CHARSET utf16 NOT NULL,
glyph CHAR(2) CHARSET utf8mb4 COLLATE utf8mb4_mn_cyrl_0900_ai_ci NOT NULL,
description VARCHAR(64) NOT NULL);
INSERT INTO t1 (codepoint, glyph, description) VALUES
(0x041E, 'О', 'CYRILLIC CAPITAL LETTER O'),
(0x04E8, 'Ө', 'CYRILLIC CAPITAL LETTER BARRED O'),
(0x041F, 'П', 'CYRILLIC CAPITAL LETTER PE '),
(0x043E, 'о', 'CYRILLIC SMALL LETTER O'),
(0x04E9, 'ө', 'CYRILLIC SMALL LETTER BARRED O'),
(0x043F, 'п', 'CYRILLIC SMALL LETTER PE'),
(0x0423, 'У', 'CYRILLIC CAPITAL LETTER U '),
(0x04AE, 'Ү', 'CYRILLIC CAPITAL LETTER STRAIGHT U '),
(0x0424, 'Ф', 'CYRILLIC CAPITAL LETTER EF '),
(0x0443, 'у', 'CYRILLIC SMALL LETTER U '),
(0x04AF, 'ү', 'CYRILLIC SMALL LETTER STRAIGHT U'),
(0x0444, 'ф', 'CYRILLIC SMALL LETTER EF');
SELECT HEX(codepoint), codepoint, glyph, description FROM t1 ORDER BY glyph, codepoint;
HEX(codepoint)	codepoint	glyph	description
041E	О	О	CYRILLIC CAPITAL LETTER O
043E	о	о	CYRILLIC SMALL LETTER O
04E8	Ө	Ө	CYRILLIC CAPITAL LETTER BARRED O
04E9	ө	ө	CYRILLIC SMALL LETTER BARRED O
041F	П	П	CYRILLIC CAPITAL LETTER PE 
043F	п	п	CYRILLIC SMALL LETTER PE
0423	У	У	CYRILLIC CAPITAL LETTER U 
0443	у	у	CYRILLIC SMALL LETTER U 
04AE	Ү	Ү	CYRILLIC CAPITAL LETTER STRAIGHT U 
04AF	ү	ү	CYRILLIC SMALL LETTER STRAIGHT U
0424	Ф	Ф	CYRILLIC CAPITAL LETTER EF 
0444	ф	ф	CYRILLIC SMALL LETTER EF
DROP TABLE t1;
#
# End of 8.0 tests
#
