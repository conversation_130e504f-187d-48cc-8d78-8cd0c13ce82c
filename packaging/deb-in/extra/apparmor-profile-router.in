# vim:syntax=apparmor

#include <tunables/global>

# flags=(attach_disconnected) is needed, otherwise when trying
# to write to NOTIFY_SOCKET Router hits:
# Failed name lookup - disconnected path" error=-13
# profile="/usr/bin/mysqlrouter" name="run/systemd/notify"

/usr/bin/mysqlrouter flags=(attach_disconnected) {
  #include <abstractions/base>
  #include <abstractions/nameservice>
  #include <abstractions/user-tmp>

# Allow system resource access
  /sys/devices/system/cpu/ r,
  capability sys_resource,
  capability dac_override,
  capability setuid,
  capability setgid,
  capability chown,
  capability fowner,

# Allow config access
  /etc/mysqlrouter/** rw,
  /var/lib/mysqlrouter/** rw,

# Allow libssl to read its global config
  /etc/ssl/openssl.cnf r,

# Allow access to libmysqlclient's charset files
  /usr/share/mysql-8.0/charsets/** r,

# Allow runtime files such as PID-file
  /var/run/mysqlrouter/** rw,
  /run/mysqlrouter/** rw,

# Allow systemd notify messages
  /{,var/}run/systemd/notify w,

# Allow read/ write to /tmp
  /tmp/ r,
  /tmp/* rw,

# Allow execution of MySQL Router binary
  /usr/bin/mysqlrouter mr,

# Allow plugin access
  /usr/lib/mysqlrouter/plugin/*.so mr,

# Allow log file access
  /var/log/mysqlrouter/ rw,
  /var/log/mysqlrouter/** rw,
}
