/*****************************************************************************

Copyright (c) 2006, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/

/** @file include/ut0list.ic
 A double-linked list

 Created 4/26/2006 Osku Salerma
 ************************************************************************/

/** Get the first node in the list.
 @return first node, or NULL */
static inline ib_list_node_t *ib_list_get_first(
    ib_list_t *list) /*!< in: list */
{
  return (list->first);
}

/** Get the last node in the list.
 @return last node, or NULL */
static inline ib_list_node_t *ib_list_get_last(ib_list_t *list) /*!< in: list */
{
  return (list->last);
}

/********************************************************************
Check if list is empty. */
static inline bool ib_list_is_empty(
    /* out: true if empty else false */
    const ib_list_t *list) /* in: list */
{
  return (!(list->first || list->last));
}
