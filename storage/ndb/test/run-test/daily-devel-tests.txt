# Copyright (c) 2004, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
#
# BACKUP
#
max-time: 900
cmd: testNdbApi
args: -n MaxOperations T1 T6 T13 

max-time: 2500
cmd: testDict
args: -n CreateTableWhenDbIsFull T6 

max-time: 1000
cmd: testSRBank
args: -n NR -l 300 -r 15 T1

max-time: 1000
cmd: testSRBank
args: -n Mix -l 300 -r 15 T1

#
#
# SYSTEM RESTARTS
#
#
max-time: 14400
cmd: testSystemRestart
args: -n SR_FULLDB T6 

#
# NODE RESTARTS
#
max-time: 2500
cmd: testNodeRestart
args: -l 1 -n MixedPkReadPkUpdate 

max-time: 5000
cmd: testNodeRestart
args: -n FullDb T6 T13 

max-time: 500
cmd: testNodeRestart
args: -n Bug16772 T1

#max-time: 500
#cmd: testNodeRestart
#args: -n StopOnError T1 
#
#
max-time: 2500
cmd: testIndex
args: -n NFNR3 T6 T13 

max-time: 2500
cmd: testIndex
args: -n NFNR3_O T6 T13 

# dict trans
max-time: 3600
cmd: testDict
args: -n SchemaTrans -l 1

# LCP Frag watchdog
max-time: 900
cmd: testNodeRestart
args: -n LCPScanFragWatchdog T2

# LCP Frag watchdog
max-time: 600
cmd: testNodeRestart
args: -n TestLCPFSErr --records=100000 T1

# GCP node failure
max-time: 1800
cmd: testNodeRestart
args: -n NodeFailGCPOpen T1

# DBUtil race on TransidAI
max-time: 500
cmd: test_event
args: -n DbUtilRace T1

# Bug18408726 added on 17.04.2014
max-time: 300
cmd: testScan
args:  -n extraNextResultBug11748194 T1

# Bug18408730 added on 17.04.2014
max-time: 1200
cmd: testScan
args: -n ScanUpdateRestart D1 D2

# Bug18408731 added on 17.04.2014
max-time: 900
cmd: testScan
args: -n ScanRead40 -l 100 T1 D1 D2

# Bug18408736 added on 17.04.2014
max-time: 1000
cmd: testScan
args: -n ScanRead488_Mixed -l 10 T6 D1 D2

# Bug18408737 added on 17.04.2014
max-time: 1800
cmd: testScan
args: -n ScanRead40RandomTable -l 100 T1

# Bug18408745 added on 17.04.2014
max-time: 600
cmd: testScan
args: -n Bug12324191 T1 T6 T13

# Bug18408747 added on 17.04.2014
max-time: 900
cmd: testScan
args: -n ScanReadError5021 T1 D1 D2

# Bug18409942 added on 17.04.2014
max-time : 300
cmd: testScan
args: -n ScanKeyInfoExhaust T1

# Bug18408741 added on 17.04.2014
max-time: 3600
cmd: test_event
args: -n EventOperationApplier_NR -l 2

# Bug18408742 added on 17.04.2014
max-time: 3600
cmd: test_event
args: -n MergeEventOperationApplier_NR -l 2

# Bug18408738 added on 17.04.2014
max-time: 2000
cmd: testOIBasic
args: -case hz

# Bug18408732 added on 17.04.2014
max-time: 3600
cmd: testNodeRestart
args: -n Bug27003 T1

# Bug18408719 added on 17.04.2014
max-time: 1800
cmd: testIndex
args: -n NFNR1_O T6 T13

# Bug18408722 added on 17.04.2014
max-time: 2500
cmd: testNodeRestart
args: -n MixedPkRead T6 T13

# Bug18408662 added on 17.04.2014
max-time : 300
cmd: testFK
args: -n CreateDropDuring T1

# Bug18408667 added on 17.04.2014
max-time : 300
cmd: testFK
args: -n CreateDropWithData T1

# Bug18408670 added on 17.04.2014
max-time : 300
cmd: testFK
args: -n Basic55 T1

# Bug18408675 added on 17.04.2014
max-time : 300
cmd: testFK
args: -n Basic1 T1

# Bug18408677 added on 17.04.2014
max-time : 300
cmd: testFK
args: -n Basic5 T1

# Bug18408672 added on 17.04.2014
max-time : 300
cmd: testFK
args: -n TransError T1

# Bug18408674 added on 17.04.2014
max-time : 300
cmd: testFK
args: -n CascadeError T1

# Bug18408680 added on 17.04.2014
max-time : 300
cmd: testFK
args: -n Cascade10 T1

max-time : 300
cmd: testFK
args: -n DropTableWithFKDuringRestart T1

# Bug18408671 added on 17.04.2014
max-time : 500
cmd: testDict
args: -n FK_SRNR1 T1

# Bug18408666 added on 17.04.2014
max-time : 500
cmd: testDict
args: -n FK_SRNR2 T1

# Bug18408665 added on 17.04.2014
max-time: 2500
cmd: testBasic
args: -n Fill T6

# Bug18408654 added on 17.04.2014
max-time: 600
cmd: testNodeRestart
args: -n ClusterSplitLatency T1

# Bug18408653 added on 17.04.2014
max-time: 300
cmd: testNdbApi
args: -n FragmentedApiFailure T1

# Bug18408682 added on 17.04.2014
max-time: 1500
cmd: testNodeRestart
args: -n MNF -l 15 T1

# Bug18408691 added on 17.04.2014
max-time : 600
cmd: testNodeRestart
args: -n Bug16766493 D1

# Bug18408693 added on 17.04.2014
max-time: 5000
cmd: testNodeRestart
args: -l 1 -n RestartNFDuringNR T6 T13

# Bug18408711 added on 17.04.2014
max-time: 1800
cmd: testNodeRestart
args: -n mixedmultiop T1 I2 I3 D2

# Bug18408713 added on 17.04.2014
max-time: 1800
cmd: testScan
args: -n TupScanRead100 -l 100 T1 D1 D2

# Refresh tuple
# Bug18408692 added on 17.04.2014
max-time: 300
cmd: testBasic
args: -n RefreshTuple T6 D1

# Bug18408712 added on 17.04.2014
max-time: 300
cmd: testSystemRestart
args: -n Bug46412 T1

#
# Bug18408715 added on 17.04.2014
max-time: 600
cmd: test_event
args: -n EventOperationApplier_NS T1

# Bug18408709 added on 17.04.2014
max-time: 1800
cmd: testScan
args: -n ScanRead100 -l 100 T1 D1 D2

# Bug18408696 added on 17.04.2014
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_2b D1

# Bug18408698 added on 17.04.2014
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_2_LCP D1

# Bug18408702 added on 17.04.2014
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_3_LCP D2

# Bug18408708 added on 17.04.2014
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_2 D1

# Bug18408705 added on 17.04.2014
max-time: 500
cmd: testDict
args: -n Bug53944 T1

# Bug18408700 added on 17.04.2014
max-time: 1500
cmd: testNdbApi
args: -n Bug44065_org

# Bug18408728 added on 17.04.2014
max-time: 2500
cmd: testNodeRestart
args: -n Terror T6 T13

# Bug18379339 added on 17.04.2014 
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_1 D1

# Bug18401499 added on 17.04.2014
max-time: 3000
cmd: testSystemRestart
args: -n SR_DD_1b D1

# Bug18401521 added on 17.04.2014
max-time: 3000
cmd: testSystemRestart
args: -n SR_DD_1b D2

# Bug18401529 added on 17.04.2014
max-time: 3000
cmd: testSystemRestart
args: -n SR_DD_1b_LCP D1

# Bug18401375 added on 17.04.2014
max-time: 3000
cmd: testSystemRestart
args: -n SR_DD_1b_LCP D2

# Bug18401576 added on 17.04.2014
max-time: 300
cmd: testSystemRestart
args: -n Bug45154 D1

# Bug18401154 added on 17.04.2014
max-time: 900
cmd: testSystemRestart
args: -n to D2

# Bug18379463 added on 17.04.2014
max-time: 1200
cmd: testNodeRestart
args: -n Bug42422 -l 1 T1

# Bug18401257 added on 17.04.2014
max-time: 600
cmd: testNodeRestart
args: -n Bug57522 T1

# Bug18401186 added on 17.04.2014
max-time: 5000
cmd: testNodeRestart
args: -n GCP -l 1 T1

# Bug18401338 added on 17.04.2014
max-time: 3000
cmd: testNodeRestart
args: -n Bug25984 T1

# Bug18401568 added on 17.04.2014
#EOF 2008-08-20
max-time: 1200
cmd: testNodeRestart
args: -n Bug41295 T1

# Bug18379632 added on 17.04.2014
max-time: 1800
cmd: testNodeRestart
args: -n Bug34216 -l 10 T1 I3 D2

# Bug18379486 added on 17.04.2014
max-time: 1500
cmd: testRedo
args: -n RestartFD -l 2 T1

# Bug18379518 added on 17.04.2014
max-time: 900
cmd: testDict
args: -n Bug46585 T1 I3 D1

# Bug18379657 added on 17.04.2014
max-time: 1000
cmd: testBackup
args: -n BackupBank T6

# Bug18401299 added on 17.04.2014
max-time: 2500
cmd: testNodeRestart
args: -l 1 -n MixedReadUpdateScan

# Bug18401324 added on 17.04.2014
# #EOF 2008-07-09
max-time: 600
cmd: test_event
args: -r 5000 -n Bug30780 T1

# Bug18401361 added on 17.04.2014
max-time: 600
cmd: testBasic
args: -n Bug54986 D2

# Bug18401439 added on 17.04.2014
max-time: 600
cmd: testTimeout
args: T1

# Bug18401475 added on 17.04.2014
max-time: 1000
cmd: test_event
args: -l 10 -n Bug27169 T1

# OJA: Moved from 'basic' to 'devel' due to instability on 15.06.2015
max-time: 1000
cmd: test_event
args: -n Apiv2HQE-latestGCI T1

# OJA: Moved from 'basic' to 'devel' due to instability on 15.06.2015
max-time: 1800
cmd: test_event
args: -n Apiv2-check_event_queue_cleared T1

# Mai: Multi* added on 13.08.2015
max-time: 1800
cmd: test_event
args: -n Multi T1

# Mai: Multi* added on 13.08.2015
max-time: 1800
cmd: test_event
args: -n Multi_NR T1

# Bug18401553 added on 17.04.2014
max-time: 3600
cmd: testTransactions
args:

# Bug18401623 added on 17.04.2014
max-time: 3000
cmd: testOIBasic
args: -case abcdefz

# Bug18401543 added on 17.04.2014
max-time: 2000
cmd: testDict
args: -l 25 -n DictRestart T1

# Bug18408639 added on 17.04.2014
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_2b_LCP D2

# Bug18408635 added on 17.04.2014
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_2b_LCP D1

# Bug18408642 added on 17.04.2014
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_3_LCP D1

# Bug18408646 added on 17.04.2014
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_2 D2

# Bug18408649 added on 17.04.2014
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_3b_LCP D2

# Bug18408626 added on 17.04.2014
max-time: 1500
cmd: testSystemRestart
args: -n SR_DD_3 D1

# Bug18408638 added on 17.04.2014
max-time: 900
cmd: testNodeRestart
args: -n pnr_lcp T1

# Bug18401844 added on 17.04.2014
max-time: 7200
cmd: testOperations
args:

# Bug18408630 added on 17.04.2014
max-time: 2500
cmd: testIndex
args: -n NFNR1 T6 T13

# Bug18401792 added on 17.04.2014
max-time : 600
cmd: testDict
args: -n DictTakeOver_1 T1

# Bug18401836 added on 17.04.2014
max-time: 3600
cmd: testNodeRestart
args: -n RestartNodeDuringLCP T6

# Bug18379566 added on 17.04.2014
max-time : 600
cmd: testNodeRestart
args: -n Bug16007980 T1

# Bug18401826 added on 17.04.2014
max-time: 900
cmd: testNodeRestart
args: -n Bug34702 T1

max-time: 3600
cmd: testNodeRestart
args: -n LCPScanFragWatchdogIsolation T1

max-time: 3600
cmd: testNodeRestart
args: -n GcpStop T1 --loops=1

max-time: 3600
cmd: testNodeRestart
args: -n GcpStopIsolation T1 --loops=1

max-time : 600
cmd: testNodeRestart
args: -n GetTabInfoOverload T1

max-time : 600
cmd: testDict
args: -n DropTableConcurrentLCP T1

max-time : 600
cmd: testDict
args: -n DropTableConcurrentLCP2 T1

max-time : 300
cmd: testDict
args: -n indexStat T1

max-time : 300
cmd: testDict
args: -n forceGCPWait T1

max-time : 600
cmd: testBackup
args: -n BackupWhenOutOfLDMRecords T1

max-time : 600
cmd: testBackup
args: -n OutOfScanRecordsInLDM T1

