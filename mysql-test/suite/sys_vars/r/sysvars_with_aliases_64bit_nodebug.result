#### Initialize ####
#### Run ####
sysvar:
- name=init_replica
- alias=init_slave
- invalid=[7, 1.9, "FALSE"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=
- max=
- block_size=1
- default=
- values=["'SELECT 1'", "'SELECT 9'", "'SELECT(3)'"]
==== Testing SET @@global.init_replica [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.init_replica should be ]
include/assert.inc [init_replica should be  in P_S.global_variables]
include/assert.inc [@@global.init_slave should be ]
include/assert.inc [init_slave should be  in P_S.global_variables]
# Valid values for variable
SET @@global.init_replica='SELECT 1';
include/assert.inc [@@global.init_replica should be SELECT 1]
include/assert.inc [init_replica should be SELECT 1 in P_S.global_variables]
include/assert.inc [@@global.init_slave should be SELECT 1]
include/assert.inc [init_slave should be SELECT 1 in P_S.global_variables]
SET @@global.init_replica='SELECT 9';
include/assert.inc [@@global.init_replica should be SELECT 9]
include/assert.inc [init_replica should be SELECT 9 in P_S.global_variables]
include/assert.inc [@@global.init_slave should be SELECT 9]
include/assert.inc [init_slave should be SELECT 9 in P_S.global_variables]
SET @@global.init_replica='SELECT(3)';
include/assert.inc [@@global.init_replica should be SELECT(3)]
include/assert.inc [init_replica should be SELECT(3) in P_S.global_variables]
include/assert.inc [@@global.init_slave should be SELECT(3)]
include/assert.inc [init_slave should be SELECT(3) in P_S.global_variables]
# Setting default value for variable
SET @@global.init_replica = DEFAULT;
include/assert.inc [@@global.init_replica should be ]
include/assert.inc [init_replica should be  in P_S.global_variables]
include/assert.inc [@@global.init_slave should be ]
include/assert.inc [init_slave should be  in P_S.global_variables]
# Invalid values
SET @@global.init_replica = 7;
Got one of the listed errors
SET @@global.init_replica = 1.9;
Got one of the listed errors
SET @@global.init_replica = FALSE;
Got one of the listed errors
# Restore default
SET @@global.init_replica = DEFAULT;
include/assert.inc [@@global.init_replica should be ]
include/assert.inc [init_replica should be  in P_S.global_variables]
include/assert.inc [@@global.init_slave should be ]
include/assert.inc [init_slave should be  in P_S.global_variables]
==== Testing SET @@session.init_replica [invalid scope] ====
SET @@session.init_replica = DEFAULT;
ERROR HY000: Variable 'init_replica' is a GLOBAL variable and should be set with SET GLOBAL
SET @@init_replica = DEFAULT;
ERROR HY000: Variable 'init_replica' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.init_replica = 'SELECT 1';
ERROR HY000: Variable 'init_replica' is a GLOBAL variable and should be set with SET GLOBAL
SET @@init_replica = 'SELECT 1';
ERROR HY000: Variable 'init_replica' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.init_replica = 'SELECT 9';
ERROR HY000: Variable 'init_replica' is a GLOBAL variable and should be set with SET GLOBAL
SET @@init_replica = 'SELECT 9';
ERROR HY000: Variable 'init_replica' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.init_replica = 'SELECT(3)';
ERROR HY000: Variable 'init_replica' is a GLOBAL variable and should be set with SET GLOBAL
SET @@init_replica = 'SELECT(3)';
ERROR HY000: Variable 'init_replica' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.init_replica;
ERROR HY000: Variable 'init_replica' is a GLOBAL variable
SELECT @@init_replica;
@@init_replica

include/assert.inc [Variable init_replica should exist in performance_schema.session_variables]
==== Testing SET @@global.init_slave [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.init_slave should be ]
include/assert.inc [init_slave should be  in P_S.global_variables]
include/assert.inc [@@global.init_replica should be ]
include/assert.inc [init_replica should be  in P_S.global_variables]
# Valid values for variable
SET @@global.init_slave='SELECT 1';
Warnings:
Warning	1287	'@@init_slave' is deprecated and will be removed in a future release. Please use init_replica instead.
include/assert.inc [@@global.init_slave should be SELECT 1]
include/assert.inc [init_slave should be SELECT 1 in P_S.global_variables]
include/assert.inc [@@global.init_replica should be SELECT 1]
include/assert.inc [init_replica should be SELECT 1 in P_S.global_variables]
SET @@global.init_slave='SELECT 9';
Warnings:
Warning	1287	'@@init_slave' is deprecated and will be removed in a future release. Please use init_replica instead.
include/assert.inc [@@global.init_slave should be SELECT 9]
include/assert.inc [init_slave should be SELECT 9 in P_S.global_variables]
include/assert.inc [@@global.init_replica should be SELECT 9]
include/assert.inc [init_replica should be SELECT 9 in P_S.global_variables]
SET @@global.init_slave='SELECT(3)';
Warnings:
Warning	1287	'@@init_slave' is deprecated and will be removed in a future release. Please use init_replica instead.
include/assert.inc [@@global.init_slave should be SELECT(3)]
include/assert.inc [init_slave should be SELECT(3) in P_S.global_variables]
include/assert.inc [@@global.init_replica should be SELECT(3)]
include/assert.inc [init_replica should be SELECT(3) in P_S.global_variables]
# Setting default value for variable
SET @@global.init_slave = DEFAULT;
Warnings:
Warning	1287	'@@init_slave' is deprecated and will be removed in a future release. Please use init_replica instead.
include/assert.inc [@@global.init_slave should be ]
include/assert.inc [init_slave should be  in P_S.global_variables]
include/assert.inc [@@global.init_replica should be ]
include/assert.inc [init_replica should be  in P_S.global_variables]
# Invalid values
SET @@global.init_slave = 7;
Got one of the listed errors
SET @@global.init_slave = 1.9;
Got one of the listed errors
SET @@global.init_slave = FALSE;
Got one of the listed errors
# Restore default
SET @@global.init_slave = DEFAULT;
Warnings:
Warning	1287	'@@init_slave' is deprecated and will be removed in a future release. Please use init_replica instead.
include/assert.inc [@@global.init_slave should be ]
include/assert.inc [init_slave should be  in P_S.global_variables]
include/assert.inc [@@global.init_replica should be ]
include/assert.inc [init_replica should be  in P_S.global_variables]
==== Testing SET @@session.init_slave [invalid scope] ====
SET @@session.init_slave = DEFAULT;
ERROR HY000: Variable 'init_slave' is a GLOBAL variable and should be set with SET GLOBAL
SET @@init_slave = DEFAULT;
ERROR HY000: Variable 'init_slave' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.init_slave = 'SELECT 1';
ERROR HY000: Variable 'init_slave' is a GLOBAL variable and should be set with SET GLOBAL
SET @@init_slave = 'SELECT 1';
ERROR HY000: Variable 'init_slave' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.init_slave = 'SELECT 9';
ERROR HY000: Variable 'init_slave' is a GLOBAL variable and should be set with SET GLOBAL
SET @@init_slave = 'SELECT 9';
ERROR HY000: Variable 'init_slave' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.init_slave = 'SELECT(3)';
ERROR HY000: Variable 'init_slave' is a GLOBAL variable and should be set with SET GLOBAL
SET @@init_slave = 'SELECT(3)';
ERROR HY000: Variable 'init_slave' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.init_slave;
ERROR HY000: Variable 'init_slave' is a GLOBAL variable
SELECT @@init_slave;
@@init_slave

Warnings:
Warning	1287	'@@init_slave' is deprecated and will be removed in a future release. Please use init_replica instead.
include/assert.inc [Variable init_slave should exist in performance_schema.session_variables]
sysvar:
- name=log_replica_updates
- alias=log_slave_updates
- invalid=[0.1, 2, -1, "''", "'x'", "'TRUE'", "'1'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=1
- dynamic=0
- min=
- max=
- block_size=1
- default=1
- values=["ON", "OFF", 1, 0, "TRUE", "FALSE", "'ON'", "'OFF'"]
==== Testing SET @@global.log_replica_updates [read-only] ====
# Initial value is default
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
# Valid values for *read-only* variable cannot be set
SET @@global.log_replica_updates = ON;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
SET @@global.log_replica_updates = OFF;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
SET @@global.log_replica_updates = 1;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
SET @@global.log_replica_updates = 0;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
SET @@global.log_replica_updates = TRUE;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
SET @@global.log_replica_updates = FALSE;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
SET @@global.log_replica_updates = 'ON';
ERROR HY000: Variable 'log_replica_updates' is a read only variable
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
SET @@global.log_replica_updates = 'OFF';
ERROR HY000: Variable 'log_replica_updates' is a read only variable
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
# Setting default value for variable
SET @@global.log_replica_updates = DEFAULT;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
==== Testing SET @@session.log_replica_updates [invalid scope] ====
SET @@session.log_replica_updates = DEFAULT;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@log_replica_updates = DEFAULT;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@session.log_replica_updates = ON;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@log_replica_updates = ON;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@session.log_replica_updates = OFF;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@log_replica_updates = OFF;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@session.log_replica_updates = 1;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@log_replica_updates = 1;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@session.log_replica_updates = 0;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@log_replica_updates = 0;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@session.log_replica_updates = TRUE;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@log_replica_updates = TRUE;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@session.log_replica_updates = FALSE;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@log_replica_updates = FALSE;
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@session.log_replica_updates = 'ON';
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@log_replica_updates = 'ON';
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@session.log_replica_updates = 'OFF';
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SET @@log_replica_updates = 'OFF';
ERROR HY000: Variable 'log_replica_updates' is a read only variable
SELECT @@session.log_replica_updates;
ERROR HY000: Variable 'log_replica_updates' is a GLOBAL variable
SELECT @@log_replica_updates;
@@log_replica_updates
1
include/assert.inc [Variable log_replica_updates should exist in performance_schema.session_variables]
==== Testing SET @@global.log_slave_updates [read-only] ====
# Initial value is default
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
# Valid values for *read-only* variable cannot be set
SET @@global.log_slave_updates = ON;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
SET @@global.log_slave_updates = OFF;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
SET @@global.log_slave_updates = 1;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
SET @@global.log_slave_updates = 0;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
SET @@global.log_slave_updates = TRUE;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
SET @@global.log_slave_updates = FALSE;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
SET @@global.log_slave_updates = 'ON';
ERROR HY000: Variable 'log_slave_updates' is a read only variable
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
SET @@global.log_slave_updates = 'OFF';
ERROR HY000: Variable 'log_slave_updates' is a read only variable
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
# Setting default value for variable
SET @@global.log_slave_updates = DEFAULT;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
include/assert.inc [@@global.log_slave_updates should be 1]
include/assert.inc [log_slave_updates should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_replica_updates should be 1]
include/assert.inc [log_replica_updates should be 1 in P_S.global_variables]
==== Testing SET @@session.log_slave_updates [invalid scope] ====
SET @@session.log_slave_updates = DEFAULT;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@log_slave_updates = DEFAULT;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@session.log_slave_updates = ON;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@log_slave_updates = ON;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@session.log_slave_updates = OFF;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@log_slave_updates = OFF;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@session.log_slave_updates = 1;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@log_slave_updates = 1;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@session.log_slave_updates = 0;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@log_slave_updates = 0;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@session.log_slave_updates = TRUE;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@log_slave_updates = TRUE;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@session.log_slave_updates = FALSE;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@log_slave_updates = FALSE;
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@session.log_slave_updates = 'ON';
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@log_slave_updates = 'ON';
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@session.log_slave_updates = 'OFF';
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SET @@log_slave_updates = 'OFF';
ERROR HY000: Variable 'log_slave_updates' is a read only variable
SELECT @@session.log_slave_updates;
ERROR HY000: Variable 'log_slave_updates' is a GLOBAL variable
SELECT @@log_slave_updates;
@@log_slave_updates
1
Warnings:
Warning	1287	'@@log_slave_updates' is deprecated and will be removed in a future release. Please use log_replica_updates instead.
include/assert.inc [Variable log_slave_updates should exist in performance_schema.session_variables]
sysvar:
- name=log_slow_replica_statements
- alias=log_slow_slave_statements
- invalid=[0.1, 2, -1, "''", "'x'", "'TRUE'", "'1'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=
- max=
- block_size=1
- default=0
- values=["ON", "OFF", 1, 0, "TRUE", "FALSE", "'ON'", "'OFF'"]
==== Testing SET @@global.log_slow_replica_statements [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
# Valid values for variable
SET @@global.log_slow_replica_statements=ON;
include/assert.inc [@@global.log_slow_replica_statements should be 1]
include/assert.inc [log_slow_replica_statements should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slow_slave_statements should be 1]
include/assert.inc [log_slow_slave_statements should be 1 in P_S.global_variables]
SET @@global.log_slow_replica_statements=OFF;
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
SET @@global.log_slow_replica_statements=1;
include/assert.inc [@@global.log_slow_replica_statements should be 1]
include/assert.inc [log_slow_replica_statements should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slow_slave_statements should be 1]
include/assert.inc [log_slow_slave_statements should be 1 in P_S.global_variables]
SET @@global.log_slow_replica_statements=0;
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
SET @@global.log_slow_replica_statements=TRUE;
include/assert.inc [@@global.log_slow_replica_statements should be 1]
include/assert.inc [log_slow_replica_statements should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slow_slave_statements should be 1]
include/assert.inc [log_slow_slave_statements should be 1 in P_S.global_variables]
SET @@global.log_slow_replica_statements=FALSE;
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
SET @@global.log_slow_replica_statements='ON';
include/assert.inc [@@global.log_slow_replica_statements should be 1]
include/assert.inc [log_slow_replica_statements should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slow_slave_statements should be 1]
include/assert.inc [log_slow_slave_statements should be 1 in P_S.global_variables]
SET @@global.log_slow_replica_statements='OFF';
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
# Setting default value for variable
SET @@global.log_slow_replica_statements = DEFAULT;
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
# Invalid values
SET @@global.log_slow_replica_statements = 0.1;
Got one of the listed errors
SET @@global.log_slow_replica_statements = 2;
Got one of the listed errors
SET @@global.log_slow_replica_statements = -1;
Got one of the listed errors
SET @@global.log_slow_replica_statements = '';
Got one of the listed errors
SET @@global.log_slow_replica_statements = 'x';
Got one of the listed errors
SET @@global.log_slow_replica_statements = 'TRUE';
Got one of the listed errors
SET @@global.log_slow_replica_statements = '1';
Got one of the listed errors
SET @@global.log_slow_replica_statements = NULL;
Got one of the listed errors
# Restore default
SET @@global.log_slow_replica_statements = DEFAULT;
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
==== Testing SET @@session.log_slow_replica_statements [invalid scope] ====
SET @@session.log_slow_replica_statements = DEFAULT;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_replica_statements = DEFAULT;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_replica_statements = ON;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_replica_statements = ON;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_replica_statements = OFF;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_replica_statements = OFF;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_replica_statements = 1;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_replica_statements = 1;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_replica_statements = 0;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_replica_statements = 0;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_replica_statements = TRUE;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_replica_statements = TRUE;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_replica_statements = FALSE;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_replica_statements = FALSE;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_replica_statements = 'ON';
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_replica_statements = 'ON';
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_replica_statements = 'OFF';
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_replica_statements = 'OFF';
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.log_slow_replica_statements;
ERROR HY000: Variable 'log_slow_replica_statements' is a GLOBAL variable
SELECT @@log_slow_replica_statements;
@@log_slow_replica_statements
0
include/assert.inc [Variable log_slow_replica_statements should exist in performance_schema.session_variables]
==== Testing SET @@global.log_slow_slave_statements [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
# Valid values for variable
SET @@global.log_slow_slave_statements=ON;
Warnings:
Warning	1287	'@@log_slow_slave_statements' is deprecated and will be removed in a future release. Please use log_slow_replica_statements instead.
include/assert.inc [@@global.log_slow_slave_statements should be 1]
include/assert.inc [log_slow_slave_statements should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slow_replica_statements should be 1]
include/assert.inc [log_slow_replica_statements should be 1 in P_S.global_variables]
SET @@global.log_slow_slave_statements=OFF;
Warnings:
Warning	1287	'@@log_slow_slave_statements' is deprecated and will be removed in a future release. Please use log_slow_replica_statements instead.
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
SET @@global.log_slow_slave_statements=1;
Warnings:
Warning	1287	'@@log_slow_slave_statements' is deprecated and will be removed in a future release. Please use log_slow_replica_statements instead.
include/assert.inc [@@global.log_slow_slave_statements should be 1]
include/assert.inc [log_slow_slave_statements should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slow_replica_statements should be 1]
include/assert.inc [log_slow_replica_statements should be 1 in P_S.global_variables]
SET @@global.log_slow_slave_statements=0;
Warnings:
Warning	1287	'@@log_slow_slave_statements' is deprecated and will be removed in a future release. Please use log_slow_replica_statements instead.
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
SET @@global.log_slow_slave_statements=TRUE;
Warnings:
Warning	1287	'@@log_slow_slave_statements' is deprecated and will be removed in a future release. Please use log_slow_replica_statements instead.
include/assert.inc [@@global.log_slow_slave_statements should be 1]
include/assert.inc [log_slow_slave_statements should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slow_replica_statements should be 1]
include/assert.inc [log_slow_replica_statements should be 1 in P_S.global_variables]
SET @@global.log_slow_slave_statements=FALSE;
Warnings:
Warning	1287	'@@log_slow_slave_statements' is deprecated and will be removed in a future release. Please use log_slow_replica_statements instead.
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
SET @@global.log_slow_slave_statements='ON';
Warnings:
Warning	1287	'@@log_slow_slave_statements' is deprecated and will be removed in a future release. Please use log_slow_replica_statements instead.
include/assert.inc [@@global.log_slow_slave_statements should be 1]
include/assert.inc [log_slow_slave_statements should be 1 in P_S.global_variables]
include/assert.inc [@@global.log_slow_replica_statements should be 1]
include/assert.inc [log_slow_replica_statements should be 1 in P_S.global_variables]
SET @@global.log_slow_slave_statements='OFF';
Warnings:
Warning	1287	'@@log_slow_slave_statements' is deprecated and will be removed in a future release. Please use log_slow_replica_statements instead.
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
# Setting default value for variable
SET @@global.log_slow_slave_statements = DEFAULT;
Warnings:
Warning	1287	'@@log_slow_slave_statements' is deprecated and will be removed in a future release. Please use log_slow_replica_statements instead.
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
# Invalid values
SET @@global.log_slow_slave_statements = 0.1;
Got one of the listed errors
SET @@global.log_slow_slave_statements = 2;
Got one of the listed errors
SET @@global.log_slow_slave_statements = -1;
Got one of the listed errors
SET @@global.log_slow_slave_statements = '';
Got one of the listed errors
SET @@global.log_slow_slave_statements = 'x';
Got one of the listed errors
SET @@global.log_slow_slave_statements = 'TRUE';
Got one of the listed errors
SET @@global.log_slow_slave_statements = '1';
Got one of the listed errors
SET @@global.log_slow_slave_statements = NULL;
Got one of the listed errors
# Restore default
SET @@global.log_slow_slave_statements = DEFAULT;
Warnings:
Warning	1287	'@@log_slow_slave_statements' is deprecated and will be removed in a future release. Please use log_slow_replica_statements instead.
include/assert.inc [@@global.log_slow_slave_statements should be 0]
include/assert.inc [log_slow_slave_statements should be 0 in P_S.global_variables]
include/assert.inc [@@global.log_slow_replica_statements should be 0]
include/assert.inc [log_slow_replica_statements should be 0 in P_S.global_variables]
==== Testing SET @@session.log_slow_slave_statements [invalid scope] ====
SET @@session.log_slow_slave_statements = DEFAULT;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_slave_statements = DEFAULT;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_slave_statements = ON;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_slave_statements = ON;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_slave_statements = OFF;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_slave_statements = OFF;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_slave_statements = 1;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_slave_statements = 1;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_slave_statements = 0;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_slave_statements = 0;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_slave_statements = TRUE;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_slave_statements = TRUE;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_slave_statements = FALSE;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_slave_statements = FALSE;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_slave_statements = 'ON';
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_slave_statements = 'ON';
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.log_slow_slave_statements = 'OFF';
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SET @@log_slow_slave_statements = 'OFF';
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.log_slow_slave_statements;
ERROR HY000: Variable 'log_slow_slave_statements' is a GLOBAL variable
SELECT @@log_slow_slave_statements;
@@log_slow_slave_statements
0
Warnings:
Warning	1287	'@@log_slow_slave_statements' is deprecated and will be removed in a future release. Please use log_slow_replica_statements instead.
include/assert.inc [Variable log_slow_slave_statements should exist in performance_schema.session_variables]
sysvar:
- name=replica_allow_batching
- alias=slave_allow_batching
- invalid=[0.1, 2, -1, "''", "'x'", "'TRUE'", "'1'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=
- max=
- block_size=1
- default=1
- values=["ON", "OFF", 1, 0, "TRUE", "FALSE", "'ON'", "'OFF'"]
==== Testing SET @@global.replica_allow_batching [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
# Valid values for variable
SET @@global.replica_allow_batching=ON;
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
SET @@global.replica_allow_batching=OFF;
include/assert.inc [@@global.replica_allow_batching should be 0]
include/assert.inc [replica_allow_batching should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_allow_batching should be 0]
include/assert.inc [slave_allow_batching should be 0 in P_S.global_variables]
SET @@global.replica_allow_batching=1;
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
SET @@global.replica_allow_batching=0;
include/assert.inc [@@global.replica_allow_batching should be 0]
include/assert.inc [replica_allow_batching should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_allow_batching should be 0]
include/assert.inc [slave_allow_batching should be 0 in P_S.global_variables]
SET @@global.replica_allow_batching=TRUE;
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
SET @@global.replica_allow_batching=FALSE;
include/assert.inc [@@global.replica_allow_batching should be 0]
include/assert.inc [replica_allow_batching should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_allow_batching should be 0]
include/assert.inc [slave_allow_batching should be 0 in P_S.global_variables]
SET @@global.replica_allow_batching='ON';
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
SET @@global.replica_allow_batching='OFF';
include/assert.inc [@@global.replica_allow_batching should be 0]
include/assert.inc [replica_allow_batching should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_allow_batching should be 0]
include/assert.inc [slave_allow_batching should be 0 in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_allow_batching = DEFAULT;
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
# Invalid values
SET @@global.replica_allow_batching = 0.1;
Got one of the listed errors
SET @@global.replica_allow_batching = 2;
Got one of the listed errors
SET @@global.replica_allow_batching = -1;
Got one of the listed errors
SET @@global.replica_allow_batching = '';
Got one of the listed errors
SET @@global.replica_allow_batching = 'x';
Got one of the listed errors
SET @@global.replica_allow_batching = 'TRUE';
Got one of the listed errors
SET @@global.replica_allow_batching = '1';
Got one of the listed errors
SET @@global.replica_allow_batching = NULL;
Got one of the listed errors
# Restore default
SET @@global.replica_allow_batching = DEFAULT;
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
==== Testing SET @@session.replica_allow_batching [invalid scope] ====
SET @@session.replica_allow_batching = DEFAULT;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_allow_batching = DEFAULT;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_allow_batching = ON;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_allow_batching = ON;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_allow_batching = OFF;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_allow_batching = OFF;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_allow_batching = 1;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_allow_batching = 1;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_allow_batching = 0;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_allow_batching = 0;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_allow_batching = TRUE;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_allow_batching = TRUE;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_allow_batching = FALSE;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_allow_batching = FALSE;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_allow_batching = 'ON';
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_allow_batching = 'ON';
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_allow_batching = 'OFF';
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_allow_batching = 'OFF';
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_allow_batching;
ERROR HY000: Variable 'replica_allow_batching' is a GLOBAL variable
SELECT @@replica_allow_batching;
@@replica_allow_batching
1
include/assert.inc [Variable replica_allow_batching should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_allow_batching [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
# Valid values for variable
SET @@global.slave_allow_batching=ON;
Warnings:
Warning	1287	'@@slave_allow_batching' is deprecated and will be removed in a future release. Please use replica_allow_batching instead.
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
SET @@global.slave_allow_batching=OFF;
Warnings:
Warning	1287	'@@slave_allow_batching' is deprecated and will be removed in a future release. Please use replica_allow_batching instead.
include/assert.inc [@@global.slave_allow_batching should be 0]
include/assert.inc [slave_allow_batching should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_allow_batching should be 0]
include/assert.inc [replica_allow_batching should be 0 in P_S.global_variables]
SET @@global.slave_allow_batching=1;
Warnings:
Warning	1287	'@@slave_allow_batching' is deprecated and will be removed in a future release. Please use replica_allow_batching instead.
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
SET @@global.slave_allow_batching=0;
Warnings:
Warning	1287	'@@slave_allow_batching' is deprecated and will be removed in a future release. Please use replica_allow_batching instead.
include/assert.inc [@@global.slave_allow_batching should be 0]
include/assert.inc [slave_allow_batching should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_allow_batching should be 0]
include/assert.inc [replica_allow_batching should be 0 in P_S.global_variables]
SET @@global.slave_allow_batching=TRUE;
Warnings:
Warning	1287	'@@slave_allow_batching' is deprecated and will be removed in a future release. Please use replica_allow_batching instead.
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
SET @@global.slave_allow_batching=FALSE;
Warnings:
Warning	1287	'@@slave_allow_batching' is deprecated and will be removed in a future release. Please use replica_allow_batching instead.
include/assert.inc [@@global.slave_allow_batching should be 0]
include/assert.inc [slave_allow_batching should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_allow_batching should be 0]
include/assert.inc [replica_allow_batching should be 0 in P_S.global_variables]
SET @@global.slave_allow_batching='ON';
Warnings:
Warning	1287	'@@slave_allow_batching' is deprecated and will be removed in a future release. Please use replica_allow_batching instead.
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
SET @@global.slave_allow_batching='OFF';
Warnings:
Warning	1287	'@@slave_allow_batching' is deprecated and will be removed in a future release. Please use replica_allow_batching instead.
include/assert.inc [@@global.slave_allow_batching should be 0]
include/assert.inc [slave_allow_batching should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_allow_batching should be 0]
include/assert.inc [replica_allow_batching should be 0 in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_allow_batching = DEFAULT;
Warnings:
Warning	1287	'@@slave_allow_batching' is deprecated and will be removed in a future release. Please use replica_allow_batching instead.
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
# Invalid values
SET @@global.slave_allow_batching = 0.1;
Got one of the listed errors
SET @@global.slave_allow_batching = 2;
Got one of the listed errors
SET @@global.slave_allow_batching = -1;
Got one of the listed errors
SET @@global.slave_allow_batching = '';
Got one of the listed errors
SET @@global.slave_allow_batching = 'x';
Got one of the listed errors
SET @@global.slave_allow_batching = 'TRUE';
Got one of the listed errors
SET @@global.slave_allow_batching = '1';
Got one of the listed errors
SET @@global.slave_allow_batching = NULL;
Got one of the listed errors
# Restore default
SET @@global.slave_allow_batching = DEFAULT;
Warnings:
Warning	1287	'@@slave_allow_batching' is deprecated and will be removed in a future release. Please use replica_allow_batching instead.
include/assert.inc [@@global.slave_allow_batching should be 1]
include/assert.inc [slave_allow_batching should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_allow_batching should be 1]
include/assert.inc [replica_allow_batching should be 1 in P_S.global_variables]
==== Testing SET @@session.slave_allow_batching [invalid scope] ====
SET @@session.slave_allow_batching = DEFAULT;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_allow_batching = DEFAULT;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_allow_batching = ON;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_allow_batching = ON;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_allow_batching = OFF;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_allow_batching = OFF;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_allow_batching = 1;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_allow_batching = 1;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_allow_batching = 0;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_allow_batching = 0;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_allow_batching = TRUE;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_allow_batching = TRUE;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_allow_batching = FALSE;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_allow_batching = FALSE;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_allow_batching = 'ON';
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_allow_batching = 'ON';
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_allow_batching = 'OFF';
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_allow_batching = 'OFF';
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_allow_batching;
ERROR HY000: Variable 'slave_allow_batching' is a GLOBAL variable
SELECT @@slave_allow_batching;
@@slave_allow_batching
1
Warnings:
Warning	1287	'@@slave_allow_batching' is deprecated and will be removed in a future release. Please use replica_allow_batching instead.
include/assert.inc [Variable slave_allow_batching should exist in performance_schema.session_variables]
sysvar:
- name=replica_checkpoint_group
- alias=slave_checkpoint_group
- invalid=[1.5, "ON", "'x'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=32
- max=524280
- block_size=8
- default=512
- values=[520, 1000, 2000]
==== Testing SET @@global.replica_checkpoint_group [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_checkpoint_group should be 512]
include/assert.inc [replica_checkpoint_group should be 512 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 512]
include/assert.inc [slave_checkpoint_group should be 512 in P_S.global_variables]
# Valid values for variable
SET @@global.replica_checkpoint_group=520;
include/assert.inc [@@global.replica_checkpoint_group should be 520]
include/assert.inc [replica_checkpoint_group should be 520 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 520]
include/assert.inc [slave_checkpoint_group should be 520 in P_S.global_variables]
SET @@global.replica_checkpoint_group=1000;
include/assert.inc [@@global.replica_checkpoint_group should be 1000]
include/assert.inc [replica_checkpoint_group should be 1000 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 1000]
include/assert.inc [slave_checkpoint_group should be 1000 in P_S.global_variables]
SET @@global.replica_checkpoint_group=2000;
include/assert.inc [@@global.replica_checkpoint_group should be 2000]
include/assert.inc [replica_checkpoint_group should be 2000 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 2000]
include/assert.inc [slave_checkpoint_group should be 2000 in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_checkpoint_group = DEFAULT;
include/assert.inc [@@global.replica_checkpoint_group should be 512]
include/assert.inc [replica_checkpoint_group should be 512 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 512]
include/assert.inc [slave_checkpoint_group should be 512 in P_S.global_variables]
# Minimum value
SET @@global.replica_checkpoint_group = 32;
include/assert.inc [@@global.replica_checkpoint_group should be 32]
include/assert.inc [replica_checkpoint_group should be 32 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 32]
include/assert.inc [slave_checkpoint_group should be 32 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.replica_checkpoint_group = 31;
Warnings:
Warning	1292	Truncated incorrect replica_checkpoint_group value: '31'
include/assert.inc [@@global.replica_checkpoint_group should be 32]
include/assert.inc [replica_checkpoint_group should be 32 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 32]
include/assert.inc [slave_checkpoint_group should be 32 in P_S.global_variables]
SET @@global.replica_checkpoint_group = 24;
Warnings:
Warning	1292	Truncated incorrect replica_checkpoint_group value: '24'
include/assert.inc [@@global.replica_checkpoint_group should be 32]
include/assert.inc [replica_checkpoint_group should be 32 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 32]
include/assert.inc [slave_checkpoint_group should be 32 in P_S.global_variables]
# Maximum value
SET @@global.replica_checkpoint_group = 524280;
include/assert.inc [@@global.replica_checkpoint_group should be 524280]
include/assert.inc [replica_checkpoint_group should be 524280 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 524280]
include/assert.inc [slave_checkpoint_group should be 524280 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.replica_checkpoint_group = 524281;
Warnings:
Warning	1292	Truncated incorrect replica_checkpoint_group value: '524281'
include/assert.inc [@@global.replica_checkpoint_group should be 524280]
include/assert.inc [replica_checkpoint_group should be 524280 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 524280]
include/assert.inc [slave_checkpoint_group should be 524280 in P_S.global_variables]
SET @@global.replica_checkpoint_group = 524288;
Warnings:
Warning	1292	Truncated incorrect replica_checkpoint_group value: '524288'
include/assert.inc [@@global.replica_checkpoint_group should be 524280]
include/assert.inc [replica_checkpoint_group should be 524280 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 524280]
include/assert.inc [slave_checkpoint_group should be 524280 in P_S.global_variables]
# Non-block-size value
SET @@global.replica_checkpoint_group = 519;
Warnings:
Warning	1292	Truncated incorrect replica_checkpoint_group value: '519'
include/assert.inc [@@global.replica_checkpoint_group should be 512]
include/assert.inc [replica_checkpoint_group should be 512 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 512]
include/assert.inc [slave_checkpoint_group should be 512 in P_S.global_variables]
# Invalid values
SET @@global.replica_checkpoint_group = 1.5;
Got one of the listed errors
SET @@global.replica_checkpoint_group = ON;
Got one of the listed errors
SET @@global.replica_checkpoint_group = 'x';
Got one of the listed errors
SET @@global.replica_checkpoint_group = NULL;
Got one of the listed errors
# Restore default
SET @@global.replica_checkpoint_group = DEFAULT;
include/assert.inc [@@global.replica_checkpoint_group should be 512]
include/assert.inc [replica_checkpoint_group should be 512 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_group should be 512]
include/assert.inc [slave_checkpoint_group should be 512 in P_S.global_variables]
==== Testing SET @@session.replica_checkpoint_group [invalid scope] ====
SET @@session.replica_checkpoint_group = DEFAULT;
ERROR HY000: Variable 'replica_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_checkpoint_group = DEFAULT;
ERROR HY000: Variable 'replica_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_checkpoint_group = 520;
ERROR HY000: Variable 'replica_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_checkpoint_group = 520;
ERROR HY000: Variable 'replica_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_checkpoint_group = 1000;
ERROR HY000: Variable 'replica_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_checkpoint_group = 1000;
ERROR HY000: Variable 'replica_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_checkpoint_group = 2000;
ERROR HY000: Variable 'replica_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_checkpoint_group = 2000;
ERROR HY000: Variable 'replica_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_checkpoint_group;
ERROR HY000: Variable 'replica_checkpoint_group' is a GLOBAL variable
SELECT @@replica_checkpoint_group;
@@replica_checkpoint_group
512
include/assert.inc [Variable replica_checkpoint_group should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_checkpoint_group [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_checkpoint_group should be 512]
include/assert.inc [slave_checkpoint_group should be 512 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 512]
include/assert.inc [replica_checkpoint_group should be 512 in P_S.global_variables]
# Valid values for variable
SET @@global.slave_checkpoint_group=520;
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
include/assert.inc [@@global.slave_checkpoint_group should be 520]
include/assert.inc [slave_checkpoint_group should be 520 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 520]
include/assert.inc [replica_checkpoint_group should be 520 in P_S.global_variables]
SET @@global.slave_checkpoint_group=1000;
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
include/assert.inc [@@global.slave_checkpoint_group should be 1000]
include/assert.inc [slave_checkpoint_group should be 1000 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 1000]
include/assert.inc [replica_checkpoint_group should be 1000 in P_S.global_variables]
SET @@global.slave_checkpoint_group=2000;
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
include/assert.inc [@@global.slave_checkpoint_group should be 2000]
include/assert.inc [slave_checkpoint_group should be 2000 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 2000]
include/assert.inc [replica_checkpoint_group should be 2000 in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_checkpoint_group = DEFAULT;
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
include/assert.inc [@@global.slave_checkpoint_group should be 512]
include/assert.inc [slave_checkpoint_group should be 512 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 512]
include/assert.inc [replica_checkpoint_group should be 512 in P_S.global_variables]
# Minimum value
SET @@global.slave_checkpoint_group = 32;
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
include/assert.inc [@@global.slave_checkpoint_group should be 32]
include/assert.inc [slave_checkpoint_group should be 32 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 32]
include/assert.inc [replica_checkpoint_group should be 32 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.slave_checkpoint_group = 31;
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
Warning	1292	Truncated incorrect replica_checkpoint_group value: '31'
include/assert.inc [@@global.slave_checkpoint_group should be 32]
include/assert.inc [slave_checkpoint_group should be 32 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 32]
include/assert.inc [replica_checkpoint_group should be 32 in P_S.global_variables]
SET @@global.slave_checkpoint_group = 24;
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
Warning	1292	Truncated incorrect replica_checkpoint_group value: '24'
include/assert.inc [@@global.slave_checkpoint_group should be 32]
include/assert.inc [slave_checkpoint_group should be 32 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 32]
include/assert.inc [replica_checkpoint_group should be 32 in P_S.global_variables]
# Maximum value
SET @@global.slave_checkpoint_group = 524280;
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
include/assert.inc [@@global.slave_checkpoint_group should be 524280]
include/assert.inc [slave_checkpoint_group should be 524280 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 524280]
include/assert.inc [replica_checkpoint_group should be 524280 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.slave_checkpoint_group = 524281;
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
Warning	1292	Truncated incorrect replica_checkpoint_group value: '524281'
include/assert.inc [@@global.slave_checkpoint_group should be 524280]
include/assert.inc [slave_checkpoint_group should be 524280 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 524280]
include/assert.inc [replica_checkpoint_group should be 524280 in P_S.global_variables]
SET @@global.slave_checkpoint_group = 524288;
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
Warning	1292	Truncated incorrect replica_checkpoint_group value: '524288'
include/assert.inc [@@global.slave_checkpoint_group should be 524280]
include/assert.inc [slave_checkpoint_group should be 524280 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 524280]
include/assert.inc [replica_checkpoint_group should be 524280 in P_S.global_variables]
# Non-block-size value
SET @@global.slave_checkpoint_group = 519;
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
Warning	1292	Truncated incorrect replica_checkpoint_group value: '519'
include/assert.inc [@@global.slave_checkpoint_group should be 512]
include/assert.inc [slave_checkpoint_group should be 512 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 512]
include/assert.inc [replica_checkpoint_group should be 512 in P_S.global_variables]
# Invalid values
SET @@global.slave_checkpoint_group = 1.5;
Got one of the listed errors
SET @@global.slave_checkpoint_group = ON;
Got one of the listed errors
SET @@global.slave_checkpoint_group = 'x';
Got one of the listed errors
SET @@global.slave_checkpoint_group = NULL;
Got one of the listed errors
# Restore default
SET @@global.slave_checkpoint_group = DEFAULT;
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
include/assert.inc [@@global.slave_checkpoint_group should be 512]
include/assert.inc [slave_checkpoint_group should be 512 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_group should be 512]
include/assert.inc [replica_checkpoint_group should be 512 in P_S.global_variables]
==== Testing SET @@session.slave_checkpoint_group [invalid scope] ====
SET @@session.slave_checkpoint_group = DEFAULT;
ERROR HY000: Variable 'slave_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_checkpoint_group = DEFAULT;
ERROR HY000: Variable 'slave_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_checkpoint_group = 520;
ERROR HY000: Variable 'slave_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_checkpoint_group = 520;
ERROR HY000: Variable 'slave_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_checkpoint_group = 1000;
ERROR HY000: Variable 'slave_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_checkpoint_group = 1000;
ERROR HY000: Variable 'slave_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_checkpoint_group = 2000;
ERROR HY000: Variable 'slave_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_checkpoint_group = 2000;
ERROR HY000: Variable 'slave_checkpoint_group' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_checkpoint_group;
ERROR HY000: Variable 'slave_checkpoint_group' is a GLOBAL variable
SELECT @@slave_checkpoint_group;
@@slave_checkpoint_group
512
Warnings:
Warning	1287	'@@slave_checkpoint_group' is deprecated and will be removed in a future release. Please use replica_checkpoint_group instead.
include/assert.inc [Variable slave_checkpoint_group should exist in performance_schema.session_variables]
sysvar:
- name=replica_checkpoint_period
- alias=slave_checkpoint_period
- invalid=[1.5, "ON", "'x'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=1
- max=4294967295
- block_size=1
- default=300
- values=[1, 2, 12323]
==== Testing SET @@global.replica_checkpoint_period [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_checkpoint_period should be 300]
include/assert.inc [replica_checkpoint_period should be 300 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_period should be 300]
include/assert.inc [slave_checkpoint_period should be 300 in P_S.global_variables]
# Valid values for variable
SET @@global.replica_checkpoint_period=1;
include/assert.inc [@@global.replica_checkpoint_period should be 1]
include/assert.inc [replica_checkpoint_period should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_period should be 1]
include/assert.inc [slave_checkpoint_period should be 1 in P_S.global_variables]
SET @@global.replica_checkpoint_period=2;
include/assert.inc [@@global.replica_checkpoint_period should be 2]
include/assert.inc [replica_checkpoint_period should be 2 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_period should be 2]
include/assert.inc [slave_checkpoint_period should be 2 in P_S.global_variables]
SET @@global.replica_checkpoint_period=12323;
include/assert.inc [@@global.replica_checkpoint_period should be 12323]
include/assert.inc [replica_checkpoint_period should be 12323 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_period should be 12323]
include/assert.inc [slave_checkpoint_period should be 12323 in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_checkpoint_period = DEFAULT;
include/assert.inc [@@global.replica_checkpoint_period should be 300]
include/assert.inc [replica_checkpoint_period should be 300 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_period should be 300]
include/assert.inc [slave_checkpoint_period should be 300 in P_S.global_variables]
# Minimum value
SET @@global.replica_checkpoint_period = 1;
include/assert.inc [@@global.replica_checkpoint_period should be 1]
include/assert.inc [replica_checkpoint_period should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_period should be 1]
include/assert.inc [slave_checkpoint_period should be 1 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.replica_checkpoint_period = 0;
Warnings:
Warning	1292	Truncated incorrect replica_checkpoint_period value: '0'
include/assert.inc [@@global.replica_checkpoint_period should be 1]
include/assert.inc [replica_checkpoint_period should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_period should be 1]
include/assert.inc [slave_checkpoint_period should be 1 in P_S.global_variables]
# Maximum value
SET @@global.replica_checkpoint_period = 4294967295;
include/assert.inc [@@global.replica_checkpoint_period should be 4294967295]
include/assert.inc [replica_checkpoint_period should be 4294967295 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_period should be 4294967295]
include/assert.inc [slave_checkpoint_period should be 4294967295 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.replica_checkpoint_period = 4294967296;
Warnings:
Warning	1292	Truncated incorrect replica_checkpoint_period value: '4294967296'
include/assert.inc [@@global.replica_checkpoint_period should be 4294967295]
include/assert.inc [replica_checkpoint_period should be 4294967295 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_period should be 4294967295]
include/assert.inc [slave_checkpoint_period should be 4294967295 in P_S.global_variables]
# Invalid values
SET @@global.replica_checkpoint_period = 1.5;
Got one of the listed errors
SET @@global.replica_checkpoint_period = ON;
Got one of the listed errors
SET @@global.replica_checkpoint_period = 'x';
Got one of the listed errors
SET @@global.replica_checkpoint_period = NULL;
Got one of the listed errors
# Restore default
SET @@global.replica_checkpoint_period = DEFAULT;
include/assert.inc [@@global.replica_checkpoint_period should be 300]
include/assert.inc [replica_checkpoint_period should be 300 in P_S.global_variables]
include/assert.inc [@@global.slave_checkpoint_period should be 300]
include/assert.inc [slave_checkpoint_period should be 300 in P_S.global_variables]
==== Testing SET @@session.replica_checkpoint_period [invalid scope] ====
SET @@session.replica_checkpoint_period = DEFAULT;
ERROR HY000: Variable 'replica_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_checkpoint_period = DEFAULT;
ERROR HY000: Variable 'replica_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_checkpoint_period = 1;
ERROR HY000: Variable 'replica_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_checkpoint_period = 1;
ERROR HY000: Variable 'replica_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_checkpoint_period = 2;
ERROR HY000: Variable 'replica_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_checkpoint_period = 2;
ERROR HY000: Variable 'replica_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_checkpoint_period = 12323;
ERROR HY000: Variable 'replica_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_checkpoint_period = 12323;
ERROR HY000: Variable 'replica_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_checkpoint_period;
ERROR HY000: Variable 'replica_checkpoint_period' is a GLOBAL variable
SELECT @@replica_checkpoint_period;
@@replica_checkpoint_period
300
include/assert.inc [Variable replica_checkpoint_period should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_checkpoint_period [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_checkpoint_period should be 300]
include/assert.inc [slave_checkpoint_period should be 300 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_period should be 300]
include/assert.inc [replica_checkpoint_period should be 300 in P_S.global_variables]
# Valid values for variable
SET @@global.slave_checkpoint_period=1;
Warnings:
Warning	1287	'@@slave_checkpoint_period' is deprecated and will be removed in a future release. Please use replica_checkpoint_period instead.
include/assert.inc [@@global.slave_checkpoint_period should be 1]
include/assert.inc [slave_checkpoint_period should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_period should be 1]
include/assert.inc [replica_checkpoint_period should be 1 in P_S.global_variables]
SET @@global.slave_checkpoint_period=2;
Warnings:
Warning	1287	'@@slave_checkpoint_period' is deprecated and will be removed in a future release. Please use replica_checkpoint_period instead.
include/assert.inc [@@global.slave_checkpoint_period should be 2]
include/assert.inc [slave_checkpoint_period should be 2 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_period should be 2]
include/assert.inc [replica_checkpoint_period should be 2 in P_S.global_variables]
SET @@global.slave_checkpoint_period=12323;
Warnings:
Warning	1287	'@@slave_checkpoint_period' is deprecated and will be removed in a future release. Please use replica_checkpoint_period instead.
include/assert.inc [@@global.slave_checkpoint_period should be 12323]
include/assert.inc [slave_checkpoint_period should be 12323 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_period should be 12323]
include/assert.inc [replica_checkpoint_period should be 12323 in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_checkpoint_period = DEFAULT;
Warnings:
Warning	1287	'@@slave_checkpoint_period' is deprecated and will be removed in a future release. Please use replica_checkpoint_period instead.
include/assert.inc [@@global.slave_checkpoint_period should be 300]
include/assert.inc [slave_checkpoint_period should be 300 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_period should be 300]
include/assert.inc [replica_checkpoint_period should be 300 in P_S.global_variables]
# Minimum value
SET @@global.slave_checkpoint_period = 1;
Warnings:
Warning	1287	'@@slave_checkpoint_period' is deprecated and will be removed in a future release. Please use replica_checkpoint_period instead.
include/assert.inc [@@global.slave_checkpoint_period should be 1]
include/assert.inc [slave_checkpoint_period should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_period should be 1]
include/assert.inc [replica_checkpoint_period should be 1 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.slave_checkpoint_period = 0;
Warnings:
Warning	1287	'@@slave_checkpoint_period' is deprecated and will be removed in a future release. Please use replica_checkpoint_period instead.
Warning	1292	Truncated incorrect replica_checkpoint_period value: '0'
include/assert.inc [@@global.slave_checkpoint_period should be 1]
include/assert.inc [slave_checkpoint_period should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_period should be 1]
include/assert.inc [replica_checkpoint_period should be 1 in P_S.global_variables]
# Maximum value
SET @@global.slave_checkpoint_period = 4294967295;
Warnings:
Warning	1287	'@@slave_checkpoint_period' is deprecated and will be removed in a future release. Please use replica_checkpoint_period instead.
include/assert.inc [@@global.slave_checkpoint_period should be 4294967295]
include/assert.inc [slave_checkpoint_period should be 4294967295 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_period should be 4294967295]
include/assert.inc [replica_checkpoint_period should be 4294967295 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.slave_checkpoint_period = 4294967296;
Warnings:
Warning	1287	'@@slave_checkpoint_period' is deprecated and will be removed in a future release. Please use replica_checkpoint_period instead.
Warning	1292	Truncated incorrect replica_checkpoint_period value: '4294967296'
include/assert.inc [@@global.slave_checkpoint_period should be 4294967295]
include/assert.inc [slave_checkpoint_period should be 4294967295 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_period should be 4294967295]
include/assert.inc [replica_checkpoint_period should be 4294967295 in P_S.global_variables]
# Invalid values
SET @@global.slave_checkpoint_period = 1.5;
Got one of the listed errors
SET @@global.slave_checkpoint_period = ON;
Got one of the listed errors
SET @@global.slave_checkpoint_period = 'x';
Got one of the listed errors
SET @@global.slave_checkpoint_period = NULL;
Got one of the listed errors
# Restore default
SET @@global.slave_checkpoint_period = DEFAULT;
Warnings:
Warning	1287	'@@slave_checkpoint_period' is deprecated and will be removed in a future release. Please use replica_checkpoint_period instead.
include/assert.inc [@@global.slave_checkpoint_period should be 300]
include/assert.inc [slave_checkpoint_period should be 300 in P_S.global_variables]
include/assert.inc [@@global.replica_checkpoint_period should be 300]
include/assert.inc [replica_checkpoint_period should be 300 in P_S.global_variables]
==== Testing SET @@session.slave_checkpoint_period [invalid scope] ====
SET @@session.slave_checkpoint_period = DEFAULT;
ERROR HY000: Variable 'slave_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_checkpoint_period = DEFAULT;
ERROR HY000: Variable 'slave_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_checkpoint_period = 1;
ERROR HY000: Variable 'slave_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_checkpoint_period = 1;
ERROR HY000: Variable 'slave_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_checkpoint_period = 2;
ERROR HY000: Variable 'slave_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_checkpoint_period = 2;
ERROR HY000: Variable 'slave_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_checkpoint_period = 12323;
ERROR HY000: Variable 'slave_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_checkpoint_period = 12323;
ERROR HY000: Variable 'slave_checkpoint_period' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_checkpoint_period;
ERROR HY000: Variable 'slave_checkpoint_period' is a GLOBAL variable
SELECT @@slave_checkpoint_period;
@@slave_checkpoint_period
300
Warnings:
Warning	1287	'@@slave_checkpoint_period' is deprecated and will be removed in a future release. Please use replica_checkpoint_period instead.
include/assert.inc [Variable slave_checkpoint_period should exist in performance_schema.session_variables]
sysvar:
- name=replica_compressed_protocol
- alias=slave_compressed_protocol
- invalid=[0.1, 2, -1, "''", "'x'", "'TRUE'", "'1'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=
- max=
- block_size=1
- default=0
- values=["ON", "OFF", 1, 0, "TRUE", "FALSE", "'ON'", "'OFF'"]
==== Testing SET @@global.replica_compressed_protocol [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
# Valid values for variable
SET @@global.replica_compressed_protocol=ON;
include/assert.inc [@@global.replica_compressed_protocol should be 1]
include/assert.inc [replica_compressed_protocol should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_compressed_protocol should be 1]
include/assert.inc [slave_compressed_protocol should be 1 in P_S.global_variables]
SET @@global.replica_compressed_protocol=OFF;
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
SET @@global.replica_compressed_protocol=1;
include/assert.inc [@@global.replica_compressed_protocol should be 1]
include/assert.inc [replica_compressed_protocol should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_compressed_protocol should be 1]
include/assert.inc [slave_compressed_protocol should be 1 in P_S.global_variables]
SET @@global.replica_compressed_protocol=0;
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
SET @@global.replica_compressed_protocol=TRUE;
include/assert.inc [@@global.replica_compressed_protocol should be 1]
include/assert.inc [replica_compressed_protocol should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_compressed_protocol should be 1]
include/assert.inc [slave_compressed_protocol should be 1 in P_S.global_variables]
SET @@global.replica_compressed_protocol=FALSE;
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
SET @@global.replica_compressed_protocol='ON';
include/assert.inc [@@global.replica_compressed_protocol should be 1]
include/assert.inc [replica_compressed_protocol should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_compressed_protocol should be 1]
include/assert.inc [slave_compressed_protocol should be 1 in P_S.global_variables]
SET @@global.replica_compressed_protocol='OFF';
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_compressed_protocol = DEFAULT;
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
# Invalid values
SET @@global.replica_compressed_protocol = 0.1;
Got one of the listed errors
SET @@global.replica_compressed_protocol = 2;
Got one of the listed errors
SET @@global.replica_compressed_protocol = -1;
Got one of the listed errors
SET @@global.replica_compressed_protocol = '';
Got one of the listed errors
SET @@global.replica_compressed_protocol = 'x';
Got one of the listed errors
SET @@global.replica_compressed_protocol = 'TRUE';
Got one of the listed errors
SET @@global.replica_compressed_protocol = '1';
Got one of the listed errors
SET @@global.replica_compressed_protocol = NULL;
Got one of the listed errors
# Restore default
SET @@global.replica_compressed_protocol = DEFAULT;
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
==== Testing SET @@session.replica_compressed_protocol [invalid scope] ====
SET @@session.replica_compressed_protocol = DEFAULT;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_compressed_protocol = DEFAULT;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_compressed_protocol = ON;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_compressed_protocol = ON;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_compressed_protocol = OFF;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_compressed_protocol = OFF;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_compressed_protocol = 1;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_compressed_protocol = 1;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_compressed_protocol = 0;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_compressed_protocol = 0;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_compressed_protocol = TRUE;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_compressed_protocol = TRUE;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_compressed_protocol = FALSE;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_compressed_protocol = FALSE;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_compressed_protocol = 'ON';
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_compressed_protocol = 'ON';
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_compressed_protocol = 'OFF';
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_compressed_protocol = 'OFF';
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_compressed_protocol;
ERROR HY000: Variable 'replica_compressed_protocol' is a GLOBAL variable
SELECT @@replica_compressed_protocol;
@@replica_compressed_protocol
0
include/assert.inc [Variable replica_compressed_protocol should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_compressed_protocol [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
# Valid values for variable
SET @@global.slave_compressed_protocol=ON;
Warnings:
Warning	1287	'@@slave_compressed_protocol' is deprecated and will be removed in a future release. Please use replica_compressed_protocol instead.
include/assert.inc [@@global.slave_compressed_protocol should be 1]
include/assert.inc [slave_compressed_protocol should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_compressed_protocol should be 1]
include/assert.inc [replica_compressed_protocol should be 1 in P_S.global_variables]
SET @@global.slave_compressed_protocol=OFF;
Warnings:
Warning	1287	'@@slave_compressed_protocol' is deprecated and will be removed in a future release. Please use replica_compressed_protocol instead.
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
SET @@global.slave_compressed_protocol=1;
Warnings:
Warning	1287	'@@slave_compressed_protocol' is deprecated and will be removed in a future release. Please use replica_compressed_protocol instead.
include/assert.inc [@@global.slave_compressed_protocol should be 1]
include/assert.inc [slave_compressed_protocol should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_compressed_protocol should be 1]
include/assert.inc [replica_compressed_protocol should be 1 in P_S.global_variables]
SET @@global.slave_compressed_protocol=0;
Warnings:
Warning	1287	'@@slave_compressed_protocol' is deprecated and will be removed in a future release. Please use replica_compressed_protocol instead.
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
SET @@global.slave_compressed_protocol=TRUE;
Warnings:
Warning	1287	'@@slave_compressed_protocol' is deprecated and will be removed in a future release. Please use replica_compressed_protocol instead.
include/assert.inc [@@global.slave_compressed_protocol should be 1]
include/assert.inc [slave_compressed_protocol should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_compressed_protocol should be 1]
include/assert.inc [replica_compressed_protocol should be 1 in P_S.global_variables]
SET @@global.slave_compressed_protocol=FALSE;
Warnings:
Warning	1287	'@@slave_compressed_protocol' is deprecated and will be removed in a future release. Please use replica_compressed_protocol instead.
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
SET @@global.slave_compressed_protocol='ON';
Warnings:
Warning	1287	'@@slave_compressed_protocol' is deprecated and will be removed in a future release. Please use replica_compressed_protocol instead.
include/assert.inc [@@global.slave_compressed_protocol should be 1]
include/assert.inc [slave_compressed_protocol should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_compressed_protocol should be 1]
include/assert.inc [replica_compressed_protocol should be 1 in P_S.global_variables]
SET @@global.slave_compressed_protocol='OFF';
Warnings:
Warning	1287	'@@slave_compressed_protocol' is deprecated and will be removed in a future release. Please use replica_compressed_protocol instead.
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_compressed_protocol = DEFAULT;
Warnings:
Warning	1287	'@@slave_compressed_protocol' is deprecated and will be removed in a future release. Please use replica_compressed_protocol instead.
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
# Invalid values
SET @@global.slave_compressed_protocol = 0.1;
Got one of the listed errors
SET @@global.slave_compressed_protocol = 2;
Got one of the listed errors
SET @@global.slave_compressed_protocol = -1;
Got one of the listed errors
SET @@global.slave_compressed_protocol = '';
Got one of the listed errors
SET @@global.slave_compressed_protocol = 'x';
Got one of the listed errors
SET @@global.slave_compressed_protocol = 'TRUE';
Got one of the listed errors
SET @@global.slave_compressed_protocol = '1';
Got one of the listed errors
SET @@global.slave_compressed_protocol = NULL;
Got one of the listed errors
# Restore default
SET @@global.slave_compressed_protocol = DEFAULT;
Warnings:
Warning	1287	'@@slave_compressed_protocol' is deprecated and will be removed in a future release. Please use replica_compressed_protocol instead.
include/assert.inc [@@global.slave_compressed_protocol should be 0]
include/assert.inc [slave_compressed_protocol should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_compressed_protocol should be 0]
include/assert.inc [replica_compressed_protocol should be 0 in P_S.global_variables]
==== Testing SET @@session.slave_compressed_protocol [invalid scope] ====
SET @@session.slave_compressed_protocol = DEFAULT;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_compressed_protocol = DEFAULT;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_compressed_protocol = ON;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_compressed_protocol = ON;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_compressed_protocol = OFF;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_compressed_protocol = OFF;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_compressed_protocol = 1;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_compressed_protocol = 1;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_compressed_protocol = 0;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_compressed_protocol = 0;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_compressed_protocol = TRUE;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_compressed_protocol = TRUE;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_compressed_protocol = FALSE;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_compressed_protocol = FALSE;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_compressed_protocol = 'ON';
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_compressed_protocol = 'ON';
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_compressed_protocol = 'OFF';
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_compressed_protocol = 'OFF';
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_compressed_protocol;
ERROR HY000: Variable 'slave_compressed_protocol' is a GLOBAL variable
SELECT @@slave_compressed_protocol;
@@slave_compressed_protocol
0
Warnings:
Warning	1287	'@@slave_compressed_protocol' is deprecated and will be removed in a future release. Please use replica_compressed_protocol instead.
include/assert.inc [Variable slave_compressed_protocol should exist in performance_schema.session_variables]
sysvar:
- name=replica_exec_mode
- alias=slave_exec_mode
- invalid=[1.5, "ON", "'x'", "NULL", "'FOO_BAR'", -1, 2]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=
- max=
- block_size=1
- default=STRICT
- values=["IDEMPOTENT", "STRICT", "'IDEMPOTENT'", 0, 1]
==== Testing SET @@global.replica_exec_mode [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_exec_mode should be STRICT]
include/assert.inc [replica_exec_mode should be STRICT in P_S.global_variables]
include/assert.inc [@@global.slave_exec_mode should be STRICT]
include/assert.inc [slave_exec_mode should be STRICT in P_S.global_variables]
# Valid values for variable
SET @@global.replica_exec_mode=IDEMPOTENT;
include/assert.inc [@@global.replica_exec_mode should be IDEMPOTENT]
include/assert.inc [replica_exec_mode should be IDEMPOTENT in P_S.global_variables]
include/assert.inc [@@global.slave_exec_mode should be IDEMPOTENT]
include/assert.inc [slave_exec_mode should be IDEMPOTENT in P_S.global_variables]
SET @@global.replica_exec_mode=STRICT;
include/assert.inc [@@global.replica_exec_mode should be STRICT]
include/assert.inc [replica_exec_mode should be STRICT in P_S.global_variables]
include/assert.inc [@@global.slave_exec_mode should be STRICT]
include/assert.inc [slave_exec_mode should be STRICT in P_S.global_variables]
SET @@global.replica_exec_mode='IDEMPOTENT';
include/assert.inc [@@global.replica_exec_mode should be IDEMPOTENT]
include/assert.inc [replica_exec_mode should be IDEMPOTENT in P_S.global_variables]
include/assert.inc [@@global.slave_exec_mode should be IDEMPOTENT]
include/assert.inc [slave_exec_mode should be IDEMPOTENT in P_S.global_variables]
SET @@global.replica_exec_mode=0;
include/assert.inc [@@global.replica_exec_mode should be STRICT]
include/assert.inc [replica_exec_mode should be STRICT in P_S.global_variables]
include/assert.inc [@@global.slave_exec_mode should be STRICT]
include/assert.inc [slave_exec_mode should be STRICT in P_S.global_variables]
SET @@global.replica_exec_mode=1;
include/assert.inc [@@global.replica_exec_mode should be IDEMPOTENT]
include/assert.inc [replica_exec_mode should be IDEMPOTENT in P_S.global_variables]
include/assert.inc [@@global.slave_exec_mode should be IDEMPOTENT]
include/assert.inc [slave_exec_mode should be IDEMPOTENT in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_exec_mode = DEFAULT;
include/assert.inc [@@global.replica_exec_mode should be STRICT]
include/assert.inc [replica_exec_mode should be STRICT in P_S.global_variables]
include/assert.inc [@@global.slave_exec_mode should be STRICT]
include/assert.inc [slave_exec_mode should be STRICT in P_S.global_variables]
# Invalid values
SET @@global.replica_exec_mode = 1.5;
Got one of the listed errors
SET @@global.replica_exec_mode = ON;
Got one of the listed errors
SET @@global.replica_exec_mode = 'x';
Got one of the listed errors
SET @@global.replica_exec_mode = NULL;
Got one of the listed errors
SET @@global.replica_exec_mode = 'FOO_BAR';
Got one of the listed errors
SET @@global.replica_exec_mode = -1;
Got one of the listed errors
SET @@global.replica_exec_mode = 2;
Got one of the listed errors
# Restore default
SET @@global.replica_exec_mode = DEFAULT;
include/assert.inc [@@global.replica_exec_mode should be STRICT]
include/assert.inc [replica_exec_mode should be STRICT in P_S.global_variables]
include/assert.inc [@@global.slave_exec_mode should be STRICT]
include/assert.inc [slave_exec_mode should be STRICT in P_S.global_variables]
==== Testing SET @@session.replica_exec_mode [invalid scope] ====
SET @@session.replica_exec_mode = DEFAULT;
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_exec_mode = DEFAULT;
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_exec_mode = IDEMPOTENT;
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_exec_mode = IDEMPOTENT;
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_exec_mode = STRICT;
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_exec_mode = STRICT;
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_exec_mode = 'IDEMPOTENT';
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_exec_mode = 'IDEMPOTENT';
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_exec_mode = 0;
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_exec_mode = 0;
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_exec_mode = 1;
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_exec_mode = 1;
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_exec_mode;
ERROR HY000: Variable 'replica_exec_mode' is a GLOBAL variable
SELECT @@replica_exec_mode;
@@replica_exec_mode
STRICT
include/assert.inc [Variable replica_exec_mode should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_exec_mode [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_exec_mode should be STRICT]
include/assert.inc [slave_exec_mode should be STRICT in P_S.global_variables]
include/assert.inc [@@global.replica_exec_mode should be STRICT]
include/assert.inc [replica_exec_mode should be STRICT in P_S.global_variables]
# Valid values for variable
SET @@global.slave_exec_mode=IDEMPOTENT;
Warnings:
Warning	1287	'@@slave_exec_mode' is deprecated and will be removed in a future release. Please use replica_exec_mode instead.
include/assert.inc [@@global.slave_exec_mode should be IDEMPOTENT]
include/assert.inc [slave_exec_mode should be IDEMPOTENT in P_S.global_variables]
include/assert.inc [@@global.replica_exec_mode should be IDEMPOTENT]
include/assert.inc [replica_exec_mode should be IDEMPOTENT in P_S.global_variables]
SET @@global.slave_exec_mode=STRICT;
Warnings:
Warning	1287	'@@slave_exec_mode' is deprecated and will be removed in a future release. Please use replica_exec_mode instead.
include/assert.inc [@@global.slave_exec_mode should be STRICT]
include/assert.inc [slave_exec_mode should be STRICT in P_S.global_variables]
include/assert.inc [@@global.replica_exec_mode should be STRICT]
include/assert.inc [replica_exec_mode should be STRICT in P_S.global_variables]
SET @@global.slave_exec_mode='IDEMPOTENT';
Warnings:
Warning	1287	'@@slave_exec_mode' is deprecated and will be removed in a future release. Please use replica_exec_mode instead.
include/assert.inc [@@global.slave_exec_mode should be IDEMPOTENT]
include/assert.inc [slave_exec_mode should be IDEMPOTENT in P_S.global_variables]
include/assert.inc [@@global.replica_exec_mode should be IDEMPOTENT]
include/assert.inc [replica_exec_mode should be IDEMPOTENT in P_S.global_variables]
SET @@global.slave_exec_mode=0;
Warnings:
Warning	1287	'@@slave_exec_mode' is deprecated and will be removed in a future release. Please use replica_exec_mode instead.
include/assert.inc [@@global.slave_exec_mode should be STRICT]
include/assert.inc [slave_exec_mode should be STRICT in P_S.global_variables]
include/assert.inc [@@global.replica_exec_mode should be STRICT]
include/assert.inc [replica_exec_mode should be STRICT in P_S.global_variables]
SET @@global.slave_exec_mode=1;
Warnings:
Warning	1287	'@@slave_exec_mode' is deprecated and will be removed in a future release. Please use replica_exec_mode instead.
include/assert.inc [@@global.slave_exec_mode should be IDEMPOTENT]
include/assert.inc [slave_exec_mode should be IDEMPOTENT in P_S.global_variables]
include/assert.inc [@@global.replica_exec_mode should be IDEMPOTENT]
include/assert.inc [replica_exec_mode should be IDEMPOTENT in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_exec_mode = DEFAULT;
Warnings:
Warning	1287	'@@slave_exec_mode' is deprecated and will be removed in a future release. Please use replica_exec_mode instead.
include/assert.inc [@@global.slave_exec_mode should be STRICT]
include/assert.inc [slave_exec_mode should be STRICT in P_S.global_variables]
include/assert.inc [@@global.replica_exec_mode should be STRICT]
include/assert.inc [replica_exec_mode should be STRICT in P_S.global_variables]
# Invalid values
SET @@global.slave_exec_mode = 1.5;
Got one of the listed errors
SET @@global.slave_exec_mode = ON;
Got one of the listed errors
SET @@global.slave_exec_mode = 'x';
Got one of the listed errors
SET @@global.slave_exec_mode = NULL;
Got one of the listed errors
SET @@global.slave_exec_mode = 'FOO_BAR';
Got one of the listed errors
SET @@global.slave_exec_mode = -1;
Got one of the listed errors
SET @@global.slave_exec_mode = 2;
Got one of the listed errors
# Restore default
SET @@global.slave_exec_mode = DEFAULT;
Warnings:
Warning	1287	'@@slave_exec_mode' is deprecated and will be removed in a future release. Please use replica_exec_mode instead.
include/assert.inc [@@global.slave_exec_mode should be STRICT]
include/assert.inc [slave_exec_mode should be STRICT in P_S.global_variables]
include/assert.inc [@@global.replica_exec_mode should be STRICT]
include/assert.inc [replica_exec_mode should be STRICT in P_S.global_variables]
==== Testing SET @@session.slave_exec_mode [invalid scope] ====
SET @@session.slave_exec_mode = DEFAULT;
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_exec_mode = DEFAULT;
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_exec_mode = IDEMPOTENT;
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_exec_mode = IDEMPOTENT;
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_exec_mode = STRICT;
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_exec_mode = STRICT;
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_exec_mode = 'IDEMPOTENT';
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_exec_mode = 'IDEMPOTENT';
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_exec_mode = 0;
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_exec_mode = 0;
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_exec_mode = 1;
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_exec_mode = 1;
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_exec_mode;
ERROR HY000: Variable 'slave_exec_mode' is a GLOBAL variable
SELECT @@slave_exec_mode;
@@slave_exec_mode
STRICT
Warnings:
Warning	1287	'@@slave_exec_mode' is deprecated and will be removed in a future release. Please use replica_exec_mode instead.
include/assert.inc [Variable slave_exec_mode should exist in performance_schema.session_variables]
sysvar:
- name=replica_load_tmpdir
- alias=slave_load_tmpdir
- invalid=["NULL", "ON"]
- global=1
- session=0
- cmdline=1
- persist=0
- persist_as_readonly=0
- dynamic=0
- min=
- max=
- block_size=1
- default=MASKED
- values=MASKED
==== Testing SET @@global.replica_load_tmpdir [read-only] ====
# Initial value is default
include/assert.inc [@@global.replica_load_tmpdir should have the expected value]
include/assert.inc [replica_load_tmpdir should have the expected value in P_S.global_variables]
include/assert.inc [@@global.slave_load_tmpdir should have the expected value]
include/assert.inc [slave_load_tmpdir should have the expected value in P_S.global_variables]
# Valid values for *read-only* variable cannot be set
SET @@global.replica_load_tmpdir = VALUE;
ERROR HY000: Variable 'replica_load_tmpdir' is a read only variable
include/assert.inc [@@global.replica_load_tmpdir should have the expected value]
include/assert.inc [replica_load_tmpdir should have the expected value in P_S.global_variables]
include/assert.inc [@@global.slave_load_tmpdir should have the expected value]
include/assert.inc [slave_load_tmpdir should have the expected value in P_S.global_variables]
SET @@global.replica_load_tmpdir = VALUE;
ERROR HY000: Variable 'replica_load_tmpdir' is a read only variable
include/assert.inc [@@global.replica_load_tmpdir should have the expected value]
include/assert.inc [replica_load_tmpdir should have the expected value in P_S.global_variables]
include/assert.inc [@@global.slave_load_tmpdir should have the expected value]
include/assert.inc [slave_load_tmpdir should have the expected value in P_S.global_variables]
SET @@global.replica_load_tmpdir = VALUE;
ERROR HY000: Variable 'replica_load_tmpdir' is a read only variable
include/assert.inc [@@global.replica_load_tmpdir should have the expected value]
include/assert.inc [replica_load_tmpdir should have the expected value in P_S.global_variables]
include/assert.inc [@@global.slave_load_tmpdir should have the expected value]
include/assert.inc [slave_load_tmpdir should have the expected value in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_load_tmpdir = DEFAULT;
ERROR HY000: Variable 'replica_load_tmpdir' is a read only variable
include/assert.inc [@@global.replica_load_tmpdir should have the expected value]
include/assert.inc [replica_load_tmpdir should have the expected value in P_S.global_variables]
include/assert.inc [@@global.slave_load_tmpdir should have the expected value]
include/assert.inc [slave_load_tmpdir should have the expected value in P_S.global_variables]
==== Testing SET @@session.replica_load_tmpdir [invalid scope] ====
SET @@session.replica_load_tmpdir = DEFAULT;
ERROR HY000: Variable 'replica_load_tmpdir' is a read only variable
SET @@replica_load_tmpdir = DEFAULT;
ERROR HY000: Variable 'replica_load_tmpdir' is a read only variable
SET @@session.replica_load_tmpdir = VALUE;
ERROR HY000: Variable 'replica_load_tmpdir' is a read only variable
SET @@replica_load_tmpdir = VALUE;
ERROR HY000: Variable 'replica_load_tmpdir' is a read only variable
SET @@session.replica_load_tmpdir = VALUE;
ERROR HY000: Variable 'replica_load_tmpdir' is a read only variable
SET @@replica_load_tmpdir = VALUE;
ERROR HY000: Variable 'replica_load_tmpdir' is a read only variable
SET @@session.replica_load_tmpdir = VALUE;
ERROR HY000: Variable 'replica_load_tmpdir' is a read only variable
SET @@replica_load_tmpdir = VALUE;
ERROR HY000: Variable 'replica_load_tmpdir' is a read only variable
SELECT @@session.replica_load_tmpdir;
ERROR HY000: Variable 'replica_load_tmpdir' is a GLOBAL variable
SELECT @@replica_load_tmpdir;
include/assert.inc [Variable replica_load_tmpdir should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_load_tmpdir [read-only] ====
# Initial value is default
include/assert.inc [@@global.slave_load_tmpdir should have the expected value]
include/assert.inc [slave_load_tmpdir should have the expected value in P_S.global_variables]
include/assert.inc [@@global.replica_load_tmpdir should have the expected value]
include/assert.inc [replica_load_tmpdir should have the expected value in P_S.global_variables]
# Valid values for *read-only* variable cannot be set
SET @@global.slave_load_tmpdir = VALUE;
ERROR HY000: Variable 'slave_load_tmpdir' is a read only variable
include/assert.inc [@@global.slave_load_tmpdir should have the expected value]
include/assert.inc [slave_load_tmpdir should have the expected value in P_S.global_variables]
include/assert.inc [@@global.replica_load_tmpdir should have the expected value]
include/assert.inc [replica_load_tmpdir should have the expected value in P_S.global_variables]
SET @@global.slave_load_tmpdir = VALUE;
ERROR HY000: Variable 'slave_load_tmpdir' is a read only variable
include/assert.inc [@@global.slave_load_tmpdir should have the expected value]
include/assert.inc [slave_load_tmpdir should have the expected value in P_S.global_variables]
include/assert.inc [@@global.replica_load_tmpdir should have the expected value]
include/assert.inc [replica_load_tmpdir should have the expected value in P_S.global_variables]
SET @@global.slave_load_tmpdir = VALUE;
ERROR HY000: Variable 'slave_load_tmpdir' is a read only variable
include/assert.inc [@@global.slave_load_tmpdir should have the expected value]
include/assert.inc [slave_load_tmpdir should have the expected value in P_S.global_variables]
include/assert.inc [@@global.replica_load_tmpdir should have the expected value]
include/assert.inc [replica_load_tmpdir should have the expected value in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_load_tmpdir = DEFAULT;
ERROR HY000: Variable 'slave_load_tmpdir' is a read only variable
include/assert.inc [@@global.slave_load_tmpdir should have the expected value]
include/assert.inc [slave_load_tmpdir should have the expected value in P_S.global_variables]
include/assert.inc [@@global.replica_load_tmpdir should have the expected value]
include/assert.inc [replica_load_tmpdir should have the expected value in P_S.global_variables]
==== Testing SET @@session.slave_load_tmpdir [invalid scope] ====
SET @@session.slave_load_tmpdir = DEFAULT;
ERROR HY000: Variable 'slave_load_tmpdir' is a read only variable
SET @@slave_load_tmpdir = DEFAULT;
ERROR HY000: Variable 'slave_load_tmpdir' is a read only variable
SET @@session.slave_load_tmpdir = VALUE;
ERROR HY000: Variable 'slave_load_tmpdir' is a read only variable
SET @@slave_load_tmpdir = VALUE;
ERROR HY000: Variable 'slave_load_tmpdir' is a read only variable
SET @@session.slave_load_tmpdir = VALUE;
ERROR HY000: Variable 'slave_load_tmpdir' is a read only variable
SET @@slave_load_tmpdir = VALUE;
ERROR HY000: Variable 'slave_load_tmpdir' is a read only variable
SET @@session.slave_load_tmpdir = VALUE;
ERROR HY000: Variable 'slave_load_tmpdir' is a read only variable
SET @@slave_load_tmpdir = VALUE;
ERROR HY000: Variable 'slave_load_tmpdir' is a read only variable
SELECT @@session.slave_load_tmpdir;
ERROR HY000: Variable 'slave_load_tmpdir' is a GLOBAL variable
SELECT @@slave_load_tmpdir;
include/assert.inc [Variable slave_load_tmpdir should exist in performance_schema.session_variables]
sysvar:
- name=replica_max_allowed_packet
- alias=slave_max_allowed_packet
- invalid=[1.5, "ON", "'x'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=1024
- max=1073741824
- block_size=1024
- default=1073741824
- values=[1024, 2048, 65536]
==== Testing SET @@global.replica_max_allowed_packet [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
# Valid values for variable
SET @@global.replica_max_allowed_packet=1024;
include/assert.inc [@@global.replica_max_allowed_packet should be 1024]
include/assert.inc [replica_max_allowed_packet should be 1024 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 1024]
include/assert.inc [slave_max_allowed_packet should be 1024 in P_S.global_variables]
SET @@global.replica_max_allowed_packet=2048;
include/assert.inc [@@global.replica_max_allowed_packet should be 2048]
include/assert.inc [replica_max_allowed_packet should be 2048 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 2048]
include/assert.inc [slave_max_allowed_packet should be 2048 in P_S.global_variables]
SET @@global.replica_max_allowed_packet=65536;
include/assert.inc [@@global.replica_max_allowed_packet should be 65536]
include/assert.inc [replica_max_allowed_packet should be 65536 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 65536]
include/assert.inc [slave_max_allowed_packet should be 65536 in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_max_allowed_packet = DEFAULT;
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
# Minimum value
SET @@global.replica_max_allowed_packet = 1024;
include/assert.inc [@@global.replica_max_allowed_packet should be 1024]
include/assert.inc [replica_max_allowed_packet should be 1024 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 1024]
include/assert.inc [slave_max_allowed_packet should be 1024 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.replica_max_allowed_packet = 1023;
Warnings:
Warning	1292	Truncated incorrect replica_max_allowed_packet value: '1023'
include/assert.inc [@@global.replica_max_allowed_packet should be 1024]
include/assert.inc [replica_max_allowed_packet should be 1024 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 1024]
include/assert.inc [slave_max_allowed_packet should be 1024 in P_S.global_variables]
SET @@global.replica_max_allowed_packet = 0;
Warnings:
Warning	1292	Truncated incorrect replica_max_allowed_packet value: '0'
include/assert.inc [@@global.replica_max_allowed_packet should be 1024]
include/assert.inc [replica_max_allowed_packet should be 1024 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 1024]
include/assert.inc [slave_max_allowed_packet should be 1024 in P_S.global_variables]
# Maximum value
SET @@global.replica_max_allowed_packet = 1073741824;
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.replica_max_allowed_packet = 1073741825;
Warnings:
Warning	1292	Truncated incorrect replica_max_allowed_packet value: '1073741825'
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
SET @@global.replica_max_allowed_packet = 1073742848;
Warnings:
Warning	1292	Truncated incorrect replica_max_allowed_packet value: '1073742848'
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
# Non-block-size value
SET @@global.replica_max_allowed_packet = 1073742847;
Warnings:
Warning	1292	Truncated incorrect replica_max_allowed_packet value: '1073742847'
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
# Invalid values
SET @@global.replica_max_allowed_packet = 1.5;
Got one of the listed errors
SET @@global.replica_max_allowed_packet = ON;
Got one of the listed errors
SET @@global.replica_max_allowed_packet = 'x';
Got one of the listed errors
SET @@global.replica_max_allowed_packet = NULL;
Got one of the listed errors
# Restore default
SET @@global.replica_max_allowed_packet = DEFAULT;
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
==== Testing SET @@session.replica_max_allowed_packet [invalid scope] ====
SET @@session.replica_max_allowed_packet = DEFAULT;
ERROR HY000: Variable 'replica_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_max_allowed_packet = DEFAULT;
ERROR HY000: Variable 'replica_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_max_allowed_packet = 1024;
ERROR HY000: Variable 'replica_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_max_allowed_packet = 1024;
ERROR HY000: Variable 'replica_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_max_allowed_packet = 2048;
ERROR HY000: Variable 'replica_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_max_allowed_packet = 2048;
ERROR HY000: Variable 'replica_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_max_allowed_packet = 65536;
ERROR HY000: Variable 'replica_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_max_allowed_packet = 65536;
ERROR HY000: Variable 'replica_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_max_allowed_packet;
ERROR HY000: Variable 'replica_max_allowed_packet' is a GLOBAL variable
SELECT @@replica_max_allowed_packet;
@@replica_max_allowed_packet
1073741824
include/assert.inc [Variable replica_max_allowed_packet should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_max_allowed_packet [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
# Valid values for variable
SET @@global.slave_max_allowed_packet=1024;
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
include/assert.inc [@@global.slave_max_allowed_packet should be 1024]
include/assert.inc [slave_max_allowed_packet should be 1024 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 1024]
include/assert.inc [replica_max_allowed_packet should be 1024 in P_S.global_variables]
SET @@global.slave_max_allowed_packet=2048;
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
include/assert.inc [@@global.slave_max_allowed_packet should be 2048]
include/assert.inc [slave_max_allowed_packet should be 2048 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 2048]
include/assert.inc [replica_max_allowed_packet should be 2048 in P_S.global_variables]
SET @@global.slave_max_allowed_packet=65536;
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
include/assert.inc [@@global.slave_max_allowed_packet should be 65536]
include/assert.inc [slave_max_allowed_packet should be 65536 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 65536]
include/assert.inc [replica_max_allowed_packet should be 65536 in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_max_allowed_packet = DEFAULT;
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
# Minimum value
SET @@global.slave_max_allowed_packet = 1024;
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
include/assert.inc [@@global.slave_max_allowed_packet should be 1024]
include/assert.inc [slave_max_allowed_packet should be 1024 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 1024]
include/assert.inc [replica_max_allowed_packet should be 1024 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.slave_max_allowed_packet = 1023;
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
Warning	1292	Truncated incorrect replica_max_allowed_packet value: '1023'
include/assert.inc [@@global.slave_max_allowed_packet should be 1024]
include/assert.inc [slave_max_allowed_packet should be 1024 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 1024]
include/assert.inc [replica_max_allowed_packet should be 1024 in P_S.global_variables]
SET @@global.slave_max_allowed_packet = 0;
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
Warning	1292	Truncated incorrect replica_max_allowed_packet value: '0'
include/assert.inc [@@global.slave_max_allowed_packet should be 1024]
include/assert.inc [slave_max_allowed_packet should be 1024 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 1024]
include/assert.inc [replica_max_allowed_packet should be 1024 in P_S.global_variables]
# Maximum value
SET @@global.slave_max_allowed_packet = 1073741824;
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.slave_max_allowed_packet = 1073741825;
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
Warning	1292	Truncated incorrect replica_max_allowed_packet value: '1073741825'
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
SET @@global.slave_max_allowed_packet = 1073742848;
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
Warning	1292	Truncated incorrect replica_max_allowed_packet value: '1073742848'
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
# Non-block-size value
SET @@global.slave_max_allowed_packet = 1073742847;
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
Warning	1292	Truncated incorrect replica_max_allowed_packet value: '1073742847'
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
# Invalid values
SET @@global.slave_max_allowed_packet = 1.5;
Got one of the listed errors
SET @@global.slave_max_allowed_packet = ON;
Got one of the listed errors
SET @@global.slave_max_allowed_packet = 'x';
Got one of the listed errors
SET @@global.slave_max_allowed_packet = NULL;
Got one of the listed errors
# Restore default
SET @@global.slave_max_allowed_packet = DEFAULT;
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
include/assert.inc [@@global.slave_max_allowed_packet should be 1073741824]
include/assert.inc [slave_max_allowed_packet should be 1073741824 in P_S.global_variables]
include/assert.inc [@@global.replica_max_allowed_packet should be 1073741824]
include/assert.inc [replica_max_allowed_packet should be 1073741824 in P_S.global_variables]
==== Testing SET @@session.slave_max_allowed_packet [invalid scope] ====
SET @@session.slave_max_allowed_packet = DEFAULT;
ERROR HY000: Variable 'slave_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_max_allowed_packet = DEFAULT;
ERROR HY000: Variable 'slave_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_max_allowed_packet = 1024;
ERROR HY000: Variable 'slave_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_max_allowed_packet = 1024;
ERROR HY000: Variable 'slave_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_max_allowed_packet = 2048;
ERROR HY000: Variable 'slave_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_max_allowed_packet = 2048;
ERROR HY000: Variable 'slave_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_max_allowed_packet = 65536;
ERROR HY000: Variable 'slave_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_max_allowed_packet = 65536;
ERROR HY000: Variable 'slave_max_allowed_packet' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_max_allowed_packet;
ERROR HY000: Variable 'slave_max_allowed_packet' is a GLOBAL variable
SELECT @@slave_max_allowed_packet;
@@slave_max_allowed_packet
1073741824
Warnings:
Warning	1287	'@@slave_max_allowed_packet' is deprecated and will be removed in a future release. Please use replica_max_allowed_packet instead.
include/assert.inc [Variable slave_max_allowed_packet should exist in performance_schema.session_variables]
sysvar:
- name=replica_net_timeout
- alias=slave_net_timeout
- invalid=[1.5, "ON", "'x'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=1
- max=31536000
- block_size=1
- default=60
- values=[1, 2, 65536]
==== Testing SET @@global.replica_net_timeout [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_net_timeout should be 60]
include/assert.inc [replica_net_timeout should be 60 in P_S.global_variables]
include/assert.inc [@@global.slave_net_timeout should be 60]
include/assert.inc [slave_net_timeout should be 60 in P_S.global_variables]
# Valid values for variable
SET @@global.replica_net_timeout=1;
include/assert.inc [@@global.replica_net_timeout should be 1]
include/assert.inc [replica_net_timeout should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_net_timeout should be 1]
include/assert.inc [slave_net_timeout should be 1 in P_S.global_variables]
SET @@global.replica_net_timeout=2;
include/assert.inc [@@global.replica_net_timeout should be 2]
include/assert.inc [replica_net_timeout should be 2 in P_S.global_variables]
include/assert.inc [@@global.slave_net_timeout should be 2]
include/assert.inc [slave_net_timeout should be 2 in P_S.global_variables]
SET @@global.replica_net_timeout=65536;
include/assert.inc [@@global.replica_net_timeout should be 65536]
include/assert.inc [replica_net_timeout should be 65536 in P_S.global_variables]
include/assert.inc [@@global.slave_net_timeout should be 65536]
include/assert.inc [slave_net_timeout should be 65536 in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_net_timeout = DEFAULT;
include/assert.inc [@@global.replica_net_timeout should be 60]
include/assert.inc [replica_net_timeout should be 60 in P_S.global_variables]
include/assert.inc [@@global.slave_net_timeout should be 60]
include/assert.inc [slave_net_timeout should be 60 in P_S.global_variables]
# Minimum value
SET @@global.replica_net_timeout = 1;
include/assert.inc [@@global.replica_net_timeout should be 1]
include/assert.inc [replica_net_timeout should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_net_timeout should be 1]
include/assert.inc [slave_net_timeout should be 1 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.replica_net_timeout = 0;
Warnings:
Warning	1292	Truncated incorrect replica_net_timeout value: '0'
include/assert.inc [@@global.replica_net_timeout should be 1]
include/assert.inc [replica_net_timeout should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_net_timeout should be 1]
include/assert.inc [slave_net_timeout should be 1 in P_S.global_variables]
# Maximum value
SET @@global.replica_net_timeout = 31536000;
include/assert.inc [@@global.replica_net_timeout should be 31536000]
include/assert.inc [replica_net_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.slave_net_timeout should be 31536000]
include/assert.inc [slave_net_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.replica_net_timeout = 31536001;
Warnings:
Warning	1292	Truncated incorrect replica_net_timeout value: '31536001'
include/assert.inc [@@global.replica_net_timeout should be 31536000]
include/assert.inc [replica_net_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.slave_net_timeout should be 31536000]
include/assert.inc [slave_net_timeout should be 31536000 in P_S.global_variables]
# Invalid values
SET @@global.replica_net_timeout = 1.5;
Got one of the listed errors
SET @@global.replica_net_timeout = ON;
Got one of the listed errors
SET @@global.replica_net_timeout = 'x';
Got one of the listed errors
SET @@global.replica_net_timeout = NULL;
Got one of the listed errors
# Restore default
SET @@global.replica_net_timeout = DEFAULT;
include/assert.inc [@@global.replica_net_timeout should be 60]
include/assert.inc [replica_net_timeout should be 60 in P_S.global_variables]
include/assert.inc [@@global.slave_net_timeout should be 60]
include/assert.inc [slave_net_timeout should be 60 in P_S.global_variables]
==== Testing SET @@session.replica_net_timeout [invalid scope] ====
SET @@session.replica_net_timeout = DEFAULT;
ERROR HY000: Variable 'replica_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_net_timeout = DEFAULT;
ERROR HY000: Variable 'replica_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_net_timeout = 1;
ERROR HY000: Variable 'replica_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_net_timeout = 1;
ERROR HY000: Variable 'replica_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_net_timeout = 2;
ERROR HY000: Variable 'replica_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_net_timeout = 2;
ERROR HY000: Variable 'replica_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_net_timeout = 65536;
ERROR HY000: Variable 'replica_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_net_timeout = 65536;
ERROR HY000: Variable 'replica_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_net_timeout;
ERROR HY000: Variable 'replica_net_timeout' is a GLOBAL variable
SELECT @@replica_net_timeout;
@@replica_net_timeout
60
include/assert.inc [Variable replica_net_timeout should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_net_timeout [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_net_timeout should be 60]
include/assert.inc [slave_net_timeout should be 60 in P_S.global_variables]
include/assert.inc [@@global.replica_net_timeout should be 60]
include/assert.inc [replica_net_timeout should be 60 in P_S.global_variables]
# Valid values for variable
SET @@global.slave_net_timeout=1;
Warnings:
Warning	1287	'@@slave_net_timeout' is deprecated and will be removed in a future release. Please use replica_net_timeout instead.
include/assert.inc [@@global.slave_net_timeout should be 1]
include/assert.inc [slave_net_timeout should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_net_timeout should be 1]
include/assert.inc [replica_net_timeout should be 1 in P_S.global_variables]
SET @@global.slave_net_timeout=2;
Warnings:
Warning	1287	'@@slave_net_timeout' is deprecated and will be removed in a future release. Please use replica_net_timeout instead.
include/assert.inc [@@global.slave_net_timeout should be 2]
include/assert.inc [slave_net_timeout should be 2 in P_S.global_variables]
include/assert.inc [@@global.replica_net_timeout should be 2]
include/assert.inc [replica_net_timeout should be 2 in P_S.global_variables]
SET @@global.slave_net_timeout=65536;
Warnings:
Warning	1287	'@@slave_net_timeout' is deprecated and will be removed in a future release. Please use replica_net_timeout instead.
include/assert.inc [@@global.slave_net_timeout should be 65536]
include/assert.inc [slave_net_timeout should be 65536 in P_S.global_variables]
include/assert.inc [@@global.replica_net_timeout should be 65536]
include/assert.inc [replica_net_timeout should be 65536 in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_net_timeout = DEFAULT;
Warnings:
Warning	1287	'@@slave_net_timeout' is deprecated and will be removed in a future release. Please use replica_net_timeout instead.
include/assert.inc [@@global.slave_net_timeout should be 60]
include/assert.inc [slave_net_timeout should be 60 in P_S.global_variables]
include/assert.inc [@@global.replica_net_timeout should be 60]
include/assert.inc [replica_net_timeout should be 60 in P_S.global_variables]
# Minimum value
SET @@global.slave_net_timeout = 1;
Warnings:
Warning	1287	'@@slave_net_timeout' is deprecated and will be removed in a future release. Please use replica_net_timeout instead.
include/assert.inc [@@global.slave_net_timeout should be 1]
include/assert.inc [slave_net_timeout should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_net_timeout should be 1]
include/assert.inc [replica_net_timeout should be 1 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.slave_net_timeout = 0;
Warnings:
Warning	1287	'@@slave_net_timeout' is deprecated and will be removed in a future release. Please use replica_net_timeout instead.
Warning	1292	Truncated incorrect replica_net_timeout value: '0'
include/assert.inc [@@global.slave_net_timeout should be 1]
include/assert.inc [slave_net_timeout should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_net_timeout should be 1]
include/assert.inc [replica_net_timeout should be 1 in P_S.global_variables]
# Maximum value
SET @@global.slave_net_timeout = 31536000;
Warnings:
Warning	1287	'@@slave_net_timeout' is deprecated and will be removed in a future release. Please use replica_net_timeout instead.
include/assert.inc [@@global.slave_net_timeout should be 31536000]
include/assert.inc [slave_net_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.replica_net_timeout should be 31536000]
include/assert.inc [replica_net_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.slave_net_timeout = 31536001;
Warnings:
Warning	1287	'@@slave_net_timeout' is deprecated and will be removed in a future release. Please use replica_net_timeout instead.
Warning	1292	Truncated incorrect replica_net_timeout value: '31536001'
include/assert.inc [@@global.slave_net_timeout should be 31536000]
include/assert.inc [slave_net_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.replica_net_timeout should be 31536000]
include/assert.inc [replica_net_timeout should be 31536000 in P_S.global_variables]
# Invalid values
SET @@global.slave_net_timeout = 1.5;
Got one of the listed errors
SET @@global.slave_net_timeout = ON;
Got one of the listed errors
SET @@global.slave_net_timeout = 'x';
Got one of the listed errors
SET @@global.slave_net_timeout = NULL;
Got one of the listed errors
# Restore default
SET @@global.slave_net_timeout = DEFAULT;
Warnings:
Warning	1287	'@@slave_net_timeout' is deprecated and will be removed in a future release. Please use replica_net_timeout instead.
include/assert.inc [@@global.slave_net_timeout should be 60]
include/assert.inc [slave_net_timeout should be 60 in P_S.global_variables]
include/assert.inc [@@global.replica_net_timeout should be 60]
include/assert.inc [replica_net_timeout should be 60 in P_S.global_variables]
==== Testing SET @@session.slave_net_timeout [invalid scope] ====
SET @@session.slave_net_timeout = DEFAULT;
ERROR HY000: Variable 'slave_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_net_timeout = DEFAULT;
ERROR HY000: Variable 'slave_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_net_timeout = 1;
ERROR HY000: Variable 'slave_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_net_timeout = 1;
ERROR HY000: Variable 'slave_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_net_timeout = 2;
ERROR HY000: Variable 'slave_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_net_timeout = 2;
ERROR HY000: Variable 'slave_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_net_timeout = 65536;
ERROR HY000: Variable 'slave_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_net_timeout = 65536;
ERROR HY000: Variable 'slave_net_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_net_timeout;
ERROR HY000: Variable 'slave_net_timeout' is a GLOBAL variable
SELECT @@slave_net_timeout;
@@slave_net_timeout
60
Warnings:
Warning	1287	'@@slave_net_timeout' is deprecated and will be removed in a future release. Please use replica_net_timeout instead.
include/assert.inc [Variable slave_net_timeout should exist in performance_schema.session_variables]
sysvar:
- name=replica_parallel_workers
- alias=slave_parallel_workers
- invalid=[1.5, "ON", "'x'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=1
- dynamic=1
- min=
- max=1024
- block_size=1
- default=4
- values=[1, 2, 100]
==== Testing SET @@global.replica_parallel_workers [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_parallel_workers should be 4]
include/assert.inc [replica_parallel_workers should be 4 in P_S.global_variables]
include/assert.inc [@@global.slave_parallel_workers should be 4]
include/assert.inc [slave_parallel_workers should be 4 in P_S.global_variables]
# Valid values for variable
SET @@global.replica_parallel_workers=1;
include/assert.inc [@@global.replica_parallel_workers should be 1]
include/assert.inc [replica_parallel_workers should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_parallel_workers should be 1]
include/assert.inc [slave_parallel_workers should be 1 in P_S.global_variables]
SET @@global.replica_parallel_workers=2;
include/assert.inc [@@global.replica_parallel_workers should be 2]
include/assert.inc [replica_parallel_workers should be 2 in P_S.global_variables]
include/assert.inc [@@global.slave_parallel_workers should be 2]
include/assert.inc [slave_parallel_workers should be 2 in P_S.global_variables]
SET @@global.replica_parallel_workers=100;
include/assert.inc [@@global.replica_parallel_workers should be 100]
include/assert.inc [replica_parallel_workers should be 100 in P_S.global_variables]
include/assert.inc [@@global.slave_parallel_workers should be 100]
include/assert.inc [slave_parallel_workers should be 100 in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_parallel_workers = DEFAULT;
include/assert.inc [@@global.replica_parallel_workers should be 4]
include/assert.inc [replica_parallel_workers should be 4 in P_S.global_variables]
include/assert.inc [@@global.slave_parallel_workers should be 4]
include/assert.inc [slave_parallel_workers should be 4 in P_S.global_variables]
# Maximum value
SET @@global.replica_parallel_workers = 1024;
include/assert.inc [@@global.replica_parallel_workers should be 1024]
include/assert.inc [replica_parallel_workers should be 1024 in P_S.global_variables]
include/assert.inc [@@global.slave_parallel_workers should be 1024]
include/assert.inc [slave_parallel_workers should be 1024 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.replica_parallel_workers = 1025;
Warnings:
Warning	1292	Truncated incorrect replica_parallel_workers value: '1025'
include/assert.inc [@@global.replica_parallel_workers should be 1024]
include/assert.inc [replica_parallel_workers should be 1024 in P_S.global_variables]
include/assert.inc [@@global.slave_parallel_workers should be 1024]
include/assert.inc [slave_parallel_workers should be 1024 in P_S.global_variables]
# Invalid values
SET @@global.replica_parallel_workers = 1.5;
Got one of the listed errors
SET @@global.replica_parallel_workers = ON;
Got one of the listed errors
SET @@global.replica_parallel_workers = 'x';
Got one of the listed errors
SET @@global.replica_parallel_workers = NULL;
Got one of the listed errors
# Restore default
SET @@global.replica_parallel_workers = DEFAULT;
include/assert.inc [@@global.replica_parallel_workers should be 4]
include/assert.inc [replica_parallel_workers should be 4 in P_S.global_variables]
include/assert.inc [@@global.slave_parallel_workers should be 4]
include/assert.inc [slave_parallel_workers should be 4 in P_S.global_variables]
==== Testing SET @@session.replica_parallel_workers [invalid scope] ====
SET @@session.replica_parallel_workers = DEFAULT;
ERROR HY000: Variable 'replica_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_parallel_workers = DEFAULT;
ERROR HY000: Variable 'replica_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_parallel_workers = 1;
ERROR HY000: Variable 'replica_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_parallel_workers = 1;
ERROR HY000: Variable 'replica_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_parallel_workers = 2;
ERROR HY000: Variable 'replica_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_parallel_workers = 2;
ERROR HY000: Variable 'replica_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_parallel_workers = 100;
ERROR HY000: Variable 'replica_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_parallel_workers = 100;
ERROR HY000: Variable 'replica_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_parallel_workers;
ERROR HY000: Variable 'replica_parallel_workers' is a GLOBAL variable
SELECT @@replica_parallel_workers;
@@replica_parallel_workers
4
include/assert.inc [Variable replica_parallel_workers should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_parallel_workers [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_parallel_workers should be 4]
include/assert.inc [slave_parallel_workers should be 4 in P_S.global_variables]
include/assert.inc [@@global.replica_parallel_workers should be 4]
include/assert.inc [replica_parallel_workers should be 4 in P_S.global_variables]
# Valid values for variable
SET @@global.slave_parallel_workers=1;
Warnings:
Warning	1287	'@@slave_parallel_workers' is deprecated and will be removed in a future release. Please use replica_parallel_workers instead.
include/assert.inc [@@global.slave_parallel_workers should be 1]
include/assert.inc [slave_parallel_workers should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_parallel_workers should be 1]
include/assert.inc [replica_parallel_workers should be 1 in P_S.global_variables]
SET @@global.slave_parallel_workers=2;
Warnings:
Warning	1287	'@@slave_parallel_workers' is deprecated and will be removed in a future release. Please use replica_parallel_workers instead.
include/assert.inc [@@global.slave_parallel_workers should be 2]
include/assert.inc [slave_parallel_workers should be 2 in P_S.global_variables]
include/assert.inc [@@global.replica_parallel_workers should be 2]
include/assert.inc [replica_parallel_workers should be 2 in P_S.global_variables]
SET @@global.slave_parallel_workers=100;
Warnings:
Warning	1287	'@@slave_parallel_workers' is deprecated and will be removed in a future release. Please use replica_parallel_workers instead.
include/assert.inc [@@global.slave_parallel_workers should be 100]
include/assert.inc [slave_parallel_workers should be 100 in P_S.global_variables]
include/assert.inc [@@global.replica_parallel_workers should be 100]
include/assert.inc [replica_parallel_workers should be 100 in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_parallel_workers = DEFAULT;
Warnings:
Warning	1287	'@@slave_parallel_workers' is deprecated and will be removed in a future release. Please use replica_parallel_workers instead.
include/assert.inc [@@global.slave_parallel_workers should be 4]
include/assert.inc [slave_parallel_workers should be 4 in P_S.global_variables]
include/assert.inc [@@global.replica_parallel_workers should be 4]
include/assert.inc [replica_parallel_workers should be 4 in P_S.global_variables]
# Maximum value
SET @@global.slave_parallel_workers = 1024;
Warnings:
Warning	1287	'@@slave_parallel_workers' is deprecated and will be removed in a future release. Please use replica_parallel_workers instead.
include/assert.inc [@@global.slave_parallel_workers should be 1024]
include/assert.inc [slave_parallel_workers should be 1024 in P_S.global_variables]
include/assert.inc [@@global.replica_parallel_workers should be 1024]
include/assert.inc [replica_parallel_workers should be 1024 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.slave_parallel_workers = 1025;
Warnings:
Warning	1287	'@@slave_parallel_workers' is deprecated and will be removed in a future release. Please use replica_parallel_workers instead.
Warning	1292	Truncated incorrect replica_parallel_workers value: '1025'
include/assert.inc [@@global.slave_parallel_workers should be 1024]
include/assert.inc [slave_parallel_workers should be 1024 in P_S.global_variables]
include/assert.inc [@@global.replica_parallel_workers should be 1024]
include/assert.inc [replica_parallel_workers should be 1024 in P_S.global_variables]
# Invalid values
SET @@global.slave_parallel_workers = 1.5;
Got one of the listed errors
SET @@global.slave_parallel_workers = ON;
Got one of the listed errors
SET @@global.slave_parallel_workers = 'x';
Got one of the listed errors
SET @@global.slave_parallel_workers = NULL;
Got one of the listed errors
# Restore default
SET @@global.slave_parallel_workers = DEFAULT;
Warnings:
Warning	1287	'@@slave_parallel_workers' is deprecated and will be removed in a future release. Please use replica_parallel_workers instead.
include/assert.inc [@@global.slave_parallel_workers should be 4]
include/assert.inc [slave_parallel_workers should be 4 in P_S.global_variables]
include/assert.inc [@@global.replica_parallel_workers should be 4]
include/assert.inc [replica_parallel_workers should be 4 in P_S.global_variables]
==== Testing SET @@session.slave_parallel_workers [invalid scope] ====
SET @@session.slave_parallel_workers = DEFAULT;
ERROR HY000: Variable 'slave_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_parallel_workers = DEFAULT;
ERROR HY000: Variable 'slave_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_parallel_workers = 1;
ERROR HY000: Variable 'slave_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_parallel_workers = 1;
ERROR HY000: Variable 'slave_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_parallel_workers = 2;
ERROR HY000: Variable 'slave_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_parallel_workers = 2;
ERROR HY000: Variable 'slave_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_parallel_workers = 100;
ERROR HY000: Variable 'slave_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_parallel_workers = 100;
ERROR HY000: Variable 'slave_parallel_workers' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_parallel_workers;
ERROR HY000: Variable 'slave_parallel_workers' is a GLOBAL variable
SELECT @@slave_parallel_workers;
@@slave_parallel_workers
4
Warnings:
Warning	1287	'@@slave_parallel_workers' is deprecated and will be removed in a future release. Please use replica_parallel_workers instead.
include/assert.inc [Variable slave_parallel_workers should exist in performance_schema.session_variables]
sysvar:
- name=replica_pending_jobs_size_max
- alias=slave_pending_jobs_size_max
- invalid=[1.5, "ON", "'x'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=1024
- max=18446744073709550592
- block_size=1024
- default=134217728
- values=[65536, 32768, 4096]
==== Testing SET @@global.replica_pending_jobs_size_max [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_pending_jobs_size_max should be 134217728]
include/assert.inc [replica_pending_jobs_size_max should be 134217728 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 134217728]
include/assert.inc [slave_pending_jobs_size_max should be 134217728 in P_S.global_variables]
# Valid values for variable
SET @@global.replica_pending_jobs_size_max=65536;
include/assert.inc [@@global.replica_pending_jobs_size_max should be 65536]
include/assert.inc [replica_pending_jobs_size_max should be 65536 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 65536]
include/assert.inc [slave_pending_jobs_size_max should be 65536 in P_S.global_variables]
SET @@global.replica_pending_jobs_size_max=32768;
include/assert.inc [@@global.replica_pending_jobs_size_max should be 32768]
include/assert.inc [replica_pending_jobs_size_max should be 32768 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 32768]
include/assert.inc [slave_pending_jobs_size_max should be 32768 in P_S.global_variables]
SET @@global.replica_pending_jobs_size_max=4096;
include/assert.inc [@@global.replica_pending_jobs_size_max should be 4096]
include/assert.inc [replica_pending_jobs_size_max should be 4096 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 4096]
include/assert.inc [slave_pending_jobs_size_max should be 4096 in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_pending_jobs_size_max = DEFAULT;
include/assert.inc [@@global.replica_pending_jobs_size_max should be 134217728]
include/assert.inc [replica_pending_jobs_size_max should be 134217728 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 134217728]
include/assert.inc [slave_pending_jobs_size_max should be 134217728 in P_S.global_variables]
# Minimum value
SET @@global.replica_pending_jobs_size_max = 1024;
include/assert.inc [@@global.replica_pending_jobs_size_max should be 1024]
include/assert.inc [replica_pending_jobs_size_max should be 1024 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 1024]
include/assert.inc [slave_pending_jobs_size_max should be 1024 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.replica_pending_jobs_size_max = 1023;
Warnings:
Warning	1292	Truncated incorrect replica_pending_jobs_size_max value: '1023'
include/assert.inc [@@global.replica_pending_jobs_size_max should be 1024]
include/assert.inc [replica_pending_jobs_size_max should be 1024 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 1024]
include/assert.inc [slave_pending_jobs_size_max should be 1024 in P_S.global_variables]
SET @@global.replica_pending_jobs_size_max = 0;
Warnings:
Warning	1292	Truncated incorrect replica_pending_jobs_size_max value: '0'
include/assert.inc [@@global.replica_pending_jobs_size_max should be 1024]
include/assert.inc [replica_pending_jobs_size_max should be 1024 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 1024]
include/assert.inc [slave_pending_jobs_size_max should be 1024 in P_S.global_variables]
# Maximum value
SET @@global.replica_pending_jobs_size_max = 18446744073709550592;
include/assert.inc [@@global.replica_pending_jobs_size_max should be 18446744073709550592]
include/assert.inc [replica_pending_jobs_size_max should be 18446744073709550592 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 18446744073709550592]
include/assert.inc [slave_pending_jobs_size_max should be 18446744073709550592 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.replica_pending_jobs_size_max = 18446744073709550593;
Warnings:
Warning	1292	Truncated incorrect replica_pending_jobs_size_max value: '18446744073709550593'
include/assert.inc [@@global.replica_pending_jobs_size_max should be 18446744073709550592]
include/assert.inc [replica_pending_jobs_size_max should be 18446744073709550592 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 18446744073709550592]
include/assert.inc [slave_pending_jobs_size_max should be 18446744073709550592 in P_S.global_variables]
SET @@global.replica_pending_jobs_size_max = 18446744073709551615;
Warnings:
Warning	1292	Truncated incorrect replica_pending_jobs_size_max value: '18446744073709551615'
include/assert.inc [@@global.replica_pending_jobs_size_max should be 18446744073709550592]
include/assert.inc [replica_pending_jobs_size_max should be 18446744073709550592 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 18446744073709550592]
include/assert.inc [slave_pending_jobs_size_max should be 18446744073709550592 in P_S.global_variables]
# Non-block-size value
SET @@global.replica_pending_jobs_size_max = 134218751;
Warnings:
Warning	1292	Truncated incorrect replica_pending_jobs_size_max value: '134218751'
include/assert.inc [@@global.replica_pending_jobs_size_max should be 134217728]
include/assert.inc [replica_pending_jobs_size_max should be 134217728 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 134217728]
include/assert.inc [slave_pending_jobs_size_max should be 134217728 in P_S.global_variables]
# Invalid values
SET @@global.replica_pending_jobs_size_max = 1.5;
Got one of the listed errors
SET @@global.replica_pending_jobs_size_max = ON;
Got one of the listed errors
SET @@global.replica_pending_jobs_size_max = 'x';
Got one of the listed errors
SET @@global.replica_pending_jobs_size_max = NULL;
Got one of the listed errors
# Restore default
SET @@global.replica_pending_jobs_size_max = DEFAULT;
include/assert.inc [@@global.replica_pending_jobs_size_max should be 134217728]
include/assert.inc [replica_pending_jobs_size_max should be 134217728 in P_S.global_variables]
include/assert.inc [@@global.slave_pending_jobs_size_max should be 134217728]
include/assert.inc [slave_pending_jobs_size_max should be 134217728 in P_S.global_variables]
==== Testing SET @@session.replica_pending_jobs_size_max [invalid scope] ====
SET @@session.replica_pending_jobs_size_max = DEFAULT;
ERROR HY000: Variable 'replica_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_pending_jobs_size_max = DEFAULT;
ERROR HY000: Variable 'replica_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_pending_jobs_size_max = 65536;
ERROR HY000: Variable 'replica_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_pending_jobs_size_max = 65536;
ERROR HY000: Variable 'replica_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_pending_jobs_size_max = 32768;
ERROR HY000: Variable 'replica_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_pending_jobs_size_max = 32768;
ERROR HY000: Variable 'replica_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_pending_jobs_size_max = 4096;
ERROR HY000: Variable 'replica_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_pending_jobs_size_max = 4096;
ERROR HY000: Variable 'replica_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_pending_jobs_size_max;
ERROR HY000: Variable 'replica_pending_jobs_size_max' is a GLOBAL variable
SELECT @@replica_pending_jobs_size_max;
@@replica_pending_jobs_size_max
134217728
include/assert.inc [Variable replica_pending_jobs_size_max should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_pending_jobs_size_max [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_pending_jobs_size_max should be 134217728]
include/assert.inc [slave_pending_jobs_size_max should be 134217728 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 134217728]
include/assert.inc [replica_pending_jobs_size_max should be 134217728 in P_S.global_variables]
# Valid values for variable
SET @@global.slave_pending_jobs_size_max=65536;
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
include/assert.inc [@@global.slave_pending_jobs_size_max should be 65536]
include/assert.inc [slave_pending_jobs_size_max should be 65536 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 65536]
include/assert.inc [replica_pending_jobs_size_max should be 65536 in P_S.global_variables]
SET @@global.slave_pending_jobs_size_max=32768;
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
include/assert.inc [@@global.slave_pending_jobs_size_max should be 32768]
include/assert.inc [slave_pending_jobs_size_max should be 32768 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 32768]
include/assert.inc [replica_pending_jobs_size_max should be 32768 in P_S.global_variables]
SET @@global.slave_pending_jobs_size_max=4096;
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
include/assert.inc [@@global.slave_pending_jobs_size_max should be 4096]
include/assert.inc [slave_pending_jobs_size_max should be 4096 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 4096]
include/assert.inc [replica_pending_jobs_size_max should be 4096 in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_pending_jobs_size_max = DEFAULT;
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
include/assert.inc [@@global.slave_pending_jobs_size_max should be 134217728]
include/assert.inc [slave_pending_jobs_size_max should be 134217728 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 134217728]
include/assert.inc [replica_pending_jobs_size_max should be 134217728 in P_S.global_variables]
# Minimum value
SET @@global.slave_pending_jobs_size_max = 1024;
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
include/assert.inc [@@global.slave_pending_jobs_size_max should be 1024]
include/assert.inc [slave_pending_jobs_size_max should be 1024 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 1024]
include/assert.inc [replica_pending_jobs_size_max should be 1024 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.slave_pending_jobs_size_max = 1023;
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
Warning	1292	Truncated incorrect replica_pending_jobs_size_max value: '1023'
include/assert.inc [@@global.slave_pending_jobs_size_max should be 1024]
include/assert.inc [slave_pending_jobs_size_max should be 1024 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 1024]
include/assert.inc [replica_pending_jobs_size_max should be 1024 in P_S.global_variables]
SET @@global.slave_pending_jobs_size_max = 0;
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
Warning	1292	Truncated incorrect replica_pending_jobs_size_max value: '0'
include/assert.inc [@@global.slave_pending_jobs_size_max should be 1024]
include/assert.inc [slave_pending_jobs_size_max should be 1024 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 1024]
include/assert.inc [replica_pending_jobs_size_max should be 1024 in P_S.global_variables]
# Maximum value
SET @@global.slave_pending_jobs_size_max = 18446744073709550592;
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
include/assert.inc [@@global.slave_pending_jobs_size_max should be 18446744073709550592]
include/assert.inc [slave_pending_jobs_size_max should be 18446744073709550592 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 18446744073709550592]
include/assert.inc [replica_pending_jobs_size_max should be 18446744073709550592 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.slave_pending_jobs_size_max = 18446744073709550593;
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
Warning	1292	Truncated incorrect replica_pending_jobs_size_max value: '18446744073709550593'
include/assert.inc [@@global.slave_pending_jobs_size_max should be 18446744073709550592]
include/assert.inc [slave_pending_jobs_size_max should be 18446744073709550592 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 18446744073709550592]
include/assert.inc [replica_pending_jobs_size_max should be 18446744073709550592 in P_S.global_variables]
SET @@global.slave_pending_jobs_size_max = 18446744073709551615;
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
Warning	1292	Truncated incorrect replica_pending_jobs_size_max value: '18446744073709551615'
include/assert.inc [@@global.slave_pending_jobs_size_max should be 18446744073709550592]
include/assert.inc [slave_pending_jobs_size_max should be 18446744073709550592 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 18446744073709550592]
include/assert.inc [replica_pending_jobs_size_max should be 18446744073709550592 in P_S.global_variables]
# Non-block-size value
SET @@global.slave_pending_jobs_size_max = 134218751;
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
Warning	1292	Truncated incorrect replica_pending_jobs_size_max value: '134218751'
include/assert.inc [@@global.slave_pending_jobs_size_max should be 134217728]
include/assert.inc [slave_pending_jobs_size_max should be 134217728 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 134217728]
include/assert.inc [replica_pending_jobs_size_max should be 134217728 in P_S.global_variables]
# Invalid values
SET @@global.slave_pending_jobs_size_max = 1.5;
Got one of the listed errors
SET @@global.slave_pending_jobs_size_max = ON;
Got one of the listed errors
SET @@global.slave_pending_jobs_size_max = 'x';
Got one of the listed errors
SET @@global.slave_pending_jobs_size_max = NULL;
Got one of the listed errors
# Restore default
SET @@global.slave_pending_jobs_size_max = DEFAULT;
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
include/assert.inc [@@global.slave_pending_jobs_size_max should be 134217728]
include/assert.inc [slave_pending_jobs_size_max should be 134217728 in P_S.global_variables]
include/assert.inc [@@global.replica_pending_jobs_size_max should be 134217728]
include/assert.inc [replica_pending_jobs_size_max should be 134217728 in P_S.global_variables]
==== Testing SET @@session.slave_pending_jobs_size_max [invalid scope] ====
SET @@session.slave_pending_jobs_size_max = DEFAULT;
ERROR HY000: Variable 'slave_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_pending_jobs_size_max = DEFAULT;
ERROR HY000: Variable 'slave_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_pending_jobs_size_max = 65536;
ERROR HY000: Variable 'slave_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_pending_jobs_size_max = 65536;
ERROR HY000: Variable 'slave_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_pending_jobs_size_max = 32768;
ERROR HY000: Variable 'slave_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_pending_jobs_size_max = 32768;
ERROR HY000: Variable 'slave_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_pending_jobs_size_max = 4096;
ERROR HY000: Variable 'slave_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_pending_jobs_size_max = 4096;
ERROR HY000: Variable 'slave_pending_jobs_size_max' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_pending_jobs_size_max;
ERROR HY000: Variable 'slave_pending_jobs_size_max' is a GLOBAL variable
SELECT @@slave_pending_jobs_size_max;
@@slave_pending_jobs_size_max
134217728
Warnings:
Warning	1287	'@@slave_pending_jobs_size_max' is deprecated and will be removed in a future release. Please use replica_pending_jobs_size_max instead.
include/assert.inc [Variable slave_pending_jobs_size_max should exist in performance_schema.session_variables]
sysvar:
- name=replica_preserve_commit_order
- alias=slave_preserve_commit_order
- invalid=[0.1, 2, -1, "''", "'x'", "'TRUE'", "'1'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=1
- dynamic=1
- min=
- max=
- block_size=1
- default=1
- values=["ON", "OFF", 1, 0, "TRUE", "FALSE", "'ON'", "'OFF'"]
==== Testing SET @@global.replica_preserve_commit_order [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
# Valid values for variable
SET @@global.replica_preserve_commit_order=ON;
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
SET @@global.replica_preserve_commit_order=OFF;
include/assert.inc [@@global.replica_preserve_commit_order should be 0]
include/assert.inc [replica_preserve_commit_order should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_preserve_commit_order should be 0]
include/assert.inc [slave_preserve_commit_order should be 0 in P_S.global_variables]
SET @@global.replica_preserve_commit_order=1;
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
SET @@global.replica_preserve_commit_order=0;
include/assert.inc [@@global.replica_preserve_commit_order should be 0]
include/assert.inc [replica_preserve_commit_order should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_preserve_commit_order should be 0]
include/assert.inc [slave_preserve_commit_order should be 0 in P_S.global_variables]
SET @@global.replica_preserve_commit_order=TRUE;
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
SET @@global.replica_preserve_commit_order=FALSE;
include/assert.inc [@@global.replica_preserve_commit_order should be 0]
include/assert.inc [replica_preserve_commit_order should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_preserve_commit_order should be 0]
include/assert.inc [slave_preserve_commit_order should be 0 in P_S.global_variables]
SET @@global.replica_preserve_commit_order='ON';
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
SET @@global.replica_preserve_commit_order='OFF';
include/assert.inc [@@global.replica_preserve_commit_order should be 0]
include/assert.inc [replica_preserve_commit_order should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_preserve_commit_order should be 0]
include/assert.inc [slave_preserve_commit_order should be 0 in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_preserve_commit_order = DEFAULT;
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
# Invalid values
SET @@global.replica_preserve_commit_order = 0.1;
Got one of the listed errors
SET @@global.replica_preserve_commit_order = 2;
Got one of the listed errors
SET @@global.replica_preserve_commit_order = -1;
Got one of the listed errors
SET @@global.replica_preserve_commit_order = '';
Got one of the listed errors
SET @@global.replica_preserve_commit_order = 'x';
Got one of the listed errors
SET @@global.replica_preserve_commit_order = 'TRUE';
Got one of the listed errors
SET @@global.replica_preserve_commit_order = '1';
Got one of the listed errors
SET @@global.replica_preserve_commit_order = NULL;
Got one of the listed errors
# Restore default
SET @@global.replica_preserve_commit_order = DEFAULT;
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
==== Testing SET @@session.replica_preserve_commit_order [invalid scope] ====
SET @@session.replica_preserve_commit_order = DEFAULT;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_preserve_commit_order = DEFAULT;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_preserve_commit_order = ON;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_preserve_commit_order = ON;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_preserve_commit_order = OFF;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_preserve_commit_order = OFF;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_preserve_commit_order = 1;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_preserve_commit_order = 1;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_preserve_commit_order = 0;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_preserve_commit_order = 0;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_preserve_commit_order = TRUE;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_preserve_commit_order = TRUE;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_preserve_commit_order = FALSE;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_preserve_commit_order = FALSE;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_preserve_commit_order = 'ON';
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_preserve_commit_order = 'ON';
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_preserve_commit_order = 'OFF';
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_preserve_commit_order = 'OFF';
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_preserve_commit_order;
ERROR HY000: Variable 'replica_preserve_commit_order' is a GLOBAL variable
SELECT @@replica_preserve_commit_order;
@@replica_preserve_commit_order
1
include/assert.inc [Variable replica_preserve_commit_order should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_preserve_commit_order [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
# Valid values for variable
SET @@global.slave_preserve_commit_order=ON;
Warnings:
Warning	1287	'@@slave_preserve_commit_order' is deprecated and will be removed in a future release. Please use replica_preserve_commit_order instead.
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
SET @@global.slave_preserve_commit_order=OFF;
Warnings:
Warning	1287	'@@slave_preserve_commit_order' is deprecated and will be removed in a future release. Please use replica_preserve_commit_order instead.
include/assert.inc [@@global.slave_preserve_commit_order should be 0]
include/assert.inc [slave_preserve_commit_order should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_preserve_commit_order should be 0]
include/assert.inc [replica_preserve_commit_order should be 0 in P_S.global_variables]
SET @@global.slave_preserve_commit_order=1;
Warnings:
Warning	1287	'@@slave_preserve_commit_order' is deprecated and will be removed in a future release. Please use replica_preserve_commit_order instead.
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
SET @@global.slave_preserve_commit_order=0;
Warnings:
Warning	1287	'@@slave_preserve_commit_order' is deprecated and will be removed in a future release. Please use replica_preserve_commit_order instead.
include/assert.inc [@@global.slave_preserve_commit_order should be 0]
include/assert.inc [slave_preserve_commit_order should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_preserve_commit_order should be 0]
include/assert.inc [replica_preserve_commit_order should be 0 in P_S.global_variables]
SET @@global.slave_preserve_commit_order=TRUE;
Warnings:
Warning	1287	'@@slave_preserve_commit_order' is deprecated and will be removed in a future release. Please use replica_preserve_commit_order instead.
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
SET @@global.slave_preserve_commit_order=FALSE;
Warnings:
Warning	1287	'@@slave_preserve_commit_order' is deprecated and will be removed in a future release. Please use replica_preserve_commit_order instead.
include/assert.inc [@@global.slave_preserve_commit_order should be 0]
include/assert.inc [slave_preserve_commit_order should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_preserve_commit_order should be 0]
include/assert.inc [replica_preserve_commit_order should be 0 in P_S.global_variables]
SET @@global.slave_preserve_commit_order='ON';
Warnings:
Warning	1287	'@@slave_preserve_commit_order' is deprecated and will be removed in a future release. Please use replica_preserve_commit_order instead.
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
SET @@global.slave_preserve_commit_order='OFF';
Warnings:
Warning	1287	'@@slave_preserve_commit_order' is deprecated and will be removed in a future release. Please use replica_preserve_commit_order instead.
include/assert.inc [@@global.slave_preserve_commit_order should be 0]
include/assert.inc [slave_preserve_commit_order should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_preserve_commit_order should be 0]
include/assert.inc [replica_preserve_commit_order should be 0 in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_preserve_commit_order = DEFAULT;
Warnings:
Warning	1287	'@@slave_preserve_commit_order' is deprecated and will be removed in a future release. Please use replica_preserve_commit_order instead.
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
# Invalid values
SET @@global.slave_preserve_commit_order = 0.1;
Got one of the listed errors
SET @@global.slave_preserve_commit_order = 2;
Got one of the listed errors
SET @@global.slave_preserve_commit_order = -1;
Got one of the listed errors
SET @@global.slave_preserve_commit_order = '';
Got one of the listed errors
SET @@global.slave_preserve_commit_order = 'x';
Got one of the listed errors
SET @@global.slave_preserve_commit_order = 'TRUE';
Got one of the listed errors
SET @@global.slave_preserve_commit_order = '1';
Got one of the listed errors
SET @@global.slave_preserve_commit_order = NULL;
Got one of the listed errors
# Restore default
SET @@global.slave_preserve_commit_order = DEFAULT;
Warnings:
Warning	1287	'@@slave_preserve_commit_order' is deprecated and will be removed in a future release. Please use replica_preserve_commit_order instead.
include/assert.inc [@@global.slave_preserve_commit_order should be 1]
include/assert.inc [slave_preserve_commit_order should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_preserve_commit_order should be 1]
include/assert.inc [replica_preserve_commit_order should be 1 in P_S.global_variables]
==== Testing SET @@session.slave_preserve_commit_order [invalid scope] ====
SET @@session.slave_preserve_commit_order = DEFAULT;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_preserve_commit_order = DEFAULT;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_preserve_commit_order = ON;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_preserve_commit_order = ON;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_preserve_commit_order = OFF;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_preserve_commit_order = OFF;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_preserve_commit_order = 1;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_preserve_commit_order = 1;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_preserve_commit_order = 0;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_preserve_commit_order = 0;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_preserve_commit_order = TRUE;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_preserve_commit_order = TRUE;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_preserve_commit_order = FALSE;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_preserve_commit_order = FALSE;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_preserve_commit_order = 'ON';
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_preserve_commit_order = 'ON';
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_preserve_commit_order = 'OFF';
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_preserve_commit_order = 'OFF';
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_preserve_commit_order;
ERROR HY000: Variable 'slave_preserve_commit_order' is a GLOBAL variable
SELECT @@slave_preserve_commit_order;
@@slave_preserve_commit_order
1
Warnings:
Warning	1287	'@@slave_preserve_commit_order' is deprecated and will be removed in a future release. Please use replica_preserve_commit_order instead.
include/assert.inc [Variable slave_preserve_commit_order should exist in performance_schema.session_variables]
sysvar:
- name=replica_skip_errors
- alias=slave_skip_errors
- invalid=[0, 1, 1.5, -1, "ON", null, "'x'"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=1
- dynamic=0
- min=
- max=
- block_size=1
- default=OFF
- values=["all", "ddl_exist_errors", "'1,2'", "'ddl_exist_errors,3,9,3,ddl_exist_errors,all'"]
==== Testing SET @@global.replica_skip_errors [read-only] ====
# Initial value is default
include/assert.inc [@@global.replica_skip_errors should be OFF]
include/assert.inc [replica_skip_errors should be OFF in P_S.global_variables]
include/assert.inc [@@global.slave_skip_errors should be OFF]
include/assert.inc [slave_skip_errors should be OFF in P_S.global_variables]
# Valid values for *read-only* variable cannot be set
SET @@global.replica_skip_errors = all;
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
include/assert.inc [@@global.replica_skip_errors should be OFF]
include/assert.inc [replica_skip_errors should be OFF in P_S.global_variables]
include/assert.inc [@@global.slave_skip_errors should be OFF]
include/assert.inc [slave_skip_errors should be OFF in P_S.global_variables]
SET @@global.replica_skip_errors = ddl_exist_errors;
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
include/assert.inc [@@global.replica_skip_errors should be OFF]
include/assert.inc [replica_skip_errors should be OFF in P_S.global_variables]
include/assert.inc [@@global.slave_skip_errors should be OFF]
include/assert.inc [slave_skip_errors should be OFF in P_S.global_variables]
SET @@global.replica_skip_errors = '1,2';
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
include/assert.inc [@@global.replica_skip_errors should be OFF]
include/assert.inc [replica_skip_errors should be OFF in P_S.global_variables]
include/assert.inc [@@global.slave_skip_errors should be OFF]
include/assert.inc [slave_skip_errors should be OFF in P_S.global_variables]
SET @@global.replica_skip_errors = 'ddl_exist_errors,3,9,3,ddl_exist_errors,all';
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
include/assert.inc [@@global.replica_skip_errors should be OFF]
include/assert.inc [replica_skip_errors should be OFF in P_S.global_variables]
include/assert.inc [@@global.slave_skip_errors should be OFF]
include/assert.inc [slave_skip_errors should be OFF in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_skip_errors = DEFAULT;
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
include/assert.inc [@@global.replica_skip_errors should be OFF]
include/assert.inc [replica_skip_errors should be OFF in P_S.global_variables]
include/assert.inc [@@global.slave_skip_errors should be OFF]
include/assert.inc [slave_skip_errors should be OFF in P_S.global_variables]
==== Testing SET @@session.replica_skip_errors [invalid scope] ====
SET @@session.replica_skip_errors = DEFAULT;
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
SET @@replica_skip_errors = DEFAULT;
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
SET @@session.replica_skip_errors = all;
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
SET @@replica_skip_errors = all;
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
SET @@session.replica_skip_errors = ddl_exist_errors;
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
SET @@replica_skip_errors = ddl_exist_errors;
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
SET @@session.replica_skip_errors = '1,2';
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
SET @@replica_skip_errors = '1,2';
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
SET @@session.replica_skip_errors = 'ddl_exist_errors,3,9,3,ddl_exist_errors,all';
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
SET @@replica_skip_errors = 'ddl_exist_errors,3,9,3,ddl_exist_errors,all';
ERROR HY000: Variable 'replica_skip_errors' is a read only variable
SELECT @@session.replica_skip_errors;
ERROR HY000: Variable 'replica_skip_errors' is a GLOBAL variable
SELECT @@replica_skip_errors;
@@replica_skip_errors
OFF
include/assert.inc [Variable replica_skip_errors should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_skip_errors [read-only] ====
# Initial value is default
include/assert.inc [@@global.slave_skip_errors should be OFF]
include/assert.inc [slave_skip_errors should be OFF in P_S.global_variables]
include/assert.inc [@@global.replica_skip_errors should be OFF]
include/assert.inc [replica_skip_errors should be OFF in P_S.global_variables]
# Valid values for *read-only* variable cannot be set
SET @@global.slave_skip_errors = all;
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
include/assert.inc [@@global.slave_skip_errors should be OFF]
include/assert.inc [slave_skip_errors should be OFF in P_S.global_variables]
include/assert.inc [@@global.replica_skip_errors should be OFF]
include/assert.inc [replica_skip_errors should be OFF in P_S.global_variables]
SET @@global.slave_skip_errors = ddl_exist_errors;
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
include/assert.inc [@@global.slave_skip_errors should be OFF]
include/assert.inc [slave_skip_errors should be OFF in P_S.global_variables]
include/assert.inc [@@global.replica_skip_errors should be OFF]
include/assert.inc [replica_skip_errors should be OFF in P_S.global_variables]
SET @@global.slave_skip_errors = '1,2';
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
include/assert.inc [@@global.slave_skip_errors should be OFF]
include/assert.inc [slave_skip_errors should be OFF in P_S.global_variables]
include/assert.inc [@@global.replica_skip_errors should be OFF]
include/assert.inc [replica_skip_errors should be OFF in P_S.global_variables]
SET @@global.slave_skip_errors = 'ddl_exist_errors,3,9,3,ddl_exist_errors,all';
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
include/assert.inc [@@global.slave_skip_errors should be OFF]
include/assert.inc [slave_skip_errors should be OFF in P_S.global_variables]
include/assert.inc [@@global.replica_skip_errors should be OFF]
include/assert.inc [replica_skip_errors should be OFF in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_skip_errors = DEFAULT;
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
include/assert.inc [@@global.slave_skip_errors should be OFF]
include/assert.inc [slave_skip_errors should be OFF in P_S.global_variables]
include/assert.inc [@@global.replica_skip_errors should be OFF]
include/assert.inc [replica_skip_errors should be OFF in P_S.global_variables]
==== Testing SET @@session.slave_skip_errors [invalid scope] ====
SET @@session.slave_skip_errors = DEFAULT;
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
SET @@slave_skip_errors = DEFAULT;
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
SET @@session.slave_skip_errors = all;
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
SET @@slave_skip_errors = all;
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
SET @@session.slave_skip_errors = ddl_exist_errors;
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
SET @@slave_skip_errors = ddl_exist_errors;
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
SET @@session.slave_skip_errors = '1,2';
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
SET @@slave_skip_errors = '1,2';
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
SET @@session.slave_skip_errors = 'ddl_exist_errors,3,9,3,ddl_exist_errors,all';
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
SET @@slave_skip_errors = 'ddl_exist_errors,3,9,3,ddl_exist_errors,all';
ERROR HY000: Variable 'slave_skip_errors' is a read only variable
SELECT @@session.slave_skip_errors;
ERROR HY000: Variable 'slave_skip_errors' is a GLOBAL variable
SELECT @@slave_skip_errors;
@@slave_skip_errors
OFF
Warnings:
Warning	1287	'@@slave_skip_errors' is deprecated and will be removed in a future release. Please use replica_skip_errors instead.
include/assert.inc [Variable slave_skip_errors should exist in performance_schema.session_variables]
sysvar:
- name=replica_sql_verify_checksum
- alias=slave_sql_verify_checksum
- invalid=[0.1, 2, -1, "''", "'x'", "'TRUE'", "'1'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=
- max=
- block_size=1
- default=1
- values=["ON", "OFF", 1, 0, "TRUE", "FALSE", "'ON'", "'OFF'"]
==== Testing SET @@global.replica_sql_verify_checksum [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
# Valid values for variable
SET @@global.replica_sql_verify_checksum=ON;
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
SET @@global.replica_sql_verify_checksum=OFF;
include/assert.inc [@@global.replica_sql_verify_checksum should be 0]
include/assert.inc [replica_sql_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_sql_verify_checksum should be 0]
include/assert.inc [slave_sql_verify_checksum should be 0 in P_S.global_variables]
SET @@global.replica_sql_verify_checksum=1;
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
SET @@global.replica_sql_verify_checksum=0;
include/assert.inc [@@global.replica_sql_verify_checksum should be 0]
include/assert.inc [replica_sql_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_sql_verify_checksum should be 0]
include/assert.inc [slave_sql_verify_checksum should be 0 in P_S.global_variables]
SET @@global.replica_sql_verify_checksum=TRUE;
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
SET @@global.replica_sql_verify_checksum=FALSE;
include/assert.inc [@@global.replica_sql_verify_checksum should be 0]
include/assert.inc [replica_sql_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_sql_verify_checksum should be 0]
include/assert.inc [slave_sql_verify_checksum should be 0 in P_S.global_variables]
SET @@global.replica_sql_verify_checksum='ON';
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
SET @@global.replica_sql_verify_checksum='OFF';
include/assert.inc [@@global.replica_sql_verify_checksum should be 0]
include/assert.inc [replica_sql_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_sql_verify_checksum should be 0]
include/assert.inc [slave_sql_verify_checksum should be 0 in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_sql_verify_checksum = DEFAULT;
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
# Invalid values
SET @@global.replica_sql_verify_checksum = 0.1;
Got one of the listed errors
SET @@global.replica_sql_verify_checksum = 2;
Got one of the listed errors
SET @@global.replica_sql_verify_checksum = -1;
Got one of the listed errors
SET @@global.replica_sql_verify_checksum = '';
Got one of the listed errors
SET @@global.replica_sql_verify_checksum = 'x';
Got one of the listed errors
SET @@global.replica_sql_verify_checksum = 'TRUE';
Got one of the listed errors
SET @@global.replica_sql_verify_checksum = '1';
Got one of the listed errors
SET @@global.replica_sql_verify_checksum = NULL;
Got one of the listed errors
# Restore default
SET @@global.replica_sql_verify_checksum = DEFAULT;
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
==== Testing SET @@session.replica_sql_verify_checksum [invalid scope] ====
SET @@session.replica_sql_verify_checksum = DEFAULT;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_sql_verify_checksum = DEFAULT;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_sql_verify_checksum = ON;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_sql_verify_checksum = ON;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_sql_verify_checksum = OFF;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_sql_verify_checksum = OFF;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_sql_verify_checksum = 1;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_sql_verify_checksum = 1;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_sql_verify_checksum = 0;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_sql_verify_checksum = 0;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_sql_verify_checksum = TRUE;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_sql_verify_checksum = TRUE;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_sql_verify_checksum = FALSE;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_sql_verify_checksum = FALSE;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_sql_verify_checksum = 'ON';
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_sql_verify_checksum = 'ON';
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_sql_verify_checksum = 'OFF';
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_sql_verify_checksum = 'OFF';
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_sql_verify_checksum;
ERROR HY000: Variable 'replica_sql_verify_checksum' is a GLOBAL variable
SELECT @@replica_sql_verify_checksum;
@@replica_sql_verify_checksum
1
include/assert.inc [Variable replica_sql_verify_checksum should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_sql_verify_checksum [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
# Valid values for variable
SET @@global.slave_sql_verify_checksum=ON;
Warnings:
Warning	1287	'@@slave_sql_verify_checksum' is deprecated and will be removed in a future release. Please use replica_sql_verify_checksum instead.
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
SET @@global.slave_sql_verify_checksum=OFF;
Warnings:
Warning	1287	'@@slave_sql_verify_checksum' is deprecated and will be removed in a future release. Please use replica_sql_verify_checksum instead.
include/assert.inc [@@global.slave_sql_verify_checksum should be 0]
include/assert.inc [slave_sql_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_sql_verify_checksum should be 0]
include/assert.inc [replica_sql_verify_checksum should be 0 in P_S.global_variables]
SET @@global.slave_sql_verify_checksum=1;
Warnings:
Warning	1287	'@@slave_sql_verify_checksum' is deprecated and will be removed in a future release. Please use replica_sql_verify_checksum instead.
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
SET @@global.slave_sql_verify_checksum=0;
Warnings:
Warning	1287	'@@slave_sql_verify_checksum' is deprecated and will be removed in a future release. Please use replica_sql_verify_checksum instead.
include/assert.inc [@@global.slave_sql_verify_checksum should be 0]
include/assert.inc [slave_sql_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_sql_verify_checksum should be 0]
include/assert.inc [replica_sql_verify_checksum should be 0 in P_S.global_variables]
SET @@global.slave_sql_verify_checksum=TRUE;
Warnings:
Warning	1287	'@@slave_sql_verify_checksum' is deprecated and will be removed in a future release. Please use replica_sql_verify_checksum instead.
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
SET @@global.slave_sql_verify_checksum=FALSE;
Warnings:
Warning	1287	'@@slave_sql_verify_checksum' is deprecated and will be removed in a future release. Please use replica_sql_verify_checksum instead.
include/assert.inc [@@global.slave_sql_verify_checksum should be 0]
include/assert.inc [slave_sql_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_sql_verify_checksum should be 0]
include/assert.inc [replica_sql_verify_checksum should be 0 in P_S.global_variables]
SET @@global.slave_sql_verify_checksum='ON';
Warnings:
Warning	1287	'@@slave_sql_verify_checksum' is deprecated and will be removed in a future release. Please use replica_sql_verify_checksum instead.
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
SET @@global.slave_sql_verify_checksum='OFF';
Warnings:
Warning	1287	'@@slave_sql_verify_checksum' is deprecated and will be removed in a future release. Please use replica_sql_verify_checksum instead.
include/assert.inc [@@global.slave_sql_verify_checksum should be 0]
include/assert.inc [slave_sql_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_sql_verify_checksum should be 0]
include/assert.inc [replica_sql_verify_checksum should be 0 in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_sql_verify_checksum = DEFAULT;
Warnings:
Warning	1287	'@@slave_sql_verify_checksum' is deprecated and will be removed in a future release. Please use replica_sql_verify_checksum instead.
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
# Invalid values
SET @@global.slave_sql_verify_checksum = 0.1;
Got one of the listed errors
SET @@global.slave_sql_verify_checksum = 2;
Got one of the listed errors
SET @@global.slave_sql_verify_checksum = -1;
Got one of the listed errors
SET @@global.slave_sql_verify_checksum = '';
Got one of the listed errors
SET @@global.slave_sql_verify_checksum = 'x';
Got one of the listed errors
SET @@global.slave_sql_verify_checksum = 'TRUE';
Got one of the listed errors
SET @@global.slave_sql_verify_checksum = '1';
Got one of the listed errors
SET @@global.slave_sql_verify_checksum = NULL;
Got one of the listed errors
# Restore default
SET @@global.slave_sql_verify_checksum = DEFAULT;
Warnings:
Warning	1287	'@@slave_sql_verify_checksum' is deprecated and will be removed in a future release. Please use replica_sql_verify_checksum instead.
include/assert.inc [@@global.slave_sql_verify_checksum should be 1]
include/assert.inc [slave_sql_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_sql_verify_checksum should be 1]
include/assert.inc [replica_sql_verify_checksum should be 1 in P_S.global_variables]
==== Testing SET @@session.slave_sql_verify_checksum [invalid scope] ====
SET @@session.slave_sql_verify_checksum = DEFAULT;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_sql_verify_checksum = DEFAULT;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_sql_verify_checksum = ON;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_sql_verify_checksum = ON;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_sql_verify_checksum = OFF;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_sql_verify_checksum = OFF;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_sql_verify_checksum = 1;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_sql_verify_checksum = 1;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_sql_verify_checksum = 0;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_sql_verify_checksum = 0;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_sql_verify_checksum = TRUE;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_sql_verify_checksum = TRUE;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_sql_verify_checksum = FALSE;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_sql_verify_checksum = FALSE;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_sql_verify_checksum = 'ON';
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_sql_verify_checksum = 'ON';
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_sql_verify_checksum = 'OFF';
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_sql_verify_checksum = 'OFF';
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_sql_verify_checksum;
ERROR HY000: Variable 'slave_sql_verify_checksum' is a GLOBAL variable
SELECT @@slave_sql_verify_checksum;
@@slave_sql_verify_checksum
1
Warnings:
Warning	1287	'@@slave_sql_verify_checksum' is deprecated and will be removed in a future release. Please use replica_sql_verify_checksum instead.
include/assert.inc [Variable slave_sql_verify_checksum should exist in performance_schema.session_variables]
sysvar:
- name=replica_transaction_retries
- alias=slave_transaction_retries
- invalid=[1.5, "ON", "'x'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=0
- max=18446744073709551615
- block_size=1
- default=10
- values=[1, 2, 99]
==== Testing SET @@global.replica_transaction_retries [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_transaction_retries should be 10]
include/assert.inc [replica_transaction_retries should be 10 in P_S.global_variables]
include/assert.inc [@@global.slave_transaction_retries should be 10]
include/assert.inc [slave_transaction_retries should be 10 in P_S.global_variables]
# Valid values for variable
SET @@global.replica_transaction_retries=1;
include/assert.inc [@@global.replica_transaction_retries should be 1]
include/assert.inc [replica_transaction_retries should be 1 in P_S.global_variables]
include/assert.inc [@@global.slave_transaction_retries should be 1]
include/assert.inc [slave_transaction_retries should be 1 in P_S.global_variables]
SET @@global.replica_transaction_retries=2;
include/assert.inc [@@global.replica_transaction_retries should be 2]
include/assert.inc [replica_transaction_retries should be 2 in P_S.global_variables]
include/assert.inc [@@global.slave_transaction_retries should be 2]
include/assert.inc [slave_transaction_retries should be 2 in P_S.global_variables]
SET @@global.replica_transaction_retries=99;
include/assert.inc [@@global.replica_transaction_retries should be 99]
include/assert.inc [replica_transaction_retries should be 99 in P_S.global_variables]
include/assert.inc [@@global.slave_transaction_retries should be 99]
include/assert.inc [slave_transaction_retries should be 99 in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_transaction_retries = DEFAULT;
include/assert.inc [@@global.replica_transaction_retries should be 10]
include/assert.inc [replica_transaction_retries should be 10 in P_S.global_variables]
include/assert.inc [@@global.slave_transaction_retries should be 10]
include/assert.inc [slave_transaction_retries should be 10 in P_S.global_variables]
# Minimum value
SET @@global.replica_transaction_retries = 0;
include/assert.inc [@@global.replica_transaction_retries should be 0]
include/assert.inc [replica_transaction_retries should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_transaction_retries should be 0]
include/assert.inc [slave_transaction_retries should be 0 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.replica_transaction_retries = -1;
Warnings:
Warning	1292	Truncated incorrect replica_transaction_retries value: '-1'
include/assert.inc [@@global.replica_transaction_retries should be 0]
include/assert.inc [replica_transaction_retries should be 0 in P_S.global_variables]
include/assert.inc [@@global.slave_transaction_retries should be 0]
include/assert.inc [slave_transaction_retries should be 0 in P_S.global_variables]
# Maximum value
SET @@global.replica_transaction_retries = 18446744073709551615;
include/assert.inc [@@global.replica_transaction_retries should be 18446744073709551615]
include/assert.inc [replica_transaction_retries should be 18446744073709551615 in P_S.global_variables]
include/assert.inc [@@global.slave_transaction_retries should be 18446744073709551615]
include/assert.inc [slave_transaction_retries should be 18446744073709551615 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Invalid values
SET @@global.replica_transaction_retries = 1.5;
Got one of the listed errors
SET @@global.replica_transaction_retries = ON;
Got one of the listed errors
SET @@global.replica_transaction_retries = 'x';
Got one of the listed errors
SET @@global.replica_transaction_retries = NULL;
Got one of the listed errors
# Restore default
SET @@global.replica_transaction_retries = DEFAULT;
include/assert.inc [@@global.replica_transaction_retries should be 10]
include/assert.inc [replica_transaction_retries should be 10 in P_S.global_variables]
include/assert.inc [@@global.slave_transaction_retries should be 10]
include/assert.inc [slave_transaction_retries should be 10 in P_S.global_variables]
==== Testing SET @@session.replica_transaction_retries [invalid scope] ====
SET @@session.replica_transaction_retries = DEFAULT;
ERROR HY000: Variable 'replica_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_transaction_retries = DEFAULT;
ERROR HY000: Variable 'replica_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_transaction_retries = 1;
ERROR HY000: Variable 'replica_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_transaction_retries = 1;
ERROR HY000: Variable 'replica_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_transaction_retries = 2;
ERROR HY000: Variable 'replica_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_transaction_retries = 2;
ERROR HY000: Variable 'replica_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_transaction_retries = 99;
ERROR HY000: Variable 'replica_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_transaction_retries = 99;
ERROR HY000: Variable 'replica_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_transaction_retries;
ERROR HY000: Variable 'replica_transaction_retries' is a GLOBAL variable
SELECT @@replica_transaction_retries;
@@replica_transaction_retries
10
include/assert.inc [Variable replica_transaction_retries should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_transaction_retries [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_transaction_retries should be 10]
include/assert.inc [slave_transaction_retries should be 10 in P_S.global_variables]
include/assert.inc [@@global.replica_transaction_retries should be 10]
include/assert.inc [replica_transaction_retries should be 10 in P_S.global_variables]
# Valid values for variable
SET @@global.slave_transaction_retries=1;
Warnings:
Warning	1287	'@@slave_transaction_retries' is deprecated and will be removed in a future release. Please use replica_transaction_retries instead.
include/assert.inc [@@global.slave_transaction_retries should be 1]
include/assert.inc [slave_transaction_retries should be 1 in P_S.global_variables]
include/assert.inc [@@global.replica_transaction_retries should be 1]
include/assert.inc [replica_transaction_retries should be 1 in P_S.global_variables]
SET @@global.slave_transaction_retries=2;
Warnings:
Warning	1287	'@@slave_transaction_retries' is deprecated and will be removed in a future release. Please use replica_transaction_retries instead.
include/assert.inc [@@global.slave_transaction_retries should be 2]
include/assert.inc [slave_transaction_retries should be 2 in P_S.global_variables]
include/assert.inc [@@global.replica_transaction_retries should be 2]
include/assert.inc [replica_transaction_retries should be 2 in P_S.global_variables]
SET @@global.slave_transaction_retries=99;
Warnings:
Warning	1287	'@@slave_transaction_retries' is deprecated and will be removed in a future release. Please use replica_transaction_retries instead.
include/assert.inc [@@global.slave_transaction_retries should be 99]
include/assert.inc [slave_transaction_retries should be 99 in P_S.global_variables]
include/assert.inc [@@global.replica_transaction_retries should be 99]
include/assert.inc [replica_transaction_retries should be 99 in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_transaction_retries = DEFAULT;
Warnings:
Warning	1287	'@@slave_transaction_retries' is deprecated and will be removed in a future release. Please use replica_transaction_retries instead.
include/assert.inc [@@global.slave_transaction_retries should be 10]
include/assert.inc [slave_transaction_retries should be 10 in P_S.global_variables]
include/assert.inc [@@global.replica_transaction_retries should be 10]
include/assert.inc [replica_transaction_retries should be 10 in P_S.global_variables]
# Minimum value
SET @@global.slave_transaction_retries = 0;
Warnings:
Warning	1287	'@@slave_transaction_retries' is deprecated and will be removed in a future release. Please use replica_transaction_retries instead.
include/assert.inc [@@global.slave_transaction_retries should be 0]
include/assert.inc [slave_transaction_retries should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_transaction_retries should be 0]
include/assert.inc [replica_transaction_retries should be 0 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.slave_transaction_retries = -1;
Warnings:
Warning	1287	'@@slave_transaction_retries' is deprecated and will be removed in a future release. Please use replica_transaction_retries instead.
Warning	1292	Truncated incorrect replica_transaction_retries value: '-1'
include/assert.inc [@@global.slave_transaction_retries should be 0]
include/assert.inc [slave_transaction_retries should be 0 in P_S.global_variables]
include/assert.inc [@@global.replica_transaction_retries should be 0]
include/assert.inc [replica_transaction_retries should be 0 in P_S.global_variables]
# Maximum value
SET @@global.slave_transaction_retries = 18446744073709551615;
Warnings:
Warning	1287	'@@slave_transaction_retries' is deprecated and will be removed in a future release. Please use replica_transaction_retries instead.
include/assert.inc [@@global.slave_transaction_retries should be 18446744073709551615]
include/assert.inc [slave_transaction_retries should be 18446744073709551615 in P_S.global_variables]
include/assert.inc [@@global.replica_transaction_retries should be 18446744073709551615]
include/assert.inc [replica_transaction_retries should be 18446744073709551615 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Invalid values
SET @@global.slave_transaction_retries = 1.5;
Got one of the listed errors
SET @@global.slave_transaction_retries = ON;
Got one of the listed errors
SET @@global.slave_transaction_retries = 'x';
Got one of the listed errors
SET @@global.slave_transaction_retries = NULL;
Got one of the listed errors
# Restore default
SET @@global.slave_transaction_retries = DEFAULT;
Warnings:
Warning	1287	'@@slave_transaction_retries' is deprecated and will be removed in a future release. Please use replica_transaction_retries instead.
include/assert.inc [@@global.slave_transaction_retries should be 10]
include/assert.inc [slave_transaction_retries should be 10 in P_S.global_variables]
include/assert.inc [@@global.replica_transaction_retries should be 10]
include/assert.inc [replica_transaction_retries should be 10 in P_S.global_variables]
==== Testing SET @@session.slave_transaction_retries [invalid scope] ====
SET @@session.slave_transaction_retries = DEFAULT;
ERROR HY000: Variable 'slave_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_transaction_retries = DEFAULT;
ERROR HY000: Variable 'slave_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_transaction_retries = 1;
ERROR HY000: Variable 'slave_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_transaction_retries = 1;
ERROR HY000: Variable 'slave_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_transaction_retries = 2;
ERROR HY000: Variable 'slave_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_transaction_retries = 2;
ERROR HY000: Variable 'slave_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_transaction_retries = 99;
ERROR HY000: Variable 'slave_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_transaction_retries = 99;
ERROR HY000: Variable 'slave_transaction_retries' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_transaction_retries;
ERROR HY000: Variable 'slave_transaction_retries' is a GLOBAL variable
SELECT @@slave_transaction_retries;
@@slave_transaction_retries
10
Warnings:
Warning	1287	'@@slave_transaction_retries' is deprecated and will be removed in a future release. Please use replica_transaction_retries instead.
include/assert.inc [Variable slave_transaction_retries should exist in performance_schema.session_variables]
sysvar:
- name=replica_type_conversions
- alias=slave_type_conversions
- invalid=[1.5, "ON", "'x'", "NULL", "'FOO_BAR'", -1, 16]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=
- max=
- block_size=1
- default=
- values=["ALL_LOSSY", "'ALL_NON_LOSSY'", "''", "'ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED'", 0, 8, 15]
==== Testing SET @@global.replica_type_conversions [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.replica_type_conversions should be ]
include/assert.inc [replica_type_conversions should be  in P_S.global_variables]
include/assert.inc [@@global.slave_type_conversions should be ]
include/assert.inc [slave_type_conversions should be  in P_S.global_variables]
# Valid values for variable
SET @@global.replica_type_conversions=ALL_LOSSY;
include/assert.inc [@@global.replica_type_conversions should be ALL_LOSSY]
include/assert.inc [replica_type_conversions should be ALL_LOSSY in P_S.global_variables]
include/assert.inc [@@global.slave_type_conversions should be ALL_LOSSY]
include/assert.inc [slave_type_conversions should be ALL_LOSSY in P_S.global_variables]
SET @@global.replica_type_conversions='ALL_NON_LOSSY';
include/assert.inc [@@global.replica_type_conversions should be ALL_NON_LOSSY]
include/assert.inc [replica_type_conversions should be ALL_NON_LOSSY in P_S.global_variables]
include/assert.inc [@@global.slave_type_conversions should be ALL_NON_LOSSY]
include/assert.inc [slave_type_conversions should be ALL_NON_LOSSY in P_S.global_variables]
SET @@global.replica_type_conversions='';
include/assert.inc [@@global.replica_type_conversions should be ]
include/assert.inc [replica_type_conversions should be  in P_S.global_variables]
include/assert.inc [@@global.slave_type_conversions should be ]
include/assert.inc [slave_type_conversions should be  in P_S.global_variables]
SET @@global.replica_type_conversions='ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED';
include/assert.inc [@@global.replica_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED]
include/assert.inc [replica_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED in P_S.global_variables]
include/assert.inc [@@global.slave_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED]
include/assert.inc [slave_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED in P_S.global_variables]
SET @@global.replica_type_conversions=0;
include/assert.inc [@@global.replica_type_conversions should be ]
include/assert.inc [replica_type_conversions should be  in P_S.global_variables]
include/assert.inc [@@global.slave_type_conversions should be ]
include/assert.inc [slave_type_conversions should be  in P_S.global_variables]
SET @@global.replica_type_conversions=8;
include/assert.inc [@@global.replica_type_conversions should be ALL_SIGNED]
include/assert.inc [replica_type_conversions should be ALL_SIGNED in P_S.global_variables]
include/assert.inc [@@global.slave_type_conversions should be ALL_SIGNED]
include/assert.inc [slave_type_conversions should be ALL_SIGNED in P_S.global_variables]
SET @@global.replica_type_conversions=15;
include/assert.inc [@@global.replica_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED]
include/assert.inc [replica_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED in P_S.global_variables]
include/assert.inc [@@global.slave_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED]
include/assert.inc [slave_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED in P_S.global_variables]
# Setting default value for variable
SET @@global.replica_type_conversions = DEFAULT;
include/assert.inc [@@global.replica_type_conversions should be ]
include/assert.inc [replica_type_conversions should be  in P_S.global_variables]
include/assert.inc [@@global.slave_type_conversions should be ]
include/assert.inc [slave_type_conversions should be  in P_S.global_variables]
# Invalid values
SET @@global.replica_type_conversions = 1.5;
Got one of the listed errors
SET @@global.replica_type_conversions = ON;
Got one of the listed errors
SET @@global.replica_type_conversions = 'x';
Got one of the listed errors
SET @@global.replica_type_conversions = NULL;
Got one of the listed errors
SET @@global.replica_type_conversions = 'FOO_BAR';
Got one of the listed errors
SET @@global.replica_type_conversions = -1;
Got one of the listed errors
SET @@global.replica_type_conversions = 16;
Got one of the listed errors
# Restore default
SET @@global.replica_type_conversions = DEFAULT;
include/assert.inc [@@global.replica_type_conversions should be ]
include/assert.inc [replica_type_conversions should be  in P_S.global_variables]
include/assert.inc [@@global.slave_type_conversions should be ]
include/assert.inc [slave_type_conversions should be  in P_S.global_variables]
==== Testing SET @@session.replica_type_conversions [invalid scope] ====
SET @@session.replica_type_conversions = DEFAULT;
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_type_conversions = DEFAULT;
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_type_conversions = ALL_LOSSY;
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_type_conversions = ALL_LOSSY;
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_type_conversions = 'ALL_NON_LOSSY';
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_type_conversions = 'ALL_NON_LOSSY';
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_type_conversions = '';
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_type_conversions = '';
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_type_conversions = 'ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED';
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_type_conversions = 'ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED';
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_type_conversions = 0;
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_type_conversions = 0;
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_type_conversions = 8;
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_type_conversions = 8;
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.replica_type_conversions = 15;
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@replica_type_conversions = 15;
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.replica_type_conversions;
ERROR HY000: Variable 'replica_type_conversions' is a GLOBAL variable
SELECT @@replica_type_conversions;
@@replica_type_conversions

include/assert.inc [Variable replica_type_conversions should exist in performance_schema.session_variables]
==== Testing SET @@global.slave_type_conversions [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.slave_type_conversions should be ]
include/assert.inc [slave_type_conversions should be  in P_S.global_variables]
include/assert.inc [@@global.replica_type_conversions should be ]
include/assert.inc [replica_type_conversions should be  in P_S.global_variables]
# Valid values for variable
SET @@global.slave_type_conversions=ALL_LOSSY;
Warnings:
Warning	1287	'@@slave_type_conversions' is deprecated and will be removed in a future release. Please use replica_type_conversions instead.
include/assert.inc [@@global.slave_type_conversions should be ALL_LOSSY]
include/assert.inc [slave_type_conversions should be ALL_LOSSY in P_S.global_variables]
include/assert.inc [@@global.replica_type_conversions should be ALL_LOSSY]
include/assert.inc [replica_type_conversions should be ALL_LOSSY in P_S.global_variables]
SET @@global.slave_type_conversions='ALL_NON_LOSSY';
Warnings:
Warning	1287	'@@slave_type_conversions' is deprecated and will be removed in a future release. Please use replica_type_conversions instead.
include/assert.inc [@@global.slave_type_conversions should be ALL_NON_LOSSY]
include/assert.inc [slave_type_conversions should be ALL_NON_LOSSY in P_S.global_variables]
include/assert.inc [@@global.replica_type_conversions should be ALL_NON_LOSSY]
include/assert.inc [replica_type_conversions should be ALL_NON_LOSSY in P_S.global_variables]
SET @@global.slave_type_conversions='';
Warnings:
Warning	1287	'@@slave_type_conversions' is deprecated and will be removed in a future release. Please use replica_type_conversions instead.
include/assert.inc [@@global.slave_type_conversions should be ]
include/assert.inc [slave_type_conversions should be  in P_S.global_variables]
include/assert.inc [@@global.replica_type_conversions should be ]
include/assert.inc [replica_type_conversions should be  in P_S.global_variables]
SET @@global.slave_type_conversions='ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED';
Warnings:
Warning	1287	'@@slave_type_conversions' is deprecated and will be removed in a future release. Please use replica_type_conversions instead.
include/assert.inc [@@global.slave_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED]
include/assert.inc [slave_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED in P_S.global_variables]
include/assert.inc [@@global.replica_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED]
include/assert.inc [replica_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED in P_S.global_variables]
SET @@global.slave_type_conversions=0;
Warnings:
Warning	1287	'@@slave_type_conversions' is deprecated and will be removed in a future release. Please use replica_type_conversions instead.
include/assert.inc [@@global.slave_type_conversions should be ]
include/assert.inc [slave_type_conversions should be  in P_S.global_variables]
include/assert.inc [@@global.replica_type_conversions should be ]
include/assert.inc [replica_type_conversions should be  in P_S.global_variables]
SET @@global.slave_type_conversions=8;
Warnings:
Warning	1287	'@@slave_type_conversions' is deprecated and will be removed in a future release. Please use replica_type_conversions instead.
include/assert.inc [@@global.slave_type_conversions should be ALL_SIGNED]
include/assert.inc [slave_type_conversions should be ALL_SIGNED in P_S.global_variables]
include/assert.inc [@@global.replica_type_conversions should be ALL_SIGNED]
include/assert.inc [replica_type_conversions should be ALL_SIGNED in P_S.global_variables]
SET @@global.slave_type_conversions=15;
Warnings:
Warning	1287	'@@slave_type_conversions' is deprecated and will be removed in a future release. Please use replica_type_conversions instead.
include/assert.inc [@@global.slave_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED]
include/assert.inc [slave_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED in P_S.global_variables]
include/assert.inc [@@global.replica_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED]
include/assert.inc [replica_type_conversions should be ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED in P_S.global_variables]
# Setting default value for variable
SET @@global.slave_type_conversions = DEFAULT;
Warnings:
Warning	1287	'@@slave_type_conversions' is deprecated and will be removed in a future release. Please use replica_type_conversions instead.
include/assert.inc [@@global.slave_type_conversions should be ]
include/assert.inc [slave_type_conversions should be  in P_S.global_variables]
include/assert.inc [@@global.replica_type_conversions should be ]
include/assert.inc [replica_type_conversions should be  in P_S.global_variables]
# Invalid values
SET @@global.slave_type_conversions = 1.5;
Got one of the listed errors
SET @@global.slave_type_conversions = ON;
Got one of the listed errors
SET @@global.slave_type_conversions = 'x';
Got one of the listed errors
SET @@global.slave_type_conversions = NULL;
Got one of the listed errors
SET @@global.slave_type_conversions = 'FOO_BAR';
Got one of the listed errors
SET @@global.slave_type_conversions = -1;
Got one of the listed errors
SET @@global.slave_type_conversions = 16;
Got one of the listed errors
# Restore default
SET @@global.slave_type_conversions = DEFAULT;
Warnings:
Warning	1287	'@@slave_type_conversions' is deprecated and will be removed in a future release. Please use replica_type_conversions instead.
include/assert.inc [@@global.slave_type_conversions should be ]
include/assert.inc [slave_type_conversions should be  in P_S.global_variables]
include/assert.inc [@@global.replica_type_conversions should be ]
include/assert.inc [replica_type_conversions should be  in P_S.global_variables]
==== Testing SET @@session.slave_type_conversions [invalid scope] ====
SET @@session.slave_type_conversions = DEFAULT;
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_type_conversions = DEFAULT;
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_type_conversions = ALL_LOSSY;
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_type_conversions = ALL_LOSSY;
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_type_conversions = 'ALL_NON_LOSSY';
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_type_conversions = 'ALL_NON_LOSSY';
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_type_conversions = '';
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_type_conversions = '';
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_type_conversions = 'ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED';
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_type_conversions = 'ALL_LOSSY,ALL_NON_LOSSY,ALL_UNSIGNED,ALL_SIGNED';
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_type_conversions = 0;
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_type_conversions = 0;
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_type_conversions = 8;
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_type_conversions = 8;
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.slave_type_conversions = 15;
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SET @@slave_type_conversions = 15;
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.slave_type_conversions;
ERROR HY000: Variable 'slave_type_conversions' is a GLOBAL variable
SELECT @@slave_type_conversions;
@@slave_type_conversions

Warnings:
Warning	1287	'@@slave_type_conversions' is deprecated and will be removed in a future release. Please use replica_type_conversions instead.
include/assert.inc [Variable slave_type_conversions should exist in performance_schema.session_variables]
sysvar:
- name=rpl_stop_replica_timeout
- alias=rpl_stop_slave_timeout
- invalid=[1.5, "ON", "'x'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=2
- max=31536000
- block_size=1
- default=31536000
- values=[100, 200, 3]
==== Testing SET @@global.rpl_stop_replica_timeout [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.rpl_stop_replica_timeout should be 31536000]
include/assert.inc [rpl_stop_replica_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_slave_timeout should be 31536000]
include/assert.inc [rpl_stop_slave_timeout should be 31536000 in P_S.global_variables]
# Valid values for variable
SET @@global.rpl_stop_replica_timeout=100;
include/assert.inc [@@global.rpl_stop_replica_timeout should be 100]
include/assert.inc [rpl_stop_replica_timeout should be 100 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_slave_timeout should be 100]
include/assert.inc [rpl_stop_slave_timeout should be 100 in P_S.global_variables]
SET @@global.rpl_stop_replica_timeout=200;
include/assert.inc [@@global.rpl_stop_replica_timeout should be 200]
include/assert.inc [rpl_stop_replica_timeout should be 200 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_slave_timeout should be 200]
include/assert.inc [rpl_stop_slave_timeout should be 200 in P_S.global_variables]
SET @@global.rpl_stop_replica_timeout=3;
include/assert.inc [@@global.rpl_stop_replica_timeout should be 3]
include/assert.inc [rpl_stop_replica_timeout should be 3 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_slave_timeout should be 3]
include/assert.inc [rpl_stop_slave_timeout should be 3 in P_S.global_variables]
# Setting default value for variable
SET @@global.rpl_stop_replica_timeout = DEFAULT;
include/assert.inc [@@global.rpl_stop_replica_timeout should be 31536000]
include/assert.inc [rpl_stop_replica_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_slave_timeout should be 31536000]
include/assert.inc [rpl_stop_slave_timeout should be 31536000 in P_S.global_variables]
# Minimum value
SET @@global.rpl_stop_replica_timeout = 2;
include/assert.inc [@@global.rpl_stop_replica_timeout should be 2]
include/assert.inc [rpl_stop_replica_timeout should be 2 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_slave_timeout should be 2]
include/assert.inc [rpl_stop_slave_timeout should be 2 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.rpl_stop_replica_timeout = 1;
Warnings:
Warning	1292	Truncated incorrect rpl_stop_replica_timeout value: '1'
include/assert.inc [@@global.rpl_stop_replica_timeout should be 2]
include/assert.inc [rpl_stop_replica_timeout should be 2 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_slave_timeout should be 2]
include/assert.inc [rpl_stop_slave_timeout should be 2 in P_S.global_variables]
# Maximum value
SET @@global.rpl_stop_replica_timeout = 31536000;
include/assert.inc [@@global.rpl_stop_replica_timeout should be 31536000]
include/assert.inc [rpl_stop_replica_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_slave_timeout should be 31536000]
include/assert.inc [rpl_stop_slave_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.rpl_stop_replica_timeout = 31536001;
Warnings:
Warning	1292	Truncated incorrect rpl_stop_replica_timeout value: '31536001'
include/assert.inc [@@global.rpl_stop_replica_timeout should be 31536000]
include/assert.inc [rpl_stop_replica_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_slave_timeout should be 31536000]
include/assert.inc [rpl_stop_slave_timeout should be 31536000 in P_S.global_variables]
# Invalid values
SET @@global.rpl_stop_replica_timeout = 1.5;
Got one of the listed errors
SET @@global.rpl_stop_replica_timeout = ON;
Got one of the listed errors
SET @@global.rpl_stop_replica_timeout = 'x';
Got one of the listed errors
SET @@global.rpl_stop_replica_timeout = NULL;
Got one of the listed errors
# Restore default
SET @@global.rpl_stop_replica_timeout = DEFAULT;
include/assert.inc [@@global.rpl_stop_replica_timeout should be 31536000]
include/assert.inc [rpl_stop_replica_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_slave_timeout should be 31536000]
include/assert.inc [rpl_stop_slave_timeout should be 31536000 in P_S.global_variables]
==== Testing SET @@session.rpl_stop_replica_timeout [invalid scope] ====
SET @@session.rpl_stop_replica_timeout = DEFAULT;
ERROR HY000: Variable 'rpl_stop_replica_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@rpl_stop_replica_timeout = DEFAULT;
ERROR HY000: Variable 'rpl_stop_replica_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.rpl_stop_replica_timeout = 100;
ERROR HY000: Variable 'rpl_stop_replica_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@rpl_stop_replica_timeout = 100;
ERROR HY000: Variable 'rpl_stop_replica_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.rpl_stop_replica_timeout = 200;
ERROR HY000: Variable 'rpl_stop_replica_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@rpl_stop_replica_timeout = 200;
ERROR HY000: Variable 'rpl_stop_replica_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.rpl_stop_replica_timeout = 3;
ERROR HY000: Variable 'rpl_stop_replica_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@rpl_stop_replica_timeout = 3;
ERROR HY000: Variable 'rpl_stop_replica_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.rpl_stop_replica_timeout;
ERROR HY000: Variable 'rpl_stop_replica_timeout' is a GLOBAL variable
SELECT @@rpl_stop_replica_timeout;
@@rpl_stop_replica_timeout
31536000
include/assert.inc [Variable rpl_stop_replica_timeout should exist in performance_schema.session_variables]
==== Testing SET @@global.rpl_stop_slave_timeout [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.rpl_stop_slave_timeout should be 31536000]
include/assert.inc [rpl_stop_slave_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_replica_timeout should be 31536000]
include/assert.inc [rpl_stop_replica_timeout should be 31536000 in P_S.global_variables]
# Valid values for variable
SET @@global.rpl_stop_slave_timeout=100;
Warnings:
Warning	1287	'@@rpl_stop_slave_timeout' is deprecated and will be removed in a future release. Please use rpl_stop_replica_timeout instead.
include/assert.inc [@@global.rpl_stop_slave_timeout should be 100]
include/assert.inc [rpl_stop_slave_timeout should be 100 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_replica_timeout should be 100]
include/assert.inc [rpl_stop_replica_timeout should be 100 in P_S.global_variables]
SET @@global.rpl_stop_slave_timeout=200;
Warnings:
Warning	1287	'@@rpl_stop_slave_timeout' is deprecated and will be removed in a future release. Please use rpl_stop_replica_timeout instead.
include/assert.inc [@@global.rpl_stop_slave_timeout should be 200]
include/assert.inc [rpl_stop_slave_timeout should be 200 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_replica_timeout should be 200]
include/assert.inc [rpl_stop_replica_timeout should be 200 in P_S.global_variables]
SET @@global.rpl_stop_slave_timeout=3;
Warnings:
Warning	1287	'@@rpl_stop_slave_timeout' is deprecated and will be removed in a future release. Please use rpl_stop_replica_timeout instead.
include/assert.inc [@@global.rpl_stop_slave_timeout should be 3]
include/assert.inc [rpl_stop_slave_timeout should be 3 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_replica_timeout should be 3]
include/assert.inc [rpl_stop_replica_timeout should be 3 in P_S.global_variables]
# Setting default value for variable
SET @@global.rpl_stop_slave_timeout = DEFAULT;
Warnings:
Warning	1287	'@@rpl_stop_slave_timeout' is deprecated and will be removed in a future release. Please use rpl_stop_replica_timeout instead.
include/assert.inc [@@global.rpl_stop_slave_timeout should be 31536000]
include/assert.inc [rpl_stop_slave_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_replica_timeout should be 31536000]
include/assert.inc [rpl_stop_replica_timeout should be 31536000 in P_S.global_variables]
# Minimum value
SET @@global.rpl_stop_slave_timeout = 2;
Warnings:
Warning	1287	'@@rpl_stop_slave_timeout' is deprecated and will be removed in a future release. Please use rpl_stop_replica_timeout instead.
include/assert.inc [@@global.rpl_stop_slave_timeout should be 2]
include/assert.inc [rpl_stop_slave_timeout should be 2 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_replica_timeout should be 2]
include/assert.inc [rpl_stop_replica_timeout should be 2 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.rpl_stop_slave_timeout = 1;
Warnings:
Warning	1287	'@@rpl_stop_slave_timeout' is deprecated and will be removed in a future release. Please use rpl_stop_replica_timeout instead.
Warning	1292	Truncated incorrect rpl_stop_replica_timeout value: '1'
include/assert.inc [@@global.rpl_stop_slave_timeout should be 2]
include/assert.inc [rpl_stop_slave_timeout should be 2 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_replica_timeout should be 2]
include/assert.inc [rpl_stop_replica_timeout should be 2 in P_S.global_variables]
# Maximum value
SET @@global.rpl_stop_slave_timeout = 31536000;
Warnings:
Warning	1287	'@@rpl_stop_slave_timeout' is deprecated and will be removed in a future release. Please use rpl_stop_replica_timeout instead.
include/assert.inc [@@global.rpl_stop_slave_timeout should be 31536000]
include/assert.inc [rpl_stop_slave_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_replica_timeout should be 31536000]
include/assert.inc [rpl_stop_replica_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.rpl_stop_slave_timeout = 31536001;
Warnings:
Warning	1287	'@@rpl_stop_slave_timeout' is deprecated and will be removed in a future release. Please use rpl_stop_replica_timeout instead.
Warning	1292	Truncated incorrect rpl_stop_replica_timeout value: '31536001'
include/assert.inc [@@global.rpl_stop_slave_timeout should be 31536000]
include/assert.inc [rpl_stop_slave_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_replica_timeout should be 31536000]
include/assert.inc [rpl_stop_replica_timeout should be 31536000 in P_S.global_variables]
# Invalid values
SET @@global.rpl_stop_slave_timeout = 1.5;
Got one of the listed errors
SET @@global.rpl_stop_slave_timeout = ON;
Got one of the listed errors
SET @@global.rpl_stop_slave_timeout = 'x';
Got one of the listed errors
SET @@global.rpl_stop_slave_timeout = NULL;
Got one of the listed errors
# Restore default
SET @@global.rpl_stop_slave_timeout = DEFAULT;
Warnings:
Warning	1287	'@@rpl_stop_slave_timeout' is deprecated and will be removed in a future release. Please use rpl_stop_replica_timeout instead.
include/assert.inc [@@global.rpl_stop_slave_timeout should be 31536000]
include/assert.inc [rpl_stop_slave_timeout should be 31536000 in P_S.global_variables]
include/assert.inc [@@global.rpl_stop_replica_timeout should be 31536000]
include/assert.inc [rpl_stop_replica_timeout should be 31536000 in P_S.global_variables]
==== Testing SET @@session.rpl_stop_slave_timeout [invalid scope] ====
SET @@session.rpl_stop_slave_timeout = DEFAULT;
ERROR HY000: Variable 'rpl_stop_slave_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@rpl_stop_slave_timeout = DEFAULT;
ERROR HY000: Variable 'rpl_stop_slave_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.rpl_stop_slave_timeout = 100;
ERROR HY000: Variable 'rpl_stop_slave_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@rpl_stop_slave_timeout = 100;
ERROR HY000: Variable 'rpl_stop_slave_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.rpl_stop_slave_timeout = 200;
ERROR HY000: Variable 'rpl_stop_slave_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@rpl_stop_slave_timeout = 200;
ERROR HY000: Variable 'rpl_stop_slave_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.rpl_stop_slave_timeout = 3;
ERROR HY000: Variable 'rpl_stop_slave_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SET @@rpl_stop_slave_timeout = 3;
ERROR HY000: Variable 'rpl_stop_slave_timeout' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.rpl_stop_slave_timeout;
ERROR HY000: Variable 'rpl_stop_slave_timeout' is a GLOBAL variable
SELECT @@rpl_stop_slave_timeout;
@@rpl_stop_slave_timeout
31536000
Warnings:
Warning	1287	'@@rpl_stop_slave_timeout' is deprecated and will be removed in a future release. Please use rpl_stop_replica_timeout instead.
include/assert.inc [Variable rpl_stop_slave_timeout should exist in performance_schema.session_variables]
sysvar:
- name=source_verify_checksum
- alias=master_verify_checksum
- invalid=[0.1, 2, -1, "''", "'x'", "'TRUE'", "'1'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=
- max=
- block_size=1
- default=0
- values=["ON", "OFF", 1, 0, "TRUE", "FALSE", "'ON'", "'OFF'"]
==== Testing SET @@global.source_verify_checksum [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
# Valid values for variable
SET @@global.source_verify_checksum=ON;
include/assert.inc [@@global.source_verify_checksum should be 1]
include/assert.inc [source_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.master_verify_checksum should be 1]
include/assert.inc [master_verify_checksum should be 1 in P_S.global_variables]
SET @@global.source_verify_checksum=OFF;
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
SET @@global.source_verify_checksum=1;
include/assert.inc [@@global.source_verify_checksum should be 1]
include/assert.inc [source_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.master_verify_checksum should be 1]
include/assert.inc [master_verify_checksum should be 1 in P_S.global_variables]
SET @@global.source_verify_checksum=0;
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
SET @@global.source_verify_checksum=TRUE;
include/assert.inc [@@global.source_verify_checksum should be 1]
include/assert.inc [source_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.master_verify_checksum should be 1]
include/assert.inc [master_verify_checksum should be 1 in P_S.global_variables]
SET @@global.source_verify_checksum=FALSE;
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
SET @@global.source_verify_checksum='ON';
include/assert.inc [@@global.source_verify_checksum should be 1]
include/assert.inc [source_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.master_verify_checksum should be 1]
include/assert.inc [master_verify_checksum should be 1 in P_S.global_variables]
SET @@global.source_verify_checksum='OFF';
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
# Setting default value for variable
SET @@global.source_verify_checksum = DEFAULT;
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
# Invalid values
SET @@global.source_verify_checksum = 0.1;
Got one of the listed errors
SET @@global.source_verify_checksum = 2;
Got one of the listed errors
SET @@global.source_verify_checksum = -1;
Got one of the listed errors
SET @@global.source_verify_checksum = '';
Got one of the listed errors
SET @@global.source_verify_checksum = 'x';
Got one of the listed errors
SET @@global.source_verify_checksum = 'TRUE';
Got one of the listed errors
SET @@global.source_verify_checksum = '1';
Got one of the listed errors
SET @@global.source_verify_checksum = NULL;
Got one of the listed errors
# Restore default
SET @@global.source_verify_checksum = DEFAULT;
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
==== Testing SET @@session.source_verify_checksum [invalid scope] ====
SET @@session.source_verify_checksum = DEFAULT;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@source_verify_checksum = DEFAULT;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.source_verify_checksum = ON;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@source_verify_checksum = ON;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.source_verify_checksum = OFF;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@source_verify_checksum = OFF;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.source_verify_checksum = 1;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@source_verify_checksum = 1;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.source_verify_checksum = 0;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@source_verify_checksum = 0;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.source_verify_checksum = TRUE;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@source_verify_checksum = TRUE;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.source_verify_checksum = FALSE;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@source_verify_checksum = FALSE;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.source_verify_checksum = 'ON';
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@source_verify_checksum = 'ON';
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.source_verify_checksum = 'OFF';
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@source_verify_checksum = 'OFF';
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.source_verify_checksum;
ERROR HY000: Variable 'source_verify_checksum' is a GLOBAL variable
SELECT @@source_verify_checksum;
@@source_verify_checksum
0
include/assert.inc [Variable source_verify_checksum should exist in performance_schema.session_variables]
==== Testing SET @@global.master_verify_checksum [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
# Valid values for variable
SET @@global.master_verify_checksum=ON;
Warnings:
Warning	1287	'@@master_verify_checksum' is deprecated and will be removed in a future release. Please use source_verify_checksum instead.
include/assert.inc [@@global.master_verify_checksum should be 1]
include/assert.inc [master_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.source_verify_checksum should be 1]
include/assert.inc [source_verify_checksum should be 1 in P_S.global_variables]
SET @@global.master_verify_checksum=OFF;
Warnings:
Warning	1287	'@@master_verify_checksum' is deprecated and will be removed in a future release. Please use source_verify_checksum instead.
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
SET @@global.master_verify_checksum=1;
Warnings:
Warning	1287	'@@master_verify_checksum' is deprecated and will be removed in a future release. Please use source_verify_checksum instead.
include/assert.inc [@@global.master_verify_checksum should be 1]
include/assert.inc [master_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.source_verify_checksum should be 1]
include/assert.inc [source_verify_checksum should be 1 in P_S.global_variables]
SET @@global.master_verify_checksum=0;
Warnings:
Warning	1287	'@@master_verify_checksum' is deprecated and will be removed in a future release. Please use source_verify_checksum instead.
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
SET @@global.master_verify_checksum=TRUE;
Warnings:
Warning	1287	'@@master_verify_checksum' is deprecated and will be removed in a future release. Please use source_verify_checksum instead.
include/assert.inc [@@global.master_verify_checksum should be 1]
include/assert.inc [master_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.source_verify_checksum should be 1]
include/assert.inc [source_verify_checksum should be 1 in P_S.global_variables]
SET @@global.master_verify_checksum=FALSE;
Warnings:
Warning	1287	'@@master_verify_checksum' is deprecated and will be removed in a future release. Please use source_verify_checksum instead.
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
SET @@global.master_verify_checksum='ON';
Warnings:
Warning	1287	'@@master_verify_checksum' is deprecated and will be removed in a future release. Please use source_verify_checksum instead.
include/assert.inc [@@global.master_verify_checksum should be 1]
include/assert.inc [master_verify_checksum should be 1 in P_S.global_variables]
include/assert.inc [@@global.source_verify_checksum should be 1]
include/assert.inc [source_verify_checksum should be 1 in P_S.global_variables]
SET @@global.master_verify_checksum='OFF';
Warnings:
Warning	1287	'@@master_verify_checksum' is deprecated and will be removed in a future release. Please use source_verify_checksum instead.
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
# Setting default value for variable
SET @@global.master_verify_checksum = DEFAULT;
Warnings:
Warning	1287	'@@master_verify_checksum' is deprecated and will be removed in a future release. Please use source_verify_checksum instead.
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
# Invalid values
SET @@global.master_verify_checksum = 0.1;
Got one of the listed errors
SET @@global.master_verify_checksum = 2;
Got one of the listed errors
SET @@global.master_verify_checksum = -1;
Got one of the listed errors
SET @@global.master_verify_checksum = '';
Got one of the listed errors
SET @@global.master_verify_checksum = 'x';
Got one of the listed errors
SET @@global.master_verify_checksum = 'TRUE';
Got one of the listed errors
SET @@global.master_verify_checksum = '1';
Got one of the listed errors
SET @@global.master_verify_checksum = NULL;
Got one of the listed errors
# Restore default
SET @@global.master_verify_checksum = DEFAULT;
Warnings:
Warning	1287	'@@master_verify_checksum' is deprecated and will be removed in a future release. Please use source_verify_checksum instead.
include/assert.inc [@@global.master_verify_checksum should be 0]
include/assert.inc [master_verify_checksum should be 0 in P_S.global_variables]
include/assert.inc [@@global.source_verify_checksum should be 0]
include/assert.inc [source_verify_checksum should be 0 in P_S.global_variables]
==== Testing SET @@session.master_verify_checksum [invalid scope] ====
SET @@session.master_verify_checksum = DEFAULT;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@master_verify_checksum = DEFAULT;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.master_verify_checksum = ON;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@master_verify_checksum = ON;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.master_verify_checksum = OFF;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@master_verify_checksum = OFF;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.master_verify_checksum = 1;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@master_verify_checksum = 1;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.master_verify_checksum = 0;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@master_verify_checksum = 0;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.master_verify_checksum = TRUE;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@master_verify_checksum = TRUE;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.master_verify_checksum = FALSE;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@master_verify_checksum = FALSE;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.master_verify_checksum = 'ON';
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@master_verify_checksum = 'ON';
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.master_verify_checksum = 'OFF';
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SET @@master_verify_checksum = 'OFF';
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.master_verify_checksum;
ERROR HY000: Variable 'master_verify_checksum' is a GLOBAL variable
SELECT @@master_verify_checksum;
@@master_verify_checksum
0
Warnings:
Warning	1287	'@@master_verify_checksum' is deprecated and will be removed in a future release. Please use source_verify_checksum instead.
include/assert.inc [Variable master_verify_checksum should exist in performance_schema.session_variables]
sysvar:
- name=sql_replica_skip_counter
- alias=sql_slave_skip_counter
- invalid=[1.5, "ON", "'x'", "NULL"]
- global=1
- session=0
- cmdline=0
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=0
- max=4294967295
- block_size=1
- default=0
- values=[1, 2, 12323]
==== Testing SET @@global.sql_replica_skip_counter [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.sql_replica_skip_counter should be 0]
include/assert.inc [sql_replica_skip_counter should be 0 in P_S.global_variables]
include/assert.inc [@@global.sql_slave_skip_counter should be 0]
include/assert.inc [sql_slave_skip_counter should be 0 in P_S.global_variables]
# Valid values for variable
SET @@global.sql_replica_skip_counter=1;
include/assert.inc [@@global.sql_replica_skip_counter should be 1]
include/assert.inc [sql_replica_skip_counter should be 1 in P_S.global_variables]
include/assert.inc [@@global.sql_slave_skip_counter should be 1]
include/assert.inc [sql_slave_skip_counter should be 1 in P_S.global_variables]
SET @@global.sql_replica_skip_counter=2;
include/assert.inc [@@global.sql_replica_skip_counter should be 2]
include/assert.inc [sql_replica_skip_counter should be 2 in P_S.global_variables]
include/assert.inc [@@global.sql_slave_skip_counter should be 2]
include/assert.inc [sql_slave_skip_counter should be 2 in P_S.global_variables]
SET @@global.sql_replica_skip_counter=12323;
include/assert.inc [@@global.sql_replica_skip_counter should be 12323]
include/assert.inc [sql_replica_skip_counter should be 12323 in P_S.global_variables]
include/assert.inc [@@global.sql_slave_skip_counter should be 12323]
include/assert.inc [sql_slave_skip_counter should be 12323 in P_S.global_variables]
# Setting default value for variable
SET @@global.sql_replica_skip_counter = DEFAULT;
include/assert.inc [@@global.sql_replica_skip_counter should be 0]
include/assert.inc [sql_replica_skip_counter should be 0 in P_S.global_variables]
include/assert.inc [@@global.sql_slave_skip_counter should be 0]
include/assert.inc [sql_slave_skip_counter should be 0 in P_S.global_variables]
# Minimum value
SET @@global.sql_replica_skip_counter = 0;
include/assert.inc [@@global.sql_replica_skip_counter should be 0]
include/assert.inc [sql_replica_skip_counter should be 0 in P_S.global_variables]
include/assert.inc [@@global.sql_slave_skip_counter should be 0]
include/assert.inc [sql_slave_skip_counter should be 0 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.sql_replica_skip_counter = -1;
Warnings:
Warning	1292	Truncated incorrect sql_replica_skip_counter value: '-1'
include/assert.inc [@@global.sql_replica_skip_counter should be 0]
include/assert.inc [sql_replica_skip_counter should be 0 in P_S.global_variables]
include/assert.inc [@@global.sql_slave_skip_counter should be 0]
include/assert.inc [sql_slave_skip_counter should be 0 in P_S.global_variables]
# Maximum value
SET @@global.sql_replica_skip_counter = 4294967295;
include/assert.inc [@@global.sql_replica_skip_counter should be 4294967295]
include/assert.inc [sql_replica_skip_counter should be 4294967295 in P_S.global_variables]
include/assert.inc [@@global.sql_slave_skip_counter should be 4294967295]
include/assert.inc [sql_slave_skip_counter should be 4294967295 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.sql_replica_skip_counter = 4294967296;
Warnings:
Warning	1292	Truncated incorrect sql_replica_skip_counter value: '4294967296'
include/assert.inc [@@global.sql_replica_skip_counter should be 4294967295]
include/assert.inc [sql_replica_skip_counter should be 4294967295 in P_S.global_variables]
include/assert.inc [@@global.sql_slave_skip_counter should be 4294967295]
include/assert.inc [sql_slave_skip_counter should be 4294967295 in P_S.global_variables]
# Invalid values
SET @@global.sql_replica_skip_counter = 1.5;
Got one of the listed errors
SET @@global.sql_replica_skip_counter = ON;
Got one of the listed errors
SET @@global.sql_replica_skip_counter = 'x';
Got one of the listed errors
SET @@global.sql_replica_skip_counter = NULL;
Got one of the listed errors
# Restore default
SET @@global.sql_replica_skip_counter = DEFAULT;
include/assert.inc [@@global.sql_replica_skip_counter should be 0]
include/assert.inc [sql_replica_skip_counter should be 0 in P_S.global_variables]
include/assert.inc [@@global.sql_slave_skip_counter should be 0]
include/assert.inc [sql_slave_skip_counter should be 0 in P_S.global_variables]
==== Testing SET @@session.sql_replica_skip_counter [invalid scope] ====
SET @@session.sql_replica_skip_counter = DEFAULT;
ERROR HY000: Variable 'sql_replica_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sql_replica_skip_counter = DEFAULT;
ERROR HY000: Variable 'sql_replica_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.sql_replica_skip_counter = 1;
ERROR HY000: Variable 'sql_replica_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sql_replica_skip_counter = 1;
ERROR HY000: Variable 'sql_replica_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.sql_replica_skip_counter = 2;
ERROR HY000: Variable 'sql_replica_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sql_replica_skip_counter = 2;
ERROR HY000: Variable 'sql_replica_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.sql_replica_skip_counter = 12323;
ERROR HY000: Variable 'sql_replica_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sql_replica_skip_counter = 12323;
ERROR HY000: Variable 'sql_replica_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.sql_replica_skip_counter;
ERROR HY000: Variable 'sql_replica_skip_counter' is a GLOBAL variable
SELECT @@sql_replica_skip_counter;
@@sql_replica_skip_counter
0
include/assert.inc [Variable sql_replica_skip_counter should exist in performance_schema.session_variables]
==== Testing SET @@global.sql_slave_skip_counter [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.sql_slave_skip_counter should be 0]
include/assert.inc [sql_slave_skip_counter should be 0 in P_S.global_variables]
include/assert.inc [@@global.sql_replica_skip_counter should be 0]
include/assert.inc [sql_replica_skip_counter should be 0 in P_S.global_variables]
# Valid values for variable
SET @@global.sql_slave_skip_counter=1;
Warnings:
Warning	1287	'@@sql_slave_skip_counter' is deprecated and will be removed in a future release. Please use sql_replica_skip_counter instead.
include/assert.inc [@@global.sql_slave_skip_counter should be 1]
include/assert.inc [sql_slave_skip_counter should be 1 in P_S.global_variables]
include/assert.inc [@@global.sql_replica_skip_counter should be 1]
include/assert.inc [sql_replica_skip_counter should be 1 in P_S.global_variables]
SET @@global.sql_slave_skip_counter=2;
Warnings:
Warning	1287	'@@sql_slave_skip_counter' is deprecated and will be removed in a future release. Please use sql_replica_skip_counter instead.
include/assert.inc [@@global.sql_slave_skip_counter should be 2]
include/assert.inc [sql_slave_skip_counter should be 2 in P_S.global_variables]
include/assert.inc [@@global.sql_replica_skip_counter should be 2]
include/assert.inc [sql_replica_skip_counter should be 2 in P_S.global_variables]
SET @@global.sql_slave_skip_counter=12323;
Warnings:
Warning	1287	'@@sql_slave_skip_counter' is deprecated and will be removed in a future release. Please use sql_replica_skip_counter instead.
include/assert.inc [@@global.sql_slave_skip_counter should be 12323]
include/assert.inc [sql_slave_skip_counter should be 12323 in P_S.global_variables]
include/assert.inc [@@global.sql_replica_skip_counter should be 12323]
include/assert.inc [sql_replica_skip_counter should be 12323 in P_S.global_variables]
# Setting default value for variable
SET @@global.sql_slave_skip_counter = DEFAULT;
Warnings:
Warning	1287	'@@sql_slave_skip_counter' is deprecated and will be removed in a future release. Please use sql_replica_skip_counter instead.
include/assert.inc [@@global.sql_slave_skip_counter should be 0]
include/assert.inc [sql_slave_skip_counter should be 0 in P_S.global_variables]
include/assert.inc [@@global.sql_replica_skip_counter should be 0]
include/assert.inc [sql_replica_skip_counter should be 0 in P_S.global_variables]
# Minimum value
SET @@global.sql_slave_skip_counter = 0;
Warnings:
Warning	1287	'@@sql_slave_skip_counter' is deprecated and will be removed in a future release. Please use sql_replica_skip_counter instead.
include/assert.inc [@@global.sql_slave_skip_counter should be 0]
include/assert.inc [sql_slave_skip_counter should be 0 in P_S.global_variables]
include/assert.inc [@@global.sql_replica_skip_counter should be 0]
include/assert.inc [sql_replica_skip_counter should be 0 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.sql_slave_skip_counter = -1;
Warnings:
Warning	1287	'@@sql_slave_skip_counter' is deprecated and will be removed in a future release. Please use sql_replica_skip_counter instead.
Warning	1292	Truncated incorrect sql_replica_skip_counter value: '-1'
include/assert.inc [@@global.sql_slave_skip_counter should be 0]
include/assert.inc [sql_slave_skip_counter should be 0 in P_S.global_variables]
include/assert.inc [@@global.sql_replica_skip_counter should be 0]
include/assert.inc [sql_replica_skip_counter should be 0 in P_S.global_variables]
# Maximum value
SET @@global.sql_slave_skip_counter = 4294967295;
Warnings:
Warning	1287	'@@sql_slave_skip_counter' is deprecated and will be removed in a future release. Please use sql_replica_skip_counter instead.
include/assert.inc [@@global.sql_slave_skip_counter should be 4294967295]
include/assert.inc [sql_slave_skip_counter should be 4294967295 in P_S.global_variables]
include/assert.inc [@@global.sql_replica_skip_counter should be 4294967295]
include/assert.inc [sql_replica_skip_counter should be 4294967295 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.sql_slave_skip_counter = 4294967296;
Warnings:
Warning	1287	'@@sql_slave_skip_counter' is deprecated and will be removed in a future release. Please use sql_replica_skip_counter instead.
Warning	1292	Truncated incorrect sql_replica_skip_counter value: '4294967296'
include/assert.inc [@@global.sql_slave_skip_counter should be 4294967295]
include/assert.inc [sql_slave_skip_counter should be 4294967295 in P_S.global_variables]
include/assert.inc [@@global.sql_replica_skip_counter should be 4294967295]
include/assert.inc [sql_replica_skip_counter should be 4294967295 in P_S.global_variables]
# Invalid values
SET @@global.sql_slave_skip_counter = 1.5;
Got one of the listed errors
SET @@global.sql_slave_skip_counter = ON;
Got one of the listed errors
SET @@global.sql_slave_skip_counter = 'x';
Got one of the listed errors
SET @@global.sql_slave_skip_counter = NULL;
Got one of the listed errors
# Restore default
SET @@global.sql_slave_skip_counter = DEFAULT;
Warnings:
Warning	1287	'@@sql_slave_skip_counter' is deprecated and will be removed in a future release. Please use sql_replica_skip_counter instead.
include/assert.inc [@@global.sql_slave_skip_counter should be 0]
include/assert.inc [sql_slave_skip_counter should be 0 in P_S.global_variables]
include/assert.inc [@@global.sql_replica_skip_counter should be 0]
include/assert.inc [sql_replica_skip_counter should be 0 in P_S.global_variables]
==== Testing SET @@session.sql_slave_skip_counter [invalid scope] ====
SET @@session.sql_slave_skip_counter = DEFAULT;
ERROR HY000: Variable 'sql_slave_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sql_slave_skip_counter = DEFAULT;
ERROR HY000: Variable 'sql_slave_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.sql_slave_skip_counter = 1;
ERROR HY000: Variable 'sql_slave_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sql_slave_skip_counter = 1;
ERROR HY000: Variable 'sql_slave_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.sql_slave_skip_counter = 2;
ERROR HY000: Variable 'sql_slave_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sql_slave_skip_counter = 2;
ERROR HY000: Variable 'sql_slave_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.sql_slave_skip_counter = 12323;
ERROR HY000: Variable 'sql_slave_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sql_slave_skip_counter = 12323;
ERROR HY000: Variable 'sql_slave_skip_counter' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.sql_slave_skip_counter;
ERROR HY000: Variable 'sql_slave_skip_counter' is a GLOBAL variable
SELECT @@sql_slave_skip_counter;
@@sql_slave_skip_counter
0
Warnings:
Warning	1287	'@@sql_slave_skip_counter' is deprecated and will be removed in a future release. Please use sql_replica_skip_counter instead.
include/assert.inc [Variable sql_slave_skip_counter should exist in performance_schema.session_variables]
sysvar:
- name=sync_source_info
- alias=sync_master_info
- invalid=[1.5, "ON", "'x'", "NULL"]
- global=1
- session=0
- cmdline=1
- persist=1
- persist_as_readonly=0
- dynamic=1
- min=0
- max=4294967295
- block_size=1
- default=10000
- values=[1, 999, 123456]
==== Testing SET @@global.sync_source_info [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.sync_source_info should be 10000]
include/assert.inc [sync_source_info should be 10000 in P_S.global_variables]
include/assert.inc [@@global.sync_master_info should be 10000]
include/assert.inc [sync_master_info should be 10000 in P_S.global_variables]
# Valid values for variable
SET @@global.sync_source_info=1;
include/assert.inc [@@global.sync_source_info should be 1]
include/assert.inc [sync_source_info should be 1 in P_S.global_variables]
include/assert.inc [@@global.sync_master_info should be 1]
include/assert.inc [sync_master_info should be 1 in P_S.global_variables]
SET @@global.sync_source_info=999;
include/assert.inc [@@global.sync_source_info should be 999]
include/assert.inc [sync_source_info should be 999 in P_S.global_variables]
include/assert.inc [@@global.sync_master_info should be 999]
include/assert.inc [sync_master_info should be 999 in P_S.global_variables]
SET @@global.sync_source_info=123456;
include/assert.inc [@@global.sync_source_info should be 123456]
include/assert.inc [sync_source_info should be 123456 in P_S.global_variables]
include/assert.inc [@@global.sync_master_info should be 123456]
include/assert.inc [sync_master_info should be 123456 in P_S.global_variables]
# Setting default value for variable
SET @@global.sync_source_info = DEFAULT;
include/assert.inc [@@global.sync_source_info should be 10000]
include/assert.inc [sync_source_info should be 10000 in P_S.global_variables]
include/assert.inc [@@global.sync_master_info should be 10000]
include/assert.inc [sync_master_info should be 10000 in P_S.global_variables]
# Minimum value
SET @@global.sync_source_info = 0;
include/assert.inc [@@global.sync_source_info should be 0]
include/assert.inc [sync_source_info should be 0 in P_S.global_variables]
include/assert.inc [@@global.sync_master_info should be 0]
include/assert.inc [sync_master_info should be 0 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.sync_source_info = -1;
Warnings:
Warning	1292	Truncated incorrect sync_source_info value: '-1'
include/assert.inc [@@global.sync_source_info should be 0]
include/assert.inc [sync_source_info should be 0 in P_S.global_variables]
include/assert.inc [@@global.sync_master_info should be 0]
include/assert.inc [sync_master_info should be 0 in P_S.global_variables]
# Maximum value
SET @@global.sync_source_info = 4294967295;
include/assert.inc [@@global.sync_source_info should be 4294967295]
include/assert.inc [sync_source_info should be 4294967295 in P_S.global_variables]
include/assert.inc [@@global.sync_master_info should be 4294967295]
include/assert.inc [sync_master_info should be 4294967295 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.sync_source_info = 4294967296;
Warnings:
Warning	1292	Truncated incorrect sync_source_info value: '4294967296'
include/assert.inc [@@global.sync_source_info should be 4294967295]
include/assert.inc [sync_source_info should be 4294967295 in P_S.global_variables]
include/assert.inc [@@global.sync_master_info should be 4294967295]
include/assert.inc [sync_master_info should be 4294967295 in P_S.global_variables]
# Invalid values
SET @@global.sync_source_info = 1.5;
Got one of the listed errors
SET @@global.sync_source_info = ON;
Got one of the listed errors
SET @@global.sync_source_info = 'x';
Got one of the listed errors
SET @@global.sync_source_info = NULL;
Got one of the listed errors
# Restore default
SET @@global.sync_source_info = DEFAULT;
include/assert.inc [@@global.sync_source_info should be 10000]
include/assert.inc [sync_source_info should be 10000 in P_S.global_variables]
include/assert.inc [@@global.sync_master_info should be 10000]
include/assert.inc [sync_master_info should be 10000 in P_S.global_variables]
==== Testing SET @@session.sync_source_info [invalid scope] ====
SET @@session.sync_source_info = DEFAULT;
ERROR HY000: Variable 'sync_source_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sync_source_info = DEFAULT;
ERROR HY000: Variable 'sync_source_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.sync_source_info = 1;
ERROR HY000: Variable 'sync_source_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sync_source_info = 1;
ERROR HY000: Variable 'sync_source_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.sync_source_info = 999;
ERROR HY000: Variable 'sync_source_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sync_source_info = 999;
ERROR HY000: Variable 'sync_source_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.sync_source_info = 123456;
ERROR HY000: Variable 'sync_source_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sync_source_info = 123456;
ERROR HY000: Variable 'sync_source_info' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.sync_source_info;
ERROR HY000: Variable 'sync_source_info' is a GLOBAL variable
SELECT @@sync_source_info;
@@sync_source_info
10000
include/assert.inc [Variable sync_source_info should exist in performance_schema.session_variables]
==== Testing SET @@global.sync_master_info [allowed values, range, invalid values] ====
# Initial value is default
include/assert.inc [@@global.sync_master_info should be 10000]
include/assert.inc [sync_master_info should be 10000 in P_S.global_variables]
include/assert.inc [@@global.sync_source_info should be 10000]
include/assert.inc [sync_source_info should be 10000 in P_S.global_variables]
# Valid values for variable
SET @@global.sync_master_info=1;
Warnings:
Warning	1287	'@@sync_master_info' is deprecated and will be removed in a future release. Please use sync_source_info instead.
include/assert.inc [@@global.sync_master_info should be 1]
include/assert.inc [sync_master_info should be 1 in P_S.global_variables]
include/assert.inc [@@global.sync_source_info should be 1]
include/assert.inc [sync_source_info should be 1 in P_S.global_variables]
SET @@global.sync_master_info=999;
Warnings:
Warning	1287	'@@sync_master_info' is deprecated and will be removed in a future release. Please use sync_source_info instead.
include/assert.inc [@@global.sync_master_info should be 999]
include/assert.inc [sync_master_info should be 999 in P_S.global_variables]
include/assert.inc [@@global.sync_source_info should be 999]
include/assert.inc [sync_source_info should be 999 in P_S.global_variables]
SET @@global.sync_master_info=123456;
Warnings:
Warning	1287	'@@sync_master_info' is deprecated and will be removed in a future release. Please use sync_source_info instead.
include/assert.inc [@@global.sync_master_info should be 123456]
include/assert.inc [sync_master_info should be 123456 in P_S.global_variables]
include/assert.inc [@@global.sync_source_info should be 123456]
include/assert.inc [sync_source_info should be 123456 in P_S.global_variables]
# Setting default value for variable
SET @@global.sync_master_info = DEFAULT;
Warnings:
Warning	1287	'@@sync_master_info' is deprecated and will be removed in a future release. Please use sync_source_info instead.
include/assert.inc [@@global.sync_master_info should be 10000]
include/assert.inc [sync_master_info should be 10000 in P_S.global_variables]
include/assert.inc [@@global.sync_source_info should be 10000]
include/assert.inc [sync_source_info should be 10000 in P_S.global_variables]
# Minimum value
SET @@global.sync_master_info = 0;
Warnings:
Warning	1287	'@@sync_master_info' is deprecated and will be removed in a future release. Please use sync_source_info instead.
include/assert.inc [@@global.sync_master_info should be 0]
include/assert.inc [sync_master_info should be 0 in P_S.global_variables]
include/assert.inc [@@global.sync_source_info should be 0]
include/assert.inc [sync_source_info should be 0 in P_S.global_variables]
include/assert.inc [Expected min value should equal min value in performance_schema]
# Less-than-minimum values
SET @@global.sync_master_info = -1;
Warnings:
Warning	1287	'@@sync_master_info' is deprecated and will be removed in a future release. Please use sync_source_info instead.
Warning	1292	Truncated incorrect sync_source_info value: '-1'
include/assert.inc [@@global.sync_master_info should be 0]
include/assert.inc [sync_master_info should be 0 in P_S.global_variables]
include/assert.inc [@@global.sync_source_info should be 0]
include/assert.inc [sync_source_info should be 0 in P_S.global_variables]
# Maximum value
SET @@global.sync_master_info = 4294967295;
Warnings:
Warning	1287	'@@sync_master_info' is deprecated and will be removed in a future release. Please use sync_source_info instead.
include/assert.inc [@@global.sync_master_info should be 4294967295]
include/assert.inc [sync_master_info should be 4294967295 in P_S.global_variables]
include/assert.inc [@@global.sync_source_info should be 4294967295]
include/assert.inc [sync_source_info should be 4294967295 in P_S.global_variables]
include/assert.inc [Expected max value should equal max value in performance_schema]
# Greater-than-maximum values
SET @@global.sync_master_info = 4294967296;
Warnings:
Warning	1287	'@@sync_master_info' is deprecated and will be removed in a future release. Please use sync_source_info instead.
Warning	1292	Truncated incorrect sync_source_info value: '4294967296'
include/assert.inc [@@global.sync_master_info should be 4294967295]
include/assert.inc [sync_master_info should be 4294967295 in P_S.global_variables]
include/assert.inc [@@global.sync_source_info should be 4294967295]
include/assert.inc [sync_source_info should be 4294967295 in P_S.global_variables]
# Invalid values
SET @@global.sync_master_info = 1.5;
Got one of the listed errors
SET @@global.sync_master_info = ON;
Got one of the listed errors
SET @@global.sync_master_info = 'x';
Got one of the listed errors
SET @@global.sync_master_info = NULL;
Got one of the listed errors
# Restore default
SET @@global.sync_master_info = DEFAULT;
Warnings:
Warning	1287	'@@sync_master_info' is deprecated and will be removed in a future release. Please use sync_source_info instead.
include/assert.inc [@@global.sync_master_info should be 10000]
include/assert.inc [sync_master_info should be 10000 in P_S.global_variables]
include/assert.inc [@@global.sync_source_info should be 10000]
include/assert.inc [sync_source_info should be 10000 in P_S.global_variables]
==== Testing SET @@session.sync_master_info [invalid scope] ====
SET @@session.sync_master_info = DEFAULT;
ERROR HY000: Variable 'sync_master_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sync_master_info = DEFAULT;
ERROR HY000: Variable 'sync_master_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.sync_master_info = 1;
ERROR HY000: Variable 'sync_master_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sync_master_info = 1;
ERROR HY000: Variable 'sync_master_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.sync_master_info = 999;
ERROR HY000: Variable 'sync_master_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sync_master_info = 999;
ERROR HY000: Variable 'sync_master_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@session.sync_master_info = 123456;
ERROR HY000: Variable 'sync_master_info' is a GLOBAL variable and should be set with SET GLOBAL
SET @@sync_master_info = 123456;
ERROR HY000: Variable 'sync_master_info' is a GLOBAL variable and should be set with SET GLOBAL
SELECT @@session.sync_master_info;
ERROR HY000: Variable 'sync_master_info' is a GLOBAL variable
SELECT @@sync_master_info;
@@sync_master_info
10000
Warnings:
Warning	1287	'@@sync_master_info' is deprecated and will be removed in a future release. Please use sync_source_info instead.
include/assert.inc [Variable sync_master_info should exist in performance_schema.session_variables]
#### Clean up ####
