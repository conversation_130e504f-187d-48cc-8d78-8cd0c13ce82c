/*****************************************************************************

Copyright (c) 2012, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/

/** @file include/sync0policy.ic
 Policy for mutexes.

 Created 2012-08-21 Sunny Bains.
 ***********************************************************************/

#include "sync0debug.h"

template <typename Mutex>
std::string GenericPolicy<Mutex>::to_string() const {
  return (sync_mutex_to_string(get_id(), sync_file_created_get(this)));
}

template <typename Mutex>
std::string BlockMutexPolicy<Mutex>::to_string() const {
  /* I don't think it makes sense to keep track of the file name
  and line number for each block mutex. Too much of overhead. Use the
  latch id to figure out the location from the source. */
  return (sync_mutex_to_string(get_id(), "buf0buf.cc:0"));
}

#ifdef UNIV_DEBUG

template <typename Mutex>
void MutexDebug<Mutex>::init(latch_id_t id) UNIV_NOTHROW {
  m_context.m_id = id;

  m_context.release();

  m_magic_n = MUTEX_MAGIC_N;
}

template <typename Mutex>
void MutexDebug<Mutex>::enter(const Mutex *mutex, const char *name,
                              ulint line) UNIV_NOTHROW {
  ut_ad(!is_owned());
  ut_ad(m_magic_n == MUTEX_MAGIC_N);

  Context context(m_context.get_id());

  context.locked(mutex, name, line);

  /* Check for latch order violation. */

  sync_check_lock_validate(&context);
}

template <typename Mutex>
void MutexDebug<Mutex>::locked(const Mutex *mutex, const char *name,
                               ulint line) UNIV_NOTHROW {
  ut_ad(!is_owned());
  ut_ad(m_context.m_thread_id == std::thread::id{});

  m_context.locked(mutex, name, line);

  sync_check_lock_granted(&m_context);
}

template <typename Mutex>
void MutexDebug<Mutex>::release(const Mutex *) UNIV_NOTHROW {
  ut_ad(is_owned());

  m_context.release();

  sync_check_unlock(&m_context);
}

#endif /* UNIV_DEBUG */
