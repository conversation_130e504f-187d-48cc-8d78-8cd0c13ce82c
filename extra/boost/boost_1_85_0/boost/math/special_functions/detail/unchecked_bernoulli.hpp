
///////////////////////////////////////////////////////////////////////////////
//  Copyright 2013 <PERSON><PERSON>
//  Copyright 2013 <PERSON>
//  Copyright 2013 <PERSON>
//  Copyright 2013 <PERSON>
//  Distributed under the Boost
//  Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_MATH_UNCHECKED_BERNOULLI_HPP
#define BOOST_MATH_UNCHECKED_BERNOULLI_HPP

#include <limits>
#include <type_traits>
#include <array>
#include <cmath>
#include <cstdint>
#include <boost/math/policies/error_handling.hpp>
#include <boost/math/constants/constants.hpp>
#include <boost/math/special_functions/math_fwd.hpp>

namespace boost { namespace math { 
   
namespace detail {

template <unsigned N>
struct max_bernoulli_index
{
   static constexpr unsigned value = 17;
};

template <>
struct max_bernoulli_index<1>
{
   static constexpr unsigned value = 32;
};

template <>
struct max_bernoulli_index<2>
{
   static constexpr unsigned value = 129;
};

template <>
struct max_bernoulli_index<3>
{
   static constexpr unsigned value = 1156;
};

template <>
struct max_bernoulli_index<4>
{
   static constexpr unsigned value = 11;
};

template <class T>
struct bernoulli_imp_variant
{
   static constexpr unsigned value = 
      (std::numeric_limits<T>::max_exponent == 128)
      && (std::numeric_limits<T>::radix == 2)
      && (std::numeric_limits<T>::digits <= std::numeric_limits<float>::digits)
      && (std::is_convertible<float, T>::value) ? 1 :
      (
         (std::numeric_limits<T>::max_exponent == 1024)
         && (std::numeric_limits<T>::radix == 2)
         && (std::numeric_limits<T>::digits <= std::numeric_limits<double>::digits)
         && (std::is_convertible<double, T>::value) ? 2 :
         (
            (std::numeric_limits<T>::max_exponent == 16384)
            && (std::numeric_limits<T>::radix == 2)
            && (std::numeric_limits<T>::digits <= std::numeric_limits<long double>::digits)
            && (std::is_convertible<long double, T>::value) ? 3 : (!std::is_convertible<std::int64_t, T>::value ? 4 : 0)
         )
      );
};

} // namespace detail

template <class T>
struct max_bernoulli_b2n : public detail::max_bernoulli_index<detail::bernoulli_imp_variant<T>::value>{};

namespace detail {
   //
   // See https://github.com/boostorg/math/issues/923
   // for rationale behind using struct's here for constexpr data.
   //
   template <class T, int>
   struct unchecked_bernoulli_data;

   template <class T>
   struct unchecked_bernoulli_data<T, 0>
   {
#ifdef BOOST_MATH_HAVE_CONSTEXPR_TABLES
      static constexpr std::array<std::int64_t, 1 + max_bernoulli_b2n<T>::value> numerators =
      { {
         std::int64_t(+1LL),
         std::int64_t(+1LL),
         std::int64_t(-1LL),
         std::int64_t(+1LL),
         std::int64_t(-1LL),
         std::int64_t(+5LL),
         std::int64_t(-691LL),
         std::int64_t(+7LL),
         std::int64_t(-3617LL),
         std::int64_t(+43867LL),
         std::int64_t(-174611LL),
         std::int64_t(+854513LL),
         std::int64_t(-236364091LL),
         std::int64_t(+8553103LL),
         std::int64_t(-23749461029LL),
         std::int64_t(+8615841276005LL),
         std::int64_t(-7709321041217LL),
         std::int64_t(+2577687858367LL)
      } };

      static constexpr std::array<std::int64_t, 1 + max_bernoulli_b2n<T>::value> denominators =
      { {
         std::int64_t(1LL),
         std::int64_t(6LL),
         std::int64_t(30LL),
         std::int64_t(42LL),
         std::int64_t(30LL),
         std::int64_t(66LL),
         std::int64_t(2730LL),
         std::int64_t(6LL),
         std::int64_t(510LL),
         std::int64_t(798LL),
         std::int64_t(330LL),
         std::int64_t(138LL),
         std::int64_t(2730LL),
         std::int64_t(6LL),
         std::int64_t(870LL),
         std::int64_t(14322LL),
         std::int64_t(510LL),
         std::int64_t(6LL)
      } };
#else
      static const std::array<std::int64_t, 1 + max_bernoulli_b2n<T>::value> denominators;
      static const std::array<std::int64_t, 1 + max_bernoulli_b2n<T>::value> numerators;
#endif
   };

#ifdef BOOST_MATH_HAVE_CONSTEXPR_TABLES
   template <class T>
   constexpr std::array<std::int64_t, 1 + max_bernoulli_b2n<T>::value> unchecked_bernoulli_data<T, 0>::numerators;
   template <class T>
   constexpr std::array<std::int64_t, 1 + max_bernoulli_b2n<T>::value> unchecked_bernoulli_data<T, 0>::denominators;
#else
   template <class T>
   const std::array<std::int64_t, 1 + max_bernoulli_b2n<T>::value> unchecked_bernoulli_data<T, 0>::numerators =
   { {
      std::int64_t(+1LL),
      std::int64_t(+1LL),
      std::int64_t(-1LL),
      std::int64_t(+1LL),
      std::int64_t(-1LL),
      std::int64_t(+5LL),
      std::int64_t(-691LL),
      std::int64_t(+7LL),
      std::int64_t(-3617LL),
      std::int64_t(+43867LL),
      std::int64_t(-174611LL),
      std::int64_t(+854513LL),
      std::int64_t(-236364091LL),
      std::int64_t(+8553103LL),
      std::int64_t(-23749461029LL),
      std::int64_t(+8615841276005LL),
      std::int64_t(-7709321041217LL),
      std::int64_t(+2577687858367LL)
   } };

   template <class T>
   const std::array<std::int64_t, 1 + max_bernoulli_b2n<T>::value> unchecked_bernoulli_data<T, 0>::denominators =
   { {
      std::int64_t(1LL),
      std::int64_t(6LL),
      std::int64_t(30LL),
      std::int64_t(42LL),
      std::int64_t(30LL),
      std::int64_t(66LL),
      std::int64_t(2730LL),
      std::int64_t(6LL),
      std::int64_t(510LL),
      std::int64_t(798LL),
      std::int64_t(330LL),
      std::int64_t(138LL),
      std::int64_t(2730LL),
      std::int64_t(6LL),
      std::int64_t(870LL),
      std::int64_t(14322LL),
      std::int64_t(510LL),
      std::int64_t(6LL)
   } };
#endif

   template <class T>
   inline BOOST_MATH_CONSTEXPR_TABLE_FUNCTION T unchecked_bernoulli_imp(std::size_t n, const std::integral_constant<int, 0>&)
   {
      return T(unchecked_bernoulli_data<T, 0>::numerators[n]) / unchecked_bernoulli_data<T, 0>::denominators[n];
   }

   template <class T>
   struct unchecked_bernoulli_data<T, 1>
   {
#ifdef BOOST_MATH_HAVE_CONSTEXPR_TABLES
      static constexpr std::array<float, 1 + max_bernoulli_b2n<T>::value> bernoulli_data =
      { {
         +1.00000000000000000000000000000000000000000F,
         +0.166666666666666666666666666666666666666667F,
         -0.0333333333333333333333333333333333333333333F,
         +0.0238095238095238095238095238095238095238095F,
         -0.0333333333333333333333333333333333333333333F,
         +0.0757575757575757575757575757575757575757576F,
         -0.253113553113553113553113553113553113553114F,
         +1.16666666666666666666666666666666666666667F,
         -7.09215686274509803921568627450980392156863F,
         +54.9711779448621553884711779448621553884712F,
         -529.124242424242424242424242424242424242424F,
         +6192.12318840579710144927536231884057971014F,
         -86580.2531135531135531135531135531135531136F,
         +1.42551716666666666666666666666666666666667e6F,
         -2.72982310678160919540229885057471264367816e7F,
         +6.01580873900642368384303868174835916771401e8F,
         -1.51163157670921568627450980392156862745098e10F,
         +4.29614643061166666666666666666666666666667e11F,
         -1.37116552050883327721590879485616327721591e13F,
         +4.88332318973593166666666666666666666666667e14F,
         -1.92965793419400681486326681448632668144863e16F,
         +8.41693047573682615000553709856035437430786e17F,
         -4.03380718540594554130768115942028985507246e19F,
         +2.11507486380819916056014539007092198581560e21F,
         -1.20866265222965259346027311937082525317819e23F,
         +7.50086674607696436685572007575757575757576e24F,
         -5.03877810148106891413789303052201257861635e26F,
         +3.65287764848181233351104308429711779448622e28F,
         -2.84987693024508822262691464329106781609195e30F,
         +2.38654274996836276446459819192192149717514e32F,
         -2.13999492572253336658107447651910973926742e34F,
         +2.05009757234780975699217330956723102516667e36F,
         -2.09380059113463784090951852900279701847092e38F,
      } };
#else
      static const std::array<float, 1 + max_bernoulli_b2n<T>::value> bernoulli_data;
#endif
   };

#ifdef BOOST_MATH_HAVE_CONSTEXPR_TABLES
   template <class T>
   constexpr std::array<float, 1 + max_bernoulli_b2n<T>::value> unchecked_bernoulli_data<T, 1>::bernoulli_data;
#else
   template <class T>
   const std::array<float, 1 + max_bernoulli_b2n<T>::value> unchecked_bernoulli_data<T, 1>::bernoulli_data =
   { {
      +1.00000000000000000000000000000000000000000F,
      +0.166666666666666666666666666666666666666667F,
      -0.0333333333333333333333333333333333333333333F,
      +0.0238095238095238095238095238095238095238095F,
      -0.0333333333333333333333333333333333333333333F,
      +0.0757575757575757575757575757575757575757576F,
      -0.253113553113553113553113553113553113553114F,
      +1.16666666666666666666666666666666666666667F,
      -7.09215686274509803921568627450980392156863F,
      +54.9711779448621553884711779448621553884712F,
      -529.124242424242424242424242424242424242424F,
      +6192.12318840579710144927536231884057971014F,
      -86580.2531135531135531135531135531135531136F,
      +1.42551716666666666666666666666666666666667e6F,
      -2.72982310678160919540229885057471264367816e7F,
      +6.01580873900642368384303868174835916771401e8F,
      -1.51163157670921568627450980392156862745098e10F,
      +4.29614643061166666666666666666666666666667e11F,
      -1.37116552050883327721590879485616327721591e13F,
      +4.88332318973593166666666666666666666666667e14F,
      -1.92965793419400681486326681448632668144863e16F,
      +8.41693047573682615000553709856035437430786e17F,
      -4.03380718540594554130768115942028985507246e19F,
      +2.11507486380819916056014539007092198581560e21F,
      -1.20866265222965259346027311937082525317819e23F,
      +7.50086674607696436685572007575757575757576e24F,
      -5.03877810148106891413789303052201257861635e26F,
      +3.65287764848181233351104308429711779448622e28F,
      -2.84987693024508822262691464329106781609195e30F,
      +2.38654274996836276446459819192192149717514e32F,
      -2.13999492572253336658107447651910973926742e34F,
      +2.05009757234780975699217330956723102516667e36F,
      -2.09380059113463784090951852900279701847092e38F,
   } };
#endif

   template <class T>
   inline BOOST_MATH_CONSTEXPR_TABLE_FUNCTION T unchecked_bernoulli_imp(std::size_t n, const std::integral_constant<int, 1>&)
   {
      return unchecked_bernoulli_data<T, 1>::bernoulli_data[n];
   }

   template <class T>
   struct unchecked_bernoulli_data<T, 2>
   {
#ifdef BOOST_MATH_HAVE_CONSTEXPR_TABLES
      static constexpr std::array<double, 1 + max_bernoulli_b2n<T>::value> bernoulli_data =
      { {
         +1.00000000000000000000000000000000000000000,
         +0.166666666666666666666666666666666666666667,
         -0.0333333333333333333333333333333333333333333,
         +0.0238095238095238095238095238095238095238095,
         -0.0333333333333333333333333333333333333333333,
         +0.0757575757575757575757575757575757575757576,
         -0.253113553113553113553113553113553113553114,
         +1.16666666666666666666666666666666666666667,
         -7.09215686274509803921568627450980392156863,
         +54.9711779448621553884711779448621553884712,
         -529.124242424242424242424242424242424242424,
         +6192.12318840579710144927536231884057971014,
         -86580.2531135531135531135531135531135531136,
         +1.42551716666666666666666666666666666666667e6,
         -2.72982310678160919540229885057471264367816e7,
         +6.01580873900642368384303868174835916771401e8,
         -1.51163157670921568627450980392156862745098e10,
         +4.29614643061166666666666666666666666666667e11,
         -1.37116552050883327721590879485616327721591e13,
         +4.88332318973593166666666666666666666666667e14,
         -1.92965793419400681486326681448632668144863e16,
         +8.41693047573682615000553709856035437430786e17,
         -4.03380718540594554130768115942028985507246e19,
         +2.11507486380819916056014539007092198581560e21,
         -1.20866265222965259346027311937082525317819e23,
         +7.50086674607696436685572007575757575757576e24,
         -5.03877810148106891413789303052201257861635e26,
         +3.65287764848181233351104308429711779448622e28,
         -2.84987693024508822262691464329106781609195e30,
         +2.38654274996836276446459819192192149717514e32,
         -2.13999492572253336658107447651910973926742e34,
         +2.05009757234780975699217330956723102516667e36,
         -2.09380059113463784090951852900279701847092e38,
         +2.27526964884635155596492603527692645814700e40,
         -2.62577102862395760473030497361582020814490e42,
         +3.21250821027180325182047923042649852435219e44,
         -4.15982781667947109139170744952623589366896e46,
         +5.69206954820352800238834562191210586444805e48,
         -8.21836294197845756922906534686173330145509e50,
         +1.25029043271669930167323398297028955241772e53,
         -2.00155832332483702749253291988132987687242e55,
         +3.36749829153643742333966769033387530162196e57,
         -5.94709705031354477186604968440515408405791e59,
         +1.10119103236279775595641307904376916046305e62,
         -2.13552595452535011886583850190410656789733e64,
         +4.33288969866411924196166130593792062184514e66,
         -9.18855282416693282262005552155018971389604e68,
         +2.03468967763290744934550279902200200659751e71,
         -4.70038339580357310785752555350060606545967e73,
         +1.13180434454842492706751862577339342678904e76,
         -2.83822495706937069592641563364817647382847e78,
         +7.40642489796788506297508271409209841768797e80,
         -2.00964548027566044834656196727153631868673e83,
         +5.66571700508059414457193460305193569614195e85,
         -1.65845111541362169158237133743199123014950e88,
         +5.03688599504923774192894219151801548124424e90,
         -1.58614682376581863693634015729664387827410e93,
         +5.17567436175456269840732406825071225612408e95,
         -1.74889218402171173396900258776181591451415e98,
         +6.11605199949521852558245252642641677807677e100,
         -2.21227769127078349422883234567129324455732e103,
         +8.27227767987709698542210624599845957312047e105,
         -3.19589251114157095835916343691808148735263e108,
         +1.27500822233877929823100243029266798669572e111,
         -5.25009230867741338994028246245651754469199e113,
         +2.23018178942416252098692981988387281437383e116,
         -9.76845219309552044386335133989802393011669e118,
         +4.40983619784529542722726228748131691918758e121,
         -2.05085708864640888397293377275830154864566e124,
         +9.82144332797912771075729696020975210414919e126,
         -4.84126007982088805087891967099634127611305e129,
         +2.45530888014809826097834674040886903996737e132,
         -1.28069268040847475487825132786017857218118e135,
         +6.86761671046685811921018885984644004360924e137,
         -3.78464685819691046949789954163795568144895e140,
         +2.14261012506652915508713231351482720966602e143,
         -1.24567271371836950070196429616376072194583e146,
         +7.43457875510001525436796683940520613117807e148,
         -4.55357953046417048940633332233212748767721e151,
         +2.86121128168588683453638472510172325229190e154,
         -1.84377235520338697276882026536287854875414e157,
         +1.21811545362210466995013165065995213558174e160,
         -8.24821871853141215484818457296893447301419e162,
         +5.72258779378329433296516498142978615918685e165,
         -4.06685305250591047267679693831158655602196e168,
         +2.95960920646420500628752695815851870426379e171,
         -2.20495225651894575090311752273445984836379e174,
         +1.68125970728895998058311525151360665754464e177,
         -1.31167362135569576486452806355817153004431e180,
         +1.04678940094780380821832853929823089643829e183,
         -8.54328935788337077185982546299082774593270e185,
         +7.12878213224865423522884066771438224721245e188,
         -6.08029314555358993000847118686477458461988e191,
         +5.29967764248499239300942910043247266228490e194,
         -4.71942591687458626443646229013379911103761e197,
         +4.29284137914029810894168296541074669045521e200,
         -3.98767449682322074434477655542938795106651e203,
         +3.78197804193588827138944181161393327898220e206,
         -3.66142336836811912436858082151197348755196e209,
         +3.61760902723728623488554609298914089477541e212,
         -3.64707726451913543621383088655499449048682e215,
         +3.75087554364544090983452410104814189306842e218,
         -3.93458672964390282694891288533713429355657e221,
         +4.20882111481900820046571171111494898242731e224,
         -4.59022962206179186559802940573325591059371e227,
         +5.10317257726295759279198185106496768539760e230,
         -5.78227623036569554015377271242917142512200e233,
         +6.67624821678358810322637794412809363451080e236,
         -7.85353076444504163225916259639312444428230e239,
         +9.41068940670587255245443288258762485293948e242,
         -1.14849338734651839938498599206805592548354e246,
         +1.42729587428487856771416320087122499897180e249,
         -1.80595595869093090142285728117654560926719e252,
         +2.32615353076608052161297985184708876161736e255,
         -3.04957517154995947681942819261542593785327e258,
         +4.06858060764339734424012124124937318633684e261,
         -5.52310313219743616252320044093186392324280e264,
         +7.62772793964343924869949690204961215533859e267,
         -1.07155711196978863132793524001065396932667e271,
         +1.53102008959691884453440916153355334355847e274,
         -2.22448916821798346676602348865048510824835e277,
         +3.28626791906901391668189736436895275365183e280,
         -4.93559289559603449020711938191575963496999e283,
         +7.53495712008325067212266049779283956727824e286,
         -1.16914851545841777278088924731655041783900e290,
         +1.84352614678389394126646201597702232396492e293,
         -2.95368261729680829728014917350525183485207e296,
         +4.80793212775015697668878704043264072227967e299,
         -7.95021250458852528538243631671158693036798e302,
         +1.33527841873546338750122832017820518292039e306
      } };
#else
      static const std::array<double, 1 + max_bernoulli_b2n<T>::value> bernoulli_data;
#endif
   };

#ifdef BOOST_MATH_HAVE_CONSTEXPR_TABLES
   template <class T>
   constexpr const std::array<double, 1 + max_bernoulli_b2n<T>::value> unchecked_bernoulli_data<T, 2>::bernoulli_data;
#else
   template <class T>
   const std::array<double, 1 + max_bernoulli_b2n<T>::value> unchecked_bernoulli_data<T, 2>::bernoulli_data =
   { {
      +1.00000000000000000000000000000000000000000,
      +0.166666666666666666666666666666666666666667,
      -0.0333333333333333333333333333333333333333333,
      +0.0238095238095238095238095238095238095238095,
      -0.0333333333333333333333333333333333333333333,
      +0.0757575757575757575757575757575757575757576,
      -0.253113553113553113553113553113553113553114,
      +1.16666666666666666666666666666666666666667,
      -7.09215686274509803921568627450980392156863,
      +54.9711779448621553884711779448621553884712,
      -529.124242424242424242424242424242424242424,
      +6192.12318840579710144927536231884057971014,
      -86580.2531135531135531135531135531135531136,
      +1.42551716666666666666666666666666666666667e6,
      -2.72982310678160919540229885057471264367816e7,
      +6.01580873900642368384303868174835916771401e8,
      -1.51163157670921568627450980392156862745098e10,
      +4.29614643061166666666666666666666666666667e11,
      -1.37116552050883327721590879485616327721591e13,
      +4.88332318973593166666666666666666666666667e14,
      -1.92965793419400681486326681448632668144863e16,
      +8.41693047573682615000553709856035437430786e17,
      -4.03380718540594554130768115942028985507246e19,
      +2.11507486380819916056014539007092198581560e21,
      -1.20866265222965259346027311937082525317819e23,
      +7.50086674607696436685572007575757575757576e24,
      -5.03877810148106891413789303052201257861635e26,
      +3.65287764848181233351104308429711779448622e28,
      -2.84987693024508822262691464329106781609195e30,
      +2.38654274996836276446459819192192149717514e32,
      -2.13999492572253336658107447651910973926742e34,
      +2.05009757234780975699217330956723102516667e36,
      -2.09380059113463784090951852900279701847092e38,
      +2.27526964884635155596492603527692645814700e40,
      -2.62577102862395760473030497361582020814490e42,
      +3.21250821027180325182047923042649852435219e44,
      -4.15982781667947109139170744952623589366896e46,
      +5.69206954820352800238834562191210586444805e48,
      -8.21836294197845756922906534686173330145509e50,
      +1.25029043271669930167323398297028955241772e53,
      -2.00155832332483702749253291988132987687242e55,
      +3.36749829153643742333966769033387530162196e57,
      -5.94709705031354477186604968440515408405791e59,
      +1.10119103236279775595641307904376916046305e62,
      -2.13552595452535011886583850190410656789733e64,
      +4.33288969866411924196166130593792062184514e66,
      -9.18855282416693282262005552155018971389604e68,
      +2.03468967763290744934550279902200200659751e71,
      -4.70038339580357310785752555350060606545967e73,
      +1.13180434454842492706751862577339342678904e76,
      -2.83822495706937069592641563364817647382847e78,
      +7.40642489796788506297508271409209841768797e80,
      -2.00964548027566044834656196727153631868673e83,
      +5.66571700508059414457193460305193569614195e85,
      -1.65845111541362169158237133743199123014950e88,
      +5.03688599504923774192894219151801548124424e90,
      -1.58614682376581863693634015729664387827410e93,
      +5.17567436175456269840732406825071225612408e95,
      -1.74889218402171173396900258776181591451415e98,
      +6.11605199949521852558245252642641677807677e100,
      -2.21227769127078349422883234567129324455732e103,
      +8.27227767987709698542210624599845957312047e105,
      -3.19589251114157095835916343691808148735263e108,
      +1.27500822233877929823100243029266798669572e111,
      -5.25009230867741338994028246245651754469199e113,
      +2.23018178942416252098692981988387281437383e116,
      -9.76845219309552044386335133989802393011669e118,
      +4.40983619784529542722726228748131691918758e121,
      -2.05085708864640888397293377275830154864566e124,
      +9.82144332797912771075729696020975210414919e126,
      -4.84126007982088805087891967099634127611305e129,
      +2.45530888014809826097834674040886903996737e132,
      -1.28069268040847475487825132786017857218118e135,
      +6.86761671046685811921018885984644004360924e137,
      -3.78464685819691046949789954163795568144895e140,
      +2.14261012506652915508713231351482720966602e143,
      -1.24567271371836950070196429616376072194583e146,
      +7.43457875510001525436796683940520613117807e148,
      -4.55357953046417048940633332233212748767721e151,
      +2.86121128168588683453638472510172325229190e154,
      -1.84377235520338697276882026536287854875414e157,
      +1.21811545362210466995013165065995213558174e160,
      -8.24821871853141215484818457296893447301419e162,
      +5.72258779378329433296516498142978615918685e165,
      -4.06685305250591047267679693831158655602196e168,
      +2.95960920646420500628752695815851870426379e171,
      -2.20495225651894575090311752273445984836379e174,
      +1.68125970728895998058311525151360665754464e177,
      -1.31167362135569576486452806355817153004431e180,
      +1.04678940094780380821832853929823089643829e183,
      -8.54328935788337077185982546299082774593270e185,
      +7.12878213224865423522884066771438224721245e188,
      -6.08029314555358993000847118686477458461988e191,
      +5.29967764248499239300942910043247266228490e194,
      -4.71942591687458626443646229013379911103761e197,
      +4.29284137914029810894168296541074669045521e200,
      -3.98767449682322074434477655542938795106651e203,
      +3.78197804193588827138944181161393327898220e206,
      -3.66142336836811912436858082151197348755196e209,
      +3.61760902723728623488554609298914089477541e212,
      -3.64707726451913543621383088655499449048682e215,
      +3.75087554364544090983452410104814189306842e218,
      -3.93458672964390282694891288533713429355657e221,
      +4.20882111481900820046571171111494898242731e224,
      -4.59022962206179186559802940573325591059371e227,
      +5.10317257726295759279198185106496768539760e230,
      -5.78227623036569554015377271242917142512200e233,
      +6.67624821678358810322637794412809363451080e236,
      -7.85353076444504163225916259639312444428230e239,
      +9.41068940670587255245443288258762485293948e242,
      -1.14849338734651839938498599206805592548354e246,
      +1.42729587428487856771416320087122499897180e249,
      -1.80595595869093090142285728117654560926719e252,
      +2.32615353076608052161297985184708876161736e255,
      -3.04957517154995947681942819261542593785327e258,
      +4.06858060764339734424012124124937318633684e261,
      -5.52310313219743616252320044093186392324280e264,
      +7.62772793964343924869949690204961215533859e267,
      -1.07155711196978863132793524001065396932667e271,
      +1.53102008959691884453440916153355334355847e274,
      -2.22448916821798346676602348865048510824835e277,
      +3.28626791906901391668189736436895275365183e280,
      -4.93559289559603449020711938191575963496999e283,
      +7.53495712008325067212266049779283956727824e286,
      -1.16914851545841777278088924731655041783900e290,
      +1.84352614678389394126646201597702232396492e293,
      -2.95368261729680829728014917350525183485207e296,
      +4.80793212775015697668878704043264072227967e299,
      -7.95021250458852528538243631671158693036798e302,
      +1.33527841873546338750122832017820518292039e306
   } };
#endif

   template <class T>
   inline BOOST_MATH_CONSTEXPR_TABLE_FUNCTION T unchecked_bernoulli_imp(std::size_t n, const std::integral_constant<int, 2>&)
   {
      return unchecked_bernoulli_data<T, 2>::bernoulli_data[n];
   }

   template <class T>
   struct unchecked_bernoulli_data<T, 3>
   {
#ifdef BOOST_MATH_HAVE_CONSTEXPR_TABLES
   static constexpr std::array<long double, 1 + max_bernoulli_b2n<T>::value> bernoulli_data =
   { {
      +1.00000000000000000000000000000000000000000L,
      +0.166666666666666666666666666666666666666667L,
      -0.0333333333333333333333333333333333333333333L,
      +0.0238095238095238095238095238095238095238095L,
      -0.0333333333333333333333333333333333333333333L,
      +0.0757575757575757575757575757575757575757576L,
      -0.253113553113553113553113553113553113553114L,
      +1.16666666666666666666666666666666666666667L,
      -7.09215686274509803921568627450980392156863L,
      +54.9711779448621553884711779448621553884712L,
      -529.124242424242424242424242424242424242424L,
      +6192.12318840579710144927536231884057971014L,
      -86580.2531135531135531135531135531135531136L,
      +1.42551716666666666666666666666666666666667E6L,
      -2.72982310678160919540229885057471264367816E7L,
      +6.01580873900642368384303868174835916771401E8L,
      -1.51163157670921568627450980392156862745098E10L,
      +4.29614643061166666666666666666666666666667E11L,
      -1.37116552050883327721590879485616327721591E13L,
      +4.88332318973593166666666666666666666666667E14L,
      -1.92965793419400681486326681448632668144863E16L,
      +8.41693047573682615000553709856035437430786E17L,
      -4.03380718540594554130768115942028985507246E19L,
      +2.11507486380819916056014539007092198581560E21L,
      -1.20866265222965259346027311937082525317819E23L,
      +7.50086674607696436685572007575757575757576E24L,
      -5.03877810148106891413789303052201257861635E26L,
      +3.65287764848181233351104308429711779448622E28L,
      -2.84987693024508822262691464329106781609195E30L,
      +2.38654274996836276446459819192192149717514E32L,
      -2.13999492572253336658107447651910973926742E34L,
      +2.05009757234780975699217330956723102516667E36L,
      -2.09380059113463784090951852900279701847092E38L,
      +2.27526964884635155596492603527692645814700E40L,
      -2.62577102862395760473030497361582020814490E42L,
      +3.21250821027180325182047923042649852435219E44L,
      -4.15982781667947109139170744952623589366896E46L,
      +5.69206954820352800238834562191210586444805E48L,
      -8.21836294197845756922906534686173330145509E50L,
      +1.25029043271669930167323398297028955241772E53L,
      -2.00155832332483702749253291988132987687242E55L,
      +3.36749829153643742333966769033387530162196E57L,
      -5.94709705031354477186604968440515408405791E59L,
      +1.10119103236279775595641307904376916046305E62L,
      -2.13552595452535011886583850190410656789733E64L,
      +4.33288969866411924196166130593792062184514E66L,
      -9.18855282416693282262005552155018971389604E68L,
      +2.03468967763290744934550279902200200659751E71L,
      -4.70038339580357310785752555350060606545967E73L,
      +1.13180434454842492706751862577339342678904E76L,
      -2.83822495706937069592641563364817647382847E78L,
      +7.40642489796788506297508271409209841768797E80L,
      -2.00964548027566044834656196727153631868673E83L,
      +5.66571700508059414457193460305193569614195E85L,
      -1.65845111541362169158237133743199123014950E88L,
      +5.03688599504923774192894219151801548124424E90L,
      -1.58614682376581863693634015729664387827410E93L,
      +5.17567436175456269840732406825071225612408E95L,
      -1.74889218402171173396900258776181591451415E98L,
      +6.11605199949521852558245252642641677807677E100L,
      -2.21227769127078349422883234567129324455732E103L,
      +8.27227767987709698542210624599845957312047E105L,
      -3.19589251114157095835916343691808148735263E108L,
      +1.27500822233877929823100243029266798669572E111L,
      -5.25009230867741338994028246245651754469199E113L,
      +2.23018178942416252098692981988387281437383E116L,
      -9.76845219309552044386335133989802393011669E118L,
      +4.40983619784529542722726228748131691918758E121L,
      -2.05085708864640888397293377275830154864566E124L,
      +9.82144332797912771075729696020975210414919E126L,
      -4.84126007982088805087891967099634127611305E129L,
      +2.45530888014809826097834674040886903996737E132L,
      -1.28069268040847475487825132786017857218118E135L,
      +6.86761671046685811921018885984644004360924E137L,
      -3.78464685819691046949789954163795568144895E140L,
      +2.14261012506652915508713231351482720966602E143L,
      -1.24567271371836950070196429616376072194583E146L,
      +7.43457875510001525436796683940520613117807E148L,
      -4.55357953046417048940633332233212748767721E151L,
      +2.86121128168588683453638472510172325229190E154L,
      -1.84377235520338697276882026536287854875414E157L,
      +1.21811545362210466995013165065995213558174E160L,
      -8.24821871853141215484818457296893447301419E162L,
      +5.72258779378329433296516498142978615918685E165L,
      -4.06685305250591047267679693831158655602196E168L,
      +2.95960920646420500628752695815851870426379E171L,
      -2.20495225651894575090311752273445984836379E174L,
      +1.68125970728895998058311525151360665754464E177L,
      -1.31167362135569576486452806355817153004431E180L,
      +1.04678940094780380821832853929823089643829E183L,
      -8.54328935788337077185982546299082774593270E185L,
      +7.12878213224865423522884066771438224721245E188L,
      -6.08029314555358993000847118686477458461988E191L,
      +5.29967764248499239300942910043247266228490E194L,
      -4.71942591687458626443646229013379911103761E197L,
      +4.29284137914029810894168296541074669045521E200L,
      -3.98767449682322074434477655542938795106651E203L,
      +3.78197804193588827138944181161393327898220E206L,
      -3.66142336836811912436858082151197348755196E209L,
      +3.61760902723728623488554609298914089477541E212L,
      -3.64707726451913543621383088655499449048682E215L,
      +3.75087554364544090983452410104814189306842E218L,
      -3.93458672964390282694891288533713429355657E221L,
      +4.20882111481900820046571171111494898242731E224L,
      -4.59022962206179186559802940573325591059371E227L,
      +5.10317257726295759279198185106496768539760E230L,
      -5.78227623036569554015377271242917142512200E233L,
      +6.67624821678358810322637794412809363451080E236L,
      -7.85353076444504163225916259639312444428230E239L,
      +9.41068940670587255245443288258762485293948E242L,
      -1.14849338734651839938498599206805592548354E246L,
      +1.42729587428487856771416320087122499897180E249L,
      -1.80595595869093090142285728117654560926719E252L,
      +2.32615353076608052161297985184708876161736E255L,
      -3.04957517154995947681942819261542593785327E258L,
      +4.06858060764339734424012124124937318633684E261L,
      -5.52310313219743616252320044093186392324280E264L,
      +7.62772793964343924869949690204961215533859E267L,
      -1.07155711196978863132793524001065396932667E271L,
      +1.53102008959691884453440916153355334355847E274L,
      -2.22448916821798346676602348865048510824835E277L,
      +3.28626791906901391668189736436895275365183E280L,
      -4.93559289559603449020711938191575963496999E283L,
      +7.53495712008325067212266049779283956727824E286L,
      -1.16914851545841777278088924731655041783900E290L,
      +1.84352614678389394126646201597702232396492E293L,
      -2.95368261729680829728014917350525183485207E296L,
      +4.80793212775015697668878704043264072227967E299L,
      -7.95021250458852528538243631671158693036798E302L,
      +1.33527841873546338750122832017820518292039E306L,
   #if LDBL_MAX_EXP == 16384
      // Entries 260 - 600 http://www.wolframalpha.com/input/?i=TABLE[N[Bernoulli[i]%2C40]%2C+{i%2C258%2C600%2C2}]
      - 2.277640649601959593875058983506938037019e309L,
      3.945184036046326234163525556422667595884e312L,
      -6.938525772130602106071724989641405550473e315L,
      1.238896367577564823729057820219210929986e319L,
      -2.245542599169309759499987966025604480745e322L,
      4.131213176073842359732511639489669404266e325L,
      -7.713581346815269584960928069762882771369e328L,
      1.461536066837669600638613788471335541313e332L,
      -2.809904606225532896862935642992712059631e335L,
      5.480957121318876639512096994413992284327e338L,
      -1.084573284087686110518125291186079616320e342L,
      2.176980775647663539729165173863716459962e345L,
      -4.431998786117553751947439433256752608068e348L,
      9.150625657715535047417756278073770096073e351L,
      -1.915867353003157351316577579148683133613e355L,
      4.067256303542212258698836003682016040629e358L,
      -8.754223791037736616228150209910348734629e361L,
      1.910173688735533667244373747124109379826e365L,
      -4.225001320265091714631115064713174404607e368L,
      9.471959352547827678466770796787503034505e371L,
      -2.152149973279986829719817376756088198573e375L,
      4.955485775334221051344839716507812871361e378L,
      -1.156225941759134696630956889716381968142e382L,
      2.733406597646137698610991926705098514017e385L,
      -6.546868135325176947099912523279938546333e388L,
      1.588524912441221472814692121069821695547e392L,
      -3.904354800861715180218598151050191841308e395L,
      9.719938686092045781827273411668132975319e398L,
      -2.450763621049522051234479737511375679283e402L,
      6.257892098396815305085674126334317095277e405L,
      -1.618113552083806592527989531636955084420e409L,
      4.236528795217618357348618613216833722648e412L,
      -1.123047068199051008086174989124136878992e416L,
      3.013971787525654770217283559392286666886e419L,
      -8.188437573221553030375681429202969070420e422L,
      2.251910591336716809153958146725775718707e426L,
      -6.268411292043789823075314151509139413399e429L,
      1.765990845202322642693572112511312471527e433L,
      -5.035154436231331651259071296731160882240e436L,
      1.452779356460483245253765356664402207266e440L,
      -4.241490890130137339052414960684151515166e443L,
      1.252966001692427774088293833338841893293e447L,
      -3.744830047478272947978103227876747240343e450L,
      1.132315806695710930595876001089232216024e454L,
      -3.463510845942701805991786197773934662578e457L,
      1.071643382649675572086865465873916611537e461L,
      -3.353824475439933688957233489984711465335e464L,
      1.061594257145875875963152734129803268488e468L,
      -3.398420969215528955528654193586189805265e471L,
      1.100192502000434096206138068020551065890e475L,
      -3.601686379213993374332690210094863486472e478L,
      1.192235170430164900533187239994513019475e482L,
      -3.990342751779668381699052942504119409180e485L,
      1.350281800938769780891258894167663309221e489L,
      -4.619325443466054312873093650888507562249e492L,
      1.597522243968586548227514639959727696694e496L,
      -5.584753729092155108530929002119620487652e499L,
      1.973443623104646193229794524759543752089e503L,
      -7.048295441989615807045620880311201930244e506L,
      2.544236702499719094591873151590280263560e510L,
      -9.281551595258615205927443367289948150345e513L,
      3.421757163154453657766296828520235351572e517L,
      -1.274733639384538364282697627345068947433e521L,
      4.798524805311016034711205886780460173566e524L,
      -1.825116948422858388787806917284878870034e528L,
      7.013667442807288452441777981425055613982e531L,
      -2.723003862685989740898815670978399383114e535L,
      1.068014853917260290630122222858884658850e539L,
      -4.231650952273697842269381683768681118533e542L,
      1.693650052202594386658903598564772900388e546L,
      -6.846944855806453360616258582310883597678e549L,
      2.795809132238082267120232174243715559601e553L,
      -1.153012972808983269106716828311318981951e557L,
      4.802368854268746357511997492039592697149e560L,
      -2.019995255271910836389761734035403905781e564L,
      8.580207235032617856059250643095019760968e567L,
      -3.680247942263468164408192134916355198549e571L,
      1.593924457586765331397457407661306895942e575L,
      -6.970267175232643679233530367569943057501e578L,
      3.077528087427698518703282907890556154309e582L,
      -1.371846760052887888926055417297342106614e586L,
      6.173627360829553396851763207025505289166e589L,
      -2.804703130495506384463249394043486916669e593L,
      1.286250900087150126167490951216207186092e597L,
      -5.954394420063617872366818601092036543220e600L,
      2.782297785278756426177542270854984091406e604L,
      -1.312214674935307746141207680066262384215e608L,
      6.246299145383554153167974732783934504370e611L,
      -3.000812007679574430883792565577444226490e615L,
      1.454904877136007844493861746476079537075e619L,
      -7.118558521873800304612781121044077357278e622L,
      3.514739820897817389472822276832677887997e626L,
      -1.751137068816377401163011262831890828437e630L,
      8.803498091818800678575314081978951179602e633L,
      -4.465612911700593572269200981612564161010e637L,
      2.285494565287530681465757798517033542888e641L,
      -1.180145168917737098025683613598595411329e645L,
      6.147941849198393232663105284575149616925e648L,
      -3.231069156963603593233679426198974663352e652L,
      1.713042725635435041806895849197608270935e656L,
      -9.161761363270648920537613435771882898051e659L,
      4.942675965960539112005679080810117766825e663L,
      -2.689684712697383518131267222872386600031e667L,
      1.476320014229917759615308193449511534656e671L,
      -8.173037740864781506597184122049453514594e674L,
      4.563462313190521363235182420178784459580e678L,
      -2.569790015236158475703055501886439298708e682L,
      1.459410219452119981958355737832022375085e686L,
      -8.358304882556983795372406183642486436653e689L,
      4.827305091483557818593092377664570208355e693L,
      -2.811394311081493166793414157061950132403e697L,
      1.651026863340675349245561261339568827739e701L,
      -9.776578579336866764167878646459810047899e704L,
      5.837207965197521880181236529616560780535e708L,
      -3.513938957938032127105389702846371181520e712L,
      2.132747371360190507595748444536911078788e716L,
      -1.305047363239192640729466563372665311602e720L,
      8.050825342678337497636292798039996484780e723L,
      -5.006884161223862543665524155681082112689e727L,
      3.139016066011452177570812014513491361235e731L,
      -1.983829535212711378291469356666001365873e735L,
      1.263822427649676371257598052486237628698e739L,
      -8.115678659900522918802121684491754629503e742L,
      5.252995164972075271667364371449050412435e746L,
      -3.427038125662404660056511738625477058135e750L,
      2.253446011834352733279946306835940729858e754L,
      -1.493407341897034717876962786798831719683e758L,
      9.974681322653365118752729509398728354442e761L,
      -6.714230142773850863927710112350816379426e765L,
      4.554668668931723346600337564274944733530e769L,
      -3.113635386023220127834102980385275379533e773L,
      2.144945411287666204679363498162954050208e777L,
      -1.488982121181387164932397544378555256016e781L,
      1.041537218854627455352298173588983048748e785L,
      -7.341073881786613676177562822942175683993e788L,
      5.213524272587199574980117351016322518428e792L,
      -3.730592531776514409283897139216167197989e796L,
      2.689592876341877079083449497724049500175e800L,
      -1.953643788231947582529884602972233135002e804L,
      1.429691073080500563348668321308878246277e808L,
      -1.054059177095488639836063073070536825675e812L,
      7.828919160938693948399336431565350676613e815L,
      -5.857884457184396382550955498026762014753e819L,
      4.415401588264172474136969345712659422380e823L,
      -3.352573884181287635796498822858109969161e827L,
      2.564210385719224000156548240934108974447e831L,
      -1.975534392116037602837941409848663077528e835L,
      1.533062123975940045180943006948008486466e839L,
      -1.198306160488763291730059994812781226903e843L,
      9.434034267770711698676321369174735725321e846L,
      -7.480619200038505368468483892246806488879e850L,
      5.974161898439971564124576801455052907638e854L,
      -4.805125663714699771668630995361572639386e858L,
      3.892332138028039952403812726744593073776e862L,
      -3.175276505779699340738548328810180869575e866L,
      2.608608681939322393581069188271626122519e870L,
      -2.158148554392732439392868052394994052628e874L,
      1.797993483301448477700600221980862686033e878L,
      -1.508407575089108597171576068862286462909e882L,
      1.274273406242459482708930389008701147244e886L,
      -1.083950475353171986748233157909397370193e890L,
      9.284292630726328432038470356821265395331e893L,
      -8.007012115449516364480417355063446317414e897L,
      6.952871948429568933888979915833266241471e901L,
      -6.078828929473797621198666799700739891205e905L,
      5.350908089710964244671334224708057812633e909L,
      -4.742168072503284973969982758434401589090e913L,
      4.231149239401967697257534662010605751136e917L,
      -3.800684612827828851942743291026898158947e921L,
      3.436984796314246158361599955909956583986e925L,
      -3.128930718993658356398482705317381808301e929L,
      //
      // 602-1300: http://www.wolframalpha.com/input/?i=TABLE[N[Bernoulli[i]%2C40]%2C+{i%2C602%2C1300%2C2}]
      2.867524740577223817164663595437919813239e933L, -2.645462974939090580963101220449509725942e937L, 2.456800827789169780295419018499543141869e941L, -2.296690549725790064673528302231294870532e945L, 2.161174697699793265715182091764676666457e949L, -2.047023224586087259305754002882269123194e953L, 1.951604806042481282712736234132803700277e957L, -1.872785206668284042110390583158639495143e961L, 1.808847160923282257302788929692654262867e965L, -1.758427529634609613399327744595257497188e969L, 1.720468488019528147087036246754294757647e973L, -1.694180279355332648057740852839804839425e977L, 1.679013685251183870616469618951463869496e981L, -1.674640861433092946269144173974414945664e985L, 1.680943600147858322148767806987527412112e989L, -1.698008433134805056489370119323402510305e993L, 1.726128304411348354183882648263448448633e997L, -1.765810838736918108045764015629875016219e1001L, 1.817793526882665071123822455897912718293e1005L, -1.883066459765807128944897377914669600374e1009L, 1.962903588035940537938222992228124233567e1013L, -2.058903881920696086033171142046100185783e1017L, 2.173044241735786946064676598703393618281e1021L, -2.307746591425236218893160658331303115253e1025L, 2.465962312241418731528973526597433097256e1029L, -2.651278087802503406316742676403301581549e1033L, 2.868048395658440423778896607880692085708e1037L, -3.121561373094393453726645989392054731637e1041L, 3.418246710091027042099932753084126095820e1045L, -3.765936717592482928796920675282930034018e1049L, 4.174194967165213973474293718362757753877e1053L, -4.654731142471753017867105249805137855862e1057L, 5.221926310090434518253178454907900079787e1061L, -5.893500145664015254409680930288710794031e1065L, 6.691361332576333738130720616841706994101e1069L, -7.642695184575063524608775697714741180954e1073L, 8.781359617440634128952082759434723165820e1077L, -1.014968338800868135594698909567734048618e1082L, 1.180079105471061498849752479044520598414e1086L, -1.380162016721660241308046692646452732446e1090L, 1.623685158291375662775444238282343536948e1094L, -1.921404880943289359290531906131400049399e1098L, 2.287040419533950152851434188305457266969e1102L, -2.738162880206032093123060939173765335255e1106L, 3.297371307848643161532227459901386725801e1110L, -3.993854689967542662299211323085023297602e1114L, 4.865474805885735467044047308902313673643e1118L, -5.961554732739027308247618738765152679497e1122L, 7.346627151757492821447573639763873833441e1126L, -9.105493288459908620636712748727395637965e1130L, 1.135007867626164861991621396462821975167e1135L, -1.422876214067403769204874786137232627418e1139L, 1.793912271573925309173135913914667878908e1143L, -2.274542916104231188526120123855259514144e1147L, 2.900273688809987694128857655036783261991e1151L, -3.719022795563122339874875448447744493398e1155L, 4.795753420982845153626611023078973364321e1159L, -6.218937220186281310109009529226561379773e1163L, 8.109611247999584815668395828940708619394e1167L, -1.063412316303440216539797215354141158589e1172L, 1.402214363674117662460496032135704328989e1176L, -1.859223235464558752766840772026058694872e1180L, 2.478828203789903637835992128856742276028e1184L, -3.323169416193176673655321536761413885767e1188L, 4.479640207312477092938541546776915956580e1192L, -6.071721672924085739424644485636889518799e1196L, 8.274698015123579607850404326757887762270e1200L, -1.133855131459773018024052539697784205966e1205L, 1.562146222050424344025824344480153248984e1209L, -2.163904570724750459592352173471446831752e1213L, 3.013703210722669908901286635073603018696e1217L, -4.219903244242308803914269531001720703294e1221L, 5.940703220571043642186808904696174833998e1225L, -8.408147464216029127243257448169774333631e1229L, 1.196419999747411909144144315499654470715e1234L, -1.711518922741148710381740436694440587059e1238L, 2.461434539630850545757453894977350505251e1242L, -3.558748530932574002484841810677232366801e1246L, 5.172525606281917297657859608800373729529e1250L, -7.557850217376323621984784308774476917753e1254L, 1.110141075986004209769735296234549704181e1259L, -1.639216556732622481406083885926912451281e1263L, 2.433138328152562628385514545400044125983e1267L, -3.630476645219033020888837165221286413171e1271L, 5.445289518636306992942604775585977779418e1275L, -8.209806424989072060381590985042272020067e1279L, 1.244209849774134691374848390346442737613e1284L, -1.895384488692308848372754844910263931874e1288L, 2.902272596647764894203369746806169285113e1292L, -4.466944174025026625137032739317650862593e1296L, 6.910485739507636504313238347702354354916e1300L, -1.074550085668784170644854815272144687769e1305L, 1.679419258904938802199084915274175753529e1309L, -2.638155207645646220849795321076977230763e1313L, 4.165284786632654168563096850610185378233e1317L, -6.609774274649031371770290191295685774584e1321L, 1.054194100570841329575393359295845860860e1326L, -1.689822316104196916970708778265725885275e1330L, 2.722340957904912685605914893019783431164e1334L, -4.407776313964403233676810178851005163725e1338L, 7.172436210641903635864868181569129834361e1342L, -1.172947440100495955246356688225986736990e1347L, 1.927745674072824377954824961348211728006e1351L, -3.184013467435655962214317208087993711563e1355L, 5.285045125125832341263897233405196808096e1359L, -8.815883582819232027207118521581424783107e1363L, 1.477818368424505276711779171224799759099e1368L, -2.489482576496570159333357550363134602876e1372L, 4.214292881345076419678976329218843808204e1376L, -7.169068531615459070909644981451297906220e1380L, 1.225513133750594558180516896275774441895e1385L, -2.105160827387119480607950260289853896637e1389L, 3.633787605672960549893307203363402915249e1393L, -6.302830804027849515239463308430185990705e1397L, 1.098521433860299633481449685364914115468e1402L, -1.923858597401607622723144320370279518600e1406L, 3.385512828549942051667348582951554570164e1410L, -5.986286250836771248147827011780631183980e1414L, 1.063572794668186370728928272374836554300e1419L, -1.898666684876492795233907174493757572290e1423L, 3.405627002840442789235393111726609930533e1427L, -6.137724140284450036591063946055819333244e1431L, 1.111411024660941507986132154479364267486e1436L, -2.022060876221034821890406900217875915949e1440L, 3.696248025817144690840539132103538834108e1444L, -6.788448439024998306316860676030442691610e1448L, 1.252615233049059554031883468823648511657e1453L, -2.322190433141265975888955985950824418729e1457L, 4.325200102353909846882217732999001735342e1461L, -8.093531903011880118699218269369570178812e1465L, 1.521558881878323790120983450270946857209e1470L, -2.873780311010933807686415826253380907421e1474L, 5.452903697278823304173192839252276211670e1478L, -1.039457922537509500320638240809547113575e1483L, 1.990610112724715126895008793014214505760e1487L, -3.829667853173777076954453401761025071562e1491L, 7.401624504283011888971231756333356050310e1495L, -1.437075122764477911733220492562365990710e1500L, 2.802940275035867428066581228962104019228e1504L, -5.491938363067613321364335249495394164430e1508L, 1.080961960603953462180593404647115933651e1513L, -2.137290931892412298654741768897581319007e1517L, 4.245031321673807283498263276791307370788e1521L, -8.469499523038763989328773224520912663309e1525L, 1.697421812794203793865032206191322699261e1530L, -3.417217332563937242285349373774004020539e1534L, 6.910378594841763785923780822895851271770e1538L, -1.403696282437585785557998429691459557649e1543L, 2.864060533055333035232343601021192111053e1547L, -5.869818290384811353182423286543086530728e1551L, 1.208359745327224593486268988808338456906e1556L, -2.498576742140453770373914215325521001990e1560L, 5.189311407347546310078739863704346083861e1564L, -1.082537954843916294257278789980768336964e1569L, 2.268238255751421312559806122980932952706e1573L, -4.773557403917983369065731568732198697502e1577L, 1.009019097334998841920279535262007639746e1582L, -2.142181266523235177327239693359275472557e1586L, 4.567814904130855969979178320003286614868e1590L, -9.782550516204803195398428611221899469345e1594L, 2.104180123097086948576304557651398411373e1599L, -4.545658958087323864004652894518442709646e1603L, 9.862563944609427542603740078470901803131e1607L, -2.149105846582226970866569209122813809019e1612L, 4.703235567543888152049628411354542509156e1616L, -1.033719212601584878353206879472796545848e1621L, 2.281767401903848796732740825793310514456e1625L, -5.058236070813950229238666252351966279306e1629L, 1.126112519657857205642546937554224492775e1634L, -2.517766761987679577706779689880657777343e1638L, 5.653225190181653388317503182908983211029e1642L, -1.274735955461074142223278576503188429497e1647L, 2.886578974679460464298863945016671299242e1651L, -6.564203307141426181809363135003467581753e1655L, 1.499036144473064593308260681782048262301e1660L, -3.437714715599902386917108442954580869236e1664L, 7.916830957072777234152907034541325149479e1668L, -1.830850567422571420661248197094782575285e1673L, 4.251778280827419894527511469762091846660e1677L, -9.915182507286989818033146623995507108134e1681L, 2.321878208636697663781227497233334385222e1686L, -5.459879022461660582811365437190884471726e1690L, 1.289222044549922720398543474297554204559e1695L, -3.056819658344217799458557578658863826289e1699L, 7.277891759142725294172926258364455941365e1703L, -1.739928293433385104144012025546489673795e1708L, 4.176797408823713136137404972612780406904e1712L, -1.006788178307821554781930741698052910780e1717L, 2.436754569909644399766538111317379484511e1721L, -5.921896599028498715774458493117079340155e1725L, 1.445045688171565118619109316933316429671e1730L, -3.540547766876069233350621578795319652040e1734L, 8.710114552028472554054293344204504325978e1738L, -2.151484527880464463303897113553085899101e1743L, 5.335928195512405709733771642389502809087e1747L, -1.328726408335015910030370523083559660016e1752L, 3.322090527232917400247098823651437597786e1756L, -8.339387326241218096865362177688582376376e1760L, 2.101842203781264395369771906884644062395e1765L, -5.318704469415522036482913743767085545209e1769L, 1.351288005941730688647540059088127991581e1774L, -3.446853546858473171100748720136784228698e1778L, 8.827284762030783576089954173424852998700e1782L, -2.269642226090373319660782216907175419317e1787L, 5.858820683661708553422363777419430816755e1791L, -1.518385813684321665045387969920683656625e1796L, 3.950661327164595923092260035122668890334e1800L, -1.031976516347387969958181456058243183780e1805L, 2.706317892325103782207094286049104555552e1809L, -7.125140422584701175967252533378906957380e1813L, 1.883260203116768075569432925204868418472e1818L, -4.997193687108743666000994570700725873035e1822L, 1.331182722092654526185433799891693838871e1827L, -3.559930289076558484535632566755216035553e1831L, 9.557281027056970446117541983785660301558e1835L, -2.575805002229372523547972911961335317502e1840L, 6.969058431277067406841032797913179025984e1844L, -1.892842481279278678390672746902260183506e1849L, 5.160964211693777744707760614147460787285e1853L, -1.412602588198037643242529860614298968137e1858L, 3.881313379962387603749693387037174052146e1862L, -1.070542170988009009334148472388319844527e1867L, 2.964094312414144330805731101996829908435e1871L, -8.238350132106899955856124602934281976453e1875L, 2.298504171050560756192352106062598639825e1880L, -6.437303944649223478093890316531995121228e1884L, 1.809727811843121957353712606428292269805e1889L, -5.107047553992257935533518628886728031061e1893L, 1.446674478990385642488446075734631327506e1898L, -4.113513327511444762766719175770513771122e1902L, 1.174067517257431444028448391638451935667e1907L, -3.363630086409895071362533854123306097827e1911L, 9.672868956071838221096869293070568259792e1915L, -2.792101741911955365960369780457612630184e1920L, 8.089710604557382430162031502761771390568e1924L, -2.352650988877130983061761312962677887796e1929L, 6.867549079740051556501575104006222995568e1933L, -2.012161201632998475706904405535757516336e1938L, 5.917489529279588702317256137229398357271e1942L, -1.746718667239329545125902248821502764273e1947L, 5.175069416058975040990816515838893249437e1951L, -1.538913401594651457295303469904084052963e1956L, 4.593185746210984655636051293374195150815e1960L, -1.375981868450401919299150690829612124045e1965L, 4.137207965217520410530508053863759216958e1969L, -1.248518564582257710069294326648626362439e1974L, 3.781575291117895093413381897917341286951e1978L, -1.149575999691408110085856948595444100435e1983L, 3.507413095836612229403470531176947165451e1987L, -1.074032838410645352804690949680310176413e1992L, 3.300857202456564870338466973024760446263e1996L, -1.018149578840803516349758843017979498322e2001L, 3.151876950233613792531594490714752800621e2005L, -9.792574827376149360558532022944033224780e2009L, 3.053456145978161645823454710737904504036e2014L, -9.555442346102849014299990542596620094035e2018L, 3.001037449298122384017009412541525703002e2023L, -9.459120112371096268275049056229023773120e2027L, 2.992168042152196502453442556462819104060e2032L, -9.498922680869041470681858599915282791899e2036L, 3.026307717971075309746179763189393755074e2041L, -9.676079238806159594565350708123427510151e2045L, 3.104778286352798464772361361434013339088e2050L, -9.997786802782252742109475924344598057966e2054L, 3.230847952724856366943939804248186203776e2059L, -1.047769651900498931701604323213605884945e2064L, 3.409958102134053489747140426163802214042e2068L, -1.113687894644055086152064258459886518528e2073L, 3.650114509271160332136458711252217684956e2077L, -1.200536387553969483433239131469825141412e2082L, 3.962482337718333099498977337189304099484e2086L, -1.312441206957064803437100929905979391106e2091L, 4.362246723746013772563799740886664288515e2095L, -1.454975881895253548422481637083633839534e2100L, 4.869831412214692119172895822285084162147e2104L, -1.635618419512383251104125916207188960680e2109L, 5.512611314145041257838234038980389596534e2113L, -1.864392957231340288547618808749072127289e2118L, 6.327317613106621547060670091824665547127e2122L, -2.154772001506498703267302897994526372056e2127L, 7.363426139490286496267931634843475368903e2131L, -2.524950643808031915843604894357998905460e2136L, 8.687956390288096215918373666581638675156e2140L, -2.999656978200020459428228924242615592768e2145L, 1.039231328851609224822335039430898644149e2150L, -3.612742437616019936358910410005123924796e2154L, 1.260211309932738404790711574105022002093e2159L, -4.410916378453971105434385837025433805752e2163L, 1.549140617923265948720013792673729394719e2168L, -5.459173749226782924959103886664322964926e2172L, 1.930343307630952098252884031069043541182e2177L, -6.848749229218425353808144618581305978045e2181L, 2.438117138001365487681440577590059588102e2186L, -8.708873656769794358508423272379627581292e2190L, 3.121268068338199458891764932384819739714e2195L, -1.122430216307539309816165910733145404999e2200L, 4.049900779207199370582177687160985635615e2204L, -1.466167983141158219266077836130256565915e2209L, 5.325678718693772500250292767751070974887e2213L, -1.940955845102272053048140384364058448998e2218L, 7.097467198361219669927211698104447309186e2222L, -2.603968771680987683436428778397387110896e2227L, 9.585403285394812946713320044815117440444e2231L, -3.540176030547640510648455468270569908446e2236L, 1.311827683984025111744358347783996339730e2241L, -4.877124229155333857009747836542843294702e2245L, 1.819213075760490882591173222316749809951e2250L, -6.808221630329265915405178596748950929642e2254L, 2.556299969544109052724772800143396857058e2259L, -9.629763347675306704861859899230073979116e2263L, 3.639508580119285595844040783082958425575e2268L, -1.380037493555816309137481185927387732499e2273L, 5.249980712165216709135893538080020409581e2277L, -2.003737844109055078145975651407367170529e2282L, 7.672522280806944397358668566379646540213e2286L, -2.947454993639165318799389781921184991045e2291L, 1.135966912801707623489383623092951142963e2296L, -4.392293711194501621873299212059053651432e2300L, 1.703813210168560937608104155973968112409e2305L, -6.630636743874062041158387022015853902938e2309L, 2.588742636486379690203698247275411406029e2314L, -1.013959594068423546627946242481463893979e2319L, 3.984265821528043268586235974854766821078e2323L, -1.570614519682157047612769672066387881154e2328L, 6.211297381339606877062824459742129064477e2332L, -2.464246931985476159686671650962783785426e2337L, 9.807833742601662212615240518855757197483e2341L, -3.916036434571217691317276306031837539092e2346L, 1.568566392975837368624727722120313955274e2351L, -6.302885887601142677858008037129298948063e2355L, 2.540704455306077495480843691828334210014e2360L, -1.027412480318234348899627142408950111875e2365L, 4.167823618450297116765978030480648316769e2369L, -1.696076602731914277275203926124423530377e2374L, 6.923904505633301788461482786634220738504e2378L, -2.835463065742506394026733592206185459035e2383L, 1.164828772275756526225951620927486307632e2388L, -4.800242878545012539781545966693324656699e2392L, 1.984381759611877246529319121941597679107e2397L, -8.228979942542641498511023600269641046627e2401L, 3.423130231367101727862739208673375060101e2406L, -1.428418168129733054582191895023094524495e2411L, 5.979153801634459282232521647160044877770e2415L, -2.510581926948409809562349588087762800160e2420L, 1.057443785053915411991029410076722022815e2425L, -4.467723713549428749678277264414266162837e2429L, 1.893474116528533144079731251913008472748e2434L, -8.049601965052954947260081891142509464888e2438L, 3.432648527503971149009691133946275281368e2443L, -1.468324699963694393989960228042259134294e2448L,
      //
      // 1302-1600: http://www.wolframalpha.com/input/?i=TABLE[N[Bernoulli[i]%2C40]%2C+{i%2C1302%2C1600%2C2}]
      6.300146502435743791500010801885493871234e2452L, -2.711520667146768856688291798851999580833e2457L, 1.170595555513900137297344452318266434006e2462L, -5.069095411973246242900074508988493530542e2466L, 2.201819284807954055092117706033113168896e2471L, -9.593088725189386197503123561368325167085e2475L, 4.192362385909155628936230811010649614060e2480L, -1.837725836941968309866675158105812946762e2485L, 8.080201101491972605313807752565294881374e2489L, -3.563536075527215702966392543784039539240e2494L, 1.576361051321107275181955665159661781175e2499L, -6.994292466180175594372663323941761853364e2503L, 3.112744353537336702834647901141392426258e2508L, -1.389481328370627358752727485697345194612e2513L, 6.221134636655213696041740685131223999953e2517L, -2.793779613656947577224654924852010601105e2522L, 1.258399062987759035354039924686781081603e2527L, -5.685208194704131918461885165870560583895e2531L, 2.576167857759537340210434756292816456179e2536L, -1.170846052338591953257169251219597581763e2541L, 5.337296787116189575571202979672747140313e2545L, -2.440264475369219459038748840841422948951e2550L, 1.119037151526195093932933161706501865175e2555L, -5.146858829220973887154576240993607686435e2559L, 2.374259791963193693837576781321391741634e2564L, -1.098501215269400934956638118646657823799e2569L, 5.097500369683616795005376807036889542869e2573L, -2.372446971688020647583535886090779018865e2578L, 1.107430282014636546248612381377039463753e2583L, -5.184597227131050012643138079903381280471e2587L, 2.434392040100910394476893838832599310265e2592L, -1.146412753331162872665743308094817095949e2597L, 5.414578104816988124950636101250217797539e2601L, -2.564835392810685332173156758121489913946e2606L, 1.218495070518549208066544111736985586178e2611L, -5.805713573821806672815019495319510297824e2615L, 2.774298194574319430697819781128985128618e2620L, -1.329580186505564627453485444017911980430e2625L, 6.390545858902318479863947547243743500916e2629L, -3.080502542499571035376377703435361520427e2634L, 1.489236104239976282318361008292980814533e2639L, -7.220413839991892382038608955317126799684e2643L, 3.510874916591640642524021216241607185085e2648L, -1.712070118580404599831061485055269100525e2653L, 8.372956919832386730490070625622785478703e2657L, -4.106629146981883685523102256292669054596e2662L, 2.019945438530802964718619732330776495740e2667L, -9.964133277392242111939720494354938982970e2671L, 4.929278642971447854669801547226335041410e2676L, -2.445509657169810919463982615395074704130e2681L, 1.216734421265677299127016883839223226884e2686L, -6.071008437677720186241562251151490713584e2690L, 3.037824949882992896564570441252792097027e2695L, -1.524402878612630565501569310883356490225e2700L, 7.671320530781999359200097739951316234193e2704L, -3.871436167706734376478728954716915204399e2709L, 1.959313530432202158587932399068682252335e2714L, -9.944063618400630821320953821427307024297e2718L, 5.061161998202463346818982228476199873781e2723L, -2.583219090831132705328958245740715185448e2728L, 1.322193991367293532684189527174543501836e2733L, -6.786569982732483290873213417465458376706e2737L, 3.493212334804776543395067018414547811062e2742L, -1.803090099978261928508495412750404640933e2747L, 9.333100843930216567894508007158644926767e2751L, -4.844499031405982604449146511179496492045e2756L, 2.521648090959971240812330574936006906830e2761L, -1.316227870932708474838173333385377250286e2766L, 6.889488826832738674261056521130795910494e2770L, -3.616184242864384509259984293501533623932e2775L, 1.903356124758119137116543283603627028779e2780L, -1.004601544584640657081847200643996069583e2785L, 5.317043885597842225603585588404817559596e2789L, -2.821938866752488868682751438901900485500e2794L, 1.501842023003449590337997900945924161741e2799L, -8.014908048137216649348740300633172710524e2803L, 4.289126235121619907138036129192558937445e2808L, -2.301619137231461344870820700320913118444e2813L, 1.238485136850053215006962645111854705210e2818L, -6.682503731149007943059244518074044280490e2822L, 3.615572393938012932030234169574978859655e2827L, -1.961565108627429629104703146282982075623e2832L, 1.067123259692924564435881096382837264046e2837L, -5.821179870182035246401397327057170726418e2841L, 3.184127229476322727732208017279268211356e2846L, -1.746429902183019597973436257300843998825e2851L, 9.604873565299766333876882842813498685054e2855L, -5.296759978724702692134960752308186890356e2860L, 2.928906353338652198977536576170287112391e2865L, -1.623961162577704769945821804737884742792e2870L, 9.028574047002736235613238355032484299017e2874L, -5.033087486357905828950503441308068892610e2879L, 2.813325650062267479031371852434194635210e2884L, -1.576791132296320840138263753339056345362e2889L, 8.861258343945925667272164531504265693289e2893L, -4.993236404321511029440212686547068244002e2898L, 2.821192993950901287717082243608730217471e2903L, -1.598254169674379493385730199445427966752e2908L, 9.078617590346932363947095804057608979359e2912L, -5.170742114456472142154347566092068443393e2917L, 2.952866185102528847516095880416675972086e2922L, -1.690794578626103552690094140317813413244e2927L, 9.707168799669516048238542260085175133847e2931L, -5.587884732306715493795271931175883605707e2936L, 3.225179489154957423492905957887744116530e2941L, -1.866424419669188178697802576490431604300e2946L, 1.082967626854618222657109354056973072044e2951L, -6.300392007169862865282706277272018077291e2955L, 3.675066377245428685118763485986517510658e2960L, -2.149348371085132073107516253339849053182e2965L, 1.260349351812619395000600434630904474324e2970L, -7.409963623771231302980906971935254993610e2974L, 4.367980758467862686643231700861155889684e2979L, -2.581566823350789671250829457603555544100e2984L, 1.529757357568342629912560827243282062227e2989L, -9.088595394263364554625061567617375176719e2993L, 5.413829169254585648363594604231030415354e2998L, -3.233288119606092759447005827969216281573e3003L, 1.936042437734875803183915765854038424658e3008L, -1.162289934202291715747729318797398221667e3013L, 6.995870350500567071550614251287615697508e3017L, -4.221776496490106417392945233048068288503e3022L, 2.554309239868912570382343877718991746122e3027L, -1.549440871550119801225143558087410562418e3032L, 9.423199525954784955533959981278992475051e3036L, -5.745689660772387668861183913170050552119e3041L, 3.512407521007240798565045328376471603253e3046L, -2.152708113797517364614914569890010876143e3051L, 1.322761289733739440340237168659770154654e3056L, -8.148777388506488753591136948542248584098e3060L, 5.032880858479326069741729004270784264612e3065L, -3.116396010103058126269735274818345780360e3070L, 1.934634831148214353514796782480703021435e3075L, -1.204077166243116651938489240924641810276e3080L, 7.513065583444964704795707060501161621868e3084L, -4.699873512563164914493150520500838535415e3089L, 2.947541197349762411713872934523813866703e3094L, -1.853262416286420077763886100673646141885e3099L, 1.168196427912100545575264493997591040800e3104L, -7.382362285873345348505276546404015842875e3108L, 4.677071041058096429847797962954927487730e3113L, -2.970642034084362431442183248944824506476e3118L, 1.891572688282564476274920103912259755482e3123L, -1.207509963440193713810418554061532113326e3128L, 7.727731208240101791845515599659441557781e3132L, -4.957988488048495669466804712012179891532e3137L, 3.188965862446236259925047956715566822864e3142L, -2.056286895821370106507670239256782411337e3147L, 1.329246918771714093479509313343886287414e3152L, -8.614188519577835653765633797787633659253e3156L,
      //
      // 1602-1900: http://www.wolframalpha.com/input/?i=TABLE[N[Bernoulli[i]%2C40]%2C+{i%2C1602%2C1900%2C2}]
      5.596396533621874175909933615343145642161e3161L, -3.644908483469388437457938883454376864180e3166L, 2.379838409026860469990569665632800095988e3171L, -1.557720925267669865362152155022069166772e3176L, 1.022143420270029721682551084917730373739e3181L, -6.723767358891570842116651998814252095792e3185L, 4.433950491570308179905446963723780229747e3190L, -2.931196854668917448553150023532223509373e3195L, 1.942557068752664549549945921392100172355e3200L, -1.290553202978622786891265558106235068695e3205L, 8.595082329732118303768775883557789195136e3209L, -5.738453265222970049867280061719670658457e3214L, 3.840687915100689856736926915331157331684e3219L, -2.576862441955523551149886625900059307506e3224L, 1.733166107320377310388765047659987844208e3229L, -1.168569552450178559412843683052610870569e3234L, 7.898289836694980777809433306209459851871e3238L, -5.351485909164216694400535493924387979018e3243L, 3.634772439350395177931952925644409735777e3248L, -2.474801048002975145046569303233576339695e3253L, 1.689126939254790850063878942448569759390e3258L, -1.155691524500722774057997965355407962525e3263L, 7.926435404542361405718288670391575676323e3267L, -5.449654814183048796524718620178906854846e3272L, 3.755898589900254795894812942275711835138e3277L, -2.594843902682143854622514329649211211808e3282L, 1.797048752397789969347915328338360264536e3287L, -1.247551415074438712713815166107969504456e3292L, 8.681719521514448143910215886388510318746e3296L, -6.056203898213120922016159444227958572276e3301L, 4.234882876331814099029781995617143573641e3306L, -2.968432911643338866295929748049749932906e3311L, 2.085723508930484816454740610260790948864e3316L, -1.469023169879432026361623513301566735138e3321L, 1.037150346505052892302077637883522696572e3326L, -7.339977067836656769144838365069396168014e3330L, 5.206985412168234130596004552956337839140e3335L, -3.702673773319239583641029108403509825141e3340L, 2.639251227995760315076225206168354089692e3345L, -1.885736353072698581595150856674914203383e3350L, 1.350563292338261784288559687678302458996e3355L, -9.695749980998301526113046898985991802000e3359L, 6.977167462628398202151721319169989304520e3364L, -5.032768280399753942925624560483352299263e3369L, 3.638844963651800168080623511900705036698e3374L, -2.637228631269251606169613775399022890118e3379L, 1.915836351653767108720464847696767898597e3384L, -1.395064293615007319328267865803567670760e3389L, 1.018249052614943190644465556486933211307e3394L, -7.449662162606857550867922631658930320805e3398L, 5.463119632208085241594107781601567713991e3403L, -4.015736541676989144201935890497836963875e3408L, 2.958754190183866660901503059509579790900e3413L, -2.185096074054288399312733179064098492511e3418L, 1.617517444557020250864919655301189186103e3423L, -1.200170662015511746748935675940010250555e3428L, 8.925888349899029449015791684428724952411e3432L, -6.653851763691885517669938275618991145962e3437L, 4.971722031098457895973348076474071155918e3442L, -3.723500582577984967442020337848702786829e3447L, 2.795153783541721373364976034391375710110e3452L, -2.103141577212720698169118819883801186873e3457L, 1.586129575320959267959148073466004084241e3462L, -1.198988457279648730711646682156242973137e3467L, 9.084402368157025658430300252246526602197e3471L, -6.898927494435965163817354296023108913714e3476L, 5.251332286149361587885046891266325872375e3481L, -4.006442950956739933884502808470603581850e3486L, 3.063718202820270282280659950794978994604e3491L, -2.348215284130973783732145823834807395920e3496L, 1.803952490148087317330011096671019781340e3501L, -1.389022326803437345760911068933754707688e3506L, 1.071986115818329525986099441493200866389e3511L, -8.292085224650940719705699485423856363908e3515L, 6.428829064452939640541475198655560890344e3520L, -4.995654440302797445368056643032307686314e3525L, 3.890847042582299188849273838681034339406e3530L, -3.037288555751484681537442833929275697351e3535L, 2.376385803695694695338601696534348875191e3540L, -1.863527130251861900692886008704804849076e3545L, 1.464674913498036269270793715104706378182e3550L, -1.153804954579033578659954846698233083197e3555L, 9.109783835348935092264268296199541780964e3559L, -7.208869193983001804305451104827153729326e3564L, 5.717530734277611949162917337810749919265e3569L, -4.544970302634007326980094771330550661605e3574L, 3.621042850825283032134228901678636353355e3579L, -2.891447067949778492831490654980043715471e3584L, 2.314060419397710657435821461707043283167e3589L, -1.856140759923563235273220981623595304434e3594L, 1.492185412981476596273279338314204171587e3599L, -1.202290032627175365810126250991853594801e3604L, 9.708881154579770196658265042625239421053e3608L, -7.857809850747029705680072304049448493252e3613L, 6.373898598298513400228819113197728735438e3618L, -5.181780406472117449048907989647202286666e3623L, 4.222036621953044040518942750638183171221e3628L, -3.447728386429130175025813550845575613047e3633L, 2.821701521717856346224159586852612710800e3638L, -2.314488376711998526455043944505424906920e3643L, 1.902671298033180765286213227393060711096e3648L, -1.567603736821312488140289549008391847440e3653L, 1.294408945316538946551785312385509945367e3658L, -1.071194533081615830960091702262923009420e3663L, 8.884351908108581551151252566466606126397e3667L, -7.384866682828103669170236267589653324531e3672L, 6.152023838008155718180876735217718355563e3677L, -5.136304310431705506236573876510219357975e3682L, 4.297736808124296434723193397876220759378e3687L, -3.603994887745884762510172194982172483480e3692L, 3.028884745605031552399167746007361297342e3697L, -2.551141302205187365552982635794121855138e3702L, 2.153467982869535549299173317536193051608e3707L, -1.821769476343602094059466497311600827296e3712L, 1.544537580582347892980177956984101211006e3717L, -1.312358705945937257247030754517293537539e3722L, 1.117518229297781388884979995402355617235e3727L, -9.536820860779441793021624381677086661097e3731L, 8.156400668831968026931547065507466530546e3736L, -6.990984948728184142718575396052260691181e3741L, 6.005124901126818071638224144541102727563e3746L, -5.169500241880947716732682089328427995109e3751L, 4.459815478235310026240134567325749844182e3756L, -3.855902253361684187081283218890336962427e3761L, 3.340988024176995223515640815937037040546e3766L, -2.901099226680215736735094376078800376829e3771L, 2.524573363444334459448089563912567842927e3776L, -2.201659455716348555524529213295341212492e3781L, 1.924190302190936448078364755844591374353e3786L, -1.685313186099770223843319514432495898517e3791L, 1.479268235966730475749985741048766689808e3796L, -1.301205702893883803117530921635013780575e3801L, 1.147035071153450453405384269242743907426e3806L, -1.013300250456366849150496776951686112298e3811L, 8.970761720605591762300958007557533865346e3815L, -7.958829781488943084496783248922217392838e3820L, 7.076146954685024795720193943027902028642e3825L, -6.304798526260409199660290516451546966159e3830L, 5.629519616664188107056583939722984509867e3835L, -5.037281594099054092767959480843344929292e3840L, 4.516946091316834843581919268794683123349e3845L, -4.058975118925834202620358386772092359951e3850L, 3.655187798978978909014603682039470653549e3855L, -3.298555903041546671060101785513812175322e3860L, 2.983031738662727912016882399515879119620e3865L, -2.703403043317732979516341931451317866898e3870L, 2.455170460800096241793872443768546335444e3875L, -2.234443928432490538417605502448376856290e3880L, 2.037854924078003280537856980560782325730e3885L, -1.862482033918775734840779765743099458137e3890L,
      //
      // 1902-2200: http://www.wolframalpha.com/input/?i=TABLE[N[Bernoulli[i]%2C40]%2C+{i%2C1902%2C2200%2C2}]
      1.705787724951999960095629912416210969679e3895L, -1.565564556110550991891247404758895970376e3900L, 1.439889351869832939488618785632174464789e3905L, -1.327084102784257406218693901793045990520e3910L, 1.225682557296027075027021534960026145706e3915L, -1.134401635488994148555787301654561211982e3920L, 1.052116934052356802920509999705307165985e3925L, -9.778417073593082219082361206542342793584e3929L, 9.107088061888562704837019028349522303725e3934L, -8.499551364633102138471246155980056936129e3939L, 7.949082681085658044610890152056533167407e3944L, -7.449748809722797718736397140511396011691e3949L, 6.996307824769340144608141799981589288378e3954L, -6.584122718472954006131003060359621706243e3959L, 6.209086595833487707192492087176843233407e3964L, -5.867557793863165391821489909125720982339e3969L, 5.556303538475260373917478405626416604297e3974L, -5.272450955936249442242634142613834212778e3979L, 5.013444428433789818228792126117223030641e3984L, -4.777008429684552423800736200488532033034e3989L, 4.561115100786341787876705283291018781137e3994L, -4.363955932181992701667719449097126840439e3999L, 4.183917007557000586305945495258591147615e4004L, -4.019557342177353010692923286760895584096e4009L, 3.869589913635745758786275231296652917580e4014L, -3.732865038934070181861017140563175000872e4019L, 3.608355799736107390800162778737339576843e4024L, -3.495145258697474565347261083975193776541e4029L, 3.392415245050326563747729613872524362741e4034L, -3.299436517958948801426629481782413630714e4039L, 3.215560142306355508598119430378551642857e4044L, -3.140209934146377815556058799557727461298e4049L, 3.072875852591406752692761744649563131272e4054L, -3.013108231854799187724018548255922550991e4059L, 2.960512761914376268185064129600549308882e4064L, -2.914746139139036596123006476633770383901e4069L, 2.875512319506974985103149834921665445532e4074L, -2.842559316984704569380036093537576068104e4079L, 2.815676498441436148701483904115879856704e4084L, -2.794692334326268275058539147656334465534e4089L, 2.779472571396106785963004020814493340829e4094L, -2.769918800191406321625251621260024635680e4099L, 2.765967395840433013288935879837390099329e4104L, -2.767588816244119880300161388073836623878e4109L, 2.774787246856347651152278076466043136230e4114L, -2.787600586224957950622601135620189837948e4119L, 2.806100771288225169339048358106052817280e4124L, -2.830394446218080573456394167711739786431e4129L, 2.860623983452244712039094143642843717029e4134L, -2.896968870550611723525738907034588104300e4139L, 2.939647481737606306044335918078617963078e4144L, -2.988919258547518526076380181812161398808e4149L, 3.045087329976721023952450383837883029431e4154L, -3.108501609077197464748958150625867523408e4159L, 3.179562410123820875787052833975010965963e4164L, -3.258724638491880104953913719767939138170e4169L, 3.346502614347964869115073881474258766546e4174L, -3.443475601364631413158991572423086599816e4179L, 3.550294123121350747300886840907918182129e4184L, -3.667687162886053419715985091863398517145e4189L, 3.796470357354794420044278000297864085607e4194L, -3.937555311976846882455930574021795626971e4199L, 4.091960185075595842547638450930710467324e4204L, -4.260821710519620959138720129506770036460e4209L, 4.445408854703156440576808070360934740837e4214L, -4.647138333645908068599900650548418672065e4219L, 4.867592250805288922190809906525766574205e4224L, -5.108538156515551259475573296900660666192e4229L, 5.371951876776035157276013631113314852508e4234L, -5.660043513521220243900043448456234873940e4239L, 5.975287081834808618140945840817834710330e4244L, -6.320454323372684034118816565375206053746e4249L, 6.698653321371992324876559665938996023646e4254L, -7.113372643219128807424340495235606473967e4259L, 7.568531854202750881338746432078817214052e4264L, -8.068539383842553693076672384509126681464e4269L, 8.618358887685935324188596304168259394311e4274L, -9.223585437012291673660319256730398171887e4279L, 9.890533091606747031464718533600572123091e4284L, -1.062633567277107015128545384570274268438e4290L, 1.143906286231591191271274413511275981288e4295L, -1.233785411712565904499340744089870916842e4300L, 1.333307331840530219050170916015276125870e4305L, -1.443648758235403286296065629219598769529e4310L, 1.566147425967471851736562867318748510088e4315L, -1.702326086290842780634120184324081017286e4320L, 1.853920350455786350409148418966087344063e4325L, -2.022911043115598592197907512410632615740e4330L, 2.211561842992792253055716743938240466613e4335L, -2.422463130294011318178080247305407476096e4340L, 2.658583129381772791030436640519847627789e4345L, -2.923327636881988941081365085520742216540e4350L, 3.220609866329557159104267531058019683271e4355L, -3.554932228621330128152149026066400241546e4360L, 3.931482212643167323798366327390058684499e4365L, -4.356244944221399578650235478583297389113e4370L, 4.836135498303121165971331625888490168138e4375L, -5.379154636371461359750682662639062606297e4380L, 5.994572359716861309678596804350346692501e4385L, -6.693144535124290060793936095397161934045e4390L, 7.487368894313509797084395689517008597061e4395L, -8.391787970609807810531578161564037339793e4400L, 9.423348062978921203475110312003096820035e4405L, -1.060182516651648405903017734022504884319e4411L, 1.195033105063952979885086754342706651656e4416L, -1.349591538868673992167798923586925758429e4421L, 1.527028315253291113905307092657539132480e4426L, -1.731065051510920640409442255224015234974e4431L, 1.966076741510092840076264635935585216200e4436L, -2.237214093245750681191361238831105906202e4441L, 2.550550094903891445719729187215253324232e4446L, -2.913255853313667303707651906277658164129e4451L, 3.333811847072394764285817140850092324169e4456L, -3.822262084288044913490118858492563410392e4461L, 4.390520310533864198186202368026630430120e4466L, -5.052739449335052080092114976206610871466e4471L, 5.825757966350870043117899492954521458799e4476L, -6.729639942938203582008846884575881320532e4481L, 7.788329466816396015493306357116312471970e4486L, -9.030444674469025073047417528762134025409e4491L, 1.049024263381993629167658236142000524752e4497L, -1.220879351508964912255081664072251573277e4502L, 1.423541151220109512749655991050110438471e4507L, -1.662940118618541616964708044356967429362e4512L, 1.946219185900482116137855064775635250366e4517L, -2.281995008842006909631764011781911322493e4522L, 2.680678198213108543648324254258111216040e4527L, -3.154866427472784086389609599207759103500e4532L, 3.719827710160801797530420206201570269720e4537L, -4.394095404360277919140027580071549980218e4542L, 5.200201854779615608741690339830306148442e4547L, -6.165584312943608652377791415603277251516e4552L, 7.323705248531382981433751104158852636445e4557L, -8.715439846124090647163930834760361817820e4562L, 1.039079696609215651011736087603304766850e4568L, -1.241105689556982425619608247473478857800e4573L, 1.485143079696380339521658550262280772546e4578L, -1.780437412164973637340821168154300094802e4583L, 2.138372099157518882088209435171770222745e4588L, -2.572985071149069551034276570909360759588e4593L, 3.101615379617643734762997559011097203354e4598L, -3.745713657616368229906151946770042703357e4603L, 4.531859496161940719835150033082561700677e4608L, -5.493040495326927998321538336584233566465e4613L, 6.670262730603009306595018122252730741798e4618L, -8.114581584793494903775255213273982440688e4623L, 9.889666561810883044159054730371102725871e4628L, -1.207504541653929734716275932570097623330e4634L, 1.477021377885843688233899471354959308782e4639L, -1.809984912147908767583043524070645821179e4644L,
      //
      // 2202-2320: http://www.wolframalpha.com/input/?i=TABLE[N[Bernoulli[i]%2C40]%2C+{i%2C2202%2C2320%2C2}]
      2.222043594325228980916360265527780300093e4649L, -2.732869701246338361699515268224049951411e4654L, 3.367233945421922463553518272642397177145e4659L, -4.156377225041273602431272489314020150392e4664L, 5.139764368092890466235162431795350591151e4669L, -6.367329693760865476879589228002216011370e4674L, 7.902356742934106007362514378717026407839e4679L, -9.825176966314431712897976595483070301406e4684L, 1.223792760178593282435724837135946867088e4690L, -1.527068151452750404853140815207477555192e4695L, 1.908935682572268829496101580401263597905e4700L, -2.390593888616966248780378941331847473699e4705L, 2.999171106576893833644521002894489856321e4710L, -3.769440655453736670024798444784356437578e4715L, 4.746047769851891438576002047529258107351e4720L, -5.986405469241447720766576164546767533359e4725L, 7.564466155536872051712519119999711534616e4730L, -9.575641408047918720040356745796976488951e4735L, 1.214322951835035451699619713803395497423e4741L, -1.542682591979864353012093794301924196234e4746L, 1.963334539793192183270983986567556358603e4751L, -2.503148969013901182572118121398034622584e4756L, 3.197076711250102964526567664729089847162e4761L, -4.090653552025822488578293526174572934858e4766L, 5.243302769651520536759521264615159906699e4771L, -6.732697170903775309261288127044088674182e4776L, 8.660529543801770516930589210020128142543e4781L, -1.116015823611149634592870112730519454113e4787L, 1.440675306432920129218036927923030695520e4792L, -1.863078034853256227415397798026969938881e4797L, 2.413595413458810442409656314019115041699e4802L, -3.132317029597258599678590012779717945144e4807L, 4.072246763371584312534474102756137619716e4812L, -5.303577511521827157146305369181950467569e4817L, 6.919417518688636032335131253584331645491e4822L, -9.043473312934241153732087612484569398979e4827L, 1.184037400265044213826044590639924237359e4833L, -1.552956685415800894409743993367334099777e4838L, 2.040404893052952221581694807126473204625e4843L, -2.685565763841580219033402331219206776210e4848L, 3.540927057361929050327811875290025248120e4853L, -4.676912607538885419407656762767991163574e4858L, 6.188165903566760647569323704623433330229e4863L, -8.202087471895029964699042637255411806373e4868L, 1.089045274355389654614196651761310970580e4874L, -1.448524684976553869119447042300206226148e4879L, 1.930028100376784839502387280956424581974e4884L, -2.576074799096023589462128312524664980682e4889L, 3.444369635011990347297134928452972402038e4894L, -4.613354441299253694113609154769978684993e4899L, 6.189834306866879018555349507257537840922e4904L, -8.319470760665157534580593571258276368233e4909L, 1.120124240070996761986102680587384813245e4915L, -1.510740451399746828351090108638980398124e4920L, 2.041108231091323198877509959371257503819e4925L, -2.762447751447012472733302936575873838539e4930L,
   #endif
      } };
#else
      static const std::array<long double, 1 + max_bernoulli_b2n<T>::value> bernoulli_data;
#endif
   };

#ifdef BOOST_MATH_HAVE_CONSTEXPR_TABLES
   template <class T>
   constexpr const std::array<long double, 1 + max_bernoulli_b2n<T>::value> unchecked_bernoulli_data<T, 3>::bernoulli_data;
#else
   template <class T>
   const std::array<long double, 1 + max_bernoulli_b2n<T>::value> unchecked_bernoulli_data<T, 3>::bernoulli_data =
   { {
      +1.00000000000000000000000000000000000000000L,
      +0.166666666666666666666666666666666666666667L,
      -0.0333333333333333333333333333333333333333333L,
      +0.0238095238095238095238095238095238095238095L,
      -0.0333333333333333333333333333333333333333333L,
      +0.0757575757575757575757575757575757575757576L,
      -0.253113553113553113553113553113553113553114L,
      +1.16666666666666666666666666666666666666667L,
      -7.09215686274509803921568627450980392156863L,
      +54.9711779448621553884711779448621553884712L,
      -529.124242424242424242424242424242424242424L,
      +6192.12318840579710144927536231884057971014L,
      -86580.2531135531135531135531135531135531136L,
      +1.42551716666666666666666666666666666666667E6L,
      -2.72982310678160919540229885057471264367816E7L,
      +6.01580873900642368384303868174835916771401E8L,
      -1.51163157670921568627450980392156862745098E10L,
      +4.29614643061166666666666666666666666666667E11L,
      -1.37116552050883327721590879485616327721591E13L,
      +4.88332318973593166666666666666666666666667E14L,
      -1.92965793419400681486326681448632668144863E16L,
      +8.41693047573682615000553709856035437430786E17L,
      -4.03380718540594554130768115942028985507246E19L,
      +2.11507486380819916056014539007092198581560E21L,
      -1.20866265222965259346027311937082525317819E23L,
      +7.50086674607696436685572007575757575757576E24L,
      -5.03877810148106891413789303052201257861635E26L,
      +3.65287764848181233351104308429711779448622E28L,
      -2.84987693024508822262691464329106781609195E30L,
      +2.38654274996836276446459819192192149717514E32L,
      -2.13999492572253336658107447651910973926742E34L,
      +2.05009757234780975699217330956723102516667E36L,
      -2.09380059113463784090951852900279701847092E38L,
      +2.27526964884635155596492603527692645814700E40L,
      -2.62577102862395760473030497361582020814490E42L,
      +3.21250821027180325182047923042649852435219E44L,
      -4.15982781667947109139170744952623589366896E46L,
      +5.69206954820352800238834562191210586444805E48L,
      -8.21836294197845756922906534686173330145509E50L,
      +1.25029043271669930167323398297028955241772E53L,
      -2.00155832332483702749253291988132987687242E55L,
      +3.36749829153643742333966769033387530162196E57L,
      -5.94709705031354477186604968440515408405791E59L,
      +1.10119103236279775595641307904376916046305E62L,
      -2.13552595452535011886583850190410656789733E64L,
      +4.33288969866411924196166130593792062184514E66L,
      -9.18855282416693282262005552155018971389604E68L,
      +2.03468967763290744934550279902200200659751E71L,
      -4.70038339580357310785752555350060606545967E73L,
      +1.13180434454842492706751862577339342678904E76L,
      -2.83822495706937069592641563364817647382847E78L,
      +7.40642489796788506297508271409209841768797E80L,
      -2.00964548027566044834656196727153631868673E83L,
      +5.66571700508059414457193460305193569614195E85L,
      -1.65845111541362169158237133743199123014950E88L,
      +5.03688599504923774192894219151801548124424E90L,
      -1.58614682376581863693634015729664387827410E93L,
      +5.17567436175456269840732406825071225612408E95L,
      -1.74889218402171173396900258776181591451415E98L,
      +6.11605199949521852558245252642641677807677E100L,
      -2.21227769127078349422883234567129324455732E103L,
      +8.27227767987709698542210624599845957312047E105L,
      -3.19589251114157095835916343691808148735263E108L,
      +1.27500822233877929823100243029266798669572E111L,
      -5.25009230867741338994028246245651754469199E113L,
      +2.23018178942416252098692981988387281437383E116L,
      -9.76845219309552044386335133989802393011669E118L,
      +4.40983619784529542722726228748131691918758E121L,
      -2.05085708864640888397293377275830154864566E124L,
      +9.82144332797912771075729696020975210414919E126L,
      -4.84126007982088805087891967099634127611305E129L,
      +2.45530888014809826097834674040886903996737E132L,
      -1.28069268040847475487825132786017857218118E135L,
      +6.86761671046685811921018885984644004360924E137L,
      -3.78464685819691046949789954163795568144895E140L,
      +2.14261012506652915508713231351482720966602E143L,
      -1.24567271371836950070196429616376072194583E146L,
      +7.43457875510001525436796683940520613117807E148L,
      -4.55357953046417048940633332233212748767721E151L,
      +2.86121128168588683453638472510172325229190E154L,
      -1.84377235520338697276882026536287854875414E157L,
      +1.21811545362210466995013165065995213558174E160L,
      -8.24821871853141215484818457296893447301419E162L,
      +5.72258779378329433296516498142978615918685E165L,
      -4.06685305250591047267679693831158655602196E168L,
      +2.95960920646420500628752695815851870426379E171L,
      -2.20495225651894575090311752273445984836379E174L,
      +1.68125970728895998058311525151360665754464E177L,
      -1.31167362135569576486452806355817153004431E180L,
      +1.04678940094780380821832853929823089643829E183L,
      -8.54328935788337077185982546299082774593270E185L,
      +7.12878213224865423522884066771438224721245E188L,
      -6.08029314555358993000847118686477458461988E191L,
      +5.29967764248499239300942910043247266228490E194L,
      -4.71942591687458626443646229013379911103761E197L,
      +4.29284137914029810894168296541074669045521E200L,
      -3.98767449682322074434477655542938795106651E203L,
      +3.78197804193588827138944181161393327898220E206L,
      -3.66142336836811912436858082151197348755196E209L,
      +3.61760902723728623488554609298914089477541E212L,
      -3.64707726451913543621383088655499449048682E215L,
      +3.75087554364544090983452410104814189306842E218L,
      -3.93458672964390282694891288533713429355657E221L,
      +4.20882111481900820046571171111494898242731E224L,
      -4.59022962206179186559802940573325591059371E227L,
      +5.10317257726295759279198185106496768539760E230L,
      -5.78227623036569554015377271242917142512200E233L,
      +6.67624821678358810322637794412809363451080E236L,
      -7.85353076444504163225916259639312444428230E239L,
      +9.41068940670587255245443288258762485293948E242L,
      -1.14849338734651839938498599206805592548354E246L,
      +1.42729587428487856771416320087122499897180E249L,
      -1.80595595869093090142285728117654560926719E252L,
      +2.32615353076608052161297985184708876161736E255L,
      -3.04957517154995947681942819261542593785327E258L,
      +4.06858060764339734424012124124937318633684E261L,
      -5.52310313219743616252320044093186392324280E264L,
      +7.62772793964343924869949690204961215533859E267L,
      -1.07155711196978863132793524001065396932667E271L,
      +1.53102008959691884453440916153355334355847E274L,
      -2.22448916821798346676602348865048510824835E277L,
      +3.28626791906901391668189736436895275365183E280L,
      -4.93559289559603449020711938191575963496999E283L,
      +7.53495712008325067212266049779283956727824E286L,
      -1.16914851545841777278088924731655041783900E290L,
      +1.84352614678389394126646201597702232396492E293L,
      -2.95368261729680829728014917350525183485207E296L,
      +4.80793212775015697668878704043264072227967E299L,
      -7.95021250458852528538243631671158693036798E302L,
      +1.33527841873546338750122832017820518292039E306L,
#if LDBL_MAX_EXP == 16384
      // Entries 260 - 600 http://www.wolframalpha.com/input/?i=TABLE[N[Bernoulli[i]%2C40]%2C+{i%2C258%2C600%2C2}]
      - 2.277640649601959593875058983506938037019e309L,
      3.945184036046326234163525556422667595884e312L,
      -6.938525772130602106071724989641405550473e315L,
      1.238896367577564823729057820219210929986e319L,
      -2.245542599169309759499987966025604480745e322L,
      4.131213176073842359732511639489669404266e325L,
      -7.713581346815269584960928069762882771369e328L,
      1.461536066837669600638613788471335541313e332L,
      -2.809904606225532896862935642992712059631e335L,
      5.480957121318876639512096994413992284327e338L,
      -1.084573284087686110518125291186079616320e342L,
      2.176980775647663539729165173863716459962e345L,
      -4.431998786117553751947439433256752608068e348L,
      9.150625657715535047417756278073770096073e351L,
      -1.915867353003157351316577579148683133613e355L,
      4.067256303542212258698836003682016040629e358L,
      -8.754223791037736616228150209910348734629e361L,
      1.910173688735533667244373747124109379826e365L,
      -4.225001320265091714631115064713174404607e368L,
      9.471959352547827678466770796787503034505e371L,
      -2.152149973279986829719817376756088198573e375L,
      4.955485775334221051344839716507812871361e378L,
      -1.156225941759134696630956889716381968142e382L,
      2.733406597646137698610991926705098514017e385L,
      -6.546868135325176947099912523279938546333e388L,
      1.588524912441221472814692121069821695547e392L,
      -3.904354800861715180218598151050191841308e395L,
      9.719938686092045781827273411668132975319e398L,
      -2.450763621049522051234479737511375679283e402L,
      6.257892098396815305085674126334317095277e405L,
      -1.618113552083806592527989531636955084420e409L,
      4.236528795217618357348618613216833722648e412L,
      -1.123047068199051008086174989124136878992e416L,
      3.013971787525654770217283559392286666886e419L,
      -8.188437573221553030375681429202969070420e422L,
      2.251910591336716809153958146725775718707e426L,
      -6.268411292043789823075314151509139413399e429L,
      1.765990845202322642693572112511312471527e433L,
      -5.035154436231331651259071296731160882240e436L,
      1.452779356460483245253765356664402207266e440L,
      -4.241490890130137339052414960684151515166e443L,
      1.252966001692427774088293833338841893293e447L,
      -3.744830047478272947978103227876747240343e450L,
      1.132315806695710930595876001089232216024e454L,
      -3.463510845942701805991786197773934662578e457L,
      1.071643382649675572086865465873916611537e461L,
      -3.353824475439933688957233489984711465335e464L,
      1.061594257145875875963152734129803268488e468L,
      -3.398420969215528955528654193586189805265e471L,
      1.100192502000434096206138068020551065890e475L,
      -3.601686379213993374332690210094863486472e478L,
      1.192235170430164900533187239994513019475e482L,
      -3.990342751779668381699052942504119409180e485L,
      1.350281800938769780891258894167663309221e489L,
      -4.619325443466054312873093650888507562249e492L,
      1.597522243968586548227514639959727696694e496L,
      -5.584753729092155108530929002119620487652e499L,
      1.973443623104646193229794524759543752089e503L,
      -7.048295441989615807045620880311201930244e506L,
      2.544236702499719094591873151590280263560e510L,
      -9.281551595258615205927443367289948150345e513L,
      3.421757163154453657766296828520235351572e517L,
      -1.274733639384538364282697627345068947433e521L,
      4.798524805311016034711205886780460173566e524L,
      -1.825116948422858388787806917284878870034e528L,
      7.013667442807288452441777981425055613982e531L,
      -2.723003862685989740898815670978399383114e535L,
      1.068014853917260290630122222858884658850e539L,
      -4.231650952273697842269381683768681118533e542L,
      1.693650052202594386658903598564772900388e546L,
      -6.846944855806453360616258582310883597678e549L,
      2.795809132238082267120232174243715559601e553L,
      -1.153012972808983269106716828311318981951e557L,
      4.802368854268746357511997492039592697149e560L,
      -2.019995255271910836389761734035403905781e564L,
      8.580207235032617856059250643095019760968e567L,
      -3.680247942263468164408192134916355198549e571L,
      1.593924457586765331397457407661306895942e575L,
      -6.970267175232643679233530367569943057501e578L,
      3.077528087427698518703282907890556154309e582L,
      -1.371846760052887888926055417297342106614e586L,
      6.173627360829553396851763207025505289166e589L,
      -2.804703130495506384463249394043486916669e593L,
      1.286250900087150126167490951216207186092e597L,
      -5.954394420063617872366818601092036543220e600L,
      2.782297785278756426177542270854984091406e604L,
      -1.312214674935307746141207680066262384215e608L,
      6.246299145383554153167974732783934504370e611L,
      -3.000812007679574430883792565577444226490e615L,
      1.454904877136007844493861746476079537075e619L,
      -7.118558521873800304612781121044077357278e622L,
      3.514739820897817389472822276832677887997e626L,
      -1.751137068816377401163011262831890828437e630L,
      8.803498091818800678575314081978951179602e633L,
      -4.465612911700593572269200981612564161010e637L,
      2.285494565287530681465757798517033542888e641L,
      -1.180145168917737098025683613598595411329e645L,
      6.147941849198393232663105284575149616925e648L,
      -3.231069156963603593233679426198974663352e652L,
      1.713042725635435041806895849197608270935e656L,
      -9.161761363270648920537613435771882898051e659L,
      4.942675965960539112005679080810117766825e663L,
      -2.689684712697383518131267222872386600031e667L,
      1.476320014229917759615308193449511534656e671L,
      -8.173037740864781506597184122049453514594e674L,
      4.563462313190521363235182420178784459580e678L,
      -2.569790015236158475703055501886439298708e682L,
      1.459410219452119981958355737832022375085e686L,
      -8.358304882556983795372406183642486436653e689L,
      4.827305091483557818593092377664570208355e693L,
      -2.811394311081493166793414157061950132403e697L,
      1.651026863340675349245561261339568827739e701L,
      -9.776578579336866764167878646459810047899e704L,
      5.837207965197521880181236529616560780535e708L,
      -3.513938957938032127105389702846371181520e712L,
      2.132747371360190507595748444536911078788e716L,
      -1.305047363239192640729466563372665311602e720L,
      8.050825342678337497636292798039996484780e723L,
      -5.006884161223862543665524155681082112689e727L,
      3.139016066011452177570812014513491361235e731L,
      -1.983829535212711378291469356666001365873e735L,
      1.263822427649676371257598052486237628698e739L,
      -8.115678659900522918802121684491754629503e742L,
      5.252995164972075271667364371449050412435e746L,
      -3.427038125662404660056511738625477058135e750L,
      2.253446011834352733279946306835940729858e754L,
      -1.493407341897034717876962786798831719683e758L,
      9.974681322653365118752729509398728354442e761L,
      -6.714230142773850863927710112350816379426e765L,
      4.554668668931723346600337564274944733530e769L,
      -3.113635386023220127834102980385275379533e773L,
      2.144945411287666204679363498162954050208e777L,
      -1.488982121181387164932397544378555256016e781L,
      1.041537218854627455352298173588983048748e785L,
      -7.341073881786613676177562822942175683993e788L,
      5.213524272587199574980117351016322518428e792L,
      -3.730592531776514409283897139216167197989e796L,
      2.689592876341877079083449497724049500175e800L,
      -1.953643788231947582529884602972233135002e804L,
      1.429691073080500563348668321308878246277e808L,
      -1.054059177095488639836063073070536825675e812L,
      7.828919160938693948399336431565350676613e815L,
      -5.857884457184396382550955498026762014753e819L,
      4.415401588264172474136969345712659422380e823L,
      -3.352573884181287635796498822858109969161e827L,
      2.564210385719224000156548240934108974447e831L,
      -1.975534392116037602837941409848663077528e835L,
      1.533062123975940045180943006948008486466e839L,
      -1.198306160488763291730059994812781226903e843L,
      9.434034267770711698676321369174735725321e846L,
      -7.480619200038505368468483892246806488879e850L,
      5.974161898439971564124576801455052907638e854L,
      -4.805125663714699771668630995361572639386e858L,
      3.892332138028039952403812726744593073776e862L,
      -3.175276505779699340738548328810180869575e866L,
      2.608608681939322393581069188271626122519e870L,
      -2.158148554392732439392868052394994052628e874L,
      1.797993483301448477700600221980862686033e878L,
      -1.508407575089108597171576068862286462909e882L,
      1.274273406242459482708930389008701147244e886L,
      -1.083950475353171986748233157909397370193e890L,
      9.284292630726328432038470356821265395331e893L,
      -8.007012115449516364480417355063446317414e897L,
      6.952871948429568933888979915833266241471e901L,
      -6.078828929473797621198666799700739891205e905L,
      5.350908089710964244671334224708057812633e909L,
      -4.742168072503284973969982758434401589090e913L,
      4.231149239401967697257534662010605751136e917L,
      -3.800684612827828851942743291026898158947e921L,
      3.436984796314246158361599955909956583986e925L,
      -3.128930718993658356398482705317381808301e929L,
      //
      // 602-1300: http://www.wolframalpha.com/input/?i=TABLE[N[Bernoulli[i]%2C40]%2C+{i%2C602%2C1300%2C2}]
      2.867524740577223817164663595437919813239e933L, -2.645462974939090580963101220449509725942e937L, 2.456800827789169780295419018499543141869e941L, -2.296690549725790064673528302231294870532e945L, 2.161174697699793265715182091764676666457e949L, -2.047023224586087259305754002882269123194e953L, 1.951604806042481282712736234132803700277e957L, -1.872785206668284042110390583158639495143e961L, 1.808847160923282257302788929692654262867e965L, -1.758427529634609613399327744595257497188e969L, 1.720468488019528147087036246754294757647e973L, -1.694180279355332648057740852839804839425e977L, 1.679013685251183870616469618951463869496e981L, -1.674640861433092946269144173974414945664e985L, 1.680943600147858322148767806987527412112e989L, -1.698008433134805056489370119323402510305e993L, 1.726128304411348354183882648263448448633e997L, -1.765810838736918108045764015629875016219e1001L, 1.817793526882665071123822455897912718293e1005L, -1.883066459765807128944897377914669600374e1009L, 1.962903588035940537938222992228124233567e1013L, -2.058903881920696086033171142046100185783e1017L, 2.173044241735786946064676598703393618281e1021L, -2.307746591425236218893160658331303115253e1025L, 2.465962312241418731528973526597433097256e1029L, -2.651278087802503406316742676403301581549e1033L, 2.868048395658440423778896607880692085708e1037L, -3.121561373094393453726645989392054731637e1041L, 3.418246710091027042099932753084126095820e1045L, -3.765936717592482928796920675282930034018e1049L, 4.174194967165213973474293718362757753877e1053L, -4.654731142471753017867105249805137855862e1057L, 5.221926310090434518253178454907900079787e1061L, -5.893500145664015254409680930288710794031e1065L, 6.691361332576333738130720616841706994101e1069L, -7.642695184575063524608775697714741180954e1073L, 8.781359617440634128952082759434723165820e1077L, -1.014968338800868135594698909567734048618e1082L, 1.180079105471061498849752479044520598414e1086L, -1.380162016721660241308046692646452732446e1090L, 1.623685158291375662775444238282343536948e1094L, -1.921404880943289359290531906131400049399e1098L, 2.287040419533950152851434188305457266969e1102L, -2.738162880206032093123060939173765335255e1106L, 3.297371307848643161532227459901386725801e1110L, -3.993854689967542662299211323085023297602e1114L, 4.865474805885735467044047308902313673643e1118L, -5.961554732739027308247618738765152679497e1122L, 7.346627151757492821447573639763873833441e1126L, -9.105493288459908620636712748727395637965e1130L, 1.135007867626164861991621396462821975167e1135L, -1.422876214067403769204874786137232627418e1139L, 1.793912271573925309173135913914667878908e1143L, -2.274542916104231188526120123855259514144e1147L, 2.900273688809987694128857655036783261991e1151L, -3.719022795563122339874875448447744493398e1155L, 4.795753420982845153626611023078973364321e1159L, -6.218937220186281310109009529226561379773e1163L, 8.109611247999584815668395828940708619394e1167L, -1.063412316303440216539797215354141158589e1172L, 1.402214363674117662460496032135704328989e1176L, -1.859223235464558752766840772026058694872e1180L, 2.478828203789903637835992128856742276028e1184L, -3.323169416193176673655321536761413885767e1188L, 4.479640207312477092938541546776915956580e1192L, -6.071721672924085739424644485636889518799e1196L, 8.274698015123579607850404326757887762270e1200L, -1.133855131459773018024052539697784205966e1205L, 1.562146222050424344025824344480153248984e1209L, -2.163904570724750459592352173471446831752e1213L, 3.013703210722669908901286635073603018696e1217L, -4.219903244242308803914269531001720703294e1221L, 5.940703220571043642186808904696174833998e1225L, -8.408147464216029127243257448169774333631e1229L, 1.196419999747411909144144315499654470715e1234L, -1.711518922741148710381740436694440587059e1238L, 2.461434539630850545757453894977350505251e1242L, -3.558748530932574002484841810677232366801e1246L, 5.172525606281917297657859608800373729529e1250L, -7.557850217376323621984784308774476917753e1254L, 1.110141075986004209769735296234549704181e1259L, -1.639216556732622481406083885926912451281e1263L, 2.433138328152562628385514545400044125983e1267L, -3.630476645219033020888837165221286413171e1271L, 5.445289518636306992942604775585977779418e1275L, -8.209806424989072060381590985042272020067e1279L, 1.244209849774134691374848390346442737613e1284L, -1.895384488692308848372754844910263931874e1288L, 2.902272596647764894203369746806169285113e1292L, -4.466944174025026625137032739317650862593e1296L, 6.910485739507636504313238347702354354916e1300L, -1.074550085668784170644854815272144687769e1305L, 1.679419258904938802199084915274175753529e1309L, -2.638155207645646220849795321076977230763e1313L, 4.165284786632654168563096850610185378233e1317L, -6.609774274649031371770290191295685774584e1321L, 1.054194100570841329575393359295845860860e1326L, -1.689822316104196916970708778265725885275e1330L, 2.722340957904912685605914893019783431164e1334L, -4.407776313964403233676810178851005163725e1338L, 7.172436210641903635864868181569129834361e1342L, -1.172947440100495955246356688225986736990e1347L, 1.927745674072824377954824961348211728006e1351L, -3.184013467435655962214317208087993711563e1355L, 5.285045125125832341263897233405196808096e1359L, -8.815883582819232027207118521581424783107e1363L, 1.477818368424505276711779171224799759099e1368L, -2.489482576496570159333357550363134602876e1372L, 4.214292881345076419678976329218843808204e1376L, -7.169068531615459070909644981451297906220e1380L, 1.225513133750594558180516896275774441895e1385L, -2.105160827387119480607950260289853896637e1389L, 3.633787605672960549893307203363402915249e1393L, -6.302830804027849515239463308430185990705e1397L, 1.098521433860299633481449685364914115468e1402L, -1.923858597401607622723144320370279518600e1406L, 3.385512828549942051667348582951554570164e1410L, -5.986286250836771248147827011780631183980e1414L, 1.063572794668186370728928272374836554300e1419L, -1.898666684876492795233907174493757572290e1423L, 3.405627002840442789235393111726609930533e1427L, -6.137724140284450036591063946055819333244e1431L, 1.111411024660941507986132154479364267486e1436L, -2.022060876221034821890406900217875915949e1440L, 3.696248025817144690840539132103538834108e1444L, -6.788448439024998306316860676030442691610e1448L, 1.252615233049059554031883468823648511657e1453L, -2.322190433141265975888955985950824418729e1457L, 4.325200102353909846882217732999001735342e1461L, -8.093531903011880118699218269369570178812e1465L, 1.521558881878323790120983450270946857209e1470L, -2.873780311010933807686415826253380907421e1474L, 5.452903697278823304173192839252276211670e1478L, -1.039457922537509500320638240809547113575e1483L, 1.990610112724715126895008793014214505760e1487L, -3.829667853173777076954453401761025071562e1491L, 7.401624504283011888971231756333356050310e1495L, -1.437075122764477911733220492562365990710e1500L, 2.802940275035867428066581228962104019228e1504L, -5.491938363067613321364335249495394164430e1508L, 1.080961960603953462180593404647115933651e1513L, -2.137290931892412298654741768897581319007e1517L, 4.245031321673807283498263276791307370788e1521L, -8.469499523038763989328773224520912663309e1525L, 1.697421812794203793865032206191322699261e1530L, -3.417217332563937242285349373774004020539e1534L, 6.910378594841763785923780822895851271770e1538L, -1.403696282437585785557998429691459557649e1543L, 2.864060533055333035232343601021192111053e1547L, -5.869818290384811353182423286543086530728e1551L, 1.208359745327224593486268988808338456906e1556L, -2.498576742140453770373914215325521001990e1560L, 5.189311407347546310078739863704346083861e1564L, -1.082537954843916294257278789980768336964e1569L, 2.268238255751421312559806122980932952706e1573L, -4.773557403917983369065731568732198697502e1577L, 1.009019097334998841920279535262007639746e1582L, -2.142181266523235177327239693359275472557e1586L, 4.567814904130855969979178320003286614868e1590L, -9.782550516204803195398428611221899469345e1594L, 2.104180123097086948576304557651398411373e1599L, -4.545658958087323864004652894518442709646e1603L, 9.862563944609427542603740078470901803131e1607L, -2.149105846582226970866569209122813809019e1612L, 4.703235567543888152049628411354542509156e1616L, -1.033719212601584878353206879472796545848e1621L, 2.281767401903848796732740825793310514456e1625L, -5.058236070813950229238666252351966279306e1629L, 1.126112519657857205642546937554224492775e1634L, -2.517766761987679577706779689880657777343e1638L, 5.653225190181653388317503182908983211029e1642L, -1.274735955461074142223278576503188429497e1647L, 2.886578974679460464298863945016671299242e1651L, -6.564203307141426181809363135003467581753e1655L, 1.499036144473064593308260681782048262301e1660L, -3.437714715599902386917108442954580869236e1664L, 7.916830957072777234152907034541325149479e1668L, -1.830850567422571420661248197094782575285e1673L, 4.251778280827419894527511469762091846660e1677L, -9.915182507286989818033146623995507108134e1681L, 2.321878208636697663781227497233334385222e1686L, -5.459879022461660582811365437190884471726e1690L, 1.289222044549922720398543474297554204559e1695L, -3.056819658344217799458557578658863826289e1699L, 7.277891759142725294172926258364455941365e1703L, -1.739928293433385104144012025546489673795e1708L, 4.176797408823713136137404972612780406904e1712L, -1.006788178307821554781930741698052910780e1717L, 2.436754569909644399766538111317379484511e1721L, -5.921896599028498715774458493117079340155e1725L, 1.445045688171565118619109316933316429671e1730L, -3.540547766876069233350621578795319652040e1734L, 8.710114552028472554054293344204504325978e1738L, -2.151484527880464463303897113553085899101e1743L, 5.335928195512405709733771642389502809087e1747L, -1.328726408335015910030370523083559660016e1752L, 3.322090527232917400247098823651437597786e1756L, -8.339387326241218096865362177688582376376e1760L, 2.101842203781264395369771906884644062395e1765L, -5.318704469415522036482913743767085545209e1769L, 1.351288005941730688647540059088127991581e1774L, -3.446853546858473171100748720136784228698e1778L, 8.827284762030783576089954173424852998700e1782L, -2.269642226090373319660782216907175419317e1787L, 5.858820683661708553422363777419430816755e1791L, -1.518385813684321665045387969920683656625e1796L, 3.950661327164595923092260035122668890334e1800L, -1.031976516347387969958181456058243183780e1805L, 2.706317892325103782207094286049104555552e1809L, -7.125140422584701175967252533378906957380e1813L, 1.883260203116768075569432925204868418472e1818L, -4.997193687108743666000994570700725873035e1822L, 1.331182722092654526185433799891693838871e1827L, -3.559930289076558484535632566755216035553e1831L, 9.557281027056970446117541983785660301558e1835L, -2.575805002229372523547972911961335317502e1840L, 6.969058431277067406841032797913179025984e1844L, -1.892842481279278678390672746902260183506e1849L, 5.160964211693777744707760614147460787285e1853L, -1.412602588198037643242529860614298968137e1858L, 3.881313379962387603749693387037174052146e1862L, -1.070542170988009009334148472388319844527e1867L, 2.964094312414144330805731101996829908435e1871L, -8.238350132106899955856124602934281976453e1875L, 2.298504171050560756192352106062598639825e1880L, -6.437303944649223478093890316531995121228e1884L, 1.809727811843121957353712606428292269805e1889L, -5.107047553992257935533518628886728031061e1893L, 1.446674478990385642488446075734631327506e1898L, -4.113513327511444762766719175770513771122e1902L, 1.174067517257431444028448391638451935667e1907L, -3.363630086409895071362533854123306097827e1911L, 9.672868956071838221096869293070568259792e1915L, -2.792101741911955365960369780457612630184e1920L, 8.089710604557382430162031502761771390568e1924L, -2.352650988877130983061761312962677887796e1929L, 6.867549079740051556501575104006222995568e1933L, -2.012161201632998475706904405535757516336e1938L, 5.917489529279588702317256137229398357271e1942L, -1.746718667239329545125902248821502764273e1947L, 5.175069416058975040990816515838893249437e1951L, -1.538913401594651457295303469904084052963e1956L, 4.593185746210984655636051293374195150815e1960L, -1.375981868450401919299150690829612124045e1965L, 4.137207965217520410530508053863759216958e1969L, -1.248518564582257710069294326648626362439e1974L, 3.781575291117895093413381897917341286951e1978L, -1.149575999691408110085856948595444100435e1983L, 3.507413095836612229403470531176947165451e1987L, -1.074032838410645352804690949680310176413e1992L, 3.300857202456564870338466973024760446263e1996L, -1.018149578840803516349758843017979498322e2001L, 3.151876950233613792531594490714752800621e2005L, -9.792574827376149360558532022944033224780e2009L, 3.053456145978161645823454710737904504036e2014L, -9.555442346102849014299990542596620094035e2018L, 3.001037449298122384017009412541525703002e2023L, -9.459120112371096268275049056229023773120e2027L, 2.992168042152196502453442556462819104060e2032L, -9.498922680869041470681858599915282791899e2036L, 3.026307717971075309746179763189393755074e2041L, -9.676079238806159594565350708123427510151e2045L, 3.104778286352798464772361361434013339088e2050L, -9.997786802782252742109475924344598057966e2054L, 3.230847952724856366943939804248186203776e2059L, -1.047769651900498931701604323213605884945e2064L, 3.409958102134053489747140426163802214042e2068L, -1.113687894644055086152064258459886518528e2073L, 3.650114509271160332136458711252217684956e2077L, -1.200536387553969483433239131469825141412e2082L, 3.962482337718333099498977337189304099484e2086L, -1.312441206957064803437100929905979391106e2091L, 4.362246723746013772563799740886664288515e2095L, -1.454975881895253548422481637083633839534e2100L, 4.869831412214692119172895822285084162147e2104L, -1.635618419512383251104125916207188960680e2109L, 5.512611314145041257838234038980389596534e2113L, -1.864392957231340288547618808749072127289e2118L, 6.327317613106621547060670091824665547127e2122L, -2.154772001506498703267302897994526372056e2127L, 7.363426139490286496267931634843475368903e2131L, -2.524950643808031915843604894357998905460e2136L, 8.687956390288096215918373666581638675156e2140L, -2.999656978200020459428228924242615592768e2145L, 1.039231328851609224822335039430898644149e2150L, -3.612742437616019936358910410005123924796e2154L, 1.260211309932738404790711574105022002093e2159L, -4.410916378453971105434385837025433805752e2163L, 1.549140617923265948720013792673729394719e2168L, -5.459173749226782924959103886664322964926e2172L, 1.930343307630952098252884031069043541182e2177L, -6.848749229218425353808144618581305978045e2181L, 2.438117138001365487681440577590059588102e2186L, -8.708873656769794358508423272379627581292e2190L, 3.121268068338199458891764932384819739714e2195L, -1.122430216307539309816165910733145404999e2200L, 4.049900779207199370582177687160985635615e2204L, -1.466167983141158219266077836130256565915e2209L, 5.325678718693772500250292767751070974887e2213L, -1.940955845102272053048140384364058448998e2218L, 7.097467198361219669927211698104447309186e2222L, -2.603968771680987683436428778397387110896e2227L, 9.585403285394812946713320044815117440444e2231L, -3.540176030547640510648455468270569908446e2236L, 1.311827683984025111744358347783996339730e2241L, -4.877124229155333857009747836542843294702e2245L, 1.819213075760490882591173222316749809951e2250L, -6.808221630329265915405178596748950929642e2254L, 2.556299969544109052724772800143396857058e2259L, -9.629763347675306704861859899230073979116e2263L, 3.639508580119285595844040783082958425575e2268L, -1.380037493555816309137481185927387732499e2273L, 5.249980712165216709135893538080020409581e2277L, -2.003737844109055078145975651407367170529e2282L, 7.672522280806944397358668566379646540213e2286L, -2.947454993639165318799389781921184991045e2291L, 1.135966912801707623489383623092951142963e2296L, -4.392293711194501621873299212059053651432e2300L, 1.703813210168560937608104155973968112409e2305L, -6.630636743874062041158387022015853902938e2309L, 2.588742636486379690203698247275411406029e2314L, -1.013959594068423546627946242481463893979e2319L, 3.984265821528043268586235974854766821078e2323L, -1.570614519682157047612769672066387881154e2328L, 6.211297381339606877062824459742129064477e2332L, -2.464246931985476159686671650962783785426e2337L, 9.807833742601662212615240518855757197483e2341L, -3.916036434571217691317276306031837539092e2346L, 1.568566392975837368624727722120313955274e2351L, -6.302885887601142677858008037129298948063e2355L, 2.540704455306077495480843691828334210014e2360L, -1.027412480318234348899627142408950111875e2365L, 4.167823618450297116765978030480648316769e2369L, -1.696076602731914277275203926124423530377e2374L, 6.923904505633301788461482786634220738504e2378L, -2.835463065742506394026733592206185459035e2383L, 1.164828772275756526225951620927486307632e2388L, -4.800242878545012539781545966693324656699e2392L, 1.984381759611877246529319121941597679107e2397L, -8.228979942542641498511023600269641046627e2401L, 3.423130231367101727862739208673375060101e2406L, -1.428418168129733054582191895023094524495e2411L, 5.979153801634459282232521647160044877770e2415L, -2.510581926948409809562349588087762800160e2420L, 1.057443785053915411991029410076722022815e2425L, -4.467723713549428749678277264414266162837e2429L, 1.893474116528533144079731251913008472748e2434L, -8.049601965052954947260081891142509464888e2438L, 3.432648527503971149009691133946275281368e2443L, -1.468324699963694393989960228042259134294e2448L,
      //
      // 1302-1600: http://www.wolframalpha.com/input/?i=TABLE[N[Bernoulli[i]%2C40]%2C+{i%2C1302%2C1600%2C2}]
      6.300146502435743791500010801885493871234e2452L, -2.711520667146768856688291798851999580833e2457L, 1.170595555513900137297344452318266434006e2462L, -5.069095411973246242900074508988493530542e2466L, 2.201819284807954055092117706033113168896e2471L, -9.593088725189386197503123561368325167085e2475L, 4.192362385909155628936230811010649614060e2480L, -1.837725836941968309866675158105812946762e2485L, 8.080201101491972605313807752565294881374e2489L, -3.563536075527215702966392543784039539240e2494L, 1.576361051321107275181955665159661781175e2499L, -6.994292466180175594372663323941761853364e2503L, 3.112744353537336702834647901141392426258e2508L, -1.389481328370627358752727485697345194612e2513L, 6.221134636655213696041740685131223999953e2517L, -2.793779613656947577224654924852010601105e2522L, 1.258399062987759035354039924686781081603e2527L, -5.685208194704131918461885165870560583895e2531L, 2.576167857759537340210434756292816456179e2536L, -1.170846052338591953257169251219597581763e2541L, 5.337296787116189575571202979672747140313e2545L, -2.440264475369219459038748840841422948951e2550L, 1.119037151526195093932933161706501865175e2555L, -5.146858829220973887154576240993607686435e2559L, 2.374259791963193693837576781321391741634e2564L, -1.098501215269400934956638118646657823799e2569L, 5.097500369683616795005376807036889542869e2573L, -2.372446971688020647583535886090779018865e2578L, 1.107430282014636546248612381377039463753e2583L, -5.184597227131050012643138079903381280471e2587L, 2.434392040100910394476893838832599310265e2592L, -1.146412753331162872665743308094817095949e2597L, 5.414578104816988124950636101250217797539e2601L, -2.564835392810685332173156758121489913946e2606L, 1.218495070518549208066544111736985586178e2611L, -5.805713573821806672815019495319510297824e2615L, 2.774298194574319430697819781128985128618e2620L, -1.329580186505564627453485444017911980430e2625L, 6.390545858902318479863947547243743500916e2629L, -3.080502542499571035376377703435361520427e2634L, 1.489236104239976282318361008292980814533e2639L, -7.220413839991892382038608955317126799684e2643L, 3.510874916591640642524021216241607185085e2648L, -1.712070118580404599831061485055269100525e2653L, 8.372956919832386730490070625622785478703e2657L, -4.106629146981883685523102256292669054596e2662L, 2.019945438530802964718619732330776495740e2667L, -9.964133277392242111939720494354938982970e2671L, 4.929278642971447854669801547226335041410e2676L, -2.445509657169810919463982615395074704130e2681L, 1.216734421265677299127016883839223226884e2686L, -6.071008437677720186241562251151490713584e2690L, 3.037824949882992896564570441252792097027e2695L, -1.524402878612630565501569310883356490225e2700L, 7.671320530781999359200097739951316234193e2704L, -3.871436167706734376478728954716915204399e2709L, 1.959313530432202158587932399068682252335e2714L, -9.944063618400630821320953821427307024297e2718L, 5.061161998202463346818982228476199873781e2723L, -2.583219090831132705328958245740715185448e2728L, 1.322193991367293532684189527174543501836e2733L, -6.786569982732483290873213417465458376706e2737L, 3.493212334804776543395067018414547811062e2742L, -1.803090099978261928508495412750404640933e2747L, 9.333100843930216567894508007158644926767e2751L, -4.844499031405982604449146511179496492045e2756L, 2.521648090959971240812330574936006906830e2761L, -1.316227870932708474838173333385377250286e2766L, 6.889488826832738674261056521130795910494e2770L, -3.616184242864384509259984293501533623932e2775L, 1.903356124758119137116543283603627028779e2780L, -1.004601544584640657081847200643996069583e2785L, 5.317043885597842225603585588404817559596e2789L, -2.821938866752488868682751438901900485500e2794L, 1.501842023003449590337997900945924161741e2799L, -8.014908048137216649348740300633172710524e2803L, 4.289126235121619907138036129192558937445e2808L, -2.301619137231461344870820700320913118444e2813L, 1.238485136850053215006962645111854705210e2818L, -6.682503731149007943059244518074044280490e2822L, 3.615572393938012932030234169574978859655e2827L, -1.961565108627429629104703146282982075623e2832L, 1.067123259692924564435881096382837264046e2837L, -5.821179870182035246401397327057170726418e2841L, 3.184127229476322727732208017279268211356e2846L, -1.746429902183019597973436257300843998825e2851L, 9.604873565299766333876882842813498685054e2855L, -5.296759978724702692134960752308186890356e2860L, 2.928906353338652198977536576170287112391e2865L, -1.623961162577704769945821804737884742792e2870L, 9.028574047002736235613238355032484299017e2874L, -5.033087486357905828950503441308068892610e2879L, 2.813325650062267479031371852434194635210e2884L, -1.576791132296320840138263753339056345362e2889L, 8.861258343945925667272164531504265693289e2893L, -4.993236404321511029440212686547068244002e2898L, 2.821192993950901287717082243608730217471e2903L, -1.598254169674379493385730199445427966752e2908L, 9.078617590346932363947095804057608979359e2912L, -5.170742114456472142154347566092068443393e2917L, 2.952866185102528847516095880416675972086e2922L, -1.690794578626103552690094140317813413244e2927L, 9.707168799669516048238542260085175133847e2931L, -5.587884732306715493795271931175883605707e2936L, 3.225179489154957423492905957887744116530e2941L, -1.866424419669188178697802576490431604300e2946L, 1.082967626854618222657109354056973072044e2951L, -6.300392007169862865282706277272018077291e2955L, 3.675066377245428685118763485986517510658e2960L, -2.149348371085132073107516253339849053182e2965L, 1.260349351812619395000600434630904474324e2970L, -7.409963623771231302980906971935254993610e2974L, 4.367980758467862686643231700861155889684e2979L, -2.581566823350789671250829457603555544100e2984L, 1.529757357568342629912560827243282062227e2989L, -9.088595394263364554625061567617375176719e2993L, 5.413829169254585648363594604231030415354e2998L, -3.233288119606092759447005827969216281573e3003L, 1.936042437734875803183915765854038424658e3008L, -1.162289934202291715747729318797398221667e3013L, 6.995870350500567071550614251287615697508e3017L, -4.221776496490106417392945233048068288503e3022L, 2.554309239868912570382343877718991746122e3027L, -1.549440871550119801225143558087410562418e3032L, 9.423199525954784955533959981278992475051e3036L, -5.745689660772387668861183913170050552119e3041L, 3.512407521007240798565045328376471603253e3046L, -2.152708113797517364614914569890010876143e3051L, 1.322761289733739440340237168659770154654e3056L, -8.148777388506488753591136948542248584098e3060L, 5.032880858479326069741729004270784264612e3065L, -3.116396010103058126269735274818345780360e3070L, 1.934634831148214353514796782480703021435e3075L, -1.204077166243116651938489240924641810276e3080L, 7.513065583444964704795707060501161621868e3084L, -4.699873512563164914493150520500838535415e3089L, 2.947541197349762411713872934523813866703e3094L, -1.853262416286420077763886100673646141885e3099L, 1.168196427912100545575264493997591040800e3104L, -7.382362285873345348505276546404015842875e3108L, 4.677071041058096429847797962954927487730e3113L, -2.970642034084362431442183248944824506476e3118L, 1.891572688282564476274920103912259755482e3123L, -1.207509963440193713810418554061532113326e3128L, 7.727731208240101791845515599659441557781e3132L, -4.957988488048495669466804712012179891532e3137L, 3.188965862446236259925047956715566822864e3142L, -2.056286895821370106507670239256782411337e3147L, 1.329246918771714093479509313343886287414e3152L, -8.614188519577835653765633797787633659253e3156L,
      //
      // 1602-1900: http://www.wolframalpha.com/input/?i=TABLE[N[Bernoulli[i]%2C40]%2C+{i%2C1602%2C1900%2C2}]
      5.596396533621874175909933615343145642161e3161L, -3.644908483469388437457938883454376864180e3166L, 2.379838409026860469990569665632800095988e3171L, -1.557720925267669865362152155022069166772e3176L, 1.022143420270029721682551084917730373739e3181L, -6.723767358891570842116651998814252095792e3185L, 4.433950491570308179905446963723780229747e3190L, -2.931196854668917448553150023532223509373e3195L, 1.942557068752664549549945921392100172355e3200L, -1.290553202978622786891265558106235068695e3205L, 8.595082329732118303768775883557789195136e3209L, -5.738453265222970049867280061719670658457e3214L, 3.840687915100689856736926915331157331684e3219L, -2.576862441955523551149886625900059307506e3224L, 1.733166107320377310388765047659987844208e3229L, -1.168569552450178559412843683052610870569e3234L, 7.898289836694980777809433306209459851871e3238L, -5.351485909164216694400535493924387979018e3243L, 3.634772439350395177931952925644409735777e3248L, -2.474801048002975145046569303233576339695e3253L, 1.689126939254790850063878942448569759390e3258L, -1.155691524500722774057997965355407962525e3263L, 7.926435404542361405718288670391575676323e3267L, -5.449654814183048796524718620178906854846e3272L, 3.755898589900254795894812942275711835138e3277L, -2.594843902682143854622514329649211211808e3282L, 1.797048752397789969347915328338360264536e3287L, -1.247551415074438712713815166107969504456e3292L, 8.681719521514448143910215886388510318746e3296L, -6.056203898213120922016159444227958572276e3301L, 4.234882876331814099029781995617143573641e3306L, -2.968432911643338866295929748049749932906e3311L, 2.085723508930484816454740610260790948864e3316L, -1.469023169879432026361623513301566735138e3321L, 1.037150346505052892302077637883522696572e3326L, -7.339977067836656769144838365069396168014e3330L, 5.206985412168234130596004552956337839140e3335L, -3.702673773319239583641029108403509825141e3340L, 2.639251227995760315076225206168354089692e3345L, -1.885736353072698581595150856674914203383e3350L, 1.350563292338261784288559687678302458996e3355L, -9.695749980998301526113046898985991802000e3359L, 6.977167462628398202151721319169989304520e3364L, -5.032768280399753942925624560483352299263e3369L, 3.638844963651800168080623511900705036698e3374L, -2.637228631269251606169613775399022890118e3379L, 1.915836351653767108720464847696767898597e3384L, -1.395064293615007319328267865803567670760e3389L, 1.018249052614943190644465556486933211307e3394L, -7.449662162606857550867922631658930320805e3398L, 5.463119632208085241594107781601567713991e3403L, -4.015736541676989144201935890497836963875e3408L, 2.958754190183866660901503059509579790900e3413L, -2.185096074054288399312733179064098492511e3418L, 1.617517444557020250864919655301189186103e3423L, -1.200170662015511746748935675940010250555e3428L, 8.925888349899029449015791684428724952411e3432L, -6.653851763691885517669938275618991145962e3437L, 4.971722031098457895973348076474071155918e3442L, -3.723500582577984967442020337848702786829e3447L, 2.795153783541721373364976034391375710110e3452L, -2.103141577212720698169118819883801186873e3457L, 1.586129575320959267959148073466004084241e3462L, -1.198988457279648730711646682156242973137e3467L, 9.084402368157025658430300252246526602197e3471L, -6.898927494435965163817354296023108913714e3476L, 5.251332286149361587885046891266325872375e3481L, -4.006442950956739933884502808470603581850e3486L, 3.063718202820270282280659950794978994604e3491L, -2.348215284130973783732145823834807395920e3496L, 1.803952490148087317330011096671019781340e3501L, -1.389022326803437345760911068933754707688e3506L, 1.071986115818329525986099441493200866389e3511L, -8.292085224650940719705699485423856363908e3515L, 6.428829064452939640541475198655560890344e3520L, -4.995654440302797445368056643032307686314e3525L, 3.890847042582299188849273838681034339406e3530L, -3.037288555751484681537442833929275697351e3535L, 2.376385803695694695338601696534348875191e3540L, -1.863527130251861900692886008704804849076e3545L, 1.464674913498036269270793715104706378182e3550L, -1.153804954579033578659954846698233083197e3555L, 9.109783835348935092264268296199541780964e3559L, -7.208869193983001804305451104827153729326e3564L, 5.717530734277611949162917337810749919265e3569L, -4.544970302634007326980094771330550661605e3574L, 3.621042850825283032134228901678636353355e3579L, -2.891447067949778492831490654980043715471e3584L, 2.314060419397710657435821461707043283167e3589L, -1.856140759923563235273220981623595304434e3594L, 1.492185412981476596273279338314204171587e3599L, -1.202290032627175365810126250991853594801e3604L, 9.708881154579770196658265042625239421053e3608L, -7.857809850747029705680072304049448493252e3613L, 6.373898598298513400228819113197728735438e3618L, -5.181780406472117449048907989647202286666e3623L, 4.222036621953044040518942750638183171221e3628L, -3.447728386429130175025813550845575613047e3633L, 2.821701521717856346224159586852612710800e3638L, -2.314488376711998526455043944505424906920e3643L, 1.902671298033180765286213227393060711096e3648L, -1.567603736821312488140289549008391847440e3653L, 1.294408945316538946551785312385509945367e3658L, -1.071194533081615830960091702262923009420e3663L, 8.884351908108581551151252566466606126397e3667L, -7.384866682828103669170236267589653324531e3672L, 6.152023838008155718180876735217718355563e3677L, -5.136304310431705506236573876510219357975e3682L, 4.297736808124296434723193397876220759378e3687L, -3.603994887745884762510172194982172483480e3692L, 3.028884745605031552399167746007361297342e3697L, -2.551141302205187365552982635794121855138e3702L, 2.153467982869535549299173317536193051608e3707L, -1.821769476343602094059466497311600827296e3712L, 1.544537580582347892980177956984101211006e3717L, -1.312358705945937257247030754517293537539e3722L, 1.117518229297781388884979995402355617235e3727L, -9.536820860779441793021624381677086661097e3731L, 8.156400668831968026931547065507466530546e3736L, -6.990984948728184142718575396052260691181e3741L, 6.005124901126818071638224144541102727563e3746L, -5.169500241880947716732682089328427995109e3751L, 4.459815478235310026240134567325749844182e3756L, -3.855902253361684187081283218890336962427e3761L, 3.340988024176995223515640815937037040546e3766L, -2.901099226680215736735094376078800376829e3771L, 2.524573363444334459448089563912567842927e3776L, -2.201659455716348555524529213295341212492e3781L, 1.924190302190936448078364755844591374353e3786L, -1.685313186099770223843319514432495898517e3791L, 1.479268235966730475749985741048766689808e3796L, -1.301205702893883803117530921635013780575e3801L, 1.147035071153450453405384269242743907426e3806L, -1.013300250456366849150496776951686112298e3811L, 8.970761720605591762300958007557533865346e3815L, -7.958829781488943084496783248922217392838e3820L, 7.076146954685024795720193943027902028642e3825L, -6.304798526260409199660290516451546966159e3830L, 5.629519616664188107056583939722984509867e3835L, -5.037281594099054092767959480843344929292e3840L, 4.516946091316834843581919268794683123349e3845L, -4.058975118925834202620358386772092359951e3850L, 3.655187798978978909014603682039470653549e3855L, -3.298555903041546671060101785513812175322e3860L, 2.983031738662727912016882399515879119620e3865L, -2.703403043317732979516341931451317866898e3870L, 2.455170460800096241793872443768546335444e3875L, -2.234443928432490538417605502448376856290e3880L, 2.037854924078003280537856980560782325730e3885L, -1.862482033918775734840779765743099458137e3890L,
      //
      // 1902-2200: http://www.wolframalpha.com/input/?i=TABLE[N[Bernoulli[i]%2C40]%2C+{i%2C1902%2C2200%2C2}]
      1.705787724951999960095629912416210969679e3895L, -1.565564556110550991891247404758895970376e3900L, 1.439889351869832939488618785632174464789e3905L, -1.327084102784257406218693901793045990520e3910L, 1.225682557296027075027021534960026145706e3915L, -1.134401635488994148555787301654561211982e3920L, 1.052116934052356802920509999705307165985e3925L, -9.778417073593082219082361206542342793584e3929L, 9.107088061888562704837019028349522303725e3934L, -8.499551364633102138471246155980056936129e3939L, 7.949082681085658044610890152056533167407e3944L, -7.449748809722797718736397140511396011691e3949L, 6.996307824769340144608141799981589288378e3954L, -6.584122718472954006131003060359621706243e3959L, 6.209086595833487707192492087176843233407e3964L, -5.867557793863165391821489909125720982339e3969L, 5.556303538475260373917478405626416604297e3974L, -5.272450955936249442242634142613834212778e3979L, 5.013444428433789818228792126117223030641e3984L, -4.777008429684552423800736200488532033034e3989L, 4.561115100786341787876705283291018781137e3994L, -4.363955932181992701667719449097126840439e3999L, 4.183917007557000586305945495258591147615e4004L, -4.019557342177353010692923286760895584096e4009L, 3.869589913635745758786275231296652917580e4014L, -3.732865038934070181861017140563175000872e4019L, 3.608355799736107390800162778737339576843e4024L, -3.495145258697474565347261083975193776541e4029L, 3.392415245050326563747729613872524362741e4034L, -3.299436517958948801426629481782413630714e4039L, 3.215560142306355508598119430378551642857e4044L, -3.140209934146377815556058799557727461298e4049L, 3.072875852591406752692761744649563131272e4054L, -3.013108231854799187724018548255922550991e4059L, 2.960512761914376268185064129600549308882e4064L, -2.914746139139036596123006476633770383901e4069L, 2.875512319506974985103149834921665445532e4074L, -2.842559316984704569380036093537576068104e4079L, 2.815676498441436148701483904115879856704e4084L, -2.794692334326268275058539147656334465534e4089L, 2.779472571396106785963004020814493340829e4094L, -2.769918800191406321625251621260024635680e4099L, 2.765967395840433013288935879837390099329e4104L, -2.767588816244119880300161388073836623878e4109L, 2.774787246856347651152278076466043136230e4114L, -2.787600586224957950622601135620189837948e4119L, 2.806100771288225169339048358106052817280e4124L, -2.830394446218080573456394167711739786431e4129L, 2.860623983452244712039094143642843717029e4134L, -2.896968870550611723525738907034588104300e4139L, 2.939647481737606306044335918078617963078e4144L, -2.988919258547518526076380181812161398808e4149L, 3.045087329976721023952450383837883029431e4154L, -3.108501609077197464748958150625867523408e4159L, 3.179562410123820875787052833975010965963e4164L, -3.258724638491880104953913719767939138170e4169L, 3.346502614347964869115073881474258766546e4174L, -3.443475601364631413158991572423086599816e4179L, 3.550294123121350747300886840907918182129e4184L, -3.667687162886053419715985091863398517145e4189L, 3.796470357354794420044278000297864085607e4194L, -3.937555311976846882455930574021795626971e4199L, 4.091960185075595842547638450930710467324e4204L, -4.260821710519620959138720129506770036460e4209L, 4.445408854703156440576808070360934740837e4214L, -4.647138333645908068599900650548418672065e4219L, 4.867592250805288922190809906525766574205e4224L, -5.108538156515551259475573296900660666192e4229L, 5.371951876776035157276013631113314852508e4234L, -5.660043513521220243900043448456234873940e4239L, 5.975287081834808618140945840817834710330e4244L, -6.320454323372684034118816565375206053746e4249L, 6.698653321371992324876559665938996023646e4254L, -7.113372643219128807424340495235606473967e4259L, 7.568531854202750881338746432078817214052e4264L, -8.068539383842553693076672384509126681464e4269L, 8.618358887685935324188596304168259394311e4274L, -9.223585437012291673660319256730398171887e4279L, 9.890533091606747031464718533600572123091e4284L, -1.062633567277107015128545384570274268438e4290L, 1.143906286231591191271274413511275981288e4295L, -1.233785411712565904499340744089870916842e4300L, 1.333307331840530219050170916015276125870e4305L, -1.443648758235403286296065629219598769529e4310L, 1.566147425967471851736562867318748510088e4315L, -1.702326086290842780634120184324081017286e4320L, 1.853920350455786350409148418966087344063e4325L, -2.022911043115598592197907512410632615740e4330L, 2.211561842992792253055716743938240466613e4335L, -2.422463130294011318178080247305407476096e4340L, 2.658583129381772791030436640519847627789e4345L, -2.923327636881988941081365085520742216540e4350L, 3.220609866329557159104267531058019683271e4355L, -3.554932228621330128152149026066400241546e4360L, 3.931482212643167323798366327390058684499e4365L, -4.356244944221399578650235478583297389113e4370L, 4.836135498303121165971331625888490168138e4375L, -5.379154636371461359750682662639062606297e4380L, 5.994572359716861309678596804350346692501e4385L, -6.693144535124290060793936095397161934045e4390L, 7.487368894313509797084395689517008597061e4395L, -8.391787970609807810531578161564037339793e4400L, 9.423348062978921203475110312003096820035e4405L, -1.060182516651648405903017734022504884319e4411L, 1.195033105063952979885086754342706651656e4416L, -1.349591538868673992167798923586925758429e4421L, 1.527028315253291113905307092657539132480e4426L, -1.731065051510920640409442255224015234974e4431L, 1.966076741510092840076264635935585216200e4436L, -2.237214093245750681191361238831105906202e4441L, 2.550550094903891445719729187215253324232e4446L, -2.913255853313667303707651906277658164129e4451L, 3.333811847072394764285817140850092324169e4456L, -3.822262084288044913490118858492563410392e4461L, 4.390520310533864198186202368026630430120e4466L, -5.052739449335052080092114976206610871466e4471L, 5.825757966350870043117899492954521458799e4476L, -6.729639942938203582008846884575881320532e4481L, 7.788329466816396015493306357116312471970e4486L, -9.030444674469025073047417528762134025409e4491L, 1.049024263381993629167658236142000524752e4497L, -1.220879351508964912255081664072251573277e4502L, 1.423541151220109512749655991050110438471e4507L, -1.662940118618541616964708044356967429362e4512L, 1.946219185900482116137855064775635250366e4517L, -2.281995008842006909631764011781911322493e4522L, 2.680678198213108543648324254258111216040e4527L, -3.154866427472784086389609599207759103500e4532L, 3.719827710160801797530420206201570269720e4537L, -4.394095404360277919140027580071549980218e4542L, 5.200201854779615608741690339830306148442e4547L, -6.165584312943608652377791415603277251516e4552L, 7.323705248531382981433751104158852636445e4557L, -8.715439846124090647163930834760361817820e4562L, 1.039079696609215651011736087603304766850e4568L, -1.241105689556982425619608247473478857800e4573L, 1.485143079696380339521658550262280772546e4578L, -1.780437412164973637340821168154300094802e4583L, 2.138372099157518882088209435171770222745e4588L, -2.572985071149069551034276570909360759588e4593L, 3.101615379617643734762997559011097203354e4598L, -3.745713657616368229906151946770042703357e4603L, 4.531859496161940719835150033082561700677e4608L, -5.493040495326927998321538336584233566465e4613L, 6.670262730603009306595018122252730741798e4618L, -8.114581584793494903775255213273982440688e4623L, 9.889666561810883044159054730371102725871e4628L, -1.207504541653929734716275932570097623330e4634L, 1.477021377885843688233899471354959308782e4639L, -1.809984912147908767583043524070645821179e4644L,
      //
      // 2202-2320: http://www.wolframalpha.com/input/?i=TABLE[N[Bernoulli[i]%2C40]%2C+{i%2C2202%2C2320%2C2}]
      2.222043594325228980916360265527780300093e4649L, -2.732869701246338361699515268224049951411e4654L, 3.367233945421922463553518272642397177145e4659L, -4.156377225041273602431272489314020150392e4664L, 5.139764368092890466235162431795350591151e4669L, -6.367329693760865476879589228002216011370e4674L, 7.902356742934106007362514378717026407839e4679L, -9.825176966314431712897976595483070301406e4684L, 1.223792760178593282435724837135946867088e4690L, -1.527068151452750404853140815207477555192e4695L, 1.908935682572268829496101580401263597905e4700L, -2.390593888616966248780378941331847473699e4705L, 2.999171106576893833644521002894489856321e4710L, -3.769440655453736670024798444784356437578e4715L, 4.746047769851891438576002047529258107351e4720L, -5.986405469241447720766576164546767533359e4725L, 7.564466155536872051712519119999711534616e4730L, -9.575641408047918720040356745796976488951e4735L, 1.214322951835035451699619713803395497423e4741L, -1.542682591979864353012093794301924196234e4746L, 1.963334539793192183270983986567556358603e4751L, -2.503148969013901182572118121398034622584e4756L, 3.197076711250102964526567664729089847162e4761L, -4.090653552025822488578293526174572934858e4766L, 5.243302769651520536759521264615159906699e4771L, -6.732697170903775309261288127044088674182e4776L, 8.660529543801770516930589210020128142543e4781L, -1.116015823611149634592870112730519454113e4787L, 1.440675306432920129218036927923030695520e4792L, -1.863078034853256227415397798026969938881e4797L, 2.413595413458810442409656314019115041699e4802L, -3.132317029597258599678590012779717945144e4807L, 4.072246763371584312534474102756137619716e4812L, -5.303577511521827157146305369181950467569e4817L, 6.919417518688636032335131253584331645491e4822L, -9.043473312934241153732087612484569398979e4827L, 1.184037400265044213826044590639924237359e4833L, -1.552956685415800894409743993367334099777e4838L, 2.040404893052952221581694807126473204625e4843L, -2.685565763841580219033402331219206776210e4848L, 3.540927057361929050327811875290025248120e4853L, -4.676912607538885419407656762767991163574e4858L, 6.188165903566760647569323704623433330229e4863L, -8.202087471895029964699042637255411806373e4868L, 1.089045274355389654614196651761310970580e4874L, -1.448524684976553869119447042300206226148e4879L, 1.930028100376784839502387280956424581974e4884L, -2.576074799096023589462128312524664980682e4889L, 3.444369635011990347297134928452972402038e4894L, -4.613354441299253694113609154769978684993e4899L, 6.189834306866879018555349507257537840922e4904L, -8.319470760665157534580593571258276368233e4909L, 1.120124240070996761986102680587384813245e4915L, -1.510740451399746828351090108638980398124e4920L, 2.041108231091323198877509959371257503819e4925L, -2.762447751447012472733302936575873838539e4930L,
#endif
      } };
#endif

template <class T>
inline BOOST_MATH_CONSTEXPR_TABLE_FUNCTION T unchecked_bernoulli_imp(std::size_t n, const std::integral_constant<int, 3>& )
{
   return unchecked_bernoulli_data<T, 3>::bernoulli_data[n];
}

template <class T>
inline T unchecked_bernoulli_imp(std::size_t n, const std::integral_constant<int, 4>& )
{
   //
   // Special case added for multiprecision types that have no conversion from long long,
   // there are very few such types, but mpfr_class is one.
   //
   static const std::array<std::int32_t, 1 + max_bernoulli_b2n<T>::value> numerators =
   {{
      std::int32_t(            +1LL),
      std::int32_t(            +1LL),
      std::int32_t(            -1LL),
      std::int32_t(            +1LL),
      std::int32_t(            -1LL),
      std::int32_t(            +5LL),
      std::int32_t(          -691LL),
      std::int32_t(            +7LL),
      std::int32_t(         -3617LL),
      std::int32_t(        +43867LL),
      std::int32_t(       -174611LL),
      std::int32_t(       +854513LL),
   }};

   static const std::array<std::int32_t, 1 + max_bernoulli_b2n<T>::value> denominators =
   {{
      std::int32_t(      1LL),
      std::int32_t(      6LL),
      std::int32_t(     30LL),
      std::int32_t(     42LL),
      std::int32_t(     30LL),
      std::int32_t(     66LL),
      std::int32_t(   2730LL),
      std::int32_t(      6LL),
      std::int32_t(    510LL),
      std::int32_t(    798LL),
      std::int32_t(    330LL),
      std::int32_t(    138LL),
   }};
   return T(numerators[n]) / T(denominators[n]);
}

} // namespace detail

template<class T>
inline BOOST_MATH_CONSTEXPR_TABLE_FUNCTION T unchecked_bernoulli_b2n(const std::size_t n)
{
   typedef std::integral_constant<int, detail::bernoulli_imp_variant<T>::value> tag_type;

   return detail::unchecked_bernoulli_imp<T>(n, tag_type());
}

}} // namespaces

#endif // BOOST_MATH_UNCHECKED_BERNOULLI_HPP
