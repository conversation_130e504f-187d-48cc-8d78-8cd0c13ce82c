# ==== Purpose ====
#
# This test's goal is to check that no deadlock occurs in an SPCO enabled
# multi-threaded replica when client connections issue global locking or
# ACL statements concurrently with the applier worker threads.
#
# ==== Requirements ====
#
#
# R1. A multi-threaded applier with enabled commit order preservation MUST
#     be able to break deadlocks mixing commit order serialization and
#     global locks like the ones taken by `SET READ_ONLY = ON` or `FLUSH
#     TABLES WITH READ LOCK`.
#
# R2. A multi-threaded applier with enabled commit order preservation MUST
#     be able to break deadlocks mixing commit order serialization and ACL
#     related locks.
#
# R3. A multi-threaded applier with enabled commit order preservation MUST
#     keep the current retry behavior by enforcing the
#     `replica_transaction_retries` variable value for the amount of retries,
#     when requested to back-off a transaction deadlocked by _R1_ and _R2_
#     described scenarios.
#
# R4. A multi-threaded applier with enabled commit order preservation MUST
#     keep the current replication channel behavior by stopping both the
#     coordinator and worker threads when the configured amount of
#     transaction retries is reached unsuccessfully, when requested to
#     back-off a transaction deadlocked by _R1_ and _R2_ described
#     scenarios.
#
# ==== Implementation ====
#
# Setup
# -----
#   1. Create two tables on the source
#   2. Setup replica to be multi-threaded
#
# Scenario logic
# --------------
# The scenarios being tested are all built upon a generic logic that will
# serliaze the client side execution and the MTA worker thread in order to
# force a deadlock situation that should be broken. This logic is
# implemented in `common/rpl/mta_rpco_generate_deadlock.inc` and the file
# contains a description on how it's done. A high-level description is:
#
#   1. On the source, set up a commit group that contains statements that
#      shouldn't be paralellized together using DBUG sync points.
#   2. On the replica, using client connection A, start a transaction and
#      assign it the same GTID as the first statement issued on the source.
#   3. On the replica, using an additional client connection, issue a
#      statement that will introduce a circular locking dependency with the
#      worker threads and their assigned statements.
#   4. Wait until all worker threads and client connections are waiting on
#      each other.
#   5. On the replica, rollback the transaction started in 2., leading to
#      the locking circularity to be complete.
#   6. Wait for the replica to error out or to overcome the deadlock,
#      depending on the configuration. In this test-case, the replica
#      should error out.
#   7. Revert the effects of 3., breaking the deadlock and restart
#      replication.
#
#   Also, each test case is repeated twice: once where worker threads are
#   all made to wait on the commit order queue and all are required to
#   back-off by the MDL infra-structure; once where the worker executing
#   the last transaction on the commit order is made to wait before
#   arriving the commit stage and until the deadlock is identified, in
#   which case this last worker will be required to back-off by the state
#   of the commit order queue.
#
# TC1. `GLOBAL READ_ONLY=ON` issued by client and DDL replicated from source.
# -----------------------------------------------------------------------------
#   1. Alter a table and insert rows on the source in a way that they will
#      belong to the same commit group.
#   2. On the replica, using client connection B, execute `SET GLOBAL
#      READ_ONLY=ON` to acquire the global read lock, leading to the
#      following lock acquisition dependencies: Client B --commit lock-->
#      Worker 2 --commit order lock--> Worker 1 --gtid lock--> Client A.
#   3. On the replica, using client conneciton A, rollback the pending
#      transaction, leading to the following lock acquisition dependencies:
#      Client B --commit lock--> Worker 2 --commit order lock--> Worker 1
#      --global read lock--> Client B.
#   4. Wait for the replication applier to error out with
#      ER_LOCK_DEADLOCK or ER_REPLICA_WORKER_STOPPED_PREVIOUS_THD_ERROR.
#   5. On the replica, using client connection B, execute `SET GLOBAL
#      READ_ONLY=OFF` to let the applier worker threads to finish after
#      restart of the applier.
#
# TC2. `GLOBAL READ_ONLY=ON` issued by client and DML replicated from source.
# -----------------------------------------------------------------------------
#   1. Insert rows on the source in a way that they will belong to the same
#      commit group.
#   2. On the replica, using client connection B, execute `SET GLOBAL
#      READ_ONLY=ON` to acquire the global read lock, leading to the
#      following lock acquisition dependencies: Client B --commit lock-->
#      Worker 2 --commit order lock--> Worker 1 --gtid lock--> Client A.
#   3. On the replica, using client conneciton A, rollback the pending
#      transaction, leading to the following lock acquisition dependencies:
#      Client B --commit lock--> Worker 2 --commit order lock--> Worker 1
#      --global read lock--> Client B.
#   4. Wait for the replication applier to error out with
#      ER_LOCK_DEADLOCK or ER_REPLICA_WORKER_STOPPED_PREVIOUS_THD_ERROR.
#   5. On the replica, using client connection B, execute `SET GLOBAL
#      READ_ONLY=OFF` to let the applier worker threads to finish.
#
# TC3. `FLUSH TABLES WITH READ LOCK` issued by client and DDL replicated
#      from source.
# -----------------------------------------------------------------------------
#   1. Alter a table and insert rows on the source in a way that they will
#      belong to the same commit group.
#   2. On the replica, using client connection B, execute `FLUSH TABLES
#      WITH READ LOCK` to acquire the global read lock, leading to the
#      following lock acquisition dependencies: Client B --commit lock-->
#      Worker 2 --commit order lock--> Worker 1 --gtid lock--> Client A.
#   3. On the replica, using client conneciton A, rollback the pending
#      transaction, leading to the following lock acquisition dependencies:
#      Client B --commit lock--> Worker 2 --commit order lock--> Worker 1
#      --global read lock--> Client B.
#   4. Wait for the replication applier to error out with
#      ER_LOCK_DEADLOCK or ER_REPLICA_WORKER_STOPPED_PREVIOUS_THD_ERROR.
#   5. On the replica, using client connection B, execute `UNLOCK TABLES`
#      to let the applier worker threads to finish.
#
# TC4. `FLUSH TABLES WITH READ LOCK` issued by client and DML replicated
#      from source.
# -----------------------------------------------------------------------------
#   1. Insert rows on the source in a way that they will belong to the same
#      commit group.
#   2. On the replica, using client connection B, execute `SET GLOBAL
#      READ_ONLY=ON` to acquire the global read lock, leading to the
#      following lock acquisition dependencies: Client B --commit lock-->
#      Worker 2 --commit order lock--> Worker 1 --gtid lock--> Client A.
#   3. On the replica, using client conneciton A, rollback the pending
#      transaction, leading to the following lock acquisition dependencies:
#      Client B --commit lock--> Worker 2 --commit order lock--> Worker 1
#      --global read lock--> Client B.
#   4. Wait for the replication applier to error out with
#      ER_LOCK_DEADLOCK or ER_REPLICA_WORKER_STOPPED_PREVIOUS_THD_ERROR.
#   5. On the replica, using client connection B, execute `UNLOCK TABLES`
#      to let the applier worker threads to finish.
#
# TC5. DML issued by client and `XA PREPARE` replicated from source.
# -----------------------------------------------------------------------------
#   1. Insert row on the source withing an XA transaction and issue `XA
#      PREPARE` in a way that they will belong to the same commit group.
#   2. On the replica, using client conneciton A, rollback the pending
#      transaction, leading to the following lock acquisition dependencies:
#      Worker 1 --data lock--> Client B.
#   3. Wait for the replica applier to error out with ER_LOCK_WAIT_TIMEOUT.
#   4. On the source, issue `XA COMMIT` to allow the applier worker threads
#      to finish.
#
# ==== References ====
#
# WL#13574 Include MDL and ACL locks in MTS deadlock detection
#          infra-structure
# BUG#29930339 MTS ORDERED COMMIT MAY CAUSE DEADLOCK
# BUG#27798650 DEADLOCK OCCURRED BY "FLUSH TABLES WITH READ LOCK" WHEN USING
#              MTS
# BUG#27415352 DEADLOCK WITH MTS WHEN REPLICA_PRESERVE_COMMIT_ORDER = ON.
# BUG#28165684 REPLICATION PARALLEL APPLIER BLOCKS AND PREVENTS SET GLOBAL
#              SUPER_READ_ONLY= 1
# BUG#29239526 PARALLEL WORKERS+REPLICA_PRESERVE_COMMIT_ORDER+FLUSHTABLES WITH
#              READ LOCK DEADLOCK
# BUG#29961792 ACL COMMANDS + DML ON MASTER MAY HANG SPCO ENABLED SLAVE
#              SERVER
# BUG#26277791 DEADLOCK WITH REPLICA_PRESERVE_COMMIT_ORDER=ON WITH BUG#86078.
# BUG#26883680 COMMIT_ORDER_MANAGER CAN'T TERMINATE MTS WORKER PROPERLY WHEN
#              DEADLOCK HAPPENS
# BUG#29512103 OPTIMIZE TABLE CAUSE THE SLAVE MTS DEADLOCK!
#
--source include/have_debug_sync.inc
--let $rpl_privilege_checks_user = *:'applier'@'localhost'
--source include/rpl/init_source_replica.inc

CALL mtr.add_suppression(".*Unsafe statement written to the binary log using statement format since BINLOG_FORMAT = STATEMENT.*");

CREATE TABLE t1(c1 INT PRIMARY KEY, c2 INT, INDEX(c2)) ENGINE = InnoDB;
INSERT INTO t1 VALUES
    (1, NULL),
    (2, 2),
    (3, NULL),
    (4, 4),
    (5, NULL),
    (6, 6);

CREATE TABLE t2(a INT PRIMARY KEY);

--source include/rpl/sync_to_replica.inc

--source common/rpl/mta_rpco_generate_deadlock_setup.inc

--let $rpl_diff_statement = SELECT * FROM t1, t2

--echo
--echo #
--echo # TC1. `GLOBAL READ_ONLY=ON` issued by client and DDL replicated
--echo #      from source.
--echo #------------------------------------------------------------------------
--source include/rpl/connection_source.inc
INSERT INTO t1 VALUES(10, 10);

--let $mts_spco_gd_trx_blocking_worker_1 = ALTER TABLE t2 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=1
--let $mts_spco_gd_trx_assigned_worker_2 = INSERT INTO t1 VALUES(11, NULL)
--let $mts_spco_gd_trx_assigned_worker_3 = INSERT INTO t1 VALUES(12, NULL)
--let $mts_spco_gd_worker_3_only_runs_after_deadlock = 0
--let $mts_spco_gd_trx_blocking_client = SET GLOBAL read_only = ON
--let $mts_spco_gd_state_blocking_client = Waiting for commit lock
--let $mts_spco_gd_error_expected_replica = ER_LOCK_DEADLOCK
--let $mts_spco_gd_trx_to_client_unblocking_workers = SET GLOBAL read_only = OFF
--source common/rpl/mta_rpco_generate_deadlock.inc

--source include/rpl/connection_source.inc
INSERT INTO t1 VALUES(13, 13);
--let $mts_spco_gd_trx_blocking_worker_1 = ALTER TABLE t2 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=1
--let $mts_spco_gd_trx_assigned_worker_2 = INSERT INTO t1 VALUES(14, NULL)
--let $mts_spco_gd_trx_assigned_worker_3 = INSERT INTO t1 VALUES(15, NULL)
--let $mts_spco_gd_worker_3_only_runs_after_deadlock = 1
--let $mts_spco_gd_trx_blocking_client = SET GLOBAL read_only = ON
--let $mts_spco_gd_state_blocking_client = Waiting for commit lock
--let $mts_spco_gd_error_expected_replica = ER_REPLICA_WORKER_STOPPED_PREVIOUS_THD_ERROR
--let $mts_spco_gd_trx_to_client_unblocking_workers = SET GLOBAL read_only = OFF
--source common/rpl/mta_rpco_generate_deadlock.inc


--echo
--echo #
--echo # TC2. `GLOBAL READ_ONLY=ON` issued by client and DML replicated
--echo #      from source.
--echo #------------------------------------------------------------------------
--source include/rpl/connection_source.inc
INSERT INTO t1 VALUES(16, 16);

--let $mts_spco_gd_trx_blocking_worker_1 = INSERT INTO t1 VALUES(17, NULL)
--let $mts_spco_gd_trx_assigned_worker_2 = INSERT INTO t1 VALUES(18, NULL)
--let $mts_spco_gd_trx_assigned_worker_3 = INSERT INTO t1 VALUES(19, NULL)
--let $mts_spco_gd_worker_3_only_runs_after_deadlock = 0
--let $mts_spco_gd_trx_blocking_client = SET GLOBAL read_only = ON
--let $mts_spco_gd_error_expected_replica = ER_LOCK_DEADLOCK
--let $mts_spco_gd_trx_to_client_unblocking_workers = SET GLOBAL read_only = OFF
--source common/rpl/mta_rpco_generate_deadlock.inc

--source include/rpl/connection_source.inc
INSERT INTO t1 VALUES(20, 20);

--let $mts_spco_gd_trx_blocking_worker_1 = INSERT INTO t1 VALUES(21, NULL)
--let $mts_spco_gd_trx_assigned_worker_2 = INSERT INTO t1 VALUES(22, NULL)
--let $mts_spco_gd_trx_assigned_worker_3 = INSERT INTO t1 VALUES(23, NULL)
--let $mts_spco_gd_worker_3_only_runs_after_deadlock = 1
--let $mts_spco_gd_trx_blocking_client = SET GLOBAL read_only = ON
--let $mts_spco_gd_error_expected_replica = ER_REPLICA_WORKER_STOPPED_PREVIOUS_THD_ERROR
--let $mts_spco_gd_trx_to_client_unblocking_workers = SET GLOBAL read_only = OFF
--source common/rpl/mta_rpco_generate_deadlock.inc

--echo
--echo #
--echo # TC3. `FLUSH TABLES WITH READ LOCK` issued by client and DDL replicated
--echo #      from source.
--echo #------------------------------------------------------------------------
--source include/rpl/connection_source.inc
INSERT INTO t1 VALUES(24, 24);

--let $mts_spco_gd_trx_blocking_worker_1 = ALTER TABLE t2 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=1
--let $mts_spco_gd_trx_assigned_worker_2 = INSERT INTO t1 VALUES(25, NULL)
--let $mts_spco_gd_trx_assigned_worker_3 = INSERT INTO t1 VALUES(26, NULL)
--let $mts_spco_gd_worker_3_only_runs_after_deadlock = 0
--let $mts_spco_gd_trx_blocking_client = FLUSH TABLES WITH READ LOCK
--let $mts_spco_gd_state_blocking_client = Waiting for commit lock
--let $mts_spco_gd_wait_for_coordinator_running_state = Replica has read all relay log; waiting for more updates
--let $mts_spco_gd_error_expected_replica = ER_LOCK_DEADLOCK
--let $mts_spco_gd_trx_to_client_unblocking_workers = UNLOCK TABLES
--source common/rpl/mta_rpco_generate_deadlock.inc

--source include/rpl/connection_source.inc
INSERT INTO t1 VALUES(27, 27);

--let $mts_spco_gd_trx_blocking_worker_1 = ALTER TABLE t2 ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=1
--let $mts_spco_gd_trx_assigned_worker_2 = INSERT INTO t1 VALUES(28, NULL)
--let $mts_spco_gd_trx_assigned_worker_3 = INSERT INTO t1 VALUES(29, NULL)
--let $mts_spco_gd_worker_3_only_runs_after_deadlock = 1
--let $mts_spco_gd_trx_blocking_client = FLUSH TABLES WITH READ LOCK
--let $mts_spco_gd_state_blocking_client = Waiting for commit lock
--let $mts_spco_gd_wait_for_coordinator_running_state = Replica has read all relay log; waiting for more updates
--let $mts_spco_gd_error_expected_replica = ER_REPLICA_WORKER_STOPPED_PREVIOUS_THD_ERROR
--let $mts_spco_gd_trx_to_client_unblocking_workers = UNLOCK TABLES
--source common/rpl/mta_rpco_generate_deadlock.inc

--echo
--echo #
--echo # TC4. `FLUSH TABLES WITH READ LOCK` issued by client and DML replicated
--echo #      from source.
--echo #------------------------------------------------------------------------
--source include/rpl/connection_source.inc
INSERT INTO t1 VALUES(30, 30);

--let $mts_spco_gd_trx_blocking_worker_1 = INSERT INTO t1 VALUES(31, NULL)
--let $mts_spco_gd_trx_assigned_worker_2 = INSERT INTO t1 VALUES(32, NULL)
--let $mts_spco_gd_trx_assigned_worker_3 = INSERT INTO t1 VALUES(33, NULL)
--let $mts_spco_gd_worker_3_only_runs_after_deadlock = 0
--let $mts_spco_gd_trx_blocking_client = FLUSH TABLES WITH READ LOCK
--let $mts_spco_gd_state_blocking_client = Waiting for commit lock
--let $mts_spco_gd_wait_for_coordinator_running_state = Replica has read all relay log; waiting for more updates
--let $mts_spco_gd_error_expected_replica = ER_LOCK_DEADLOCK
--let $mts_spco_gd_trx_to_client_unblocking_workers = UNLOCK TABLES
--source common/rpl/mta_rpco_generate_deadlock.inc

--source include/rpl/connection_source.inc
INSERT INTO t1 VALUES(34, 34);

--let $mts_spco_gd_trx_blocking_worker_1 = INSERT INTO t1 VALUES(35, NULL)
--let $mts_spco_gd_trx_assigned_worker_2 = INSERT INTO t1 VALUES(36, NULL)
--let $mts_spco_gd_trx_assigned_worker_3 = INSERT INTO t1 VALUES(37, NULL)
--let $mts_spco_gd_worker_3_only_runs_after_deadlock = 1
--let $mts_spco_gd_trx_blocking_client = FLUSH TABLES WITH READ LOCK
--let $mts_spco_gd_state_blocking_client = Waiting for commit lock
--let $mts_spco_gd_wait_for_coordinator_running_state = Replica has read all relay log; waiting for more updates
--let $mts_spco_gd_error_expected_replica = ER_REPLICA_WORKER_STOPPED_PREVIOUS_THD_ERROR
--let $mts_spco_gd_trx_to_client_unblocking_workers = UNLOCK TABLES
--source common/rpl/mta_rpco_generate_deadlock.inc

--echo
--echo #
--echo # TC5. DML issued by client and `XA PREPARE` replicated from source.
--echo #------------------------------------------------------------------------
--source include/rpl/connection_source.inc
INSERT INTO t1 VALUES(41, 41);

--let $mts_spco_gd_trx_blocking_worker_1 = XA START 'xa1'; INSERT INTO t1 VALUES(42, NULL); XA END 'xa1'; XA PREPARE 'xa1'
--let $mts_spco_gd_worker_3_only_runs_after_deadlock = 0
--let $mts_spco_gd_trx_blocking_client = BEGIN; INSERT INTO t1 VALUES(42, NULL)
--let $mts_spco_gd_state_blocking_client = None
--let $mts_spco_gd_error_expected_replica = ER_LOCK_WAIT_TIMEOUT
--let $mts_spco_gd_trx_to_client_unblocking_workers = ROLLBACK
--let $mts_spco_gd_trx_finishing_group = XA COMMIT 'xa1'
--source common/rpl/mta_rpco_generate_deadlock.inc

--source include/rpl/start_applier.inc
--source include/rpl/connection_source.inc
DROP TABLE t1;
DROP TABLE t2;
--source include/rpl/deinit.inc

--source common/rpl/mta_rpco_generate_deadlock_cleanup.inc
