DROP TABLE IF EXISTS t1;
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bit(1) DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bit(1) DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bit(1) DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bit(1) DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bit(1) NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bit(1) NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bit(1) NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bit(1) NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyint DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyint DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyint DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyint DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyint NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyint NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyint NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyint NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 SMALLINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` smallint DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 SMALLINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` smallint DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 SMALLINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` smallint DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 SMALLINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` smallint DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 SMALLINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` smallint NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 SMALLINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` smallint NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 SMALLINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` smallint NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 SMALLINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` smallint NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumint DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumint DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumint DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumint DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumint NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumint NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumint NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumint NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INTEGER  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INTEGER  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INTEGER  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INTEGER  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INTEGER  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INTEGER  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INTEGER  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 INTEGER  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIGINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bigint DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIGINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bigint DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIGINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bigint DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIGINT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bigint DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIGINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bigint NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIGINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bigint NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIGINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bigint NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BIGINT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` bigint NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 REAL  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 REAL  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 REAL  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 REAL  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 REAL  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 REAL  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 REAL  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 REAL  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DOUBLE  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DOUBLE  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DOUBLE  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DOUBLE  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DOUBLE  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DOUBLE  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DOUBLE  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DOUBLE  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` double NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 FLOAT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` float DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 FLOAT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` float DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 FLOAT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` float DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 FLOAT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` float DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 FLOAT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` float NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 FLOAT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` float NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 FLOAT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` float NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 FLOAT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` float NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DECIMAL  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DECIMAL  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DECIMAL  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DECIMAL  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DECIMAL  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DECIMAL  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DECIMAL  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DECIMAL  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 NUMERIC  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 NUMERIC  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 NUMERIC  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 NUMERIC  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 NUMERIC  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 NUMERIC  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 NUMERIC  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 NUMERIC  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` decimal(10,0) NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DATE  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` date DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DATE  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` date DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DATE  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` date DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DATE  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` date DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DATE  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` date NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DATE  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` date NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DATE  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` date NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 DATE  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` date NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIME  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` time DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIME  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` time DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIME  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` time DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIME  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` time DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIME  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` time NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIME  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` time NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIME  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` time NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIME  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` time NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIMESTAMP  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` timestamp NULL DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIMESTAMP  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` timestamp NULL DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIMESTAMP  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` timestamp NULL DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIMESTAMP  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` timestamp NULL DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIMESTAMP  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` timestamp NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIMESTAMP  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` timestamp NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIMESTAMP  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` timestamp NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TIMESTAMP  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` timestamp NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 YEAR  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` year DEFAULT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 YEAR  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` year DEFAULT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 YEAR  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` year DEFAULT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 YEAR  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` year DEFAULT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 YEAR  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` year NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 YEAR  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` year NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 YEAR  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` year NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 YEAR  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` year NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYBLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyblob,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYBLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyblob,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYBLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyblob,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYBLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyblob,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYBLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyblob NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYBLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyblob NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYBLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyblob NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYBLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinyblob NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` blob,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` blob,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` blob,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` blob,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` blob NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` blob NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` blob NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 BLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` blob NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMBLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumblob,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMBLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumblob,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMBLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumblob,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMBLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumblob,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMBLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumblob NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMBLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumblob NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMBLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumblob NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMBLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumblob NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGBLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longblob,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGBLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longblob,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGBLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longblob,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGBLOB  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longblob,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGBLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longblob NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGBLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longblob NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGBLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longblob NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGBLOB  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longblob NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYTEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinytext,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYTEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinytext,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYTEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinytext,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYTEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinytext,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYTEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinytext NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYTEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinytext NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYTEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinytext NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TINYTEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` tinytext NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` text,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` text,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` text,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` text,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` text NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` text NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` text NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 TEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` text NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMTEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumtext,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMTEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumtext,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMTEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumtext,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMTEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumtext,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMTEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumtext NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMTEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumtext NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMTEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumtext NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 MEDIUMTEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` mediumtext NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGTEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longtext,
  `c3` char(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGTEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longtext,
  `c3` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGTEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longtext,
  `c3` binary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGTEXT  NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longtext,
  `c3` varbinary(20) DEFAULT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 CHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 CHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGTEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` char(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longtext NOT NULL,
  `c3` char(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARCHAR(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARCHAR(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGTEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varchar(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longtext NOT NULL,
  `c3` varchar(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 BINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 BINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGTEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` binary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longtext NOT NULL,
  `c3` binary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
CREATE TABLE t1(c1 VARBINARY(15) NOT NULL PRIMARY KEY, c2 INT NOT NULL, c3 VARBINARY(20) NOT NULL);
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
ALTER TABLE t1 ADD c4 LONGTEXT  NOT NULL AFTER c2;
SHOW TABLES;
Tables_in_test
t1
SHOW CREATE TABLE t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `c1` varbinary(15) NOT NULL,
  `c2` int NOT NULL,
  `c4` longtext NOT NULL,
  `c3` varbinary(20) NOT NULL,
  PRIMARY KEY (`c1`)
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
DROP TABLE t1;
SHOW TABLES;
Tables_in_test
