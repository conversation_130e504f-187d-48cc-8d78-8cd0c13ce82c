set optimizer_switch='index_condition_pushdown=on';
set @save_storage_engine= @@default_storage_engine;
set default_storage_engine=InnoDB;
# Bug#36981 - "innodb crash when selecting for update"
#
CREATE TABLE t1 (
c1 CHAR(1),
c2 CHAR(10),
<PERSON>EY (c1)
);
INSERT INTO t1 VALUES ('3', null);
SELECT * FROM t1 WHERE c1='3' FOR UPDATE;
c1	c2
3	NULL
DROP TABLE t1;
CREATE TABLE t1 (a INT) charset latin1;
INSERT INTO t1 VALUES (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t2 (a INT) charset latin1;
INSERT INTO t2 SELECT A.a + 10*(B.a + 10*C.a) FROM t1 A, t1 B, t1 C;
CREATE TABLE t3 (
c1 CHAR(10) NOT NULL,
c2 CHAR(10) NOT NULL,
c3 CHAR(200) NOT NULL,
KEY (c1)
);
INSERT INTO t3 
SELECT CONCAT('c-',1000+t2.a,'=w'), CONCAT('c-',1000+ t2.a,'=w'), 'filler'
  FROM t2;
INSERT INTO t3 
SELECT CONCAT('c-',1000+t2.a,'=w'), CONCAT('c-',2000+t2.a,'=w'), 'filler-1'
  FROM t2;
INSERT INTO t3
SELECT CONCAT('c-',1000+t2.a,'=w'), CONCAT('c-',3000+t2.a,'=w'), 'filler-2'
  FROM t2;
SELECT c1,c3 FROM t3 WHERE c1 >= 'c-1994=w' and c1 != 'c-1996=w' FOR UPDATE;
c1	c3
c-1994=w	filler
c-1994=w	filler-1
c-1994=w	filler-2
c-1995=w	filler
c-1995=w	filler-1
c-1995=w	filler-2
c-1997=w	filler
c-1997=w	filler-1
c-1997=w	filler-2
c-1998=w	filler
c-1998=w	filler-1
c-1998=w	filler-2
c-1999=w	filler
c-1999=w	filler-1
c-1999=w	filler-2
DROP TABLE t1,t2,t3;
#
# Bug#43360 - Server crash with a simple multi-table update
#
CREATE TABLE t1 (
a CHAR(2) NOT NULL PRIMARY KEY, 
b VARCHAR(20) NOT NULL,
KEY (b)
);
CREATE TABLE t2 (
a CHAR(2) NOT NULL PRIMARY KEY,
b VARCHAR(30) NOT NULL,
KEY (b)
);
INSERT INTO t1 VALUES 
('AB','MySQL AB'),
('JA','Sun Microsystems'),
('MS','Microsoft'),
('IB','IBM- Inc.'),
('GO','Google Inc.');
INSERT INTO t2 VALUES
('AB','Sweden'),
('JA','USA'),
('MS','United States of America'),
('IB','North America'),
('GO','South America');
UPDATE t1,t2 SET t1.b=UPPER(t1.b) WHERE t1.b LIKE 'United%';
SELECT * FROM t1 ORDER BY a;
a	b
AB	MySQL AB
GO	Google Inc.
IB	IBM- Inc.
JA	Sun Microsystems
MS	Microsoft
SELECT * FROM t2 ORDER BY a;
a	b
AB	Sweden
GO	South America
IB	North America
JA	USA
MS	United States of America
DROP TABLE t1,t2;
#
# Bug#40992 - InnoDB: Crash when engine_condition_pushdown is on
#
CREATE TABLE t (
dummy INT PRIMARY KEY, 
a INT UNIQUE, 
b INT
);
INSERT INTO t VALUES (1,1,1),(3,3,3),(5,5,5);
SELECT * FROM t WHERE a > 2 FOR UPDATE;
dummy	a	b
3	3	3
5	5	5
DROP TABLE t;
#
# Bug#35080 - Innodb crash at mem_block_get_len line 72
#
CREATE TABLE t1 (
t1_autoinc INT(11) NOT NULL AUTO_INCREMENT,
uuid VARCHAR(36) DEFAULT NULL,
PRIMARY KEY (t1_autoinc),
KEY k (uuid)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE TABLE t2 (
t2_autoinc INT(11) NOT NULL AUTO_INCREMENT,
uuid VARCHAR(36) DEFAULT NULL,
date DATETIME DEFAULT NULL,
PRIMARY KEY (t2_autoinc),
KEY k (uuid)
);
Warnings:
Warning	1681	Integer display width is deprecated and will be removed in a future release.
CREATE VIEW v1 AS 
SELECT t1_autoinc, uuid
FROM t1
WHERE (ISNULL(uuid) OR (uuid like '%-%'));
CREATE VIEW v2 AS 
SELECT t2_autoinc, uuid, date 
FROM t2
WHERE (ISNULL(uuid) OR (LENGTH(uuid) = 36));
CREATE PROCEDURE delete_multi (IN uuid CHAR(36))
DELETE v1, v2 FROM v1 INNER JOIN v2
ON v1.uuid = v2.uuid
WHERE v1.uuid = @uuid;
SET @uuid = UUID();
INSERT INTO v1 (uuid) VALUES (@uuid);
INSERT INTO v2 (uuid, date) VALUES (@uuid, '2009-09-09');
CALL delete_multi(@uuid);
DROP procedure delete_multi;
DROP table t1,t2;
DROP view v1,v2;
#
# Bug#41996 - multi-table delete crashes server (InnoDB table)
#
CREATE TABLE t1 (
b BIGINT,
i INT, 
KEY (b)
);
INSERT INTO t1 VALUES (2, 2);
DELETE t1 FROM t1 a, t1 WHERE a.i=t1.b;
DROP TABLE t1;
#
# Bug#43448 - Server crashes on multi table delete with Innodb
#
CREATE TABLE t1 (
id1 INT NOT NULL AUTO_INCREMENT PRIMARY KEY, 
t CHAR(12)
);
CREATE TABLE t2 (
id2 INT NOT NULL, 
t CHAR(12)
);
CREATE TABLE t3(
id3 INT NOT NULL, 
t CHAR(12), 
INDEX(id3)
);
CREATE PROCEDURE insert_data ()
BEGIN
DECLARE i1 INT DEFAULT 20;
DECLARE i2 INT;
DECLARE i3 INT;
WHILE (i1 > 0) DO
INSERT INTO t1(t) VALUES (i1);
SET i2 = 2;
WHILE (i2 > 0) DO
INSERT INTO t2(id2, t) VALUES (i1, i2);
SET i3 = 2;
WHILE (i3 > 0) DO
INSERT INTO t3(id3, t) VALUES (i1, i2);
SET i3 = i3 -1;
END WHILE;
SET i2 = i2 -1;
END WHILE;
SET i1 = i1 - 1;
END WHILE;
END |
CALL insert_data();
SELECT COUNT(*) FROM t1 WHERE id1 > 10;
COUNT(*)
10
SELECT COUNT(*) FROM t2 WHERE id2 > 10;
COUNT(*)
20
SELECT COUNT(*) FROM t3 WHERE id3 > 10;
COUNT(*)
40
DELETE t1, t2, t3 
FROM t1, t2, t3 
WHERE t1.id1 = t2.id2 AND t2.id2 = t3.id3 AND t1.id1 > 3;
SELECT COUNT(*) FROM t1;
COUNT(*)
3
SELECT COUNT(*) FROM t2;
COUNT(*)
6
SELECT COUNT(*) FROM t3;
COUNT(*)
12
DROP PROCEDURE insert_data;
DROP TABLE t1, t2, t3;
#
# Bug#42580 - Innodb's ORDER BY ..LIMIT returns no rows for 
#             null-safe operator <=> NULL
#
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1(
c1 DATE NOT NULL, 
c2 DATE NULL, 
c3 DATETIME, 
c4 TIMESTAMP, 
PRIMARY KEY(c1), 
UNIQUE(c2)
);

INSERT INTO t1 VALUES('0000-00-00', '0000-00-00', '2008-01-04', '2008-01-05');
INSERT INTO t1 VALUES('2007-05-25', '2007-05-25', '2007-05-26', '2007-05-26');
INSERT INTO t1 VALUES('2008-01-01', NULL        , '2008-01-02', '2008-01-03');
INSERT INTO t1 VALUES('2008-01-17', NULL        , NULL        , '2009-01-29');
INSERT INTO t1 VALUES('2009-01-29', '2009-01-29', '2009-01-29', '2009-01-29');

SELECT * FROM t1 WHERE c2 <=> NULL ORDER BY c1,c2;
c1	c2	c3	c4
2008-01-01	NULL	2008-01-02 00:00:00	2008-01-03 00:00:00
2008-01-17	NULL	NULL	2009-01-29 00:00:00

SELECT * FROM t1 WHERE c2 <=> NULL ORDER BY c1,c2 LIMIT 2;
c1	c2	c3	c4
2008-01-01	NULL	2008-01-02 00:00:00	2008-01-03 00:00:00
2008-01-17	NULL	NULL	2009-01-29 00:00:00

DROP TABLE t1;
SET sql_mode = default;
#
# Bug#43617 - Innodb returns wrong results with timestamp's range value 
#             in IN clause
# (Note: Fixed by patch for BUG#42580)
#
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
CREATE TABLE t1(
c1 TIMESTAMP NOT NULL, 
c2 TIMESTAMP NULL, 
c3 DATE, 
c4 DATETIME, 
PRIMARY KEY(c1), 
UNIQUE INDEX(c2)
);
INSERT INTO t1 VALUES
('0000-00-00 00:00:00','0000-00-00 00:00:00','2008-01-04','2008-01-05 00:00:00'),
('1971-01-01 00:00:01','1980-01-01 00:00:01','2009-01-01','2009-01-02 00:00:00'),
('1999-01-01 00:00:00','1999-01-01 00:00:00', NULL,        NULL),
('2007-05-23 09:15:28','2007-05-23 09:15:28','2007-05-24','2007-05-24 09:15:28'),
('2007-05-27 00:00:00','2007-05-25 00:00:00','2007-05-26','2007-05-26 00:00:00'),
('2008-01-01 00:00:00', NULL,                '2008-01-02','2008-01-03 00:00:00'),
('2009-01-29 11:11:27','2009-01-29 11:11:27','2009-01-29','2009-01-29 11:11:27'),
('2038-01-09 03:14:07','2038-01-09 03:14:07','2009-01-05','2009-01-06 00:00:00');

SELECT * 
FROM t1 
WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') 
ORDER BY c2;
c1	c2	c3	c4
2038-01-09 03:14:07	2038-01-09 03:14:07	2009-01-05	2009-01-06 00:00:00

SELECT * 
FROM t1 
WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') 
ORDER BY c2 LIMIT 2;
c1	c2	c3	c4
2038-01-09 03:14:07	2038-01-09 03:14:07	2009-01-05	2009-01-06 00:00:00

SELECT * 
FROM t1 
WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') 
ORDER BY c2 DESC;
c1	c2	c3	c4
2038-01-09 03:14:07	2038-01-09 03:14:07	2009-01-05	2009-01-06 00:00:00

SELECT * 
FROM t1 
WHERE c2 IN ('1971-01-01 00:00:01','2038-01-09 03:14:07') 
ORDER BY c2 DESC LIMIT 2;
c1	c2	c3	c4
2038-01-09 03:14:07	2038-01-09 03:14:07	2009-01-05	2009-01-06 00:00:00

DROP TABLE t1;
SET sql_mode = default;
#
# Bug#43249 - Innodb returns zero time for the time column 
#             with <=> NULL order by limit 
# (Note: Fixed by patch for BUG#42580)
#
CREATE TABLE t1(
c1 TIME NOT NULL, 
c2 TIME NULL,
c3 DATE, 
PRIMARY KEY(c1), 
UNIQUE INDEX(c2)
);
INSERT INTO t1 VALUES('8:29:45',NULL,'2009-02-01');

SELECT * FROM t1 WHERE c2 <=> NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01

SELECT * FROM t1 WHERE c2 <=> NULL ORDER BY c2 LIMIT 2;
c1	c2	c3
08:29:45	NULL	2009-02-01
DROP TABLE t1;
#
# BUG#43618: MyISAM&Maria returns wrong results with 'between' 
#            on timestamp
#
CREATE TABLE t1(
ts TIMESTAMP NOT NULL, 
c char NULL,
PRIMARY KEY(ts)
);
INSERT INTO t1 VALUES
('1971-01-01','a'),
('2007-05-25','b'),
('2008-01-01','c'),
('2038-01-09','d');

# Execute select with invalid timestamp, desc ordering
SET sql_mode = '';
SELECT *
FROM t1 
WHERE ts BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' 
ORDER BY ts DESC
LIMIT 2;
ts	c
2008-01-01 00:00:00	c
2007-05-25 00:00:00	b
SET sql_mode = default;

# Should use index condition
EXPLAIN
SELECT *
FROM t1 
WHERE ts BETWEEN '0000-00-00' AND '2010-00-01 00:00:00' 
ORDER BY ts DESC
LIMIT 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	PRIMARY	PRIMARY	4	NULL	4	100.00	Using where; Backward index scan

DROP TABLE t1;
#
# BUG#49906: Assertion failed - Field_varstring::val_str in field.cc
#
CREATE TABLE t1 ( 
f1 VARCHAR(1024),
f2 VARCHAR(10),
INDEX test_idx USING BTREE (f2,f1(5))
);
INSERT INTO t1 VALUES  ('a','c'), ('b','d');
SELECT f1
FROM t1 
WHERE f2 LIKE 'd' 
ORDER BY f1;
f1
b
DROP TABLE t1;
#
# Bug#52660 - "Perf. regr. using ICP for MyISAM on range queries on 
#              an index containing TEXT"
#
CREATE TABLE t1 (a INT);
INSERT INTO t1 VALUES (0),(1),(2),(3),(4),(5),(6),(7),(8),(9);
CREATE TABLE t2 (a INT);
INSERT INTO t2 SELECT A.a + 10*(B.a) FROM t1 A, t1 B;
CREATE TABLE t3 (
c1 TINYTEXT NOT NULL,
i1 INT NOT NULL,
KEY (c1(6),i1)
) charset utf8mb4;
INSERT INTO t3 SELECT CONCAT('c-',1000+t2.a,'=w'), 1 FROM t2;
EXPLAIN SELECT c1 FROM t3 WHERE c1 >= 'c-1004=w' and c1 <= 'c-1006=w' and i1 > 2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t3	NULL	range	c1	c1	30	NULL	2	33.33	Using index condition; Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`c1` AS `c1` from `test`.`t3` where ((`test`.`t3`.`c1` >= 'c-1004=w') and (`test`.`t3`.`c1` <= 'c-1006=w') and (`test`.`t3`.`i1` > 2))
EXPLAIN FORMAT=tree SELECT c1 FROM t3 WHERE c1 >= 'c-1004=w' and c1 <= 'c-1006=w' and i1 > 2;
EXPLAIN
-> Filter: ((t3.c1 >= 'c-1004=w') and (t3.c1 <= 'c-1006=w'))  (cost=1.16 rows=0.667)
    -> Index range scan on t3 using c1 over (unprintable_blob_value <= c1 <= unprintable_blob_value AND 2 < i1), with index condition: (t3.i1 > 2)  (cost=1.16 rows=2)

SELECT c1 FROM t3 WHERE c1 >= 'c-1004=w' and c1 <= 'c-1006=w' and i1 > 2;
c1
DROP TABLE t1, t2, t3;
#
# Bug#57372 "Multi-table updates and deletes fail when running with ICP 
#            against InnoDB"
#
CREATE TABLE t1 (
a INT KEY, 
b INT
) ENGINE = INNODB;
CREATE TABLE t2 (
a INT KEY, 
b INT
) ENGINE = INNODB;
INSERT INTO t1 VALUES (1, 101), (2, 102), (3, 103), (4, 104), (5, 105);
INSERT INTO t2 VALUES (1, 1), (2, 2), (3, 3), (4, 4), (5, 5);
UPDATE t1, t2 
SET t1.a = t1.a + 100, t2.b = t1.a + 10 
WHERE t1.a BETWEEN 2 AND 4 AND t2.a = t1.b - 100;
SELECT * FROM t1;
a	b
1	101
102	102
103	103
104	104
5	105
SELECT * FROM t2;
a	b
1	1
2	12
3	13
4	14
5	5
DROP TABLE t1, t2;
#
# Bug#52605 - "Adding LIMIT 1 clause to query with complex range 
#              predicate causes wrong results"
#
CREATE TABLE t1 (
pk INT NOT NULL,
c1 INT,
PRIMARY KEY (pk),
KEY k1 (c1)
);
INSERT INTO t1 VALUES (1,NULL);
INSERT INTO t1 VALUES (2,6);
INSERT INTO t1 VALUES (3,NULL);
INSERT INTO t1 VALUES (4,6);
INSERT INTO t1 VALUES (5,NULL);
INSERT INTO t1 VALUES (6,NULL);
INSERT INTO t1 VALUES (7,9);
INSERT INTO t1 VALUES (8,0);
SELECT pk, c1
FROM t1  
WHERE (pk BETWEEN 4 AND 5 OR pk < 2) AND c1 < 240
ORDER BY c1
LIMIT 1;
pk	c1
4	6
EXPLAIN SELECT pk, c1
FROM t1  
WHERE (pk BETWEEN 4 AND 5 OR pk < 2) AND c1 < 240
ORDER BY c1
LIMIT 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	PRIMARY,k1	k1	5	NULL	4	37.50	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c1` AS `c1` from `test`.`t1` where (((`test`.`t1`.`pk` between 4 and 5) or (`test`.`t1`.`pk` < 2)) and (`test`.`t1`.`c1` < 240)) order by `test`.`t1`.`c1` limit 1
DROP TABLE t1;
#
# Bug#42991 "invalid memory access and/or crash when using
#            index condition pushdown + InnoDB"
#
CREATE TABLE t1 (
c1 TINYTEXT NOT NULL,
c2 INT NOT NULL,
PRIMARY KEY (c2),
KEY id1 (c1(4))
) charset utf8mb4;
INSERT INTO t1 VALUES ('Anastasia', 5);
INSERT INTO t1 VALUES ('Karianne', 4);
SELECT * FROM t1 WHERE (c1 <= '6566-06-15' AND c2 <> 3);
c1	c2
EXPLAIN SELECT * FROM t1 WHERE (c1 <= '6566-06-15' AND c2 <> 3);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	PRIMARY,id1	id1	18	NULL	1	100.00	Using index condition; Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1`,`test`.`t1`.`c2` AS `c2` from `test`.`t1` where ((`test`.`t1`.`c1` <= '6566-06-15') and (`test`.`t1`.`c2` <> 3))
DROP TABLE t1;
#
# Bug#56529 - "Crash due to long semaphore wait in InnoDB 
#              with ICP and subqueries"
#
CREATE TABLE t1 (
col_int_nokey INTEGER,
col_int_key INTEGER,
col_varchar_key VARCHAR(1),
KEY (col_int_key),
KEY (col_varchar_key, col_int_key)
) charset utf8mb4 stats_persistent=0;
INSERT INTO t1 VALUES (NULL,2,'w');
INSERT INTO t1 VALUES (7,9,'m');
INSERT INTO t1 VALUES (9,3,'m');
INSERT INTO t1 VALUES (7,9,'k');
INSERT INTO t1 VALUES (4,NULL,'r');
INSERT INTO t1 VALUES (2,9,'t');
INSERT INTO t1 VALUES (6,3,'j');
INSERT INTO t1 VALUES (8,8,'u');
INSERT INTO t1 VALUES (NULL,8,'h');
INSERT INTO t1 VALUES (5,53,'o');
INSERT INTO t1 VALUES (NULL,0,NULL);
INSERT INTO t1 VALUES (6,5,'k');
INSERT INTO t1 VALUES (188,166,'e');
INSERT INTO t1 VALUES (2,3,'n');
INSERT INTO t1 VALUES (1,0,'t');
INSERT INTO t1 VALUES (1,1,'c');
INSERT INTO t1 VALUES (0,9,'m');
INSERT INTO t1 VALUES (9,5,'y');
INSERT INTO t1 VALUES (NULL,6,'f');
CREATE TABLE t2 (
c1 INTEGER NOT NULL
) stats_persistent=0;
EXPLAIN SELECT table1.col_int_nokey
FROM t1 AS table1 STRAIGHT_JOIN (
t1 AS table2 INNER JOIN t1 AS table3
ON table3.col_varchar_key = table2.col_varchar_key)
ON table3.col_int_nokey = table1.col_int_key
WHERE table3.col_int_key != SOME ( SELECT c1 FROM t2 );
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	table1	NULL	ALL	col_int_key	NULL	NULL	NULL	19	100.00	NULL
1	PRIMARY	table3	NULL	ALL	col_varchar_key	NULL	NULL	NULL	19	10.00	Using where; Using join buffer (hash join)
1	PRIMARY	table2	NULL	ref	col_varchar_key	col_varchar_key	7	test.table3.col_varchar_key	1	100.00	Using index
2	DEPENDENT SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`table1`.`col_int_nokey` AS `col_int_nokey` from `test`.`t1` `table1` join `test`.`t1` `table2` join `test`.`t1` `table3` where ((`test`.`table2`.`col_varchar_key` = `test`.`table3`.`col_varchar_key`) and (`test`.`table3`.`col_int_nokey` = `test`.`table1`.`col_int_key`) and <nop>(<in_optimizer>(`test`.`table3`.`col_int_key`,<exists>(/* select#2 */ select `test`.`t2`.`c1` from `test`.`t2` where (<cache>(`test`.`table3`.`col_int_key`) <> `test`.`t2`.`c1`)))))
SELECT table1.col_int_nokey
FROM t1 AS table1 STRAIGHT_JOIN (
t1 AS table2 INNER JOIN t1 AS table3
ON table3.col_varchar_key = table2.col_varchar_key)
ON table3.col_int_nokey = table1.col_int_key
WHERE table3.col_int_key != SOME ( SELECT c1 FROM t2 );
col_int_nokey
DROP TABLE t1, t2;
#
# Bug#58243 "RQG test optimizer_subquery causes server crash
#            when running with ICP"
#
CREATE TABLE t1 (
pk INTEGER NOT NULL,
c1 INTEGER NOT NULL,
c2 INTEGER NOT NULL,
PRIMARY KEY (pk)
);
INSERT INTO t1 VALUES (1,6,7);
CREATE TABLE t2 (
c1 INTEGER NOT NULL
);
EXPLAIN SELECT t1.c1
FROM t1
WHERE t1.pk < 317 AND 2 IN (SELECT COUNT(t2.c1)
FROM t2)
ORDER BY t1.c2;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
2	SUBQUERY	t2	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`c1` AS `c1` from `test`.`t1` where false order by `test`.`t1`.`c2`
SELECT t1.c1
FROM t1
WHERE t1.pk < 317 AND 2 IN (SELECT COUNT(t2.c1)
FROM t2)
ORDER BY t1.c2;
c1
DROP TABLE t1, t2;
CREATE TABLE t1 (
i1 INTEGER NOT NULL,
c1 VARCHAR(1) NOT NULL
) charset latin1;
INSERT INTO t1 VALUES (2,'w');
CREATE TABLE t2 (
i1 INTEGER NOT NULL,
c1 VARCHAR(1) NOT NULL,
c2 VARCHAR(1) NOT NULL,
KEY (c1, i1)
) charset latin1;
INSERT INTO t2 VALUES (8,'d','d');
INSERT INTO t2 VALUES (4,'v','v');
CREATE TABLE t3 (
c1 VARCHAR(1) NOT NULL
) charset latin1;
INSERT INTO t3 VALUES ('v');
EXPLAIN SELECT i1
FROM t1
WHERE EXISTS (SELECT t2.c1
FROM (t2 INNER JOIN t3 ON (t3.c1 = t2.c1)) 
WHERE t2.c2 != t1.c1 AND t2.c2 = (SELECT MIN(t3.c1)
FROM t3));
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
2	DEPENDENT SUBQUERY	t3	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ref	c1	c1	3	test.t3.c1	1	50.00	Using where
3	SUBQUERY	t3	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1276	Field or reference 'test.t1.c1' of SELECT #2 was resolved in SELECT #1
Note	1003	/* select#1 */ select `test`.`t1`.`i1` AS `i1` from `test`.`t1` where exists(/* select#2 */ select `test`.`t2`.`c1` from `test`.`t2` join `test`.`t3` where ((`test`.`t2`.`c1` = `test`.`t3`.`c1`) and (`test`.`t2`.`c2` = (/* select#3 */ select min(`test`.`t3`.`c1`) from `test`.`t3`)) and ((/* select#3 */ select min(`test`.`t3`.`c1`) from `test`.`t3`) <> `test`.`t1`.`c1`)))
SELECT i1
FROM t1
WHERE EXISTS (SELECT t2.c1
FROM (t2 INNER JOIN t3 ON (t3.c1 = t2.c1)) 
WHERE t2.c2 != t1.c1 AND t2.c2 = (SELECT MIN(t3.c1)
FROM t3));
i1
2
DROP TABLE t1,t2,t3;
#
# Bug#58015 "Assert in row_sel_field_store_in_mysql_format
#            when running innodb_mrr_icp test"
#
create table t1 (a char(2) charset utf8mb3,b double, primary key (a(1)),key (b));
Warnings:
Warning	1287	'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
insert into t1 values ('',1);
select 1 from t1 where b <= 1 and a <> '';
1
drop table t1;
#
# Bug#59259 "Incorrect rows returned for a correlated subquery
#            when ICP is on"
#
CREATE TABLE t1 (pk INTEGER PRIMARY KEY, i INTEGER NOT NULL) ENGINE=InnoDB;
INSERT INTO t1 VALUES (11,0);
INSERT INTO t1 VALUES (12,5);
INSERT INTO t1 VALUES (15,0);
CREATE TABLE t2 (pk INTEGER PRIMARY KEY, i INTEGER NOT NULL) ENGINE=InnoDB;
INSERT INTO t2 VALUES (11,1);
INSERT INTO t2 VALUES (12,2);
INSERT INTO t2 VALUES (15,4);
SELECT * FROM t1
WHERE pk IN (SELECT it.pk FROM t2 JOIN t2 AS it ON it.i=it.i WHERE t1.i);
pk	i
12	5
DROP TABLE t1, t2;
#
# Bug #58816 "Extra temporary duplicate rows in result set when 
#             switching ICP off"
#
set @save_optimizer_switch_bug58816= @@optimizer_switch;
CREATE TABLE t1 (
pk INT NOT NULL,
c1 INT NOT NULL,
PRIMARY KEY (pk)
);
INSERT INTO t1 VALUES (1,9),(2,7),(3,6),(4,3),(5,1);
EXPLAIN SELECT pk, c1 FROM t1 WHERE pk <> 3;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	PRIMARY	PRIMARY	4	NULL	4	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk`,`test`.`t1`.`c1` AS `c1` from `test`.`t1` where (`test`.`t1`.`pk` <> 3)
SET SESSION optimizer_switch='index_condition_pushdown=off';
SELECT pk, c1 FROM t1 WHERE pk <> 3;
pk	c1
1	9
2	7
4	3
5	1
DROP TABLE t1;
set optimizer_switch= @save_optimizer_switch_bug58816;
#
# Bug#58837: ICP crash or valgrind error due to uninitialized 
#            value in innobase_index_cond
#
CREATE TABLE t1 (
t1_int INT,
t1_time TIME
);
CREATE TABLE t2 ( 
t2_int int PRIMARY KEY,
t2_int2 INT
);
INSERT IGNORE INTO t2 VALUES ();
INSERT INTO t1 VALUES ();

SELECT *
FROM t1 AS t1a 
WHERE NOT EXISTS
(SELECT *
FROM t1 AS t1b
WHERE t1b.t1_int NOT IN
(SELECT t2.t2_int 
FROM t2
WHERE t1b.t1_time LIKE t1b.t1_int
OR t1b.t1_time <> t2.t2_int2
AND 6=7 
)
)
;;
t1_int	t1_time

EXPLAIN SELECT *
FROM t1 AS t1a 
WHERE NOT EXISTS
(SELECT *
FROM t1 AS t1b
WHERE t1b.t1_int NOT IN
(SELECT t2.t2_int 
FROM t2
WHERE t1b.t1_time LIKE t1b.t1_int
OR t1b.t1_time <> t2.t2_int2
AND 6=7 
)
)
;;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	NULL	Impossible WHERE
2	SUBQUERY	t1b	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	Using where
3	DEPENDENT SUBQUERY	t2	NULL	unique_subquery	PRIMARY	PRIMARY	4	func	1	100.00	Using where; Full scan on NULL key
Warnings:
Note	1276	Field or reference 'test.t1b.t1_time' of SELECT #3 was resolved in SELECT #2
Note	1276	Field or reference 'test.t1b.t1_int' of SELECT #3 was resolved in SELECT #2
Note	1276	Field or reference 'test.t1b.t1_time' of SELECT #3 was resolved in SELECT #2
Note	1003	/* select#1 */ select `test`.`t1a`.`t1_int` AS `t1_int`,`test`.`t1a`.`t1_time` AS `t1_time` from `test`.`t1` `t1a` where false

DROP TABLE t1,t2;
#
# Bug#59186 Wrong results of join when ICP is enabled
#
CREATE TABLE t1 (
pk INTEGER NOT NULL,
c1 VARCHAR(3) NOT NULL,
PRIMARY KEY (pk)
);
INSERT INTO t1 VALUES (1,'y'),(0,'or');
CREATE TABLE t2 (
pk INTEGER NOT NULL,
c1 VARCHAR(3) NOT NULL,
c2 VARCHAR(6) NOT NULL,
PRIMARY KEY (pk)
);
INSERT INTO t2 VALUES (6,'y','RPOYT'),(10,'m','JINQE');
EXPLAIN SELECT c2 FROM t1 JOIN t2 ON t1.c1 = t2.c1
WHERE (t2.pk <= 4 AND t1.pk IN (2,1)) OR
(t1.pk > 1 AND t2.pk BETWEEN 6 AND 6);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	range	PRIMARY	PRIMARY	4	NULL	1	100.00	Using where
1	SIMPLE	t2	NULL	range	PRIMARY	PRIMARY	4	NULL	2	50.00	Using where; Using join buffer (hash join)
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`c2` AS `c2` from `test`.`t1` join `test`.`t2` where ((`test`.`t2`.`c1` = `test`.`t1`.`c1`) and (((`test`.`t2`.`pk` <= 4) and (`test`.`t1`.`pk` in (2,1))) or ((`test`.`t2`.`pk` = 6) and (`test`.`t1`.`pk` > 1))))
SELECT c2 FROM t1 JOIN t2 ON t1.c1 = t2.c1
WHERE (t2.pk <= 4 AND t1.pk IN (2,1)) OR
(t1.pk > 1 AND t2.pk BETWEEN 6 AND 6);
c2
DROP TABLE t1, t2;
#
# Bug#58838 "Wrong results with HAVING + LIMIT without GROUP BY when 
#            ICP is enabled"
# 
CREATE TABLE t1 (
pk INT NOT NULL,
c1 INT,
PRIMARY KEY (pk),
KEY col_int_key (c1)
);
INSERT INTO t1 VALUES (1,37),(2,8),(3,-25),(4,NULL),(5,55);
SELECT pk FROM t1 WHERE c1 <> 1 HAVING pk = 3 ORDER BY pk LIMIT 0;
pk
SELECT pk FROM t1 WHERE c1 <> 1 HAVING pk = 3 ORDER BY pk LIMIT 1;
pk
3
SELECT pk FROM t1 WHERE c1 <> 1 HAVING pk = 3 ORDER BY pk LIMIT 2;
pk
3
SELECT pk FROM t1 WHERE c1 <> 1 HAVING pk = 3 ORDER BY pk LIMIT 5;
pk
3
DROP TABLE t1;
#
# Bug#59483 "Crash on INSERT/REPLACE in
#            rec_convert_dtuple_to_rec_comp with ICP on"
#
CREATE TABLE t1 (
pk INTEGER AUTO_INCREMENT PRIMARY KEY,
i1 INTEGER,
c1 CHAR(6),
i2 INTEGER NOT NULL,
KEY (i2)
);
INSERT INTO t1 VALUES
(NULL, 4, 'that', 8),
(NULL, 1, 'she', 6),
(NULL, 6, 'tell', 2);
SELECT * FROM t1 WHERE i2 IN (3, 6) LIMIT 2 FOR UPDATE;
pk	i1	c1	i2
2	1	she	6
INSERT INTO t1 (i2) VALUES (1);
DROP TABLE t1;
#
# Bug #11766678 - 59843:
# USING UNINITIALISED VALUE IN USES_INDEX_FIELDS_ONLY
#
CREATE TABLE t1 (
col999 FLOAT NOT NULL,
COL1000 VARBINARY(179) NOT NULL,
col1003 DATE DEFAULT NULL,
KEY idx4267 (col1000, col1003)
);
INSERT IGNORE INTO t1 VALUES (),();
Warnings:
Warning	1364	Field 'col999' doesn't have a default value
Warning	1364	Field 'COL1000' doesn't have a default value
SELECT col999 FROM t1 WHERE col1000 = "3" AND col1003 <=> sysdate();
col999
DROP TABLE t1;
#
# Bug#11873324 "WRONG RESULT WITH ICP AND STRAIGHT_JOIN"
#
CREATE TABLE t1 (  
pk INTEGER NOT NULL,
i1 INTEGER,
PRIMARY KEY (pk),
KEY col_int_key (i1)
);
INSERT INTO t1 VALUES (14,NULL), (18,133);
CREATE TABLE t2 (  
pk INTEGER NOT NULL,
i1 INTEGER,
c1 VARCHAR(1),
PRIMARY KEY (pk),
KEY col_int_key (i1)
);
INSERT INTO t2 VALUES (1,7,'f');
set @old_opt_switch=@@optimizer_switch;
EXPLAIN SELECT t1.i1
FROM t1
WHERE t1.i1 NOT IN
( SELECT STRAIGHT_JOIN subquery_t1.pk
FROM t1 AS subquery_t1
JOIN t2 AS subquery_t2
ON subquery_t2.i1 = subquery_t1.pk
WHERE subquery_t1.i1 > 0
OR subquery_t2.c1 = 'a'
);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t1	NULL	index	NULL	col_int_key	5	NULL	2	100.00	Using where; Using index
2	DEPENDENT SUBQUERY	subquery_t1	NULL	eq_ref	PRIMARY,col_int_key	PRIMARY	4	func	1	100.00	Using where; Full scan on NULL key
2	DEPENDENT SUBQUERY	subquery_t2	NULL	ref	col_int_key	col_int_key	5	func	1	100.00	Using where; Full scan on NULL key
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i1` AS `i1` from `test`.`t1` where <in_optimizer>(`test`.`t1`.`i1`,<exists>(/* select#2 */ select straight_join `test`.`subquery_t1`.`pk` from `test`.`t1` `subquery_t1` join `test`.`t2` `subquery_t2` where ((`test`.`subquery_t2`.`i1` = `test`.`subquery_t1`.`pk`) and ((`test`.`subquery_t1`.`i1` > 0) or (`test`.`subquery_t2`.`c1` = 'a')) and <if>(outer_field_is_not_null, (<cache>(`test`.`t1`.`i1`) = `test`.`subquery_t1`.`pk`), true))) is false)
SELECT t1.i1
FROM t1
WHERE t1.i1 NOT IN
( SELECT STRAIGHT_JOIN subquery_t1.pk
FROM t1 AS subquery_t1
JOIN t2 AS subquery_t2
ON subquery_t2.i1 = subquery_t1.pk
WHERE subquery_t1.i1 > 0
OR subquery_t2.c1 = 'a'
);
i1
NULL
133
set @@optimizer_switch=@old_opt_switch;
DROP TABLE t1,t2;
#
# Bug#11876420 "MISSING ROW IN RESULT WITH SUBQUERY + IN + XOR + 
#               NULL VALUES AND ICP ENABLED"
# 
CREATE TABLE t1 (  
i1 INTEGER,
c1 VARCHAR(1),
KEY col_varchar_key (c1)
) charset utf8mb4;
INSERT INTO t1 VALUES (1,'j'), (0,'e'), (210,'f'), (8,'v'), (7,'x'),
(5,'m'), (NULL,'c');
CREATE TABLE t2 (  
i1 INTEGER,
c1 VARCHAR(1),
KEY col_varchar_key (c1)
) charset utf8mb4;
INSERT INTO t2 VALUES (8,NULL);
CREATE TABLE t3 (  
i1 INTEGER,
c1 VARCHAR(1),
KEY col_varchar_key (c1)
) charset utf8mb4 ENGINE=InnoDB;
INSERT INTO t3 VALUES (NULL,'w'), (1,NULL), (2,'d');
set @old_opt_switch=@@optimizer_switch;
EXPLAIN SELECT i1
FROM t3
WHERE c1 IN
( SELECT t1.c1
FROM t2 JOIN t1
ON t2.i1 >= t1.i1
WHERE t1.c1 > t2.c1
)
XOR i1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	t3	NULL	ALL	NULL	NULL	NULL	NULL	3	100.00	Using where
2	DEPENDENT SUBQUERY	t2	NULL	ALL	col_varchar_key	NULL	NULL	NULL	1	100.00	NULL
2	DEPENDENT SUBQUERY	t1	NULL	ref_or_null	col_varchar_key	col_varchar_key	7	func	2	33.33	Using where; Full scan on NULL key
Warnings:
Note	1003	/* select#1 */ select `test`.`t3`.`i1` AS `i1` from `test`.`t3` where (<in_optimizer>(`test`.`t3`.`c1`,<exists>(/* select#2 */ select `test`.`t1`.`c1` from `test`.`t2` join `test`.`t1` where ((`test`.`t1`.`c1` > `test`.`t2`.`c1`) and <if>(outer_field_is_not_null, ((<cache>(`test`.`t3`.`c1`) = `test`.`t1`.`c1`) or (`test`.`t1`.`c1` is null)), true) and (`test`.`t2`.`i1` >= `test`.`t1`.`i1`)) having <if>(outer_field_is_not_null, <is_not_null_test>(`test`.`t1`.`c1`), true))) xor (0 <> `test`.`t3`.`i1`))
SELECT i1
FROM t3
WHERE c1 IN
( SELECT t1.c1
FROM t2 JOIN t1
ON t2.i1 >= t1.i1
WHERE t1.c1 > t2.c1
)
XOR i1;
i1
1
2
set @@optimizer_switch=@old_opt_switch;
DROP TABLE t1, t2, t3;
#
# Bug#12355958 "FAILING ASSERTION: TRX->LOCK.N_ACTIVE_THRS == 1"
#
CREATE TABLE t1 (
pk INTEGER PRIMARY KEY, 
a INTEGER NOT NULL, 
b CHAR(1), 
KEY(b)
) charset utf8mb4;
INSERT INTO t1 VALUES (23,5,'d');
EXPLAIN SELECT a1.pk 
FROM t1 AS a1 JOIN (SELECT * FROM t1 LIMIT 1) AS a2 ON a2.b = a1.b
WHERE a1.a = (SELECT pk FROM t1 LIMIT 1) 
AND (a1.a != a2.a OR a1.b IS NULL);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	<derived2>	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
1	PRIMARY	a1	NULL	ref	b	b	5	const	1	100.00	Using where
3	SUBQUERY	t1	NULL	index	NULL	b	5	NULL	1	100.00	Using index
2	DERIVED	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`a1`.`pk` AS `pk` from `test`.`t1` `a1` where ((`test`.`a1`.`b` = 'd') and (`test`.`a1`.`a` = (/* select#3 */ select `test`.`t1`.`pk` from `test`.`t1` limit 1)) and (((/* select#3 */ select `test`.`t1`.`pk` from `test`.`t1` limit 1) <> '5') or (`test`.`a1`.`b` is null)))
SELECT a1.pk 
FROM t1 AS a1 JOIN (SELECT * FROM t1 LIMIT 1) AS a2 ON a2.b = a1.b
WHERE a1.a = (SELECT pk FROM t1 LIMIT 1) 
AND (a1.a != a2.a OR a1.b IS NULL);
pk
CREATE VIEW v1 AS SELECT * FROM t1;
EXPLAIN SELECT a1.pk 
FROM v1 AS a1 JOIN (SELECT * FROM v1 LIMIT 1) AS a2 ON a2.b = a1.b
WHERE a1.a = (SELECT pk FROM v1 LIMIT 1) 
AND (a1.a != a2.a OR a1.b IS NULL);
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	PRIMARY	<derived2>	NULL	system	NULL	NULL	NULL	NULL	1	100.00	NULL
1	PRIMARY	t1	NULL	ref	b	b	5	const	1	100.00	Using where
3	SUBQUERY	t1	NULL	index	NULL	b	5	NULL	1	100.00	Using index
2	DERIVED	t1	NULL	ALL	NULL	NULL	NULL	NULL	1	100.00	NULL
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`pk` AS `pk` from `test`.`t1` where ((`test`.`t1`.`b` = 'd') and (`test`.`t1`.`a` = (/* select#3 */ select `test`.`t1`.`pk` from `test`.`t1` limit 1)) and ((`test`.`t1`.`a` <> '5') or (`test`.`t1`.`b` is null)))
SELECT a1.pk 
FROM v1 AS a1 JOIN (SELECT * FROM v1 LIMIT 1) AS a2 ON a2.b = a1.b
WHERE a1.a = (SELECT pk FROM v1 LIMIT 1) 
AND (a1.a != a2.a OR a1.b IS NULL);
pk
DROP VIEW v1;
DROP TABLE t1;
#
# BUG#12601961 "SEGFAULT IN HANDLER::COMPARE_KEY2"
# BUG#12724899 "SELECT STRAIGHT_JOIN QUERY GIVES 2 DATES VERSUS
#               2 WARNINGS WITH ICP ON"
#
CREATE TABLE t1 (
pk INTEGER NOT NULL, 
i1 INTEGER NOT NULL,
c1 VARCHAR(1) NOT NULL,
PRIMARY KEY (pk)
);
INSERT INTO t1 VALUES (1,3,'j'), (20,8,'e');
EXPLAIN SELECT alias2.i1
FROM t1 AS alias1 STRAIGHT_JOIN t1 AS alias2
ON alias2.pk AND alias2.pk <= alias1.c1
WHERE alias2.pk = 1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	alias1	NULL	ALL	NULL	NULL	NULL	NULL	2	100.00	NULL
1	SIMPLE	alias2	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`alias2`.`i1` AS `i1` from `test`.`t1` `alias1` straight_join `test`.`t1` `alias2` where ((`test`.`alias2`.`pk` = 1) and (cast(`test`.`alias2`.`pk` as double) <= cast(`test`.`alias1`.`c1` as double)))
SELECT alias2.i1
FROM t1 AS alias1 STRAIGHT_JOIN t1 AS alias2
ON alias2.pk AND alias2.pk <= alias1.c1
WHERE alias2.pk = 1;
i1
Warning	1292	Truncated incorrect DOUBLE value: 'e'
Warning	1292	Truncated incorrect DOUBLE value: 'j'
Warnings:
DROP TABLE t1;
#
# BUG#12822678 - 2 MORE ROWS WHEN ICP=ON W/ STRAIGHT_JOIN
#
CREATE TABLE t1 (
i1 INTEGER NOT NULL,
d1 DOUBLE,
KEY k1 (d1)
);
INSERT INTO t1 VALUES (10,1), (17,NULL), (22,NULL);
CREATE TABLE t2 (
pk INTEGER NOT NULL,
i1 INTEGER NOT NULL,
PRIMARY KEY (pk)
);
INSERT INTO t2 VALUES (4,1);
EXPLAIN SELECT t1.d1, t2.pk, t2.i1
FROM t1 STRAIGHT_JOIN t2 ON t2.i1
WHERE t2.pk <> t1.d1 AND t2.pk = 4;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	k1	9	NULL	3	100.00	Using index
1	SIMPLE	t2	NULL	const	PRIMARY	PRIMARY	4	const	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`d1` AS `d1`,`test`.`t2`.`pk` AS `pk`,`test`.`t2`.`i1` AS `i1` from `test`.`t1` straight_join `test`.`t2` where ((`test`.`t2`.`pk` = 4) and (`test`.`t2`.`pk` <> `test`.`t1`.`d1`) and (0 <> `test`.`t2`.`i1`))
SELECT t1.d1, t2.pk, t2.i1
FROM t1 STRAIGHT_JOIN t2 ON t2.i1
WHERE t2.pk <> t1.d1 AND t2.pk = 4;
d1	pk	i1
1	4	1
DROP TABLE t1, t2;
#
# BUG#12838420 "DUPLICATE VALUES FOR GROUP-BY COLUMN WHEN JOIN
# BUFFERING IS OFF"
#
CREATE TABLE t1 (
col_int_key INT,
pk INT,
PRIMARY KEY (pk),
KEY (col_int_key)
);
INSERT INTO t1 VALUES (2,3),(3,2),(3,5),(4,6);
CREATE TABLE t2 (
col_int_key INT,
pk INT,
PRIMARY KEY (pk),
KEY (col_int_key)
);
INSERT INTO t2 VALUES (0,9),(3,10),(4,6),(6,1),(100,3),(200,5);
set @old_opt_switch=@@optimizer_switch;
SET optimizer_switch="block_nested_loop=off";
EXPLAIN SELECT t2.col_int_key AS field1
FROM t2 USE INDEX(col_int_key) STRAIGHT_JOIN t1 ON t2.col_int_key
WHERE t2.pk < 7 AND t2.col_int_key <> 7
GROUP BY field1;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t2	NULL	index	col_int_key	col_int_key	5	NULL	6	33.33	Using where; Using index
1	SIMPLE	t1	NULL	index	NULL	col_int_key	5	NULL	4	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select `test`.`t2`.`col_int_key` AS `field1` from `test`.`t2` USE INDEX (`col_int_key`) straight_join `test`.`t1` where ((`test`.`t2`.`pk` < 7) and (`test`.`t2`.`col_int_key` <> 7) and (0 <> `test`.`t2`.`col_int_key`)) group by `field1`
SELECT t2.col_int_key AS field1
FROM t2 USE INDEX(col_int_key) STRAIGHT_JOIN t1 ON t2.col_int_key
WHERE t2.pk < 7 AND t2.col_int_key <> 7
GROUP BY field1;
field1
100
200
4
6
SET @@optimizer_switch=@old_opt_switch;
DROP TABLE t1,t2;
#
# Bug#12976163 "CRASH IN INDEX CONDITION PUSHDOWN CODE AGAINST
#               A MYISAM TABLE"
#
CREATE TABLE t1 (
i1 INTEGER NOT NULL,
i2 INTEGER NOT NULL
);
INSERT INTO t1 VALUES (14,1), (15,2), (16,3);
CREATE TABLE t2 (
i1 INTEGER NOT NULL,
i2 INTEGER NOT NULL,
c1 TINYTEXT
);
INSERT INTO t2
SELECT i1, 10 * i2, "MySQL" FROM t1;
CREATE PROCEDURE proc1(id INTEGER)
BEGIN
SELECT i2
FROM (
(SELECT i1, i2, NULL AS a1 FROM t1)
UNION
(SELECT i1, i2, c1 AS a1 FROM t2)
) u1
WHERE i1 = id;
END$$
CALL proc1(15);
i2
2
20
DROP PROCEDURE proc1;
DROP TABLE t1, t2;
#
# Bug#13655397 "CRASH IN SYNC_THREAD_LEVELS_NONEMPTY_TRX"
#
CREATE TABLE t1 (
i1 INTEGER NOT NULL,
i2 INTEGER NOT NULL,
KEY (i1)
);
INSERT INTO t1 VALUES (4,4), (5,5);
CREATE TABLE t2 (
pk INTEGER NOT NULL,
PRIMARY KEY (pk)
);
INSERT INTO t2 VALUES (1);
CREATE FUNCTION f1() RETURNS INTEGER
RETURN (SELECT MOD(COUNT(DISTINCT pk), 10) FROM t2);
EXPLAIN SELECT i1, i2 FROM t1 WHERE f1() = 1 AND i1 = 5;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	i1	i1	4	const	1	100.00	Using where
Warnings:
Note	1003	/* select#1 */ select `test`.`t1`.`i1` AS `i1`,`test`.`t1`.`i2` AS `i2` from `test`.`t1` where ((`test`.`t1`.`i1` = 5) and (`f1`() = 1))
SELECT i1, i2 FROM t1 WHERE f1() = 1 AND i1 = 5;
i1	i2
5	5
DROP FUNCTION f1;
DROP TABLE t1, t2;
set default_storage_engine= @save_storage_engine;
set optimizer_switch=default;
