DROP TABLE IF EXISTS t1;
select 1, ST_Intersects(ST_GeomFromText('POLYGON((0 0,20 0,20 20,0 20,0 0))'), ST_GeomFromText('POLYGON((10 10,30 10,30 30,10 30,10 10))'));
1	ST_Intersects(ST_GeomFromText('POLYGON((0 0,20 0,20 20,0 20,0 0))'), ST_GeomFromText('POLYGON((10 10,30 10,30 30,10 30,10 10))'))
1	1
select 0, ST_Intersects(ST_GeomFromText('POLYGON((0 0,20 10,10 30, 0 0))'), ST_GeomFromText('POLYGON((10 40, 40 50, 20 70, 10 40))'));
0	ST_Intersects(ST_GeomFromText('POLYGON((0 0,20 10,10 30, 0 0))'), ST_GeomFromText('POLYGON((10 40, 40 50, 20 70, 10 40))'))
0	0
select 1, ST_Intersects(ST_GeomFromText('POLYGON((0 0,20 10,10 30, 0 0))'), ST_GeomFromText('POINT(10 10)'));
1	ST_Intersects(ST_GeomFromText('POLYGON((0 0,20 10,10 30, 0 0))'), ST_GeomFromText('POINT(10 10)'))
1	1
select 1, ST_Intersects(ST_GeomFromText('POLYGON((0 0,20 10,10 30, 0 0))'), ST_GeomFromText('POLYGON((10 10,30 20,20 40, 10 10))'));
1	ST_Intersects(ST_GeomFromText('POLYGON((0 0,20 10,10 30, 0 0))'), ST_GeomFromText('POLYGON((10 10,30 20,20 40, 10 10))'))
1	1
select 0, ST_Within(ST_GeomFromText('POLYGON((0 0,20 10,10 30, 0 0))'), ST_GeomFromText('POLYGON((10 10,30 20,20 40, 10 10))'));
0	ST_Within(ST_GeomFromText('POLYGON((0 0,20 10,10 30, 0 0))'), ST_GeomFromText('POLYGON((10 10,30 20,20 40, 10 10))'))
0	0
select 1, ST_Within(ST_GeomFromText('POLYGON((1 1,20 10,10 30, 1 1))'), ST_GeomFromText('POLYGON((0 0,30 5,10 40, 0 0))'));
1	ST_Within(ST_GeomFromText('POLYGON((1 1,20 10,10 30, 1 1))'), ST_GeomFromText('POLYGON((0 0,30 5,10 40, 0 0))'))
1	1
create table t1 (g point);
insert into t1 values 
(ST_GeomFromText('POINT(2 2)')), (ST_GeomFromText('POINT(2 4)')), (ST_GeomFromText('POINT(2 6)')), (ST_GeomFromText('POINT(2 8)')),
(ST_GeomFromText('POINT(4 2)')), (ST_GeomFromText('POINT(4 4)')), (ST_GeomFromText('POINT(4 6)')), (ST_GeomFromText('POINT(4 8)')),
(ST_GeomFromText('POINT(6 2)')), (ST_GeomFromText('POINT(6 4)')), (ST_GeomFromText('POINT(6 6)')), (ST_GeomFromText('POINT(6 8)')),
(ST_GeomFromText('POINT(8 2)')), (ST_GeomFromText('POINT(8 4)')), (ST_GeomFromText('POINT(8 6)')), (ST_GeomFromText('POINT(8 8)'));
select ST_astext(g) from t1 where ST_Within(g, ST_GeomFromText('POLYGON((5 1, 7 1, 7 7, 5 7, 3 3, 5 3, 5 1))'));
ST_astext(g)
POINT(4 4)
POINT(6 2)
POINT(6 4)
POINT(6 6)
select 'Contains';
Contains
Contains
select ST_astext(g) from t1 where ST_Contains(ST_GeomFromText('POLYGON((5 1, 7 1, 7 7, 5 7, 3 3, 5 3, 5 1))'), g);
ST_astext(g)
POINT(4 4)
POINT(6 2)
POINT(6 4)
POINT(6 6)
select 'st_Intersects';
st_Intersects
st_Intersects
select ST_astext(g) from t1 where ST_Intersects(ST_GeomFromText('POLYGON((5 1, 7 1, 7 7, 5 7, 3 3, 5 3, 5 1))'), g);
ST_astext(g)
POINT(4 4)
POINT(6 2)
POINT(6 4)
POINT(6 6)
select 'Contains';
Contains
Contains
select ST_astext(g) from t1 where ST_Contains(ST_GeomFromText('POLYGON((5 1, 7 1, 7 7, 5 7, 3 3, 5 3, 5 1))'), g);
ST_astext(g)
POINT(4 4)
POINT(6 2)
POINT(6 4)
POINT(6 6)
select 'Contains2';
Contains2
Contains2
select ST_astext(g) from t1 where ST_Contains(ST_GeomFromText('POLYGON((5 1, 7 1, 7 7, 5 7, 3 3, 5 3, 5 1), (5.01 3.01, 6 5, 9 5, 8 3, 5.01 3.01))'), g);
ST_astext(g)
POINT(4 4)
POINT(6 2)
POINT(6 6)
DROP TABLE t1;
select 0, ST_Within(ST_GeomFromText('LINESTRING(15 15, 50 50, 60 60)'), ST_GeomFromText('POLYGON((10 10,30 20,20 40, 10 10))'));
0	ST_Within(ST_GeomFromText('LINESTRING(15 15, 50 50, 60 60)'), ST_GeomFromText('POLYGON((10 10,30 20,20 40, 10 10))'))
0	0
select 1, ST_Within(ST_GeomFromText('LINESTRING(15 15, 16 16)'), ST_GeomFromText('POLYGON((10 10,30 20,20 40, 10 10))'));
1	ST_Within(ST_GeomFromText('LINESTRING(15 15, 16 16)'), ST_GeomFromText('POLYGON((10 10,30 20,20 40, 10 10))'))
1	1
select 1, ST_Intersects(ST_GeomFromText('LINESTRING(15 15, 50 50)'), ST_GeomFromText('LINESTRING(50 15, 15 50)'));
1	ST_Intersects(ST_GeomFromText('LINESTRING(15 15, 50 50)'), ST_GeomFromText('LINESTRING(50 15, 15 50)'))
1	1
select 1, ST_Intersects(ST_GeomFromText('LINESTRING(15 15, 50 50)'), ST_GeomFromText('LINESTRING(16 16, 51 51)'));
1	ST_Intersects(ST_GeomFromText('LINESTRING(15 15, 50 50)'), ST_GeomFromText('LINESTRING(16 16, 51 51)'))
1	1
select 1, ST_Intersects(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('POLYGON((50 5, 55 10, 0 45, 50 5))'));
1	ST_Intersects(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('POLYGON((50 5, 55 10, 0 45, 50 5))'))
1	1
select ST_astext(ST_Union(ST_geometryfromtext('point(1 1)'), ST_geometryfromtext('polygon((0 0, 2 0, 1 2, 0 0))')));
ST_astext(ST_Union(ST_geometryfromtext('point(1 1)'), ST_geometryfromtext('polygon((0 0, 2 0, 1 2, 0 0))')))
POLYGON((0 0,2 0,1 2,0 0))
select ST_astext(ST_Intersection(ST_geometryfromtext('point(1 1)'), ST_geometryfromtext('polygon((0 0, 2 0, 1 2, 0 0))')));
ST_astext(ST_Intersection(ST_geometryfromtext('point(1 1)'), ST_geometryfromtext('polygon((0 0, 2 0, 1 2, 0 0))')))
POINT(1 1)
select ST_Intersects(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('POLYGON((50 5, 55 10, 0 45, 50 5))'));
ST_Intersects(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('POLYGON((50 5, 55 10, 0 45, 50 5))'))
1
select ST_contains(ST_GeomFromText('MULTIPOLYGON(((0 0, 0 5, 5 5, 5 0, 0 0)), ((6 6, 6 11, 11 11, 11 6, 6 6)))'), ST_GeomFromText('POINT(5 10)'));
ST_contains(ST_GeomFromText('MULTIPOLYGON(((0 0, 0 5, 5 5, 5 0, 0 0)), ((6 6, 6 11, 11 11, 11 6, 6 6)))'), ST_GeomFromText('POINT(5 10)'))
0
select ST_Disjoint(ST_GeomFromText('POLYGON((0 0, 0 5, 5 5, 5 0, 0 0))'), ST_GeomFromText('POLYGON((10 10, 10 15, 15 15, 15 10, 10 10))'));
ST_Disjoint(ST_GeomFromText('POLYGON((0 0, 0 5, 5 5, 5 0, 0 0))'), ST_GeomFromText('POLYGON((10 10, 10 15, 15 15, 15 10, 10 10))'))
1
select ST_Disjoint(ST_GeomFromText('POLYGON((0 0, 0 5, 5 5, 5 0, 0 0))'), ST_GeomFromText('POLYGON((10 10, 10 4, 4 4, 4 10, 10 10))'));
ST_Disjoint(ST_GeomFromText('POLYGON((0 0, 0 5, 5 5, 5 0, 0 0))'), ST_GeomFromText('POLYGON((10 10, 10 4, 4 4, 4 10, 10 10))'))
0
select ST_Overlaps(ST_GeomFromText('POLYGON((0 0, 0 5, 5 5, 5 0, 0 0))'), ST_GeomFromText('POLYGON((10 10, 10 4, 4 4, 4 10, 10 10))'));
ST_Overlaps(ST_GeomFromText('POLYGON((0 0, 0 5, 5 5, 5 0, 0 0))'), ST_GeomFromText('POLYGON((10 10, 10 4, 4 4, 4 10, 10 10))'))
1
select ST_Overlaps(ST_GeomFromText('POLYGON((0 0, 0 5, 5 5, 5 0, 0 0))'), ST_GeomFromText('POLYGON((1 1, 1 4, 4 4, 4 1, 1 1))'));
ST_Overlaps(ST_GeomFromText('POLYGON((0 0, 0 5, 5 5, 5 0, 0 0))'), ST_GeomFromText('POLYGON((1 1, 1 4, 4 4, 4 1, 1 1))'))
0
select ST_DISTANCE(ST_GeomFromText('polygon((0 0, 1 2, 2 1, 0 0))'), ST_GeomFromText('polygon((2 2, 3 4, 4 3, 2 2))'));
ST_DISTANCE(ST_GeomFromText('polygon((0 0, 1 2, 2 1, 0 0))'), ST_GeomFromText('polygon((2 2, 3 4, 4 3, 2 2))'))
0.7071067811865476
select ST_DISTANCE(ST_GeomFromText('polygon((0 0, 1 2, 2 1, 0 0))'), ST_GeomFromText('linestring(0 1, 1 0)'));
ST_DISTANCE(ST_GeomFromText('polygon((0 0, 1 2, 2 1, 0 0))'), ST_GeomFromText('linestring(0 1, 1 0)'))
0
select ST_DISTANCE(ST_GeomFromText('polygon((0 0, 3 6, 6 3, 0 0))'), ST_GeomFromText('polygon((2 2, 3 4, 4 3, 2 2))'));
ST_DISTANCE(ST_GeomFromText('polygon((0 0, 3 6, 6 3, 0 0))'), ST_GeomFromText('polygon((2 2, 3 4, 4 3, 2 2))'))
0
select ST_DISTANCE(ST_GeomFromText('polygon((0 0, 3 6, 6 3, 0 0),(2 2, 3 4, 4 3, 2 2))'), ST_GeomFromText('point(3 3)'));
ST_DISTANCE(ST_GeomFromText('polygon((0 0, 3 6, 6 3, 0 0),(2 2, 3 4, 4 3, 2 2))'), ST_GeomFromText('point(3 3)'))
0.4472135954999579
select ST_DISTANCE(ST_GeomFromText('linestring(0 0, 3 6, 6 3, 0 0)'), ST_GeomFromText('polygon((2 2, 3 4, 4 3, 2 2))'));
ST_DISTANCE(ST_GeomFromText('linestring(0 0, 3 6, 6 3, 0 0)'), ST_GeomFromText('polygon((2 2, 3 4, 4 3, 2 2))'))
0.8944271909999159
select ST_astext(ST_Intersection(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('POLYGON((50 5, 55 10, 0 45, 50 5))')));
ST_astext(ST_Intersection(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('POLYGON((50 5, 55 10, 0 45, 50 5))')))
POLYGON((21.951219512195124 27.4390243902439,26.470588235294116 23.823529411764707,29.28994082840237 26.36094674556213,23.85542168674699 29.819277108433734,21.951219512195124 27.4390243902439))
select ST_astext(ST_Intersection(ST_GeomFromText('LINESTRING(0 0, 50 45, 40 50, 0 0)'), ST_GeomFromText('LINESTRING(50 5, 55 10, 0 45, 50 5)')));
ST_astext(ST_Intersection(ST_GeomFromText('LINESTRING(0 0, 50 45, 40 50, 0 0)'), ST_GeomFromText('LINESTRING(50 5, 55 10, 0 45, 50 5)')))
MULTIPOINT((26.470588235294116 23.823529411764707),(29.28994082840237 26.36094674556213),(23.85542168674699 29.819277108433734),(21.951219512195124 27.4390243902439))
select ST_astext(ST_Intersection(ST_GeomFromText('LINESTRING(0 0, 50 45, 40 50)'), ST_GeomFromText('LINESTRING(50 5, 55 10, 0 45)')));
ST_astext(ST_Intersection(ST_GeomFromText('LINESTRING(0 0, 50 45, 40 50)'), ST_GeomFromText('LINESTRING(50 5, 55 10, 0 45)')))
POINT(29.28994082840237 26.36094674556213)
select ST_astext(ST_Intersection(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('POINT(20 20)')));
ST_astext(ST_Intersection(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('POINT(20 20)')))
POINT(20 20)
select ST_astext(ST_Intersection(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('LINESTRING(-10 -10, 200 200)')));
ST_astext(ST_Intersection(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('LINESTRING(-10 -10, 200 200)')))
LINESTRING(0 0,46.666666666666664 46.666666666666664)
select ST_astext(ST_Intersection(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('LINESTRING(-10 -10, 200 200, 199 201, -11 -9)')));
ST_astext(ST_Intersection(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('LINESTRING(-10 -10, 200 200, 199 201, -11 -9)')))
GEOMETRYCOLLECTION(LINESTRING(0 0,46.666666666666664 46.666666666666664),LINESTRING(45.33333333333334 47.33333333333334,8 10))
select ST_astext(ST_UNION(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('LINESTRING(-10 -10, 200 200, 199 201, -11 -9)')));
ST_astext(ST_UNION(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('LINESTRING(-10 -10, 200 200, 199 201, -11 -9)')))
GEOMETRYCOLLECTION(POLYGON((0 0,50 45,40 50,0 0)),LINESTRING(-10 -10,0 0),LINESTRING(46.666666666666664 46.666666666666664,200 200,199 201,45.33333333333334 47.33333333333334),LINESTRING(8 10,-11 -9))
select ST_astext(ST_intersection(ST_GeomFromText('polygon((0 0, 1 0, 0 1, 0 0))'), ST_GeomFromText('polygon((0 0, 1 1, 0 2, 0 0))')));
ST_astext(ST_intersection(ST_GeomFromText('polygon((0 0, 1 0, 0 1, 0 0))'), ST_GeomFromText('polygon((0 0, 1 1, 0 2, 0 0))')))
POLYGON((0 1,0 0,0.5 0.5,0 1))
select ST_astext(ST_symdifference(ST_GeomFromText('polygon((0 0, 1 0, 0 1, 0 0))'), ST_GeomFromText('polygon((0 0, 1 1, 0 2, 0 0))')));
ST_astext(ST_symdifference(ST_GeomFromText('polygon((0 0, 1 0, 0 1, 0 0))'), ST_GeomFromText('polygon((0 0, 1 1, 0 2, 0 0))')))
MULTIPOLYGON(((0.5 0.5,0 0,1 0,0.5 0.5)),((0.5 0.5,1 1,0 2,0 1,0.5 0.5)))
select ST_astext(ST_UNION(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('LINESTRING(-10 -10, 200 200, 199 201, -11 -9)')));
ST_astext(ST_UNION(ST_GeomFromText('POLYGON((0 0, 50 45, 40 50, 0 0))'), ST_GeomFromText('LINESTRING(-10 -10, 200 200, 199 201, -11 -9)')))
GEOMETRYCOLLECTION(POLYGON((0 0,50 45,40 50,0 0)),LINESTRING(-10 -10,0 0),LINESTRING(46.666666666666664 46.666666666666664,200 200,199 201,45.33333333333334 47.33333333333334),LINESTRING(8 10,-11 -9))
select ST_astext(ST_buffer(ST_geometryfromtext('point(1 1)'), 1));
ST_astext(ST_buffer(ST_geometryfromtext('point(1 1)'), 1))
POLYGON((2 1,1.98079 1.19509,1.92388 1.38268,1.83147 1.55557,1.70711 1.70711,1.55557 1.83147,1.38268 1.92388,1.19509 1.98079,1 2,0.80491 1.98079,0.61732 1.92388,0.44443 1.83147,0.29289 1.70711,0.16853 1.55557,0.07612 1.38268,0.01921 1.19509,0 1,0.01921 0.80491,0.07612 0.61732,0.16853 0.44443,0.29289 0.29289,0.44443 0.16853,0.61732 0.07612,0.80491 0.01921,1 0,1.19509 0.01921,1.38268 0.07612,1.55557 0.16853,1.70711 0.29289,1.83147 0.44443,1.92388 0.61732,1.98079 0.80491,2 1))
create table t1(geom geometrycollection);
insert into t1 values (ST_GeomFromText('POLYGON((0 0, 10 10, 0 8, 0 0))'));
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
insert into t1 values (ST_GeomFromText('POLYGON((1 1, 10 10, 0 8, 1 1))'));
ERROR 22003: Cannot get geometry object from data you send to the GEOMETRY field
select ST_astext(geom), ST_area(geom),ST_area(ST_buffer(geom,2)) from t1;
ST_astext(geom)	ST_area(geom)	ST_area(ST_buffer(geom,2))
select ST_NUMPOINTS(ST_EXTERIORRING(ST_buffer(geom,2))) from t1;
ST_NUMPOINTS(ST_EXTERIORRING(ST_buffer(geom,2)))
set @geom=ST_GeomFromText('LINESTRING(2 1, 4 2, 2 3, 2 5)');
set @buff=ST_buffer(@geom,1);
select ST_NUMPOINTS(ST_EXTERIORRING(@buff)) from t1;
ST_NUMPOINTS(ST_EXTERIORRING(@buff))
DROP TABLE t1;
select st_touches(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'));
st_touches(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))
NULL
select st_touches(ST_GeomFromText('point(1 1)'), ST_GeomFromText('point(1 1)'));
st_touches(ST_GeomFromText('point(1 1)'), ST_GeomFromText('point(1 1)'))
NULL
select st_touches(ST_GeomFromText('polygon((0 0, 2 2, 0 4, 0 0))'), ST_GeomFromText('point(1 1)'));
st_touches(ST_GeomFromText('polygon((0 0, 2 2, 0 4, 0 0))'), ST_GeomFromText('point(1 1)'))
1
select st_touches(ST_GeomFromText('polygon((0 0, 2 2, 0 4, 0 0))'), ST_GeomFromText('point(1 0)'));
st_touches(ST_GeomFromText('polygon((0 0, 2 2, 0 4, 0 0))'), ST_GeomFromText('point(1 0)'))
0
select st_touches(ST_GeomFromText('polygon((0 0, 2 2, 0 4, 0 0))'), ST_GeomFromText('point(1 2)'));
st_touches(ST_GeomFromText('polygon((0 0, 2 2, 0 4, 0 0))'), ST_GeomFromText('point(1 2)'))
0
select st_touches(ST_GeomFromText('polygon((0 0, 2 2, 0 4, 0 0))'), ST_GeomFromText('polygon((1 1.2, 1 0, 2 0, 1 1.2))'));
st_touches(ST_GeomFromText('polygon((0 0, 2 2, 0 4, 0 0))'), ST_GeomFromText('polygon((1 1.2, 1 0, 2 0, 1 1.2))'))
0
select st_touches(ST_GeomFromText('polygon((0 0, 2 2, 0 4, 0 0))'), ST_GeomFromText('polygon((1 1, 1 0, 2 0, 1 1))'));
st_touches(ST_GeomFromText('polygon((0 0, 2 2, 0 4, 0 0))'), ST_GeomFromText('polygon((1 1, 1 0, 2 0, 1 1))'))
1
SELECT ST_Equals(ST_PolyFromText('POLYGON((67 13, 67 18, 67 18, 59 18, 59 13, 67 13) )'),ST_PolyFromText('POLYGON((67 13, 67 18, 59 19, 59 13, 59 13, 67 13) )')) as result;
result
0
SELECT ST_Equals(ST_PolyFromText('POLYGON((67 13, 67 18, 67 18, 59 18, 59 13, 67 13) )'),ST_PolyFromText('POLYGON((67 13, 67 18, 59 18, 59 13, 59 13, 67 13) )')) as result;
result
1
SELECT ST_Equals(ST_PointFromText('POINT (12 13)'),ST_PointFromText('POINT (12 13)')) as result;
result
1
select mbrcoveredby(ST_GeomFromText("point(2 4)"), ST_GeomFromText("polygon((2 2, 10 2, 10 10, 2 10, 2 2))"));
mbrcoveredby(ST_GeomFromText("point(2 4)"), ST_GeomFromText("polygon((2 2, 10 2, 10 10, 2 10, 2 2))"))
1
select mbrcontains(ST_GeomFromText("polygon((2 2, 10 2, 10 10, 2 10, 2 2))"), ST_GeomFromText("point(2 4)"));
mbrcontains(ST_GeomFromText("polygon((2 2, 10 2, 10 10, 2 10, 2 2))"), ST_GeomFromText("point(2 4)"))
0
select mbrcovers(ST_GeomFromText("polygon((2 2, 10 2, 10 10, 2 10, 2 2))"), ST_GeomFromText("point(2 4)"));
mbrcovers(ST_GeomFromText("polygon((2 2, 10 2, 10 10, 2 10, 2 2))"), ST_GeomFromText("point(2 4)"))
1
select mbrtouches(ST_GeomFromText("point (2 4)"), ST_GeomFromText("point (2 4)"));
mbrtouches(ST_GeomFromText("point (2 4)"), ST_GeomFromText("point (2 4)"))
0
select mbrtouches(ST_GeomFromText("point(2 4)"), ST_GeomFromText("linestring(2 0, 2 6)"));
mbrtouches(ST_GeomFromText("point(2 4)"), ST_GeomFromText("linestring(2 0, 2 6)"))
0
select mbrtouches(ST_GeomFromText("point(2 4)"), ST_GeomFromText("linestring(2 0, 2 4)"));
mbrtouches(ST_GeomFromText("point(2 4)"), ST_GeomFromText("linestring(2 0, 2 4)"))
1
select mbrtouches(ST_GeomFromText("point(2 4)"), ST_GeomFromText("polygon((2 2, 6 2, 6 6, 2 6, 2 2))"));
mbrtouches(ST_GeomFromText("point(2 4)"), ST_GeomFromText("polygon((2 2, 6 2, 6 6, 2 6, 2 2))"))
1
select mbrtouches(ST_GeomFromText("linestring(1 0, 2 0)"), ST_GeomFromText("polygon((0 0, 3 0, 3 3, 0 3, 0 0))"));
mbrtouches(ST_GeomFromText("linestring(1 0, 2 0)"), ST_GeomFromText("polygon((0 0, 3 0, 3 3, 0 3, 0 0))"))
1
select mbrtouches(ST_GeomFromText("linestring(3 2, 4 2)"), ST_GeomFromText("polygon((0 0, 3 0, 3 3, 0 3, 0 0))"));
mbrtouches(ST_GeomFromText("linestring(3 2, 4 2)"), ST_GeomFromText("polygon((0 0, 3 0, 3 3, 0 3, 0 0))"))
1
select mbrwithin(ST_GeomFromText("point(2 4)"), ST_GeomFromText("point(2 4)"));
mbrwithin(ST_GeomFromText("point(2 4)"), ST_GeomFromText("point(2 4)"))
1
select mbrwithin(ST_GeomFromText("point(2 4)"), ST_GeomFromText("linestring(2 0, 2 6)"));
mbrwithin(ST_GeomFromText("point(2 4)"), ST_GeomFromText("linestring(2 0, 2 6)"))
1
select mbrwithin(ST_GeomFromText("point(2 4)"), ST_GeomFromText("linestring(2 0, 2 4)"));
mbrwithin(ST_GeomFromText("point(2 4)"), ST_GeomFromText("linestring(2 0, 2 4)"))
0
select mbrwithin(ST_GeomFromText("point(2 4)"), ST_GeomFromText("polygon((2 2, 10 2, 10 10, 2 10, 2 2))"));
mbrwithin(ST_GeomFromText("point(2 4)"), ST_GeomFromText("polygon((2 2, 10 2, 10 10, 2 10, 2 2))"))
0
select mbrwithin(ST_GeomFromText("linestring(1 0, 2 0)"), ST_GeomFromText("linestring(0 0, 3 0)"));
mbrwithin(ST_GeomFromText("linestring(1 0, 2 0)"), ST_GeomFromText("linestring(0 0, 3 0)"))
1
select mbrwithin(ST_GeomFromText("linestring(1 0, 2 0)"), ST_GeomFromText("polygon((0 0, 3 0, 3 3, 0 3, 0 0))"));
mbrwithin(ST_GeomFromText("linestring(1 0, 2 0)"), ST_GeomFromText("polygon((0 0, 3 0, 3 3, 0 3, 0 0))"))
0
select mbrwithin(ST_GeomFromText("linestring(1 1, 2 1)"), ST_GeomFromText("polygon((0 0, 3 0, 3 3, 0 3, 0 0))"));
mbrwithin(ST_GeomFromText("linestring(1 1, 2 1)"), ST_GeomFromText("polygon((0 0, 3 0, 3 3, 0 3, 0 0))"))
1
select mbrwithin(ST_GeomFromText("linestring(0 1, 3 1)"), ST_GeomFromText("polygon((0 0, 3 0, 3 3, 0 3, 0 0))"));
mbrwithin(ST_GeomFromText("linestring(0 1, 3 1)"), ST_GeomFromText("polygon((0 0, 3 0, 3 3, 0 3, 0 0))"))
1
#
# BUG#11755628/47429: INTERSECTION FUNCTION CRASHED MYSQLD 
# BUG#11759650/51979: UNION/INTERSECTION OF POLYGONS CRASHES MYSQL 
#
# Invalid geometry in input.
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('POLYGON((525000 183300,525400
183300,525400 18370, 525000 183700,525000 183300))'),
ST_GeomFromText('POLYGON((525298.67 183511.53,525296.57
183510.39,525296.42 183510.31,525289.11 183506.62,525283.17
183503.47,525280.98 183502.26,525278.63 183500.97,525278.39
183500.84,525276.79 183500,525260.7 183491.55,525263.95
183484.75,525265.58 183481.95,525278.97 183488.73,525276.5
183493.45,525275.5 183495.7,525280.35 183498.2,525282.3
183499.1,525282.2 183499.3,525283.55 183500,525301.75
183509.35,525304.45 183504.25,525307.85 183504.95,525304.5
183510.83,525302.81 183513.8,525298.67 183511.53),(525275.06
183489.89,525272.06 183488.37,525268.94 183494.51,525271.94
183496.03,525275.06 183489.89),(525263.26 183491.55,525266.15
183493.04,525269.88 183485.82,525266.99 183484.33,525263.26
183491.55))'))) st_u;
SET @a=0x0000000001030000000200000005000000000000000000000000000000000000000000000000002440000000000000000000000000000024400000000000002440000000000000000000000000000024400000000000000000000000000000000000000000000000000000F03F000000000000F03F0000000000000040000000000000F03F00000000000000400000000000000040000000000000F03F0000000000000040000000000000F03F000000000000F03F;
SELECT ST_ASTEXT(ST_TOUCHES(@a, ST_GEOMFROMTEXT('point(0 0)'))) t;
ERROR 22023: Invalid GIS data provided to function st_touches.
#
# Bug 18408988 - WL7220 : OVERLAPPING MEMCPY CRASH IN BG_GEOMETRY_COLLECTION::CUT
# Bug 18408919 - WL7220 : OVERLAPPING MEMCPY CRASH IN GEOMETRY::AS_WKB
# Bug 18408875 - WL7220 : ASSERTION FAILED: *P == 0 || *P == 1
#
select st_union((cast(linestring(point(6,-68), point(-22,-4)) as binary(13))),
st_intersection(point(6,8),multipoint(point(3,1),point(-4,-6),point(1,6),point(-3,-5),point(5,4))));
ERROR 22023: Invalid GIS data provided to function st_union.
SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
select st_difference((convert(st_polygonfromwkb(linestring(point(1,1))) using gb18030)),
st_geomcollfromwkb(point(1,1)));
ERROR 22023: Invalid GIS data provided to function linestring.
SET sql_mode = default;
select ST_astext(ST_geomfromwkb(ST_AsWKB(st_intersection(linestring(point(-59,82),point(32,29)), point(2,-5))))) as result;
result
GEOMETRYCOLLECTION EMPTY
SELECT ST_AsText(ST_Symdifference(ST_GeomFromText('POLYGON((5 0,15 25,25 0,15 5,5 0))'),ST_GeomFromText('POLYGON((5 0,15 25,25 0,15 5,5 0))')));
ST_AsText(ST_Symdifference(ST_GeomFromText('POLYGON((5 0,15 25,25 0,15 5,5 0))'),ST_GeomFromText('POLYGON((5 0,15 25,25 0,15 5,5 0))')))
GEOMETRYCOLLECTION EMPTY
SELECT st_equals(ST_GeomFromWKB(ST_AsWKB(Polygon(Linestring(Point(0, 0),Point(1, 0),Point(1, 1),Point(0, 1), Point(0, 0))))),
ST_GeomFromText('POLYGON((0 0, 1 0, 1 1, 0 1, 0 0))'));
st_equals(ST_GeomFromWKB(ST_AsWKB(Polygon(Linestring(Point(0, 0),Point(1, 0),Point(1, 1),Point(0, 1), Point(0, 0))))),
ST_GeomFromText('POLYGON((0 0, 1 0, 1 1, 0 1, 0 0))'))
1
SET @plgnwkb=0x0103000000010000000500000000000000000000000000000000000000000000000000F03F0000000000000000000000000000F03F000000000000F03F0000000000000000000000000000F03F00000000000000000000000000000000;
SELECT ST_equals(ST_GeomFromWKB(@plgnwkb), ST_GeomFromText('polygon((0 0, 1 0, 1 1, 0 1, 0 0))'));
ST_equals(ST_GeomFromWKB(@plgnwkb), ST_GeomFromText('polygon((0 0, 1 0, 1 1, 0 1, 0 0))'))
1
#
# Bug 18423730 - WL7220: ASSERTION FAILED: LIST ITERATOR NOT INCREMENTABLE
# Enable using empty geometry collection as intermediate and final result in GIS computation.
select ST_astext(st_union(
st_intersection(
multipoint(point(-1,-1)),
point(1,-1)
),
st_difference(
multipoint(point(-1,1)),
point(-1,-1)
)));
ST_astext(st_union(
st_intersection(
multipoint(point(-1,-1)),
point(1,-1)
),
st_difference(
multipoint(point(-1,1)),
point(-1,-1)
)))
POINT(-1 1)
select ST_astext(st_union(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))));
ST_astext(st_union(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))))
GEOMETRYCOLLECTION EMPTY
select ST_astext(st_intersection(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))));
ST_astext(st_intersection(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))))
GEOMETRYCOLLECTION EMPTY
select ST_astext(st_difference(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))));
ST_astext(st_difference(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))))
GEOMETRYCOLLECTION EMPTY
select ST_astext(st_symdifference(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))));
ST_astext(st_symdifference(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))))
GEOMETRYCOLLECTION EMPTY
select ST_astext(st_intersection(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_difference(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))));
ST_astext(st_intersection(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_difference(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))))
GEOMETRYCOLLECTION EMPTY
select ST_astext(st_difference(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_difference(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))));
ST_astext(st_difference(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_difference(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))))
GEOMETRYCOLLECTION EMPTY
select ST_astext(st_symdifference(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_difference(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))));
ST_astext(st_symdifference(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_difference(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))))
POINT(0 0)
select ST_astext(st_union(ST_GeomFromText('multipoint(2 2, 3 3)'), st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))));
ST_astext(st_union(ST_GeomFromText('multipoint(2 2, 3 3)'), st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))))
MULTIPOINT((2 2),(3 3))
select ST_astext(st_intersection(ST_GeomFromText('multipoint(2 2, 3 3)'), st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))));
ST_astext(st_intersection(ST_GeomFromText('multipoint(2 2, 3 3)'), st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))))
GEOMETRYCOLLECTION EMPTY
select ST_astext(st_symdifference(ST_GeomFromText('multipoint(2 2, 3 3)'), st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))));
ST_astext(st_symdifference(ST_GeomFromText('multipoint(2 2, 3 3)'), st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))))
MULTIPOINT((2 2),(3 3))
select ST_astext(st_difference(ST_GeomFromText('multipoint(2 2, 3 3)'), st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))));
ST_astext(st_difference(ST_GeomFromText('multipoint(2 2, 3 3)'), st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)'))))
MULTIPOINT((2 2),(3 3))
select ST_astext(st_difference(ST_GeomFromText('multipoint(2 2, 3 3)'), ST_GeomFromText('multipoint(0 0, 4 4)')));
ST_astext(st_difference(ST_GeomFromText('multipoint(2 2, 3 3)'), ST_GeomFromText('multipoint(0 0, 4 4)')))
MULTIPOINT((2 2),(3 3))
select ST_astext(st_difference(ST_GeomFromText('multipoint(2 2, 3 3)'), ST_GeomFromText('multipoint(2 2, 3 3)')));
ST_astext(st_difference(ST_GeomFromText('multipoint(2 2, 3 3)'), ST_GeomFromText('multipoint(2 2, 3 3)')))
GEOMETRYCOLLECTION EMPTY
select ST_astext(st_symdifference(ST_GeomFromText('multipoint(2 2, 3 3)'), ST_GeomFromText('multipoint(0 0, 4 4)')));
ST_astext(st_symdifference(ST_GeomFromText('multipoint(2 2, 3 3)'), ST_GeomFromText('multipoint(0 0, 4 4)')))
MULTIPOINT((2 2),(3 3),(0 0),(4 4))
select ST_astext(st_symdifference(ST_GeomFromText('multipoint(2 2, 3 3)'), ST_GeomFromText('multipoint(2 2, 3 3)')));
ST_astext(st_symdifference(ST_GeomFromText('multipoint(2 2, 3 3)'), ST_GeomFromText('multipoint(2 2, 3 3)')))
GEOMETRYCOLLECTION EMPTY
select st_intersects(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_intersects(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_disjoint(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_disjoint(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_equals(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_equals(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
1
select st_contains(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_contains(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_within(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_within(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_touches(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_touches(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_overlaps(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_overlaps(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_crosses(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_crosses(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_intersects(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_intersects(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_disjoint(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_disjoint(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_equals(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_equals(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
0
select st_contains(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_contains(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_within(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_within(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_touches(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_touches(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_overlaps(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_overlaps(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_crosses(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_crosses(st_intersection(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_union(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_intersects(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_intersects(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_disjoint(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_disjoint(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_equals(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_equals(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
0
select st_contains(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_contains(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_within(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_within(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_touches(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_touches(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_overlaps(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_overlaps(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
select st_crosses(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')));
st_crosses(st_union(ST_GeomFromText('point(1 1)'), ST_GeomFromText('multipoint(2 2, 3 3)')), 
st_intersection(ST_GeomFromText('point(0 0)'), ST_GeomFromText('point(1 1)')))
NULL
#
# Bug 18701114 - WL7220: ASSERTION FAILED: (PTR != 0 && NBYTES > 0) || (PTR == 0 && NBYTES == 0),
#
select
st_within(
multipoint(point(4,2),point(-6,-8)),
polygon(
linestring(point(13,0),point(13,0)),
linestring(point(2,4), point(2,4))
));
ERROR 22023: Invalid GIS data provided to function polygon.
#
# Bug 18851092 - ST_GEOMFROMTEXT DOES NOT UNDERSTAND NESTED GEOMETRYCOLLECTIONS
#
SELECT ST_AsText(ST_GeomFromText('GeometryCollection()'));
ST_AsText(ST_GeomFromText('GeometryCollection()'))
GEOMETRYCOLLECTION EMPTY
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection())'));
ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection())'))
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION EMPTY)
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(,)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(())'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(point(0 0),)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(,point(0 0))'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(())'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText('GeometryCollection((point(0 0)))'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection())'));
ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection())'))
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION EMPTY)
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))'));
ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))'))
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1)))
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))'));
ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))'))
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1)))
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'));
ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'))
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1)),GEOMETRYCOLLECTION(LINESTRING(1 1,2 2)))
SELECT ST_AsText(st_union(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))')));
ST_AsText(st_union(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))')))
LINESTRING(1 1,2 2)
SELECT ST_AsText(st_difference(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))')));
ST_AsText(st_difference(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))')))
LINESTRING(1 1,2 2)
SELECT ST_AsText(st_intersection(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))')));
ST_AsText(st_intersection(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))')))
POINT(1 1)
SELECT ST_AsText(st_symdifference(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1), Point(2 2)))')));
ST_AsText(st_symdifference(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1), Point(2 2)))')))
LINESTRING(1 1,2 2)
#
# Bug 18701114 - WL7220: ASSERTION FAILED: (PTR != 0 && NBYTES > 0) || (PTR == 0 && NBYTES == 0),
#
select
st_within(
multipoint(point(4,2),point(-6,-8)),
polygon(
linestring(point(13,0),point(13,0)),
linestring(point(2,4), point(2,4))
));
ERROR 22023: Invalid GIS data provided to function polygon.
#
# Bug 18851092 - ST_GEOMFROMTEXT DOES NOT UNDERSTAND NESTED GEOMETRYCOLLECTIONS
#
SELECT ST_AsText(ST_GeomFromText('GeometryCollection()'));
ST_AsText(ST_GeomFromText('GeometryCollection()'))
GEOMETRYCOLLECTION EMPTY
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection())'));
ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection())'))
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION EMPTY)
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(,)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(())'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(point(0 0),)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(,point(0 0))'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(())'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText('GeometryCollection((point(0 0)))'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection())'));
ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection())'))
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION EMPTY)
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))'));
ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))'))
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1)))
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))'));
ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))'))
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1)))
SELECT ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'));
ST_AsText(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'))
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1)),GEOMETRYCOLLECTION(LINESTRING(1 1,2 2)))
SELECT ST_AsText(st_union(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))')));
ST_AsText(st_union(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))')))
LINESTRING(1 1,2 2)
SELECT ST_AsText(st_difference(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))')));
ST_AsText(st_difference(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))')))
LINESTRING(1 1,2 2)
SELECT ST_AsText(st_intersection(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))')));
ST_AsText(st_intersection(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)))')))
POINT(1 1)
SELECT ST_AsText(st_symdifference(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1), Point(2 2)))')));
ST_AsText(st_symdifference(ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1)), GeometryCollection(linestring(1 1, 2 2)))'),
ST_GeomFromText('GeometryCollection(GeometryCollection(Point(1 1), Point(2 2)))')))
LINESTRING(1 1,2 2)
select ST_astext(st_difference(ST_GeomFromText('polygon((0 0, 1 0, 0 1, 0 0))'), ST_GeomFromText('polygon((0 0, 0 1, 1 1, 1 0, 0 0))'))) as result;
result
GEOMETRYCOLLECTION EMPTY
select ST_astext(st_symdifference(ST_GeomFromText('polygon((0 0, 1 0, 0 1, 0 0))'), ST_GeomFromText('polygon((0 0, 0 1, 1 1, 1 0, 0 0))'))) as result;
result
POLYGON((0 1,1 0,1 1,0 1))
# Invalid geometry in input
select ST_astext(ST_symdifference(ST_GeomFromText('polygon((0 0, 1 0, 0 1, 0 0))'), ST_GeomFromText('polygon((0 0, 1 0, 0 0,0 1, 0 0))'))) as result;
result
GEOMETRYCOLLECTION EMPTY
SELECT ST_Equals(ST_GeomFromText('polygon((0 0, 1 0, 0 1, 0 0))'), ST_GeomFromText('polygon((0 0, 1 0, 0 0,0 1, 0 0))')) as result;
result
0
select ST_astext(ST_PolyFromWKB(ST_AsWKB(Polygon(LineString(Point(0, 0), Point(30, 0), Point(30, 30), Point(1, 0), Point(0, 0))))));
ST_astext(ST_PolyFromWKB(ST_AsWKB(Polygon(LineString(Point(0, 0), Point(30, 0), Point(30, 30), Point(1, 0), Point(0, 0))))))
POLYGON((0 0,30 0,30 30,1 0,0 0))
select ST_AsText(ST_PolygonFromText('POLYGON((10 10,20 10,20 20,10 20, 10 10))'));
ST_AsText(ST_PolygonFromText('POLYGON((10 10,20 10,20 20,10 20, 10 10))'))
POLYGON((10 10,20 10,20 20,10 20,10 10))
select ST_area(ST_PolygonFromText('POLYGON((10 10,20 10,20 20,10 20, 10 10))'));
ST_area(ST_PolygonFromText('POLYGON((10 10,20 10,20 20,10 20, 10 10))'))
100
select ST_AsText(Polygon(LineString(Point(0, 0), Point(1, 0), Point(1,1), Point(0, 1), Point(0, 0))));
ST_AsText(Polygon(LineString(Point(0, 0), Point(1, 0), Point(1,1), Point(0, 1), Point(0, 0))))
POLYGON((0 0,1 0,1 1,0 1,0 0))
select ST_AsText(ST_GeometryFromWKB(ST_AsWKB(GeometryCollection(POINT(0, 0),
MULTIPOINT(point(0, 0), point(1, 1)), LINESTRING(point(0, 0),point(10, 10)),
MULTILINESTRING(LINESTRING(point(1, 2), point(1, 3))),
POLYGON(LineString(Point(10, 20), Point(1, 1), Point(2, 2), Point(1, 1),
Point(10, 20))), MULTIPOLYGON(Polygon(LineString(Point(0, 0), Point(1, 0),
Point(1, 1), Point(0, 0)))))))) as Result;
Result
GEOMETRYCOLLECTION(POINT(0 0),MULTIPOINT((0 0),(1 1)),LINESTRING(0 0,10 10),MULTILINESTRING((1 2,1 3)),POLYGON((10 20,1 1,2 2,1 1,10 20)),MULTIPOLYGON(((0 0,1 0,1 1,0 0))))
select ST_AsText(ST_GeometryFromWKB(ST_AsWKB(GeometryCollection(POINT(0, 0),
MULTIPOINT(point(0, 0), point(1, 1)), LINESTRING(point(0, 0),point(10, 10)),
MULTILINESTRING(LINESTRING(point(1, 2), point(1, 3))),
POLYGON(LineString(Point(10, 20), Point(1, 1), Point(2, 2), Point(10, 20))),
MULTIPOLYGON(Polygon(LineString(Point(0, 0), Point(1, 0), Point(1, 1),
Point(0, 0)))))))) as Result;
Result
GEOMETRYCOLLECTION(POINT(0 0),MULTIPOINT((0 0),(1 1)),LINESTRING(0 0,10 10),MULTILINESTRING((1 2,1 3)),POLYGON((10 20,1 1,2 2,10 20)),MULTIPOLYGON(((0 0,1 0,1 1,0 0))))
select st_touches(ST_GeometryFromText('geometrycollection(polygon((0 0, 1 0, 1 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 2 0, 2 1, 1 1, 1 0)))'));
st_touches(ST_GeometryFromText('geometrycollection(polygon((0 0, 1 0, 1 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 2 0, 2 1, 1 1, 1 0)))'))
1
select st_overlaps(ST_GeometryFromText('geometrycollection(polygon((0 0, 2 0, 2 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 3 0, 3 1, 1 1, 1 0)))'));
st_overlaps(ST_GeometryFromText('geometrycollection(polygon((0 0, 2 0, 2 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 3 0, 3 1, 1 1, 1 0)))'))
1
select st_crosses(ST_GeometryFromText('geometrycollection(multipoint(0 0, 1 0, 1 1, 0 1, 0 0))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 2 0, 2 1, 1 1, 1 0)))'));
st_crosses(ST_GeometryFromText('geometrycollection(multipoint(0 0, 1 0, 1 1, 0 1, 0 0))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 2 0, 2 1, 1 1, 1 0)))'))
0
select st_astext(st_union(ST_GeometryFromText('geometrycollection(polygon((0 0, 1 0, 1 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 2 0, 2 1, 1 1, 1 0)))')));
st_astext(st_union(ST_GeometryFromText('geometrycollection(polygon((0 0, 1 0, 1 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 2 0, 2 1, 1 1, 1 0)))')))
POLYGON((2 1,0 1,0 0,1 0,2 0,2 1))
select st_astext(st_union(ST_GeometryFromText('geometrycollection(polygon((0 0, 2 0, 2 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 3 0, 3 1, 1 1, 1 0)))')));
st_astext(st_union(ST_GeometryFromText('geometrycollection(polygon((0 0, 2 0, 2 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 3 0, 3 1, 1 1, 1 0)))')))
POLYGON((3 1,0 1,0 0,1 0,3 0,3 1))
select st_astext(st_intersection(ST_GeometryFromText('geometrycollection(polygon((0 0, 1 0, 1 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 2 0, 2 1, 1 1, 1 0)))')));
st_astext(st_intersection(ST_GeometryFromText('geometrycollection(polygon((0 0, 1 0, 1 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 2 0, 2 1, 1 1, 1 0)))')))
LINESTRING(1 1,1 0)
select st_astext(st_intersection(ST_GeometryFromText('geometrycollection(polygon((0 0, 2 0, 2 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 3 0, 3 1, 1 1, 1 0)))')));
st_astext(st_intersection(ST_GeometryFromText('geometrycollection(polygon((0 0, 2 0, 2 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 3 0, 3 1, 1 1, 1 0)))')))
POLYGON((1 1,1 0,2 0,2 1,1 1))
select st_astext(st_difference(ST_GeometryFromText('geometrycollection(polygon((0 0, 1 0, 1 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 2 0, 2 1, 1 1, 1 0)))')));
st_astext(st_difference(ST_GeometryFromText('geometrycollection(polygon((0 0, 1 0, 1 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 2 0, 2 1, 1 1, 1 0)))')))
POLYGON((1 1,0 1,0 0,1 0,1 1))
select st_astext(st_difference(ST_GeometryFromText('geometrycollection(polygon((0 0, 2 0, 2 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 3 0, 3 1, 1 1, 1 0)))')));
st_astext(st_difference(ST_GeometryFromText('geometrycollection(polygon((0 0, 2 0, 2 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 3 0, 3 1, 1 1, 1 0)))')))
POLYGON((1 1,0 1,0 0,1 0,1 1))
select st_astext(st_symdifference(ST_GeometryFromText('geometrycollection(polygon((0 0, 1 0, 1 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 2 0, 2 1, 1 1, 1 0)))')));
st_astext(st_symdifference(ST_GeometryFromText('geometrycollection(polygon((0 0, 1 0, 1 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 2 0, 2 1, 1 1, 1 0)))')))
POLYGON((0 0,2 0,2 1,1 1,0 1,0 0))
select st_astext(st_symdifference(ST_GeometryFromText('geometrycollection(polygon((0 0, 2 0, 2 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 3 0, 3 1, 1 1, 1 0)))')));
st_astext(st_symdifference(ST_GeometryFromText('geometrycollection(polygon((0 0, 2 0, 2 1, 0 1, 0 0)))'), ST_GeometryFromText('geometrycollection(polygon((1 0, 3 0, 3 1, 1 1, 1 0)))')))
MULTIPOLYGON(((1 1,0 1,0 0,1 0,1 1)),((2 1,2 0,3 0,3 1,2 1)))
select ST_astext(Polygon(LineString(Point(0, 0), Point(30, 0), Point(30, 30), Point(1, 0), Point(0, 0))));
ST_astext(Polygon(LineString(Point(0, 0), Point(30, 0), Point(30, 30), Point(1, 0), Point(0, 0))))
POLYGON((0 0,30 0,30 30,1 0,0 0))
select ST_astext(ST_envelope(ST_PolyFromWKB(ST_AsWKB(Polygon(LineString(Point(0, 0), Point(30, 0), Point(30, 30), Point(1, 0), Point(0, 0)))))));
ST_astext(ST_envelope(ST_PolyFromWKB(ST_AsWKB(Polygon(LineString(Point(0, 0), Point(30, 0), Point(30, 30), Point(1, 0), Point(0, 0)))))))
POLYGON((0 0,30 0,30 30,0 30,0 0))
select ST_astext(ST_centroid(ST_PolyFromWKB(ST_AsWKB(Polygon(LineString(Point(0, 0), Point(30, 0), Point(30, 30), Point(1, 0), Point(0, 0)))))));
ERROR 22023: Invalid GIS data provided to function st_centroid.
select ST_astext(ST_convexhull(ST_PolyFromWKB(ST_AsWKB(Polygon(LineString(Point(0, 0), Point(30, 0), Point(30, 30), Point(1, 0), Point(0, 0)))))));
ERROR 22023: Invalid GIS data provided to function st_convexhull.
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POINT(10 10),MULTIPOINT(0 0,10 10))')));
ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POINT(10 10),MULTIPOINT(0 0,10 10))')))
POINT(6.666666666666667 6.666666666666667)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POINT(10 10),MULTIPOINT(0 0,10 10))')));
ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POINT(10 10),MULTIPOINT(0 0,10 10))')))
POINT(6.666666666666667 6.666666666666667)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('linestring(0 0, 1 1, 2 2)')));
ST_AsText(ST_Centroid(ST_GeomFromText('linestring(0 0, 1 1, 2 2)')))
POINT(1 1)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('multipoint(0 0, 1 1, 2 2)')));
ST_AsText(ST_Centroid(ST_GeomFromText('multipoint(0 0, 1 1, 2 2)')))
POINT(1 1)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('multilinestring((0 0, 1 1, 2 2), (3 3, 4 4, 5 5))')));
ST_AsText(ST_Centroid(ST_GeomFromText('multilinestring((0 0, 1 1, 2 2), (3 3, 4 4, 5 5))')))
POINT(2.5 2.5)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('polygon((0 0, 1 1, 2 2, 0 0), (3 3, 4 4, 5 5, 3 3))')));
ERROR 22023: Invalid GIS data provided to function st_centroid.
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('multipolygon(((0 0, 1 1, 2 2, 0 0), (3 3, 4 4, 5 5, 3 3)), ((0 0, 1 0, 1 1, 0 1, 0 0)))')));
ERROR 22023: Invalid GIS data provided to function st_centroid.
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('geometrycollection(polygon((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)), polygon((0 0, 1 0, 1 1, 0 1, 0 0)))')));
ST_AsText(ST_Centroid(ST_GeomFromText('geometrycollection(polygon((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)), polygon((0 0, 1 0, 1 1, 0 1, 0 0)))')))
POINT(1.3888888888888888 1.3888888888888888)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('multipolygon(((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)), ((0 0, 1 0, 1 1, 0 1, 0 0)))')));
ST_AsText(ST_Centroid(ST_GeomFromText('multipolygon(((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)), ((0 0, 1 0, 1 1, 0 1, 0 0)))')))
POINT(1.3888888888888888 1.3888888888888888)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('multipolygon(((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)))')));
ST_AsText(ST_Centroid(ST_GeomFromText('multipolygon(((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)))')))
POINT(1.5 1.5)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('polygon((0 0, 1 0, 1 1, 0 1, 0 0))')));
ST_AsText(ST_Centroid(ST_GeomFromText('polygon((0 0, 1 0, 1 1, 0 1, 0 0))')))
POINT(0.5 0.5)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('multipoint(0 0, 3 0, 3 3, 0 3, 0 0, 1 1, 2 1, 2 2, 1 2, 1 1)')));
ST_AsText(ST_Centroid(ST_GeomFromText('multipoint(0 0, 3 0, 3 3, 0 3, 0 0, 1 1, 2 1, 2 2, 1 2, 1 1)')))
POINT(1.3 1.3)
SELECT ST_AsText(ST_Convexhull(ST_GeomFromText('GEOMETRYCOLLECTION(POINT(10 10),MULTIPOINT(0 0,10 10))')));
ST_AsText(ST_Convexhull(ST_GeomFromText('GEOMETRYCOLLECTION(POINT(10 10),MULTIPOINT(0 0,10 10))')))
LINESTRING(0 0,10 10)
SELECT ST_AsText(ST_Convexhull(ST_GeomFromText('linestring(0 0, 1 1, 2 2)')));
ST_AsText(ST_Convexhull(ST_GeomFromText('linestring(0 0, 1 1, 2 2)')))
LINESTRING(0 0,2 2)
SELECT ST_AsText(ST_Convexhull(ST_GeomFromText('multipoint(0 0, 1 1, 2 2)')));
ST_AsText(ST_Convexhull(ST_GeomFromText('multipoint(0 0, 1 1, 2 2)')))
LINESTRING(0 0,2 2)
SELECT ST_AsText(ST_Convexhull(ST_GeomFromText('multilinestring((0 0, 1 1, 2 2), (3 3, 4 4, 5 5))')));
ST_AsText(ST_Convexhull(ST_GeomFromText('multilinestring((0 0, 1 1, 2 2), (3 3, 4 4, 5 5))')))
LINESTRING(0 0,5 5)
SELECT ST_AsText(ST_Convexhull(ST_GeomFromText('polygon((0 0, 1 1, 2 2, 0 0), (3 3, 4 4, 5 5, 3 3))')));
ERROR 22023: Invalid GIS data provided to function st_convexhull.
SELECT ST_AsText(ST_Convexhull(ST_GeomFromText('multipolygon(((0 0, 1 1, 2 2, 0 0), (3 3, 4 4, 5 5, 3 3)), ((0 0, 1 0, 1 1, 0 1, 0 0)))')));
ERROR 22023: Invalid GIS data provided to function st_convexhull.
SELECT ST_AsText(ST_Convexhull(ST_GeomFromText('geometrycollection(polygon((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)), polygon((0 0, 1 0, 1 1, 0 1, 0 0)))')));
ST_AsText(ST_Convexhull(ST_GeomFromText('geometrycollection(polygon((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)), polygon((0 0, 1 0, 1 1, 0 1, 0 0)))')))
POLYGON((0 0,3 0,3 3,0 3,0 0))
SELECT ST_AsText(ST_Convexhull(ST_GeomFromText('multipolygon(((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)), ((0 0, 1 0, 1 1, 0 1, 0 0)))')));
ST_AsText(ST_Convexhull(ST_GeomFromText('multipolygon(((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)), ((0 0, 1 0, 1 1, 0 1, 0 0)))')))
POLYGON((0 0,3 0,3 3,0 3,0 0))
SELECT ST_AsText(ST_Convexhull(ST_GeomFromText('multipolygon(((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)))')));
ST_AsText(ST_Convexhull(ST_GeomFromText('multipolygon(((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)))')))
POLYGON((0 0,3 0,3 3,0 3,0 0))
SELECT ST_AsText(ST_Convexhull(ST_GeomFromText('polygon((0 0, 1 0, 1 1, 0 1, 0 0))')));
ST_AsText(ST_Convexhull(ST_GeomFromText('polygon((0 0, 1 0, 1 1, 0 1, 0 0))')))
POLYGON((0 0,1 0,1 1,0 1,0 0))
SELECT ST_AsText(ST_Convexhull(ST_GeomFromText('multipoint(0 0, 3 0, 3 3, 0 3, 0 0, 1 1, 2 1, 2 2, 1 2, 1 1)')));
ST_AsText(ST_Convexhull(ST_GeomFromText('multipoint(0 0, 3 0, 3 3, 0 3, 0 0, 1 1, 2 1, 2 2, 1 2, 1 1)')))
POLYGON((0 0,3 0,3 3,0 3,0 0))
#
# Bug #18911154     ST_CENTROID() RETURNING ERROR MESSAGE FOR VALID GEOMETRY-COLLECTION GEOMETRY
#
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(MULTIPOINT(0 0,10 10))')));
ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(MULTIPOINT(0 0,10 10))')))
POINT(5 5)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0)))')));
ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0)))')))
POINT(5 5)
SELECT ST_AsText(ST_ConvexHull(ST_GeomFromText('GEOMETRYCOLLECTION()')));
ST_AsText(ST_ConvexHull(ST_GeomFromText('GEOMETRYCOLLECTION()')))
NULL
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('GEOMETRYCOLLECTION()')));
ST_AsText(ST_Envelope(ST_GeomFromText('GEOMETRYCOLLECTION()')))
GEOMETRYCOLLECTION EMPTY
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION()')));
ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION()')))
NULL
SELECT ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION()'),ST_GeomFromText('GEOMETRYCOLLECTION()'));
ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION()'),ST_GeomFromText('GEOMETRYCOLLECTION()'))
NULL
SELECT ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION()'),ST_GeomFromText('POINT(10 10)'));
ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION()'),ST_GeomFromText('POINT(10 10)'))
NULL
SELECT ST_Distance(ST_GeomFromText('POINT(10 10)'),ST_GeomFromText('GEOMETRYCOLLECTION()'));
ST_Distance(ST_GeomFromText('POINT(10 10)'),ST_GeomFromText('GEOMETRYCOLLECTION()'))
NULL
Select ST_Area(ST_PolygonFromText('POLYGON((0 0, 30 30, 30 0, 0 5, 0 0, 30 5, 30 0, 0 10, 0 0, 30 10, 30 0, 0 0))')) as Result;
Result
450
Select ST_Area(ST_PolygonFromText('POLYGON((1 1, 10 1, 1 0, 10 0, 1 -1, 10 -1, 7 2, 7 -2, 4 2, 4 -2, 1 1))')) as Result;
Result
0
Select ST_AsText(ST_Centroid(ST_PolyFromText('POLYGON((1 1, 10 1, 10 20, 1 20, 1 1), (-1 -1, -10 -1, -10 -15, -1 -15, -1 -1))'))) as Result;
Result
POINT(36.3 62.3)
Select ST_AsText(ST_Centroid(ST_MultiPolygonFromText('MULTIPOLYGON(((1 1, 10 1, 10 20, 1 20, 1 1),(-1 -1, -10 -1, -10 -15, -1 -15, -1 -1)))'))) as Result;
Result
POINT(36.3 62.3)
Select ST_AsText(ST_Centroid(ST_PolyFromText('POLYGON((1 1, 10 1, 10 20, 1 20, 1 1), (5 5))'))) as Result;
ERROR 22023: Invalid GIS data provided to function st_polyfromtext.
Select ST_AsText(ST_Centroid(ST_PolyFromText('POLYGON((1 1, 10 1, 10 20, 1 20, 1 1), (1 1))'))) as Result;
ERROR 22023: Invalid GIS data provided to function st_polyfromtext.
Select ST_AsText(ST_Centroid(ST_PolyFromText('POLYGON((1 1, 10 1, 10 20, 1 20, 1 1), (5 5, 6 5, 6 6, 5 6, 5 5))'))) as Result;
Result
POINT(5.5 10.529411764705882)
Select ST_AsText(ST_Centroid(ST_PolyFromText('POLYGON((1 0, 2 0, 3 0, 4 0, 5 0, 1 0))'))) as Result;
ERROR 22023: Invalid GIS data provided to function st_centroid.
Select ST_AsText(ST_Centroid(ST_PolyFromText('POLYGON((1 0, 2 0, 3 0, 4 0, 5 0, 1 0), (2 0, 3 0, 2 0))'))) as Result;
ERROR 22023: Invalid GIS data provided to function st_polyfromtext.
Select ST_AsText(ST_Centroid(ST_MultiPolygonFromText('MULTIPOLYGON(((1 1, 2 1, 2 3, 1 3, 1 1),(1 1, 2 1, 2 3, 1 3, 1 1)),((1 1, 2 1, 2 3, 1 3, 1 1)))'))) as Result;
Result
POINT(1.5 2)
Select ST_AsText(ST_Centroid(ST_MultiPolygonFromText('MULTIPOLYGON(((1 1, 2 1, 2 3, 1 3, 1 1),(1 1, 2 1, 2 3, 1 3, 1 1)),((20 20, 30 20, 30 40, 20 40, 20 20)))'))) as Result;
Result
POINT(25 30)
select ST_astext(ST_MPointFromWKB(ST_AsWKB(MultiPoint(Point('0', '0'),Point('-0', '0'), Point('0', '-0'))))) as result;
result
MULTIPOINT((0 0),(-0 0),(0 -0))
select ST_Astext(ST_Envelope(ST_MPointFromWKB(ST_AsWKB(MultiPoint(Point('0', '0'),Point('-0', '0'), Point('0', '-0')))))) as result;
result
POINT(0 0)
select ST_astext(ST_MPointFromWKB(ST_AsWKB(MultiPoint(Point('0', '-0'),Point('-0', '0'), Point('0', '0'))))) as result;
result
MULTIPOINT((0 -0),(-0 0),(0 0))
select ST_Astext(ST_Envelope(ST_MPointFromWKB(ST_AsWKB(MultiPoint(Point('0', '-0'),Point('-0', '0'), Point('0', '0')))))) as result;
result
POINT(0 -0)
#
# Bug #18919820  ST_CONVEXHULL() FUNCTION PRODUCES INVALID POLYGON AS THE RESULT
# Bug #18957010  ST_CONVEXHULL() FUNCTION RETURNS WRONG RESULT
#
Select ST_AsText(ST_ConvexHull(ST_GeomFromText('MULTIPOINT(5 0,25 0,15 10,15 25)')));
ST_AsText(ST_ConvexHull(ST_GeomFromText('MULTIPOINT(5 0,25 0,15 10,15 25)')))
POLYGON((5 0,25 0,15 25,5 0))
Select ST_AsText(ST_ConvexHull(ST_GeomFromText('POLYGON((5 0,15 25,25 0,15 5,5 0))')));
ST_AsText(ST_ConvexHull(ST_GeomFromText('POLYGON((5 0,15 25,25 0,15 5,5 0))')))
POLYGON((5 0,25 0,15 25,5 0))
Select ST_AsText(ST_ConvexHull(ST_GeomFromText('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0)),((4 4,4 6,6 6,6 4,4 4)))')));
ST_AsText(ST_ConvexHull(ST_GeomFromText('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0)),((4 4,4 6,6 6,6 4,4 4)))')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
SELECT ST_AsText(ST_convexhull(ST_GeomFromText('MULTIPOINT(5 -3,0 2,5 7,10 2,10 0,10 -2)')));
ST_AsText(ST_convexhull(ST_GeomFromText('MULTIPOINT(5 -3,0 2,5 7,10 2,10 0,10 -2)')))
POLYGON((0 2,5 -3,10 -2,10 2,5 7,0 2))
SELECT ST_AsText(ST_ConvexHull(ST_GeomFromText('MULTIPOINT(5 0,0 5,5 10,10 5,10 -5)')));
ST_AsText(ST_ConvexHull(ST_GeomFromText('MULTIPOINT(5 0,0 5,5 10,10 5,10 -5)')))
POLYGON((0 5,10 -5,10 5,5 10,0 5))
SELECT ST_AsText(ST_ConvexHull(ST_GeomFromText('MULTIPOINT(2 -5,2 5,8 5,8 2,8 0)')));
ST_AsText(ST_ConvexHull(ST_GeomFromText('MULTIPOINT(2 -5,2 5,8 5,8 2,8 0)')))
POLYGON((2 -5,8 0,8 5,2 5,2 -5))
SELECT ST_AsText(st_ConvexHull(ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0)),POLYGON((0 0,10 0,10 -10,0 -10,0 0)))')));
ST_AsText(st_ConvexHull(ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0)),POLYGON((0 0,10 0,10 -10,0 -10,0 0)))')))
POLYGON((0 -10,10 -10,10 10,0 10,0 -10))
SELECT ST_AsText(st_ConvexHull(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10,10 0,0 0),LINESTRING(0 0,10 0,10 -10,0 -10,0 0))')));
ST_AsText(st_ConvexHull(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10,10 0,0 0),LINESTRING(0 0,10 0,10 -10,0 -10,0 0))')))
POLYGON((0 -10,10 -10,10 10,0 10,0 -10))
SELECT ST_AsText(ST_ConvexHull(ST_GeomFromText('MULTILINESTRING((4 7,1 0,1 7),(4 9,8 6,9 4))')));
ST_AsText(ST_ConvexHull(ST_GeomFromText('MULTILINESTRING((4 7,1 0,1 7),(4 9,8 6,9 4))')))
POLYGON((1 0,9 4,8 6,4 9,1 7,1 0))
SELECT ST_AsText(ST_ConvexHull(ST_GeomFromText('POLYGON((5 2,5 5,2 8,5 2))')));
ST_AsText(ST_ConvexHull(ST_GeomFromText('POLYGON((5 2,5 5,2 8,5 2))')))
POLYGON((2 8,5 2,5 5,2 8))
SELECT ST_AsText(st_Convexhull(ST_GeomFromText('multipoint(0 0, 3 0, 3 3, 0 3, 0 0, 1 1, 2 1, 2 2, 1 2, 1 1)')));
ST_AsText(st_Convexhull(ST_GeomFromText('multipoint(0 0, 3 0, 3 3, 0 3, 0 0, 1 1, 2 1, 2 2, 1 2, 1 1)')))
POLYGON((0 0,3 0,3 3,0 3,0 0))
SELECT ST_AsText(st_Convexhull(ST_GeomFromText('multipolygon(((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)), ((0 0, 1 0, 1 1, 0 1, 0 0)))')));
ST_AsText(st_Convexhull(ST_GeomFromText('multipolygon(((0 0, 3 0, 3 3, 0 3, 0 0), (1 1, 2 1, 2 2, 1 2, 1 1)), ((0 0, 1 0, 1 1, 0 1, 0 0)))')))
POLYGON((0 0,3 0,3 3,0 3,0 0))
#
# Bug #18972363   ST_CENTROID() FUNCTION RETURNS INCORRECT RESULT
#
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10,10 0),LINESTRING(0 0,10 0,10 -10,0 -10))'))) as ST_centroid;
ST_centroid
POINT(5.833333333333333 0.8333333333333334)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('MULTILINESTRING((0 0,0 10,10 10,10 0),(0 0,10 0,10 -10,0 -10))'))) as ST_centroid;
ST_centroid
POINT(5.833333333333333 0.8333333333333334)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,10 0,10 10,0 10),LINESTRING(0 0,10 0,10 -10,0 -10))'))) as ST_centroid;
ST_centroid
POINT(6.666666666666667 0)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('MULTILINESTRING((0 0,10 0,10 10,0 10),(0 0,10 0,10 -10,0 -10))'))) as ST_centroid;
ST_centroid
POINT(6.666666666666667 0)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0)),POLYGON((0 0,10 0,10 -10,0 -10,0 0)))'))) as ST_centroid;
ST_centroid
POINT(5 0)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0)),((0 0,10 0,10 -10,0 -10,0 0)))'))) as ST_centroid;
ST_centroid
POINT(5 0)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10,10 0,0 0),LINESTRING(0 0,10 0,10 -10,0 -10,0 0))'))) as ST_centroid;
ST_centroid
POINT(5 0)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('MULTILINESTRING((0 0,0 10,10 10,10 0,0 0),(0 0,10 0,10 -10,0 -10,0 0))'))) as ST_centroid;
ST_centroid
POINT(5 0)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(multipoint(0 0, 1 1, 2 2), LINESTRING(5 5, 10 10), point(-1 -1),POLYGON((0 0,0 10,10 10,10 0,0 0)),point(3 8), LINESTRING(0 1, 1 0, 2 2), MULTILINESTRING((3 3, 0 3, -3 0), (0 8, 0 0, 8 0)), point( 8 3), POLYGON((0 0,10 0,10 -10,0 -10,0 0)), MULTILINESTRING((4 4, 8 8, 8 4), (0 3, 3 0, 0 -3)), point(9 9), multipoint(10 10, 20 20))'))) as ST_centroid;
ST_centroid
POINT(5 0)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 1, 1 0, 2 2), point(3 8), point( 8 3), MULTILINESTRING((3 3, 0 3, -3 0), (0 8, 0 0, 8 0)), LINESTRING(5 5, 10 10), multipoint(0 0, 1 1, 2 2), point(-1 -1), MULTILINESTRING((4 4, 8 8, 8 4), (0 3, 3 0, 0 -3)), point(9 9), multipoint(10 10, 20 20))'))) as ST_centroid;
ST_centroid
POINT(3.1838733307161555 3.09524586263968)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(point(3 8), point( 8 3), multipoint(0 0, 1 1, 2 2), point(-1 -1), point(9 9), multipoint(10 10, 20 20))'))) as ST_centroid;
ST_centroid
POINT(5.777777777777778 5.777777777777778)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,-10 10,-10 0,0 0)),POLYGON((0 0,0 10,10 10,10 0,0 0)))')));
ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,-10 10,-10 0,0 0)),POLYGON((0 0,0 10,10 10,10 0,0 0)))')))
POINT(0 5)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0)),POLYGON((0 0,0 -10,10 -10,10 0,0 0)))')));
ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0)),POLYGON((0 0,0 -10,10 -10,10 0,0 0)))')))
POINT(5 0)
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0)),POLYGON((0 0,10 0,10 -10,0 -10,0 0)))')));
ST_AsText(ST_Centroid(ST_GeomFromText('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0)),POLYGON((0 0,10 0,10 -10,0 -10,0 0)))')))
POINT(5 0)
#
# BUG #18911119 ASSERTION FAILED: !NO_DATA(NBYTES)
#
select (ST_aswkb(cast(st_union(multipoint(
point(8,6), point(1,-17679),
point(-9,-9)),
linestring(point(91,12), point(-77,49),
point(53,-81)))as binary(40))))
in  ('1','2');
ERROR 22023: Invalid GIS data provided to function st_aswkb.
#
# Bug #19204199  DISTANCE() AND ST_ENVELOPE() FUNCTIONS RETURN WRONG RESULT
#
SELECT ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20),GEOMETRYCOLLECTION())'),ST_GeomFromText('LINESTRING(5 0,10 0)'));
ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20),GEOMETRYCOLLECTION())'),ST_GeomFromText('LINESTRING(5 0,10 0)'))
3.5355339059327378
SELECT ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20),GEOMETRYCOLLECTION())'),ST_GeomFromText('multipoint(5 0,10 0)'));
ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20),GEOMETRYCOLLECTION())'),ST_GeomFromText('multipoint(5 0,10 0)'))
3.5355339059327378
SELECT ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20))'),ST_GeomFromText('LINESTRING(5 0,10 0)'));
ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20))'),ST_GeomFromText('LINESTRING(5 0,10 0)'))
3.5355339059327378
SELECT ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GeomFromText('GEOMETRYCOLLECTION()'));
ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GeomFromText('GEOMETRYCOLLECTION()'))
NULL
SELECT ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20),GEOMETRYCOLLECTION(point(2 4), GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION()))))'),ST_GeomFromText('GEOMETRYCOLLECTION(multipoint(5 0,10 0), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION()))'));
ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20),GEOMETRYCOLLECTION(point(2 4), GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION()))))'),ST_GeomFromText('
3.5355339059327378
SELECT ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20),GEOMETRYCOLLECTION())'),ST_GeomFromText('LINESTRING(5 0,10 0)'));
ST_Distance(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,10 10,20 20),GEOMETRYCOLLECTION())'),ST_GeomFromText('LINESTRING(5 0,10 0)'))
3.5355339059327378
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10),GEOMETRYCOLLECTION())')));
ST_AsText(ST_Envelope(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10),GEOMETRYCOLLECTION())')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10),GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))')));
ST_AsText(ST_Envelope(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10),GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10),GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), point(4 4), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))')));
ST_AsText(ST_Envelope(ST_GeomFromText('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10,10 10),GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), point(4 4), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('GEOMETRYCOLLECTION()')));
ST_AsText(ST_Envelope(ST_GeomFromText('GEOMETRYCOLLECTION()')))
GEOMETRYCOLLECTION EMPTY
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))')));
ST_AsText(ST_Envelope(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION(), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))')))
GEOMETRYCOLLECTION EMPTY
SELECT ST_intersects(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GeomFromText('GEOMETRYCOLLECTION()'));
ST_intersects(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GeomFromText('GEOMETRYCOLLECTION()'))
NULL
SELECT ST_disjoint(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GeomFromText('GEOMETRYCOLLECTION()'));
ST_disjoint(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GeomFromText('GEOMETRYCOLLECTION()'))
NULL
#
# Bug #19133043  REPRESENTATION OF THE SAME POINT GEOMETRY ISSUE WITH SPATIAL ANALYSIS FUNCTIONS
#
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('POINT(-0 0)')));
ST_AsText(ST_Envelope(ST_GeomFromText('POINT(-0 0)')))
POINT(-0 0)
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('POINT(0 -0)')));
ST_AsText(ST_Envelope(ST_GeomFromText('POINT(0 -0)')))
POINT(0 -0)
SELECT ST_AsText(ST_ConvexHull(ST_GeomFromText('POINT(0 -0)')));
ST_AsText(ST_ConvexHull(ST_GeomFromText('POINT(0 -0)')))
POINT(0 -0)
SELECT ST_AsText(ST_ConvexHull(ST_GeomFromText('POINT(-0 0)')));
ST_AsText(ST_ConvexHull(ST_GeomFromText('POINT(-0 0)')))
POINT(-0 0)
SELECT ST_AsText(ST_ConvexHull(ST_GeomFromText('POINT(0 -0)'))) =ST_AsText(ST_ConvexHull(ST_GeomFromText('POINT(-0 0)'))) ;
ST_AsText(ST_ConvexHull(ST_GeomFromText('POINT(0 -0)'))) =ST_AsText(ST_ConvexHull(ST_GeomFromText('POINT(-0 0)')))
0
#
# Bug #19142227   ST_ENVELOPE() SPATIAL ANALYSIS FUNCTION ACCEPTS INVALID POLYGONS AS THE ARGUMENT
#
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('POLYGON((0 0))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('POLYGON((0 0,5 5))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('POLYGON((0 0,5 5,10 10, 0 0))')));
ST_AsText(ST_Envelope(ST_GeomFromText('POLYGON((0 0,5 5,10 10, 0 0))')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('MULTIPOLYGON(((0 0,5 5)),((1 1,1 1,1 1,1 1)))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
#
# Bug #19304320 ST_CENTROID() AND ST_CONVEXHULL() FUNCTIONS RETURN NULL WITH VALID GEOMETRY INPUT
#
Select st_astext(st_centroid(ST_GeomFromText('geometrycollection(polygon((0 0,0 10,10 10,10 0,0 0)))')));
st_astext(st_centroid(ST_GeomFromText('geometrycollection(polygon((0 0,0 10,10 10,10 0,0 0)))')))
POINT(5 5)
Select st_astext(st_centroid(ST_GeomFromText('geometrycollection(geometrycollection(),polygon((0 0,0 10,10 10,10 0,0 0)))')));
st_astext(st_centroid(ST_GeomFromText('geometrycollection(geometrycollection(),polygon((0 0,0 10,10 10,10 0,0 0)))')))
POINT(5 5)
Select st_astext(st_convexhull(ST_GeomFromText('geometrycollection(polygon((0 0,0 10,10 10,10 0,0 0)))')));
st_astext(st_convexhull(ST_GeomFromText('geometrycollection(polygon((0 0,0 10,10 10,10 0,0 0)))')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
Select st_astext(st_convexhull(ST_GeomFromText('geometrycollection(geometrycollection(),polygon((0 0,0 10,10 10,10 0,0 0)))')));
st_astext(st_convexhull(ST_GeomFromText('geometrycollection(geometrycollection(),polygon((0 0,0 10,10 10,10 0,0 0)))')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
select st_distance(ST_GeomFromText('geometrycollection(geometrycollection(),polygon((0 0,0 10,10 10,10 0,0 0)))'),ST_GeomFromText('linestring(0 0,10 10)'));
st_distance(ST_GeomFromText('geometrycollection(geometrycollection(),polygon((0 0,0 10,10 10,10 0,0 0)))'),ST_GeomFromText('linestring(0 0,10 10)'))
0
select st_distance(ST_GeomFromText('geometrycollection(geometrycollection(),polygon((0 0,0 10,10 10,10 0,0 0)))'),ST_GeomFromText('point(100 100)'));
st_distance(ST_GeomFromText('geometrycollection(geometrycollection(),polygon((0 0,0 10,10 10,10 0,0 0)))'),ST_GeomFromText('point(100 100)'))
127.27922061357856
Select st_distance(ST_GeomFromText('geometrycollection(polygon((0 0,0 10,10 10,10 0,0 0)))'),ST_GeomFromText('point(100 100)'));
st_distance(ST_GeomFromText('geometrycollection(polygon((0 0,0 10,10 10,10 0,0 0)))'),ST_GeomFromText('point(100 100)'))
127.27922061357856
Select st_distance(ST_GeomFromText('geometrycollection(polygon((0 0,0 10,10 10,10 0,0 0)))'),ST_GeomFromText('linestring(0 0,10 10)'));
st_distance(ST_GeomFromText('geometrycollection(polygon((0 0,0 10,10 10,10 0,0 0)))'),ST_GeomFromText('linestring(0 0,10 10)'))
0
#
# Bug #19351967  ST_CENTROID() AND COVEXHULL() ACCEPTS GEOMETRYCOLLECTION CONTAINING INVALID POLYGON
#
SELECT ST_AsText(ST_ConvexHull(ST_GeomFromText('GeometryCollection(POLYGON((0 0)))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_Centroid(ST_GeomFromText('GeometryCollection(POLYGON((0 0)))')));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_DISTANCE(ST_GeomFromText('POLYGON((0 0, 1 1))'), ST_GeomFromText('POLYGON((1 0, 0 1))'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
select st_astext(st_makeenvelope(st_geomfromtext('point(0 0)'), st_geomfromtext('point(2 2)')));
st_astext(st_makeenvelope(st_geomfromtext('point(0 0)'), st_geomfromtext('point(2 2)')))
POLYGON((0 0,2 0,2 2,0 2,0 0))
select st_astext(st_makeenvelope(st_geomfromtext('point(0 0)'), st_geomfromtext('point(-22 -11)')));
st_astext(st_makeenvelope(st_geomfromtext('point(0 0)'), st_geomfromtext('point(-22 -11)')))
POLYGON((-22 -11,0 -11,0 0,-22 0,-22 -11))
select st_distance_sphere(st_geomfromtext('point(-120 45)'), st_geomfromtext('point(30.24 68.37)'));
st_distance_sphere(st_geomfromtext('point(-120 45)'), st_geomfromtext('point(30.24 68.37)'))
7168298.1969
#
# Bug #19552241   MBRTOUCHES() FUNCTION RETURNS INCORRECT RESULT
#
SELECT 1, MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'),ST_GEOMFROMTEXT('MULTIPOINT(5 10,5 15)'));
1	MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'),ST_GEOMFROMTEXT('MULTIPOINT(5 10,5 15)'))
1	1
SELECT MBRTOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(5 0,5 10),GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('LINESTRING(5 0,5 10)'));
MBRTOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(5 0,5 10),GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('LINESTRING(5 0,5 10)'))
0
SELECT MBRTOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(5 0,5 10),GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'));
MBRTOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOINT(5 0,5 10),GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'))
0
SELECT MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'),ST_GEOMFROMTEXT('MULTIPOINT(5 1,5 11)'));
MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'),ST_GEOMFROMTEXT('MULTIPOINT(5 1,5 11)'))
0
SELECT 1, MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'),ST_GEOMFROMTEXT('MULTIPOINT(5 10,5 15)'));
1	MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'),ST_GEOMFROMTEXT('MULTIPOINT(5 10,5 15)'))
1	1
SELECT MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'),ST_GEOMFROMTEXT('MULTIPOINT(5 11,5 15)'));
MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'),ST_GEOMFROMTEXT('MULTIPOINT(5 11,5 15)'))
0
SELECT MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(3 0,15 0)'));
MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(3 0,15 0)'))
0
SELECT 1, MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(3 0,5 0)'));
1	MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(3 0,5 0)'))
1	1
SELECT MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(3 0,4 0)'));
MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(3 0,4 0)'))
0
SELECT 1, MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'),ST_GEOMFROMTEXT('MULTIPOINT(5 3,15 3)'));
1	MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'),ST_GEOMFROMTEXT('MULTIPOINT(5 3,15 3)'))
1	1
SELECT MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'),ST_GEOMFROMTEXT('MULTIPOINT(0 3,15 3)'));
MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,5 10)'),ST_GEOMFROMTEXT('MULTIPOINT(0 3,15 3)'))
0
SELECT 1, MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(6 0,6 10)'));
1	MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(6 0,6 10)'))
1	1
SELECT MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(6 -5,6 10)'));
MBRTOUCHES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 0)'),ST_GEOMFROMTEXT('MULTIPOINT(6 -5,6 10)'))
0
#
# Bug #19784515  ASSERTION `DIM1 >= 0 && DIM1 <= 2 && DIM2 >= 0 && DIM2 <= 2' FAILED
#
# Invalid geometry in input
set @centroid_point = ST_CENTROID(ST_UNION(ST_UNION(ST_GEOMFROMTEXT('MULTILINESTRING((-556 966,-721 -210),(-202 390,-954 804,682 504,-394 -254,832 371,907 -369,827 126,-567 -337,-304 -555,-957 -483,-660 792),(-965 -940,814 -804,-477 -909,-128 57,-819 880,761 497,-559 40,-431 427,179 -291,-707 315,137 -781,-416 -371,-5 -156),(-600 -570,-481 -191,991 -361,768 888,-647 566,795 -861,-82 -575,-593 539))'), ST_GEOMFROMTEXT('MULTIPOLYGON(((805 69,249 708,147 455,546 -672,-218 843,458 24,-630 -420,-83 -69, 805 69)),((196 -219,-201 663,-867 521,-910 -315,-749 801,-402 820,-167 -817,-526 -163,744 -988,-588 -370,573 695,-597 513,-246 439, 196 -219)),((32 -903,189 -871,-778 -741,784 340,403 -555,607 -540,-513 -982,700 -124,344 732,714 151,-812 -252,-440 -895,-426 231,-819 -357, 32 -903)),((-395 830,454 -143,788 -279,618 -843,-490 -507,-224 17, -395 830)))')), ST_INTERSECTION(ST_UNION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(-169 -570),MULTIPOINT(384 290,-601 123,408 86,-616 -300,160 -474,-979 -4,-63 -824,-689 -765,-219 802,-54 -93,191 -982,-723 -449),MULTILINESTRING((683 4,864 -634,548 -891,727 -691,-570 32,-334 -438,127 -317,241 -12,-807 947,-987 693,-345 -867,854 -106)),MULTIPOINT(384 290,-601 123,408 86,-616 -300,160 -474,-979 -4,-63 -824,-689 -765,-219 802,-54 -93,191 -982,-723 -449),MULTIPOLYGON(((266 51,851 523,-781 366,-607 -581, 266 51)),((416 -450,-973 880,103 226,-896 -857,-369 761, 416 -450)),((168 171,26 -99,-606 -490,-174 -138,-325 -218,-833 -652,-255 -445,-882 -762,-202 -560, 168 171)),((-423 -216,-531 -190,-147 821,362 441,645 -128,-997 708,134 -426,714 -9,147 842,-887 -870,688 -330,689 17,-314 -262,401 -112,-606 761, -423 -216)),((-582 -373,-360 -84,-727 -171,412 -660,750 -846,-464 718,163 -11,489 -659,586 -324,-741 -198,144 -165,644 -80,930 -487,-504 -205, -582 -373))),MULTIPOLYGON(((266 51,851 523,-781 366,-607 -581, 266 51)),((416 -450,-973 880,103 226,-896 -857,-369 761, 416 -450)),((168 171,26 -99,-606 -490,-174 -138,-325 -218,-833 -652,-255 -445,-882 -762,-202 -560, 168 171)),((-423 -216,-531 -190,-147 821,362 441,645 -128,-997 708,134 -426,714 -9,147 842,-887 -870,688 -330,689 17,-314 -262,401 -112,-606 761, -423 -216)),((-582 -373,-360 -84,-727 -171,412 -660,750 -846,-464 718,163 -11,489 -659,586 -324,-741 -198,144 -165,644 -80,930 -487,-504 -205, -582 -373))),GEOMETRYCOLLECTION(),MULTIPOINT(384 290,-601 123,408 86,-616 -300,160 -474,-979 -4,-63 -824,-689 -765,-219 802,-54 -93,191 -982,-723 -449),MULTILINESTRING((683 4,864 -634,548 -891,727 -691,-570 32,-334 -438,127 -317,241 -12,-807 947,-987 693,-345 -867,854 -106)))'), ST_GEOMFROMTEXT('MULTIPOINT(157 69,-725 -189,-176 -41,676 375,33 -672,-76 47)')), ST_UNION(ST_ENVELOPE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(-896 100,-793 810,243 -525,650 -373,599 170,-554 -890),POINT(945 -828),POINT(945 -828),LINESTRING(-896 100,-793 810,243 -525,650 -373,599 170,-554 -890),POINT(945 -828),MULTIPOINT(-47 307,-768 -425,-3 167,-170 30,-784 721,951 146,407 790,37 850,-466 738),GEOMETRYCOLLECTION(),MULTIPOINT(-47 307,-768 -425,-3 167,-170 30,-784 721,951 146,407 790,37 850,-466 738),MULTIPOLYGON(((104 113,688 423,-859 602,272 978, 104 113)),((981 -394,189 -400,649 -325,-977 371,30 859,590 318,329 -894,-51 262,197 952,-846 -139,-920 399, 981 -394)),((-236 -759,834 757,857 747,437 -146,194 913,316 862,976 -491,-745 933,610 687,-149 -164,-803 -565,451 -275, -236 -759)),((572 96,-160 -607,529 930,-544 -132,458 294, 572 96))))')), ST_CENTROID(ST_GEOMFROMTEXT('POINT(-939 -921)'))))));
DO ST_AsText(@centroid_point) as centroid;
DO MBRWITHIN(@centroid_point, ST_INTERSECTION(ST_GEOMFROMTEXT('MULTILINESTRING((541 -927,-414 316,-429 -444,212 260,-125 104,445 563,-713 -975,-976 514),(-830 882,-377 914,-915 919,-535 -23,-508 979),(806 347,-87 220,226 -22,-12 468,707 598,83 951,-592 701,833 964,270 -932,743 -514,231 469,-575 -122,-99 -245,416 465,801 -587))'), ST_GEOMFROMTEXT('LINESTRING(-96 -182,-373 75,697 687,-881 -463,-557 -959,-493 810)'))) as result;
# 
# Bug #19566186  ST_Length RETURNS NON-INF VALUE ON 32BIT PLATFORMS FOR LINESTRING OF INF LENGTH
#
select ST_Length(ST_MLineFromWKB(0x0000000005000000020000000002000000035FB317E5EF3AB327E3A4B378469B67320000000000000000C0240000000000003FF05FD8ADAB9F560000000000000000000000000200000003000000000000000000000000000000000000000000000000BFF08B439581062540240000000000004341C37937E08000)) as length;
ERROR 22023: Invalid GIS data provided to function st_mlinefromwkb.
#
# Bug #19584716  ST_DISTANCE: ASSERTION FAILED: DIST <= 1.7976931348623158E+308
#
do st_distance(linestring(point(26,87),point(13,95)),
geometrycollection(point(4.297374e+307,8.433875e+307)));
ERROR 22023: Invalid GIS data provided to function st_distance.
select st_distance(linestring(point(26,87),point(13,95)),
geometrycollection(point(4.297374e+307,8.433875e+307))) as dist;
ERROR 22023: Invalid GIS data provided to function st_distance.
select st_distance(linestring(point(26,87),point(13,95)),
geometrycollection(point(4.297374e+307,8.433875e+307), point(1e308, 1e308))) as dist;
ERROR 22023: Invalid GIS data provided to function st_distance.
#
# Bug #18304448  ST_TOUCHES: CRASH IN GCALC_HEAP::NEW_POINT_INFO
#
drop table if exists t1;
Warnings:
Note	1051	Unknown table 'test.t1'
create table t1(a int)engine=innodb;
delete from t1 where
st_touches(
linestring(point(4294967224,4294967212),
point(-4398046511107,-4611686018427387904),
point(4294967226,4294967293),
point(4294967273,47)
),
multilinestring(linestring(point(1,2),point(1,2)),
linestring(point(1,2),point(4294967270,4294967270))
)
);
drop table if exists t1;
#
# Bug #17894858  ST_INTERSECTS WORKS IMPROPERLY
#
select ST_GeomFromText("POLYGON((0 0, 0 10, 10 10, 10 0, 0 0))") into @a;
select ST_GeomFromText('linestring(-2 -2, 12 7)') into @l;
select st_intersects(@a, @l);
st_intersects(@a, @l)
1
select ST_GeomFromText('linestring(5 5, 15 4)') into @l;
select st_intersects(@a, @l);
st_intersects(@a, @l)
1
select ST_GeomFromText('linestring(7 6, 15 4)') into @l;
select st_intersects(@a, @l);
st_intersects(@a, @l)
1
select ST_GeomFromText('linestring(6 2, 12 1)') into @l;
select st_intersects(@a, @l);
st_intersects(@a, @l)
1
#
# Bug #16174580  ST_OVERLAPS AND ST_INTERSECTS GIVE ERRONEOUS RESULTS
#
drop table if exists tbl_polygon;
Warnings:
Note	1051	Unknown table 'test.tbl_polygon'
create table tbl_polygon(id varchar(32), geom POLYGON);
insert into tbl_polygon (id, geom) values
('POLY1',ST_GeomFromText('POLYGON((0 0,0 10,10 10,10 0,0 0))'));
insert into tbl_polygon (id, geom) values
('POLY2',ST_GeomFromText('POLYGON((0 0,0 -10,10 -10,10 0,0 0))'));
select 100, st_area(t.geom) from tbl_polygon t
where t.id like 'POLY%';
100	st_area(t.geom)
100	100
100	100
select 1, ST_touches(t.geom, p.geom)
from tbl_polygon t, tbl_polygon p
where t.id = 'POLY1' and p.id = 'POLY2';
1	ST_touches(t.geom, p.geom)
1	1
select 1, st_intersects(t.geom, p.geom)
from tbl_polygon t, tbl_polygon p
where t.id = 'POLY1' and p.id = 'POLY2';
1	st_intersects(t.geom, p.geom)
1	1
select 0, st_overlaps(t.geom, p.geom)
from tbl_polygon t, tbl_polygon p
where t.id = 'POLY1' and p.id = 'POLY2';
0	st_overlaps(t.geom, p.geom)
0	0
select 1, st_touches(t.geom, p.geom)
from tbl_polygon t, tbl_polygon p
where t.id = 'POLY1' and p.id = 'POLY2';
1	st_touches(t.geom, p.geom)
1	1
select 1, st_intersects(t.geom, p.geom)
from tbl_polygon t, tbl_polygon p
where t.id = 'POLY1' and p.id = 'POLY2';
1	st_intersects(t.geom, p.geom)
1	1
select 0, st_overlaps(t.geom, p.geom)
from tbl_polygon t, tbl_polygon p
where t.id = 'POLY1' and p.id = 'POLY2';
0	st_overlaps(t.geom, p.geom)
0	0
drop table if exists tbl_polygon;
#
# Bug #20106767  CRASH WITH ST_ISVALID() FUNCTION
#
SELECT
ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTILINESTRING((-7 -2,-9 3,-2 -8),(3 7,5 6,-7 -9,7 -1,-2 -8,2 9,4 6,-5 -5)),
                                               MULTILINESTRING((2 -2,2 -3,2 -1,-10 7,1 -2,-2 0,-9 -2,10 5,-7 -8,-9 -1,1 -1,-2 3,5 -9,-8 -9,-10 -9)),
                                               MULTIPOINT(-7 -5,6 9,7 4))'));
ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTILINESTRING((-7 -2,-9 3,-2 -8),(3 7,5 6,-7 -9,7 -1,-2 -8,2 9,4 6,-5 -5)),
                                               MULTILINESTRING((2 -2,2 -3,2 -1,-10 7,1 -2,-2 0,-9 -2,10 5,-7 -8,-9 -1,1 -1,-2 3,5 
1
#
# Bug #20112849  ST_ISVALID() FUNCTION RETURNS 'FALSE' WITH VALID POLYGON/MULTIPOLYGON INPUT
#
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'));
ST_ISVALID(ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4))'));
ST_ISVALID(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)))'));
ST_ISVALID(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 5,5 5,5 0,0 0)))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0)),((14 14,14 16,16 16,16 14,14 14)))'));
ST_ISVALID(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,0 0)),((14 14,14 16,16 16,16 14,14 14)))'))
1
#
# Bug#20112707 ST_VALIDATE() DOES NOT RETURN ANY VALUE WITH VALID GEOMETRY INPUT
#
SELECT st_astext(ST_VALIDATE(ST_GEOMFROMTEXT('POINT(0 0)')));
st_astext(ST_VALIDATE(ST_GEOMFROMTEXT('POINT(0 0)')))
POINT(0 0)
SELECT st_astext(ST_VALIDATE(ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)')));
st_astext(ST_VALIDATE(ST_GEOMFROMTEXT('LINESTRING(0 0,10 10)')))
LINESTRING(0 0,10 10)
SELECT st_astext(ST_VALIDATE(ST_GEOMFROMTEXT('POLYGON((0 0, 10 0, 10 10, 0 10, 0 0))')));
st_astext(ST_VALIDATE(ST_GEOMFROMTEXT('POLYGON((0 0, 10 0, 10 10, 0 10, 0 0))')))
POLYGON((0 0,10 0,10 10,0 10,0 0))
#
# Bug#20119431 ST_ISVALID() FUNCTION RETURNS 'FALSE' WITH VALID GEOMETRYCOLLECTION INPUT
#
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0, 0 0))'));
ST_ISVALID(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0, 0 0))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('LINESTRING(0 0,0 10)'));
ST_ISVALID(ST_GEOMFROMTEXT('LINESTRING(0 0,0 10)'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POINT(0 0)'));
ST_ISVALID(ST_GEOMFROMTEXT('POINT(0 0)'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0, 0 0)))'));
ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0, 0 0)))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10))'));
ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0,0 10))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'));
ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0, 0 0))))'));
ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0, 0 0))))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(LINESTRING(0 0,0 10)))'));
ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(LINESTRING(0 0,0 10)))'))
1
SELECT ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(0 0)))'));
ST_ISVALID(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(0 0)))'))
1
#
# Bug #20135389  ST_MAKEENVELOPE() RETURNS SAME SET OF SRID VALUES IN THE ERROR MESSAGE
#
SELECT ST_ASTEXT(ST_MAKEENVELOPE(ST_GEOMFROMTEXT('POINT(0 1)', 4236),
ST_GEOMFROMTEXT('POINT(0 0)', 0)));
ERROR HY000: Binary geometry function st_makeenvelope given two geometries of different srids: 4236 and 0, which should have been identical.
SELECT ST_ASTEXT(ST_MAKEENVELOPE(ST_GEOMFROMTEXT('POINT(0 1)', 4145),
ST_GEOMFROMTEXT('POINT(0 0)', 0)));
ERROR HY000: Binary geometry function st_makeenvelope given two geometries of different srids: 4145 and 0, which should have been identical.
SELECT ST_ASTEXT(ST_MAKEENVELOPE(ST_GEOMFROMTEXT('POINT(0 1)', 0),
ST_GEOMFROMTEXT('POINT(0 0)', 2000)));
ERROR HY000: Binary geometry function st_makeenvelope given two geometries of different srids: 0 and 2000, which should have been identical.
#
# Bug #20085563  ASSERTION `!NULL_VALUE && OPDONE && STR_VALUE_ARG->LENGTH() > 0' FAILED
#
SELECT
ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))'))) as geom;
geom
GEOMETRYCOLLECTION EMPTY
SELECT
ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))'))) as geom;
geom
GEOMETRYCOLLECTION EMPTY
SELECT
ST_ASTEXT(ST_DIFFERENCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))'))) as geom;
geom
GEOMETRYCOLLECTION EMPTY
SELECT
ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))'))) as geom;
geom
GEOMETRYCOLLECTION EMPTY
SELECT
ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))'))) as geom;
geom
GEOMETRYCOLLECTION EMPTY
SELECT
ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))'))) as geom;
geom
POINT(1 1)
SELECT
ST_ASTEXT(ST_DIFFERENCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))'))) as geom;
geom
GEOMETRYCOLLECTION EMPTY
SELECT
ST_ASTEXT(ST_DIFFERENCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))'))) as geom;
geom
POINT(1 1)
SELECT
ST_ASTEXT(ST_DIFFERENCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))'))) as geom;
geom
GEOMETRYCOLLECTION EMPTY
SELECT
ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))'))) as geom;
geom
POINT(1 1)
SELECT
ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))'))) as geom;
geom
POINT(1 1)
SELECT
ST_ASTEXT(ST_SYMDIFFERENCE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(1 1), GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())))'))) as geom;
geom
GEOMETRYCOLLECTION EMPTY
#
# Bug#20111542 SET OPERATIONS RETURN GEOMETRYCOLLECTION CONTAINING SINGLE GEOMETRY COMPONENT
#
SELECT ST_AsText(ST_Union(ST_GeomFromText('MULTIPOINT(0 0,100 100)'),
ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),GEOMETRYCOLLECTION())'))) as result;
result
MULTIPOINT((0 0),(100 100))
SELECT ST_AsText(ST_Difference(ST_GeomFromText('MULTIPOLYGON(((4 4,4 6,6 6,6 4, 4 4)),((0 0,0 10,10 10,10 0, 0 0)))'),
ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION()))'))) as result;
result
MULTIPOLYGON(((4 4,6 4,6 6,4 6,4 4)),((0 0,10 0,10 10,0 10,0 0)))
SELECT ST_AsText(ST_Difference(ST_GeomFromText('MULTILinestring((4 4,4 6,6 6,6 4),(0 0,0 10,10 10,10 0))'),
ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(GEOMETRYCOLLECTION()))'))) as result;
result
MULTILINESTRING((4 4,4 6,6 6,6 4),(0 0,0 10,10 10,10 0))
SELECT ST_AsText(ST_Union(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(MULTIPOINT(0 0,100 100), MULTIPOINT(1 1, 2 2)))'),
ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),GEOMETRYCOLLECTION())'))) as result;
result
MULTIPOINT((0 0),(100 100),(1 1),(2 2))
SELECT ST_AsText(ST_Union(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(linestring(0 0,100 100), MULTIPOINT(1 1, 2 2)))'),
ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),GEOMETRYCOLLECTION())'))) as result;
result
LINESTRING(0 0,100 100)
SELECT ST_AsText(ST_Union(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(polygon((0 0,10 0, 10 10, 0 10, 0 0)), MULTIPOINT(1 1, 2 2)))'),
ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),GEOMETRYCOLLECTION())'))) as result;
result
POLYGON((0 0,10 0,10 10,0 10,0 0))
SELECT ST_AsText(ST_Union(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(MULTIPOINT(0 0,100 100), linestring(1 1, 2 2)))'),
ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),GEOMETRYCOLLECTION())'))) as result;
result
GEOMETRYCOLLECTION(LINESTRING(1 1,2 2),POINT(0 0),POINT(100 100))
SELECT ST_AsText(ST_Union(ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(polygon((0 0,10 0, 10 10, 0 10, 0 0)), polygon((0 0, 1 0, 1 1, 0 1, 0 0))))'),
ST_GeomFromText('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(),GEOMETRYCOLLECTION())'))) as result;
result
POLYGON((0 10,0 0,10 0,10 10,0 10))
#
# Bug #20073238  WL7541: MEMORY ISSUE FOUND BY VALGRIND AND MANUAL INSPECTION
#
# Invalid geometry in input
set @centroid_point = ST_CENTROID(ST_UNION(ST_UNION(ST_GEOMFROMTEXT('MULTILINESTRING((-556 966,-721 -210),(-202 390,-954 804,682 504,-394 -254,832 371,907 -369,827 126,-567 -337,-304 -555,-957 -483,-660 792),(-965 -940,814 -804,-477 -909,-128 57,-819 880,761 497,-559 40,-431 427,179 -291,-707 315,137 -781,-416 -371,-5 -156),(-600 -570,-481 -191,991 -361,768 888,-647 566,795 -861,-82 -575,-593 539))'), ST_GEOMFROMTEXT('MULTIPOLYGON(((805 69,249 708,147 455,546 -672,-218 843,458 24,-630 -420,-83 -69, 805 69)),((196 -219,-201 663,-867 521,-910 -315,-749 801,-402 820,-167 -817,-526 -163,744 -988,-588 -370,573 695,-597 513,-246 439, 196 -219)),((32 -903,189 -871,-778 -741,784 340,403 -555,607 -540,-513 -982,700 -124,344 732,714 151,-812 -252,-440 -895,-426 231,-819 -357, 32 -903)),((-395 830,454 -143,788 -279,618 -843,-490 -507,-224 17, -395 830)))')), ST_INTERSECTION(ST_UNION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(-169 -570),MULTIPOINT(384 290,-601 123,408 86,-616 -300,160 -474,-979 -4,-63 -824,-689 -765,-219 802,-54 -93,191 -982,-723 -449),MULTILINESTRING((683 4,864 -634,548 -891,727 -691,-570 32,-334 -438,127 -317,241 -12,-807 947,-987 693,-345 -867,854 -106)),MULTIPOINT(384 290,-601 123,408 86,-616 -300,160 -474,-979 -4,-63 -824,-689 -765,-219 802,-54 -93,191 -982,-723 -449),MULTIPOLYGON(((266 51,851 523,-781 366,-607 -581, 266 51)),((416 -450,-973 880,103 226,-896 -857,-369 761, 416 -450)),((168 171,26 -99,-606 -490,-174 -138,-325 -218,-833 -652,-255 -445,-882 -762,-202 -560, 168 171)),((-423 -216,-531 -190,-147 821,362 441,645 -128,-997 708,134 -426,714 -9,147 842,-887 -870,688 -330,689 17,-314 -262,401 -112,-606 761, -423 -216)),((-582 -373,-360 -84,-727 -171,412 -660,750 -846,-464 718,163 -11,489 -659,586 -324,-741 -198,144 -165,644 -80,930 -487,-504 -205, -582 -373))),MULTIPOLYGON(((266 51,851 523,-781 366,-607 -581, 266 51)),((416 -450,-973 880,103 226,-896 -857,-369 761, 416 -450)),((168 171,26 -99,-606 -490,-174 -138,-325 -218,-833 -652,-255 -445,-882 -762,-202 -560, 168 171)),((-423 -216,-531 -190,-147 821,362 441,645 -128,-997 708,134 -426,714 -9,147 842,-887 -870,688 -330,689 17,-314 -262,401 -112,-606 761, -423 -216)),((-582 -373,-360 -84,-727 -171,412 -660,750 -846,-464 718,163 -11,489 -659,586 -324,-741 -198,144 -165,644 -80,930 -487,-504 -205, -582 -373))),GEOMETRYCOLLECTION(),MULTIPOINT(384 290,-601 123,408 86,-616 -300,160 -474,-979 -4,-63 -824,-689 -765,-219 802,-54 -93,191 -982,-723 -449),MULTILINESTRING((683 4,864 -634,548 -891,727 -691,-570 32,-334 -438,127 -317,241 -12,-807 947,-987 693,-345 -867,854 -106)))'), ST_GEOMFROMTEXT('MULTIPOINT(157 69,-725 -189,-176 -41,676 375,33 -672,-76 47)')), ST_UNION(ST_ENVELOPE(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(-896 100,-793 810,243 -525,650 -373,599 170,-554 -890),POINT(945 -828),POINT(945 -828),LINESTRING(-896 100,-793 810,243 -525,650 -373,599 170,-554 -890),POINT(945 -828),MULTIPOINT(-47 307,-768 -425,-3 167,-170 30,-784 721,951 146,407 790,37 850,-466 738),GEOMETRYCOLLECTION(),MULTIPOINT(-47 307,-768 -425,-3 167,-170 30,-784 721,951 146,407 790,37 850,-466 738),MULTIPOLYGON(((104 113,688 423,-859 602,272 978, 104 113)),((981 -394,189 -400,649 -325,-977 371,30 859,590 318,329 -894,-51 262,197 952,-846 -139,-920 399, 981 -394)),((-236 -759,834 757,857 747,437 -146,194 913,316 862,976 -491,-745 933,610 687,-149 -164,-803 -565,451 -275, -236 -759)),((572 96,-160 -607,529 930,-544 -132,458 294, 572 96))))')), ST_CENTROID(ST_GEOMFROMTEXT('POINT(-939 -921)'))))));
DO ST_AsText(@centroid_point) as centroid;
DO MBRWITHIN(@centroid_point, ST_INTERSECTION(ST_GEOMFROMTEXT('MULTILINESTRING((541 -927,-414 316,-429 -444,212 260,-125 104,445 563,-713 -975,-976 514),(-830 882,-377 914,-915 919,-535 -23,-508 979),(806 347,-87 220,226 -22,-12 468,707 598,83 951,-592 701,833 964,270 -932,743 -514,231 469,-575 -122,-99 -245,416 465,801 -587))'), ST_GEOMFROMTEXT('LINESTRING(-96 -182,-373 75,697 687,-881 -463,-557 -959,-493 810)'))) as result;
#
# Bug #20179082  ST_TOUCHES() RETURNS INCORRECT RESULT WITH GEOMRTRYCOLLECTION GEOMETRY
#
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('POINT(0 0)'),
ST_GEOMFROMTEXT('POINT(0 0)'));
ST_TOUCHES(ST_GEOMFROMTEXT('POINT(0 0)'),
ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('POINT(0 0)'),
ST_GEOMFROMTEXT('POINT(1 1)'));
ST_TOUCHES(ST_GEOMFROMTEXT('POINT(0 0)'),
ST_GEOMFROMTEXT('POINT(1 1)'))
NULL
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('POINT(1 0)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'));
ST_TOUCHES(ST_GEOMFROMTEXT('POINT(1 0)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'))
NULL
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('POINT(0 0)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'));
ST_TOUCHES(ST_GEOMFROMTEXT('POINT(0 0)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'))
NULL
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLlECTION(POINT(0 0))'),
ST_GEOMFROMTEXT('POINT(0 0)'));
ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLlECTION(POINT(0 0))'),
ST_GEOMFROMTEXT('POINT(0 0)'))
NULL
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLlECTION(POINT(0 0))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'));
ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLlECTION(POINT(0 0))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(0 0))'))
NULL
#
# Bug#20188574 ST_WITHIN(MULTIPOINT, POLYGON) RETURNS WRONG RESULT
#
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 5,10 10)'),ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 5,10 10)'),ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'))
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,3 3)'),ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'));
ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(0 0,5 0,3 3)'),ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'))
1
#
# Bug#20170591  ST_CROSSES(MULTIPOINT, LINESRING) RETURNS INCORRECT RESULT
#
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 10)'),ST_GEOMFROMTEXT('POLYGON((0 0,5 0,5 5,0 5, 0 0))'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 10)'),ST_GEOMFROMTEXT('POLYGON((0 0,5 0,5 5,0 5, 0 0))'))
0
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(2 2, 5 0,10 10)'),ST_GEOMFROMTEXT('POLYGON((0 0,5 0,5 5,0 5, 0 0))'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(2 2, 5 0,10 10)'),ST_GEOMFROMTEXT('POLYGON((0 0,5 0,5 5,0 5, 0 0))'))
1
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 10,20 20)'),ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(5 0,10 10,20 20)'),ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'))
0
SELECT ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(2 2, 5 0,10 10,20 20)'),ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'));
ST_CROSSES(ST_GEOMFROMTEXT('MULTIPOINT(2 2, 5 0,10 10,20 20)'),ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'))
1
#
# Bug#20234376 ST_CONTAINS(POLYGON, MULTIPOINT) RETURNS INCORRECT RESULT
#
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(3 3,13 13)'));
ST_CONTAINS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(3 3,13 13)'))
0
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((5 0,0 5,10 5,5 0)),((5 0,0 -5,10 -5,5 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(5 0)'));
ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((5 0,0 5,10 5,5 0)),((5 0,0 -5,10 -5,5 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(5 0)'))
0
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(5 2,15 14)'));
ST_CONTAINS(ST_GEOMFROMTEXT('POLYGON((0 0,0 5,5 5,5 0,0 0))'), ST_GEOMFROMTEXT('MULTIPOINT(5 2,15 14)'))
0
#
# Bug#20187460  OVERLAPPING GEOMETRY COMPONENTS OF SAME TYPE ARE NOT GETTING MERGED PROPERLY
#
SELECT ST_ASTEXT(ST_INTERSECTION(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'), 
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((4 4,4 6,6 6,6 4,4 4)),
                                                                     POLYGON((5 5,5 7,7 7,7 5,5 5)))'))) as result;
result
POLYGON((5 6,4 6,4 4,6 4,6 5,7 5,7 7,5 7,5 6))
#
# Bug#20187460  OVERLAPPING GEOMETRY COMPONENTS OF SAME TYPE ARE NOT GETTING MERGED PROPERLY
#
# Invalid geometry in input
DO ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((4 -10,-3 0,-5 6,9 9,5 7,9 -6,9 -10,7 9,0 -4,-6 10,0 -3,9 3, 4 -10)),
                                                           GEOMETRYCOLLECTION(),
                                                           GEOMETRYCOLLECTION(MULTIPOLYGON(((-8 0,-2 -6,-10 -9,-9 7,-2 -10,7 -9,3 -6,-5 -8,-10 -7,-6 10,4 -10,-7 -8,6 -1, -8 0)),((-4 -3,7 -10,-4 -1,-10 -3,3 -3,-10 -4,-1 2,-2 -10,6 -7,-9 -8, -4 -3))),
                                                                              POINT(5 0),
                                                                              MULTIPOINT(-3 0,-4 -8,-3 -4,10 4,0 7,-7 2,4 -8,1 -6),
                                                                              MULTILINESTRING((-10 10,-10 5,9 -9,2 2,-7 2,0 -3,2 3,-6 -4,0 -2),(5 -9,0 -9,6 -4,1 -4,-1 6,2 -9,5 -7,8 10)),
                                                                              MULTILINESTRING((-10 10,-10 5,9 -9,2 2,-7 2,0 -3,2 3,-6 -4,0 -2),(5 -9,0 -9,6 -4,1 -4,-1 6,2 -9,5 -7,8 10)),
                                                                              LINESTRING(10 5,-4 7,-5 -8,-4 4,-4 6,-5 9,-1 6,0 -5)),
                                                           GEOMETRYCOLLECTION(MULTILINESTRING((-8 5,9 -10,-9 9,-9 4,3 -2,4 -6),(3 -10,3 8,-10 4,6 -3,8 -2,3 3,4 -7,-8 6,-3 2,5 3,0 10,2 4,1 -5,-6 1),(10 3,-4 2,4 -4,3 -1,-8 0,-7 -2,10 9,-5 5,-3 6),(0 3,3 7,0 2,4 -1,8 8,-10 -4,2 7,-4 5)),
                                                                              POINT(2 -1),
                                                                              MULTIPOLYGON(((-10 8,1 -4,0 -8,9 2,-8 -6,-3 -7,-10 -1,-10 -9,10 -3, -10 8)),((-6 7,-1 3,2 8,10 -6,-8 4,-9 -10,-8 -8,-2 -1,-9 7,6 -9,5 1,-1 -6, -6 7)),((8 9,0 1,-5 -8,0 8,-3 -6,-9 -2,9 -6,-5 5,-1 -3,-8 2,2 -9,5 -5,7 -7,-9 3,0 -3, 8 9)),((1 -4,-7 -10,10 10,5 3,8 -9,-6 5,2 2,-5 5,-1 5, 1 -4))),
                                                                              MULTIPOLYGON(((-10 8,1 -4,0 -8,9 2,-8 -6,-3 -7,-10 -1,-10 -9,10 -3, -10 8)),((-6 7,-1 3,2 8,10 -6,-8 4,-9 -10,-8 -8,-2 -1,-9 7,6 -9,5 1,-1 -6, -6 7)),((8 9,0 1,-5 -8,0 8,-3 -6,-9 -2,9 -6,-5 5,-1 -3,-8 2,2 -9,5 -5,7 -7,-9 3,0 -3, 8 9)),((1 -4,-7 -10,10 10,5 3,8 -9,-6 5,2 2,-5 5,-1 5, 1 -4)))),
                                                           MULTIPOLYGON(((8 0,7 -6,7 -2,-7 -9,-3 10,-4 -3,3 -10, 8 0)),((7 3,7 6,1 8,4 6,-8 -7,-6 -7,9 -4,-1 3,-7 10, 7 3)),((3 -6,5 4,-3 -6,-5 1,-6 0,0 0,0 -7,-2 -10,-4 8,9 9,4 6, 3 -6)),((7 -7,3 4,-5 0, 7 -7)),((4 -9,-2 -9,-2 10, 4 -9))),
                                                           POLYGON((-3 -6,-3 0,3 -10,3 10,1 -4,-6 -10,8 -5,-9 -8,2 -4,9 10,1 -3, -3 -6)),
                                                           POLYGON((2 -5,0 -2,-3 9,0 4,6 -6,5 -4,-4 2,-6 6,3 -4,1 0,-10 -7,1 6,-7 2, 2 -5)),
                                                           MULTIPOINT(7 8,-6 -3,-1 -7,0 7,-2 1,-8 -8))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))) as result;
#
# Bug#20211491 ASSERTION `RTREE_RESULT.SIZE() != 0' FAILED
#
SELECT ST_ASTEXT(ST_UNION(ST_ENVELOPE(ST_GEOMFROMTEXT('LINESTRING(5 9,-1 10,-2 -6,2 9,2 0,3 6,-3 3,9 -2,-3 -10,-7 -4,1 4)')),
ST_UNION(ST_GEOMFROMTEXT('MULTILINESTRING((6 -8,10 -8,3 0,-6 1,0 8,-1 8,-3 -3,6 -6,0 6,1 -6,-1 7,8 3),(-9 -10,-4 0,0 1,-9 1,6 9,-8 7,-2 -6,2 10,-1 -5,3 -5,-1 -10))'), 
ST_GEOMFROMTEXT('MULTILINESTRING((8 7,2 6,-6 -8,-2 10,4 1,9 7,5 9,4 1,8 2,-2 10,8 -5))')))) as result;
result
GEOMETRYCOLLECTION(POLYGON((-7 -10,9 -10,9 10,-7 10,-7 -10)),LINESTRING(9 -8,10 -8,9 -6.857142857142858),LINESTRING(-9 -10,-7 -6),LINESTRING(-7 1,-9 1,-7 2.0666666666666664),LINESTRING(-7 7.142857142857142,-8 7,-7 4.833333333333334))
# Invalid geometry in input.
DO ST_ASTEXT(ST_UNION(ST_INTERSECTION(ST_GEOMFROMTEXT('MULTIPOINT(7 2,-4 -5,6 -9,-7 3,-10 5,8 -6,0 -10,10 -4,8 -10,2 6,8 -4,-8 2,-4 2)'),
ST_GEOMFROMTEXT('MULTIPOLYGON(((6 2,1 1,-4 5,1 4,-3 -4,-7 9,-10 2,-6 1,10 -7,0 1,9 4, 6 2)))')), 
ST_UNION(ST_GEOMFROMTEXT('LINESTRING(-1 -5,0 -6,4 6,3 3,2 8,-2 6,-4 5,6 -7,-1 -1,-8 6,4 -2)'), 
ST_GEOMFROMTEXT('MULTIPOLYGON(((5 -4,-5 -9,-1 -6,-3 0,5 -2, 5 -4)),((-5 -10,-8 -2,-3 7,1 5,5 -10,1 -5,0 10,3 2,1 1, -5 -10)),((4 -2,6 3,7 5,1 2,8 -9,-10 -5,7 -10,-2 -9,-2 0,2 -8,-8 3,5 0, 4 -2)),((6 -4,0 4,-8 -2,10 -10,-6 5, 6 -4)))')))) as result;
SELECT ST_CONTAINS(ST_UNION(ST_INTERSECTION(ST_GEOMFROMTEXT('POINT(-3 3)'),
ST_GEOMFROMTEXT('POLYGON((8 3,-2 9,-10 2,-10 -9,7 -1,4 1,7 6,5 -10,5 3,2 1,-10 0, 8 3))')), 
ST_CONVEXHULL(ST_GEOMFROMTEXT('MULTIPOINT(8 -8,-7 5)'))), 
ST_UNION(ST_GEOMFROMTEXT('POINT(4 1)'), 
ST_GEOMFROMTEXT('MULTIPOINT(-10 -10,5 -2,-6 -7,1 5,-3 0)'))) as result;
result
0
SELECT ST_CONTAINS(ST_UNION(ST_INTERSECTION(ST_GEOMFROMTEXT('POINT(-3 3)'),
ST_GEOMFROMTEXT('POLYGON((8 3,-2 9,-10 2,-10 -9,7 -1,4 1,7 6,5 -10,5 3,2 1,-10 0, 8 3))')), 
ST_CONVEXHULL(ST_GEOMFROMTEXT('MULTIPOINT(8 -8,-7 5)'))), 
ST_UNION(ST_GEOMFROMTEXT('POINT(4 1)'), 
ST_GEOMFROMTEXT('MULTIPOINT(-10 -10,5 -2,-6 -7,1 5,-3 0)'))) as result;
result
0
#
# Bug#20126719 ST_DISTANCE_SPHERE() RETURNS INCORRECT RESULT WITH MULTIPOINT ARGUMENT
#
SELECT ST_DISTANCE_SPHERE(ST_GEOMFROMTEXT('POINT(0 0)'),
ST_GEOMFROMTEXT('POINT(180 0)')) as result;
result
20015042.813723423
SELECT ST_DISTANCE_SPHERE(ST_GEOMFROMTEXT('POINT(0 0)'),
ST_GEOMFROMTEXT('MULTIPOINT(180 0)')) as result;
result
20015042.813723423
SELECT ST_DISTANCE_SPHERE(ST_GEOMFROMTEXT('MULTIPOINT(0 0)'),
ST_GEOMFROMTEXT('MULTIPOINT(180 0)')) as result;
result
20015042.813723423
#
# Bug#20126719 ST_DISTANCE_SPHERE() RETURNS INCORRECT RESULT WITH MULTIPOINT ARGUMENT
#
SELECT ST_DISTANCE_SPHERE(ST_GEOMFROMTEXT('POINT(0 0)'),
ST_GEOMFROMTEXT('POINT(180 0)')) as result;
result
20015042.813723423
SELECT ST_DISTANCE_SPHERE(ST_GEOMFROMTEXT('POINT(0 0)'),
ST_GEOMFROMTEXT('MULTIPOINT(180 0)')) as result;
result
20015042.813723423
SELECT ST_DISTANCE_SPHERE(ST_GEOMFROMTEXT('MULTIPOINT(0 0)'),
ST_GEOMFROMTEXT('MULTIPOINT(180 0)')) as result;
result
20015042.813723423
#
# Bug#20406722 ST_ISVALID() DOES NOT PRINT THE FUNCTION NAME IN THE ERROR MESSAGE
#
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POINT(0 0)', -1));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POINT(0 0)', 1));
ERROR SR001: There's no spatial reference system with SRID 1.
SELECT ST_ISVALID(ST_GEOMFROMTEXT('POINT(0 0)', 1000));
ERROR SR001: There's no spatial reference system with SRID 1000.
#
# Bug#20406850 WL#8034 : CRASH WITH ST_ISVAID() IN ITEM_FUNC_ISVALID::VAL_INT FUNCTION
#
SET @star_center= 'POINT(15 10)';
SET @star_all_points= 'MULTIPOINT(5 0,25 0,15 10,15 25)';
SELECT ST_ISVALID(ST_GEOMFROMTEXT(@star_center,-1024));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_ISVALID(ST_GEOMFROMTEXT(@star_all_points,-1));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
#
# Bug#20414966 WL#8034 : CRASH WITH ST_VAIDATE() IN ITEM_FUNC_VALIDATE::VAL_STR
#
SET @star_center= 'POINT(15 10)';
SET @star_all_points= 'MULTIPOINT(5 0,25 0,15 10,15 25)';
SELECT ST_VALIDATE(ST_GEOMFROMTEXT(@star_center,-1024));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
SELECT ST_VALIDATE(ST_GEOMFROMTEXT(@star_all_points,-1));
ERROR 22003: SRID value is out of range in 'st_geomfromtext'
#
# Bug#20315283 ST_TOUCHES(LINESTRING, MULTIPOINT) RETURNS INCORRECT RESULT
#
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('LINESTRING(0 0,5 0,10 0)'),
ST_GEOMFROMTEXT('MULTIPOINT(10 0)')) as result;
result
1
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('LINESTRING(0 0,5 0,10 0)'),
ST_GEOMFROMTEXT('MULTIPOINT(0 0,10 0)')) as result;
result
1
#
# Bug#20240519 ST_TOUCHES(POLYGON, MULTIPOINT) RETURNS INCORRECT RESULT
#
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('MULTIPOINT(0 0)'));
ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('MULTIPOINT(0 0)'))
1
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 10)'));
ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 10)'))
1
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 10,10 10)'));
ST_TOUCHES(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'),ST_GEOMFROMTEXT('MULTIPOINT(0 0,0 10,10 10)'))
1
#
# Bug#20309003 ST_CONTAINS(GEOMETRYCOLLECTION(POLY,POLY), MULTIPOINT) RETURNS WRONG RESULT
#
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 5,5 5,5 0,0 0)),POLYGON((0 0,0 -5,-5 -5,-5 0,0 0)))'),ST_GEOMFROMTEXT('MULTIPOINT(4 2,-4 -2)')) as result;
result
1
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((5 0,0 10,10 10, 5 0)),POLYGON((5 0,0 -10,10 -10, 5 0)))'),ST_GEOMFROMTEXT('MULTIPOINT(5 2,5 -2)')) as result;
result
1
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0, 8 0, 8 8, 0 8,0 0)),POLYGON((0 0,-8 0,-8 -8, 0 -8, 0 0)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOlygon(((2 2,4 2, 4 4, 2 4, 2 2)), ((-2 -2, -4 -2, -4 -4, -2 -4, -2 -2))), MULTIPOINT(4 4, -4 -4))')) as result;
result
1
#
# Bug#20235165 ST_EQUALS() FUNCTION RETURNS WRONG RESULT
#
SELECT ST_EQUALS(ST_GEOMFROMTEXT('MULTIPOINT(2 2,3 3)'),ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(2 2),POINT(3 3))')) as result;
result
1
SELECT ST_EQUALS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTIPOlygon(((2 2,4 2, 4 4, 2 4, 2 2)), ((-2 -2, -4 -2, -4 -4, -2 -4, -2 -2))), MULTIPOINT(4 4, -4 -4))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POlygon((2 2,4 2, 4 4, 2 4, 2 2)), POLYGON((-2 -2, -4 -2, -4 -4, -2 -4, -2 -2)), POINT(4 4), POINT(-4 -4))')) as result;
result
1
#
# Bug#18293562 GIS SPATIAL RELATION CHECK FUNCTIONS GIVES INCORRECT RESULTS
#
select ST_Intersects(ST_GeomFromText('LINESTRING(15 10,10 0)'),ST_GeomFromText('POINT(15 10)')) as result;
result
1
select ST_Touches(ST_GeomFromText('LINESTRING(15 5,15 25)'),ST_GeomFromText('LINESTRING(15 5,15 25)')) as result;
result
0
select ST_Touches(ST_GeomFromText('POLYGON((0 0,5 0,5 5,0 5,0 0),(1 1,3 1,3 3,1 3,1 1))'),ST_GeomFromText('LINESTRING(3 3,10 10)')) as result;
result
0
select ST_Contains(ST_GeomFromText('POLYGON((0 0,5 0,5 5,0 5,0 0))'),ST_GeomFromText('LINESTRING(1 2,5 5)')) as result;
result
1
select ST_Crosses(ST_GeomFromText('MULTIPOINT(0 0,3 3)'),ST_GeomFromText('LINESTRING(1 1,10 10)')) as result;
result
1
select ST_Crosses(ST_GeomFromText('MULTIPOINT(1 0,15 0,10 10)'),ST_GeomFromText('MULTILINESTRING((15 0,20 0,20 20),(10 10,20 20,15 0))')) as result;
result
1
select ST_Crosses(ST_GeomFromText('MULTIPOINT(1 0,15 0,10 10)'),ST_GeomFromText('LINESTRING(15 0,20 0,10 10,20 20)')) as result;
result
1
select ST_Crosses(ST_GeomFromText('MULTIPOINT(1 0,15 0,10 10)'),ST_GeomFromText('MULTILINESTRING((15 0,20 0,20 20),(10 10,20 20,15 0))')) as result;
result
1
select ST_Crosses(ST_GeomFromText('MULTIPOINT(1 0,15 0,10 10)'),ST_GeomFromText('MULTILINESTRING((15 0,20 0,20 20,15 0))')) as result;
result
1
#
# Bug#20322499 WKT OUTPUT BUFFER OVERRUN
#
SELECT ST_ASTEXT(ST_INTERSECTION(
ST_GEOMFROMTEXT(
'MULTIPOLYGON(((3 2,7 5,0 0,-9.9 9,10.0002 1,2.232432 2,4 -0.7654,3 2)))'),
ST_GEOMFROMWKB(ST_AsWKB(
MULTILINESTRING(
LINESTRING(POINT(7,0)),LINESTRING(POINT(4,6),POINT(7,5),POINT(5,2),POINT(6,9),POINT(1,8.4),POINT(4,6),POINT(6,9),POINT(0,5),POINT(9,8),POINT(-3.6,+.5)),
LINESTRING(POINT(+.9,5)),
LINESTRING(POINT(2,5),POINT(7,1),POINT(2,5),POINT(2.8,6),POINT(1,3),POINT(3,9),POINT(9,7),POINT(6.1,4),POINT(2,7),POINT(8,6),POINT(0,0),POINT(1,9),POINT(4,6.5)),
LINESTRING(POINT(-.5,6)))))));
ERROR 22023: Invalid GIS data provided to function linestring.
#
# Bug#2030883 SPATIAL RELATION CHECK FUNCTIONS RETURN NULL VALUE
#
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_DISJOINT(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_INTERSECTS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_EQUALS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_CROSSES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_DISJOINT(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_INTERSECTS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_EQUALS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
0
SELECT ST_WITHIN(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_TOUCHES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_CROSSES(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
SELECT ST_OVERLAPS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(GEOMETRYCOLLECTION())'),ST_GEOMFROMTEXT('POINT(0 0)')) as result;
result
NULL
#
# Bug#20329124 ST_WITHIN(MULTIPOINT, GEOMETRYCOLLECTION(POLYGON,POLYGON)) RETURNS WRONG RESULT
#
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(4 4,5 5)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)),POLYGON((4 4,4 6,6 6,6 4,4 4)))')) as result;
result
1
SELECT ST_WITHIN(ST_GEOMFROMTEXT('MULTIPOINT(4 4,3 3)'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)),POLYGON((4 4,4 6,6 6,6 4,4 4)))')) as result;
result
1
#
# Bug#20328959 ST_EQUALS(POLYGON, GEOMETRYCOLLECTION(POLYGON, POLYGON)) RETURNS WRONG RESULT
#
SELECT ST_EQUALS(ST_GEOMFROMTEXT('POLYGON((0 0,0 10,10 10,10 0,0 0))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0),(4 4,4 6,6 6,6 4,4 4)),POLYGON((4 4,4 6,6 6,4 4)))')) as result;
result
0
#
# Bug#20393159 ST_CONTAINS(MULTIPOLYGON, MULTIPOINT) GIVES WRONG RESULT
#
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('MULTIPOLYGON(((0 0,0 10,10 10,10 0,5 5,0 0)))'), ST_GEOMFROMTEXT('MULTIPOINT(2 9, 1 0)')) as result;
result
0
SELECT ST_CONTAINS(ST_GEOMFROMTEXT('GeometryCollection(point(1 1), MULTIPOLYGON(((0 0,0 10,10 10,10 0,5 5,0 0))))'), ST_GEOMFROMTEXT('MULTIPOINT(2 9, 1 0)')) as result;
result
0
#
# Bug#20430521 WL#8034 : CRASH WITH ST_SIMPLIFY() IN ITEM_FUNC_SIMPLIFY::VAL_STR
#
SELECT ST_ASTEXT(ST_SIMPLIFY(0x000000000200000000000000000000000000000000, 1)) as result;
ERROR 22023: Invalid GIS data provided to function st_simplify.
#
# Bug#20452274 WL#8034 : SERVER IS HANGING WITH ST_VALIDATE() FUNCTION
#
SELECT ST_ASTEXT(ST_VALIDATE(ST_UNION(ST_GEOMFROMTEXT('MULTIPOLYGON(((-7 -9,-3 7,0 -10,-6 5,10 10,-3 -4,7 9,2 -9)),((1 -10,-3 10,-2 5)))'),
ST_GEOMFROMTEXT('POLYGON((6 10,-7 10,-1 -6,0 5,5 4,1 -9,1 3,-10 -7,-10 8))')))) as result;
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('MULTIPOLYGON(((-7 -9,-3 7,0 -10,-6 5,10 10,-3 -4,7 9,2 -9)),((1 -10,-3 10,-2 5)))'),
ST_GEOMFROMTEXT('POLYGON((6 10,-7 10,-1 -6,0 5,5 4,1 -9,1 3,-10 -7,-10 8))'))) as result;
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
#
# Bug#20454073 ASSERTION `M_GEO_VECT && GET_GEOTYPE() != GEOMETRY::WKB_POLYGON' FAILED
#
SELECT ST_ASTEXT(ST_SIMPLIFY(ST_GEOMFROMTEXT('MULTIPOINT(19 -4, -2 -6, -8 2)'), 1)) as result;
result
MULTIPOINT((19 -4),(-2 -6),(-8 2))
#
# Bug#20510359 ST_BUFFER() RETURNS NULL INSTEAD OF AN EMPTY GEOMETRY
#
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'), -10));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'), -10))
GEOMETRYCOLLECTION EMPTY
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'), -8));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'), -8))
GEOMETRYCOLLECTION EMPTY
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'), -6));
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('POLYGON((0 0,10 0,10 10,0 10,0 0))'), -6))
GEOMETRYCOLLECTION EMPTY
#
# Bug#20510010 ST_BUFFER() : MYSQLD GOT SIGNAL 11
#
SELECT
ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTILINESTRING((12 -12,-15 19),(2 -9,-4 -8,18 3,-9 -8),(13 11,-15 9,-16 6,-17 5)),
                                                        LINESTRING(14 -16,-3 18,-13 -7,-10 1))'), 6561)) as result;
result
POLYGON((-1156 6456,-1274 6445,-1304 6439,-1348 6425,-1518 6402,-1773 6315,-2103 6239,-2405 6103,-2534 6064,-2580 6040,-2705 5997,-2933 5866,-3276 5712,-3537 5527,-3668 5456,-3713 5420,-3793 5373,-3987 5206,-4324 4967,-4534 4744,-4661 4639,-4702 4590,-4743 4554,-4796 4488,-4804 4480,-4888 4370,-4898 4359,-5207 4031,-5359 3787,-5476 3644,-5508 3584,-5520 3569,-5558 3495,-5586 3459,-5662 3304,-5889 2941,-5985 2690,-6081 2509,-6127 2359,-6153 2305,-6156 2297,-6208 2103,-6347 1739,-6388 1495,-6453 1278,-6467 1133,-6486 1063,-6503 812,-6560 470,-6554 246,-6578 -2,-6565 -129,-6571 -212,-6532 -515,-6523 -815,-6477 -1013,-6451 -1282,-6419 -1386,-6407 -1478,-6354 -1636,-6354 -1637,-6352 -1643,-6292 -1822,-6235 -2069,-6160 -2235,-6102 -2428,-6085 -2483,-6084 -2485,-6076 -2512,-6034 -2591,-6001 -2689,-5919 -2833,-5914 -2846,-5871 -2916,-5854 -2950,-5827 -2994,-5793 -3053,-5708 -3242,-5620 -3366,-5485 -3621,-5474 -3635,-5468 -3646,-5423 -3701,-5367 -3799,-5262 -3920,-5247 -3946,-5186 -4012,-5169 -4039,-5058 -4156,-5058 -4156,-4963 -4290,-4936 -4321,-4860 -4392,-4675 -4620,-4607 -4677,-4531 -4764,-4410 -4859,-4378 -4894,-4317 -4939,-4285 -4973,-4124 -5086,-4000 -5204,-3890 -5272,-3686 -5442,-3608 -5484,-3524 -5550,-3396 -5614,-3341 -5655,-3285 -5680,-3235 -5716,-3050 -5799,-2910 -5886,-2762 -5943,-2555 -6055,-2470 -6081,-2383 -6125,-2257 -6160,-2175 -6198,-2131 -6209,-2061 -6240,-1858 -6286,-1708 -6344,-1521 -6375,-1326 -6435,-1237 -6445,-1153 -6468,-1035 -6477,-926 -6504,-895 -6505,-807 -6525,-595 -6531,-439 -6557,-219 -6551,-47 -6569,42 -6561,120 -6567,224 -6554,359 -6560,376 -6558,479 -6560,691 -6524,846 -6520,1092 -6463,1233 -6450,1320 -6425,1389 -6416,1463 -6392,1582 -6372,1630 -6365,1635 -6363,1747 -6344,1950 -6266,2100 -6232,2356 -6117,2466 -6084,2493 -6073,2488 -6061,2499 -6058,2530 -6048,2533 -6046,2604 -6023,2647 -6000,2948 -5884,3133 -5768,3273 -5705,3583 -5485,3593 -5478,3664 -5440,3689 -5420,3720 -5402,3739 -5387,4037 -5199,4195 -5050,4321 -4960,4572 -4694,4657 -4623,4701 -4570,4854 -4425,4885 -4397,4888 -4393,4971 -4315,5095 -4138,5204 -4024,5384 -3736,5472 -3628,5514 -3551,5637 -3385,5652 -3353,5714 -3265,5801 -3070,5886 -2934,5996 -2645,6077 -2493,6101 -2415,6181 -2249,6198 -2181,6238 -2091,6285 -1886,6344 -1732,6389 -1461,6449 -1262,6457 -1185,6497 -1029,6504 -923,6523 -837,6529 -632,6557 -463,6550 -224,6574 18,6567 92,6575 228,6556 371,6558 449,6525 646,6520 822,6474 1021,6447 1298,6426 1366,6411 1478,6353 1650,6342 1717,6273 1898,6232 2076,6163 2230,6072 2528,6039 2589,6011 2672,5903 2863,5882 2918,5865 2952,5770 3104,5705 3249,5626 3360,5464 3662,5422 3713,5391 3769,5254 3929,5200 4015,5033 4194,4960 4297,4933 4328,4861 4396,4647 4655,4598 4696,4572 4726,4435 4835,4344 4932,4117 5098,3997 5211,3880 5284,3652 5470,3599 5499,3585 5510,3461 5574,3330 5670,3072 5790,2907 5893,2737 5958,2517 6075,2359 6123,2193 6200,1918 6269,1705 6351,1482 6388,1286 6447,1160 6460,977 6505,696 6521,436 6564,165 6556,6 6572,-85 6563,-276 6573,-549 6536,-849 6527,-1156 6456))
#
# Bug#20558379 ST_BUFFER() RETURNS AN ERROR WITH VALID GEOMETRYCOLLECTION INPUT
#
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 10,10 10,10 0,0 0)), POLYGON((10 10,10 20,20 20,20 10,10 10)))'), -1)) as result;
result
MULTIPOLYGON(((1 9,1 1,9 1,9 9,1 9)),((19 11,19 19,11 19,11 11,19 11)))
SELECT ST_ASTEXT(ST_BUFFER(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0,0 5,5 5,5 0,0 0)), POLYGON((10 10,10 20,20 20,20 10,10 10)))'), -1)) as result;
result
MULTIPOLYGON(((19 11,19 19,11 19,11 11,19 11)),((4 1,4 4,1 4,1 1,4 1)))
#
# Bug#20316779 Invalid linestrings with one point are permitted
# Bug#20316794 Invalid polygons with rings of less than 3 points are permitted
#
SELECT ST_DISTANCE(ST_GEOMFROMTEXT('MULTILINESTRING((9 8))'),ST_GEOMFROMTEXT('POINT(2 9)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_DISTANCE(ST_GEOMFROMTEXT('MULTIPOINT(-.1 5,3 6,2 2,9 5)'),ST_GEOMFROMTEXT('LINESTRING(0 8)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('LINESTRING(0 0)'))) as result;
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('POLYGON((0 0))'))) as result;
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('POLYGON((0 0, 1 1))'))) as result;
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT HEX(LINESTRING(POINT(0,0)));
ERROR 22023: Invalid GIS data provided to function linestring.
SELECT HEX(ST_GeomFromGeoJSON('{"type":"LineString","coordinates":[[0,0]]}'));
ERROR HY000: Invalid GeoJSON data provided to function st_geomfromgeojson
SELECT HEX(POLYGON(LINESTRING(POINT(0,0))));
ERROR 22023: Invalid GIS data provided to function linestring.
SELECT HEX(POLYGON(LINESTRING(POINT(0,0),POINT(1,0))));
ERROR 22023: Invalid GIS data provided to function polygon.
SELECT HEX(POLYGON(LINESTRING(POINT(0,0),POINT(1,0),POINT(0,0))));
ERROR 22023: Invalid GIS data provided to function polygon.
#
# Bug#19811953  ENVELOPE() FUNCTION RETURNS INVALID POLYGON
#
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('POINT(10 10)')));
ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('POINT(10 10)')))
POINT(10 10)
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('LINESTRING(0 10,10 10)')));
ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('LINESTRING(0 10,10 10)')))
LINESTRING(0 10,10 10)
SELECT ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('LINESTRING(0 0,0 10)')));
ST_ASTEXT(ST_ENVELOPE(ST_GEOMFROMTEXT('LINESTRING(0 0,0 10)')))
LINESTRING(0 0,0 10)
#
# Bug#20196720  ENVELOPE(P) RETURNS NULL FOR A NON NULL POLYGON P
#
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('POLYGON((0 0, 1 0, 2 0, 0 0))')));
ST_AsText(ST_Envelope(ST_GeomFromText('POLYGON((0 0, 1 0, 2 0, 0 0))')))
LINESTRING(0 0,2 0)
SELECT ST_AsText(ST_Envelope(ST_GeomFromText('POLYGON((0 0, 0 0, 0 0, 0 0))')));
ST_AsText(ST_Envelope(ST_GeomFromText('POLYGON((0 0, 0 0, 0 0, 0 0))')))
POINT(0 0)
#
# Bug#20842030 ST_BUFFER CAN BE CALLED WITH PARAMETERS TO CONSUME
# INORDINATE AMOUNT OF MEMORY
#
select st_astext(st_buffer(point(-5,0),8772,
st_buffer_strategy( 'point_circle',1024*1024*1024)))
as result;
ERROR HY000: Parameter points_per_circle exceeds the maximum number of points in a geometry (65536) in function st_buffer_strategy.
do st_buffer(point(-5,0),8772,
st_buffer_strategy('point_circle',1024*1024*1024));
ERROR HY000: Parameter points_per_circle exceeds the maximum number of points in a geometry (65536) in function st_buffer_strategy.
set session max_points_in_geometry=1024*1024;
select st_astext(st_buffer(point(-5,0),8772,
st_buffer_strategy( 'point_circle',1024*1024*1024)))
as result;
ERROR HY000: Parameter points_per_circle exceeds the maximum number of points in a geometry (1048576) in function st_buffer_strategy.
set session max_points_in_geometry=4*1024*1024;
Warnings:
Warning	1292	Truncated incorrect max_points_in_geometry value: '4194304'
select st_astext(st_buffer(point(-5,0),8772,
st_buffer_strategy( 'point_circle',1024*1024*1024)))
as result;
ERROR HY000: Parameter points_per_circle exceeds the maximum number of points in a geometry (1048576) in function st_buffer_strategy.
#
# Bug#20379981 ST_CONTAINS(GEOMETRYCOLLECTION(POLY,POLY), LINESTRING) RETURNS WRONG RESULT
#
SELECT
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((5 0,0 10,10 10,5 0)),
                                                POLYGON((5 0,0 -10,10 -10,5 0)))'),
ST_GEOMFROMTEXT('LINESTRING(5 -2,5 2)')) as result;
result
1
SELECT 1,
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((5 0,0 10,10 10,5 0)),
                                                POLYGON((5 0,0 -10,10 -10,5 0)))'),
ST_GEOMFROMTEXT('MULTIPOINT(4 -3,4 2)')) as result;
1	result
1	1
SELECT 1,
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0, 2 0, 2 2, 0 2, 0 0)),
                                                POLYGON((2 0, 4 0, 4 2, 2 2, 2 0)))'),
ST_GEOMFROMTEXT('POLYGON((1 0, 3 0, 3 1, 1 1, 1 0))')) as result;
1	result
1	1
SELECT 1,
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0, 3 0), LINESTRING(3 0, 6 0))'),
ST_GEOMFROMTEXT('LINESTRING(2 0, 4 0)')) as result;
1	result
1	1
SELECT 1,
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0, 3 0), LINESTRING(2 0, 2 8))'),
ST_GEOMFROMTEXT('LINESTRING(0 0, 2 0, 2 4)')) as result;
1	result
1	1
SELECT 1,
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0, 3 0), LINESTRING(3 0, 6 0))'),
ST_GEOMFROMTEXT('MULTIPOINT(2 0, 4 0)')) as result;
1	result
1	1
SELECT 1,
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(LINESTRING(0 0, 3 0), LINESTRING(2 0, 2 8))'),
ST_GEOMFROMTEXT('MULTIPOINT(0 0, 2 0, 2 4)')) as result;
1	result
1	1
SELECT 1,
ST_CONTAINS(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POLYGON((0 0, 3 0, 3 3, 0 3, 0 0)),
                                                LINESTRING(3 1, 5 1))'),
ST_GEOMFROMTEXT('LINESTRING(2 1, 4 1)')) as result;
1	result
1	1
SELECT ST_AsText(ST_Union(ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(POLYGON((5 0,0 10,10 10,5 0)),
                           POLYGON((5 0,0 -10,10 -10,5 0)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))) as result;
result
MULTIPOLYGON(((5 0,0 -10,10 -10,5 0)),((5 0,10 10,0 10,5 0)))
SELECT ST_AsText(ST_Union(ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(POLYGON((0 0, 5 0, 5 5, 0 5,0 0)),
                           POLYGON((5 0,10 0, 10 3,5 3,5 0)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))) as result;
result
POLYGON((5 3,5 5,0 5,0 0,5 0,10 0,10 3,5 3))
SELECT ST_AsText(ST_Union(ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(POLYGON((0 0, 3 0, 3 3, 0 3, 0 0)),
                           LINESTRING(0 1, 4 1))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))) as result;
result
GEOMETRYCOLLECTION(POLYGON((0 0,3 0,3 3,0 3,0 0)),LINESTRING(3 1,4 1))
SELECT ST_AsText(ST_Union(ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(POLYGON((0 0, 3 0, 3 3, 0 3, 0 0)),
                           LINESTRING(3 0, 3 1, 4 2))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))) as result;
result
GEOMETRYCOLLECTION(POLYGON((0 0,3 0,3 3,0 3,0 0)),LINESTRING(3 1,4 2))
SELECT ST_AsText(ST_Union(ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(POLYGON((0 0, 3 0, 3 3, 0 3, 0 0)),
                           LINESTRING(3 1, 4 1))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))) as result;
result
GEOMETRYCOLLECTION(POLYGON((0 0,3 0,3 3,0 3,0 0)),LINESTRING(3 1,4 1))
# MultiLinestring overlap over an interval.
SELECT ST_AsText(ST_Union(ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(LINESTRING(4 1, 6 1),
                           LINESTRING(0 1, 4 1))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))) as result;
result
MULTILINESTRING((4 1,6 1),(0 1,4 1))
SELECT ST_AsText(ST_Union(ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(LINESTRING(3 1, 6 1),
                           LINESTRING(0 1, 4 1))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))) as result;
result
MULTILINESTRING((3 1,6 1),(0 1,4 1))
SELECT ST_AsText(ST_Union(ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(LINESTRING(3 1, 3 3),
                           LINESTRING(0 1, 4 1))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))) as result;
result
MULTILINESTRING((3 1,3 3),(0 1,4 1))
SELECT ST_AsText(ST_Union(ST_GEOMFROMTEXT(
'GEOMETRYCOLLECTION(LINESTRING(3 0, 3 3),
                           LINESTRING(0 1, 4 1))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION()'))) as result;
result
MULTILINESTRING((3 0,3 3),(0 1,4 1))
#
# Bug#21061746 ASSERTION `GET_WKB_GEOTYPE(P + 1) == GEOMETRY::WKB_LINESTRING' FAILED
#
select ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTILINESTRING((0 -14,13 -8),(-5 -3,8 7),(-6 18,17 -11,-12 19,19 5),(16 11,9 -5),(17 -5,5 10),(-4 17,6 4),(-12 15,17 13,-18 11,15 10),(7 0,2 -16,-18 13,-6 4),(-17 -6,-6 -7,1 4,-18 0)),MULTILINESTRING((-11 -2,17 -14),(18 -12,18 -8),(-13 -16,9 16,9 -10,-7 20),(-14 -5,10 -9,4 1,17 -8),(-9 -4,-2 -12,9 -13,-5 4),(15 17,13 20)))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTILINESTRING((-13 -18,-16 0),(17 11,-1 11,-18 -19,-4 -18),(-8 -8,-15 -13,3 -18,6 8)),LINESTRING(5 16,0 -9,-6 4,-15 17))'))) as result;
result
MULTILINESTRING((0 -14,13 -8),(-5 -3,8 7),(-6 18,17 -11,-12 19,19 5),(16 11,9 -5),(17 -5,5 10),(-4 17,6 4),(-12 15,17 13,-18 11,15 10),(7 0,2 -16,-18 13,-6 4),(-17 -6,-6 -7,1 4,-18 0),(-11 -2,17 -14),(18 -12,18 -8),(-13 -16,9 16,9 -10,-7 20),(-14 -5,10 -9,4 1,17 -8),(-9 -4,-2 -12,9 -13,-5 4),(15 17,13 20),(-13 -18,-16 0),(17 11,-1 11,-18 -19,-4 -18),(-8 -8,-15 -13,3 -18,6 8),(5 16,0 -9,-6 4,-15 17))
select ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTILINESTRING((0 -14,13 -8),(-5 -3,8 7),(-6 18,17 -11,-12 19,19 5),(16 11,9 -5),(17 -5,5 10),(-4 17,6 4),(-12 15,17 13,-18 11,15 10),(7 0,2 -16,-18 13,-6 4),(-17 -6,-6 -7,1 4,-18 0)),MULTIPOINT(0 14,-9 -11),MULTILINESTRING((-11 -2,17 -14),(18 -12,18 -8),(-13 -16,9 16,9 -10,-7 20),(-14 -5,10 -9,4 1,17 -8),(-9 -4,-2 -12,9 -13,-5 4),(15 17,13 20)),MULTIPOINT(16 1,-9 -17,-16 6,-17 3),POINT(-18 13))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(7 0),MULTILINESTRING((-13 -18,-16 0),(17 11,-1 11,-18 -19,-4 -18),(-8 -8,-15 -13,3 -18,6 8)),LINESTRING(5 16,0 -9,-6 4,-15 17),MULTIPOINT(-9 -5,5 15,12 -11,12 11))'))) as result;
result
GEOMETRYCOLLECTION(LINESTRING(0 -14,13 -8),LINESTRING(-5 -3,8 7),LINESTRING(-6 18,17 -11,-12 19,19 5),LINESTRING(16 11,9 -5),LINESTRING(17 -5,5 10),LINESTRING(-4 17,6 4),LINESTRING(-12 15,17 13,-18 11,15 10),LINESTRING(7 0,2 -16,-18 13,-6 4),LINESTRING(-17 -6,-6 -7,1 4,-18 0),LINESTRING(-11 -2,17 -14),LINESTRING(18 -12,18 -8),LINESTRING(-13 -16,9 16,9 -10,-7 20),LINESTRING(-14 -5,10 -9,4 1,17 -8),LINESTRING(-9 -4,-2 -12,9 -13,-5 4),LINESTRING(15 17,13 20),LINESTRING(-13 -18,-16 0),LINESTRING(17 11,-1 11,-18 -19,-4 -18),LINESTRING(-8 -8,-15 -13,3 -18,6 8),LINESTRING(5 16,0 -9,-6 4,-15 17),POINT(0 14),POINT(-9 -11),POINT(16 1),POINT(-9 -17),POINT(-16 6),POINT(-17 3),POINT(-9 -5),POINT(5 15),POINT(12 -11))
select ST_ASTEXT(ST_UNION(ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(MULTILINESTRING((0 -14,13 -8),(-5 -3,8 7),(-6 18,17 -11,-12 19,19 5),(16 11,9 -5),(17 -5,5 10),(-4 17,6 4),(-12 15,17 13,-18 11,15 10),(7 0,2 -16,-18 13,-6 4),(-17 -6,-6 -7,1 4,-18 0)),GEOMETRYCOLLECTION(MULTIPOINT(0 14,-9 -11),MULTILINESTRING((-11 -2,17 -14),(18 -12,18 -8),(-13 -16,9 16,9 -10,-7 20),(-14 -5,10 -9,4 1,17 -8),(-9 -4,-2 -12,9 -13,-5 4),(15 17,13 20))),MULTIPOINT(16 1,-9 -17,-16 6,-17 3),POINT(-18 13))'),
ST_GEOMFROMTEXT('GEOMETRYCOLLECTION(POINT(7 0),MULTILINESTRING((-13 -18,-16 0),(17 11,-1 11,-18 -19,-4 -18),(-8 -8,-15 -13,3 -18,6 8)),LINESTRING(5 16,0 -9,-6 4,-15 17),MULTIPOINT(-9 -5,5 15,12 -11,12 11))'))) as result;
result
GEOMETRYCOLLECTION(LINESTRING(0 -14,13 -8),LINESTRING(-5 -3,8 7),LINESTRING(-6 18,17 -11,-12 19,19 5),LINESTRING(16 11,9 -5),LINESTRING(17 -5,5 10),LINESTRING(-4 17,6 4),LINESTRING(-12 15,17 13,-18 11,15 10),LINESTRING(7 0,2 -16,-18 13,-6 4),LINESTRING(-17 -6,-6 -7,1 4,-18 0),LINESTRING(-11 -2,17 -14),LINESTRING(18 -12,18 -8),LINESTRING(-13 -16,9 16,9 -10,-7 20),LINESTRING(-14 -5,10 -9,4 1,17 -8),LINESTRING(-9 -4,-2 -12,9 -13,-5 4),LINESTRING(15 17,13 20),LINESTRING(-13 -18,-16 0),LINESTRING(17 11,-1 11,-18 -19,-4 -18),LINESTRING(-8 -8,-15 -13,3 -18,6 8),LINESTRING(5 16,0 -9,-6 4,-15 17),POINT(0 14),POINT(-9 -11),POINT(16 1),POINT(-9 -17),POINT(-16 6),POINT(-17 3),POINT(-9 -5),POINT(5 15),POINT(12 -11))
#
# Bug#21127270 CAN NOT CREATE EMPTY GEOMETRY WITH GEOMETRYCOLLECTION()
#
select st_astext(geometrycollection()) as result;
result
GEOMETRYCOLLECTION EMPTY
set @empty_geom = geometrycollection();
select 1, @empty_geom = st_geomfromtext('geometrycollection()') as equal;
1	equal
1	1
#
# Bug#20712775 GEOMETRY OBJECT CONSTRUCTION FUNCTIONS ACCEPT ILLEGAL INPUT
#
SELECT HEX(POLYGON(POINT(0,0)));
ERROR HY000: Incorrect arguments to polygon
SELECT HEX(POINT(POINT(0,0),0));
ERROR HY000: Incorrect arguments to point
SELECT ST_ASTEXT(POINT(LINESTRING(POINT(0,0),POINT(1,1)),LINESTRING(POINT(2,2),POINT(3,3))));
ERROR HY000: Incorrect arguments to point
SELECT ST_ASTEXT(POINT(HEX(POINT(0,0)),HEX(LINESTRING(POINT(0, 0), POINT(1, 1)))));
ST_ASTEXT(POINT(HEX(POINT(0,0)),HEX(LINESTRING(POINT(0, 0), POINT(1, 1)))))
POINT(1.01e40 1.0200000002e60)
Warnings:
Warning	1292	Truncated incorrect DOUBLE value: '0000000001020000000200000000000000000000000000000000000000000000000000F03F000000000000F03F'
SELECT ST_AsText(Point('123', '456'));
ST_AsText(Point('123', '456'))
POINT(123 456)
SELECT ST_AsText(Point('123abc', '456def'));
ST_AsText(Point('123abc', '456def'))
POINT(123 456)
Warnings:
Warning	1292	Truncated incorrect DOUBLE value: '123abc'
Warning	1292	Truncated incorrect DOUBLE value: '456def'
SELECT ST_AsText(Point('abc123', 'def456'));
ST_AsText(Point('abc123', 'def456'))
POINT(0 0)
Warnings:
Warning	1292	Truncated incorrect DOUBLE value: 'abc123'
Warning	1292	Truncated incorrect DOUBLE value: 'def456'
SELECT
HEX(MULTIPOLYGON(LINESTRING(POINT(0,0),POINT(1,0),POINT(1,1),POINT(0,0))));
ERROR HY000: Incorrect arguments to multipolygon
SELECT HEX(MULTIPOLYGON(POINT(0,0)));
ERROR HY000: Incorrect arguments to multipolygon
SELECT HEX(MULTIPOINT(LINESTRING(POINT(0,0),POINT(1,1))));
ERROR HY000: Incorrect arguments to multipoint
SELECT ST_GeomFromWKB(0x020100000000000000000000000000000000000000);
ERROR 22023: Invalid GIS data provided to function st_geomfromwkb.
SELECT ST_GeomFromWKB(0x01040000000100000001020000000100000000000000000000000000000000000000);
ERROR 22023: Invalid GIS data provided to function st_geomfromwkb.
SELECT ST_GeomFromWKB(0x01040000000100000002020000000100000000000000000000000000000000000000);
ERROR 22023: Invalid GIS data provided to function st_geomfromwkb.
SELECT ST_GeomFromWKB(0x01050000000100000001040000000100000000000000000000000000000000000000);
ERROR 22023: Invalid GIS data provided to function st_geomfromwkb.
SELECT ST_GeomFromWKB(0x01050000000100000002040000000100000000000000000000000000000000000000);
ERROR 22023: Invalid GIS data provided to function st_geomfromwkb.
SELECT ST_GeomFromWKB(0x01060000000100000001020000000100000000000000000000000000000000000000);
ERROR 22023: Invalid GIS data provided to function st_geomfromwkb.
SELECT ST_GeomFromWKB(0x01060000000100000003020000000100000000000000000000000000000000000000);
ERROR 22023: Invalid GIS data provided to function st_geomfromwkb.
SELECT ST_GeomFromWKB(0x01070000000100000002010000000000000000000000);
ERROR 22023: Invalid GIS data provided to function st_geomfromwkb.
#
# Bug#21153716 ST_CONVEXHULL RETURNS WRONG RESULT
#
SELECT ST_ASTEXT(ST_ConvexHull(ST_GEOMFROMTEXT(
'MULTILINESTRING((0 10,10 0),(10 0,0 0),(0 0,10 10))'))) as result;
result
POLYGON((0 0,10 0,10 10,0 10,0 0))
SELECT ST_ASTEXT(ST_ConvexHull(ST_GEOMFROMTEXT('MULTIPOINT(1 2, 1 2)')))
AS result;
result
POINT(1 2)
#
# Bug#21109896 POLYGON INTERSECTION POLYGON CAN'T PRODUCE LINESTRINGS AS RESULTS
#
select st_astext(st_intersection(
st_geomfromtext('polygon((0 0, 1 0, 1 1, 0 1, 0 0))'),
st_geomfromtext('polygon((2 0, 3 0, 3 1, 2 1, 2 0))')))
as result;
result
GEOMETRYCOLLECTION EMPTY
select st_astext(st_intersection(
st_geomfromtext('polygon((0 0, 2 0, 2 1, 0 1, 0 0))'),
st_geomfromtext('polygon((1 0, 3 0, 3 1, 1 1, 1 0))')))
as result;
result
POLYGON((1 1,1 0,2 0,2 1,1 1))
select st_astext(st_intersection(
st_geomfromtext('polygon((0 0, 1 0, 1 1, 0 1, 0 0))'),
st_geomfromtext('polygon((1 0, 2 0, 2 1, 1 1, 1 0))')))
as result;
result
LINESTRING(1 0,1 1)
select st_astext(st_intersection(
st_geomfromtext('polygon((0 0, 10 0, 10 3, 0 3, 0 0))'),
st_geomfromtext('polygon((0 5, 1 3, 2 5, 0 5))')))
as result;
result
POINT(1 3)
select st_astext(st_intersection(
st_geomfromtext('polygon((0 0, 10 0, 10 3, 0 3, 0 0))'),
st_geomfromtext('polygon((0 5, 1 3, 2 5, 3 3, 4 3, 4 5, 0 5))')))
as result;
result
GEOMETRYCOLLECTION(POINT(1 3),LINESTRING(4 3,3 3))
# Invalid geometry in input.
select st_astext(st_intersection(
st_geomfromtext('polygon((0 0, 10 0, 10 3, 0 3, 0 0))'),
st_geomfromtext('polygon((0 5, 1 3, 2 5, 3 2, 6 2, 6 5, 0 5))')))
as result;
result
GEOMETRYCOLLECTION(POINT(1 3),POLYGON((6 3,2.6666666666666665 3,3 2,6 2,6 3)))
select st_astext(st_intersection(
st_geomfromtext('polygon((0 0, 10 0, 10 3, 0 3, 0 0))'),
st_geomfromtext('polygon((0 5, 1 3, 4 3, 4 2, 6 2, 6 5, 0 5))')))
as result;
result
GEOMETRYCOLLECTION(LINESTRING(4 3,1 3),POLYGON((6 3,4 3,4 2,6 2,6 3)))
select st_astext(st_intersection(
st_geomfromtext('polygon((0 0, 10 0, 10 3, 0 3, 0 0))'),
st_geomfromtext('polygon((0 5, 1 3, 4 3, 4 0, 6 0, 6 3, 5 3, 5 5, 0 5))')))
as result;
result
GEOMETRYCOLLECTION(LINESTRING(4 3,1 3),POLYGON((6 3,4 3,4 0,6 0,6 3)))
select st_astext(st_intersection(
st_geomfromtext('polygon((0 0, 10 0, 10 3, 0 3, 0 0))'),
st_geomfromtext('polygon((0 5, 1 3, 2 5, 3 3, 4 3, 4 2, 6 2, 6 5, 0 5))')))
as result;
result
GEOMETRYCOLLECTION(POINT(1 3),LINESTRING(4 3,3 3),POLYGON((6 3,4 3,4 2,6 2,6 3)))
select st_astext(st_intersection(
st_geomfromtext('polygon((0 0, 10 0, 10 3, 0 3, 0 0))'),
st_geomfromtext('polygon((0 5, 1 3, 2 5, 3 3, 4 3, 4 0,
                          10 0, 10 3, 6 3, 6 5, 0 5))')))
as result;
result
GEOMETRYCOLLECTION(POINT(1 3),LINESTRING(4 3,3 3),POLYGON((10 3,4 3,4 0,10 0,10 3)))
#
# Bug#21198064 GEOMETRY CONSTRUCTION FUNCTIONS ALLOWS INVALID WKT
#
SELECT ST_AsText(ST_GeomFromText("POINT(10 11) POINT(11 12)")) as result;
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_PointFromText("POINT(10 11) POINT(11 12)")) as result;
ERROR 22023: Invalid GIS data provided to function st_pointfromtext.
SELECT ST_AsText(ST_GeomFromText("MULTIPOINT(10 11, 12 13), 14 15")) as result;
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_AsText(ST_GeomFromText("POINT(10 11)FOOBAR")) as result;
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT HEX(ST_AsWKB(ST_GeomFromText("POINT(10 10)    "))) as result;
result
010100000000000000000024400000000000002440
SELECT HEX(ST_AsWKB(ST_GeomFromText("GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(10 10)), LINESTRING(0 0, 1 1), GEOMETRYCOLLECTION())   "))) as result;
result
01070000000300000001070000000100000001010000000000000000002440000000000000244001020000000200000000000000000000000000000000000000000000000000F03F000000000000F03F010700000000000000
SELECT ST_AsText(ST_GeomFromWKB(0x010100000000000000000024400000000000002440)) as result;
result
POINT(10 10)
SELECT ST_AsText(ST_GeomFromWKB(0x01010000000000000000002440000000000000244011111111)) as result;
ERROR 22023: Invalid GIS data provided to function st_geomfromwkb.
SELECT ST_AsText(ST_GeomFromWKB(0x01070000000300000001070000000100000001010000000000000000002440000000000000244001020000000200000000000000000000000000000000000000000000000000F03F000000000000F03F010700000000000000)) as result;
result
GEOMETRYCOLLECTION(GEOMETRYCOLLECTION(POINT(10 10)),LINESTRING(0 0,1 1),GEOMETRYCOLLECTION EMPTY)
SELECT ST_AsText(ST_GeomFromWKB(0x01070000000300000001070000000100000001010000000000000000002440000000000000244001020000000200000000000000000000000000000000000000000000000000F03F000000000000F03F01070000000000000011111111)) as result;
ERROR 22023: Invalid GIS data provided to function st_geomfromwkb.
#
# Bug#21546656 ASSERTION FAILED: DATA_LEN - SRID_SZ - WKB_HEADER_SIZE >= POINT_DATA_SIZE
#
do st_union(cast(point(1,1)as binary(12)),point(1,1));
ERROR 22023: Invalid GIS data provided to function st_union.
do st_union(cast(point(1,1)as binary(13)),point(1,1));
ERROR 22023: Invalid GIS data provided to function st_union.
select st_astext(st_union(cast(point(1,1)as binary(15)),point(1,1))) as res;
ERROR 22023: Invalid GIS data provided to function st_union.
#
# Bug #21663612 AUTO INCREMENT COLUMN VALUES ARE NOT VISIBLE IN A TABLE CONTAINING SPATIAL INDEX
#
CREATE TABLE t2 (id INT PRIMARY KEY AUTO_INCREMENT, g GEOMETRY NOT
NULL SRID 0, SPATIAL INDEX(g)) ENGINE=INNODB;
INSERT INTO t2(g) VALUES (ST_GEOMFROMTEXT('POINT(0 0)'));
INSERT INTO t2(g) VALUES (ST_GEOMFROMTEXT('POINT(10 0)'));
INSERT INTO t2(g) VALUES (ST_GEOMFROMTEXT('POINT(10 10)'));
SELECT id FROM t2;
id
1
2
3
DROP TABLE t2;
CREATE TABLE t3 (id INT PRIMARY KEY, g GEOMETRY NOT NULL SRID 0,
SPATIAL INDEX(g)) ENGINE=INNODB;
INSERT INTO t3(id, g) VALUES
(1, ST_GEOMFROMTEXT('POINT(0 0)')),
(2, ST_GEOMFROMTEXT('POINT(10 0)')),
(3, ST_GEOMFROMTEXT('POINT(10 10)'));
SELECT id FROM t3;
id
1
2
3
DROP TABLE t3;
#
# Bug#17167633 WKT <EXACT NUMERIC LITERAL> WITHOUT LEADING ZEROS
#
SELECT ST_AsText(ST_PointFromText('POINT(1 . 1)'));
ERROR 22023: Invalid GIS data provided to function st_pointfromtext.
SELECT ST_AsText(ST_PointFromText('POINT(1 .1)'));
ST_AsText(ST_PointFromText('POINT(1 .1)'))
POINT(1 0.1)
SELECT ST_AsText(ST_LineFromText('LINESTRING(.0 0,0 0,0 0)'));
ST_AsText(ST_LineFromText('LINESTRING(.0 0,0 0,0 0)'))
LINESTRING(0 0,0 0,0 0)
SELECT ST_X(ST_PointFromText('POINT(.0  0)'));
ST_X(ST_PointFromText('POINT(.0  0)'))
0
SELECT ST_Y(ST_PointFromText('point(0  .0)'));
ST_Y(ST_PointFromText('point(0  .0)'))
0
CREATE TABLE t1 (col_1 CHAR(7));
INSERT INTO t1 VALUES ('POINT(.');
SELECT ST_GeomFromText(col_1) FROM t1;
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
DROP TABLE t1;
#
# Bug#11761559 SUPPORT OGC STANDARD WKT FOR MULTIPOINT
#
SELECT ST_ASTEXT(ST_GEOMFROMTEXT('MULTIPOINT(0 0, 1 1), (2 2)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_GEOMFROMTEXT('MULTIPOINT(0 0, (1 1, 2 2))'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_GEOMFROMTEXT('MULTIPOINT(0 0, (1 1), 2 2)'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_GEOMFROMTEXT('MULTIPOINT((0 0), (1 1), 2 2, (3 3), 4 4, 5 5, (6 6))'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_GEOMFROMTEXT('MULTIPOINT((0 0), (1 1), (2 2), (3 3), (4 4), (5 5), (6 6))'));
ST_ASTEXT(ST_GEOMFROMTEXT('MULTIPOINT((0 0), (1 1), (2 2), (3 3), (4 4), (5 5), (6 6))'))
MULTIPOINT((0 0),(1 1),(2 2),(3 3),(4 4),(5 5),(6 6))
SELECT ST_ASTEXT(ST_GEOMFROMTEXT('MULTIPOINT()'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_GEOMFROMTEXT('MULTIPOINT(())'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_GEOMFROMTEXT('MULTIPOINT((()))'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
SELECT ST_ASTEXT(ST_GEOMFROMTEXT('MULTIPOINT((), ())'));
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
DROP TABLE IF EXISTS p1;
CREATE PROCEDURE p1(dist DOUBLE, geom TEXT)
BEGIN
DECLARE g GEOMETRY;
SET g=ST_GeomFromText(geom);
SELECT geom AS `-----`;
SELECT dist, ST_GeometryType(@buf:=ST_Buffer(g, dist)) AS `buffer`, ROUND(ST_AREA(@buf),2) AS buf_area;
END|
Warnings:
Warning	1287	Setting user variables within expressions is deprecated and will be removed in a future release. Consider alternatives: 'SET variable=expression, ...', or 'SELECT expression(s) INTO variables(s)'.
#
# Testing ST_BUFFER with positive distance
#
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
-----
POINT(0 0)
dist	buffer	buf_area
1	POLYGON	3.12
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
-----
LineString(0 1, 1 1)
dist	buffer	buf_area
1	POLYGON	5.12
-----
LineString(9 9,8 1,1 5,0 0)
dist	buffer	buf_area
1	POLYGON	44.6
-----
Polygon((2 2,2 8,8 8,8 2,2 2))
dist	buffer	buf_area
1	POLYGON	63.12
-----
Polygon((0 0,0 8,8 8,8 0,0 0),(2 2,6 2,6 6,2 6,2 2))
dist	buffer	buf_area
1	POLYGON	95.12
-----
Polygon((0 0, 0 8, 8 8, 8 10, -10 10, -10 0, 0 0))
dist	buffer	buf_area
1	POLYGON	174.9
-----
MultiPoint(9 9,8 1,1 5)
dist	buffer	buf_area
1	MULTIPOLYGON	9.36
-----
MultiLineString((0 0,2 2))
dist	buffer	buf_area
1	POLYGON	8.78
-----
MultiLineString((0 0,2 2,0 4))
dist	buffer	buf_area
1	POLYGON	14.22
-----
MultiLineString((0 0,2 2),(0 2,2 0))
dist	buffer	buf_area
1	POLYGON	13.56
-----
MultiLineString((2 2,2 8,-2 8),(-6 -6, 6 6),(10 10, 14 14))
dist	buffer	buf_area
1	MULTIPOLYGON	70.01
-----
MultiLineString((0 0,0 1),(3 0,3 1))
dist	buffer	buf_area
1	MULTIPOLYGON	10.24
# Invalid geometry in input
-----
MultiPolygon(((2 2,2 8,8 8,8 2,2 2)), ((9 9,8 1,1 5,9 9)))
dist	buffer	buf_area
1	POLYGON	73.16
# Invalid geometry in input
-----
MultiPolygon(((2 2,2 8,8 8,8 2,2 2), (4 4,4 6,6 6,6 4,4 4)),((9 9,8 1,1 5,9 9)))
dist	buffer	buf_area
1	POLYGON	73.16
-----
GeometryCollection(Point(0 0))
dist	buffer	buf_area
1	POLYGON	3.12
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
-----
GeometryCollection(LineString(0 0, 2 2))
dist	buffer	buf_area
1	POLYGON	8.78
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
-----
GeometryCollection(Polygon((2 2,2 8,8 8,8 2,2 2)))
dist	buffer	buf_area
1	POLYGON	63.12
-----
GeometryCollection(MultiPoint(9 9,8 1,1 5))
dist	buffer	buf_area
1	MULTIPOLYGON	9.36
-----
GeometryCollection(MultiLineString((0 0,0 1),(3 0,3 1)))
dist	buffer	buf_area
1	MULTIPOLYGON	10.24
-----
GeometryCollection(MultiPolygon(((0 0, 3 0, 3 3, 0 3, 0 0)),((6 6,6 9,9 9,9 6,6 6))))
dist	buffer	buf_area
1	MULTIPOLYGON	48.24
-----
GeometryCollection(Point(9 9),LineString(1 5,0 0),Polygon((2 2,2 8,8 8,8 2,2 2)))
dist	buffer	buf_area
1	POLYGON	75.88
#
# Testing ST_BUFFER with zero distance
#
-----
Polygon((2 2,2 8,8 8,8 2,2 2))
dist	buffer	buf_area
0	POLYGON	36
-----
Polygon((0 0,0 8,8 8,8 0,0 0),(2 2,6 2,6 6,2 6,2 2))
dist	buffer	buf_area
0	POLYGON	48
-----
Polygon((0 0, 0 8, 8 8, 8 10, -10 10, -10 0, 0 0))
dist	buffer	buf_area
0	POLYGON	116
# Invalid geometry in input
-----
MultiPolygon(((2 2,2 8,8 8,8 2,2 2)), ((9 9,8 1,1 5,9 9)))
dist	buffer	buf_area
0	MULTIPOLYGON	66
# Invalid geometry in input
-----
MultiPolygon(((2 2,2 8,8 8,8 2,2 2), (4 4,4 6,6 6,6 4,4 4)),((9 9,8 1,1 5,9 9)))
dist	buffer	buf_area
0	MULTIPOLYGON	62
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
#
# Testing ST_BUFFER with negative distance
#
-----
POINT(0 0)
ERROR HY000: Incorrect arguments to st_buffer
-----
LineString(0 1, 1 1)
ERROR HY000: Incorrect arguments to st_buffer
-----
LineString(9 9,8 1,1 5,0 0)
ERROR HY000: Incorrect arguments to st_buffer
-----
Polygon((2 2,2 8,8 8,8 2,2 2))
dist	buffer	buf_area
-1	POLYGON	16
-----
Polygon((0 0,0 8,8 8,8 0,0 0),(2 2,6 2,6 6,2 6,2 2))
dist	buffer	buf_area
-1	MULTIPOLYGON	0.88
-----
Polygon((0 0, 0 8, 8 8, 8 10, -10 10, -10 0, 0 0))
dist	buffer	buf_area
-1	POLYGON	64.22
-----
MultiPoint(9 9,8 1,1 5)
ERROR HY000: Incorrect arguments to st_buffer
-----
MultiLineString((0 0,2 2))
ERROR HY000: Incorrect arguments to st_buffer
-----
MultiLineString((0 0,2 2,0 4))
ERROR HY000: Incorrect arguments to st_buffer
-----
MultiLineString((0 0,2 2),(0 2,2 0))
ERROR HY000: Incorrect arguments to st_buffer
-----
MultiLineString((2 2,2 8,-2 8),(-6 -6, 6 6),(10 10, 14 14))
ERROR HY000: Incorrect arguments to st_buffer
# Invalid geometry in input
-----
MultiPolygon(((2 2,2 8,8 8,8 2,2 2)), ((9 9,8 1,1 5,9 9)))
dist	buffer	buf_area
-1	POLYGON	8
# Invalid geometry in input
-----
MultiPolygon(((2 2,2 8,8 8,8 2,2 2), (4 4,4 6,6 6,6 4,4 4)),((9 9,8 1,1 5,9 9)))
dist	buffer	buf_area
-1	MULTIPOLYGON	0.74
-----
GeometryCollection(Point(0 0))
ERROR HY000: Incorrect arguments to st_buffer
-----
GeometryCollection(LineString(0 0, 2 2))
ERROR HY000: Incorrect arguments to st_buffer
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
ERROR 22023: Invalid GIS data provided to function st_geomfromtext.
-----
GeometryCollection(Polygon((2 2,2 8,8 8,8 2,2 2)))
dist	buffer	buf_area
-1	POLYGON	16
-----
GeometryCollection(MultiPoint(9 9,8 1,1 5))
ERROR HY000: Incorrect arguments to st_buffer
-----
GeometryCollection(MultiLineString((0 0,0 1),(3 0,3 1)))
ERROR HY000: Incorrect arguments to st_buffer
-----
GeometryCollection(Point(9 9),LineString(1 5,0 0),Polygon((2 2,2 8,8 8,8 2,2 2)))
ERROR HY000: Incorrect arguments to st_buffer
SELECT ST_CONTAINS(
ST_GeomFromText('MULTIPOLYGON(((0 0, 0 5, 5 5, 5 0, 0 0)),((6 6, 6 11, 11 11, 11 6, 6 6)))'),
ST_GeomFromText('POINT(5 10)'));
ST_CONTAINS(
ST_GeomFromText('MULTIPOLYGON(((0 0, 0 5, 5 5, 5 0, 0 0)),((6 6, 6 11, 11 11, 11 6, 6 6)))'),
ST_GeomFromText('POINT(5 10)'))
0
SELECT ST_AsText(ST_UNION(
ST_GeomFromText('MULTIPOLYGON(((0 0, 0 5, 5 5, 5 0, 0 0)),((6 6, 6 11, 11 11, 11 6, 6 6)))'),
ST_GeomFromText('POINT(5 10)')));
ST_AsText(ST_UNION(
ST_GeomFromText('MULTIPOLYGON(((0 0, 0 5, 5 5, 5 0, 0 0)),((6 6, 6 11, 11 11, 11 6, 6 6)))'),
ST_GeomFromText('POINT(5 10)')))
GEOMETRYCOLLECTION(POLYGON((0 0,5 0,5 5,0 5,0 0)),POLYGON((6 6,11 6,11 11,6 11,6 6)),POINT(5 10))
DROP PROCEDURE p1;
#
# Bug #13833019 ASSERTION `T1->RESULT_RANGE' FAILED IN GCALC_OPERATION_REDUCER::END_COUPLE
#
SELECT ST_GeometryType(ST_BUFFER(ST_MULTIPOLYGONFROMTEXT('MULTIPOLYGON(((0 0,9 4,3 3,0 0)),((2 2,2 2,8 8,2 3,2 2)))'), 3));
ST_GeometryType(ST_BUFFER(ST_MULTIPOLYGONFROMTEXT('MULTIPOLYGON(((0 0,9 4,3 3,0 0)),((2 2,2 2,8 8,2 3,2 2)))'), 3))
POLYGON
#
# Bug #13832749 	HANDLE_FATAL_SIGNAL IN GCALC_FUNCTION::COUNT_INTERNAL
#
SELECT ST_GeometryType(ST_BUFFER(ST_MULTIPOLYGONFROMTEXT('MULTIPOLYGON(((3 5,2 5,2 4,3 4,3 5)),((2 2,2 8,8 8,8 2,2 2), (4 4,4 6,6 6,6 4,4 4)), ((9 9,8 1,1 5,9 9)))'),1));
ST_GeometryType(ST_BUFFER(ST_MULTIPOLYGONFROMTEXT('MULTIPOLYGON(((3 5,2 5,2 4,3 4,3 5)),((2 2,2 8,8 8,8 2,2 2), (4 4,4 6,6 6,6 4,4 4)), ((9 9,8 1,1 5,9 9)))'),1))
POLYGON
#
# Bug#13358363 - ASSERTION: N > 0 && N < SINUSES_CALCULATED*2+1 | GET_N_SINCOS/ADD_EDGE_BUFFER
#
DO ST_BUFFER(ST_LINEFROMTEXT('linestring(1 1,2 2)'),'');
SELECT ST_WITHIN(
ST_LINESTRINGFROMTEXT(' LINESTRING(3 8,9 2,3 8,3 3,7 6,4 7,4 7,8 1) '),
ST_BUFFER(ST_MULTIPOLYGONFROMTEXT(' MULTIPOLYGON(((3 5,2 5,2 4,3 4,3 5)),((2 2,2 8,8 8,8 2,2 2),(4 4,4 6,6 6,6 4,4 4)),((0 5,3 5,3 2,1 2,1 1,3 1,3 0,0 0,0 3,2 3,2 4,0 4,0 5))) '),
ST_NUMINTERIORRINGS(ST_POLYGONFROMTEXT('POLYGON((3 5,2 4,2 5,3 5)) '))));
ST_WITHIN(
ST_LINESTRINGFROMTEXT(' LINESTRING(3 8,9 2,3 8,3 3,7 6,4 7,4 7,8 1) '),
ST_BUFFER(ST_MULTIPOLYGONFROMTEXT(' MULTIPOLYGON(((3 5,2 5,2 4,3 4,3 5)),((2 2,2 8,8 8,8 2,2 2),(4 4,4 6,6 6,6 4,4 4)),((0 5,3 5,3 2,1 2,1 1,3 1,3 0,0 0,0 3,2 3,2 4,0 4,0 5
0
SELECT ST_DIMENSION(ST_BUFFER(ST_POLYGONFROMTEXT(' POLYGON((3 5,2 5,2 4,3 4,3 5)) '),
ST_NUMINTERIORRINGS(ST_POLYGONFROMTEXT(' POLYGON((0 0,9 3,4 2,0 0))'))));
ST_DIMENSION(ST_BUFFER(ST_POLYGONFROMTEXT(' POLYGON((3 5,2 5,2 4,3 4,3 5)) '),
ST_NUMINTERIORRINGS(ST_POLYGONFROMTEXT(' POLYGON((0 0,9 3,4 2,0 0))'))))
2
SELECT ST_NUMINTERIORRINGS(
ST_ENVELOPE(ST_BUFFER(ST_MULTIPOLYGONFROMTEXT('MULTIPOLYGON(((3 5,2 5,2 4,3 4,3 5))) '),
ST_SRID(ST_MULTILINESTRINGFROMTEXT('MULTILINESTRING((2 2,4 2,1 2,2 4,2 2)) ')))));
ST_NUMINTERIORRINGS(
ST_ENVELOPE(ST_BUFFER(ST_MULTIPOLYGONFROMTEXT('MULTIPOLYGON(((3 5,2 5,2 4,3 4,3 5))) '),
ST_SRID(ST_MULTILINESTRINGFROMTEXT('MULTILINESTRING((2 2,4 2,1 2,2 4,2 2)) ')))))
0
SELECT ST_ASTEXT(ST_BUFFER(ST_POLYGONFROMTEXT(' POLYGON((9 9,5 2,4 5,9 9))'),
ST_SRID(ST_GEOMETRYFROMTEXT(' MULTIPOINT(8 4,5 0,7 8,6 9,3 4,7 3,5 5) '))));
ST_ASTEXT(ST_BUFFER(ST_POLYGONFROMTEXT(' POLYGON((9 9,5 2,4 5,9 9))'),
ST_SRID(ST_GEOMETRYFROMTEXT(' MULTIPOINT(8 4,5 0,7 8,6 9,3 4,7 3,5 5) '))))
POLYGON((9 9,5 2,4 5,9 9))
#
# 18701868 CRASH IN MY_DECIMAL::SANITY_CHECK,
#          ORDER BY CAST (INVALID GIS FUNCTION RESULT)
#
CREATE TABLE t1 (a INT) engine=innodb;
INSERT INTO t1 VALUES (1);
SELECT * FROM t1 ORDER BY cast(st_distance(1,1) as DECIMAL);
a
1
DELETE FROM t1 ORDER BY cast(st_distance(1,1) as DECIMAL);
ERROR 22023: Invalid GIS data provided to function st_distance.
UPDATE t1 SET a=1 ORDER BY cast(st_distance(1,1) as DECIMAL);
ERROR 22023: Invalid GIS data provided to function st_distance.
DROP TABLE t1;
