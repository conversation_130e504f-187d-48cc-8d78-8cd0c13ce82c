--disable_warnings
DROP TABLE IF EXISTS t1,t2,t3,t4;
DROP FUNCTION IF EXISTS sf1;
--enable_warnings
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET#       ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# CONTAINS SQL     ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# NO SQL     ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# READS SQL DATA     ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# MODIFIES SQL DATA     ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET#     COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# CONTAINS SQL   COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# NO SQL   COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# READS SQL DATA   COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# MODIFIES SQL DATA   COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET#       ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# CONTAINS SQL     ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# NO SQL     ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# READS SQL DATA     ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# MODIFIES SQL DATA     ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET#     COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# CONTAINS SQL   COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# NO SQL   COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# READS SQL DATA   COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# MODIFIES SQL DATA   COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET#   SQL SECURITY INVOKER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# CONTAINS SQL SQL SECURITY INVOKER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# NO SQL SQL SECURITY INVOKER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# READS SQL DATA SQL SECURITY INVOKER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# MODIFIES SQL DATA SQL SECURITY INVOKER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET#   SQL SECURITY INVOKER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# NO SQL SQL SECURITY INVOKER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET#   SQL SECURITY INVOKER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# CONTAINS SQL SQL SECURITY INVOKER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# NO SQL SQL SECURITY INVOKER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# READS SQL DATA SQL SECURITY INVOKER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# MODIFIES SQL DATA SQL SECURITY INVOKER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET#   SQL SECURITY INVOKER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# CONTAINS SQL SQL SECURITY INVOKER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# NO SQL SQL SECURITY INVOKER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# READS SQL DATA SQL SECURITY INVOKER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# MODIFIES SQL DATA SQL SECURITY INVOKER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET#    SQL SECURITY DEFINER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# CONTAINS SQL  SQL SECURITY DEFINER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# NO SQL  SQL SECURITY DEFINER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# READS SQL DATA  SQL SECURITY DEFINER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# MODIFIES SQL DATA  SQL SECURITY DEFINER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET#    SQL SECURITY DEFINER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# NO SQL  SQL SECURITY DEFINER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1    #DET# MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET#    SQL SECURITY DEFINER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# CONTAINS SQL  SQL SECURITY DEFINER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# NO SQL  SQL SECURITY DEFINER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# READS SQL DATA  SQL SECURITY DEFINER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# MODIFIES SQL DATA  SQL SECURITY DEFINER   ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET#    SQL SECURITY DEFINER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# CONTAINS SQL  SQL SECURITY DEFINER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# NO SQL  SQL SECURITY DEFINER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# READS SQL DATA  SQL SECURITY DEFINER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;
CREATE TABLE t1 (id INTEGER NULL , data INTEGER NULL, KEY(id));
CREATE TABLE t2 (i INTEGER NULL);
CREATE TABLE t3 (id INTEGER NULL, data INTEGER NULL, KEY(id));
CREATE TABLE t4 (i INTEGER NULL);
INSERT INTO t1 VALUES(1,1),(2,1),(3,4),(4,5);
INSERT INTO t2 VALUES(1),(2),(3);
DELIMITER //;
CREATE FUNCTION sf1() RETURNS INTEGER
BEGIN
DECLARE done INT DEFAULT 0;
DECLARE a CHAR(16);
DECLARE b,c INT;
DECLARE cur1 CURSOR FOR SELECT id,data FROM t1;
DECLARE cur2 CURSOR FOR SELECT i FROM t2;
DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
OPEN cur1;
OPEN cur2;
REPEAT
FETCH cur1 INTO a, b;
FETCH cur2 INTO c;
IF NOT done THEN
IF b < c THEN
INSERT INTO t3 VALUES (a,b);
ELSE
INSERT INTO t3 VALUES (a,c);
END IF;
END IF;
UNTIL done END REPEAT;
CLOSE cur1;
CLOSE cur2;
RETURN 0;
END//
DELIMITER ;//
ALTER FUNCTION sf1  LANGUAGE SQL #DET# MODIFIES SQL DATA  SQL SECURITY DEFINER COMMENT 'comment' ;
SELECT sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
INSERT INTO t4 VALUES(sf1());
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
UPDATE t4 SET i = sf1() + 1 WHERE i = sf1();
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DELETE FROM t4 WHERE i = sf1() + 1;
SELECT * FROM t1 ORDER BY id;
SELECT * FROM t2 ORDER BY i;
SELECT * FROM t3 ORDER BY id;
DROP FUNCTION sf1;
DROP TABLE t1,t2,t3,t4;

