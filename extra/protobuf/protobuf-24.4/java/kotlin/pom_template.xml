<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>{groupId}</groupId>
    <artifactId>protobuf-parent</artifactId>
    <version>{version}</version>
  </parent>

  <artifactId>{artifactId}</artifactId>
  <packaging>{type}</packaging>

  <name>Protocol Buffers [Kotlin-Core]</name>
  <description>
    Kotlin core Protocol Buffers library. Protocol Buffers are a way of encoding structured data in an
    efficient yet extensible format.
  </description>
  <dependencies>
    <dependency>
      <groupId>{groupId}</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>{version}</version>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib</artifactId>
      <version>${kotlin.version}</version>
    </dependency>
    {dependencies}
  </dependencies>

  <properties>
    <kotlin.version>1.6.0</kotlin.version>
  </properties>

</project>
