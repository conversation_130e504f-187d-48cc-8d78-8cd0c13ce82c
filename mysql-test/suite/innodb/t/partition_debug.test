--source include/have_debug.inc
--source include/have_innodb_max_16k.inc
--source include/have_debug_sync.inc

--echo # Different partitions can have different row format
--echo # Case one: between dynamic and redundant
--echo #

# Have to set the default row format to a non-default one.
SET GLOBAL innodb_default_row_format = REDUNDANT;

CREATE TABLE t1 (a INT NOT NULL, d INT NOT NULL, index(a, d)) ENGINE=InnoDB PARTITION BY LIST COLUMNS (a,d)  (PARTITION p36_1 VALUES IN ((10,5),(10,6)), PARTITION p36_2 VALUES IN ((11,5),(11,6)));

SET SESSION debug= '+d,skip_dd_table_access_check';
SELECT name, se_private_data FROM mysql.table_partitions;

--source include/restart_mysqld.inc

ALTER TABLE t1 ADD PARTITION (PARTITION p_r13 VALUES IN ((741,377), (21,728)), PARTITION p_r23 VALUES IN ((102,337), (453,337), (-9,179)));

SET SESSION debug= '+d,skip_dd_table_access_check';
SELECT name, se_private_data FROM mysql.table_partitions;

CHECK TABLE t1;

SHOW CREATE TABLE t1;

--source include/restart_mysqld.inc

SET SESSION debug= '+d,skip_dd_table_access_check';
SELECT name, se_private_data FROM mysql.table_partitions;

CHECK TABLE t1;

DROP TABLE t1;

SET GLOBAL innodb_default_row_format = Default;


--echo #
--echo # Different partitions can have different row format
--echo # Case two: between dynamic and compressed
--echo #

CREATE TABLE t1 (a INT, b INT, key(a)) ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=2 PARTITION BY RANGE (a % 7) (PARTITION p1 VALUES LESS THAN(1), PARTITION p2 VALUES LESS THAN (2), PARTITION p3 VALUES LESS THAN (5), PARTITION p4 VALUES LESS THAN(MAXVALUE));

SHOW CREATE TABLE t1;

SET @orig_innodb_file_per_table= @@innodb_file_per_table;
SET GLOBAL innodb_file_per_table = 0;
SET @save_innodb_strict_mode=@@session.innodb_strict_mode;
SET SESSION innodb_strict_mode = 0;

SET SESSION debug= '+d,skip_dd_table_access_check';
SELECT name, se_private_data FROM mysql.table_partitions;

ALTER TABLE t1 REORGANIZE PARTITION p1, p2 INTO (PARTITION p1 VALUES LESS THAN(2));

SELECT name, se_private_data FROM mysql.table_partitions;

SHOW CREATE TABLE t1;

SET GLOBAL innodb_file_per_table = @orig_innodb_file_per_table;
SET SESSION innodb_strict_mode=@save_innodb_strict_mode;

--source include/restart_mysqld.inc

SET SESSION debug= '+d,skip_dd_table_access_check';
SELECT name, se_private_data FROM mysql.table_partitions;

CHECK TABLE t1;

SHOW CREATE TABLE t1;

DROP TABLE t1;
