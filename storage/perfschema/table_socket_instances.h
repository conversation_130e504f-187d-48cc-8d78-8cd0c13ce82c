/* Copyright (c) 2008, 2025, Oracle and/or its affiliates.

  This program is free software; you can redistribute it and/or modify
  it under the terms of the GNU General Public License, version 2.0,
  as published by the Free Software Foundation.

  This program is designed to work with certain software (including
  but not limited to OpenSSL) that is licensed under separate terms,
  as designated in a particular file or component or in included license
  documentation.  The authors of MySQL hereby grant you an additional
  permission to link the program and your derivative works with the
  separately licensed software that they have either included with
  the program or referenced in the documentation.

  This program is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License, version 2.0, for more details.

  You should have received a copy of the GNU General Public License
  along with this program; if not, write to the Free Software
  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */

#ifndef TABLE_SOCKET_INSTANCES_H
#define TABLE_SOCKET_INSTANCES_H

/**
  @file storage/perfschema/table_socket_instances.h
  Table SOCKET_INSTANCES (declarations).
*/

#include "my_config.h"

#ifdef HAVE_NETINET_IN_H
#include <netinet/in.h>
#endif
#include <sys/types.h>

#include "my_base.h"
#include "my_inttypes.h"
#include "mysql/components/services/bits/psi_socket_bits.h"
#include "storage/perfschema/pfs_engine_table.h"
#include "storage/perfschema/table_helper.h"

class Field;
class Plugin_table;
struct PFS_socket;
struct TABLE;
struct THR_LOCK;

/**
  @addtogroup performance_schema_tables
  @{
*/

/** A row of PERFORMANCE_SCHEMA.SOCKET_INSTANCES. */
struct row_socket_instances {
  /** Column EVENT_NAME. */
  const char *m_event_name{nullptr};
  /** Length in bytes of @c m_event_name. */
  uint m_event_name_length{0};
  /** Column OBJECT_INSTANCE_BEGIN */
  const void *m_identity{nullptr};
  /** Column THREAD_ID */
  ulonglong m_thread_id{0};
  /** True if thread_is is set */
  bool m_thread_id_set{false};
  /** Column SOCKET_ID */
  uint m_fd{0};
  /** Socket ip address, IPV4 or IPV6 */
  char m_ip[INET6_ADDRSTRLEN + 1];
  /** Length in bytes of @c m_ip. */
  uint m_ip_length{0};
  /** Column PORT */
  uint m_port{0};
  /** Socket state: ACTIVE or IDLE */
  PSI_socket_state m_state;
};

class PFS_index_socket_instances : public PFS_engine_index {
 public:
  explicit PFS_index_socket_instances(PFS_engine_key *key_1)
      : PFS_engine_index(key_1) {}

  PFS_index_socket_instances(PFS_engine_key *key_1, PFS_engine_key *key_2)
      : PFS_engine_index(key_1, key_2) {}

  ~PFS_index_socket_instances() override = default;

  virtual bool match(const PFS_socket *pfs) = 0;
};

class PFS_index_socket_instances_by_instance
    : public PFS_index_socket_instances {
 public:
  PFS_index_socket_instances_by_instance()
      : PFS_index_socket_instances(&m_key), m_key("OBJECT_INSTANCE_BEGIN") {}

  ~PFS_index_socket_instances_by_instance() override = default;

  bool match(const PFS_socket *pfs) override;

 private:
  PFS_key_object_instance m_key;
};

class PFS_index_socket_instances_by_thread : public PFS_index_socket_instances {
 public:
  PFS_index_socket_instances_by_thread()
      : PFS_index_socket_instances(&m_key), m_key("THREAD_ID") {}

  ~PFS_index_socket_instances_by_thread() override = default;

  bool match(const PFS_socket *pfs) override;

 private:
  PFS_key_thread_id m_key;
};

class PFS_index_socket_instances_by_socket : public PFS_index_socket_instances {
 public:
  PFS_index_socket_instances_by_socket()
      : PFS_index_socket_instances(&m_key), m_key("SOCKET_ID") {}

  ~PFS_index_socket_instances_by_socket() override = default;

  bool match(const PFS_socket *pfs) override;

 private:
  PFS_key_socket_id m_key;
};

class PFS_index_socket_instances_by_ip_port
    : public PFS_index_socket_instances {
 public:
  PFS_index_socket_instances_by_ip_port()
      : PFS_index_socket_instances(&m_key_1, &m_key_2),
        m_key_1("IP"),
        m_key_2("PORT") {}

  ~PFS_index_socket_instances_by_ip_port() override = default;

  bool match(const PFS_socket *pfs) override;

 private:
  PFS_key_ip m_key_1;
  PFS_key_port m_key_2;
};

/** Table PERFORMANCE_SCHEMA.SOCKET_INSTANCES. */
class table_socket_instances : public PFS_engine_table {
 public:
  /** Table share */
  static PFS_engine_table_share m_share;
  static PFS_engine_table *create(PFS_engine_table_share *);
  static ha_rows get_row_count();

  void reset_position() override;

  int rnd_next() override;
  int rnd_pos(const void *pos) override;

  int index_init(uint idx, bool sorted) override;
  int index_next() override;

 private:
  int read_row_values(TABLE *table, unsigned char *buf, Field **fields,
                      bool read_all) override;
  table_socket_instances();

 public:
  ~table_socket_instances() override = default;

 protected:
  int make_row(PFS_socket *pfs);

  /** Table share lock. */
  static THR_LOCK m_table_lock;
  /** Table definition. */
  static Plugin_table m_table_def;

  /** Current row. */
  row_socket_instances m_row;
  /** Current position. */
  PFS_simple_index m_pos;
  /** Next position. */
  PFS_simple_index m_next_pos;

  PFS_index_socket_instances *m_opened_index;
};

/** @} */
#endif
