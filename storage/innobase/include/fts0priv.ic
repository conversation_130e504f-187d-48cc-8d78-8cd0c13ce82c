/*****************************************************************************

Copyright (c) 2011, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License, version 2.0, as published by the
Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0,
for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

*****************************************************************************/

/** @file include/fts0priv.ic
 Full text search internal header file

 Created 2011/11/12 Sunny Bains
 ***********************************************************************/

/** Write the table id to the given buffer (including final NUL). Buffer must be
 at least FTS_AUX_MIN_TABLE_ID_LENGTH bytes long.
 @param[in]     id      a table/index id
 @param[in]     str     buffer to write the id to
 @return number of bytes written */
static inline int fts_write_object_id(ib_id_t id, char *str) {
  return (sprintf(str, UINT64PFx, id));
}

static inline bool fts_read_object_id(ib_id_t *id, const char *str) {
  /* NOTE: this func doesn't care about whether current table
  is set with HEX_NAME, the user of the id read here will check
  if the id is HEX or DEC and do the right thing with it. */
  return (sscanf(str, UINT64PFx, id) == 1);
}

/** Compare two fts_trx_table_t instances.
 @return < 0 if n1 < n2, 0 if n1 == n2, > 0 if n1 > n2 */
static inline int fts_trx_table_cmp(const void *p1, /*!< in: id1 */
                                    const void *p2) /*!< in: id2 */
{
  const dict_table_t *table1 =
      (*static_cast<const fts_trx_table_t *const *>(p1))->table;

  const dict_table_t *table2 =
      (*static_cast<const fts_trx_table_t *const *>(p2))->table;

  return ((table1->id > table2->id) ? 1 : (table1->id == table2->id) ? 0 : -1);
}

/** Compare a table id with a fts_trx_table_t table id.
 @return < 0 if n1 < n2, 0 if n1 == n2,> 0 if n1 > n2 */
static inline int fts_trx_table_id_cmp(const void *p1, /*!< in: id1 */
                                       const void *p2) /*!< in: id2 */
{
  const uintmax_t *table_id = static_cast<const uintmax_t *>(p1);
  const dict_table_t *table2 =
      (*static_cast<const fts_trx_table_t *const *>(p2))->table;

  return ((*table_id > table2->id) ? 1 : (*table_id == table2->id) ? 0 : -1);
}
