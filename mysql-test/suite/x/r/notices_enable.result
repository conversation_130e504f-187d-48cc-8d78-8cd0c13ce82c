
###
### General setup for all scenarios
###

### Scenario 1: Enable group replication notifications
### Expected notices:
### - Myself: N/A
##########################################################################

command ok

### Scenario 2: Execute SQL queries that generate warning notices
### Expected notices:
### - Myself: Warnings
##########################################################################

##
## Check with dropping db which doesn't exists
RUN drop database if exists mysqltest

0 rows affected
Warnings generated:
NOTE | 1008 | Can't drop database 'mysqltest'; database doesn't exist
Got expected warning: 1008 

##
## Check with dropping table which doesn't exists
RUN drop table if exists test.mysqltest

0 rows affected
Warnings generated:
NOTE | 1051 | Unknown table 'test.mysqltest'
Got expected warning: 1051 

##
## Check with show variables like version
RUN show variables like 'versio'
Variable_name	Value
0 rows affected

##
## Check with dropping procedure which doesn't exists
RUN drop procedure if exists test.test_invalid

0 rows affected
Warnings generated:
NOTE | 1305 | PROCEDURE test.test_invalid does not exist
Got expected warning: 1305 

##
## Check with dropping function which doesn't exists
RUN drop function if exists test.test_signal_func

0 rows affected
Warnings generated:
NOTE | 1305 | FUNCTION test.test_signal_func does not exist
Got expected warning: 1305 

##
## Check with dropping view which doesn't exists
RUN drop view if exists test.v1

0 rows affected
Warnings generated:
NOTE | 1051 | Unknown table 'test.v1'
Got expected warning: 1051 

##
## Out of range values for INT,UNSIGN INT
RUN create table test.t1 (c1 INT, c2 INT UNSIGNED)

0 rows affected
RUN insert ignore into test.t1 values ('21474836461','21474836461')

1 rows affected
Warnings generated:
WARNING | 1264 | Out of range value for column 'c1' at row 1
WARNING | 1264 | Out of range value for column 'c2' at row 1
Got expected warning: 1264 
RUN insert ignore into test.t1 values ('-21474836461','-21474836461')

1 rows affected
Warnings generated:
WARNING | 1264 | Out of range value for column 'c1' at row 1
WARNING | 1264 | Out of range value for column 'c2' at row 1
Got expected warning: 1264 
RUN drop table test.t1

0 rows affected

##
## Checking double data type
RUN create table test.t1 (grp int, a bigint unsigned, c char(10) not null, d char(10) not null, index grp_idx(grp))

0 rows affected
RUN insert into test.t1 values (1,1,"a","a")

1 rows affected
RUN insert into test.t1 values (2,2,"b","a")

1 rows affected
RUN insert into test.t1 values (2,3,"c","b")

1 rows affected
RUN insert into test.t1 values (3,4,"E","a")

1 rows affected
RUN insert into test.t1 values (3,5,"C","b")

1 rows affected
RUN insert into test.t1 values (3,6,"D","b")

1 rows affected
RUN insert into test.t1 values (3,7,"d","d")

1 rows affected
RUN insert into test.t1 values (3,8,"d","d")

1 rows affected
RUN insert into test.t1 values (3,9,"D","c")

1 rows affected
RUN select grp,group_concat(a order by a,d-ascii(c)-a) from test.t1 group by grp order by grp
grp	group_concat(a order by a,d-ascii(c)-a)
1	1
2	2,3
3	4,5,6,7,8,9
0 rows affected
Warnings generated:
WARNING | 1292 | Truncated incorrect DOUBLE value: 'a'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'a'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'b'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'a'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'b'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'b'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'd'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'd'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'c'
Got expected warning: 1292 
RUN select grp,group_concat(a order by c-ascii(c),a) from test.t1 group by grp order by grp
grp	group_concat(a order by c-ascii(c),a)
1	1
2	3,2
3	7,8,4,6,9,5
0 rows affected
Warnings generated:
WARNING | 1292 | Truncated incorrect DOUBLE value: 'a'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'b'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'c'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'E'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'C'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'D'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'd'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'd'
WARNING | 1292 | Truncated incorrect DOUBLE value: 'D'
Got expected warning: 1292 
RUN set group_concat_max_len = 4

0 rows affected
RUN select grp,group_concat(c order by c) from test.t1 group by grp order by grp
grp	group_concat(c order by c)
1	a
2	b,c
3	C,D,
0 rows affected
Warnings generated:
WARNING | 1260 | Row 6 was cut by GROUP_CONCAT()
Got expected warning: 1260 
RUN drop table test.t1

0 rows affected

##
## Checking character data type
## Error is returned instead of warning for insert operation with xplugin
RUN SET sql_mode = 'NO_ENGINE_SUBSTITUTION'

0 rows affected
RUN prepare stmt from 'create table test.t1 (a varchar(10) character set utf8mb3)'

0 rows affected
Statement prepared
Warnings generated:
WARNING | 1287 | 'utf8mb3' is deprecated and will be removed in a future release. Please use utf8mb4 instead
Got expected warning: 1287 
RUN execute stmt

0 rows affected
RUN insert into test.t1 (a) values (repeat('a', 20))

1 rows affected
Warnings generated:
WARNING | 1265 | Data truncated for column 'a' at row 1
Got expected warning: 1265 
RUN select length(a) from test.t1
length(a)
10
0 rows affected
RUN drop table test.t1

0 rows affected
RUN execute stmt

0 rows affected

##
## Checking directly from IS tables
RUN show session variables like 'max_join_size'
Variable_name	Value
max_join_size	18446744073709551615
0 rows affected
RUN show session variables like 'concurrent_insert'
Variable_name	Value
concurrent_insert	AUTO
0 rows affected
RUN show session variables like 'default_storage_engine'
Variable_name	Value
default_storage_engine	InnoDB
0 rows affected

##
## Checking errors and warnings with duplicate table
RUN SET SQL_WARNINGS=1

0 rows affected

##
## Checking errors and warnings with duplicate table names
RUN create table test.dup (a int)

0 rows affected
RUN create table test.dup (a int)
While executing create table test.dup (a int):
Got expected error: Table 'dup' already exists (code 1050)
RUN show count(*) errors
@@session.error_count
1
0 rows affected
RUN show errors
Level	Code	Message
Error	1050	Table 'dup' already exists
0 rows affected
RUN drop table test.dup

0 rows affected

##
## Checking creating table with if not exists
RUN flush status

0 rows affected
RUN use test

0 rows affected
RUN create table t1 (a int not null, b int, primary key (a))
While executing create table t1 (a int not null, b int, primary key (a)):
Got expected error: Table 't1' already exists (code 1050)
RUN create table if not exists t1 select 2

0 rows affected
Warnings generated:
NOTE | 1050 | Table 't1' already exists
Got expected warning: 1050 
RUN select * from t1
a
0 rows affected
RUN create table if not exists t1 select 3 as 'a',4 as 'b'

0 rows affected
Warnings generated:
NOTE | 1050 | Table 't1' already exists
Got expected warning: 1050 
RUN show status like "Opened_tables"
Variable_name	Value
Opened_tables	1
0 rows affected
RUN select * from t1
a
0 rows affected
RUN drop table t1

0 rows affected

##
## Try dropping multiple tables with if exists and check multiple warnings
RUN use test

0 rows affected
RUN drop tables if exists t1, t2

0 rows affected
Warnings generated:
NOTE | 1051 | Unknown table 'test.t1'
NOTE | 1051 | Unknown table 'test.t2'
Got expected warning: 1051 

##
## Try checking many warnings
RUN drop tables if exists t1, t2,t3,t4,t5,t6,t7,t8,t9,t10,t11,t12,t13,t14,t15,t16,t17,t18,t19,t20,t21,t22,t23,t24,t25,t26,t27,t28,t29,t30,a1, a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14,a15,a16,a17,a18,a19,a20,a21,a22,a23,a24,a25,a26,a27,a28,a29,a30,aa1, aa2,aa3,aa4,aa5,aa6,aa7,aa8,aa9,aa10,aa11,aa12,aa13,aa14,aa15,aa16,aa17,aa18,aa19,aa20,aa21,aa22,aa23,aa24,aa25,aa26,aa27,aa28,aa29,aa30,aaaa1, aaaa2,aaaa3,aaaa4,aaaa5,aaaa6,aaaa7,aaaa8,aaaa9,aaaa10,aaaa11,aaaa12,aaaa13,aaaa14,aaaa15,aaaa16,aaaa17,aaaa18,aaaa19,aaaa20,aaaa21,aaaa22,aaaa23,aaaa24,aaaa25,aaaa26,aaaa27,aaaa28,aaaa29,aaaa30

0 rows affected
Warnings generated:
NOTE | 1051 | Unknown table 'test.t1'
NOTE | 1051 | Unknown table 'test.t2'
NOTE | 1051 | Unknown table 'test.t3'
NOTE | 1051 | Unknown table 'test.t4'
NOTE | 1051 | Unknown table 'test.t5'
NOTE | 1051 | Unknown table 'test.t6'
NOTE | 1051 | Unknown table 'test.t7'
NOTE | 1051 | Unknown table 'test.t8'
NOTE | 1051 | Unknown table 'test.t9'
NOTE | 1051 | Unknown table 'test.t10'
NOTE | 1051 | Unknown table 'test.t11'
NOTE | 1051 | Unknown table 'test.t12'
NOTE | 1051 | Unknown table 'test.t13'
NOTE | 1051 | Unknown table 'test.t14'
NOTE | 1051 | Unknown table 'test.t15'
NOTE | 1051 | Unknown table 'test.t16'
NOTE | 1051 | Unknown table 'test.t17'
NOTE | 1051 | Unknown table 'test.t18'
NOTE | 1051 | Unknown table 'test.t19'
NOTE | 1051 | Unknown table 'test.t20'
NOTE | 1051 | Unknown table 'test.t21'
NOTE | 1051 | Unknown table 'test.t22'
NOTE | 1051 | Unknown table 'test.t23'
NOTE | 1051 | Unknown table 'test.t24'
NOTE | 1051 | Unknown table 'test.t25'
NOTE | 1051 | Unknown table 'test.t26'
NOTE | 1051 | Unknown table 'test.t27'
NOTE | 1051 | Unknown table 'test.t28'
NOTE | 1051 | Unknown table 'test.t29'
NOTE | 1051 | Unknown table 'test.t30'
NOTE | 1051 | Unknown table 'test.a1'
NOTE | 1051 | Unknown table 'test.a2'
NOTE | 1051 | Unknown table 'test.a3'
NOTE | 1051 | Unknown table 'test.a4'
NOTE | 1051 | Unknown table 'test.a5'
NOTE | 1051 | Unknown table 'test.a6'
NOTE | 1051 | Unknown table 'test.a7'
NOTE | 1051 | Unknown table 'test.a8'
NOTE | 1051 | Unknown table 'test.a9'
NOTE | 1051 | Unknown table 'test.a10'
NOTE | 1051 | Unknown table 'test.a11'
NOTE | 1051 | Unknown table 'test.a12'
NOTE | 1051 | Unknown table 'test.a13'
NOTE | 1051 | Unknown table 'test.a14'
NOTE | 1051 | Unknown table 'test.a15'
NOTE | 1051 | Unknown table 'test.a16'
NOTE | 1051 | Unknown table 'test.a17'
NOTE | 1051 | Unknown table 'test.a18'
NOTE | 1051 | Unknown table 'test.a19'
NOTE | 1051 | Unknown table 'test.a20'
NOTE | 1051 | Unknown table 'test.a21'
NOTE | 1051 | Unknown table 'test.a22'
NOTE | 1051 | Unknown table 'test.a23'
NOTE | 1051 | Unknown table 'test.a24'
NOTE | 1051 | Unknown table 'test.a25'
NOTE | 1051 | Unknown table 'test.a26'
NOTE | 1051 | Unknown table 'test.a27'
NOTE | 1051 | Unknown table 'test.a28'
NOTE | 1051 | Unknown table 'test.a29'
NOTE | 1051 | Unknown table 'test.a30'
NOTE | 1051 | Unknown table 'test.aa1'
NOTE | 1051 | Unknown table 'test.aa2'
NOTE | 1051 | Unknown table 'test.aa3'
NOTE | 1051 | Unknown table 'test.aa4'
NOTE | 1051 | Unknown table 'test.aa5'
NOTE | 1051 | Unknown table 'test.aa6'
NOTE | 1051 | Unknown table 'test.aa7'
NOTE | 1051 | Unknown table 'test.aa8'
NOTE | 1051 | Unknown table 'test.aa9'
NOTE | 1051 | Unknown table 'test.aa10'
NOTE | 1051 | Unknown table 'test.aa11'
NOTE | 1051 | Unknown table 'test.aa12'
NOTE | 1051 | Unknown table 'test.aa13'
NOTE | 1051 | Unknown table 'test.aa14'
NOTE | 1051 | Unknown table 'test.aa15'
NOTE | 1051 | Unknown table 'test.aa16'
NOTE | 1051 | Unknown table 'test.aa17'
NOTE | 1051 | Unknown table 'test.aa18'
NOTE | 1051 | Unknown table 'test.aa19'
NOTE | 1051 | Unknown table 'test.aa20'
NOTE | 1051 | Unknown table 'test.aa21'
NOTE | 1051 | Unknown table 'test.aa22'
NOTE | 1051 | Unknown table 'test.aa23'
NOTE | 1051 | Unknown table 'test.aa24'
NOTE | 1051 | Unknown table 'test.aa25'
NOTE | 1051 | Unknown table 'test.aa26'
NOTE | 1051 | Unknown table 'test.aa27'
NOTE | 1051 | Unknown table 'test.aa28'
NOTE | 1051 | Unknown table 'test.aa29'
NOTE | 1051 | Unknown table 'test.aa30'
NOTE | 1051 | Unknown table 'test.aaaa1'
NOTE | 1051 | Unknown table 'test.aaaa2'
NOTE | 1051 | Unknown table 'test.aaaa3'
NOTE | 1051 | Unknown table 'test.aaaa4'
NOTE | 1051 | Unknown table 'test.aaaa5'
NOTE | 1051 | Unknown table 'test.aaaa6'
NOTE | 1051 | Unknown table 'test.aaaa7'
NOTE | 1051 | Unknown table 'test.aaaa8'
NOTE | 1051 | Unknown table 'test.aaaa9'
NOTE | 1051 | Unknown table 'test.aaaa10'
NOTE | 1051 | Unknown table 'test.aaaa11'
NOTE | 1051 | Unknown table 'test.aaaa12'
NOTE | 1051 | Unknown table 'test.aaaa13'
NOTE | 1051 | Unknown table 'test.aaaa14'
NOTE | 1051 | Unknown table 'test.aaaa15'
NOTE | 1051 | Unknown table 'test.aaaa16'
NOTE | 1051 | Unknown table 'test.aaaa17'
NOTE | 1051 | Unknown table 'test.aaaa18'
NOTE | 1051 | Unknown table 'test.aaaa19'
NOTE | 1051 | Unknown table 'test.aaaa20'
NOTE | 1051 | Unknown table 'test.aaaa21'
NOTE | 1051 | Unknown table 'test.aaaa22'
NOTE | 1051 | Unknown table 'test.aaaa23'
NOTE | 1051 | Unknown table 'test.aaaa24'
NOTE | 1051 | Unknown table 'test.aaaa25'
NOTE | 1051 | Unknown table 'test.aaaa26'
NOTE | 1051 | Unknown table 'test.aaaa27'
NOTE | 1051 | Unknown table 'test.aaaa28'
NOTE | 1051 | Unknown table 'test.aaaa29'
NOTE | 1051 | Unknown table 'test.aaaa30'
Got expected warning: 1051 

##
## Dropping same table with if exists and observe error/warnings
RUN drop tables if exists t1,t1
While executing drop tables if exists t1,t1:
Got expected error: Not unique table/alias: 't1' (code 1066)
RUN set max_error_count=65535

0 rows affected

##
## Check more than 64 warnings returned
RUN drop tables if exists t1, t2,t3,t4,t5,t6,t7,t8,t9,t10,t11,t12,t13,t14,t15,t16,t17,t18,t19,t20,t21,t22,t23,t24,t25,t26,t27,t28,t29,t30,a1, a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14,a15,a16,a17,a18,a19,a20,a21,a22,a23,a24,a25,a26,a27,a28,a29,a30,aa1, aa2,aa3,aa4,aa5,aa6,aa7,aa8,aa9,aa10,aa11,aa12,aa13,aa14,aa15,aa16,aa17,aa18,aa19,aa20,aa21,aa22,aa23,aa24,aa25,aa26,aa27,aa28,aa29,aa30,aaaa1, aaaa2,aaaa3,aaaa4,aaaa5,aaaa6,aaaa7,aaaa8,aaaa9,aaaa10,aaaa11,aaaa12,aaaa13,aaaa14,aaaa15,aaaa16,aaaa17,aaaa18,aaaa19,aaaa20,aaaa21,aaaa22,aaaa23,aaaa24,aaaa25,aaaa26,aaaa27,aaaa28,aaaa29,aaaa30,bb1, bb2,bb3,bb4,bb5,bb6,bb7,bb8,bb9,bb10,bb11,bb12,bb13,bb14,bb15,bb16,bb17,bb18,bb19,bb20,bb21,bb22,bb23,bb24,bb25,bb26,bb27,bb28,bb29,bb30,b1, b2,b3,b4,b5,b6,b7,b8,b9,b10,b11,b12,b13,b14,b15,b16,b17,b18,b19,b20,b21,b22,b23,b24,b25,b26,b27,b28,b29,b30

0 rows affected
Warnings generated:
NOTE | 1051 | Unknown table 'test.t1'
NOTE | 1051 | Unknown table 'test.t2'
NOTE | 1051 | Unknown table 'test.t3'
NOTE | 1051 | Unknown table 'test.t4'
NOTE | 1051 | Unknown table 'test.t5'
NOTE | 1051 | Unknown table 'test.t6'
NOTE | 1051 | Unknown table 'test.t7'
NOTE | 1051 | Unknown table 'test.t8'
NOTE | 1051 | Unknown table 'test.t9'
NOTE | 1051 | Unknown table 'test.t10'
NOTE | 1051 | Unknown table 'test.t11'
NOTE | 1051 | Unknown table 'test.t12'
NOTE | 1051 | Unknown table 'test.t13'
NOTE | 1051 | Unknown table 'test.t14'
NOTE | 1051 | Unknown table 'test.t15'
NOTE | 1051 | Unknown table 'test.t16'
NOTE | 1051 | Unknown table 'test.t17'
NOTE | 1051 | Unknown table 'test.t18'
NOTE | 1051 | Unknown table 'test.t19'
NOTE | 1051 | Unknown table 'test.t20'
NOTE | 1051 | Unknown table 'test.t21'
NOTE | 1051 | Unknown table 'test.t22'
NOTE | 1051 | Unknown table 'test.t23'
NOTE | 1051 | Unknown table 'test.t24'
NOTE | 1051 | Unknown table 'test.t25'
NOTE | 1051 | Unknown table 'test.t26'
NOTE | 1051 | Unknown table 'test.t27'
NOTE | 1051 | Unknown table 'test.t28'
NOTE | 1051 | Unknown table 'test.t29'
NOTE | 1051 | Unknown table 'test.t30'
NOTE | 1051 | Unknown table 'test.a1'
NOTE | 1051 | Unknown table 'test.a2'
NOTE | 1051 | Unknown table 'test.a3'
NOTE | 1051 | Unknown table 'test.a4'
NOTE | 1051 | Unknown table 'test.a5'
NOTE | 1051 | Unknown table 'test.a6'
NOTE | 1051 | Unknown table 'test.a7'
NOTE | 1051 | Unknown table 'test.a8'
NOTE | 1051 | Unknown table 'test.a9'
NOTE | 1051 | Unknown table 'test.a10'
NOTE | 1051 | Unknown table 'test.a11'
NOTE | 1051 | Unknown table 'test.a12'
NOTE | 1051 | Unknown table 'test.a13'
NOTE | 1051 | Unknown table 'test.a14'
NOTE | 1051 | Unknown table 'test.a15'
NOTE | 1051 | Unknown table 'test.a16'
NOTE | 1051 | Unknown table 'test.a17'
NOTE | 1051 | Unknown table 'test.a18'
NOTE | 1051 | Unknown table 'test.a19'
NOTE | 1051 | Unknown table 'test.a20'
NOTE | 1051 | Unknown table 'test.a21'
NOTE | 1051 | Unknown table 'test.a22'
NOTE | 1051 | Unknown table 'test.a23'
NOTE | 1051 | Unknown table 'test.a24'
NOTE | 1051 | Unknown table 'test.a25'
NOTE | 1051 | Unknown table 'test.a26'
NOTE | 1051 | Unknown table 'test.a27'
NOTE | 1051 | Unknown table 'test.a28'
NOTE | 1051 | Unknown table 'test.a29'
NOTE | 1051 | Unknown table 'test.a30'
NOTE | 1051 | Unknown table 'test.aa1'
NOTE | 1051 | Unknown table 'test.aa2'
NOTE | 1051 | Unknown table 'test.aa3'
NOTE | 1051 | Unknown table 'test.aa4'
NOTE | 1051 | Unknown table 'test.aa5'
NOTE | 1051 | Unknown table 'test.aa6'
NOTE | 1051 | Unknown table 'test.aa7'
NOTE | 1051 | Unknown table 'test.aa8'
NOTE | 1051 | Unknown table 'test.aa9'
NOTE | 1051 | Unknown table 'test.aa10'
NOTE | 1051 | Unknown table 'test.aa11'
NOTE | 1051 | Unknown table 'test.aa12'
NOTE | 1051 | Unknown table 'test.aa13'
NOTE | 1051 | Unknown table 'test.aa14'
NOTE | 1051 | Unknown table 'test.aa15'
NOTE | 1051 | Unknown table 'test.aa16'
NOTE | 1051 | Unknown table 'test.aa17'
NOTE | 1051 | Unknown table 'test.aa18'
NOTE | 1051 | Unknown table 'test.aa19'
NOTE | 1051 | Unknown table 'test.aa20'
NOTE | 1051 | Unknown table 'test.aa21'
NOTE | 1051 | Unknown table 'test.aa22'
NOTE | 1051 | Unknown table 'test.aa23'
NOTE | 1051 | Unknown table 'test.aa24'
NOTE | 1051 | Unknown table 'test.aa25'
NOTE | 1051 | Unknown table 'test.aa26'
NOTE | 1051 | Unknown table 'test.aa27'
NOTE | 1051 | Unknown table 'test.aa28'
NOTE | 1051 | Unknown table 'test.aa29'
NOTE | 1051 | Unknown table 'test.aa30'
NOTE | 1051 | Unknown table 'test.aaaa1'
NOTE | 1051 | Unknown table 'test.aaaa2'
NOTE | 1051 | Unknown table 'test.aaaa3'
NOTE | 1051 | Unknown table 'test.aaaa4'
NOTE | 1051 | Unknown table 'test.aaaa5'
NOTE | 1051 | Unknown table 'test.aaaa6'
NOTE | 1051 | Unknown table 'test.aaaa7'
NOTE | 1051 | Unknown table 'test.aaaa8'
NOTE | 1051 | Unknown table 'test.aaaa9'
NOTE | 1051 | Unknown table 'test.aaaa10'
NOTE | 1051 | Unknown table 'test.aaaa11'
NOTE | 1051 | Unknown table 'test.aaaa12'
NOTE | 1051 | Unknown table 'test.aaaa13'
NOTE | 1051 | Unknown table 'test.aaaa14'
NOTE | 1051 | Unknown table 'test.aaaa15'
NOTE | 1051 | Unknown table 'test.aaaa16'
NOTE | 1051 | Unknown table 'test.aaaa17'
NOTE | 1051 | Unknown table 'test.aaaa18'
NOTE | 1051 | Unknown table 'test.aaaa19'
NOTE | 1051 | Unknown table 'test.aaaa20'
NOTE | 1051 | Unknown table 'test.aaaa21'
NOTE | 1051 | Unknown table 'test.aaaa22'
NOTE | 1051 | Unknown table 'test.aaaa23'
NOTE | 1051 | Unknown table 'test.aaaa24'
NOTE | 1051 | Unknown table 'test.aaaa25'
NOTE | 1051 | Unknown table 'test.aaaa26'
NOTE | 1051 | Unknown table 'test.aaaa27'
NOTE | 1051 | Unknown table 'test.aaaa28'
NOTE | 1051 | Unknown table 'test.aaaa29'
NOTE | 1051 | Unknown table 'test.aaaa30'
NOTE | 1051 | Unknown table 'test.bb1'
NOTE | 1051 | Unknown table 'test.bb2'
NOTE | 1051 | Unknown table 'test.bb3'
NOTE | 1051 | Unknown table 'test.bb4'
NOTE | 1051 | Unknown table 'test.bb5'
NOTE | 1051 | Unknown table 'test.bb6'
NOTE | 1051 | Unknown table 'test.bb7'
NOTE | 1051 | Unknown table 'test.bb8'
NOTE | 1051 | Unknown table 'test.bb9'
NOTE | 1051 | Unknown table 'test.bb10'
NOTE | 1051 | Unknown table 'test.bb11'
NOTE | 1051 | Unknown table 'test.bb12'
NOTE | 1051 | Unknown table 'test.bb13'
NOTE | 1051 | Unknown table 'test.bb14'
NOTE | 1051 | Unknown table 'test.bb15'
NOTE | 1051 | Unknown table 'test.bb16'
NOTE | 1051 | Unknown table 'test.bb17'
NOTE | 1051 | Unknown table 'test.bb18'
NOTE | 1051 | Unknown table 'test.bb19'
NOTE | 1051 | Unknown table 'test.bb20'
NOTE | 1051 | Unknown table 'test.bb21'
NOTE | 1051 | Unknown table 'test.bb22'
NOTE | 1051 | Unknown table 'test.bb23'
NOTE | 1051 | Unknown table 'test.bb24'
NOTE | 1051 | Unknown table 'test.bb25'
NOTE | 1051 | Unknown table 'test.bb26'
NOTE | 1051 | Unknown table 'test.bb27'
NOTE | 1051 | Unknown table 'test.bb28'
NOTE | 1051 | Unknown table 'test.bb29'
NOTE | 1051 | Unknown table 'test.bb30'
NOTE | 1051 | Unknown table 'test.b1'
NOTE | 1051 | Unknown table 'test.b2'
NOTE | 1051 | Unknown table 'test.b3'
NOTE | 1051 | Unknown table 'test.b4'
NOTE | 1051 | Unknown table 'test.b5'
NOTE | 1051 | Unknown table 'test.b6'
NOTE | 1051 | Unknown table 'test.b7'
NOTE | 1051 | Unknown table 'test.b8'
NOTE | 1051 | Unknown table 'test.b9'
NOTE | 1051 | Unknown table 'test.b10'
NOTE | 1051 | Unknown table 'test.b11'
NOTE | 1051 | Unknown table 'test.b12'
NOTE | 1051 | Unknown table 'test.b13'
NOTE | 1051 | Unknown table 'test.b14'
NOTE | 1051 | Unknown table 'test.b15'
NOTE | 1051 | Unknown table 'test.b16'
NOTE | 1051 | Unknown table 'test.b17'
NOTE | 1051 | Unknown table 'test.b18'
NOTE | 1051 | Unknown table 'test.b19'
NOTE | 1051 | Unknown table 'test.b20'
NOTE | 1051 | Unknown table 'test.b21'
NOTE | 1051 | Unknown table 'test.b22'
NOTE | 1051 | Unknown table 'test.b23'
NOTE | 1051 | Unknown table 'test.b24'
NOTE | 1051 | Unknown table 'test.b25'
NOTE | 1051 | Unknown table 'test.b26'
NOTE | 1051 | Unknown table 'test.b27'
NOTE | 1051 | Unknown table 'test.b28'
NOTE | 1051 | Unknown table 'test.b29'
NOTE | 1051 | Unknown table 'test.b30'
Got expected warning: 1051 

##
## Check no warnings based on max_error_count parameter set
RUN set max_error_count=0

0 rows affected
RUN drop tables if exists t1,t2

0 rows affected
RUN set max_error_count=-64

0 rows affected
RUN show variables like 'max_error_count'
Variable_name	Value
max_error_count	0
0 rows affected
RUN set max_error_count=64

0 rows affected

##
## Checking creating temporary table when normal and temp table exists
RUN use test

0 rows affected
RUN create table if not exists t1 (a int) select 2 as a

1 rows affected
Records: 1  Duplicates: 0  Warnings: 0
RUN create temporary table t1 (a int) select 2 as a

1 rows affected
Records: 1  Duplicates: 0  Warnings: 0
RUN select * from t1
a
2
0 rows affected
RUN create temporary table if not exists t1 (a int) select 2 as a

0 rows affected
Warnings generated:
NOTE | 1050 | Table 't1' already exists
Got expected warning: 1050 
RUN select * from t1
a
2
0 rows affected
RUN drop temporary table t1

0 rows affected
RUN drop table t1

0 rows affected

##
## Checking data truncation warnings for default values
RUN use test

0 rows affected
RUN CREATE TABLE t1 SELECT 0.123456789012345678901234567890123456 AS f1

1 rows affected
Records: 1  Duplicates: 0  Warnings: 1
Warnings generated:
NOTE | 1265 | Data truncated for column 'f1' at row 1
Got expected warning: 1265 
RUN drop table t1

0 rows affected

##
## Checking data truncation warnings while creating table with expression
RUN use test

0 rows affected
RUN create table t1 as select 5.05000000000000000000000123456789 + 1

1 rows affected
Records: 1  Duplicates: 0  Warnings: 1
Warnings generated:
NOTE | 1265 | Data truncated for column '5.05000000000000000000000123456789 + 1' at row 1
Got expected warning: 1265 
RUN drop table t1

0 rows affected

##
## Checking warnings with insert and select operations
## Below insert into t1 returns 17775 warnings,checking only limited warnings as test takes more time to render the warnings
RUN use test

0 rows affected
RUN create table t2 (a char(1))

0 rows affected
RUN insert into t2 values ('0'),('1'),('2'),('3'),('4'),('5'),('6'),('7')

8 rows affected
Records: 8  Duplicates: 0  Warnings: 0
RUN insert into t2 values ('8'),('9'),('A'),('B'),('C'),('D'),('E'),('F')

8 rows affected
Records: 8  Duplicates: 0  Warnings: 0
RUN create table t1 (a varchar(2) character set cp932) engine=myisam

0 rows affected

##
## Checking for more than default 64 warnings
RUN set max_error_count=500

0 rows affected
RUN insert into t1 select unhex(concat(t24.a, t23.a, t22.a, t21.a)) from t2 t21, t2 t22, t2 t23, t2 t24
  ORDER BY t24.a, t23.a, t22.a, t21.a

65536 rows affected
Records: 65536  Duplicates: 0  Warnings: 17775
Warnings generated:
WARNING | 1366 | Incorrect string value: '\x80' for column 'a' at row 129
WARNING | 1366 | Incorrect string value: '\x81' for column 'a' at row 130
WARNING | 1366 | Incorrect string value: '\x82' for column 'a' at row 131
WARNING | 1366 | Incorrect string value: '\x83' for column 'a' at row 132
WARNING | 1366 | Incorrect string value: '\x84' for column 'a' at row 133
WARNING | 1366 | Incorrect string value: '\x85' for column 'a' at row 134
WARNING | 1366 | Incorrect string value: '\x86' for column 'a' at row 135
WARNING | 1366 | Incorrect string value: '\x87' for column 'a' at row 136
WARNING | 1366 | Incorrect string value: '\x88' for column 'a' at row 137
WARNING | 1366 | Incorrect string value: '\x89' for column 'a' at row 138
WARNING | 1366 | Incorrect string value: '\x8A' for column 'a' at row 139
WARNING | 1366 | Incorrect string value: '\x8B' for column 'a' at row 140
WARNING | 1366 | Incorrect string value: '\x8C' for column 'a' at row 141
WARNING | 1366 | Incorrect string value: '\x8D' for column 'a' at row 142
WARNING | 1366 | Incorrect string value: '\x8E' for column 'a' at row 143
WARNING | 1366 | Incorrect string value: '\x8F' for column 'a' at row 144
WARNING | 1366 | Incorrect string value: '\x90' for column 'a' at row 145
WARNING | 1366 | Incorrect string value: '\x91' for column 'a' at row 146
WARNING | 1366 | Incorrect string value: '\x92' for column 'a' at row 147
WARNING | 1366 | Incorrect string value: '\x93' for column 'a' at row 148
WARNING | 1366 | Incorrect string value: '\x94' for column 'a' at row 149
WARNING | 1366 | Incorrect string value: '\x95' for column 'a' at row 150
WARNING | 1366 | Incorrect string value: '\x96' for column 'a' at row 151
WARNING | 1366 | Incorrect string value: '\x97' for column 'a' at row 152
WARNING | 1366 | Incorrect string value: '\x98' for column 'a' at row 153
WARNING | 1366 | Incorrect string value: '\x99' for column 'a' at row 154
WARNING | 1366 | Incorrect string value: '\x9A' for column 'a' at row 155
WARNING | 1366 | Incorrect string value: '\x9B' for column 'a' at row 156
WARNING | 1366 | Incorrect string value: '\x9C' for column 'a' at row 157
WARNING | 1366 | Incorrect string value: '\x9D' for column 'a' at row 158
WARNING | 1366 | Incorrect string value: '\x9E' for column 'a' at row 159
WARNING | 1366 | Incorrect string value: '\x9F' for column 'a' at row 160
WARNING | 1366 | Incorrect string value: '\xA0' for column 'a' at row 161
WARNING | 1366 | Incorrect string value: '\xE0' for column 'a' at row 225
WARNING | 1366 | Incorrect string value: '\xE1' for column 'a' at row 226
WARNING | 1366 | Incorrect string value: '\xE2' for column 'a' at row 227
WARNING | 1366 | Incorrect string value: '\xE3' for column 'a' at row 228
WARNING | 1366 | Incorrect string value: '\xE4' for column 'a' at row 229
WARNING | 1366 | Incorrect string value: '\xE5' for column 'a' at row 230
WARNING | 1366 | Incorrect string value: '\xE6' for column 'a' at row 231
WARNING | 1366 | Incorrect string value: '\xE7' for column 'a' at row 232
WARNING | 1366 | Incorrect string value: '\xE8' for column 'a' at row 233
WARNING | 1366 | Incorrect string value: '\xE9' for column 'a' at row 234
WARNING | 1366 | Incorrect string value: '\xEA' for column 'a' at row 235
WARNING | 1366 | Incorrect string value: '\xEB' for column 'a' at row 236
WARNING | 1366 | Incorrect string value: '\xEC' for column 'a' at row 237
WARNING | 1366 | Incorrect string value: '\xED' for column 'a' at row 238
WARNING | 1366 | Incorrect string value: '\xEE' for column 'a' at row 239
WARNING | 1366 | Incorrect string value: '\xEF' for column 'a' at row 240
WARNING | 1366 | Incorrect string value: '\xF0' for column 'a' at row 241
WARNING | 1366 | Incorrect string value: '\xF1' for column 'a' at row 242
WARNING | 1366 | Incorrect string value: '\xF2' for column 'a' at row 243
WARNING | 1366 | Incorrect string value: '\xF3' for column 'a' at row 244
WARNING | 1366 | Incorrect string value: '\xF4' for column 'a' at row 245
WARNING | 1366 | Incorrect string value: '\xF5' for column 'a' at row 246
WARNING | 1366 | Incorrect string value: '\xF6' for column 'a' at row 247
WARNING | 1366 | Incorrect string value: '\xF7' for column 'a' at row 248
WARNING | 1366 | Incorrect string value: '\xF8' for column 'a' at row 249
WARNING | 1366 | Incorrect string value: '\xF9' for column 'a' at row 250
WARNING | 1366 | Incorrect string value: '\xFA' for column 'a' at row 251
WARNING | 1366 | Incorrect string value: '\xFB' for column 'a' at row 252
WARNING | 1366 | Incorrect string value: '\xFC' for column 'a' at row 253
WARNING | 1366 | Incorrect string value: '\xFD' for column 'a' at row 254
WARNING | 1366 | Incorrect string value: '\xFE' for column 'a' at row 255
WARNING | 1366 | Incorrect string value: '\xFF' for column 'a' at row 256
WARNING | 1366 | Incorrect string value: '\x80' for column 'a' at row 385
WARNING | 1366 | Incorrect string value: '\x81' for column 'a' at row 386
WARNING | 1366 | Incorrect string value: '\x82' for column 'a' at row 387
WARNING | 1366 | Incorrect string value: '\x83' for column 'a' at row 388
WARNING | 1366 | Incorrect string value: '\x84' for column 'a' at row 389
WARNING | 1366 | Incorrect string value: '\x85' for column 'a' at row 390
WARNING | 1366 | Incorrect string value: '\x86' for column 'a' at row 391
WARNING | 1366 | Incorrect string value: '\x87' for column 'a' at row 392
WARNING | 1366 | Incorrect string value: '\x88' for column 'a' at row 393
WARNING | 1366 | Incorrect string value: '\x89' for column 'a' at row 394
WARNING | 1366 | Incorrect string value: '\x8A' for column 'a' at row 395
WARNING | 1366 | Incorrect string value: '\x8B' for column 'a' at row 396
WARNING | 1366 | Incorrect string value: '\x8C' for column 'a' at row 397
WARNING | 1366 | Incorrect string value: '\x8D' for column 'a' at row 398
WARNING | 1366 | Incorrect string value: '\x8E' for column 'a' at row 399
WARNING | 1366 | Incorrect string value: '\x8F' for column 'a' at row 400
WARNING | 1366 | Incorrect string value: '\x90' for column 'a' at row 401
WARNING | 1366 | Incorrect string value: '\x91' for column 'a' at row 402
WARNING | 1366 | Incorrect string value: '\x92' for column 'a' at row 403
WARNING | 1366 | Incorrect string value: '\x93' for column 'a' at row 404
WARNING | 1366 | Incorrect string value: '\x94' for column 'a' at row 405
WARNING | 1366 | Incorrect string value: '\x95' for column 'a' at row 406
WARNING | 1366 | Incorrect string value: '\x96' for column 'a' at row 407
WARNING | 1366 | Incorrect string value: '\x97' for column 'a' at row 408
WARNING | 1366 | Incorrect string value: '\x98' for column 'a' at row 409
WARNING | 1366 | Incorrect string value: '\x99' for column 'a' at row 410
WARNING | 1366 | Incorrect string value: '\x9A' for column 'a' at row 411
WARNING | 1366 | Incorrect string value: '\x9B' for column 'a' at row 412
WARNING | 1366 | Incorrect string value: '\x9C' for column 'a' at row 413
WARNING | 1366 | Incorrect string value: '\x9D' for column 'a' at row 414
WARNING | 1366 | Incorrect string value: '\x9E' for column 'a' at row 415
WARNING | 1366 | Incorrect string value: '\x9F' for column 'a' at row 416
WARNING | 1366 | Incorrect string value: '\xA0' for column 'a' at row 417
WARNING | 1366 | Incorrect string value: '\xE0' for column 'a' at row 481
WARNING | 1366 | Incorrect string value: '\xE1' for column 'a' at row 482
WARNING | 1366 | Incorrect string value: '\xE2' for column 'a' at row 483
WARNING | 1366 | Incorrect string value: '\xE3' for column 'a' at row 484
WARNING | 1366 | Incorrect string value: '\xE4' for column 'a' at row 485
WARNING | 1366 | Incorrect string value: '\xE5' for column 'a' at row 486
WARNING | 1366 | Incorrect string value: '\xE6' for column 'a' at row 487
WARNING | 1366 | Incorrect string value: '\xE7' for column 'a' at row 488
WARNING | 1366 | Incorrect string value: '\xE8' for column 'a' at row 489
WARNING | 1366 | Incorrect string value: '\xE9' for column 'a' at row 490
WARNING | 1366 | Incorrect string value: '\xEA' for column 'a' at row 491
WARNING | 1366 | Incorrect string value: '\xEB' for column 'a' at row 492
WARNING | 1366 | Incorrect string value: '\xEC' for column 'a' at row 493
WARNING | 1366 | Incorrect string value: '\xED' for column 'a' at row 494
WARNING | 1366 | Incorrect string value: '\xEE' for column 'a' at row 495
WARNING | 1366 | Incorrect string value: '\xEF' for column 'a' at row 496
WARNING | 1366 | Incorrect string value: '\xF0' for column 'a' at row 497
WARNING | 1366 | Incorrect string value: '\xF1' for column 'a' at row 498
WARNING | 1366 | Incorrect string value: '\xF2' for column 'a' at row 499
WARNING | 1366 | Incorrect string value: '\xF3' for column 'a' at row 500
WARNING | 1366 | Incorrect string value: '\xF4' for column 'a' at row 501
WARNING | 1366 | Incorrect string value: '\xF5' for column 'a' at row 502
WARNING | 1366 | Incorrect string value: '\xF6' for column 'a' at row 503
WARNING | 1366 | Incorrect string value: '\xF7' for column 'a' at row 504
WARNING | 1366 | Incorrect string value: '\xF8' for column 'a' at row 505
WARNING | 1366 | Incorrect string value: '\xF9' for column 'a' at row 506
WARNING | 1366 | Incorrect string value: '\xFA' for column 'a' at row 507
WARNING | 1366 | Incorrect string value: '\xFB' for column 'a' at row 508
WARNING | 1366 | Incorrect string value: '\xFC' for column 'a' at row 509
WARNING | 1366 | Incorrect string value: '\xFD' for column 'a' at row 510
WARNING | 1366 | Incorrect string value: '\xFE' for column 'a' at row 511
WARNING | 1366 | Incorrect string value: '\xFF' for column 'a' at row 512
WARNING | 1366 | Incorrect string value: '\x80' for column 'a' at row 641
WARNING | 1366 | Incorrect string value: '\x81' for column 'a' at row 642
WARNING | 1366 | Incorrect string value: '\x82' for column 'a' at row 643
WARNING | 1366 | Incorrect string value: '\x83' for column 'a' at row 644
WARNING | 1366 | Incorrect string value: '\x84' for column 'a' at row 645
WARNING | 1366 | Incorrect string value: '\x85' for column 'a' at row 646
WARNING | 1366 | Incorrect string value: '\x86' for column 'a' at row 647
WARNING | 1366 | Incorrect string value: '\x87' for column 'a' at row 648
WARNING | 1366 | Incorrect string value: '\x88' for column 'a' at row 649
WARNING | 1366 | Incorrect string value: '\x89' for column 'a' at row 650
WARNING | 1366 | Incorrect string value: '\x8A' for column 'a' at row 651
WARNING | 1366 | Incorrect string value: '\x8B' for column 'a' at row 652
WARNING | 1366 | Incorrect string value: '\x8C' for column 'a' at row 653
WARNING | 1366 | Incorrect string value: '\x8D' for column 'a' at row 654
WARNING | 1366 | Incorrect string value: '\x8E' for column 'a' at row 655
WARNING | 1366 | Incorrect string value: '\x8F' for column 'a' at row 656
WARNING | 1366 | Incorrect string value: '\x90' for column 'a' at row 657
WARNING | 1366 | Incorrect string value: '\x91' for column 'a' at row 658
WARNING | 1366 | Incorrect string value: '\x92' for column 'a' at row 659
WARNING | 1366 | Incorrect string value: '\x93' for column 'a' at row 660
WARNING | 1366 | Incorrect string value: '\x94' for column 'a' at row 661
WARNING | 1366 | Incorrect string value: '\x95' for column 'a' at row 662
WARNING | 1366 | Incorrect string value: '\x96' for column 'a' at row 663
WARNING | 1366 | Incorrect string value: '\x97' for column 'a' at row 664
WARNING | 1366 | Incorrect string value: '\x98' for column 'a' at row 665
WARNING | 1366 | Incorrect string value: '\x99' for column 'a' at row 666
WARNING | 1366 | Incorrect string value: '\x9A' for column 'a' at row 667
WARNING | 1366 | Incorrect string value: '\x9B' for column 'a' at row 668
WARNING | 1366 | Incorrect string value: '\x9C' for column 'a' at row 669
WARNING | 1366 | Incorrect string value: '\x9D' for column 'a' at row 670
WARNING | 1366 | Incorrect string value: '\x9E' for column 'a' at row 671
WARNING | 1366 | Incorrect string value: '\x9F' for column 'a' at row 672
WARNING | 1366 | Incorrect string value: '\xA0' for column 'a' at row 673
WARNING | 1366 | Incorrect string value: '\xE0' for column 'a' at row 737
WARNING | 1366 | Incorrect string value: '\xE1' for column 'a' at row 738
WARNING | 1366 | Incorrect string value: '\xE2' for column 'a' at row 739
WARNING | 1366 | Incorrect string value: '\xE3' for column 'a' at row 740
WARNING | 1366 | Incorrect string value: '\xE4' for column 'a' at row 741
WARNING | 1366 | Incorrect string value: '\xE5' for column 'a' at row 742
WARNING | 1366 | Incorrect string value: '\xE6' for column 'a' at row 743
WARNING | 1366 | Incorrect string value: '\xE7' for column 'a' at row 744
WARNING | 1366 | Incorrect string value: '\xE8' for column 'a' at row 745
WARNING | 1366 | Incorrect string value: '\xE9' for column 'a' at row 746
WARNING | 1366 | Incorrect string value: '\xEA' for column 'a' at row 747
WARNING | 1366 | Incorrect string value: '\xEB' for column 'a' at row 748
WARNING | 1366 | Incorrect string value: '\xEC' for column 'a' at row 749
WARNING | 1366 | Incorrect string value: '\xED' for column 'a' at row 750
WARNING | 1366 | Incorrect string value: '\xEE' for column 'a' at row 751
WARNING | 1366 | Incorrect string value: '\xEF' for column 'a' at row 752
WARNING | 1366 | Incorrect string value: '\xF0' for column 'a' at row 753
WARNING | 1366 | Incorrect string value: '\xF1' for column 'a' at row 754
WARNING | 1366 | Incorrect string value: '\xF2' for column 'a' at row 755
WARNING | 1366 | Incorrect string value: '\xF3' for column 'a' at row 756
WARNING | 1366 | Incorrect string value: '\xF4' for column 'a' at row 757
WARNING | 1366 | Incorrect string value: '\xF5' for column 'a' at row 758
WARNING | 1366 | Incorrect string value: '\xF6' for column 'a' at row 759
WARNING | 1366 | Incorrect string value: '\xF7' for column 'a' at row 760
WARNING | 1366 | Incorrect string value: '\xF8' for column 'a' at row 761
WARNING | 1366 | Incorrect string value: '\xF9' for column 'a' at row 762
WARNING | 1366 | Incorrect string value: '\xFA' for column 'a' at row 763
WARNING | 1366 | Incorrect string value: '\xFB' for column 'a' at row 764
WARNING | 1366 | Incorrect string value: '\xFC' for column 'a' at row 765
WARNING | 1366 | Incorrect string value: '\xFD' for column 'a' at row 766
WARNING | 1366 | Incorrect string value: '\xFE' for column 'a' at row 767
WARNING | 1366 | Incorrect string value: '\xFF' for column 'a' at row 768
WARNING | 1366 | Incorrect string value: '\x80' for column 'a' at row 897
WARNING | 1366 | Incorrect string value: '\x81' for column 'a' at row 898
WARNING | 1366 | Incorrect string value: '\x82' for column 'a' at row 899
WARNING | 1366 | Incorrect string value: '\x83' for column 'a' at row 900
WARNING | 1366 | Incorrect string value: '\x84' for column 'a' at row 901
WARNING | 1366 | Incorrect string value: '\x85' for column 'a' at row 902
WARNING | 1366 | Incorrect string value: '\x86' for column 'a' at row 903
WARNING | 1366 | Incorrect string value: '\x87' for column 'a' at row 904
WARNING | 1366 | Incorrect string value: '\x88' for column 'a' at row 905
WARNING | 1366 | Incorrect string value: '\x89' for column 'a' at row 906
WARNING | 1366 | Incorrect string value: '\x8A' for column 'a' at row 907
WARNING | 1366 | Incorrect string value: '\x8B' for column 'a' at row 908
WARNING | 1366 | Incorrect string value: '\x8C' for column 'a' at row 909
WARNING | 1366 | Incorrect string value: '\x8D' for column 'a' at row 910
WARNING | 1366 | Incorrect string value: '\x8E' for column 'a' at row 911
WARNING | 1366 | Incorrect string value: '\x8F' for column 'a' at row 912
WARNING | 1366 | Incorrect string value: '\x90' for column 'a' at row 913
WARNING | 1366 | Incorrect string value: '\x91' for column 'a' at row 914
WARNING | 1366 | Incorrect string value: '\x92' for column 'a' at row 915
WARNING | 1366 | Incorrect string value: '\x93' for column 'a' at row 916
WARNING | 1366 | Incorrect string value: '\x94' for column 'a' at row 917
WARNING | 1366 | Incorrect string value: '\x95' for column 'a' at row 918
WARNING | 1366 | Incorrect string value: '\x96' for column 'a' at row 919
WARNING | 1366 | Incorrect string value: '\x97' for column 'a' at row 920
WARNING | 1366 | Incorrect string value: '\x98' for column 'a' at row 921
WARNING | 1366 | Incorrect string value: '\x99' for column 'a' at row 922
WARNING | 1366 | Incorrect string value: '\x9A' for column 'a' at row 923
WARNING | 1366 | Incorrect string value: '\x9B' for column 'a' at row 924
WARNING | 1366 | Incorrect string value: '\x9C' for column 'a' at row 925
WARNING | 1366 | Incorrect string value: '\x9D' for column 'a' at row 926
WARNING | 1366 | Incorrect string value: '\x9E' for column 'a' at row 927
WARNING | 1366 | Incorrect string value: '\x9F' for column 'a' at row 928
WARNING | 1366 | Incorrect string value: '\xA0' for column 'a' at row 929
WARNING | 1366 | Incorrect string value: '\xE0' for column 'a' at row 993
WARNING | 1366 | Incorrect string value: '\xE1' for column 'a' at row 994
WARNING | 1366 | Incorrect string value: '\xE2' for column 'a' at row 995
WARNING | 1366 | Incorrect string value: '\xE3' for column 'a' at row 996
WARNING | 1366 | Incorrect string value: '\xE4' for column 'a' at row 997
WARNING | 1366 | Incorrect string value: '\xE5' for column 'a' at row 998
WARNING | 1366 | Incorrect string value: '\xE6' for column 'a' at row 999
WARNING | 1366 | Incorrect string value: '\xE7' for column 'a' at row 1000
WARNING | 1366 | Incorrect string value: '\xE8' for column 'a' at row 1001
WARNING | 1366 | Incorrect string value: '\xE9' for column 'a' at row 1002
WARNING | 1366 | Incorrect string value: '\xEA' for column 'a' at row 1003
WARNING | 1366 | Incorrect string value: '\xEB' for column 'a' at row 1004
WARNING | 1366 | Incorrect string value: '\xEC' for column 'a' at row 1005
WARNING | 1366 | Incorrect string value: '\xED' for column 'a' at row 1006
WARNING | 1366 | Incorrect string value: '\xEE' for column 'a' at row 1007
WARNING | 1366 | Incorrect string value: '\xEF' for column 'a' at row 1008
WARNING | 1366 | Incorrect string value: '\xF0' for column 'a' at row 1009
WARNING | 1366 | Incorrect string value: '\xF1' for column 'a' at row 1010
WARNING | 1366 | Incorrect string value: '\xF2' for column 'a' at row 1011
WARNING | 1366 | Incorrect string value: '\xF3' for column 'a' at row 1012
WARNING | 1366 | Incorrect string value: '\xF4' for column 'a' at row 1013
WARNING | 1366 | Incorrect string value: '\xF5' for column 'a' at row 1014
WARNING | 1366 | Incorrect string value: '\xF6' for column 'a' at row 1015
WARNING | 1366 | Incorrect string value: '\xF7' for column 'a' at row 1016
WARNING | 1366 | Incorrect string value: '\xF8' for column 'a' at row 1017
WARNING | 1366 | Incorrect string value: '\xF9' for column 'a' at row 1018
WARNING | 1366 | Incorrect string value: '\xFA' for column 'a' at row 1019
WARNING | 1366 | Incorrect string value: '\xFB' for column 'a' at row 1020
WARNING | 1366 | Incorrect string value: '\xFC' for column 'a' at row 1021
WARNING | 1366 | Incorrect string value: '\xFD' for column 'a' at row 1022
WARNING | 1366 | Incorrect string value: '\xFE' for column 'a' at row 1023
WARNING | 1366 | Incorrect string value: '\xFF' for column 'a' at row 1024
WARNING | 1366 | Incorrect string value: '\x80' for column 'a' at row 1153
WARNING | 1366 | Incorrect string value: '\x81' for column 'a' at row 1154
WARNING | 1366 | Incorrect string value: '\x82' for column 'a' at row 1155
WARNING | 1366 | Incorrect string value: '\x83' for column 'a' at row 1156
WARNING | 1366 | Incorrect string value: '\x84' for column 'a' at row 1157
WARNING | 1366 | Incorrect string value: '\x85' for column 'a' at row 1158
WARNING | 1366 | Incorrect string value: '\x86' for column 'a' at row 1159
WARNING | 1366 | Incorrect string value: '\x87' for column 'a' at row 1160
WARNING | 1366 | Incorrect string value: '\x88' for column 'a' at row 1161
WARNING | 1366 | Incorrect string value: '\x89' for column 'a' at row 1162
WARNING | 1366 | Incorrect string value: '\x8A' for column 'a' at row 1163
WARNING | 1366 | Incorrect string value: '\x8B' for column 'a' at row 1164
WARNING | 1366 | Incorrect string value: '\x8C' for column 'a' at row 1165
WARNING | 1366 | Incorrect string value: '\x8D' for column 'a' at row 1166
WARNING | 1366 | Incorrect string value: '\x8E' for column 'a' at row 1167
WARNING | 1366 | Incorrect string value: '\x8F' for column 'a' at row 1168
WARNING | 1366 | Incorrect string value: '\x90' for column 'a' at row 1169
WARNING | 1366 | Incorrect string value: '\x91' for column 'a' at row 1170
WARNING | 1366 | Incorrect string value: '\x92' for column 'a' at row 1171
WARNING | 1366 | Incorrect string value: '\x93' for column 'a' at row 1172
WARNING | 1366 | Incorrect string value: '\x94' for column 'a' at row 1173
WARNING | 1366 | Incorrect string value: '\x95' for column 'a' at row 1174
WARNING | 1366 | Incorrect string value: '\x96' for column 'a' at row 1175
WARNING | 1366 | Incorrect string value: '\x97' for column 'a' at row 1176
WARNING | 1366 | Incorrect string value: '\x98' for column 'a' at row 1177
WARNING | 1366 | Incorrect string value: '\x99' for column 'a' at row 1178
WARNING | 1366 | Incorrect string value: '\x9A' for column 'a' at row 1179
WARNING | 1366 | Incorrect string value: '\x9B' for column 'a' at row 1180
WARNING | 1366 | Incorrect string value: '\x9C' for column 'a' at row 1181
WARNING | 1366 | Incorrect string value: '\x9D' for column 'a' at row 1182
WARNING | 1366 | Incorrect string value: '\x9E' for column 'a' at row 1183
WARNING | 1366 | Incorrect string value: '\x9F' for column 'a' at row 1184
WARNING | 1366 | Incorrect string value: '\xA0' for column 'a' at row 1185
WARNING | 1366 | Incorrect string value: '\xE0' for column 'a' at row 1249
WARNING | 1366 | Incorrect string value: '\xE1' for column 'a' at row 1250
WARNING | 1366 | Incorrect string value: '\xE2' for column 'a' at row 1251
WARNING | 1366 | Incorrect string value: '\xE3' for column 'a' at row 1252
WARNING | 1366 | Incorrect string value: '\xE4' for column 'a' at row 1253
WARNING | 1366 | Incorrect string value: '\xE5' for column 'a' at row 1254
WARNING | 1366 | Incorrect string value: '\xE6' for column 'a' at row 1255
WARNING | 1366 | Incorrect string value: '\xE7' for column 'a' at row 1256
WARNING | 1366 | Incorrect string value: '\xE8' for column 'a' at row 1257
WARNING | 1366 | Incorrect string value: '\xE9' for column 'a' at row 1258
WARNING | 1366 | Incorrect string value: '\xEA' for column 'a' at row 1259
WARNING | 1366 | Incorrect string value: '\xEB' for column 'a' at row 1260
WARNING | 1366 | Incorrect string value: '\xEC' for column 'a' at row 1261
WARNING | 1366 | Incorrect string value: '\xED' for column 'a' at row 1262
WARNING | 1366 | Incorrect string value: '\xEE' for column 'a' at row 1263
WARNING | 1366 | Incorrect string value: '\xEF' for column 'a' at row 1264
WARNING | 1366 | Incorrect string value: '\xF0' for column 'a' at row 1265
WARNING | 1366 | Incorrect string value: '\xF1' for column 'a' at row 1266
WARNING | 1366 | Incorrect string value: '\xF2' for column 'a' at row 1267
WARNING | 1366 | Incorrect string value: '\xF3' for column 'a' at row 1268
WARNING | 1366 | Incorrect string value: '\xF4' for column 'a' at row 1269
WARNING | 1366 | Incorrect string value: '\xF5' for column 'a' at row 1270
WARNING | 1366 | Incorrect string value: '\xF6' for column 'a' at row 1271
WARNING | 1366 | Incorrect string value: '\xF7' for column 'a' at row 1272
WARNING | 1366 | Incorrect string value: '\xF8' for column 'a' at row 1273
WARNING | 1366 | Incorrect string value: '\xF9' for column 'a' at row 1274
WARNING | 1366 | Incorrect string value: '\xFA' for column 'a' at row 1275
WARNING | 1366 | Incorrect string value: '\xFB' for column 'a' at row 1276
WARNING | 1366 | Incorrect string value: '\xFC' for column 'a' at row 1277
WARNING | 1366 | Incorrect string value: '\xFD' for column 'a' at row 1278
WARNING | 1366 | Incorrect string value: '\xFE' for column 'a' at row 1279
WARNING | 1366 | Incorrect string value: '\xFF' for column 'a' at row 1280
WARNING | 1366 | Incorrect string value: '\x80' for column 'a' at row 1409
WARNING | 1366 | Incorrect string value: '\x81' for column 'a' at row 1410
WARNING | 1366 | Incorrect string value: '\x82' for column 'a' at row 1411
WARNING | 1366 | Incorrect string value: '\x83' for column 'a' at row 1412
WARNING | 1366 | Incorrect string value: '\x84' for column 'a' at row 1413
WARNING | 1366 | Incorrect string value: '\x85' for column 'a' at row 1414
WARNING | 1366 | Incorrect string value: '\x86' for column 'a' at row 1415
WARNING | 1366 | Incorrect string value: '\x87' for column 'a' at row 1416
WARNING | 1366 | Incorrect string value: '\x88' for column 'a' at row 1417
WARNING | 1366 | Incorrect string value: '\x89' for column 'a' at row 1418
WARNING | 1366 | Incorrect string value: '\x8A' for column 'a' at row 1419
WARNING | 1366 | Incorrect string value: '\x8B' for column 'a' at row 1420
WARNING | 1366 | Incorrect string value: '\x8C' for column 'a' at row 1421
WARNING | 1366 | Incorrect string value: '\x8D' for column 'a' at row 1422
WARNING | 1366 | Incorrect string value: '\x8E' for column 'a' at row 1423
WARNING | 1366 | Incorrect string value: '\x8F' for column 'a' at row 1424
WARNING | 1366 | Incorrect string value: '\x90' for column 'a' at row 1425
WARNING | 1366 | Incorrect string value: '\x91' for column 'a' at row 1426
WARNING | 1366 | Incorrect string value: '\x92' for column 'a' at row 1427
WARNING | 1366 | Incorrect string value: '\x93' for column 'a' at row 1428
WARNING | 1366 | Incorrect string value: '\x94' for column 'a' at row 1429
WARNING | 1366 | Incorrect string value: '\x95' for column 'a' at row 1430
WARNING | 1366 | Incorrect string value: '\x96' for column 'a' at row 1431
WARNING | 1366 | Incorrect string value: '\x97' for column 'a' at row 1432
WARNING | 1366 | Incorrect string value: '\x98' for column 'a' at row 1433
WARNING | 1366 | Incorrect string value: '\x99' for column 'a' at row 1434
WARNING | 1366 | Incorrect string value: '\x9A' for column 'a' at row 1435
WARNING | 1366 | Incorrect string value: '\x9B' for column 'a' at row 1436
WARNING | 1366 | Incorrect string value: '\x9C' for column 'a' at row 1437
WARNING | 1366 | Incorrect string value: '\x9D' for column 'a' at row 1438
WARNING | 1366 | Incorrect string value: '\x9E' for column 'a' at row 1439
WARNING | 1366 | Incorrect string value: '\x9F' for column 'a' at row 1440
WARNING | 1366 | Incorrect string value: '\xA0' for column 'a' at row 1441
WARNING | 1366 | Incorrect string value: '\xE0' for column 'a' at row 1505
WARNING | 1366 | Incorrect string value: '\xE1' for column 'a' at row 1506
WARNING | 1366 | Incorrect string value: '\xE2' for column 'a' at row 1507
WARNING | 1366 | Incorrect string value: '\xE3' for column 'a' at row 1508
WARNING | 1366 | Incorrect string value: '\xE4' for column 'a' at row 1509
WARNING | 1366 | Incorrect string value: '\xE5' for column 'a' at row 1510
WARNING | 1366 | Incorrect string value: '\xE6' for column 'a' at row 1511
WARNING | 1366 | Incorrect string value: '\xE7' for column 'a' at row 1512
WARNING | 1366 | Incorrect string value: '\xE8' for column 'a' at row 1513
WARNING | 1366 | Incorrect string value: '\xE9' for column 'a' at row 1514
WARNING | 1366 | Incorrect string value: '\xEA' for column 'a' at row 1515
WARNING | 1366 | Incorrect string value: '\xEB' for column 'a' at row 1516
WARNING | 1366 | Incorrect string value: '\xEC' for column 'a' at row 1517
WARNING | 1366 | Incorrect string value: '\xED' for column 'a' at row 1518
WARNING | 1366 | Incorrect string value: '\xEE' for column 'a' at row 1519
WARNING | 1366 | Incorrect string value: '\xEF' for column 'a' at row 1520
WARNING | 1366 | Incorrect string value: '\xF0' for column 'a' at row 1521
WARNING | 1366 | Incorrect string value: '\xF1' for column 'a' at row 1522
WARNING | 1366 | Incorrect string value: '\xF2' for column 'a' at row 1523
WARNING | 1366 | Incorrect string value: '\xF3' for column 'a' at row 1524
WARNING | 1366 | Incorrect string value: '\xF4' for column 'a' at row 1525
WARNING | 1366 | Incorrect string value: '\xF5' for column 'a' at row 1526
WARNING | 1366 | Incorrect string value: '\xF6' for column 'a' at row 1527
WARNING | 1366 | Incorrect string value: '\xF7' for column 'a' at row 1528
WARNING | 1366 | Incorrect string value: '\xF8' for column 'a' at row 1529
WARNING | 1366 | Incorrect string value: '\xF9' for column 'a' at row 1530
WARNING | 1366 | Incorrect string value: '\xFA' for column 'a' at row 1531
WARNING | 1366 | Incorrect string value: '\xFB' for column 'a' at row 1532
WARNING | 1366 | Incorrect string value: '\xFC' for column 'a' at row 1533
WARNING | 1366 | Incorrect string value: '\xFD' for column 'a' at row 1534
WARNING | 1366 | Incorrect string value: '\xFE' for column 'a' at row 1535
WARNING | 1366 | Incorrect string value: '\xFF' for column 'a' at row 1536
WARNING | 1366 | Incorrect string value: '\x80' for column 'a' at row 1665
WARNING | 1366 | Incorrect string value: '\x81' for column 'a' at row 1666
WARNING | 1366 | Incorrect string value: '\x82' for column 'a' at row 1667
WARNING | 1366 | Incorrect string value: '\x83' for column 'a' at row 1668
WARNING | 1366 | Incorrect string value: '\x84' for column 'a' at row 1669
WARNING | 1366 | Incorrect string value: '\x85' for column 'a' at row 1670
WARNING | 1366 | Incorrect string value: '\x86' for column 'a' at row 1671
WARNING | 1366 | Incorrect string value: '\x87' for column 'a' at row 1672
WARNING | 1366 | Incorrect string value: '\x88' for column 'a' at row 1673
WARNING | 1366 | Incorrect string value: '\x89' for column 'a' at row 1674
WARNING | 1366 | Incorrect string value: '\x8A' for column 'a' at row 1675
WARNING | 1366 | Incorrect string value: '\x8B' for column 'a' at row 1676
WARNING | 1366 | Incorrect string value: '\x8C' for column 'a' at row 1677
WARNING | 1366 | Incorrect string value: '\x8D' for column 'a' at row 1678
WARNING | 1366 | Incorrect string value: '\x8E' for column 'a' at row 1679
WARNING | 1366 | Incorrect string value: '\x8F' for column 'a' at row 1680
WARNING | 1366 | Incorrect string value: '\x90' for column 'a' at row 1681
WARNING | 1366 | Incorrect string value: '\x91' for column 'a' at row 1682
WARNING | 1366 | Incorrect string value: '\x92' for column 'a' at row 1683
WARNING | 1366 | Incorrect string value: '\x93' for column 'a' at row 1684
WARNING | 1366 | Incorrect string value: '\x94' for column 'a' at row 1685
WARNING | 1366 | Incorrect string value: '\x95' for column 'a' at row 1686
WARNING | 1366 | Incorrect string value: '\x96' for column 'a' at row 1687
WARNING | 1366 | Incorrect string value: '\x97' for column 'a' at row 1688
WARNING | 1366 | Incorrect string value: '\x98' for column 'a' at row 1689
WARNING | 1366 | Incorrect string value: '\x99' for column 'a' at row 1690
WARNING | 1366 | Incorrect string value: '\x9A' for column 'a' at row 1691
WARNING | 1366 | Incorrect string value: '\x9B' for column 'a' at row 1692
WARNING | 1366 | Incorrect string value: '\x9C' for column 'a' at row 1693
WARNING | 1366 | Incorrect string value: '\x9D' for column 'a' at row 1694
WARNING | 1366 | Incorrect string value: '\x9E' for column 'a' at row 1695
WARNING | 1366 | Incorrect string value: '\x9F' for column 'a' at row 1696
WARNING | 1366 | Incorrect string value: '\xA0' for column 'a' at row 1697
WARNING | 1366 | Incorrect string value: '\xE0' for column 'a' at row 1761
WARNING | 1366 | Incorrect string value: '\xE1' for column 'a' at row 1762
WARNING | 1366 | Incorrect string value: '\xE2' for column 'a' at row 1763
WARNING | 1366 | Incorrect string value: '\xE3' for column 'a' at row 1764
WARNING | 1366 | Incorrect string value: '\xE4' for column 'a' at row 1765
WARNING | 1366 | Incorrect string value: '\xE5' for column 'a' at row 1766
WARNING | 1366 | Incorrect string value: '\xE6' for column 'a' at row 1767
WARNING | 1366 | Incorrect string value: '\xE7' for column 'a' at row 1768
WARNING | 1366 | Incorrect string value: '\xE8' for column 'a' at row 1769
WARNING | 1366 | Incorrect string value: '\xE9' for column 'a' at row 1770
WARNING | 1366 | Incorrect string value: '\xEA' for column 'a' at row 1771
WARNING | 1366 | Incorrect string value: '\xEB' for column 'a' at row 1772
WARNING | 1366 | Incorrect string value: '\xEC' for column 'a' at row 1773
WARNING | 1366 | Incorrect string value: '\xED' for column 'a' at row 1774
WARNING | 1366 | Incorrect string value: '\xEE' for column 'a' at row 1775
WARNING | 1366 | Incorrect string value: '\xEF' for column 'a' at row 1776
WARNING | 1366 | Incorrect string value: '\xF0' for column 'a' at row 1777
WARNING | 1366 | Incorrect string value: '\xF1' for column 'a' at row 1778
WARNING | 1366 | Incorrect string value: '\xF2' for column 'a' at row 1779
WARNING | 1366 | Incorrect string value: '\xF3' for column 'a' at row 1780
WARNING | 1366 | Incorrect string value: '\xF4' for column 'a' at row 1781
WARNING | 1366 | Incorrect string value: '\xF5' for column 'a' at row 1782
WARNING | 1366 | Incorrect string value: '\xF6' for column 'a' at row 1783
WARNING | 1366 | Incorrect string value: '\xF7' for column 'a' at row 1784
WARNING | 1366 | Incorrect string value: '\xF8' for column 'a' at row 1785
WARNING | 1366 | Incorrect string value: '\xF9' for column 'a' at row 1786
WARNING | 1366 | Incorrect string value: '\xFA' for column 'a' at row 1787
WARNING | 1366 | Incorrect string value: '\xFB' for column 'a' at row 1788
WARNING | 1366 | Incorrect string value: '\xFC' for column 'a' at row 1789
WARNING | 1366 | Incorrect string value: '\xFD' for column 'a' at row 1790
WARNING | 1366 | Incorrect string value: '\xFE' for column 'a' at row 1791
WARNING | 1366 | Incorrect string value: '\xFF' for column 'a' at row 1792
WARNING | 1366 | Incorrect string value: '\x80' for column 'a' at row 1921
WARNING | 1366 | Incorrect string value: '\x81' for column 'a' at row 1922
WARNING | 1366 | Incorrect string value: '\x82' for column 'a' at row 1923
WARNING | 1366 | Incorrect string value: '\x83' for column 'a' at row 1924
WARNING | 1366 | Incorrect string value: '\x84' for column 'a' at row 1925
WARNING | 1366 | Incorrect string value: '\x85' for column 'a' at row 1926
WARNING | 1366 | Incorrect string value: '\x86' for column 'a' at row 1927
WARNING | 1366 | Incorrect string value: '\x87' for column 'a' at row 1928
WARNING | 1366 | Incorrect string value: '\x88' for column 'a' at row 1929
WARNING | 1366 | Incorrect string value: '\x89' for column 'a' at row 1930
WARNING | 1366 | Incorrect string value: '\x8A' for column 'a' at row 1931
WARNING | 1366 | Incorrect string value: '\x8B' for column 'a' at row 1932
WARNING | 1366 | Incorrect string value: '\x8C' for column 'a' at row 1933
WARNING | 1366 | Incorrect string value: '\x8D' for column 'a' at row 1934
WARNING | 1366 | Incorrect string value: '\x8E' for column 'a' at row 1935
WARNING | 1366 | Incorrect string value: '\x8F' for column 'a' at row 1936
WARNING | 1366 | Incorrect string value: '\x90' for column 'a' at row 1937
WARNING | 1366 | Incorrect string value: '\x91' for column 'a' at row 1938
WARNING | 1366 | Incorrect string value: '\x92' for column 'a' at row 1939
WARNING | 1366 | Incorrect string value: '\x93' for column 'a' at row 1940
WARNING | 1366 | Incorrect string value: '\x94' for column 'a' at row 1941
WARNING | 1366 | Incorrect string value: '\x95' for column 'a' at row 1942
WARNING | 1366 | Incorrect string value: '\x96' for column 'a' at row 1943
WARNING | 1366 | Incorrect string value: '\x97' for column 'a' at row 1944
WARNING | 1366 | Incorrect string value: '\x98' for column 'a' at row 1945
WARNING | 1366 | Incorrect string value: '\x99' for column 'a' at row 1946
WARNING | 1366 | Incorrect string value: '\x9A' for column 'a' at row 1947
WARNING | 1366 | Incorrect string value: '\x9B' for column 'a' at row 1948
WARNING | 1366 | Incorrect string value: '\x9C' for column 'a' at row 1949
WARNING | 1366 | Incorrect string value: '\x9D' for column 'a' at row 1950
WARNING | 1366 | Incorrect string value: '\x9E' for column 'a' at row 1951
WARNING | 1366 | Incorrect string value: '\x9F' for column 'a' at row 1952
WARNING | 1366 | Incorrect string value: '\xA0' for column 'a' at row 1953
WARNING | 1366 | Incorrect string value: '\xE0' for column 'a' at row 2017
WARNING | 1366 | Incorrect string value: '\xE1' for column 'a' at row 2018
WARNING | 1366 | Incorrect string value: '\xE2' for column 'a' at row 2019
WARNING | 1366 | Incorrect string value: '\xE3' for column 'a' at row 2020
WARNING | 1366 | Incorrect string value: '\xE4' for column 'a' at row 2021
WARNING | 1366 | Incorrect string value: '\xE5' for column 'a' at row 2022
WARNING | 1366 | Incorrect string value: '\xE6' for column 'a' at row 2023
WARNING | 1366 | Incorrect string value: '\xE7' for column 'a' at row 2024
WARNING | 1366 | Incorrect string value: '\xE8' for column 'a' at row 2025
WARNING | 1366 | Incorrect string value: '\xE9' for column 'a' at row 2026
WARNING | 1366 | Incorrect string value: '\xEA' for column 'a' at row 2027
WARNING | 1366 | Incorrect string value: '\xEB' for column 'a' at row 2028
Got expected warning: 1366 
RUN set max_error_count=64

0 rows affected
RUN drop tables t1,t2

0 rows affected

##
## Checking warnings with prepare and execute statements
RUN use test

0 rows affected
RUN CREATE TABLE t1(f1 VARCHAR(6) NOT NULL, FULLTEXT KEY(f1), UNIQUE(f1))

0 rows affected
RUN INSERT INTO t1 VALUES ('test')

1 rows affected
RUN SELECT 1 FROM t1 WHERE 1 > ALL((SELECT 1 FROM t1 JOIN t1 a ON (MATCH(t1.f1) against ("")) WHERE t1.f1 GROUP BY t1.f1)) xor f1
1
1
0 rows affected
Warnings generated:
WARNING | 1292 | Truncated incorrect DOUBLE value: 'test'
Got expected warning: 1292 
RUN PREPARE stmt FROM 'SELECT 1 FROM t1 WHERE 1 > ALL((SELECT 1 FROM t1 RIGHT OUTER JOIN t1 a ON (MATCH(t1.f1) against ("")) WHERE t1.f1 GROUP BY t1.f1)) xor f1'

0 rows affected
Statement prepared
RUN EXECUTE stmt
1
1
0 rows affected
Warnings generated:
WARNING | 1292 | Truncated incorrect DOUBLE value: 'test'
Got expected warning: 1292 
RUN EXECUTE stmt
1
1
0 rows affected
Warnings generated:
WARNING | 1292 | Truncated incorrect DOUBLE value: 'test'
Got expected warning: 1292 
RUN DEALLOCATE PREPARE stmt

0 rows affected
RUN PREPARE stmt FROM 'SELECT 1 FROM t1 WHERE 1 > ALL((SELECT 1 FROM t1 JOIN t1 a ON (MATCH(t1.f1) against ("")) WHERE t1.f1 GROUP BY t1.f1))'

0 rows affected
Statement prepared
RUN EXECUTE stmt
1
1
0 rows affected
RUN EXECUTE stmt
1
1
0 rows affected
RUN DEALLOCATE PREPARE stmt

0 rows affected
RUN drop table t1

0 rows affected

##
## Checking warnings with group by when sql_mode is no_engine_substitution
RUN use test

0 rows affected
RUN SET sql_mode = 'NO_ENGINE_SUBSTITUTION'

0 rows affected
RUN create table t1 (c1 char(3), c2 char(3))

0 rows affected
RUN create table t2 (c3 char(3), c4 char(3))

0 rows affected
RUN insert into t1 values ('aaa', 'bb1'), ('aaa', 'bb2')

2 rows affected
Records: 2  Duplicates: 0  Warnings: 0
RUN insert into t2 values ('aaa', 'bb1'), ('aaa', 'bb2')

2 rows affected
Records: 2  Duplicates: 0  Warnings: 0
RUN select t1.c1 as c2 from t1, t2 where t1.c2 = t2.c4 group by c2
c2
aaa
aaa
0 rows affected
Warnings generated:
WARNING | 1052 | Column 'c2' in group statement is ambiguous
Got expected warning: 1052 
RUN select t1.c1 as c2 from t1, t2 where t1.c2 = t2.c4 group by t1.c1
c2
aaa
0 rows affected
RUN drop table t1, t2

0 rows affected

##
## Setting back to default of sql_mode
RUN SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION'

0 rows affected
Warnings generated:
WARNING | 3135 | 'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
Got expected warning: 3135 

##
## Checking with group by where both error and warnings expected with default sql_mode
RUN use test

0 rows affected
RUN create table t1 (c1 char(3), c2 char(3))

0 rows affected
RUN create table t2 (c3 char(3), c4 char(3))

0 rows affected
RUN insert into t1 values ('aaa', 'bb1'), ('aaa', 'bb2')

2 rows affected
Records: 2  Duplicates: 0  Warnings: 0
RUN insert into t2 values ('aaa', 'bb1'), ('aaa', 'bb2')

2 rows affected
Records: 2  Duplicates: 0  Warnings: 0
RUN select t1.c1 as c2 from t1, t2 where t1.c2 = t2.c4 group by c2
Warnings generated:
WARNING | 1052 | Column 'c2' in group statement is ambiguous
While executing select t1.c1 as c2 from t1, t2 where t1.c2 = t2.c4 group by c2:
Got expected error: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'test.t1.c1' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by (code 1055)
Got expected warning: 1052 

##
## Expect both error and warning
RUN select t1.c1 as c2 from t1, t2 where t1.c2 = t2.c4 group by t1.c1
c2
aaa
0 rows affected
RUN drop table t1, t2

0 rows affected

##
## Checking warnings with rollback on non-transactional tables
RUN use test

0 rows affected
RUN create table t1 (n int not null primary key) engine=myisam

0 rows affected
RUN begin work

0 rows affected
RUN insert into t1 values (4)

1 rows affected
RUN insert into t1 values (5)

1 rows affected
RUN rollback

0 rows affected
Warnings generated:
WARNING | 1196 | Some non-transactional changed tables couldn't be rolled back
Got expected warning: 1196 
RUN show warnings
Level	Code	Message
Warning	1196	Some non-transactional changed tables couldn't be rolled back
0 rows affected
RUN show errors
Level	Code	Message
0 rows affected
RUN select @@warning_count,@@error_count
@@warning_count	@@error_count
1	0
0 rows affected
RUN select * from t1
n
4
5
0 rows affected
RUN show warnings
Level	Code	Message
0 rows affected
RUN select @@warning_count
@@warning_count
0
0 rows affected
RUN drop table t1

0 rows affected

##
## Expect 2 warnings for insert related to truncation and incorrect date value
RUN use test

0 rows affected
RUN set sql_mode=''

0 rows affected
RUN create table t1 (
a date not null,
b mediumtext generated always as ((a not in (a,a))) virtual,
c timestamp generated always as ((a not in (b,b))) stored not null
)

0 rows affected
RUN insert t1(a) values(7777777777)

1 rows affected
Records: 1  Duplicates: 0  Warnings: 2
Warnings generated:
WARNING | 1265 | Data truncated for column 'a' at row 1
WARNING | 1292 | Incorrect date value: '0' for column 'a' at row 1
Got expected warnings: 1265 1292 
RUN drop table t1

0 rows affected
RUN SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION'

0 rows affected
Warnings generated:
WARNING | 3135 | 'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release.
Got expected warning: 3135 

##
## Checking warnings while creating index which is more than row size
RUN use test

0 rows affected
RUN create table worklog5743_1(a1 int, a2 TEXT not null) KEY_BLOCK_SIZE=1

0 rows affected
RUN create table worklog5743_2(a1 int, a2 TEXT not null) KEY_BLOCK_SIZE=2

0 rows affected
RUN create table worklog5743_4(a1 int, a2 TEXT not null) KEY_BLOCK_SIZE=4

0 rows affected
RUN create table worklog5743_8(a1 int, a2 TEXT, a3 TEXT) KEY_BLOCK_SIZE=8

0 rows affected

##
## Below statement throws warning as Error and actual error is missing
## Change result file once Bug#21529270 fixed
RUN create index idx1 on worklog5743_1(a2(4000))
While executing create index idx1 on worklog5743_1(a2(4000)):
Got expected error: Specified key was too long; max key length is 3072 bytes (code 1071)
RUN set global innodb_large_prefix=1
While executing set global innodb_large_prefix=1:
Got expected error: Unknown system variable 'innodb_large_prefix' (code 1193)
RUN drop table worklog5743_1

0 rows affected
RUN drop table worklog5743_2

0 rows affected
RUN drop table worklog5743_4

0 rows affected
RUN drop table worklog5743_8

0 rows affected

##
## Checking warnings with different innodb file format
RUN use test

0 rows affected
RUN set innodb_strict_mode = off

0 rows affected
RUN set global innodb_file_per_table = 0

0 rows affected
RUN create temporary table t (i int) engine = innodb row_format = compressed

0 rows affected
Warnings generated:
WARNING | 1478 | InnoDB: ROW_FORMAT=COMPRESSED is ignored for TEMPORARY TABLE.
WARNING | 1478 | InnoDB: assuming ROW_FORMAT=DYNAMIC.
Got expected warning: 1478 
RUN drop table t

0 rows affected
RUN set innodb_strict_mode = on

0 rows affected
RUN set @@global.innodb_file_per_table = 1

0 rows affected

##
## Checking warnings with different innodb row format
RUN use test

0 rows affected

##
## Expect one Error and Warning
RUN create table t2 (id int primary key) engine = innodb key_block_size = 9
Warnings generated:
WARNING | 1478 | InnoDB: invalid KEY_BLOCK_SIZE = 9. Valid values are [1, 2, 4, 8, 16]
While executing create table t2 (id int primary key) engine = innodb key_block_size = 9:
Got expected error: Table storage engine for 't2' doesn't have this option (code 1031)
Got expected warning: 1478 

##
## Expect 2 warnings and 1 Error
RUN create table t2 (id int primary key) engine = innodb key_block_size = 9 row_format = redundant
Warnings generated:
WARNING | 1478 | InnoDB: invalid KEY_BLOCK_SIZE = 9. Valid values are [1, 2, 4, 8, 16]
WARNING | 1478 | InnoDB: cannot specify ROW_FORMAT=REDUNDANT with KEY_BLOCK_SIZE.
While executing create table t2 (id int primary key) engine = innodb key_block_size = 9 row_format = redundant:
Got expected error: Table storage engine for 't2' doesn't have this option (code 1031)
Got expected warning: 1478 

##
## Expect 1 warning and 2 Error
RUN create table bug46000(`id` int, key `GEN_clust_INDEX`(`id`))engine=innodb
Warnings generated:
WARNING | 1280 | Cannot Create Index with name 'GEN_CLUST_INDEX'. The name is reserved for the system default primary index.
WARNING | 1280 | Incorrect index name 'GEN_CLUST_INDEX'
WARNING | 1030 | Got error 124 - 'Wrong index given to function' from storage engine
While executing create table bug46000(`id` int, key `GEN_clust_INDEX`(`id`))engine=innodb:
Got expected error: Incorrect index name 'GEN_CLUST_INDEX' (code 1280)
Got expected warnings: 1030 1280 

##
## Check with show variables like versio and expect warning related to GTID session
RUN show variables like 'versio'
Variable_name	Value
0 rows affected

##
## Check with show variables like versio and expect warning related to GTID session
RUN show session variables like 'max_join_size'
Variable_name	Value
max_join_size	18446744073709551615
0 rows affected
RUN show session variables like 'concurrent_insert'
Variable_name	Value
concurrent_insert	AUTO
0 rows affected
RUN show session variables like 'default_storage_engine'
Variable_name	Value
default_storage_engine	InnoDB
0 rows affected

##
## Verify the notices statistics
Verify [Status variable "Mysqlx_notice_warning_sent" needs to have a value of 855]
Verify [Status variable "Mysqlx_notice_other_sent" needs to have a value of 168]
Verify [Status variable "Mysqlx_stmt_disable_notices" needs to have a value of 0]
Verify [Status variable "Mysqlx_stmt_enable_notices" needs to have a value of 1]
Verify [Status variable "Mysqlx_stmt_list_notices" needs to have a value of 0]
Mysqlx.Ok {
  msg: "bye!"
}
ok
checking warnings with procedure execution

0 rows affected
@start_global_value
1024
0 rows affected

0 rows affected
@start_session_value
1024
0 rows affected

0 rows affected

1 rows affected

0 rows affected

0 rows affected

0 rows affected

0 rows affected

0 rows affected

0 rows affected

0 rows affected

0 rows affected

0 rows affected

0 rows affected
Got expected error: Oops in proc_1 (code 1644)

0 rows affected
@@session.max_error_count
5
0 rows affected
Got expected error: Oops in proc_1 (code 1644)

0 rows affected
@@session.max_error_count
7
0 rows affected
Got expected error: Oops in proc_1 (code 1644)

0 rows affected
@@session.max_error_count
9
0 rows affected
Got expected error: Oops in proc_1 (code 1644)

0 rows affected

0 rows affected
@@global.max_error_count
1024
0 rows affected

0 rows affected
@@session.max_error_count
1024
0 rows affected
checking warnings while creating index with more than row size

0 rows affected

0 rows affected

0 rows affected

0 rows affected

0 rows affected
below statement throwing warning as an Error and actual error is missing
change result file once Bug#21529270 is fixed
Got expected error: Specified key was too long; max key length is 3072 bytes (code 1071)

0 rows affected

0 rows affected

0 rows affected

0 rows affected
Verify [Status variable "Mysqlx_notice_warning_sent" needs to have a value of 32]
Verify [Status variable "Mysqlx_notice_other_sent" needs to have a value of 38]
Verify [Status variable "Mysqlx_stmt_disable_notices" needs to have a value of 0]
Verify [Status variable "Mysqlx_stmt_enable_notices" needs to have a value of 0]
Verify [Status variable "Mysqlx_stmt_list_notices" needs to have a value of 0]
Mysqlx.Ok {
  msg: "bye!"
}
ok
Try enable warnings

command ok
Try enable account_expired

command ok
Try enable generated_insert_id

command ok
Try enable rows_affected

command ok
Try enable produced_message

command ok
Verify [Status variable "Mysqlx_notice_global_sent" needs to have a value of 1]
Verify [Status variable "Mysqlx_notice_warning_sent" needs to have a value of 0]
Verify [Status variable "Mysqlx_notice_other_sent" needs to have a value of 3]
Verify [Status variable "Mysqlx_stmt_disable_notices" needs to have a value of 0]
Verify [Status variable "Mysqlx_stmt_enable_notices" needs to have a value of 5]
Verify [Status variable "Mysqlx_stmt_list_notices" needs to have a value of 0]
Mysqlx.Ok {
  msg: "bye!"
}
ok
create user xplugin@localhost identified by 'xplugin';
Try enable warnings

command ok
Try enable account_expired

command ok
Try enable generated_insert_id

command ok
Try enable rows_affected

command ok
Try enable produced_message

command ok
Verify [Status variable "Mysqlx_notice_global_sent" needs to have a value of 1]
Verify [Status variable "Mysqlx_notice_warning_sent" needs to have a value of 0]
Verify [Status variable "Mysqlx_notice_other_sent" needs to have a value of 3]
Verify [Status variable "Mysqlx_stmt_disable_notices" needs to have a value of 0]
Verify [Status variable "Mysqlx_stmt_enable_notices" needs to have a value of 5]
Verify [Status variable "Mysqlx_stmt_list_notices" needs to have a value of 0]
Mysqlx.Ok {
  msg: "bye!"
}
ok
Enable multiple notices both read-only and read-write with single StmtExecute,expect success

command ok
notice	enabled
warnings	1
group_replication/membership/quorum_loss	0
group_replication/membership/view	0
group_replication/status/role_change	0
group_replication/status/state_change	0
account_expired	1
generated_insert_id	1
rows_affected	1
produced_message	1
command ok
Verify [Status variable "Mysqlx_notice_global_sent" needs to have a value of 1]
Verify [Status variable "Mysqlx_notice_warning_sent" needs to have a value of 0]
Verify [Status variable "Mysqlx_notice_other_sent" needs to have a value of 3]
Verify [Status variable "Mysqlx_stmt_disable_notices" needs to have a value of 0]
Verify [Status variable "Mysqlx_stmt_enable_notices" needs to have a value of 1]
Verify [Status variable "Mysqlx_stmt_list_notices" needs to have a value of 1]
Mysqlx.Ok {
  msg: "bye!"
}
ok

0 rows affected

0 rows affected
Warnings generated:
NOTE | 1051 | Unknown table 'mysql.t1'
expect Mysqlx_notice_other_sent=8 and Mysqlx_notice_warning_sent=3
seems like some problem with these stats, need to change values once issues fixed
disable warnings notice and observe stats

command ok
enable account_expired notice and check stats

command ok
enable account_expired notice and check stats

command ok
listing notices after enabling warnings,account_expired notices
notice	enabled
warnings	1
group_replication/membership/quorum_loss	0
group_replication/membership/view	0
group_replication/status/role_change	0
group_replication/status/state_change	0
account_expired	1
generated_insert_id	1
rows_affected	1
produced_message	1
command ok
listing notices with mixed case
notice	enabled
warnings	1
group_replication/membership/quorum_loss	0
group_replication/membership/view	0
group_replication/status/role_change	0
group_replication/status/state_change	0
account_expired	1
generated_insert_id	1
rows_affected	1
produced_message	1
command ok
Verify [Status variable "Mysqlx_notice_global_sent" needs to have a value of 1]
Verify [Status variable "Mysqlx_notice_warning_sent" needs to have a value of 1]
Verify [Status variable "Mysqlx_notice_other_sent" needs to have a value of 5]
Verify [Status variable "Mysqlx_stmt_disable_notices" needs to have a value of 1]
Verify [Status variable "Mysqlx_stmt_enable_notices" needs to have a value of 2]
Verify [Status variable "Mysqlx_stmt_list_notices" needs to have a value of 2]
Mysqlx.Ok {
  msg: "bye!"
}
ok
# restart: 

0 rows affected

0 rows affected
Warnings generated:
NOTE | 1051 | Unknown table 'mysql.t1'
Verify [Status variable "Mysqlx_notice_global_sent" needs to have a value of 1]
Verify [Status variable "Mysqlx_notice_warning_sent" needs to have a value of 1]
Verify [Status variable "Mysqlx_notice_other_sent" needs to have a value of 5]
Mysqlx.Ok {
  msg: "bye!"
}
ok
# restart: 
create user noti@localhost identified by 'noti';
While executing use mysql:
Got expected error: Access denied for user 'noti'@'localhost' to database 'mysql' (code 1044)
Got expected error: No database selected (code 1046)
Verify [Status variable "Mysqlx_notice_global_sent" needs to have a value of 1]
Verify [Status variable "Mysqlx_notice_warning_sent" needs to have a value of 0]
Verify [Status variable "Mysqlx_notice_other_sent" needs to have a value of 3]
Verify [Status variable "Mysqlx_stmt_disable_notices" needs to have a value of 0]
Verify [Status variable "Mysqlx_stmt_enable_notices" needs to have a value of 0]
Verify [Status variable "Mysqlx_stmt_list_notices" needs to have a value of 0]
Mysqlx.Ok {
  msg: "bye!"
}
ok
drop user noti@localhost;
drop user xplugin@localhost;
