# Copyright (c) 2015, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

# We provide our own versioning scheme so this warning is overridden
mysql-@DEB_PRODUCTNAME@ source: changelog-should-mention-nmu
mysql-@DEB_PRODUCTNAME@ source: source-nmu-has-incorrect-version-number

# The rapidjson library includes the json license, but the code
# using it has been removed from the source
mysql-@DEB_PRODUCTNAME@ source: license-problem-json-evil LICENSE
mysql-@DEB_PRODUCTNAME@ source: license-problem-json-evil extra/rapidjson/license.txt

# Translations not currently supported
mysql-@DEB_PRODUCTNAME@ source: untranslatable-debconf-templates
mysql-@DEB_PRODUCTNAME@ source: not-using-po-debconf

# d/copyright file is just a reference to the MySQL LICENSE file
mysql-@DEB_PRODUCTNAME@ source: ambiguous-paragraph-in-dep5-copyright
mysql-@DEB_PRODUCTNAME@ source: empty-short-license-in-dep5-copyright
