############## mysql-test\t\max_sp_recursion_depth_basic.test ###############
#                                                                             #
# Variable Name: max_sp_recursion_depth                                       #
# Scope: GLOBAL | SESSION                                                     #
# Access Type: Dynamic                                                        #
# Data Type: numeric                                                          #
# Default Value:0                                                             #
# Max value:255                                                               #
#                                                                             #
#                                                                             #
# Creation Date: 2008-02-07                                                   #
# Author:  Salman                                                             #
#                                                                             #
# Description: Test Cases of Dynamic System Variable max_sp_recursion_depth   #
#              that checks the behavior of this variable in the following ways#
#              * Default Value                                                #
#              * Valid & Invalid values                                       #
#              * Scope & Access method                                        #
#              * Data Integrity                                               #
#                                                                             #
# Reference: http://dev.mysql.com/doc/refman/5.1/en/                          #
#  server-system-variables.html                                               #
#                                                                             #
###############################################################################

--source include/load_sysvars.inc


############################################
#   START OF max_sp_recursion_depth TESTS  #
############################################


#############################################################
#                 Save initial value                        #
#############################################################

SET @start_global_value = @@global.max_sp_recursion_depth;
SELECT @start_global_value;
SET @start_session_value = @@session.max_sp_recursion_depth;
SELECT @start_session_value;


--echo '#--------------------FN_DYNVARS_085_01-------------------------#'
##############################################################
#    Display the DEFAULT value of max_sp_recursion_depth     #
##############################################################

SET @@global.max_sp_recursion_depth = 1000;
SET @@global.max_sp_recursion_depth = DEFAULT;
SELECT @@global.max_sp_recursion_depth;


SET @@session.max_sp_recursion_depth = 1000;
SET @@session.max_sp_recursion_depth = DEFAULT;
SELECT @@session.max_sp_recursion_depth;


--echo '#--------------------FN_DYNVARS_085_02-------------------------#'
##############################################################
#     Check the DEFAULT value of max_sp_recursion_depth      #
##############################################################

SET @@global.max_sp_recursion_depth = DEFAULT;
SELECT @@global.max_sp_recursion_depth = 0;

SET @@session.max_sp_recursion_depth = DEFAULT;
SELECT @@session.max_sp_recursion_depth = 0;



--echo '#--------------------FN_DYNVARS_085_03-------------------------#'
#########################################################################
# Change the value of max_sp_recursion_depth to a valid value for GLOBAL Scope #
#########################################################################

SET @@global.max_sp_recursion_depth = 0;
SELECT @@global.max_sp_recursion_depth;
SET @@global.max_sp_recursion_depth = 1;
SELECT @@global.max_sp_recursion_depth;
SET @@global.max_sp_recursion_depth = 254;
SELECT @@global.max_sp_recursion_depth;
SET @@global.max_sp_recursion_depth = 255;
SELECT @@global.max_sp_recursion_depth;
SET @@global.max_sp_recursion_depth = 150;
SELECT @@global.max_sp_recursion_depth;

--echo '#--------------------FN_DYNVARS_085_04-------------------------#'
#################################################################################
# Change the value of max_sp_recursion_depth to a valid value for SESSION Scope #
#################################################################################

SET @@session.max_sp_recursion_depth = 0;
SELECT @@session.max_sp_recursion_depth;

SET @@session.max_sp_recursion_depth = 1;
SELECT @@session.max_sp_recursion_depth;

SET @@session.max_sp_recursion_depth = 254;
SELECT @@session.max_sp_recursion_depth;

SET @@session.max_sp_recursion_depth = 255;
SELECT @@session.max_sp_recursion_depth;

SET @@session.max_sp_recursion_depth = 150;
SELECT @@session.max_sp_recursion_depth;


--echo '#------------------FN_DYNVARS_085_05-----------------------#'
##################################################################
# Change the value of max_sp_recursion_depth to an invalid value #
##################################################################

SET @@global.max_sp_recursion_depth = -1024;
SELECT @@global.max_sp_recursion_depth;
SET @@global.max_sp_recursion_depth = 256;
SELECT @@global.max_sp_recursion_depth;
SET @@global.max_sp_recursion_depth = -1;
SELECT @@global.max_sp_recursion_depth;
SET @@global.max_sp_recursion_depth = 3000;
SELECT @@global.max_sp_recursion_depth;
--Error ER_WRONG_TYPE_FOR_VAR
SET @@global.max_sp_recursion_depth = 65530.34;
SELECT @@global.max_sp_recursion_depth;
--Error ER_WRONG_TYPE_FOR_VAR
SET @@global.max_sp_recursion_depth = test;
SELECT @@global.max_sp_recursion_depth;
--Error ER_WRONG_TYPE_FOR_VAR
SET @@global.max_sp_recursion_depth = ' ';
SELECT @@global.max_sp_recursion_depth;

SET @@session.max_sp_recursion_depth = 256;
SELECT @@session.max_sp_recursion_depth;
SET @@session.max_sp_recursion_depth = -1;
SELECT @@session.max_sp_recursion_depth;
SET @@session.max_sp_recursion_depth = -1024;
SELECT @@session.max_sp_recursion_depth;

SET @@session.max_sp_recursion_depth = 500000;
SELECT @@session.max_sp_recursion_depth;
SET @@session.max_sp_recursion_depth = -001;
SELECT @@session.max_sp_recursion_depth;
--Error ER_WRONG_TYPE_FOR_VAR
SET @@session.max_sp_recursion_depth = 65530.34;
SET @@session.max_sp_recursion_depth = 10737418241;
SELECT @@session.max_sp_recursion_depth;
--Error ER_WRONG_TYPE_FOR_VAR
SET @@session.max_sp_recursion_depth = test;
SELECT @@session.max_sp_recursion_depth;
--Error ER_WRONG_TYPE_FOR_VAR
SET @@session.max_sp_recursion_depth = ' ';
SELECT @@session.max_sp_recursion_depth;

--echo '#------------------FN_DYNVARS_085_06-----------------------#'
####################################################################
#   Check if the value in GLOBAL Table matches value in variable   #
####################################################################


--disable_warnings
SELECT @@global.max_sp_recursion_depth = VARIABLE_VALUE 
FROM performance_schema.global_variables 
WHERE VARIABLE_NAME='max_sp_recursion_depth';
--enable_warnings

--echo '#------------------FN_DYNVARS_085_07-----------------------#'
####################################################################
#  Check if the value in SESSION Table matches value in variable   #
####################################################################

--disable_warnings
SELECT @@session.max_sp_recursion_depth = VARIABLE_VALUE 
FROM performance_schema.session_variables 
WHERE VARIABLE_NAME='max_sp_recursion_depth';
--enable_warnings


--echo '#------------------FN_DYNVARS_085_08-----------------------#'
####################################################################
#     Check if TRUE and FALSE values can be used on variable       #
####################################################################

SET @@global.max_sp_recursion_depth = TRUE;
SELECT @@global.max_sp_recursion_depth;
SET @@global.max_sp_recursion_depth = FALSE;
SELECT @@global.max_sp_recursion_depth;


--echo '#---------------------FN_DYNVARS_085_09----------------------#'
#################################################################################
#  Check if accessing variable with and without GLOBAL point to same variable   #
#################################################################################

SET @@global.max_sp_recursion_depth = 20;
SELECT @@max_sp_recursion_depth = @@global.max_sp_recursion_depth;


--echo '#---------------------FN_DYNVARS_085_10----------------------#'
########################################################################################################
#    Check if accessing variable with SESSION,LOCAL and without SCOPE points to same session variable  #
########################################################################################################

SET @@max_sp_recursion_depth = 255;
SELECT @@max_sp_recursion_depth = @@local.max_sp_recursion_depth;
SELECT @@local.max_sp_recursion_depth = @@session.max_sp_recursion_depth;


--echo '#---------------------FN_DYNVARS_085_11----------------------#'
##########################################################################
#   Check if max_sp_recursion_depth can be accessed with and without @@ sign    #
##########################################################################


SET max_sp_recursion_depth = 102;
SELECT @@max_sp_recursion_depth;
--Error ER_UNKNOWN_TABLE
SELECT local.max_sp_recursion_depth;
--Error ER_UNKNOWN_TABLE
SELECT session.max_sp_recursion_depth;
--Error ER_BAD_FIELD_ERROR
SELECT max_sp_recursion_depth = @@session.max_sp_recursion_depth;


####################################
#     Restore initial value        #
####################################

SET @@global.max_sp_recursion_depth = @start_global_value;
SELECT @@global.max_sp_recursion_depth;
SET @@session.max_sp_recursion_depth = @start_session_value;
SELECT @@session.max_sp_recursion_depth;


####################################################
#                 END OF max_sp_recursion_depth TESTS     #
####################################################

