/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */
#include "xcom_vp.h"
/* Copyright (c) 2010, 2024, Oracle and/or its affiliates.
   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.
   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.
   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */
#include "xcom/xcom_vp_platform.h"
#include "xcom/xcom_limits.h"
#include "xcom/xcom_profile.h"
extern synode_no const null_synode;
extern synode_no get_delivered_msg();
#ifndef _WIN32
#include <strings.h> /* For bzero */
#endif
bool_t
xdr_xcom_proto_1_0 (XDR *xdrs, xcom_proto *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_delivery_status_1_0 (XDR *xdrs, delivery_status *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cons_type_1_0 (XDR *xdrs, cons_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cargo_type_1_0 (XDR *xdrs, cargo_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_recover_action_1_0 (XDR *xdrs, recover_action *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_op_1_0 (XDR *xdrs, pax_op *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_msg_type_1_0 (XDR *xdrs, pax_msg_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_client_reply_code_1_0 (XDR *xdrs, client_reply_code *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_start_t_1_0 (XDR *xdrs, start_t *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_xcom_event_horizon_1_0 (XDR *xdrs, xcom_event_horizon *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_1_0 (XDR *xdrs, node_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_set_1_0 (XDR *xdrs, node_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_set_val, (u_int *) &objp->node_set_len, NSERVERS,
		sizeof (bool_t), (xdrproc_t) xdr_bool))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_mask_1_0 (XDR *xdrs, bit_mask *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_set_1_0 (XDR *xdrs, bit_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->bits.bits_val, (u_int *) &objp->bits.bits_len, NSERVERS,
		sizeof (bit_mask), (xdrproc_t) xdr_bit_mask_1_0))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_blob_1_0 (XDR *xdrs, blob *objp)
{
	register int32_t *buf;
	 if (!xdr_bytes (xdrs, (char **)&objp->data.data_val, (u_int *) &objp->data.data_len, MAXBLOB))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_proto_range_1_0 (XDR *xdrs, x_proto_range *objp)
{
	register int32_t *buf;
	 if (!xdr_xcom_proto_1_0 (xdrs, &objp->min_proto))
		 return FALSE;
	 if (!xdr_xcom_proto_1_0 (xdrs, &objp->max_proto))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_1_0 (XDR *xdrs, synode_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->msgno))
		 return FALSE;
	 if (!xdr_node_no_1_0 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_id_1_0 (XDR *xdrs, trans_id *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_0 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pc))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_paxos_role_1_0 (XDR *xdrs, paxos_role *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_address_1_0 (XDR *xdrs, node_address *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	 if (!xdr_blob_1_0 (xdrs, &objp->uuid))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->proto.min_proto = x_1_0;
 objp->proto.max_proto = x_1_0;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->services = P_PROP | P_ACC | P_LEARN;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_node_list_1_0 (XDR *xdrs, node_list *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_list_val, (u_int *) &objp->node_list_len, NSERVERS,
		sizeof (node_address), (xdrproc_t) xdr_node_address_1_0))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_array_1_0 (XDR *xdrs, node_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_no_array_val, (u_int *) &objp->node_no_array_len, NSERVERS,
		sizeof (node_no), (xdrproc_t) xdr_node_no_1_0))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_array_1_0 (XDR *xdrs, synode_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_no_array_val, (u_int *) &objp->synode_no_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_no), (xdrproc_t) xdr_synode_no_1_0))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_uncommitted_list_1_0 (XDR *xdrs, uncommitted_list *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->active))
		 return FALSE;
	 if (!xdr_synode_no_array_1_0 (xdrs, &objp->vers))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_repository_1_0 (XDR *xdrs, repository *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_0 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_synode_no_array_1_0 (xdrs, &objp->msg_list))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_0 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_error_1_0 (XDR *xdrs, x_error *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->nodeid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->code))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->message, MAXERROR))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_data_1_0 (XDR *xdrs, trans_data *objp)
{
	register int32_t *buf;
	 if (!xdr_trans_id_1_0 (xdrs, &objp->tid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->pc))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->cluster_name, MAXNAME))
		 return FALSE;
	 if (!xdr_x_error_1_0 (xdrs, &objp->errmsg))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_1_0 (XDR *xdrs, leader *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_array_1_0 (XDR *xdrs, leader_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->leader_array_val, (u_int *) &objp->leader_array_len, NSERVERS,
		sizeof (leader), (xdrproc_t) xdr_leader_1_0))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_u_1_0 (XDR *xdrs, app_u *objp)
{
	register int32_t *buf;
	 if (!xdr_cargo_type_1_0 (xdrs, &objp->c_t))
		 return FALSE;
	switch (objp->c_t) {
	case unified_boot_type:
	case add_node_type:
	case remove_node_type:
	case force_config_type:
	case xcom_boot_type:
	case xcom_set_group:
		 if (!xdr_node_list_1_0 (xdrs, &objp->app_u_u.nodes))
			 return FALSE;
		break;
	case app_type:
		 if (!xdr_checked_data (xdrs, &objp->app_u_u.data))
			 return FALSE;
		break;
	case exit_type:
	case reset_type:
		break;
	case remove_reset_type:
		break;
	case begin_trans:
		break;
	case prepared_trans:
	case abort_trans:
		 if (!xdr_trans_data_1_0 (xdrs, &objp->app_u_u.td))
			 return FALSE;
		break;
	case view_msg:
		 if (!xdr_node_set_1_0 (xdrs, &objp->app_u_u.present))
			 return FALSE;
		break;
	case set_cache_limit:
		 if (!xdr_uint64_t (xdrs, &objp->app_u_u.cache_limit))
			 return FALSE;
		break;
	case get_event_horizon_type:
		break;
	case set_event_horizon_type:
		 if (!xdr_xcom_event_horizon_1_0 (xdrs, &objp->app_u_u.event_horizon))
			 return FALSE;
		break;
	case get_synode_app_data_type:
		 if (!xdr_synode_no_array_1_0 (xdrs, &objp->app_u_u.synodes))
			 return FALSE;
		break;
	case convert_into_local_server_type:
		break;
	case set_max_leaders:
		 if (!xdr_node_no_1_0 (xdrs, &objp->app_u_u.max_leaders))
			 return FALSE;
		break;
	case set_leaders_type:
		 if (!xdr_leader_array_1_0 (xdrs, &objp->app_u_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_app_data_1_0 (XDR *xdrs, app_data *objp)
{
	register int32_t *buf;
	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_synode_no_1_0 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_0 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_0 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		IXDR_PUT_BOOL(buf, objp->notused);
		IXDR_PUT_BOOL(buf, objp->log_it);
		IXDR_PUT_BOOL(buf, objp->chosen);
		}
		 if (!xdr_recover_action_1_0 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_0 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_0))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_synode_no_1_0 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_0 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_0 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		objp->notused = IXDR_GET_BOOL(buf);
		objp->log_it = IXDR_GET_BOOL(buf);
		objp->chosen = IXDR_GET_BOOL(buf);
		}
		 if (!xdr_recover_action_1_0 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_0 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_0))
			 return FALSE;
	 return TRUE;
	}
	 if (!xdr_synode_no_1_0 (xdrs, &objp->unique_id))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->lsn))
		 return FALSE;
	 if (!xdr_synode_no_1_0 (xdrs, &objp->app_key))
		 return FALSE;
	 if (!xdr_cons_type_1_0 (xdrs, &objp->consensus))
		 return FALSE;
	 if (!xdr_double (xdrs, &objp->expiry_time))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->notused))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->log_it))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->chosen))
		 return FALSE;
	 if (!xdr_recover_action_1_0 (xdrs, &objp->recover))
		 return FALSE;
	 if (!xdr_app_u_1_0 (xdrs, &objp->body))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_0))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_1_0 (XDR *xdrs, app_data_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data), (xdrproc_t) xdr_app_data_1_0))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_array_1_0 (XDR *xdrs, app_data_ptr_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->app_data_ptr_array_val, (u_int *) &objp->app_data_ptr_array_len, MAX_APP_PTR_ARRAY,
		sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_0))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_list_1_0 (XDR *xdrs, app_data_list *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_0))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_key_range_1_0 (XDR *xdrs, key_range *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_0 (xdrs, &objp->k1))
		 return FALSE;
	 if (!xdr_synode_no_1_0 (xdrs, &objp->k2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_ballot_1_0 (XDR *xdrs, ballot *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_node_no_1_0 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_snapshot_1_0 (XDR *xdrs, snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_0 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_app_data_ptr_array_1_0 (xdrs, &objp->snap))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_0 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config_1_0 (XDR *xdrs, config *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_0 (xdrs, &objp->start))
		 return FALSE;
	 if (!xdr_synode_no_1_0 (xdrs, &objp->boot_key))
		 return FALSE;
	 if (!xdr_node_list_1_0 (xdrs, &objp->nodes))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->global_node_set.node_set_len = 0;
 objp->global_node_set.node_set_val = 0;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->event_horizon = EVENT_HORIZON_MIN;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->max_active_leaders = 0; /* Set active leaders to all as default */
 synthesize_leaders(&objp->leaders); /* Install all nodes as leaders */
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_config_ptr_1_0 (XDR *xdrs, config_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (config), (xdrproc_t) xdr_config_1_0))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_configs_1_0 (XDR *xdrs, configs *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->configs_val, (u_int *) &objp->configs_len, MAX_SITE_DEFS,
		sizeof (config_ptr), (xdrproc_t) xdr_config_ptr_1_0))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_gcs_snapshot_1_0 (XDR *xdrs, gcs_snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_0 (xdrs, &objp->log_start))
		 return FALSE;
	 if (!xdr_configs_1_0 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_blob_1_0 (xdrs, &objp->app_snap))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->log_end = null_synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_1_0 (XDR *xdrs, synode_app_data *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_0 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_checked_data (xdrs, &objp->data))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->origin = objp->synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_array_1_0 (XDR *xdrs, synode_app_data_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_app_data_array_val, (u_int *) &objp->synode_app_data_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_app_data), (xdrproc_t) xdr_synode_app_data_1_0))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_type_1_0 (XDR *xdrs, reply_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_info_data_1_0 (XDR *xdrs, leader_info_data *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_0 (xdrs, &objp->max_nr_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_0 (xdrs, &objp->preferred_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_0 (xdrs, &objp->actual_leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_data_1_0 (XDR *xdrs, reply_data *objp)
{
	register int32_t *buf;
	 if (!xdr_reply_type_1_0 (xdrs, &objp->rt))
		 return FALSE;
	switch (objp->rt) {
	case leader_info:
		 if (!xdr_leader_info_data_1_0 (xdrs, &objp->reply_data_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_pax_msg_1_0 (XDR *xdrs, pax_msg *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_0 (xdrs, &objp->to))
		 return FALSE;
	 if (!xdr_node_no_1_0 (xdrs, &objp->from))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_synode_no_1_0 (xdrs, &objp->max_synode))
		 return FALSE;
	 if (!xdr_start_t_1_0 (xdrs, &objp->start_type))
		 return FALSE;
	 if (!xdr_ballot_1_0 (xdrs, &objp->reply_to))
		 return FALSE;
	 if (!xdr_ballot_1_0 (xdrs, &objp->proposal))
		 return FALSE;
	 if (!xdr_pax_op_1_0 (xdrs, &objp->op))
		 return FALSE;
	 if (!xdr_synode_no_1_0 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_pax_msg_type_1_0 (xdrs, &objp->msg_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->receivers, sizeof (bit_set), (xdrproc_t) xdr_bit_set_1_0))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->a, sizeof (app_data), (xdrproc_t) xdr_app_data_1_0))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->snap, sizeof (snapshot), (xdrproc_t) xdr_snapshot_1_0))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->gcs_snap, sizeof (gcs_snapshot), (xdrproc_t) xdr_gcs_snapshot_1_0))
		 return FALSE;
	 if (!xdr_client_reply_code_1_0 (xdrs, &objp->cli_err))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->force_delivery))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->refcnt))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->delivered_msg = get_delivered_msg(); /* Use our own minimum */
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->event_horizon = 0;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->requested_synode_app_data.synode_app_data_array_len = 0;
 objp->requested_synode_app_data.synode_app_data_array_val = NULL;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->rd = NULL;
 }
/* END protocol conversion code */
	return TRUE;
}
/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */
#include "xcom_vp.h"
/* Copyright (c) 2010, 2024, Oracle and/or its affiliates.
   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.
   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.
   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */
#include "xcom/xcom_vp_platform.h"
#include "xcom/xcom_limits.h"
#include "xcom/xcom_profile.h"
extern synode_no const null_synode;
extern synode_no get_delivered_msg();
#ifndef _WIN32
#include <strings.h> /* For bzero */
#endif
bool_t
xdr_xcom_proto_1_1 (XDR *xdrs, xcom_proto *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_delivery_status_1_1 (XDR *xdrs, delivery_status *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cons_type_1_1 (XDR *xdrs, cons_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cargo_type_1_1 (XDR *xdrs, cargo_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_recover_action_1_1 (XDR *xdrs, recover_action *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_op_1_1 (XDR *xdrs, pax_op *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_msg_type_1_1 (XDR *xdrs, pax_msg_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_client_reply_code_1_1 (XDR *xdrs, client_reply_code *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_start_t_1_1 (XDR *xdrs, start_t *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_xcom_event_horizon_1_1 (XDR *xdrs, xcom_event_horizon *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_1_1 (XDR *xdrs, node_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_set_1_1 (XDR *xdrs, node_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_set_val, (u_int *) &objp->node_set_len, NSERVERS,
		sizeof (bool_t), (xdrproc_t) xdr_bool))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_mask_1_1 (XDR *xdrs, bit_mask *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_set_1_1 (XDR *xdrs, bit_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->bits.bits_val, (u_int *) &objp->bits.bits_len, NSERVERS,
		sizeof (bit_mask), (xdrproc_t) xdr_bit_mask_1_1))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_blob_1_1 (XDR *xdrs, blob *objp)
{
	register int32_t *buf;
	 if (!xdr_bytes (xdrs, (char **)&objp->data.data_val, (u_int *) &objp->data.data_len, MAXBLOB))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_proto_range_1_1 (XDR *xdrs, x_proto_range *objp)
{
	register int32_t *buf;
	 if (!xdr_xcom_proto_1_1 (xdrs, &objp->min_proto))
		 return FALSE;
	 if (!xdr_xcom_proto_1_1 (xdrs, &objp->max_proto))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_1_1 (XDR *xdrs, synode_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->msgno))
		 return FALSE;
	 if (!xdr_node_no_1_1 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_id_1_1 (XDR *xdrs, trans_id *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_1 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pc))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_paxos_role_1_1 (XDR *xdrs, paxos_role *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_address_1_1 (XDR *xdrs, node_address *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	 if (!xdr_blob_1_1 (xdrs, &objp->uuid))
		 return FALSE;
	 if (!xdr_x_proto_range_1_1 (xdrs, &objp->proto))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->services = P_PROP | P_ACC | P_LEARN;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_node_list_1_1 (XDR *xdrs, node_list *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_list_val, (u_int *) &objp->node_list_len, NSERVERS,
		sizeof (node_address), (xdrproc_t) xdr_node_address_1_1))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_array_1_1 (XDR *xdrs, node_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_no_array_val, (u_int *) &objp->node_no_array_len, NSERVERS,
		sizeof (node_no), (xdrproc_t) xdr_node_no_1_1))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_array_1_1 (XDR *xdrs, synode_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_no_array_val, (u_int *) &objp->synode_no_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_no), (xdrproc_t) xdr_synode_no_1_1))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_uncommitted_list_1_1 (XDR *xdrs, uncommitted_list *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->active))
		 return FALSE;
	 if (!xdr_synode_no_array_1_1 (xdrs, &objp->vers))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_repository_1_1 (XDR *xdrs, repository *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_1 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_synode_no_array_1_1 (xdrs, &objp->msg_list))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_1 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_error_1_1 (XDR *xdrs, x_error *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->nodeid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->code))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->message, MAXERROR))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_data_1_1 (XDR *xdrs, trans_data *objp)
{
	register int32_t *buf;
	 if (!xdr_trans_id_1_1 (xdrs, &objp->tid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->pc))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->cluster_name, MAXNAME))
		 return FALSE;
	 if (!xdr_x_error_1_1 (xdrs, &objp->errmsg))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_1_1 (XDR *xdrs, leader *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_array_1_1 (XDR *xdrs, leader_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->leader_array_val, (u_int *) &objp->leader_array_len, NSERVERS,
		sizeof (leader), (xdrproc_t) xdr_leader_1_1))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_u_1_1 (XDR *xdrs, app_u *objp)
{
	register int32_t *buf;
	 if (!xdr_cargo_type_1_1 (xdrs, &objp->c_t))
		 return FALSE;
	switch (objp->c_t) {
	case unified_boot_type:
	case add_node_type:
	case remove_node_type:
	case force_config_type:
	case xcom_boot_type:
	case xcom_set_group:
		 if (!xdr_node_list_1_1 (xdrs, &objp->app_u_u.nodes))
			 return FALSE;
		break;
	case app_type:
		 if (!xdr_checked_data (xdrs, &objp->app_u_u.data))
			 return FALSE;
		break;
	case exit_type:
	case reset_type:
		break;
	case remove_reset_type:
		break;
	case begin_trans:
		break;
	case prepared_trans:
	case abort_trans:
		 if (!xdr_trans_data_1_1 (xdrs, &objp->app_u_u.td))
			 return FALSE;
		break;
	case view_msg:
		 if (!xdr_node_set_1_1 (xdrs, &objp->app_u_u.present))
			 return FALSE;
		break;
	case set_cache_limit:
		 if (!xdr_uint64_t (xdrs, &objp->app_u_u.cache_limit))
			 return FALSE;
		break;
	case get_event_horizon_type:
		break;
	case set_event_horizon_type:
		 if (!xdr_xcom_event_horizon_1_1 (xdrs, &objp->app_u_u.event_horizon))
			 return FALSE;
		break;
	case get_synode_app_data_type:
		 if (!xdr_synode_no_array_1_1 (xdrs, &objp->app_u_u.synodes))
			 return FALSE;
		break;
	case convert_into_local_server_type:
		break;
	case set_max_leaders:
		 if (!xdr_node_no_1_1 (xdrs, &objp->app_u_u.max_leaders))
			 return FALSE;
		break;
	case set_leaders_type:
		 if (!xdr_leader_array_1_1 (xdrs, &objp->app_u_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_app_data_1_1 (XDR *xdrs, app_data *objp)
{
	register int32_t *buf;
	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_synode_no_1_1 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_1 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_1 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		IXDR_PUT_BOOL(buf, objp->notused);
		IXDR_PUT_BOOL(buf, objp->log_it);
		IXDR_PUT_BOOL(buf, objp->chosen);
		}
		 if (!xdr_recover_action_1_1 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_1 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_1))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_synode_no_1_1 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_1 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_1 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		objp->notused = IXDR_GET_BOOL(buf);
		objp->log_it = IXDR_GET_BOOL(buf);
		objp->chosen = IXDR_GET_BOOL(buf);
		}
		 if (!xdr_recover_action_1_1 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_1 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_1))
			 return FALSE;
	 return TRUE;
	}
	 if (!xdr_synode_no_1_1 (xdrs, &objp->unique_id))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->lsn))
		 return FALSE;
	 if (!xdr_synode_no_1_1 (xdrs, &objp->app_key))
		 return FALSE;
	 if (!xdr_cons_type_1_1 (xdrs, &objp->consensus))
		 return FALSE;
	 if (!xdr_double (xdrs, &objp->expiry_time))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->notused))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->log_it))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->chosen))
		 return FALSE;
	 if (!xdr_recover_action_1_1 (xdrs, &objp->recover))
		 return FALSE;
	 if (!xdr_app_u_1_1 (xdrs, &objp->body))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_1))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_1_1 (XDR *xdrs, app_data_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data), (xdrproc_t) xdr_app_data_1_1))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_array_1_1 (XDR *xdrs, app_data_ptr_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->app_data_ptr_array_val, (u_int *) &objp->app_data_ptr_array_len, MAX_APP_PTR_ARRAY,
		sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_1))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_list_1_1 (XDR *xdrs, app_data_list *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_1))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_key_range_1_1 (XDR *xdrs, key_range *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_1 (xdrs, &objp->k1))
		 return FALSE;
	 if (!xdr_synode_no_1_1 (xdrs, &objp->k2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_ballot_1_1 (XDR *xdrs, ballot *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_node_no_1_1 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_snapshot_1_1 (XDR *xdrs, snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_1 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_app_data_ptr_array_1_1 (xdrs, &objp->snap))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_1 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config_1_1 (XDR *xdrs, config *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_1 (xdrs, &objp->start))
		 return FALSE;
	 if (!xdr_synode_no_1_1 (xdrs, &objp->boot_key))
		 return FALSE;
	 if (!xdr_node_list_1_1 (xdrs, &objp->nodes))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->global_node_set.node_set_len = 0;
 objp->global_node_set.node_set_val = 0;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->event_horizon = EVENT_HORIZON_MIN;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->max_active_leaders = 0; /* Set active leaders to all as default */
 synthesize_leaders(&objp->leaders); /* Install all nodes as leaders */
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_config_ptr_1_1 (XDR *xdrs, config_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (config), (xdrproc_t) xdr_config_1_1))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_configs_1_1 (XDR *xdrs, configs *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->configs_val, (u_int *) &objp->configs_len, MAX_SITE_DEFS,
		sizeof (config_ptr), (xdrproc_t) xdr_config_ptr_1_1))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_gcs_snapshot_1_1 (XDR *xdrs, gcs_snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_1 (xdrs, &objp->log_start))
		 return FALSE;
	 if (!xdr_configs_1_1 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_blob_1_1 (xdrs, &objp->app_snap))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->log_end = null_synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_1_1 (XDR *xdrs, synode_app_data *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_1 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_checked_data (xdrs, &objp->data))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->origin = objp->synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_array_1_1 (XDR *xdrs, synode_app_data_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_app_data_array_val, (u_int *) &objp->synode_app_data_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_app_data), (xdrproc_t) xdr_synode_app_data_1_1))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_type_1_1 (XDR *xdrs, reply_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_info_data_1_1 (XDR *xdrs, leader_info_data *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_1 (xdrs, &objp->max_nr_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_1 (xdrs, &objp->preferred_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_1 (xdrs, &objp->actual_leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_data_1_1 (XDR *xdrs, reply_data *objp)
{
	register int32_t *buf;
	 if (!xdr_reply_type_1_1 (xdrs, &objp->rt))
		 return FALSE;
	switch (objp->rt) {
	case leader_info:
		 if (!xdr_leader_info_data_1_1 (xdrs, &objp->reply_data_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_pax_msg_1_1 (XDR *xdrs, pax_msg *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_1 (xdrs, &objp->to))
		 return FALSE;
	 if (!xdr_node_no_1_1 (xdrs, &objp->from))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_synode_no_1_1 (xdrs, &objp->max_synode))
		 return FALSE;
	 if (!xdr_start_t_1_1 (xdrs, &objp->start_type))
		 return FALSE;
	 if (!xdr_ballot_1_1 (xdrs, &objp->reply_to))
		 return FALSE;
	 if (!xdr_ballot_1_1 (xdrs, &objp->proposal))
		 return FALSE;
	 if (!xdr_pax_op_1_1 (xdrs, &objp->op))
		 return FALSE;
	 if (!xdr_synode_no_1_1 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_pax_msg_type_1_1 (xdrs, &objp->msg_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->receivers, sizeof (bit_set), (xdrproc_t) xdr_bit_set_1_1))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->a, sizeof (app_data), (xdrproc_t) xdr_app_data_1_1))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->snap, sizeof (snapshot), (xdrproc_t) xdr_snapshot_1_1))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->gcs_snap, sizeof (gcs_snapshot), (xdrproc_t) xdr_gcs_snapshot_1_1))
		 return FALSE;
	 if (!xdr_client_reply_code_1_1 (xdrs, &objp->cli_err))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->force_delivery))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->refcnt))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->delivered_msg = get_delivered_msg(); /* Use our own minimum */
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->event_horizon = 0;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->requested_synode_app_data.synode_app_data_array_len = 0;
 objp->requested_synode_app_data.synode_app_data_array_val = NULL;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->rd = NULL;
 }
/* END protocol conversion code */
	return TRUE;
}
/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */
#include "xcom_vp.h"
/* Copyright (c) 2010, 2024, Oracle and/or its affiliates.
   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.
   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.
   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */
#include "xcom/xcom_vp_platform.h"
#include "xcom/xcom_limits.h"
#include "xcom/xcom_profile.h"
extern synode_no const null_synode;
extern synode_no get_delivered_msg();
#ifndef _WIN32
#include <strings.h> /* For bzero */
#endif
bool_t
xdr_xcom_proto_1_2 (XDR *xdrs, xcom_proto *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_delivery_status_1_2 (XDR *xdrs, delivery_status *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cons_type_1_2 (XDR *xdrs, cons_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cargo_type_1_2 (XDR *xdrs, cargo_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_recover_action_1_2 (XDR *xdrs, recover_action *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_op_1_2 (XDR *xdrs, pax_op *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_msg_type_1_2 (XDR *xdrs, pax_msg_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_client_reply_code_1_2 (XDR *xdrs, client_reply_code *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_start_t_1_2 (XDR *xdrs, start_t *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_xcom_event_horizon_1_2 (XDR *xdrs, xcom_event_horizon *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_1_2 (XDR *xdrs, node_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_set_1_2 (XDR *xdrs, node_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_set_val, (u_int *) &objp->node_set_len, NSERVERS,
		sizeof (bool_t), (xdrproc_t) xdr_bool))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_mask_1_2 (XDR *xdrs, bit_mask *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_set_1_2 (XDR *xdrs, bit_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->bits.bits_val, (u_int *) &objp->bits.bits_len, NSERVERS,
		sizeof (bit_mask), (xdrproc_t) xdr_bit_mask_1_2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_blob_1_2 (XDR *xdrs, blob *objp)
{
	register int32_t *buf;
	 if (!xdr_bytes (xdrs, (char **)&objp->data.data_val, (u_int *) &objp->data.data_len, MAXBLOB))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_proto_range_1_2 (XDR *xdrs, x_proto_range *objp)
{
	register int32_t *buf;
	 if (!xdr_xcom_proto_1_2 (xdrs, &objp->min_proto))
		 return FALSE;
	 if (!xdr_xcom_proto_1_2 (xdrs, &objp->max_proto))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_1_2 (XDR *xdrs, synode_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->msgno))
		 return FALSE;
	 if (!xdr_node_no_1_2 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_id_1_2 (XDR *xdrs, trans_id *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pc))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_paxos_role_1_2 (XDR *xdrs, paxos_role *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_address_1_2 (XDR *xdrs, node_address *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	 if (!xdr_blob_1_2 (xdrs, &objp->uuid))
		 return FALSE;
	 if (!xdr_x_proto_range_1_2 (xdrs, &objp->proto))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->services = P_PROP | P_ACC | P_LEARN;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_node_list_1_2 (XDR *xdrs, node_list *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_list_val, (u_int *) &objp->node_list_len, NSERVERS,
		sizeof (node_address), (xdrproc_t) xdr_node_address_1_2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_array_1_2 (XDR *xdrs, node_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_no_array_val, (u_int *) &objp->node_no_array_len, NSERVERS,
		sizeof (node_no), (xdrproc_t) xdr_node_no_1_2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_array_1_2 (XDR *xdrs, synode_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_no_array_val, (u_int *) &objp->synode_no_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_no), (xdrproc_t) xdr_synode_no_1_2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_uncommitted_list_1_2 (XDR *xdrs, uncommitted_list *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->active))
		 return FALSE;
	 if (!xdr_synode_no_array_1_2 (xdrs, &objp->vers))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_repository_1_2 (XDR *xdrs, repository *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_synode_no_array_1_2 (xdrs, &objp->msg_list))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_2 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_error_1_2 (XDR *xdrs, x_error *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->nodeid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->code))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->message, MAXERROR))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_data_1_2 (XDR *xdrs, trans_data *objp)
{
	register int32_t *buf;
	 if (!xdr_trans_id_1_2 (xdrs, &objp->tid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->pc))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->cluster_name, MAXNAME))
		 return FALSE;
	 if (!xdr_x_error_1_2 (xdrs, &objp->errmsg))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_1_2 (XDR *xdrs, leader *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_array_1_2 (XDR *xdrs, leader_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->leader_array_val, (u_int *) &objp->leader_array_len, NSERVERS,
		sizeof (leader), (xdrproc_t) xdr_leader_1_2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_u_1_2 (XDR *xdrs, app_u *objp)
{
	register int32_t *buf;
	 if (!xdr_cargo_type_1_2 (xdrs, &objp->c_t))
		 return FALSE;
	switch (objp->c_t) {
	case unified_boot_type:
	case add_node_type:
	case remove_node_type:
	case force_config_type:
	case xcom_boot_type:
	case xcom_set_group:
		 if (!xdr_node_list_1_2 (xdrs, &objp->app_u_u.nodes))
			 return FALSE;
		break;
	case app_type:
		 if (!xdr_checked_data (xdrs, &objp->app_u_u.data))
			 return FALSE;
		break;
	case exit_type:
	case reset_type:
		break;
	case remove_reset_type:
		break;
	case begin_trans:
		break;
	case prepared_trans:
	case abort_trans:
		 if (!xdr_trans_data_1_2 (xdrs, &objp->app_u_u.td))
			 return FALSE;
		break;
	case view_msg:
		 if (!xdr_node_set_1_2 (xdrs, &objp->app_u_u.present))
			 return FALSE;
		break;
	case set_cache_limit:
		 if (!xdr_uint64_t (xdrs, &objp->app_u_u.cache_limit))
			 return FALSE;
		break;
	case get_event_horizon_type:
		break;
	case set_event_horizon_type:
		 if (!xdr_xcom_event_horizon_1_2 (xdrs, &objp->app_u_u.event_horizon))
			 return FALSE;
		break;
	case get_synode_app_data_type:
		 if (!xdr_synode_no_array_1_2 (xdrs, &objp->app_u_u.synodes))
			 return FALSE;
		break;
	case convert_into_local_server_type:
		break;
	case set_max_leaders:
		 if (!xdr_node_no_1_2 (xdrs, &objp->app_u_u.max_leaders))
			 return FALSE;
		break;
	case set_leaders_type:
		 if (!xdr_leader_array_1_2 (xdrs, &objp->app_u_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_app_data_1_2 (XDR *xdrs, app_data *objp)
{
	register int32_t *buf;
	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_synode_no_1_2 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_2 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_2 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		IXDR_PUT_BOOL(buf, objp->notused);
		IXDR_PUT_BOOL(buf, objp->log_it);
		IXDR_PUT_BOOL(buf, objp->chosen);
		}
		 if (!xdr_recover_action_1_2 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_2 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_2))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_synode_no_1_2 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_2 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_2 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		objp->notused = IXDR_GET_BOOL(buf);
		objp->log_it = IXDR_GET_BOOL(buf);
		objp->chosen = IXDR_GET_BOOL(buf);
		}
		 if (!xdr_recover_action_1_2 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_2 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_2))
			 return FALSE;
	 return TRUE;
	}
	 if (!xdr_synode_no_1_2 (xdrs, &objp->unique_id))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->lsn))
		 return FALSE;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->app_key))
		 return FALSE;
	 if (!xdr_cons_type_1_2 (xdrs, &objp->consensus))
		 return FALSE;
	 if (!xdr_double (xdrs, &objp->expiry_time))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->notused))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->log_it))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->chosen))
		 return FALSE;
	 if (!xdr_recover_action_1_2 (xdrs, &objp->recover))
		 return FALSE;
	 if (!xdr_app_u_1_2 (xdrs, &objp->body))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_1_2 (XDR *xdrs, app_data_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data), (xdrproc_t) xdr_app_data_1_2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_array_1_2 (XDR *xdrs, app_data_ptr_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->app_data_ptr_array_val, (u_int *) &objp->app_data_ptr_array_len, MAX_APP_PTR_ARRAY,
		sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_list_1_2 (XDR *xdrs, app_data_list *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_key_range_1_2 (XDR *xdrs, key_range *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->k1))
		 return FALSE;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->k2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_ballot_1_2 (XDR *xdrs, ballot *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_node_no_1_2 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_snapshot_1_2 (XDR *xdrs, snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_app_data_ptr_array_1_2 (xdrs, &objp->snap))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_2 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config_1_2 (XDR *xdrs, config *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->start))
		 return FALSE;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->boot_key))
		 return FALSE;
	 if (!xdr_node_list_1_2 (xdrs, &objp->nodes))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->global_node_set.node_set_len = 0;
 objp->global_node_set.node_set_val = 0;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->event_horizon = EVENT_HORIZON_MIN;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->max_active_leaders = 0; /* Set active leaders to all as default */
 synthesize_leaders(&objp->leaders); /* Install all nodes as leaders */
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_config_ptr_1_2 (XDR *xdrs, config_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (config), (xdrproc_t) xdr_config_1_2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_configs_1_2 (XDR *xdrs, configs *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->configs_val, (u_int *) &objp->configs_len, MAX_SITE_DEFS,
		sizeof (config_ptr), (xdrproc_t) xdr_config_ptr_1_2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_gcs_snapshot_1_2 (XDR *xdrs, gcs_snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->log_start))
		 return FALSE;
	 if (!xdr_configs_1_2 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_blob_1_2 (xdrs, &objp->app_snap))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->log_end = null_synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_1_2 (XDR *xdrs, synode_app_data *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_checked_data (xdrs, &objp->data))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->origin = objp->synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_array_1_2 (XDR *xdrs, synode_app_data_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_app_data_array_val, (u_int *) &objp->synode_app_data_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_app_data), (xdrproc_t) xdr_synode_app_data_1_2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_type_1_2 (XDR *xdrs, reply_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_info_data_1_2 (XDR *xdrs, leader_info_data *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_2 (xdrs, &objp->max_nr_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_2 (xdrs, &objp->preferred_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_2 (xdrs, &objp->actual_leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_data_1_2 (XDR *xdrs, reply_data *objp)
{
	register int32_t *buf;
	 if (!xdr_reply_type_1_2 (xdrs, &objp->rt))
		 return FALSE;
	switch (objp->rt) {
	case leader_info:
		 if (!xdr_leader_info_data_1_2 (xdrs, &objp->reply_data_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_pax_msg_1_2 (XDR *xdrs, pax_msg *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_2 (xdrs, &objp->to))
		 return FALSE;
	 if (!xdr_node_no_1_2 (xdrs, &objp->from))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->max_synode))
		 return FALSE;
	 if (!xdr_start_t_1_2 (xdrs, &objp->start_type))
		 return FALSE;
	 if (!xdr_ballot_1_2 (xdrs, &objp->reply_to))
		 return FALSE;
	 if (!xdr_ballot_1_2 (xdrs, &objp->proposal))
		 return FALSE;
	 if (!xdr_pax_op_1_2 (xdrs, &objp->op))
		 return FALSE;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_pax_msg_type_1_2 (xdrs, &objp->msg_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->receivers, sizeof (bit_set), (xdrproc_t) xdr_bit_set_1_2))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->a, sizeof (app_data), (xdrproc_t) xdr_app_data_1_2))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->snap, sizeof (snapshot), (xdrproc_t) xdr_snapshot_1_2))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->gcs_snap, sizeof (gcs_snapshot), (xdrproc_t) xdr_gcs_snapshot_1_2))
		 return FALSE;
	 if (!xdr_client_reply_code_1_2 (xdrs, &objp->cli_err))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->force_delivery))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->refcnt))
		 return FALSE;
	 if (!xdr_synode_no_1_2 (xdrs, &objp->delivered_msg))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->event_horizon = 0;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->requested_synode_app_data.synode_app_data_array_len = 0;
 objp->requested_synode_app_data.synode_app_data_array_val = NULL;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->rd = NULL;
 }
/* END protocol conversion code */
	return TRUE;
}
/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */
#include "xcom_vp.h"
/* Copyright (c) 2010, 2024, Oracle and/or its affiliates.
   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.
   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.
   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */
#include "xcom/xcom_vp_platform.h"
#include "xcom/xcom_limits.h"
#include "xcom/xcom_profile.h"
extern synode_no const null_synode;
extern synode_no get_delivered_msg();
#ifndef _WIN32
#include <strings.h> /* For bzero */
#endif
bool_t
xdr_xcom_proto_1_3 (XDR *xdrs, xcom_proto *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_delivery_status_1_3 (XDR *xdrs, delivery_status *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cons_type_1_3 (XDR *xdrs, cons_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cargo_type_1_3 (XDR *xdrs, cargo_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_recover_action_1_3 (XDR *xdrs, recover_action *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_op_1_3 (XDR *xdrs, pax_op *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_msg_type_1_3 (XDR *xdrs, pax_msg_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_client_reply_code_1_3 (XDR *xdrs, client_reply_code *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_start_t_1_3 (XDR *xdrs, start_t *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_xcom_event_horizon_1_3 (XDR *xdrs, xcom_event_horizon *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_1_3 (XDR *xdrs, node_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_set_1_3 (XDR *xdrs, node_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_set_val, (u_int *) &objp->node_set_len, NSERVERS,
		sizeof (bool_t), (xdrproc_t) xdr_bool))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_mask_1_3 (XDR *xdrs, bit_mask *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_set_1_3 (XDR *xdrs, bit_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->bits.bits_val, (u_int *) &objp->bits.bits_len, NSERVERS,
		sizeof (bit_mask), (xdrproc_t) xdr_bit_mask_1_3))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_blob_1_3 (XDR *xdrs, blob *objp)
{
	register int32_t *buf;
	 if (!xdr_bytes (xdrs, (char **)&objp->data.data_val, (u_int *) &objp->data.data_len, MAXBLOB))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_proto_range_1_3 (XDR *xdrs, x_proto_range *objp)
{
	register int32_t *buf;
	 if (!xdr_xcom_proto_1_3 (xdrs, &objp->min_proto))
		 return FALSE;
	 if (!xdr_xcom_proto_1_3 (xdrs, &objp->max_proto))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_1_3 (XDR *xdrs, synode_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->msgno))
		 return FALSE;
	 if (!xdr_node_no_1_3 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_id_1_3 (XDR *xdrs, trans_id *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pc))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_paxos_role_1_3 (XDR *xdrs, paxos_role *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_address_1_3 (XDR *xdrs, node_address *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	 if (!xdr_blob_1_3 (xdrs, &objp->uuid))
		 return FALSE;
	 if (!xdr_x_proto_range_1_3 (xdrs, &objp->proto))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->services = P_PROP | P_ACC | P_LEARN;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_node_list_1_3 (XDR *xdrs, node_list *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_list_val, (u_int *) &objp->node_list_len, NSERVERS,
		sizeof (node_address), (xdrproc_t) xdr_node_address_1_3))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_array_1_3 (XDR *xdrs, node_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_no_array_val, (u_int *) &objp->node_no_array_len, NSERVERS,
		sizeof (node_no), (xdrproc_t) xdr_node_no_1_3))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_array_1_3 (XDR *xdrs, synode_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_no_array_val, (u_int *) &objp->synode_no_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_no), (xdrproc_t) xdr_synode_no_1_3))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_uncommitted_list_1_3 (XDR *xdrs, uncommitted_list *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->active))
		 return FALSE;
	 if (!xdr_synode_no_array_1_3 (xdrs, &objp->vers))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_repository_1_3 (XDR *xdrs, repository *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_synode_no_array_1_3 (xdrs, &objp->msg_list))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_3 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_error_1_3 (XDR *xdrs, x_error *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->nodeid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->code))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->message, MAXERROR))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_data_1_3 (XDR *xdrs, trans_data *objp)
{
	register int32_t *buf;
	 if (!xdr_trans_id_1_3 (xdrs, &objp->tid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->pc))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->cluster_name, MAXNAME))
		 return FALSE;
	 if (!xdr_x_error_1_3 (xdrs, &objp->errmsg))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_1_3 (XDR *xdrs, leader *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_array_1_3 (XDR *xdrs, leader_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->leader_array_val, (u_int *) &objp->leader_array_len, NSERVERS,
		sizeof (leader), (xdrproc_t) xdr_leader_1_3))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_u_1_3 (XDR *xdrs, app_u *objp)
{
	register int32_t *buf;
	 if (!xdr_cargo_type_1_3 (xdrs, &objp->c_t))
		 return FALSE;
	switch (objp->c_t) {
	case unified_boot_type:
	case add_node_type:
	case remove_node_type:
	case force_config_type:
	case xcom_boot_type:
	case xcom_set_group:
		 if (!xdr_node_list_1_3 (xdrs, &objp->app_u_u.nodes))
			 return FALSE;
		break;
	case app_type:
		 if (!xdr_checked_data (xdrs, &objp->app_u_u.data))
			 return FALSE;
		break;
	case exit_type:
	case reset_type:
		break;
	case remove_reset_type:
		break;
	case begin_trans:
		break;
	case prepared_trans:
	case abort_trans:
		 if (!xdr_trans_data_1_3 (xdrs, &objp->app_u_u.td))
			 return FALSE;
		break;
	case view_msg:
		 if (!xdr_node_set_1_3 (xdrs, &objp->app_u_u.present))
			 return FALSE;
		break;
	case set_cache_limit:
		 if (!xdr_uint64_t (xdrs, &objp->app_u_u.cache_limit))
			 return FALSE;
		break;
	case get_event_horizon_type:
		break;
	case set_event_horizon_type:
		 if (!xdr_xcom_event_horizon_1_3 (xdrs, &objp->app_u_u.event_horizon))
			 return FALSE;
		break;
	case get_synode_app_data_type:
		 if (!xdr_synode_no_array_1_3 (xdrs, &objp->app_u_u.synodes))
			 return FALSE;
		break;
	case convert_into_local_server_type:
		break;
	case set_max_leaders:
		 if (!xdr_node_no_1_3 (xdrs, &objp->app_u_u.max_leaders))
			 return FALSE;
		break;
	case set_leaders_type:
		 if (!xdr_leader_array_1_3 (xdrs, &objp->app_u_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_app_data_1_3 (XDR *xdrs, app_data *objp)
{
	register int32_t *buf;
	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_synode_no_1_3 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_3 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_3 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		IXDR_PUT_BOOL(buf, objp->notused);
		IXDR_PUT_BOOL(buf, objp->log_it);
		IXDR_PUT_BOOL(buf, objp->chosen);
		}
		 if (!xdr_recover_action_1_3 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_3 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_3))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_synode_no_1_3 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_3 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_3 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		objp->notused = IXDR_GET_BOOL(buf);
		objp->log_it = IXDR_GET_BOOL(buf);
		objp->chosen = IXDR_GET_BOOL(buf);
		}
		 if (!xdr_recover_action_1_3 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_3 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_3))
			 return FALSE;
	 return TRUE;
	}
	 if (!xdr_synode_no_1_3 (xdrs, &objp->unique_id))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->lsn))
		 return FALSE;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->app_key))
		 return FALSE;
	 if (!xdr_cons_type_1_3 (xdrs, &objp->consensus))
		 return FALSE;
	 if (!xdr_double (xdrs, &objp->expiry_time))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->notused))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->log_it))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->chosen))
		 return FALSE;
	 if (!xdr_recover_action_1_3 (xdrs, &objp->recover))
		 return FALSE;
	 if (!xdr_app_u_1_3 (xdrs, &objp->body))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_3))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_1_3 (XDR *xdrs, app_data_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data), (xdrproc_t) xdr_app_data_1_3))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_array_1_3 (XDR *xdrs, app_data_ptr_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->app_data_ptr_array_val, (u_int *) &objp->app_data_ptr_array_len, MAX_APP_PTR_ARRAY,
		sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_3))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_list_1_3 (XDR *xdrs, app_data_list *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_3))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_key_range_1_3 (XDR *xdrs, key_range *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->k1))
		 return FALSE;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->k2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_ballot_1_3 (XDR *xdrs, ballot *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_node_no_1_3 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_snapshot_1_3 (XDR *xdrs, snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_app_data_ptr_array_1_3 (xdrs, &objp->snap))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_3 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config_1_3 (XDR *xdrs, config *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->start))
		 return FALSE;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->boot_key))
		 return FALSE;
	 if (!xdr_node_list_1_3 (xdrs, &objp->nodes))
		 return FALSE;
	 if (!xdr_node_set_1_3 (xdrs, &objp->global_node_set))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->event_horizon = EVENT_HORIZON_MIN;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->max_active_leaders = 0; /* Set active leaders to all as default */
 synthesize_leaders(&objp->leaders); /* Install all nodes as leaders */
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_config_ptr_1_3 (XDR *xdrs, config_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (config), (xdrproc_t) xdr_config_1_3))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_configs_1_3 (XDR *xdrs, configs *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->configs_val, (u_int *) &objp->configs_len, MAX_SITE_DEFS,
		sizeof (config_ptr), (xdrproc_t) xdr_config_ptr_1_3))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_gcs_snapshot_1_3 (XDR *xdrs, gcs_snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->log_start))
		 return FALSE;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->log_end))
		 return FALSE;
	 if (!xdr_configs_1_3 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_blob_1_3 (xdrs, &objp->app_snap))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_app_data_1_3 (XDR *xdrs, synode_app_data *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_checked_data (xdrs, &objp->data))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->origin = objp->synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_array_1_3 (XDR *xdrs, synode_app_data_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_app_data_array_val, (u_int *) &objp->synode_app_data_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_app_data), (xdrproc_t) xdr_synode_app_data_1_3))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_type_1_3 (XDR *xdrs, reply_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_info_data_1_3 (XDR *xdrs, leader_info_data *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_3 (xdrs, &objp->max_nr_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_3 (xdrs, &objp->preferred_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_3 (xdrs, &objp->actual_leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_data_1_3 (XDR *xdrs, reply_data *objp)
{
	register int32_t *buf;
	 if (!xdr_reply_type_1_3 (xdrs, &objp->rt))
		 return FALSE;
	switch (objp->rt) {
	case leader_info:
		 if (!xdr_leader_info_data_1_3 (xdrs, &objp->reply_data_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_pax_msg_1_3 (XDR *xdrs, pax_msg *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_3 (xdrs, &objp->to))
		 return FALSE;
	 if (!xdr_node_no_1_3 (xdrs, &objp->from))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->max_synode))
		 return FALSE;
	 if (!xdr_start_t_1_3 (xdrs, &objp->start_type))
		 return FALSE;
	 if (!xdr_ballot_1_3 (xdrs, &objp->reply_to))
		 return FALSE;
	 if (!xdr_ballot_1_3 (xdrs, &objp->proposal))
		 return FALSE;
	 if (!xdr_pax_op_1_3 (xdrs, &objp->op))
		 return FALSE;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_pax_msg_type_1_3 (xdrs, &objp->msg_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->receivers, sizeof (bit_set), (xdrproc_t) xdr_bit_set_1_3))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->a, sizeof (app_data), (xdrproc_t) xdr_app_data_1_3))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->snap, sizeof (snapshot), (xdrproc_t) xdr_snapshot_1_3))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->gcs_snap, sizeof (gcs_snapshot), (xdrproc_t) xdr_gcs_snapshot_1_3))
		 return FALSE;
	 if (!xdr_client_reply_code_1_3 (xdrs, &objp->cli_err))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->force_delivery))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->refcnt))
		 return FALSE;
	 if (!xdr_synode_no_1_3 (xdrs, &objp->delivered_msg))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->event_horizon = 0;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->requested_synode_app_data.synode_app_data_array_len = 0;
 objp->requested_synode_app_data.synode_app_data_array_val = NULL;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->rd = NULL;
 }
/* END protocol conversion code */
	return TRUE;
}
/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */
#include "xcom_vp.h"
/* Copyright (c) 2010, 2024, Oracle and/or its affiliates.
   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.
   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.
   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */
#include "xcom/xcom_vp_platform.h"
#include "xcom/xcom_limits.h"
#include "xcom/xcom_profile.h"
extern synode_no const null_synode;
extern synode_no get_delivered_msg();
#ifndef _WIN32
#include <strings.h> /* For bzero */
#endif
bool_t
xdr_xcom_proto_1_4 (XDR *xdrs, xcom_proto *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_delivery_status_1_4 (XDR *xdrs, delivery_status *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cons_type_1_4 (XDR *xdrs, cons_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cargo_type_1_4 (XDR *xdrs, cargo_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_recover_action_1_4 (XDR *xdrs, recover_action *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_op_1_4 (XDR *xdrs, pax_op *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_msg_type_1_4 (XDR *xdrs, pax_msg_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_client_reply_code_1_4 (XDR *xdrs, client_reply_code *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_start_t_1_4 (XDR *xdrs, start_t *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_xcom_event_horizon_1_4 (XDR *xdrs, xcom_event_horizon *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_1_4 (XDR *xdrs, node_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_set_1_4 (XDR *xdrs, node_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_set_val, (u_int *) &objp->node_set_len, NSERVERS,
		sizeof (bool_t), (xdrproc_t) xdr_bool))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_mask_1_4 (XDR *xdrs, bit_mask *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_set_1_4 (XDR *xdrs, bit_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->bits.bits_val, (u_int *) &objp->bits.bits_len, NSERVERS,
		sizeof (bit_mask), (xdrproc_t) xdr_bit_mask_1_4))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_blob_1_4 (XDR *xdrs, blob *objp)
{
	register int32_t *buf;
	 if (!xdr_bytes (xdrs, (char **)&objp->data.data_val, (u_int *) &objp->data.data_len, MAXBLOB))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_proto_range_1_4 (XDR *xdrs, x_proto_range *objp)
{
	register int32_t *buf;
	 if (!xdr_xcom_proto_1_4 (xdrs, &objp->min_proto))
		 return FALSE;
	 if (!xdr_xcom_proto_1_4 (xdrs, &objp->max_proto))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_1_4 (XDR *xdrs, synode_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->msgno))
		 return FALSE;
	 if (!xdr_node_no_1_4 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_id_1_4 (XDR *xdrs, trans_id *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pc))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_paxos_role_1_4 (XDR *xdrs, paxos_role *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_address_1_4 (XDR *xdrs, node_address *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	 if (!xdr_blob_1_4 (xdrs, &objp->uuid))
		 return FALSE;
	 if (!xdr_x_proto_range_1_4 (xdrs, &objp->proto))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->services = P_PROP | P_ACC | P_LEARN;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_node_list_1_4 (XDR *xdrs, node_list *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_list_val, (u_int *) &objp->node_list_len, NSERVERS,
		sizeof (node_address), (xdrproc_t) xdr_node_address_1_4))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_array_1_4 (XDR *xdrs, node_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_no_array_val, (u_int *) &objp->node_no_array_len, NSERVERS,
		sizeof (node_no), (xdrproc_t) xdr_node_no_1_4))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_array_1_4 (XDR *xdrs, synode_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_no_array_val, (u_int *) &objp->synode_no_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_no), (xdrproc_t) xdr_synode_no_1_4))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_uncommitted_list_1_4 (XDR *xdrs, uncommitted_list *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->active))
		 return FALSE;
	 if (!xdr_synode_no_array_1_4 (xdrs, &objp->vers))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_repository_1_4 (XDR *xdrs, repository *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_synode_no_array_1_4 (xdrs, &objp->msg_list))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_4 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_error_1_4 (XDR *xdrs, x_error *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->nodeid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->code))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->message, MAXERROR))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_data_1_4 (XDR *xdrs, trans_data *objp)
{
	register int32_t *buf;
	 if (!xdr_trans_id_1_4 (xdrs, &objp->tid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->pc))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->cluster_name, MAXNAME))
		 return FALSE;
	 if (!xdr_x_error_1_4 (xdrs, &objp->errmsg))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_1_4 (XDR *xdrs, leader *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_array_1_4 (XDR *xdrs, leader_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->leader_array_val, (u_int *) &objp->leader_array_len, NSERVERS,
		sizeof (leader), (xdrproc_t) xdr_leader_1_4))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_u_1_4 (XDR *xdrs, app_u *objp)
{
	register int32_t *buf;
	 if (!xdr_cargo_type_1_4 (xdrs, &objp->c_t))
		 return FALSE;
	switch (objp->c_t) {
	case unified_boot_type:
	case add_node_type:
	case remove_node_type:
	case force_config_type:
	case xcom_boot_type:
	case xcom_set_group:
		 if (!xdr_node_list_1_4 (xdrs, &objp->app_u_u.nodes))
			 return FALSE;
		break;
	case app_type:
		 if (!xdr_checked_data (xdrs, &objp->app_u_u.data))
			 return FALSE;
		break;
	case exit_type:
	case reset_type:
		break;
	case remove_reset_type:
		break;
	case begin_trans:
		break;
	case prepared_trans:
	case abort_trans:
		 if (!xdr_trans_data_1_4 (xdrs, &objp->app_u_u.td))
			 return FALSE;
		break;
	case view_msg:
		 if (!xdr_node_set_1_4 (xdrs, &objp->app_u_u.present))
			 return FALSE;
		break;
	case set_cache_limit:
		 if (!xdr_uint64_t (xdrs, &objp->app_u_u.cache_limit))
			 return FALSE;
		break;
	case get_event_horizon_type:
		break;
	case set_event_horizon_type:
		 if (!xdr_xcom_event_horizon_1_4 (xdrs, &objp->app_u_u.event_horizon))
			 return FALSE;
		break;
	case get_synode_app_data_type:
		 if (!xdr_synode_no_array_1_4 (xdrs, &objp->app_u_u.synodes))
			 return FALSE;
		break;
	case convert_into_local_server_type:
		break;
	case set_max_leaders:
		 if (!xdr_node_no_1_4 (xdrs, &objp->app_u_u.max_leaders))
			 return FALSE;
		break;
	case set_leaders_type:
		 if (!xdr_leader_array_1_4 (xdrs, &objp->app_u_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_app_data_1_4 (XDR *xdrs, app_data *objp)
{
	register int32_t *buf;
	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_synode_no_1_4 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_4 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_4 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		IXDR_PUT_BOOL(buf, objp->notused);
		IXDR_PUT_BOOL(buf, objp->log_it);
		IXDR_PUT_BOOL(buf, objp->chosen);
		}
		 if (!xdr_recover_action_1_4 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_4 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_4))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_synode_no_1_4 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_4 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_4 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		objp->notused = IXDR_GET_BOOL(buf);
		objp->log_it = IXDR_GET_BOOL(buf);
		objp->chosen = IXDR_GET_BOOL(buf);
		}
		 if (!xdr_recover_action_1_4 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_4 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_4))
			 return FALSE;
	 return TRUE;
	}
	 if (!xdr_synode_no_1_4 (xdrs, &objp->unique_id))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->lsn))
		 return FALSE;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->app_key))
		 return FALSE;
	 if (!xdr_cons_type_1_4 (xdrs, &objp->consensus))
		 return FALSE;
	 if (!xdr_double (xdrs, &objp->expiry_time))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->notused))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->log_it))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->chosen))
		 return FALSE;
	 if (!xdr_recover_action_1_4 (xdrs, &objp->recover))
		 return FALSE;
	 if (!xdr_app_u_1_4 (xdrs, &objp->body))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_4))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_1_4 (XDR *xdrs, app_data_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data), (xdrproc_t) xdr_app_data_1_4))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_array_1_4 (XDR *xdrs, app_data_ptr_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->app_data_ptr_array_val, (u_int *) &objp->app_data_ptr_array_len, MAX_APP_PTR_ARRAY,
		sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_4))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_list_1_4 (XDR *xdrs, app_data_list *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_4))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_key_range_1_4 (XDR *xdrs, key_range *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->k1))
		 return FALSE;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->k2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_ballot_1_4 (XDR *xdrs, ballot *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_node_no_1_4 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_snapshot_1_4 (XDR *xdrs, snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_app_data_ptr_array_1_4 (xdrs, &objp->snap))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_4 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config_1_4 (XDR *xdrs, config *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->start))
		 return FALSE;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->boot_key))
		 return FALSE;
	 if (!xdr_node_list_1_4 (xdrs, &objp->nodes))
		 return FALSE;
	 if (!xdr_xcom_event_horizon_1_4 (xdrs, &objp->event_horizon))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->global_node_set.node_set_len = 0;
 objp->global_node_set.node_set_val = 0;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->max_active_leaders = 0; /* Set active leaders to all as default */
 synthesize_leaders(&objp->leaders); /* Install all nodes as leaders */
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_config_ptr_1_4 (XDR *xdrs, config_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (config), (xdrproc_t) xdr_config_1_4))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_configs_1_4 (XDR *xdrs, configs *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->configs_val, (u_int *) &objp->configs_len, MAX_SITE_DEFS,
		sizeof (config_ptr), (xdrproc_t) xdr_config_ptr_1_4))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_gcs_snapshot_1_4 (XDR *xdrs, gcs_snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->log_start))
		 return FALSE;
	 if (!xdr_configs_1_4 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_blob_1_4 (xdrs, &objp->app_snap))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->log_end = null_synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_1_4 (XDR *xdrs, synode_app_data *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_checked_data (xdrs, &objp->data))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->origin = objp->synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_array_1_4 (XDR *xdrs, synode_app_data_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_app_data_array_val, (u_int *) &objp->synode_app_data_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_app_data), (xdrproc_t) xdr_synode_app_data_1_4))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_type_1_4 (XDR *xdrs, reply_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_info_data_1_4 (XDR *xdrs, leader_info_data *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_4 (xdrs, &objp->max_nr_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_4 (xdrs, &objp->preferred_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_4 (xdrs, &objp->actual_leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_data_1_4 (XDR *xdrs, reply_data *objp)
{
	register int32_t *buf;
	 if (!xdr_reply_type_1_4 (xdrs, &objp->rt))
		 return FALSE;
	switch (objp->rt) {
	case leader_info:
		 if (!xdr_leader_info_data_1_4 (xdrs, &objp->reply_data_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_pax_msg_1_4 (XDR *xdrs, pax_msg *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_4 (xdrs, &objp->to))
		 return FALSE;
	 if (!xdr_node_no_1_4 (xdrs, &objp->from))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->max_synode))
		 return FALSE;
	 if (!xdr_start_t_1_4 (xdrs, &objp->start_type))
		 return FALSE;
	 if (!xdr_ballot_1_4 (xdrs, &objp->reply_to))
		 return FALSE;
	 if (!xdr_ballot_1_4 (xdrs, &objp->proposal))
		 return FALSE;
	 if (!xdr_pax_op_1_4 (xdrs, &objp->op))
		 return FALSE;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_pax_msg_type_1_4 (xdrs, &objp->msg_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->receivers, sizeof (bit_set), (xdrproc_t) xdr_bit_set_1_4))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->a, sizeof (app_data), (xdrproc_t) xdr_app_data_1_4))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->snap, sizeof (snapshot), (xdrproc_t) xdr_snapshot_1_4))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->gcs_snap, sizeof (gcs_snapshot), (xdrproc_t) xdr_gcs_snapshot_1_4))
		 return FALSE;
	 if (!xdr_client_reply_code_1_4 (xdrs, &objp->cli_err))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->force_delivery))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->refcnt))
		 return FALSE;
	 if (!xdr_synode_no_1_4 (xdrs, &objp->delivered_msg))
		 return FALSE;
	 if (!xdr_xcom_event_horizon_1_4 (xdrs, &objp->event_horizon))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->requested_synode_app_data.synode_app_data_array_len = 0;
 objp->requested_synode_app_data.synode_app_data_array_val = NULL;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->rd = NULL;
 }
/* END protocol conversion code */
	return TRUE;
}
/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */
#include "xcom_vp.h"
/* Copyright (c) 2010, 2024, Oracle and/or its affiliates.
   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.
   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.
   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */
#include "xcom/xcom_vp_platform.h"
#include "xcom/xcom_limits.h"
#include "xcom/xcom_profile.h"
extern synode_no const null_synode;
extern synode_no get_delivered_msg();
#ifndef _WIN32
#include <strings.h> /* For bzero */
#endif
bool_t
xdr_xcom_proto_1_5 (XDR *xdrs, xcom_proto *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_delivery_status_1_5 (XDR *xdrs, delivery_status *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cons_type_1_5 (XDR *xdrs, cons_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cargo_type_1_5 (XDR *xdrs, cargo_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_recover_action_1_5 (XDR *xdrs, recover_action *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_op_1_5 (XDR *xdrs, pax_op *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_msg_type_1_5 (XDR *xdrs, pax_msg_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_client_reply_code_1_5 (XDR *xdrs, client_reply_code *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_start_t_1_5 (XDR *xdrs, start_t *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_xcom_event_horizon_1_5 (XDR *xdrs, xcom_event_horizon *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_1_5 (XDR *xdrs, node_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_set_1_5 (XDR *xdrs, node_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_set_val, (u_int *) &objp->node_set_len, NSERVERS,
		sizeof (bool_t), (xdrproc_t) xdr_bool))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_mask_1_5 (XDR *xdrs, bit_mask *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_set_1_5 (XDR *xdrs, bit_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->bits.bits_val, (u_int *) &objp->bits.bits_len, NSERVERS,
		sizeof (bit_mask), (xdrproc_t) xdr_bit_mask_1_5))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_blob_1_5 (XDR *xdrs, blob *objp)
{
	register int32_t *buf;
	 if (!xdr_bytes (xdrs, (char **)&objp->data.data_val, (u_int *) &objp->data.data_len, MAXBLOB))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_proto_range_1_5 (XDR *xdrs, x_proto_range *objp)
{
	register int32_t *buf;
	 if (!xdr_xcom_proto_1_5 (xdrs, &objp->min_proto))
		 return FALSE;
	 if (!xdr_xcom_proto_1_5 (xdrs, &objp->max_proto))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_1_5 (XDR *xdrs, synode_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->msgno))
		 return FALSE;
	 if (!xdr_node_no_1_5 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_id_1_5 (XDR *xdrs, trans_id *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pc))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_paxos_role_1_5 (XDR *xdrs, paxos_role *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_address_1_5 (XDR *xdrs, node_address *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	 if (!xdr_blob_1_5 (xdrs, &objp->uuid))
		 return FALSE;
	 if (!xdr_x_proto_range_1_5 (xdrs, &objp->proto))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->services = P_PROP | P_ACC | P_LEARN;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_node_list_1_5 (XDR *xdrs, node_list *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_list_val, (u_int *) &objp->node_list_len, NSERVERS,
		sizeof (node_address), (xdrproc_t) xdr_node_address_1_5))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_array_1_5 (XDR *xdrs, node_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_no_array_val, (u_int *) &objp->node_no_array_len, NSERVERS,
		sizeof (node_no), (xdrproc_t) xdr_node_no_1_5))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_array_1_5 (XDR *xdrs, synode_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_no_array_val, (u_int *) &objp->synode_no_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_no), (xdrproc_t) xdr_synode_no_1_5))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_uncommitted_list_1_5 (XDR *xdrs, uncommitted_list *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->active))
		 return FALSE;
	 if (!xdr_synode_no_array_1_5 (xdrs, &objp->vers))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_repository_1_5 (XDR *xdrs, repository *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_synode_no_array_1_5 (xdrs, &objp->msg_list))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_5 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_error_1_5 (XDR *xdrs, x_error *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->nodeid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->code))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->message, MAXERROR))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_data_1_5 (XDR *xdrs, trans_data *objp)
{
	register int32_t *buf;
	 if (!xdr_trans_id_1_5 (xdrs, &objp->tid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->pc))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->cluster_name, MAXNAME))
		 return FALSE;
	 if (!xdr_x_error_1_5 (xdrs, &objp->errmsg))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_1_5 (XDR *xdrs, leader *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_array_1_5 (XDR *xdrs, leader_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->leader_array_val, (u_int *) &objp->leader_array_len, NSERVERS,
		sizeof (leader), (xdrproc_t) xdr_leader_1_5))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_u_1_5 (XDR *xdrs, app_u *objp)
{
	register int32_t *buf;
	 if (!xdr_cargo_type_1_5 (xdrs, &objp->c_t))
		 return FALSE;
	switch (objp->c_t) {
	case unified_boot_type:
	case add_node_type:
	case remove_node_type:
	case force_config_type:
	case xcom_boot_type:
	case xcom_set_group:
		 if (!xdr_node_list_1_5 (xdrs, &objp->app_u_u.nodes))
			 return FALSE;
		break;
	case app_type:
		 if (!xdr_checked_data (xdrs, &objp->app_u_u.data))
			 return FALSE;
		break;
	case exit_type:
	case reset_type:
		break;
	case remove_reset_type:
		break;
	case begin_trans:
		break;
	case prepared_trans:
	case abort_trans:
		 if (!xdr_trans_data_1_5 (xdrs, &objp->app_u_u.td))
			 return FALSE;
		break;
	case view_msg:
		 if (!xdr_node_set_1_5 (xdrs, &objp->app_u_u.present))
			 return FALSE;
		break;
	case set_cache_limit:
		 if (!xdr_uint64_t (xdrs, &objp->app_u_u.cache_limit))
			 return FALSE;
		break;
	case get_event_horizon_type:
		break;
	case set_event_horizon_type:
		 if (!xdr_xcom_event_horizon_1_5 (xdrs, &objp->app_u_u.event_horizon))
			 return FALSE;
		break;
	case get_synode_app_data_type:
		 if (!xdr_synode_no_array_1_5 (xdrs, &objp->app_u_u.synodes))
			 return FALSE;
		break;
	case convert_into_local_server_type:
		break;
	case set_max_leaders:
		 if (!xdr_node_no_1_5 (xdrs, &objp->app_u_u.max_leaders))
			 return FALSE;
		break;
	case set_leaders_type:
		 if (!xdr_leader_array_1_5 (xdrs, &objp->app_u_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_app_data_1_5 (XDR *xdrs, app_data *objp)
{
	register int32_t *buf;
	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_synode_no_1_5 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_5 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_5 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		IXDR_PUT_BOOL(buf, objp->notused);
		IXDR_PUT_BOOL(buf, objp->log_it);
		IXDR_PUT_BOOL(buf, objp->chosen);
		}
		 if (!xdr_recover_action_1_5 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_5 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_5))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_synode_no_1_5 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_5 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_5 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		objp->notused = IXDR_GET_BOOL(buf);
		objp->log_it = IXDR_GET_BOOL(buf);
		objp->chosen = IXDR_GET_BOOL(buf);
		}
		 if (!xdr_recover_action_1_5 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_5 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_5))
			 return FALSE;
	 return TRUE;
	}
	 if (!xdr_synode_no_1_5 (xdrs, &objp->unique_id))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->lsn))
		 return FALSE;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->app_key))
		 return FALSE;
	 if (!xdr_cons_type_1_5 (xdrs, &objp->consensus))
		 return FALSE;
	 if (!xdr_double (xdrs, &objp->expiry_time))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->notused))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->log_it))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->chosen))
		 return FALSE;
	 if (!xdr_recover_action_1_5 (xdrs, &objp->recover))
		 return FALSE;
	 if (!xdr_app_u_1_5 (xdrs, &objp->body))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_5))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_1_5 (XDR *xdrs, app_data_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data), (xdrproc_t) xdr_app_data_1_5))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_array_1_5 (XDR *xdrs, app_data_ptr_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->app_data_ptr_array_val, (u_int *) &objp->app_data_ptr_array_len, MAX_APP_PTR_ARRAY,
		sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_5))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_list_1_5 (XDR *xdrs, app_data_list *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_5))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_key_range_1_5 (XDR *xdrs, key_range *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->k1))
		 return FALSE;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->k2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_ballot_1_5 (XDR *xdrs, ballot *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_node_no_1_5 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_snapshot_1_5 (XDR *xdrs, snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_app_data_ptr_array_1_5 (xdrs, &objp->snap))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_5 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config_1_5 (XDR *xdrs, config *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->start))
		 return FALSE;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->boot_key))
		 return FALSE;
	 if (!xdr_node_list_1_5 (xdrs, &objp->nodes))
		 return FALSE;
	 if (!xdr_xcom_event_horizon_1_5 (xdrs, &objp->event_horizon))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->global_node_set.node_set_len = 0;
 objp->global_node_set.node_set_val = 0;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->max_active_leaders = 0; /* Set active leaders to all as default */
 synthesize_leaders(&objp->leaders); /* Install all nodes as leaders */
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_config_ptr_1_5 (XDR *xdrs, config_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (config), (xdrproc_t) xdr_config_1_5))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_configs_1_5 (XDR *xdrs, configs *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->configs_val, (u_int *) &objp->configs_len, MAX_SITE_DEFS,
		sizeof (config_ptr), (xdrproc_t) xdr_config_ptr_1_5))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_gcs_snapshot_1_5 (XDR *xdrs, gcs_snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->log_start))
		 return FALSE;
	 if (!xdr_configs_1_5 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_blob_1_5 (xdrs, &objp->app_snap))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->log_end = null_synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_1_5 (XDR *xdrs, synode_app_data *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_checked_data (xdrs, &objp->data))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->origin = objp->synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_array_1_5 (XDR *xdrs, synode_app_data_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_app_data_array_val, (u_int *) &objp->synode_app_data_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_app_data), (xdrproc_t) xdr_synode_app_data_1_5))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_type_1_5 (XDR *xdrs, reply_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_info_data_1_5 (XDR *xdrs, leader_info_data *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_5 (xdrs, &objp->max_nr_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_5 (xdrs, &objp->preferred_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_5 (xdrs, &objp->actual_leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_data_1_5 (XDR *xdrs, reply_data *objp)
{
	register int32_t *buf;
	 if (!xdr_reply_type_1_5 (xdrs, &objp->rt))
		 return FALSE;
	switch (objp->rt) {
	case leader_info:
		 if (!xdr_leader_info_data_1_5 (xdrs, &objp->reply_data_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_pax_msg_1_5 (XDR *xdrs, pax_msg *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_5 (xdrs, &objp->to))
		 return FALSE;
	 if (!xdr_node_no_1_5 (xdrs, &objp->from))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->max_synode))
		 return FALSE;
	 if (!xdr_start_t_1_5 (xdrs, &objp->start_type))
		 return FALSE;
	 if (!xdr_ballot_1_5 (xdrs, &objp->reply_to))
		 return FALSE;
	 if (!xdr_ballot_1_5 (xdrs, &objp->proposal))
		 return FALSE;
	 if (!xdr_pax_op_1_5 (xdrs, &objp->op))
		 return FALSE;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_pax_msg_type_1_5 (xdrs, &objp->msg_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->receivers, sizeof (bit_set), (xdrproc_t) xdr_bit_set_1_5))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->a, sizeof (app_data), (xdrproc_t) xdr_app_data_1_5))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->snap, sizeof (snapshot), (xdrproc_t) xdr_snapshot_1_5))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->gcs_snap, sizeof (gcs_snapshot), (xdrproc_t) xdr_gcs_snapshot_1_5))
		 return FALSE;
	 if (!xdr_client_reply_code_1_5 (xdrs, &objp->cli_err))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->force_delivery))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->refcnt))
		 return FALSE;
	 if (!xdr_synode_no_1_5 (xdrs, &objp->delivered_msg))
		 return FALSE;
	 if (!xdr_xcom_event_horizon_1_5 (xdrs, &objp->event_horizon))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->requested_synode_app_data.synode_app_data_array_len = 0;
 objp->requested_synode_app_data.synode_app_data_array_val = NULL;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->rd = NULL;
 }
/* END protocol conversion code */
	return TRUE;
}
/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */
#include "xcom_vp.h"
/* Copyright (c) 2010, 2024, Oracle and/or its affiliates.
   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.
   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.
   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */
#include "xcom/xcom_vp_platform.h"
#include "xcom/xcom_limits.h"
#include "xcom/xcom_profile.h"
extern synode_no const null_synode;
extern synode_no get_delivered_msg();
#ifndef _WIN32
#include <strings.h> /* For bzero */
#endif
bool_t
xdr_xcom_proto_1_6 (XDR *xdrs, xcom_proto *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_delivery_status_1_6 (XDR *xdrs, delivery_status *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cons_type_1_6 (XDR *xdrs, cons_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cargo_type_1_6 (XDR *xdrs, cargo_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_recover_action_1_6 (XDR *xdrs, recover_action *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_op_1_6 (XDR *xdrs, pax_op *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_msg_type_1_6 (XDR *xdrs, pax_msg_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_client_reply_code_1_6 (XDR *xdrs, client_reply_code *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_start_t_1_6 (XDR *xdrs, start_t *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_xcom_event_horizon_1_6 (XDR *xdrs, xcom_event_horizon *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_1_6 (XDR *xdrs, node_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_set_1_6 (XDR *xdrs, node_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_set_val, (u_int *) &objp->node_set_len, NSERVERS,
		sizeof (bool_t), (xdrproc_t) xdr_bool))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_mask_1_6 (XDR *xdrs, bit_mask *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_set_1_6 (XDR *xdrs, bit_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->bits.bits_val, (u_int *) &objp->bits.bits_len, NSERVERS,
		sizeof (bit_mask), (xdrproc_t) xdr_bit_mask_1_6))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_blob_1_6 (XDR *xdrs, blob *objp)
{
	register int32_t *buf;
	 if (!xdr_bytes (xdrs, (char **)&objp->data.data_val, (u_int *) &objp->data.data_len, MAXBLOB))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_proto_range_1_6 (XDR *xdrs, x_proto_range *objp)
{
	register int32_t *buf;
	 if (!xdr_xcom_proto_1_6 (xdrs, &objp->min_proto))
		 return FALSE;
	 if (!xdr_xcom_proto_1_6 (xdrs, &objp->max_proto))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_1_6 (XDR *xdrs, synode_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->msgno))
		 return FALSE;
	 if (!xdr_node_no_1_6 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_id_1_6 (XDR *xdrs, trans_id *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pc))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_paxos_role_1_6 (XDR *xdrs, paxos_role *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_address_1_6 (XDR *xdrs, node_address *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	 if (!xdr_blob_1_6 (xdrs, &objp->uuid))
		 return FALSE;
	 if (!xdr_x_proto_range_1_6 (xdrs, &objp->proto))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->services = P_PROP | P_ACC | P_LEARN;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_node_list_1_6 (XDR *xdrs, node_list *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_list_val, (u_int *) &objp->node_list_len, NSERVERS,
		sizeof (node_address), (xdrproc_t) xdr_node_address_1_6))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_array_1_6 (XDR *xdrs, node_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_no_array_val, (u_int *) &objp->node_no_array_len, NSERVERS,
		sizeof (node_no), (xdrproc_t) xdr_node_no_1_6))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_array_1_6 (XDR *xdrs, synode_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_no_array_val, (u_int *) &objp->synode_no_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_no), (xdrproc_t) xdr_synode_no_1_6))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_uncommitted_list_1_6 (XDR *xdrs, uncommitted_list *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->active))
		 return FALSE;
	 if (!xdr_synode_no_array_1_6 (xdrs, &objp->vers))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_repository_1_6 (XDR *xdrs, repository *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_synode_no_array_1_6 (xdrs, &objp->msg_list))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_6 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_error_1_6 (XDR *xdrs, x_error *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->nodeid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->code))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->message, MAXERROR))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_data_1_6 (XDR *xdrs, trans_data *objp)
{
	register int32_t *buf;
	 if (!xdr_trans_id_1_6 (xdrs, &objp->tid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->pc))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->cluster_name, MAXNAME))
		 return FALSE;
	 if (!xdr_x_error_1_6 (xdrs, &objp->errmsg))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_1_6 (XDR *xdrs, leader *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_array_1_6 (XDR *xdrs, leader_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->leader_array_val, (u_int *) &objp->leader_array_len, NSERVERS,
		sizeof (leader), (xdrproc_t) xdr_leader_1_6))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_u_1_6 (XDR *xdrs, app_u *objp)
{
	register int32_t *buf;
	 if (!xdr_cargo_type_1_6 (xdrs, &objp->c_t))
		 return FALSE;
	switch (objp->c_t) {
	case unified_boot_type:
	case add_node_type:
	case remove_node_type:
	case force_config_type:
	case xcom_boot_type:
	case xcom_set_group:
		 if (!xdr_node_list_1_6 (xdrs, &objp->app_u_u.nodes))
			 return FALSE;
		break;
	case app_type:
		 if (!xdr_checked_data (xdrs, &objp->app_u_u.data))
			 return FALSE;
		break;
	case exit_type:
	case reset_type:
		break;
	case remove_reset_type:
		break;
	case begin_trans:
		break;
	case prepared_trans:
	case abort_trans:
		 if (!xdr_trans_data_1_6 (xdrs, &objp->app_u_u.td))
			 return FALSE;
		break;
	case view_msg:
		 if (!xdr_node_set_1_6 (xdrs, &objp->app_u_u.present))
			 return FALSE;
		break;
	case set_cache_limit:
		 if (!xdr_uint64_t (xdrs, &objp->app_u_u.cache_limit))
			 return FALSE;
		break;
	case get_event_horizon_type:
		break;
	case set_event_horizon_type:
		 if (!xdr_xcom_event_horizon_1_6 (xdrs, &objp->app_u_u.event_horizon))
			 return FALSE;
		break;
	case get_synode_app_data_type:
		 if (!xdr_synode_no_array_1_6 (xdrs, &objp->app_u_u.synodes))
			 return FALSE;
		break;
	case convert_into_local_server_type:
		break;
	case set_max_leaders:
		 if (!xdr_node_no_1_6 (xdrs, &objp->app_u_u.max_leaders))
			 return FALSE;
		break;
	case set_leaders_type:
		 if (!xdr_leader_array_1_6 (xdrs, &objp->app_u_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_app_data_1_6 (XDR *xdrs, app_data *objp)
{
	register int32_t *buf;
	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_synode_no_1_6 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_6 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_6 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		IXDR_PUT_BOOL(buf, objp->notused);
		IXDR_PUT_BOOL(buf, objp->log_it);
		IXDR_PUT_BOOL(buf, objp->chosen);
		}
		 if (!xdr_recover_action_1_6 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_6 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_6))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_synode_no_1_6 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_6 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_6 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		objp->notused = IXDR_GET_BOOL(buf);
		objp->log_it = IXDR_GET_BOOL(buf);
		objp->chosen = IXDR_GET_BOOL(buf);
		}
		 if (!xdr_recover_action_1_6 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_6 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_6))
			 return FALSE;
	 return TRUE;
	}
	 if (!xdr_synode_no_1_6 (xdrs, &objp->unique_id))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->lsn))
		 return FALSE;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->app_key))
		 return FALSE;
	 if (!xdr_cons_type_1_6 (xdrs, &objp->consensus))
		 return FALSE;
	 if (!xdr_double (xdrs, &objp->expiry_time))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->notused))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->log_it))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->chosen))
		 return FALSE;
	 if (!xdr_recover_action_1_6 (xdrs, &objp->recover))
		 return FALSE;
	 if (!xdr_app_u_1_6 (xdrs, &objp->body))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_6))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_1_6 (XDR *xdrs, app_data_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data), (xdrproc_t) xdr_app_data_1_6))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_array_1_6 (XDR *xdrs, app_data_ptr_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->app_data_ptr_array_val, (u_int *) &objp->app_data_ptr_array_len, MAX_APP_PTR_ARRAY,
		sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_6))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_list_1_6 (XDR *xdrs, app_data_list *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_6))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_key_range_1_6 (XDR *xdrs, key_range *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->k1))
		 return FALSE;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->k2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_ballot_1_6 (XDR *xdrs, ballot *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_node_no_1_6 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_snapshot_1_6 (XDR *xdrs, snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_app_data_ptr_array_1_6 (xdrs, &objp->snap))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_6 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config_1_6 (XDR *xdrs, config *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->start))
		 return FALSE;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->boot_key))
		 return FALSE;
	 if (!xdr_node_list_1_6 (xdrs, &objp->nodes))
		 return FALSE;
	 if (!xdr_xcom_event_horizon_1_6 (xdrs, &objp->event_horizon))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->global_node_set.node_set_len = 0;
 objp->global_node_set.node_set_val = 0;
 }
 if (xdrs->x_op == XDR_DECODE) {
 objp->max_active_leaders = 0; /* Set active leaders to all as default */
 synthesize_leaders(&objp->leaders); /* Install all nodes as leaders */
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_config_ptr_1_6 (XDR *xdrs, config_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (config), (xdrproc_t) xdr_config_1_6))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_configs_1_6 (XDR *xdrs, configs *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->configs_val, (u_int *) &objp->configs_len, MAX_SITE_DEFS,
		sizeof (config_ptr), (xdrproc_t) xdr_config_ptr_1_6))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_gcs_snapshot_1_6 (XDR *xdrs, gcs_snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->log_start))
		 return FALSE;
	 if (!xdr_configs_1_6 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_blob_1_6 (xdrs, &objp->app_snap))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->log_end = null_synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_1_6 (XDR *xdrs, synode_app_data *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_checked_data (xdrs, &objp->data))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->origin = objp->synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_array_1_6 (XDR *xdrs, synode_app_data_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_app_data_array_val, (u_int *) &objp->synode_app_data_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_app_data), (xdrproc_t) xdr_synode_app_data_1_6))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_type_1_6 (XDR *xdrs, reply_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_info_data_1_6 (XDR *xdrs, leader_info_data *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_6 (xdrs, &objp->max_nr_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_6 (xdrs, &objp->preferred_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_6 (xdrs, &objp->actual_leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_data_1_6 (XDR *xdrs, reply_data *objp)
{
	register int32_t *buf;
	 if (!xdr_reply_type_1_6 (xdrs, &objp->rt))
		 return FALSE;
	switch (objp->rt) {
	case leader_info:
		 if (!xdr_leader_info_data_1_6 (xdrs, &objp->reply_data_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_pax_msg_1_6 (XDR *xdrs, pax_msg *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_6 (xdrs, &objp->to))
		 return FALSE;
	 if (!xdr_node_no_1_6 (xdrs, &objp->from))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->max_synode))
		 return FALSE;
	 if (!xdr_start_t_1_6 (xdrs, &objp->start_type))
		 return FALSE;
	 if (!xdr_ballot_1_6 (xdrs, &objp->reply_to))
		 return FALSE;
	 if (!xdr_ballot_1_6 (xdrs, &objp->proposal))
		 return FALSE;
	 if (!xdr_pax_op_1_6 (xdrs, &objp->op))
		 return FALSE;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_pax_msg_type_1_6 (xdrs, &objp->msg_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->receivers, sizeof (bit_set), (xdrproc_t) xdr_bit_set_1_6))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->a, sizeof (app_data), (xdrproc_t) xdr_app_data_1_6))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->snap, sizeof (snapshot), (xdrproc_t) xdr_snapshot_1_6))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->gcs_snap, sizeof (gcs_snapshot), (xdrproc_t) xdr_gcs_snapshot_1_6))
		 return FALSE;
	 if (!xdr_client_reply_code_1_6 (xdrs, &objp->cli_err))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->force_delivery))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->refcnt))
		 return FALSE;
	 if (!xdr_synode_no_1_6 (xdrs, &objp->delivered_msg))
		 return FALSE;
	 if (!xdr_xcom_event_horizon_1_6 (xdrs, &objp->event_horizon))
		 return FALSE;
	 if (!xdr_synode_app_data_array_1_6 (xdrs, &objp->requested_synode_app_data))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->rd = NULL;
 }
/* END protocol conversion code */
	return TRUE;
}
/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */
#include "xcom_vp.h"
/* Copyright (c) 2010, 2024, Oracle and/or its affiliates.
   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.
   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.
   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */
#include "xcom/xcom_vp_platform.h"
#include "xcom/xcom_limits.h"
#include "xcom/xcom_profile.h"
extern synode_no const null_synode;
extern synode_no get_delivered_msg();
#ifndef _WIN32
#include <strings.h> /* For bzero */
#endif
bool_t
xdr_xcom_proto_1_7 (XDR *xdrs, xcom_proto *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_delivery_status_1_7 (XDR *xdrs, delivery_status *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cons_type_1_7 (XDR *xdrs, cons_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cargo_type_1_7 (XDR *xdrs, cargo_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_recover_action_1_7 (XDR *xdrs, recover_action *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_op_1_7 (XDR *xdrs, pax_op *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_msg_type_1_7 (XDR *xdrs, pax_msg_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_client_reply_code_1_7 (XDR *xdrs, client_reply_code *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_start_t_1_7 (XDR *xdrs, start_t *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_xcom_event_horizon_1_7 (XDR *xdrs, xcom_event_horizon *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_1_7 (XDR *xdrs, node_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_set_1_7 (XDR *xdrs, node_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_set_val, (u_int *) &objp->node_set_len, NSERVERS,
		sizeof (bool_t), (xdrproc_t) xdr_bool))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_mask_1_7 (XDR *xdrs, bit_mask *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_set_1_7 (XDR *xdrs, bit_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->bits.bits_val, (u_int *) &objp->bits.bits_len, NSERVERS,
		sizeof (bit_mask), (xdrproc_t) xdr_bit_mask_1_7))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_blob_1_7 (XDR *xdrs, blob *objp)
{
	register int32_t *buf;
	 if (!xdr_bytes (xdrs, (char **)&objp->data.data_val, (u_int *) &objp->data.data_len, MAXBLOB))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_proto_range_1_7 (XDR *xdrs, x_proto_range *objp)
{
	register int32_t *buf;
	 if (!xdr_xcom_proto_1_7 (xdrs, &objp->min_proto))
		 return FALSE;
	 if (!xdr_xcom_proto_1_7 (xdrs, &objp->max_proto))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_1_7 (XDR *xdrs, synode_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->msgno))
		 return FALSE;
	 if (!xdr_node_no_1_7 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_id_1_7 (XDR *xdrs, trans_id *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pc))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_paxos_role_1_7 (XDR *xdrs, paxos_role *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_address_1_7 (XDR *xdrs, node_address *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	 if (!xdr_blob_1_7 (xdrs, &objp->uuid))
		 return FALSE;
	 if (!xdr_x_proto_range_1_7 (xdrs, &objp->proto))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->services = P_PROP | P_ACC | P_LEARN;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_node_list_1_7 (XDR *xdrs, node_list *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_list_val, (u_int *) &objp->node_list_len, NSERVERS,
		sizeof (node_address), (xdrproc_t) xdr_node_address_1_7))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_array_1_7 (XDR *xdrs, node_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_no_array_val, (u_int *) &objp->node_no_array_len, NSERVERS,
		sizeof (node_no), (xdrproc_t) xdr_node_no_1_7))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_array_1_7 (XDR *xdrs, synode_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_no_array_val, (u_int *) &objp->synode_no_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_no), (xdrproc_t) xdr_synode_no_1_7))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_uncommitted_list_1_7 (XDR *xdrs, uncommitted_list *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->active))
		 return FALSE;
	 if (!xdr_synode_no_array_1_7 (xdrs, &objp->vers))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_repository_1_7 (XDR *xdrs, repository *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_synode_no_array_1_7 (xdrs, &objp->msg_list))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_7 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_error_1_7 (XDR *xdrs, x_error *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->nodeid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->code))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->message, MAXERROR))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_data_1_7 (XDR *xdrs, trans_data *objp)
{
	register int32_t *buf;
	 if (!xdr_trans_id_1_7 (xdrs, &objp->tid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->pc))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->cluster_name, MAXNAME))
		 return FALSE;
	 if (!xdr_x_error_1_7 (xdrs, &objp->errmsg))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_1_7 (XDR *xdrs, leader *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_array_1_7 (XDR *xdrs, leader_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->leader_array_val, (u_int *) &objp->leader_array_len, NSERVERS,
		sizeof (leader), (xdrproc_t) xdr_leader_1_7))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_u_1_7 (XDR *xdrs, app_u *objp)
{
	register int32_t *buf;
	 if (!xdr_cargo_type_1_7 (xdrs, &objp->c_t))
		 return FALSE;
	switch (objp->c_t) {
	case unified_boot_type:
	case add_node_type:
	case remove_node_type:
	case force_config_type:
	case xcom_boot_type:
	case xcom_set_group:
		 if (!xdr_node_list_1_7 (xdrs, &objp->app_u_u.nodes))
			 return FALSE;
		break;
	case app_type:
		 if (!xdr_checked_data (xdrs, &objp->app_u_u.data))
			 return FALSE;
		break;
	case exit_type:
	case reset_type:
		break;
	case remove_reset_type:
		break;
	case begin_trans:
		break;
	case prepared_trans:
	case abort_trans:
		 if (!xdr_trans_data_1_7 (xdrs, &objp->app_u_u.td))
			 return FALSE;
		break;
	case view_msg:
		 if (!xdr_node_set_1_7 (xdrs, &objp->app_u_u.present))
			 return FALSE;
		break;
	case set_cache_limit:
		 if (!xdr_uint64_t (xdrs, &objp->app_u_u.cache_limit))
			 return FALSE;
		break;
	case get_event_horizon_type:
		break;
	case set_event_horizon_type:
		 if (!xdr_xcom_event_horizon_1_7 (xdrs, &objp->app_u_u.event_horizon))
			 return FALSE;
		break;
	case get_synode_app_data_type:
		 if (!xdr_synode_no_array_1_7 (xdrs, &objp->app_u_u.synodes))
			 return FALSE;
		break;
	case convert_into_local_server_type:
		break;
	case set_max_leaders:
		 if (!xdr_node_no_1_7 (xdrs, &objp->app_u_u.max_leaders))
			 return FALSE;
		break;
	case set_leaders_type:
		 if (!xdr_leader_array_1_7 (xdrs, &objp->app_u_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_app_data_1_7 (XDR *xdrs, app_data *objp)
{
	register int32_t *buf;
	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_synode_no_1_7 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_7 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_7 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		IXDR_PUT_BOOL(buf, objp->notused);
		IXDR_PUT_BOOL(buf, objp->log_it);
		IXDR_PUT_BOOL(buf, objp->chosen);
		}
		 if (!xdr_recover_action_1_7 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_7 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_7))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_synode_no_1_7 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_7 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_7 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		objp->notused = IXDR_GET_BOOL(buf);
		objp->log_it = IXDR_GET_BOOL(buf);
		objp->chosen = IXDR_GET_BOOL(buf);
		}
		 if (!xdr_recover_action_1_7 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_7 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_7))
			 return FALSE;
	 return TRUE;
	}
	 if (!xdr_synode_no_1_7 (xdrs, &objp->unique_id))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->lsn))
		 return FALSE;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->app_key))
		 return FALSE;
	 if (!xdr_cons_type_1_7 (xdrs, &objp->consensus))
		 return FALSE;
	 if (!xdr_double (xdrs, &objp->expiry_time))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->notused))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->log_it))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->chosen))
		 return FALSE;
	 if (!xdr_recover_action_1_7 (xdrs, &objp->recover))
		 return FALSE;
	 if (!xdr_app_u_1_7 (xdrs, &objp->body))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_7))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_1_7 (XDR *xdrs, app_data_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data), (xdrproc_t) xdr_app_data_1_7))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_array_1_7 (XDR *xdrs, app_data_ptr_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->app_data_ptr_array_val, (u_int *) &objp->app_data_ptr_array_len, MAX_APP_PTR_ARRAY,
		sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_7))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_list_1_7 (XDR *xdrs, app_data_list *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_7))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_key_range_1_7 (XDR *xdrs, key_range *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->k1))
		 return FALSE;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->k2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_ballot_1_7 (XDR *xdrs, ballot *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_node_no_1_7 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_snapshot_1_7 (XDR *xdrs, snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_app_data_ptr_array_1_7 (xdrs, &objp->snap))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_7 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config_1_7 (XDR *xdrs, config *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->start))
		 return FALSE;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->boot_key))
		 return FALSE;
	 if (!xdr_node_list_1_7 (xdrs, &objp->nodes))
		 return FALSE;
	 if (!xdr_node_set_1_7 (xdrs, &objp->global_node_set))
		 return FALSE;
	 if (!xdr_xcom_event_horizon_1_7 (xdrs, &objp->event_horizon))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->max_active_leaders = 0; /* Set active leaders to all as default */
 synthesize_leaders(&objp->leaders); /* Install all nodes as leaders */
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_config_ptr_1_7 (XDR *xdrs, config_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (config), (xdrproc_t) xdr_config_1_7))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_configs_1_7 (XDR *xdrs, configs *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->configs_val, (u_int *) &objp->configs_len, MAX_SITE_DEFS,
		sizeof (config_ptr), (xdrproc_t) xdr_config_ptr_1_7))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_gcs_snapshot_1_7 (XDR *xdrs, gcs_snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->log_start))
		 return FALSE;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->log_end))
		 return FALSE;
	 if (!xdr_configs_1_7 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_blob_1_7 (xdrs, &objp->app_snap))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_app_data_1_7 (XDR *xdrs, synode_app_data *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_checked_data (xdrs, &objp->data))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->origin = objp->synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_array_1_7 (XDR *xdrs, synode_app_data_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_app_data_array_val, (u_int *) &objp->synode_app_data_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_app_data), (xdrproc_t) xdr_synode_app_data_1_7))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_type_1_7 (XDR *xdrs, reply_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_info_data_1_7 (XDR *xdrs, leader_info_data *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_7 (xdrs, &objp->max_nr_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_7 (xdrs, &objp->preferred_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_7 (xdrs, &objp->actual_leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_data_1_7 (XDR *xdrs, reply_data *objp)
{
	register int32_t *buf;
	 if (!xdr_reply_type_1_7 (xdrs, &objp->rt))
		 return FALSE;
	switch (objp->rt) {
	case leader_info:
		 if (!xdr_leader_info_data_1_7 (xdrs, &objp->reply_data_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_pax_msg_1_7 (XDR *xdrs, pax_msg *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_7 (xdrs, &objp->to))
		 return FALSE;
	 if (!xdr_node_no_1_7 (xdrs, &objp->from))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->max_synode))
		 return FALSE;
	 if (!xdr_start_t_1_7 (xdrs, &objp->start_type))
		 return FALSE;
	 if (!xdr_ballot_1_7 (xdrs, &objp->reply_to))
		 return FALSE;
	 if (!xdr_ballot_1_7 (xdrs, &objp->proposal))
		 return FALSE;
	 if (!xdr_pax_op_1_7 (xdrs, &objp->op))
		 return FALSE;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_pax_msg_type_1_7 (xdrs, &objp->msg_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->receivers, sizeof (bit_set), (xdrproc_t) xdr_bit_set_1_7))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->a, sizeof (app_data), (xdrproc_t) xdr_app_data_1_7))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->snap, sizeof (snapshot), (xdrproc_t) xdr_snapshot_1_7))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->gcs_snap, sizeof (gcs_snapshot), (xdrproc_t) xdr_gcs_snapshot_1_7))
		 return FALSE;
	 if (!xdr_client_reply_code_1_7 (xdrs, &objp->cli_err))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->force_delivery))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->refcnt))
		 return FALSE;
	 if (!xdr_synode_no_1_7 (xdrs, &objp->delivered_msg))
		 return FALSE;
	 if (!xdr_xcom_event_horizon_1_7 (xdrs, &objp->event_horizon))
		 return FALSE;
	 if (!xdr_synode_app_data_array_1_7 (xdrs, &objp->requested_synode_app_data))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->rd = NULL;
 }
/* END protocol conversion code */
	return TRUE;
}
/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */
#include "xcom_vp.h"
/* Copyright (c) 2010, 2024, Oracle and/or its affiliates.
   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.
   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.
   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */
#include "xcom/xcom_vp_platform.h"
#include "xcom/xcom_limits.h"
#include "xcom/xcom_profile.h"
extern synode_no const null_synode;
extern synode_no get_delivered_msg();
#ifndef _WIN32
#include <strings.h> /* For bzero */
#endif
bool_t
xdr_xcom_proto_1_8 (XDR *xdrs, xcom_proto *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_delivery_status_1_8 (XDR *xdrs, delivery_status *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cons_type_1_8 (XDR *xdrs, cons_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cargo_type_1_8 (XDR *xdrs, cargo_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_recover_action_1_8 (XDR *xdrs, recover_action *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_op_1_8 (XDR *xdrs, pax_op *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_msg_type_1_8 (XDR *xdrs, pax_msg_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_client_reply_code_1_8 (XDR *xdrs, client_reply_code *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_start_t_1_8 (XDR *xdrs, start_t *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_xcom_event_horizon_1_8 (XDR *xdrs, xcom_event_horizon *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_1_8 (XDR *xdrs, node_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_set_1_8 (XDR *xdrs, node_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_set_val, (u_int *) &objp->node_set_len, NSERVERS,
		sizeof (bool_t), (xdrproc_t) xdr_bool))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_mask_1_8 (XDR *xdrs, bit_mask *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_set_1_8 (XDR *xdrs, bit_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->bits.bits_val, (u_int *) &objp->bits.bits_len, NSERVERS,
		sizeof (bit_mask), (xdrproc_t) xdr_bit_mask_1_8))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_blob_1_8 (XDR *xdrs, blob *objp)
{
	register int32_t *buf;
	 if (!xdr_bytes (xdrs, (char **)&objp->data.data_val, (u_int *) &objp->data.data_len, MAXBLOB))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_proto_range_1_8 (XDR *xdrs, x_proto_range *objp)
{
	register int32_t *buf;
	 if (!xdr_xcom_proto_1_8 (xdrs, &objp->min_proto))
		 return FALSE;
	 if (!xdr_xcom_proto_1_8 (xdrs, &objp->max_proto))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_1_8 (XDR *xdrs, synode_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->msgno))
		 return FALSE;
	 if (!xdr_node_no_1_8 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_id_1_8 (XDR *xdrs, trans_id *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pc))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_paxos_role_1_8 (XDR *xdrs, paxos_role *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_address_1_8 (XDR *xdrs, node_address *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	 if (!xdr_blob_1_8 (xdrs, &objp->uuid))
		 return FALSE;
	 if (!xdr_x_proto_range_1_8 (xdrs, &objp->proto))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->services = P_PROP | P_ACC | P_LEARN;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_node_list_1_8 (XDR *xdrs, node_list *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_list_val, (u_int *) &objp->node_list_len, NSERVERS,
		sizeof (node_address), (xdrproc_t) xdr_node_address_1_8))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_array_1_8 (XDR *xdrs, node_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_no_array_val, (u_int *) &objp->node_no_array_len, NSERVERS,
		sizeof (node_no), (xdrproc_t) xdr_node_no_1_8))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_array_1_8 (XDR *xdrs, synode_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_no_array_val, (u_int *) &objp->synode_no_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_no), (xdrproc_t) xdr_synode_no_1_8))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_uncommitted_list_1_8 (XDR *xdrs, uncommitted_list *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->active))
		 return FALSE;
	 if (!xdr_synode_no_array_1_8 (xdrs, &objp->vers))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_repository_1_8 (XDR *xdrs, repository *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_synode_no_array_1_8 (xdrs, &objp->msg_list))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_8 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_error_1_8 (XDR *xdrs, x_error *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->nodeid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->code))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->message, MAXERROR))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_data_1_8 (XDR *xdrs, trans_data *objp)
{
	register int32_t *buf;
	 if (!xdr_trans_id_1_8 (xdrs, &objp->tid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->pc))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->cluster_name, MAXNAME))
		 return FALSE;
	 if (!xdr_x_error_1_8 (xdrs, &objp->errmsg))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_1_8 (XDR *xdrs, leader *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_array_1_8 (XDR *xdrs, leader_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->leader_array_val, (u_int *) &objp->leader_array_len, NSERVERS,
		sizeof (leader), (xdrproc_t) xdr_leader_1_8))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_u_1_8 (XDR *xdrs, app_u *objp)
{
	register int32_t *buf;
	 if (!xdr_cargo_type_1_8 (xdrs, &objp->c_t))
		 return FALSE;
	switch (objp->c_t) {
	case unified_boot_type:
	case add_node_type:
	case remove_node_type:
	case force_config_type:
	case xcom_boot_type:
	case xcom_set_group:
		 if (!xdr_node_list_1_8 (xdrs, &objp->app_u_u.nodes))
			 return FALSE;
		break;
	case app_type:
		 if (!xdr_checked_data (xdrs, &objp->app_u_u.data))
			 return FALSE;
		break;
	case exit_type:
	case reset_type:
		break;
	case remove_reset_type:
		break;
	case begin_trans:
		break;
	case prepared_trans:
	case abort_trans:
		 if (!xdr_trans_data_1_8 (xdrs, &objp->app_u_u.td))
			 return FALSE;
		break;
	case view_msg:
		 if (!xdr_node_set_1_8 (xdrs, &objp->app_u_u.present))
			 return FALSE;
		break;
	case set_cache_limit:
		 if (!xdr_uint64_t (xdrs, &objp->app_u_u.cache_limit))
			 return FALSE;
		break;
	case get_event_horizon_type:
		break;
	case set_event_horizon_type:
		 if (!xdr_xcom_event_horizon_1_8 (xdrs, &objp->app_u_u.event_horizon))
			 return FALSE;
		break;
	case get_synode_app_data_type:
		 if (!xdr_synode_no_array_1_8 (xdrs, &objp->app_u_u.synodes))
			 return FALSE;
		break;
	case convert_into_local_server_type:
		break;
	case set_max_leaders:
		 if (!xdr_node_no_1_8 (xdrs, &objp->app_u_u.max_leaders))
			 return FALSE;
		break;
	case set_leaders_type:
		 if (!xdr_leader_array_1_8 (xdrs, &objp->app_u_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_app_data_1_8 (XDR *xdrs, app_data *objp)
{
	register int32_t *buf;
	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_synode_no_1_8 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_8 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_8 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		IXDR_PUT_BOOL(buf, objp->notused);
		IXDR_PUT_BOOL(buf, objp->log_it);
		IXDR_PUT_BOOL(buf, objp->chosen);
		}
		 if (!xdr_recover_action_1_8 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_8 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_8))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_synode_no_1_8 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_8 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_8 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		objp->notused = IXDR_GET_BOOL(buf);
		objp->log_it = IXDR_GET_BOOL(buf);
		objp->chosen = IXDR_GET_BOOL(buf);
		}
		 if (!xdr_recover_action_1_8 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_8 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_8))
			 return FALSE;
	 return TRUE;
	}
	 if (!xdr_synode_no_1_8 (xdrs, &objp->unique_id))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->lsn))
		 return FALSE;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->app_key))
		 return FALSE;
	 if (!xdr_cons_type_1_8 (xdrs, &objp->consensus))
		 return FALSE;
	 if (!xdr_double (xdrs, &objp->expiry_time))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->notused))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->log_it))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->chosen))
		 return FALSE;
	 if (!xdr_recover_action_1_8 (xdrs, &objp->recover))
		 return FALSE;
	 if (!xdr_app_u_1_8 (xdrs, &objp->body))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_8))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_1_8 (XDR *xdrs, app_data_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data), (xdrproc_t) xdr_app_data_1_8))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_array_1_8 (XDR *xdrs, app_data_ptr_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->app_data_ptr_array_val, (u_int *) &objp->app_data_ptr_array_len, MAX_APP_PTR_ARRAY,
		sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_8))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_list_1_8 (XDR *xdrs, app_data_list *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_8))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_key_range_1_8 (XDR *xdrs, key_range *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->k1))
		 return FALSE;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->k2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_ballot_1_8 (XDR *xdrs, ballot *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_node_no_1_8 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_snapshot_1_8 (XDR *xdrs, snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_app_data_ptr_array_1_8 (xdrs, &objp->snap))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_8 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config_1_8 (XDR *xdrs, config *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->start))
		 return FALSE;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->boot_key))
		 return FALSE;
	 if (!xdr_node_list_1_8 (xdrs, &objp->nodes))
		 return FALSE;
	 if (!xdr_node_set_1_8 (xdrs, &objp->global_node_set))
		 return FALSE;
	 if (!xdr_xcom_event_horizon_1_8 (xdrs, &objp->event_horizon))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->max_active_leaders = 0; /* Set active leaders to all as default */
 synthesize_leaders(&objp->leaders); /* Install all nodes as leaders */
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_config_ptr_1_8 (XDR *xdrs, config_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (config), (xdrproc_t) xdr_config_1_8))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_configs_1_8 (XDR *xdrs, configs *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->configs_val, (u_int *) &objp->configs_len, MAX_SITE_DEFS,
		sizeof (config_ptr), (xdrproc_t) xdr_config_ptr_1_8))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_gcs_snapshot_1_8 (XDR *xdrs, gcs_snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->log_start))
		 return FALSE;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->log_end))
		 return FALSE;
	 if (!xdr_configs_1_8 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_blob_1_8 (xdrs, &objp->app_snap))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_app_data_1_8 (XDR *xdrs, synode_app_data *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_checked_data (xdrs, &objp->data))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->origin = objp->synode;
 }
/* END protocol conversion code */
	return TRUE;
}
bool_t
xdr_synode_app_data_array_1_8 (XDR *xdrs, synode_app_data_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_app_data_array_val, (u_int *) &objp->synode_app_data_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_app_data), (xdrproc_t) xdr_synode_app_data_1_8))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_type_1_8 (XDR *xdrs, reply_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_info_data_1_8 (XDR *xdrs, leader_info_data *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_8 (xdrs, &objp->max_nr_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_8 (xdrs, &objp->preferred_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_8 (xdrs, &objp->actual_leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_data_1_8 (XDR *xdrs, reply_data *objp)
{
	register int32_t *buf;
	 if (!xdr_reply_type_1_8 (xdrs, &objp->rt))
		 return FALSE;
	switch (objp->rt) {
	case leader_info:
		 if (!xdr_leader_info_data_1_8 (xdrs, &objp->reply_data_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_pax_msg_1_8 (XDR *xdrs, pax_msg *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_8 (xdrs, &objp->to))
		 return FALSE;
	 if (!xdr_node_no_1_8 (xdrs, &objp->from))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->max_synode))
		 return FALSE;
	 if (!xdr_start_t_1_8 (xdrs, &objp->start_type))
		 return FALSE;
	 if (!xdr_ballot_1_8 (xdrs, &objp->reply_to))
		 return FALSE;
	 if (!xdr_ballot_1_8 (xdrs, &objp->proposal))
		 return FALSE;
	 if (!xdr_pax_op_1_8 (xdrs, &objp->op))
		 return FALSE;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_pax_msg_type_1_8 (xdrs, &objp->msg_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->receivers, sizeof (bit_set), (xdrproc_t) xdr_bit_set_1_8))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->a, sizeof (app_data), (xdrproc_t) xdr_app_data_1_8))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->snap, sizeof (snapshot), (xdrproc_t) xdr_snapshot_1_8))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->gcs_snap, sizeof (gcs_snapshot), (xdrproc_t) xdr_gcs_snapshot_1_8))
		 return FALSE;
	 if (!xdr_client_reply_code_1_8 (xdrs, &objp->cli_err))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->force_delivery))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->refcnt))
		 return FALSE;
	 if (!xdr_synode_no_1_8 (xdrs, &objp->delivered_msg))
		 return FALSE;
	 if (!xdr_xcom_event_horizon_1_8 (xdrs, &objp->event_horizon))
		 return FALSE;
	 if (!xdr_synode_app_data_array_1_8 (xdrs, &objp->requested_synode_app_data))
		 return FALSE;
/* BEGIN protocol conversion code */
 if (xdrs->x_op == XDR_DECODE) {
 objp->rd = NULL;
 }
/* END protocol conversion code */
	return TRUE;
}
/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */
#include "xcom_vp.h"
/* Copyright (c) 2010, 2024, Oracle and/or its affiliates.
   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.
   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.
   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */
#include "xcom/xcom_vp_platform.h"
#include "xcom/xcom_limits.h"
#include "xcom/xcom_profile.h"
extern synode_no const null_synode;
extern synode_no get_delivered_msg();
#ifndef _WIN32
#include <strings.h> /* For bzero */
#endif
bool_t
xdr_xcom_proto_1_9 (XDR *xdrs, xcom_proto *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_delivery_status_1_9 (XDR *xdrs, delivery_status *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cons_type_1_9 (XDR *xdrs, cons_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cargo_type_1_9 (XDR *xdrs, cargo_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_recover_action_1_9 (XDR *xdrs, recover_action *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_op_1_9 (XDR *xdrs, pax_op *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_msg_type_1_9 (XDR *xdrs, pax_msg_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_client_reply_code_1_9 (XDR *xdrs, client_reply_code *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_start_t_1_9 (XDR *xdrs, start_t *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_xcom_event_horizon_1_9 (XDR *xdrs, xcom_event_horizon *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_1_9 (XDR *xdrs, node_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_set_1_9 (XDR *xdrs, node_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_set_val, (u_int *) &objp->node_set_len, NSERVERS,
		sizeof (bool_t), (xdrproc_t) xdr_bool))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_mask_1_9 (XDR *xdrs, bit_mask *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_set_1_9 (XDR *xdrs, bit_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->bits.bits_val, (u_int *) &objp->bits.bits_len, NSERVERS,
		sizeof (bit_mask), (xdrproc_t) xdr_bit_mask_1_9))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_blob_1_9 (XDR *xdrs, blob *objp)
{
	register int32_t *buf;
	 if (!xdr_bytes (xdrs, (char **)&objp->data.data_val, (u_int *) &objp->data.data_len, MAXBLOB))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_proto_range_1_9 (XDR *xdrs, x_proto_range *objp)
{
	register int32_t *buf;
	 if (!xdr_xcom_proto_1_9 (xdrs, &objp->min_proto))
		 return FALSE;
	 if (!xdr_xcom_proto_1_9 (xdrs, &objp->max_proto))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_1_9 (XDR *xdrs, synode_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->msgno))
		 return FALSE;
	 if (!xdr_node_no_1_9 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_id_1_9 (XDR *xdrs, trans_id *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pc))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_paxos_role_1_9 (XDR *xdrs, paxos_role *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_address_1_9 (XDR *xdrs, node_address *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	 if (!xdr_blob_1_9 (xdrs, &objp->uuid))
		 return FALSE;
	 if (!xdr_x_proto_range_1_9 (xdrs, &objp->proto))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->services))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_list_1_9 (XDR *xdrs, node_list *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_list_val, (u_int *) &objp->node_list_len, NSERVERS,
		sizeof (node_address), (xdrproc_t) xdr_node_address_1_9))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_array_1_9 (XDR *xdrs, node_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_no_array_val, (u_int *) &objp->node_no_array_len, NSERVERS,
		sizeof (node_no), (xdrproc_t) xdr_node_no_1_9))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_array_1_9 (XDR *xdrs, synode_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_no_array_val, (u_int *) &objp->synode_no_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_no), (xdrproc_t) xdr_synode_no_1_9))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_uncommitted_list_1_9 (XDR *xdrs, uncommitted_list *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->active))
		 return FALSE;
	 if (!xdr_synode_no_array_1_9 (xdrs, &objp->vers))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_repository_1_9 (XDR *xdrs, repository *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_synode_no_array_1_9 (xdrs, &objp->msg_list))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_9 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_error_1_9 (XDR *xdrs, x_error *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->nodeid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->code))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->message, MAXERROR))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_data_1_9 (XDR *xdrs, trans_data *objp)
{
	register int32_t *buf;
	 if (!xdr_trans_id_1_9 (xdrs, &objp->tid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->pc))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->cluster_name, MAXNAME))
		 return FALSE;
	 if (!xdr_x_error_1_9 (xdrs, &objp->errmsg))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_1_9 (XDR *xdrs, leader *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_array_1_9 (XDR *xdrs, leader_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->leader_array_val, (u_int *) &objp->leader_array_len, NSERVERS,
		sizeof (leader), (xdrproc_t) xdr_leader_1_9))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_u_1_9 (XDR *xdrs, app_u *objp)
{
	register int32_t *buf;
	 if (!xdr_cargo_type_1_9 (xdrs, &objp->c_t))
		 return FALSE;
	switch (objp->c_t) {
	case unified_boot_type:
	case add_node_type:
	case remove_node_type:
	case force_config_type:
	case xcom_boot_type:
	case xcom_set_group:
		 if (!xdr_node_list_1_9 (xdrs, &objp->app_u_u.nodes))
			 return FALSE;
		break;
	case app_type:
		 if (!xdr_checked_data (xdrs, &objp->app_u_u.data))
			 return FALSE;
		break;
	case exit_type:
	case reset_type:
		break;
	case remove_reset_type:
		break;
	case begin_trans:
		break;
	case prepared_trans:
	case abort_trans:
		 if (!xdr_trans_data_1_9 (xdrs, &objp->app_u_u.td))
			 return FALSE;
		break;
	case view_msg:
		 if (!xdr_node_set_1_9 (xdrs, &objp->app_u_u.present))
			 return FALSE;
		break;
	case set_cache_limit:
		 if (!xdr_uint64_t (xdrs, &objp->app_u_u.cache_limit))
			 return FALSE;
		break;
	case get_event_horizon_type:
		break;
	case set_event_horizon_type:
		 if (!xdr_xcom_event_horizon_1_9 (xdrs, &objp->app_u_u.event_horizon))
			 return FALSE;
		break;
	case get_synode_app_data_type:
		 if (!xdr_synode_no_array_1_9 (xdrs, &objp->app_u_u.synodes))
			 return FALSE;
		break;
	case convert_into_local_server_type:
		break;
	case set_max_leaders:
		 if (!xdr_node_no_1_9 (xdrs, &objp->app_u_u.max_leaders))
			 return FALSE;
		break;
	case set_leaders_type:
		 if (!xdr_leader_array_1_9 (xdrs, &objp->app_u_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_app_data_1_9 (XDR *xdrs, app_data *objp)
{
	register int32_t *buf;
	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_synode_no_1_9 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_9 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_9 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		IXDR_PUT_BOOL(buf, objp->notused);
		IXDR_PUT_BOOL(buf, objp->log_it);
		IXDR_PUT_BOOL(buf, objp->chosen);
		}
		 if (!xdr_recover_action_1_9 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_9 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_9))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_synode_no_1_9 (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no_1_9 (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type_1_9 (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		objp->notused = IXDR_GET_BOOL(buf);
		objp->log_it = IXDR_GET_BOOL(buf);
		objp->chosen = IXDR_GET_BOOL(buf);
		}
		 if (!xdr_recover_action_1_9 (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u_1_9 (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_9))
			 return FALSE;
	 return TRUE;
	}
	 if (!xdr_synode_no_1_9 (xdrs, &objp->unique_id))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->lsn))
		 return FALSE;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->app_key))
		 return FALSE;
	 if (!xdr_cons_type_1_9 (xdrs, &objp->consensus))
		 return FALSE;
	 if (!xdr_double (xdrs, &objp->expiry_time))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->notused))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->log_it))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->chosen))
		 return FALSE;
	 if (!xdr_recover_action_1_9 (xdrs, &objp->recover))
		 return FALSE;
	 if (!xdr_app_u_1_9 (xdrs, &objp->body))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data_1_9))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_1_9 (XDR *xdrs, app_data_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data), (xdrproc_t) xdr_app_data_1_9))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_array_1_9 (XDR *xdrs, app_data_ptr_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->app_data_ptr_array_val, (u_int *) &objp->app_data_ptr_array_len, MAX_APP_PTR_ARRAY,
		sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_9))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_list_1_9 (XDR *xdrs, app_data_list *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr_1_9))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_key_range_1_9 (XDR *xdrs, key_range *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->k1))
		 return FALSE;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->k2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_ballot_1_9 (XDR *xdrs, ballot *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_node_no_1_9 (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_snapshot_1_9 (XDR *xdrs, snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_app_data_ptr_array_1_9 (xdrs, &objp->snap))
		 return FALSE;
	 if (!xdr_uncommitted_list_1_9 (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config_1_9 (XDR *xdrs, config *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->start))
		 return FALSE;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->boot_key))
		 return FALSE;
	 if (!xdr_node_list_1_9 (xdrs, &objp->nodes))
		 return FALSE;
	 if (!xdr_node_set_1_9 (xdrs, &objp->global_node_set))
		 return FALSE;
	 if (!xdr_xcom_event_horizon_1_9 (xdrs, &objp->event_horizon))
		 return FALSE;
	 if (!xdr_node_no_1_9 (xdrs, &objp->max_active_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_9 (xdrs, &objp->leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config_ptr_1_9 (XDR *xdrs, config_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (config), (xdrproc_t) xdr_config_1_9))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_configs_1_9 (XDR *xdrs, configs *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->configs_val, (u_int *) &objp->configs_len, MAX_SITE_DEFS,
		sizeof (config_ptr), (xdrproc_t) xdr_config_ptr_1_9))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_gcs_snapshot_1_9 (XDR *xdrs, gcs_snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->log_start))
		 return FALSE;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->log_end))
		 return FALSE;
	 if (!xdr_configs_1_9 (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_blob_1_9 (xdrs, &objp->app_snap))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_app_data_1_9 (XDR *xdrs, synode_app_data *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_checked_data (xdrs, &objp->data))
		 return FALSE;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->origin))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_app_data_array_1_9 (XDR *xdrs, synode_app_data_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_app_data_array_val, (u_int *) &objp->synode_app_data_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_app_data), (xdrproc_t) xdr_synode_app_data_1_9))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_type_1_9 (XDR *xdrs, reply_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_info_data_1_9 (XDR *xdrs, leader_info_data *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_9 (xdrs, &objp->max_nr_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_9 (xdrs, &objp->preferred_leaders))
		 return FALSE;
	 if (!xdr_leader_array_1_9 (xdrs, &objp->actual_leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_data_1_9 (XDR *xdrs, reply_data *objp)
{
	register int32_t *buf;
	 if (!xdr_reply_type_1_9 (xdrs, &objp->rt))
		 return FALSE;
	switch (objp->rt) {
	case leader_info:
		 if (!xdr_leader_info_data_1_9 (xdrs, &objp->reply_data_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_pax_msg_1_9 (XDR *xdrs, pax_msg *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no_1_9 (xdrs, &objp->to))
		 return FALSE;
	 if (!xdr_node_no_1_9 (xdrs, &objp->from))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->max_synode))
		 return FALSE;
	 if (!xdr_start_t_1_9 (xdrs, &objp->start_type))
		 return FALSE;
	 if (!xdr_ballot_1_9 (xdrs, &objp->reply_to))
		 return FALSE;
	 if (!xdr_ballot_1_9 (xdrs, &objp->proposal))
		 return FALSE;
	 if (!xdr_pax_op_1_9 (xdrs, &objp->op))
		 return FALSE;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_pax_msg_type_1_9 (xdrs, &objp->msg_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->receivers, sizeof (bit_set), (xdrproc_t) xdr_bit_set_1_9))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->a, sizeof (app_data), (xdrproc_t) xdr_app_data_1_9))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->snap, sizeof (snapshot), (xdrproc_t) xdr_snapshot_1_9))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->gcs_snap, sizeof (gcs_snapshot), (xdrproc_t) xdr_gcs_snapshot_1_9))
		 return FALSE;
	 if (!xdr_client_reply_code_1_9 (xdrs, &objp->cli_err))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->force_delivery))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->refcnt))
		 return FALSE;
	 if (!xdr_synode_no_1_9 (xdrs, &objp->delivered_msg))
		 return FALSE;
	 if (!xdr_xcom_event_horizon_1_9 (xdrs, &objp->event_horizon))
		 return FALSE;
	 if (!xdr_synode_app_data_array_1_9 (xdrs, &objp->requested_synode_app_data))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->rd, sizeof (reply_data), (xdrproc_t) xdr_reply_data_1_9))
		 return FALSE;
	return TRUE;
}
/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */
#include "xcom_vp.h"
/* Copyright (c) 2010, 2024, Oracle and/or its affiliates.
   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.
   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.
   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */
#include "xcom/xcom_vp_platform.h"
#include "xcom/xcom_limits.h"
#include "xcom/xcom_profile.h"
extern synode_no const null_synode;
extern synode_no get_delivered_msg();
#ifndef _WIN32
#include <strings.h> /* For bzero */
#endif
bool_t
xdr_xcom_proto (XDR *xdrs, xcom_proto *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_delivery_status (XDR *xdrs, delivery_status *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cons_type (XDR *xdrs, cons_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_cargo_type (XDR *xdrs, cargo_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_recover_action (XDR *xdrs, recover_action *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_op (XDR *xdrs, pax_op *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_pax_msg_type (XDR *xdrs, pax_msg_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_client_reply_code (XDR *xdrs, client_reply_code *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_start_t (XDR *xdrs, start_t *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_xcom_event_horizon (XDR *xdrs, xcom_event_horizon *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no (XDR *xdrs, node_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_set (XDR *xdrs, node_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_set_val, (u_int *) &objp->node_set_len, NSERVERS,
		sizeof (bool_t), (xdrproc_t) xdr_bool))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_mask (XDR *xdrs, bit_mask *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_bit_set (XDR *xdrs, bit_set *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->bits.bits_val, (u_int *) &objp->bits.bits_len, NSERVERS,
		sizeof (bit_mask), (xdrproc_t) xdr_bit_mask))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_blob (XDR *xdrs, blob *objp)
{
	register int32_t *buf;
	 if (!xdr_bytes (xdrs, (char **)&objp->data.data_val, (u_int *) &objp->data.data_len, MAXBLOB))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_proto_range (XDR *xdrs, x_proto_range *objp)
{
	register int32_t *buf;
	 if (!xdr_xcom_proto (xdrs, &objp->min_proto))
		 return FALSE;
	 if (!xdr_xcom_proto (xdrs, &objp->max_proto))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no (XDR *xdrs, synode_no *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->msgno))
		 return FALSE;
	 if (!xdr_node_no (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_id (XDR *xdrs, trans_id *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pc))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_paxos_role (XDR *xdrs, paxos_role *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_address (XDR *xdrs, node_address *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	 if (!xdr_blob (xdrs, &objp->uuid))
		 return FALSE;
	 if (!xdr_x_proto_range (xdrs, &objp->proto))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->services))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_list (XDR *xdrs, node_list *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_list_val, (u_int *) &objp->node_list_len, NSERVERS,
		sizeof (node_address), (xdrproc_t) xdr_node_address))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_node_no_array (XDR *xdrs, node_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->node_no_array_val, (u_int *) &objp->node_no_array_len, NSERVERS,
		sizeof (node_no), (xdrproc_t) xdr_node_no))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_no_array (XDR *xdrs, synode_no_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_no_array_val, (u_int *) &objp->synode_no_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_no), (xdrproc_t) xdr_synode_no))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_uncommitted_list (XDR *xdrs, uncommitted_list *objp)
{
	register int32_t *buf;
	 if (!xdr_uint32_t (xdrs, &objp->active))
		 return FALSE;
	 if (!xdr_synode_no_array (xdrs, &objp->vers))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_repository (XDR *xdrs, repository *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_synode_no_array (xdrs, &objp->msg_list))
		 return FALSE;
	 if (!xdr_uncommitted_list (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_x_error (XDR *xdrs, x_error *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->nodeid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->code))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->message, MAXERROR))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_trans_data (XDR *xdrs, trans_data *objp)
{
	register int32_t *buf;
	 if (!xdr_trans_id (xdrs, &objp->tid))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->pc))
		 return FALSE;
	 if (!xdr_string (xdrs, &objp->cluster_name, MAXNAME))
		 return FALSE;
	 if (!xdr_x_error (xdrs, &objp->errmsg))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader (XDR *xdrs, leader *objp)
{
	register int32_t *buf;
	 if (!xdr_string (xdrs, &objp->address, MAXNAME))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_array (XDR *xdrs, leader_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->leader_array_val, (u_int *) &objp->leader_array_len, NSERVERS,
		sizeof (leader), (xdrproc_t) xdr_leader))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_u (XDR *xdrs, app_u *objp)
{
	register int32_t *buf;
	 if (!xdr_cargo_type (xdrs, &objp->c_t))
		 return FALSE;
	switch (objp->c_t) {
	case unified_boot_type:
	case add_node_type:
	case remove_node_type:
	case force_config_type:
	case xcom_boot_type:
	case xcom_set_group:
		 if (!xdr_node_list (xdrs, &objp->app_u_u.nodes))
			 return FALSE;
		break;
	case app_type:
		 if (!xdr_checked_data (xdrs, &objp->app_u_u.data))
			 return FALSE;
		break;
	case exit_type:
	case reset_type:
		break;
	case remove_reset_type:
		break;
	case begin_trans:
		break;
	case prepared_trans:
	case abort_trans:
		 if (!xdr_trans_data (xdrs, &objp->app_u_u.td))
			 return FALSE;
		break;
	case view_msg:
		 if (!xdr_node_set (xdrs, &objp->app_u_u.present))
			 return FALSE;
		break;
	case set_cache_limit:
		 if (!xdr_uint64_t (xdrs, &objp->app_u_u.cache_limit))
			 return FALSE;
		break;
	case get_event_horizon_type:
		break;
	case set_event_horizon_type:
		 if (!xdr_xcom_event_horizon (xdrs, &objp->app_u_u.event_horizon))
			 return FALSE;
		break;
	case get_synode_app_data_type:
		 if (!xdr_synode_no_array (xdrs, &objp->app_u_u.synodes))
			 return FALSE;
		break;
	case convert_into_local_server_type:
		break;
	case set_max_leaders:
		 if (!xdr_node_no (xdrs, &objp->app_u_u.max_leaders))
			 return FALSE;
		break;
	case set_leaders_type:
		 if (!xdr_leader_array (xdrs, &objp->app_u_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_app_data (XDR *xdrs, app_data *objp)
{
	register int32_t *buf;
	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_synode_no (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		IXDR_PUT_BOOL(buf, objp->notused);
		IXDR_PUT_BOOL(buf, objp->log_it);
		IXDR_PUT_BOOL(buf, objp->chosen);
		}
		 if (!xdr_recover_action (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_synode_no (xdrs, &objp->unique_id))
			 return FALSE;
		 if (!xdr_uint32_t (xdrs, &objp->group_id))
			 return FALSE;
		 if (!xdr_uint64_t (xdrs, &objp->lsn))
			 return FALSE;
		 if (!xdr_synode_no (xdrs, &objp->app_key))
			 return FALSE;
		 if (!xdr_cons_type (xdrs, &objp->consensus))
			 return FALSE;
		 if (!xdr_double (xdrs, &objp->expiry_time))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_bool (xdrs, &objp->notused))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->log_it))
				 return FALSE;
			 if (!xdr_bool (xdrs, &objp->chosen))
				 return FALSE;
		} else {
		objp->notused = IXDR_GET_BOOL(buf);
		objp->log_it = IXDR_GET_BOOL(buf);
		objp->chosen = IXDR_GET_BOOL(buf);
		}
		 if (!xdr_recover_action (xdrs, &objp->recover))
			 return FALSE;
		 if (!xdr_app_u (xdrs, &objp->body))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data))
			 return FALSE;
	 return TRUE;
	}
	 if (!xdr_synode_no (xdrs, &objp->unique_id))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->lsn))
		 return FALSE;
	 if (!xdr_synode_no (xdrs, &objp->app_key))
		 return FALSE;
	 if (!xdr_cons_type (xdrs, &objp->consensus))
		 return FALSE;
	 if (!xdr_double (xdrs, &objp->expiry_time))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->notused))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->log_it))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->chosen))
		 return FALSE;
	 if (!xdr_recover_action (xdrs, &objp->recover))
		 return FALSE;
	 if (!xdr_app_u (xdrs, &objp->body))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->next, sizeof (app_data), (xdrproc_t) xdr_app_data))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr (XDR *xdrs, app_data_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data), (xdrproc_t) xdr_app_data))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_ptr_array (XDR *xdrs, app_data_ptr_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->app_data_ptr_array_val, (u_int *) &objp->app_data_ptr_array_len, MAX_APP_PTR_ARRAY,
		sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_app_data_list (XDR *xdrs, app_data_list *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (app_data_ptr), (xdrproc_t) xdr_app_data_ptr))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_key_range (XDR *xdrs, key_range *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no (xdrs, &objp->k1))
		 return FALSE;
	 if (!xdr_synode_no (xdrs, &objp->k2))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_ballot (XDR *xdrs, ballot *objp)
{
	register int32_t *buf;
	 if (!xdr_int32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_node_no (xdrs, &objp->node))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_snapshot (XDR *xdrs, snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no (xdrs, &objp->vers))
		 return FALSE;
	 if (!xdr_app_data_ptr_array (xdrs, &objp->snap))
		 return FALSE;
	 if (!xdr_uncommitted_list (xdrs, &objp->u_list))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config (XDR *xdrs, config *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no (xdrs, &objp->start))
		 return FALSE;
	 if (!xdr_synode_no (xdrs, &objp->boot_key))
		 return FALSE;
	 if (!xdr_node_list (xdrs, &objp->nodes))
		 return FALSE;
	 if (!xdr_node_set (xdrs, &objp->global_node_set))
		 return FALSE;
	 if (!xdr_xcom_event_horizon (xdrs, &objp->event_horizon))
		 return FALSE;
	 if (!xdr_node_no (xdrs, &objp->max_active_leaders))
		 return FALSE;
	 if (!xdr_leader_array (xdrs, &objp->leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_config_ptr (XDR *xdrs, config_ptr *objp)
{
	register int32_t *buf;
	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (config), (xdrproc_t) xdr_config))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_configs (XDR *xdrs, configs *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->configs_val, (u_int *) &objp->configs_len, MAX_SITE_DEFS,
		sizeof (config_ptr), (xdrproc_t) xdr_config_ptr))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_gcs_snapshot (XDR *xdrs, gcs_snapshot *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no (xdrs, &objp->log_start))
		 return FALSE;
	 if (!xdr_synode_no (xdrs, &objp->log_end))
		 return FALSE;
	 if (!xdr_configs (xdrs, &objp->cfg))
		 return FALSE;
	 if (!xdr_blob (xdrs, &objp->app_snap))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_app_data (XDR *xdrs, synode_app_data *objp)
{
	register int32_t *buf;
	 if (!xdr_synode_no (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_checked_data (xdrs, &objp->data))
		 return FALSE;
	 if (!xdr_synode_no (xdrs, &objp->origin))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_synode_app_data_array (XDR *xdrs, synode_app_data_array *objp)
{
	register int32_t *buf;
	 if (!xdr_array (xdrs, (char **)&objp->synode_app_data_array_val, (u_int *) &objp->synode_app_data_array_len, MAX_SYNODE_ARRAY,
		sizeof (synode_app_data), (xdrproc_t) xdr_synode_app_data))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_type (XDR *xdrs, reply_type *objp)
{
	register int32_t *buf;
	 if (!xdr_enum (xdrs, (enum_t *) objp))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_leader_info_data (XDR *xdrs, leader_info_data *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no (xdrs, &objp->max_nr_leaders))
		 return FALSE;
	 if (!xdr_leader_array (xdrs, &objp->preferred_leaders))
		 return FALSE;
	 if (!xdr_leader_array (xdrs, &objp->actual_leaders))
		 return FALSE;
	return TRUE;
}
bool_t
xdr_reply_data (XDR *xdrs, reply_data *objp)
{
	register int32_t *buf;
	 if (!xdr_reply_type (xdrs, &objp->rt))
		 return FALSE;
	switch (objp->rt) {
	case leader_info:
		 if (!xdr_leader_info_data (xdrs, &objp->reply_data_u.leaders))
			 return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}
bool_t
xdr_pax_msg (XDR *xdrs, pax_msg *objp)
{
	register int32_t *buf;
	 if (!xdr_node_no (xdrs, &objp->to))
		 return FALSE;
	 if (!xdr_node_no (xdrs, &objp->from))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->group_id))
		 return FALSE;
	 if (!xdr_synode_no (xdrs, &objp->max_synode))
		 return FALSE;
	 if (!xdr_start_t (xdrs, &objp->start_type))
		 return FALSE;
	 if (!xdr_ballot (xdrs, &objp->reply_to))
		 return FALSE;
	 if (!xdr_ballot (xdrs, &objp->proposal))
		 return FALSE;
	 if (!xdr_pax_op (xdrs, &objp->op))
		 return FALSE;
	 if (!xdr_synode_no (xdrs, &objp->synode))
		 return FALSE;
	 if (!xdr_pax_msg_type (xdrs, &objp->msg_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->receivers, sizeof (bit_set), (xdrproc_t) xdr_bit_set))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->a, sizeof (app_data), (xdrproc_t) xdr_app_data))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->snap, sizeof (snapshot), (xdrproc_t) xdr_snapshot))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->gcs_snap, sizeof (gcs_snapshot), (xdrproc_t) xdr_gcs_snapshot))
		 return FALSE;
	 if (!xdr_client_reply_code (xdrs, &objp->cli_err))
		 return FALSE;
	 if (!xdr_bool (xdrs, &objp->force_delivery))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->refcnt))
		 return FALSE;
	 if (!xdr_synode_no (xdrs, &objp->delivered_msg))
		 return FALSE;
	 if (!xdr_xcom_event_horizon (xdrs, &objp->event_horizon))
		 return FALSE;
	 if (!xdr_synode_app_data_array (xdrs, &objp->requested_synode_app_data))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->rd, sizeof (reply_data), (xdrproc_t) xdr_reply_data))
		 return FALSE;
	return TRUE;
}
