drop table if exists t1,t2;
create table t1 (name char(20) not null, primary key (name)) charset latin1;
create table t2 (name char(20) collate utf8mb4_bin not null, primary key (name));
insert into t1 values ("å");
insert into t1 values ("ä");
insert into t1 values ("ö");
insert into t2 select * from t1;
select * from t1 order by name;
name
å
ä
ö
select concat("*",name,"*") from t1 order by 1;
concat("*",name,"*")
*å*
*ä*
*ö*
select min(name),min(concat("*",name,"*")),max(name),max(concat("*",name,"*")) from t1;
min(name)	min(concat("*",name,"*"))	max(name)	max(concat("*",name,"*"))
å	*å*	ö	*ö*
select * from t2 order by name;
name
ä
å
ö
select concat("*",name,"*") from t2 order by 1;
concat("*",name,"*")
*ä*
*å*
*ö*
select min(name),min(concat("*",name,"*")),max(name),max(concat("*",name,"*")) from t2;
min(name)	min(concat("*",name,"*"))	max(name)	max(concat("*",name,"*"))
ä	*ä*	ö	*ö*
select name from t1 where name between 'Ä' and 'Ö';
name
ä
ö
select name from t2 where name between 'ä' and 'ö';
name
ä
å
ö
select name from t2 where name between 'Ä' and 'Ö';
name
drop table t1,t2;
create table t1 (a char(10) not null, b char(10) collate latin1_bin not null,key (a), key(b)) charset latin1;
insert into t1 values ("hello ","hello "),("hello2 ","hello2 ");
select concat("-",a,"-",b,"-") from t1 where a="hello";
concat("-",a,"-",b,"-")
-hello-hello-
select concat("-",a,"-",b,"-") from t1 where a="hello ";
concat("-",a,"-",b,"-")
-hello-hello-
select concat("-",a,"-",b,"-") from t1 ignore index (a) where a="hello ";
concat("-",a,"-",b,"-")
-hello-hello-
select concat("-",a,"-",b,"-") from t1 where b="hello";
concat("-",a,"-",b,"-")
-hello-hello-
select concat("-",a,"-",b,"-") from t1 where b="hello ";
concat("-",a,"-",b,"-")
-hello-hello-
select concat("-",a,"-",b,"-") from t1 ignore index (b) where b="hello ";
concat("-",a,"-",b,"-")
-hello-hello-
alter table t1 modify b tinytext not null, drop key b, add key (b(100));
select concat("-",a,"-",b,"-") from t1;
concat("-",a,"-",b,"-")
-hello-hello-
-hello2-hello2-
select concat("-",a,"-",b,"-") from t1 where b="hello ";
concat("-",a,"-",b,"-")
-hello-hello-
select concat("-",a,"-",b,"-") from t1 ignore index (b) where b="hello ";
concat("-",a,"-",b,"-")
-hello-hello-
drop table t1;
create table t1 (b char(8));
insert into t1 values(NULL);
select b from t1 where cast(b as binary)  like '';
b
select b from t1 group by cast(b as binary) like '';
b
NULL
select b from t1 having cast(b as binary) like '';
b
drop table t1;
create table t1 (a char(3) binary, b binary(3)) charset latin1;
Warnings:
Warning	1287	'BINARY as attribute of a type' is deprecated and will be removed in a future release. Please use a CHARACTER SET clause with _bin collation instead
insert into t1 values ('aaa','bbb'),('AAA','BBB');
select upper(a),upper(b) from t1;
upper(a)	upper(b)
AAA	bbb
AAA	BBB
select lower(a),lower(b) from t1;
lower(a)	lower(b)
aaa	bbb
aaa	BBB
select * from t1 where upper(a)='AAA';
a	b
aaa	bbb
AAA	BBB
select * from t1 where lower(a)='aaa';
a	b
aaa	bbb
AAA	BBB
select * from t1 where upper(b)='BBB';
a	b
AAA	BBB
select * from t1 where lower(b)='bbb';
a	b
aaa	bbb
select charset(a), charset(b), charset(cast('ccc' as binary)) from t1 limit 1;
charset(a)	charset(b)	charset(cast('ccc' as binary))
latin1	binary	binary
select collation(a), collation(b), collation(cast('ccc' as binary)) from t1 limit 1;
collation(a)	collation(b)	collation(cast('ccc' as binary))
latin1_bin	binary	binary
drop table t1;
create table t1( firstname char(20), lastname char(20));
insert into t1 values ("john","doe"),("John","Doe");
select * from t1 where firstname='john' and firstname like cast('john' as binary);
firstname	lastname
john	doe
select * from t1 where firstname='john' and cast('john' as binary) = firstname;
firstname	lastname
john	doe
select * from t1 where firstname='john' and firstname = cast('john' as binary);
firstname	lastname
john	doe
select * from t1 where firstname='John' and firstname like cast('john' as binary);
firstname	lastname
john	doe
select * from t1 where firstname='john' and firstname like cast('john' as binary);
firstname	lastname
john	doe
drop table t1;
create table t1 (a binary) charset utf8mb4;
show create table t1;
Table	Create Table
t1	CREATE TABLE `t1` (
  `a` binary(1) DEFAULT NULL
) ENGINE=ENGINE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
drop table t1;
create table t1 (col1 binary(4));
insert into t1 values ('a'),('a ');
select hex(col1) from t1;
hex(col1)
61000000
61200000
alter table t1 modify col1 binary(10);
select hex(col1) from t1;
hex(col1)
61000000000000000000
61200000000000000000
insert into t1 values ('b'),('b ');
select hex(col1) from t1;
hex(col1)
61000000000000000000
61200000000000000000
62000000000000000000
62200000000000000000
drop table t1;
CREATE TABLE t1 (
a binary(20) NOT NULL DEFAULT '\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0', 
index idx(a)
);
INSERT INTO t1 SET a=unhex('1F9480179366F2BF567E1C4B964C1EF029087575');
INSERT INTO t1 SET a=unhex('1F9480179366F2BF567E1C4B964C1EF029082020');
INSERT INTO t1 SET a=unhex('1F9480179366F2BF567E1C4B964C1EF029080707');
SELECT hex(a) FROM t1 order by a;
hex(a)
1F9480179366F2BF567E1C4B964C1EF029080707
1F9480179366F2BF567E1C4B964C1EF029082020
1F9480179366F2BF567E1C4B964C1EF029087575
EXPLAIN SELECT hex(a) FROM t1 order by a;
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	index	NULL	idx	20	NULL	3	100.00	Using index
Warnings:
Note	1003	/* select#1 */ select hex(`test`.`t1`.`a`) AS `hex(a)` from `test`.`t1` order by `test`.`t1`.`a`
SELECT hex(a) from t1 WHERE a=unhex('1F9480179366F2BF567E1C4B964C1EF029082020');
hex(a)
1F9480179366F2BF567E1C4B964C1EF029082020
EXPLAIN
SELECT hex(a) from t1 WHERE a=unhex('1F9480179366F2BF567E1C4B964C1EF029082020');
id	select_type	table	partitions	type	possible_keys	key	key_len	ref	rows	filtered	Extra
1	SIMPLE	t1	NULL	ref	idx	idx	20	const	1	100.00	Using where; Using index
Warnings:
Note	1003	/* select#1 */ select hex(`test`.`t1`.`a`) AS `hex(a)` from `test`.`t1` where (`test`.`t1`.`a` = <cache>(unhex('1F9480179366F2BF567E1C4B964C1EF029082020')))
SELECT hex(a) from t1 WHERE a=unhex('1F9480179366F2BF567E1C4B964C1EF02908');
hex(a)
DROP TABLE t1;
CREATE TABLE t1 (
id numeric(20) NOT NULL,
lang varchar(8) NOT NULL,
msg varchar(32) NOT NULL,
PRIMARY KEY (id,lang)
);
INSERT INTO t1 VALUES (33, 'en', 'zzzzzzz');
INSERT INTO t1 VALUES (31, 'en', 'xxxxxxx');
INSERT INTO t1 VALUES (32, 'en', 'yyyyyyy');
SELECT * FROM t1 WHERE id=32;
id	lang	msg
32	en	yyyyyyy
DROP TABLE t1;
