target=pc-linux-i686
base_dir=/ndb
src_clone_base=<EMAIL>:/home/<USER>
run_dir=/space/autotest
build_dir=/ndb
hosts="ndb01 ndb02 ndb03 ndb04 ndb05 ndb06 ndb07 ndb08 ndb09 ndb10 ndb11 ndb12"
result_host="foo.mysql.com"
result_path="public_html"
configure='CC=gcc CXX=gcc CFLAGS="-Wall -pedantic -Wno-long-long" CXXFLAGS="-Wall -pedantic -Wno-long-long" ./configure --with-ndbcluster --with-ndb-test --with-ndbcc-flags="-g -DERROR_INSERT"' 

