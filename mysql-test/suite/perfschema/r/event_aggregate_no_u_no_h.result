Warnings:
Warning	1681	'FLUSH PRIVILEGES' is deprecated and will be removed in a future release.
"================== Step 1 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
execute dump_stages_account;
user	host	event_name	count_star
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
execute dump_statements_account;
user	host	event_name	count_star
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	0
statement/sql/insert	0
statement/sql/select	0
execute dump_statements_history;
event_name	count(event_name)
execute dump_transactions_account;
user	host	event_name	count_star
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	0
execute dump_transactions_history;
event_name	count(event_name)
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
"================== con1 connected =================="
"================== Step 2 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/file/sql/query_log	1
user1	wait/synch/mutex/sql/LOCK_connection_count	0
user1	wait/synch/mutex/sql/LOCK_uuid_generator	0
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	1
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	1
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	1
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	0
user1	localhost	statement/sql/insert	0
user1	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	0
statement/sql/insert	0
statement/sql/select	0
execute dump_statements_history;
event_name	count(event_name)
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	0
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	0
execute dump_transactions_history;
event_name	count(event_name)
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
select uuid_short() <> 1;
uuid_short() <> 1
1
select uuid_short() <> 1;
uuid_short() <> 1
1
start transaction;
insert into test.t1 values ("marker");
commit;
select test.f(10,20);
test.f(10,20)
30
"================== con1 marker =================="
"================== Step 3 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/file/sql/query_log	7
user1	wait/synch/mutex/sql/LOCK_connection_count	0
user1	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	7
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	7
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	7
wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	8
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	4
stage/sql/closing tables	6
stage/sql/init	4
stage/sql/Opening tables	5
stage/sql/starting	8
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	4
stage/sql/closing tables	6
stage/sql/init	4
stage/sql/Opening tables	5
stage/sql/starting	8
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	1
statement/sql/insert	1
statement/sql/select	3
execute dump_statements_history;
event_name	count(event_name)
statement/sp/freturn	1
statement/sql/insert	1
statement/sql/select	3
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	1
execute dump_transactions_history;
event_name	count(event_name)
transaction	1
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
"================== con2 connected =================="
"================== Step 4 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/file/sql/query_log	7
user1	wait/synch/mutex/sql/LOCK_connection_count	0
user1	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user2	wait/io/file/sql/query_log	1
user2	wait/synch/mutex/sql/LOCK_connection_count	0
user2	wait/synch/mutex/sql/LOCK_uuid_generator	0
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	7
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user2	localhost	wait/io/file/sql/query_log	1
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	8
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	8
wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	8
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	4
stage/sql/closing tables	6
stage/sql/init	4
stage/sql/Opening tables	5
stage/sql/starting	8
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	4
stage/sql/closing tables	6
stage/sql/init	4
stage/sql/Opening tables	5
stage/sql/starting	8
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	0
user2	localhost	statement/sql/insert	0
user2	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	1
statement/sql/insert	1
statement/sql/select	3
execute dump_statements_history;
event_name	count(event_name)
statement/sp/freturn	1
statement/sql/insert	1
statement/sql/select	3
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	0
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	1
execute dump_transactions_history;
event_name	count(event_name)
transaction	1
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	1	1
user2	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
select uuid_short() <> 1;
uuid_short() <> 1
1
select uuid_short() <> 1;
uuid_short() <> 1
1
start transaction;
insert into test.t1 values ("marker");
commit;
select test.f(10,20);
test.f(10,20)
30
"================== con2 marker =================="
"================== Step 5 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/file/sql/query_log	7
user1	wait/synch/mutex/sql/LOCK_connection_count	0
user1	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user2	wait/io/file/sql/query_log	7
user2	wait/synch/mutex/sql/LOCK_connection_count	0
user2	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	7
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user2	localhost	wait/io/file/sql/query_log	7
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	14
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	4
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	14
wait/synch/mutex/sql/LOCK_uuid_generator	4
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	8
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	8
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	8
stage/sql/closing tables	12
stage/sql/init	8
stage/sql/Opening tables	10
stage/sql/starting	16
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	8
stage/sql/closing tables	12
stage/sql/init	8
stage/sql/Opening tables	10
stage/sql/starting	16
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	2
statement/sql/insert	2
statement/sql/select	6
execute dump_statements_history;
event_name	count(event_name)
statement/sp/freturn	2
statement/sql/insert	2
statement/sql/select	6
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	2
execute dump_transactions_history;
event_name	count(event_name)
transaction	2
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	1	1
user2	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
"================== con3 connected =================="
"================== Step 6 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/file/sql/query_log	7
user1	wait/synch/mutex/sql/LOCK_connection_count	0
user1	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user2	wait/io/file/sql/query_log	7
user2	wait/synch/mutex/sql/LOCK_connection_count	0
user2	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user3	wait/io/file/sql/query_log	1
user3	wait/synch/mutex/sql/LOCK_connection_count	0
user3	wait/synch/mutex/sql/LOCK_uuid_generator	0
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	7
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user2	localhost	wait/io/file/sql/query_log	7
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user3	localhost	wait/io/file/sql/query_log	1
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	15
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	4
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	15
wait/synch/mutex/sql/LOCK_uuid_generator	4
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	8
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	8
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	8
stage/sql/closing tables	12
stage/sql/init	8
stage/sql/Opening tables	10
stage/sql/starting	16
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	8
stage/sql/closing tables	12
stage/sql/init	8
stage/sql/Opening tables	10
stage/sql/starting	16
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	0
user3	localhost	statement/sql/insert	0
user3	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	2
statement/sql/insert	2
statement/sql/select	6
execute dump_statements_history;
event_name	count(event_name)
statement/sp/freturn	2
statement/sql/insert	2
statement/sql/select	6
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	0
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	2
execute dump_transactions_history;
event_name	count(event_name)
transaction	2
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	1	1
user2	localhost	1	1
user3	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
select uuid_short() <> 1;
uuid_short() <> 1
1
select uuid_short() <> 1;
uuid_short() <> 1
1
start transaction;
insert into test.t1 values ("marker");
commit;
select test.f(10,20);
test.f(10,20)
30
"================== con3 marker =================="
"================== Step 7 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/file/sql/query_log	7
user1	wait/synch/mutex/sql/LOCK_connection_count	0
user1	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user2	wait/io/file/sql/query_log	7
user2	wait/synch/mutex/sql/LOCK_connection_count	0
user2	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user3	wait/io/file/sql/query_log	7
user3	wait/synch/mutex/sql/LOCK_connection_count	0
user3	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	7
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user2	localhost	wait/io/file/sql/query_log	7
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user3	localhost	wait/io/file/sql/query_log	7
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	21
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	6
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	21
wait/synch/mutex/sql/LOCK_uuid_generator	6
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	8
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	8
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	8
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	12
stage/sql/closing tables	18
stage/sql/init	12
stage/sql/Opening tables	15
stage/sql/starting	24
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	12
stage/sql/closing tables	18
stage/sql/init	12
stage/sql/Opening tables	15
stage/sql/starting	24
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	3
statement/sql/insert	3
statement/sql/select	9
execute dump_statements_history;
event_name	count(event_name)
statement/sp/freturn	3
statement/sql/insert	3
statement/sql/select	9
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	3
execute dump_transactions_history;
event_name	count(event_name)
transaction	3
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	1	1
user2	localhost	1	1
user3	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
"================== con4 connected =================="
"================== Step 8 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/file/sql/query_log	7
user1	wait/synch/mutex/sql/LOCK_connection_count	0
user1	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user2	wait/io/file/sql/query_log	7
user2	wait/synch/mutex/sql/LOCK_connection_count	0
user2	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user3	wait/io/file/sql/query_log	7
user3	wait/synch/mutex/sql/LOCK_connection_count	0
user3	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user4	wait/io/file/sql/query_log	1
user4	wait/synch/mutex/sql/LOCK_connection_count	0
user4	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	7
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user2	localhost	wait/io/file/sql/query_log	7
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user3	localhost	wait/io/file/sql/query_log	7
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user4	localhost	wait/io/file/sql/query_log	1
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	22
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	6
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	22
wait/synch/mutex/sql/LOCK_uuid_generator	6
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	8
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	8
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	8
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	12
stage/sql/closing tables	18
stage/sql/init	12
stage/sql/Opening tables	15
stage/sql/starting	24
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	12
stage/sql/closing tables	18
stage/sql/init	12
stage/sql/Opening tables	15
stage/sql/starting	24
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	0
user4	localhost	statement/sql/insert	0
user4	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	3
statement/sql/insert	3
statement/sql/select	9
execute dump_statements_history;
event_name	count(event_name)
statement/sp/freturn	3
statement/sql/insert	3
statement/sql/select	9
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	0
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	3
execute dump_transactions_history;
event_name	count(event_name)
transaction	3
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	1	1
user2	localhost	1	1
user3	localhost	1	1
user4	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
select uuid_short() <> 1;
uuid_short() <> 1
1
select uuid_short() <> 1;
uuid_short() <> 1
1
start transaction;
insert into test.t1 values ("marker");
commit;
select test.f(10,20);
test.f(10,20)
30
"================== con4 marker =================="
"================== Step 9 =================="
call dump_thread();
username	event_name	count_star
user1	wait/io/file/sql/query_log	7
user1	wait/synch/mutex/sql/LOCK_connection_count	0
user1	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user2	wait/io/file/sql/query_log	7
user2	wait/synch/mutex/sql/LOCK_connection_count	0
user2	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user3	wait/io/file/sql/query_log	7
user3	wait/synch/mutex/sql/LOCK_connection_count	0
user3	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user4	wait/io/file/sql/query_log	7
user4	wait/synch/mutex/sql/LOCK_connection_count	0
user4	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	7
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user2	localhost	wait/io/file/sql/query_log	7
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user3	localhost	wait/io/file/sql/query_log	7
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user4	localhost	wait/io/file/sql/query_log	7
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	28
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	28
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	8
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	8
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	8
user4	localhost	stage/sql/checking permissions	4
user4	localhost	stage/sql/closing tables	6
user4	localhost	stage/sql/init	4
user4	localhost	stage/sql/Opening tables	5
user4	localhost	stage/sql/starting	8
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	32
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	32
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	1	1
user2	localhost	1	1
user3	localhost	1	1
user4	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
"================== con1 disconnected =================="
"================== Step 10 =================="
call dump_thread();
username	status
user1	not found
username	event_name	count_star
user2	wait/io/file/sql/query_log	7
user2	wait/synch/mutex/sql/LOCK_connection_count	0
user2	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user3	wait/io/file/sql/query_log	7
user3	wait/synch/mutex/sql/LOCK_connection_count	0
user3	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user4	wait/io/file/sql/query_log	7
user4	wait/synch/mutex/sql/LOCK_connection_count	0
user4	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	8
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user2	localhost	wait/io/file/sql/query_log	7
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user3	localhost	wait/io/file/sql/query_log	7
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user4	localhost	wait/io/file/sql/query_log	7
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	29
wait/synch/mutex/sql/LOCK_connection_count	1
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	29
wait/synch/mutex/sql/LOCK_connection_count	1
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	9
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	8
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	8
user4	localhost	stage/sql/checking permissions	4
user4	localhost	stage/sql/closing tables	6
user4	localhost	stage/sql/init	4
user4	localhost	stage/sql/Opening tables	5
user4	localhost	stage/sql/starting	8
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	33
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	33
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	1
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	1
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	1	1
user3	localhost	1	1
user4	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
"================== con2 disconnected =================="
"================== Step 11 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	event_name	count_star
user3	wait/io/file/sql/query_log	7
user3	wait/synch/mutex/sql/LOCK_connection_count	0
user3	wait/synch/mutex/sql/LOCK_uuid_generator	2
username	event_name	count_star
user4	wait/io/file/sql/query_log	7
user4	wait/synch/mutex/sql/LOCK_connection_count	0
user4	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	8
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user2	localhost	wait/io/file/sql/query_log	8
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user3	localhost	wait/io/file/sql/query_log	7
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user4	localhost	wait/io/file/sql/query_log	7
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	30
wait/synch/mutex/sql/LOCK_connection_count	2
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	30
wait/synch/mutex/sql/LOCK_connection_count	2
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	9
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	9
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	8
user4	localhost	stage/sql/checking permissions	4
user4	localhost	stage/sql/closing tables	6
user4	localhost	stage/sql/init	4
user4	localhost	stage/sql/Opening tables	5
user4	localhost	stage/sql/starting	8
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	34
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	34
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	2
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	2
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	1	1
user4	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
"================== con3 disconnected =================="
"================== Step 12 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	event_name	count_star
user4	wait/io/file/sql/query_log	7
user4	wait/synch/mutex/sql/LOCK_connection_count	0
user4	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	8
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user2	localhost	wait/io/file/sql/query_log	8
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user3	localhost	wait/io/file/sql/query_log	8
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user4	localhost	wait/io/file/sql/query_log	7
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	31
wait/synch/mutex/sql/LOCK_connection_count	3
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	31
wait/synch/mutex/sql/LOCK_connection_count	3
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	9
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	9
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	9
user4	localhost	stage/sql/checking permissions	4
user4	localhost	stage/sql/closing tables	6
user4	localhost	stage/sql/init	4
user4	localhost	stage/sql/Opening tables	5
user4	localhost	stage/sql/starting	8
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	35
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	35
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	3
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	3
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
"================== con4 disconnected =================="
"================== Step 13 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	8
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user2	localhost	wait/io/file/sql/query_log	8
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user3	localhost	wait/io/file/sql/query_log	8
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user4	localhost	wait/io/file/sql/query_log	8
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	9
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	9
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	9
user4	localhost	stage/sql/checking permissions	4
user4	localhost	stage/sql/closing tables	6
user4	localhost	stage/sql/init	4
user4	localhost	stage/sql/Opening tables	5
user4	localhost	stage/sql/starting	9
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	1
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_waits_summary_by_thread_by_event_name;
"================== WAITS_BY_THREAD truncated =================="
"================== Step 14 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	8
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user2	localhost	wait/io/file/sql/query_log	8
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user3	localhost	wait/io/file/sql/query_log	8
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
user4	localhost	wait/io/file/sql/query_log	8
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	1
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	2
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	9
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	9
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	9
user4	localhost	stage/sql/checking permissions	4
user4	localhost	stage/sql/closing tables	6
user4	localhost	stage/sql/init	4
user4	localhost	stage/sql/Opening tables	5
user4	localhost	stage/sql/starting	9
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	1
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_waits_summary_by_account_by_event_name;
"================== WAITS_BY_ACCOUNT truncated =================="
"================== Step 15 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	9
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	9
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	9
user4	localhost	stage/sql/checking permissions	4
user4	localhost	stage/sql/closing tables	6
user4	localhost	stage/sql/init	4
user4	localhost	stage/sql/Opening tables	5
user4	localhost	stage/sql/starting	9
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	1
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_waits_summary_by_user_by_event_name;
"================== WAITS_BY_USER truncated =================="
"================== Step 16 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	9
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	9
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	9
user4	localhost	stage/sql/checking permissions	4
user4	localhost	stage/sql/closing tables	6
user4	localhost	stage/sql/init	4
user4	localhost	stage/sql/Opening tables	5
user4	localhost	stage/sql/starting	9
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	1
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_waits_summary_by_host_by_event_name;
"================== WAITS_BY_HOST truncated =================="
"================== Step 17 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	9
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	9
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	9
user4	localhost	stage/sql/checking permissions	4
user4	localhost	stage/sql/closing tables	6
user4	localhost	stage/sql/init	4
user4	localhost	stage/sql/Opening tables	5
user4	localhost	stage/sql/starting	9
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	1
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_waits_summary_global_by_event_name;
"================== WAITS_GLOBAL truncated =================="
"================== Step 18 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	9
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	9
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	9
user4	localhost	stage/sql/checking permissions	4
user4	localhost	stage/sql/closing tables	6
user4	localhost	stage/sql/init	4
user4	localhost	stage/sql/Opening tables	5
user4	localhost	stage/sql/starting	9
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	1
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_stages_summary_by_thread_by_event_name;
"================== STAGES_BY_THREAD truncated =================="
"================== Step 19 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	4
user1	localhost	stage/sql/closing tables	6
user1	localhost	stage/sql/init	4
user1	localhost	stage/sql/Opening tables	5
user1	localhost	stage/sql/starting	9
user2	localhost	stage/sql/checking permissions	4
user2	localhost	stage/sql/closing tables	6
user2	localhost	stage/sql/init	4
user2	localhost	stage/sql/Opening tables	5
user2	localhost	stage/sql/starting	9
user3	localhost	stage/sql/checking permissions	4
user3	localhost	stage/sql/closing tables	6
user3	localhost	stage/sql/init	4
user3	localhost	stage/sql/Opening tables	5
user3	localhost	stage/sql/starting	9
user4	localhost	stage/sql/checking permissions	4
user4	localhost	stage/sql/closing tables	6
user4	localhost	stage/sql/init	4
user4	localhost	stage/sql/Opening tables	5
user4	localhost	stage/sql/starting	9
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	1
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_stages_summary_by_account_by_event_name;
"================== STAGES_BY_ACCOUNT truncated =================="
"================== Step 20 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	1
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_stages_summary_by_user_by_event_name;
"================== STAGES_BY_USER truncated =================="
"================== Step 21 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	1
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_stages_summary_by_host_by_event_name;
"================== STAGES_BY_HOST truncated =================="
"================== Step 22 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	1
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_stages_summary_global_by_event_name;
"================== STAGES_GLOBAL truncated =================="
"================== Step 23 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	1
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_statements_summary_by_thread_by_event_name;
"================== STATEMENTS_BY_THREAD truncated =================="
"================== Step 24 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	1
user1	localhost	statement/sp/freturn	1
user1	localhost	statement/sql/insert	1
user1	localhost	statement/sql/select	3
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	1
user2	localhost	statement/sp/freturn	1
user2	localhost	statement/sql/insert	1
user2	localhost	statement/sql/select	3
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	1
user3	localhost	statement/sp/freturn	1
user3	localhost	statement/sql/insert	1
user3	localhost	statement/sql/select	3
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	1
user4	localhost	statement/sp/freturn	1
user4	localhost	statement/sql/insert	1
user4	localhost	statement/sql/select	3
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_statements_summary_by_account_by_event_name;
"================== STATEMENTS_BY_ACCOUNT truncated =================="
"================== Step 25 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	0
user1	localhost	statement/sql/insert	0
user1	localhost	statement/sql/select	0
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	0
user2	localhost	statement/sql/insert	0
user2	localhost	statement/sql/select	0
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	0
user3	localhost	statement/sql/insert	0
user3	localhost	statement/sql/select	0
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	0
user4	localhost	statement/sql/insert	0
user4	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_statements_summary_by_user_by_event_name;
"================== STATEMENTS_BY_USER truncated =================="
"================== Step 26 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	0
user1	localhost	statement/sql/insert	0
user1	localhost	statement/sql/select	0
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	0
user2	localhost	statement/sql/insert	0
user2	localhost	statement/sql/select	0
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	0
user3	localhost	statement/sql/insert	0
user3	localhost	statement/sql/select	0
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	0
user4	localhost	statement/sql/insert	0
user4	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_statements_summary_by_host_by_event_name;
"================== STATEMENTS_BY_HOST truncated =================="
"================== Step 27 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	0
user1	localhost	statement/sql/insert	0
user1	localhost	statement/sql/select	0
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	0
user2	localhost	statement/sql/insert	0
user2	localhost	statement/sql/select	0
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	0
user3	localhost	statement/sql/insert	0
user3	localhost	statement/sql/select	0
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	0
user4	localhost	statement/sql/insert	0
user4	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_statements_summary_global_by_event_name;
"================== STATEMENTS_GLOBAL truncated =================="
"================== Step 28 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	0
user1	localhost	statement/sql/insert	0
user1	localhost	statement/sql/select	0
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	0
user2	localhost	statement/sql/insert	0
user2	localhost	statement/sql/select	0
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	0
user3	localhost	statement/sql/insert	0
user3	localhost	statement/sql/select	0
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	0
user4	localhost	statement/sql/insert	0
user4	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	0
statement/sql/insert	0
statement/sql/select	0
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_transactions_summary_by_thread_by_event_name;
"================== TRANSACTIONS_BY_THREAD truncated =================="
"================== Step 29 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	0
user1	localhost	statement/sql/insert	0
user1	localhost	statement/sql/select	0
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	0
user2	localhost	statement/sql/insert	0
user2	localhost	statement/sql/select	0
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	0
user3	localhost	statement/sql/insert	0
user3	localhost	statement/sql/select	0
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	0
user4	localhost	statement/sql/insert	0
user4	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	0
statement/sql/insert	0
statement/sql/select	0
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	1
user2	localhost	transaction	1
user3	localhost	transaction	1
user4	localhost	transaction	1
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_transactions_summary_by_account_by_event_name;
"================== TRANSACTIONS_BY_ACCOUNT truncated =================="
"================== Step 30 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	0
user1	localhost	statement/sql/insert	0
user1	localhost	statement/sql/select	0
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	0
user2	localhost	statement/sql/insert	0
user2	localhost	statement/sql/select	0
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	0
user3	localhost	statement/sql/insert	0
user3	localhost	statement/sql/select	0
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	0
user4	localhost	statement/sql/insert	0
user4	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	0
statement/sql/insert	0
statement/sql/select	0
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	0
user2	localhost	transaction	0
user3	localhost	transaction	0
user4	localhost	transaction	0
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_transactions_summary_by_user_by_event_name;
"================== TRANSACTIONS_BY_USER truncated =================="
"================== Step 31 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	0
user1	localhost	statement/sql/insert	0
user1	localhost	statement/sql/select	0
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	0
user2	localhost	statement/sql/insert	0
user2	localhost	statement/sql/select	0
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	0
user3	localhost	statement/sql/insert	0
user3	localhost	statement/sql/select	0
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	0
user4	localhost	statement/sql/insert	0
user4	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	0
statement/sql/insert	0
statement/sql/select	0
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	0
user2	localhost	transaction	0
user3	localhost	transaction	0
user4	localhost	transaction	0
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_transactions_summary_by_host_by_event_name;
"================== TRANSACTIONS_BY_HOST truncated =================="
"================== Step 32 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	0
user1	localhost	statement/sql/insert	0
user1	localhost	statement/sql/select	0
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	0
user2	localhost	statement/sql/insert	0
user2	localhost	statement/sql/select	0
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	0
user3	localhost	statement/sql/insert	0
user3	localhost	statement/sql/select	0
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	0
user4	localhost	statement/sql/insert	0
user4	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	0
statement/sql/insert	0
statement/sql/select	0
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	0
user2	localhost	transaction	0
user3	localhost	transaction	0
user4	localhost	transaction	0
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	4
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.events_transactions_summary_global_by_event_name;
"================== TRANSACTIONS_GLOBAL truncated =================="
"================== Step 33 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
user1	localhost	wait/io/file/sql/query_log	0
user1	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user1	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user2	localhost	wait/io/file/sql/query_log	0
user2	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user2	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user3	localhost	wait/io/file/sql/query_log	0
user3	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user3	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
user4	localhost	wait/io/file/sql/query_log	0
user4	localhost	wait/synch/mutex/sql/LOCK_connection_count	0
user4	localhost	wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
user1	localhost	stage/sql/checking permissions	0
user1	localhost	stage/sql/closing tables	0
user1	localhost	stage/sql/init	0
user1	localhost	stage/sql/Opening tables	0
user1	localhost	stage/sql/starting	0
user2	localhost	stage/sql/checking permissions	0
user2	localhost	stage/sql/closing tables	0
user2	localhost	stage/sql/init	0
user2	localhost	stage/sql/Opening tables	0
user2	localhost	stage/sql/starting	0
user3	localhost	stage/sql/checking permissions	0
user3	localhost	stage/sql/closing tables	0
user3	localhost	stage/sql/init	0
user3	localhost	stage/sql/Opening tables	0
user3	localhost	stage/sql/starting	0
user4	localhost	stage/sql/checking permissions	0
user4	localhost	stage/sql/closing tables	0
user4	localhost	stage/sql/init	0
user4	localhost	stage/sql/Opening tables	0
user4	localhost	stage/sql/starting	0
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
user1	localhost	statement/com/Error	0
user1	localhost	statement/com/Quit	0
user1	localhost	statement/sp/freturn	0
user1	localhost	statement/sql/insert	0
user1	localhost	statement/sql/select	0
user2	localhost	statement/com/Error	0
user2	localhost	statement/com/Quit	0
user2	localhost	statement/sp/freturn	0
user2	localhost	statement/sql/insert	0
user2	localhost	statement/sql/select	0
user3	localhost	statement/com/Error	0
user3	localhost	statement/com/Quit	0
user3	localhost	statement/sp/freturn	0
user3	localhost	statement/sql/insert	0
user3	localhost	statement/sql/select	0
user4	localhost	statement/com/Error	0
user4	localhost	statement/com/Quit	0
user4	localhost	statement/sp/freturn	0
user4	localhost	statement/sql/insert	0
user4	localhost	statement/sql/select	0
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	0
statement/sql/insert	0
statement/sql/select	0
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
user1	localhost	transaction	0
user2	localhost	transaction	0
user3	localhost	transaction	0
user4	localhost	transaction	0
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	0
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
user1	localhost	0	1
user2	localhost	0	1
user3	localhost	0	1
user4	localhost	0	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.accounts;
"================== ACCOUNTS truncated =================="
"================== Step 34 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	0
statement/sql/insert	0
statement/sql/select	0
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	0
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.users;
"================== USERS truncated =================="
"================== Step 35 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	0
statement/sql/insert	0
statement/sql/select	0
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	0
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
truncate performance_schema.hosts;
"================== HOSTS truncated =================="
"================== Step 36 =================="
call dump_thread();
username	status
user1	not found
username	status
user2	not found
username	status
user3	not found
username	status
user4	not found
execute dump_waits_account;
user	host	event_name	count_star
execute dump_waits_user;
user	event_name	count_star
execute dump_waits_host;
host	event_name	count_star
execute dump_waits_global;
event_name	count_star
wait/io/file/sql/query_log	0
wait/synch/mutex/sql/LOCK_connection_count	0
wait/synch/mutex/sql/LOCK_uuid_generator	0
execute dump_waits_history;
event_name	count(event_name)
wait/io/file/sql/query_log	32
wait/synch/mutex/sql/LOCK_connection_count	4
wait/synch/mutex/sql/LOCK_uuid_generator	8
execute dump_stages_account;
user	host	event_name	count_star
execute dump_stages_user;
user	event_name	count_star
execute dump_stages_host;
host	event_name	count_star
execute dump_stages_global;
event_name	count_star
stage/sql/checking permissions	0
stage/sql/closing tables	0
stage/sql/init	0
stage/sql/Opening tables	0
stage/sql/starting	0
execute dump_stages_history;
event_name	count(event_name)
stage/sql/checking permissions	16
stage/sql/closing tables	24
stage/sql/init	16
stage/sql/Opening tables	20
stage/sql/starting	36
execute dump_statements_account;
user	host	event_name	count_star
execute dump_statements_user;
user	event_name	count_star
execute dump_statements_host;
host	event_name	count_star
execute dump_statements_global;
event_name	count_star
statement/com/Error	0
statement/com/Quit	0
statement/sp/freturn	0
statement/sql/insert	0
statement/sql/select	0
execute dump_statements_history;
event_name	count(event_name)
statement/com/Quit	4
statement/sp/freturn	4
statement/sql/insert	4
statement/sql/select	12
execute dump_transactions_account;
user	host	event_name	count_star
execute dump_transactions_user;
user	event_name	count_star
execute dump_transactions_host;
host	event_name	count_star
execute dump_transactions_global;
event_name	count_star
transaction	0
execute dump_transactions_history;
event_name	count(event_name)
transaction	4
execute dump_accounts;
USER	HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
event_scheduler	localhost	1	1
root	localhost	1	1
execute dump_users;
USER	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
execute dump_hosts;
HOST	CURRENT_CONNECTIONS	TOTAL_CONNECTIONS
Warnings:
Warning	1681	'FLUSH PRIVILEGES' is deprecated and will be removed in a future release.
